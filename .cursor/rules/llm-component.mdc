---
description: Guidelines for the LLM component architecture
globs: frontend/src/components/LLMSelection/**/*.js,frontend/src/components/LLMSelection/**/*.jsx,server/utils/AiProviders/**/*.js
alwaysApply: false
---

# LLM Component Architecture

## Component Structure

### Core Components

1. **BaseLLMPreference** (`frontend/src/components/LLMSelection/BaseLLMPreference/index.jsx`)
   - The main component that renders the LLM provider selection UI
   - Handles searching and filtering of available LLM providers
   - Displays the currently selected provider
   - Renders provider-specific option components when a provider is selected

2. **LLMProviderConfig** (`frontend/src/components/LLMSelection/LLMProviderConfig/index.jsx`)
   - Defines all available LLM providers with their metadata
   - Contains the `getLLMProviders` function that returns the list of available providers
   - Manages model preferences through the `getModelPrefKey` function

3. **Provider-Specific Option Components**
   - Each provider has its own component for configuration (e.g., `OpenAiOptions`, `AnthropicAiOptions`)
   - These components handle provider-specific settings like API keys and model selection
   - Located in provider-specific directories under `frontend/src/components/LLMSelection/`

### Backend Components

1. **Provider Implementations** (`server/utils/AiProviders/`)
   - Each LLM provider has a corresponding implementation in the backend
   - These implementations handle the actual API calls to the provider services
   - They manage authentication, rate limiting, and error handling

2. **Model Mapping** (`server/utils/AiProviders/modelMap.js`)
   - Defines a mapping of model names to their context window sizes
   - Used to determine appropriate token limits for different models

## Provider Integration

### Frontend Provider Setup

Each provider requires:

1. A logo image (stored in `frontend/src/media/llmprovider/`)
2. An entry in the `getLLMProviders` array in `LLMProviderConfig/index.jsx`
3. A provider-specific options component that handles:
   - API key input and validation
   - Model selection
   - Other provider-specific settings

### Backend Provider Implementation

Each provider has a corresponding class in the backend:

1. Implementation class (e.g., `OpenAiLLM` in `server/utils/AiProviders/openAi/index.js`)
2. Standard methods for initialization, completion, streaming, and embedding
3. Provider-specific functionality for handling the unique aspects of that provider's API

## Settings and Suffixes

The system supports multiple contexts for LLM settings through suffixes:

- **DEFAULT** (`""`) - Main chat functionality
- **DOCUMENT_DRAFTING** (`"_DD"`) - Document drafting functionality
- **VALIDATE_ANSWER** (`"_VA"`) - Answer validation
- **COMPLEX_DOCUMENT_BUILDER** (`"_CDB"`) - Contextual database queries
- **PROMPT_UPGRADE** (`"_PU"`) - Prompt upgrading
- And others as defined in `PROVIDER_SUFFIXES`

Each suffix creates a separate set of settings with the same base keys, allowing different configurations for different parts of the application.

### Context-Specific State Management

To ensure that different LLM components maintain their individual settings, we use a two-pronged approach:

1. **Provider Selection Management**:
   The BaseLLMPreference component handles selection of the LLM provider and directly updates the system settings with the context-specific suffix:

   ```javascript
   const updateLLMChoice = (selection) => {
     // Update the local state via callback
     onLLMChange(selection);

     // Directly update the system settings with the specific moduleSuffix
     System.updateSystem({
       [`LLMProvider${moduleSuffix}`]: selection,
     });
   };
   ```

2. **Provider-Specific Settings Management**:
   Each provider component (like OpenAiOptions) must initialize with context-specific settings and update them with the appropriate suffix:

   ```javascript
   // Initialize with context-specific settings
   const [selectedModel, setSelectedModel] = useState(
     settings?.[`OpenAiModelPref${moduleSuffix}`] || ""
   );

   // Update system settings with the suffix when changed
   const handleModelChange = (e) => {
     const newModel = e.target.value;
     setSelectedModel(newModel);

     System.updateSystem({
       [`OpenAiModelPref${moduleSuffix}`]: newModel,
     });
   };
   ```

### Best Practices for Context-Specific Settings

1. **Always use the moduleSuffix**: When referencing or updating settings, always include the moduleSuffix:

   ```javascript
   settings?.[`OpenAiModelPref${moduleSuffix}`];
   ```

2. **Include route parameters in dependencies**: For components that change based on route parameters, ensure effect hooks include the relevant parameter or derived values in their dependency arrays.

3. **Immediate settings updates**: When making changes to settings, update the system immediately to ensure they're persisted:

   ```javascript
   System.updateSystem({
     [`LLMProvider${moduleSuffix}`]: newValue,
   });
   ```

4. **Provider-specific keys**: Use standardized key naming conventions with provider prefixes and context suffixes:

   ```javascript
   // For OpenAI in the validate answer context
   OpenAiKey_VA;
   OpenAiModelPref_VA;
   ```
