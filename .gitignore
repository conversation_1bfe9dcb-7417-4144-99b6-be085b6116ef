v-env
!.env
!.env.example

node_modules
__pycache__
v-env
.DS_Store
aws_cf_deploy_anything_llm.json
yarn.lock
*.bak
server/.env
collector/.env
!collector/.env.example
!collector/.env.development.example
/.idea/
AgentTemp/
/frontend/public/pdf.worker.js
.vscode

# Coverage directory used by tools like jest
/frontend/coverage/
/server/coverage/
/coverage/

# Claude AI local settings
.claude/settings.local.json

# SQLite database files
*.db
*.db-wal
*.db-shm
server/storage/*.db*

# Compiled TypeScript output
collector/dist/
server/dist/
server/temp/

# Ignore local settings files
server/settings.local.json
collector/settings.local.json
frontend/settings.local.json
server/.claude/settings.local.json
frontend/.claude/settings.local.json
collector/.claude/settings.local.json

collector/coverage

# Jest cache directories
server/.jest-cache/
server/.jest-cache-ci/
.jest-cache/
jest-cache/
**/jest-cache/

# Jest log files
jest-output.log
**/jest-output.log
*.jest.log

# Test results
server/test-results
test-results/

# Upload logs
server/storage/uploadlogs/

# Docker build logs
docker/build.log
docker/build-optimized.log
