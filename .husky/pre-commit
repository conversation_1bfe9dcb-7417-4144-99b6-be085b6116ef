#!/usr/bin/env sh

echo "Generating Prisma client..."
cd server && npx prisma generate || {
  echo "⚠️  Prisma client generation failed, continuing anyway..."
}
cd ..

echo "Running Prettier, ESLint, and TypeScript checks on staged files..."
npx lint-staged || exit 1 # Exit if formatting, linting, or type checking fails

echo "Running TypeScript checks..."
echo "🔍 Checking TypeScript for server, frontend, and collector..."

# Run all TypeScript checks in parallel
(cd server && npx tsc --noEmit -p tsconfig.check.json) &
SERVER_PID=$!

(cd frontend && npx tsc --noEmit --skipLibCheck) &
FRONTEND_PID=$!

(cd collector && npx tsc --noEmit) &
COLLECTOR_PID=$!

# Wait for all processes and capture their exit codes
wait $SERVER_PID
SERVER_EXIT=$?

wait $FRONTEND_PID
FRONTEND_EXIT=$?

wait $COLLECTOR_PID
COLLECTOR_EXIT=$?

# Check if any TypeScript check failed
if [ $SERVER_EXIT -ne 0 ]; then
  echo "❌ Server TypeScript check failed"
  exit 1
fi

if [ $FRONTEND_EXIT -ne 0 ]; then
  echo "❌ Frontend TypeScript check failed"
  exit 1
fi

if [ $COLLECTOR_EXIT -ne 0 ]; then
  echo "❌ Collector TypeScript check failed"
  exit 1
fi

echo "✅ All TypeScript checks passed"

echo "Verifying locale file consistency..."
node scripts/verifyLocaleFiles.mjs || {
  echo "Locale file verification failed"
  exit 1
}

echo "Running unit tests..."
npm run test:unit || {
  echo "Unit tests failed"
  exit 1
}

echo "✅ All pre-commit checks passed!"
exit 0

