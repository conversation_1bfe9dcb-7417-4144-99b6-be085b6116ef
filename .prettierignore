# defaults
**/.git
**/.svn
**/.hg
**/node_modules

#docs
.cursor/rules/README.md

#frontend
frontend/bundleinspector.html
**/dist

#server
server/swagger/openapi.json
server/storage/
server/utils/MODULE_PATCHING_README.md
server/utils/vectorDbProviders/milvus/MILVUS_SETUP.md

#embed
**/static/**
embed/src/utils/chat/hljs.js

# Markdown documentation files
server/tests/integration/BACKEND_COLLECTOR_INTEGRATION_TESTS.md
server/utils/MODULE_PATCHING_README.md
server/utils/vectorDbProviders/milvus/MILVUS_SETUP.md
