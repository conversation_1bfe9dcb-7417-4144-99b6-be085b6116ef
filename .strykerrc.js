/**
 * Stryker configuration for ISTLegal mutation testing
 * This file provides the runtime configuration for mutation testing
 */

module.exports = {
  // Use the main configuration file
  configFile: "./stryker.conf.json",

  // Environment-specific overrides
  ...(process.env.CI && {
    // CI-specific settings
    maxConcurrentTestRunners: 2,
    timeoutMS: 180000,
    timeoutFactor: 3,
    logLevel: "info",
    reporters: ["progress", "clear-text", "json"],
    dashboard: {
      reportType: "full",
    },
  }),

  // Local development overrides
  ...(!process.env.CI && {
    // Local development settings
    maxConcurrentTestRunners: Math.max(
      1,
      Math.floor(require("os").cpus().length / 2)
    ),
    timeoutMS: 30000,
    timeoutFactor: 2,
    logLevel: "debug",
    reporters: ["html", "clear-text", "progress"],
    allowConsoleColors: true,
  }),

  // Performance optimization
  ignorePatterns: [
    "**/node_modules/**",
    "**/dist/**",
    "**/build/**",
    "**/coverage/**",
    "**/reports/**",
    "**/*.test.ts",
    "**/*.test.js",
    "**/*.spec.ts",
    "**/*.spec.js",
    "**/*.d.ts",
    "**/migrations/**",
    "**/seeds/**",
    "**/docs/**",
    "**/scripts/**",
    "**/public/**",
    "**/tmp/**",
  ],

  // Jest configuration for mutation testing
  jest: {
    enableFindRelatedTests: true,
    projectType: "custom",
  },

  // Mutation testing specific settings
  mutator: {
    excludedMutations: [
      "StringLiteral", // Exclude string literal mutations for configuration files
      "BlockStatement", // Exclude block statement mutations to reduce noise
    ],
  },
};
