# XML prompt example

Present a complete plan to solve the problem and implement it in the codebase.

At the end of your response, respond with the following XML section (if applicable).

XML Section:

- Do not get lazy. Always output the full code in the XML section.
- Enclose this entire section in a markdown codeblock
- Include all of the changed files
- Specify each file operation with CREATE, UPDATE, or DELETE
- For CREATE or UPDATE operations, include the full file code
- Include the full file path (relative to the project directory, good: app/page.tsx, bad: /Users/<USER>/Desktop/projects/new-chat-template/app/page.tsx)
- Enclose the code with ![CDATA[__CODE HERE__]]
- Use the following XML structure:

```xml
<code_changes>
  <changed_files>
    <file>
      <file_operation>__FILE OPERATION HERE__</file_operation>
      <file_path>__FILE PATH HERE__</file_path>
      <file_code><![CDATA[
__FULL FILE CODE HERE__
]]></file_code>
    </file>
    __REMAINING FILES HERE__
  </changed_files>
</code_changes>
```

Other rules:

- DO NOT remove <ai_context> sections. These are to provide you additional context about each file.
- If you create a file, add an <ai_context> comment section at the top of the file.
- If you update a file make sure its <ai_context> stays up-to-date
- DO NOT add comments related to your edits
- DO NOT remove my existing comments

We may go back and forth a few times. If we do, remember to continue to output the entirety of the code in an XML section (if applicable).

Take all the time you need.
