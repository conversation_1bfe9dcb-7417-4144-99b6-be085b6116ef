# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

**IMPORTANT**: Before implementing any feature or making changes, always consult the relevant documentation in:

- `frontend/docs/` - Frontend architecture, components, and patterns
- `server/docs/` - Backend APIs, database schemas, and integrations
- `collector/docs/` - Document processing and file handling

## Project Overview

ISTLegal is a comprehensive legal document management and AI-assisted legal work platform. It's a Retrieval-Augmented Generation (RAG) application with:

- Multi-tenant architecture with workspace management
- Document ingestion and processing capabilities
- Integration with multiple LLM providers (OpenAI, Anthropic, Google, AWS Bedrock, Ollama, etc.)
- Vector database support for semantic search
- Multi-language support (de, en, fr, no, pl, rw, sv)

## Technology Stack

### Frontend

- React 18.2 with Vite build tool
- Tailwind CSS (using template color system)
- Zustand for state management
- i18next for internationalization
- TipTap for rich text editing
- React PDF for document handling

### Backend

- Node.js (>= 20) with Express.js
- **TypeScript** - Fully migrated with strict type checking
- Prisma ORM with SQLite database
- JWT authentication
- WebSocket for real-time features
- Multiple document parsers and OCR capabilities

### Additional Services

- Collector service for document processing
- Docker containerization
- Jenkins CI/CD pipeline

## Development Setup

```bash
## Initial setup (from root)
npm run setup

## Run development servers (in separate terminals)
npm run dev:server     # Backend on http://localhost:3001
npm run dev:frontend   # Frontend on http://localhost:3000
npm run dev:collector  # Document processor on http://localhost:8888
```

**Note**: All servers auto-reload on file changes. Manual restart only needed for environment variable changes.

## Key Development Commands

### Linting & Code Quality

```bash
npm run lint              # Lint all code
npm run lint:server       # Lint server only
npm run lint:frontend     # Lint frontend only
```

### TypeScript Commands

```bash
npm run build:server      # Compile TypeScript to JavaScript (from server directory)
npm run typecheck         # Run TypeScript type checking (from server directory)
tsc                       # Compile TypeScript (from server directory)
```

### Database Management

```bash
npm run prisma:generate   # Generate Prisma client
npm run prisma:migrate    # Run migrations
npm run prisma:seed       # Seed database
npm run prisma:setup      # Full setup (generate + migrate + seed)
npm run prisma:reset      # Reset database to clean state
npm run prisma:devupdate  # Push schema changes in development
```

### Translation Verification

```bash
npm run check-translations    # Verify all locale files are in sync
npm run verify:translations   # Same as above
```

### Version Management

```bash
npm run version:info          # Show current version info
npm run version:sync          # Sync versions across packages
npm run version:bump:patch    # Bump patch version
npm run version:bump:minor    # Bump minor version
npm run version:bump:major    # Bump major version
npm run version:set           # Set specific version
npm run version:release       # Create release
npm run version:tag           # Create git tag
```

### Production

```bash
npm run prod:server      # Start production server
npm run prod:frontend    # Build frontend for production
```

### Testing

```bash
npm test                 # Run all tests
npx jest                 # Run tests from root
```

## Project Structure

```text
/
├── frontend/           # React application
│   ├── src/           # Source code
│   ├── public/        # Static assets
│   └── docs/          # Frontend documentation
├── server/            # Express.js backend (TypeScript)
│   ├── endpoints/     # API routes (.ts files)
│   ├── models/        # Data models (.ts files)
│   ├── types/         # TypeScript type definitions
│   ├── utils/         # Utilities (AI providers, embeddings) (.ts files)
│   ├── prisma/        # Database schema
│   ├── dist/          # Compiled JavaScript output
│   └── storage/       # File storage (istlegal.db location)
├── collector/         # Document processing service
│   ├── hotdir/        # Upload directory
│   └── processSingleFile/  # File converters
├── docker/            # Docker configurations
├── scripts/           # Utility scripts
│   ├── reset-db.js    # Database reset script
│   └── version-management.js  # Version management
└── .cursor/rules/     # Development guidelines
```

## Important Files & Directories

- Database: `server/storage/istlegal.db`
- Environment files: `.env` files in server, frontend, and collector directories
- Prisma schema: `server/prisma/schema.prisma`
- **TypeScript config**: `server/tsconfig.json`
- **Type definitions**: `server/types/` directory
- **Compiled output**: `server/dist/` directory
- Vector storage: `server/storage/lancedb/` and `server/storage/vector-cache/`
- Document storage: `server/storage/documents/`
- Upload directory: `collector/hotdir/`

## Git Workflow

Branch strategy: `develop → main-dev → main-stage → main-prod → main`

- Pull requests go to `develop`
- Tested changes move through deployment branches
- Production releases go from `main-prod` to `main`

## Docker Development

```bash
## Initialize database in Docker (first time only)
sudo docker exec -it istlegal bash -c "cd /app/server && npx prisma migrate dev --name init"

## Debug container
sudo docker logs istlegal
sudo docker logs --tail 100 istlegal
sudo docker exec -it istlegal /bin/bash
```

## Mobile Testing

To test on mobile devices:

1. Find your local IP: `ipconfig` (Windows) or `ifconfig` (Mac/Linux)
2. Update frontend `.env`: `VITE_API_BASE=http://YOUR_LOCAL_IP:3001`
3. Restart frontend server
4. Access from mobile: `http://YOUR_LOCAL_IP:3000`

## Common Issues & Solutions

1. **Port conflicts**: Check if ports 3000, 3001, or 8888 are in use
2. **Database issues**: Run `npm run prisma:reset` to reset database
3. **Missing dependencies**: Run `npm run setup` again
4. **Translation errors**: Run `npm run check-translations` to identify missing keys
5. **Build errors**: Ensure Node.js >= 20 is installed
6. **TypeScript errors**: Run `npm run typecheck` (from server directory) to identify type issues
7. **Compilation errors**: Run `npm run build:server` (from server directory) to compile TypeScript

## Documentation Resources

Detailed documentation for each component is available in:

- **Frontend Documentation**: `frontend/docs/`
  - Component architecture
  - State management patterns
  - UI/UX guidelines
  - Translation system

- **Server Documentation**: `server/docs/`
  - API endpoint specifications
  - Database schema details
  - Authentication flow
  - Integration guides for LLM providers

- **Collector Documentation**: `collector/docs/`
  - Document processing pipeline
  - Supported file formats
  - OCR and transcription capabilities
  - Performance optimization

Always update relevant documentation when making changes to ensure future developers understand the system.

## Coding Standards & Guidelines

**IMPORTANT**: Follow the comprehensive coding standards and guidelines defined in `.cursor/rules/`. Key files include:

- `coding-standards.mdc` - Core coding principles (minimal, self-documenting, secure, performant)
- `frontend-standards.mdc` - React component guidelines, state management, styling rules
- `backend-standards.mdc` - API design, database operations, authentication patterns
- `documentation-updates.mdc` - Documentation requirements for every commit
- `git-workflow.mdc` - Branch strategy and commit guidelines
- `common-commands.mdc` - Frequently used development commands

Always consult these files before making significant changes to ensure consistency with project standards.

**For major code changes**: Always refer to `.cursor/rules/implementation-strategy.mdc` for the systematic approach to implementing significant features or refactoring existing code.

### Git Commit Policy

**CRITICAL**: NEVER commit changes unless the user explicitly confirms a suggested commit. It is VERY IMPORTANT to only commit when explicitly confirmed, otherwise the user will feel that you are being too proactive. Always wait for explicit user confirmation before making any commits.

### Testing Requirements

**IMPORTANT**: All new features and implementations MUST include comprehensive tests:

1. **Unit Tests** - Test individual functions and components in isolation
   - Frontend: Use Jest and React Testing Library for component tests
   - Backend: Use Jest for model and utility function tests
   - Aim for high code coverage of business logic

2. **Integration Tests** - Test interactions between different parts of the system
   - API endpoint tests using Supertest
   - Database operation tests with test data
   - Component integration tests for complex UI flows

3. **End-to-End Tests** - Test complete user workflows
   - Test critical user journeys from UI to database
   - Verify multi-service interactions (frontend → server → collector)
   - Include error scenarios and edge cases

**Test locations**:

- Frontend tests: Adjacent to components (e.g., `Component.test.jsx`)
- Server tests: In `__tests__` directories within each module (.ts and .js files)
- E2E tests: In dedicated test directories

**Note**: Server tests are in transition from JavaScript to TypeScript. Both `.js` and `.ts` test files exist during the migration period. Some TypeScript test files may have compilation errors during the migration process - these are expected and being addressed incrementally.

Run tests with `npm test` from the root directory or within specific packages.

**After completing any feature**: Always run `npx jest` from the root directory to ensure all system tests pass before considering the feature complete. This catches integration issues across the entire codebase.

### End-to-End (E2E) Testing with Playwright

The project includes Playwright for browser-based end-to-end testing:

**Location**: `e2e-playwright/` directory

**Running E2E tests**:

```bash
npm run test:e2e              # Run all E2E tests
npx playwright test --headed  # Run with visible browser
npx playwright test --ui      # Run in interactive UI mode
```

**Key test areas**:

- Authentication flows
- API health checks
- Navigation and responsive design
- Network error handling

See `e2e-playwright/README.md` for detailed E2E testing documentation.

Also run the following commands after completing a feature to ensure code quality:

1. `npm run lint` - Run ESLint to check for code style and potential errors
2. `npm run typecheck` - Run TypeScript type checking to catch type errors (from server directory)
3. `npm run build:server` - Compile TypeScript to ensure no compilation errors (from server directory)
4. `npx prettier --write .` - Format code according to project standards

This ensures consistent code style, type safety, and catches potential issues before they become problems.

### TypeScript Strict Typing Guidelines

**CRITICAL**: Never use `any` types in code. Always use proper TypeScript typing for maximum type safety and code quality.

**Strict Type Safety Requirements**:

1. **No `any` Types**: Avoid `any` types completely. Use specific interfaces, union types, or generic constraints instead

   ```typescript
   // ❌ Bad - using any
   const mockFunction = jest.fn().mockReturnValue({} as any);

   // ✅ Good - using proper types
   const mockFunction = jest
     .fn()
     .mockReturnValue({} as ModelResponse<FilteredUser>);
   ```

2. **Proper Interface Usage**: Use existing TypeScript interfaces from the `types/` directory

   ```typescript
   // ❌ Bad - any typing
   const user: any = { id: 1, username: "test" };

   // ✅ Good - proper interface
   const user: FilteredUser = {
     id: 1,
     username: "test",
     // ... other required fields
   };
   ```

3. **Mock Function Typing**: Mock functions should return properly typed values

   ```typescript
   // ❌ Bad - any return type
   jest
     .mocked(User.get)
     .mockImplementation(async (clause: any) => ({ ...clause }) as any);

   // ✅ Good - proper return type
   jest.mocked(User.get).mockImplementation(async (clause: WhereClause) => {
     // Implementation with proper FilteredUser return type
     return mockUser as FilteredUser;
   });
   ```

4. **Type Assertions**: Use specific type assertions only when absolutely necessary

   ```typescript
   // ❌ Bad - overly broad any assertion
   const result = (response as any).data;

   // ✅ Good - specific type assertion
   const result = (response as { data: ModelResponse<FilteredUser> }).data;
   ```

5. **Union Types**: Use union types for complex scenarios instead of any

   ```typescript
   // ❌ Bad - any for multiple possible types
   const handleEvent = (event: any) => { ... };

   // ✅ Good - union type
   const handleEvent = (event: MouseEvent | KeyboardEvent | TouchEvent) => { ... };
   ```

### TypeScript Compliance for Test Files

**CRITICAL**: ALL test files (including `.test.ts`, `.test.js`, `.spec.ts`, and E2E test files) MUST be fully TypeScript compliant with zero warnings or errors.

**Requirements for test files**:

1. **Type Safety**: All test files must pass TypeScript type checking without any implicit `any` types or type errors
2. **ESLint Compliance**: Test files must pass all ESLint rules including:
   - No unused variables (prefix with `_` if intentionally unused)
   - No constant binary expressions (use regex patterns instead of logical OR in expect statements)
   - Proper type annotations for function parameters
3. **Parameter Handling**:
   - Mock function parameters must have explicit types: `(req: any, res: any, next: any)`
   - Unused test function parameters must be prefixed with underscore: `async ({ page: _page })`
   - Array callback parameters need proper typing: `(data: any, index: number)`

**Common patterns to follow**:

```typescript
// Mock middleware functions
jest.mock("../../utils/middleware/example", () => ({
  middleware: jest.fn(() => (req: any, res: any, next: any) => next()),
}));

// Unused test parameters
test("example test", async ({ page: _page, context: _context }) => {
  // Test implementation
});

// Expect patterns with regex instead of logical OR
expect(response.toLowerCase()).toMatch(/pattern1|pattern2|pattern3/);
```

**Before committing**: Always run `npm run lint` and `npm run typecheck` to ensure all test files are compliant.

### Critical Testing Policy

**MANDATORY TEST COVERAGE**:

- **If you modify ANY existing code that lacks test coverage, you MUST add tests for that code**
- **If you add new functionality, you MUST include comprehensive tests**
- **If you fix a bug, you MUST add a test that would have caught that bug**

**100% TEST SUCCESS REQUIREMENT**:

- **ALL tests MUST pass with 100% success rate before ANY commit**
- **No exceptions - failing tests indicate broken functionality**
- **If a test is failing, either fix the code or fix the test, but NEVER commit with failing tests**

**Test Coverage Extension Protocol**:

1. Before modifying any file, check if it has existing test coverage
2. If no tests exist, create a test file following the project's naming conventions
3. Write tests that cover:
   - Happy path scenarios
   - Error conditions
   - Edge cases
   - Integration with other components
4. Ensure new tests follow existing patterns in the codebase
5. Run all tests locally before committing

**Verification Steps Before Commit**:

1. Run `npm test` from root - ALL tests must pass
2. Run `npm run lint` - No linting errors allowed
3. Run `npm run typecheck` (from server directory) - No type errors allowed
4. Run `npm run build:server` (from server directory) - Must compile successfully
5. Verify test coverage for modified files using `npm test -- --coverage`

Remember: **Untested code is broken code waiting to happen**. The extra time spent writing tests saves countless hours of debugging and prevents regressions.

### Database Unavailability in Tests

**IMPORTANT**: Tests that require database access must handle scenarios where the database is unavailable (e.g., GitHub Actions CI).

For detailed implementation patterns, see: `server/docs/testing-database-unavailability.md`

### Maintaining Cursor Rules

When you discover new patterns, conventions, or important insights about the codebase:

1. **Update existing rules** in `.cursor/rules/`:
   - Edit the relevant `.mdc` file to include new findings
   - Ensure YAML frontmatter remains valid (description and globs fields)
   - Keep rules concise and actionable

2. **Create new rule files** when needed:
   - Use descriptive filenames (e.g., `testing-patterns.mdc`, `api-versioning.mdc`)
   - Include proper YAML frontmatter:

     ```yaml
     ---
     description: "Brief description of what this rule covers"
     globs: ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"] # File patterns this rule applies to
     ---
     ```

   - Write clear, specific guidelines

3. **Examples of insights to document**:
   - Discovered architectural patterns
   - Common bug fixes or gotchas
   - Performance optimization techniques
   - Integration quirks with third-party services
   - Testing strategies that work well

## MCP (Model Context Protocol) Integration

This project uses MCP servers to enhance Claude Code's capabilities with external tools and up-to-date information.

### Context7 - Up-to-Date Documentation

**IMPORTANT**: For the most current and accurate documentation on external libraries, frameworks, and APIs, use Context7 MCP integration by including "use context7" in your prompts. Context7 automatically fetches the latest official documentation and code examples, ensuring you work with up-to-date information rather than potentially outdated knowledge.

#### When to Use Context7

Use Context7 when you need:

- Current API documentation for React, Vite, Tailwind CSS, Prisma, or any other dependency
- Latest syntax and best practices for libraries used in this project
- Version-specific implementation details
- Accurate code examples from official sources

Example: "use context7 to show me the latest Prisma client query syntax for filtering"

This ensures you're always working with the most recent and accurate information, preventing errors from outdated API usage.

### GitHub MCP Server - Repository Integration

The GitHub MCP server provides direct integration with GitHub repositories, enabling:

- File operations and repository management
- Issue and pull request interactions
- Code search functionality
- Branch creation and management
- Git history operations

#### GitHub MCP Usage Examples

- "List all open issues in this repository"
- "Create a new branch for feature development"
- "Search for files containing specific code patterns"
- "Show recent commits on the develop branch"

**Note**: The GitHub MCP server automatically creates branches when needed and maintains proper Git history without force pushing.

## Development Workflow with Specialized Sub-Agents

When working on development tasks, **ALWAYS** use the following specialized sub-agents at the appropriate phases:

### 1. Planning Phase - feature-upgrade-planner

**ALWAYS use the feature-upgrade-planner sub-agent** when:

- Drafting new features or planning comprehensive upgrades
- Analyzing existing functionality before major changes
- Creating architectural improvements
- Planning database schema updates or API modifications

This agent will help you:

- Analyze existing functionality and architecture
- Propose architectural improvements aligned with project standards
- Create detailed implementation roadmaps
- Consider all aspects: frontend UI/UX, backend API, database schema, and integration impacts

### 2. Implementation Phase

During implementation:

- Follow the roadmap created by the feature-upgrade-planner
- Use TodoWrite to track progress on implementation tasks
- Use search tools extensively to understand existing patterns
- Implement solutions following project conventions

### 3. Verification Phase - code-review-expert and test-engineer

**ALWAYS use these agents after completing implementation:**

#### code-review-expert

Use after writing or modifying code to:

- Review code changes against project coding standards
- Check adherence to TypeScript best practices
- Verify compliance with guidelines in:
  - `.cursor/rules/` - Development standards
  - `frontend/docs/` - Frontend patterns
  - `server/docs/` - Backend patterns
  - `collector/docs/` - Document processing patterns

#### test-engineer

Use after code review to:

- Run all tests in the codebase
- Diagnose and fix any test failures
- Evaluate test coverage for new features
- Ensure 100% test success before completion

### Important Notes

- These agents are stateless - provide comprehensive context in your prompts
- Launch agents concurrently when possible for better performance
- Each agent returns a report - summarize key findings for the user
- NEVER skip these verification steps - they ensure code quality and prevent regressions

### Using Gemini CLI with Sub-Agents

When working with sub-agents and you need to analyze large portions of the codebase that might exceed context limits:

1. **Before launching the feature-upgrade-planner**: Use Gemini CLI to gather comprehensive insights about the existing system:

   ```bash
   gemini -p "@frontend/src/ @server/ @collector/ Analyze the current architecture and identify all components related to [feature name]"
   ```

2. **When providing context to sub-agents**: Include key insights from Gemini analysis in your prompts to the sub-agents

3. **For code-review-expert**: Use Gemini to pre-scan for patterns:

   ```bash
   gemini -p "@src/ @server/ Check if all new code follows the established patterns for error handling, logging, and security"
   ```

4. **For test-engineer**: Use Gemini to identify test coverage gaps:

   ```bash
   gemini -p "@src/ @__tests__/ @server/__tests__/ Identify which new functions or components lack test coverage"
   ```

This approach combines the specialized expertise of sub-agents with Gemini's ability to analyze the entire codebase at once.

## TypeScript Migration & Type System

The backend has been fully migrated to TypeScript with comprehensive type safety:

### Key Features

- **Strict TypeScript Configuration**: Full type checking with strict mode enabled
- **Comprehensive Type Definitions**: All types centralized in `server/types/` directory
- **Type-Safe Database Operations**: Prisma integration with full type safety
- **API Type Safety**: All endpoints have proper TypeScript interfaces

### Type Definition Structure

```text
server/types/
├── agents.ts              # Agent system types
├── ai-providers.ts        # AI provider interfaces
├── api.ts                 # API endpoint types
├── auth.ts               # Authentication types
├── chat-agent.ts         # Chat agent types
├── chat-flow.ts          # Chat flow types
├── document-processing.ts # Document processing types
├── document.ts           # Document types
├── jobs.ts               # Background job types
├── llm.ts                # LLM provider types
├── models.ts             # Database model types
├── shared.ts             # Shared types across modules
├── utils.ts              # Utility types
├── vectorDb.ts           # Vector database types
└── *.d.ts files          # Type declarations for external libraries
```

### Working with TypeScript

1. **Entry Point**: `server/index.ts` (migrated from `index.js`)
2. **Compilation**: TypeScript compiles to `server/dist/` directory
3. **Development**: Use `nodemon server/index.ts` for auto-reload
4. **Build**: Run `npm run build:server` to compile (from server directory)
5. **Type Checking**: Run `npm run typecheck` to verify types (from server directory)

### Migration Status

- **Production Code**: ~75% migrated to TypeScript
- **API Endpoints**: Fully migrated with type safety
- **Models & Utils**: Fully migrated
- **Tests**: Mixed - some tests still in JavaScript during transition
- **Test Migration**: Some TypeScript test files have compilation errors during migration
- **Build Process**: Full TypeScript compilation pipeline

### Best Practices

1. **Import Types**: Use proper type imports from `server/types/`
2. **Strict Typing**: Follow the strict TypeScript configuration
3. **Type Definitions**: Add new types to appropriate files in `server/types/`
4. **Compilation**: Always ensure TypeScript compiles without errors
5. **Type Safety**: Leverage TypeScript's type checking for better code quality

## Using Gemini CLI for Large Codebase Analysis

When analyzing large codebases or multiple files that might exceed context limits, use the Gemini CLI with its massive
context window. Use `gemini -p` to leverage Google Gemini's large context capacity.

## File and Directory Inclusion Syntax

Use the `@` syntax to include files and directories in your Gemini prompts. The paths should be relative to WHERE you run the
gemini command:

### Gemini CLI Usage Examples

**Single file analysis:**
gemini -p "@src/main.ts Explain this file's purpose and structure"

Multiple files:
gemini -p "@package.json @server/index.ts Analyze the dependencies used in the code"

Entire directory:
gemini -p "@src/ Summarize the architecture of this codebase"

Multiple directories:
gemini -p "@src/ @tests/ Analyze test coverage for the source code"

Current directory and subdirectories:
gemini -p "@./ Give me an overview of this entire project"

### Or use --all_files flag

gemini --all_files -p "Analyze the project structure and dependencies"

### Implementation Verification Examples

Check if a feature is implemented:
gemini -p "@src/ @lib/ Has dark mode been implemented in this codebase? Show me the relevant files and functions"

Verify authentication implementation:
gemini -p "@src/ @middleware/ Is JWT authentication implemented? List all auth-related endpoints and middleware"

Check for specific patterns:
gemini -p "@src/ Are there any React hooks that handle WebSocket connections? List them with file paths"

Verify error handling:
gemini -p "@src/ @api/ Is proper error handling implemented for all API endpoints? Show examples of try-catch blocks"

Check for rate limiting:
gemini -p "@backend/ @middleware/ Is rate limiting implemented for the API? Show the implementation details"

Verify caching strategy:
gemini -p "@src/ @lib/ @services/ Is Redis caching implemented? List all cache-related functions and their usage"

Check for specific security measures:
gemini -p "@src/ @api/ Are SQL injection protections implemented? Show how user inputs are sanitized"

Verify test coverage for features:
gemini -p "@src/payment/ @tests/ Is the payment processing module fully tested? List all test cases"

### When to Use Gemini CLI

Use gemini -p when:

- Analyzing entire codebases or large directories
- Comparing multiple large files
- Need to understand project-wide patterns or architecture
- Current context window is insufficient for the task
- Working with files totaling more than 100KB
- Verifying if specific features, patterns, or security measures are implemented
- Checking for the presence of certain coding patterns across the entire codebase

### Gemini CLI Important Notes

- Paths in @ syntax are relative to your current working directory when invoking gemini
- The CLI will include file contents directly in the context
- No need for --yolo flag for read-only analysis
- Gemini's context window can handle entire codebases that would overflow Claude's context
- When checking implementations, be specific about what you're looking for to get accurate results
