# Mutation Testing Guide for ISTLegal

## Overview

Mutation testing is a method of software testing where the source code is deliberately modified (mutated) to verify that the test suite can detect these changes. This helps ensure the quality and effectiveness of our test suite.

## Configuration

### Main Configuration Files

1. **`stryker.conf.json`** - Main mutation testing configuration
2. **`mutation-testing.config.js`** - Advanced configuration with component-specific settings
3. **Package.json scripts** - Various mutation testing commands

## Running Mutation Tests

### Basic Commands

```bash
# Run all mutation tests
npm run test:mutation

# Run mutation tests with detailed reports
npm run test:mutation:report

# Run mutation tests in CI environment
npm run test:mutation:ci
```

### Component-Specific Testing

```bash
# Test security components (highest priority)
npm run test:mutation:security

# Test core business logic
npm run test:mutation:core

# Test AI providers
npm run test:mutation:ai

# Test vector database providers
npm run test:mutation:vector

# Test frontend utilities
npm run test:mutation:frontend
```

## Mutation Testing Targets

### High Priority Components

#### Security Middleware

- `server/utils/middleware/authenticatedUserOnly.ts`
- `server/utils/middleware/validWorkspace.ts`
- `server/utils/middleware/requireAdminRole.ts`

**Threshold**: 95% mutation score
**Timeout**: 45 seconds

#### Core Business Logic

- `server/models/systemSettings.ts`
- `server/models/eventLogs.ts`
- `server/models/workspaceChats.ts`

**Threshold**: 90% mutation score
**Timeout**: 60 seconds

### Medium Priority Components

#### AI Providers

- `server/utils/AiProviders/cohere/index.ts`

**Threshold**: 85% mutation score
**Timeout**: 90 seconds

#### Vector Database Providers

- `server/utils/vectorDbProviders/chroma/index.ts`
- `server/utils/vectorDbProviders/pinecone/index.ts`
- `server/utils/vectorDbProviders/qdrant/index.ts`

**Threshold**: 85% mutation score
**Timeout**: 120 seconds

### Lower Priority Components

#### Frontend Utilities

- `frontend/src/utils/workspace.ts`
- `frontend/src/utils/numbers.ts`
- `frontend/src/utils/events.ts`
- `frontend/src/stores/attachmentStore.ts`
- `frontend/src/stores/progressStore.ts`

**Threshold**: 80% mutation score
**Timeout**: 30 seconds

## Mutation Types

The following mutation types are applied:

- **ArithmeticOperator**: Changes `+` to `-`, `*` to `/`, etc.
- **BooleanLiteral**: Changes `true` to `false` and vice versa
- **ConditionalExpression**: Changes `if` conditions
- **EqualityOperator**: Changes `===` to `!==`, `==` to `!=`, etc.
- **LogicalOperator**: Changes `&&` to `||`, `!` to empty, etc.
- **MethodExpression**: Changes method calls
- **StringLiteral**: Changes string values
- **UnaryOperator**: Changes `!` to empty, `-` to `+`, etc.
- **UpdateOperator**: Changes `++` to `--`, etc.

## Interpreting Results

### Mutation Score

- **95%+**: Excellent test quality
- **90-94%**: Very good test quality
- **85-89%**: Good test quality
- **80-84%**: Acceptable test quality
- **<80%**: Needs improvement

### Report Files

Reports are generated in `reports/mutation-testing/`:

- `mutation-report.html` - Visual HTML report
- `mutation-report.json` - JSON data for CI integration

## Best Practices

### 1. Focus on Critical Code

Prioritize mutation testing for:

- Security-critical components
- Core business logic
- Complex algorithms
- Error handling paths

### 2. Analyze Surviving Mutants

If a mutant survives, it means:

- The test suite didn't catch the change
- You may need additional test cases
- The code might be redundant

### 3. Performance Considerations

- Use `maxConcurrentTestRunners` to optimize performance
- Set appropriate timeouts for different components
- Use `coverageAnalysis: 'perTest'` for faster execution

### 4. CI Integration

Add mutation testing to your CI pipeline:

```yaml
# Example GitHub Actions step
- name: Run Mutation Tests
  run: npm run test:mutation:ci
  timeout-minutes: 30
```

## Troubleshooting

### Common Issues

1. **Timeout Errors**
   - Increase timeout values in configuration
   - Reduce concurrent test runners
   - Focus on specific components

2. **Memory Issues**
   - Reduce `maxConcurrentTestRunners`
   - Increase Node.js memory limit: `--max-old-space-size=4096`

3. **Test Failures**
   - Ensure all regular tests pass first
   - Check for test isolation issues
   - Verify mock configurations

### Performance Tips

1. **Incremental Testing**

   ```bash
   # Test only changed files
   npm run test:mutation -- --incremental
   ```

2. **Parallel Execution**

   ```bash
   # Optimize for your machine
   npm run test:mutation -- --concurrency 4
   ```

3. **Skip Slow Tests**

   ```bash
   # Skip integration tests during mutation testing
   npm run test:mutation -- --testPathIgnorePatterns integration
   ```

## Maintenance

### Regular Tasks

1. **Update Thresholds**: Review and adjust mutation score thresholds quarterly
2. **Add New Components**: Include new critical components in mutation testing
3. **Performance Tuning**: Optimize configuration based on CI performance
4. **Report Review**: Regularly review mutation reports for insights

### Monitoring

- Track mutation scores over time
- Monitor test execution time
- Identify frequently surviving mutants
- Adjust configuration based on results

## Integration with Development Workflow

### Pre-Commit Hook

Add mutation testing to pre-commit hooks for critical files:

```bash
#!/bin/sh
# Pre-commit hook for critical security files
npm run test:mutation:security
```

### Code Review Process

1. Include mutation testing results in code reviews
2. Require mutation testing for security-critical changes
3. Review surviving mutants for test completeness

## Resources

- [Stryker Mutator Documentation](https://stryker-mutator.io/)
- [Mutation Testing Best Practices](https://stryker-mutator.io/docs/General/guides/mutation-testing-best-practices)
- [Jest Integration Guide](https://stryker-mutator.io/docs/stryker-js/jest-runner)

## Support

For issues or questions about mutation testing:

1. Check the troubleshooting section above
2. Review Stryker documentation
3. Check test configurations in `stryker.conf.json`
4. Consult the advanced configuration in `mutation-testing.config.js`
