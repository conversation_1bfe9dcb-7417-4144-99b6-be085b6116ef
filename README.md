# IST Legal Platform

A comprehensive legal document management and AI-assisted legal work platform.

## Testing

The project includes comprehensive testing with Jest, Stryker mutation tests, and Playwright E2E tests:

### Comprehensive Test Suite

```bash
npm run test:comprehensive    # Run ALL tests (<PERSON><PERSON> + <PERSON><PERSON><PERSON> + <PERSON><PERSON>)
npm run test:all             # Run Jest + <PERSON><PERSON><PERSON> + <PERSON>wright (simple)
npm run test:all:ci          # Run all tests in CI mode
```

### Unit and Integration Tests (Jest)

```bash
npm test                      # Run all Jest tests
npm run test:unit            # Unit tests only
npm run test:integration     # Integration tests
npm run test:integration:only # Integration tests (exclude E2E)
npm run test:coverage        # With coverage report
npm run test:watch           # Watch mode
npm run test:fast            # Fast unit tests only
```

### Mutation Testing (Stryker)

```bash
npm run test:mutation        # Run all mutation tests
npm run test:mutation:security # Security middleware tests
npm run test:mutation:core   # Core model tests
npm run test:mutation:ai     # AI provider tests
npm run test:mutation:vector # Vector DB tests
npm run test:mutation:frontend # Frontend tests
npm run test:mutation:ci     # CI-optimized mutation tests
```

### End-to-End Tests (<PERSON>wright)

```bash
npm run test:e2e             # Run E2E tests
npx playwright test --ui     # Interactive UI mode
npx playwright test --headed # See browser
```

### Test Utilities

```bash
npm run test:cleanup         # Clean up test artifacts
npm run test:cleanup:stats   # Show cleanup statistics
npm run test:validate        # Validate test environment
npm run test:validate:fix    # Fix test environment issues
npm run test:comprehensive:cleanup # Legacy comprehensive tests
```

### Pre-push Testing

```bash
npm run test:pre-push        # Run comprehensive pre-push checks
```

See `e2e-playwright/README.md` for detailed E2E testing documentation.

## Deployment

### Github Action

Upon push (or merge) to the main branch, an action is automatically triggered to build and push a Docker image to Github Packages (ghcr.io). The configuration for this can be found in `.github/workflows/build-and-push-image.yaml`.
Upon push (or merge) to the develop branch, an action is automatically triggered to build and push a Docker image to Github Packages (ghcr.io). The configuration for this can be found in `.github/workflows/build-and-push-dev image.yaml`.

## Branch Usage Image

---

1. main Previous Stable PROD istlegal:main
1. main-prod Current Stable PROD istlegal:prod
1. main-stage (PR + Patches: tested) STAGE istlegal:stage
1. main-dev (PR + Patches: in test) DEV istlegal:dev
1. develop (only PR: in test) DEV istlegal:develop

## Flow

- Open PR ------> develop
- Merged PR + New Patches ------> main-dev
- Untested/failed patches ------> remains in main-dev
- Failed PR ------> remains in main-dev
- Tested/Passed PR ------> main-stage
- Tested/Passed Patches ------> main-stage
- Accepted PR ------> main-prod
- Accepted Patches ------> main-prod
- from main-prod ------> main: After a stable Prod release (backup)

### First Time Docker Deploy -> initialize database

```bash
sudo docker exec -it istlegal bash -c "cd /app/server && npx prisma migrate dev --name init"
```

## Debug deployed container

- SSH into the instance

```bash
sudo docker logs istlegal
sudo docker logs --tail 100 istlegal
sudo docker exec -it istlegal /bin/bash
sudo docker logs istlegal 2>&1 | grep "converted & ready for embedding"
sudo docker logs istlegal 2>&1 | grep "File copied and moved successfully from"
```

## Debugging ISTLegal.db on AWS

- SSH into the instance

```bash
sudo yum update
sudo yum install sqlite
cd /home/<USER>/istlegal
sqlite3 [DBNAME] such as istlegal.db
.tables
.schema
```

- Edit tables schema based on a working local db file. This includes adding columns which are missing in the production db file.

## Analysis

```bash
ls -lh /home/<USER>/istlegal/istlegal.db
find /home/<USER>/istlegal/hotdir -type f -name "_.pdf" | wc -l
find /home/<USER>/istlegal/documents -type f -name "_.json" | wc -l
find /home/<USER>/istlegal -type f -iname "Yttrandefrihetsgrundlagen-(1991-1469)_"
find /home/<USER>/istlegal -type f -iname "Mal-nr319--321-11_"
```

## Migrating users from a server to another

- SSH into the source server

```bash
cd /home/<USER>/istlegal
sqlite3 istlegal.db
SELECT * FROM users;
```

- Copy all users to a empty file in cursor.
- Prompt Cursor to make this file into valid sqlite3 inserts, each row should have this format:

```sql
(1, 'accountname', '$2b$10$z2Ewb.lgbd4rdrNSSwJJ0.vlpLwznBeCkVeENOsN1Y5tPKEb5KgHa', 'admin', 1, *************, *************, 1)
```

- SSH into the target server

```bash
cd /home/<USER>/istlegal
sqlite3 istlegal.db
DELETE FROM users;
INSERT INTO users (id, username, password, role, suspended, createdAt, lastUpdatedAt, seen_recovery_codes) VALUES
```

- Copy all the values from Cursor and paste into the sqlite3 command line.
- To run the commands and exit sqlite3 shell:

```sql
;
.exit
```

## Create Storage backup

- SSH into the instance

```bash
cd /home/<USER>/istlegal
zip -r storage.zip ./\*
cp istlegal.dp istlegal-1.dp
```

## Restore Storage backup

- SSH into the instance

```bash
cd /home/<USER>/istlegal
find . -type f ! -name '\*.zip' -delete
find . -type d -empty -delete
unzip -o storage.zip
```

## Clear all Embeddings

```bash
cd /home/<USER>/istlegal
sudo rm -rf lancedb
sudo rm -rf vector-cache
sqlite3 istlegal.db
DELETE FROM workspace_documents;
```

## Telemetry & Privacy

IST Legal contains a telemetry feature that collects anonymous usage information.

### Opting out

Set `DISABLE_TELEMETRY` in your server or docker .env settings to "true" to opt out of telemetry. You can also do this in-app by going to the sidebar > `Privacy` and disabling telemetry.

## GIT Modules

- `embed`: Submodule specifically for generation & creation of the [web embed widget](https://github.com/Mintplex-Labs/anythingllm-embed).
- `browser-extension`: Submodule for the [chrome browser extension](https://github.com/Mintplex-Labs/anythingllm-extension).

## !! Stop and Remove All Containers

```bash
sudo docker stop $(sudo docker ps -aq)
sudo docker rm $(sudo docker ps -aq)
sudo docker rmi $(sudo docker images -q)
```

## SSH Connect to instance

```bash
chmod 400 "IST Legal Rw - Dev.pem"

ssh -i "IST Legal Rw - Dev.pem" <<EMAIL>>
ssh -i "IST Legal Rw - Stage.pem" <<EMAIL>>
ssh -i "IST Legal Rw - Prod.pem" <<EMAIL>>

ssh -i "Foynet SSH.pem" <<EMAIL>>
ssh -i "Foynet SSH.pem" <<EMAIL>>
ssh -i "Poland Prod.pem" <<EMAIL>>
ssh -i "IST Support.pem" <<EMAIL>>
```

## !! PDR Algorithm

- When a user enables PDR on a document, the document is added to prompt if it contains the user's query most relevant chunk.
- Pinned documents are always given priority over PDR.
- if a document is already pinned, PDR will not add it to the prompt even if it contains the most relevant chunk.
- The PDR context window is autocalculated based on the LLM model maximum context window, history of the chat, and the response window.
- There is a pdr system settings used to control the pdr token limit, response token limit, and adjacent vector limit.
- If the PDR document is too large compared to the pdr token limit, the document is not added.
- for large documents, adjacent vectors are sent to the LLM based on the selected settings.
  -Pinned documents are compressed according to the available context window. It is not advised to pin too many documents but PDR can be enabled for many documents.

## Scripts Directory

Under the `scripts` directory, you can find useful utility scripts for managing the application:

### Reset Database Script (`reset-db.js`)

A utility script that resets and initializes the database for local development. It performs the following:

- Clears existing data (users, settings, workspaces, documents)
- Creates an admin user (username: "admin", password: "admin")
- Initializes system settings with default values
- Creates a default workspace
- Links the admin user to the workspace

To use the reset script:

1. Navigate to server directory: `cd server`
1. Run Prisma migrations if needed: `npx prisma migrate dev`
1. Return to project root: `cd ..`
1. Make script executable: `chmod +x scripts/reset-db.js`
1. Run the script: `./scripts/reset-db.js`

Note: Ensure you have Node.js installed and project dependencies (`npm install`) before running scripts.

### Database Migration

Creating good migrations is essential for a reliable application, especially in containerized environments like Docker. Here are best practices for creating robust migrations:

#### Creating Good Migrations with Prisma

1. **Create new migrations with:**

```bash
npx prisma migrate dev --name descriptive_name
```

1. **Always use conditional creation:**

- For tables: Use `CREATE TABLE IF NOT EXISTS`
- For indexes: Check if the index exists before creating `CREATE INDEX IF NOT EXISTS`
- For columns: Check if the column exists including it in your migration

1. **Handle schema dependencies properly:**

- Create tables before creating indexes or foreign keys
- When adding foreign keys, ensure referenced tables exist

1. **Command to inspect migrations in the database:**

```bash
cd /home/<USER>/istlegal (for server)
cd /server/storage/istlegal (for local)
sqlite3 istlegal.db
```

## View All Non-Completed Migrations

```sql
-- Display all migrations that haven't been completed
SELECT * FROM _prisma_migrations WHERE finished_at IS NULL ORDER BY started_at;
```

## View All Completed Migrations

```sql
-- Display all migrations that have been completed
SELECT * FROM _prisma_migrations WHERE finished_at IS NOT NULL ORDER BY started_at;
```

## Mark All Incomplete Migrations as Done

```sql
-- Mark all incomplete migrations as completed
UPDATE _prisma_migrations SET finished_at = CURRENT_TIMESTAMP, applied_steps_count = 1, logs = 'Manually marked as completed' WHERE finished_at IS NULL;
```

## Delete All Incomplete Migrations

```sql
-- Permanently delete all incomplete migration records
DELETE FROM _prisma_migrations WHERE finished_at IS NULL;
```

## Delete All Completed Migrations

```sql
-- Permanently delete all completed migration records
DELETE FROM _prisma_migrations WHERE finished_at IS NOT NULL;
```

## Delete All Migrations (Both Complete and Incomplete)

```sql
-- Delete all migration records from the table
DELETE FROM _prisma_migrations;
```

1. **Common SQL commands for migration troubleshooting:**

- View incomplete migrations
- View completed migrations
- Mark incomplete migrations as done
- Delete migration records as needed

1. **Common Issues**

- Failed migrations often occur when trying to create already existing objects
- Docker container restarts can trigger repeated migration attempts
- SQLite has limitations with IF NOT EXISTS for certain operations
- Schema dependencies must be properly ordered

## Running Tests

Run all tests for the frontend and server with:

```bash
npm test
```

Or from root

```bash
npx jest
```
