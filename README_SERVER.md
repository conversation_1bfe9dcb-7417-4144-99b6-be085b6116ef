# Server README

Server handling

## Docker

The configuration for building the Docker image is located in `docker/Dockerfile`. Briefly, the steps performed when the image is built are:
sudo yum update -y
sudo yum install -y docker
sudo service docker start
sudo systemctl enable docker
sudo usermod -aG docker ec2-user

## AWS setup

1. Set the GitHub Token and Username:
   `export GITHUB_TOKEN=<my_token>`
   `export GITHUB_USERNAME=<my_username>`
2. Login to GitHub Container Registry:
   `sudo docker login ghcr.io -u $GITHUB_USERNAME -p $GITHUB_TOKEN`

### RESTART DOCKER, UPDATE DOCKER -> main branch [Previous PRODUCTION instances]

sudo docker stop istlegal
sudo docker rm istlegal
sudo docker pull ghcr.io/rahswe/istlegal:main
sudo docker run -d --restart unless-stopped -p 80:3001 -p 443:3001 -p 3001:3001 --cap-add SYS_ADMIN --name istlegal -v /home/<USER>/istlegal:/app/server/storage -v /home/<USER>/istlegal/.env:/app/server/.env -e STORAGE_DIR=/app/server/storage ghcr.io/rahswe/istlegal:main

### RESTART DOCKER, UPDATE DOCKER -> main-prod branch [Current PRODUCTION instances]

sudo docker stop istlegal
sudo docker rm istlegal
sudo docker pull ghcr.io/rahswe/istlegal:prod
sudo docker run -d --restart unless-stopped -p 80:3001 -p 443:3001 -p 3001:3001 --cap-add SYS_ADMIN --name istlegal -v /home/<USER>/istlegal:/app/server/storage -v /home/<USER>/istlegal/.env:/app/server/.env -e STORAGE_DIR=/app/server/storage ghcr.io/rahswe/istlegal:prod

### RESTART DOCKER, UPDATE DOCKER -> main-stage branch [STAGE instances]

sudo docker stop istlegal
sudo docker rm istlegal
sudo docker pull ghcr.io/rahswe/istlegal:stage
sudo docker run -d --restart unless-stopped -p 80:3001 -p 443:3001 -p 3001:3001 --cap-add SYS_ADMIN --name istlegal -v /home/<USER>/istlegal:/app/server/storage -v /home/<USER>/istlegal/.env:/app/server/.env -e STORAGE_DIR=/app/server/storage ghcr.io/rahswe/istlegal:stage

### RESTART DOCKER, UPDATE DOCKER -> main-dev branch [DEVELOP instances]

sudo docker stop istlegal
sudo docker rm istlegal
sudo docker pull ghcr.io/rahswe/istlegal:dev
sudo docker run -d --restart unless-stopped -p 80:3001 -p 443:3001 -p 3001:3001 --cap-add SYS_ADMIN --name istlegal -v /home/<USER>/istlegal:/app/server/storage -v /home/<USER>/istlegal/.env:/app/server/.env -e STORAGE_DIR=/app/server/storage ghcr.io/rahswe/istlegal:dev

### RESTART DOCKER, UPDATE DOCKER -> develop branch [Latest PR]

sudo docker stop istlegal
sudo docker rm istlegal
sudo docker pull ghcr.io/rahswe/istlegal:develop
sudo docker run -d --restart unless-stopped -p 80:3001 -p 443:3001 -p 3001:3001 --cap-add SYS_ADMIN --name istlegal -v /home/<USER>/istlegal:/app/server/storage -v /home/<USER>/istlegal/.env:/app/server/.env -e STORAGE_DIR=/app/server/storage ghcr.io/rahswe/istlegal:develop

## START FILE SERVER

! lsof -i :8080 > /dev/null && mkdir -p c && cd /home/<USER>/istlegal/hotdir && python3 -m http.server 8080

## Ensure .env file exists

sudo rm -r /home/<USER>/istlegal/.env
sudo touch /home/<USER>/istlegal/.env
sudo chmod 644 /home/<USER>/istlegal/.env
sudo ls -l /home/<USER>/istlegal/.env
sudo nano /home/<USER>/istlegal/.env

## Grant permission

sudo chmod -R 755 /home/<USER>/istlegal
sudo chown -R ec2-user:ec2-user /home/<USER>/istlegal
sudo chmod 644 /home/<USER>/istlegal/.env

## Grant docker permission

sudo docker exec -it istlegal /bin/bash
ls -ld /app/collector/hotdir
ls -ld /server/storage/hotdir
chmod -R 755 /app/server/.env
chmod -R 755 /app/collector/hotdir
chmod -R 755 /app/server/storage/hotdir
exit
