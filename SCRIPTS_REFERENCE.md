# NPM Scripts Reference

This document explains when to use the different npm scripts in this project.

## Git Hook Configuration

- **`prepare`** - Automatically configures git hooks (runs automatically during npm install)

## Pre-Commit and Post-Commit Testing (GitHub Flow)

- **Pre-commit/Pre-push Tests:**
  - The script **`test:pre-push`** is run automatically before a git push (configured via git hooks). This script runs the test suite to ensure code quality before changes are pushed to the remote repository. It helps catch issues early and prevents broken code from being pushed.
  - You can run this manually with:

    ```bash
    npm run test:pre-push
    ```

- **Post-commit/Continuous Integration (CI) Tests:**
  - After code is pushed, **GitHub Actions** (or other CI systems) run the **`test:ci`** script. This script executes the full test suite in a clean CI environment to verify that all tests pass on the remote server.
  - The CI workflow may also run **`lint`**, **`test:coverage`**, and other quality checks as part of the automated pipeline.

- **Summary Table:**

  | Stage             | Script          | When It Runs               |
  | ----------------- | --------------- | -------------------------- |
  | Pre-push (local)  | `test:pre-push` | Before pushing to remote   |
  | Post-push (CI/CD) | `test:ci`       | After push, in CI workflow |

## Code Quality and Linting

Run these before committing code:

- **`lint`** - Runs linting for both server and frontend
- **`lint:server`** - Runs ESLint on server code only
- **`lint:frontend`** - Runs ESLint on frontend code only

## Translation Management

Run when adding or modifying translations:

- **`check-translations`** - Verifies all locale files are synchronized
- **`verify:translations`** - Same as check-translations

## Initial Project Setup

Run once after cloning the repository:

- **`setup`** - Complete project setup (installs dependencies, configures environment, sets up database)
- **`setup:envs`** - Copies environment template files
- **`setup:codex`** - Sets up Codex-specific configuration

## Development Servers

Run in separate terminals for local development:

- **`dev:server`** - Starts the backend development server (port 3001)
- **`dev:collector`** - Starts the document collector service (port 8888)
- **`dev:frontend`** - Starts the frontend development server (port 3000)

## Database Management

Use for schema changes and data management:

- **`prisma:generate`** - Generates Prisma client from schema
- **`prisma:migrate`** - Runs database migrations
- **`prisma:seed`** - Seeds the database with initial data
- **`prisma:setup`** - Complete database setup (generate + migrate + seed)
- **`prisma:reset`** - Resets database to clean state
- **`prisma:devupdate`** - Pushes schema changes directly to database (development only)

## Production Builds

Use for deployment:

- **`prod:server`** - Starts production server
- **`prod:frontend`** - Builds frontend for production

## Cloud Deployment

Use for generating infrastructure configurations:

- **`generate:cloudformation`** - Generates AWS CloudFormation templates
- **`generate::gcp_deployment`** - Generates Google Cloud Platform deployment configs

## Version Management

Use for releases and version tracking:

- **`version:info`** - Shows current version information
- **`version:sync`** - Synchronizes versions across all packages
- **`version:bump:patch`** - Bumps patch version (1.0.0 -> 1.0.1)
- **`version:bump:minor`** - Bumps minor version (1.0.0 -> 1.1.0)
- **`version:bump:major`** - Bumps major version (1.0.0 -> 2.0.0)
- **`version:set`** - Sets a specific version
- **`version:release`** - Creates a release
- **`version:tag`** - Creates a git tag

## Standard Testing

Run regularly during development:

- **`test`** - Runs all tests with Jest
- **`test:fast`** - Runs unit tests only with parallel workers
- **`test:unit`** - Runs unit tests only
- **`test:integration`** - Runs integration and end-to-end tests
- **`test:watch`** - Runs tests in watch mode (re-runs on file changes)
- **`test:coverage`** - Runs tests with coverage reporting
- **`test:pre-push`** - Runs tests before git push (used by git hooks)
- **`test:ci`** - Runs tests in CI environment
- **`test:e2e`** - Runs Playwright end-to-end tests with browser automation

## Mutation Testing

Run for comprehensive test quality analysis:

- **`test:mutation`** - Runs complete mutation testing suite
- **`test:mutation:security`** - Tests security middleware mutations
- **`test:mutation:core`** - Tests core model mutations
- **`test:mutation:ai`** - Tests AI provider mutations
- **`test:mutation:vector`** - Tests vector database provider mutations
- **`test:mutation:frontend`** - Tests frontend code mutations
- **`test:mutation:report`** - Generates detailed mutation testing reports
- **`test:mutation:ci`** - Runs mutation testing in CI environment

## Test Maintenance

Run for test environment management:

- **`test:cleanup`** - Cleans up test log files
- **`test:cleanup:stats`** - Shows test cleanup statistics
- **`test:validate`** - Validates test environment setup
- **`test:validate:fix`** - Fixes test environment issues automatically
- **`test:comprehensive`** - Runs comprehensive test suite with cleanup

## Common Workflows

### First-time Setup

```bash
npm run setup
```

### Daily Development

```bash
# Terminal 1
npm run dev:server

# Terminal 2
npm run dev:collector

# Terminal 3
npm run dev:frontend
```

### Before Committing

```bash
npm run lint
npm test
```

### Database Schema Changes

```bash
# Make changes to schema.prisma
npm run prisma:generate
npm run prisma:migrate
```

### Quality Assurance

```bash
npm run test:coverage
npm run test:mutation
npm run test:e2e
```

### Release Process

```bash
npm run version:bump:minor
npm run version:release
npm run version:tag
```
