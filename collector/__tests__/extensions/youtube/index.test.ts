import {
  fetchVideoTranscriptContent,
  loadYouTubeTranscript,
} from "../../../extensions/youtube";
import { YoutubeLoader } from "../../../extensions/youtube/YoutubeLoader";
import { writeToServerDocuments } from "../../../utils/files";
import { tokenizeString } from "../../../utils/tokenizer";
import fs from "fs";

// Mock dependencies
jest.mock("../../../extensions/youtube/YoutubeLoader");
jest.mock("../../../utils/files");
jest.mock("../../../utils/tokenizer");
jest.mock("fs");
jest.mock("../../../i18n", () => ({
  t: (key: string) => key,
}));

describe("YouTube Extension", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("fetchVideoTranscriptContent", () => {
    const mockDocs = [
      {
        pageContent: "Test transcript content",
        metadata: {
          source: "test123",
          title: "Test Video",
          author: "Test Author",
          description: "Test description",
        },
      },
    ];

    it("should return error for invalid YouTube URL", async () => {
      const result = await fetchVideoTranscriptContent({
        url: "https://example.com",
      });

      expect(result).toEqual({
        success: false,
        reason: "errors.sync.invalid-youtube-url",
        content: null,
        metadata: { source: "" },
      });
    });

    it("should fetch transcript successfully for valid YouTube URL", async () => {
      const mockLoader = {
        load: jest.fn().mockResolvedValue(mockDocs),
      };
      (YoutubeLoader.createFromUrl as jest.Mock).mockReturnValue(mockLoader);

      const result = await fetchVideoTranscriptContent({
        url: "https://www.youtube.com/watch?v=test123",
      });

      expect(result).toEqual({
        success: true,
        reason: null,
        content: "Test transcript content",
        metadata: mockDocs[0]?.metadata || {},
      });
    });

    it("should handle transcript fetch errors", async () => {
      const mockLoader = {
        load: jest.fn().mockRejectedValue(new Error("Network error")),
      };
      (YoutubeLoader.createFromUrl as jest.Mock).mockReturnValue(mockLoader);

      const result = await fetchVideoTranscriptContent({
        url: "https://www.youtube.com/watch?v=test123",
      });

      expect(result).toEqual({
        success: false,
        reason: "Network error",
        content: null,
        metadata: { source: "" },
      });
    });

    it("should handle empty transcript content", async () => {
      const emptyDocs = [{ pageContent: "", metadata: {} }];
      const mockLoader = {
        load: jest.fn().mockResolvedValue(emptyDocs),
      };
      (YoutubeLoader.createFromUrl as jest.Mock).mockReturnValue(mockLoader);

      const result = await fetchVideoTranscriptContent({
        url: "https://www.youtube.com/watch?v=test123",
      });

      expect(result).toEqual({
        success: false,
        reason: "errors.sync.transcript-parse-failed",
        content: null,
        metadata: { source: "" },
      });
    });
  });

  describe("loadYouTubeTranscript", () => {
    const mockTranscriptResult = {
      success: true,
      reason: null,
      content: "Test transcript content",
      metadata: {
        source: "test123",
        title: "Test Video",
        author: "Test Author",
        description: "Test description",
      },
    };

    beforeEach(() => {
      (fs.existsSync as jest.Mock).mockReturnValue(false);
      (fs.mkdirSync as jest.Mock).mockImplementation(() => {});
      (tokenizeString as jest.Mock).mockReturnValue(["token1", "token2"]);
      (writeToServerDocuments as jest.Mock).mockResolvedValue(undefined);
    });

    it("should handle transcript fetch failure", async () => {
      const mockLoader = {
        load: jest.fn().mockRejectedValue(new Error("Fetch failed")),
      };
      (YoutubeLoader.createFromUrl as jest.Mock).mockReturnValue(mockLoader);

      const result = await loadYouTubeTranscript({
        url: "https://www.youtube.com/watch?v=test123",
      });

      expect(result).toEqual({
        success: false,
        reason: "Fetch failed",
      });
    });

    it("should handle missing metadata gracefully", async () => {
      const mockLoader = {
        load: jest.fn().mockResolvedValue([
          {
            pageContent: "Test content",
            metadata: undefined,
          },
        ]),
      };
      (YoutubeLoader.createFromUrl as jest.Mock).mockReturnValue(mockLoader);

      const result = await loadYouTubeTranscript({
        url: "https://www.youtube.com/watch?v=test123",
      });

      // The implementation actually handles missing metadata by providing defaults
      expect(result).toEqual({
        success: true,
        reason: null,
        data: {
          title: "Untitled",
          author: "Unknown",
          destination: "undefined-youtube-transcripts",
        },
      });
    });

    it("should successfully load and save YouTube transcript", async () => {
      const mockLoader = {
        load: jest.fn().mockResolvedValue([
          {
            pageContent: mockTranscriptResult.content,
            metadata: mockTranscriptResult.metadata,
          },
        ]),
      };
      (YoutubeLoader.createFromUrl as jest.Mock).mockReturnValue(mockLoader);

      const result = await loadYouTubeTranscript({
        url: "https://www.youtube.com/watch?v=test123",
      });

      expect(result).toEqual({
        success: true,
        reason: null,
        data: {
          title: "Test Video",
          author: "Test Author",
          destination: "test-author-youtube-transcripts",
        },
      });

      expect(writeToServerDocuments).toHaveBeenCalledWith(
        expect.objectContaining({
          url: "https://www.youtube.com/watch?v=test123.youtube",
          title: "Test Video",
          docAuthor: "Test Author",
          description: "Test description",
          wordCount: 3,
          pageContent: "Test transcript content",
          token_count_estimate: 2,
        }),
        "Test Video",
        expect.stringMatching(/Test-Video-[a-f0-9-]+/),
        expect.any(String)
      );
    });

    it("should create output directory if it does not exist", async () => {
      const mockLoader = {
        load: jest.fn().mockResolvedValue([
          {
            pageContent: mockTranscriptResult.content,
            metadata: mockTranscriptResult.metadata,
          },
        ]),
      };
      (YoutubeLoader.createFromUrl as jest.Mock).mockReturnValue(mockLoader);

      await loadYouTubeTranscript({
        url: "https://www.youtube.com/watch?v=test123",
      });

      expect(fs.mkdirSync).toHaveBeenCalledWith(
        expect.stringContaining("test-author-youtube-transcripts"),
        { recursive: true }
      );
    });

    it("should handle development vs production environments correctly", async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "development";

      const mockLoader = {
        load: jest.fn().mockResolvedValue([
          {
            pageContent: mockTranscriptResult.content,
            metadata: mockTranscriptResult.metadata,
          },
        ]),
      };
      (YoutubeLoader.createFromUrl as jest.Mock).mockReturnValue(mockLoader);

      await loadYouTubeTranscript({
        url: "https://www.youtube.com/watch?v=test123",
      });

      expect(writeToServerDocuments).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(String),
        expect.any(String),
        expect.stringContaining("server/storage/documents")
      );

      process.env.NODE_ENV = originalEnv;
    });
  });
});
