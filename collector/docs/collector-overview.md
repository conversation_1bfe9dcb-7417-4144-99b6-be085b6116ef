# Collector Service Overview

## Introduction

The Collector service is a critical component of the ISTLegal system responsible for ingesting and processing documents from various sources. It converts different file types into a standardized format that can be embedded and used for Retrieval-Augmented Generation (RAG).

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Key Components](#key-components)
3. [Supported File Types](#supported-file-types)
4. [Processing Flow](#processing-flow)
5. [Extension System](#extension-system)
6. [Integration with Server](#integration-with-server)
7. [Configuration](#configuration)

## Architecture Overview

The Collector service is designed as a standalone service that communicates with the main server. It follows a modular architecture with specialized converters for different file types and sources.

```text
collector/
├── index.ts                 # Main entry point
├── processSingleFile/       # File processing logic
│   ├── index.ts             # Orchestration
│   └── convert/             # File type converters
│       ├── asAudio.ts       # Audio file converter
│       ├── asDocx.ts        # DOCX converter
│       ├── asEPub.ts        # EPUB converter
│       ├── asMbox.ts        # MBOX converter
│       ├── asOfficeMime.ts  # Office MIME converter
│       ├── asPDF/           # PDF converter module
│       ├── asTxt.ts         # Text file converter
│       └── asXlsx.ts        # XLSX converter
├── processLink/             # Link processing logic
│   ├── index.ts             # Orchestration
│   └── convert/             # Link type converters
│       └── generic.ts       # Generic link converter
├── processRawText/          # Raw text processing
│   └── index.ts             # Text processing logic
├── extensions/              # Extension system
│   ├── index.ts             # Extension registration
│   └── resync/              # Resync functionality
│       └── index.ts         # Resync logic
├── middleware/              # Express middleware
│   ├── index.ts             # Middleware exports
│   ├── setDataSigner.ts     # Data signing middleware
│   ├── validateResyncRequest.ts  # Resync validation
│   └── verifyIntegrity.ts   # Request integrity verification
├── i18n/                    # Internationalization
│   └── index.ts             # i18n setup
├── locales/                 # Translation files
│   ├── index.ts             # Locale exports
│   ├── types.ts             # Locale type definitions
│   └── [de|en|fr|no|pl|rw|sv]/  # Language folders
│       └── collector.ts     # Translations
├── src/types/               # TypeScript type definitions
│   ├── index.d.ts           # Main type definitions
│   ├── environment.d.ts     # Environment types
│   └── modules.d.ts         # Module declarations
├── utils/                   # Shared utilities
│   ├── files/               # File operations
│   │   ├── index.ts         # File utilities
│   │   └── mime.ts          # MIME type handling
│   ├── tokenizer/           # Text tokenization
│   │   └── index.ts         # Tokenizer implementation
│   ├── comKey/              # Communication key
│   │   └── index.ts         # Key management
│   ├── EncryptionWorker/    # Encryption utilities
│   │   └── index.ts         # Encryption implementation
│   ├── OCRLoader/           # OCR functionality
│   │   ├── index.ts         # OCR loader
│   │   └── validLangs.ts    # Supported OCR languages
│   ├── WhisperProviders/    # Audio transcription
│   │   ├── index.ts         # Provider exports
│   │   ├── OpenAiWhisper.ts # OpenAI Whisper
│   │   └── localWhisper.ts  # Local Whisper
│   ├── http/                # HTTP utilities
│   │   └── index.ts         # HTTP helpers
│   ├── logger/              # Logging
│   │   └── index.ts         # Logger setup
│   ├── url/                 # URL utilities
│   │   └── index.ts         # URL helpers
│   ├── constants.ts         # Application constants
│   └── envWatcher.ts        # Environment variable watcher
├── dist/                    # Compiled JavaScript output
├── tsconfig.json            # TypeScript configuration
└── package.json             # Package configuration
```

## Key Components

### 1. File Processing (`processSingleFile/`)

The file processing module handles the conversion of uploaded files into a standardized text format:

- **Orchestration** (`index.ts`): Manages the overall file processing flow with type-safe interfaces
- **Converters** (`convert/*.ts`): Strongly-typed converters for different file types
- **Chunking**: Splits large documents into manageable chunks
- **Metadata Extraction**: Extracts and preserves metadata from files

### 2. Link Processing (`processLink/`)

The link processing module handles the extraction of content from external URLs:

- **Orchestration** (`index.ts`): Manages the link processing flow with type safety
- **Converters** (`convert/*.ts`): Type-safe converters for different link types
- **Web Scraping**: Extracts content from web pages
- **API Integration**: Connects to external APIs for content retrieval

### 3. Raw Text Processing (`processRawText/`)

The raw text processing module handles direct text input:

- **Text Processing** (`index.ts`): Processes raw text input with TypeScript interfaces
- **Formatting**: Applies consistent formatting to raw text
- **Chunking**: Splits large text inputs into manageable chunks

### 4. Extension System (`extensions/`)

The extension system provides specialized connectors for different data sources:

- **Resync** (`resync/index.ts`): Handles document resyncing for watched sources
- **Legacy Extensions** (in `utils/extensions/`): GitHub, YouTube, Confluence, and Website crawlers (still in JavaScript, pending migration)

### 5. Utilities (`utils/`)

The utilities module provides shared functionality across the Collector service:

- **File Operations** (`files/index.ts`, `mime.ts`): Type-safe file operations and MIME handling
- **Tokenization** (`tokenizer/index.ts`): Text tokenization with TypeScript interfaces
- **Communication Key** (`comKey/index.ts`): Secure communication with type safety
- **Encryption** (`EncryptionWorker/index.ts`): Type-safe encryption utilities
- **OCR** (`OCRLoader/`): Optical character recognition with language support
- **Audio Transcription** (`WhisperProviders/`): Multiple providers for audio-to-text
- **HTTP Utilities** (`http/index.ts`): Type-safe HTTP request handling
- **Logging** (`logger/index.ts`): Structured logging with TypeScript
- **URL Utilities** (`url/index.ts`): URL parsing and validation

## Supported File Types

The Collector service supports a wide range of file types:

### Document Files

- PDF (`.pdf`)
- Microsoft Word (`.docx`, `.doc`)
- Microsoft Excel (`.xlsx`, `.xls`)
- Microsoft PowerPoint (`.pptx`, `.ppt`)
- Text files (`.txt`, `.md`, `.csv`)
- Rich Text Format (`.rtf`)
- HTML (`.html`, `.htm`)
- XML (`.xml`)
- JSON (`.json`)
- EPUB (`.epub`)

### Email Files

- MBOX (`.mbox`)
- EML (`.eml`)

### Media Files

- Audio (`.mp3`, `.wav`, `.m4a`, `.flac`) - transcribed to text
- Video (`.mp4`, `.avi`, `.mov`) - audio extracted and transcribed

### Code Files

- Various programming languages (`.js`, `.py`, `.java`, `.cpp`, etc.)

### Archive Files

- ZIP (`.zip`) - contents are extracted and processed
- TAR (`.tar`, `.tar.gz`, `.tgz`) - contents are extracted and processed

## Processing Flow

The document processing flow follows these steps:

1. **Request Validation**
   - Verify request integrity
   - Validate file type and size
   - Check for required parameters

2. **File Conversion**
   - Select appropriate converter based on file type
   - Extract text content from the file
   - Preserve document structure when possible
   - Extract metadata (title, author, creation date, etc.)

3. **Text Processing**
   - Clean and normalize text
   - Remove irrelevant content (e.g., headers, footers)
   - Handle special characters and encoding issues

4. **Chunking**
   - Split large documents into manageable chunks
   - Maintain context across chunk boundaries
   - Balance chunk size for optimal embedding

5. **Metadata Enrichment**
   - Add processing metadata (timestamp, processor version, etc.)
   - Calculate token counts for each chunk
   - Generate unique identifiers for chunks

6. **Output Generation**
   - Create standardized JSON output
   - Include both content and metadata
   - Prepare for storage and embedding

## Extension System

The extension system allows the Collector to process specialized data sources:

### GitHub Extension

Processes GitHub repositories and files:

- Clone repositories
- Extract file content
- Process markdown files
- Handle code files with appropriate syntax highlighting
- Support for specific branches and directories

### YouTube Extension

Processes YouTube video transcripts:

- Extract video metadata
- Retrieve transcripts (auto-generated or manual)
- Process transcripts into text chunks
- Preserve timestamp information

### Resync Extension

Handles document resyncing for watched sources:

- Check for updates to previously processed sources
- Process only changed content
- Maintain version history
- Support for scheduled resyncing

## Integration with Server

The Collector service integrates with the main server through:

1. **API Endpoints**
   - `/process`: Process a single file
   - `/process-link`: Process content from a URL
   - `/process-text`: Process raw text input
   - `/extensions/*`: Specialized extension endpoints

2. **Communication Protocol**
   - REST API for command and control
   - Secure communication using shared keys
   - File transfer via multipart/form-data
   - JSON response format

3. **Error Handling**
   - Standardized error responses
   - Detailed error information for debugging
   - Graceful failure handling

## Configuration

The Collector service is configured through:

1. **Environment Variables**
   - `PORT`: Service port (default: 8888)
   - `HOST`: Service host (default: localhost)
   - `MAX_FILE_SIZE`: Maximum file size in bytes
   - `TEMP_DIR`: Directory for temporary files
   - `OUTPUT_DIR`: Directory for processed output
   - `LOG_LEVEL`: Logging verbosity

2. **Runtime Configuration**
   - Converter-specific settings
   - Extension-specific settings
   - Processing parameters

3. **Security Configuration**
   - Communication keys
   - Encryption settings
   - Access control
