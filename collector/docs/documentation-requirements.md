# Documentation Requirements

This document outlines the documentation requirements for the ISTLegal project, with a specific focus on the Collector service. All team members are expected to follow these guidelines when making changes to the codebase.

## Table of Contents

- [Documentation Requirements](#documentation-requirements)
  - [Table of Contents](#table-of-contents)
  - [Documentation Locations](#documentation-locations)
  - [When to Update Documentation](#when-to-update-documentation)
  - [Documentation Standards](#documentation-standards)
  - [Commit Process](#commit-process)
  - [Documentation Types](#documentation-types)
  - [Enforcement](#enforcement)

## Documentation Locations

The ISTLegal project maintains documentation in three primary locations:

1. **Frontend Documentation** (`frontend/docs/`)
   - Documentation for frontend architecture, components, and workflows
   - User interface guidelines and patterns
   - Frontend-specific implementation details

2. **Server Documentation** (`server/docs/`)
   - Documentation for server architecture, endpoints, and data models
   - API specifications and usage examples
   - Backend-specific implementation details

3. **Collector Documentation** (`collector/docs/`)
   - Documentation for collector architecture and components
   - File processing and link processing details
   - Extension system and integration with the server

4. **Cursor Project Rules** (`.cursor/rules/`)
   - Development guidelines and patterns
   - Best practices for specific areas of the codebase
   - Rules for AI-assisted development

## When to Update Documentation

Documentation must be updated in the following scenarios:

1. **Adding New Features**
   - Create new documentation files for significant new features
   - Update existing documentation to reference new features where relevant
   - Document new file converters, link handlers, or extensions

2. **Modifying Existing Features**
   - Update documentation to reflect changes in behavior or API
   - Remove or update outdated information
   - Document changes to processing flow or output format

3. **Fixing Bugs**
   - Update documentation if the bug fix changes expected behavior
   - Add notes about edge cases or limitations if relevant
   - Document workarounds for known issues

4. **Refactoring Code**
   - Update documentation if the refactoring changes public APIs or behavior
   - Update architecture documentation if the refactoring changes system structure
   - Document performance improvements or changes in resource usage

5. **Changing Development Patterns**
   - Update Cursor project rules to reflect new development patterns
   - Add new rule files for significant new patterns or guidelines
   - Document changes to coding standards or best practices

## Documentation Standards

All documentation should follow these standards:

1. **Format**
   - Use Markdown for all documentation
   - Follow consistent heading structure (# for title, ## for sections, etc.)
   - Use code blocks with appropriate language tags (`javascript,`json, etc.)
   - Include diagrams where appropriate (using Mermaid syntax when possible)

2. **Content**
   - Start with a clear title and brief overview
   - Include a table of contents for longer documents
   - Provide code examples for complex concepts
   - Include diagrams for architecture and flow documentation

3. **Style**
   - Use clear, concise language
   - Avoid jargon and acronyms without explanation
   - Use present tense and active voice
   - Be consistent with terminology

4. **Maintenance**
   - Keep documentation up-to-date with code changes
   - Remove outdated information
   - Update examples to reflect current API and behavior
   - Review documentation regularly for accuracy

## Commit Process

The documentation update process should be integrated into your development workflow:

1. **Plan Documentation Updates**
   - Identify which documentation needs to be updated before making code changes
   - Consider creating or updating documentation first to clarify your approach

2. **Update Documentation With Code**
   - Include documentation updates in the same commit as code changes
   - If documentation changes are extensive, consider a separate follow-up commit

3. **Review Documentation**
   - Ensure documentation accurately reflects the code changes
   - Check for clarity, completeness, and correctness
   - Verify that examples work as described

4. **Commit Message**
   - Include "Updated docs" or similar in your commit message
   - Specify which documentation areas were updated
   - Example: "Added new PDF converter and updated collector/docs/file-processing.md"

## Documentation Types

Different types of changes require different documentation approaches:

1. **Feature Documentation**
   - Explain what the feature does
   - Provide usage examples
   - Document configuration options
   - Describe integration with other features

2. **API Documentation**
   - Document endpoints, parameters, and return values
   - Provide request and response examples
   - Document error cases and handling
   - Explain authentication and authorization requirements

3. **Architecture Documentation**
   - Describe system components and their interactions
   - Explain data flow and processing
   - Document design decisions and trade-offs
   - Include diagrams for complex systems

4. **Development Guidelines**
   - Document coding standards and patterns
   - Explain best practices for specific areas
   - Provide examples of correct implementation
   - Document common pitfalls and how to avoid them

## Enforcement

The documentation update requirement is enforced through:

1. **Pre-commit Hook**
   - Reminds developers to update documentation when making code changes
   - Can be bypassed for temporary commits with `--no-verify`

2. **Code Review**
   - Reviewers should check for documentation updates
   - PRs without appropriate documentation updates should be returned for revision

3. **Cursor Project Rules**
   - The `documentation-updates.mdc` rule is always applied
   - Provides guidance to developers and AI assistants

Remember: Documentation is not an afterthought—it's an integral part of the development process. Keeping documentation current is everyone's responsibility.
