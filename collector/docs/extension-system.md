# Extension System in Collector

## Overview

The extension system in the Collector service provides specialized connectors for different data sources, enabling the processing of content from various platforms and services. This document provides a detailed explanation of how the extension system works and how to implement new extensions.

## Table of Contents

1. [Architecture](#architecture)
2. [Extension Registration](#extension-registration)
3. [Core Extensions](#core-extensions)
4. [Extension API](#extension-api)
5. [Implementing New Extensions](#implementing-new-extensions)
6. [Extension Configuration](#extension-configuration)
7. [Security Considerations](#security-considerations)
8. [Best Practices](#best-practices)

## Architecture

The extension system is organized as follows:

```text
extensions/
├── index.ts                 # Extension registration and routing (TypeScript)
└── resync/                  # Resync extension
    └── index.ts             # Main resync extension logic (TypeScript)

utils/extensions/            # Legacy extensions (JavaScript - pending migration)
├── Confluence/              # Confluence extension
│   ├── index.js             # Main Confluence extension logic
│   └── ConfluenceLoader/
│       └── index.js         # Confluence content loader
├── RepoLoader/              # Repository loaders
│   ├── index.ts             # Repository loader exports (TypeScript)
│   ├── GithubRepo/          # GitHub repository loader
│   │   ├── index.js         # GitHub extension entry
│   │   └── RepoLoader/
│   │       └── index.js     # GitHub repository processor
│   └── GitlabRepo/          # GitLab repository loader
│       ├── index.js         # GitLab extension entry
│       └── RepoLoader/
│           └── index.js     # GitLab repository processor
├── WebsiteDepth/            # Website crawler extension
│   └── index.js             # Website depth crawler
└── YoutubeTranscript/       # YouTube extension
    ├── index.js             # YouTube extension entry
    └── YoutubeLoader/
        ├── index.js         # YouTube content loader
        └── youtube-transcript.js  # Transcript extraction
```

## Extension Registration

Extensions are registered in the main `extensions/index.ts` file:

```typescript
// extensions/index.ts
import { Router } from "express";
import resyncExtension from "./resync";

const router = Router();

// Register TypeScript extensions
router.use("/resync", resyncExtension);

// Legacy extensions are loaded separately from utils/extensions/
// and mounted in the main application

export default router;
```

**Note**: Legacy extensions (GitHub, YouTube, Confluence, Website crawler) are currently located in `utils/extensions/` and are still in JavaScript. They are pending migration to TypeScript and will be moved to the main extensions directory.

This router is then mounted in the main Collector application:

```typescript
// index.ts
import express from "express";
import extensions from "./extensions";

const app = express();

// Mount extensions router
app.use("/extensions", extensions);

// Legacy extensions are mounted separately
// (handled in the main application setup)
```

## Core Extensions

The Collector includes several core extensions:

### GitHub Extension

The GitHub extension processes content from GitHub repositories:

- **Capabilities**:
  - Clone entire repositories
  - Process individual files or directories
  - Handle different branches and tags
  - Process README files and documentation
  - Extract code with proper syntax highlighting

- **Endpoints**:
  - `POST /extensions/github/repo`: Process a GitHub repository
  - `POST /extensions/github/file`: Process a specific file from GitHub
  - `POST /extensions/github/directory`: Process a specific directory from GitHub

- **Authentication**:
  - Supports GitHub personal access tokens
  - Handles public and private repositories
  - Respects GitHub API rate limits

### YouTube Extension

The YouTube extension processes content from YouTube videos:

- **Capabilities**:
  - Extract video metadata (title, description, etc.)
  - Retrieve video transcripts
  - Process transcript with timestamp information
  - Handle multiple languages
  - Support for playlists and channels

- **Endpoints**:
  - `POST /extensions/youtube/video`: Process a YouTube video
  - `POST /extensions/youtube/playlist`: Process a YouTube playlist
  - `POST /extensions/youtube/channel`: Process a YouTube channel

- **Authentication**:
  - Supports YouTube API keys
  - Handles public and private videos (with proper authentication)
  - Respects YouTube API quotas and rate limits

### Confluence Extension

The Confluence extension processes content from Confluence pages and spaces:

- **Capabilities**:
  - Extract page content and metadata
  - Process attachments and embedded content
  - Handle page hierarchies and relationships
  - Support for spaces and collections
  - Extract comments and page history

- **Endpoints**:
  - `POST /extensions/confluence/page`: Process a Confluence page
  - `POST /extensions/confluence/space`: Process a Confluence space
  - `POST /extensions/confluence/search`: Process Confluence search results

- **Authentication**:
  - Supports Confluence API tokens
  - Handles basic authentication
  - Respects Confluence API rate limits

### Resync Extension

The resync extension handles document resyncing for watched sources:

- **Capabilities**:
  - Check for updates to previously processed sources
  - Process only changed content
  - Maintain version history
  - Support for scheduled resyncing
  - Handle different source types (GitHub, YouTube, Confluence, etc.)

- **Endpoints**:
  - `POST /extensions/resync/check`: Check for updates to a source
  - `POST /extensions/resync/process`: Process updates to a source
  - `POST /extensions/resync/schedule`: Schedule regular resyncing for a source

- **Scheduling**:
  - Supports different resyncing intervals
  - Handles one-time and recurring schedules
  - Provides status updates and notifications

## Extension API

Each extension implements a standard API:

### 1. Route Handler

```typescript
// extensions/example/index.ts
import { Router, Request, Response } from "express";
import { processContent } from "./processor";
import { ExtensionRequest, ExtensionResponse } from "../../src/types";

const router = Router();

// Define routes with proper typing
router.post(
  "/process",
  async (
    req: Request<{}, {}, ExtensionRequest>,
    res: Response<ExtensionResponse>
  ) => {
    try {
      const result = await processContent(req.body);
      res.json(result);
    } catch (error) {
      res.status(500).json({
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }
);

export default router;
```

### 2. Processor

```typescript
// extensions/example/processor.ts
import { fetchContent } from "./api";
import { ProcessedDocument, ExtensionRequest } from "../../src/types";

export async function processContent(
  params: ExtensionRequest
): Promise<ProcessedDocument> {
  // Validate parameters
  if (!params.url) {
    throw new Error("URL is required");
  }

  // Fetch content
  const content = await fetchContent(params.url);

  // Process content
  const chunks = splitIntoChunks(content);

  // Return result with proper typing
  return {
    metadata: {
      source_url: params.url,
      processed_at: new Date().toISOString(),
      source_type: "extension",
      language: "en",
      processor_version: "1.0.0",
      token_count_estimate: calculateTokenCount(content),
    },
    chunks,
  };
}

function splitIntoChunks(content: string): ProcessedDocument["chunks"] {
  // Process content and split into chunks
  // Implementation here...
  return [];
}

function calculateTokenCount(content: string): number {
  // Token counting logic
  return content.split(" ").length * 1.3; // Rough estimate
}
```

### 3. API Integration

```typescript
// extensions/example/api.ts
import axios from "axios";

interface CacheInterface {
  get(key: string): Promise<string | null>;
  set(key: string, value: string): Promise<void>;
}

interface RateLimiter {
  acquire(): Promise<void>;
  release(): void;
}

// These would be properly imported from utils
declare const cache: CacheInterface;
declare const rateLimiter: RateLimiter;

export async function fetchContent(url: string): Promise<string> {
  // Check cache
  const cachedContent = await cache.get(url);
  if (cachedContent) {
    return cachedContent;
  }

  // Apply rate limiting
  await rateLimiter.acquire();

  try {
    // Fetch content with proper error handling
    const response = await axios.get<string>(url, {
      timeout: 30000,
      headers: {
        "User-Agent": "ISTLegal-Collector/1.0",
      },
    });

    const content = response.data;

    // Cache content
    await cache.set(url, content);

    return content;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(`Failed to fetch content: ${error.message}`);
    }
    throw error;
  } finally {
    // Release rate limiter
    rateLimiter.release();
  }
}
```

## Implementing New Extensions

To implement a new extension:

### 1. Create Extension Directory

Create a new directory in the `extensions` folder:

```text
extensions/
└── new-extension/
    ├── index.ts             # Main extension logic
    ├── api.ts               # API integration
    ├── processor.ts         # Content processor
    └── types.ts             # Extension-specific types
```

### 2. Implement Extension Logic

Implement the extension logic in the files:

- `index.js`: Define routes and handle requests
- `api.js`: Integrate with external APIs
- `processor.js`: Process content and generate output

### 3. Register Extension

Register the extension in `extensions/index.ts`:

```typescript
import newExtension from "./new-extension";
router.use("/new-extension", newExtension);
```

### 4. Test Extension

Test the extension with sample requests:

```bash
curl -X POST http://localhost:8888/extensions/new-extension/process \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com/resource"}'
```

## Extension Configuration

Extensions can be configured through:

### 1. Environment Variables

```env
# .env
NEW_EXTENSION_API_KEY=your-api-key
NEW_EXTENSION_BASE_URL=https://api.example.com
NEW_EXTENSION_TIMEOUT=30000
```

### 2. Configuration Files

```typescript
// extensions/new-extension/config.ts
interface ExtensionConfig {
  apiKey: string;
  baseUrl: string;
  timeout: number;
}

export const config: ExtensionConfig = {
  apiKey: process.env.NEW_EXTENSION_API_KEY || "",
  baseUrl: process.env.NEW_EXTENSION_BASE_URL || "https://api.example.com",
  timeout: parseInt(process.env.NEW_EXTENSION_TIMEOUT || "30000", 10),
  // Additional configuration...
};
```

### 3. Runtime Configuration

```typescript
// extensions/new-extension/index.ts
interface ConfigureRequest {
  apiKey?: string;
  baseUrl?: string;
  timeout?: number;
}

router.post(
  "/configure",
  (req: Request<{}, {}, ConfigureRequest>, res: Response) => {
    const { apiKey, baseUrl, timeout } = req.body;

    // Update configuration with validation
    if (apiKey) config.apiKey = apiKey;
    if (baseUrl) config.baseUrl = baseUrl;
    if (timeout && timeout > 0) config.timeout = timeout;

    res.json({ success: true, config });
  }
);
```

## Security Considerations

When implementing extensions, consider these security aspects:

### 1. Authentication

- Securely store API keys and tokens
- Use environment variables for sensitive information
- Implement proper token rotation and management
- Use the principle of least privilege

### 2. Rate Limiting

- Respect API rate limits
- Implement backoff strategies
- Cache results when appropriate
- Monitor usage and quotas

### 3. Input Validation

- Validate all input parameters
- Sanitize URLs and other inputs
- Implement proper error handling
- Prevent injection attacks

### 4. Output Sanitization

- Sanitize content before processing
- Remove potentially harmful content
- Validate output before returning
- Handle sensitive information appropriately

## Best Practices

Follow these best practices when working with extensions:

### 1. Modular Design

- Keep extensions modular and focused
- Separate concerns (routing, API integration, processing)
- Reuse common utilities
- Follow consistent patterns across extensions

### 2. Error Handling

- Implement comprehensive error handling
- Provide meaningful error messages
- Log errors for debugging
- Fail gracefully when possible

### 3. Performance Optimization

- Implement caching for API responses
- Use streaming for large content
- Process content in chunks
- Optimize resource usage

### 4. Documentation

- Document extension capabilities and limitations
- Provide usage examples
- Document configuration options
- Keep documentation up-to-date
