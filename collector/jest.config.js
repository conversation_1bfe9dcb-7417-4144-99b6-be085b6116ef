module.exports = {
  preset: "ts-jest",
  testEnvironment: "node",
  roots: ["<rootDir>"],
  testMatch: ["**/__tests__/**/*.+(ts|tsx)", "**/?(*.)+(spec|test).+(ts|tsx)"],
  testPathIgnorePatterns: [
    "/node_modules/",
    "/dist/",
    "/hotdir/",
    "/storage/",
    "processRawText/test.ts"
  ],
  testPathIgnorePatterns: [
    "/node_modules/",
    "/dist/",
    "/hotdir/",
    "/storage/",
    "processRawText/test.ts"
  ],
  transform: {
    "^.+\\.(ts|tsx)$": [
      "ts-jest",
      {
        tsconfig: {
          esModuleInterop: true,
          allowSyntheticDefaultImports: true
        }
      }
    ]
  },
  collectCoverageFrom: [
    "**/*.{js,ts}",
    "!**/*.d.ts",
    "!**/node_modules/**",
    "!**/dist/**",
    "!**/coverage/**",
    "!jest.config.js",
    "!**/hotdir/**",
    "!**/storage/**"
  ],
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/$1",
    "^@utils/(.*)$": "<rootDir>/utils/$1",
    "^@middleware/(.*)$": "<rootDir>/middleware/$1",
    "^@extensions/(.*)$": "<rootDir>/extensions/$1"
  },
  setupFilesAfterEnv: ["<rootDir>/jest.setup.js"],
  testTimeout: 30000
}
