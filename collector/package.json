{"name": "ist-legal-document-collector", "version": "1.1.3", "description": "Document collector server endpoints", "main": "dist/index.js", "author": "IST Legal", "license": "Copyright", "private": false, "engines": {"node": ">=18.12.1"}, "scripts": {"dev:collector": "cd collector && npm run dev", "dev": "nodemon --ignore hotdir --ignore storage --exec ts-node index.ts", "dev:ts": "cross-env NODE_ENV=development nodemon --ignore hotdir --ignore storage --exec \"node --trace-warnings\" dist/index.js", "build": "rm -rf dist && ./node_modules/.bin/tsc", "build:watch": "tsc --watch", "start": "cross-env NODE_ENV=production node dist/index.js", "start:js": "cross-env NODE_ENV=production node index.js", "typecheck": "tsc --noEmit", "lint": "npx prettier --write . && npx eslint . --fix && npm run typecheck", "prod": "node index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:verbose": "jest --verbose"}, "dependencies": {"@langchain/community": "^0.2.23", "@xenova/transformers": "^2.11.0", "bcrypt": "^5.1.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "cross-env": "^7.0.3", "dotenv": "^16.0.3", "epub2": "^3.0.2", "express": "^4.18.2", "fluent-ffmpeg": "^2.1.2", "gm": "^1.25.0", "html-to-text": "^9.0.5", "i18next": "^24.2.0", "ignore": "^5.3.0", "js-tiktoken": "^1.0.8", "langchain": "0.1.36", "mammoth": "^1.6.0", "mbox-parser": "^1.0.1", "mime": "^3.0.0", "moment": "^2.29.4", "node-html-parser": "^6.1.13", "node-xlsx": "^0.24.0", "officeparser": "^4.0.5", "openai": "4.38.5", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "puppeteer": "~21.5.2", "sharp": "^0.33.5", "slugify": "^1.6.6", "tesseract.js": "^5.1.1", "tmp": "^0.2.3", "url-pattern": "^1.0.3", "uuid": "^9.0.0", "wavefile": "^11.0.0", "winston": "^3.13.0", "youtubei.js": "^9.1.0"}, "devDependencies": {"@jest/globals": "^30.0.4", "@types/bcrypt": "^5.0.2", "@types/body-parser": "^1.19.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/fluent-ffmpeg": "^2.1.27", "@types/gm": "^1.25.4", "@types/html-to-text": "^9.0.4", "@types/jest": "^30.0.0", "@types/mime-types": "^3.0.1", "@types/node": "^24.0.8", "@types/pdf-parse": "^1.1.5", "@types/tmp": "^0.2.6", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "babel-jest": "^30.0.4", "concurrently": "^9.2.0", "eslint": "^9.25.1", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-unused-imports": "^4.1.4", "jest": "^30.0.4", "nodemon": "^2.0.22", "prettier": "^3.5.3", "rimraf": "^6.0.1", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}