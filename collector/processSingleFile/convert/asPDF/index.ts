import { v4 } from "uuid";
import fs from "fs";
import { promises as fsPromises } from "fs";
import path from "path";
const pdfParse = require("pdf-parse");
// import sharp from "sharp"; // Commented out - only used in convertPDFPageToImage which is currently disabled
// const gm = require("gm"); // Commented out - only used in convertPDFPageToImage which is currently disabled
import {
  writeToServerDocuments,
  removeExtension,
  trashFile,
  createdDate,
} from "../../../utils/files";
import { tokenizeString } from "../../../utils/tokenizer";
import slugify from "slugify";
// import tmp from "tmp"; // Commented out - only used in convertPDFPageToImage which is currently disabled
import OCRLoader from "../../../utils/OCRLoader";
import PDFLoader from "./PDFLoader";
import type { ProcessingResult, ProcessedDocument } from "../../../src/types";

// Configure gm to use ImageMagick
// const gmWithImageMagick = gm.subClass({ imageMagick: true }); // Commented out - only used in convertPDFPageToImage which is currently disabled

interface AsPdfOptions {
  fullFilePath: string;
  filename: string;
  folderName?: string;
  options?: {
    ocr?: {
      langList?: string;
    };
  };
}

interface PDFDocument {
  pageContent: string;
  metadata: {
    source: string;
    pdf?: {
      version: string;
      info?: any;
      metadata?: any;
      totalPages: number;
    };
    loc?: {
      pageNumber: number;
    };
  };
}

interface PDFParseResult {
  text?: string;
  info?: Record<string, any>;
  metadata?: {
    pdf?: {
      info?: Record<string, any>;
    };
  };
}

interface CopyResult {
  success: boolean;
  size: number;
  path: string;
}

interface DocumentData {
  id: string;
  url: string;
  title: string;
  docAuthor: string;
  description: string;
  docSource: string;
  chunkSource: string;
  published: string;
  wordCount: number;
  pageContent: string;
  token_count_estimate: number;
  pdfPath?: string;
  serverPdfPath?: string;
  pdfCopySuccess?: boolean;
  pdfCopyError?: string;
  originalFilePath?: string;
  frontendPath?: string;
  frontendHotdirCopySuccess?: boolean;
  frontendHotdirCopyError?: string;
  pdfCreateError?: string;
  location?: string;
}

// Utility function to get the appropriate PDF storage path with enhanced environment detection
function getPdfStoragePath(): string {
  const possiblePaths: string[] = [
    // New server pdfview path in documents folder
    path.resolve(__dirname, "../../../../server/storage/documents/pdfview"),

    // Legacy paths for backward compatibility
    ...(process.env.NODE_ENV === "production"
      ? [path.resolve("/app/server/public/hotdir")]
      : []),
    ...(process.env.STORAGE_DIR
      ? [path.resolve(process.env.STORAGE_DIR, "hotdir")]
      : []),
    ...(process.env.PDF_STORAGE_PATH
      ? [path.resolve(process.env.PDF_STORAGE_PATH)]
      : []),
    ...(process.env.FRONTEND_HOTDIR
      ? [path.resolve(process.env.FRONTEND_HOTDIR)]
      : []),
    path.resolve(__dirname, "../../../../frontend/public/hotdir"),
    path.resolve(__dirname, "../../../../server/public/hotdir"),
    path.resolve(__dirname, "../../../storage/hotdir"),
  ];

  // Log all paths we're checking (helpful for debugging)
  console.log("Checking PDF storage paths:", possiblePaths);

  // First try to find an existing path
  for (const pathToCheck of possiblePaths) {
    if (fs.existsSync(pathToCheck)) {
      console.log("Found valid PDF storage path:", pathToCheck);
      return pathToCheck;
    }
  }

  // If no paths exist, try to create them
  for (const pathToCreate of possiblePaths) {
    try {
      fs.mkdirSync(pathToCreate, { recursive: true });
      console.log("Created PDF storage path:", pathToCreate);
      return pathToCreate;
    } catch (err: any) {
      console.warn(`Failed to create path ${pathToCreate}:`, err.message);
      continue;
    }
  }

  console.error(
    "No valid PDF storage paths found. Checked paths:",
    possiblePaths
  );
  throw new Error(
    "No valid PDF storage path found. Please ensure at least one storage location exists."
  );
}

// Initialize the PDF storage path
const PDF_STORAGE_PATH = getPdfStoragePath();
console.log(`Using PDF storage path: ${PDF_STORAGE_PATH}`);

// Note: This function is preserved for potential future use
// async function convertPDFPageToImage(pdfPath: string, pageNum: number): Promise<Buffer> {
//   try {
//     const tempFile = tmp.fileSync({ postfix: ".png" });
//
//     await new Promise<void>((resolve, reject) => {
//       gmWithImageMagick(`${pdfPath}[${pageNum}]`)
//         .density(300, 300)
//         .quality(100)
//         .strip()
//         .write(tempFile.name, (err: Error | null) => {
//           if (err) reject(err);
//           else resolve();
//         });
//     });
//
//     const imageBuffer = await sharp(tempFile.name)
//       .greyscale()
//       .normalize()
//       .sharpen()
//       .toBuffer();
//
//     tempFile.removeCallback();
//     return imageBuffer;
//   } catch (error) {
//     console.error("Error converting page:", error);
//     throw error;
//   }
// }

// File copying function with retry mechanism
const copyFileWithRetry = (
  src: string,
  dest: string,
  maxRetries: number = 3,
  delayMs: number = 1000
): Promise<CopyResult> => {
  return new Promise((resolve, reject) => {
    const attemptCopy = (retriesLeft: number): void => {
      try {
        // Step 1: Validate source file
        if (!fs.existsSync(src)) {
          throw new Error(`Source file doesn't exist: ${src}`);
        }

        // Validate source file is readable and is a PDF
        const srcStats = fs.statSync(src);
        if (srcStats.size === 0) {
          throw new Error(`Source file is empty: ${src}`);
        }

        // Optional: Check file headers for PDF signature
        const isPDF = ((): boolean => {
          try {
            // Read first 5 bytes to check for PDF signature
            const fd = fs.openSync(src, "r");
            const buffer = Buffer.alloc(5);
            fs.readSync(fd, buffer, 0, 5, 0);
            fs.closeSync(fd);
            return buffer.toString() === "%PDF-";
          } catch (err: any) {
            console.warn(`Could not validate PDF signature: ${err.message}`);
            // Continue anyway even if we can't validate the signature
            return true;
          }
        })();

        if (!isPDF) {
          console.warn(
            `Warning: File doesn't appear to be a valid PDF: ${src}`
          );
          // Continue anyway but log the warning
        }

        // Step 2: Ensure destination directory exists
        const destDir = path.dirname(dest);
        if (!fs.existsSync(destDir)) {
          fs.mkdirSync(destDir, { recursive: true });
          console.log(`Created destination directory: ${destDir}`);
        }

        // Step 3: Perform the copy
        fs.copyFileSync(src, dest);

        // Step 4: Verify the file was copied correctly
        if (fs.existsSync(dest)) {
          const destStats = fs.statSync(dest);

          if (srcStats.size !== destStats.size) {
            throw new Error(
              `File size mismatch after copy - Source: ${srcStats.size}, Destination: ${destStats.size}`
            );
          }

          // Step 5: Validate file can be accessed as expected
          // This is similar to how DocumentHighlight checks PDF accessibility
          try {
            // Try to open the file to make sure it's not corrupt
            const fd = fs.openSync(dest, "r");
            fs.closeSync(fd);
          } catch (accessErr: any) {
            throw new Error(
              `Copied file exists but cannot be accessed: ${accessErr.message}`
            );
          }

          console.log(
            `Successfully copied file to ${dest} (${destStats.size} bytes)`
          );
          resolve({
            success: true,
            size: destStats.size,
            path: dest,
          });
        } else {
          throw new Error(`Destination file wasn't created: ${dest}`);
        }
      } catch (err: any) {
        console.error(
          `Error copying file (${retriesLeft} retries left): ${err.message}`
        );

        if (retriesLeft > 0) {
          console.log(`Retrying copy in ${delayMs}ms...`);
          setTimeout(() => attemptCopy(retriesLeft - 1), delayMs);
        } else {
          console.error(`Failed to copy after ${maxRetries} attempts`);
          reject(err);
        }
      }
    };

    attemptCopy(maxRetries);
  });
};

async function asPdf({
  fullFilePath = "",
  filename = "",
  folderName = "",
  options = {},
}: AsPdfOptions): Promise<ProcessingResult> {
  const isDocumentDrafting = folderName.startsWith("document-drafting/");
  // FIXED: Check if this is a DnD upload by looking for the proper pattern in the folder path
  // DnD uploads typically don't have "custom-documents" in the path
  const isDnDUpload = !folderName.includes("custom-documents");

  // Add detailed path logging to diagnose the issue
  console.log(`Debug - Full file path: ${fullFilePath}`);
  console.log(`Debug - Folder name: ${folderName}`);
  console.log(`Debug - Raw filename: ${filename}`);
  console.log(`Debug - Is DnD upload: ${isDnDUpload}`);
  console.log(`Debug - Is document drafting: ${isDocumentDrafting}`);

  // Print the path components to help diagnose path issues
  console.log(`Debug - Path components:`, {
    dirname: path.dirname(fullFilePath),
    basename: path.basename(fullFilePath),
    resolved: path.resolve(fullFilePath),
  });

  let content = "";
  let isOcr = false;
  let detectedLanguage: string | null = null;
  // let confidence = 100; // Default confidence for direct text extraction - unused variable
  let ocrError: string | null = null;
  let ocrCharacterCount = 0; // Variable to store character count from OCRLoader

  let dataDoc: PDFParseResult = {};
  let docs: PDFDocument[] = [];
  let initialContent = "";

  // 1. Initial attempt with PDFLoader for direct text extraction
  try {
    const pdfLoader = new PDFLoader(fullFilePath, { splitPages: true });
    docs = await pdfLoader.load();
    if (docs.length > 0) {
      initialContent = docs
        .map((doc) => doc.pageContent)
        .join("\n\n")
        .trim();
      content = initialContent; // Start with PDFLoader content
      console.log(
        `[asPDF] PDFLoader extracted content (${content.length} chars) for ${filename}.`
      );
    } else {
      console.log(`[asPDF] PDFLoader found no text content for ${filename}.`);
    }
  } catch (pdfLoaderErr) {
    console.error(`[asPDF] PDFLoader failed for ${filename}:`, pdfLoaderErr);
    // Continue to OCR attempt
  }

  // 2. Primary OCR Attempt (if PDFLoader failed or content is minimal)
  if (docs.length === 0 || !content || content.length < 100) {
    if (docs.length > 0) {
      console.log(
        `[asPDF] PDFLoader content is minimal (${content.length} chars). Attempting primary OCR with OCRLoader.`
      );
    } else {
      console.log(
        `[asPDF] PDFLoader failed. Attempting primary OCR with OCRLoader.`
      );
    }

    try {
      // Call OCRLoader and destructure the result
      const ocrLoader = new OCRLoader({
        targetLanguages: options?.ocr?.langList,
      });
      const { documents: ocrDocsResult, totalCharacters: ocrChars } =
        await ocrLoader.ocrPDF(fullFilePath);

      if (ocrDocsResult && ocrDocsResult.length > 0) {
        const ocrContent = ocrDocsResult
          .map((doc) => doc.pageContent)
          .join("\n\n")
          .trim();
        // Only use OCR content if it's substantially more than initial minimal content
        if (ocrContent.length > (content?.length || 0) + 100) {
          content = ocrContent;
          docs = ocrDocsResult; // Use OCR docs if content is better
          ocrCharacterCount = ocrChars; // Store the character count
          isOcr = true;
          // confidence = 0; // OCRLoader does not provide confidence score directly - unused variable
          detectedLanguage = null;
          ocrError = null;
          console.log(
            `[asPDF] OCRLoader successfully extracted better content (${content.length} chars).`
          );
        } else {
          console.log(
            `[asPDF] OCRLoader content (${
              ocrContent.length
            } chars) not substantially better than initial content (${
              content?.length || 0
            } chars). Keeping initial content.`
          );
          // Keep initial content if OCR wasn't significantly better
        }
      } else {
        console.error(
          `[asPDF] Primary OCRLoader attempt failed to extract content for ${filename}.`
        );
        if (!ocrError) ocrError = "Primary OCRLoader failed."; // Record error if not already set
      }
    } catch (ocrErr: any) {
      console.error(`[asPDF] Error during primary OCRLoader attempt:`, ocrErr);
      if (!ocrError)
        ocrError = ocrErr.message || "Primary OCRLoader failed with an error.";
    }
  }

  // 3. Fallback Extraction Attempt (pdf-parse) only if content is still empty/minimal *and* OCR didn't succeed
  if ((!content || content.length < 100) && !isOcr) {
    console.log(
      `[asPDF] Content still minimal/missing after PDFLoader and OCRLoader. Attempting fallback with pdf-parse.`
    );
    try {
      const dataBuffer = await fsPromises.readFile(fullFilePath);
      dataDoc = await pdfParse(dataBuffer); // Store pdf-parse result for metadata
      const pdfParseContent = dataDoc.text?.trim() || "";

      if (pdfParseContent.length > (content?.length || 0)) {
        content = pdfParseContent;
        isOcr = false; // Mark as not OCR if pdf-parse provided the final content
        // confidence = 100; // Reset confidence for direct text extraction - unused variable
        ocrError = null; // Clear any previous OCR error
        console.log(
          `[asPDF] pdf-parse fallback successfully extracted content (${content.length} chars).`
        );
        // Note: We don't update `docs` here as pdf-parse doesn't provide structured docs like the loaders
      } else {
        console.log(
          `[asPDF] pdf-parse fallback content (${
            pdfParseContent.length
          } chars) not better than existing content (${
            content?.length || 0
          } chars).`
        );
      }
    } catch (err: any) {
      console.error("[asPDF] Error during pdf-parse fallback processing:", err);
      // Don't set ocrError here, as this was a pdf-parse error
      if (!content && !isDocumentDrafting) {
        // Check if content is STILL empty
        return {
          success: false,
          reason: `Processing failed: All extraction methods failed. Last error: ${err.message}`,
          documents: [],
        };
      }
    }
  }

  // Add check for insufficient OCR content *before* the general content check
  if (isOcr && (!content || content.length < 50)) {
    console.error(
      `[asPDF] OCR processing resulted in insufficient content (${
        content?.length || 0
      } chars < 50) for ${filename}. Aborting.`
    );
    trashFile(fullFilePath);
    return {
      success: false,
      reason: `OCR processing resulted in insufficient content (< 50 characters).`,
      documents: [],
    };
  }

  // Final check for general content absence after all attempts
  if (!isDocumentDrafting && !content) {
    console.error(`[asPDF] No content could be extracted from ${filename}.`);
    trashFile(fullFilePath);
    return {
      success: false,
      reason: `Failed to extract content from ${filename}.`,
      documents: [],
    };
  }

  const fileExtension = path.extname(fullFilePath).toLowerCase();
  const docId = v4();
  const docName = `${slugify(removeExtension(filename))}-${docId}`;

  // Initialize the data object *after* all content extraction attempts are done
  const data: DocumentData = {
    id: docId,
    url: `${docName}${fileExtension}`,
    title: filename,
    // Use conditional logic for OCR vs direct extraction metadata
    docAuthor: isOcr
      ? dataDoc?.info?.Author ||
        dataDoc?.metadata?.pdf?.info?.Creator ||
        "no author found (OCR)" // Prioritize Author, then Creator, then default
      : dataDoc?.info?.Author ||
        dataDoc?.metadata?.pdf?.info?.Creator ||
        "no author found",
    description: isOcr
      ? `Content extracted using OCR${
          ocrError ? ` - Warning: ${ocrError}` : "."
        }` // Consistent OCR description
      : dataDoc?.info?.Title ||
        dataDoc?.metadata?.pdf?.info?.Title ||
        "No description found.", // Prioritize Title
    docSource: "pdf file uploaded by the user.", // Consistent source
    chunkSource: "", // Consistent chunk source
    published: createdDate(fullFilePath), // Always set published date
    wordCount: content?.split(/\s+/)?.filter(Boolean)?.length || 0, // Calculate word count safely
    pageContent: content || "", // Ensure pageContent is always a string
    token_count_estimate: tokenizeString(content || "").length, // Calculate token count safely
    // ocrAttempted: isOcr, // Temporarily removed to prevent LanceDB schema errors
    // confidenceScore: isOcr ? 0 : 100, // Temporarily removed to prevent LanceDB schema errors
  };

  // RESTRUCTURED: First try to save PDF to server/storage/documents/pdfview
  // for non-DnD uploads before calling writeToServerDocuments
  if (!isDnDUpload) {
    console.log(
      `Debug - Copying PDF file to server/storage/documents/pdfview for regular upload`
    );
    console.log(
      `Debug - Folder test: "${folderName}" includes "custom-documents"? ${folderName.includes(
        "custom-documents"
      )}`
    );

    // Ensure the directory exists
    try {
      try {
        await fsPromises.access(PDF_STORAGE_PATH);
      } catch {
        console.log(`Debug - Creating PDF storage path: ${PDF_STORAGE_PATH}`);
        await fsPromises.mkdir(PDF_STORAGE_PATH, { recursive: true });
      }

      const pdfDestinationPath = path.join(
        PDF_STORAGE_PATH,
        `${docName}.pdf` // Add .pdf extension to ensure the file is properly identified
      );
      console.log(`Debug - PDF destination path: ${pdfDestinationPath}`);

      try {
        // Add file existence check before copying
        if (!fs.existsSync(fullFilePath)) {
          console.error(
            `Debug - Source PDF file doesn't exist at ${fullFilePath}`
          );
          data.pdfCopyError = "Source file doesn't exist";
          data.pdfCopySuccess = false;
          data.originalFilePath = fullFilePath;
        } else {
          // Use retry mechanism to copy the PDF file
          await copyFileWithRetry(fullFilePath, pdfDestinationPath, 3, 2000);
          console.log(
            `Debug - Successfully copied PDF to server storage pdfview directory`
          );

          // Store the path information in the data object before writeToServerDocuments
          data.pdfPath = `/pdfview/${docName}.pdf`.replace(/\\/g, "/");
          data.serverPdfPath = pdfDestinationPath;
          data.pdfCopySuccess = true;

          // This will tell writeToServerDocuments not to copy to frontend hotdir
          data.frontendHotdirCopySuccess = false;
        }
      } catch (err: any) {
        console.error(
          `Debug - Error copying PDF to server storage pdfview directory:`,
          err
        );
        data.pdfCopyError = err.message;
        data.pdfCopySuccess = false;
        data.originalFilePath = fullFilePath;
      }
    } catch (dirErr: any) {
      console.error(`Debug - Error creating PDF storage directory:`, dirErr);
      data.pdfCreateError = dirErr.message;
      data.pdfCopySuccess = false;
      data.originalFilePath = fullFilePath;
    }
  } else {
    console.log(`Debug - Skipping pdfview copy because it's a DnD upload`);
    data.pdfCopySuccess = false; // Ensure this is explicitly set for DnD uploads
  }

  // AFTER trying to copy to server storage, now call writeToServerDocuments
  // It will use the pdfCopySuccess flag to determine if it should copy to frontend hotdir
  const document = await writeToServerDocuments(
    data as unknown as Record<string, unknown>,
    filename,
    `${docName}${fileExtension}`,
    null,
    folderName
  );

  // Now we can safely delete the original file from collector hotdir
  // Only if we have successfully copied it to the server or frontend hotdir or it's a DnD upload
  if (
    isDnDUpload ||
    document.pdfCopySuccess ||
    document.frontendHotdirCopySuccess
  ) {
    trashFile(fullFilePath);
  } else {
    console.log(
      `Debug - Not trashing original file due to copy failure, may be needed for API access.`
    );
  }

  console.log(
    `[SUCCESS]: ${filename} processed (${
      isOcr
        ? `OCR processed ${ocrCharacterCount} characters ${
            detectedLanguage ? `(${detectedLanguage})` : ""
          }${ocrError ? ` with error: ${ocrError}` : ""}`
        : "direct text extraction"
    }) & ready for embedding.\n`
  );

  // Transform the document data to match ProcessedDocument interface
  const processedDocument: ProcessedDocument = {
    id: typeof document.id === "string" ? document.id : undefined,
    content:
      typeof document.pageContent === "string"
        ? document.pageContent
        : String(document.pageContent ?? ""),
    token_count_estimate:
      typeof document.token_count_estimate === "number"
        ? document.token_count_estimate
        : undefined,
    metadata: {
      title: typeof document.title === "string" ? document.title : undefined,
      description:
        typeof document.description === "string"
          ? document.description
          : undefined,
      source:
        typeof document.docSource === "string" ? document.docSource : undefined,
      author:
        typeof document.docAuthor === "string" ? document.docAuthor : undefined,
      published:
        typeof document.published === "string" ? document.published : undefined,
      url: typeof document.url === "string" ? document.url : undefined,
      wordCount:
        typeof document.wordCount === "number" ? document.wordCount : undefined,
      // Include any additional properties from the document (type-safe spread)
      ...(typeof document === "object" && document !== null ? document : {}),
    },
  };

  return { success: true, reason: undefined, documents: [processedDocument] };
}

export default asPdf;
