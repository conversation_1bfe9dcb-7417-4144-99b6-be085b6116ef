import { v4 } from "uuid";
import fs from "fs";
import path from "path";
import slugify from "slugify";
import { tokenizeString } from "../../utils/tokenizer";
import {
  createdDate,
  trashFile,
  writeToServerDocuments,
  removeExtension,
  isOcrType,
} from "../../utils/files";
import { ProcessingResult, ProcessedDocument } from "../../src/types";
import ocrService from "./../../ocr";

interface ConvertOptions {
  fullFilePath: string;
  filename: string;
  folderName?: string;
}

async function asTxt({
  fullFilePath = "",
  filename = "",
  folderName = "",
}: ConvertOptions): Promise<ProcessingResult> {
  const isDocumentDrafting = folderName.startsWith("document-drafting/");
  // const isDnDUpload = !folderName.includes("custom-documents");
  const isOcr = isOcrType(fullFilePath);
  let content = "";
  let detectedLanguage: string | null = null;
  let confidence = 0;
  let ocrError: string | null = null;

  try {
    if (isOcr) {
      const imageBuffer = fs.readFileSync(fullFilePath);
      const ocrResult = await ocrService.processFile(imageBuffer, {
        minConfidence: 60,
        defaultLanguage: "en",
      });

      content = ocrResult.text || filename;
      detectedLanguage = ocrResult.language;
      confidence = ocrResult.confidence;
      ocrError = ocrResult.error || null;
    } else {
      content = fs.readFileSync(fullFilePath, "utf8");
    }
  } catch (err) {
    console.error("Could not read file!", err);
  }

  if (!content?.length && !isOcr) {
    content = filename;
  }

  if (!isDocumentDrafting && !content?.length) {
    console.error(`Resulting text content was empty for ${filename}.`);
    trashFile(fullFilePath);
    return {
      success: false,
      reason: `No text content found in ${filename}.`,
      documents: [],
    };
  }

  const fileExtension = path.extname(fullFilePath).toLowerCase();
  const docId = v4();
  const docName = `${slugify(removeExtension(filename))}-${docId}`;

  const data = {
    id: docId,
    url: `${docName}${fileExtension}`,
    title: filename,
    docAuthor: "Unknown",
    description: isOcr
      ? `Content extracted using OCR${
          detectedLanguage ? ` (${detectedLanguage})` : ""
        }${ocrError ? ` - Warning: ${ocrError}` : ""}.`
      : "Unknown",
    docSource: isOcr
      ? "an image file processed with OCR."
      : "a text file uploaded by the user.",
    chunkSource: "",
    published: createdDate(fullFilePath),
    wordCount: content.split(/\s+/).filter(Boolean).length,
    pageContent: content,
    token_count_estimate: tokenizeString(content).length,
  };

  const document = await writeToServerDocuments(
    data,
    filename,
    `${docName}${fileExtension}`,
    null,
    folderName
  );

  trashFile(fullFilePath);
  console.log(
    `[SUCCESS]: ${filename} ${
      isOcr
        ? `processed with OCR (${detectedLanguage}, ${confidence.toFixed(
            2
          )}% confidence) with error: ${ocrError}`
        : "converted"
    } & ready for embedding.\n`
  );

  // Transform the document data to match ProcessedDocument interface
  const processedDocument: ProcessedDocument = {
    id: typeof document.id === "string" ? document.id : undefined,
    content:
      typeof document.pageContent === "string"
        ? document.pageContent
        : String(document.pageContent ?? ""),
    token_count_estimate:
      typeof document.token_count_estimate === "number"
        ? document.token_count_estimate
        : undefined,
    metadata: {
      title: typeof document.title === "string" ? document.title : undefined,
      description:
        typeof document.description === "string"
          ? document.description
          : undefined,
      source:
        typeof document.docSource === "string" ? document.docSource : undefined,
      author:
        typeof document.docAuthor === "string" ? document.docAuthor : undefined,
      published:
        typeof document.published === "string" ? document.published : undefined,
      url: typeof document.url === "string" ? document.url : undefined,
      wordCount:
        typeof document.wordCount === "number" ? document.wordCount : undefined,
    },
  };

  return { success: true, reason: undefined, documents: [processedDocument] };
}

export default asTxt;
