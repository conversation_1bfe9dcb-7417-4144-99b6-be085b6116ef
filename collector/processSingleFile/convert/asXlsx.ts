import { v4 as uuidv4 } from "uuid";
import * as xlsx from "node-xlsx";
import path from "path";
import fs from "fs";
import {
  createdDate,
  trashFile,
  writeToServerDocuments,
} from "../../utils/files";
import { tokenizeString } from "../../utils/tokenizer";
import slugify from "slugify";
import { ProcessingResult, ProcessedDocument } from "../../src/types";

// Define types for node-xlsx since @types/node-xlsx doesn't exist
interface XlsxSheet {
  name: string;
  data: Array<Array<string | number | boolean | Date | null>>;
}

interface AsXlsxOptions {
  fullFilePath: string;
  filename: string;
  folderName: string;
}

interface SheetData {
  id: string;
  url: string;
  title: string;
  docAuthor: string;
  description: string;
  docSource: string;
  chunkSource: string;
  published: string;
  wordCount: number;
  pageContent: string;
  token_count_estimate: number;
}

function convertToCSV(
  data: Array<Array<string | number | boolean | Date | null>>
): string {
  return data
    .map((row) =>
      row
        .map((cell) => {
          if (cell === null || cell === undefined) return "";
          if (typeof cell === "string" && cell.includes(","))
            return `"${cell}"`;
          return String(cell);
        })
        .join(",")
    )
    .join("\n");
}

async function asXlsx({
  fullFilePath = "",
  filename = "",
  folderName = "",
}: AsXlsxOptions): Promise<ProcessingResult> {
  const documents: ProcessedDocument[] = [];
  const sheetFolderName = slugify(
    `${path.basename(filename)}-${uuidv4().slice(0, 4)}`,
    {
      lower: true,
      trim: true,
    }
  );

  const outFolderPath =
    process.env.NODE_ENV === "development"
      ? path.resolve(
          __dirname,
          `../../../server/storage/documents/${sheetFolderName}`
        )
      : path.resolve(
          process.env.STORAGE_DIR || "",
          `documents/${sheetFolderName}`
        );

  try {
    const workSheetsFromFile: XlsxSheet[] = xlsx.parse(fullFilePath);
    if (!fs.existsSync(outFolderPath))
      fs.mkdirSync(outFolderPath, { recursive: true });

    for (const sheet of workSheetsFromFile) {
      try {
        const { name, data } = sheet;
        const content = convertToCSV(data);

        if (!content?.length) {
          console.warn(`Sheet "${name}" is empty. Skipping.`);
          continue;
        }

        console.log(`-- Processing sheet: ${name} --`);
        const sheetData: SheetData = {
          id: uuidv4(),
          url: `file://${path.join(outFolderPath, `${slugify(name)}.csv`)}`,
          title: `${filename} - Sheet:${name}`,
          docAuthor: "Unknown",
          description: `Spreadsheet data from sheet: ${name}`,
          docSource: "an xlsx file uploaded by the user.",
          chunkSource: "",
          published: createdDate(fullFilePath),
          wordCount: content.split(/\s+/).length,
          pageContent: content,
          token_count_estimate: tokenizeString(content).length,
        };

        const document = await writeToServerDocuments(
          sheetData as unknown as Record<string, unknown>,
          `sheet-${slugify(name)}`,
          outFolderPath,
          null,
          folderName
        );

        // Transform the document to match ProcessedDocument interface
        const processedDoc: ProcessedDocument = {
          id: typeof document.id === "string" ? document.id : undefined,
          content:
            typeof document.pageContent === "string"
              ? document.pageContent
              : String(document.pageContent ?? ""),
          token_count_estimate:
            typeof document.token_count_estimate === "number"
              ? document.token_count_estimate
              : undefined,
          metadata: {
            title:
              typeof document.title === "string" ? document.title : undefined,
            description:
              typeof document.description === "string"
                ? document.description
                : undefined,
            source:
              typeof document.docSource === "string"
                ? document.docSource
                : undefined,
            sourceLink:
              typeof document.url === "string" ? document.url : undefined,
            author:
              typeof document.docAuthor === "string"
                ? document.docAuthor
                : undefined,
            published:
              typeof document.published === "string"
                ? document.published
                : undefined,
            wordCount:
              typeof document.wordCount === "number"
                ? document.wordCount
                : undefined,
          },
        };

        documents.push(processedDoc);
        console.log(
          `[SUCCESS]: Sheet "${name}" converted & ready for embedding.`
        );
      } catch (err) {
        console.error(`Error processing sheet "${name}":`, err);
        continue;
      }
    }
  } catch (err: unknown) {
    console.error("Could not process xlsx file!", err);
    return {
      success: false,
      reason: `Error processing ${filename}: ${
        err instanceof Error ? err.message : String(err)
      }`,
      documents: [],
    };
  } finally {
    trashFile(fullFilePath);
  }

  if (documents.length === 0) {
    console.error(`No valid sheets found in ${filename}.`);
    return {
      success: false,
      reason: `No valid sheets found in ${filename}.`,
      documents: [],
    };
  }

  console.log(
    `[SUCCESS]: ${filename} fully processed. Created ${documents.length} document(s).\n`
  );
  return { success: true, reason: undefined, documents };
}

export default asXlsx;
