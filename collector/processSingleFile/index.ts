import path from "path";
import fs from "fs";
import {
  WATCH_DIRECTORY,
  SUPPORTED_FILETYPE_CONVERTERS,
} from "../utils/constants";
import {
  trashFile,
  isTextType,
  isOcrType,
  normalizePath,
  isWithin,
} from "../utils/files";
import { t } from "../i18n";
import { ProcessingResult, ProcessingOptions } from "../src/types";

const RESERVED_FILES = ["__HOTDIR__.md"];

export async function processSingleFile(
  targetFilename: string,
  options: ProcessingOptions = {},
  folderName: string = ""
): Promise<ProcessingResult> {
  const fullFilePath = path.resolve(
    WATCH_DIRECTORY,
    normalizePath(targetFilename)
  );

  if (!isWithin(path.resolve(WATCH_DIRECTORY), fullFilePath))
    return {
      success: false,
      reason: t("errors.files.invalid-filename-path"),
      documents: [],
    };

  if (RESERVED_FILES.includes(targetFilename))
    return {
      success: false,
      reason: t("errors.files.reserved-filename"),
      documents: [],
    };
  if (!fs.existsSync(fullFilePath))
    return {
      success: false,
      reason: t("errors.files.file-not-in-upload"),
      documents: [],
    };

  const fileExtension = path.extname(fullFilePath).toLowerCase();
  if (fullFilePath.includes(".") && !fileExtension) {
    return {
      success: false,
      reason: t("errors.files.no-extension"),
      documents: [],
    };
  }

  let processFileAs = fileExtension;
  if (
    !Object.prototype.hasOwnProperty.call(
      SUPPORTED_FILETYPE_CONVERTERS,
      fileExtension
    )
  ) {
    if (isTextType(fullFilePath) || isOcrType(fullFilePath)) {
      console.log(
        `\x1b[33m[Collector]\x1b[0m The provided filetype of ${fileExtension} does not have a preset and will be processed as .txt.`
      );
      processFileAs = ".txt";
    } else {
      trashFile(fullFilePath);
      return {
        success: false,
        reason: t("errors.files.unsupported-extension", {
          extension: fileExtension,
        }),
        documents: [],
      };
    }
  }

  const converterPath = SUPPORTED_FILETYPE_CONVERTERS[processFileAs]!;

  // When using ts-node, we need to handle .js extensions that map to .ts files
  let FileTypeProcessor;
  try {
    FileTypeProcessor = require(converterPath);
  } catch {
    // If .js file not found, try loading the .ts version
    const tsPath = converterPath.replace(/\.js$/, ".ts");
    try {
      FileTypeProcessor = require(tsPath);
    } catch {
      throw new Error(`Could not load file processor for ${processFileAs}`);
    }
  }

  // Handle both CommonJS and ES module exports
  const processorFunction = FileTypeProcessor.default || FileTypeProcessor;

  return await processorFunction({
    fullFilePath,
    filename: targetFilename,
    options,
    folderName,
  });
}
