# Playwright E2E Tests

This directory contains end-to-end (E2E) tests for the IST Legal platform using Playwright.

## Overview

Playwright is a modern web testing framework that enables reliable end-to-end testing across all major browsers. Our E2E tests verify critical user flows and ensure the application works correctly from a user's perspective.

## Running Tests

### Quick Start

```bash
# Run all E2E tests
npm run test:e2e

# Run tests in headed mode (see browser)
npx playwright test --headed

# Run tests in UI mode (interactive)
npx playwright test --ui

# Run a specific test file
npx playwright test e2e-playwright/auth.spec.ts

# Run tests with debug mode
npx playwright test --debug
```

### Prerequisites

1. **Install Playwright browsers** (first time only):

   ```bash
   npx playwright install
   ```

2. **Ensure the application is running**:
   - The tests are configured to automatically start the development server
   - Alternatively, manually start the server: `npm run dev:server`
   - Frontend should be accessible at http://localhost:3000
   - Backend API should be accessible at http://localhost:3001

## Test Structure

### Current Test Suites

1. **`test-basic.spec.ts`** - Basic connectivity test
   - Verifies the application is accessible
   - Checks page title

2. **`api-health.spec.ts`** - API health checks
   - Server availability on port 3001
   - Frontend availability on port 3000
   - API ping endpoint functionality

3. **`auth.spec.ts`** - Authentication flow tests
   - Login page display
   - Login form validation
   - Error handling for invalid credentials

4. **`navigation.spec.ts`** - Navigation and UI tests
   - Page title verification
   - Responsive viewport testing (desktop, tablet, mobile)
   - Network error handling

## Writing New Tests

### Test File Structure

```typescript
import { test, expect } from "@playwright/test";

test.describe("Feature Name", () => {
  test("should do something specific", async ({ page }) => {
    // Navigate to page
    await page.goto("http://localhost:3000/path");

    // Interact with elements
    await page.click('button[type="submit"]');

    // Assert expectations
    await expect(page.locator(".success-message")).toBeVisible();
  });
});
```

### Common Patterns

#### Waiting for Elements

```typescript
// Wait for element to be visible
await expect(page.locator(".loading")).toBeVisible();

// Wait for element to disappear
await expect(page.locator(".spinner")).not.toBeVisible();

// Wait with timeout
await expect(page.locator(".slow-load")).toBeVisible({ timeout: 10000 });
```

#### Form Interactions

```typescript
// Fill form fields
await page.fill('input[name="email"]', "<EMAIL>");
await page.fill('input[name="password"]', "password123");

// Select dropdown
await page.selectOption('select[name="role"]', "admin");

// Check checkbox
await page.check('input[type="checkbox"]');
```

#### API Testing

```typescript
test("should call API endpoint", async ({ request }) => {
  const response = await request.get("http://localhost:3001/api/endpoint");
  expect(response.status()).toBe(200);

  const data = await response.json();
  expect(data).toHaveProperty("success", true);
});
```

## Configuration

The Playwright configuration is defined in `playwright.config.mjs`:

- **Test Directory**: `./e2e-playwright`
- **Timeout**: 30 seconds per test
- **Retries**: 2 on CI, 0 locally
- **Browser**: Chromium (can be extended to Firefox, Safari)
- **Base URL**: http://localhost:3000
- **Web Server**: Automatically starts dev server on port 3001

### Extending Configuration

To add more browsers:

```javascript
projects: [
  {
    name: "chromium",
    use: { ...devices["Desktop Chrome"] },
  },
  {
    name: "firefox",
    use: { ...devices["Desktop Firefox"] },
  },
  {
    name: "webkit",
    use: { ...devices["Desktop Safari"] },
  },
];
```

## Best Practices

1. **Test Independence**: Each test should be able to run independently
2. **Clear Test Names**: Use descriptive test names that explain what is being tested
3. **Page Object Model**: For complex pages, consider using the Page Object Model pattern
4. **Avoid Hard Waits**: Use Playwright's built-in waiting mechanisms instead of `page.waitForTimeout()`
5. **Test Data Cleanup**: Clean up any test data created during tests
6. **Selective Running**: Use `.only` during development, but never commit it
7. **Screenshots on Failure**: Playwright automatically captures screenshots on test failure

## Debugging Failed Tests

1. **Run in headed mode** to see what's happening:

   ```bash
   npx playwright test --headed
   ```

2. **Use debug mode** to step through tests:

   ```bash
   npx playwright test --debug
   ```

3. **View test report** after running tests:

   ```bash
   npx playwright show-report
   ```

4. **Check screenshots** in the test results for failed tests

5. **Use `page.pause()`** in your test to pause execution:
   ```typescript
   await page.pause(); // Pauses test execution
   ```

## CI/CD Integration

The tests are configured to run in CI environments with:

- Headless browser execution
- 2 retry attempts for flaky tests
- Single worker to avoid resource constraints
- Automatic server startup

## Troubleshooting

### Common Issues

1. **"Browser not installed" error**
   - Run: `npx playwright install`

2. **"Connection refused" errors**
   - Ensure the dev server is running
   - Check ports 3000 and 3001 are not in use

3. **Timeout errors**
   - Increase timeout in specific tests: `test.setTimeout(60000)`
   - Check if the application is slow to load

4. **Element not found**
   - Use Playwright's inspector: `npx playwright test --debug`
   - Verify selectors using browser DevTools

### Environment Variables

The tests respect the following environment variables:

- `CI` - Set to true in CI environments for different retry behavior
- `BASE_URL` - Override the default base URL if needed

## Resources

- [Playwright Documentation](https://playwright.dev/docs/intro)
- [Writing Tests](https://playwright.dev/docs/writing-tests)
- [Best Practices](https://playwright.dev/docs/best-practices)
- [API Testing](https://playwright.dev/docs/api-testing)
- [Debugging](https://playwright.dev/docs/debug)
