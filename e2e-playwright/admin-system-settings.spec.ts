import { test, expect } from "@playwright/test";

test.describe("Admin Features and System Settings", () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin
    await page.goto("/");
    await expect(page.locator('input[name="username"]')).toBeVisible({
      timeout: 10000,
    });
    await page.fill('input[name="username"]', "admin");
    await page.fill('input[name="password"]', "password");
    await page.click('button[type="submit"]');
    await page.waitForURL("**/home", { timeout: 10000 });
  });

  test("admin dashboard and user management", async ({ page }) => {
    // Navigate to admin panel
    await test.step("Navigate to admin dashboard", async () => {
      // Look for admin link in navigation
      const adminLink = page.locator(
        'a:has-text("Admin"), button:has-text("Admin"), [aria-label*="admin"]'
      );

      if (await adminLink.isVisible({ timeout: 5000 }).catch(() => false)) {
        await adminLink.click();
        await expect(
          page.locator("text=/Admin|Administration|Dashboard/i")
        ).toBeVisible({
          timeout: 10000,
        });
      } else {
        // Try direct navigation
        await page.goto("/admin");
      }
    });

    // User management
    await test.step("Access user management", async () => {
      const usersLink = page.locator(
        'a:has-text("Users"), button:has-text("Users")'
      );

      if (await usersLink.isVisible({ timeout: 5000 }).catch(() => false)) {
        await usersLink.click();

        // Verify users list
        await expect(
          page.locator('table, [data-testid="users-list"]')
        ).toBeVisible({
          timeout: 10000,
        });

        // Check for admin user
        await expect(page.locator('text="admin"')).toBeVisible();
      }
    });

    // Create new user
    await test.step("Create new user", async () => {
      const newUserButton = page.locator(
        'button:has-text("New User"), button:has-text("Add User")'
      );

      if (await newUserButton.isVisible({ timeout: 5000 }).catch(() => false)) {
        await newUserButton.click();

        // Fill user form
        await page.fill('input[name="username"]', `testuser${Date.now()}`);
        await page.fill('input[name="password"]', "TestPassword123!");
        await page.fill('input[name="email"]', `test${Date.now()}@example.com`);

        // Select role if available
        const roleSelect = page.locator('select[name="role"]');
        if (await roleSelect.isVisible({ timeout: 2000 }).catch(() => false)) {
          await roleSelect.selectOption("default");
        }

        // Submit form
        await page.click('button[type="submit"]');

        // Verify success
        await expect(page.locator("text=/created|success|added/i")).toBeVisible(
          {
            timeout: 10000,
          }
        );
      }
    });
  });

  test("system settings configuration", async ({ page }) => {
    // Navigate to system settings
    await test.step("Navigate to system settings", async () => {
      const settingsLink = page.locator(
        'a:has-text("Settings"), a:has-text("System"), [href*="settings"]'
      );

      if (await settingsLink.isVisible({ timeout: 5000 }).catch(() => false)) {
        await settingsLink.click();
      } else {
        await page.goto("/admin/settings");
      }

      await expect(page.locator("text=/Settings|Configuration/i")).toBeVisible({
        timeout: 10000,
      });
    });

    // LLM Provider settings
    await test.step("Configure LLM provider", async () => {
      const llmSection = page.locator("text=/LLM|Language Model|AI Provider/i");

      if (await llmSection.isVisible({ timeout: 5000 }).catch(() => false)) {
        // Find provider select
        const providerSelect = page
          .locator('select[name*="provider"], select[name*="llm"]')
          .first();
        if (
          await providerSelect.isVisible({ timeout: 2000 }).catch(() => false)
        ) {
          // Get current value
          const currentValue = await providerSelect.inputValue();

          // Change to different provider
          const options = await providerSelect
            .locator("option")
            .allTextContents();
          const differentOption = options.find((opt) => opt !== currentValue);
          if (differentOption) {
            await providerSelect.selectOption(differentOption);
          }
        }

        // Look for API key field
        const apiKeyInput = page
          .locator('input[name*="api"][name*="key"], input[type="password"]')
          .first();
        if (await apiKeyInput.isVisible({ timeout: 2000 }).catch(() => false)) {
          // Don't actually change API key, just verify field exists
          expect(apiKeyInput).toBeTruthy();
        }
      }
    });

    // Vector database settings
    await test.step("Configure vector database", async () => {
      const vectorSection = page.locator("text=/Vector|Embedding|Database/i");

      if (await vectorSection.isVisible({ timeout: 5000 }).catch(() => false)) {
        const vectorSelect = page
          .locator('select[name*="vector"], select[name*="embedding"]')
          .first();
        if (
          await vectorSelect.isVisible({ timeout: 2000 }).catch(() => false)
        ) {
          // Verify options exist
          const options = await vectorSelect.locator("option").count();
          expect(options).toBeGreaterThan(0);
        }
      }
    });

    // Save settings
    await test.step("Save system settings", async () => {
      const saveButton = page
        .locator('button:has-text("Save"), button[type="submit"]:visible')
        .first();

      if (await saveButton.isVisible({ timeout: 5000 }).catch(() => false)) {
        await saveButton.click();

        // Verify save success
        await expect(page.locator("text=/saved|updated|success/i")).toBeVisible(
          {
            timeout: 10000,
          }
        );
      }
    });
  });

  test("workspace management and permissions", async ({ page }) => {
    // Admin workspace management
    await test.step("Navigate to workspace management", async () => {
      const workspacesLink = page.locator(
        'a:has-text("Workspaces"), [href*="workspaces"]'
      );

      if (
        await workspacesLink.isVisible({ timeout: 5000 }).catch(() => false)
      ) {
        await workspacesLink.click();
      } else {
        await page.goto("/admin/workspaces");
      }
    });

    // View all workspaces
    await test.step("View workspace list", async () => {
      await expect(
        page.locator('table, [data-testid="workspaces-list"]')
      ).toBeVisible({
        timeout: 10000,
      });

      // Check workspace details
      const workspaceRow = page
        .locator('tr:has(td), [data-testid="workspace-item"]')
        .first();
      if (await workspaceRow.isVisible({ timeout: 5000 }).catch(() => false)) {
        // Look for action buttons
        const actionsButton = workspaceRow.locator(
          'button[aria-label*="actions"], button:has-text("...")'
        );
        if (
          await actionsButton.isVisible({ timeout: 2000 }).catch(() => false)
        ) {
          await actionsButton.click();

          // Check available actions
          const menuItems = page.locator('[role="menuitem"], .dropdown-item');
          const itemCount = await menuItems.count();
          expect(itemCount).toBeGreaterThan(0);

          // Close menu
          await page.keyboard.press("Escape");
        }
      }
    });

    // Workspace permissions
    await test.step("Manage workspace permissions", async () => {
      const workspaceRow = page
        .locator('tr:has(td), [data-testid="workspace-item"]')
        .first();

      if (await workspaceRow.isVisible({ timeout: 5000 }).catch(() => false)) {
        // Look for permissions or users button
        const permissionsButton = workspaceRow.locator(
          'button:has-text("Permissions"), button:has-text("Users")'
        );
        if (
          await permissionsButton
            .isVisible({ timeout: 2000 })
            .catch(() => false)
        ) {
          await permissionsButton.click();

          // Verify permissions modal/page
          await expect(
            page.locator("text=/Permissions|Access|Users/i")
          ).toBeVisible({
            timeout: 10000,
          });

          // Close modal if opened
          const closeButton = page.locator(
            'button[aria-label="Close"], button:has-text("Close")'
          );
          if (
            await closeButton.isVisible({ timeout: 2000 }).catch(() => false)
          ) {
            await closeButton.click();
          }
        }
      }
    });
  });

  test("system logs and monitoring", async ({ page }) => {
    // Navigate to logs
    await test.step("Access system logs", async () => {
      const logsLink = page.locator(
        'a:has-text("Logs"), a:has-text("Activity"), [href*="logs"]'
      );

      if (await logsLink.isVisible({ timeout: 5000 }).catch(() => false)) {
        await logsLink.click();

        // Verify logs are displayed
        await expect(
          page.locator('table, [data-testid="logs-list"], .log-entries')
        ).toBeVisible({
          timeout: 10000,
        });
      }
    });

    // Filter logs
    await test.step("Filter system logs", async () => {
      // Look for filter options
      const filterInput = page.locator(
        'input[placeholder*="filter"], input[placeholder*="search"]'
      );

      if (await filterInput.isVisible({ timeout: 5000 }).catch(() => false)) {
        await filterInput.fill("login");
        await filterInput.press("Enter");

        // Wait for filtered results
        await page.waitForTimeout(2000);

        // Clear filter
        await filterInput.clear();
        await filterInput.press("Enter");
      }

      // Date range filter
      const dateFilter = page
        .locator('input[type="date"], [data-testid="date-filter"]')
        .first();
      if (await dateFilter.isVisible({ timeout: 2000 }).catch(() => false)) {
        const today = new Date().toISOString().split("T")[0];
        await dateFilter.fill(today);
      }
    });

    // Export logs
    await test.step("Export logs", async () => {
      const exportButton = page.locator(
        'button:has-text("Export"), button[aria-label*="export"]'
      );

      if (await exportButton.isVisible({ timeout: 5000 }).catch(() => false)) {
        // Set up download promise before clicking
        const downloadPromise = page
          .waitForEvent("download", { timeout: 10000 })
          .catch(() => null);

        await exportButton.click();

        // Check if download started
        const download = await downloadPromise;
        if (download) {
          expect(download.suggestedFilename()).toContain("log");
        }
      }
    });
  });

  test("security and authentication settings", async ({ page }) => {
    // Navigate to security settings
    await test.step("Access security settings", async () => {
      const securityLink = page.locator(
        'a:has-text("Security"), [href*="security"]'
      );

      if (await securityLink.isVisible({ timeout: 5000 }).catch(() => false)) {
        await securityLink.click();
      } else {
        await page.goto("/admin/security");
      }
    });

    // Password policy
    await test.step("Configure password policy", async () => {
      const passwordSection = page
        .locator("text=/Password|Authentication/i")
        .first();

      if (
        await passwordSection.isVisible({ timeout: 5000 }).catch(() => false)
      ) {
        // Look for password length setting
        const minLengthInput = page.locator(
          'input[name*="length"], input[name*="password"][name*="min"]'
        );
        if (
          await minLengthInput.isVisible({ timeout: 2000 }).catch(() => false)
        ) {
          await minLengthInput.fill("12");
        }

        // Complexity requirements
        const complexityCheckboxes = page.locator(
          'input[type="checkbox"][name*="password"], input[type="checkbox"][name*="require"]'
        );
        const checkboxCount = await complexityCheckboxes.count();

        // Check some boxes
        for (let i = 0; i < Math.min(2, checkboxCount); i++) {
          const checkbox = complexityCheckboxes.nth(i);
          if (!(await checkbox.isChecked())) {
            await checkbox.check();
          }
        }
      }
    });

    // Session settings
    await test.step("Configure session settings", async () => {
      const sessionTimeout = page.locator(
        'input[name*="timeout"], input[name*="session"]'
      );

      if (
        await sessionTimeout.isVisible({ timeout: 5000 }).catch(() => false)
      ) {
        await sessionTimeout.fill("30");
      }
    });

    // Multi-factor authentication
    await test.step("Check MFA settings", async () => {
      const mfaSection = page.locator(
        "text=/Multi-factor|Two-factor|2FA|MFA/i"
      );

      if (await mfaSection.isVisible({ timeout: 5000 }).catch(() => false)) {
        const mfaToggle = page.locator(
          'input[type="checkbox"][name*="mfa"], button[aria-label*="mfa"]'
        );
        if (await mfaToggle.isVisible({ timeout: 2000 }).catch(() => false)) {
          // Just verify it exists, don't change
          expect(mfaToggle).toBeTruthy();
        }
      }
    });
  });

  test("api key management", async ({ page }) => {
    // Navigate to API settings
    await test.step("Access API key management", async () => {
      const apiLink = page.locator(
        'a:has-text("API"), a:has-text("Keys"), [href*="api"]'
      );

      if (await apiLink.isVisible({ timeout: 5000 }).catch(() => false)) {
        await apiLink.click();
      }
    });

    // Generate new API key
    await test.step("Generate API key", async () => {
      const generateButton = page.locator(
        'button:has-text("Generate"), button:has-text("New Key"), button:has-text("Create")'
      );

      if (
        await generateButton.isVisible({ timeout: 5000 }).catch(() => false)
      ) {
        await generateButton.click();

        // Fill key details if form appears
        const keyNameInput = page.locator(
          'input[name="name"], input[placeholder*="name"]'
        );
        if (
          await keyNameInput.isVisible({ timeout: 2000 }).catch(() => false)
        ) {
          await keyNameInput.fill(`Test API Key ${Date.now()}`);

          // Submit
          await page.click('button[type="submit"]');
        }

        // Look for generated key
        await expect(page.locator("text=/key|token|secret/i")).toBeVisible({
          timeout: 10000,
        });

        // Close modal if present
        const closeButton = page.locator(
          'button[aria-label="Close"], button:has-text("Close"), button:has-text("Done")'
        );
        if (await closeButton.isVisible({ timeout: 2000 }).catch(() => false)) {
          await closeButton.click();
        }
      }
    });

    // View API keys
    await test.step("View existing API keys", async () => {
      await expect(
        page.locator('table, [data-testid="api-keys-list"]')
      ).toBeVisible({
        timeout: 10000,
      });

      // Check for key actions
      const keyRow = page
        .locator('tr:has(td), [data-testid="api-key-item"]')
        .first();
      if (await keyRow.isVisible({ timeout: 5000 }).catch(() => false)) {
        // Look for revoke button
        const revokeButton = keyRow.locator(
          'button:has-text("Revoke"), button:has-text("Delete")'
        );
        if (
          await revokeButton.isVisible({ timeout: 2000 }).catch(() => false)
        ) {
          // Don't actually revoke, just verify button exists
          expect(revokeButton).toBeTruthy();
        }
      }
    });
  });
});
