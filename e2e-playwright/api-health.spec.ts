import { test, expect } from "@playwright/test";

test.describe("API Health Checks", () => {
  test("should have server running on port 3001", async ({ request }) => {
    const response = await request.get("http://localhost:3001/api");
    expect(response.status()).toBeLessThan(500);
  });

  test("should serve frontend on port 3000", async ({ page }) => {
    const response = await page.goto("http://localhost:3000");
    expect(response?.status()).toBeLessThan(500);
  });

  test("should have ping endpoint available", async ({ request }) => {
    // Test the ping endpoint
    const response = await request.get("http://localhost:3001/api/ping");

    // The ping endpoint should respond with 200
    expect(response.status()).toBe(200);

    const data = await response.json();
    expect(data).toHaveProperty("message");
    expect(data.message).toBe("pong");
  });
});
