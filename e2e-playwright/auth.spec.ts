import { test, expect } from "@playwright/test";

test.describe("Authentication Flow", () => {
  test("should display login page", async ({ page }) => {
    await page.goto("http://localhost:3000");

    // Check for login form elements
    await expect(page.locator("text=Login")).toBeVisible({ timeout: 10000 });

    // Look for username/email and password fields
    const usernameField = page
      .locator('input[type="text"], input[type="email"]')
      .first();
    const passwordField = page.locator('input[type="password"]').first();

    await expect(usernameField).toBeVisible();
    await expect(passwordField).toBeVisible();
  });

  test("should show error on invalid login", async ({ page }) => {
    await page.goto("http://localhost:3000");

    // Find and fill login fields
    const usernameField = page
      .locator('input[type="text"], input[type="email"]')
      .first();
    const passwordField = page.locator('input[type="password"]').first();

    await usernameField.fill("<EMAIL>");
    await passwordField.fill("wrongpassword");

    // Submit form
    const submitButton = page.locator('button[type="submit"]').first();
    await submitButton.click();

    // Check for error message
    await expect(page.locator("text=Invalid credentials")).toBeVisible({
      timeout: 10000,
    });
  });
});
