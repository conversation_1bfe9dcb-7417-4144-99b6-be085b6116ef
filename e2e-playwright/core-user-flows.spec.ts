import { test, expect } from "@playwright/test";

test.describe("Core User Flows", () => {
  test("basic application accessibility", async ({ page }) => {
    // Navigate to the application
    await page.goto("http://localhost:3000");

    // Wait for the page to load
    await page.waitForLoadState("networkidle");

    // Check if login page or main app is visible
    const loginVisible = await page
      .locator("text=Login")
      .isVisible()
      .catch(() => false);
    const workspaceVisible = await page
      .locator("text=/workspace/i")
      .isVisible()
      .catch(() => false);

    // Application should show either login or workspace content
    expect(loginVisible || workspaceVisible).toBeTruthy();
  });

  test("login flow with flexible selectors", async ({ page }) => {
    await page.goto("http://localhost:3000");

    // Check if already logged in
    const alreadyLoggedIn = await page
      .locator("text=/workspace/i")
      .isVisible({ timeout: 5000 })
      .catch(() => false);

    if (!alreadyLoggedIn) {
      // Wait for login form
      await expect(page.locator("text=Login")).toBeVisible({ timeout: 10000 });

      // Find input fields by type
      const textInputs = page.locator(
        'input[type="text"], input[type="email"]'
      );
      const passwordInputs = page.locator('input[type="password"]');

      // Fill first text input (username/email)
      if ((await textInputs.count()) > 0) {
        await textInputs.first().fill("<EMAIL>");
      }

      // Fill password
      if ((await passwordInputs.count()) > 0) {
        await passwordInputs.first().fill("password");
      }

      // Find and click submit button
      const submitButtons = page.locator(
        'button[type="submit"], button:has-text("Login"), button:has-text("Sign in")'
      );
      if ((await submitButtons.count()) > 0) {
        await submitButtons.first().click();
      }

      // Wait a bit for login to process
      await page.waitForTimeout(3000);
    }

    // Verify we're logged in or see an error
    const loggedIn = await page
      .locator("text=/workspace|dashboard|home/i")
      .isVisible({ timeout: 5000 })
      .catch(() => false);
    const hasError = await page
      .locator("text=/error|invalid|incorrect/i")
      .isVisible({ timeout: 5000 })
      .catch(() => false);

    // Either login succeeded or we got an error
    expect(loggedIn || hasError).toBeTruthy();
  });

  test("workspace navigation", async ({ page }) => {
    await page.goto("http://localhost:3000");
    await page.waitForLoadState("networkidle");

    // Look for workspace-related elements
    const workspaceElements = [
      page.locator("text=/workspace/i"),
      page.locator('a[href*="workspace"]'),
      page.locator('[data-testid*="workspace"]'),
      page.locator(".workspace, #workspace"),
    ];

    let foundWorkspace = false;
    for (const element of workspaceElements) {
      if (
        await element
          .first()
          .isVisible({ timeout: 2000 })
          .catch(() => false)
      ) {
        foundWorkspace = true;
        break;
      }
    }

    // If workspace elements found, try to interact
    if (foundWorkspace) {
      const workspaceLink = page.locator('a[href*="workspace"]').first();
      if (await workspaceLink.isVisible({ timeout: 2000 }).catch(() => false)) {
        await workspaceLink.click();
        await page.waitForLoadState("networkidle");
      }
    }
  });

  test("API connectivity", async ({ request }) => {
    // Test API endpoints
    const apiEndpoints = ["/api/ping", "/api/health", "/api/status"];

    let apiResponsive = false;

    for (const endpoint of apiEndpoints) {
      try {
        const response = await request.get(`http://localhost:3001${endpoint}`);
        if (response.ok()) {
          apiResponsive = true;
          break;
        }
      } catch (error) {
        // Continue to next endpoint
      }
    }

    expect(apiResponsive).toBeTruthy();
  });

  test("responsive design check", async ({ page }) => {
    await page.goto("http://localhost:3000");

    // Desktop view
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.waitForTimeout(1000);

    // Tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(1000);

    // Mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);

    // Check if page adapts (no horizontal scroll on mobile)
    const bodyWidth = await page.evaluate(() => document.body.scrollWidth);
    const viewportWidth = await page.evaluate(() => window.innerWidth);

    // Body shouldn't be wider than viewport (allowing small margin)
    expect(bodyWidth).toBeLessThanOrEqual(viewportWidth + 20);
  });

  test("error handling", async ({ page }) => {
    // Test 404 page
    await page.goto("http://localhost:3000/non-existent-page-12345");
    await page.waitForLoadState("networkidle");

    // Should show either 404 message or redirect to home
    const has404 = await page
      .locator("text=/404|not found/i")
      .isVisible({ timeout: 5000 })
      .catch(() => false);
    const redirectedHome = await page
      .locator("text=/login|workspace/i")
      .isVisible({ timeout: 5000 })
      .catch(() => false);

    expect(has404 || redirectedHome).toBeTruthy();
  });

  test("basic chat functionality", async ({ page }) => {
    await page.goto("http://localhost:3000");
    await page.waitForLoadState("networkidle");

    // Look for chat input
    const chatSelectors = [
      'textarea[placeholder*="message"]',
      'input[placeholder*="message"]',
      'textarea[placeholder*="chat"]',
      'input[placeholder*="chat"]',
      '[data-testid="chat-input"]',
      ".chat-input",
    ];

    let chatInput = null;
    for (const selector of chatSelectors) {
      const element = page.locator(selector).first();
      if (await element.isVisible({ timeout: 2000 }).catch(() => false)) {
        chatInput = element;
        break;
      }
    }

    if (chatInput) {
      // Type a message
      await chatInput.fill("Hello, this is a test message");

      // Look for send button or press enter
      const sendButton = page
        .locator('button[type="submit"], button:has-text("Send")')
        .first();
      if (await sendButton.isVisible({ timeout: 2000 }).catch(() => false)) {
        await sendButton.click();
      } else {
        await chatInput.press("Enter");
      }

      // Wait for any response or message to appear
      await page.waitForTimeout(3000);
    }
  });

  test("document upload area exists", async ({ page }) => {
    await page.goto("http://localhost:3000");
    await page.waitForLoadState("networkidle");

    // Look for upload-related elements
    const uploadSelectors = [
      'button:has-text("Upload")',
      'input[type="file"]',
      '[data-testid="upload"]',
      ".upload-area",
      "text=/upload|document|file/i",
    ];

    let uploadElementFound = false;
    for (const selector of uploadSelectors) {
      if (
        await page
          .locator(selector)
          .first()
          .isVisible({ timeout: 2000 })
          .catch(() => false)
      ) {
        uploadElementFound = true;
        break;
      }
    }

    // Upload functionality should be available somewhere in the app
    expect(uploadElementFound).toBeTruthy();
  });

  test("settings or configuration accessible", async ({ page }) => {
    await page.goto("http://localhost:3000");
    await page.waitForLoadState("networkidle");

    // Look for settings-related elements
    const settingsSelectors = [
      "text=/settings|configuration|preferences/i",
      'a[href*="settings"]',
      'button[aria-label*="settings"]',
      '[data-testid="settings"]',
    ];

    let settingsFound = false;
    for (const selector of settingsSelectors) {
      if (
        await page
          .locator(selector)
          .first()
          .isVisible({ timeout: 2000 })
          .catch(() => false)
      ) {
        settingsFound = true;
        break;
      }
    }

    // Settings should be accessible somewhere
    expect(settingsFound).toBeTruthy();
  });
});
