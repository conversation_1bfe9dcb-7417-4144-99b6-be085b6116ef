import { test, expect } from "@playwright/test";
import * as path from "path";

test.describe("Document Processing and Chat Flow", () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto("/");
    await expect(page.locator('input[name="username"]')).toBeVisible({
      timeout: 10000,
    });
    await page.fill('input[name="username"]', "admin");
    await page.fill('input[name="password"]', "password");
    await page.click('button[type="submit"]');
    await page.waitForURL("**/home", { timeout: 10000 });
  });

  test("upload and process multiple document types", async ({ page }) => {
    // Navigate to workspace
    await test.step("Navigate to workspace", async () => {
      const workspaceLink = page
        .locator('a[href*="/workspace/"]:not([href*="/new"])')
        .first();

      if (await workspaceLink.isVisible({ timeout: 5000 }).catch(() => false)) {
        await workspaceLink.click();
      } else {
        // Create workspace if none exists
        await page.goto("/workspace/new");
        await page.fill('input[name="name"]', "Document Test Workspace");
        await page.click('button[type="submit"]');
      }

      await page.waitForURL("**/workspace/**", { timeout: 10000 });
    });

    // Test different document types
    const documentTypes = [
      {
        name: "test.txt",
        content: "This is a plain text document for testing.",
        mimeType: "text/plain",
      },
      {
        name: "test.pdf",
        content: "PDF content would go here",
        mimeType: "application/pdf",
      },
      {
        name: "test.docx",
        content: "Word document content",
        mimeType:
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      },
    ];

    for (const doc of documentTypes) {
      await test.step(`Upload ${doc.name}`, async () => {
        // Find upload button
        const uploadButton = page.locator(
          'button:has-text("Upload"), button[aria-label*="upload"]'
        );
        if (
          await uploadButton.isVisible({ timeout: 5000 }).catch(() => false)
        ) {
          await uploadButton.click();

          // Set file input
          const fileInput = page.locator('input[type="file"]');
          await fileInput.setInputFiles({
            name: doc.name,
            mimeType: doc.mimeType,
            buffer: Buffer.from(doc.content),
          });

          // Wait for upload completion
          await expect(page.locator(`text="${doc.name}"`)).toBeVisible({
            timeout: 30000,
          });
        }
      });
    }

    // Verify documents are listed
    await test.step("Verify documents are listed", async () => {
      // Look for documents section
      const documentsSection = page.locator(
        '[data-testid="documents-list"], .documents-list, [aria-label="Documents"]'
      );

      if (
        await documentsSection.isVisible({ timeout: 5000 }).catch(() => false)
      ) {
        for (const doc of documentTypes) {
          await expect(
            documentsSection.locator(`text="${doc.name}"`)
          ).toBeVisible();
        }
      }
    });
  });

  test("document search and filtering", async ({ page }) => {
    // Navigate to workspace with documents
    await test.step("Navigate to workspace", async () => {
      const workspaceLink = page
        .locator('a[href*="/workspace/"]:not([href*="/new"])')
        .first();
      if (await workspaceLink.isVisible({ timeout: 5000 }).catch(() => false)) {
        await workspaceLink.click();
        await page.waitForURL("**/workspace/**", { timeout: 10000 });
      }
    });

    // Test search functionality
    await test.step("Search for documents", async () => {
      const searchInput = page.locator(
        'input[placeholder*="search"], input[type="search"]'
      );

      if (await searchInput.isVisible({ timeout: 5000 }).catch(() => false)) {
        // Search for documents
        await searchInput.fill("test");
        await searchInput.press("Enter");

        // Wait for results
        await page.waitForTimeout(2000);

        // Clear search
        await searchInput.clear();
        await searchInput.press("Enter");
      }
    });

    // Test filtering
    await test.step("Filter documents", async () => {
      // Look for filter options
      const filterButton = page.locator(
        'button:has-text("Filter"), [aria-label*="filter"]'
      );

      if (await filterButton.isVisible({ timeout: 5000 }).catch(() => false)) {
        await filterButton.click();

        // Look for filter options
        const filterOption = page
          .locator('[role="option"], .filter-option')
          .first();
        if (
          await filterOption.isVisible({ timeout: 2000 }).catch(() => false)
        ) {
          await filterOption.click();
        }
      }
    });
  });

  test("chat with uploaded documents", async ({ page }) => {
    // Navigate to workspace
    await test.step("Navigate to workspace with documents", async () => {
      const workspaceLink = page
        .locator('a[href*="/workspace/"]:not([href*="/new"])')
        .first();
      if (await workspaceLink.isVisible({ timeout: 5000 }).catch(() => false)) {
        await workspaceLink.click();
        await page.waitForURL("**/workspace/**", { timeout: 10000 });
      }
    });

    // Start chat about documents
    await test.step("Ask questions about documents", async () => {
      const chatInput = page.locator(
        'textarea[placeholder*="message"], input[placeholder*="message"]'
      );

      if (await chatInput.isVisible({ timeout: 5000 }).catch(() => false)) {
        // Ask about documents
        await chatInput.fill(
          "What documents have been uploaded to this workspace?"
        );
        await chatInput.press("Enter");

        // Wait for response
        await expect(
          page.locator('[data-testid="chat-message"], .chat-message').last()
        ).toBeVisible({
          timeout: 30000,
        });

        // Ask follow-up question
        await chatInput.fill(
          "Can you summarize the content of the uploaded documents?"
        );
        await chatInput.press("Enter");

        // Wait for second response
        await page.waitForTimeout(2000);
        await expect(
          page.locator('[data-testid="chat-message"], .chat-message').last()
        ).toBeVisible({
          timeout: 30000,
        });
      }
    });

    // Test chat history
    await test.step("Verify chat history", async () => {
      // Check if messages are preserved
      const messages = page.locator(
        '[data-testid="chat-message"], .chat-message'
      );
      const messageCount = await messages.count();

      expect(messageCount).toBeGreaterThan(2); // At least 2 messages and 2 responses
    });
  });

  test("document actions and bulk operations", async ({ page }) => {
    // Navigate to workspace
    await test.step("Navigate to workspace", async () => {
      const workspaceLink = page
        .locator('a[href*="/workspace/"]:not([href*="/new"])')
        .first();
      if (await workspaceLink.isVisible({ timeout: 5000 }).catch(() => false)) {
        await workspaceLink.click();
        await page.waitForURL("**/workspace/**", { timeout: 10000 });
      }
    });

    // Test document selection
    await test.step("Select multiple documents", async () => {
      // Look for checkboxes
      const checkboxes = page.locator(
        'input[type="checkbox"][data-testid*="document"], .document-checkbox'
      );
      const checkboxCount = await checkboxes.count();

      if (checkboxCount > 0) {
        // Select first few documents
        for (let i = 0; i < Math.min(3, checkboxCount); i++) {
          await checkboxes.nth(i).check();
        }

        // Look for bulk action buttons
        const bulkActionButton = page.locator(
          'button:has-text("Actions"), button[aria-label*="bulk"]'
        );
        if (
          await bulkActionButton.isVisible({ timeout: 2000 }).catch(() => false)
        ) {
          await bulkActionButton.click();

          // Just verify menu opens, don't perform actions
          await page.keyboard.press("Escape");
        }
      }
    });

    // Test document preview
    await test.step("Preview document", async () => {
      const documentLink = page
        .locator('[data-testid="document-link"], .document-name')
        .first();

      if (await documentLink.isVisible({ timeout: 5000 }).catch(() => false)) {
        await documentLink.click();

        // Wait for preview modal or page
        await expect(
          page.locator(
            '[data-testid="document-preview"], .document-viewer, [role="dialog"]'
          )
        ).toBeVisible({
          timeout: 10000,
        });

        // Close preview
        const closeButton = page.locator(
          'button[aria-label="Close"], button:has-text("Close")'
        );
        if (await closeButton.isVisible({ timeout: 2000 }).catch(() => false)) {
          await closeButton.click();
        } else {
          await page.keyboard.press("Escape");
        }
      }
    });
  });

  test("document embedding and vector search", async ({ page }) => {
    // This test verifies the RAG (Retrieval-Augmented Generation) functionality

    await test.step("Navigate to workspace", async () => {
      const workspaceLink = page
        .locator('a[href*="/workspace/"]:not([href*="/new"])')
        .first();
      if (await workspaceLink.isVisible({ timeout: 5000 }).catch(() => false)) {
        await workspaceLink.click();
        await page.waitForURL("**/workspace/**", { timeout: 10000 });
      }
    });

    // Upload a document with specific content
    await test.step("Upload document with known content", async () => {
      const uploadButton = page.locator(
        'button:has-text("Upload"), button[aria-label*="upload"]'
      );
      if (await uploadButton.isVisible({ timeout: 5000 }).catch(() => false)) {
        await uploadButton.click();

        const fileInput = page.locator('input[type="file"]');
        const testContent = `
          Legal Contract Terms and Conditions
          
          This document contains important legal information.
          Section 1: Parties involved
          Section 2: Terms of agreement
          Section 3: Dispute resolution
          
          The agreement is governed by the laws of the jurisdiction.
        `;

        await fileInput.setInputFiles({
          name: "legal-contract.txt",
          mimeType: "text/plain",
          buffer: Buffer.from(testContent),
        });

        // Wait for embedding to complete
        await expect(page.locator('text="legal-contract.txt"')).toBeVisible({
          timeout: 30000,
        });

        // Give time for embedding
        await page.waitForTimeout(5000);
      }
    });

    // Test vector search through chat
    await test.step("Test semantic search", async () => {
      const chatInput = page.locator(
        'textarea[placeholder*="message"], input[placeholder*="message"]'
      );

      if (await chatInput.isVisible({ timeout: 5000 }).catch(() => false)) {
        // Ask about specific content
        await chatInput.fill(
          "What are the dispute resolution procedures mentioned in the documents?"
        );
        await chatInput.press("Enter");

        // Wait for response that should reference the uploaded document
        const response = await page.waitForSelector(
          '[data-testid="chat-message"]:last-child, .chat-message:last-child',
          {
            timeout: 30000,
          }
        );

        // Verify response references our document
        const responseText = await response.textContent();
        expect(responseText?.toLowerCase()).toContain("dispute");
      }
    });
  });

  test("document processing status and errors", async ({ page }) => {
    // Test handling of document processing states

    await test.step("Navigate to workspace", async () => {
      const workspaceLink = page
        .locator('a[href*="/workspace/"]:not([href*="/new"])')
        .first();
      if (await workspaceLink.isVisible({ timeout: 5000 }).catch(() => false)) {
        await workspaceLink.click();
        await page.waitForURL("**/workspace/**", { timeout: 10000 });
      }
    });

    // Upload large document to test processing status
    await test.step("Upload large document", async () => {
      const uploadButton = page.locator(
        'button:has-text("Upload"), button[aria-label*="upload"]'
      );
      if (await uploadButton.isVisible({ timeout: 5000 }).catch(() => false)) {
        await uploadButton.click();

        const fileInput = page.locator('input[type="file"]');
        // Create a larger document to test processing
        const largeContent = "Large document content. ".repeat(1000);

        await fileInput.setInputFiles({
          name: "large-document.txt",
          mimeType: "text/plain",
          buffer: Buffer.from(largeContent),
        });

        // Look for processing indicator
        await expect(
          page.locator("text=/processing|uploading|embedding/i")
        ).toBeVisible({
          timeout: 5000,
        });
      }
    });

    // Test error handling
    await test.step("Test invalid file upload", async () => {
      const uploadButton = page.locator(
        'button:has-text("Upload"), button[aria-label*="upload"]'
      );
      if (await uploadButton.isVisible({ timeout: 5000 }).catch(() => false)) {
        await uploadButton.click();

        const fileInput = page.locator('input[type="file"]');

        // Try to upload an invalid file type
        await fileInput.setInputFiles({
          name: "invalid.xyz",
          mimeType: "application/octet-stream",
          buffer: Buffer.from("Invalid file content"),
        });

        // Check for error message
        const errorMessage = page.locator(
          "text=/error|failed|invalid|unsupported/i"
        );
        // Some systems might accept any file, so don't fail if no error
        if (
          await errorMessage.isVisible({ timeout: 5000 }).catch(() => false)
        ) {
          expect(await errorMessage.textContent()).toBeTruthy();
        }
      }
    });
  });
});
