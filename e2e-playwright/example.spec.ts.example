import { test, expect, Page } from '@playwright/test';

/**
 * Example E2E test demonstrating best practices
 * Copy this file and rename to .spec.ts to create new tests
 */

// Page Object Model example for better maintainability
class LoginPage {
  constructor(private page: Page) {}

  async goto() {
    await this.page.goto('http://localhost:3000/login');
  }

  async login(email: string, password: string) {
    await this.page.fill('input[name="email"]', email);
    await this.page.fill('input[name="password"]', password);
    await this.page.click('button[type="submit"]');
  }

  async getErrorMessage() {
    return this.page.locator('.error-message').textContent();
  }
}

test.describe('Example Test Suite', () => {
  // Run before each test
  test.beforeEach(async ({ page }) => {
    // Common setup code
    await page.goto('http://localhost:3000');
  });

  // Basic test
  test('should load the home page', async ({ page }) => {
    await expect(page).toHaveTitle(/IST Legal/);
    await expect(page.locator('h1')).toContainText('Welcome');
  });

  // Test with Page Object Model
  test('should show error on invalid login', async ({ page }) => {
    const loginPage = new LoginPage(page);
    await loginPage.goto();
    await loginPage.login('<EMAIL>', 'wrongpassword');
    
    const errorMessage = await loginPage.getErrorMessage();
    expect(errorMessage).toContain('Invalid credentials');
  });

  // API test example
  test('should get user data from API', async ({ request }) => {
    const response = await request.get('http://localhost:3001/api/user/profile', {
      headers: {
        'Authorization': 'Bearer test-token'
      }
    });
    
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(data).toHaveProperty('email');
  });

  // Test with multiple assertions
  test('should display all navigation elements', async ({ page }) => {
    const nav = page.locator('nav');
    
    // Check multiple elements exist
    await expect(nav.locator('a[href="/"]')).toBeVisible();
    await expect(nav.locator('a[href="/workspaces"]')).toBeVisible();
    await expect(nav.locator('a[href="/settings"]')).toBeVisible();
    
    // Check element count
    const links = nav.locator('a');
    await expect(links).toHaveCount(3);
  });

  // Test with custom timeout
  test('should load large dataset', async ({ page }) => {
    test.setTimeout(60000); // 60 seconds for this specific test
    
    await page.goto('http://localhost:3000/data/large');
    await expect(page.locator('.data-loaded')).toBeVisible({ timeout: 30000 });
  });

  // Test with screenshot on specific step
  test('should display error state correctly', async ({ page }) => {
    await page.goto('http://localhost:3000/error-test');
    
    // Take screenshot for visual regression testing
    await page.screenshot({ path: 'error-state.png', fullPage: true });
    
    await expect(page.locator('.error-container')).toBeVisible();
  });

  // Test with network mocking
  test('should handle API errors gracefully', async ({ page }) => {
    // Mock API to return error
    await page.route('**/api/data', (route) => {
      route.fulfill({
        status: 500,
        body: JSON.stringify({ error: 'Server error' })
      });
    });
    
    await page.goto('http://localhost:3000/data');
    await expect(page.locator('.error-message')).toContainText('Server error');
  });

  // Test with file upload
  test('should upload a file', async ({ page }) => {
    await page.goto('http://localhost:3000/upload');
    
    // Upload file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles('path/to/test-file.pdf');
    
    await page.click('button[type="submit"]');
    await expect(page.locator('.upload-success')).toBeVisible();
  });

  // Test with browser context manipulation
  test('should work with cookies', async ({ page, context }) => {
    // Set cookie
    await context.addCookies([{
      name: 'session',
      value: 'test-session-id',
      domain: 'localhost',
      path: '/'
    }]);
    
    await page.goto('http://localhost:3000/dashboard');
    await expect(page.locator('.user-dashboard')).toBeVisible();
  });

  // Parameterized test example
  const testCases = [
    { viewport: { width: 1920, height: 1080 }, name: 'desktop' },
    { viewport: { width: 768, height: 1024 }, name: 'tablet' },
    { viewport: { width: 375, height: 667 }, name: 'mobile' }
  ];

  testCases.forEach(({ viewport, name }) => {
    test(`should be responsive on ${name}`, async ({ page }) => {
      await page.setViewportSize(viewport);
      await page.goto('http://localhost:3000');
      
      // Add viewport-specific assertions
      if (name === 'mobile') {
        await expect(page.locator('.mobile-menu-button')).toBeVisible();
      } else {
        await expect(page.locator('.desktop-nav')).toBeVisible();
      }
    });
  });
});