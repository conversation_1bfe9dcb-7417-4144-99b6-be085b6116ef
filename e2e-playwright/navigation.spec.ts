import { test, expect } from "@playwright/test";

test.describe("Navigation Tests", () => {
  test("should have proper page title", async ({ page }) => {
    await page.goto("http://localhost:3000");

    // Check page title
    const title = await page.title();
    expect(title).toContain("IST Legal");
  });

  test("should have responsive viewport", async ({ page }) => {
    await page.goto("http://localhost:3000");

    // Test desktop view
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.waitForTimeout(500);

    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(500);

    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500);

    // Should not crash on any viewport size
    const isVisible = await page.isVisible("body");
    expect(isVisible).toBe(true);
  });

  test("should handle network errors gracefully", async ({ page, context }) => {
    // Simulate offline mode
    await context.setOffline(true);

    try {
      await page.goto("http://localhost:3000", {
        waitUntil: "domcontentloaded",
      });
    } catch (error) {
      // Expected to fail when offline
      expect(error).toBeTruthy();
    }

    // Go back online
    await context.setOffline(false);

    // Should work again
    const response = await page.goto("http://localhost:3000");
    expect(response?.status()).toBeLessThan(500);
  });
});
