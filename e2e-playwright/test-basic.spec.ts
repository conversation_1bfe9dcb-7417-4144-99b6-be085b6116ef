import { test, expect } from "@playwright/test";

test("basic test", async ({ page }) => {
  // Try to navigate to the frontend URL
  await page.goto("http://localhost:3000");

  // Just check if we can reach the page or get an error
  const title = await page.title();
  console.log("Page title:", title);

  // This test will fail if the server isn't running, but at least we can verify <PERSON><PERSON> works
  expect(title).toBeTruthy();
});
