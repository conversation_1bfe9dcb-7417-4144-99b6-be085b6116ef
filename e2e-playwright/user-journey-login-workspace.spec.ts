import { test, expect } from "@playwright/test";

test.describe("User Journey: Login and Workspace Management", () => {
  test.beforeEach(async ({ page }) => {
    // Start from the home page
    await page.goto("http://localhost:3000");
  });

  test("complete user journey: login, create workspace, upload document, and chat", async ({
    page,
  }) => {
    // Step 1: Login
    await test.step("User logs in", async () => {
      // Wait for login form
      await expect(page.locator("text=Login")).toBeVisible({ timeout: 10000 });

      // Find and fill login fields
      const usernameField = page
        .locator('input[type="text"], input[type="email"]')
        .first();
      const passwordField = page.locator('input[type="password"]').first();

      await usernameField.fill("<EMAIL>");
      await passwordField.fill("password");

      // Submit login form
      const submitButton = page.locator('button[type="submit"]').first();
      await submitButton.click();

      // Wait for successful login - either navigation or workspace content
      await page
        .waitForSelector(
          '[data-testid="workspace"], text=/workspace/i, a[href*="/workspace"]',
          {
            timeout: 10000,
            state: "visible",
          }
        )
        .catch(() => {
          // If no workspace elements found, just continue
        });
    });

    // Step 2: Create new workspace
    await test.step("Create new workspace", async () => {
      // Look for new workspace button/link
      const newWorkspaceButton = page.locator(
        'button:has-text("New Workspace"), a:has-text("New Workspace"), [aria-label="Create new workspace"]'
      );

      // Click if found, otherwise navigate directly
      if (
        await newWorkspaceButton.isVisible({ timeout: 5000 }).catch(() => false)
      ) {
        await newWorkspaceButton.click();
      } else {
        // Navigate to workspace creation if button not found
        await page.goto("/workspace/new");
      }

      // Fill workspace name
      const workspaceName = `Test Workspace ${Date.now()}`;
      await page.fill(
        'input[name="name"], input[placeholder*="workspace"]',
        workspaceName
      );

      // Submit workspace creation
      await page.click('button[type="submit"], button:has-text("Create")');

      // Wait for workspace to be created and navigate to it
      await page.waitForURL("**/workspace/**", { timeout: 10000 });

      // Verify workspace was created
      await expect(page.locator(`text=${workspaceName}`)).toBeVisible({
        timeout: 10000,
      });
    });

    // Step 3: Upload document
    await test.step("Upload document to workspace", async () => {
      // Look for upload button
      const uploadButton = page.locator(
        'button:has-text("Upload"), button[aria-label*="upload"], [data-testid="upload-button"]'
      );

      if (await uploadButton.isVisible({ timeout: 5000 }).catch(() => false)) {
        await uploadButton.click();

        // Wait for file input
        const fileInput = page.locator('input[type="file"]');

        // Create a test file
        const testContent = "This is a test document for E2E testing.";
        await fileInput.setInputFiles({
          name: "test-document.txt",
          mimeType: "text/plain",
          buffer: Buffer.from(testContent),
        });

        // Wait for upload to complete
        await expect(page.locator('text="test-document.txt"')).toBeVisible({
          timeout: 30000,
        });
      }
    });

    // Step 4: Start a chat
    await test.step("Start chat in workspace", async () => {
      // Look for chat input
      const chatInput = page.locator(
        'textarea[placeholder*="message"], input[placeholder*="message"], [data-testid="chat-input"]'
      );

      if (await chatInput.isVisible({ timeout: 5000 }).catch(() => false)) {
        // Type a message
        await chatInput.fill(
          "Hello, can you help me understand the uploaded document?"
        );

        // Send message (Enter key or send button)
        const sendButton = page.locator(
          'button[type="submit"], button:has-text("Send"), [aria-label*="send"]'
        );
        if (await sendButton.isVisible({ timeout: 2000 }).catch(() => false)) {
          await sendButton.click();
        } else {
          await chatInput.press("Enter");
        }

        // Wait for response
        await expect(
          page.locator(
            '[data-testid="chat-message"], .chat-message, [role="log"]'
          )
        ).toBeVisible({
          timeout: 30000,
        });
      }
    });

    // Step 5: Verify workspace in sidebar
    await test.step("Verify workspace appears in sidebar", async () => {
      // Look for sidebar or workspace list
      const sidebar = page.locator(
        '[data-testid="sidebar"], aside, nav, .sidebar'
      );

      if (await sidebar.isVisible({ timeout: 5000 }).catch(() => false)) {
        // Verify our workspace is listed
        await expect(sidebar.locator("text=/Test Workspace/")).toBeVisible();
      }
    });
  });

  test("user journey: workspace settings and configuration", async ({
    page,
  }) => {
    // First login
    await test.step("Login as admin", async () => {
      await expect(page.locator('input[name="username"]')).toBeVisible({
        timeout: 10000,
      });
      await page.fill('input[name="username"]', "admin");
      await page.fill('input[name="password"]', "password");
      await page.click('button[type="submit"]');
      await page.waitForURL("**/home", { timeout: 10000 });
    });

    // Navigate to workspace settings
    await test.step("Navigate to workspace settings", async () => {
      // Try to find an existing workspace or create one
      const workspaceLink = page
        .locator('a[href*="/workspace/"]:not([href*="/new"])')
        .first();

      if (await workspaceLink.isVisible({ timeout: 5000 }).catch(() => false)) {
        await workspaceLink.click();
      } else {
        // Create a workspace if none exists
        await page.goto("/workspace/new");
        await page.fill(
          'input[name="name"], input[placeholder*="workspace"]',
          "Settings Test Workspace"
        );
        await page.click('button[type="submit"], button:has-text("Create")');
        await page.waitForURL("**/workspace/**", { timeout: 10000 });
      }

      // Look for settings button
      const settingsButton = page.locator(
        'button:has-text("Settings"), a:has-text("Settings"), [aria-label*="settings"]'
      );
      if (
        await settingsButton.isVisible({ timeout: 5000 }).catch(() => false)
      ) {
        await settingsButton.click();
        await expect(
          page.locator("text=/Settings|Configuration|Preferences/")
        ).toBeVisible({
          timeout: 10000,
        });
      }
    });

    // Update workspace settings
    await test.step("Update workspace configuration", async () => {
      // Try to find and update temperature setting
      const tempInput = page.locator(
        'input[name="temperature"], input[name="openAiTemp"], [data-testid="temperature-input"]'
      );
      if (await tempInput.isVisible({ timeout: 5000 }).catch(() => false)) {
        await tempInput.fill("0.7");
      }

      // Try to find and update model selection
      const modelSelect = page.locator(
        'select[name="model"], select[name="chatModel"], [data-testid="model-select"]'
      );
      if (await modelSelect.isVisible({ timeout: 5000 }).catch(() => false)) {
        await modelSelect.selectOption({ index: 1 }); // Select second option
      }

      // Save settings
      const saveButton = page.locator(
        'button:has-text("Save"), button[type="submit"]:has-text("Update")'
      );
      if (await saveButton.isVisible({ timeout: 5000 }).catch(() => false)) {
        await saveButton.click();

        // Wait for success message
        await expect(page.locator("text=/saved|updated|success/i")).toBeVisible(
          {
            timeout: 10000,
          }
        );
      }
    });
  });

  test("user journey: document management and search", async ({ page }) => {
    // Login first
    await test.step("Login", async () => {
      await expect(page.locator('input[name="username"]')).toBeVisible({
        timeout: 10000,
      });
      await page.fill('input[name="username"]', "admin");
      await page.fill('input[name="password"]', "password");
      await page.click('button[type="submit"]');
      await page.waitForURL("**/home", { timeout: 10000 });
    });

    // Navigate to documents
    await test.step("Navigate to document management", async () => {
      // Try to find documents link
      const documentsLink = page.locator(
        'a:has-text("Documents"), button:has-text("Documents")'
      );

      if (await documentsLink.isVisible({ timeout: 5000 }).catch(() => false)) {
        await documentsLink.click();
      } else {
        // Navigate to a workspace first
        const workspaceLink = page
          .locator('a[href*="/workspace/"]:not([href*="/new"])')
          .first();
        if (
          await workspaceLink.isVisible({ timeout: 5000 }).catch(() => false)
        ) {
          await workspaceLink.click();
          await page.waitForURL("**/workspace/**", { timeout: 10000 });
        }
      }
    });

    // Search for documents
    await test.step("Search for documents", async () => {
      const searchInput = page.locator(
        'input[placeholder*="search"], input[type="search"], [data-testid="search-input"]'
      );

      if (await searchInput.isVisible({ timeout: 5000 }).catch(() => false)) {
        await searchInput.fill("test");
        await searchInput.press("Enter");

        // Wait for search results
        await page.waitForTimeout(2000); // Give search time to complete
      }
    });

    // Document actions
    await test.step("Perform document actions", async () => {
      // Look for any document in the list
      const documentItem = page
        .locator('[data-testid="document-item"], .document-item, tr:has(td)')
        .first();

      if (await documentItem.isVisible({ timeout: 5000 }).catch(() => false)) {
        // Try to find action buttons
        const actionButton = documentItem.locator(
          'button[aria-label*="actions"], button:has-text("...")'
        );
        if (
          await actionButton.isVisible({ timeout: 2000 }).catch(() => false)
        ) {
          await actionButton.click();

          // Look for menu options
          const menuOption = page
            .locator('[role="menuitem"], .dropdown-item')
            .first();
          if (
            await menuOption.isVisible({ timeout: 2000 }).catch(() => false)
          ) {
            // Just verify menu opens, don't perform destructive actions
            await page.keyboard.press("Escape");
          }
        }
      }
    });
  });

  test("user journey: multi-user collaboration", async ({ page, context }) => {
    // This test simulates multiple users accessing the same workspace

    // User 1 logs in
    await test.step("User 1 logs in and creates workspace", async () => {
      await expect(page.locator('input[name="username"]')).toBeVisible({
        timeout: 10000,
      });
      await page.fill('input[name="username"]', "admin");
      await page.fill('input[name="password"]', "password");
      await page.click('button[type="submit"]');
      await page.waitForURL("**/home", { timeout: 10000 });

      // Create a shared workspace
      const newWorkspaceButton = page.locator(
        'button:has-text("New Workspace"), a:has-text("New Workspace")'
      );
      if (
        await newWorkspaceButton.isVisible({ timeout: 5000 }).catch(() => false)
      ) {
        await newWorkspaceButton.click();
        const workspaceName = `Shared Workspace ${Date.now()}`;
        await page.fill(
          'input[name="name"], input[placeholder*="workspace"]',
          workspaceName
        );
        await page.click('button[type="submit"], button:has-text("Create")');
        await page.waitForURL("**/workspace/**", { timeout: 10000 });
      }
    });

    // Simulate second user in new browser context
    const page2 = await context.newPage();

    await test.step("User 2 logs in and accesses shared workspace", async () => {
      await page2.goto("/");

      // Login as different user (if multi-user mode is enabled)
      await expect(page2.locator('input[name="username"]')).toBeVisible({
        timeout: 10000,
      });
      await page2.fill('input[name="username"]', "user2");
      await page2.fill('input[name="password"]', "password");
      await page2.click('button[type="submit"]');

      // Check if login successful or use same user
      const loginError = await page2
        .locator('.error, [role="alert"]')
        .isVisible({ timeout: 2000 })
        .catch(() => false);
      if (loginError) {
        // If user2 doesn't exist, continue with admin
        await page2.fill('input[name="username"]', "admin");
        await page2.click('button[type="submit"]');
      }

      await page2.waitForURL("**/home", { timeout: 10000 });
    });

    // Clean up
    await page2.close();
  });

  test("user journey: error handling and recovery", async ({ page }) => {
    // Test how the application handles errors

    await test.step("Handle network errors", async () => {
      // Simulate offline mode
      await page.route("**/api/**", (route) => route.abort());

      // Try to login
      await page.fill('input[name="username"]', "admin");
      await page.fill('input[name="password"]', "password");
      await page.click('button[type="submit"]');

      // Verify error message appears
      await expect(page.locator("text=/error|failed|unable/i")).toBeVisible({
        timeout: 10000,
      });

      // Restore network
      await page.unroute("**/api/**");
    });

    await test.step("Handle invalid data", async () => {
      // Try login with invalid credentials
      await page.reload();
      await page.fill('input[name="username"]', "invalid_user");
      await page.fill('input[name="password"]', "wrong_password");
      await page.click('button[type="submit"]');

      // Verify error handling
      await expect(page.locator("text=/invalid|incorrect|wrong/i")).toBeVisible(
        {
          timeout: 10000,
        }
      );
    });
  });
});
