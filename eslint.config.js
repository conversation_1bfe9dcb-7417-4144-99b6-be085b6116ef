import packageJson from "./server/node_modules/eslint-plugin-package-json/lib/index.js"
import jsoncESLintParser from "./server/node_modules/jsonc-eslint-parser/lib/index.js"
import globals from "./server/node_modules/globals/index.js"
import eslintRecommended from "./server/node_modules/@eslint/js/src/index.js"
import eslintConfigPrettier from "./server/node_modules/eslint-config-prettier/index.js"
import prettier from "./server/node_modules/eslint-plugin-prettier/eslint-plugin-prettier.js"
import react from "./server/node_modules/eslint-plugin-react/index.js"
import reactRefresh from "./server/node_modules/eslint-plugin-react-refresh/index.js"
import reactHooks from "./server/node_modules/eslint-plugin-react-hooks/index.js"
import ftFlow from "./server/node_modules/eslint-plugin-ft-flow/dist/index.js"
import hermesParser from "./server/node_modules/hermes-eslint/dist/index.js"
import unusedImports from "./server/node_modules/eslint-plugin-unused-imports/dist/index.js"
import typescriptPlugin from "./server/node_modules/@typescript-eslint/eslint-plugin/dist/index.js"
import typescriptParser from "./server/node_modules/@typescript-eslint/parser/dist/index.js"

const reactRecommended = react.configs.recommended
const jsxRuntime = react.configs["jsx-runtime"]

const commonRules = {
  // Less critical issues that should be warnings
  "no-case-declarations": "warn",
  "no-useless-catch": "warn",
  "no-prototype-builtins": "warn",
  "no-unused-vars": "off", // Use TypeScript's rule instead
  "no-useless-escape": "warn",
  "no-irregular-whitespace": "warn",
  "no-empty-pattern": "warn",
  "no-empty": "warn",
  "no-extra-boolean-cast": "warn",
  "prettier/prettier": "warn",
  // Unused imports cleanup
  "unused-imports/no-unused-imports": "error",
  "unused-imports/no-unused-vars": [
    "warn",
    {
      vars: "all",
      varsIgnorePattern: "^_",
      args: "after-used",
      argsIgnorePattern: "^_",
      caughtErrorsIgnorePattern: "^_"
    }
  ]
}
export default [
  // Global ignores
  {
    ignores: [
      "server/temp/**",
      "server/tmp/**",
      "server/dist/**",
      "server/storage/**",
      "server/node_modules/**",
      "frontend/node_modules/**",
      "collector/node_modules/**",
      "collector/**/*.d.ts",
      "collector/**/*.d.ts.map",
      "collector/**/*.js.map",
      "collector/coverage/**",
      "collector/hotdir/**",
      "collector/storage/**",
      "node_modules/**",
      "**/temp/**",
      "**/tmp/**",
      "**/dist/**",
      "**/build/**"
    ]
  },
  eslintRecommended.configs.recommended,
  eslintConfigPrettier,
  {
    languageOptions: {
      parser: hermesParser,
      parserOptions: {
        ecmaFeatures: { jsx: true }
      },
      ecmaVersion: 2022,
      sourceType: "module",
      globals: {
        ...globals.browser,
        ...globals.es2020,
        ...globals.node,
        globalThis: true
      }
    },
    linterOptions: { reportUnusedDisableDirectives: true },
    settings: { react: { version: "18.2" } },
    plugins: {
      ftFlow,
      react,
      "jsx-runtime": jsxRuntime,
      "react-hooks": reactHooks,
      prettier,
      "unused-imports": unusedImports
    },
    rules: {
      ...reactRecommended.rules,
      ...reactHooks.configs.recommended.rules,
      ...ftFlow.recommended,
      ...commonRules
    }
  },
  {
    files: ["**/package.json"],
    languageOptions: {
      parser: jsoncESLintParser
    },
    plugins: {
      "package-json": packageJson
    },
    rules: {
      "package-json/order-properties": "off",
      "package-json/require-author": "off",
      "package-json/require-description": "off",
      "package-json/require-engines": "off",
      "package-json/require-files": "off",
      "package-json/require-keywords": "off",
      "package-json/require-name": "off",
      "package-json/require-types": "off",
      "package-json/require-version": "off",
      "package-json/sort-collections": "off",
      "package-json/valid-local-dependency": "off",
      "package-json/valid-name": "off",
      "package-json/valid-package-definition": "off",
      "package-json/valid-repository-directory": "off",
      "package-json/valid-version": "off",
      "package-json/no-empty-fields": "off",
      "package-json/no-redundant-files": "off",
      "package-json/repository-shorthand": "off",
      "package-json/restrict-dependency-ranges": "off",
      "package-json/unique-dependencies": "error"
    }
  },
  // Frontend JS files
  {
    files: ["frontend/src/**/*.js"],
    plugins: {
      ftFlow,
      prettier,
      "unused-imports": unusedImports
    },
    rules: {
      ...commonRules
    }
  },
  // Frontend JSX files
  {
    files: ["frontend/src/**/*.jsx"],
    plugins: {
      ftFlow,
      react,
      "jsx-runtime": jsxRuntime,
      "react-hooks": reactHooks,
      "react-refresh": reactRefresh,
      prettier,
      "unused-imports": unusedImports
    },
    rules: {
      ...jsxRuntime.rules,
      ...commonRules,
      "react/prop-types": "off",
      "react-refresh/only-export-components": "warn",
      "react/no-unescaped-entities": "warn",
      "react-hooks/exhaustive-deps": "off"
    }
  },
  // Frontend TypeScript files
  {
    files: ["frontend/src/**/*.ts"],
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        ecmaVersion: 2022,
        sourceType: "module",
        project: "./frontend/tsconfig.json"
      }
    },
    plugins: {
      "@typescript-eslint": typescriptPlugin,
      ftFlow,
      prettier,
      "unused-imports": unusedImports
    },
    rules: {
      ...commonRules,
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-unused-vars": [
        "warn",
        {
          argsIgnorePattern: "^_",
          varsIgnorePattern: "^_",
          caughtErrorsIgnorePattern: "^_",
          ignoreRestSiblings: true
        }
      ],
      "@typescript-eslint/ban-ts-comment": [
        "warn",
        {
          "ts-ignore": "allow-with-description",
          "ts-expect-error": "allow-with-description",
          "ts-nocheck": "allow-with-description",
          minimumDescriptionLength: 5
        }
      ],
      "@typescript-eslint/no-var-requires": "off",
      "@typescript-eslint/no-require-imports": "off",
      "@typescript-eslint/no-empty-object-type": "off",
      "@typescript-eslint/no-unsafe-function-type": "off",
      "@typescript-eslint/no-non-null-asserted-optional-chain": "off",
      "@typescript-eslint/no-unused-expressions": "off",
      "no-undef": "off",
      "no-unsafe-optional-chaining": "off",
      "no-constant-binary-expression": "off"
    }
  },
  // Frontend TypeScript JSX files
  {
    files: ["frontend/src/**/*.tsx"],
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        ecmaVersion: 2022,
        sourceType: "module",
        project: "./frontend/tsconfig.json",
        ecmaFeatures: { jsx: true }
      }
    },
    plugins: {
      "@typescript-eslint": typescriptPlugin,
      ftFlow,
      react,
      "jsx-runtime": jsxRuntime,
      "react-hooks": reactHooks,
      "react-refresh": reactRefresh,
      prettier,
      "unused-imports": unusedImports
    },
    rules: {
      ...jsxRuntime.rules,
      ...commonRules,
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-unused-vars": [
        "warn",
        {
          argsIgnorePattern: "^_",
          varsIgnorePattern: "^_",
          caughtErrorsIgnorePattern: "^_",
          ignoreRestSiblings: true
        }
      ],
      "@typescript-eslint/ban-ts-comment": [
        "warn",
        {
          "ts-ignore": "allow-with-description",
          "ts-expect-error": "allow-with-description",
          "ts-nocheck": "allow-with-description",
          minimumDescriptionLength: 5
        }
      ],
      "@typescript-eslint/no-var-requires": "off",
      "@typescript-eslint/no-require-imports": "off",
      "@typescript-eslint/no-empty-object-type": "off",
      "@typescript-eslint/no-unsafe-function-type": "off",
      "@typescript-eslint/no-non-null-asserted-optional-chain": "off",
      "@typescript-eslint/no-unused-expressions": "off",
      "no-undef": "off",
      "no-unsafe-optional-chaining": "off",
      "no-constant-binary-expression": "off",
      "react/prop-types": "off",
      "react-refresh/only-export-components": "warn",
      "react/no-unescaped-entities": "warn",
      "react-hooks/exhaustive-deps": "off"
    }
  },
  // Server JavaScript files (non-test)
  {
    files: [
      "server/endpoints/**/*.js",
      "server/models/**/*.js",
      "server/swagger/**/*.js",
      "server/utils/**/*.js",
      "server/index.js"
    ],
    plugins: {
      prettier,
      "unused-imports": unusedImports
    },
    rules: {
      ...commonRules,
      "react-hooks/rules-of-hooks": "off",
      "react/prop-types": "off",
      "react-refresh/only-export-components": "off"
    }
  },
  // Server TypeScript files (non-test)
  {
    files: ["server/**/*.ts", "server/**/*.tsx"],
    ignores: [
      "server/**/*.test.ts",
      "server/**/*.test.tsx",
      "server/**/*.spec.ts",
      "server/**/*.spec.tsx",
      "server/tests/**/*.ts",
      "server/tests/**/*.tsx"
    ],
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        ecmaVersion: 2022,
        sourceType: "module",
        project: "./server/tsconfig.json",
        tsconfigRootDir: import.meta.dirname
      }
    },
    plugins: {
      "@typescript-eslint": typescriptPlugin,
      prettier,
      "unused-imports": unusedImports
    },
    rules: {
      ...commonRules,
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-unused-vars": [
        "warn",
        {
          argsIgnorePattern: "^_",
          varsIgnorePattern: "^_",
          caughtErrorsIgnorePattern: "^_",
          ignoreRestSiblings: true
        }
      ],
      "@typescript-eslint/ban-ts-comment": [
        "warn",
        {
          "ts-ignore": "allow-with-description",
          "ts-expect-error": "allow-with-description",
          "ts-nocheck": "allow-with-description",
          minimumDescriptionLength: 5
        }
      ],
      "@typescript-eslint/no-var-requires": "off",
      "@typescript-eslint/no-require-imports": "off",
      "@typescript-eslint/no-empty-object-type": "off",
      "@typescript-eslint/no-unsafe-function-type": "off",
      "@typescript-eslint/no-non-null-asserted-optional-chain": "off",
      "@typescript-eslint/no-unused-expressions": "off",
      "no-undef": "off",
      "no-unsafe-optional-chaining": "off",
      "no-constant-binary-expression": "off",
      "react-hooks/rules-of-hooks": "off",
      "react/prop-types": "off",
      "react-refresh/only-export-components": "off"
    }
  },
  // TypeScript test files
  {
    files: [
      "**/*.test.ts",
      "**/*.test.tsx",
      "**/*.spec.ts",
      "**/*.spec.tsx",
      "frontend/src/setupTests.ts",
      "server/tests/**/*.ts"
    ],
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        ecmaVersion: 2022,
        sourceType: "module",
        project: ["./server/tsconfig.json", "./frontend/tsconfig.json"],
        tsconfigRootDir: import.meta.dirname
      },
      globals: {
        ...globals.jest,
        ...globals.node
      }
    },
    plugins: {
      "@typescript-eslint": typescriptPlugin,
      prettier,
      "unused-imports": unusedImports
    },
    rules: {
      ...commonRules,
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-unused-vars": [
        "warn",
        {
          argsIgnorePattern: "^_",
          varsIgnorePattern: "^_",
          caughtErrorsIgnorePattern: "^_",
          ignoreRestSiblings: true
        }
      ],
      "@typescript-eslint/ban-ts-comment": "off",
      "@typescript-eslint/no-var-requires": "off",
      "@typescript-eslint/no-require-imports": "off",
      "no-var": "off"
    }
  },
  // JavaScript test files
  {
    files: [
      "**/*.test.js",
      "**/*.test.jsx",
      "**/*.spec.js",
      "**/*.spec.jsx",
      "frontend/src/setupTests.js",
      "server/tests/**/*.js",
      "collector/jest.setup.js"
    ],
    languageOptions: {
      parser: hermesParser,
      parserOptions: {
        ecmaVersion: 2022,
        sourceType: "module",
        ecmaFeatures: { jsx: true }
      },
      globals: {
        ...globals.jest,
        ...globals.node
      }
    },
    plugins: {
      prettier,
      "unused-imports": unusedImports
    },
    rules: {
      ...commonRules,
      "no-var": "off"
    }
  },
  // Collector JavaScript files
  {
    files: ["collector/**/*.js"],
    ignores: ["collector/dist/**/*", "collector/node_modules/**/*"],
    plugins: {
      prettier,
      "unused-imports": unusedImports
    },
    rules: {
      ...commonRules,
      "react-hooks/rules-of-hooks": "off",
      "react/prop-types": "off",
      "react-refresh/only-export-components": "off"
    }
  },
  // Collector TypeScript files
  {
    files: ["collector/**/*.ts"],
    ignores: ["collector/dist/**/*", "collector/node_modules/**/*"],
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        ecmaVersion: 2022,
        sourceType: "module",
        project: "./collector/tsconfig.json"
      }
    },
    plugins: {
      "@typescript-eslint": typescriptPlugin,
      prettier,
      "unused-imports": unusedImports
    },
    rules: {
      ...commonRules,
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-unused-vars": [
        "warn",
        {
          argsIgnorePattern: "^_",
          varsIgnorePattern: "^_",
          caughtErrorsIgnorePattern: "^_",
          ignoreRestSiblings: true
        }
      ],
      "@typescript-eslint/ban-ts-comment": [
        "warn",
        {
          "ts-ignore": "allow-with-description",
          "ts-expect-error": "allow-with-description",
          "ts-nocheck": "allow-with-description",
          minimumDescriptionLength: 5
        }
      ],
      "@typescript-eslint/no-var-requires": "off",
      "@typescript-eslint/no-require-imports": "off",
      "@typescript-eslint/no-empty-object-type": "off",
      "@typescript-eslint/no-unsafe-function-type": "off",
      "@typescript-eslint/no-non-null-asserted-optional-chain": "off",
      "@typescript-eslint/no-unused-expressions": "off",
      "no-undef": "off",
      "no-unsafe-optional-chaining": "off",
      "no-constant-binary-expression": "off",
      "react-hooks/rules-of-hooks": "off",
      "react/prop-types": "off",
      "react-refresh/only-export-components": "off"
    }
  },
  // Playwright root-level test files
  {
    files: ["test-playwright.spec.ts"],
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        ecmaVersion: 2022,
        sourceType: "module",
        project: "./tsconfig.playwright.json",
        tsconfigRootDir: import.meta.dirname
      },
      globals: {
        ...globals.node
      }
    },
    plugins: {
      "@typescript-eslint": typescriptPlugin,
      prettier,
      "unused-imports": unusedImports
    },
    rules: {
      ...commonRules,
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-unused-vars": [
        "warn",
        {
          argsIgnorePattern: "^_",
          varsIgnorePattern: "^_",
          caughtErrorsIgnorePattern: "^_",
          ignoreRestSiblings: true
        }
      ],
      "@typescript-eslint/ban-ts-comment": "off",
      "@typescript-eslint/no-var-requires": "off",
      "@typescript-eslint/no-require-imports": "off",
      "no-var": "off"
    }
  },
  // Playwright e2e-playwright directory test files
  {
    files: ["e2e-playwright/**/*.ts", "e2e-playwright/**/*.spec.ts"],
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        ecmaVersion: 2022,
        sourceType: "module",
        project: "./tsconfig.playwright.json",
        tsconfigRootDir: import.meta.dirname
      },
      globals: {
        ...globals.node
      }
    },
    plugins: {
      "@typescript-eslint": typescriptPlugin,
      prettier,
      "unused-imports": unusedImports
    },
    rules: {
      ...commonRules,
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-unused-vars": [
        "warn",
        {
          argsIgnorePattern: "^_",
          varsIgnorePattern: "^_",
          caughtErrorsIgnorePattern: "^_",
          ignoreRestSiblings: true
        }
      ],
      "@typescript-eslint/ban-ts-comment": "off",
      "@typescript-eslint/no-var-requires": "off",
      "@typescript-eslint/no-require-imports": "off",
      "no-var": "off"
    }
  },
  // Ignore all dist directories
  {
    ignores: ["**/dist/**/*", "**/node_modules/**/*"]
  }
]
