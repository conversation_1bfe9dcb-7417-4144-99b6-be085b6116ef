import js from "@eslint/js"
import react from "eslint-plugin-react"
import reactHooks from "eslint-plugin-react-hooks"
import reactRefresh from "eslint-plugin-react-refresh"
import unusedImports from "eslint-plugin-unused-imports"
import prettier from "eslint-config-prettier"
import globals from "globals"
import tsParser from "@typescript-eslint/parser"
import typescript from "@typescript-eslint/eslint-plugin"

export default [
  // Configuration for TypeScript files
  {
    files: ["src/**/*.{ts,tsx}"],
    ignores: [
      "dist/**",
      "node_modules/**",
      "public/**",
      "*.d.ts",
      "vite.config.js",
      "tailwind.config.js",
      "postcss.config.js",
      "jest.config.cjs",
      "babel.config.cjs",
      "**/*.test.*",
      "**/*.spec.*"
    ],
    languageOptions: {
      parser: tsParser,
      ecmaVersion: 2020,
      sourceType: "module",
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.es2020,
        // TypeScript globals
        React: "readonly",
        JSX: "readonly",
        Record: "readonly",
        Partial: "readonly",
        Omit: "readonly",
        Pick: "readonly",
        Exclude: "readonly",
        Extract: "readonly",
        NonNullable: "readonly",
        ReturnType: "readonly",
        Parameters: "readonly",
        ConstructorParameters: "readonly",
        InstanceType: "readonly",
        ThisParameterType: "readonly",
        OmitThisParameter: "readonly",
        ThisType: "readonly",
        Readonly: "readonly",
        Required: "readonly",
        // Node.js globals
        NodeJS: "readonly",
        HeadersInit: "readonly",
        // Browser globals
        FileItem: "readonly",
        unknown: "readonly",
        object: "readonly"
      },
      parserOptions: {
        ecmaFeatures: {
          jsx: true
        },
        project: "./tsconfig.json"
      }
    },
    plugins: {
      react,
      "react-hooks": reactHooks,
      "react-refresh": reactRefresh,
      "unused-imports": unusedImports,
      "@typescript-eslint": typescript
    },
    rules: {
      ...js.configs.recommended.rules,
      ...react.configs.recommended.rules,
      ...reactHooks.configs.recommended.rules,

      // React specific
      "react/react-in-jsx-scope": "off",
      "react/prop-types": "off",
      "react/jsx-uses-react": "off",
      "react/jsx-uses-vars": "error",

      // TypeScript specific
      "no-undef": "off", // TypeScript handles this
      "no-unused-vars": "off",
      "@typescript-eslint/no-explicit-any": "off", // Disable TypeScript any warnings
      "unused-imports/no-unused-imports": "error",
      "unused-imports/no-unused-vars": [
        "warn",
        {
          vars: "all",
          varsIgnorePattern: "^_",
          args: "after-used",
          argsIgnorePattern: "^_"
        }
      ],

      // General
      "no-console": ["warn", { allow: ["error"] }],
      "no-debugger": "warn",
      "prefer-const": "error",
      "no-var": "error"
    },
    settings: {
      react: {
        version: "detect"
      }
    }
  },
  // Configuration for test files
  {
    files: ["**/*.test.{ts,tsx}", "**/*.spec.{ts,tsx}"],
    ignores: [
      "dist/**",
      "node_modules/**",
      "public/**",
      "*.d.ts",
      "eslint.config.js",
      "vite.config.js",
      "tailwind.config.js",
      "postcss.config.js",
      "jest.config.cjs",
      "babel.config.cjs"
    ],
    languageOptions: {
      parser: tsParser,
      ecmaVersion: 2020,
      sourceType: "module",
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.es2020,
        // TypeScript globals
        React: "readonly",
        JSX: "readonly",
        Record: "readonly",
        Partial: "readonly",
        Omit: "readonly",
        Pick: "readonly",
        Exclude: "readonly",
        Extract: "readonly",
        NonNullable: "readonly",
        ReturnType: "readonly",
        Parameters: "readonly",
        ConstructorParameters: "readonly",
        InstanceType: "readonly",
        ThisParameterType: "readonly",
        OmitThisParameter: "readonly",
        ThisType: "readonly",
        Readonly: "readonly",
        Required: "readonly",
        // Node.js globals
        NodeJS: "readonly",
        HeadersInit: "readonly",
        // Browser globals
        FileItem: "readonly",
        unknown: "readonly",
        object: "readonly",
        // Jest globals
        jest: "readonly",
        describe: "readonly",
        it: "readonly",
        test: "readonly",
        expect: "readonly",
        beforeEach: "readonly",
        afterEach: "readonly",
        beforeAll: "readonly",
        afterAll: "readonly"
      },
      parserOptions: {
        ecmaFeatures: {
          jsx: true
        },
        project: "./tsconfig.test.json"
      }
    },
    plugins: {
      react,
      "react-hooks": reactHooks,
      "react-refresh": reactRefresh,
      "unused-imports": unusedImports,
      "@typescript-eslint": typescript
    },
    rules: {
      ...js.configs.recommended.rules,
      ...react.configs.recommended.rules,
      ...reactHooks.configs.recommended.rules,

      // React specific
      "react/react-in-jsx-scope": "off",
      "react/prop-types": "off",
      "react/jsx-uses-react": "off",
      "react/jsx-uses-vars": "error",

      // TypeScript specific
      "no-undef": "off", // TypeScript handles this
      "no-unused-vars": "off",
      "@typescript-eslint/no-explicit-any": "off", // Disable TypeScript any warnings
      "unused-imports/no-unused-imports": "error",
      "unused-imports/no-unused-vars": [
        "warn",
        {
          vars: "all",
          varsIgnorePattern: "^_",
          args: "after-used",
          argsIgnorePattern: "^_"
        }
      ],

      // General
      "no-console": ["warn", { allow: ["error"] }],
      "no-debugger": "warn",
      "prefer-const": "error",
      "no-var": "error"
    },
    settings: {
      react: {
        version: "detect"
      }
    }
  },
  // Configuration for JavaScript files (including config files)
  {
    files: ["**/*.{js,jsx}"],
    ignores: ["dist/**", "node_modules/**", "public/**"],
    languageOptions: {
      ecmaVersion: 2020,
      sourceType: "module",
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.es2020,
        // Common globals
        React: "readonly",
        JSX: "readonly",
        module: "readonly",
        require: "readonly",
        __dirname: "readonly",
        __filename: "readonly",
        process: "readonly",
        Buffer: "readonly",
        global: "readonly",
        console: "readonly",
        // Jest globals for test files
        jest: "readonly",
        describe: "readonly",
        it: "readonly",
        test: "readonly",
        expect: "readonly",
        beforeEach: "readonly",
        afterEach: "readonly",
        beforeAll: "readonly",
        afterAll: "readonly"
      },
      parserOptions: {
        ecmaFeatures: {
          jsx: true
        }
      }
    },
    plugins: {
      react,
      "react-hooks": reactHooks,
      "unused-imports": unusedImports
    },
    rules: {
      ...js.configs.recommended.rules,
      ...react.configs.recommended.rules,
      ...reactHooks.configs.recommended.rules,

      // React specific
      "react/react-in-jsx-scope": "off",
      "react/prop-types": "off",
      "react/jsx-uses-react": "off",
      "react/jsx-uses-vars": "error",

      // JavaScript specific
      "no-unused-vars": "off",
      "unused-imports/no-unused-imports": "error",
      "unused-imports/no-unused-vars": [
        "warn",
        {
          vars: "all",
          varsIgnorePattern: "^_",
          args: "after-used",
          argsIgnorePattern: "^_"
        }
      ],

      // General (allow console in scripts)
      "no-console": "off",
      "no-debugger": "warn",
      "prefer-const": "error",
      "no-var": "error"
    },
    settings: {
      react: {
        version: "detect"
      }
    }
  },
  prettier
]
