module.exports = {
  testEnvironment: "jsdom",
  globals: {
    "import.meta": {
      env: {
        VITE_API_BASE: "/api"
      }
    }
  },
  transform: {
    "^.+\\.(js|jsx|mjs|cjs|ts|tsx)$": [
      "babel-jest",
      {
        presets: [
          [
            require.resolve("@babel/preset-env"),
            { targets: { node: "current" }, modules: "commonjs" },
          ],
          [require.resolve("@babel/preset-react"), { runtime: "automatic" }],
          require.resolve("@babel/preset-typescript"),
        ],
        plugins: []
      },
    ],
  },
  moduleNameMapper: {
    "\\.(css|less|scss|sass)$": "identity-obj-proxy",
    "\\.(png|jpg|jpeg|gif|svg|webp|ico|bmp|tiff)$": "<rootDir>/src/__mocks__/fileMock.js",
    "@/media/(.*)\\.(png|jpg|jpeg|gif|svg|webp|ico|bmp|tiff)$": "<rootDir>/src/__mocks__/fileMock.js",
    "@/utils/piperTTS": "<rootDir>/src/__mocks__/piperTTS.js",
    "^@/(.*)$": "<rootDir>/src/$1",
    "react-markdown": "<rootDir>/src/__mocks__/react-markdown.js",
    "remark-gfm": "<rootDir>/src/__mocks__/remark-gfm.js",
    "rehype-raw": "<rootDir>/src/__mocks__/rehype-raw.js",
    "react-syntax-highlighter": "<rootDir>/src/__mocks__/react-syntax-highlighter.js",
    "react-syntax-highlighter/(.*)": "<rootDir>/src/__mocks__/react-syntax-highlighter.js",
    "react-speech-recognition": "<rootDir>/src/__mocks__/react-speech-recognition.js",
    "react-dnd": "<rootDir>/src/__mocks__/react-dnd.js",
    "react-dnd-html5-backend": "<rootDir>/src/__mocks__/react-dnd-html5-backend.js",
    "immutability-helper": "<rootDir>/src/__mocks__/immutability-helper.js",
  },
  modulePathIgnorePatterns: ["<rootDir>/dist/"],
  extensionsToTreatAsEsm: [".ts", ".tsx"],
  setupFilesAfterEnv: ["<rootDir>/src/setupTests.ts"],
  testMatch: [
    "**/__tests__/**/*.test.(js|jsx|ts|tsx)",
    "**/?(*.)+(spec|test).(js|jsx|ts|tsx)",
  ],
  moduleFileExtensions: ["js", "jsx", "json", "mjs", "cjs", "ts", "tsx"],
  testPathIgnorePatterns: ["/node_modules/", "/dist/"],
  transformIgnorePatterns: [
    "/node_modules/(?!(zustand|immer|@testing-library|react-markdown|remark-gfm|rehype-raw|devlop|micromark|mdast-util|unist-util|hast-util|property-information|space-separated-tokens|comma-separated-tokens|vfile|unified|bail|is-plain-obj|trough|remark-parse|remark-rehype|rehype-stringify|mdast-util-to-hast|html-void-elements|zwitch|longest-streak|mdast-util-to-string|decode-named-character-reference|character-entities|trim-lines|markdown-table|ccount|escape-string-regexp|unist-builder|mdast-util-definitions|web-namespaces|hast-util-to-jsx-runtime|estree-util)/)",
  ],
  collectCoverageFrom: [
    "src/stores/attachmentStore.ts",
    "src/stores/progressStore.ts",
    "src/hooks/useThreadProgress.ts",
    "src/components/ChatProgress/index.tsx",
    "src/components/ProgressList/index.tsx",
    "src/utils/events.ts",
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
