/**
 * Global Jest Setup for Frontend
 *
 * This file runs once before all test suites.
 * Sets up the test environment with proper memory limits.
 */

module.exports = async () => {
  // Set Node.js memory limit if not already set
  if (!process.env.NODE_OPTIONS || !process.env.NODE_OPTIONS.includes("--max-old-space-size")) {
    process.env.NODE_OPTIONS = `${process.env.NODE_OPTIONS || ""} --max-old-space-size=2048`.trim();
  }
  
  // Note: We cannot use jest.spyOn() in globalSetup as Jest is not fully initialized here.
  // Console warning suppression is handled in setupTests.ts using the shared utility.
  
  console.log("Running frontend jest global setup...");
  console.log(`NODE_OPTIONS: ${process.env.NODE_OPTIONS}`);
};