module.exports = async () => {
  // Force garbage collection if available
  if (global.gc) {
    global.gc();
    console.log("Forced garbage collection in global teardown");
  }

  // Clear all timers
  if (global.setTimeout) {
    for (let i = 1; i < 100000; i++) {
      try {
        clearTimeout(i);
        clearInterval(i);
      } catch {
        // Ignore errors
      }
    }
  }

  // Remove all process listeners
  const events = [
    "exit",
    "SIGINT",
    "SIGTERM",
    "SIGUSR1",
    "SIGUSR2",
    "uncaughtException",
    "unhandledRejection",
    "beforeExit",
  ];
  events.forEach((event) => {
    try {
      process.removeAllListeners(event);
    } catch {
      // Ignore errors
    }
  });

  // Wait a bit for any remaining async operations
  await new Promise((resolve) => setTimeout(resolve, 100));
};