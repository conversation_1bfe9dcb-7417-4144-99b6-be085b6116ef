import { render, screen, waitFor } from "@testing-library/react";
import { ContextWrapper } from "../AuthProvider";
import { useInitialData } from "../hooks/useInitialData";
import useSystemSettingsStore from "../stores/settingsStore";
import useWorkspaceStore from "../stores/workspaceStore";
import { AUTH_TOKEN, AUTH_USER } from "../utils/constants";

// Mock dependencies
jest.mock("../hooks/useInitialData");
jest.mock("../stores/settingsStore");
jest.mock("../stores/workspaceStore");

// Mock the actual store implementations
const mockFetchAllSettings = jest.fn();
const mockFetchModuleWorkspaces = jest.fn();
const mockFetchPopulatedWorkspaces = jest.fn();

describe("AuthProvider", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();

    // Setup store mocks - need to mock the hook selector pattern
    (useSystemSettingsStore as unknown as jest.Mock).mockImplementation(
      (selector) => {
        const state = {
          fetchAllSettings: mockFetchAllSettings,
        };
        return selector ? selector(state) : state;
      }
    );

    (useWorkspaceStore as unknown as jest.Mock).mockImplementation(
      (selector) => {
        const state = {
          fetchModuleWorkspaces: mockFetchModuleWorkspaces,
          fetchPopulatedWorkspaces: mockFetchPopulatedWorkspaces,
        };
        return selector ? selector(state) : state;
      }
    );
  });

  afterEach(() => {
    localStorage.clear();
  });

  it("should use useInitialData hook on mount", async () => {
    const mockUseInitialData = jest.mocked(useInitialData);
    mockUseInitialData.mockReturnValue({ loading: false, error: null });

    render(
      <ContextWrapper>
        <div>Test Child</div>
      </ContextWrapper>
    );

    expect(mockUseInitialData).toHaveBeenCalled();
  });

  it("should wait for initial data to load before fetching additional data", async () => {
    const mockUseInitialData = jest.mocked(useInitialData);

    // Set up authenticated user BEFORE rendering
    const mockUser = { id: 1, username: "testuser", role: "default" };
    localStorage.setItem(AUTH_USER, JSON.stringify(mockUser));
    localStorage.setItem(AUTH_TOKEN, "test-token");

    // Start with loading state
    mockUseInitialData.mockReturnValue({ loading: true, error: null });

    const { rerender } = render(
      <ContextWrapper>
        <div>Test Child</div>
      </ContextWrapper>
    );

    // Should not fetch additional data while loading
    expect(mockFetchModuleWorkspaces).not.toHaveBeenCalled();
    expect(mockFetchAllSettings).not.toHaveBeenCalled();

    // Simulate initial data loading complete
    mockUseInitialData.mockReturnValue({ loading: false, error: null });

    // Force re-render with new loading state
    rerender(
      <ContextWrapper>
        <div>Test Child</div>
      </ContextWrapper>
    );

    await waitFor(() => {
      expect(mockFetchModuleWorkspaces).toHaveBeenCalledWith(false);
    });

    // Non-admin users don't fetch all settings
    expect(mockFetchAllSettings).not.toHaveBeenCalled();
  });

  it("should not fetch populated workspaces after initial data loads", async () => {
    const mockUseInitialData = jest.mocked(useInitialData);
    mockUseInitialData.mockReturnValue({ loading: false, error: null });

    // Set up authenticated user
    const mockUser = { id: 1, username: "testuser", role: "default" };
    localStorage.setItem(AUTH_USER, JSON.stringify(mockUser));
    localStorage.setItem(AUTH_TOKEN, "test-token");

    render(
      <ContextWrapper>
        <div>Test Child</div>
      </ContextWrapper>
    );

    await waitFor(() => {
      // Should NOT call fetchPopulatedWorkspaces as it's handled by useInitialData
      expect(mockFetchPopulatedWorkspaces).not.toHaveBeenCalled();
      // Should still fetch module workspaces
      expect(mockFetchModuleWorkspaces).toHaveBeenCalled();
    });
  });

  it("should fetch all settings for admin users", async () => {
    const mockUseInitialData = jest.mocked(useInitialData);
    mockUseInitialData.mockReturnValue({ loading: false, error: null });

    // Set up admin user
    const mockUser = { id: 1, username: "admin", role: "admin" };
    localStorage.setItem(AUTH_USER, JSON.stringify(mockUser));
    localStorage.setItem(AUTH_TOKEN, "test-token");

    render(
      <ContextWrapper>
        <div>Test Child</div>
      </ContextWrapper>
    );

    await waitFor(() => {
      // Admin users should fetch all settings with force=false
      expect(mockFetchAllSettings).toHaveBeenCalledWith(false);
      expect(mockFetchModuleWorkspaces).toHaveBeenCalledWith(false);
    });
  });

  it("should handle initial data loading error", async () => {
    // Set up authenticated user BEFORE rendering
    const mockUser = { id: 1, username: "testuser", role: "default" };
    localStorage.setItem(AUTH_USER, JSON.stringify(mockUser));
    localStorage.setItem(AUTH_TOKEN, "test-token");

    const mockUseInitialData = jest.mocked(useInitialData);
    mockUseInitialData.mockReturnValue({
      loading: false,
      error: "Failed to load initial data",
    });

    render(
      <ContextWrapper>
        <div>Test Child</div>
      </ContextWrapper>
    );

    await waitFor(() => {
      // Should still attempt to fetch additional data even if initial data failed
      expect(mockFetchModuleWorkspaces).toHaveBeenCalledWith(false);
    });

    // Non-admin users don't fetch all settings
    expect(mockFetchAllSettings).not.toHaveBeenCalled();
  });

  it("should not fetch data for unauthenticated users", async () => {
    const mockUseInitialData = jest.mocked(useInitialData);
    mockUseInitialData.mockReturnValue({ loading: false, error: null });

    // No user in localStorage

    render(
      <ContextWrapper>
        <div>Test Child</div>
      </ContextWrapper>
    );

    await waitFor(() => {
      expect(mockFetchModuleWorkspaces).not.toHaveBeenCalled();
      expect(mockFetchAllSettings).not.toHaveBeenCalled();
    });
  });

  it("should provide auth context to children", () => {
    const mockUseInitialData = jest.mocked(useInitialData);
    mockUseInitialData.mockReturnValue({ loading: false, error: null });

    const TestChild = () => {
      return <div>Test Child Rendered</div>;
    };

    render(
      <ContextWrapper>
        <TestChild />
      </ContextWrapper>
    );

    expect(screen.getByText("Test Child Rendered")).toBeInTheDocument();
  });

  it("should handle race conditions between initial data and auth state", async () => {
    const mockUseInitialData = jest.mocked(useInitialData);

    // Start without user
    localStorage.clear();

    // Start with loading
    mockUseInitialData.mockReturnValue({ loading: true, error: null });

    render(
      <ContextWrapper>
        <div>Test Child</div>
      </ContextWrapper>
    );

    // Should not fetch while no user
    expect(mockFetchModuleWorkspaces).not.toHaveBeenCalled();

    // Simulate user login while initial data is still loading
    const mockUser = { id: 1, username: "testuser", role: "default" };
    localStorage.setItem(AUTH_USER, JSON.stringify(mockUser));
    localStorage.setItem(AUTH_TOKEN, "test-token");

    // Still loading, so should not fetch yet
    expect(mockFetchModuleWorkspaces).not.toHaveBeenCalled();

    // Simulate initial data finished loading
    mockUseInitialData.mockReturnValue({ loading: false, error: null });

    // Need to create a new instance to pick up localStorage changes
    render(
      <ContextWrapper>
        <div>Test Child Updated</div>
      </ContextWrapper>
    );

    await waitFor(() => {
      // Should fetch additional data after both conditions are met
      expect(mockFetchModuleWorkspaces).toHaveBeenCalledWith(false);
    });

    // Non-admin user doesn't fetch all settings
    expect(mockFetchAllSettings).not.toHaveBeenCalled();
  });

  it("should only fetch data once even with multiple re-renders", async () => {
    const mockUseInitialData = jest.mocked(useInitialData);
    mockUseInitialData.mockReturnValue({ loading: false, error: null });

    const mockUser = { id: 1, username: "testuser", role: "default" };
    localStorage.setItem(AUTH_USER, JSON.stringify(mockUser));
    localStorage.setItem(AUTH_TOKEN, "test-token");

    const { rerender } = render(
      <ContextWrapper>
        <div>Test Child</div>
      </ContextWrapper>
    );

    await waitFor(() => {
      expect(mockFetchModuleWorkspaces).toHaveBeenCalledTimes(1);
    });

    // Clear the mock to ensure we're counting fresh
    mockFetchModuleWorkspaces.mockClear();

    // Multiple re-renders
    rerender(
      <ContextWrapper>
        <div>Test Child Updated</div>
      </ContextWrapper>
    );

    rerender(
      <ContextWrapper>
        <div>Test Child Updated Again</div>
      </ContextWrapper>
    );

    // Wait a bit to ensure no additional calls
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Should not have been called again
    expect(mockFetchModuleWorkspaces).not.toHaveBeenCalled();
    // Non-admin users never call fetchAllSettings
    expect(mockFetchAllSettings).not.toHaveBeenCalled();
  });
});
