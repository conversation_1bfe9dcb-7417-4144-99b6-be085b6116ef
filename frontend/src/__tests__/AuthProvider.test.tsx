import { render, screen, waitFor } from "@testing-library/react";
import { ContextWrapper } from "../AuthProvider";
import { useInitialData } from "../hooks/useInitialData";
import useSystemSettingsStore from "../stores/settingsStore";
import useWorkspaceStore from "../stores/workspaceStore";

// Mock dependencies
jest.mock("../hooks/useInitialData");
jest.mock("../stores/settingsStore");
jest.mock("../stores/workspaceStore");

// Mock constants to avoid importing the entire constants file
jest.mock("../utils/constants", () => ({
  AUTH_USER: "istlegal_user",
  AUTH_TOKEN: "istlegal_authToken",
  AUTH_TIMESTAMP: "istlegal_authTimestamp",
}));

describe("AuthProvider", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();

    // Setup minimal store mocks for the remaining tests
    (useSystemSettingsStore as unknown as jest.Mock).mockReturnValue(jest.fn());
    (useWorkspaceStore as unknown as jest.Mock).mockReturnValue(jest.fn());
  });

  afterEach(() => {
    localStorage.clear();
    jest.clearAllTimers();
    jest.useRealTimers();
    jest.restoreAllMocks();
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
  });

  it("should use useInitialData hook on mount", async () => {
    const mockUseInitialData = jest.mocked(useInitialData);
    mockUseInitialData.mockReturnValue({ loading: false, error: null });

    render(
      <ContextWrapper>
        <div>Test Child</div>
      </ContextWrapper>
    );

    expect(mockUseInitialData).toHaveBeenCalled();
  });

  it("should not fetch data for unauthenticated users", async () => {
    const mockUseInitialData = jest.mocked(useInitialData);
    mockUseInitialData.mockReturnValue({ loading: false, error: null });

    // No user in localStorage

    render(
      <ContextWrapper>
        <div>Test Child</div>
      </ContextWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText("Test Child")).toBeInTheDocument();
    });
  });

  it("should provide auth context to children", () => {
    const mockUseInitialData = jest.mocked(useInitialData);
    mockUseInitialData.mockReturnValue({ loading: false, error: null });

    const TestChild = () => {
      return <div>Test Child Rendered</div>;
    };

    render(
      <ContextWrapper>
        <TestChild />
      </ContextWrapper>
    );

    expect(screen.getByText("Test Child Rendered")).toBeInTheDocument();
  });
});
