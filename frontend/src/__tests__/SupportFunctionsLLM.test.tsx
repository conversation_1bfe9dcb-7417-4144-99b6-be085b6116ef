// Mock the system model
jest.mock("../models/system", () => ({
  __esModule: true,
  default: {
    setSupportFunctionsLLM: jest.fn(),
    keys: jest.fn(),
  },
}));

// Remove unused React import
import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import { BrowserRouter } from "react-router-dom";
import "@testing-library/jest-dom";
import SupportFunctionsLLM from "../pages/GeneralSettings/SupportFunctionsLLM";
import System from "../models/system";

// Mock the toast system
jest.mock("../utils/toast", () => jest.fn());

// Mock the translation system
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string, options?: Record<string, unknown>) => {
      if (options && typeof options === "object") {
        let result = key;
        Object.keys(options).forEach((optionKey) => {
          result = result.replace(
            `{{${optionKey}}}`,
            String(options[optionKey])
          );
        });
        return result;
      }
      return key;
    },
  }),
}));

// Mock paths utility
jest.mock("../utils/paths", () => ({
  home: () => "/",
}));

// Mock components
jest.mock("../components/SettingsSidebar", () => {
  return function MockSidebar() {
    return <div data-testid="sidebar">Sidebar</div>;
  };
});

jest.mock("../components/HeaderWorkspace", () => {
  return function MockHeaderWorkspace() {
    return <div data-testid="header-workspace">Header</div>;
  };
});

// Mock the Preloader component to be conditionally rendered
jest.mock("../components/Preloader", () => {
  return function MockPreLoader() {
    return <div data-testid="preloader">Loading...</div>;
  };
});

interface MockButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  type?: "button" | "submit" | "reset";
  [key: string]: unknown;
}

jest.mock("../components/Button", () => ({
  Button: function MockButton({
    children,
    onClick,
    disabled,
    type,
    ...props
  }: MockButtonProps) {
    return (
      <button
        onClick={onClick}
        disabled={disabled}
        type={type}
        data-testid="save-button"
        {...props}
      >
        {children}
      </button>
    );
  },
}));

interface MockToggleProps {
  label: string;
  description?: string;
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
}

jest.mock("../components/ui/Toggle", () => {
  return function MockToggle({
    label,
    description,
    checked,
    onCheckedChange,
  }: MockToggleProps) {
    return (
      <div data-testid="toggle">
        <label>
          <input
            type="checkbox"
            checked={checked}
            onChange={(e) => onCheckedChange(e.target.checked)}
            data-testid={`toggle-${
              label.includes("prompt-upgrade")
                ? "prompt-upgrade"
                : label.includes("validation")
                  ? "validation"
                  : "manual-time"
            }`}
          />
          {label}
        </label>
        {description && <p>{description}</p>}
      </div>
    );
  };
});

interface MockBaseLLMPreferenceProps {
  selectedLLM: string | null;
  onLLMChange: (value: string) => void;
  settings?: Record<string, unknown>;
  setHasChanges?: (value: boolean) => void;
}

jest.mock("../components/LLMSelection/BaseLLMPreference", () => {
  return function MockBaseLLMPreference({
    selectedLLM,
    onLLMChange,
    settings: _settings,
    setHasChanges,
  }: MockBaseLLMPreferenceProps) {
    return (
      <div data-testid="base-llm-preference">
        <div data-testid="provider-key">LLMProvider_SUPPORT</div>
        <input
          data-testid="llm-provider-input"
          value={selectedLLM || ""}
          onChange={(e) => {
            onLLMChange(e.target.value);
            if (setHasChanges) setHasChanges(true);
          }}
          placeholder="Select LLM provider"
        />
      </div>
    );
  };
});

// Mock @phosphor-icons/react
jest.mock("@phosphor-icons/react", () => ({
  ArrowLeft: function MockArrowLeft(
    props: React.HTMLAttributes<HTMLSpanElement>
  ) {
    return (
      <span data-testid="arrow-left" {...props}>
        ←
      </span>
    );
  },
}));

// Test wrapper component
interface TestWrapperProps {
  children: React.ReactNode;
}

const TestWrapper = ({ children }: TestWrapperProps) => (
  <BrowserRouter
    future={{
      v7_startTransition: true,
      v7_relativeSplatPath: true,
    }}
  >
    {children}
  </BrowserRouter>
);

describe("SupportFunctionsLLM Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Suppress React Router future flag warnings
    const originalError = console.error;
    console.error = (...args: unknown[]) => {
      const firstArg = args[0];
      if (
        typeof firstArg === "string" &&
        firstArg.includes("React Router Future Flag Warning")
      ) {
        return;
      }
      originalError.apply(console, args);
    };

    // Setup default mock responses with immediate resolution
    (System.setSupportFunctionsLLM as jest.Mock).mockResolvedValue({
      success: true,
    });

    // Make System.keys return immediately resolved promise
    const defaultSettings = {
      LLMProvider_SUPPORT: "openai",
      supportFunctionPromptUpgrade: false,
      supportFunctionValidation: false,
      supportFunctionManualTime: false,
    };
    (System.keys as jest.Mock).mockResolvedValue(defaultSettings);
  });

  afterEach(() => {
    // Restore console.error
    jest.restoreAllMocks();
  });

  const renderComponent = async (mockSettings = {}) => {
    if (Object.keys(mockSettings).length > 0) {
      (System.keys as jest.Mock).mockResolvedValue(mockSettings);
    }

    let result: any;
    await act(async () => {
      result = render(
        <TestWrapper>
          <SupportFunctionsLLM />
        </TestWrapper>
      );

      // Give time for useEffect to run
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    // Wait for the component to finish loading
    await waitFor(
      () => {
        expect(screen.queryByTestId("preloader")).not.toBeInTheDocument();
      },
      { timeout: 3000 }
    );

    return result;
  };

  test("renders the support functions LLM page", async () => {
    await renderComponent();

    expect(screen.getByText("support-functions-llm.title")).toBeInTheDocument();
    expect(
      screen.getByText("support-functions-llm.description")
    ).toBeInTheDocument();
    expect(screen.getByTestId("base-llm-preference")).toBeInTheDocument();
  });

  test("displays loading state initially", () => {
    act(() => {
      render(
        <TestWrapper>
          <SupportFunctionsLLM />
        </TestWrapper>
      );
    });

    expect(screen.getByTestId("preloader")).toBeInTheDocument();
  });

  test("loads settings on mount", async () => {
    await renderComponent();

    expect(System.keys).toHaveBeenCalledWith(false);
  });

  test("displays LLM provider selection", async () => {
    await renderComponent();

    const providerInput = screen.getByTestId("llm-provider-input");
    expect(providerInput).toBeInTheDocument();
    expect(screen.getByTestId("provider-key")).toHaveTextContent(
      "LLMProvider_SUPPORT"
    );
  });

  test("displays support function toggles", async () => {
    await renderComponent();

    expect(screen.getByTestId("toggle-prompt-upgrade")).toBeInTheDocument();
    expect(screen.getByTestId("toggle-validation")).toBeInTheDocument();
    expect(screen.getByTestId("toggle-manual-time")).toBeInTheDocument();

    expect(
      screen.getByText("support-functions-llm.prompt-upgrade.title")
    ).toBeInTheDocument();
    expect(
      screen.getByText("support-functions-llm.validation.title")
    ).toBeInTheDocument();
    expect(
      screen.getByText("support-functions-llm.manual-time.title")
    ).toBeInTheDocument();
  });

  test("save button is disabled when no changes are made", async () => {
    await renderComponent();

    // Initially no save button should be visible since hasChanges is false
    expect(screen.queryByTestId("save-button")).not.toBeInTheDocument();
  });

  test("save button appears when changes are made", async () => {
    await renderComponent();

    const promptUpgradeInput = screen.getByTestId("toggle-prompt-upgrade");
    fireEvent.click(promptUpgradeInput);

    await waitFor(() => {
      const saveButton = screen.getByTestId("save-button");
      expect(saveButton).toBeInTheDocument();
      expect(saveButton).not.toBeDisabled();
    });
  });

  test("toggles can be enabled and disabled", async () => {
    await renderComponent();

    const promptUpgradeInput = screen.getByTestId("toggle-prompt-upgrade");
    const validationInput = screen.getByTestId("toggle-validation");
    const manualTimeInput = screen.getByTestId("toggle-manual-time");

    expect(promptUpgradeInput).not.toBeChecked();
    expect(validationInput).not.toBeChecked();
    expect(manualTimeInput).not.toBeChecked();

    fireEvent.click(promptUpgradeInput);
    fireEvent.click(validationInput);

    expect(promptUpgradeInput).toBeChecked();
    expect(validationInput).toBeChecked();
    expect(manualTimeInput).not.toBeChecked();
  });

  test("LLM provider can be selected", async () => {
    await renderComponent();

    const providerInput = screen.getByTestId(
      "llm-provider-input"
    ) as HTMLInputElement;

    fireEvent.change(providerInput, { target: { value: "anthropic" } });

    expect(providerInput.value).toBe("anthropic");

    await waitFor(() => {
      const saveButton = screen.getByTestId("save-button");
      expect(saveButton).not.toBeDisabled();
    });
  });

  test("calls setSupportFunctionsLLM on save", async () => {
    const mockSettings = {
      LLMProvider_SUPPORT: "openai",
      OpenAiModelPref_SUPPORT: "gpt-4o-mini",
      supportFunctionPromptUpgrade: true,
      supportFunctionValidation: false,
      supportFunctionManualTime: true,
    };

    await renderComponent(mockSettings);

    // Make a change to enable the save button (just toggle change, no LLM change)
    const toggleElement = screen.getByTestId("toggle-prompt-upgrade");
    fireEvent.click(toggleElement);

    await waitFor(() => {
      expect(screen.getByTestId("save-button")).toBeInTheDocument();
    });

    const saveButton = screen.getByTestId("save-button");
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(System.setSupportFunctionsLLM).toHaveBeenCalledWith({
        // No LLM provider included since selectedLLM === settings.LLMProvider_SUPPORT
        // Model preferences included because selectedLLM is "openai" and the pref exists
        OpenAiModelPref_SUPPORT: "gpt-4o-mini",
        supportFunctionPromptUpgrade: false, // Will be toggled from true to false
        supportFunctionValidation: false,
        supportFunctionManualTime: true,
      });
    });
  });

  test("should have proper component structure", async () => {
    await renderComponent();

    // Should have sidebar and header
    expect(screen.getByTestId("sidebar")).toBeInTheDocument();
    expect(screen.getByTestId("header-workspace")).toBeInTheDocument();

    // Should have proper heading structure
    expect(screen.getByText("support-functions-llm.title")).toBeInTheDocument();
    expect(
      screen.getByText("support-functions-llm.description")
    ).toBeInTheDocument();
  });

  test("should prevent multiple submissions when saving", async () => {
    await renderComponent();

    // Make a change to enable the save button
    const promptUpgradeToggle = screen.getByTestId("toggle-prompt-upgrade");
    fireEvent.click(promptUpgradeToggle);

    await waitFor(() => {
      expect(screen.getByTestId("save-button")).toBeInTheDocument();
    });

    const saveButton = screen.getByTestId("save-button");

    // Click multiple times quickly
    fireEvent.click(saveButton);
    fireEvent.click(saveButton);
    fireEvent.click(saveButton);

    // Should only be called once due to saving state protection
    await waitFor(() => {
      expect(System.setSupportFunctionsLLM).toHaveBeenCalledTimes(1);
    });
  });

  test("should handle settings with model preferences", async () => {
    const mockSettings = {
      LLMProvider_SUPPORT: "anthropic",
      AnthropicModelPref_SUPPORT: "claude-3-haiku",
      supportFunctionPromptUpgrade: false,
      supportFunctionValidation: true,
      supportFunctionManualTime: false,
    };

    await renderComponent(mockSettings);

    // Make a change to enable the save button (just toggle change, no LLM change)
    const toggleElement = screen.getByTestId("toggle-validation");
    fireEvent.click(toggleElement);

    await waitFor(() => {
      expect(screen.getByTestId("save-button")).toBeInTheDocument();
    });

    const saveButton = screen.getByTestId("save-button");
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(System.setSupportFunctionsLLM).toHaveBeenCalledWith({
        // No LLM provider included since selectedLLM === settings.LLMProvider_SUPPORT
        // Model preferences included because selectedLLM is "anthropic" and the pref exists
        AnthropicModelPref_SUPPORT: "claude-3-haiku",
        supportFunctionPromptUpgrade: false,
        supportFunctionValidation: false, // Will be toggled from true to false
        supportFunctionManualTime: false,
      });
    });
  });

  test("should include LLM provider when it changes", async () => {
    const mockSettings = {
      LLMProvider_SUPPORT: "openai",
      supportFunctionPromptUpgrade: false,
      supportFunctionValidation: false,
      supportFunctionManualTime: false,
    };

    await renderComponent(mockSettings);

    // Change the LLM provider
    const providerInput = screen.getByTestId("llm-provider-input");
    fireEvent.change(providerInput, { target: { value: "anthropic" } });

    await waitFor(() => {
      expect(screen.getByTestId("save-button")).toBeInTheDocument();
    });

    const saveButton = screen.getByTestId("save-button");
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(System.setSupportFunctionsLLM).toHaveBeenCalledWith({
        LLMProvider_SUPPORT: "anthropic", // Included because it changed
        supportFunctionPromptUpgrade: false,
        supportFunctionValidation: false,
        supportFunctionManualTime: false,
      });
    });
  });
});
