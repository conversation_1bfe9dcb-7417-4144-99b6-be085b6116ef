// React import not needed for this test
import { render, screen, waitFor, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import SystemReportModal from "../components/Modals/SystemReportModal";

// Mock the translation hook
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock the user hook
jest.mock("../hooks/useUser", () => ({
  __esModule: true,
  default: () => ({
    user: {
      id: "1",
      username: "testuser",
      role: "admin",
    },
  }),
}));

// Mock the Modal component
interface MockModalProps {
  isOpen: boolean;
  children: React.ReactNode;
  title: string;
  onClose: () => void;
}

jest.mock("../components/ui/Modal", () => ({
  __esModule: true,
  default: ({ isOpen, children, title, onClose }: MockModalProps) =>
    isOpen ? (
      <div data-testid="modal" role="dialog">
        <div data-testid="modal-title">{title}</div>
        <button data-testid="modal-close" onClick={onClose}>
          Close
        </button>
        {children}
      </div>
    ) : null,
}));

// Mock Phosphor icons
jest.mock("@phosphor-icons/react", () => ({
  Bug: () => <span data-testid="bug-icon">Bug</span>,
  Lightbulb: () => <span data-testid="lightbulb-icon">Lightbulb</span>,
}));

// Mock sub-components
interface MockReport {
  id: string;
  title: string;
  type: string;
  status: string;
}

interface MockSystemReportListProps {
  reports: MockReport[];
  onSelectReport: (reportId: string) => void;
  onNewReport: () => void;
  reportType: "INCIDENT" | "FEATURE_REQUEST";
}

jest.mock("../components/Modals/SystemReportModal/SystemReportList", () => ({
  __esModule: true,
  default: ({
    reports,
    onSelectReport,
    onNewReport,
    reportType,
  }: MockSystemReportListProps) => (
    <div data-testid="system-report-list">
      <div data-testid="report-type">{reportType}</div>
      <button data-testid="new-report-btn" onClick={onNewReport}>
        New Report
      </button>
      {reports.map((report) => (
        <div
          key={report.id}
          data-testid={`report-${report.id}`}
          onClick={() => onSelectReport(report.id)}
        >
          {report.title}
        </div>
      ))}
    </div>
  ),
}));

interface MockNewReportFormProps {
  onBack: () => void;
  reportType?: "INCIDENT" | "FEATURE_REQUEST";
}

jest.mock("../components/Modals/SystemReportModal/NewReportForm", () => ({
  __esModule: true,
  default: ({ onBack, reportType }: MockNewReportFormProps) => (
    <div data-testid="new-report-form">
      <div data-testid="form-report-type">{reportType}</div>
      <button data-testid="form-back-btn" onClick={onBack}>
        Back
      </button>
    </div>
  ),
}));

interface MockSystemReportDetailProps {
  reportId: string | number;
  onBack: () => void;
}

jest.mock("../components/Modals/SystemReportModal/SystemReportDetail", () => ({
  __esModule: true,
  default: ({ reportId, onBack }: MockSystemReportDetailProps) => (
    <div data-testid="system-report-detail">
      <div data-testid="detail-report-id">{reportId}</div>
      <button data-testid="detail-back-btn" onClick={onBack}>
        Back
      </button>
    </div>
  ),
}));

// Mock store with proper structure matching actual store
interface MockSystemReport {
  id: string;
  title: string;
  type: "INCIDENT" | "FEATURE_REQUEST";
  status: string;
  description?: string;
  priority?: string;
  severity?: string;
  affected_service?: string;
  created_at?: string;
  users?: {
    username: string;
  };
  resolver_user?: {
    username: string;
  };
  _count?: {
    system_report_messages: number;
  };
}

interface MockStore {
  reports: MockSystemReport[];
  loading: boolean;
  selectedReport: MockSystemReport | null;
  error: string | null;
  statistics: unknown | null;
  constants: unknown | null;
  constantsLoading: boolean;
  fetchReports: jest.Mock;
  fetchStatistics: jest.Mock;
  fetchConstants: jest.Mock;
  createReport: jest.Mock;
  fetchReport: jest.Mock;
  updateReportStatus: jest.Mock;
  assignResolver: jest.Mock;
  updateIncidentDetails: jest.Mock;
  addResolutionComment: jest.Mock;
  addMessage: jest.Mock;
  updateReportDetails: jest.Mock;
  resolveReport: jest.Mock;
  deleteReport: jest.Mock;
  confirmResolution: jest.Mock;
  rejectResolution: jest.Mock;
}

const mockStore: MockStore = {
  reports: [],
  loading: false,
  selectedReport: null,
  error: null,
  statistics: null,
  constants: null,
  constantsLoading: false,
  fetchReports: jest.fn(() => Promise.resolve()),
  fetchStatistics: jest.fn(),
  fetchConstants: jest.fn(),
  createReport: jest.fn(),
  fetchReport: jest.fn(),
  updateReportStatus: jest.fn(),
  assignResolver: jest.fn(),
  updateIncidentDetails: jest.fn(),
  addResolutionComment: jest.fn(),
  addMessage: jest.fn(),
  updateReportDetails: jest.fn(),
  resolveReport: jest.fn(),
  deleteReport: jest.fn(),
  confirmResolution: jest.fn(),
  rejectResolution: jest.fn(),
};

jest.mock("../stores/useSystemReportStore", () => ({
  __esModule: true,
  default: () => mockStore,
}));

describe("SystemReportModal", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset mock store reports
    mockStore.reports = [];
    // Ensure fetchReports returns a Promise
    mockStore.fetchReports = jest.fn(() => Promise.resolve());
  });

  it("should not render when isOpen is false", () => {
    render(<SystemReportModal isOpen={false} onClose={() => {}} />);

    expect(screen.queryByTestId("modal")).not.toBeInTheDocument();
  });

  it("should render when isOpen is true", async () => {
    render(<SystemReportModal isOpen={true} onClose={() => {}} />);

    expect(screen.getByTestId("modal")).toBeInTheDocument();
    expect(screen.getByTestId("modal-title")).toHaveTextContent(
      "incident-management.modal-title"
    );

    // Should call fetchReports when opened
    await waitFor(() => {
      expect(mockStore.fetchReports).toHaveBeenCalled();
    });
  });

  it("should display tab navigation in list view", () => {
    render(<SystemReportModal isOpen={true} onClose={() => {}} />);

    // Should show incidents and features tabs
    expect(
      screen.getByText("incident-management.tabs.incidents")
    ).toBeInTheDocument();
    expect(
      screen.getByText("incident-management.tabs.feature-requests")
    ).toBeInTheDocument();
    expect(screen.getByTestId("bug-icon")).toBeInTheDocument();
    expect(screen.getByTestId("lightbulb-icon")).toBeInTheDocument();
  });

  it("should display role-based access message", () => {
    render(<SystemReportModal isOpen={true} onClose={() => {}} />);

    // Should show admin message based on mocked user role
    expect(
      screen.getByText("system-report.admin-all-reports-message")
    ).toBeInTheDocument();
  });

  it("should switch between incident and feature tabs", () => {
    render(<SystemReportModal isOpen={true} onClose={() => {}} />);

    const incidentsTab = screen
      .getByText("incident-management.tabs.incidents")
      .closest("button");
    const featuresTab = screen
      .getByText("incident-management.tabs.feature-requests")
      .closest("button");

    expect(incidentsTab).toHaveClass("border-primary");
    expect(featuresTab).toHaveClass("border-transparent");

    // Click features tab
    fireEvent.click(featuresTab!);

    expect(featuresTab).toHaveClass("border-primary");
    expect(incidentsTab).toHaveClass("border-transparent");
  });

  it("should display system report list by default", () => {
    render(<SystemReportModal isOpen={true} onClose={() => {}} />);

    expect(screen.getByTestId("system-report-list")).toBeInTheDocument();
    expect(screen.getByTestId("report-type")).toHaveTextContent("INCIDENT");
  });

  it("should navigate to new report form", () => {
    render(<SystemReportModal isOpen={true} onClose={() => {}} />);

    const newReportBtn = screen.getByTestId("new-report-btn");
    fireEvent.click(newReportBtn);

    expect(screen.getByTestId("new-report-form")).toBeInTheDocument();
    expect(screen.getByTestId("form-report-type")).toHaveTextContent(
      "INCIDENT"
    );
  });

  it("should navigate back from new report form", () => {
    render(<SystemReportModal isOpen={true} onClose={() => {}} />);

    // Navigate to new report form
    const newReportBtn = screen.getByTestId("new-report-btn");
    fireEvent.click(newReportBtn);

    // Navigate back
    const backBtn = screen.getByTestId("form-back-btn");
    fireEvent.click(backBtn);

    expect(screen.getByTestId("system-report-list")).toBeInTheDocument();
  });

  it("should navigate to report detail", () => {
    const mockReports: MockSystemReport[] = [
      { id: "1", title: "Test Report", type: "INCIDENT", status: "REPORTED" },
    ];
    mockStore.reports = mockReports;

    render(<SystemReportModal isOpen={true} onClose={() => {}} />);

    const reportItem = screen.getByTestId("report-1");
    fireEvent.click(reportItem);

    expect(screen.getByTestId("system-report-detail")).toBeInTheDocument();
    expect(screen.getByTestId("detail-report-id")).toHaveTextContent("1");
  });

  it("should navigate back from report detail", () => {
    const mockReports: MockSystemReport[] = [
      { id: "1", title: "Test Report", type: "INCIDENT", status: "REPORTED" },
    ];
    mockStore.reports = mockReports;

    render(<SystemReportModal isOpen={true} onClose={() => {}} />);

    // Navigate to detail
    const reportItem = screen.getByTestId("report-1");
    fireEvent.click(reportItem);

    // Navigate back
    const backBtn = screen.getByTestId("detail-back-btn");
    fireEvent.click(backBtn);

    expect(screen.getByTestId("system-report-list")).toBeInTheDocument();
  });

  it("should display unresolved count badges", () => {
    const mockReports: MockSystemReport[] = [
      { id: "1", title: "Report 1", type: "INCIDENT", status: "REPORTED" },
      { id: "2", title: "Report 2", type: "INCIDENT", status: "RESOLVED" },
      {
        id: "3",
        title: "Report 3",
        type: "FEATURE_REQUEST",
        status: "IN_PROGRESS",
      },
    ];
    mockStore.reports = mockReports;

    render(<SystemReportModal isOpen={true} onClose={() => {}} />);

    // Should show badge for incidents (1 unresolved)
    const incidentsTab = screen
      .getByText("incident-management.tabs.incidents")
      .closest("button");
    expect(incidentsTab).toBeInTheDocument();

    // Should show badge for features (1 unresolved)
    const featuresTab = screen
      .getByText("incident-management.tabs.feature-requests")
      .closest("button");
    expect(featuresTab).toBeInTheDocument();
  });

  it("should filter reports by type", () => {
    const mockReports: MockSystemReport[] = [
      {
        id: "1",
        title: "Incident Report",
        type: "INCIDENT",
        status: "REPORTED",
      },
      {
        id: "2",
        title: "Feature Request",
        type: "FEATURE_REQUEST",
        status: "IN_PROGRESS",
      },
    ];
    mockStore.reports = mockReports;

    render(<SystemReportModal isOpen={true} onClose={() => {}} />);

    // Default to incidents - should show INCIDENT type
    expect(screen.getByTestId("report-type")).toHaveTextContent("INCIDENT");

    // Switch to features tab
    const featuresTab = screen
      .getByText("incident-management.tabs.feature-requests")
      .closest("button");
    fireEvent.click(featuresTab!);

    expect(screen.getByTestId("report-type")).toHaveTextContent(
      "FEATURE_REQUEST"
    );
  });

  it("should call onClose when modal is closed", () => {
    const mockOnClose = jest.fn();
    render(<SystemReportModal isOpen={true} onClose={mockOnClose} />);

    const closeBtn = screen.getByTestId("modal-close");
    fireEvent.click(closeBtn);

    expect(mockOnClose).toHaveBeenCalled();
  });
});
