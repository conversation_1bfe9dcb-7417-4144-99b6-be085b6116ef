/// <reference types="@testing-library/jest-dom" />
import { render, screen, act } from "@testing-library/react";
import { describe, it, expect, jest, beforeEach } from "@jest/globals";
import { ToastContainer } from "react-toastify";
import showToast from "@/utils/toast";
import "@testing-library/jest-dom";

// Mock react-toastify
jest.mock("react-toastify", () => ({
  ToastContainer: ({
    children,
    className,
    style,
    role,
    ...props
  }: {
    children?: React.ReactNode;
    className?: string;
    style?: React.CSSProperties;
    role?: string;
    "aria-label"?: string;
    [key: string]: unknown;
  }) => (
    <div
      data-testid="toast-container"
      data-toast-props={JSON.stringify(props)}
      className={className}
      style={style}
      {...(typeof role === "string" ? { role } : {})}
      aria-label={props["aria-label"] as string | undefined}
    >
      {children}
    </div>
  ),
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    dismiss: jest.fn(),
  },
}));

// Mock the toast utility
jest.mock("@/utils/toast");

const mockShowToast = showToast as jest.MockedFunction<typeof showToast>;

describe("ToastContainer Integration Tests", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("ToastContainer Rendering", () => {
    it("should render ToastContainer component", () => {
      render(<ToastContainer />);

      const toastContainer = screen.getByTestId("toast-container");
      expect(toastContainer).toBeInTheDocument();
    });

    it("should render ToastContainer with default props", () => {
      render(<ToastContainer />);

      const toastContainer = screen.getByTestId("toast-container");
      expect(toastContainer).toBeInTheDocument();
    });

    it("should render ToastContainer with custom props", () => {
      const customProps = {
        position: "bottom-right" as const,
        autoClose: 3000,
        hideProgressBar: true,
        newestOnTop: false,
        closeOnClick: true,
        rtl: false,
        pauseOnFocusLoss: true,
        draggable: true,
        pauseOnHover: true,
        theme: "dark" as const,
      };

      render(<ToastContainer {...customProps} />);

      const toastContainer = screen.getByTestId("toast-container");
      expect(toastContainer).toBeInTheDocument();

      const props = JSON.parse(
        toastContainer.getAttribute("data-toast-props") || "{}"
      );
      expect(props.position).toBe("bottom-right");
      expect(props.autoClose).toBe(3000);
      expect(props.hideProgressBar).toBe(true);
    });
  });

  describe("Toast Integration Scenarios", () => {
    it("should handle success toast notifications", async () => {
      render(<ToastContainer />);

      await act(async () => {
        mockShowToast("Success message", "success");
      });

      expect(mockShowToast).toHaveBeenCalledWith("Success message", "success");
    });

    it("should handle error toast notifications", async () => {
      render(<ToastContainer />);

      await act(async () => {
        mockShowToast("Error message", "error");
      });

      expect(mockShowToast).toHaveBeenCalledWith("Error message", "error");
    });

    it("should handle info toast notifications", async () => {
      render(<ToastContainer />);

      await act(async () => {
        mockShowToast("Info message", "info");
      });

      expect(mockShowToast).toHaveBeenCalledWith("Info message", "info");
    });

    it("should handle warning toast notifications", async () => {
      render(<ToastContainer />);

      await act(async () => {
        mockShowToast("Warning message", "warning");
      });

      expect(mockShowToast).toHaveBeenCalledWith("Warning message", "warning");
    });

    it("should handle multiple toast notifications", async () => {
      render(<ToastContainer />);

      await act(async () => {
        mockShowToast("First message", "success");
        mockShowToast("Second message", "error");
        mockShowToast("Third message", "info");
      });

      expect(mockShowToast).toHaveBeenCalledTimes(3);
      expect(mockShowToast).toHaveBeenNthCalledWith(
        1,
        "First message",
        "success"
      );
      expect(mockShowToast).toHaveBeenNthCalledWith(
        2,
        "Second message",
        "error"
      );
      expect(mockShowToast).toHaveBeenNthCalledWith(3, "Third message", "info");
    });
  });

  describe("Toast Options Integration", () => {
    it("should handle toast with custom options", async () => {
      render(<ToastContainer />);

      const customOptions = {
        position: "top-center" as const,
        autoClose: 2000,
        hideProgressBar: true,
      };

      await act(async () => {
        mockShowToast("Custom options message", "success", customOptions);
      });

      expect(mockShowToast).toHaveBeenCalledWith(
        "Custom options message",
        "success",
        customOptions
      );
    });

    it("should handle toast with clear option", async () => {
      render(<ToastContainer />);

      const clearOptions = {
        clear: true,
        autoClose: 1000,
      };

      await act(async () => {
        mockShowToast("Clear previous toasts", "error", clearOptions);
      });

      expect(mockShowToast).toHaveBeenCalledWith(
        "Clear previous toasts",
        "error",
        clearOptions
      );
    });
  });

  describe("Toast Accessibility", () => {
    it("should support screen readers", () => {
      render(<ToastContainer role="region" aria-label="Notifications" />);

      const toastContainer = screen.getByTestId("toast-container");
      expect(toastContainer).toHaveAttribute("role", "region");
      expect(toastContainer).toHaveAttribute("aria-label", "Notifications");
    });

    it("should support keyboard navigation", () => {
      render(<ToastContainer />);

      const toastContainer = screen.getByTestId("toast-container");
      expect(toastContainer).toBeInTheDocument();

      // Test that the container can receive focus for keyboard navigation
      expect(toastContainer).toBeInTheDocument();
    });
  });

  describe("Toast Styling and Theming", () => {
    it("should apply custom className", () => {
      const customClassName = "custom-toast-container";
      render(<ToastContainer className={customClassName} />);

      const toastContainer = screen.getByTestId("toast-container");
      expect(toastContainer).toHaveClass(customClassName);
    });

    it("should support different themes", () => {
      const { container: lightContainer } = render(
        <ToastContainer theme="light" />
      );
      const { container: darkContainer } = render(
        <ToastContainer theme="dark" />
      );
      const { container: coloredContainer } = render(
        <ToastContainer theme="colored" />
      );

      const lightProps = JSON.parse(
        lightContainer
          .querySelector("[data-testid='toast-container']")
          ?.getAttribute("data-toast-props") || "{}"
      );
      const darkProps = JSON.parse(
        darkContainer
          .querySelector("[data-testid='toast-container']")
          ?.getAttribute("data-toast-props") || "{}"
      );
      const coloredProps = JSON.parse(
        coloredContainer
          .querySelector("[data-testid='toast-container']")
          ?.getAttribute("data-toast-props") || "{}"
      );

      expect(lightProps.theme).toBe("light");
      expect(darkProps.theme).toBe("dark");
      expect(coloredProps.theme).toBe("colored");
    });

    it("should support custom styles", () => {
      const customStyle = {
        backgroundColor: "red",
        color: "white",
        zIndex: 9999,
      };

      render(<ToastContainer style={customStyle} />);

      const toastContainer = screen.getByTestId("toast-container");
      (expect(toastContainer) as any).toHaveStyle("background-color: red");
      (expect(toastContainer) as any).toHaveStyle("color: white");
      (expect(toastContainer) as any).toHaveStyle("z-index: 9999");
    });
  });

  describe("Toast Positioning", () => {
    it("should support all toast positions", () => {
      const positions = [
        "top-left",
        "top-right",
        "top-center",
        "bottom-left",
        "bottom-right",
        "bottom-center",
      ] as const;

      positions.forEach((position) => {
        const { unmount } = render(<ToastContainer position={position} />);

        const toastContainer = screen.getByTestId("toast-container");
        const props = JSON.parse(
          toastContainer.getAttribute("data-toast-props") || "{}"
        );
        expect(props.position).toBe(position);

        unmount();
      });
    });
  });

  describe("Toast Behavior Configuration", () => {
    it("should configure auto-close behavior", () => {
      render(<ToastContainer autoClose={3000} />);

      const toastContainer = screen.getByTestId("toast-container");
      const props = JSON.parse(
        toastContainer.getAttribute("data-toast-props") || "{}"
      );
      expect(props.autoClose).toBe(3000);
    });

    it("should configure progress bar visibility", () => {
      const { container: withProgressBar } = render(
        <ToastContainer hideProgressBar={false} />
      );
      const { container: withoutProgressBar } = render(
        <ToastContainer hideProgressBar={true} />
      );

      const withProgressBarProps = JSON.parse(
        withProgressBar
          .querySelector("[data-testid='toast-container']")
          ?.getAttribute("data-toast-props") || "{}"
      );
      const withoutProgressBarProps = JSON.parse(
        withoutProgressBar
          .querySelector("[data-testid='toast-container']")
          ?.getAttribute("data-toast-props") || "{}"
      );

      expect(withProgressBarProps.hideProgressBar).toBe(false);
      expect(withoutProgressBarProps.hideProgressBar).toBe(true);
    });

    it("should configure interaction behavior", () => {
      render(
        <ToastContainer
          closeOnClick={true}
          pauseOnHover={true}
          draggable={true}
          pauseOnFocusLoss={true}
        />
      );

      const toastContainer = screen.getByTestId("toast-container");
      const props = JSON.parse(
        toastContainer.getAttribute("data-toast-props") || "{}"
      );
      expect(props.closeOnClick).toBe(true);
      expect(props.pauseOnHover).toBe(true);
      expect(props.draggable).toBe(true);
      expect(props.pauseOnFocusLoss).toBe(true);
    });
  });

  describe("Toast Stacking and Limits", () => {
    it("should configure toast stacking", () => {
      render(<ToastContainer newestOnTop={true} />);

      const toastContainer = screen.getByTestId("toast-container");
      const props = JSON.parse(
        toastContainer.getAttribute("data-toast-props") || "{}"
      );
      expect(props.newestOnTop).toBe(true);
    });

    it("should configure toast limit", () => {
      render(<ToastContainer limit={5} />);

      const toastContainer = screen.getByTestId("toast-container");
      const props = JSON.parse(
        toastContainer.getAttribute("data-toast-props") || "{}"
      );
      expect(props.limit).toBe(5);
    });
  });

  describe("Toast Animations and Transitions", () => {
    it("should support transition configurations", () => {
      render(<ToastContainer transition={undefined} />);

      const toastContainer = screen.getByTestId("toast-container");
      expect(toastContainer).toBeInTheDocument();
    });

    it("should support custom transition timing", () => {
      render(<ToastContainer />);

      const toastContainer = screen.getByTestId("toast-container");
      expect(toastContainer).toBeInTheDocument();
    });
  });

  describe("Error Handling and Edge Cases", () => {
    it("should handle invalid props gracefully", () => {
      expect(() => {
        render(<ToastContainer autoClose={-1} />);
      }).not.toThrow();
    });

    it("should handle undefined props gracefully", () => {
      expect(() => {
        render(<ToastContainer position={undefined} />);
      }).not.toThrow();
    });

    it("should handle null props gracefully", () => {
      expect(() => {
        render(
          <ToastContainer
            theme={null as unknown as "light" | "dark" | "colored"}
          />
        );
      }).not.toThrow();
    });
  });

  describe("Integration with Application State", () => {
    it("should work with multiple ToastContainer instances", () => {
      const { container } = render(
        <>
          <ToastContainer containerId="container1" />
          <ToastContainer containerId="container2" />
        </>
      );

      const containers = container.querySelectorAll(
        "[data-testid='toast-container']"
      );
      expect(containers).toHaveLength(2);

      const props1 = JSON.parse(
        containers[0].getAttribute("data-toast-props") || "{}"
      );
      const props2 = JSON.parse(
        containers[1].getAttribute("data-toast-props") || "{}"
      );

      expect(props1.containerId).toBe("container1");
      expect(props2.containerId).toBe("container2");
    });

    it("should persist across re-renders", () => {
      const { rerender } = render(<ToastContainer />);

      let toastContainer = screen.getByTestId("toast-container");
      expect(toastContainer).toBeInTheDocument();

      rerender(<ToastContainer autoClose={3000} />);

      toastContainer = screen.getByTestId("toast-container");
      expect(toastContainer).toBeInTheDocument();
      const props = JSON.parse(
        toastContainer.getAttribute("data-toast-props") || "{}"
      );
      expect(props.autoClose).toBe(3000);
    });
  });

  describe("Performance Considerations", () => {
    it("should handle rapid toast creation", async () => {
      render(<ToastContainer />);

      await act(async () => {
        for (let i = 0; i < 10; i++) {
          mockShowToast(`Message ${i}`, "info");
        }
      });

      expect(mockShowToast).toHaveBeenCalledTimes(10);
    });

    it("should handle concurrent toast operations", async () => {
      render(<ToastContainer />);

      await act(async () => {
        const promises = [];
        for (let i = 0; i < 5; i++) {
          promises.push(
            Promise.resolve(mockShowToast(`Concurrent message ${i}`, "success"))
          );
        }
        await Promise.all(promises);
      });

      expect(mockShowToast).toHaveBeenCalledTimes(5);
    });
  });
});
