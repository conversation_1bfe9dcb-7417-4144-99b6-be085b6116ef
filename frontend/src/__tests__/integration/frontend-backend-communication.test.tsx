/**
 * Frontend-Backend Communication Integration Tests
 *
 * Tests frontend HTTP requests and their integration with backend APIs
 * - API client functionality
 * - Request/response handling
 * - Error handling on frontend
 * - State management during API calls
 * - Authentication flow integration
 */

import { render, screen, waitFor, act } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import React from "react";

// Mock the request utility
const mockRequest = {
  get: jest.fn(),
  post: jest.fn(),
  delete: jest.fn(),
  put: jest.fn(),
};

jest.mock("../../utils/request", () => mockRequest);

// Mock authentication context
const mockAuth = {
  user: { id: 1, username: "test_user", role: "admin" },
  token: "mock-jwt-token",
  isAuthenticated: true,
  login: jest.fn(),
  logout: jest.fn(),
};

jest.mock("../../AuthProvider", () => {
  return {
    useAuth: () => mockAuth,
    AuthProvider: ({ children }: { children: React.ReactNode }) => (
      <div>{children}</div>
    ),
  };
});

// Mock workspace store
const mockWorkspaceStore = {
  workspaces: [{ id: 1, name: "Test Workspace", slug: "test-workspace" }],
  currentWorkspace: { id: 1, name: "Test Workspace", slug: "test-workspace" },
  loading: false,
  error: null,
  fetchWorkspaces: jest.fn(),
  setCurrentWorkspace: jest.fn(),
  createWorkspace: jest.fn(),
  updateWorkspace: jest.fn(),
  deleteWorkspace: jest.fn(),
};

jest.mock("../../stores/workspaceStore", () => ({
  useWorkspaceStore: () => mockWorkspaceStore,
}));

// Mock toast notifications
const mockShowToast = jest.fn();
jest.mock("../../utils/toast", () => ({
  __esModule: true,
  default: mockShowToast,
}));

// Mock server setup - simplified without MSW for TypeScript compatibility

// Simple test components to verify integration
const TestLoginComponent = () => {
  const [credentials, setCredentials] = React.useState({
    username: "",
    password: "",
  });
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState("");

  const handleLogin = async () => {
    setLoading(true);
    setError("");

    try {
      const response = await mockRequest.post(
        "/api/request-token",
        credentials
      );
      if (response.token) {
        mockAuth.login(response.user, response.token);
      }
      setLoading(false);
    } catch (error) {
      setError("Login failed");
      setLoading(false);
    }
  };

  return (
    <div>
      <input
        data-testid="username-input"
        value={credentials.username}
        onChange={(e) =>
          setCredentials((prev) => ({ ...prev, username: e.target.value }))
        }
        placeholder="Username"
      />
      <input
        data-testid="password-input"
        type="password"
        value={credentials.password}
        onChange={(e) =>
          setCredentials((prev) => ({ ...prev, password: e.target.value }))
        }
        placeholder="Password"
      />
      <button
        data-testid="login-button"
        onClick={handleLogin}
        disabled={loading}
      >
        {loading ? "Logging in..." : "Login"}
      </button>
      {error && <div data-testid="login-error">{error}</div>}
    </div>
  );
};

const TestWorkspaceList = () => {
  const [workspaces, setWorkspaces] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(false);

  const fetchWorkspaces = async () => {
    setLoading(true);
    try {
      const response = await mockRequest.get("/api/workspaces");
      setWorkspaces(response.workspaces || []);
    } catch (error) {
      console.error("Failed to fetch workspaces:", error);
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    fetchWorkspaces();
  }, []);

  return (
    <div>
      <h2>Workspaces</h2>
      {loading && <div data-testid="workspaces-loading">Loading...</div>}
      <ul data-testid="workspaces-list">
        {workspaces.map((workspace) => (
          <li key={workspace.id} data-testid={`workspace-${workspace.id}`}>
            {workspace.name}
          </li>
        ))}
      </ul>
      <button data-testid="refresh-workspaces" onClick={fetchWorkspaces}>
        Refresh
      </button>
    </div>
  );
};

const TestChatComponent = () => {
  const [message, setMessage] = React.useState("");
  const [response, setResponse] = React.useState("");
  const [loading, setLoading] = React.useState(false);

  const sendMessage = async () => {
    setLoading(true);
    try {
      const result = await mockRequest.post(
        "/api/workspace/test-workspace/stream-chat/standard",
        { message, mode: "chat" }
      );
      setResponse(result);
      setLoading(false);
    } catch (error) {
      console.error("Chat failed:", error);
      setResponse("Error occurred");
      setLoading(false);
    }
  };

  return (
    <div>
      <input
        data-testid="chat-input"
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        placeholder="Enter message"
      />
      <button
        data-testid="send-message"
        onClick={sendMessage}
        disabled={loading}
      >
        {loading ? "Sending..." : "Send"}
      </button>
      {response && <div data-testid="chat-response">{response}</div>}
    </div>
  );
};

describe("Frontend-Backend Communication Integration Tests", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset mock implementations
    mockRequest.get.mockResolvedValue({ workspaces: [] });
    mockRequest.post.mockResolvedValue({ success: true });
  });

  describe("Authentication Integration", () => {
    test("should handle login request and response", async () => {
      const user = userEvent.setup();

      mockRequest.post.mockResolvedValueOnce({
        token: "mock-jwt-token",
        user: { id: 1, username: "test_user", role: "admin" },
      });

      render(<TestLoginComponent />);

      // Fill in credentials
      await user.type(screen.getByTestId("username-input"), "test_user");
      await user.type(screen.getByTestId("password-input"), "password123");

      // Submit login
      await user.click(screen.getByTestId("login-button"));

      // Verify API call was made
      await waitFor(() => {
        expect(mockRequest.post).toHaveBeenCalledWith("/api/request-token", {
          username: "test_user",
          password: "password123",
        });
      });
    });

    test("should handle authentication errors", async () => {
      const user = userEvent.setup();

      mockRequest.post.mockRejectedValueOnce(
        new Error("Authentication failed")
      );

      render(<TestLoginComponent />);

      await user.type(screen.getByTestId("username-input"), "wrong_user");
      await user.type(screen.getByTestId("password-input"), "wrong_password");

      await act(async () => {
        await user.click(screen.getByTestId("login-button"));
      });

      await waitFor(
        () => {
          expect(screen.getByTestId("login-error")).toHaveTextContent(
            "Login failed"
          );
        },
        { timeout: 5000 }
      );
    });
  });

  describe("Workspace API Integration", () => {
    test("should fetch and display workspaces", async () => {
      mockRequest.get.mockResolvedValueOnce({
        workspaces: [
          { id: 1, name: "Test Workspace", slug: "test-workspace" },
          { id: 2, name: "Another Workspace", slug: "another-workspace" },
        ],
      });

      await act(async () => {
        render(<TestWorkspaceList />);
      });

      // Wait for workspaces to load (skip loading state check as it may be too fast)
      await waitFor(
        () => {
          expect(screen.getByTestId("workspace-1")).toHaveTextContent(
            "Test Workspace"
          );
          expect(screen.getByTestId("workspace-2")).toHaveTextContent(
            "Another Workspace"
          );
        },
        { timeout: 10000 }
      );

      // Verify API call was made
      expect(mockRequest.get).toHaveBeenCalledWith("/api/workspaces");
    }, 15000);

    test("should handle workspace refresh", async () => {
      const user = userEvent.setup();

      mockRequest.get
        .mockResolvedValueOnce({
          workspaces: [{ id: 1, name: "Initial Workspace", slug: "initial" }],
        })
        .mockResolvedValueOnce({
          workspaces: [
            { id: 2, name: "Refreshed Workspace", slug: "refreshed" },
          ],
        });

      await act(async () => {
        render(<TestWorkspaceList />);
      });

      // Wait for initial load
      await waitFor(
        () => {
          expect(screen.getByText("Initial Workspace")).toBeInTheDocument();
        },
        { timeout: 10000 }
      );

      // Click refresh
      await act(async () => {
        await user.click(screen.getByTestId("refresh-workspaces"));
      });

      // Wait for refresh
      await waitFor(
        () => {
          expect(screen.getByText("Refreshed Workspace")).toBeInTheDocument();
        },
        { timeout: 10000 }
      );

      // Verify API was called twice
      expect(mockRequest.get).toHaveBeenCalledTimes(2);
    }, 20000);
  });

  describe("Chat Integration", () => {
    test("should send chat message and handle response", async () => {
      const user = userEvent.setup();

      mockRequest.post.mockResolvedValueOnce("Test chat response");

      await act(async () => {
        render(<TestChatComponent />);
      });

      // Type message
      await user.type(screen.getByTestId("chat-input"), "Hello, test message");

      // Send message
      await act(async () => {
        await user.click(screen.getByTestId("send-message"));
      });

      // Wait for response
      await waitFor(
        () => {
          expect(screen.getByTestId("chat-response")).toHaveTextContent(
            "Test chat response"
          );
        },
        { timeout: 10000 }
      );

      // Verify API call
      expect(mockRequest.post).toHaveBeenCalledWith(
        "/api/workspace/test-workspace/stream-chat/standard",
        { message: "Hello, test message", mode: "chat" }
      );
    }, 15000);

    test("should show loading state during chat request", async () => {
      const user = userEvent.setup();

      // Mock a delayed response
      let resolvePromise: (value: any) => void;
      const delayedPromise = new Promise((resolve) => {
        resolvePromise = resolve;
      });
      mockRequest.post.mockReturnValueOnce(delayedPromise);

      await act(async () => {
        render(<TestChatComponent />);
      });

      await user.type(screen.getByTestId("chat-input"), "Test message");

      await act(async () => {
        await user.click(screen.getByTestId("send-message"));
      });

      // Should show loading state
      expect(screen.getByTestId("send-message")).toHaveTextContent(
        "Sending..."
      );
      expect(screen.getByTestId("send-message")).toBeDisabled();

      // Resolve the promise
      await act(async () => {
        resolvePromise!("Response received");
      });

      // Wait for completion
      await waitFor(
        () => {
          expect(screen.getByTestId("send-message")).toHaveTextContent("Send");
          expect(screen.getByTestId("send-message")).not.toBeDisabled();
        },
        { timeout: 10000 }
      );
    }, 15000);
  });

  describe("Error Handling Integration", () => {
    test("should handle API errors gracefully", async () => {
      mockRequest.get.mockRejectedValueOnce(new Error("Network error"));

      render(<TestWorkspaceList />);

      // Should handle error without crashing
      await waitFor(() => {
        expect(screen.getByTestId("workspaces-list")).toBeInTheDocument();
      });

      // Error should be logged (not shown to user in this simple component)
      expect(mockRequest.get).toHaveBeenCalled();
    });

    test("should handle timeout scenarios", async () => {
      const user = userEvent.setup();

      // Mock a request that never resolves (simulating timeout)
      const neverResolve = new Promise(() => {});
      mockRequest.post.mockReturnValueOnce(neverResolve);

      render(<TestChatComponent />);

      await user.type(screen.getByTestId("chat-input"), "Test timeout");
      await user.click(screen.getByTestId("send-message"));

      // Should show loading state
      expect(screen.getByTestId("send-message")).toHaveTextContent(
        "Sending..."
      );

      // In a real implementation, there would be timeout handling
      // For this test, we just verify the loading state is shown
    });
  });

  describe("Request/Response Data Flow", () => {
    test("should properly serialize request data", async () => {
      const complexData = {
        message: "Test message",
        metadata: {
          timestamp: Date.now(),
          userAgent: "test-agent",
        },
        attachments: [],
      };

      mockRequest.post.mockResolvedValueOnce({ success: true });

      // Simulate a complex API request
      await act(async () => {
        await mockRequest.post("/api/complex-endpoint", complexData);
      });

      expect(mockRequest.post).toHaveBeenCalledWith(
        "/api/complex-endpoint",
        complexData
      );
    });

    test("should handle different response formats", async () => {
      const testCases = [
        { response: { data: "string response" }, expected: "string response" },
        { response: { items: [1, 2, 3] }, expected: [1, 2, 3] },
        {
          response: { success: true, id: 123 },
          expected: { success: true, id: 123 },
        },
      ];

      for (const testCase of testCases) {
        mockRequest.get.mockResolvedValueOnce(testCase.response);

        const result = await mockRequest.get("/api/test-endpoint");
        expect(result).toEqual(testCase.response);
      }
    });
  });

  describe("State Management During API Calls", () => {
    test("should maintain component state during async operations", async () => {
      const user = userEvent.setup();

      // Mock sequential API calls
      mockRequest.get
        .mockResolvedValueOnce({
          workspaces: [{ id: 1, name: "First", slug: "first" }],
        })
        .mockResolvedValueOnce({
          workspaces: [{ id: 2, name: "Second", slug: "second" }],
        });

      await act(async () => {
        render(<TestWorkspaceList />);
      });

      // Wait for first load
      await waitFor(
        () => {
          expect(screen.getByText("First")).toBeInTheDocument();
        },
        { timeout: 10000 }
      );

      // Trigger second load
      await act(async () => {
        await user.click(screen.getByTestId("refresh-workspaces"));
      });

      // Verify state transition
      await waitFor(
        () => {
          expect(screen.getByText("Second")).toBeInTheDocument();
        },
        { timeout: 10000 }
      );
    }, 25000);

    test("should handle rapid successive API calls", async () => {
      const user = userEvent.setup();

      mockRequest.get.mockImplementation(
        () =>
          new Promise((resolve) =>
            setTimeout(() => resolve({ workspaces: [] }), 100)
          )
      );

      render(<TestWorkspaceList />);

      // Click refresh multiple times rapidly
      await user.click(screen.getByTestId("refresh-workspaces"));
      await user.click(screen.getByTestId("refresh-workspaces"));
      await user.click(screen.getByTestId("refresh-workspaces"));

      // Should not crash and should handle all calls
      await waitFor(
        () => {
          expect(mockRequest.get).toHaveBeenCalled();
        },
        { timeout: 1000 }
      );
    });
  });

  describe("Performance and Optimization", () => {
    test("should handle large response data efficiently", async () => {
      // Mock a large dataset
      const largeWorkspaceList = Array.from({ length: 1000 }, (_, i) => ({
        id: i,
        name: `Workspace ${i}`,
        slug: `workspace-${i}`,
      }));

      mockRequest.get.mockResolvedValueOnce({ workspaces: largeWorkspaceList });

      const startTime = Date.now();
      render(<TestWorkspaceList />);

      await waitFor(() => {
        expect(screen.getByTestId("workspaces-list")).toBeInTheDocument();
      });

      const renderTime = Date.now() - startTime;

      // Should render within reasonable time (this is a basic check)
      expect(renderTime).toBeLessThan(5000); // 5 seconds max
    });

    test("should handle concurrent API requests", async () => {
      // Mock multiple concurrent requests
      const promises = [
        mockRequest.get("/api/workspaces"),
        mockRequest.get("/api/documents"),
        mockRequest.get("/api/users"),
      ];

      mockRequest.get.mockImplementation((url) => {
        if (url.includes("workspaces"))
          return Promise.resolve({ workspaces: [] });
        if (url.includes("documents"))
          return Promise.resolve({ documents: [] });
        if (url.includes("users")) return Promise.resolve({ users: [] });
        return Promise.resolve({});
      });

      const results = await Promise.all(promises);

      // All requests should complete successfully
      expect(results).toHaveLength(3);
      expect(mockRequest.get).toHaveBeenCalledTimes(3);
    });
  });
});
