import { renderHook } from "@testing-library/react";
import { useNews } from "@/hooks/useNews";
import {
  systemNewsItems,
  systemNewsInitialized,
  getActiveSystemNews,
  getSystemNewsForRoles,
  resolveSystemNewsTranslations,
  clearSystemNewsCache,
  reinitializeSystemNews,
} from "@/data/news";
import { SystemNewsItem } from "@/data/news/types";

// Mock dependencies
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: jest.fn((key, fallback) => fallback || key),
  }),
}));

jest.mock("@/hooks/useUser", () => ({
  __esModule: true,
  default: () => ({
    user: { id: "1", role: "default" },
    authToken: "mock-token",
    setUser: jest.fn(),
  }),
}));

jest.mock("@/utils/constants", () => ({
  API_BASE: "http://localhost:3001/api",
}));

jest.mock("@/utils/request", () => ({
  baseHeaders: () => ({ "Content-Type": "application/json" }),
}));

// Save original fetch before mocking it
const originalFetch = global.fetch;

// Mock fetch with proper typing
const mockFetch = jest.fn() as jest.MockedFunction<typeof fetch>;
global.fetch = mockFetch;

describe("News System Frontend Tests", () => {
  beforeEach(() => {
    mockFetch.mockClear();
    jest.clearAllMocks();
    clearSystemNewsCache(); // Clear cache before each test
  });

  afterEach(() => {
    jest.clearAllMocks();
    mockFetch.mockClear();
    clearSystemNewsCache(); // Ensure cache is cleared after each test
  });

  afterAll(() => {
    // Restore original fetch to prevent cross-test pollution
    global.fetch = originalFetch;
    // Reset any global state
    clearSystemNewsCache();
  });

  describe("System News Data Structure", () => {
    beforeEach(() => {
      // Set up default news data for system news tests
      mockFetch.mockImplementation((url: string | URL | Request) => {
        const urlString = url.toString();
        if (urlString.includes("/api/news/system")) {
          return Promise.resolve({
            ok: true,
            json: () =>
              Promise.resolve({
                success: true,
                systemNews: [
                  {
                    id: "system-welcome-2024",
                    title: "Welcome to IST Legal Platform",
                    content: "Welcome to our comprehensive legal platform...",
                    titleKey: "news-system-items.system-welcome-2024.title",
                    contentKey: "news-system-items.system-welcome-2024.content",
                    priority: "high",
                    isActive: true,
                    isSystemNews: true,
                    targetRoles: null,
                    createdAt: "2024-01-01T00:00:00Z",
                    expiresAt: null,
                    version: "1.0.0",
                    metadata: {
                      author: "system",
                      category: "general",
                      tags: [],
                    },
                  },
                  {
                    id: "system-new-features-2025",
                    title: "New System Functions Available",
                    content: "We've added exciting new features...",
                    titleKey:
                      "news-system-items.system-new-features-2025.title",
                    contentKey:
                      "news-system-items.system-new-features-2025.content",
                    priority: "medium",
                    isActive: true,
                    isSystemNews: true,
                    targetRoles: null,
                    createdAt: "2025-01-01T00:00:00Z",
                    expiresAt: null,
                    version: "1.0.0",
                    metadata: {
                      author: "system",
                      category: "features",
                      tags: [],
                    },
                  },
                ],
              }),
          } as Response);
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ success: true }),
        } as Response);
      });
    });

    test("should have valid system news items", async () => {
      // Get fresh data from API since systemNewsItems might be empty on first load
      const newsItems = await getActiveSystemNews();
      expect(newsItems).toBeDefined();
      expect(Array.isArray(newsItems)).toBe(true);
      expect(newsItems.length).toBeGreaterThan(0);

      // Test each news item has required properties
      newsItems.forEach((item) => {
        expect(item).toHaveProperty("id");
        expect(item).toHaveProperty("isActive");
        expect(item).toHaveProperty("isSystemNews", true);
        expect(item).toHaveProperty("priority");
        expect(["low", "medium", "high", "urgent"]).toContain(item.priority);

        // Should have titleKey/contentKey
        const hasTranslationKeys =
          item.titleKey &&
          item.contentKey &&
          typeof item.titleKey === "string" &&
          typeof item.contentKey === "string";

        expect(hasTranslationKeys).toBe(true);

        // If using translation keys, they should be strings
        if (item.titleKey) {
          expect(typeof item.titleKey).toBe("string");
        }
        if (item.contentKey) {
          expect(typeof item.contentKey).toBe("string");
        }
      });
    });

    test("should filter active system news correctly", async () => {
      const activeNews = await getActiveSystemNews();
      expect(Array.isArray(activeNews)).toBe(true);

      activeNews.forEach((item) => {
        expect(item.isActive).toBe(true);

        // Check expiration
        if (item.expiresAt) {
          const expirationDate = new Date(item.expiresAt);
          const now = new Date();
          expect(expirationDate.getTime()).toBeGreaterThan(now.getTime());
        }
      });
    });

    test("should filter system news by user roles correctly", async () => {
      const adminNews = await getSystemNewsForRoles(["admin"]);
      const defaultNews = await getSystemNewsForRoles(["default"]);
      const multiRoleNews = await getSystemNewsForRoles(["admin", "manager"]);

      expect(Array.isArray(adminNews)).toBe(true);
      expect(Array.isArray(defaultNews)).toBe(true);
      expect(Array.isArray(multiRoleNews)).toBe(true);

      // News with no target roles should appear for all users
      const allNews = await getActiveSystemNews();
      const noTargetRoleNews = allNews.filter(
        (item) =>
          item.isActive && (!item.targetRoles || item.targetRoles.length === 0)
      );

      expect(adminNews.length).toBeGreaterThanOrEqual(noTargetRoleNews.length);
      expect(defaultNews.length).toBeGreaterThanOrEqual(
        noTargetRoleNews.length
      );
    });
  });

  describe("System News Translation Resolution", () => {
    beforeEach(() => {
      // Set up mock for system news fetch
      mockFetch.mockImplementation((url: string | URL | Request) => {
        const urlString = url.toString();
        if (urlString.includes("/api/news/system")) {
          return Promise.resolve({
            ok: true,
            json: () =>
              Promise.resolve({
                success: true,
                systemNews: [
                  {
                    id: "system-welcome-2024",
                    title: "Welcome to IST Legal Platform",
                    content: "Welcome to our comprehensive legal platform...",
                    titleKey: "news-system-items.system-welcome-2024.title",
                    contentKey: "news-system-items.system-welcome-2024.content",
                    priority: "high",
                    isActive: true,
                    isSystemNews: true,
                    targetRoles: null,
                    createdAt: "2024-01-01T00:00:00Z",
                    expiresAt: null,
                    version: "1.0.0",
                    metadata: {
                      author: "system",
                      category: "general",
                      tags: [],
                    },
                  },
                ],
              }),
          } as Response);
        }
        return Promise.resolve({
          ok: false,
          json: () => Promise.resolve({ success: false }),
        } as Response);
      });
    });

    test("should resolve translations correctly for all supported languages", async () => {
      const mockTranslationFunction: jest.MockedFunction<
        (key: string, fallback?: string) => string
      > = jest.fn((key: string, fallback?: string): string => {
        // Mock different language responses
        const translations: Record<string, Record<string, string>> = {
          "news-system-items.system-welcome-2024.title": {
            en: "Welcome to IST Legal Platform",
            sv: "Välkommen till IST Legal Platform",
            fr: "Bienvenue sur la Plateforme IST Legal",
            de: "Willkommen bei der IST Legal Platform",
            no: "Velkommen til IST Legal Platform",
            pl: "Witamy na Platformie IST Legal",
            rw: "Murakaza neza kuri IST Legal Platform",
          },
          "news-system-items.system-welcome-2024.content": {
            en: "Welcome content in English",
            sv: "Välkomstinnehåll på svenska",
            fr: "Contenu de bienvenue en français",
            de: "Willkommensinhalt auf Deutsch",
            no: "Velkomstinnhold på norsk",
            pl: "Treść powitalna w języku polskim",
            rw: "Ibirimo byo kwakira mu Kinyarwanda",
          },
        };

        const currentLang: string =
          (mockTranslationFunction as any).currentLang || "en";
        return translations[key]?.[currentLang] || fallback || key;
      });

      const supportedLanguages = ["en", "sv", "fr", "de", "no", "pl", "rw"];

      for (const lang of supportedLanguages) {
        (mockTranslationFunction as any).currentLang = lang;

        // Ensure system news is initialized
        await reinitializeSystemNews();
        const allNews = await getActiveSystemNews();
        const newsWithTranslationKeys = allNews.find(
          (item) => item.titleKey && item.contentKey
        );

        // Ensure the suite actually validates something
        expect(newsWithTranslationKeys).toBeDefined();

        if (newsWithTranslationKeys) {
          const resolved = await resolveSystemNewsTranslations(
            newsWithTranslationKeys,
            mockTranslationFunction
          );

          expect(resolved.titleKey).toBeDefined();
          expect(resolved.contentKey).toBeDefined();
          expect(resolved.titleKey).not.toBe(newsWithTranslationKeys.titleKey);
          expect(resolved.contentKey).not.toBe(
            newsWithTranslationKeys.contentKey
          );
        }
      }
    });

    test("should handle missing translations gracefully", async () => {
      const mockTranslationFunction = jest.fn(
        (key: string, fallback?: string) => fallback || key
      );

      const newsItem: SystemNewsItem = {
        id: "test-news",
        titleKey: "missing.translation.key",
        contentKey: "missing.content.key",
        title: "Test title",
        content: "Test content",
        priority: "medium",
        targetRoles: null,
        expiresAt: null,
        isActive: true,
        isSystemNews: true,
        createdAt: "2024-01-01T00:00:00Z",
        version: "1.0.0",
        metadata: {
          author: "test",
          category: "test",
          tags: [],
        },
      };

      const resolved = await resolveSystemNewsTranslations(
        newsItem,
        mockTranslationFunction
      );

      expect(resolved.titleKey).toBe("missing.translation.key");
      expect(resolved.contentKey).toBe("missing.content.key");
    });
  });

  describe("useNews Hook", () => {
    beforeEach(() => {
      // Set up minimal successful responses for useNews hook tests
      mockFetch.mockImplementation((url: string | URL | Request) => {
        const urlString = url.toString();
        if (urlString.includes("/api/news/system")) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ success: true, systemNews: [] }),
          } as Response);
        }
        if (urlString.includes("/news/dismissed-system")) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ dismissedIds: [] }),
          } as Response);
        }
        if (urlString.includes("/news/unread")) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ success: true, news: [] }),
          } as Response);
        }
        if (urlString.includes("/news/all-active")) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ success: true, news: [] }),
          } as Response);
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ success: true }),
        } as Response);
      });
    });

    test("should initialize with correct default state", () => {
      const { result, unmount } = renderHook(() => useNews());

      try {
        // Initially, the hook should have empty state
        expect(result.current.unreadNews).toEqual([]);
        expect(result.current.allActiveNews).toEqual([]);
        expect(result.current.error).toBe(null);
        expect(result.current.systemNewsCount).toBe(0);
        expect(result.current.databaseNewsCount).toBe(0);
        expect(typeof result.current.isLoading).toBe("boolean");
      } finally {
        unmount();
      }
    });

    test("should load system news correctly", () => {
      const { result, unmount } = renderHook(() => useNews());

      try {
        expect(result.current.systemNewsCount).toBeGreaterThanOrEqual(0);
        expect(Array.isArray(result.current.systemNews)).toBe(true);
      } finally {
        unmount();
      }
    });

    test("should handle dismissed system news correctly", () => {
      const { result, unmount } = renderHook(() => useNews());

      try {
        // Verify that the hook is functioning properly
        expect(result.current.unreadNews).toBeDefined();
        expect(Array.isArray(result.current.unreadNews)).toBe(true);
        expect(result.current.systemNewsCount).toBeGreaterThanOrEqual(0);
        expect(typeof result.current.dismissSystemNews).toBe("function");
      } finally {
        unmount();
      }
    });

    test("should have access to fetchUnreadNews function", () => {
      const { result, unmount } = renderHook(() => useNews());

      try {
        // Check that the hook returns the expected functions
        expect(typeof result.current.fetchUnreadNews).toBe("function");
        expect(typeof result.current.dismissNews).toBe("function");
        expect(typeof result.current.markAsRead).toBe("function");
        expect(typeof result.current.refetch).toBe("function");
      } finally {
        unmount();
      }
    });

    test("should handle API errors gracefully", () => {
      const { result, unmount } = renderHook(() => useNews());

      try {
        // Test that error state is initially null
        expect(result.current.error).toBe(null);
        expect(typeof result.current.isLoading).toBe("boolean");
      } finally {
        unmount();
      }
    });

    test("should dismiss system news correctly", () => {
      const { result, unmount } = renderHook(() => useNews());

      try {
        const initialSystemNewsCount = result.current.systemNewsCount;

        // Test that the dismiss function exists and initial state is reasonable
        expect(typeof result.current.dismissNews).toBe("function");
        expect(typeof result.current.dismissSystemNews).toBe("function");
        expect(initialSystemNewsCount).toBeGreaterThanOrEqual(0);
      } finally {
        unmount();
      }
    });

    test("should dismiss local news correctly", () => {
      const { result, unmount } = renderHook(() => useNews());

      try {
        // Test that dismiss functions are available
        expect(typeof result.current.dismissNews).toBe("function");
        expect(typeof result.current.markAsRead).toBe("function");
        expect(result.current.databaseNewsCount).toBeGreaterThanOrEqual(0);
      } finally {
        unmount();
      }
    });

    test("should combine and sort news correctly", () => {
      const { result, unmount } = renderHook(() => useNews());

      try {
        const allNews = result.current.unreadNews;
        expect(Array.isArray(allNews)).toBe(true);
        expect(allNews.length).toBeGreaterThanOrEqual(0);

        // Test that the hook provides the expected priority ordering logic
        const priorityOrder: Record<string, number> = {
          urgent: 4,
          high: 3,
          medium: 2,
          low: 1,
        };

        // Verify that priority order mapping works
        expect(priorityOrder.urgent).toBe(4);
        expect(priorityOrder.high).toBe(3);
        expect(priorityOrder.medium).toBe(2);
        expect(priorityOrder.low).toBe(1);
      } finally {
        unmount();
      }
    });
  });

  describe("News System Integration", () => {
    test("should handle mixed system and local news correctly", () => {
      const { result, unmount } = renderHook(() => useNews());

      try {
        const allNews = result.current.unreadNews;
        expect(Array.isArray(allNews)).toBe(true);

        // Test that the news counts are consistent
        const systemNewsCount = result.current.systemNewsCount;
        const localNewsCount = result.current.databaseNewsCount;
        expect(systemNewsCount).toBeGreaterThanOrEqual(0);
        expect(localNewsCount).toBeGreaterThanOrEqual(0);
      } finally {
        unmount();
      }
    });

    test("should maintain news state consistency during operations", () => {
      const { result, unmount } = renderHook(() => useNews());

      try {
        const initialAllNewsCount = result.current.allUnreadNews.length;
        const initialSystemNewsCount = result.current.systemNewsCount;
        const initialLocalNewsCount = result.current.databaseNewsCount;

        // Test that the state is consistent
        expect(initialAllNewsCount).toBe(
          initialSystemNewsCount + initialLocalNewsCount
        );
        expect(initialSystemNewsCount).toBeGreaterThanOrEqual(0);
        expect(initialLocalNewsCount).toBeGreaterThanOrEqual(0);
      } finally {
        unmount();
      }
    });
  });

  describe("Async Initialization Pattern", () => {
    test("systemNewsInitialized promise should resolve when systemNewsItems is loaded", async () => {
      // Reinitialize to ensure fresh data
      await reinitializeSystemNews();

      // Wait for initialization to complete
      await systemNewsInitialized();

      // Now systemNewsItems should be populated (or empty array if no news)
      expect(Array.isArray(systemNewsItems)).toBe(true);

      // The array should be the same as what we get from the async function
      const asyncNews = await getActiveSystemNews();
      expect(systemNewsItems.length).toBe(asyncNews.length);
    });

    test("should demonstrate proper usage patterns", async () => {
      // Pattern 1: PREFERRED - Use async functions (no race conditions)
      const activeNews = await getActiveSystemNews();
      const roleNews = await getSystemNewsForRoles(["admin"]);

      expect(Array.isArray(activeNews)).toBe(true);
      expect(Array.isArray(roleNews)).toBe(true);

      // Pattern 2: If you need the synchronous systemNewsItems array
      await systemNewsInitialized(); // Wait for initialization
      expect(Array.isArray(systemNewsItems)).toBe(true); // Now safe to use

      // Pattern 3: AVOID - Direct synchronous access without waiting
      // This is what we're fixing - don't do this:
      // console.log(systemNewsItems); // May be [] if accessed too early
    });
  });
});
