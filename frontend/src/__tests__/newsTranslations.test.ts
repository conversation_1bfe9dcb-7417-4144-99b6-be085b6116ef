import { describe, test, expect } from "@jest/globals";

// Import all translation files
import enNewsSystemItems from "@/locales/en/newsSystemItems";
import svNewsSystemItems from "@/locales/sv/newsSystemItems";
import frNewsSystemItems from "@/locales/fr/newsSystemItems";
import deNewsSystemItems from "@/locales/de/newsSystemItems";
import noNewsSystemItems from "@/locales/no/newsSystemItems";
import plNewsSystemItems from "@/locales/pl/newsSystemItems";
import rwNewsSystemItems from "@/locales/rw/newsSystemItems";

// Import system news items
import { clearSystemNewsCache } from "@/data/news";

// Mock system news items for testing (matching server structure)
const mockSystemNewsItems = [
  {
    id: "system-welcome-2024",
    title: "Welcome to IST Legal Platform",
    content: "Welcome to our comprehensive legal platform...",
    titleKey: "news-system-items.system-welcome-2024.title",
    contentKey: "news-system-items.system-welcome-2024.content",
    priority: "high",
    isActive: true,
    isSystemNews: true,
    targetRoles: null,
    createdAt: "2024-06-30T00:00:00Z",
    expiresAt: null,
  },
  {
    id: "system-new-features-2025",
    title: "New System Functions Available",
    content: "We've added exciting new features...",
    titleKey: "news-system-items.system-new-features-2025.title",
    contentKey: "news-system-items.system-new-features-2025.content",
    priority: "medium",
    isActive: true,
    isSystemNews: true,
    targetRoles: null,
    createdAt: "2025-01-01T00:00:00Z",
    expiresAt: null,
  },
  {
    id: "system-version-111-2025",
    title: "Platform Update 1.1.1 Available",
    content: "We've released version 1.1.1...",
    titleKey: "news-system-items.system-version-111-2025.title",
    contentKey: "news-system-items.system-version-111-2025.content",
    priority: "medium",
    isActive: true,
    isSystemNews: true,
    targetRoles: null,
    createdAt: "2025-06-08T12:00:00Z",
    expiresAt: null,
  },
  {
    id: "system-version-112-2025",
    title: "Platform Update 1.1.2 Available",
    content: "We've released version 1.1.2...",
    titleKey: "news-system-items.system-version-112-2025.title",
    contentKey: "news-system-items.system-version-112-2025.content",
    priority: "high",
    isActive: true,
    isSystemNews: true,
    targetRoles: null,
    createdAt: "2025-06-24T14:00:00Z",
    expiresAt: null,
  },
  {
    id: "system-version-113-2025",
    title: "Platform Update 1.1.3 Available",
    content: "We've released version 1.1.3...",
    titleKey: "news-system-items.system-version-113-2025.title",
    contentKey: "news-system-items.system-version-113-2025.content",
    priority: "high",
    isActive: true,
    isSystemNews: true,
    targetRoles: null,
    createdAt: "2025-06-28T10:00:00Z",
    expiresAt: null,
  },
];

// Type definitions for news translations
interface NewsTranslationItem {
  title: string;
  content: string;
}

interface NewsMetadata {
  author: string;
  version: string;
  category: string;
}

interface NewsAuthors {
  [key: string]: string;
}

interface NewsCategories {
  [key: string]: string;
}

interface NewsTranslations {
  "news-system-items": {
    metadata: NewsMetadata;
    authors: NewsAuthors;
    categories: NewsCategories;
    [key: string]:
      | NewsTranslationItem
      | NewsMetadata
      | NewsAuthors
      | NewsCategories;
  };
}

// Type guard functions
function isNewsTranslationItem(item: unknown): item is NewsTranslationItem {
  return (
    typeof item === "object" &&
    item !== null &&
    "title" in item &&
    "content" in item &&
    typeof (item as NewsTranslationItem).title === "string" &&
    typeof (item as NewsTranslationItem).content === "string"
  );
}

// Helper function to safely access translation items
function getTranslationItem(
  translations: NewsTranslations,
  itemId: string
): NewsTranslationItem | null {
  const item = translations["news-system-items"][itemId];
  if (isNewsTranslationItem(item)) {
    return item;
  }
  return null;
}

describe("News System Translation Tests", () => {
  // Mock fetch for the server-based news system
  // This test dynamically imports actual system news items from the server
  // so it doesn't need manual updates when new news items are added
  beforeEach(() => {
    clearSystemNewsCache(); // Clear cache before each test
    const mockFetch = jest.fn() as jest.MockedFunction<typeof fetch>;
    global.fetch = mockFetch;

    mockFetch.mockImplementation((url: string | URL | Request) => {
      const urlString = url.toString();
      if (urlString.includes("/api/news/system")) {
        // Use mock system news items for testing
        return Promise.resolve({
          ok: true,
          status: 200,
          statusText: "OK",
          headers: new Headers({ "Content-Type": "application/json" }),
          json: () =>
            Promise.resolve({
              success: true,
              systemNews: mockSystemNewsItems,
            }),
        } as Response);
      }
      return Promise.resolve({
        ok: true,
        status: 200,
        statusText: "OK",
        headers: new Headers({ "Content-Type": "application/json" }),
        json: () => Promise.resolve({ success: true }),
      } as Response);
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  const supportedLanguages: Record<string, NewsTranslations> = {
    en: enNewsSystemItems as NewsTranslations,
    sv: svNewsSystemItems as NewsTranslations,
    fr: frNewsSystemItems as NewsTranslations,
    de: deNewsSystemItems as NewsTranslations,
    no: noNewsSystemItems as NewsTranslations,
    pl: plNewsSystemItems as NewsTranslations,
    rw: rwNewsSystemItems as NewsTranslations,
  };

  describe("Translation File Structure", () => {
    test("all language files should have the same structure", () => {
      const baseStructure = enNewsSystemItems as NewsTranslations;

      Object.entries(supportedLanguages).forEach(([_lang, translations]) => {
        expect(translations).toHaveProperty("news-system-items");
        expect(typeof translations["news-system-items"]).toBe("object");

        // Check that all languages have the same top-level keys
        const baseKeys = Object.keys(baseStructure["news-system-items"]);
        const langKeys = Object.keys(translations["news-system-items"]);

        expect(langKeys.sort()).toEqual(baseKeys.sort());
      });
    });

    test("all translation files should export valid objects", () => {
      Object.entries(supportedLanguages).forEach(([_lang, translations]) => {
        expect(translations).toBeDefined();
        expect(typeof translations).toBe("object");
        expect(translations).not.toBeNull();
      });
    });
  });

  describe("System News Translation Coverage", () => {
    test("all system news items with translation keys should have translations in all languages", async () => {
      // Use mock system news items directly since the fetch is mocked
      const allSystemNewsItems = mockSystemNewsItems;

      // Find all system news items that use translation keys
      const newsWithTranslationKeys = allSystemNewsItems.filter(
        (item) => item.titleKey && item.contentKey
      );

      expect(newsWithTranslationKeys.length).toBeGreaterThan(0);

      newsWithTranslationKeys.forEach((newsItem) => {
        const newsId = newsItem.id;

        Object.entries(supportedLanguages).forEach(([_lang, translations]) => {
          const newsTranslations = getTranslationItem(translations, newsId);

          expect(newsTranslations).toBeDefined();
          if (newsTranslations) {
            expect(newsTranslations).toHaveProperty("title");
            expect(newsTranslations).toHaveProperty("content");
            expect(typeof newsTranslations.title).toBe("string");
            expect(typeof newsTranslations.content).toBe("string");
            expect(newsTranslations.title.length).toBeGreaterThan(0);
            expect(newsTranslations.content.length).toBeGreaterThan(0);
          }
        });
      });
    });

    test("welcome news should be translated in all languages", () => {
      const welcomeNewsId = "system-welcome-2024";

      Object.entries(supportedLanguages).forEach(([lang, translations]) => {
        const welcomeTranslations = getTranslationItem(
          translations,
          welcomeNewsId
        );

        expect(welcomeTranslations).toBeDefined();
        if (welcomeTranslations) {
          expect(welcomeTranslations.title).toBeDefined();
          expect(welcomeTranslations.content).toBeDefined();

          // Check that translations are not just the English version
          if (lang !== "en") {
            const englishTranslations = getTranslationItem(
              enNewsSystemItems as NewsTranslations,
              welcomeNewsId
            );
            if (englishTranslations) {
              expect(welcomeTranslations.title).not.toBe(
                englishTranslations.title
              );
              expect(welcomeTranslations.content).not.toBe(
                englishTranslations.content
              );
            }
          }
        }
      });
    });

    test("template example should be translated in all languages", () => {
      const templateNewsId = "system-template-example";

      Object.entries(supportedLanguages).forEach(([lang, translations]) => {
        const templateTranslations = getTranslationItem(
          translations,
          templateNewsId
        );

        expect(templateTranslations).toBeDefined();
        if (templateTranslations) {
          expect(templateTranslations.title).toBeDefined();
          expect(templateTranslations.content).toBeDefined();

          // Check that translations are not just the English version
          if (lang !== "en") {
            const englishTranslations = getTranslationItem(
              enNewsSystemItems as NewsTranslations,
              templateNewsId
            );
            if (englishTranslations) {
              expect(templateTranslations.title).not.toBe(
                englishTranslations.title
              );
              expect(templateTranslations.content).not.toBe(
                englishTranslations.content
              );
            }
          }
        }
      });
    });

    test("new features news should be translated in all languages", () => {
      const newFeaturesNewsId = "system-new-features-2025";

      Object.entries(supportedLanguages).forEach(([lang, translations]) => {
        const newFeaturesTranslations = getTranslationItem(
          translations,
          newFeaturesNewsId
        );

        expect(newFeaturesTranslations).toBeDefined();
        if (newFeaturesTranslations) {
          expect(newFeaturesTranslations.title).toBeDefined();
          expect(newFeaturesTranslations.content).toBeDefined();

          // Check that translations are not just the English version
          if (lang !== "en") {
            const englishTranslations = getTranslationItem(
              enNewsSystemItems as NewsTranslations,
              newFeaturesNewsId
            );
            if (englishTranslations) {
              expect(newFeaturesTranslations.title).not.toBe(
                englishTranslations.title
              );
              expect(newFeaturesTranslations.content).not.toBe(
                englishTranslations.content
              );
            }
          }
        }
      });
    });
  });

  describe("Translation Quality", () => {
    test("all translations should contain meaningful content", () => {
      Object.entries(supportedLanguages).forEach(([_lang, translations]) => {
        const newsItems = translations["news-system-items"];

        Object.entries(newsItems).forEach(([newsId, newsTranslations]) => {
          // Skip metadata, authors, and categories sections as they contain labels, not news items
          if (
            newsId === "metadata" ||
            newsId === "authors" ||
            newsId === "categories"
          )
            return;

          // Type guard to ensure we're working with news items that have title and content
          if (isNewsTranslationItem(newsTranslations)) {
            // Title should be meaningful (not just placeholder text)
            expect(newsTranslations.title).not.toMatch(/^(title|Title|TITLE)$/);
            expect(newsTranslations.title).not.toMatch(/^(test|Test|TEST)/);
            expect(newsTranslations.title.length).toBeGreaterThan(5);

            // Content should be meaningful
            expect(newsTranslations.content).not.toMatch(
              /^(content|Content|CONTENT)$/
            );
            expect(newsTranslations.content).not.toMatch(/^(test|Test|TEST)/);
            expect(newsTranslations.content.length).toBeGreaterThan(20);
          }
        });
      });
    });

    test("translations should not contain obvious placeholder text", () => {
      const placeholderPatterns = [
        /lorem ipsum/i,
        /placeholder/i,
        /TODO/i,
        /FIXME/i,
        /\[(?!.*\]\(.*\)).*\]/, // Text in brackets like [TRANSLATE] but not markdown links [text](url)
        /{{.*}}/, // Template variables like {{title}}
      ];

      Object.entries(supportedLanguages).forEach(([_lang, translations]) => {
        const newsItems = translations["news-system-items"];

        Object.entries(newsItems).forEach(([newsId, newsTranslations]) => {
          // Skip metadata, authors, and categories sections as they contain labels, not news items
          if (
            newsId === "metadata" ||
            newsId === "authors" ||
            newsId === "categories"
          )
            return;

          // Type guard to ensure we're working with news items that have title and content
          if (isNewsTranslationItem(newsTranslations)) {
            placeholderPatterns.forEach((pattern) => {
              expect(newsTranslations.title).not.toMatch(pattern);
              expect(newsTranslations.content).not.toMatch(pattern);
            });
          }
        });
      });
    });

    test("translations should preserve markdown formatting", () => {
      Object.entries(supportedLanguages).forEach(([_lang, translations]) => {
        const newsItems = translations["news-system-items"];

        Object.entries(newsItems).forEach(([newsId, newsTranslations]) => {
          // Skip metadata, authors, and categories sections as they contain labels, not news items
          if (
            newsId === "metadata" ||
            newsId === "authors" ||
            newsId === "categories"
          )
            return;

          // Type guard to ensure we're working with news items that have title and content
          if (isNewsTranslationItem(newsTranslations)) {
            const content = newsTranslations.content;

            // If English version has markdown, translated versions should too
            const englishItem = getTranslationItem(
              enNewsSystemItems as NewsTranslations,
              newsId
            );
            const englishContent = englishItem ? englishItem.content : "";

            if (englishContent.includes("**")) {
              expect(content).toMatch(/\*\*.*\*\*/); // Bold text
            }

            if (/^- /m.test(englishContent)) {
              expect(content).toMatch(/^- /m); // List items
            }

            if (englishContent.includes("[") && englishContent.includes("](")) {
              expect(content).toMatch(/\[.*\]\(.*\)/); // Links
            }
          }
        });
      });
    });
  });

  describe("Language-Specific Validation", () => {
    test("Swedish translations should use appropriate Swedish characters", () => {
      const swedishTranslations = svNewsSystemItems["news-system-items"];

      Object.entries(swedishTranslations).forEach(
        ([newsId, newsTranslations]) => {
          // Skip metadata, authors, and categories sections as they contain labels, not news items
          if (
            newsId === "metadata" ||
            newsId === "authors" ||
            newsId === "categories"
          )
            return;

          // Type guard to ensure we're working with news items that have title and content
          if (isNewsTranslationItem(newsTranslations)) {
            const text =
              newsTranslations.title + " " + newsTranslations.content;

            // Swedish should contain some Swedish-specific words or characters
            const hasSwedishCharacters = /[åäöÅÄÖ]/.test(text);
            const hasSwedishWords =
              /\b(och|eller|för|till|från|med|på|av|är|har|kan|ska|kommer|använd|hjälp)\b/i.test(
                text
              );

            expect(hasSwedishCharacters || hasSwedishWords).toBe(true);
          }
        }
      );
    });

    test("French translations should use appropriate French characters", () => {
      const frenchTranslations = frNewsSystemItems["news-system-items"];

      Object.entries(frenchTranslations).forEach(
        ([newsId, newsTranslations]) => {
          // Skip metadata, authors, and categories sections as they contain labels, not news items
          if (
            newsId === "metadata" ||
            newsId === "authors" ||
            newsId === "categories"
          )
            return;

          // Type guard to ensure we're working with news items that have title and content
          if (isNewsTranslationItem(newsTranslations)) {
            const text =
              newsTranslations.title + " " + newsTranslations.content;

            // French should contain some French-specific words or characters
            const hasFrenchCharacters =
              /[àâäéèêëïîôöùûüÿçÀÂÄÉÈÊËÏÎÔÖÙÛÜŸÇ]/.test(text);
            const hasFrenchWords =
              /\b(et|ou|pour|avec|sur|dans|de|du|des|le|la|les|un|une|est|sont|avoir|être)\b/i.test(
                text
              );

            expect(hasFrenchCharacters || hasFrenchWords).toBe(true);
          }
        }
      );
    });

    test("German translations should use appropriate German characteristics", () => {
      const germanTranslations = deNewsSystemItems["news-system-items"];

      Object.entries(germanTranslations).forEach(
        ([newsId, newsTranslations]) => {
          // Skip metadata, authors, and categories sections as they contain labels, not news items
          if (
            newsId === "metadata" ||
            newsId === "authors" ||
            newsId === "categories"
          )
            return;

          // Type guard to ensure we're working with news items that have title and content
          if (isNewsTranslationItem(newsTranslations)) {
            const text =
              newsTranslations.title + " " + newsTranslations.content;

            // German should contain some German-specific words or characters
            const hasGermanCharacters = /[äöüßÄÖÜ]/.test(text);
            const hasGermanWords =
              /\b(und|oder|für|mit|auf|von|zu|der|die|das|ein|eine|ist|sind|haben|können)\b/i.test(
                text
              );

            expect(hasGermanCharacters || hasGermanWords).toBe(true);
          }
        }
      );
    });

    test("Polish translations should use appropriate Polish characters", () => {
      const polishTranslations = plNewsSystemItems["news-system-items"];

      Object.entries(polishTranslations).forEach(
        ([newsId, newsTranslations]) => {
          // Skip metadata, authors, and categories sections as they contain labels, not news items
          if (
            newsId === "metadata" ||
            newsId === "authors" ||
            newsId === "categories"
          )
            return;

          // Type guard to ensure we're working with news items that have title and content
          if (isNewsTranslationItem(newsTranslations)) {
            const text =
              newsTranslations.title + " " + newsTranslations.content;

            // Polish should contain some Polish-specific characters
            const hasPolishCharacters = /[ąćęłńóśźżĄĆĘŁŃÓŚŹŻ]/.test(text);
            const hasPolishWords =
              /\b(i|lub|dla|z|na|w|do|od|jest|są|może|można|będzie)\b/i.test(
                text
              );

            expect(hasPolishCharacters || hasPolishWords).toBe(true);
          }
        }
      );
    });
  });

  describe("Translation Consistency", () => {
    test("all languages should have consistent key structure for each news item", () => {
      const baseKeys = Object.keys(enNewsSystemItems["news-system-items"]);

      baseKeys.forEach((newsId) => {
        // Skip metadata, authors, and categories sections as they have different structure
        if (
          newsId === "metadata" ||
          newsId === "authors" ||
          newsId === "categories"
        )
          return;

        const englishItem = getTranslationItem(
          enNewsSystemItems as NewsTranslations,
          newsId
        );
        if (!englishItem) return;

        const englishSubKeys = Object.keys(englishItem).sort();

        Object.entries(supportedLanguages).forEach(([_lang, translations]) => {
          if (_lang === "en") return; // Skip English as it's the base

          const translatedItem = getTranslationItem(translations, newsId);
          expect(translatedItem).toBeDefined();

          if (translatedItem) {
            const translatedSubKeys = Object.keys(translatedItem).sort();
            expect(translatedSubKeys).toEqual(englishSubKeys);
          }
        });
      });
    });

    test("translation lengths should be reasonable compared to English", () => {
      Object.entries(supportedLanguages).forEach(([_lang, translations]) => {
        if (_lang === "en") return; // Skip English as it's the base

        const newsItems = translations["news-system-items"];

        Object.entries(newsItems).forEach(([newsId, newsTranslations]) => {
          // Skip metadata, authors, and categories sections as they contain labels, not news items
          if (
            newsId === "metadata" ||
            newsId === "authors" ||
            newsId === "categories"
          )
            return;

          // Type guard to ensure we're working with news items that have title and content
          if (isNewsTranslationItem(newsTranslations)) {
            const englishItem = getTranslationItem(
              enNewsSystemItems as NewsTranslations,
              newsId
            );

            if (englishItem) {
              // Translated title should not be more than 3x or less than 0.3x the English length
              const titleRatio =
                newsTranslations.title.length / englishItem.title.length;
              expect(titleRatio).toBeGreaterThan(0.3);
              expect(titleRatio).toBeLessThan(3.0);

              // Translated content should not be more than 2x or less than 0.5x the English length
              const contentRatio =
                newsTranslations.content.length / englishItem.content.length;
              expect(contentRatio).toBeGreaterThan(0.5);
              expect(contentRatio).toBeLessThan(2.0);
            }
          }
        });
      });
    });
  });

  describe("Integration with System News", () => {
    test("all system news translation keys should match actual news items", async () => {
      const allSystemNewsItems = mockSystemNewsItems;
      const systemNewsIds = allSystemNewsItems
        .filter((item) => item.titleKey && item.contentKey)
        .map((item) => item.id);

      Object.entries(supportedLanguages).forEach(([_lang, translations]) => {
        const translationKeys = Object.keys(translations["news-system-items"]);

        systemNewsIds.forEach((newsId) => {
          expect(translationKeys).toContain(newsId);
        });
      });
    });

    test("no orphaned translations should exist", async () => {
      const allSystemNewsItems = mockSystemNewsItems;
      const systemNewsIds = allSystemNewsItems.map((item) => item.id);

      Object.entries(supportedLanguages).forEach(([_lang, translations]) => {
        const translationKeys = Object.keys(translations["news-system-items"]);

        translationKeys.forEach((translationKey) => {
          // Allow template example as it's for documentation
          if (translationKey === "system-template-example") return;
          // Allow metadata section as it contains translation labels
          if (translationKey === "metadata") return;
          // Allow authors section as it contains author translation labels
          if (translationKey === "authors") return;
          // Allow categories section as it contains category translation labels
          if (translationKey === "categories") return;

          expect(systemNewsIds).toContain(translationKey);
        });
      });
    });
  });

  describe("Performance and Loading", () => {
    test("all translation files should load without errors", () => {
      Object.entries(supportedLanguages).forEach(([_lang, translations]) => {
        expect(() => {
          JSON.stringify(translations);
        }).not.toThrow();
      });
    });

    test("translation files should not be excessively large", () => {
      Object.entries(supportedLanguages).forEach(([_lang, translations]) => {
        const serialized = JSON.stringify(translations);

        // Each translation file should be less than 50KB
        expect(serialized.length).toBeLessThan(50000);
      });
    });
  });
});
