import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import LoginForm from "../LoginForm";
import System from "@/models/system";

// Mock i18next
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock System model
jest.mock("@/models/system", () => ({
  __esModule: true,
  default: {
    fetchCustomParagraph: jest.fn().mockResolvedValue({
      paragraphText: "Custom login text",
    }),
  },
}));

describe("LoginForm", () => {
  const mockOnSubmit = jest.fn();
  const mockSetLoading = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    // Restore the default mock implementation after clearing
    (System.fetchCustomParagraph as jest.Mock).mockResolvedValue({
      paragraphText: "Custom login text",
    });
  });

  describe("Rendering", () => {
    it("should render login form with username and password inputs", () => {
      render(<LoginForm onSubmit={mockOnSubmit} loading={false} />);

      expect(
        screen.getByPlaceholderText("login.multi-user.placeholder-username")
      ).toBeInTheDocument();
      expect(
        screen.getByPlaceholderText("login.multi-user.placeholder-password")
      ).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: "login.button" })
      ).toBeInTheDocument();
    });

    it("should show loading state when loading prop is true", () => {
      render(<LoginForm onSubmit={mockOnSubmit} loading={true} />);

      const submitButton = screen.getByRole("button");
      expect(submitButton).toHaveTextContent("login.logging");
      expect(submitButton).toBeDisabled();
    });

    it("should display error message when error prop is provided", () => {
      const errorMessage = "Invalid credentials";
      render(
        <LoginForm
          onSubmit={mockOnSubmit}
          loading={false}
          error={errorMessage}
        />
      );

      expect(screen.getByRole("alert")).toHaveTextContent(errorMessage);
    });

    it("should not display error when showError is false", () => {
      const errorMessage = "Invalid credentials";
      render(
        <LoginForm
          onSubmit={mockOnSubmit}
          loading={false}
          error={errorMessage}
          showError={false}
        />
      );

      expect(screen.queryByRole("alert")).not.toBeInTheDocument();
    });

    it("should apply Rwanda style when isRwandaStyle is true", () => {
      render(
        <LoginForm
          onSubmit={mockOnSubmit}
          loading={false}
          isRwandaStyle={true}
        />
      );

      const submitButton = screen.getByRole("button");
      expect(submitButton).toHaveClass("submit-btn-rw");
    });
  });

  describe("Form submission", () => {
    it("should call onSubmit with form data when form is submitted", async () => {
      const user = userEvent.setup();
      render(<LoginForm onSubmit={mockOnSubmit} loading={false} />);

      const usernameInput = screen.getByPlaceholderText(
        "login.multi-user.placeholder-username"
      );
      const passwordInput = screen.getByPlaceholderText(
        "login.multi-user.placeholder-password"
      );
      const submitButton = screen.getByRole("button", { name: "login.button" });

      await user.type(usernameInput, "testuser");
      await user.type(passwordInput, "testpassword");
      await user.click(submitButton);

      expect(mockOnSubmit).toHaveBeenCalledTimes(1);
      const formData = mockOnSubmit.mock.calls[0][0];
      expect(formData.get("username")).toBe("testuser");
      expect(formData.get("password")).toBe("testpassword");
    });

    it("should have required fields", () => {
      render(<LoginForm onSubmit={mockOnSubmit} loading={false} />);

      // In real browsers, HTML5 validation would prevent submission
      // In tests, we need to check that the inputs have required attribute
      const usernameInput = screen.getByPlaceholderText(
        "login.multi-user.placeholder-username"
      );
      const passwordInput = screen.getByPlaceholderText(
        "login.multi-user.placeholder-password"
      );

      expect(usernameInput).toHaveAttribute("required");
      expect(passwordInput).toHaveAttribute("required");
    });

    it("should not submit form when loading", async () => {
      const user = userEvent.setup();
      render(<LoginForm onSubmit={mockOnSubmit} loading={true} />);

      const submitButton = screen.getByRole("button");
      await user.click(submitButton);

      expect(mockOnSubmit).not.toHaveBeenCalled();
    });
  });

  describe("Accessibility", () => {
    it("should have proper ARIA labels and attributes", () => {
      render(
        <LoginForm
          onSubmit={mockOnSubmit}
          loading={false}
          error="Error message"
        />
      );

      const usernameInput = screen.getByPlaceholderText(
        "login.multi-user.placeholder-username"
      );
      const passwordInput = screen.getByPlaceholderText(
        "login.multi-user.placeholder-password"
      );

      expect(usernameInput).toHaveAttribute("aria-required", "true");
      expect(passwordInput).toHaveAttribute("aria-required", "true");
      expect(usernameInput).toHaveAttribute("aria-invalid", "true");
      expect(passwordInput).toHaveAttribute("aria-invalid", "true");
      expect(usernameInput).toHaveAttribute("aria-describedby", "login-error");
      expect(passwordInput).toHaveAttribute("aria-describedby", "login-error");
    });

    it("should have proper autocomplete attributes", () => {
      render(<LoginForm onSubmit={mockOnSubmit} loading={false} />);

      const usernameInput = screen.getByPlaceholderText(
        "login.multi-user.placeholder-username"
      );
      const passwordInput = screen.getByPlaceholderText(
        "login.multi-user.placeholder-password"
      );

      expect(usernameInput).toHaveAttribute("autocomplete", "username");
      expect(passwordInput).toHaveAttribute("autocomplete", "current-password");
    });

    it("should have screen reader only labels", () => {
      render(<LoginForm onSubmit={mockOnSubmit} loading={false} />);

      expect(
        screen.getByLabelText("login.multi-user.placeholder-username")
      ).toBeInTheDocument();
      expect(
        screen.getByLabelText("login.multi-user.placeholder-password")
      ).toBeInTheDocument();
    });
  });

  describe("Custom login text", () => {
    it("should fetch and display custom login text on mount", async () => {
      render(
        <LoginForm
          onSubmit={mockOnSubmit}
          loading={false}
          setLoading={mockSetLoading}
        />
      );

      await waitFor(() => {
        expect(System.fetchCustomParagraph).toHaveBeenCalledTimes(1);
      });

      expect(mockSetLoading).toHaveBeenCalledWith(false);
    });

    it("should use fallback text when custom paragraph is not available", async () => {
      System.fetchCustomParagraph = jest.fn().mockResolvedValue({});

      render(
        <LoginForm
          onSubmit={mockOnSubmit}
          loading={false}
          setLoading={mockSetLoading}
        />
      );

      await waitFor(() => {
        expect(System.fetchCustomParagraph).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe("Keyboard interactions", () => {
    it("should submit form when Enter key is pressed", async () => {
      const user = userEvent.setup();
      render(<LoginForm onSubmit={mockOnSubmit} loading={false} />);

      const usernameInput = screen.getByPlaceholderText(
        "login.multi-user.placeholder-username"
      );
      const passwordInput = screen.getByPlaceholderText(
        "login.multi-user.placeholder-password"
      );

      await user.type(usernameInput, "testuser");
      await user.type(passwordInput, "testpassword{enter}");

      expect(mockOnSubmit).toHaveBeenCalledTimes(1);
    });

    it("should allow tab navigation between fields", async () => {
      const user = userEvent.setup();
      render(<LoginForm onSubmit={mockOnSubmit} loading={false} />);

      const usernameInput = screen.getByPlaceholderText(
        "login.multi-user.placeholder-username"
      );
      const passwordInput = screen.getByPlaceholderText(
        "login.multi-user.placeholder-password"
      );
      const submitButton = screen.getByRole("button", { name: "login.button" });

      usernameInput.focus();
      expect(document.activeElement).toBe(usernameInput);

      await user.tab();
      expect(document.activeElement).toBe(passwordInput);

      await user.tab();
      expect(document.activeElement).toBe(submitButton);
    });
  });
});
