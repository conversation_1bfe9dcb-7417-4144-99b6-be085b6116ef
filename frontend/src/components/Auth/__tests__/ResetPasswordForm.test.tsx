import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import ResetPasswordForm from "../ResetPasswordForm";

// Mock i18next
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

describe("ResetPasswordForm", () => {
  const mockOnSubmit = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Rendering", () => {
    it("should render reset password form with all elements", () => {
      render(<ResetPasswordForm onSubmit={mockOnSubmit} />);

      // Use getAllByText since the text appears in both heading and button
      const resetPasswordTexts = screen.getAllByText(
        "login.multi-user.reset-password"
      );
      expect(resetPasswordTexts).toHaveLength(2); // heading and button

      expect(screen.getByText("user-menu.new-password")).toBeInTheDocument();
      expect(screen.getByPlaceholderText("New Password")).toBeInTheDocument();
      expect(
        screen.getByPlaceholderText("Confirm Password")
      ).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: "login.multi-user.reset-password" })
      ).toBeInTheDocument();
    });

    it("should have password type for both input fields", () => {
      render(<ResetPasswordForm onSubmit={mockOnSubmit} />);

      const newPasswordInput = screen.getByPlaceholderText("New Password");
      const confirmPasswordInput =
        screen.getByPlaceholderText("Confirm Password");

      expect(newPasswordInput).toHaveAttribute("type", "password");
      expect(confirmPasswordInput).toHaveAttribute("type", "password");
    });

    it("should have required attribute on both input fields", () => {
      render(<ResetPasswordForm onSubmit={mockOnSubmit} />);

      const newPasswordInput = screen.getByPlaceholderText("New Password");
      const confirmPasswordInput =
        screen.getByPlaceholderText("Confirm Password");

      expect(newPasswordInput).toHaveAttribute("required");
      expect(confirmPasswordInput).toHaveAttribute("required");
    });
  });

  describe("Form interaction", () => {
    it("should update new password field when typing", async () => {
      const user = userEvent.setup();
      render(<ResetPasswordForm onSubmit={mockOnSubmit} />);

      const newPasswordInput = screen.getByPlaceholderText("New Password");
      await user.type(newPasswordInput, "newPassword123");

      expect(newPasswordInput).toHaveValue("newPassword123");
    });

    it("should update confirm password field when typing", async () => {
      const user = userEvent.setup();
      render(<ResetPasswordForm onSubmit={mockOnSubmit} />);

      const confirmPasswordInput =
        screen.getByPlaceholderText("Confirm Password");
      await user.type(confirmPasswordInput, "newPassword123");

      expect(confirmPasswordInput).toHaveValue("newPassword123");
    });

    it("should maintain separate state for both password fields", async () => {
      const user = userEvent.setup();
      render(<ResetPasswordForm onSubmit={mockOnSubmit} />);

      const newPasswordInput = screen.getByPlaceholderText("New Password");
      const confirmPasswordInput =
        screen.getByPlaceholderText("Confirm Password");

      await user.type(newPasswordInput, "password1");
      await user.type(confirmPasswordInput, "password2");

      expect(newPasswordInput).toHaveValue("password1");
      expect(confirmPasswordInput).toHaveValue("password2");
    });
  });

  describe("Form submission", () => {
    it("should call onSubmit with both passwords when form is submitted", async () => {
      const user = userEvent.setup();
      render(<ResetPasswordForm onSubmit={mockOnSubmit} />);

      const newPasswordInput = screen.getByPlaceholderText("New Password");
      const confirmPasswordInput =
        screen.getByPlaceholderText("Confirm Password");
      const submitButton = screen.getByRole("button", {
        name: "login.multi-user.reset-password",
      });

      await user.type(newPasswordInput, "newPassword123");
      await user.type(confirmPasswordInput, "newPassword123");
      await user.click(submitButton);

      expect(mockOnSubmit).toHaveBeenCalledTimes(1);
      expect(mockOnSubmit).toHaveBeenCalledWith(
        "newPassword123",
        "newPassword123"
      );
    });

    it("should submit form when passwords are different", async () => {
      const user = userEvent.setup();
      render(<ResetPasswordForm onSubmit={mockOnSubmit} />);

      const newPasswordInput = screen.getByPlaceholderText("New Password");
      const confirmPasswordInput =
        screen.getByPlaceholderText("Confirm Password");
      const submitButton = screen.getByRole("button", {
        name: "login.multi-user.reset-password",
      });

      await user.type(newPasswordInput, "password1");
      await user.type(confirmPasswordInput, "password2");
      await user.click(submitButton);

      expect(mockOnSubmit).toHaveBeenCalledWith("password1", "password2");
    });

    it("should have required attributes on password fields", () => {
      render(<ResetPasswordForm onSubmit={mockOnSubmit} />);

      const newPasswordInput = screen.getByPlaceholderText("New Password");
      const confirmPasswordInput =
        screen.getByPlaceholderText("Confirm Password");

      expect(newPasswordInput).toHaveAttribute("required");
      expect(confirmPasswordInput).toHaveAttribute("required");
    });

    it("should submit form when Enter key is pressed", async () => {
      const user = userEvent.setup();
      render(<ResetPasswordForm onSubmit={mockOnSubmit} />);

      const newPasswordInput = screen.getByPlaceholderText("New Password");
      const confirmPasswordInput =
        screen.getByPlaceholderText("Confirm Password");

      await user.type(newPasswordInput, "newPassword123");
      await user.type(confirmPasswordInput, "newPassword123{enter}");

      expect(mockOnSubmit).toHaveBeenCalledTimes(1);
      expect(mockOnSubmit).toHaveBeenCalledWith(
        "newPassword123",
        "newPassword123"
      );
    });
  });

  describe("Keyboard navigation", () => {
    it("should allow tab navigation between fields", async () => {
      const user = userEvent.setup();
      render(<ResetPasswordForm onSubmit={mockOnSubmit} />);

      const newPasswordInput = screen.getByPlaceholderText("New Password");
      const confirmPasswordInput =
        screen.getByPlaceholderText("Confirm Password");
      const submitButton = screen.getByRole("button", {
        name: "login.multi-user.reset-password",
      });

      newPasswordInput.focus();
      expect(document.activeElement).toBe(newPasswordInput);

      await user.tab();
      expect(document.activeElement).toBe(confirmPasswordInput);

      await user.tab();
      expect(document.activeElement).toBe(submitButton);
    });
  });

  describe("Edge cases", () => {
    it("should handle very long passwords", async () => {
      const user = userEvent.setup();
      render(<ResetPasswordForm onSubmit={mockOnSubmit} />);

      const longPassword = "a".repeat(100);
      const newPasswordInput = screen.getByPlaceholderText("New Password");
      const confirmPasswordInput =
        screen.getByPlaceholderText("Confirm Password");

      await user.type(newPasswordInput, longPassword);
      await user.type(confirmPasswordInput, longPassword);

      expect(newPasswordInput).toHaveValue(longPassword);
      expect(confirmPasswordInput).toHaveValue(longPassword);
    });

    it("should handle special characters in passwords", async () => {
      const user = userEvent.setup();
      render(<ResetPasswordForm onSubmit={mockOnSubmit} />);

      const specialPassword = "P@ssw0rd!#$%^&*()";
      const newPasswordInput = screen.getByPlaceholderText("New Password");
      const confirmPasswordInput =
        screen.getByPlaceholderText("Confirm Password");
      const submitButton = screen.getByRole("button", {
        name: "login.multi-user.reset-password",
      });

      await user.type(newPasswordInput, specialPassword);
      await user.type(confirmPasswordInput, specialPassword);
      await user.click(submitButton);

      expect(mockOnSubmit).toHaveBeenCalledWith(
        specialPassword,
        specialPassword
      );
    });

    it("should handle rapid typing", async () => {
      const user = userEvent.setup({ delay: null });
      render(<ResetPasswordForm onSubmit={mockOnSubmit} />);

      const newPasswordInput = screen.getByPlaceholderText("New Password");
      await user.type(newPasswordInput, "rapidtyping123");

      expect(newPasswordInput).toHaveValue("rapidtyping123");
    });
  });
});
