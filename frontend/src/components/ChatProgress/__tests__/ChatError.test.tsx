import { render, screen, fireEvent, within } from "@testing-library/react";
import { describe, it, expect, jest, beforeEach } from "@jest/globals";
import "@testing-library/jest-dom";
import ChatError from "../ChatError";

// Mock dependencies
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

jest.mock("@phosphor-icons/react", () => ({
  Warning: ({ className }: { className?: string }) => (
    <svg data-testid="warning-icon" className={className}>
      Warning Icon
    </svg>
  ),
}));

jest.mock("react-icons/lu", () => ({
  LuChevronDown: ({ className }: { className?: string }) => (
    <svg data-testid="chevron-icon" className={className}>
      Chevron Icon
    </svg>
  ),
}));

jest.mock("@/components/Button", () => ({
  Button: ({ children, onClick, className, variant, size }: any) => (
    <button
      onClick={onClick}
      className={`${className} ${variant} ${size}`}
      data-testid={`button-${typeof children === "string" ? children : "complex"}`}
    >
      {children}
    </button>
  ),
}));

describe("ChatError Component", () => {
  const mockOnDismiss = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Rendering Behavior", () => {
    it("should not render when error is null", () => {
      const { container } = render(<ChatError error={null} />);
      expect(container.firstChild).toBeNull();
    });

    it("should not render when error is undefined", () => {
      const { container } = render(<ChatError error={undefined} />);
      expect(container.firstChild).toBeNull();
    });

    it("should not render when error is empty string", () => {
      const { container } = render(<ChatError error="" />);
      expect(container.firstChild).toBeNull();
    });

    it("should render when error is provided", () => {
      render(<ChatError error="Test error message" />);

      (expect(screen.getByRole("alert")) as any).toBeInTheDocument();
      (
        expect(screen.getByText("chatProgress.error.title")) as any
      ).toBeInTheDocument();
      (
        expect(screen.getByText("chatProgress.error.description")) as any
      ).toBeInTheDocument();
    });

    it("should display warning icon", () => {
      render(<ChatError error="Test error" />);

      const icon = screen.getByTestId("warning-icon");
      (expect(icon) as any).toBeInTheDocument();
      (expect(icon) as any).toHaveClass("h-5", "w-5", "text-red-500");
    });
  });

  describe("Error Details Toggle", () => {
    it("should not show error details initially", () => {
      render(<ChatError error="Detailed error message" />);

      (
        expect(screen.queryByText("Detailed error message")) as any
      ).not.toBeInTheDocument();
      (
        expect(screen.getByText("chatProgress.error.showDetails")) as any
      ).toBeInTheDocument();
    });

    it("should show error details when show details button is clicked", () => {
      render(<ChatError error="Detailed error message" />);

      const showDetailsButton = screen.getByText(
        "chatProgress.error.showDetails"
      );
      fireEvent.click(showDetailsButton);

      (
        expect(screen.getByText("Detailed error message")) as any
      ).toBeInTheDocument();
      (
        expect(screen.getByText("chatProgress.error.hideDetails")) as any
      ).toBeInTheDocument();
    });

    it("should hide error details when hide details button is clicked", () => {
      render(<ChatError error="Detailed error message" />);

      // First show details
      const showDetailsButton = screen.getByText(
        "chatProgress.error.showDetails"
      );
      fireEvent.click(showDetailsButton);

      (
        expect(screen.getByText("Detailed error message")) as any
      ).toBeInTheDocument();

      // Then hide details
      const hideDetailsButton = screen.getByText(
        "chatProgress.error.hideDetails"
      );
      fireEvent.click(hideDetailsButton);

      (
        expect(screen.queryByText("Detailed error message")) as any
      ).not.toBeInTheDocument();
    });

    it("should rotate chevron icon when details are shown", () => {
      render(<ChatError error="Test error" />);

      const chevronIcon = screen.getByTestId("chevron-icon");

      // Initially not rotated (no -rotate-90 class)
      (expect(chevronIcon) as any).not.toHaveClass("-rotate-90");

      // Click to show details
      const showDetailsButton = screen.getByText(
        "chatProgress.error.showDetails"
      );
      fireEvent.click(showDetailsButton);

      // Should be rotated
      (expect(chevronIcon) as any).toHaveClass("-rotate-90");
    });

    it("should toggle details multiple times", () => {
      render(<ChatError error="Toggle test error" />);

      const toggleButton = screen.getByText("chatProgress.error.showDetails");

      // Toggle multiple times
      for (let i = 0; i < 3; i++) {
        fireEvent.click(toggleButton);
        (
          expect(screen.getByText("Toggle test error")) as any
        ).toBeInTheDocument();

        fireEvent.click(screen.getByText("chatProgress.error.hideDetails"));
        (
          expect(screen.queryByText("Toggle test error")) as any
        ).not.toBeInTheDocument();
      }
    });
  });

  describe("Dismiss Functionality", () => {
    it("should call onDismiss when dismiss button is clicked", () => {
      render(<ChatError error="Test error" onDismiss={mockOnDismiss} />);

      const dismissButton = screen.getByText("chatProgress.error.dismiss");
      fireEvent.click(dismissButton);

      expect(mockOnDismiss).toHaveBeenCalledTimes(1);
    });

    it("should not throw error when dismiss is clicked without onDismiss prop", () => {
      render(<ChatError error="Test error" />);

      const dismissButton = screen.getByText("chatProgress.error.dismiss");

      // Should not throw error
      expect(() => {
        fireEvent.click(dismissButton);
      }).not.toThrow();
    });

    it("should still show error details after dismiss is clicked", () => {
      render(<ChatError error="Persistent error" onDismiss={mockOnDismiss} />);

      // Show details
      fireEvent.click(screen.getByText("chatProgress.error.showDetails"));
      (expect(screen.getByText("Persistent error")) as any).toBeInTheDocument();

      // Click dismiss
      fireEvent.click(screen.getByText("chatProgress.error.dismiss"));

      // Details should still be visible (component doesn't unmount itself)
      (expect(screen.getByText("Persistent error")) as any).toBeInTheDocument();
      expect(mockOnDismiss).toHaveBeenCalled();
    });
  });

  describe("Error Message Display", () => {
    it("should display short error messages correctly", () => {
      const shortError = "Short error";
      render(<ChatError error={shortError} />);

      fireEvent.click(screen.getByText("chatProgress.error.showDetails"));

      const errorDisplay = screen.getByText(shortError);
      (expect(errorDisplay) as any).toBeInTheDocument();
      (expect(errorDisplay) as any).toHaveClass(
        "font-medium",
        "text-sm",
        "line-clamp-5",
        "py-2",
        "px-3",
        "h-fit",
        "rounded-lg",
        "border",
        "font-mono",
        "bg-white",
        "overflow-y-auto"
      );
    });

    it("should display long error messages with proper styling", () => {
      const longError = "A".repeat(500); // Very long error message
      render(<ChatError error={longError} />);

      fireEvent.click(screen.getByText("chatProgress.error.showDetails"));

      const errorDisplay = screen.getByText(longError);
      (expect(errorDisplay) as any).toBeInTheDocument();
      (expect(errorDisplay) as any).toHaveClass(
        "line-clamp-5",
        "overflow-y-auto"
      );
    });

    it("should display multi-line error messages", () => {
      const multiLineError = "Line 1\nLine 2\nLine 3";
      render(<ChatError error={multiLineError} />);

      fireEvent.click(screen.getByText("chatProgress.error.showDetails"));

      // Use getAllByText and check for the error message in the details section
      const errorElements = screen.getAllByText((_content, element) => {
        return element?.textContent === multiLineError;
      });

      // Should find the error text in the details section (last element)
      expect(errorElements.length).toBeGreaterThan(0);
      const detailsError = errorElements[errorElements.length - 1];
      (expect(detailsError) as any).toBeInTheDocument();
      (expect(detailsError) as any).toHaveClass(
        "font-medium",
        "text-sm",
        "line-clamp-5"
      );
    });

    it("should display error messages with special characters", () => {
      const specialCharsError = "Error: <script>alert('xss')</script> & more";
      render(<ChatError error={specialCharsError} />);

      fireEvent.click(screen.getByText("chatProgress.error.showDetails"));

      (expect(screen.getByText(specialCharsError)) as any).toBeInTheDocument();
    });
  });

  describe("Accessibility", () => {
    it("should have proper ARIA attributes", () => {
      render(<ChatError error="Accessible error" />);

      const alertElement = screen.getByRole("alert");
      (expect(alertElement) as any).toHaveAttribute("aria-live", "assertive");
      (expect(alertElement) as any).toHaveAttribute("aria-atomic", "true");
    });

    it("should have keyboard accessible buttons", () => {
      render(<ChatError error="Keyboard test" onDismiss={mockOnDismiss} />);

      const showDetailsButton = screen.getByText(
        "chatProgress.error.showDetails"
      );
      const dismissButton = screen.getByText("chatProgress.error.dismiss");

      // Buttons should be focusable
      expect(showDetailsButton.tagName).toBe("BUTTON");
      expect(dismissButton.tagName).toBe("BUTTON");
    });

    it("should maintain focus order when details are shown", () => {
      render(<ChatError error="Focus test error" />);

      // Show details
      fireEvent.click(screen.getByText("chatProgress.error.showDetails"));

      // All interactive elements should still be accessible
      const buttons = screen.getAllByRole("button");
      expect(buttons).toHaveLength(2); // Hide details + Dismiss
    });
  });

  describe("Styling and Layout", () => {
    it("should apply correct base styling classes", () => {
      render(<ChatError error="Style test" />);

      const container = screen.getByRole("alert");
      (expect(container) as any).toHaveClass(
        "relative",
        "flex",
        "flex-col",
        "gap-4",
        "rounded-xl",
        "border",
        "bg-red-50",
        "border-red-200"
      );
    });

    it("should apply correct button container styling", () => {
      render(<ChatError error="Button style test" />);

      const showDetailsButton = screen.getByText(
        "chatProgress.error.showDetails"
      );
      const buttonContainer = showDetailsButton.parentElement;

      (expect(buttonContainer) as any).toHaveClass("flex", "gap-6", "mt-2");
    });

    it("should apply correct error details container styling", () => {
      render(<ChatError error="Details style test" />);

      fireEvent.click(screen.getByText("chatProgress.error.showDetails"));

      const errorText = screen.getByText("Details style test");
      (expect(errorText) as any).toHaveClass(
        "font-medium",
        "text-sm",
        "line-clamp-5",
        "py-2",
        "px-3",
        "h-fit",
        "rounded-lg",
        "border",
        "font-mono",
        "bg-white",
        "overflow-y-auto"
      );
    });

    it("should maintain responsive layout", () => {
      render(<ChatError error="Responsive test" />);

      const container = screen.getByRole("alert");
      (expect(container) as any).toHaveClass("w-full", "max-w-[32rem]");
    });
  });

  describe("State Management", () => {
    it("should maintain independent state for multiple instances", () => {
      render(
        <>
          <ChatError error="Error 1" />
          <ChatError error="Error 2" />
        </>
      );

      const alerts = screen.getAllByRole("alert");
      expect(alerts).toHaveLength(2);

      // Show details on first error
      const firstAlert = alerts[0];
      const firstShowButton = within(firstAlert).getByText(
        "chatProgress.error.showDetails"
      );
      fireEvent.click(firstShowButton);

      // First error should show details
      (
        expect(within(firstAlert).getByText("Error 1")) as any
      ).toBeInTheDocument();

      // Second error should not show details
      const secondAlert = alerts[1];
      (
        expect(within(secondAlert).queryByText("Error 2")) as any
      ).not.toBeInTheDocument();
    });

    it("should preserve state when error message changes", () => {
      const { rerender } = render(<ChatError error="Initial error" />);

      // Show details
      fireEvent.click(screen.getByText("chatProgress.error.showDetails"));
      (expect(screen.getByText("Initial error")) as any).toBeInTheDocument();

      // Change error message
      rerender(<ChatError error="Updated error" />);

      // Details should still be shown with new error
      (
        expect(screen.queryByText("Initial error")) as any
      ).not.toBeInTheDocument();
      (expect(screen.getByText("Updated error")) as any).toBeInTheDocument();
    });
  });

  describe("Edge Cases", () => {
    it("should handle rapid button clicks", () => {
      render(<ChatError error="Rapid click test" onDismiss={mockOnDismiss} />);

      const showDetailsButton = screen.getByText(
        "chatProgress.error.showDetails"
      );
      const dismissButton = screen.getByText("chatProgress.error.dismiss");

      // Rapid clicks on show details
      for (let i = 0; i < 5; i++) {
        fireEvent.click(showDetailsButton);
      }

      // Should only show details once
      const errorTexts = screen.getAllByText("Rapid click test");
      expect(errorTexts).toHaveLength(1);

      // Rapid clicks on dismiss
      for (let i = 0; i < 5; i++) {
        fireEvent.click(dismissButton);
      }

      // Should only call onDismiss 5 times
      expect(mockOnDismiss).toHaveBeenCalledTimes(5);
    });

    it("should handle very long error messages without breaking layout", () => {
      const veryLongError = "Error: " + "x".repeat(1000);
      render(<ChatError error={veryLongError} />);

      fireEvent.click(screen.getByText("chatProgress.error.showDetails"));

      const errorDisplay = screen.getByText(veryLongError);
      // Should have line-clamp-5 to prevent breaking layout
      (expect(errorDisplay) as any).toHaveClass("line-clamp-5");
    });

    it("should handle error messages with only whitespace", () => {
      render(<ChatError error="   " />);

      // Should still render (non-empty string)
      (expect(screen.getByRole("alert")) as any).toBeInTheDocument();

      fireEvent.click(screen.getByText("chatProgress.error.showDetails"));

      // Whitespace should be preserved
      const detailsContainer = screen.getByText(
        "chatProgress.error.hideDetails"
      ).parentElement?.parentElement?.parentElement;
      (expect(detailsContainer) as any).toBeInTheDocument();
    });

    it("should handle undefined onDismiss gracefully", () => {
      render(<ChatError error="No dismiss handler" onDismiss={undefined} />);

      const dismissButton = screen.getByText("chatProgress.error.dismiss");

      // Should not throw when clicked
      expect(() => {
        fireEvent.click(dismissButton);
      }).not.toThrow();
    });
  });

  describe("Integration Scenarios", () => {
    it("should work correctly when error changes from null to defined", () => {
      const { rerender } = render(<ChatError error={null} />);

      // Initially no render
      (expect(screen.queryByRole("alert")) as any).not.toBeInTheDocument();

      // Update to show error
      rerender(<ChatError error="New error appeared" />);

      (expect(screen.getByRole("alert")) as any).toBeInTheDocument();
      (
        expect(screen.getByText("chatProgress.error.title")) as any
      ).toBeInTheDocument();
    });

    it("should work correctly when error changes from defined to null", () => {
      const { rerender } = render(<ChatError error="Existing error" />);

      (expect(screen.getByRole("alert")) as any).toBeInTheDocument();

      // Update to remove error
      rerender(<ChatError error={null} />);

      (expect(screen.queryByRole("alert")) as any).not.toBeInTheDocument();
    });

    it("should handle prop updates correctly", () => {
      const firstDismiss = jest.fn();
      const secondDismiss = jest.fn();

      const { rerender } = render(
        <ChatError error="Test error" onDismiss={firstDismiss} />
      );

      // Click dismiss with first handler
      fireEvent.click(screen.getByText("chatProgress.error.dismiss"));
      expect(firstDismiss).toHaveBeenCalledTimes(1);
      expect(secondDismiss).toHaveBeenCalledTimes(0);

      // Update handler
      rerender(<ChatError error="Test error" onDismiss={secondDismiss} />);

      // Click dismiss with second handler
      fireEvent.click(screen.getByText("chatProgress.error.dismiss"));
      expect(firstDismiss).toHaveBeenCalledTimes(1);
      expect(secondDismiss).toHaveBeenCalledTimes(1);
    });
  });
});
