/// <reference types="@testing-library/jest-dom" />
import "@testing-library/jest-dom";
import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import React from "react";
import ChatProgress from "../index";
import useThreadProgress from "@/hooks/useThreadProgress";
import { ChatMessage } from "@/types";

// Use global Jest functions instead of importing from @jest/globals
// This ensures proper integration with jest-dom matchers
const { describe, it, beforeEach, afterEach } = global;

// Mock dependencies
jest.mock("@/hooks/useThreadProgress");
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock child components
jest.mock("@/components/Button", () => ({
  Button: ({ children, onClick, className, variant, size }: any) => (
    <button
      onClick={onClick}
      className={`${className} ${variant} ${size}`}
      data-testid={children}
    >
      {children}
    </button>
  ),
}));

jest.mock("@/components/ui/Progress", () => ({
  __esModule: true,
  default: ({ value }: { value: number }) => (
    <div data-testid="progress-bar" data-value={value}>
      Progress: {value}%
    </div>
  ),
}));

jest.mock("@/components/Modals/ProgressModal", () => ({
  __esModule: true,
  default: ({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) =>
    isOpen ? (
      <div data-testid="progress-modal">
        <button onClick={onClose}>Close Progress Modal</button>
      </div>
    ) : null,
}));

jest.mock("@/components/Modals/AbortWarningModal", () => ({
  __esModule: true,
  default: ({
    isOpen,
    onCancel,
    onAbort,
  }: {
    isOpen: boolean;
    onCancel: () => void;
    onAbort: () => void;
  }) =>
    isOpen ? (
      <div data-testid="abort-modal">
        <button onClick={onCancel}>Cancel Abort</button>
        <button onClick={onAbort}>Confirm Abort</button>
      </div>
    ) : null,
}));

// Mock utility functions
jest.mock("@/utils/progressUtils", () => ({
  calculateIncrementalProgress: (
    currentStep: number,
    totalSteps: number,
    _stepDetails: any[]
  ) => {
    // Handle undefined values
    if (!currentStep || !totalSteps || totalSteps === 0) return 0;
    // Simple mock calculation
    return Math.round(((currentStep - 1) / totalSteps) * 100);
  },
}));

const mockUseThreadProgress = useThreadProgress as jest.MockedFunction<
  typeof useThreadProgress
>;

describe("ChatProgress Component - Comprehensive Tests", () => {
  const mockCancel = jest.fn();
  const mockUpdateHistory = jest.fn() as jest.MockedFunction<
    React.Dispatch<React.SetStateAction<ChatMessage[]>>
  >;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  describe("Component Rendering", () => {
    it("should render with fade-in animation after delay", async () => {
      mockUseThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 3,
        totalSteps: 7,
        startTime: Date.now(),
        flowType: "standard",
        currentSubStep: null,
        stepStatus: "in_progress",
        stepMessage: null,
        stepDetails: [],
        error: null,
        isCompleted: false,
        completionTime: null,
        start: jest.fn(),
        update: jest.fn(),
        finish: jest.fn(),
        cancel: mockCancel,
        setError: jest.fn(),
        clearError: jest.fn(),
        forceCleanup: jest.fn(),
        clearStaleProgress: jest.fn(),
        getAbortSignal: jest.fn(() => null),
      } as any);

      const { container } = render(<ChatProgress threadSlug="test-thread" />);

      // Initially should have opacity-0 class
      const progressContainer = container.querySelector(".opacity-0");
      expect(progressContainer).toBeInTheDocument();

      // After 350ms, should animate to visible
      act(() => {
        jest.advanceTimersByTime(350);
      });

      await waitFor(() => {
        const visibleContainer = container.querySelector(".opacity-100");
        expect(visibleContainer).toBeInTheDocument();
      });
    });

    it("should display step label and description from translation keys", () => {
      mockUseThreadProgress.mockReturnValue({
        currentStep: 2,
        totalSteps: 5,
        flowType: "document_processing",
        stepDetails: [],
        cancel: mockCancel,
      } as any);

      render(<ChatProgress threadSlug="test-thread" />);

      act(() => {
        jest.advanceTimersByTime(350);
      });

      // Should show translated step label and description
      expect(
        screen.getByText("chatProgress.types.document_processing.step2.label")
      ).toBeInTheDocument();
      expect(
        screen.getByText("chatProgress.types.document_processing.step2.desc")
      ).toBeInTheDocument();
    });

    it("should display fallback labels when flowType is not provided", () => {
      mockUseThreadProgress.mockReturnValue({
        currentStep: 3,
        totalSteps: 5,
        flowType: null,
        stepDetails: [],
        cancel: mockCancel,
      } as any);

      render(<ChatProgress threadSlug="test-thread" />);

      act(() => {
        jest.advanceTimersByTime(350);
      });

      // Should show generic step label
      expect(screen.getByText("chatProgress.step 3")).toBeInTheDocument();
      expect(screen.getByText("chatProgress.processing")).toBeInTheDocument();
    });

    it("should render action buttons", () => {
      mockUseThreadProgress.mockReturnValue({
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [],
        cancel: mockCancel,
      } as any);

      render(<ChatProgress threadSlug="test-thread" />);

      act(() => {
        jest.advanceTimersByTime(350);
      });

      expect(screen.getByTestId("chatProgress.details")).toBeInTheDocument();
      expect(screen.getByTestId("chatProgress.abort")).toBeInTheDocument();
    });
  });

  describe("Progress Calculation", () => {
    it("should calculate and display correct progress percentage", () => {
      mockUseThreadProgress.mockReturnValue({
        currentStep: 4,
        totalSteps: 10,
        flowType: null,
        stepDetails: [],
        cancel: mockCancel,
      } as any);

      render(<ChatProgress threadSlug="test-thread" />);

      act(() => {
        jest.advanceTimersByTime(350);
      });

      const progressBar = screen.getByTestId("progress-bar");
      expect(progressBar).toHaveAttribute("data-value", "30"); // (4-1)/10 * 100 = 30%
      expect(screen.getByText("Progress: 30%")).toBeInTheDocument();
    });

    it("should handle edge cases in progress calculation", () => {
      // Test with currentStep = 1 (should be 0%)
      mockUseThreadProgress.mockReturnValue({
        currentStep: 1,
        totalSteps: 5,
        flowType: null,
        stepDetails: [],
        cancel: mockCancel,
      } as any);

      const { rerender } = render(<ChatProgress threadSlug="test-thread" />);

      act(() => {
        jest.advanceTimersByTime(350);
      });

      expect(screen.getByTestId("progress-bar")).toHaveAttribute(
        "data-value",
        "0"
      );

      // Test with currentStep = totalSteps (should be close to 100%)
      mockUseThreadProgress.mockReturnValue({
        currentStep: 5,
        totalSteps: 5,
        flowType: null,
        stepDetails: [],
        cancel: mockCancel,
      } as any);

      rerender(<ChatProgress threadSlug="test-thread" />);

      expect(screen.getByTestId("progress-bar")).toHaveAttribute(
        "data-value",
        "80"
      ); // (5-1)/5 * 100 = 80%
    });
  });

  describe("Modal Interactions", () => {
    beforeEach(() => {
      mockUseThreadProgress.mockReturnValue({
        currentStep: 2,
        totalSteps: 5,
        flowType: null,
        stepDetails: [],
        cancel: mockCancel,
      } as any);
    });

    it("should open and close progress details modal", async () => {
      render(<ChatProgress threadSlug="test-thread" />);

      act(() => {
        jest.advanceTimersByTime(350);
      });

      // Initially modal should not be visible
      expect(screen.queryByTestId("progress-modal")).not.toBeInTheDocument();

      // Click details button
      fireEvent.click(screen.getByTestId("chatProgress.details"));

      // Modal should open
      await waitFor(() => {
        expect(screen.getByTestId("progress-modal")).toBeInTheDocument();
      });

      // Close modal
      fireEvent.click(screen.getByText("Close Progress Modal"));

      // Modal should close
      await waitFor(() => {
        expect(screen.queryByTestId("progress-modal")).not.toBeInTheDocument();
      });
    });

    it("should open and handle abort warning modal", async () => {
      render(<ChatProgress threadSlug="test-thread" />);

      act(() => {
        jest.advanceTimersByTime(350);
      });

      // Click abort button
      fireEvent.click(screen.getByTestId("chatProgress.abort"));

      // Abort modal should open
      await waitFor(() => {
        expect(screen.getByTestId("abort-modal")).toBeInTheDocument();
      });

      // Test cancel action
      fireEvent.click(screen.getByText("Cancel Abort"));

      await waitFor(() => {
        expect(screen.queryByTestId("abort-modal")).not.toBeInTheDocument();
      });

      expect(mockCancel).not.toHaveBeenCalled();
    });

    it("should confirm abort and add cancellation message", async () => {
      const mockHistoryArray: ChatMessage[] = [];
      mockUpdateHistory.mockImplementation((updater) => {
        if (typeof updater === "function") {
          const newHistory = updater(mockHistoryArray);
          mockHistoryArray.push(...newHistory);
        } else {
          mockHistoryArray.length = 0;
          mockHistoryArray.push(...updater);
        }
      });

      render(
        <ChatProgress
          threadSlug="test-thread"
          updateHistory={mockUpdateHistory}
        />
      );

      act(() => {
        jest.advanceTimersByTime(350);
      });

      // Open abort modal
      fireEvent.click(screen.getByTestId("chatProgress.abort"));

      await waitFor(() => {
        expect(screen.getByTestId("abort-modal")).toBeInTheDocument();
      });

      // Confirm abort
      fireEvent.click(screen.getByText("Confirm Abort"));

      await waitFor(() => {
        expect(screen.queryByTestId("abort-modal")).not.toBeInTheDocument();
      });

      // Should call cancel function
      expect(mockCancel).toHaveBeenCalledTimes(1);

      // Should add cancellation message to history
      expect(mockUpdateHistory).toHaveBeenCalledTimes(1);
      expect(mockUpdateHistory).toHaveBeenCalledWith(expect.any(Function));

      // Verify the cancellation message structure
      const updateFunction = mockUpdateHistory.mock.calls[0][0] as (
        prev: ChatMessage[]
      ) => ChatMessage[];
      const newHistory = updateFunction([]);
      expect(newHistory).toHaveLength(1);
      expect(newHistory[0]).toMatchObject({
        type: "statusResponse",
        content: "chatProgress.cancelled",
        role: "assistant",
        closed: true,
        error: null,
        animate: false,
        pending: false,
      });
      expect(newHistory[0].uuid).toMatch(/^cancelled-\d+$/);
    });

    it("should handle abort without updateHistory function", async () => {
      render(<ChatProgress threadSlug="test-thread" />);

      act(() => {
        jest.advanceTimersByTime(350);
      });

      // Open and confirm abort
      fireEvent.click(screen.getByTestId("chatProgress.abort"));
      await waitFor(() => {
        expect(screen.getByTestId("abort-modal")).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText("Confirm Abort"));

      // Should still call cancel even without updateHistory
      expect(mockCancel).toHaveBeenCalledTimes(1);
      expect(mockUpdateHistory).not.toHaveBeenCalled();
    });
  });

  describe("Props and State Management", () => {
    it("should handle empty threadSlug gracefully", () => {
      mockUseThreadProgress.mockReturnValue({
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [],
        cancel: mockCancel,
      } as any);

      // Render with empty string (default value)
      render(<ChatProgress />);

      act(() => {
        jest.advanceTimersByTime(350);
      });

      // Component should still render
      expect(screen.getByTestId("chatProgress.details")).toBeInTheDocument();
    });

    it("should pass correct threadSlug to useThreadProgress hook", () => {
      const testSlug = "test-thread-123";

      // Set up mock before render
      mockUseThreadProgress.mockReturnValue({
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [],
        cancel: mockCancel,
      } as any);

      render(<ChatProgress threadSlug={testSlug} />);

      expect(mockUseThreadProgress).toHaveBeenCalledWith(testSlug);
    });

    it("should handle different flow types", () => {
      const flowTypes = [
        "standard",
        "document_processing",
        "data_analysis",
        "custom_flow",
      ];

      flowTypes.forEach((flowType) => {
        mockUseThreadProgress.mockReturnValue({
          currentStep: 2,
          totalSteps: 5,
          flowType,
          stepDetails: [],
          cancel: mockCancel,
        } as any);

        const { unmount } = render(<ChatProgress threadSlug="test" />);

        act(() => {
          jest.advanceTimersByTime(350);
        });

        expect(
          screen.getByText(`chatProgress.types.${flowType}.step2.label`)
        ).toBeInTheDocument();

        unmount();
      });
    });
  });

  describe("Accessibility", () => {
    it("should have proper button labels for screen readers", () => {
      mockUseThreadProgress.mockReturnValue({
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [],
        cancel: mockCancel,
      } as any);

      render(<ChatProgress threadSlug="test-thread" />);

      act(() => {
        jest.advanceTimersByTime(350);
      });

      const detailsButton = screen.getByTestId("chatProgress.details");
      const abortButton = screen.getByTestId("chatProgress.abort");

      expect(detailsButton).toHaveTextContent("chatProgress.details");
      expect(abortButton).toHaveTextContent("chatProgress.abort");
    });

    it("should manage focus correctly when modals open/close", async () => {
      mockUseThreadProgress.mockReturnValue({
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [],
        cancel: mockCancel,
      } as any);

      render(<ChatProgress threadSlug="test-thread" />);

      act(() => {
        jest.advanceTimersByTime(350);
      });

      const detailsButton = screen.getByTestId("chatProgress.details");

      // Open modal
      fireEvent.click(detailsButton);

      await waitFor(() => {
        expect(screen.getByTestId("progress-modal")).toBeInTheDocument();
      });

      // Close modal
      fireEvent.click(screen.getByText("Close Progress Modal"));

      await waitFor(() => {
        expect(screen.queryByTestId("progress-modal")).not.toBeInTheDocument();
      });
    });
  });

  describe("Error Handling and Edge Cases", () => {
    it("should handle undefined or null values from useThreadProgress", () => {
      mockUseThreadProgress.mockReturnValue({
        currentStep: undefined,
        totalSteps: undefined,
        flowType: null,
        stepDetails: null,
        cancel: mockCancel,
      } as any);

      render(<ChatProgress threadSlug="test-thread" />);

      act(() => {
        jest.advanceTimersByTime(350);
      });

      // Should render with fallback values
      expect(
        screen.getByText("chatProgress.step undefined")
      ).toBeInTheDocument();
    });

    it("should handle rapid button clicks", async () => {
      mockUseThreadProgress.mockReturnValue({
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [],
        cancel: mockCancel,
      } as any);

      render(<ChatProgress threadSlug="test-thread" />);

      act(() => {
        jest.advanceTimersByTime(350);
      });

      const detailsButton = screen.getByTestId("chatProgress.details");

      // Rapid clicks
      fireEvent.click(detailsButton);
      fireEvent.click(detailsButton);
      fireEvent.click(detailsButton);

      // Should still only show one modal
      await waitFor(() => {
        const modals = screen.getAllByTestId("progress-modal");
        expect(modals).toHaveLength(1);
      });
    });

    it("should cleanup timer on unmount", () => {
      mockUseThreadProgress.mockReturnValue({
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [],
        cancel: mockCancel,
      } as any);

      const { unmount } = render(<ChatProgress threadSlug="test-thread" />);

      // Unmount before timer completes
      unmount();

      // Advance timers - should not cause any errors
      act(() => {
        jest.advanceTimersByTime(350);
      });

      // No assertions needed - test passes if no errors are thrown
    });
  });

  describe("Integration with Thread Progress", () => {
    it("should update display when progress values change", () => {
      // Set up initial mock before render
      mockUseThreadProgress.mockReturnValue({
        currentStep: 2,
        totalSteps: 10,
        flowType: "analysis",
        stepDetails: [],
        cancel: mockCancel,
      } as any);

      const { rerender } = render(<ChatProgress threadSlug="test-thread" />);

      act(() => {
        jest.advanceTimersByTime(350);
      });

      // Update mock to return new values
      mockUseThreadProgress.mockReturnValue({
        currentStep: 5,
        totalSteps: 10,
        flowType: "analysis",
        stepDetails: [],
        cancel: mockCancel,
      } as any);

      rerender(<ChatProgress threadSlug="test-thread" />);

      // Should show updated values
      expect(
        screen.getByText("chatProgress.types.analysis.step5.label")
      ).toBeInTheDocument();
      expect(screen.getByTestId("progress-bar")).toHaveAttribute(
        "data-value",
        "40"
      ); // (5-1)/10 * 100 = 40%
    });

    it("should handle step details array correctly", () => {
      mockUseThreadProgress.mockReturnValue({
        currentStep: 3,
        totalSteps: 5,
        flowType: null,
        stepDetails: [
          { step: 1, detail: "Completed" },
          { step: 2, detail: "Completed" },
          { step: 3, detail: "In Progress" },
        ],
        cancel: mockCancel,
      } as any);

      render(<ChatProgress threadSlug="test-thread" />);

      act(() => {
        jest.advanceTimersByTime(350);
      });

      // Progress calculation should use stepDetails
      const progressBar = screen.getByTestId("progress-bar");
      expect(progressBar).toBeInTheDocument();
    });
  });

  describe("Styling and CSS Classes", () => {
    it("should apply correct CSS classes for styling", () => {
      mockUseThreadProgress.mockReturnValue({
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [],
        cancel: mockCancel,
      } as any);

      const { container } = render(<ChatProgress threadSlug="test-thread" />);

      act(() => {
        jest.advanceTimersByTime(350);
      });

      const mainContainer = container.firstChild as HTMLElement;
      expect(mainContainer).toHaveClass(
        "flex",
        "flex-col",
        "gap-6",
        "rounded-xl",
        "border",
        "bg-elevated"
      );
    });

    it("should apply correct styles to abort button", () => {
      mockUseThreadProgress.mockReturnValue({
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [],
        cancel: mockCancel,
      } as any);

      render(<ChatProgress threadSlug="test-thread" />);

      act(() => {
        jest.advanceTimersByTime(350);
      });

      const abortButton = screen.getByTestId("chatProgress.abort");
      expect(abortButton).toHaveClass("text-red-600");
    });
  });
});
