import { Worker, Viewer } from "@react-pdf-viewer/core";
import { searchPlugin } from "@react-pdf-viewer/search";
import "@react-pdf-viewer/core/lib/styles/index.css";
import "@react-pdf-viewer/search/lib/styles/index.css";
import "./DocumentHighlight.css";
import { decode as HTMLDecode } from "he";
import { useState, useEffect, useRef, useMemo, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { API_BASE } from "@/utils/constants";
import Modal from "@/components/ui/Modal";
import { Button } from "@/components/Button";
import { LuExternalLink, LuChevronUp, LuChevronDown } from "react-icons/lu";
import initPdfWorker from "@/utils/pdfWorker";
import { baseHeaders } from "@/utils/request";

interface Chunk {
  text: string;
  chunkSource?: string;
}

interface Metadata {
  [key: string]: unknown;
}

interface DocumentHighlightProps {
  url?: string;
  pathUrl?: string;
  chunks?: Chunk[];
  metadata?: Metadata;
  handleClose?: () => void;
  loadContent?: boolean;
  useDefault?: boolean;
}

interface ResolvedPath {
  location: "frontend" | "server" | null;
  path: string | null;
  frontendPath: string | null;
  serverPath: string | null;
  description?: string | null;
}

interface ErrorDetails {
  message: string;
  checkedPaths?: Array<{
    description: string;
    path: string;
  }>;
  documentPath?: string;
  path?: string;
  status?: number;
}

interface PathResolveResponse {
  success: boolean;
  location?: "frontend" | "server";
  path?: string;
  frontendPath?: string;
  serverPath?: string;
  description?: string;
  error?: string;
  checkedPaths?: Array<{
    description: string;
    path: string;
  }>;
}

interface Highlight {
  keyword: string;
  wholeWords: boolean;
  searchPattern: string;
  regex: RegExp;
}

/**
 * DocumentHighlight component for rendering PDF documents with highlight support
 * Exported as both named and default export for backwards compatibility
 */
export function DocumentHighlight(props: DocumentHighlightProps) {
  const {
    url,
    pathUrl,
    chunks,
    metadata: _metadata,
    handleClose: _handleClose,
    loadContent = false,
    useDefault = navigator.pdfViewerEnabled,
  } = props;

  // Initialize PDF.js worker when component mounts
  useEffect(() => {
    initPdfWorker();
  }, []);

  const { t } = useTranslation();
  // Initialize PDF search plugin unconditionally
  const searchPluginInstance = searchPlugin();
  const {
    ShowSearchPopoverButton,
    highlight,
    jumpToNextMatch,
    jumpToPreviousMatch,
  } = searchPluginInstance;

  // Calculate initial document path
  const documentPath = useMemo(() => {
    if (!chunks?.length) return pathUrl;

    // Get the document path from chunks
    let path = chunks[0]?.chunkSource?.split("://")?.[1] || pathUrl;

    // Check if path is undefined and provide a fallback
    if (!path || path === "undefined" || path === "null") {
      // Invalid path from chunks, using url fallback
      return url || "";
    }

    // Additional validation for common problematic paths
    if (path.includes("undefined") || path.includes("null")) {
      // Path contains undefined/null, cleaning
      path = path.replace(/undefined|null/g, "").replace(/\/+/g, "/");
    }

    return path;
  }, [chunks, pathUrl, url]);

  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [pdfData, setPdfData] = useState<string | null>(null);
  const [pdfLoadAttempted, setPdfLoadAttempted] = useState<boolean>(false);
  const [fallbackMode, setFallbackMode] = useState<boolean>(false);
  const [fallbackAttempted, setFallbackAttempted] = useState<boolean>(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [_iframeLoaded, setIframeLoaded] = useState<boolean>(false);

  // Simplified state for path resolution
  const [resolvedPath, setResolvedPath] = useState<ResolvedPath>({
    location: null,
    path: null,
    frontendPath: null,
    serverPath: null,
    description: null,
  });

  // Add error modal state
  const [showErrorModal, setShowErrorModal] = useState<boolean>(false);
  const [errorDetails, setErrorDetails] = useState<ErrorDetails | null>(null);

  // Improve path resolution effect to better handle the server response
  useEffect(() => {
    const resolvePaths = async (documentPath: string): Promise<boolean> => {
      if (!documentPath) {
        setError("No document path provided");
        return false;
      }

      // Step 1: Check frontend hotdir first (only in development)
      const filename = documentPath.split("/").pop() ?? "";

      // Handle PDF.json case - extract correct PDF filename
      const isPdfJson = filename?.endsWith(".pdf.json");
      const pdfFilename = isPdfJson
        ? filename.replace(/\.json$/, "")
        : filename?.replace(/\.json$/, ".pdf");

      // Only try frontend hotdir in development environments
      const isDevelopment =
        process.env.NODE_ENV === "development" ||
        window.location.hostname === "localhost";

      if (isDevelopment && pdfFilename) {
        // Ensure consistency with hotdir path format
        const frontendPath = `hotdir/${pdfFilename}`;
        const frontendUrl = `/hotdir/${pdfFilename}`;

        // Checking frontend hotdir

        try {
          const frontendResponse = await fetch(frontendUrl, {
            method: "HEAD",
            headers: {
              Accept: "application/pdf",
              ...baseHeaders(),
            },
          });

          if (frontendResponse.ok) {
            // Found PDF in frontend hotdir
            setResolvedPath({
              location: "frontend",
              path: frontendPath,
              frontendPath,
              serverPath: null,
            });
            return true;
          }
        } catch {
          // Frontend hotdir check failed
        }
      }

      // Step 2: Use server endpoint for path resolution (works in both dev and production)
      try {
        const encodedPath = encodeURIComponent(documentPath);
        const resolveUrl = `${API_BASE}/document/resolve-pdf-path?path=${encodedPath}`;
        // Checking server path resolution

        const resolveResponse = await fetch(resolveUrl, {
          headers: baseHeaders(),
        });

        if (resolveResponse.ok) {
          const resolveData: PathResolveResponse = await resolveResponse.json();

          if (resolveData.success) {
            // Set the resolved path based on server response
            // Handle both old and new response formats
            if (resolveData.location === "frontend") {
              setResolvedPath({
                location: "frontend",
                path: resolveData.path || resolveData.frontendPath || null,
                frontendPath:
                  resolveData.frontendPath || resolveData.path || null,
                serverPath: null,
              });
            } else {
              setResolvedPath({
                location: "server",
                path: resolveData.path || resolveData.serverPath || null,
                frontendPath: resolveData.frontendPath || null,
                serverPath: resolveData.serverPath || resolveData.path || null,
                description: resolveData.description || "Server path",
              });
            }
            return true;
          } else {
            // Server couldn't resolve the path - set detailed error information
            // Enhanced error handling with checkedPaths
            if (
              resolveData.checkedPaths &&
              resolveData.checkedPaths.length > 0
            ) {
              // Set detailed error information
              setErrorDetails({
                message: resolveData.error || "PDF file not found",
                checkedPaths: resolveData.checkedPaths,
                documentPath: documentPath,
              });
            }

            setError(resolveData.error || "PDF file not found");
          }
        } else {
          // Handle server error response
          try {
            const errorData = await resolveResponse.json();
            setError(errorData.error || "Server path resolution failed");
          } catch {
            const errorMsg = `Server error: ${resolveResponse.status} ${resolveResponse.statusText}`;
            setError(errorMsg);
          }
        }
      } catch (error) {
        setError(
          "Server path resolution request failed: " + (error as Error).message
        );
      }

      // Step 3: Final fallback - try direct server PDF endpoints

      // Try different server endpoint patterns
      const fallbackEndpoints = [
        `${API_BASE}/document/pdf-contents?path=${encodeURIComponent(documentPath)}`,
        `${API_BASE}/document/pdf-contents?path=${encodeURIComponent(pdfFilename || "")}`,
        `${API_BASE}/document/pdf/${encodeURIComponent(pdfFilename || "")}`,
        `${API_BASE}/document/pdf-contents/${encodeURIComponent(pdfFilename || "")}`,
      ];

      for (const endpoint of fallbackEndpoints) {
        try {
          const fallbackResponse = await fetch(endpoint, {
            method: "HEAD",
            headers: { ...baseHeaders(), Accept: "application/pdf" },
          });

          if (fallbackResponse.ok) {
            setResolvedPath({
              location: "server",
              path: documentPath,
              frontendPath: null,
              serverPath: documentPath,
              description: "Fallback server endpoint",
            });
            return true;
          }
        } catch {
          // Fallback endpoint failed
        }
      }

      setError("No valid PDF path found");
      return false;
    };

    let isMounted = true;

    if (documentPath && loadContent) {
      setIsLoading(true);
      setError(null);
      setErrorDetails(null);

      resolvePaths(documentPath).then((hasValidPath) => {
        if (!isMounted) return;

        if (!hasValidPath) {
          if (!error) {
            setError("No valid PDF path found");
          }
        }
        setIsLoading(false);
      });
    } else {
      setIsLoading(false);
    }

    return () => {
      isMounted = false;
    };
  }, [documentPath, loadContent]);

  // Helper to ensure consistent URL format for hotdir paths
  const getHotdirUrl = (path: string | null): string => {
    if (!path) return "";
    // Ensure path has single leading slash and no double slashes
    return `/${path.replace(/^\/+|^hotdir\//g, "")}`.replace(
      /^\/+/,
      "/hotdir/"
    );
  };

  // Simplified PDF fetching function
  const fetchPdfDocument = useCallback(async (): Promise<string> => {
    if (!resolvedPath.path) {
      throw new Error("No valid PDF path available");
    }

    try {
      let response: Response;

      // Use the appropriate endpoint based on path location
      if (resolvedPath.location === "frontend") {
        const frontendUrl = getHotdirUrl(resolvedPath.frontendPath);
        response = await fetch(frontendUrl, {
          headers: {
            Accept: "application/pdf",
            ...baseHeaders(),
          },
        });
      } else {
        response = await fetch(
          API_BASE +
            "/document/pdf-contents?path=" +
            encodeURIComponent(resolvedPath.serverPath || ""),
          {
            headers: { ...baseHeaders(), Accept: "application/pdf" },
          }
        );
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();
      if (blob.size === 0) {
        throw new Error("PDF file is empty");
      }

      const objectUrl = URL.createObjectURL(blob);
      return objectUrl;
    } catch (error) {
      throw new Error(
        error instanceof Error ? error.message : "Failed to fetch PDF document"
      );
    }
  }, [
    resolvedPath.location,
    resolvedPath.frontendPath,
    resolvedPath.serverPath,
  ]);

  // PDF loading effect
  useEffect(() => {
    let isMounted = true;
    let currentPdfData: string | null = null;

    if (!resolvedPath.path || !loadContent) {
      return;
    }

    setIsLoading(true);
    setError(null);
    setPdfData(null);
    setPdfLoadAttempted(false);
    setFallbackMode(false);
    setFallbackAttempted(false);

    fetchPdfDocument()
      .then((objectUrl) => {
        if (!isMounted) {
          // Clean up object URL if component unmounted
          if (objectUrl && objectUrl.startsWith("blob:")) {
            URL.revokeObjectURL(objectUrl);
          }
          return;
        }
        currentPdfData = objectUrl;
        setPdfData(objectUrl);
        setError(null);
        setPdfLoadAttempted(true);
      })
      .catch((error) => {
        if (!isMounted) return;
        setError((error as Error).message);
        setPdfLoadAttempted(true);
      })
      .finally(() => {
        if (!isMounted) return;
        setIsLoading(false);
      });

    return () => {
      isMounted = false;
      // Clean up any existing PDF data
      if (
        currentPdfData &&
        typeof currentPdfData === "string" &&
        currentPdfData.startsWith("blob:")
      ) {
        URL.revokeObjectURL(currentPdfData);
      }
    };
  }, [resolvedPath.path, loadContent, fetchPdfDocument]);

  // Check if the browser can render PDFs natively
  const canRenderPdf = useMemo(() => {
    // Try to detect if the browser can render PDFs
    const nav = navigator?.pdfViewerEnabled;

    // Additional checks for Chrome, Firefox, Safari, and Edge
    const userAgent = navigator.userAgent.toLowerCase();
    const isChrome = userAgent.includes("chrome");
    const isFirefox = userAgent.includes("firefox");
    const isSafari = userAgent.includes("safari") && !isChrome;
    const isEdge = userAgent.includes("edg");

    return nav || isChrome || isFirefox || isSafari || isEdge;
  }, []);

  // Fallback checks
  useEffect(() => {
    if (pdfLoadAttempted && pdfData && !fallbackMode) {
      // Delay check to allow time for rendering
      const emptyViewerCheckTimer = setTimeout(() => {
        const pdfContainer = document.querySelector(".rpv-core__viewer");
        const pdfPages = document.querySelectorAll(".rpv-core__page-layer");

        // If PDF viewer is empty, switch to fallback
        if (
          pdfContainer &&
          pdfContainer.children.length === 0 &&
          (pdfContainer as HTMLElement).clientHeight < 50 &&
          pdfPages.length === 0
        ) {
          // Switching to fallback - Native viewer failed to render
          if (!fallbackAttempted) {
            setFallbackMode(true);
            setFallbackAttempted(true);
          }
        }
      }, 3000);

      return () => clearTimeout(emptyViewerCheckTimer);
    }
  }, [pdfLoadAttempted, pdfData, fallbackMode, fallbackAttempted]);

  // Compute and memoize unique highlights from chunks
  const uniqueHighlights = useMemo(() => {
    if (!chunks || chunks.length < 1) return [];

    // Strip metadata header
    function omitChunkHeader(text: string): string {
      if (!text.startsWith("<document_metadata>")) return text;
      return text.split("</document_metadata>")[1].trim();
    }

    const highlights = chunks
      .map(({ text }) => {
        const processedText = HTMLDecode(omitChunkHeader(text))
          .replace(/\r\n/g, "\n")
          .replace(/\s+$/gm, "")
          .replace(/^\s+/gm, "")
          .replace(/\n{3,}/g, "\n\n")
          .trim();
        const variations: Highlight[] = [];
        const lines = processedText
          .split("\n")
          .filter((line) => line.trim().length > 0);
        if (lines.length < 2) return variations;

        const findTextBetweenLines = (
          startLine: string,
          endLine: string
        ): Highlight => {
          const escapeRegex = (str: string) =>
            str.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
          const pattern = `${escapeRegex(startLine)}[\\s\\S]*?${escapeRegex(endLine)}`;
          const regex = new RegExp(pattern, "gim");
          return {
            keyword: `${startLine}${endLine}`,
            wholeWords: false,
            searchPattern: pattern,
            regex,
          };
        };

        const createLineHighlight = (line: string): Highlight => {
          const escapeRegex = (str: string) =>
            str.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
          const pattern = escapeRegex(line);
          const regex = new RegExp(pattern, "g");
          return {
            keyword: line,
            wholeWords: false,
            searchPattern: pattern,
            regex,
          };
        };

        lines.forEach((_, i) => {
          for (let j = i + 1; j < lines.length; j++) {
            const startLine = lines[i].trim();
            const endLine = lines[j].trim();
            if (
              startLine.length < 3 ||
              endLine.length < 3 ||
              startLine === endLine
            )
              continue;
            const blockHighlight = findTextBetweenLines(startLine, endLine);
            if (blockHighlight.regex.test(processedText))
              variations.push(blockHighlight);
            const startHighlight = createLineHighlight(startLine);
            const endHighlight = createLineHighlight(endLine);
            if (startHighlight.regex.test(processedText))
              variations.push(startHighlight);
            if (endHighlight.regex.test(processedText))
              variations.push(endHighlight);
          }
        });
        return variations;
      })
      .flat();
    // Deduplicate highlights
    return highlights.filter(
      (h, idx, arr) => arr.findIndex((x) => x.keyword === h.keyword) === idx
    );
  }, [chunks]);

  // Apply highlights once the PDF data has loaded
  useEffect(() => {
    let termsToHighlight: Array<{
      keyword: string;
      matchCase: boolean;
      wholeWords: boolean;
    }> = [];
    if (pdfData && !fallbackMode && uniqueHighlights.length > 0) {
      termsToHighlight = uniqueHighlights.map(({ keyword, wholeWords }) => ({
        keyword,
        matchCase: false,
        wholeWords,
      }));
    }
    const safeHighlight =
      typeof highlight === "function" ? highlight : () => () => {};
    const clearFn = safeHighlight(termsToHighlight);
    return () => {
      if (typeof clearFn === "function") clearFn();
    };
  }, [pdfData, fallbackMode, uniqueHighlights, highlight]);

  // Iframe handlers for fallback mode
  const handleIframeLoad = (): void => {
    setIframeLoaded(true);
  };

  const handleIframeError = (): void => {
    setError(
      "Failed to load PDF in iframe. Please try downloading the document instead."
    );
    // Try to open in new tab as last resort
    if (resolvedPath.path) {
      const pdfUrl =
        resolvedPath.location === "frontend"
          ? `/${resolvedPath.frontendPath}`
          : `${API_BASE}/document/pdf-contents?path=${encodeURIComponent(resolvedPath.serverPath || "")}`;
      window.open(pdfUrl, "_blank");
    }
  };

  // Simplified DownloadPdfLink component
  const DownloadPdfLink = () => {
    // const downloadText = t("citations.download-btn", {
    //     defaultValue: "Download PDF",
    //   });
    const openInBrowserText = t("citations.open-in-browser", {
      defaultValue: "Open in Browser",
    });
    const unavailableText = t("citations.pdf-viewer-unavailable", {
      defaultValue: "PDF viewer is not available in your browser",
    });

    const downloadUrl =
      resolvedPath.location === "frontend"
        ? getHotdirUrl(resolvedPath.frontendPath)
        : `${API_BASE}/document/pdf-contents?path=${encodeURIComponent(resolvedPath.serverPath || "")}`;

    const handleOpenInBrowser = (e: React.MouseEvent): void => {
      e.preventDefault();
      const urlToOpen = url || downloadUrl;
      window.open(urlToOpen, "_blank", "noopener,noreferrer");
    };

    return (
      <div className="flex flex-col items-center justify-center p-4 border border-gray-300 rounded-lg bg-gray-50">
        <p className="mb-4">{unavailableText}</p>
        <div className="flex gap-4">
          {url && url !== downloadUrl && (
            <Button onClick={handleOpenInBrowser} variant="outline">
              {openInBrowserText}
            </Button>
          )}
        </div>
      </div>
    );
  };

  // Render fallback content (iframe or download link)
  const renderFallbackContent = () => {
    if (!resolvedPath.path) {
      return (
        <div className="flex items-center justify-center h-full">
          <p className="text-red-500">No PDF data available</p>
        </div>
      );
    }

    const displayUrl =
      resolvedPath.location === "frontend"
        ? getHotdirUrl(resolvedPath.frontendPath)
        : `${API_BASE}/document/pdf-contents?path=${encodeURIComponent(resolvedPath.serverPath || "")}`;

    const handleOpenInBrowser = (e: React.MouseEvent): void => {
      e.preventDefault();
      window.open(displayUrl, "_blank", "noopener,noreferrer");
    };

    return (
      <div className="flex flex-col items-center justify-center w-full h-full">
        <div className="p-1 bg-gray-100 border-b border-gray-200 text-xs text-gray-600 flex justify-center items-center">
          <span>
            {t("citations.pdf-collapse-tip", {
              defaultValue:
                "Tip: You can collapse this PDF tab using the button in the upper left corner",
            })}
          </span>
        </div>

        {/* Try iframe as fallback */}
        <div className="relative flex-grow w-full pdf-fallback-iframe-container">
          <iframe
            ref={iframeRef}
            src={displayUrl}
            title="PDF Document"
            className="w-full h-full border-0 pdf-fallback-iframe"
            onLoad={handleIframeLoad}
            onError={handleIframeError}
          />
        </div>

        {/* Always show action buttons at bottom */}
        <div className="p-4 border-t border-gray-200 flex justify-center bg-white w-full z-20 sticky bottom-0 shadow-md">
          <div className="flex gap-4">
            <Button
              onClick={handleOpenInBrowser}
              variant="outline"
              size="default"
              className="gap-1"
            >
              {t("citations.open-in-browser", {
                defaultValue: "Open in Browser",
              })}
              <LuExternalLink />
            </Button>
          </div>
        </div>
      </div>
    );
  };

  // Error state
  if (error) {
    const errorText = t("citations.error-loading");
    const noValidPathText = t("citations.no-valid-path");
    const openInBrowserText = t("citations.open-in-browser");
    const viewDetailsText = t("citations.view-details", {
      defaultValue: "View Details",
    });

    const errorUrl =
      resolvedPath.location === "frontend"
        ? getHotdirUrl(resolvedPath.frontendPath)
        : resolvedPath.serverPath
          ? `${API_BASE}/document/pdf-contents?path=${encodeURIComponent(resolvedPath.serverPath)}`
          : url;

    const handleOpenInBrowser = (e: React.MouseEvent): void => {
      e.preventDefault();
      if (errorUrl) {
        window.open(errorUrl, "_blank", "noopener,noreferrer");
      }
    };

    const handleTryAlternativeUrl = (e: React.MouseEvent): void => {
      e.preventDefault();
      // Try to construct alternative URLs based on the document path
      if (documentPath) {
        const filename = documentPath.split("/").pop();
        const alternativeUrl = `${API_BASE}/document/pdf-contents/${encodeURIComponent(filename || "")}`;
        window.open(alternativeUrl, "_blank", "noopener,noreferrer");
      }
    };

    return (
      <div className="flex flex-col items-center justify-center p-4 text-center">
        <p className="text-red-500 mb-4">
          {errorText}:{" "}
          {error === "No valid PDF path found" ? noValidPathText : error}
        </p>

        {/* Show document path for debugging */}
        {documentPath && (
          <div className="mb-4 text-left bg-gray-100 p-3 rounded max-w-full overflow-x-auto">
            <p className="font-semibold mb-2">Document Information:</p>
            <p className="text-sm text-gray-700">
              <span className="font-medium">Requested path:</span>{" "}
              <span className="font-mono text-xs break-all">
                {documentPath}
              </span>
            </p>
            {url && (
              <p className="text-sm text-gray-700 mt-1">
                <span className="font-medium">Source URL:</span>{" "}
                <span className="font-mono text-xs break-all">{url}</span>
              </p>
            )}
          </div>
        )}

        {errorDetails &&
          errorDetails.checkedPaths &&
          errorDetails.checkedPaths.length > 0 && (
            <div className="mb-4 text-left bg-gray-100 p-3 rounded max-w-full overflow-x-auto">
              <p className="font-semibold mb-2">Checked these locations:</p>
              <ul className="text-sm text-gray-700 list-disc pl-5 max-h-64 overflow-y-auto">
                {errorDetails.checkedPaths.map((pathInfo, index) => (
                  <li key={index} className="mb-1 break-all">
                    <span className="font-medium">{pathInfo.description}:</span>{" "}
                    <span className="font-mono text-xs">{pathInfo.path}</span>
                  </li>
                ))}
              </ul>
              {errorDetails.documentPath && (
                <p className="mt-2 text-sm text-gray-600">
                  <span className="font-medium">Original path:</span>{" "}
                  <span className="font-mono text-xs">
                    {errorDetails.documentPath}
                  </span>
                </p>
              )}
            </div>
          )}

        <div className="flex gap-4 flex-wrap justify-center">
          {errorUrl && (
            <Button
              onClick={handleOpenInBrowser}
              variant="ghost"
              className="text-blue-500 hover:underline hover:bg-transparent"
            >
              {openInBrowserText}
            </Button>
          )}

          {documentPath && (
            <Button
              onClick={handleTryAlternativeUrl}
              variant="ghost"
              className="text-blue-500 hover:underline hover:bg-transparent"
            >
              Try Alternative URL
            </Button>
          )}

          {errorDetails && !errorDetails.checkedPaths && (
            <Button
              variant="ghost"
              className="text-blue-500 hover:underline hover:bg-transparent"
              onClick={() => setShowErrorModal(true)}
            >
              {viewDetailsText}
            </Button>
          )}
        </div>

        {/* Development mode debugging info */}
        {process.env.NODE_ENV === "development" && (
          <details className="mt-4 text-left bg-yellow-50 p-3 rounded max-w-full overflow-x-auto">
            <summary className="font-semibold cursor-pointer">
              Debug Information (Development Only)
            </summary>
            <div className="mt-2 text-sm">
              <p>
                <strong>Document Path:</strong> {documentPath || "undefined"}
              </p>
              <p>
                <strong>URL:</strong> {url || "undefined"}
              </p>
              <p>
                <strong>Resolved Path Location:</strong>{" "}
                {resolvedPath.location || "none"}
              </p>
              <p>
                <strong>Resolved Path:</strong> {resolvedPath.path || "none"}
              </p>
              <p>
                <strong>Frontend Path:</strong>{" "}
                {resolvedPath.frontendPath || "none"}
              </p>
              <p>
                <strong>Server Path:</strong>{" "}
                {resolvedPath.serverPath || "none"}
              </p>
              <p>
                <strong>Load Content:</strong> {loadContent ? "true" : "false"}
              </p>
              <p>
                <strong>Error:</strong> {error}
              </p>
            </div>
          </details>
        )}
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-4 h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        <p className="ml-2 text-gray-600">{t("citations.loading-pdf")}</p>
      </div>
    );
  }

  // If in fallback mode or browser can't render PDFs properly
  if (fallbackMode || useDefault) {
    return (
      <div className={`pdf-highlighter flex flex-col`}>
        {canRenderPdf ? renderFallbackContent() : <DownloadPdfLink />}
      </div>
    );
  }

  // Function to handle opening the PDF in browser
  const handleOpenInBrowser = (e: React.MouseEvent): void => {
    e.preventDefault();
    const displayUrl =
      resolvedPath.location === "frontend"
        ? getHotdirUrl(resolvedPath.frontendPath)
        : `${API_BASE}/document/pdf-contents?path=${encodeURIComponent(resolvedPath.serverPath || "")}`;

    window.open(displayUrl, "_blank", "noopener,noreferrer");
  };

  const handlePrevHighlight = (): void => {
    jumpToPreviousMatch();
  };

  const handleNextHighlight = (): void => {
    jumpToNextMatch();
  };

  // Render main PDF viewer
  return (
    <div className={`pdf-highlighter`}>
      {showErrorModal && (
        <Modal
          isOpen={showErrorModal}
          onClose={() => setShowErrorModal(false)}
          title="PDF Loading Error"
          footer={
            <Button onClick={() => setShowErrorModal(false)}>Close</Button>
          }
        >
          <div className="p-2">
            <p className="text-red-500 mb-4">{errorDetails?.message}</p>
            {errorDetails?.path && (
              <p className="text-sm text-gray-600">Path: {errorDetails.path}</p>
            )}
            {errorDetails?.status && (
              <p className="text-sm text-gray-600">
                Status: {errorDetails.status}
              </p>
            )}
          </div>
        </Modal>
      )}
      <div className="custom-pdf-viewer-container border-2 border-gray-400/40 flex flex-col h-auto w-full rounded-t-lg">
        <div className="border-b border-black/10 flex justify-between p-1 rounded-lg">
          <div></div> {/* Empty div for flex spacing */}
          <div className="flex items-center gap-2">
            <ShowSearchPopoverButton />
            <Button
              onClick={handleOpenInBrowser}
              variant="secondary"
              size="sm"
              className="shadow-sm"
            >
              {t("citations.open-in-browser", {
                defaultValue: "Open in Browser",
              })}
              <LuExternalLink size={16} />
            </Button>
          </div>
          <div></div> {/* Empty div for flex spacing */}
        </div>
        <div className="pdf-viewer-scroll-container">
          {pdfData ? (
            <>
              {uniqueHighlights.length > 0 && (
                <div className="absolute right-12 top-1/3 flex flex-col gap-2 z-10">
                  <Button
                    onClick={handlePrevHighlight}
                    className="p-0 rounded-full shadow-md"
                    size="icon"
                    variant="secondary"
                    title={t("citations.previous-highlight", {
                      defaultValue: "Previous highlight",
                    })}
                  >
                    <LuChevronUp />
                  </Button>
                  <Button
                    onClick={handleNextHighlight}
                    className="p-0 rounded-full shadow-md"
                    size="icon"
                    variant="secondary"
                    title={t("citations.next-highlight", {
                      defaultValue: "Next highlight",
                    })}
                  >
                    <LuChevronDown />
                  </Button>
                </div>
              )}
              <Worker workerUrl="/pdf.worker.js">
                <Viewer
                  fileUrl={pdfData}
                  plugins={[searchPluginInstance]}
                  renderError={(_error: unknown) => {
                    if (!fallbackAttempted) {
                      setFallbackMode(true);
                      setFallbackAttempted(true);
                    }
                    return (
                      <div className="flex justify-center items-center h-full">
                        <p className="text-red-500">
                          {t("citations.error-loading-pdf")}
                        </p>
                        <Button
                          variant="secondary"
                          size="sm"
                          className="ml-2 shadow-sm"
                          onClick={() => {
                            if (!fallbackAttempted) {
                              setFallbackMode(true);
                              setFallbackAttempted(true);
                            }
                          }}
                        >
                          {t("citations.try-alternative-view")}
                        </Button>
                      </div>
                    );
                  }}
                />
              </Worker>
            </>
          ) : (
            <div className="flex justify-center items-center h-full">
              <p className="text-red-500">{t("citations.no-pdf-data")}</p>
              <DownloadPdfLink />
            </div>
          )}
        </div>

        {/* Consistent footer with action buttons that's always visible */}
        <div className="p-3 border-t border-gray-200 flex justify-center w-full">
          <div className="flex gap-4">
            <Button
              onClick={handleOpenInBrowser}
              variant="secondary"
              size="sm"
              className="shadow-sm"
            >
              {t("citations.open-in-browser", {
                defaultValue: "Open in Browser",
              })}
              <LuExternalLink size={16} />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default DocumentHighlight;
