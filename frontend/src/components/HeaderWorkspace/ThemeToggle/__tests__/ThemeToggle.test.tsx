import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, jest } from "@jest/globals";
import ThemeToggle from "../index";
import { useTheme, useSetTheme } from "@/stores/userStore";

// Mock the user store module
jest.mock("@/stores/userStore", () => ({
  useTheme: jest.fn(),
  useSetTheme: jest.fn(),
}));

// Type the mocked functions
const mockUseTheme = useTheme as jest.MockedFunction<typeof useTheme>;
const mockUseSetTheme = useSetTheme as jest.MockedFunction<typeof useSetTheme>;

describe("ThemeToggle", () => {
  it("renders the button and toggles the theme correctly on click", () => {
    const mockSetTheme = jest.fn();

    mockUseTheme.mockReturnValue("light");
    mockUseSetTheme.mockReturnValue(mockSetTheme);

    const { rerender } = render(<ThemeToggle />);

    const button = screen.getByLabelText("theme-toggle");
    expect(button).toBeInTheDocument();

    fireEvent.click(button);
    expect(mockSetTheme).toHaveBeenCalledTimes(1);
    expect(mockSetTheme).toHaveBeenCalledWith("dark");

    mockUseTheme.mockReturnValue("dark");
    rerender(<ThemeToggle />);

    const buttonAfterRerender = screen.getByLabelText("theme-toggle");
    fireEvent.click(buttonAfterRerender);

    expect(mockSetTheme).toHaveBeenCalledTimes(2);
    expect(mockSetTheme).toHaveBeenCalledWith("light");
  });
});
