import React from "react";
import { useTheme, useSetTheme } from "@/stores/userStore";
import { BsMoonStars } from "react-icons/bs";
import { Sun } from "@phosphor-icons/react";

const ThemeToggle: React.FC = () => {
  const theme = useTheme();
  const setTheme = useSetTheme();

  const toggleTheme = () => {
    setTheme(theme === "light" ? "dark" : "light");
  };

  return (
    <button
      onClick={toggleTheme}
      className="focus:outline-none"
      aria-label="theme-toggle"
    >
      {theme === "light" ? (
        <BsMoonStars
          size={20}
          className="text-foreground navbar-blk-btn opacity-80"
        />
      ) : (
        <Sun className="text-foreground navbar-blk-btn opacity-80" />
      )}
    </button>
  );
};

export default ThemeToggle;
