import React, { useState } from "react";
import { Button } from "@/components/Button";
import { BookOpen } from "@phosphor-icons/react";
import { useTranslation } from "react-i18next";
import UseGuideModal from "./UseGuideModal";

const UseGuide: React.FC = () => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);

  return (
    <>
      <Button variant="outline" onClick={() => setOpen(true)}>
        <BookOpen />
        {t("use-guide.button")}
      </Button>
      <UseGuideModal isOpen={open} onClose={() => setOpen(false)} />
    </>
  );
};

export default UseGuide;
