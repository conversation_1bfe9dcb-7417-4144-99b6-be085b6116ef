import { useState, ChangeEvent } from "react";
import { useTranslation } from "react-i18next";
import System from "@/models/system";

interface Settings {
  [key: `AnthropicApiKey${string}`]: string | undefined;
  [key: `AnthropicModelPref${string}`]: string | undefined;
  [key: `credentialsOnly${string}`]: boolean | undefined;
  AnthropicApiKey?: string;
  [key: string]: string | boolean | undefined;
}

interface AnthropicAiOptionsProps {
  settings?: Settings;
  moduleSuffix?: string;
  onError?: (error: string) => void;
  onChange?: () => void;
}

const ANTHROPIC_MODELS = [
  // Claude 4 Models (Latest Generation)
  "claude-opus-4-20250514",
  "claude-sonnet-4-20250514",
  "claude-opus-4-0",
  "claude-sonnet-4-0",
  // Claude 3.x Models
  "claude-3-haiku-20240307",
  "claude-3-opus-latest",
  "claude-3-sonnet-20240229",
  "claude-3-5-haiku-latest",
  "claude-3-5-sonnet-20240620",
  "claude-3-5-sonnet-20241022",
  "claude-3-7-sonnet-20250219",
  "claude-3-7-sonnet-latest",
] as const;

export default function AnthropicAiOptions({
  settings,
  moduleSuffix = "",
  onError,
  onChange = () => {},
}: AnthropicAiOptionsProps): JSX.Element {
  const { t } = useTranslation();
  const apiKeyFieldName = `AnthropicApiKey${moduleSuffix}`;
  const baseApiKeyFieldName = "AnthropicApiKey";
  const [apiKeyError, setApiKeyError] = useState<string>("");
  const [inputValue, setInputValue] = useState<string>("");
  const [anthropicApiKey, setAnthropicApiKey] = useState<string>(
    settings
      ? String(settings[apiKeyFieldName] || settings[baseApiKeyFieldName] || "")
      : ""
  );
  const [selectedModel, setSelectedModel] = useState<string>(
    settings?.[`AnthropicModelPref${moduleSuffix}`] ||
      "claude-sonnet-4-20250514"
  );

  const validateApiKey = (value: string): boolean => {
    if (!value.startsWith("sk-ant-")) {
      const error = t("errors.env.anthropic-key-format");
      setApiKeyError(error);
      if (onError) onError(error);
      return false;
    }
    setApiKeyError("");
    if (onError) onError("");
    return true;
  };

  const handleApiKeyChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value;
    setInputValue(value);

    // Notify parent of changes to trigger save button
    onChange();

    if (value && !validateApiKey(value)) {
      e.preventDefault();
      return;
    }
    setApiKeyError("");
    if (onError) onError("");
  };

  const handleApiKeyBlur = (): void => {
    // If inputValue is empty and we have an existing API key (user-set or base), don't update
    if (inputValue === "" && anthropicApiKey) {
      return;
    }

    setAnthropicApiKey(inputValue);

    // Only update if the value changed and is valid
    if (
      inputValue !== settings?.[apiKeyFieldName] &&
      (inputValue === "" || validateApiKey(inputValue))
    ) {
      // Update system settings for the API key
      System.updateSystem({
        [apiKeyFieldName]: inputValue,
      });
    }
  };

  const handleModelChange = (e: ChangeEvent<HTMLSelectElement>): void => {
    const newModel = e.target.value;
    setSelectedModel(newModel);

    // Update the system settings with the specific moduleSuffix
    System.updateSystem({
      [`AnthropicModelPref${moduleSuffix}`]: newModel,
    });
  };

  return (
    <div className="w-full flex flex-col">
      <div className="w-full flex items-center gap-4">
        <div className="flex flex-col w-80">
          <label className="text-foreground text-sm font-semibold block mb-4">
            {t("anthropic.api-key")}
          </label>
          <input
            type="password"
            name={apiKeyFieldName}
            placeholder={t("anthropic.api-key-placeholder")}
            defaultValue={anthropicApiKey ? "*".repeat(20) : ""}
            required={true}
            autoComplete="off"
            spellCheck={false}
            onChange={handleApiKeyChange}
            onFocus={() => {
              // Clear input when focusing on a masked field
              if (anthropicApiKey && inputValue === "") {
                setInputValue("");
              }
            }}
            onBlur={handleApiKeyBlur}
            className={`dark-input-mdl text-foreground text-sm rounded-lg block w-full p-2.5 ${
              apiKeyError ? "border-red-500" : ""
            }`}
          />
          {apiKeyError && (
            <div className="text-red-500 text-sm mt-1">{apiKeyError}</div>
          )}
        </div>

        {!settings?.[`credentialsOnly${moduleSuffix}`] && (
          <div className="flex flex-col w-60">
            <label
              className="text-foreground text-sm font-semibold block mb-4"
              htmlFor={`AnthropicModelPref${moduleSuffix}`}
            >
              {t("anthropic.model-selection")}
            </label>
            <select
              id={`AnthropicModelPref${moduleSuffix}`}
              name={`AnthropicModelPref${moduleSuffix}`}
              value={selectedModel}
              onChange={handleModelChange}
              required={true}
              className="dark-input-mdl text-foreground text-sm rounded-lg block w-full p-2.5"
              title={t("anthropic.model-selection")}
            >
              {ANTHROPIC_MODELS.map((model) => {
                return (
                  <option key={model} value={model} className="text-foreground">
                    {model}
                  </option>
                );
              })}
            </select>
          </div>
        )}
      </div>
    </div>
  );
}
