import React, { useState, useEffect } from "react";
import System from "@/models/system";
import { useTranslation } from "react-i18next";

interface GroqSettings {
  [key: `GroqApiKey${string}`]: string | boolean | undefined;
  [key: `GroqModelPref${string}`]: string | undefined;
  [key: `credentialsOnly${string}`]: boolean | undefined;
  GroqApiKey?: string | boolean;
  GroqModelPref?: string;
  credentialsOnly?: boolean;
  [key: string]: string | boolean | undefined;
}

interface Props {
  settings?: GroqSettings;
  moduleSuffix?: string;
}

interface GroqAIModelSelectionProps {
  apiKey: string | boolean | undefined;
  settings?: GroqSettings;
  moduleSuffix?: string;
}

interface CustomModel {
  id: string;
}

export default function GroqAiOptions({
  settings,
  moduleSuffix = "",
}: Props): React.ReactElement {
  const { t } = useTranslation();
  const apiKeyFieldName = `GroqApiKey${moduleSuffix}`;
  const baseApiKeyFieldName = "GroqApiKey";
  const [inputValue, setInputValue] = useState<string>("");
  const [apiKey, setApiKey] = useState<string>(
    settings
      ? String(settings[apiKeyFieldName] || settings[baseApiKeyFieldName] || "")
      : ""
  );

  return (
    <div className="flex gap-x-4">
      <div className="flex flex-col w-60">
        <label className="text-foreground text-sm font-semibold block mb-4">
          {t("groq.api-key")}
        </label>
        <input
          type="password"
          name={apiKeyFieldName}
          className="dark-input-mdl w-full text-foreground  text-sm rounded-lg block w-full p-2.5"
          placeholder={t("groq.api-key-placeholder")}
          defaultValue={apiKey ? "*".repeat(20) : ""}
          required={true}
          autoComplete="off"
          spellCheck={false}
          onChange={(e) => setInputValue(e.target.value)}
          onFocus={() => {
            // Clear input when focusing on a masked field
            if (apiKey && inputValue === "") {
              setInputValue("");
            }
          }}
          onBlur={() => {
            // If inputValue is empty and we have an existing API key (user-set or base), don't update
            if (inputValue === "" && apiKey) {
              return;
            }
            setApiKey(inputValue);
          }}
        />
      </div>

      {!settings?.[`credentialsOnly${moduleSuffix}`] && (
        <GroqAIModelSelection
          settings={settings}
          apiKey={apiKey}
          moduleSuffix={moduleSuffix}
        />
      )}
    </div>
  );
}

function GroqAIModelSelection({
  apiKey,
  settings,
  moduleSuffix = "",
}: GroqAIModelSelectionProps): React.ReactElement {
  const { t } = useTranslation();
  const [customModels, setCustomModels] = useState<CustomModel[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    async function findCustomModels() {
      if (!apiKey) {
        setCustomModels([]);
        setLoading(true);
        return;
      }

      try {
        setLoading(true);
        const { models } = await System.customModels(
          "groq",
          typeof apiKey === "string" ? apiKey : null
        );
        setCustomModels((models as CustomModel[]) || []);
      } catch {
        // Failed to fetch custom models
        setCustomModels([]);
      } finally {
        setLoading(false);
      }
    }
    findCustomModels();
  }, [apiKey]);

  if (loading) {
    return (
      <div className="flex flex-col w-60">
        <label className="text-foreground text-sm font-semibold block mb-3">
          {t("groq.model-selection")}
        </label>
        <select
          name={`GroqModelPref${moduleSuffix}`}
          disabled={true}
          value=""
          className="dark-input-mdl text-foreground text-sm rounded-lg block w-full p-2.5"
          title="Groq Model Selector"
        >
          <option disabled={true} value="" className="text-foreground">
            {t("groq.loading-models")}
          </option>
        </select>
        <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60 mt-2">
          {t("groq.enter-api-key")}
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-60">
      <label className="text-foreground text-sm font-semibold block mb-3">
        {t("groq.model-selection")}
      </label>
      <select
        name={`GroqModelPref${moduleSuffix}`}
        required={true}
        value={
          typeof settings?.[`GroqModelPref${moduleSuffix}`] === "string"
            ? settings?.[`GroqModelPref${moduleSuffix}`]
            : ""
        }
        className="dark-input-mdl text-foreground text-sm rounded-lg block w-full p-2.5"
        title="Groq Region Selector"
      >
        {customModels.length > 0 && (
          <optgroup label={t("groq.available-models")}>
            {customModels.map((model: CustomModel) => {
              return (
                <option
                  className="text-foreground"
                  key={model.id}
                  value={model.id}
                >
                  {model.id}
                </option>
              );
            })}
          </optgroup>
        )}
      </select>
      <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60 mt-2">
        {t("groq.model-description")}
      </p>
    </div>
  );
}
