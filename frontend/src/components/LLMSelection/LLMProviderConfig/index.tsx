import React from "react";
import { TFunction } from "i18next";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/media/llmprovider/openai.png";
import GenericOpenAiLogo from "@/media/llmprovider/generic-openai.png";
import AzureOpenAiLogo from "@/media/llmprovider/azure.png";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/media/llmprovider/anthropic.png";
import <PERSON><PERSON><PERSON> from "@/media/llmprovider/gemini.png";
import <PERSON>lla<PERSON><PERSON>ogo from "@/media/llmprovider/ollama.png";
import LMStudioLogo from "@/media/llmprovider/lmstudio.png";
import LocalAiLogo from "@/media/llmprovider/localai.png";
import TogetherA<PERSON>ogo from "@/media/llmprovider/togetherai.png";
import FireworksAILogo from "@/media/llmprovider/fireworksai.jpeg";
import MistralLogo from "@/media/llmprovider/mistral.jpeg";
import HuggingFaceLogo from "@/media/llmprovider/huggingface.png";
import Perplex<PERSON><PERSON>ogo from "@/media/llmprovider/perplexity.png";
import <PERSON><PERSON><PERSON><PERSON><PERSON>ogo from "@/media/llmprovider/openrouter.jpeg";
import <PERSON>ro<PERSON><PERSON>ogo from "@/media/llmprovider/groq.png";
import KoboldCPPLogo from "@/media/llmprovider/koboldcpp.png";
import TextGenWebUILogo from "@/media/llmprovider/text-generation-webui.png";
import CohereLogo from "@/media/llmprovider/cohere.png";
import LiteLLMLogo from "@/media/llmprovider/litellm.png";
import AWSBedrockLogo from "@/media/llmprovider/bedrock.png";
import DeepSeekLogo from "@/media/llmprovider/deepseek.png";

import OpenAiOptions from "@/components/LLMSelection/OpenAiOptions";
import GenericOpenAiOptions from "@/components/LLMSelection/GenericOpenAiOptions";
import AzureAiOptions from "@/components/LLMSelection/AzureAiOptions";
import AnthropicAiOptions from "@/components/LLMSelection/AnthropicAiOptions";
import LMStudioOptions from "@/components/LLMSelection/LMStudioOptions";
import LocalAiOptions from "@/components/LLMSelection/LocalAiOptions";
import NativeLLMOptions from "@/components/LLMSelection/NativeLLMOptions";
import GeminiLLMOptions from "@/components/LLMSelection/GeminiLLMOptions";
import OllamaLLMOptions from "@/components/LLMSelection/OllamaLLMOptions";
import TogetherAiOptions from "@/components/LLMSelection/TogetherAiOptions";
import FireworksAiOptions from "@/components/LLMSelection/FireworksAiOptions";
import MistralOptions from "@/components/LLMSelection/MistralOptions";
import HuggingFaceOptions from "@/components/LLMSelection/HuggingFaceOptions";
import PerplexityOptions from "@/components/LLMSelection/PerplexityOptions";
import OpenRouterOptions from "@/components/LLMSelection/OpenRouterOptions";
import GroqAiOptions from "@/components/LLMSelection/GroqAiOptions";
import CohereAiOptions from "@/components/LLMSelection/CohereAiOptions";
import KoboldCPPOptions from "@/components/LLMSelection/KoboldCPPOptions";
import TextGenWebUIOptions from "@/components/LLMSelection/TextGenWebUIOptions";
import LiteLLMOptions from "@/components/LLMSelection/LiteLLMOptions";
import AWSBedrockLLMOptions from "@/components/LLMSelection/AwsBedrockLLMOptions";
import DeepSeekOptions from "@/components/LLMSelection/DeepSeekOptions";

export interface LLMProviderSettings {
  [key: string]: string | boolean | number | undefined;
}

export interface LLMProvider {
  name: string;
  value: string;
  logo?: string;
  options: (
    settings: LLMProviderSettings,
    suffix: string,
    onError?: (error: string) => void,
    onChange?: (settings?: LLMProviderSettings) => void
  ) => React.ReactElement;
  description: string;
  requiredConfig: string[];
}

export const getLLMProviders = (
  t: TFunction,
  moduleSuffix: string = ""
): LLMProvider[] => [
  {
    name: "OpenAI",
    value: "openai",
    logo: OpenAiLogo,
    options: (settings, suffix, onError, onChange) => (
      <OpenAiOptions
        settings={settings as LLMProviderSettings}
        moduleSuffix={suffix || moduleSuffix}
        onError={onError}
        onChange={
          onChange as React.ComponentProps<typeof OpenAiOptions>["onChange"]
        }
      />
    ),
    description: t("llm-provider.openai"),
    requiredConfig: ["OpenAiKey"],
  },
  {
    name: "Azure OpenAI",
    value: "azure",
    logo: AzureOpenAiLogo,
    options: (settings, suffix) => (
      <AzureAiOptions
        settings={
          settings as React.ComponentProps<typeof AzureAiOptions>["settings"]
        }
        moduleSuffix={suffix || moduleSuffix}
      />
    ),
    description: t("llm-provider.azure"),
    requiredConfig: ["AzureOpenAiEndpoint"],
  },
  {
    name: "Anthropic",
    value: "anthropic",
    logo: AnthropicLogo,
    options: (settings, suffix, onError, onChange) => (
      <AnthropicAiOptions
        settings={
          settings as React.ComponentProps<
            typeof AnthropicAiOptions
          >["settings"]
        }
        moduleSuffix={suffix || moduleSuffix}
        onError={onError}
        onChange={
          onChange as React.ComponentProps<
            typeof AnthropicAiOptions
          >["onChange"]
        }
      />
    ),
    description: t("llm-provider.anthropic"),
    requiredConfig: ["AnthropicApiKey"],
  },
  {
    name: "Gemini",
    value: "gemini",
    logo: GeminiLogo,
    options: (settings, suffix, onError, onChange) => (
      <GeminiLLMOptions
        settings={
          settings as React.ComponentProps<typeof GeminiLLMOptions>["settings"]
        }
        moduleSuffix={suffix || moduleSuffix}
        onError={onError}
        onChange={
          onChange as React.ComponentProps<typeof GeminiLLMOptions>["onChange"]
        }
      />
    ),
    description: t("llm-provider.gemini"),
    requiredConfig: ["GeminiLLMApiKey"],
  },
  {
    name: "Hugging Face",
    value: "huggingface",
    logo: HuggingFaceLogo,
    options: (settings, suffix) => (
      <HuggingFaceOptions
        settings={
          settings as React.ComponentProps<
            typeof HuggingFaceOptions
          >["settings"]
        }
        moduleSuffix={suffix || moduleSuffix}
      />
    ),
    description: t("llm-provider.huggingface"),
    requiredConfig: ["HuggingFaceApiKey"],
  },
  {
    name: "Ollama",
    value: "ollama",
    logo: OllamaLogo,
    options: (settings, suffix, onError, onChange) => (
      <OllamaLLMOptions
        settings={
          settings as React.ComponentProps<typeof OllamaLLMOptions>["settings"]
        }
        moduleSuffix={suffix || moduleSuffix}
        onError={onError}
        onChange={
          onChange as React.ComponentProps<typeof OllamaLLMOptions>["onChange"]
        }
      />
    ),
    description: t("llm-provider.ollama"),
    requiredConfig: ["OllamaApiHost"],
  },
  {
    name: "LM Studio",
    value: "lmstudio",
    logo: LMStudioLogo,
    options: (settings, suffix) => (
      <LMStudioOptions
        settings={
          settings as React.ComponentProps<typeof LMStudioOptions>["settings"]
        }
        moduleSuffix={suffix || moduleSuffix}
      />
    ),
    description: t("llm-provider.lmstudio"),
    requiredConfig: ["LMStudioBasePath"],
  },
  {
    name: "Local AI",
    value: "localai",
    logo: LocalAiLogo,
    options: (settings, suffix) => (
      <LocalAiOptions
        settings={
          settings as React.ComponentProps<typeof LocalAiOptions>["settings"]
        }
        moduleSuffix={suffix || moduleSuffix}
      />
    ),
    description: t("llm-provider.localai"),
    requiredConfig: ["LocalAiURL"],
  },
  {
    name: "Together AI",
    value: "togetherai",
    logo: TogetherAILogo,
    options: (settings, suffix) => (
      <TogetherAiOptions
        settings={
          settings as React.ComponentProps<typeof TogetherAiOptions>["settings"]
        }
        moduleSuffix={suffix || moduleSuffix}
      />
    ),
    description: t("llm-provider.together-ai"),
    requiredConfig: ["TogetherAiApiKey"],
  },
  {
    name: "Fireworks AI",
    value: "fireworksai",
    logo: FireworksAILogo,
    options: (settings) => (
      <FireworksAiOptions
        settings={
          settings as React.ComponentProps<
            typeof FireworksAiOptions
          >["settings"]
        }
      />
    ),
    description: t("llm-provider.fireworks-ai"),
    requiredConfig: ["FireworksAiApiKey"],
  },
  {
    name: "Mistral",
    value: "mistral",
    logo: MistralLogo,
    options: (settings, suffix) => (
      <MistralOptions
        settings={
          settings as React.ComponentProps<typeof MistralOptions>["settings"]
        }
        moduleSuffix={suffix || moduleSuffix}
      />
    ),
    description: t("llm-provider.mistral"),
    requiredConfig: ["MistralApiKey"],
  },
  {
    name: "Perplexity",
    value: "perplexity",
    logo: PerplexityLogo,
    options: (settings, suffix) => (
      <PerplexityOptions
        settings={
          settings as React.ComponentProps<typeof PerplexityOptions>["settings"]
        }
        moduleSuffix={suffix || moduleSuffix}
      />
    ),
    description: t("llm-provider.perplexity"),
    requiredConfig: ["PerplexityApiKey"],
  },
  {
    name: "Open Router",
    value: "openrouter",
    logo: OpenRouterLogo,
    options: (settings, suffix) => (
      <OpenRouterOptions
        settings={
          settings as React.ComponentProps<typeof OpenRouterOptions>["settings"]
        }
        moduleSuffix={suffix || moduleSuffix}
      />
    ),
    description: t("llm-provider.openrouter"),
    requiredConfig: ["OpenRouterApiKey"],
  },
  {
    name: "Groq",
    value: "groq",
    logo: GroqLogo,
    options: (settings, suffix) => (
      <GroqAiOptions
        settings={
          settings as React.ComponentProps<typeof GroqAiOptions>["settings"]
        }
        moduleSuffix={suffix || moduleSuffix}
      />
    ),
    description: t("llm-provider.groq"),
    requiredConfig: ["GroqApiKey"],
  },
  {
    name: "Cohere",
    value: "cohere",
    logo: CohereLogo,
    options: (settings, suffix) => (
      <CohereAiOptions
        settings={
          settings as React.ComponentProps<typeof CohereAiOptions>["settings"]
        }
        moduleSuffix={suffix || moduleSuffix}
      />
    ),
    description: t("llm-provider.cohere"),
    requiredConfig: ["CohereApiKey"],
  },
  {
    name: "KoboldCPP",
    value: "koboldcpp",
    logo: KoboldCPPLogo,
    options: (settings, suffix) => (
      <KoboldCPPOptions
        settings={
          settings as React.ComponentProps<typeof KoboldCPPOptions>["settings"]
        }
        moduleSuffix={suffix || moduleSuffix}
      />
    ),
    description: t("llm-provider.koboldcpp"),
    requiredConfig: ["KoboldCPPUrl"],
  },
  {
    name: "text-generation-webui",
    value: "textgenwebui",
    logo: TextGenWebUILogo,
    options: (settings, suffix) => (
      <TextGenWebUIOptions
        settings={
          settings as React.ComponentProps<
            typeof TextGenWebUIOptions
          >["settings"]
        }
        moduleSuffix={suffix || moduleSuffix}
      />
    ),
    description: t("llm-provider.textgenwebui"),
    requiredConfig: ["TextgenUrl", "TextgenApiKey"],
  },
  {
    name: "LiteLLM",
    value: "litellm",
    logo: LiteLLMLogo,
    options: (settings, suffix) => (
      <LiteLLMOptions
        settings={
          settings as React.ComponentProps<typeof LiteLLMOptions>["settings"]
        }
        moduleSuffix={suffix || moduleSuffix}
      />
    ),
    description: t("llm-provider.litellm"),
    requiredConfig: ["LiteLLMApiKey", "LiteLLMBaseURL"],
  },
  {
    name: "AWS Bedrock",
    value: "bedrock",
    logo: AWSBedrockLogo,
    options: (settings, suffix) => (
      <AWSBedrockLLMOptions
        settings={
          settings as React.ComponentProps<
            typeof AWSBedrockLLMOptions
          >["settings"]
        }
        moduleSuffix={suffix || moduleSuffix}
      />
    ),
    description: t("llm-provider.bedrock"),
    requiredConfig: [
      "BedrockAwsRegion",
      "BedrockAwsAccessKey",
      "BedrockAwsSecretKey",
    ],
  },
  {
    name: "DeepSeek",
    value: "deepseek",
    logo: DeepSeekLogo,
    options: (settings) => (
      <DeepSeekOptions
        settings={
          settings as React.ComponentProps<typeof DeepSeekOptions>["settings"]
        }
      />
    ),
    description: t("llm-provider.deepseek"),
    requiredConfig: ["DeepSeekApiKey"],
  },
  {
    name: "Generic OpenAI API",
    value: "generic-openai",
    logo: GenericOpenAiLogo,
    options: (settings, suffix) => (
      <GenericOpenAiOptions
        settings={
          settings as React.ComponentProps<
            typeof GenericOpenAiOptions
          >["settings"]
        }
        moduleSuffix={suffix || moduleSuffix}
      />
    ),
    description: t("llm-provider.openai-generic"),
    requiredConfig: ["GenericOpenAiKey", "GenericOpenAiBaseUrl"],
  },
  {
    name: "Native",
    value: "native",
    options: (settings, suffix) => (
      <NativeLLMOptions
        settings={
          settings as React.ComponentProps<typeof NativeLLMOptions>["settings"]
        }
        moduleSuffix={suffix || moduleSuffix}
      />
    ),
    description: t("llm-provider.system-default"),
    requiredConfig: [],
  },
];

export const getModelPrefKey = (
  provider: string,
  suffix: string = ""
): string => {
  const providerLower = provider?.toLowerCase();
  switch (providerLower) {
    case "gemini":
      return `GeminiLLMModelPref${suffix}`;
    case "ollama":
      return `OllamaLLMModelPref${suffix}`;
    case "native":
      return `NativeLLMModelPref${suffix}`;
    case "litellm":
      return `LiteLLMModelPref${suffix}`;
    case "openai":
      return `OpenAiModelPref${suffix}`;
    case "azureopenai":
      return `AzureOpenAiModelPref${suffix}`;
    case "genericopenai":
      return `GenericOpenAiModelPref${suffix}`;
    case "lmstudio":
      return `LMStudioModelPref${suffix}`;
    case "togetherai":
      return `TogetherAiModelPref${suffix}`;
    case "openrouter":
      return `OpenRouterModelPref${suffix}`;
    case "anthropic":
      return `AnthropicModelPref${suffix}`;
    case "mistral":
      return `MistralModelPref${suffix}`;
    case "groq":
      return `GroqModelPref${suffix}`;
    case "perplexity":
      return `PerplexityModelPref${suffix}`;
    case "deepseek":
      return `DeepseekModelPref${suffix}`;
    case "xai":
      return `XAiModelPref${suffix}`;
    default: {
      const providerPrefix =
        provider?.charAt(0).toUpperCase() + provider?.slice(1);
      return `${providerPrefix}ModelPref${suffix}`;
    }
  }
};
