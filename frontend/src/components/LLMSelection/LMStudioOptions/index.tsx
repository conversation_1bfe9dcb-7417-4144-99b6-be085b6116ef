import {
  useEffect,
  useState,
  ChangeEvent,
  WheelEvent,
  MouseEvent,
} from "react";
import { Info, CaretDown, CaretUp } from "@phosphor-icons/react";
import paths from "@/utils/paths";
import System from "@/models/system";
import PreLoader from "@/components/Preloader";
import { LMSTUDIO_COMMON_URLS } from "@/utils/constants";
import useProviderEndpointAutoDiscovery from "@/hooks/useProviderEndpointAutoDiscovery";
import { useTranslation } from "react-i18next";
import { SystemSettings } from "@/types";

interface LMStudioOptionsProps {
  /** System settings object */
  settings?: SystemSettings;
  /** Whether to show embedding requirement alert */
  showAlert?: boolean;
  /** Module suffix for configuration keys */
  moduleSuffix?: string;
}

interface ModelInfo {
  id: string;
  name?: string;
}

export default function LMStudioOptions({
  settings,
  showAlert = false,
  moduleSuffix = "",
}: LMStudioOptionsProps): JSX.Element {
  const { t } = useTranslation();
  const {
    autoDetecting: loading,
    basePath,
    basePathValue,
    showAdvancedControls,
    setShowAdvancedControls,
    handleAutoDetectClick,
  } = useProviderEndpointAutoDiscovery({
    provider: "lmstudio",
    initialBasePath: settings?.[`LMStudioBasePath${moduleSuffix}`] as string,
    ENDPOINTS: [...LMSTUDIO_COMMON_URLS],
    moduleSuffix: moduleSuffix,
  });

  const [maxTokens, setMaxTokens] = useState<number>(
    (settings?.[`LMStudioTokenLimit${moduleSuffix}`] as number) || 4096
  );

  const handleMaxTokensChange = (e: ChangeEvent<HTMLInputElement>): void => {
    setMaxTokens(Number(e.target.value));
  };

  const handleNumberInputScroll = (e: WheelEvent<HTMLInputElement>): void => {
    e.currentTarget.blur();
  };

  const handleAdvancedToggle = (e: MouseEvent<HTMLButtonElement>): void => {
    e.preventDefault();
    setShowAdvancedControls(!showAdvancedControls);
  };

  return (
    <div className="w-full flex flex-col">
      {showAlert && (
        <div className="flex flex-col md:flex-row md:items-center gap-x-2 text-foreground primary-bg mb-6 w-fit rounded-lg px-4 py-2">
          <div className="gap-x-2 flex items-center">
            <Info size={12} className="hidden md:visible" />
            <p className="text-sm md:text-base">
              {t("lmstudio.embedding-required")}
            </p>
          </div>
          <a
            href={paths.settings.embedder.modelPreference()}
            className="text-sm md:text-base my-2 underline"
          >
            {t("lmstudio.manage-embedding")} &rarr;
          </a>
        </div>
      )}
      <div className="w-full flex items-start gap-4">
        <LMStudioModelSelection
          settings={settings}
          basePath={basePath.value}
          moduleSuffix={moduleSuffix}
        />
        <div className="flex flex-col w-full">
          <label className="text-foreground text-sm font-semibold block mb-2">
            {t("lmstudio.max-tokens")}
          </label>
          <input
            className=" text-foreground dark-input-mdl  text-sm rounded-md block w-full p-2 focus:outline-none"
            type="number"
            name={`LMStudioTokenLimit${moduleSuffix}`}
            placeholder="4096"
            min={1}
            value={maxTokens}
            onChange={handleMaxTokensChange}
            onWheel={handleNumberInputScroll}
            required={true}
            autoComplete="off"
          />
          <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60 mt-2">
            {t("lmstudio.max-tokens-desc")}
          </p>
        </div>
      </div>
      <div className="flex justify-start mt-4">
        <button
          onClick={handleAdvancedToggle}
          className="text-foreground  flex items-center text-sm"
        >
          {showAdvancedControls
            ? t("lmstudio.hide-advanced")
            : t("lmstudio.show-advanced")}
          {showAdvancedControls ? (
            <CaretUp size={14} className="ml-1" />
          ) : (
            <CaretDown size={14} className="ml-1" />
          )}
        </button>
      </div>

      <div hidden={!showAdvancedControls}>
        <div className="w-full flex items-start gap-4 mt-4">
          <div className="flex flex-col w-60">
            <div className="flex justify-between items-center mb-2">
              <label className="text-foreground text-sm font-semibold">
                {t("lmstudio.base-url")}
              </label>
              {loading ? (
                <PreLoader size="6" />
              ) : (
                <>
                  {!basePathValue.value && (
                    <button
                      onClick={handleAutoDetectClick}
                      className="bg-primary-button text-xs font-medium px-2 py-1 rounded-md text-white"
                    >
                      {t("lmstudio.auto-detect")}
                    </button>
                  )}
                </>
              )}
            </div>
            <input
              className="dark-input-mdl text-foreground text-sm rounded-lg block w-full p-2"
              type="url"
              name={`LMStudioBasePath${moduleSuffix}`}
              placeholder={t("lmstudio.base-url-placeholder")}
              value={basePathValue.value}
              required={true}
              autoComplete="off"
              spellCheck={false}
              onChange={basePath.onChange}
              onBlur={basePath.onBlur}
            />
            <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60 mt-2">
              {t("lmstudio.base-url-desc")}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

interface LMStudioModelSelectionProps {
  settings?: SystemSettings;
  basePath?: string | null;
  moduleSuffix?: string;
}

function LMStudioModelSelection({
  settings,
  basePath = null,
  moduleSuffix = "",
}: LMStudioModelSelectionProps): JSX.Element {
  const { t } = useTranslation();
  const [customModels, setCustomModels] = useState<ModelInfo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    async function findCustomModels(): Promise<void> {
      if (!basePath) {
        setCustomModels([]);
        setLoading(false);
        return;
      }
      setLoading(true);
      try {
        const { models } = await System.customModels(
          "lmstudio",
          null,
          basePath
        );
        setCustomModels((models as ModelInfo[]) || []);
      } catch {
        // Failed to fetch models
        setCustomModels([]);
      }
      setLoading(false);
    }
    findCustomModels();
  }, [basePath, t]);

  if (loading || customModels.length === 0) {
    return (
      <div className="flex flex-col w-80">
        <label className="text-foreground text-sm font-semibold block mb-2">
          {t("lmstudio.model")}
        </label>
        <select
          name={`LMStudioModelPref${moduleSuffix}`}
          disabled={true}
          value="loading"
          className="dark-input-mdl text-foreground  focus:outline-none text-sm rounded-md block w-full p-2"
          title="LM Studio Model Selector"
        >
          <option value="loading" disabled={true}>
            {basePath ? t("loading.models") : t("model.enter-url")}
          </option>
        </select>
        <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60 mt-2">
          {t("lmstudio.model-desc")}
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-60">
      <label className="text-foreground text-sm font-semibold block mb-2">
        {t("lmstudio.model")}
      </label>
      <select
        name={`LMStudioModelPref${moduleSuffix}`}
        required={true}
        defaultValue={
          (settings?.[`LMStudioModelPref${moduleSuffix}`] as string) || ""
        }
        className="dark-input-mdl text-foreground  focus:outline-none text-sm rounded-lg block w-full p-2"
        title="LM Studio Model Selector"
      >
        {customModels.length > 0 && (
          <optgroup label={t("model.your-models")} className="text-black">
            {customModels.map((model) => (
              <option key={model.id} value={model.id}>
                {model.id}
              </option>
            ))}
          </optgroup>
        )}
      </select>
      <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60 mt-2">
        {t("lmstudio.model-choose")}
      </p>
    </div>
  );
}
