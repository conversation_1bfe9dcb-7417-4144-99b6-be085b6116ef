import { useEffect, useState } from "react";
import { Info, CaretDown, CaretUp } from "@phosphor-icons/react";
import paths from "@/utils/paths";
import System from "@/models/system";
import PreLoader from "@/components/Preloader";
import { LOCALAI_COMMON_URLS } from "@/utils/constants";
import useProviderEndpointAutoDiscovery from "@/hooks/useProviderEndpointAutoDiscovery";
import { useTranslation } from "react-i18next";
import { SystemSettings } from "@/types";

interface LocalAiOptionsProps {
  settings: SystemSettings;
  showAlert?: boolean;
  moduleSuffix?: string;
}

interface LocalAIModelSelectionProps {
  settings: SystemSettings;
  basePath: string | null;
  apiKey: string | null;
  moduleSuffix?: string;
}

interface CustomModel {
  id: string;
  name?: string;
}

export default function LocalAiOptions({
  settings,
  showAlert = false,
  moduleSuffix = "",
}: LocalAiOptionsProps) {
  const { t } = useTranslation();
  const {
    autoDetecting: loading,
    basePath,
    basePathValue,
    showAdvancedControls,
    setShowAdvancedControls,
    handleAutoDetectClick,
  } = useProviderEndpointAutoDiscovery({
    provider: "localai",
    initialBasePath: settings?.LocalAiBasePath as string,
    ENDPOINTS: [...LOCALAI_COMMON_URLS],
  });
  const apiKeyFieldName = `LocalAiApiKey${moduleSuffix}`;
  const baseApiKeyFieldName = "LocalAiApiKey";
  const [apiKeyValue, setApiKeyValue] = useState("");
  const [apiKey, setApiKey] = useState<string>(
    settings
      ? String(settings[apiKeyFieldName] || settings[baseApiKeyFieldName] || "")
      : ""
  );

  return (
    <div className="w-full flex flex-col gap-y-4">
      {showAlert && (
        <div className="flex flex-col md:flex-row md:items-center gap-x-2 text-foreground mb-6 bg-blue-800/30 w-fit rounded-lg px-4 py-2">
          <div className="gap-x-2 flex items-center">
            <Info size={12} className="hidden md:visible" />
            <p className="text-sm md:text-base">
              {t("localai.embedding-required")}
            </p>
          </div>
          <a
            href={paths.settings.embedder.modelPreference()}
            className="text-sm md:text-base my-2 underline"
          >
            {t("localai.manage-embedding")} &rarr;
          </a>
        </div>
      )}
      <div className="w-full flex items-center gap-4">
        {!settings?.[`credentialsOnly${moduleSuffix}`] && (
          <>
            <LocalAIModelSelection
              settings={settings}
              basePath={basePath.value}
              apiKey={apiKey}
              moduleSuffix={moduleSuffix}
            />
            <div className="flex flex-col w-60">
              <label className="text-foreground text-sm font-semibold block mb-2">
                {t("localai.token-window")}
              </label>
              <input
                type="number"
                name={`LocalAiTokenLimit${moduleSuffix}`}
                className=" dark-input-mdl text-foreground text-sm rounded-lg block w-full p-2"
                placeholder={t("localai.token-window-placeholder")}
                min={1}
                onScroll={(e) => e.currentTarget.blur()}
                defaultValue={
                  (settings?.[`LocalAiTokenLimit${moduleSuffix}`] as string) ||
                  ""
                }
                required={true}
                autoComplete="off"
              />
            </div>
          </>
        )}
        <div className="flex flex-col w-60">
          <div className="flex flex-col gap-y-1 mb-2">
            <label className="text-foreground text-sm font-semibold flex items-center gap-x-2">
              {t("localai.api-key")}{" "}
              <p className="!text-xs !italic !font-thin">
                {t("localai.api-key-optional")}
              </p>
            </label>
          </div>
          <input
            type="password"
            name={apiKeyFieldName}
            className=" dark-input-mdl text-foreground text-sm rounded-lg block w-full p-2"
            placeholder={t("localai.api-key-placeholder")}
            defaultValue={apiKey ? "*".repeat(20) : ""}
            autoComplete="off"
            spellCheck={false}
            onChange={(e) => setApiKeyValue(e.target.value)}
            onFocus={() => {
              // Clear input when focusing on a masked field
              if (apiKey && apiKeyValue === "") {
                setApiKeyValue("");
              }
            }}
            onBlur={() => {
              // If apiKeyValue is empty and we have an existing API key (user-set or base), don't update
              if (apiKeyValue === "" && apiKey) {
                return;
              }
              setApiKey(apiKeyValue);
            }}
          />
        </div>
      </div>
      <div className="flex justify-start mt-4">
        <button
          onClick={(e) => {
            e.preventDefault();
            setShowAdvancedControls(!showAdvancedControls);
          }}
          className="text-white hover:text-white/70 flex items-center text-sm"
        >
          {showAdvancedControls
            ? t("localai.hide-advanced")
            : t("localai.show-advanced")}
          {showAdvancedControls ? (
            <CaretUp size={14} className="ml-1" />
          ) : (
            <CaretDown size={14} className="ml-1" />
          )}
        </button>
      </div>
      <div hidden={!showAdvancedControls}>
        <div className="w-full flex items-center gap-4">
          <div className="flex flex-col w-60">
            <div className="flex justify-between items-center mb-2">
              <label className="text-white text-sm font-semibold">
                {t("localai.base-url")}
              </label>
              {loading ? (
                <PreLoader size="6" />
              ) : (
                <>
                  {!basePathValue.value && (
                    <button
                      onClick={handleAutoDetectClick}
                      className="bg-primary-button text-xs font-medium px-2 py-1 rounded-lg hover:bg-secondary hover:text-white shadow-[0_4px_14px_rgba(0,0,0,0.25)]"
                    >
                      {t("localai.auto-detect")}
                    </button>
                  )}
                </>
              )}
            </div>
            <input
              type="url"
              name="LocalAiBasePath"
              className="bg-zinc-900 text-white placeholder:text-white/20 text-sm rounded-lg focus:outline-primary-button active:outline-primary-button outline-none block w-full p-2"
              placeholder={t("localai.base-url-placeholder")}
              value={basePathValue.value}
              required={true}
              autoComplete="off"
              spellCheck={false}
              onChange={basePath.onChange}
              onBlur={basePath.onBlur}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

function LocalAIModelSelection({
  settings,
  basePath = null,
  apiKey = null,
  moduleSuffix = "",
}: LocalAIModelSelectionProps) {
  const { t } = useTranslation();
  const [customModels, setCustomModels] = useState<CustomModel[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function findCustomModels() {
      if (!basePath || !basePath.includes("/v1")) {
        setCustomModels([]);
        setLoading(false);
        return;
      }
      setLoading(true);
      const { models } = await System.customModels(
        "localai",
        typeof apiKey === "boolean" ? null : apiKey,
        basePath
      );
      setCustomModels((models as CustomModel[]) || []);
      setLoading(false);
    }
    findCustomModels();
  }, [basePath, apiKey]);

  if (loading || customModels.length == 0) {
    return (
      <div className="flex flex-col w-60">
        <label
          className="text-foreground text-sm font-semibold block mb-2"
          htmlFor={`LocalAiModelPref${moduleSuffix}`}
        >
          {t("localai.model-selection")}
        </label>
        <select
          id={`LocalAiModelPref${moduleSuffix}`}
          name={`LocalAiModelPref${moduleSuffix}`}
          disabled={true}
          value="loading"
          className="dark-input-mdl text-foreground text-sm rounded-lg block w-full p-2"
          title={t("localai.model-selection")}
        >
          <option value="loading" disabled={true} className="text-foreground">
            {basePath?.includes("/v1")
              ? t("localai.loading-models")
              : t("localai.waiting-url")}
          </option>
        </select>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-60">
      <label
        className="text-foreground text-sm font-semibold block mb-2"
        htmlFor={`LocalAiModelPref${moduleSuffix}`}
      >
        {t("localai.model-selection")}
      </label>
      <select
        id={`LocalAiModelPref${moduleSuffix}`}
        name={`LocalAiModelPref${moduleSuffix}`}
        required={true}
        value={(settings?.[`LocalAiModelPref${moduleSuffix}`] as string) || ""}
        className="dark-input-mdl text-foreground text-sm rounded-lg block w-full p-2"
        title={t("localai.model-selection")}
      >
        {customModels.length > 0 && (
          <optgroup label={t("localai.loaded-models")}>
            {customModels.map((model) => (
              <option
                className="text-foreground"
                key={model.id}
                value={model.id}
              >
                {model.id}
              </option>
            ))}
          </optgroup>
        )}
      </select>
    </div>
  );
}
