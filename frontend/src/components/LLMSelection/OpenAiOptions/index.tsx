import { useState, useEffect, useRef, ChangeEvent } from "react";
import System from "@/models/system";
import { useTranslation } from "react-i18next";

interface Settings {
  [key: string]: string | number | boolean | undefined | null;
}

interface ModelInfo {
  id: string;
  name: string;
  organization: string;
}

interface GroupedModels {
  [organization: string]: ModelInfo[];
}

interface ChangeNotification {
  autoSaved?: boolean;
  modelChanged?: boolean;
  provider?: string;
  modelKey?: string;
  modelValue?: string;
}

interface OpenAiOptionsProps {
  settings?: Settings;
  moduleSuffix?: string;
  onError?: (error: string) => void;
  onChange?: (notification?: ChangeNotification) => void;
}

interface OpenAIModelSelectionProps {
  apiKey?: string | boolean;
  moduleSuffix?: string;
  selectedModel?: string;
  onModelChange?: (
    e: ChangeEvent<HTMLSelectElement> | CustomSyntheticEvent
  ) => void;
  settings?: Settings;
}

interface CustomSyntheticEvent {
  target: { value: string };
  isAutoSelection?: boolean;
}

interface CustomModelsResponse {
  models: ModelInfo[];
}

export default function OpenAiOptions({
  settings,
  moduleSuffix = "",
  onError,
  onChange = () => {},
}: OpenAiOptionsProps): JSX.Element {
  const { t } = useTranslation();
  const apiKeyFieldName = `OpenAiKey${moduleSuffix}`;
  const baseApiKeyFieldName = "OpenAiKey";
  const [inputValue, setInputValue] = useState<string>("");
  const [openAIKey, setOpenAIKey] = useState<string>(
    settings
      ? String(settings[apiKeyFieldName] || settings[baseApiKeyFieldName] || "")
      : ""
  );
  const [selectedModel, setSelectedModel] = useState<string>(
    String(settings?.[`OpenAiModelPref${moduleSuffix}`] || "")
  );
  const [apiKeyError, setApiKeyError] = useState<string>("");
  const initializedRef = useRef<boolean>(false);

  console.log(settings);

  // Immediate context window initialization on mount - run this first, outside of any state dependencies
  useEffect(() => {
    // Get initial model value directly from settings
    const initialModel = settings?.[`OpenAiModelPref${moduleSuffix}`];

    if (initialModel && !initializedRef.current) {
      initializedRef.current = true;

      // Force immediate context window update
      setTimeout(() => {
        onChange({
          autoSaved: true,
          modelChanged: true,
          provider: "openai",
          modelKey: `OpenAiModelPref${moduleSuffix}`,
          modelValue: String(initialModel ?? ""),
        });
      }, 0);
    }
  }, [moduleSuffix, settings, onChange]); // Only depend on these props, not state

  const validateApiKey = (value: string): boolean => {
    if (!value.startsWith("sk-")) {
      const error = t("errors.env.openai-key-format");
      setApiKeyError(error);
      if (onError) onError(error);
      return false;
    }
    setApiKeyError("");
    if (onError) onError("");
    return true;
  };

  const handleApiKeyChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value;
    setInputValue(value);

    // Notify parent of changes to trigger save button
    onChange();

    if (value && !validateApiKey(value)) {
      setApiKeyError(t("openai.api-key-invalid") || "Invalid API key format");
      if (onError)
        onError(t("openai.api-key-invalid") || "Invalid API key format");
      return;
    }

    setApiKeyError("");
    if (onError) onError("");

    // Do NOT update openAIKey here (only update on blur)
  };

  const handleApiKeyBlur = (): void => {
    // If inputValue is empty and we have an existing API key (user-set or base), don't update
    if (inputValue === "" && openAIKey) {
      return;
    }

    // Only update openAIKey if the value is valid or empty
    if (inputValue === "" || validateApiKey(inputValue)) {
      setOpenAIKey(inputValue);
    }

    // Only update if the value actually changed and is valid
    if (
      inputValue !== settings?.[apiKeyFieldName] &&
      (inputValue === "" || validateApiKey(inputValue))
    ) {
      // Update system settings for the API key
      System.updateSystem({
        [apiKeyFieldName]: inputValue,
      });
    }
  };

  const handleModelChange = (
    e: ChangeEvent<HTMLSelectElement> | CustomSyntheticEvent
  ): void => {
    const newModel = e.target.value;
    setSelectedModel(newModel);

    // Update the system settings with the specific moduleSuffix
    System.updateSystem({
      [`OpenAiModelPref${moduleSuffix}`]: newModel,
    }).catch(() => {
      // Error updating model preference - continue silently
    });

    // Immediately notify parent about model change to update context window
    if (typeof onChange === "function") {
      onChange({
        autoSaved: true, // Always mark as auto-saved since we're directly updating the system
        modelChanged: true,
        provider: "openai",
        modelKey: `OpenAiModelPref${moduleSuffix}`,
        modelValue: newModel,
      });
    }
  };

  return (
    <div className="flex gap-x-4">
      <div className="flex flex-col w-80">
        <label className="text-foreground text-sm font-semibold block mb-4">
          {t("openai.api-key")}
        </label>
        <input
          type="password"
          name={apiKeyFieldName}
          placeholder={t("openai.api-key-placeholder")}
          defaultValue={openAIKey ? "*".repeat(20) : ""}
          required={true}
          autoComplete="off"
          spellCheck={false}
          onChange={handleApiKeyChange}
          onFocus={() => {
            // Clear input when focusing on a masked field
            if (openAIKey && inputValue === "") {
              setInputValue("");
            }
          }}
          onBlur={handleApiKeyBlur}
          className="dark-input-mdl text-foreground text-sm rounded-lg block w-full p-2.5"
        />
        {apiKeyError && (
          <div className="text-red-500 text-sm mt-1">{apiKeyError}</div>
        )}
      </div>
      {!settings?.[`credentialsOnly${moduleSuffix}`] && (
        <OpenAIModelSelection
          settings={settings}
          apiKey={openAIKey}
          moduleSuffix={moduleSuffix}
          selectedModel={selectedModel}
          onModelChange={handleModelChange}
        />
      )}
    </div>
  );
}

function OpenAIModelSelection({
  apiKey,
  moduleSuffix = "",
  selectedModel,
  onModelChange,
  settings,
}: OpenAIModelSelectionProps): JSX.Element {
  const { t } = useTranslation();
  const [groupedModels, setGroupedModels] = useState<GroupedModels>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [currentModel, setCurrentModel] = useState<string>(selectedModel || "");
  const modelInitializedRef = useRef<boolean>(false);

  console.log(settings);

  useEffect(() => {
    async function findCustomModels(): Promise<void> {
      setLoading(true);
      try {
        // Ensure we have a valid provider string
        const provider = "openai";
        if (!provider) {
          console.error("Provider is required for customModels");
          setGroupedModels({});
          setLoading(false);
          return;
        }

        const response = await System.customModels(
          provider,
          typeof apiKey === "boolean" ? null : apiKey
        );
        const models = (response as unknown as CustomModelsResponse).models;

        if (models?.length > 0) {
          const modelsByOrganization = models.reduce(
            (acc: GroupedModels, model: ModelInfo) => {
              acc[model.organization] = acc[model.organization] || [];
              acc[model.organization].push(model);
              return acc;
            },
            {}
          );
          setGroupedModels(modelsByOrganization);

          // If there's no current model selected but we have models available,
          // initialize with the current model from settings with the specific suffix
          if (!currentModel && settings?.[`OpenAiModelPref${moduleSuffix}`]) {
            setCurrentModel(
              String(settings[`OpenAiModelPref${moduleSuffix}`] ?? "")
            );
          } else if (
            !currentModel &&
            models.length > 0 &&
            !modelInitializedRef.current
          ) {
            // Select first model if none is selected and we haven't initialized yet
            const firstModelId = models[0].id;
            setCurrentModel(firstModelId);
            modelInitializedRef.current = true;

            // Add a delay to ensure this doesn't interfere with other initialization
            setTimeout(() => {
              if (onModelChange) {
                // Create a synthetic event with the value property set
                const syntheticEvent: CustomSyntheticEvent = {
                  target: { value: firstModelId },
                  // Tag this event as coming from auto-selection
                  isAutoSelection: true,
                };
                onModelChange(syntheticEvent);
              }
            }, 500); // Delay to ensure the component is fully mounted
          }
        } else {
          setGroupedModels({});
        }
      } catch {
        // Failed to fetch models - continue silently
        setGroupedModels({});
      }
      setLoading(false);
    }
    // Only fetch models if we have a valid API key (not empty string, not just whitespace)
    if (apiKey && typeof apiKey === "string" && apiKey.trim().length > 0) {
      findCustomModels();
    } else {
      setGroupedModels({});
      setLoading(false);
    }
  }, [apiKey, moduleSuffix, settings, currentModel, onModelChange]);

  // Update currentModel when selectedModel prop changes
  useEffect(() => {
    setCurrentModel(selectedModel || "");
  }, [selectedModel]);

  const handleChange = (e: ChangeEvent<HTMLSelectElement>): void => {
    const newValue = e.target.value;
    setCurrentModel(newValue);

    // Call parent's onModelChange, which will update settings and context window
    if (onModelChange) {
      onModelChange(e);
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col w-60">
        <label className="text-foreground text-sm font-semibold block mb-4">
          {t("openai.model-selection")}
        </label>
        <select
          name={`OpenAiModelPref${moduleSuffix}`}
          disabled={true}
          value="loading"
          className="dark-input-mdl text-foreground focus:outline-none text-sm rounded-lg block w-full p-2.5"
          title={t("openai.model-selection")}
        >
          <option value="loading" className="normal-text">
            {t("loading.models")}
          </option>
        </select>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-60">
      <label className="text-foreground text-sm font-semibold block mb-4">
        {t("openai.model-selection")}
      </label>
      <select
        name={`OpenAiModelPref${moduleSuffix}`}
        required={true}
        value={currentModel}
        onChange={handleChange}
        className="dark-input-mdl text-foreground focus:outline-none text-sm rounded-lg block w-full p-2.5"
        title={t("openai.model-selection")}
      >
        {/* Manually selectable models */}
        <optgroup label="Manual Options">
          <option value="chatgpt-4o-latest" className="text-foreground">
            chatgpt-4o-latest
          </option>
        </optgroup>

        {/* Existing custom models separator and optgroups */}
        {Object.keys(groupedModels).length > 0 && (
          <option disabled className="text-gray-500">
            ──────────
          </option>
        )}
        {Object.keys(groupedModels)
          .sort()
          .map((organization) => (
            <optgroup key={organization} label={organization}>
              {groupedModels[organization].map((model) => (
                <option
                  key={model.id}
                  value={model.id}
                  className="text-foreground"
                >
                  {model.name}
                </option>
              ))}
            </optgroup>
          ))}
      </select>
    </div>
  );
}
