import { useState, useEffect, ChangeEvent, WheelEvent } from "react";
import System from "@/models/system";
import { useTranslation } from "react-i18next";
import { SystemSettings } from "@/types";

interface XAILLMOptionsProps {
  settings?: SystemSettings;
  moduleSuffix?: string;
  onError?: (error: string) => void;
  onChange?: () => void;
}

interface ModelInfo {
  id: string;
  name?: string;
}

export default function XAILLMOptions({
  settings,
  moduleSuffix = "",
  onError: _onError,
  onChange = () => {},
}: XAILLMOptionsProps): JSX.Element {
  const { t } = useTranslation();
  const apiKeyFieldName = `XAIApiKey${moduleSuffix}`;
  const baseApiKeyFieldName = "XAIApiKey";
  const [inputValue, setInputValue] = useState<string>("");
  const [apiKey, setApiKey] = useState<string>(
    settings
      ? String(settings[apiKeyFieldName] || settings[baseApiKeyFieldName] || "")
      : ""
  );

  const handleApiKeyChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value;
    setInputValue(value);

    // Notify parent of changes to trigger save button
    onChange();
  };

  const handleApiKeyBlur = (): void => {
    // If inputValue is empty and we have an existing API key (user-set or base), don't update
    if (inputValue === "" && apiKey) {
      return;
    }

    setApiKey(inputValue);

    // Update system settings for the API key if it has changed
    if (inputValue !== settings?.[apiKeyFieldName]) {
      System.updateSystem({
        [apiKeyFieldName]: inputValue,
      }).catch(() => {
        // Failed to update XAI API key
      });
    }
  };

  const handleNumberInputScroll = (e: WheelEvent<HTMLInputElement>): void => {
    e.currentTarget.blur();
  };

  return (
    <div className="w-full flex flex-col gap-y-4">
      <div className="w-full flex items-start gap-4">
        <div className="flex flex-col w-60">
          <label className="normal-text text-sm font-semibold block mb-3">
            {t("xai.api-key")}
          </label>
          <input
            type="password"
            name={apiKeyFieldName}
            className="dark-input-mdl normal-text text-sm rounded-lg focus:outline-none block w-full p-2.5"
            placeholder={t("xai.api-key-placeholder")}
            defaultValue={apiKey ? "*".repeat(20) : ""}
            required={true}
            autoComplete="off"
            spellCheck={false}
            onChange={handleApiKeyChange}
            onFocus={() => {
              // Clear input when focusing on a masked field
              if (apiKey && inputValue === "") {
                setInputValue("");
              }
            }}
            onBlur={handleApiKeyBlur}
          />
        </div>

        {!settings?.credentialsOnly && (
          <>
            <XAIModelSelection
              settings={settings}
              apiKey={apiKey}
              moduleSuffix={moduleSuffix}
            />
            <div className="flex flex-col w-60">
              <label className="normal-text text-sm font-semibold block mb-3">
                Token context window
              </label>
              <input
                type="number"
                name={`XAITokenLimit${moduleSuffix}`}
                className="dark-input-mdl normal-text text-sm rounded-lg focus:outline-none block w-full p-2.5"
                placeholder="4096"
                min={1}
                onWheel={handleNumberInputScroll}
                defaultValue={
                  (settings?.[`XAITokenLimit${moduleSuffix}`] as string) ||
                  "4096"
                }
                required={true}
                autoComplete="off"
              />
              <p className="text-xs leading-[18px] font-base normal-text text-opacity-60 mt-2">
                Maximum number of tokens for context and response.
              </p>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

interface XAIModelSelectionProps {
  apiKey?: string;
  settings?: SystemSettings;
  moduleSuffix?: string;
}

function XAIModelSelection({
  apiKey,
  settings,
  moduleSuffix = "",
}: XAIModelSelectionProps): JSX.Element {
  const { t } = useTranslation();
  const [customModels, setCustomModels] = useState<ModelInfo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    async function findCustomModels(): Promise<void> {
      if (!apiKey) {
        setCustomModels([]);
        setLoading(true);
        return;
      }

      try {
        setLoading(true);
        const response = await System.customModels("xai", apiKey);
        const models = Array.isArray(response?.models)
          ? (response.models as ModelInfo[])
          : [];
        setCustomModels(models);
      } catch {
        // Failed to fetch custom models
        setCustomModels([]);
      } finally {
        setLoading(false);
      }
    }
    findCustomModels();
  }, [apiKey]);

  if (loading) {
    return (
      <div className="flex flex-col w-60">
        <label className="normal-text text-sm font-semibold block mb-4">
          {t("xai.model-selection")}
        </label>
        <select
          name={`XAIModelPref${moduleSuffix}`}
          disabled={true}
          value="loading"
          className="dark-input-mdl normal-text focus:outline-none text-sm rounded-lg block w-full p-2"
          title={t("xai.model-selection")}
        >
          <option disabled={true} value="loading" className="normal-text">
            {t("xai.loading-models")}
          </option>
        </select>
        <p className="text-xs leading-[18px] font-base normal-text text-opacity-60 mt-2">
          {t("xai.enter-api-key")}
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-60">
      <label className="normal-text text-sm font-semibold block mb-4">
        {t("xai.model-selection")}
      </label>
      <select
        name={`XAIModelPref${moduleSuffix}`}
        required={true}
        defaultValue={
          (settings?.[`XAIModelPref${moduleSuffix}`] as string) || ""
        }
        className="dark-input-mdl normal-text focus:outline-none text-sm rounded-lg block w-full p-2"
        title={t("xai.model-selection")}
      >
        {customModels.length > 0 && (
          <optgroup label={t("xai.available-models")}>
            {customModels.map((model) => (
              <option className="normal-text" key={model.id} value={model.id}>
                {model.id}
              </option>
            ))}
          </optgroup>
        )}
      </select>
      <p className="text-xs leading-[18px] font-base normal-text text-opacity-60 mt-2">
        {t("xai.model-description")}
      </p>
    </div>
  );
}
