import React, { useState, useEffect, ReactNode } from "react";
import { useTranslation } from "react-i18next";
import { Trash, PencilSimple } from "@phosphor-icons/react";
import Modal from "@/components/ui/Modal";
import { Button } from "@/components/Button";
import EditLegalTask from "@/components/Modals/EditLegalTask";
import { useConfirmation } from "@/hooks/useConfirmation";
import showToast from "@/utils/toast";

interface LegalTask {
  id: string | number;
  name: string;
  sub_category?: string;
  subCategory?: string;
  description?: string;
  legal_task_prompt?: string;
  legalPrompt?: string;
  legalTaskType?: string;
}

interface DeleteResult {
  success: boolean;
  error?: string;
}

interface LegalTasksTableComponentProps {
  legalTasks?: LegalTask[];
  loading?: boolean;
  onEdit?: (task: LegalTask) => void;
  onDelete?: (id: string | number) => Promise<DeleteResult>;
  onRefresh?: () => void;
  showHeader?: boolean;
}

/**
 * Reusable Legal Tasks Table Component
 */
const LegalTasksTableComponent: React.FC<LegalTasksTableComponentProps> = ({
  legalTasks = [],
  loading = false,
  onDelete,
  onRefresh,
  showHeader = true,
}) => {
  const { t } = useTranslation();
  const [editingTask, setEditingTask] = useState<LegalTask | null>(null);
  const {
    isConfirmationOpen,
    openConfirmation,
    closeConfirmation,
    handleConfirm,
  } = useConfirmation();

  const handleEdit = (task: LegalTask): void => {
    setEditingTask(task);
  };

  const handleDelete = (id: string | number): void => {
    openConfirmation(async () => {
      if (typeof onDelete === "function") {
        const result = await onDelete(id);
        if (result?.success) {
          showToast(t("document-builder.table.delete-success"), "success");
        } else {
          showToast(t("document-builder.table.delete-error"), "error");
        }
      }
    });
  };

  // Handle Enter key press for confirmation modal
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent): void => {
      if (isConfirmationOpen && event.key === "Enter") {
        event.preventDefault();
        handleConfirm();
      }
    };

    if (isConfirmationOpen) {
      document.addEventListener("keydown", handleKeyDown);
      return () => {
        document.removeEventListener("keydown", handleKeyDown);
      };
    }
  }, [isConfirmationOpen, handleConfirm]);

  const renderPromptPreview = (task: LegalTask): ReactNode => {
    const prompt = task.legal_task_prompt || task.legalPrompt;
    if (!prompt) return "N/A";

    // Get all possible content example tags from different locales
    const contentExampleTag = t(
      "docx-edit.content-examples-tag-open",
      "<CONTENT_EXAMPLES>"
    );

    // Check if the prompt contains any of the localized content example tags
    // by looking for a pattern like <SOMETHING_EXAMPLES> or <EXAMPLES_DE_SOMETHING>
    const regexPattern =
      /<[A-Za-z0-9_]+((_EXAMPLE|BEISPIEL|EXEMPEL|EKSEMPEL|TRE(Ś|S)CI|CONTENU|[YZ]_IBIKUBIYEMO)[^>]*)?>/i;
    const hasExampleContent =
      prompt.includes(contentExampleTag) || regexPattern.test(prompt);

    if (hasExampleContent) {
      // Find the position of the tag in the text
      let tagPosition = prompt.indexOf(contentExampleTag);

      // If the exact translated tag isn't found, find any matching pattern
      if (tagPosition === -1) {
        const match = prompt.match(regexPattern);
        if (match) {
          tagPosition = match.index || -1;
        }
      }

      // Extract the content before the tag
      const contentBeforeTag =
        tagPosition !== -1 ? prompt.substring(0, tagPosition) : prompt;

      return (
        <>
          {contentBeforeTag.substring(0, 100)}
          {contentBeforeTag.length > 100 ? "..." : ""}
          <span className="text-blue-500">
            {" "}
            {t(
              "docx-edit.contains-example-content",
              "[Contains example content]"
            )}
          </span>
        </>
      );
    } else {
      return (
        <>
          {prompt.substring(0, 100)}
          {prompt.length > 100 ? "..." : ""}
        </>
      );
    }
  };

  return (
    <>
      {showHeader && (
        <h2 className="text-xl font-bold mb-2 text-foreground">
          {t("document-builder.table.title")}
        </h2>
      )}

      {loading ? (
        <div className="flex items-center justify-center p-4">
          <p>{t("document-builder.loading")}</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="table-auto text-sm text-left rounded-lg mt-3 border-top">
            <thead className="text-foreground text-opacity-80 text-xs leading-[18px] font-bold uppercase border-white border-b border-opacity-60">
              <tr>
                <th className="px-6 py-3">
                  {t("document-builder.table.name")}
                </th>
                <th className="px-6 py-3">
                  {t("document-builder.table.sub-category")}
                </th>
                <th className="px-6 py-3">
                  {t("document-builder.table.description")}
                </th>
                <th className="px-6 py-3">
                  {t("document-builder.table.legal-task-type")}
                </th>
                <th className="px-6 py-3">
                  {t("document-builder.table.prompt")}
                </th>
                <th className="px-6 py-3">
                  {t("document-builder.table.actions")}
                </th>
              </tr>
            </thead>
            <tbody>
              {legalTasks.length > 0 ? (
                legalTasks.map((task, index) => (
                  <tr
                    key={task.id || index}
                    className="bg-transparent text-foreground text-opacity-80 text-sm table-tr-item"
                  >
                    <td className="px-4 py-3">
                      <b>{task.name}</b>
                    </td>
                    <td className="px-4 py-3">
                      {task.sub_category || task.subCategory || "N/A"}
                    </td>
                    <td className="px-4 py-3">{task.description || "N/A"}</td>
                    <td className="px-4 py-3">{task.legalTaskType || "N/A"}</td>
                    <td className="px-4 py-3">
                      {task.legal_task_prompt || task.legalPrompt ? (
                        <div>{renderPromptPreview(task)}</div>
                      ) : (
                        "N/A"
                      )}
                    </td>
                    <td className="px-4 py-2 flex items-center gap-x-6 mt-1">
                      <button
                        onClick={() => handleEdit(task)}
                        className="flex flex-row justify-between items-center gap-1 text-[13px] font-medium text-white bg-blue-400 px-2 py-1 rounded-lg hover:bg-blue-600"
                        title={t("document-builder.table.edit")}
                      >
                        <PencilSimple size={16} />
                      </button>
                      <button
                        onClick={() => handleDelete(task.id)}
                        className="flex flex-row justify-between items-center gap-1 text-[13px] font-medium text-white bg-red-400 px-2 py-1 rounded-lg hover:bg-red-600"
                        title={t("document-builder.table.delete")}
                      >
                        <Trash size={16} />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    className="border border-gray-300 px-4 py-2 text-center"
                    colSpan={6}
                  >
                    {t("document-builder.table.no-tasks")}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}

      {/* Edit Task Modal */}
      <Modal
        isOpen={!!editingTask}
        onClose={() => setEditingTask(null)}
        className="w-[800px] max-w-[90vw]"
      >
        {editingTask && (
          <EditLegalTask
            task={editingTask}
            onClose={() => setEditingTask(null)}
            onSuccess={() => {
              setEditingTask(null);
              if (typeof onRefresh === "function") {
                onRefresh();
              }
            }}
          />
        )}
      </Modal>

      {/* Confirmation Modal */}
      <Modal
        isOpen={isConfirmationOpen}
        onClose={closeConfirmation}
        title={t("document-builder.table.delete-title")}
        footer={
          <>
            <Button variant="secondary" onClick={closeConfirmation}>
              {t("button.cancel")}
            </Button>
            <Button variant="destructive" onClick={handleConfirm}>
              {t("button.delete")}
            </Button>
          </>
        }
      >
        <p className="text-foreground">
          {t("document-builder.table.delete-confirm")}
        </p>
      </Modal>
    </>
  );
};

export default LegalTasksTableComponent;
