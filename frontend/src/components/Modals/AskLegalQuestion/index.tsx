import React, { useState, useEffect, useRef, ChangeEvent } from "react";
import { useTranslation } from "react-i18next";
import Workspace from "@/models/workspace";
import System from "@/models/system";
import { useParams } from "react-router-dom";
import { AiOutlineLoading } from "react-icons/ai";
import { MdArrowForwardIos } from "react-icons/md";
import { TbArrowNarrowLeft } from "react-icons/tb";
import { IoWarningOutline } from "react-icons/io5";
import { FiSettings } from "react-icons/fi";
import { FaWrench } from "react-icons/fa";
import { Button } from "@/components/Button";
import Modal from "@/components/ui/Modal";
import useUser from "@/hooks/useUser";
import LegalTasksSettingsModal from "@/components/Modals/LegalTasksSettingsModal";
import FlowSettingsModal from "@/components/Modals/FlowSettingsModal";
import useThreadProgress from "@/hooks/useThreadProgress";
import useProgressStore from "@/stores/progressStore";
import { cleanFileName } from "@/utils/fileHelpers";

interface FileItem {
  name: string;
  type: "file" | "folder";
  items?: FileItem[];
  path?: string;
  id?: string;
  starred?: boolean;
  pinned?: boolean;
  pdr?: boolean;
  docId?: string;
  cached?: boolean;
  token_count_estimate?: number;
  [key: string]: string | boolean | number | undefined | FileItem[] | null;
}

// Generic type for directory/file items that works with both FileItem and DirectoryItem
interface GenericFileItem {
  name: string;
  type: "file" | "folder";
  items?: GenericFileItem[];
  path?: string;
  id?: string;
  starred?: boolean;
  pinned?: boolean;
  pdr?: boolean;
  docId?: string;
  cached?: boolean;
  token_count_estimate?: number;
}

interface Category {
  name: string;
  subCategories?: SubCategory[];
}

interface SubCategory {
  name: string;
  description?: string;
  legalPrompt?: string;
  legalTaskType?: "mainDoc" | "noMainDoc" | "referenceFiles";
  requiresMainDocument?: boolean;
}

interface PerformLegalTaskModalProps {
  sendCommand: (
    command: string,
    isLegalTask: boolean,
    files: FileItem[],
    attachments: FileItem[],
    options: SendCommandOptions
  ) => Promise<void>;
  onClose: () => void;
  onProcessingChange?: (processing: boolean) => void;
  threadSlug?: string;
}

interface SendCommandOptions {
  preventNewChat: boolean;
  preventChatCreation: boolean;
  cdb: boolean;
  cdbOptions: (string | null | string[])[];
  abortController: AbortController;
  chatIdRef: React.MutableRefObject<string | null>;
  chatHandler: (chatResult: ChatResult) => void;
  displayMessage: string;
}

interface ChatResult {
  type: string;
  label?: string;
  chatId?: string;
  close?: boolean;
  error?: string;
}

interface NoFilesWarningModalProps {
  onClose: () => void;
}

// Warning modal component for when no JSON files are found
const NoFilesWarningModal: React.FC<NoFilesWarningModalProps> = ({
  onClose,
}) => {
  const { t } = useTranslation();
  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title={t("performLegalTask.warning-title")}
      className="w-[400px]"
    >
      <div className="flex flex-col items-center justify-center p-4 text-center">
        <IoWarningOutline className="text-yellow-500 text-5xl mb-4" />
        <h4 className="text-lg font-medium text-foreground mb-2">
          {t("performLegalTask.no-files-title")}
        </h4>
        <p className="text-foreground text-opacity-80 mb-4">
          {t("performLegalTask.no-files-description")}
        </p>
        <Button onClick={onClose}>{t("common.ok")}</Button>
      </div>
    </Modal>
  );
};

// Helper function to extract all file names from the workspace tree
const extractFileNames = (items: GenericFileItem[]): string[] => {
  const files: string[] = [];
  const recurse = (currentItems: GenericFileItem[]): void => {
    for (const item of currentItems) {
      // CDB processes .json files from the root or nested, ensure we get them
      if (item.type === "file" && item.name.endsWith(".json")) {
        files.push(item.name);
      } else if (item.type === "folder" && item.items) {
        recurse(item.items);
      }
    }
  };
  if (items) recurse(items);
  return files;
};

export default function PerformLegalTaskModal({
  sendCommand,
  onClose,
  onProcessingChange,
  threadSlug = "",
}: PerformLegalTaskModalProps): React.ReactElement {
  const { t } = useTranslation();
  const { slug } = useParams<{ slug?: string }>();
  const { user } = useUser();
  const [loading, setLoading] = useState<boolean>(true);
  const [workspace, setWorkspace] = useState<{
    id: string;
    name: string;
    slug: string;
    [key: string]: string | boolean | number | undefined;
  } | null>(null);
  const [legalTasks, setLegalTasks] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(
    null
  );
  const [subCategories, setSubCategories] = useState<SubCategory[]>([]);
  const [loadingSubCategories, setLoadingSubCategories] =
    useState<boolean>(false);
  const [selectedTask, setSelectedTask] = useState<SubCategory | null>(null);
  const [customInstructions, setCustomInstructions] = useState<string>("");
  const [selectedMainDocName, setSelectedMainDocName] = useState<string>("");
  const [selectedReferenceFiles, setSelectedReferenceFiles] = useState<
    string[]
  >([]);
  const [workspaceFiles, setWorkspaceFiles] = useState<string[]>([]);
  const [generatedFiles, setGeneratedFiles] = useState<string[]>([]);
  const [showNoFilesWarning, setShowNoFilesWarning] = useState<boolean>(false);
  const [isSettingsModalOpen, setIsSettingsModalOpen] =
    useState<boolean>(false);
  const [isFlowSettingsModalOpen, setIsFlowSettingsModalOpen] =
    useState<boolean>(false);
  const abortController = useRef<AbortController>(new AbortController());

  const chatIdRef = useRef<string | null>(null);

  // Permission check for settings access
  const hasAdminAccess = ["admin", "manager", "superuser"].includes(
    user?.role || ""
  );

  const safeThreadSlug = threadSlug || "";
  const progress = useThreadProgress(safeThreadSlug);

  useEffect(() => {
    async function fetchFilesForSelection(): Promise<void> {
      if (workspace?.id) {
        try {
          const localFilesData = await System.localFiles(workspace.id);

          if (localFilesData && localFilesData.items) {
            const extracted = extractFileNames(localFilesData.items);

            // Store names without .json for display, will add back when sending
            setWorkspaceFiles(
              extracted.map((name) => name.replace(".json", ""))
            );
          } else {
            setWorkspaceFiles([]);
          }
        } catch {
          setWorkspaceFiles([]);
        }
      }
    }
    fetchFilesForSelection();
  }, [workspace]);

  const handleFetchSubCategories = async (name: string): Promise<void> => {
    setLoadingSubCategories(true);

    try {
      const response = await System.fetchSubCategories(name);

      if (response.success && response.data) {
        setSubCategories(response.data as SubCategory[]);
        setSelectedCategory({
          name,
          subCategories: response.data as SubCategory[],
        });
      } else {
        setSubCategories([]);
        setSelectedCategory({ name, subCategories: [] });
      }
    } catch {
      setSubCategories([]);
      setSelectedCategory({ name, subCategories: [] });
    } finally {
      setLoadingSubCategories(false);
    }
  };

  const handleCategorySelect = async (category: Category): Promise<void> => {
    await handleFetchSubCategories(category.name);
  };

  const handleTaskSelect = (subCategory: SubCategory): void => {
    setSelectedTask(subCategory);
    setSelectedMainDocName("");
    setSelectedReferenceFiles([]);
  };

  const checkWorkspaceFiles = async (workspaceId: string): Promise<boolean> => {
    try {
      const localFiles = await System.localFiles(workspaceId);

      // Check if there are any JSON files in the workspace
      let hasFiles = false;

      if (localFiles && localFiles.items) {
        // Recursively check for files in all folders
        const checkFolderForFiles = (folder: GenericFileItem): boolean => {
          if (folder.type === "file") {
            return true;
          }

          if (folder.items && folder.items.length > 0) {
            return folder.items.some((item: GenericFileItem) => {
              if (item.type === "file") {
                return true;
              }
              return (
                item.items && item.items.length > 0 && checkFolderForFiles(item)
              );
            });
          }

          return false;
        };

        hasFiles = localFiles.items.some((item) => checkFolderForFiles(item));
      }

      return hasFiles;
    } catch {
      return false;
    }
  };

  const handleConfirmTask = async (): Promise<void> => {
    if (!selectedTask || !workspace) return;

    if (selectedTask.legalTaskType === "mainDoc" && !selectedMainDocName) {
      return;
    }

    if (
      selectedTask.legalTaskType === "referenceFiles" &&
      selectedReferenceFiles.length === 0
    ) {
      return;
    }

    const hasFiles = await checkWorkspaceFiles(workspace.id);
    if (!hasFiles) {
      setShowNoFilesWarning(true);
      return;
    }

    if (progress.isActive) {
      return;
    }

    const cdbOptions: (string | null | string[])[] = [
      selectedTask?.legalPrompt || null,
      customInstructions || null,
      selectedTask.legalTaskType === "mainDoc"
        ? `${selectedMainDocName}.json`
        : null,
      selectedTask?.legalTaskType || null,
      selectedTask.legalTaskType === "referenceFiles"
        ? selectedReferenceFiles.map((f) => `${workspace.name}/${f}`)
        : null,
    ];

    let determinedFlowType: string;
    if (selectedTask.legalTaskType === "mainDoc") {
      determinedFlowType = "main";
    } else if (selectedTask.legalTaskType === "noMainDoc") {
      determinedFlowType = "noMain";
    } else if (selectedTask.legalTaskType === "referenceFiles") {
      determinedFlowType = "referenceFiles";
    } else {
      determinedFlowType = selectedTask.requiresMainDocument
        ? "main"
        : "noMain";
    }

    try {
      if (safeThreadSlug) {
        const totalSteps = determinedFlowType === "referenceFiles" ? 8 : 9;

        // Clear any stale progress data before starting new process
        progress.clearStaleProgress();
        progress.start(totalSteps, determinedFlowType);
      }

      // Close the modal immediately when process starts
      onClose();

      // Get the AbortController from the progress store (created when progress.start() was called)
      const progressAbortController = useProgressStore
        .getState()
        .getAbortController(safeThreadSlug);

      // Use the progress store's abort controller, or fallback to local one
      const activeAbortController =
        progressAbortController || abortController.current;

      chatIdRef.current = null;

      await sendCommand(selectedTask?.name, true, [], [], {
        preventNewChat: true,
        preventChatCreation: false,
        cdb: true,
        cdbOptions: cdbOptions,
        abortController: activeAbortController,
        chatIdRef: chatIdRef,
        chatHandler: (chatResult: ChatResult) => {
          if (chatResult.type === "cdbProgress" && safeThreadSlug) {
            progress.update(chatResult);

            if (chatResult.label) {
              setGeneratedFiles((prev) =>
                prev.includes(chatResult.label!)
                  ? prev
                  : [...prev, chatResult.label!]
              );
            }
          }
          if (
            chatResult.type === "finalizeResponseStream" &&
            chatResult.chatId &&
            safeThreadSlug
          ) {
            chatIdRef.current = chatResult.chatId;
            progress.finish();
          }
          // Handle textResponse completion (final document)
          if (
            chatResult.type === "textResponse" &&
            chatResult.close &&
            chatResult.chatId &&
            safeThreadSlug
          ) {
            chatIdRef.current = chatResult.chatId;
            progress.finish();
          }
          if (chatResult.type === "stopGeneration") {
            handleAbortTask(false);
          }
          if (
            chatResult.type === "abort" &&
            chatResult.error &&
            safeThreadSlug
          ) {
            progress.setError(chatResult.error);
          }
        },
        displayMessage: selectedTask?.name,
      });

      if (generatedFiles.length > 0) {
        try {
          await System.deleteDocuments(generatedFiles);
          setGeneratedFiles([]);
        } catch {
          // Error cleaning generated files
        }
      }

      // Close the modal and trigger a refresh of the chat history
      setTimeout(function () {
        window.dispatchEvent(
          new CustomEvent("CDB_PROCESS_COMPLETE", {
            detail: { chatId: chatIdRef.current },
          })
        );
      }, 1000);
    } catch {
      // Reset progress state to clear UI indicators
      if (safeThreadSlug) {
        progress.cancel();
      }
    }
  };

  const handleOpenSettings = (): void => {
    // Open the legal tasks settings modal
    setIsSettingsModalOpen(true);
  };

  const handleOpenFlowSettings = (): void => {
    // Open the flow settings modal
    setIsFlowSettingsModalOpen(true);
  };

  const fetchGroupedLegalTasks = async (): Promise<void> => {
    setLoading(true);

    const response = await System.fetchGroupedLegalTasks();
    if (response.success) {
      setLegalTasks(response.data as Category[]);
    } else {
      // Error fetching grouped legal tasks
    }

    setLoading(false);
  };

  useEffect(() => {
    fetchGroupedLegalTasks();
  }, []);

  useEffect(() => {
    async function getWorkspace(): Promise<void> {
      if (!slug) return;
      setLoading(true);

      try {
        const _workspace = await Workspace.bySlug(slug);

        if (!_workspace) {
          setLoading(false);
          return;
        }

        setWorkspace({
          id: _workspace.id,
          name: _workspace.name,
          slug: _workspace.slug,
        });

        await checkWorkspaceFiles(_workspace.id);
      } catch {
        // Error fetching workspace
      } finally {
        setLoading(false);
      }
    }

    getWorkspace();
  }, [slug]);

  const handleAbortTask = async (
    shouldCloseModal: boolean = true
  ): Promise<void> => {
    // Cancel the progress first (this will call the progress store's cancel method)
    if (safeThreadSlug) {
      progress.cancel();
    }

    // Also abort the local controller as a fallback
    abortController.current.abort();
    // ensure next run gets a fresh controller
    abortController.current = new AbortController();

    try {
      await System.purgeDocumentBuilder();
    } catch {
      // Failed to purge CDB files on abort
    }

    if (generatedFiles.length > 0) {
      try {
        await System.deleteDocuments(generatedFiles);
        setGeneratedFiles([]);
      } catch {
        // Error deleting generated files
      }
    }

    if (onProcessingChange) onProcessingChange(false);

    if (shouldCloseModal) {
      onClose();
    }
  };

  const handleCustomInstructionsChange = (
    e: ChangeEvent<HTMLTextAreaElement>
  ): void => {
    setCustomInstructions(e.target.value);
  };

  const handleMainDocSelectChange = (
    e: ChangeEvent<HTMLSelectElement>
  ): void => {
    setSelectedMainDocName(e.target.value);
  };

  const handleReferenceFilesChange = (
    e: ChangeEvent<HTMLSelectElement>
  ): void => {
    setSelectedReferenceFiles(
      Array.from(e.target.selectedOptions, (opt) => opt.value)
    );
  };

  if (showNoFilesWarning) {
    return <NoFilesWarningModal onClose={() => setShowNoFilesWarning(false)} />;
  }

  return (
    <div className="w-full flex flex-col">
      <div className="flex-1 overflow-y-auto">
        <p className="text-foreground text-opacity-60 mb-6 break-words">
          {t("performLegalTask.duration-info")}
        </p>

        {loading ? (
          <div className="flex items-center justify-center p-4">
            <AiOutlineLoading className="animate-spin text-blue-500 text-lg" />
            <span className="ml-2">
              {t("performLegalTask.loading-subcategory")}
            </span>
          </div>
        ) : selectedCategory ? (
          <div className="flex flex-col">
            <div className="flex items-center mb-4">
              <button
                onClick={() => setSelectedCategory(null)}
                className="flex items-center text-foreground hover:text-opacity-80"
              >
                <TbArrowNarrowLeft className="mr-2" />
                {t("performLegalTask.select-category")}
              </button>
            </div>
            {loadingSubCategories ? (
              <div className="flex items-center justify-center p-4">
                <AiOutlineLoading className="animate-spin text-blue-500 text-lg" />
                <span className="ml-2">
                  {t("performLegalTask.loading-subcategory")}
                </span>
              </div>
            ) : subCategories.length === 0 ? (
              <div className="text-center p-4">
                {t("performLegalTask.noSubtskfund")}
              </div>
            ) : (
              <>
                <h3 className="text-lg font-medium text-foreground mb-4">
                  {t("performLegalTask.choose-task")}
                </h3>
                <div className="space-y-4">
                  {subCategories.map((subCategory) => (
                    <div key={subCategory.name} className="relative">
                      <div className="relative">
                        {/* Use a div instead of a button when the task is selected */}
                        {selectedTask?.name === subCategory.name ? (
                          <div className="flex items-center justify-between w-full p-3 text-left rounded-lg transition-colors duration-200 bg-secondary border-2 border-primary">
                            <div>
                              <span className="text-foreground">
                                {subCategory.name}
                              </span>
                              {subCategory.description && (
                                <p className="text-sm text-foreground text-opacity-60 mt-1">
                                  {subCategory.description}
                                </p>
                              )}
                            </div>
                          </div>
                        ) : (
                          <button
                            onClick={() => handleTaskSelect(subCategory)}
                            className="flex items-center justify-between w-full p-3 text-left rounded-lg transition-colors duration-200 border bg-secondary hover:bg-secondary-hover"
                          >
                            <div>
                              <span className="text-foreground">
                                {subCategory.name}
                              </span>
                              {subCategory.description && (
                                <p className="text-sm text-foreground text-opacity-60 mt-1">
                                  {subCategory.description}
                                </p>
                              )}
                            </div>
                            <MdArrowForwardIos className="text-foreground text-opacity-60" />
                          </button>
                        )}
                      </div>

                      {selectedTask?.name === subCategory.name && (
                        <div className="mt-2 p-4 bg-background rounded-lg border border-gray-200">
                          <div className="mb-2">
                            <label className="block text-sm font-medium text-foreground">
                              {t("performLegalTask.custom-instructions-label")}
                            </label>
                          </div>
                          <textarea
                            value={customInstructions}
                            onChange={handleCustomInstructionsChange}
                            placeholder={t(
                              "performLegalTask.custom-instructions-placeholder"
                            )}
                            className="w-full p-2 text-sm border rounded-md bg-background text-foreground placeholder-foreground-muted"
                            rows={9}
                          />
                          {selectedTask?.legalTaskType === "mainDoc" && (
                            <div className="mt-4">
                              <label
                                htmlFor="main-document-select"
                                className="block text-sm font-medium text-foreground mb-1"
                              >
                                {t(
                                  "performLegalTask.select-main-document-label"
                                )}
                              </label>
                              {workspaceFiles.length > 0 ? (
                                <select
                                  id="main-document-select"
                                  value={selectedMainDocName}
                                  onChange={handleMainDocSelectChange}
                                  className="w-full p-2 text-sm border rounded-md bg-background text-foreground"
                                  required
                                >
                                  <option value="" disabled>
                                    {t(
                                      "performLegalTask.select-document-placeholder"
                                    )}
                                  </option>
                                  {workspaceFiles.map((fileName) => (
                                    <option key={fileName} value={fileName}>
                                      {cleanFileName(fileName)}
                                    </option>
                                  ))}
                                </select>
                              ) : (
                                <p className="text-sm text-foreground text-opacity-60">
                                  {t("performLegalTask.no-files-available")}
                                </p>
                              )}
                            </div>
                          )}
                          {selectedTask?.legalTaskType === "referenceFiles" && (
                            <div className="mt-4">
                              <label
                                htmlFor="reference-files-select"
                                className="block text-sm font-medium text-foreground mb-1"
                              >
                                {t(
                                  "performLegalTask.selectReferenceFilesLabel"
                                )}
                              </label>
                              {workspaceFiles.length > 0 ? (
                                <select
                                  multiple
                                  id="reference-files-select"
                                  value={selectedReferenceFiles}
                                  onChange={handleReferenceFilesChange}
                                  className="w-full p-2 text-sm border rounded-md bg-background text-foreground"
                                >
                                  {workspaceFiles.map((fileName) => (
                                    <option key={fileName} value={fileName}>
                                      {cleanFileName(fileName)}
                                    </option>
                                  ))}
                                </select>
                              ) : (
                                <p className="text-sm text-foreground text-opacity-60">
                                  {t("performLegalTask.no-files-available")}
                                </p>
                              )}
                            </div>
                          )}
                          <div className="mt-4 flex justify-end space-x-3">
                            <Button
                              onClick={() => {
                                setSelectedTask(null);
                                setCustomInstructions("");
                                setSelectedMainDocName("");
                                setSelectedReferenceFiles([]);
                              }}
                              variant="secondary"
                            >
                              {t("common.cancel")}
                            </Button>
                            <Button
                              onClick={handleConfirmTask}
                              disabled={
                                (selectedTask?.legalTaskType === "mainDoc" &&
                                  !selectedMainDocName) ||
                                (selectedTask?.legalTaskType ===
                                  "referenceFiles" &&
                                  selectedReferenceFiles.length === 0)
                              }
                            >
                              {t("common.confirmstart")}
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        ) : (
          <>
            <h3 className="text-lg font-medium text-foreground mb-4">
              {t("performLegalTask.select-category")}
            </h3>
            <div className="grid grid-cols-1 gap-4 pb-4 text-foreground">
              {legalTasks.length === 0 ? (
                <div className="text-center p-4">
                  {t("performLegalTask.noTaskfund")}
                </div>
              ) : (
                legalTasks.map((category, index) => (
                  <button
                    key={index}
                    className="text-left p-4 rounded-lg border transition-colors relative bg-secondary hover:bg-secondary-hover"
                    onClick={() => handleCategorySelect(category)}
                  >
                    <h4 className="font-medium text-foreground">
                      {category.name}
                    </h4>
                    <MdArrowForwardIos className="absolute right-4 top-1/2 transform -translate-y-1/2" />
                  </button>
                ))
              )}
            </div>
          </>
        )}
      </div>

      {/* Settings buttons for admin, manager, and superuser roles */}
      {hasAdminAccess && (
        <div className="mt-6 border-t border-gray-200 pt-4 flex flex-wrap justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleOpenSettings}
            className="flex items-center gap-2"
          >
            <FiSettings className="h-4 w-4" />
            {t("performLegalTask.settings-button")}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleOpenFlowSettings}
            className="flex items-center gap-2"
          >
            <FaWrench className="h-4 w-4" />
            {t("performLegalTask.flow-settings-button")}
          </Button>
        </div>
      )}

      {/* Legal Tasks Settings Modal */}
      <LegalTasksSettingsModal
        isOpen={isSettingsModalOpen}
        onClose={() => {
          setIsSettingsModalOpen(false);
          fetchGroupedLegalTasks();

          if (selectedCategory?.name) {
            handleFetchSubCategories(selectedCategory.name);
            setSelectedTask(null);
          }
        }}
      />

      {/* Flow Settings Modal */}
      <FlowSettingsModal
        isOpen={isFlowSettingsModalOpen}
        onClose={() => setIsFlowSettingsModalOpen(false)}
      />
    </div>
  );
}
