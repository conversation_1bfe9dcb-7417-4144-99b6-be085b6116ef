import { render, screen, fireEvent } from "@testing-library/react";
import InvoiceReferenceNavigationModal from "../index";

// Mock the Rexor store hook
jest.mock("@/stores/rexorStore", () => ({
  useRexorActiveReference: () => "REF-12345",
}));

// Mock the Modal component to render its children
jest.mock("@/components/ui/Modal", () => ({
  __esModule: true,
  default: ({ isOpen, children, footer, title, onClose }: any) =>
    isOpen ? (
      <div
        data-testid="modal"
        role="dialog"
        onKeyDown={(e: any) => {
          if (e.key === "Escape") onClose();
        }}
      >
        {title && <h2>{title}</h2>}
        {children}
        <div data-testid="modal-footer">{footer}</div>
      </div>
    ) : null,
}));

// Mock the Button component
jest.mock("@/components/Button", () => ({
  Button: ({ children, onClick, ...props }: any) => (
    <button onClick={onClick} {...props}>
      {children}
    </button>
  ),
}));

// Mock the translation hook
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (
      key: string,
      options?: { reference?: string; destinationType?: string }
    ) => {
      const translations: Record<string, string> = {
        "invoice-reference-navigation.title": "Active Invoice Reference",
        "invoice-reference-navigation.message":
          "You have an active invoice reference ({{reference}}) and are about to navigate to a different {{destinationType}}. What would you like to do?",
        "invoice-reference-navigation.clear-and-continue":
          "Clear Reference and Continue",
        "invoice-reference-navigation.keep-and-continue":
          "Keep Reference and Continue",
        "invoice-reference-navigation.current-reference": "Current Reference:",
        "invoice-reference-navigation.explanation":
          "Clearing the reference will remove it from your current session. Keeping it will maintain the reference for your new destination.",
        "invoice-reference-navigation.destination-types.thread": "thread",
        "invoice-reference-navigation.destination-types.workspace": "workspace",
        "invoice-reference-navigation.destination-types.module": "module",
        "invoice-reference-navigation.destination-types.location": "module",
      };

      let translation = translations[key as keyof typeof translations] || key;

      // Handle template variable replacement
      if (options) {
        if (options.reference) {
          translation = translation.replace("{{reference}}", options.reference);
        }
        if (options.destinationType) {
          translation = translation.replace(
            "{{destinationType}}",
            options.destinationType
          );
        }
      }

      return translation;
    },
  }),
}));

// Import types for the component
type InvoiceReferenceNavigationModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onClearAndContinue: () => void;
  onKeepAndContinue: () => void;
  destinationType?: "location" | "thread" | "workspace";
};

describe("InvoiceReferenceNavigationModal", () => {
  const defaultProps: InvoiceReferenceNavigationModalProps = {
    isOpen: true,
    onClose: jest.fn(),
    onClearAndContinue: jest.fn(),
    onKeepAndContinue: jest.fn(),
    destinationType: "workspace" as const,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the modal when isOpen is true", () => {
    render(
      <InvoiceReferenceNavigationModal
        {...defaultProps}
        destinationType="workspace"
      />
    );

    expect(screen.getByText("Active Invoice Reference")).toBeInTheDocument();
    expect(
      screen.getByText(/You have an active invoice reference \(REF-12345\)/)
    ).toBeInTheDocument();
  });

  it("does not render the modal when isOpen is false", () => {
    render(
      <InvoiceReferenceNavigationModal {...defaultProps} isOpen={false} />
    );

    expect(
      screen.queryByText("Active Invoice Reference")
    ).not.toBeInTheDocument();
  });

  it("displays the correct destination type in the message", () => {
    render(
      <InvoiceReferenceNavigationModal
        {...defaultProps}
        destinationType="thread"
      />
    );

    expect(
      screen.getByText(/navigate to a different thread/)
    ).toBeInTheDocument();
  });

  it("calls onClearAndContinue when Clear Reference button is clicked", () => {
    render(<InvoiceReferenceNavigationModal {...defaultProps} />);

    const clearButton = screen.getByText("Clear Reference and Continue");
    fireEvent.click(clearButton);

    expect(defaultProps.onClearAndContinue).toHaveBeenCalledTimes(1);
  });

  it("calls onKeepAndContinue when Keep Reference button is clicked", () => {
    render(<InvoiceReferenceNavigationModal {...defaultProps} />);

    const keepButton = screen.getByText("Keep Reference and Continue");
    fireEvent.click(keepButton);

    expect(defaultProps.onKeepAndContinue).toHaveBeenCalledTimes(1);
  });

  it("calls onClose when modal is closed", () => {
    render(<InvoiceReferenceNavigationModal {...defaultProps} />);

    // Simulate closing the modal (this would typically be done by clicking outside or pressing escape)
    // The exact implementation depends on how the Modal component handles closing
    const modal = screen.getByRole("dialog");
    fireEvent.keyDown(modal, { key: "Escape", code: "Escape" });

    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it("handles different destination types correctly", () => {
    const { rerender } = render(
      <InvoiceReferenceNavigationModal
        {...defaultProps}
        destinationType="location"
      />
    );
    expect(
      screen.getByText(/navigate to a different module/)
    ).toBeInTheDocument();

    rerender(
      <InvoiceReferenceNavigationModal
        {...defaultProps}
        destinationType="thread"
      />
    );
    expect(
      screen.getByText(/navigate to a different thread/)
    ).toBeInTheDocument();

    rerender(
      <InvoiceReferenceNavigationModal
        {...defaultProps}
        destinationType="workspace"
      />
    );
    expect(
      screen.getByText(/navigate to a different workspace/)
    ).toBeInTheDocument();
  });

  it("displays the active reference number in the message", () => {
    render(<InvoiceReferenceNavigationModal {...defaultProps} />);

    // Check that the reference appears in the main message
    expect(
      screen.getByText(/You have an active invoice reference \(REF-12345\)/)
    ).toBeInTheDocument();

    // Check that the reference display section exists
    expect(screen.getByText("Current Reference:")).toBeInTheDocument();
  });
});
