import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import UploadFile from "../index";
import Workspace from "@/models/workspace";
import Document from "@/models/document";

// Mock dependencies
jest.mock("@/models/workspace");
jest.mock("@/models/document");
jest.mock("@/utils/toast");
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key, defaultValue, vars) => {
      if (vars) {
        return defaultValue.replace(
          /\{\{(\w+)\}\}/g,
          (match, varName) => vars[varName] || match
        );
      }
      return defaultValue || key;
    },
  }),
}));

describe("Bulk Upload File Operations", () => {
  const mockWorkspace = {
    slug: "test-workspace",
    id: "workspace-123",
  };

  const mockFetchKeys = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("File Path Handling", () => {
    it("should correctly parse file paths from bulk upload", () => {
      const testCases = [
        {
          input: "test-workspace/custom-documents/file1.pdf.json",
          expected: {
            fileName: "file1.pdf.json",
            currentFolder: "custom-documents",
          },
        },
        {
          input: "test-workspace/folder1/file2.pdf.json",
          expected: {
            fileName: "file2.pdf.json",
            currentFolder: "folder1",
          },
        },
        {
          input: "test-workspace/folder1/subfolder/file3.pdf.json",
          expected: {
            fileName: "file3.pdf.json",
            currentFolder: "folder1/subfolder",
          },
        },
      ];

      testCases.forEach(({ input, expected }) => {
        const pathParts = input.split("/");
        const fileName = pathParts.pop();
        pathParts.shift(); // Remove workspace slug
        const currentFolder = pathParts.length > 0 ? pathParts.join("/") : null;

        expect(fileName).toBe(expected.fileName);
        expect(currentFolder).toBe(expected.currentFolder);
      });
    });

    it("should handle file paths after moving to a folder", () => {
      const originalPath = "test-workspace/custom-documents/file1.pdf.json";
      const targetFolder = "new-folder";

      // Simulate the path update after moving
      const pathParts = originalPath.split("/");
      const fileName = pathParts.pop();
      const workspaceSlug = pathParts[0];
      const newPath = `${workspaceSlug}/${targetFolder}/${fileName}`;

      expect(newPath).toBe("test-workspace/new-folder/file1.pdf.json");
    });

    it("should correctly extract paths for embedding", () => {
      const filePaths = [
        "test-workspace/custom-documents/file1.pdf.json",
        "test-workspace/folder1/file2.pdf.json",
        "test-workspace/folder2/subfolder/file3.pdf.json",
      ];

      const embeddingPaths = filePaths.map((filePath) => {
        const pathParts = filePath.split("/");
        if (pathParts.length > 1) {
          pathParts.shift(); // Remove workspace slug
          return pathParts.join("/");
        }
        return filePath;
      });

      expect(embeddingPaths).toEqual([
        "custom-documents/file1.pdf.json",
        "folder1/file2.pdf.json",
        "folder2/subfolder/file3.pdf.json",
      ]);
    });
  });

  describe("Move to Folder Operations", () => {
    it("should format files correctly for moveToFolder API", () => {
      const successfulFiles = [
        "test-workspace/custom-documents/file1.pdf.json",
        "test-workspace/custom-documents/file2.pdf.json",
      ];

      const filesToMove = successfulFiles.map((filePath) => {
        const pathParts = filePath.split("/");
        const fileName = pathParts.pop();

        if (pathParts.length > 1) {
          pathParts.shift(); // Remove workspace slug
          const currentFolder = pathParts.join("/");
          return {
            name: fileName,
            folderName: currentFolder,
          };
        }

        return {
          name: fileName,
          folderName: null,
        };
      });

      expect(filesToMove).toEqual([
        {
          name: "file1.pdf.json",
          folderName: "custom-documents",
        },
        {
          name: "file2.pdf.json",
          folderName: "custom-documents",
        },
      ]);
    });

    it("should handle moving files from one folder to another", () => {
      const successfulFiles = [
        "test-workspace/folder1/file1.pdf.json",
        "test-workspace/folder2/file2.pdf.json",
      ];

      const targetFolder = "folder3";

      const filesToMove = successfulFiles.map((filePath) => {
        const pathParts = filePath.split("/");
        const fileName = pathParts.pop();

        if (pathParts.length > 1) {
          pathParts.shift(); // Remove workspace slug
          const currentFolder = pathParts.join("/");
          return {
            name: fileName,
            folderName: currentFolder,
          };
        }

        return {
          name: fileName,
          folderName: null,
        };
      });

      // Simulate the Document.moveToFolder call
      const moveRequest = {
        files: filesToMove,
        targetFolder: targetFolder,
        workspaceSlug: "test-workspace",
      };

      expect(moveRequest.files[0].folderName).toBe("folder1");
      expect(moveRequest.files[1].folderName).toBe("folder2");
      expect(moveRequest.targetFolder).toBe("folder3");
    });
  });

  describe("Embedding Path Resolution", () => {
    it("should resolve paths correctly for files in custom-documents", () => {
      const files = [
        "test-workspace/custom-documents/file1.pdf.json",
        "test-workspace/custom-documents/file2.pdf.json",
      ];

      const embeddingPaths = files.map((filePath) => {
        const pathParts = filePath.split("/");
        if (pathParts.length > 1) {
          pathParts.shift(); // Remove workspace slug
          return pathParts.join("/");
        }
        return filePath;
      });

      expect(embeddingPaths).toEqual([
        "custom-documents/file1.pdf.json",
        "custom-documents/file2.pdf.json",
      ]);
    });

    it("should resolve paths correctly for files moved to folders", () => {
      const files = [
        "test-workspace/legal-docs/file1.pdf.json",
        "test-workspace/contracts/2024/file2.pdf.json",
      ];

      const embeddingPaths = files.map((filePath) => {
        const pathParts = filePath.split("/");
        if (pathParts.length > 1) {
          pathParts.shift(); // Remove workspace slug
          return pathParts.join("/");
        }
        return filePath;
      });

      expect(embeddingPaths).toEqual([
        "legal-docs/file1.pdf.json",
        "contracts/2024/file2.pdf.json",
      ]);
    });
  });

  describe("Integration Scenarios", () => {
    it("should handle the complete flow: upload -> move -> embed", async () => {
      // Mock bulk upload response
      const bulkUploadJobStatus = {
        status: "completed",
        processedFiles: 2,
        successfulFiles: [
          "test-workspace/custom-documents/file1.pdf.json",
          "test-workspace/custom-documents/file2.pdf.json",
        ],
        failedFiles: 0,
      };

      // Step 1: Move files to a folder
      const targetFolder = "legal-documents";
      const filesToMove = bulkUploadJobStatus.successfulFiles.map(
        (filePath) => {
          const pathParts = filePath.split("/");
          const fileName = pathParts.pop();
          pathParts.shift(); // Remove workspace slug
          const currentFolder = pathParts.join("/");

          return {
            name: fileName,
            folderName: currentFolder,
          };
        }
      );

      expect(filesToMove).toEqual([
        { name: "file1.pdf.json", folderName: "custom-documents" },
        { name: "file2.pdf.json", folderName: "custom-documents" },
      ]);

      // Simulate successful move
      Document.moveToFolder.mockResolvedValueOnce({ success: true });

      // Update paths after move
      const updatedPaths = bulkUploadJobStatus.successfulFiles.map((file) => {
        const pathParts = file.split("/");
        const fileName = pathParts[pathParts.length - 1];
        const workspaceSlug = pathParts[0];
        return `${workspaceSlug}/${targetFolder}/${fileName}`;
      });

      expect(updatedPaths).toEqual([
        "test-workspace/legal-documents/file1.pdf.json",
        "test-workspace/legal-documents/file2.pdf.json",
      ]);

      // Step 2: Prepare for embedding
      const embeddingPaths = updatedPaths.map((filePath) => {
        const pathParts = filePath.split("/");
        pathParts.shift(); // Remove workspace slug
        return pathParts.join("/");
      });

      expect(embeddingPaths).toEqual([
        "legal-documents/file1.pdf.json",
        "legal-documents/file2.pdf.json",
      ]);

      // Mock embedding response
      Workspace.modifyEmbeddings.mockResolvedValueOnce({
        bulkJob: true,
        jobId: "embed-job-123",
      });

      // Verify the embedding call would be made with correct paths
      const embeddingPayload = {
        adds: embeddingPaths,
        deletes: [],
      };

      expect(embeddingPayload.adds).toHaveLength(2);
      expect(embeddingPayload.adds[0]).toBe("legal-documents/file1.pdf.json");
      expect(embeddingPayload.adds[1]).toBe("legal-documents/file2.pdf.json");
    });

    it("should handle multiple folder moves", async () => {
      let currentPaths = ["test-workspace/custom-documents/file1.pdf.json"];

      // First move: custom-documents -> folder1
      const firstMove = currentPaths.map((filePath) => {
        const pathParts = filePath.split("/");
        const fileName = pathParts.pop();
        pathParts.shift();
        return {
          name: fileName,
          folderName: pathParts.join("/"),
        };
      });

      expect(firstMove[0].folderName).toBe("custom-documents");

      // Update paths after first move
      currentPaths = ["test-workspace/folder1/file1.pdf.json"];

      // Second move: folder1 -> folder2
      const secondMove = currentPaths.map((filePath) => {
        const pathParts = filePath.split("/");
        const fileName = pathParts.pop();
        pathParts.shift();
        return {
          name: fileName,
          folderName: pathParts.join("/"),
        };
      });

      expect(secondMove[0].folderName).toBe("folder1");

      // Final path
      const finalPath = "test-workspace/folder2/file1.pdf.json";

      // Extract for embedding
      const pathParts = finalPath.split("/");
      pathParts.shift();
      const embeddingPath = pathParts.join("/");

      expect(embeddingPath).toBe("folder2/file1.pdf.json");
    });
  });
});
