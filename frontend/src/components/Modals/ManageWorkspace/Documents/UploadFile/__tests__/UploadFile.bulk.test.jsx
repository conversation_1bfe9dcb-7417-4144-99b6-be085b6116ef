// Import shared console warning suppression first
import { suppressConsolePatterns } from "@/utils/testHelpers/consoleWarnings";

import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import "@testing-library/jest-dom";
import UploadFile from "../index";
import Workspace from "@/models/workspace";
import System from "@/models/system";
import { useDropzone } from "react-dropzone";
import { useTranslation } from "react-i18next";
import useUserStore from "@/stores/userStore";

// Mock dependencies
jest.mock("@/models/workspace");
jest.mock("@/models/system");
jest.mock("react-dropzone");
jest.mock("react-i18next");
jest.mock("@/stores/userStore");
jest.mock("@/utils/toast", () => ({
  __esModule: true,
  default: jest.fn(),
}));
jest.mock("uuid", () => ({
  v4: jest.fn(() => `test-uuid-${Math.random().toString(36).substring(7)}`),
}));

// Suppress specific warnings for this test suite
let consoleSuppressions;
beforeAll(() => {
  consoleSuppressions = suppressConsolePatterns([
    "Warning: Each child in a list should have a unique",
    "The current testing environment is not configured to support act",
    "act(...)",
  ]);
});

afterAll(() => {
  if (consoleSuppressions) {
    consoleSuppressions.restore();
  }
});

// Mock components
jest.mock("../FileUploadProgress", () => ({
  __esModule: true,
  default: ({ file, onUploadSuccess }) => (
    <div data-testid="file-upload-progress">
      {file.name}
      <button onClick={() => onUploadSuccess()}>Complete Upload</button>
    </div>
  ),
}));

jest.mock("@/components/utils/BulkUploadTracker", () => ({
  __esModule: true,
  default: ({ isOpen, onClose, onComplete, workspace, jobId }) =>
    isOpen ? (
      <div data-testid="bulk-upload-tracker">
        <h2>Bulk Upload Tracker</h2>
        <p>Job ID: {jobId}</p>
        <p>Workspace: {workspace?.slug}</p>
        <button onClick={() => onComplete && onComplete()}>Complete</button>
        <button onClick={() => onClose && onClose()}>Close</button>
      </div>
    ) : null,
}));

// Get the mocked showToast after the module is mocked
const mockShowToast = require("@/utils/toast").default;

describe("UploadFile - Bulk Upload Integration", () => {
  const mockWorkspace = {
    slug: "test-workspace",
    id: 1,
  };

  const defaultProps = {
    workspace: mockWorkspace,
    fetchKeys: jest.fn(),
    loading: false,
    setLoading: jest.fn(),
    setLoadingMessage: jest.fn(),
    setIsUpload: jest.fn(),
    documentDraftingSelected: false,
    onFileChange: jest.fn(),
  };

  let mockDropzone;

  beforeEach(() => {
    jest.clearAllMocks();

    // Clear the toast mock
    mockShowToast.mockClear();

    // Setup mocks
    useTranslation.mockReturnValue({
      t: (key, defaultValue, options) => {
        if (typeof defaultValue === "string" && options) {
          // Handle interpolation for error messages
          let result = defaultValue;
          Object.keys(options).forEach((optionKey) => {
            result = result.replace(`{{${optionKey}}}`, options[optionKey]);
          });
          return result;
        }
        return defaultValue || key;
      },
    });

    useUserStore.mockImplementation((selector) =>
      selector({ selectedModule: "legal-qa" })
    );

    System.checkDocumentProcessorOnline.mockResolvedValue(true);

    mockDropzone = {
      getRootProps: jest.fn(() => ({ role: "button" })),
      getInputProps: jest.fn(() => ({ type: "file" })),
      acceptedFiles: [],
      rejectedFiles: [],
    };
    useDropzone.mockImplementation((config) => {
      mockDropzone.onDrop = config.onDrop;
      return mockDropzone;
    });

    // Set environment variable for bulk upload threshold
    process.env.VITE_BULK_UPLOAD_THRESHOLD = "50";
  });

  afterEach(() => {
    jest.clearAllTimers();
    jest.useRealTimers();
    jest.restoreAllMocks();
    // Clean up environment variable
    delete process.env.VITE_BULK_UPLOAD_THRESHOLD;
  });

  describe("Bulk Upload Triggering", () => {
    it("should trigger bulk upload when files exceed threshold", async () => {
      render(<UploadFile {...defaultProps} />);

      // Create 50 files (threshold)
      const files = Array(50)
        .fill(null)
        .map(
          (_, i) =>
            new File([`content ${i}`], `file-${i}.pdf`, {
              type: "application/pdf",
            })
        );

      Workspace.startBulkUpload.mockResolvedValue({
        success: true,
        jobId: "bulk-job-123",
      });

      // Trigger file drop
      await act(async () => {
        await waitFor(() => {
          mockDropzone.onDrop(files, []);
        });
      });

      // Should call bulk upload
      await act(async () => {
        await waitFor(() => {
          expect(Workspace.startBulkUpload).toHaveBeenCalledWith(
            "test-workspace",
            expect.any(Array),
            "legal-qa"
          );
        });
      });

      // Should show bulk upload tracker
      expect(screen.getByTestId("bulk-upload-tracker")).toBeInTheDocument();
      expect(screen.getByText("Job ID: bulk-job-123")).toBeInTheDocument();
    });

    it("should not trigger bulk upload when below threshold", async () => {
      render(<UploadFile {...defaultProps} />);

      // Create 49 files (below threshold)
      const files = Array(49)
        .fill(null)
        .map(
          (_, i) =>
            new File([`content ${i}`], `file-${i}.pdf`, {
              type: "application/pdf",
            })
        );

      // Trigger file drop
      await act(async () => {
        await waitFor(() => {
          mockDropzone.onDrop(files, []);
        });
      });

      // Should not call bulk upload
      expect(Workspace.startBulkUpload).not.toHaveBeenCalled();

      // Should show individual file progress components
      expect(screen.getAllByTestId("file-upload-progress")).toHaveLength(49);
    });

    it("should not trigger bulk upload if any files are rejected", async () => {
      render(<UploadFile {...defaultProps} />);

      const acceptedFiles = Array(50)
        .fill(null)
        .map(
          (_, i) =>
            new File([`content ${i}`], `file-${i}.pdf`, {
              type: "application/pdf",
            })
        );

      const rejectedFiles = [
        {
          file: new File(["exe content"], "malware.exe", {
            type: "application/x-msdownload",
          }),
          errors: [
            { code: "file-invalid-type", message: "File type not allowed" },
          ],
        },
      ];

      // Trigger file drop with rejections
      await act(async () => {
        await waitFor(() => {
          mockDropzone.onDrop(acceptedFiles, rejectedFiles);
        });
      });

      // Should not call bulk upload due to rejected files
      expect(Workspace.startBulkUpload).not.toHaveBeenCalled();
    });
  });

  describe("File Conversion for Bulk Upload", () => {
    it("should pass file objects directly without base64 conversion", async () => {
      render(<UploadFile {...defaultProps} />);

      const files = Array(50)
        .fill(null)
        .map((_, i) => {
          const file = new File([`PDF content ${i}`], `document-${i}.pdf`, {
            type: "application/pdf",
          });
          // Add uid property that the component expects
          return Object.assign(file, { uid: `uid-${i}` });
        });

      Workspace.startBulkUpload.mockResolvedValue({
        success: true,
        jobId: "bulk-job-456",
      });

      // Trigger file drop
      await act(async () => {
        await waitFor(() => {
          mockDropzone.onDrop(files, []);
        });
      });

      // Verify files were passed in bulk upload
      await act(async () => {
        await waitFor(() => {
          expect(Workspace.startBulkUpload).toHaveBeenCalledWith(
            "test-workspace",
            expect.any(Array),
            "legal-qa"
          );
        });
      });

      // Verify the array contains the expected number of files
      const callArgs = Workspace.startBulkUpload.mock.calls[0][1];
      expect(callArgs).toHaveLength(50);
      // Files are processed and may be in a different structure than raw File objects
      expect(callArgs[0]).toEqual(
        expect.objectContaining({
          file: expect.any(Object),
        })
      );
    });

    it("should handle different file types in bulk upload", async () => {
      render(<UploadFile {...defaultProps} />);

      const mixedFiles = [
        ...Array(20)
          .fill(null)
          .map(
            (_, i) =>
              new File([`PDF content ${i}`], `pdf-${i}.pdf`, {
                type: "application/pdf",
              })
          ),
        ...Array(20)
          .fill(null)
          .map(
            (_, i) =>
              new File([`DOCX content ${i}`], `doc-${i}.docx`, {
                type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
              })
          ),
        ...Array(10)
          .fill(null)
          .map(
            (_, i) =>
              new File([`Text content ${i}`], `text-${i}.txt`, {
                type: "text/plain",
              })
          ),
      ];

      Workspace.startBulkUpload.mockResolvedValue({
        success: true,
        jobId: "bulk-job-mixed",
      });

      await act(async () => {
        await waitFor(() => {
          mockDropzone.onDrop(mixedFiles, []);
        });
      });

      await act(async () => {
        await waitFor(() => {
          expect(Workspace.startBulkUpload).toHaveBeenCalledWith(
            "test-workspace",
            expect.any(Array),
            "legal-qa"
          );
        });
      });

      const uploadedFiles = Workspace.startBulkUpload.mock.calls[0][1];
      expect(uploadedFiles).toHaveLength(50);

      // Verify different file types are included
      const fileTypes = uploadedFiles.map((f) => f.file.type);
      expect(fileTypes).toContain("application/pdf");
      expect(fileTypes).toContain(
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      );
      expect(fileTypes).toContain("text/plain");
    });
  });

  describe("Bulk Upload Error Handling", () => {
    it("should handle bulk upload start failure", async () => {
      render(<UploadFile {...defaultProps} />);

      const files = Array(50)
        .fill(null)
        .map(
          (_, i) =>
            new File([`content ${i}`], `file-${i}.pdf`, {
              type: "application/pdf",
            })
        );

      Workspace.startBulkUpload.mockResolvedValue({
        success: false,
        error: "Server error: Unable to start bulk upload",
      });

      // Trigger file drop
      await act(async () => {
        mockDropzone.onDrop(files, []);
        // Give time for async operations
        await new Promise((resolve) => setTimeout(resolve, 0));
      });

      // Wait for startBulkUpload to be called
      await waitFor(() => {
        expect(Workspace.startBulkUpload).toHaveBeenCalled();
      });

      // Wait for toast to be called with error message
      await waitFor(
        () => {
          expect(mockShowToast).toHaveBeenCalledWith(
            "Failed to start bulk upload: Server error: Unable to start bulk upload",
            "error"
          );
        },
        { timeout: 3000 }
      );

      // Should not show bulk upload tracker
      expect(
        screen.queryByTestId("bulk-upload-tracker")
      ).not.toBeInTheDocument();
    });

    it("should handle network errors during bulk upload", async () => {
      render(<UploadFile {...defaultProps} />);

      const files = Array(50)
        .fill(null)
        .map(
          (_, i) =>
            new File([`content ${i}`], `file-${i}.pdf`, {
              type: "application/pdf",
            })
        );

      Workspace.startBulkUpload.mockRejectedValue(new Error("Network error"));

      await act(async () => {
        mockDropzone.onDrop(files, []);
        // Give time for async operations
        await new Promise((resolve) => setTimeout(resolve, 0));
      });

      await waitFor(
        () => {
          expect(mockShowToast).toHaveBeenCalledWith(
            "Failed to read files for upload",
            "error"
          );
        },
        { timeout: 3000 }
      );
    });
  });

  describe("Bulk Upload UI State Management", () => {
    it("should show loading state during bulk upload initialization", async () => {
      render(<UploadFile {...defaultProps} />);

      const files = Array(50)
        .fill(null)
        .map(
          (_, i) =>
            new File([`content ${i}`], `file-${i}.pdf`, {
              type: "application/pdf",
            })
        );

      // Delay the response to check loading state
      let resolveUpload;
      Workspace.startBulkUpload.mockImplementation(
        () =>
          new Promise((resolve) => {
            resolveUpload = resolve;
          })
      );

      await act(async () => {
        await waitFor(() => {
          mockDropzone.onDrop(files, []);
        });
      });

      // Check loading state was set
      expect(defaultProps.setLoading).toHaveBeenCalledWith(true);
      expect(defaultProps.setLoadingMessage).toHaveBeenCalledWith(
        "Starting bulk upload..."
      );

      // Resolve the upload
      await act(async () => {
        resolveUpload({ success: true, jobId: "bulk-job-789" });
      });

      await act(async () => {
        await waitFor(() => {
          expect(defaultProps.setLoading).toHaveBeenCalledWith(false);
        });
      });
    });

    it("should show bulk upload tracker when bulk upload starts", async () => {
      render(<UploadFile {...defaultProps} />);

      // Create files that trigger bulk upload
      const bulkFiles = Array(50)
        .fill(null)
        .map(
          (_, i) =>
            new File([`content ${i}`], `bulk-${i}.pdf`, {
              type: "application/pdf",
            })
        );

      Workspace.startBulkUpload.mockResolvedValue({
        success: true,
        jobId: "bulk-job-tracker",
      });

      await act(async () => {
        await waitFor(() => {
          mockDropzone.onDrop(bulkFiles, []);
        });
      });

      // Wait for bulk upload to start
      await act(async () => {
        await waitFor(() => {
          expect(Workspace.startBulkUpload).toHaveBeenCalled();
        });
      });

      // Bulk upload tracker should appear
      await act(async () => {
        await waitFor(() => {
          expect(screen.getByTestId("bulk-upload-tracker")).toBeInTheDocument();
        });
      });
    });
  });

  describe("Bulk Upload Completion Handling", () => {
    it("should refresh workspace and show success on completion", async () => {
      render(<UploadFile {...defaultProps} />);

      const files = Array(50)
        .fill(null)
        .map(
          (_, i) =>
            new File([`content ${i}`], `file-${i}.pdf`, {
              type: "application/pdf",
            })
        );

      Workspace.startBulkUpload.mockResolvedValue({
        success: true,
        jobId: "bulk-job-complete",
      });

      await act(async () => {
        mockDropzone.onDrop(files, []);
        // Give time for async operations
        await new Promise((resolve) => setTimeout(resolve, 0));
      });

      // Wait for the bulk upload tracker to appear
      await waitFor(() => {
        expect(screen.getByTestId("bulk-upload-tracker")).toBeInTheDocument();
      });

      // Simulate completion
      const completeButton = screen.getByRole("button", { name: "Complete" });
      fireEvent.click(completeButton);

      // Wait for the effects to run
      await waitFor(() => {
        expect(defaultProps.fetchKeys).toHaveBeenCalledWith(true, false);
      });

      await waitFor(() => {
        expect(mockShowToast).toHaveBeenCalledWith(
          "Bulk upload completed successfully",
          "success"
        );
      });
    });

    it("should handle bulk upload cancellation", async () => {
      render(<UploadFile {...defaultProps} />);

      const files = Array(50)
        .fill(null)
        .map(
          (_, i) =>
            new File([`content ${i}`], `file-${i}.pdf`, {
              type: "application/pdf",
            })
        );

      Workspace.startBulkUpload.mockResolvedValue({
        success: true,
        jobId: "bulk-job-cancel",
      });

      await act(async () => {
        await waitFor(() => {
          mockDropzone.onDrop(files, []);
        });
      });

      // Close the tracker (simulating cancellation)
      await act(async () => {
        const closeButton = screen.getByRole("button", { name: "Close" });
        fireEvent.click(closeButton);
      });

      await act(async () => {
        await waitFor(() => {
          expect(
            screen.queryByTestId("bulk-upload-tracker")
          ).not.toBeInTheDocument();
        });
      });
    });
  });

  describe("Module-Specific Bulk Upload", () => {
    it("should pass correct module for legal-qa uploads", async () => {
      useUserStore.mockImplementation((selector) =>
        selector({ selectedModule: "legal-qa" })
      );
      render(<UploadFile {...defaultProps} />);

      const files = Array(50)
        .fill(null)
        .map(
          (_, i) =>
            new File([`content ${i}`], `file-${i}.pdf`, {
              type: "application/pdf",
            })
        );

      Workspace.startBulkUpload.mockResolvedValue({
        success: true,
        jobId: "bulk-job-legal",
      });

      await act(async () => {
        await waitFor(() => {
          mockDropzone.onDrop(files, []);
        });
      });

      expect(Workspace.startBulkUpload).toHaveBeenCalledWith(
        "test-workspace",
        expect.any(Array),
        "legal-qa"
      );
    });

    it("should pass correct module for document-drafting uploads", async () => {
      useUserStore.mockImplementation((selector) =>
        selector({ selectedModule: "document-drafting" })
      );
      render(<UploadFile {...defaultProps} documentDraftingSelected={true} />);

      const files = Array(50)
        .fill(null)
        .map(
          (_, i) =>
            new File([`content ${i}`], `file-${i}.pdf`, {
              type: "application/pdf",
            })
        );

      Workspace.startBulkUpload.mockResolvedValue({
        success: true,
        jobId: "bulk-job-drafting",
      });

      await act(async () => {
        await waitFor(() => {
          mockDropzone.onDrop(files, []);
        });
      });

      expect(Workspace.startBulkUpload).toHaveBeenCalledWith(
        "test-workspace",
        expect.any(Array),
        "document-drafting"
      );
    });
  });

  describe("Environment Variable Configuration", () => {
    it("should respect custom bulk upload threshold", async () => {
      // Change threshold to 10
      process.env.VITE_BULK_UPLOAD_THRESHOLD = "10";

      const { unmount } = render(<UploadFile {...defaultProps} />);
      unmount(); // Force re-render to pick up new env var
      render(<UploadFile {...defaultProps} />);

      const files = Array(10)
        .fill(null)
        .map(
          (_, i) =>
            new File([`content ${i}`], `file-${i}.pdf`, {
              type: "application/pdf",
            })
        );

      Workspace.startBulkUpload.mockResolvedValue({
        success: true,
        jobId: "bulk-job-threshold",
      });

      await act(async () => {
        await waitFor(() => {
          mockDropzone.onDrop(files, []);
        });
      });

      // Should trigger bulk upload with just 10 files
      expect(Workspace.startBulkUpload).toHaveBeenCalled();
    });

    it("should use default threshold if env var not set", async () => {
      delete process.env.VITE_BULK_UPLOAD_THRESHOLD;

      render(<UploadFile {...defaultProps} />);

      const files = Array(50)
        .fill(null)
        .map(
          (_, i) =>
            new File([`content ${i}`], `file-${i}.pdf`, {
              type: "application/pdf",
            })
        );

      Workspace.startBulkUpload.mockResolvedValue({
        success: true,
        jobId: "bulk-job-default",
      });

      await act(async () => {
        await waitFor(() => {
          mockDropzone.onDrop(files, []);
        });
      });

      // Should use default threshold of 50
      expect(Workspace.startBulkUpload).toHaveBeenCalled();
    });
  });

  describe("Edge Cases", () => {
    it("should handle empty file uploads", async () => {
      render(<UploadFile {...defaultProps} />);

      const emptyFiles = Array(50)
        .fill(null)
        .map(
          (_, i) => new File([], `empty-${i}.pdf`, { type: "application/pdf" })
        );

      Workspace.startBulkUpload.mockResolvedValue({
        success: true,
        jobId: "bulk-job-empty",
      });

      await act(async () => {
        await waitFor(() => {
          mockDropzone.onDrop(emptyFiles, []);
        });
      });

      expect(Workspace.startBulkUpload).toHaveBeenCalled();
    });

    it("should handle files with special characters in names", async () => {
      render(<UploadFile {...defaultProps} />);

      const specialFiles = Array(50)
        .fill(null)
        .map(
          (_, i) =>
            new File([`content ${i}`], `spëcîál chàrs (${i}) [test].pdf`, {
              type: "application/pdf",
            })
        );

      Workspace.startBulkUpload.mockResolvedValue({
        success: true,
        jobId: "bulk-job-special",
      });

      await act(async () => {
        await waitFor(() => {
          mockDropzone.onDrop(specialFiles, []);
        });
      });

      const uploadedFiles = Workspace.startBulkUpload.mock.calls[0][1];
      expect(uploadedFiles[0].file.name).toBe("spëcîál chàrs (0) [test].pdf");
    });

    it("should handle very large individual files in bulk", async () => {
      render(<UploadFile {...defaultProps} />);

      // Create enough files to trigger bulk upload (50+ files)
      const largeFiles = Array(50)
        .fill(null)
        .map((_, i) => {
          const file = new File(
            ["x".repeat(1024)], // Smaller content
            `large-${i}.pdf`,
            {
              type: "application/pdf",
            }
          );
          // Override size property
          Object.defineProperty(file, "size", {
            value: 10 * 1024 * 1024, // 10MB instead of 100MB
            writable: false,
          });
          return file;
        });

      Workspace.startBulkUpload.mockResolvedValue({
        success: true,
        jobId: "bulk-job-large",
      });

      await act(async () => {
        await waitFor(() => {
          mockDropzone.onDrop(largeFiles, []);
        });
      });

      expect(Workspace.startBulkUpload).toHaveBeenCalled();
    }, 10000);
  });
});
