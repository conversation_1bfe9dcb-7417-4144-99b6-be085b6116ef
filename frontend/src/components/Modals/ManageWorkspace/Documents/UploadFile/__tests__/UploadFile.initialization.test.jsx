/**
 * Workspace Upload Modal Initialization Test Suite
 *
 * Tests the complete initialization flow of the workspace upload modal,
 * including FileRow and DocumentRow components, and all required endpoint calls.
 */

// Import shared console warning suppression first
import { suppressConsolePatterns } from "@/utils/testHelpers/consoleWarnings";

import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import "@testing-library/jest-dom";
import UploadFile from "../index";
import Workspace from "@/models/workspace";
import System from "@/models/system";
import { useDropzone } from "react-dropzone";
import { useTranslation } from "react-i18next";
import useUserStore from "@/stores/userStore";
import { useDocumentDraftingEnabled } from "@/stores/settingsStore";

// Mock all dependencies
jest.mock("@/models/workspace");
jest.mock("@/models/system");
jest.mock("react-dropzone");
jest.mock("react-i18next");
jest.mock("@/stores/userStore");
jest.mock("@/stores/settingsStore");
jest.mock("@/utils/toast");
jest.mock("uuid", () => ({
  v4: jest.fn(() => `test-uuid-${Math.random().toString(36).substring(7)}`),
}));

// Suppress specific warnings for this test suite
let consoleSuppressions;
beforeAll(() => {
  consoleSuppressions = suppressConsolePatterns([
    "Warning: Each child in a list should have a unique",
    "The current testing environment is not configured to support act",
    "act(...)",
  ]);
});

afterAll(() => {
  if (consoleSuppressions) {
    consoleSuppressions.restore();
  }
});

// Mock child components
jest.mock("../FileUploadProgress", () => ({
  __esModule: true,
  default: ({ file, onUploadSuccess, onUploadError, workspace }) => (
    <div data-testid="file-upload-progress" data-filename={file.name}>
      <div>Uploading: {file.name}</div>
      <div>Workspace: {workspace?.slug}</div>
      <button onClick={() => onUploadSuccess()} data-testid="complete-upload">
        Complete Upload
      </button>
      <button
        onClick={() => onUploadError("Test error")}
        data-testid="error-upload"
      >
        Error Upload
      </button>
    </div>
  ),
}));

jest.mock("@/components/utils/BulkUploadTracker", () => ({
  __esModule: true,
  default: ({ isOpen, onClose, onComplete, workspace, jobId }) =>
    isOpen ? (
      <div data-testid="bulk-upload-tracker">
        <h2>Bulk Upload Tracker</h2>
        <p>Job ID: {jobId}</p>
        <p>Workspace: {workspace?.slug}</p>
        <button onClick={onComplete} data-testid="tracker-complete">
          Complete
        </button>
        <button onClick={onClose} data-testid="tracker-close">
          Close
        </button>
      </div>
    ) : null,
}));

jest.mock("@/components/utils/FolderSelectionModal", () => ({
  __esModule: true,
  default: ({ isOpen, onClose, onConfirm, workspace }) =>
    isOpen ? (
      <div data-testid="folder-selection-modal">
        <h2>Select Folder</h2>
        <p>Workspace: {workspace?.slug}</p>
        <button
          onClick={() => onConfirm("custom-documents")}
          data-testid="select-folder"
        >
          Select Custom Documents
        </button>
        <button onClick={onClose} data-testid="close-folder-modal">
          Close
        </button>
      </div>
    ) : null,
}));

jest.mock("../../BulkProcessingProgress", () => ({
  __esModule: true,
  default: ({ isVisible, progress, onClose }) =>
    isVisible ? (
      <div data-testid="bulk-processing-progress">
        <div>Progress: {progress}%</div>
        <button onClick={onClose} data-testid="close-progress">
          Close
        </button>
      </div>
    ) : null,
}));

// Mock File constructor for consistent testing
global.File = class MockFile {
  constructor(_data, filename, options = {}) {
    this.name = filename;
    this.size = options.size || 1024;
    this.type = options.type || "application/pdf";
    this.lastModified = Date.now();
  }
};

describe("UploadFile Modal Initialization", () => {
  // Mock workspace object
  const mockWorkspace = {
    id: "test-workspace-id",
    slug: "test-workspace-cleanup",
    name: "Test Workspace Cleanup",
  };

  // Mock user data
  const mockUser = {
    id: "test-user-id",
    username: "testuser",
    role: "default",
  };

  // Test cleanup utilities
  const testFiles = [];
  const cleanupTestFiles = () => {
    testFiles.forEach((file) => {
      try {
        if (file && typeof file.cleanup === "function") {
          file.cleanup();
        }
      } catch (error) {
        console.warn("Cleanup failed for test file:", error);
      }
    });
    testFiles.length = 0;
  };

  // Mock props
  const mockProps = {
    fetchKeys: jest.fn(),
    loading: false,
    setLoading: jest.fn(),
    setLoadingMessage: jest.fn(),
    setIsUpload: jest.fn(),
    documentDraftingSelected: false,
    onFileChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock translation hook
    useTranslation.mockReturnValue({
      t: (key) => key,
      i18n: { language: "en" },
    });

    // Mock user store
    useUserStore.mockReturnValue({
      user: mockUser,
      selectedModule: "default",
    });

    // Mock document drafting setting
    useDocumentDraftingEnabled.mockReturnValue({ isDocumentDrafting: false });

    // Mock dropzone
    useDropzone.mockReturnValue({
      getRootProps: () => ({
        "data-testid": "dropzone",
      }),
      getInputProps: () => ({
        "data-testid": "file-input",
      }),
      isDragActive: false,
      acceptedFiles: [],
      fileRejections: [],
    });

    // Mock System API calls - only the ones actually used
    System.checkDocumentProcessorOnline = jest.fn().mockResolvedValue(true);

    // Mock Workspace API calls
    Workspace.uploadLink = jest.fn().mockResolvedValue({
      response: { ok: true },
      data: { success: true },
    });

    Workspace.getTokenCount = jest.fn().mockResolvedValue({
      tokenCount: 0,
      promptLimit: 1000000,
    });

    Workspace.startBulkUpload = jest.fn().mockResolvedValue({
      success: true,
      jobId: "bulk-upload-123",
    });

    Workspace.modifyEmbeddings = jest.fn().mockResolvedValue({
      success: true,
      error: null,
    });

    // Mock process.env
    process.env.VITE_BULK_UPLOAD_THRESHOLD = "20";
  });

  afterEach(() => {
    jest.restoreAllMocks();
    cleanupTestFiles();
  });

  afterAll(() => {
    // Final cleanup
    cleanupTestFiles();
  });

  describe("Modal Initialization", () => {
    it("should initialize upload modal with all required components", async () => {
      await act(async () => {
        render(<UploadFile workspace={mockWorkspace} {...mockProps} />);
      });

      // Check modal container exists
      expect(screen.getByTestId("dropzone")).toBeInTheDocument();
      expect(screen.getByTestId("file-input")).toBeInTheDocument();

      // Check initial API calls are made
      await waitFor(() => {
        expect(System.checkDocumentProcessorOnline).toHaveBeenCalled();
      });
    });

    it("should load existing documents and display document rows", async () => {
      await act(async () => {
        render(
          <UploadFile
            workspace={mockWorkspace}
            {...mockProps}
            documentDraftingSelected={true}
          />
        );
      });

      await waitFor(() => {
        expect(Workspace.getTokenCount).toHaveBeenCalledWith(
          mockWorkspace.slug
        );
      });

      // Should load token count for document drafting
      expect(Workspace.getTokenCount).toHaveBeenCalled();
    });

    it("should check upload permissions and token limits on initialization", async () => {
      await act(async () => {
        render(
          <UploadFile
            workspace={mockWorkspace}
            {...mockProps}
            documentDraftingSelected={true}
          />
        );
      });

      await waitFor(() => {
        expect(Workspace.getTokenCount).toHaveBeenCalled();
      });

      // Verify token check endpoint call
      expect(Workspace.getTokenCount).toHaveBeenCalledWith(mockWorkspace.slug);
    });

    it("should handle document processor online check", async () => {
      System.checkDocumentProcessorOnline.mockResolvedValue(true);

      await act(async () => {
        render(<UploadFile workspace={mockWorkspace} {...mockProps} />);
      });

      await waitFor(() => {
        expect(System.checkDocumentProcessorOnline).toHaveBeenCalled();
      });
    });
  });

  describe("File Drop and Selection", () => {
    it("should handle file drop initialization", async () => {
      await act(async () => {
        render(<UploadFile workspace={mockWorkspace} {...mockProps} />);
      });

      // Verify dropzone is configured with onDrop and disabled state
      expect(useDropzone).toHaveBeenCalledWith(
        expect.objectContaining({
          onDrop: expect.any(Function),
          disabled: expect.any(Boolean),
        })
      );
    });

    it("should initialize file rows when files are selected", async () => {
      const uploadTestFiles = [
        new File(["content1"], "cleanup-test1.pdf", {
          type: "application/pdf",
        }),
        new File(["content2"], "cleanup-test2.docx", {
          type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        }),
      ];

      // First render the component
      let dropzoneOnDrop;
      useDropzone.mockImplementation((options) => {
        dropzoneOnDrop = options.onDrop;
        return {
          getRootProps: () => ({
            "data-testid": "dropzone",
          }),
          getInputProps: () => ({
            "data-testid": "file-input",
          }),
          isDragActive: false,
          acceptedFiles: [],
          fileRejections: [],
        };
      });

      await act(async () => {
        render(<UploadFile workspace={mockWorkspace} {...mockProps} />);
      });

      // Simulate dropping files
      await act(async () => {
        dropzoneOnDrop(uploadTestFiles, []);
      });

      // Should create file upload progress components for each file
      await waitFor(() => {
        const progressElements = screen.getAllByTestId("file-upload-progress");
        expect(progressElements).toHaveLength(2);
      });
    });

    it("should handle file rejection scenarios", async () => {
      const rejectedFiles = [
        {
          file: new File(["content"], "rejected.exe", {
            type: "application/x-executable",
          }),
          errors: [
            { code: "file-invalid-type", message: "File type not allowed" },
          ],
        },
      ];

      useDropzone.mockReturnValue({
        getRootProps: () => ({
          "data-testid": "dropzone",
        }),
        getInputProps: () => ({
          "data-testid": "file-input",
        }),
        isDragActive: false,
        acceptedFiles: [],
        fileRejections: rejectedFiles,
      });

      await act(async () => {
        render(
          <UploadFile
            workspace={mockWorkspace}
            open={true}
            onClose={jest.fn()}
          />
        );
      });

      // Should handle rejected files appropriately
      expect(useDropzone).toHaveBeenCalled();
    });
  });

  describe("File Upload Progress Integration", () => {
    it("should initialize FileUploadProgress components with correct props", async () => {
      const testFile = new File(["content"], "test.pdf", {
        type: "application/pdf",
      });

      let dropzoneOnDrop;
      useDropzone.mockImplementation((options) => {
        dropzoneOnDrop = options.onDrop;
        return {
          getRootProps: () => ({
            "data-testid": "dropzone",
          }),
          getInputProps: () => ({
            "data-testid": "file-input",
          }),
          isDragActive: false,
          acceptedFiles: [],
          fileRejections: [],
        };
      });

      await act(async () => {
        render(<UploadFile workspace={mockWorkspace} {...mockProps} />);
      });

      // Simulate dropping files
      await act(async () => {
        dropzoneOnDrop([testFile], []);
      });

      await waitFor(() => {
        const progressComponent = screen.getByTestId("file-upload-progress");
        expect(progressComponent).toBeInTheDocument();
        expect(progressComponent).toHaveAttribute("data-filename", "test.pdf");
      });
    });

    it("should handle file upload completion", async () => {
      const testFile = new File(["content"], "test.pdf", {
        type: "application/pdf",
      });

      let dropzoneOnDrop;
      useDropzone.mockImplementation((options) => {
        dropzoneOnDrop = options.onDrop;
        return {
          getRootProps: () => ({
            "data-testid": "dropzone",
          }),
          getInputProps: () => ({
            "data-testid": "file-input",
          }),
          isDragActive: false,
          acceptedFiles: [],
          fileRejections: [],
        };
      });

      await act(async () => {
        render(<UploadFile workspace={mockWorkspace} {...mockProps} />);
      });

      // Simulate dropping files
      await act(async () => {
        dropzoneOnDrop([testFile], []);
      });

      // Wait for file upload component to render
      await waitFor(() => {
        expect(screen.getByTestId("file-upload-progress")).toBeInTheDocument();
      });

      // Simulate upload completion
      const completeButton = screen.getByTestId("complete-upload");

      await act(async () => {
        fireEvent.click(completeButton);
      });

      // Should trigger fetchKeys to refresh the workspace
      await waitFor(() => {
        expect(mockProps.fetchKeys).toHaveBeenCalledWith(true, false);
      });
    });

    it("should handle file upload errors", async () => {
      const testFile = new File(["content"], "test.pdf", {
        type: "application/pdf",
      });

      let dropzoneOnDrop;
      useDropzone.mockImplementation((options) => {
        dropzoneOnDrop = options.onDrop;
        return {
          getRootProps: () => ({
            "data-testid": "dropzone",
          }),
          getInputProps: () => ({
            "data-testid": "file-input",
          }),
          isDragActive: false,
          acceptedFiles: [],
          fileRejections: [],
        };
      });

      await act(async () => {
        render(<UploadFile workspace={mockWorkspace} {...mockProps} />);
      });

      // Simulate dropping files
      await act(async () => {
        dropzoneOnDrop([testFile], []);
      });

      // Wait for file upload component to render
      await waitFor(() => {
        expect(screen.getByTestId("file-upload-progress")).toBeInTheDocument();
      });

      // Simulate upload error
      const errorButton = screen.getByTestId("error-upload");

      await act(async () => {
        fireEvent.click(errorButton);
      });

      // The error handling is done in the mocked FileUploadProgress component
      // which calls onUploadError. The component should track failed uploads
      // but toast is shown by the FileUploadProgress component itself
    });
  });

  describe("Bulk Upload Integration", () => {
    it("should initialize bulk upload tracker when needed", async () => {
      // Mock a scenario that triggers bulk upload
      const manyFiles = Array.from(
        { length: 10 },
        (_, i) =>
          new File([`content${i}`], `file${i}.pdf`, { type: "application/pdf" })
      );

      useDropzone.mockReturnValue({
        getRootProps: () => ({
          "data-testid": "dropzone",
        }),
        getInputProps: () => ({
          "data-testid": "file-input",
        }),
        isDragActive: false,
        acceptedFiles: manyFiles,
        fileRejections: [],
      });

      await act(async () => {
        render(
          <UploadFile
            workspace={mockWorkspace}
            open={true}
            onClose={jest.fn()}
          />
        );
      });

      // Bulk upload might be triggered for many files
      // This depends on the component's bulk upload logic
    });

    it("should handle folder selection modal", async () => {
      await act(async () => {
        render(
          <UploadFile
            workspace={mockWorkspace}
            open={true}
            onClose={jest.fn()}
          />
        );
      });

      // The folder selection modal should be available for initialization
      // when bulk upload or folder-based upload is triggered
    });
  });

  describe("Document Rows and Workspace State", () => {
    it("should load and display existing workspace documents", async () => {
      await act(async () => {
        render(
          <UploadFile
            workspace={mockWorkspace}
            {...mockProps}
            documentDraftingSelected={true}
          />
        );
      });

      await waitFor(() => {
        expect(Workspace.getTokenCount).toHaveBeenCalledWith(
          mockWorkspace.slug
        );
      });

      // Verify token count is loaded
      expect(Workspace.getTokenCount).toHaveBeenCalled();
    });

    it("should refresh document list after successful uploads", async () => {
      const testFile = new File(["content"], "new-doc.pdf", {
        type: "application/pdf",
      });

      let dropzoneOnDrop;
      useDropzone.mockImplementation((options) => {
        dropzoneOnDrop = options.onDrop;
        return {
          getRootProps: () => ({
            "data-testid": "dropzone",
          }),
          getInputProps: () => ({
            "data-testid": "file-input",
          }),
          isDragActive: false,
          acceptedFiles: [],
          fileRejections: [],
        };
      });

      await act(async () => {
        render(<UploadFile workspace={mockWorkspace} {...mockProps} />);
      });

      // Simulate dropping files
      await act(async () => {
        dropzoneOnDrop([testFile], []);
      });

      // Wait for file upload component to render
      await waitFor(() => {
        expect(screen.getByTestId("file-upload-progress")).toBeInTheDocument();
      });

      // Complete an upload
      const completeButton = screen.getByTestId("complete-upload");

      await act(async () => {
        fireEvent.click(completeButton);
      });

      // Should call fetchKeys to refresh
      await waitFor(() => {
        expect(mockProps.fetchKeys).toHaveBeenCalled();
      });
    });
  });

  describe("Endpoint Integration", () => {
    it("should make all required API calls during initialization", async () => {
      await act(async () => {
        render(
          <UploadFile
            workspace={mockWorkspace}
            {...mockProps}
            documentDraftingSelected={true}
          />
        );
      });

      // Verify all endpoint calls
      await waitFor(() => {
        expect(System.checkDocumentProcessorOnline).toHaveBeenCalled();
        expect(Workspace.getTokenCount).toHaveBeenCalledWith(
          mockWorkspace.slug
        );
      });
    });

    it("should handle API call failures gracefully", async () => {
      // Mock API failures
      System.checkDocumentProcessorOnline.mockRejectedValue(
        new Error("Processor check failed")
      );
      Workspace.getTokenCount.mockRejectedValue(
        new Error("Token count failed")
      );

      await act(async () => {
        render(
          <UploadFile
            workspace={mockWorkspace}
            {...mockProps}
            documentDraftingSelected={true}
          />
        );
      });

      // Should still render the modal despite API failures
      expect(screen.getByTestId("dropzone")).toBeInTheDocument();
    });

    it("should handle token limit restrictions", async () => {
      Workspace.getTokenCount.mockResolvedValue({
        tokenCount: 2000000,
        promptLimit: 2000000,
      });

      await act(async () => {
        render(
          <UploadFile
            workspace={mockWorkspace}
            {...mockProps}
            documentDraftingSelected={true}
          />
        );
      });

      await waitFor(() => {
        expect(Workspace.getTokenCount).toHaveBeenCalled();
      });

      // Should handle token limits appropriately
      expect(mockProps.onFileChange).toHaveBeenCalledWith({
        tokenCount: 2000000,
        promptLimit: 2000000,
      });
    });
  });

  describe("User Store Integration", () => {
    it("should access user data from store for upload attribution", async () => {
      await act(async () => {
        render(<UploadFile workspace={mockWorkspace} {...mockProps} />);
      });

      // Should access user store
      expect(useUserStore).toHaveBeenCalled();
    });

    it("should handle different user roles and permissions", async () => {
      // Mock admin user
      useUserStore.mockReturnValue({
        user: { ...mockUser, role: "admin" },
        selectedModule: "admin",
      });

      await act(async () => {
        render(<UploadFile workspace={mockWorkspace} {...mockProps} />);
      });

      // Should handle admin-specific features
      expect(useUserStore).toHaveBeenCalled();
    });
  });

  describe("Settings Store Integration", () => {
    it("should check document drafting settings", async () => {
      await act(async () => {
        render(<UploadFile workspace={mockWorkspace} {...mockProps} />);
      });

      // Should check document drafting setting
      expect(useDocumentDraftingEnabled).toHaveBeenCalled();
    });

    it("should adapt UI based on document drafting setting", async () => {
      useDocumentDraftingEnabled.mockReturnValue({ isDocumentDrafting: true });

      await act(async () => {
        render(
          <UploadFile
            workspace={mockWorkspace}
            {...mockProps}
            documentDraftingSelected={true}
          />
        );
      });

      // Should show document drafting features when enabled
      expect(useDocumentDraftingEnabled).toHaveBeenCalled();
      expect(Workspace.getTokenCount).toHaveBeenCalled();
    });
  });
});
