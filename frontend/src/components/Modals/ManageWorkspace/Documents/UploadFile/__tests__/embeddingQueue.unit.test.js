import Workspace from "@/models/workspace";

// Mock dependencies
jest.mock("@/models/workspace");
jest.mock("@/utils/toast");

// Constants from the implementation
const EMBEDDING_BATCH_SIZE = 50;
const EMBEDDING_BATCH_DELAY = 2000;
const MAX_CONCURRENT_BATCHES = 2;

describe("Embedding Queue Unit Tests", () => {
  let originalConsoleError;
  let originalSetTimeout;
  let pendingTimers = [];

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    // Mock console.error to suppress expected error logs during tests
    originalConsoleError = console.error;
    console.error = jest.fn();
    // Store original setTimeout
    originalSetTimeout = global.setTimeout;
    pendingTimers = [];
  });

  afterEach(() => {
    // Restore console.error
    console.error = originalConsoleError;
    // Clear any pending timers
    pendingTimers.forEach((timer) => clearTimeout(timer));
    pendingTimers = [];
    // Use real timers again
    jest.useRealTimers();
    // Always restore the original setTimeout
    global.setTimeout = originalSetTimeout;
    // Clear any remaining timers
    jest.clearAllTimers();
  });

  // Helper function to simulate the batch processing logic
  const processEmbeddingBatch = async (
    batch,
    batchIndex,
    totalBatches,
    workspaceSlug
  ) => {
    try {
      const response = await Workspace.modifyEmbeddings(workspaceSlug, {
        adds: batch,
        deletes: [],
      });

      return response;
    } catch (error) {
      // Using the mocked console.error for testing
      console.error(`Error processing batch ${batchIndex + 1}:`, error);
      throw error;
    }
  };

  // Helper function to simulate the queue processing logic
  const processEmbeddingQueue = async (fileNames, workspaceSlug) => {
    const batches = [];

    // Split files into batches
    for (let i = 0; i < fileNames.length; i += EMBEDDING_BATCH_SIZE) {
      batches.push(fileNames.slice(i, i + EMBEDDING_BATCH_SIZE));
    }

    const results = [];
    const errors = [];

    // Process batches with concurrency control
    for (let i = 0; i < batches.length; i += MAX_CONCURRENT_BATCHES) {
      const concurrentBatches = batches.slice(i, i + MAX_CONCURRENT_BATCHES);

      try {
        // Process concurrent batches
        const batchPromises = concurrentBatches.map((batch, index) =>
          processEmbeddingBatch(batch, i + index, batches.length, workspaceSlug)
        );

        const batchResults = await Promise.allSettled(batchPromises);

        // Collect results and errors
        batchResults.forEach((result, index) => {
          if (result.status === "fulfilled") {
            results.push(result.value);
          } else {
            errors.push({
              batch: i + index,
              error: result.reason,
            });
          }
        });

        // Add delay between batch groups to prevent rate limiting
        if (i + MAX_CONCURRENT_BATCHES < batches.length) {
          await new Promise((resolve) => {
            const timer = setTimeout(resolve, EMBEDDING_BATCH_DELAY);
            pendingTimers.push(timer);
          });
        }
      } catch (error) {
        console.error("Error processing concurrent batches:", error);
        errors.push({ batch: i, error });
      }
    }

    return { results, errors, totalBatches: batches.length };
  };

  describe("Batch Splitting", () => {
    it("should split files into correct batch sizes", () => {
      const files = Array.from({ length: 120 }, (_, i) => `file${i}.json`);
      const batches = [];

      for (let i = 0; i < files.length; i += EMBEDDING_BATCH_SIZE) {
        batches.push(files.slice(i, i + EMBEDDING_BATCH_SIZE));
      }

      expect(batches.length).toBe(3); // 120 / 50 = 2.4, so 3 batches
      expect(batches[0].length).toBe(50);
      expect(batches[1].length).toBe(50);
      expect(batches[2].length).toBe(20);
    });

    it("should handle files less than batch size", () => {
      const files = Array.from({ length: 30 }, (_, i) => `file${i}.json`);
      const batches = [];

      for (let i = 0; i < files.length; i += EMBEDDING_BATCH_SIZE) {
        batches.push(files.slice(i, i + EMBEDDING_BATCH_SIZE));
      }

      expect(batches.length).toBe(1);
      expect(batches[0].length).toBe(30);
    });

    it("should handle empty file list", () => {
      const files = [];
      const batches = [];

      for (let i = 0; i < files.length; i += EMBEDDING_BATCH_SIZE) {
        batches.push(files.slice(i, i + EMBEDDING_BATCH_SIZE));
      }

      expect(batches.length).toBe(0);
    });
  });

  describe("Batch Processing", () => {
    it("should process single batch successfully", async () => {
      Workspace.modifyEmbeddings.mockResolvedValue({ success: true });

      const batch = ["file1.json", "file2.json", "file3.json"];
      const result = await processEmbeddingBatch(batch, 0, 1, "test-workspace");

      expect(result).toEqual({ success: true });
      expect(Workspace.modifyEmbeddings).toHaveBeenCalledWith(
        "test-workspace",
        {
          adds: batch,
          deletes: [],
        }
      );
    });

    it("should handle batch processing error", async () => {
      Workspace.modifyEmbeddings.mockRejectedValue(new Error("API Error"));

      const batch = ["file1.json"];

      await expect(
        processEmbeddingBatch(batch, 0, 1, "test-workspace")
      ).rejects.toThrow("API Error");
    });
  });

  describe("Queue Processing", () => {
    it("should process multiple batches in queue", async () => {
      Workspace.modifyEmbeddings.mockResolvedValue({ success: true });

      const files = Array.from({ length: 75 }, (_, i) => `file${i}.json`);
      const result = await processEmbeddingQueue(files, "test-workspace");

      expect(result.totalBatches).toBe(2); // 75 / 50 = 1.5, so 2 batches
      expect(result.results.length).toBe(2);
      expect(result.errors.length).toBe(0);

      // Verify both batches were called
      expect(Workspace.modifyEmbeddings).toHaveBeenCalledTimes(2);

      // First batch should have 50 files
      expect(Workspace.modifyEmbeddings).toHaveBeenNthCalledWith(
        1,
        "test-workspace",
        {
          adds: expect.arrayContaining([
            expect.stringMatching(/^file\d+\.json$/),
          ]),
          deletes: [],
        }
      );
    });

    it("should handle mixed success and failure", async () => {
      // First call succeeds, second fails
      Workspace.modifyEmbeddings
        .mockResolvedValueOnce({ success: true })
        .mockRejectedValueOnce(new Error("Rate limit"));

      const files = Array.from({ length: 100 }, (_, i) => `file${i}.json`);
      const result = await processEmbeddingQueue(files, "test-workspace");

      expect(result.totalBatches).toBe(2);
      expect(result.results.length).toBe(1);
      expect(result.errors.length).toBe(1);
      expect(result.errors[0].error.message).toBe("Rate limit");
    });

    it("should respect concurrency limit", async () => {
      let concurrentCalls = 0;
      let maxConcurrent = 0;

      Workspace.modifyEmbeddings.mockImplementation(async () => {
        concurrentCalls++;
        maxConcurrent = Math.max(maxConcurrent, concurrentCalls);

        // Simulate processing time
        await Promise.resolve();

        concurrentCalls--;
        return { success: true };
      });

      const files = Array.from({ length: 150 }, (_, i) => `file${i}.json`);

      // Start the processing
      const queuePromise = processEmbeddingQueue(files, "test-workspace");

      // Advance timers to process all batches
      await jest.runAllTimersAsync();

      // Wait for the queue to complete
      await queuePromise;

      // Should never exceed MAX_CONCURRENT_BATCHES
      expect(maxConcurrent).toBeLessThanOrEqual(MAX_CONCURRENT_BATCHES);
    });
  });

  describe("Rate Limiting", () => {
    it("should add delay between batch groups", async () => {
      // Mock setTimeout to track delays
      const delays = [];

      global.setTimeout = jest.fn((callback, delay) => {
        delays.push(delay);
        // Execute callback immediately for testing
        callback();
      });

      Workspace.modifyEmbeddings.mockResolvedValue({ success: true });

      const files = Array.from({ length: 150 }, (_, i) => `file${i}.json`);
      await processEmbeddingQueue(files, "test-workspace");

      // Should have been called 3 times (150 / 50 = 3)
      expect(Workspace.modifyEmbeddings).toHaveBeenCalledTimes(3);

      // Should have added delays between batch groups
      // With 3 batches and MAX_CONCURRENT_BATCHES=2, there should be 1 delay
      const delayCount = delays.filter(
        (d) => d === EMBEDDING_BATCH_DELAY
      ).length;
      expect(delayCount).toBeGreaterThan(0);
    });
  });

  describe("File Path Processing", () => {
    it("should correctly extract file paths", () => {
      const filePaths = [
        "workspace-slug/custom-documents/file1.json",
        "workspace-slug/folder/file2.json",
        "file3.json",
      ];

      const processed = filePaths.map((filePath) => {
        const pathParts = filePath.split("/");
        if (pathParts.length > 1) {
          pathParts.shift(); // Remove workspace slug
          return pathParts.join("/");
        }
        return filePath;
      });

      expect(processed).toEqual([
        "custom-documents/file1.json",
        "folder/file2.json",
        "file3.json",
      ]);
    });
  });

  describe("Edge Cases", () => {
    it("should handle very large file sets", async () => {
      // Mock setTimeout to avoid real delays
      global.setTimeout = jest.fn((callback) => {
        callback();
      });

      Workspace.modifyEmbeddings.mockResolvedValue({ success: true });

      const files = Array.from({ length: 1000 }, (_, i) => `file${i}.json`);
      const result = await processEmbeddingQueue(files, "test-workspace");

      expect(result.totalBatches).toBe(20); // 1000 / 50 = 20
      expect(result.results.length).toBe(20);
      expect(Workspace.modifyEmbeddings).toHaveBeenCalledTimes(20);
    });

    it("should handle exactly batch size files", async () => {
      Workspace.modifyEmbeddings.mockResolvedValue({ success: true });

      const files = Array.from({ length: 50 }, (_, i) => `file${i}.json`);
      const result = await processEmbeddingQueue(files, "test-workspace");

      expect(result.totalBatches).toBe(1);
      expect(result.results.length).toBe(1);
      expect(Workspace.modifyEmbeddings).toHaveBeenCalledTimes(1);
    });

    it("should continue processing after errors", async () => {
      // Mock setTimeout to avoid real delays
      global.setTimeout = jest.fn((callback) => {
        callback();
      });

      // Fail first batch, succeed rest
      Workspace.modifyEmbeddings
        .mockRejectedValueOnce(new Error("Error 1"))
        .mockResolvedValue({ success: true });

      const files = Array.from({ length: 150 }, (_, i) => `file${i}.json`);
      const result = await processEmbeddingQueue(files, "test-workspace");

      expect(result.totalBatches).toBe(3);
      expect(result.results.length).toBe(2); // 2 successful
      expect(result.errors.length).toBe(1); // 1 failed
    });
  });
});
