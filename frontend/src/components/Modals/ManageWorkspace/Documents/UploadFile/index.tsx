import { CloudArrowUp } from "@phosphor-icons/react";
import { useEffect, useState, useCallback } from "react";
import ReactDOM from "react-dom";
import showToast from "../../../../../utils/toast";
import System from "../../../../../models/system";
import { useDropzone, FileRejection, DropzoneOptions } from "react-dropzone";
import { v4 as uuidv4 } from "uuid";
import FileUploadProgress from "./FileUploadProgress";
import Workspace from "../../../../../models/workspace";
import { useTranslation } from "react-i18next";
import { useDocumentDraftingEnabled } from "@/stores/settingsStore";
import Input from "@/components/ui/Input";
import { Button } from "@/components/Button";
import BulkUploadTracker from "@/components/utils/BulkUploadTracker";
import FolderSelectionModal from "@/components/utils/FolderSelectionModal";
import BulkProcessingProgress from "../BulkProcessingProgress";
import useUserStore from "@/stores/userStore";
import { numberWithCommas } from "@/utils/numbers";

// Rate limiting configuration for embeddings
const EMBEDDING_BATCH_SIZE = 50; // Max files per batch
const EMBEDDING_BATCH_DELAY = 2000; // Delay between batches (ms)
const MAX_CONCURRENT_BATCHES = 2; // Max concurrent embedding requests

interface TokenStats {
  tokenCount: number;
  promptLimit: number;
}

interface FailedUpload {
  fileName: string;
  error: string;
}

interface FileWithMetadata {
  uid: string;
  file: File;
  rejected?: boolean;
  reason?: string;
}

interface WorkspaceType {
  slug: string;
  name?: string;
  id?: string;
  type?: string;
}

interface BulkUploadJobStatus {
  status: string;
  successfulFiles: string[];
  failedFiles: string[];
  filesMoved?: boolean;
  movedToFolder?: string;
}

interface EmbeddingQueueProgress {
  current: number;
  total: number;
  processed: number;
}

interface UploadFileProps {
  workspace: WorkspaceType;
  fetchKeys: (
    refresh?: boolean,
    documentDrafting?: boolean
  ) => void | Promise<void>;
  loading: boolean;
  setLoading: (loading: boolean) => void;
  setLoadingMessage: (message: string) => void;
  setIsUpload: (isUpload: boolean) => void;
  documentDraftingSelected: boolean;
  onFileChange?: (data: TokenStats | null) => void;
}

export default function UploadFile({
  workspace,
  fetchKeys,
  loading: _loading,
  setLoading,
  setLoadingMessage,
  setIsUpload,
  documentDraftingSelected,
  onFileChange,
}: UploadFileProps) {
  const { isDocumentDrafting } = useDocumentDraftingEnabled();
  const [ready, setReady] = useState<boolean>(false);
  const [files, setFiles] = useState<FileWithMetadata[]>([]);
  const [fetchingUrl, setFetchingUrl] = useState<boolean>(false);
  const [failedUploads, setFailedUploads] = useState<FailedUpload[]>([]);
  const [tokenStats, setTokenStats] = useState<TokenStats>({
    tokenCount: 0,
    promptLimit: 0,
  });
  const [bulkUploadJobId, setBulkUploadJobId] = useState<string | null>(null);
  const [showBulkUploadTracker, setShowBulkUploadTracker] =
    useState<boolean>(false);
  const [showFolderSelection, setShowFolderSelection] =
    useState<boolean>(false);
  const [currentJobStatus, setCurrentJobStatus] =
    useState<BulkUploadJobStatus | null>(null);
  const [showBulkProcessing, setShowBulkProcessing] = useState<boolean>(false);
  const [bulkProcessingJobId, setBulkProcessingJobId] = useState<string | null>(
    null
  );
  const [embeddingQueueProgress, setEmbeddingQueueProgress] =
    useState<EmbeddingQueueProgress | null>(null);
  const selectedModule = useUserStore((state) => state.selectedModule);
  const BULK_UPLOAD_THRESHOLD = parseInt(
    process.env.VITE_BULK_UPLOAD_THRESHOLD || "20"
  );

  const { t } = useTranslation();

  const handleSendLink = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setLoadingMessage(t("modale.document.scraping-link", "Scraping link..."));
    setFetchingUrl(true);
    const formEl = e.target as HTMLFormElement;
    const form = new FormData(formEl);
    const linkValue = form.get("link") as string;

    try {
      const { response, data } = await Workspace.uploadLink(
        workspace.slug,
        linkValue
      );
      if (!response.ok) {
        showToast(
          t("show-toast.link-upload-error", { error: data.error }),
          "error"
        );
      } else {
        fetchKeys(true, documentDraftingSelected);
        showToast(t("show-toast.link-upload-success"), "success");
        formEl.reset();
      }
    } catch {
      showToast(
        t("show-toast.link-upload-error", { error: "Network error" }),
        "error"
      );
    }
    setLoading(false);
    setFetchingUrl(false);
    setIsUpload(true);
  };

  // Don't spam fetchKeys, wait 1s between calls at least.
  // For document-drafting mode, add a slight delay to ensure file system has updated
  const handleUploadSuccess = useCallback(() => {
    if (documentDraftingSelected) {
      // In document-drafting mode, wait a bit for file system to update
      setTimeout(() => {
        fetchKeys(true, documentDraftingSelected);
      }, 500);
    } else {
      // Normal mode can update immediately
      fetchKeys(true, documentDraftingSelected);
    }
  }, [fetchKeys, documentDraftingSelected]);

  const fetchTokenStats = useCallback(async (): Promise<TokenStats | null> => {
    if (!documentDraftingSelected || !workspace?.slug) return null;
    try {
      const data = await Workspace.getTokenCount(workspace.slug);
      if (data) {
        const stats: TokenStats = {
          tokenCount: data.tokenCount,
          promptLimit: data.promptLimit,
        };
        setTokenStats(stats);
        onFileChange?.(stats);
        return stats;
      }
      return null;
    } catch {
      return null;
    }
  }, [workspace?.slug, documentDraftingSelected, onFileChange]);

  useEffect(() => {
    fetchTokenStats();
  }, [workspace.slug, documentDraftingSelected, onFileChange, fetchTokenStats]);

  useEffect(() => {
    if (documentDraftingSelected) {
      fetchTokenStats();
    }
  }, [files, documentDraftingSelected, fetchTokenStats]);

  // Add a forced refresh of token stats when component is visible
  useEffect(() => {
    if (documentDraftingSelected) {
      // Set up an interval to refresh token stats regularly when component is mounted
      const intervalId = setInterval(() => {
        fetchTokenStats();
      }, 2000); // Refresh every 2 seconds

      // Cleanup interval on unmount
      return () => clearInterval(intervalId);
    }
  }, [documentDraftingSelected, fetchTokenStats]);

  const handleUploadError = useCallback(
    (file: { originalname?: string; error: string }) => {
      setFailedUploads((prev) => [
        ...prev,
        { fileName: file.originalname || "Unknown file", error: file.error },
      ]);
    },
    []
  );

  const onDrop = useCallback(
    async (acceptedFiles: File[], rejections: FileRejection[]) => {
      setFailedUploads([]);
      const existingFiles = files.map((f) => `${f.file.name}-${f.file.size}`);

      const newAccepted: FileWithMetadata[] = acceptedFiles
        .filter((file) => !existingFiles.includes(`${file.name}-${file.size}`))
        .map((file) => ({
          uid: uuidv4(),
          file,
        }));

      const newRejected: FileWithMetadata[] = rejections.map((rejection) => ({
        uid: uuidv4(),
        file: rejection.file,
        rejected: true,
        reason: rejection.errors[0]?.code || "Unknown error",
      }));

      // Check if we should use bulk upload (default: 20+ files, max: 5000 files)
      const existingAcceptedFiles = files.filter((f) => !f.rejected);
      const totalAcceptedFiles = [...existingAcceptedFiles, ...newAccepted];
      if (
        totalAcceptedFiles.length >= BULK_UPLOAD_THRESHOLD &&
        !newRejected.length
      ) {
        // Start bulk upload
        setLoading(true);
        setLoadingMessage(t("bulk-upload.starting", "Starting bulk upload..."));

        try {
          // Pass files directly without base64 conversion
          const result = await Workspace.startBulkUpload(
            workspace.slug,
            newAccepted, // Pass the file objects directly
            selectedModule
          );

          if (result.success) {
            setBulkUploadJobId(result.jobId);
            setShowBulkUploadTracker(true);
            setFiles([]);
            // Clear any previous job status to avoid stale data
            setCurrentJobStatus(null);
          } else {
            showToast(
              t(
                "bulk-upload.start-failed",
                "Failed to start bulk upload: {{error}}",
                { error: result.error }
              ),
              "error"
            );
          }
        } catch {
          // Handle file reading error
          showToast(
            t("bulk-upload.read-failed", "Failed to read files for upload"),
            "error"
          );
        }
        setLoading(false);
      } else {
        // Regular file upload
        setFiles((prevFiles) => [...prevFiles, ...newAccepted, ...newRejected]);
      }
    },
    [
      files,
      workspace.slug,
      selectedModule,
      t,
      BULK_UPLOAD_THRESHOLD,
      setLoading,
      setLoadingMessage,
    ]
  );

  const handleFileRemoval = useCallback(
    (uid: string) => {
      setFiles((prevFiles) => {
        const newFiles = prevFiles.filter((file) => file.uid !== uid);
        // Schedule token count update for next tick after state update
        setTimeout(() => {
          if (documentDraftingSelected) {
            fetchTokenStats();
          }
        }, 0);
        return newFiles;
      });
    },
    [documentDraftingSelected, fetchTokenStats]
  );

  const handleMoveToFolder = useCallback(
    async (jobStatus: BulkUploadJobStatus) => {
      // Store the CURRENT job status for later use (not a stale one)
      setCurrentJobStatus(jobStatus);
      // Open folder selection modal
      setShowFolderSelection(true);
    },
    []
  );

  const handleFolderSelected = useCallback(
    async (folderName: string) => {
      try {
        if (!currentJobStatus || !currentJobStatus.successfulFiles) {
          showToast(
            t("bulk-upload.no-files-to-move", "No files available to move"),
            "error"
          );
          return;
        }

        showToast(
          t("bulk-upload.moving-files", "Moving files to folder..."),
          "info"
        );

        // Convert file paths to the format expected by moveToFolder

        const filesToMove = currentJobStatus.successfulFiles.map(
          (filePath: string) => {
            // Handle string file path format
            let currentFolder: string | null = null;

            // Parse the path: "workspace-slug/folder/filename.json" or "workspace-slug/custom-documents/filename.json"
            const pathParts = filePath.split("/");

            // The filename is always the last part
            const fileName = pathParts.pop();

            // Remove workspace slug (first part) to get the folder path
            if (pathParts.length > 1) {
              pathParts.shift(); // Remove workspace slug
              currentFolder = pathParts.join("/");
            } else if (pathParts.length === 1) {
              // File is in root of workspace
              currentFolder = null;
            }

            return {
              name: fileName || "",
              folderName: currentFolder || undefined,
            };
          }
        );

        const Document = (await import("../../../../../models/document"))
          .default;
        const result = await Document.moveToFolder(
          filesToMove,
          folderName,
          workspace.slug,
          documentDraftingSelected
        );

        if (result.success) {
          showToast(
            t(
              "bulk-upload.files-moved-success",
              "Files moved to {{folderName}} successfully",
              {
                folderName,
              }
            ),
            "success"
          );

          // Refresh the workspace to show updated file locations
          await fetchKeys(true, documentDraftingSelected);

          // Update the job status to reflect files have been moved
          setCurrentJobStatus({
            ...currentJobStatus,
            filesMoved: true,
            movedToFolder: folderName,
            successfulFiles: currentJobStatus.successfulFiles.map((file) => {
              // Extract the filename from the path
              const pathParts = file.split("/");
              const fileName = pathParts[pathParts.length - 1];

              // Get the workspace slug (first part of the path)
              const workspaceSlug = pathParts[0];

              // Return the new path with the folder
              return `${workspaceSlug}/${folderName}/${fileName}`;
            }),
          });

          // Keep the bulk upload tracker open to allow further actions
          // setShowBulkUploadTracker(false);
          // setBulkUploadJobId(null);

          // Close the folder selection modal
          setShowFolderSelection(false);
        } else {
          showToast(
            t(
              "bulk-upload.files-moved-failed",
              "Failed to move files: {{error}}",
              {
                error: result.error || "Unknown error",
              }
            ),
            "error"
          );
        }
      } catch (error: unknown) {
        // Handle file moving error
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error";
        showToast(
          t(
            "bulk-upload.files-moved-failed",
            "Failed to move files: {{error}}",
            {
              error: errorMessage,
            }
          ),
          "error"
        );
      } finally {
        setCurrentJobStatus(null);
        // Ensure the folder selection modal is closed in all cases
        setShowFolderSelection(false);
      }
    },
    [currentJobStatus, workspace.slug, fetchKeys, documentDraftingSelected, t]
  );

  // Helper function to process embedding batches with rate limiting
  const processEmbeddingBatch = useCallback(
    async (batch: string[], batchIndex: number, totalBatches: number) => {
      const response = await Workspace.modifyEmbeddings(workspace.slug, {
        adds: batch,
        deletes: [],
      });

      // Update progress
      setEmbeddingQueueProgress({
        current: batchIndex + 1,
        total: totalBatches,
        processed: (batchIndex + 1) * batch.length,
      });

      return response;
    },
    [workspace.slug]
  );

  // Queue processor for embedding batches
  const processEmbeddingQueue = useCallback(
    async (fileNames: string[], _totalFiles: number) => {
      const batches = [];

      // Split files into batches
      for (let i = 0; i < fileNames.length; i += EMBEDDING_BATCH_SIZE) {
        batches.push(fileNames.slice(i, i + EMBEDDING_BATCH_SIZE));
      }

      const results: any[] = [];
      const errors: any[] = [];

      // Process batches with concurrency control
      for (let i = 0; i < batches.length; i += MAX_CONCURRENT_BATCHES) {
        const concurrentBatches = batches.slice(i, i + MAX_CONCURRENT_BATCHES);

        try {
          // Process concurrent batches
          const batchPromises = concurrentBatches.map((batch, index) =>
            processEmbeddingBatch(batch, i + index, batches.length)
          );

          const batchResults = await Promise.allSettled(batchPromises);

          // Collect results and errors
          batchResults.forEach((result, index) => {
            if (result.status === "fulfilled") {
              results.push(result.value);
            } else {
              errors.push({
                batch: i + index,
                error: result.reason,
              });
            }
          });

          // Add delay between batch groups to prevent rate limiting
          if (i + MAX_CONCURRENT_BATCHES < batches.length) {
            await new Promise((resolve) =>
              setTimeout(resolve, EMBEDDING_BATCH_DELAY)
            );
          }
        } catch (error) {
          // Handle concurrent batch processing error
          errors.push({ batch: i, error });
        }
      }

      return { results, errors, totalBatches: batches.length };
    },
    [processEmbeddingBatch]
  );

  const handleStartEmbedding = useCallback(
    async (jobStatus: BulkUploadJobStatus) => {
      try {
        // Get the list of successfully uploaded files from job status
        const filesToEmbed = jobStatus.successfulFiles || [];

        if (filesToEmbed.length === 0) {
          showToast(
            t(
              "bulk-upload.no-files-to-embed",
              "No files available for embedding"
            ),
            "warning"
          );
          return;
        }

        // Extract the proper document paths for embedding
        const fileNamesForEmbedding = filesToEmbed.map((filePath) => {
          const pathParts = filePath.split("/");
          if (pathParts.length > 1) {
            pathParts.shift(); // Remove workspace slug
            return pathParts.join("/");
          }
          return filePath;
        });

        // Check if we need to use rate-limited processing
        if (fileNamesForEmbedding.length > EMBEDDING_BATCH_SIZE) {
          showToast(
            t("bulk-upload.embedding-queue-started", {
              defaultValue: `Processing ${fileNamesForEmbedding.length} files in batches...`,
              count: fileNamesForEmbedding.length,
            }),
            "info"
          );

          // Initialize progress tracking
          setEmbeddingQueueProgress({
            current: 0,
            total: Math.ceil(
              fileNamesForEmbedding.length / EMBEDDING_BATCH_SIZE
            ),
            processed: 0,
          });

          // Process with rate limiting
          const {
            results,
            errors,
            totalBatches: _totalBatches,
          } = await processEmbeddingQueue(
            fileNamesForEmbedding,
            fileNamesForEmbedding.length
          );

          // Check if any batch triggered bulk processing
          const bulkJobResponse = results.find((r) => r?.bulkJob && r?.jobId);

          if (bulkJobResponse) {
            // Close the bulk upload tracker
            setShowBulkUploadTracker(false);
            setBulkUploadJobId(null);
            setEmbeddingQueueProgress(null);

            showToast(
              t("show-toast.bulk-processing-started", {
                count: fileNamesForEmbedding.length,
                jobId: bulkJobResponse.jobId,
              }),
              "info"
            );

            // Show the bulk processing progress modal
            setBulkProcessingJobId(bulkJobResponse.jobId);
            setShowBulkProcessing(true);
          } else {
            // All batches processed without triggering bulk job
            setShowBulkUploadTracker(false);
            setBulkUploadJobId(null);
            setEmbeddingQueueProgress(null);

            await fetchKeys(true, documentDraftingSelected);

            if (errors.length > 0) {
              showToast(
                t("bulk-upload.embedding-completed-with-errors", {
                  defaultValue: `Embedding completed with ${errors.length} errors`,
                  errorCount: errors.length,
                }),
                "warning"
              );
            } else {
              showToast(
                t(
                  "bulk-upload.embedding-completed",
                  "Embedding process completed"
                ),
                "success"
              );
            }
          }
        } else {
          // Small batch - process normally
          showToast(
            t("bulk-upload.embedding-started", "Starting embedding process..."),
            "info"
          );

          const response = await Workspace.modifyEmbeddings(workspace.slug, {
            adds: fileNamesForEmbedding,
            deletes: [],
          });

          if (response.bulkJob && response.jobId) {
            // Close the bulk upload tracker
            setShowBulkUploadTracker(false);
            setBulkUploadJobId(null);

            showToast(
              t("show-toast.bulk-processing-started", {
                count: fileNamesForEmbedding.length,
                jobId: response.jobId,
              }),
              "info"
            );

            // Show the bulk processing progress modal
            setBulkProcessingJobId(response.jobId);
            setShowBulkProcessing(true);
          } else {
            // For smaller batches, close the bulk upload tracker and refresh
            setShowBulkUploadTracker(false);
            setBulkUploadJobId(null);

            await fetchKeys(true, documentDraftingSelected);
            showToast(
              t(
                "bulk-upload.embedding-completed",
                "Embedding process completed"
              ),
              "success"
            );
          }
        }
      } catch {
        // Handle embedding start error
        showToast(
          t("bulk-upload.embedding-start-failed", "Failed to start embedding"),
          "error"
        );
      } finally {
        setEmbeddingQueueProgress(null);
      }
    },
    [
      workspace.slug,
      fetchKeys,
      documentDraftingSelected,
      t,
      processEmbeddingQueue,
    ]
  );

  const handleBulkUploadComplete = useCallback(() => {
    fetchKeys(true, documentDraftingSelected);
    showToast(
      t("bulk-upload.completed", "Bulk upload completed successfully"),
      "success"
    );
  }, [fetchKeys, documentDraftingSelected, t]);

  const handleBulkProcessingComplete = useCallback(() => {
    setShowBulkProcessing(false);
    setBulkProcessingJobId(null);
    fetchKeys(true, documentDraftingSelected);
    showToast(
      t("bulk-processing.completed", "Bulk processing completed successfully"),
      "success"
    );
  }, [fetchKeys, documentDraftingSelected, t]);

  const handleBulkProcessingClose = useCallback(() => {
    setShowBulkProcessing(false);
    setBulkProcessingJobId(null);
  }, []);

  useEffect(() => {
    async function checkProcessorOnline() {
      try {
        const online = await System.checkDocumentProcessorOnline();
        setReady(online);
      } catch {
        setReady(false);
      }
    }
    checkProcessorOnline();
  }, []);

  const dropzoneOptions: DropzoneOptions = {
    onDrop,
    disabled: !ready,
  };

  const { getRootProps, getInputProps } = useDropzone(dropzoneOptions);

  const getAcceptedFileTypes = (): string => {
    return isDocumentDrafting ? "application/pdf,image/*" : "application/pdf";
  };

  const calculateProgressPercentage = (): number => {
    return Math.min(
      (tokenStats.tokenCount / tokenStats.promptLimit) * 100,
      100
    );
  };

  const getProgressBarColor = (): string => {
    return tokenStats.tokenCount > tokenStats.promptLimit
      ? "#ef4444"
      : "#2563eb";
  };

  const isTokenLimitExceeded = (): boolean => {
    return tokenStats.tokenCount > tokenStats.promptLimit;
  };

  return (
    <div>
      <div
        className={`w-full max-w-[560px] border-2 border-dashed border-border bg-elevated rounded-lg p-6 transition-colors ${
          ready
            ? "cursor-pointer hover:border-primary hover:bg-secondary/50"
            : "cursor-not-allowed opacity-60"
        }`}
        {...getRootProps()}
      >
        <input {...getInputProps()} accept={getAcceptedFileTypes()} />
        {ready === false ? (
          <div className="flex flex-col items-center justify-center h-full min-h-[120px]">
            <CloudArrowUp className="w-8 h-8 text-muted mb-3" />
            <div className="text-foreground text-sm font-semibold mb-2">
              {t("modale.document.doc-processor")}
            </div>
            <div className="text-muted text-xs font-medium text-center max-w-xs">
              {t("modale.document.processor-offline")}
            </div>
          </div>
        ) : files.length === 0 ? (
          <div className="flex flex-col items-center justify-center min-h-[120px]">
            <CloudArrowUp className="w-8 h-8 text-primary mb-3" />
            <div className="text-foreground text-sm font-semibold mb-2">
              {t("modale.document.drag-drop")}
            </div>
            <div className="text-muted text-xs font-medium">
              {t("modale.document.supported-files")}
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-3 max-h-[180px] overflow-y-auto">
            {files.map((file) => (
              <FileUploadProgress
                key={file.uid}
                file={file.file}
                uuid={file.uid}
                setFiles={handleFileRemoval}
                slug={workspace.slug}
                workspace={workspace}
                rejected={file?.rejected}
                reason={file?.reason}
                onUploadSuccess={handleUploadSuccess}
                onUploadError={handleUploadError}
                setLoading={setLoading}
                setLoadingMessage={setLoadingMessage}
                setIsUpload={setIsUpload}
                isModalUpload={true}
                fetchTokenStats={
                  documentDraftingSelected ? fetchTokenStats : undefined
                }
                onFileChange={onFileChange}
              />
            ))}
          </div>
        )}
        {failedUploads.length > 0 && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <h3 className="text-red-600 font-semibold text-sm mb-2">
              {t("modale.document.failed-uploads")}
            </h3>
            <div className="max-h-[100px] overflow-y-auto space-y-1">
              {failedUploads.map((failedFile, index) => (
                <div key={index} className="text-xs text-red-500">
                  {index + 1}. {failedFile.fileName}: {failedFile.error}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      {!documentDraftingSelected && (
        <div className="mt-4">
          <div className="text-center text-muted text-xs font-medium mb-3">
            {t("modale.document.submit-link")}
          </div>
          <form onSubmit={handleSendLink} className="flex gap-2">
            <Input
              disabled={fetchingUrl}
              name="link"
              type="url"
              placeholder="https://example.com"
              autoComplete="off"
              className="flex-1"
            />
            <Button
              disabled={fetchingUrl}
              type="submit"
              size="sm"
              isLoading={fetchingUrl}
            >
              {fetchingUrl
                ? t("modale.document.justify-betweening")
                : t("modale.document.fetch")}
            </Button>
          </form>
          <div className="mt-3 text-left text-muted text-xs">
            {t("modale.document.file-desc")}
          </div>
        </div>
      )}
      {documentDraftingSelected && (
        <div className="mt-4">
          <div className="bg-elevated border border-border rounded-lg p-4">
            <div className="flex justify-between text-sm text-muted mb-2">
              <span>
                {t("workspace-chats.total-tokens")}:{" "}
                {numberWithCommas(tokenStats.tokenCount)}
              </span>
              <span>
                {t("workspace-chats.context-window")}:{" "}
                {numberWithCommas(tokenStats.promptLimit)}
              </span>
            </div>
            <div className="w-full bg-border rounded-full h-2">
              <div
                className="h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${calculateProgressPercentage()}%`,
                  backgroundColor: getProgressBarColor(),
                }}
              />
            </div>
            {isTokenLimitExceeded() && (
              <div className="text-red-500 text-xs mt-3 p-2 bg-red-50 border border-red-200 rounded">
                {t(
                  "modale.document.exceeds-prompt-limit",
                  "Note: The uploaded content exceeds what can fit with one prompt. The system will process any requests by multiple prompts which will increase the time for generating the answer, and precision may be affected."
                )}
              </div>
            )}
          </div>
        </div>
      )}
      {showBulkUploadTracker && (
        <BulkUploadTracker
          isOpen={showBulkUploadTracker}
          onClose={() => {
            setShowBulkUploadTracker(false);
            // Clear job status when closing to avoid stale data
            setCurrentJobStatus(null);
          }}
          workspace={workspace}
          jobId={bulkUploadJobId || ""}
          onComplete={handleBulkUploadComplete}
          onCancel={() => {
            setBulkUploadJobId(null);
            setCurrentJobStatus(null);
            showToast(
              t("bulk-upload.cancelled", "Bulk upload cancelled"),
              "info"
            );
          }}
          onMoveToFolder={(jobStatus) => {
            handleMoveToFolder(jobStatus as unknown as BulkUploadJobStatus);
          }}
          onStartEmbedding={(jobStatus) => {
            handleStartEmbedding(jobStatus as unknown as BulkUploadJobStatus);
          }}
          documentDraftingMode={documentDraftingSelected}
        />
      )}
      {/* Render folder selection modal as a portal */}
      {showFolderSelection &&
        typeof window !== "undefined" &&
        ReactDOM.createPortal(
          <FolderSelectionModal
            isOpen={showFolderSelection}
            onClose={() => {
              setShowFolderSelection(false);
              setCurrentJobStatus(null);
            }}
            onFolderSelect={handleFolderSelected}
            workspaceSlug={workspace.slug}
            title={t(
              "bulk-upload.move-to-folder-title",
              "Move Files to Folder"
            )}
            zIndex={9999}
          />,
          document.body
        )}
      {/* Bulk Processing Progress Modal */}
      <BulkProcessingProgress
        isOpen={showBulkProcessing}
        onClose={handleBulkProcessingClose}
        jobId={bulkProcessingJobId || ""}
        workspaceSlug={workspace?.slug}
        onComplete={handleBulkProcessingComplete}
        zIndex={9999}
      />
      {/* Embedding Queue Progress Overlay */}
      {embeddingQueueProgress &&
        typeof window !== "undefined" &&
        ReactDOM.createPortal(
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[10000]">
            <div className="bg-elevated border border-border rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold text-foreground mb-4">
                {t(
                  "bulk-upload.embedding-progress-title",
                  "Processing Embeddings"
                )}
              </h3>
              <div className="space-y-3">
                <div className="text-sm text-muted">
                  {t("bulk-upload.embedding-file-progress", {
                    defaultValue: `Processing file ${embeddingQueueProgress.current} of ${embeddingQueueProgress.total}`,
                    current: embeddingQueueProgress.current,
                    total: embeddingQueueProgress.total,
                  })}
                </div>
                <div className="w-full bg-border rounded-full h-2">
                  <div
                    className="h-2 bg-primary rounded-full transition-all duration-300"
                    style={{
                      width: `${(embeddingQueueProgress.current / embeddingQueueProgress.total) * 100}%`,
                    }}
                  />
                </div>
                <div className="text-xs text-muted text-center">
                  {t("bulk-upload.embedding-files-processed", {
                    defaultValue: `${embeddingQueueProgress.processed} files processed`,
                    count: embeddingQueueProgress.processed,
                  })}
                </div>
                <div className="text-xs text-muted text-center mt-4">
                  {t(
                    "bulk-upload.embedding-rate-limit-info",
                    "Processing in batches to prevent server overload..."
                  )}
                </div>
              </div>
            </div>
          </div>,
          document.body
        )}
    </div>
  );
}
