import { useEffect, useState, useCallback, useRef } from "react";
import ReactDOM from "react-dom";
import { ArrowsDownUp } from "@phosphor-icons/react";
import Workspace from "../../../../models/workspace";
import System from "../../../../models/system";
import showToast from "../../../../utils/toast";
import Directory from "./Directory";
import WorkspaceDirectory from "./WorkspaceDirectory";
import BulkProcessingProgress from "./BulkProcessingProgress";
import { useTranslation } from "react-i18next";
import { filterFileSearchResults } from "@/components/Modals/ManageWorkspace/Documents/Directory/utils";
import { useIsDocumentDrafting } from "@/stores/userStore";
import { aggregateFolderStatus } from "@/utils/directories/folderStatus";

// Import types from utils

// OpenAI Cost per token
// ref: https://openai.com/pricing#:~:text=%C2%A0/%201K%20tokens-,Embedding%20models,-Build%20advanced%20search

const MODEL_COSTS: Record<string, number> = {
  "text-embedding-ada-002": 0.0000001, // $0.0001 / 1K tokens
  "text-embedding-3-small": 0.00000002, // $0.00002 / 1K tokens
  "text-embedding-3-large": 0.00000013, // $0.00013 / 1K tokens
};

// Rate limiting configuration for embeddings (matching UploadFile component)
const EMBEDDING_BATCH_SIZE = 50; // Max files per batch
const EMBEDDING_BATCH_DELAY = 2000; // Delay between batches (ms)
const MAX_CONCURRENT_BATCHES = 2; // Max concurrent embedding requests

interface DocumentSettingsProps {
  workspace: any;
  systemSettings: any;
}

export interface FileItem {
  id: string;
  path: string;
  type: "file" | "folder";
  name?: string;
  items?: FileItem[];
  starred?: boolean;
  pinned?: boolean;
  pdr?: boolean;
  docId?: string;
  cached?: boolean;
  token_count_estimate?: number;
  [key: string]: any;
}

export interface FolderItem extends FileItem {
  type: "folder";
  items: FileItem[];
}

export interface FilesData {
  items: FolderItem[];
  [key: string]: any;
}

interface MovedItem extends FileItem {
  folderName?: string;
}

interface TokenStats {
  tokenCount: number;
  promptLimit: number;
}

interface BulkJobResponse {
  success?: boolean;
  bulkJob?: boolean;
  jobId?: string;
  message?: string;
}

interface EmbeddingQueueProgress {
  current: number;
  total: number;
  processed: number;
}

export default function DocumentSettings({
  workspace,
  systemSettings,
}: DocumentSettingsProps): JSX.Element {
  const { t } = useTranslation();
  const [highlightWorkspace, setHighlightWorkspace] = useState<boolean>(false);
  const [availableDocs, setAvailableDocs] = useState<FilesData>({ items: [] });
  const [loading, setLoading] = useState<boolean>(true);
  const [isUpload, setIsUpload] = useState<boolean>(true);
  const [workspaceDocs, setWorkspaceDocs] = useState<FilesData>({ items: [] });
  const [selectedItems, setSelectedItems] = useState<Record<string, boolean>>(
    {}
  );
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const [movedItems, setMovedItems] = useState<MovedItem[]>([]);
  const [embeddingsCost, setEmbeddingsCost] = useState<number>(0);
  const [loadingMessage, setLoadingMessage] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [_tokenStats, setTokenStats] = useState<TokenStats>({
    tokenCount: 0,
    promptLimit: 0,
  });
  const [bulkJobId, setBulkJobId] = useState<string | null>(null);
  const [showBulkProgress, setShowBulkProgress] = useState<boolean>(false);
  const [embeddingQueueProgress, setEmbeddingQueueProgress] =
    useState<EmbeddingQueueProgress | null>(null);
  const documentDraftingSelected = useIsDocumentDrafting();

  // Ref for cleanup
  const isMountedRef = useRef<boolean>(true);

  // Cleanup effect for component unmounting
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const handleFileChange = useCallback((data: TokenStats) => {
    if (data) {
      setTokenStats(data);
    }
  }, []);

  const fetchKeys = useCallback(
    async (_refetchWorkspace = false, selectAll = false) => {
      setLoading(true);

      try {
        // Always fetch fresh workspace data with documents to ensure all document states are up-to-date.
        // The bySlug method handles cache invalidation internally.
        const currentWorkspace = await Workspace.bySlug(workspace.slug, true);

        // Validate workspace data
        if (!currentWorkspace) {
          setLoading(false);
          return;
        }

        const documentsInWorkspace = (
          currentWorkspace?.documents ||
          currentWorkspace?.workspace_documents ||
          []
        ).map((doc: any) => doc.docpath);

        // Documents that are not in the workspace
        const localFilesResponse = await System.localFiles(currentWorkspace.id);

        // Validate local files data
        if (!localFilesResponse || typeof localFilesResponse !== "object") {
          setLoading(false);
          return;
        }

        // Convert the response to FilesData structure
        const localFiles: FilesData =
          localFilesResponse as unknown as FilesData;

        // Aggregate folder status
        if (localFiles?.items) {
          localFiles.items = localFiles.items.map((folder: FolderItem) => {
            const flags = aggregateFolderStatus(folder);
            return { ...folder, ...flags };
          });
        }

        const availableDocs: FilesData = {
          ...localFiles,
          items: (localFiles?.items || []).map((folder: FolderItem) => {
            if (folder?.items && folder.type === "folder") {
              return {
                ...folder,
                items: folder.items.filter((file: FileItem) => {
                  if (file.type !== "file") return false;

                  const filePath = file.path;

                  // Normalize path separators for comparison
                  const normalizedFilePath = filePath.replace(/\\/g, "/");
                  const normalizedDocs = documentsInWorkspace.map((doc) =>
                    doc.replace(/\\/g, "/")
                  );

                  const existsInWorkspace =
                    normalizedDocs.includes(normalizedFilePath);

                  // For document drafting mode, show all files
                  if (documentDraftingSelected) return true;

                  // For normal mode, only show files NOT in workspace
                  return !existsInWorkspace;
                }),
              };
            } else {
              return folder;
            }
          }),
        };

        // Documents that are already in the workspace

        const workspaceDocs: FilesData = {
          ...localFiles,
          items: (localFiles?.items || []).map((folder: FolderItem) => {
            if (folder?.items && folder.type === "folder") {
              return {
                ...folder,
                items: folder.items.filter((file: FileItem) => {
                  if (file.type !== "file") return false;
                  if (documentDraftingSelected) return false;

                  const filePath = file.path;
                  // Normalize path separators for comparison
                  const normalizedFilePath = filePath.replace(/\\/g, "/");
                  const normalizedDocs = documentsInWorkspace.map((doc) =>
                    doc.replace(/\\/g, "/")
                  );

                  const existsInWorkspace =
                    normalizedDocs.includes(normalizedFilePath);

                  // Find the document in the workspace to get its properties
                  if (existsInWorkspace) {
                    const docInWorkspace = (
                      currentWorkspace?.documents ||
                      currentWorkspace?.workspace_documents ||
                      []
                    ).find(
                      (doc: any) =>
                        doc.docpath.replace(/\\/g, "/") === normalizedFilePath
                    );

                    if (docInWorkspace) {
                      // Update all document properties from the database
                      file.starred = docInWorkspace.starred || false;
                      file.pinned = docInWorkspace.pinned || false;
                      file.pdr = docInWorkspace.pdr || false;
                      file.docId = docInWorkspace.docId;
                    }
                  }

                  return existsInWorkspace;
                }),
              };
            } else {
              return folder;
            }
          }),
        };

        // Recompute aggregated flags for workspaceDocs after properties update
        if (workspaceDocs?.items) {
          workspaceDocs.items = workspaceDocs.items.map(
            (folder: FolderItem) => {
              const flags = aggregateFolderStatus(folder);
              return { ...folder, ...flags };
            }
          );
        }

        setAvailableDocs(availableDocs);
        setWorkspaceDocs(workspaceDocs);

        if (selectAll) {
          const filteredFiles = filterFileSearchResults(
            availableDocs,
            searchTerm
          );
          const newSelectedItems: Record<string, boolean> = {};
          filteredFiles.forEach((item: FileItem) => {
            if (item.type === "folder") {
              newSelectedItems[item.name || ""] = true;
              item.items?.forEach((file) => {
                newSelectedItems[file.id] = true;
              });
            } else {
              newSelectedItems[item.id] = true;
            }
          });
          setSelectedItems(newSelectedItems);
        }

        setLoading(false);
      } catch {
        setLoading(false);
      }
    },
    [
      workspace,
      documentDraftingSelected,
      searchTerm,
      setSelectedItems,
      setAvailableDocs,
      setWorkspaceDocs,
      setLoading,
    ]
  );

  useEffect(() => {
    fetchKeys(true, documentDraftingSelected);
  }, [documentDraftingSelected, fetchKeys]);

  // Helper function to process embedding batches with rate limiting
  const processEmbeddingBatch = async (
    batch: string[],
    batchIndex: number,
    totalBatches: number
  ) => {
    const response = await Workspace.modifyEmbeddings(workspace.slug, {
      adds: batch,
      deletes: [],
    });

    // Update progress
    setEmbeddingQueueProgress({
      current: batchIndex + 1,
      total: totalBatches,
      processed: (batchIndex + 1) * batch.length,
    });

    return response;
  };

  // Queue processor for embedding batches
  const processEmbeddingQueue = async (
    fileNames: string[],
    _totalFiles: number
  ) => {
    const batches: string[][] = [];

    // Split files into batches
    for (let i = 0; i < fileNames.length; i += EMBEDDING_BATCH_SIZE) {
      batches.push(fileNames.slice(i, i + EMBEDDING_BATCH_SIZE));
    }

    const results: any[] = [];
    const errors: any[] = [];

    // Process batches with concurrency control
    for (let i = 0; i < batches.length; i += MAX_CONCURRENT_BATCHES) {
      const concurrentBatches = batches.slice(i, i + MAX_CONCURRENT_BATCHES);

      try {
        // Process concurrent batches
        const batchPromises = concurrentBatches.map((batch, index) =>
          processEmbeddingBatch(batch, i + index, batches.length)
        );

        const batchResults = await Promise.allSettled(batchPromises);

        // Collect results and errors
        batchResults.forEach((result, index) => {
          if (result.status === "fulfilled") {
            results.push(result.value);
          } else {
            errors.push({
              batch: i + index,
              error: result.reason,
            });
          }
        });

        // Add delay between batch groups to prevent rate limiting
        if (i + MAX_CONCURRENT_BATCHES < batches.length) {
          await new Promise((resolve) =>
            setTimeout(resolve, EMBEDDING_BATCH_DELAY)
          );
        }
      } catch (error) {
        errors.push({ batch: i, error });
      }
    }

    return { results, errors, totalBatches: batches.length };
  };

  const updateWorkspace = async (
    e?: React.FormEvent,
    newMovedItems: MovedItem[] = []
  ) => {
    if (e) e.preventDefault();

    setLoading(true);
    setLoadingMessage(t("modale.document.loading-message"));
    showToast(t("show-toast.updating-workspace"), "info", { autoClose: false });

    const changesToSend = {
      adds: (newMovedItems && newMovedItems.length > 0
        ? newMovedItems
        : movedItems
      ).map((item) => item.path),
    };

    setSelectedItems({});
    setHasChanges(false);
    setHighlightWorkspace(false);

    try {
      let response: BulkJobResponse;

      // Check if we need to use rate-limited processing
      if (changesToSend.adds.length > EMBEDDING_BATCH_SIZE) {
        showToast(
          t("bulk-upload.embedding-queue-started", {
            defaultValue: `Processing ${changesToSend.adds.length} files in batches...`,
            count: changesToSend.adds.length,
          }),
          "info"
        );

        // Initialize progress tracking
        setEmbeddingQueueProgress({
          current: 0,
          total: Math.ceil(changesToSend.adds.length / EMBEDDING_BATCH_SIZE),
          processed: 0,
        });

        // Process with rate limiting
        const {
          results,
          errors,
          totalBatches: _totalBatches,
        } = await processEmbeddingQueue(
          changesToSend.adds,
          changesToSend.adds.length
        );

        // Check if any batch triggered bulk processing
        const bulkJobResponse = results.find(
          (r: any) => r?.bulkJob && r?.jobId
        );

        if (bulkJobResponse) {
          response = bulkJobResponse;
        } else {
          // All batches processed without triggering bulk job
          setEmbeddingQueueProgress(null);

          if (errors.length > 0) {
            showToast(
              t("bulk-upload.embedding-completed-with-errors", {
                defaultValue: `Embedding completed with ${errors.length} errors`,
                errorCount: errors.length,
              }),
              "warning"
            );
          } else {
            showToast(
              t(
                "bulk-upload.embedding-completed",
                "Embedding process completed"
              ),
              "success"
            );
          }

          // Continue with normal flow
          response = { success: true };
        }
      } else {
        // Small batch - process normally
        response = (await Workspace.modifyEmbeddings(
          workspace.slug,
          changesToSend
        )) as BulkJobResponse;
      }

      // Check if bulk processing was initiated
      if (response.bulkJob && response.jobId) {
        setEmbeddingQueueProgress(null);
        showToast(
          t("show-toast.bulk-processing-started", {
            count: changesToSend.adds.length,
            jobId: response.jobId,
          }),
          "info",
          { clear: true }
        );

        // Show bulk processing modal
        setBulkJobId(response.jobId);
        setShowBulkProgress(true);
        setLoading(false);
        setLoadingMessage("");
        setMovedItems([]);
        return;
      }

      // Handle regular processing response
      if (response.message) {
        showToast(
          t("show-toast.workspace-update-error", { error: response.message }),
          "error",
          { clear: true }
        );
      } else {
        showToast(t("show-toast.workspace-updated-success"), "success", {
          clear: true,
        });

        // Refresh workspace data and complete the process
        await fetchKeys(true, documentDraftingSelected);
        setLoading(false);
        setLoadingMessage("");
        setMovedItems([]);
        return;
      }
    } catch (error) {
      showToast(
        t("show-toast.workspace-update-failed", {
          error: (error as Error).message,
        }),
        "error",
        {
          clear: true,
        }
      );
      setEmbeddingQueueProgress(null);
    }

    setMovedItems([]);
    await fetchKeys(true, documentDraftingSelected);
    setLoading(false);
    setLoadingMessage("");
    setEmbeddingQueueProgress(null);
  };

  const moveSelectedItemsToWorkspace = async (): Promise<MovedItem[]> => {
    setHighlightWorkspace(false);
    setHasChanges(true);

    const newMovedItems: MovedItem[] = [];

    for (const itemId of Object.keys(selectedItems)) {
      for (const folder of availableDocs.items) {
        const foundItem = folder.items.find((file) => file.id === itemId);
        if (foundItem) {
          newMovedItems.push({ ...foundItem, folderName: folder.name });
          break;
        }
      }
    }

    let totalTokenCount = 0;
    newMovedItems.forEach((item) => {
      const { cached, token_count_estimate } = item;
      if (!cached) {
        totalTokenCount += token_count_estimate || 0;
      }
    });

    // Do not do cost estimation unless the embedding engine is OpenAi.
    if (systemSettings?.EMBEDDING_ENGINE === "openai") {
      const COST_PER_TOKEN =
        MODEL_COSTS[
          systemSettings?.EmbeddingModelPref || "text-embedding-ada-002"
        ];

      const dollarAmount = (totalTokenCount / 1000) * COST_PER_TOKEN;
      setEmbeddingsCost(dollarAmount);
    }

    const combineMovedItems = [...movedItems, ...newMovedItems];
    setMovedItems(combineMovedItems);

    const newAvailableDocs = JSON.parse(
      JSON.stringify(availableDocs)
    ) as FilesData;
    const newWorkspaceDocs = JSON.parse(
      JSON.stringify(workspaceDocs)
    ) as FilesData;

    for (const itemId of Object.keys(selectedItems)) {
      let foundItem: FileItem | null = null;
      let foundFolderIndex: number | null = null;

      newAvailableDocs.items = newAvailableDocs.items.map(
        (folder, folderIndex) => {
          const remainingItems = folder.items.filter((file) => {
            const match = file.id === itemId;
            if (match) {
              foundItem = { ...file, folderName: folder.name } as MovedItem;
              foundFolderIndex = folderIndex;
            }
            return !match;
          });

          return {
            ...folder,
            items: remainingItems,
          };
        }
      );

      if (foundItem && foundFolderIndex !== null) {
        newWorkspaceDocs.items[foundFolderIndex].items.push(foundItem);
      }
    }

    setAvailableDocs(newAvailableDocs);
    setWorkspaceDocs(newWorkspaceDocs);
    setSelectedItems({});

    return combineMovedItems;
  };

  const handleBulkProcessingComplete = async (
    _updatedWorkspace: any
  ): Promise<void> => {
    // Close the bulk processing modal
    setShowBulkProgress(false);
    setBulkJobId(null);

    // Show success message
    showToast(t("show-toast.bulk-processing-completed"), "success", {
      clear: true,
    });

    // Refresh the workspace data
    await fetchKeys(true, documentDraftingSelected);
  };

  const handleBulkProcessingClose = (): void => {
    setShowBulkProgress(false);
    setBulkJobId(null);
  };

  return (
    <div className="flex flex-col h-full upload-modal z-10">
      <Directory
        files={availableDocs}
        setFiles={setAvailableDocs}
        loading={loading}
        loadingMessage={loadingMessage}
        setLoading={setLoading}
        workspace={workspace}
        fetchKeys={fetchKeys}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        isUpload={isUpload}
        setIsUpload={setIsUpload}
        hasChanges={hasChanges}
        setHasChanges={setHasChanges}
        setHighlightWorkspace={setHighlightWorkspace}
        moveToWorkspace={() => {
          moveSelectedItemsToWorkspace();
        }}
        setLoadingMessage={setLoadingMessage}
        documentDraftingSelected={documentDraftingSelected}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        onFileChange={() => {
          handleFileChange({
            tokenCount: 0,
            promptLimit: 0,
          });
        }}
      />
      {!documentDraftingSelected && (
        <div className="upload-modal-arrow">
          <ArrowsDownUp className="text-foreground text-base font-bold rotate-90 w-11 h-11" />
        </div>
      )}
      {!documentDraftingSelected && (
        <WorkspaceDirectory
          workspace={workspace}
          files={{
            items: workspaceDocs.items.flatMap((folder) =>
              folder.items.map((file) => ({
                ...file,
                type: "file" as const,
                title: file.name || file.path || "",
                folderName: folder.name,
              }))
            ),
          }}
          highlightWorkspace={highlightWorkspace}
          loading={loading}
          loadingMessage={loadingMessage}
          setLoadingMessage={setLoadingMessage}
          setLoading={setLoading}
          fetchKeys={fetchKeys}
          hasChanges={() => hasChanges}
          saveChanges={() => updateWorkspace()}
          embeddingCosts={embeddingsCost}
          movedItems={new Set(movedItems.map((item) => item.id))}
          documentDraftingSelected={documentDraftingSelected}
          onFileChange={() => {
            handleFileChange({
              tokenCount: 0,
              promptLimit: 0,
            });
          }}
        />
      )}

      {/* Bulk Processing Progress Modal */}
      <BulkProcessingProgress
        isOpen={showBulkProgress}
        onClose={handleBulkProcessingClose}
        jobId={bulkJobId || ""}
        workspaceSlug={workspace?.slug || ""}
        onComplete={handleBulkProcessingComplete}
        zIndex={9999}
      />

      {/* Embedding Queue Progress Overlay */}
      {embeddingQueueProgress &&
        typeof window !== "undefined" &&
        ReactDOM.createPortal(
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[10000]">
            <div className="bg-elevated border border-border rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold text-foreground mb-4">
                {t(
                  "bulk-upload.embedding-progress-title",
                  "Processing Embeddings"
                )}
              </h3>
              <div className="space-y-3">
                <div className="text-sm text-muted">
                  {t("bulk-upload.embedding-file-progress", {
                    defaultValue: `Processing file ${embeddingQueueProgress.current} of ${embeddingQueueProgress.total}`,
                    current: embeddingQueueProgress.current,
                    total: embeddingQueueProgress.total,
                  })}
                </div>
                <div className="w-full bg-border rounded-full h-2">
                  <div
                    className="h-2 bg-primary rounded-full transition-all duration-300"
                    style={{
                      width: `${(embeddingQueueProgress.current / embeddingQueueProgress.total) * 100}%`,
                    }}
                  />
                </div>
                <div className="text-xs text-muted text-center">
                  {t("bulk-upload.embedding-files-processed", {
                    defaultValue: `${embeddingQueueProgress.processed} files processed`,
                    count: embeddingQueueProgress.processed,
                  })}
                </div>
                <div className="text-xs text-muted text-center mt-4">
                  {t(
                    "bulk-upload.embedding-rate-limit-info",
                    "Processing in batches to prevent server overload..."
                  )}
                </div>
              </div>
            </div>
          </div>,
          document.body
        )}
    </div>
  );
}
