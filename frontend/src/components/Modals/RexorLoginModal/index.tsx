import React, { useState, useEffect, FormEvent, ChangeEvent } from "react";
import {
  useRexorGetSavedUsername,
  useRexorLogin,
  useRexorLogout,
  useRexorCheckLoginStatus,
  useRexorLoading,
} from "@/stores/rexorStore";
import { useTranslation } from "react-i18next";
import Modal from "@/components/ui/Modal";
import { Button } from "@/components/Button";

interface RexorLoginModalProps {
  show: boolean;
  onClose?: () => void;
  onSuccess?: (token: string) => void;
  onLogout?: () => void;
}

export default function RexorLoginModal({
  show,
  onClose,
  onSuccess,
  onLogout,
}: RexorLoginModalProps): React.ReactElement | null {
  const { t } = useTranslation();

  const getSavedUsername = useRexorGetSavedUsername();
  const login = useRexorLogin();
  const logout = useRexorLogout();
  const checkLoginStatus = useRexorCheckLoginStatus();
  const storeLoading = useRexorLoading();

  const [username, setUsername] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [error, setError] = useState<string>("");

  useEffect(() => {
    if (show) {
      const savedUsername = getSavedUsername();
      if (savedUsername) {
        setUsername(savedUsername);
      }
    }
  }, [show, getSavedUsername]);

  const handleLogin = async (e: FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault();
    setError("");

    try {
      const token = await login(username, password, true);
      if (!token) {
        setError(t("rexor.account.no-token"));
        return;
      }
      checkLoginStatus();
      onSuccess?.(token);
      onClose?.();
    } catch (err: any) {
      setError(err.message);
    }
  };

  const handleLogout = (e: React.MouseEvent<HTMLButtonElement>): void => {
    e.preventDefault();
    setUsername("");
    setPassword("");
    setError("");
    logout();
    onLogout?.();
    onClose?.();
  };

  if (!show) return null;

  return (
    <Modal
      isOpen={show}
      onClose={onClose}
      title={t("rexor.account.title")}
      footer={
        <>
          <Button
            onClick={handleLogout}
            variant="outline"
            aria-label="rexor-logout"
          >
            {t("rexor.account.logout")}
          </Button>
          <Button
            type="submit"
            form="rexor-login"
            disabled={storeLoading}
            aria-label="rexor-login"
          >
            {storeLoading ? t("login.logging") : t("login.button")}
          </Button>
        </>
      }
    >
      <form onSubmit={handleLogin} className="py-2 px-4" id="rexor-login">
        <div className="mb-4">
          <label className="block mb-2 text-foreground text-smt">
            {t("rexor.account.username")}
          </label>
          <input
            type="text"
            value={username}
            onChange={(e: ChangeEvent<HTMLInputElement>) =>
              setUsername(e.target.value)
            }
            className="dark-input-mdl w-full text-foreground text-sm rounded-md block p-2"
            required
          />
        </div>

        <div className="mb-4">
          <label className="block mb-2 text-foreground text-sm">
            {t("rexor.account.password")}
          </label>
          <input
            type="password"
            value={password}
            onChange={(e: ChangeEvent<HTMLInputElement>) =>
              setPassword(e.target.value)
            }
            className="dark-input-mdl w-full text-foreground text-sm rounded-md block p-2"
            required
          />
        </div>

        {error && <div className="text-red-500 mb-4">{error}</div>}
      </form>
    </Modal>
  );
}
