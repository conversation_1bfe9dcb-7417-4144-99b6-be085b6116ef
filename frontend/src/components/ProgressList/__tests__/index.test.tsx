/// <reference types="@testing-library/jest-dom" />
import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, jest, beforeEach } from "@jest/globals";
import "@testing-library/jest-dom";
import ProgressList from "../index";

// Mock the useThreadProgress hook
jest.mock("@/hooks/useThreadProgress", () => {
  return jest.fn(() => ({
    isActive: false,
    currentStep: 1,
    totalSteps: 7,
    flowType: null,
    stepDetails: [],
  }));
});

// Mock translation
const mockT = jest.fn((key: string, options?: Record<string, any>) => {
  const translations: Record<string, string> = {
    "chatProgress.step": "Step",
    "chatProgress.noThreadSelected": "No thread selected",
    "chatProgress.noActiveProgress": "No active progress",
    "chatProgress.processing": "Processing",
    "chatProgress.types.cdb.step1.label": "Step 1: Document Analysis",
    "chatProgress.types.cdb.step1.labelNeutral": "Step 1: Analyze Documents",
    "chatProgress.types.cdb.step1.labelFinished": "Step 1: Analyzed Documents",
    "chatProgress.types.cdb.step1.desc": "Analyzing uploaded documents",
    "chatProgress.types.cdb.step2.label": "Step 2: Content Processing",
    "chatProgress.types.cdb.step2.labelNeutral": "Step 2: Process Content",
    "chatProgress.types.cdb.step2.labelFinished": "Step 2: Processed Content",
    "chatProgress.types.cdb.step2.desc": "Processing document content",
    "cdbProgress.general.placeholderSubTask": "Sub-task {{index}}",
    "cdbProgress.general.placeholderSubTaskOngoing":
      "Processing sub-task {{index}}",
    "cdbProgress.subTasks.mappingSections":
      "Mapping sections for: {{filename}}...",
    "cdbProgress.subTasks.mappingSectionsNeutral":
      "Map sections for: {{filename}}...",
    "cdbProgress.subTasks.mappingSectionsFinished":
      "Mapped sections for: {{filename}}",
    "cdbProgress.subTasks.identifyingIssues":
      "Identifying issues for Section {{sectionNumber}}: {{sectionTitle}}...",
    "cdbProgress.subTasks.identifyingIssuesNeutral":
      "Identify issues for Section {{sectionNumber}}: {{sectionTitle}}...",
    "cdbProgress.subTasks.identifyingIssuesFinished":
      "Identified issues for Section {{sectionNumber}}: {{sectionTitle}}",
    "cdbProgress.subTasks.processingDocument":
      "Processing: {{filename}} - {{action}}...",
    "cdbProgress.subTasks.processingDocumentNeutral":
      "Process: {{filename}} - {{action}}...",
    "cdbProgress.subTasks.processingDocumentFinished":
      "Processed: {{filename}} - {{action}}",
    "cdbProgress.subTasks.actions.generatingDescription":
      "Generating description",
    "cdbProgress.subTasks.generatingMemo":
      "Generating memo for: {{issueText}}...",
    "cdbProgress.subTasks.generatingMemoNeutral":
      "Generate memo for: {{issueText}}...",
    "cdbProgress.subTasks.generatingMemoFinished":
      "Generated memo for: {{issueText}}",
    "cdbProgress.subTasks.finishedMemo": "Finished memo for: {{issueText}}",
    "cdbProgress.subTasks.resolvingWorkspace":
      "Resolving workspace for: {{issueText}}...",
    "cdbProgress.subTasks.resolvingWorkspaceNeutral":
      "Resolve workspace for: {{issueText}}...",
    "cdbProgress.subTasks.resolvingWorkspaceFinished":
      "Resolved workspace for: {{issueText}}",
    "cdbProgress.subTasks.memoFor":
      "Memo for: {{issueText}} (ws: {{workspaceSlug}})",
    "cdbProgress.subTasks.memoForNeutral":
      "Generate memo for: {{issueText}} (ws: {{workspaceSlug}})",
    "cdbProgress.subTasks.memoForFinished":
      "Generated memo for: {{issueText}} (ws: {{workspaceSlug}})",
    "cdbProgress.subTasks.errorNoWorkspace":
      "Error - No workspace for: {{issueText}}...",
    // StreamDD translation keys
    "streamdd_progress_modal.sub_step_chunk_label":
      "Processing document group {{index}}",
    "streamdd_progress_modal.sub_step_chunk_label_neutral":
      "Process document group {{index}}",
    "streamdd_progress_modal.sub_step_chunk_label_finished":
      "Processed document group {{index}}",
    "streamdd_progress_modal.sub_step_memo_label":
      "Fetched legal data from {{workspaceSlug}}",
    "streamdd_progress_modal.sub_step_memo_label_neutral":
      "Fetch legal data from {{workspaceSlug}}",
    "streamdd_progress_modal.sub_step_memo_label_finished":
      "Fetched legal data from {{workspaceSlug}}",
  };

  if (options && typeof options === "object" && !Array.isArray(options)) {
    let result = translations[key] || key;
    Object.keys(options).forEach((optionKey) => {
      result = result.replace(`{{${optionKey}}}`, String(options[optionKey]));
    });
    return result;
  }

  return translations[key] || key;
});

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: mockT,
  }),
}));

const useThreadProgress = require("@/hooks/useThreadProgress");

// Type cast helper for jest-dom matchers
const expectToBeInTheDocument = (element: HTMLElement | null) =>
  (expect(element) as any).toBeInTheDocument();
const expectNotToBeInTheDocument = (element: HTMLElement | null) =>
  (expect(element) as any).not.toBeInTheDocument();

describe("ProgressList", () => {
  const MOCK_THREAD_ID = "thread-123";

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Reset the mock implementation
    useThreadProgress.mockReturnValue({
      isActive: false,
      currentStep: 1,
      totalSteps: 7,
      flowType: null,
      stepDetails: [],
    });
  });

  describe("empty states", () => {
    it("should show no thread selected message when no threadSlug provided", () => {
      render(<ProgressList threadSlug={null} />);
      expectToBeInTheDocument(screen.getByText("No thread selected"));
    });

    it("should show no thread selected message when threadSlug is null", () => {
      render(<ProgressList threadSlug={null} />);
      expectToBeInTheDocument(screen.getByText("No thread selected"));
    });

    it("should show no active progress message when process is not active", () => {
      useThreadProgress.mockReturnValue({
        isActive: false,
        currentStep: 1,
        totalSteps: 7,
        flowType: null,
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);
      expectToBeInTheDocument(screen.getByText("No active progress"));
    });
  });

  describe("step rendering", () => {
    it("should render basic steps with default labels", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 3,
        flowType: null,
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      expectToBeInTheDocument(screen.getByText("Step 1"));
      expectToBeInTheDocument(screen.getByText("Step 2"));
      expectToBeInTheDocument(screen.getByText("Step 3"));
    });

    it("should render flow-specific step labels when flowType is provided", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 2,
        flowType: "cdb",
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      expectToBeInTheDocument(screen.getByText("Step 1: Document Analysis")); // Step 1 is in progress
      expectToBeInTheDocument(screen.getByText("Step 2: Process Content")); // Step 2 is pending, so shows neutral form
    });

    it("should use backend message when available in stepDetails", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 3,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "complete",
            message: "Custom step 1 message",
            subTasks: [],
          },
          {
            step: 2,
            status: "in_progress",
            message: "Custom step 2 message",
            subTasks: [],
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      expectToBeInTheDocument(screen.getByText("Custom step 1 message"));
      expectToBeInTheDocument(screen.getByText("Custom step 2 message"));
      expectToBeInTheDocument(screen.getByText("Step 3")); // No detail, so falls back to default
    });
  });

  describe("step status and icons", () => {
    it("should show correct icons for different step statuses", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 3,
        totalSteps: 5,
        flowType: null,
        stepDetails: [
          { step: 1, status: "complete", subTasks: [] },
          { step: 2, status: "complete", subTasks: [] },
          { step: 3, status: "in_progress", subTasks: [] },
          { step: 4, status: "pending", subTasks: [] },
          { step: 5, status: "pending", subTasks: [] },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Complete steps should have checkmark icons (we can't easily test for specific icons)
      // But we can verify the steps are rendered with proper status
      const steps = screen.getAllByText(/Step \d/);
      (expect(steps) as any).toHaveLength(5);
    });

    it("should derive correct status for steps without explicit stepDetails", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 3,
        totalSteps: 5,
        flowType: null,
        stepDetails: [], // No explicit step details
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Should render all steps with derived statuses
      expectToBeInTheDocument(screen.getByText("Step 1"));
      expectToBeInTheDocument(screen.getByText("Step 2"));
      expectToBeInTheDocument(screen.getByText("Step 3"));
      expectToBeInTheDocument(screen.getByText("Step 4"));
      expectToBeInTheDocument(screen.getByText("Step 5"));
    });

    it("should handle when process is inactive but currentStep > 1", () => {
      useThreadProgress.mockReturnValue({
        isActive: false,
        currentStep: 3,
        totalSteps: 5,
        flowType: null,
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Should still render steps
      expectToBeInTheDocument(screen.getByText("Step 1"));
      expectToBeInTheDocument(screen.getByText("Step 2"));
      expectToBeInTheDocument(screen.getByText("Step 3"));
    });
  });

  describe("sub-tasks", () => {
    it("should render sub-tasks when they exist", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 2,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "in_progress",
            message: "Step with sub-tasks",
            subTasks: [
              {
                subStep: 1,
                status: "complete",
                message: "Sub-task 1 complete",
              },
              {
                subStep: 2,
                status: "in_progress",
                message: "Sub-task 2 processing",
              },
            ],
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      expectToBeInTheDocument(screen.getByText("Step with sub-tasks"));

      // Click to expand sub-tasks
      fireEvent.click(screen.getByText("Step with sub-tasks"));

      expectToBeInTheDocument(screen.getByText("Sub-task 1 complete"));
      expectToBeInTheDocument(screen.getByText("Sub-task 2 processing"));
      expectToBeInTheDocument(screen.getByText("Processing..."));
    });

    it("should translate subtask messages based on status", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 3,
        totalSteps: 3,
        flowType: null,
        stepDetails: [
          {
            step: 3,
            status: "in_progress",
            message: "Step with status-based translations",
            subTasks: [
              {
                subStep: 1,
                status: "pending",
                message: "Mapping sections for: test-file.pdf...",
              },
              {
                subStep: 2,
                status: "in_progress",
                message: "Identifying issues for Section 1: Test Section...",
              },
              {
                subStep: 3,
                status: "complete",
                message: "Processing: document.pdf - Generating description...",
              },
            ],
          },
        ],
      });

      render(<ProgressList threadSlug="test-thread" />);

      // Expand the step to see subtasks
      fireEvent.click(screen.getByText("Step with status-based translations"));

      // Check that different statuses use different translation keys
      expectToBeInTheDocument(
        screen.getByText("Map sections for: test-file...")
      ); // pending -> neutral (filename cleaned)
      expectToBeInTheDocument(
        screen.getByText("Identifying issues for Section 1: Test Section...")
      ); // in_progress -> ongoing
      expectToBeInTheDocument(
        screen.getByText("Processed: document - Generating description")
      ); // complete -> finished
    });

    it("should show placeholder message for sub-tasks without custom message", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "in_progress",
            message: "Step with sub-tasks",
            subTasks: [
              { subStep: 1, status: "complete" }, // No message - should use ongoing form for complete
              { subStep: 2, status: "in_progress" }, // No message - should use ongoing form for in_progress
              { subStep: 3, status: "pending" }, // No message - should use infinitive form for pending
            ],
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Click to expand sub-tasks
      fireEvent.click(screen.getByText("Step with sub-tasks"));

      expectToBeInTheDocument(screen.getByText("Processing sub-task 1")); // complete status uses ongoing form
      expectToBeInTheDocument(screen.getByText("Processing sub-task 2")); // in_progress status uses ongoing form
      expectToBeInTheDocument(screen.getByText("Sub-task 3")); // pending status uses infinitive form
    });

    it("should toggle sub-task visibility when clicking on expandable step", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "in_progress",
            message: "Expandable step",
            subTasks: [
              { subStep: 1, status: "complete", message: "Hidden sub-task" },
            ],
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Sub-task should not be visible initially
      expectNotToBeInTheDocument(screen.queryByText("Hidden sub-task"));

      // Click to expand
      fireEvent.click(screen.getByText("Expandable step"));
      expectToBeInTheDocument(screen.getByText("Hidden sub-task"));

      // Click to collapse
      fireEvent.click(screen.getByText("Expandable step"));
      expectNotToBeInTheDocument(screen.queryByText("Hidden sub-task"));
    });

    it("should not make step clickable if it has no sub-tasks", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "complete",
            message: "Simple step",
            subTasks: [],
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      const stepElement = screen.getByText("Simple step");
      // Should not have hover effects or be clickable (hard to test without DOM inspection)
      expectToBeInTheDocument(stepElement);
    });
  });

  describe("step expansion state", () => {
    it("should maintain independent expansion state for multiple steps", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 2,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "complete",
            message: "First expandable step",
            subTasks: [{ subStep: 1, message: "First sub-task" }],
          },
          {
            step: 2,
            status: "in_progress",
            message: "Second expandable step",
            subTasks: [{ subStep: 1, message: "Second sub-task" }],
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Expand first step
      fireEvent.click(screen.getByText("First expandable step"));
      expectToBeInTheDocument(screen.getByText("First sub-task"));
      expectNotToBeInTheDocument(screen.queryByText("Second sub-task"));

      // Expand second step (first should remain expanded)
      fireEvent.click(screen.getByText("Second expandable step"));
      expectToBeInTheDocument(screen.getByText("First sub-task"));
      expectToBeInTheDocument(screen.getByText("Second sub-task"));

      // Collapse first step (second should remain expanded)
      fireEvent.click(screen.getByText("First expandable step"));
      expectNotToBeInTheDocument(screen.queryByText("First sub-task"));
      expectToBeInTheDocument(screen.getByText("Second sub-task"));
    });
  });

  describe("edge cases", () => {
    it("should handle empty stepDetails gracefully", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 3,
        flowType: null,
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Should still render steps with default labels
      expectToBeInTheDocument(screen.getByText("Step 1"));
      expectToBeInTheDocument(screen.getByText("Step 2"));
      expectToBeInTheDocument(screen.getByText("Step 3"));
    });

    it("should handle totalSteps of 0", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 0,
        flowType: null,
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Should not render any steps
      expectNotToBeInTheDocument(screen.queryByText(/Step \d/));
    });

    it("should handle missing subTasks array", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "complete",
            message: "Step without subTasks array",
            // subTasks property missing
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      expectToBeInTheDocument(screen.getByText("Step without subTasks array"));
      // Should not crash and step should not be expandable
    });

    it("should handle invalid step numbers in stepDetails", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 3,
        flowType: null,
        stepDetails: [
          {
            step: 999,
            status: "complete",
            message: "Invalid step",
            subTasks: [],
          },
          { step: 1, status: "complete", message: "Valid step", subTasks: [] },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Should render all totalSteps regardless of invalid stepDetails
      expectToBeInTheDocument(screen.getByText("Valid step")); // Step 1 has detail
      expectToBeInTheDocument(screen.getByText("Step 2")); // Step 2 has no detail, uses default
      expectToBeInTheDocument(screen.getByText("Step 3")); // Step 3 has no detail, uses default
      expectNotToBeInTheDocument(screen.queryByText("Invalid step")); // Step 999 is outside range
    });

    it("should handle streamdd translation keys with status-based variations", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "in_progress",
            message: "Processing documents",
            subTasks: [
              {
                subStep: 1,
                status: "pending",
                label: "streamdd_progress_modal.sub_step_chunk_label",
                labelArgs: { index: 1 },
              },
              {
                subStep: 2,
                status: "in_progress",
                label: "streamdd_progress_modal.sub_step_chunk_label",
                labelArgs: { index: 2 },
              },
              {
                subStep: 3,
                status: "complete",
                label: "streamdd_progress_modal.sub_step_chunk_label",
                labelArgs: { index: 3 },
              },
              {
                subStep: 4,
                status: "pending",
                label: "streamdd_progress_modal.sub_step_memo_label",
                labelArgs: { workspaceSlug: "test-workspace" },
              },
            ],
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Expand the step to see subtasks
      fireEvent.click(screen.getByText("Processing documents"));

      // Check that status-based translations are used correctly
      expectToBeInTheDocument(screen.getByText("Process document group 1")); // pending -> neutral
      expectToBeInTheDocument(screen.getByText("Processing document group 2")); // in_progress -> base
      expectToBeInTheDocument(screen.getByText("Processed document group 3")); // complete -> finished
      expectToBeInTheDocument(
        screen.getByText("Fetch legal data from test-workspace")
      ); // pending -> neutral
    });
  });

  describe("scrolling", () => {
    it("should have scrollable container with proper max height", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 5,
        totalSteps: 10,
        flowType: null,
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Test that container exists (we can't easily test CSS in jsdom)
      const container = screen.getByText("Step 1").closest("div");
      expectToBeInTheDocument(container);
    });
  });
});
