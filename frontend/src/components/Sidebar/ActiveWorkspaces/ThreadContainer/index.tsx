import React, { useEffect, useState, useCallback, useRef } from "react";
import Workspace, {
  type WorkspaceData as WorkspaceType,
} from "@/models/workspace";
import paths from "@/utils/paths";
import showToast from "@/utils/toast";
import { CircleNotch, Trash } from "@phosphor-icons/react";
import { LuPlus } from "react-icons/lu";
import ThreadItem from "./ThreadItem";
import { useParams, useNavigate, useMatch } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/Button";
import useNavigationWithInvoiceCheck from "@/hooks/useNavigationWithInvoiceCheck";
import InvoiceReferenceNavigationModal from "@/components/Modals/InvoiceReferenceNavigationModal";

export const THREAD_RENAME_EVENT = "renameThread";

interface SidebarThread {
  id: number;
  slug: string;
  name: string;
  deleted?: boolean;
}

interface ThreadContainerProps {
  workspace: WorkspaceType;
  isActive?: boolean;
}

interface ThreadRenameEvent extends CustomEvent {
  detail: {
    threadSlug: string;
    newName: string;
  };
}

// Remove unused interface - using inline response typing

interface ThreadCreateResponse {
  thread: SidebarThread;
  error?: string;
}

function ThreadContainer({
  workspace,
}: ThreadContainerProps): JSX.Element | null {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { threadSlug = null } = useParams<{ threadSlug?: string }>();
  const isOnThreadsPage = useMatch(`/workspace/:${workspace?.slug}`);
  const isOnSpecificThreadPage = useMatch(
    `/workspace/:${workspace?.slug}/t/:${threadSlug}`
  );

  const isThreadRoute = isOnThreadsPage || isOnSpecificThreadPage;

  const [threads, setThreads] = useState<SidebarThread[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [ctrlPressed, setCtrlPressed] = useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const deleteTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const chatHandler = useCallback((event: Event) => {
    const renameEvent = event as ThreadRenameEvent;
    const { threadSlug, newName } = renameEvent.detail;
    setThreads((prevThreads) =>
      prevThreads.map((thread) => {
        if (thread.slug === threadSlug) {
          return { ...thread, name: newName };
        }
        return thread;
      })
    );
  }, []);

  useEffect(() => {
    window.addEventListener(THREAD_RENAME_EVENT, chatHandler);
    return () => {
      window.removeEventListener(THREAD_RENAME_EVENT, chatHandler);
    };
  }, [chatHandler]);

  useEffect(() => {
    let mounted = true;
    async function fetchThreads(): Promise<void> {
      if (!workspace?.slug) return;

      try {
        const response = await Workspace.threads.all(workspace.slug);
        if (!mounted) return;

        setLoading(false);
        setThreads(
          response.threads.map((t: any) => ({
            id: typeof t.id === "string" ? parseInt(t.id) : t.id,
            slug: t.slug,
            name: t.name || "",
            deleted: false,
          }))
        );

        // If there are no threads, automatically create a new one
        if (response.threads.length === 0) {
          setLoading(true); // Show loading state while creating thread
          const createResponse = await Workspace.threads.new(workspace.slug);
          if (!mounted) return;

          if (createResponse.error) {
            showToast(
              t("toast.workspace.thread-create-failed", {
                error: createResponse.error,
              }),
              "error",
              {
                clear: true,
              }
            );
            setLoading(false);
            return;
          }

          if (createResponse.thread) {
            try {
              // Recheck that no threads were created while we were creating this one
              const latestResponse = await Workspace.threads.all(
                workspace.slug
              );
              if (!mounted) return;

              if (latestResponse.threads.length === 0) {
                setThreads([
                  {
                    id:
                      typeof createResponse.thread.id === "string"
                        ? parseInt(createResponse.thread.id)
                        : createResponse.thread.id,
                    slug: createResponse.thread.slug,
                    name: createResponse.thread.name || "",
                    deleted: false,
                  },
                ]); // Update local state immediately
                navigate(
                  paths.workspace.thread(
                    workspace.slug,
                    createResponse.thread.slug
                  ),
                  {
                    replace: true,
                  }
                );
              } else {
                setThreads(
                  latestResponse.threads.map((t: any) => ({
                    id: typeof t.id === "string" ? parseInt(t.id) : t.id,
                    slug: t.slug,
                    name: t.name || "",
                    deleted: false,
                  }))
                );
                // Navigate to the last thread in latestThreads
                const lastThread =
                  latestResponse.threads[latestResponse.threads.length - 1];
                navigate(
                  paths.workspace.thread(workspace.slug, lastThread.slug),
                  {
                    replace: true,
                  }
                );
              }
            } catch {
              if (!mounted) return;
              showToast(t("errors.thread-fetch-failed"), "error", {
                clear: true,
              });
            }
          }
          setLoading(false);
        } else if (
          !threadSlug ||
          !response.threads.find((thread) => thread.slug === threadSlug)
        ) {
          if (!isThreadRoute) return;

          // If there are threads but no specific thread is selected
          // OR if the current threadSlug doesn't exist in the threads list
          // navigate to the last created thread
          const lastThread = response.threads[response.threads.length - 1];
          navigate(paths.workspace.thread(workspace.slug, lastThread.slug), {
            replace: true,
          });
        }
      } catch {
        if (!mounted) return;
        setLoading(false);
        showToast(t("errors.thread-fetch-failed"), "error", { clear: true });
      }
    }

    fetchThreads();
    return () => {
      mounted = false;
    };
  }, [workspace?.slug, t, navigate, threadSlug, isThreadRoute]);

  // Enable toggling of bulk-deletion by holding meta-key (ctrl on win and cmd/fn on others)
  useEffect(() => {
    const resetState = (): void => {
      setCtrlPressed(false);
      setThreads((prev) =>
        prev.map((t) => {
          return { ...t, deleted: false };
        })
      );
    };

    const handleKeyDown = (event: KeyboardEvent): void => {
      if (["Control", "Meta"].includes(event.key) && !event.shiftKey) {
        setCtrlPressed(true);
      }
    };

    const handleKeyUp = (event: KeyboardEvent): void => {
      if (["Control", "Meta"].includes(event.key)) {
        resetState();
      }
    };

    const handleBlur = (): void => {
      resetState();
    };

    const handleVisibilityChange = (): void => {
      if (document.hidden) {
        resetState();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("keyup", handleKeyUp);
    window.addEventListener("blur", handleBlur);
    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("keyup", handleKeyUp);
      window.removeEventListener("blur", handleBlur);
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, []);

  const toggleForDeletion = useCallback((id: number): void => {
    setThreads((prev) =>
      prev.map((t) => {
        if (t.id !== id) return t;
        return { ...t, deleted: !t.deleted };
      })
    );
  }, []);

  const handleDeleteAll = useCallback(async (): Promise<void> => {
    if (!workspace?.slug) return;
    try {
      setIsDeleting(true);
      const slugs = threads
        .filter((t) => t.deleted === true)
        .map((t) => t.slug);
      const success = await Workspace.threads.deleteBulk(workspace.slug, slugs);

      if (success) {
        const remainingThreads = threads.filter((t) => !t.deleted);
        setThreads(remainingThreads);
        showToast(t("show-toast.threads-deleted"), "success", { clear: true });

        // If all threads were deleted, create a new one
        if (remainingThreads.length === 0) {
          setLoading(true);
          const createResponse: any = await Workspace.threads.new(
            workspace.slug
          );
          const threadCreateResponse: ThreadCreateResponse = {
            thread: {
              id:
                typeof createResponse.thread.id === "string"
                  ? parseInt(createResponse.thread.id)
                  : createResponse.thread.id,
              slug: createResponse.thread.slug,
              name: createResponse.thread.name || "",
              deleted: false,
            },
            error: createResponse.error,
          };
          if (threadCreateResponse.error) {
            showToast(
              t("toast.workspace.thread-create-failed", {
                error: threadCreateResponse.error,
              }),
              "error",
              {
                clear: true,
              }
            );
            setLoading(false);
            return;
          }
          setThreads([threadCreateResponse.thread]);
          navigate(
            paths.workspace.thread(
              workspace.slug,
              threadCreateResponse.thread.slug
            ),
            {
              replace: true,
            }
          );
        }
      } else {
        throw new Error("Failed to delete threads");
      }
    } catch {
      showToast(t("errors.delete-threads-failed"), "error", { clear: true });
      // Reset deleted state on error
      setThreads((prev) => prev.map((t) => ({ ...t, deleted: false })));
    } finally {
      setIsDeleting(false);
    }
  }, [workspace?.slug, threads, t, navigate]);

  const removeThread = useCallback(
    (threadId: number): void => {
      if (!workspace?.slug) return;
      // Get the thread being deleted
      const deletedThread = threads.find((t) => t.id === threadId);
      const isActiveThread = deletedThread?.slug === threadSlug;
      const remainingThreads = threads.filter((t) => t.id !== threadId);

      // If we're deleting the active thread, handle navigation first
      if (isActiveThread) {
        if (remainingThreads.length > 0) {
          // Navigate to the last remaining thread
          const lastThread = remainingThreads[remainingThreads.length - 1];
          navigate(paths.workspace.thread(workspace.slug, lastThread.slug), {
            replace: true,
          });
        }
      }

      // For single thread deletion, we'll remove it immediately from the UI
      setThreads(remainingThreads);

      // Clear any existing timeout
      if (deleteTimeoutRef.current) {
        clearTimeout(deleteTimeoutRef.current);
      }

      // Set new timeout and store the reference for cleanup and new thread creation
      deleteTimeoutRef.current = setTimeout(async () => {
        // Use state updater to get latest threads state
        setThreads((currentThreads) => {
          // If this was the last thread, create a new one
          if (currentThreads.length === 0) {
            setLoading(true);
            Workspace.threads
              .new(workspace.slug)
              .then((createResponse: any) => {
                const threadCreateResponse: ThreadCreateResponse = {
                  thread: {
                    id:
                      typeof createResponse.thread.id === "string"
                        ? parseInt(createResponse.thread.id)
                        : createResponse.thread.id,
                    slug: createResponse.thread.slug,
                    name: createResponse.thread.name || "",
                    deleted: false,
                  },
                  error: createResponse.error,
                };
                if (threadCreateResponse.error) {
                  showToast(
                    t("toast.workspace.thread-create-failed", {
                      error: threadCreateResponse.error,
                    }),
                    "error",
                    {
                      clear: true,
                    }
                  );
                  setLoading(false);
                  return;
                }
                setThreads([threadCreateResponse.thread]);
                navigate(
                  paths.workspace.thread(
                    workspace.slug,
                    threadCreateResponse.thread.slug
                  ),
                  {
                    replace: true,
                  }
                );
                setLoading(false);
              });
          }

          return currentThreads;
        });

        deleteTimeoutRef.current = null;
      }, 500); // Keep the 500ms delay for cleanup
    },
    [workspace?.slug, t, navigate, threads, threadSlug]
  );

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (deleteTimeoutRef.current) {
        clearTimeout(deleteTimeoutRef.current);
      }
    };
  }, []);

  // Handle null workspace after all hooks to comply with Rules of Hooks
  if (!workspace) {
    return null;
  }

  if (loading) {
    return (
      <div className="flex flex-col bg-pulse w-full h-10 items-center justify-center">
        <p className="text-xs animate-pulse text-foreground">
          {t("sidebar.thread.load-thread")}
        </p>
      </div>
    );
  }

  // Update activeThreadIdx calculation to handle edge cases
  const activeThreadIdx =
    threadSlug && threads.length > 0
      ? threads.findIndex((thread) => thread?.slug === threadSlug)
      : threads.length > 0
        ? threads.length - 1 // Default to last thread if no threadSlug
        : -1; // No threads available

  return (
    <div
      className="flex flex-col gap-y-2 mt-4 ml-4 pl-4 border-l"
      role="list"
      aria-label="Threads"
    >
      {threads.map((thread, i) => (
        <div role="listitem" key={thread.slug}>
          <ThreadItem
            idx={i}
            ctrlPressed={ctrlPressed}
            toggleMarkForDeletion={toggleForDeletion}
            activeIdx={activeThreadIdx}
            isActive={activeThreadIdx === i}
            workspace={workspace}
            onRemove={removeThread}
            thread={thread}
            hasNext={i !== threads.length - 1}
          />
        </div>
      ))}
      <div role="listitem">
        <DeleteAllThreadButton
          ctrlPressed={ctrlPressed}
          threads={threads}
          onDelete={handleDeleteAll}
          isDeleting={isDeleting}
        />
      </div>
      <div role="listitem">
        <NewThreadButton workspace={workspace} />
      </div>
    </div>
  );
}

interface NewThreadButtonProps {
  workspace: WorkspaceType;
}

function NewThreadButton({ workspace }: NewThreadButtonProps): JSX.Element {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState<boolean>(false);

  const {
    checkAndNavigate,
    showModal,
    destinationType,
    handleClearAndContinue,
    handleKeepAndContinue,
    handleCloseModal,
  } = useNavigationWithInvoiceCheck();

  const createNewThread = async (): Promise<void> => {
    setLoading(true);
    const response: any = await Workspace.threads.new(workspace.slug);
    const threadResponse: ThreadCreateResponse = {
      thread: {
        id:
          typeof response.thread.id === "string"
            ? parseInt(response.thread.id)
            : response.thread.id,
        slug: response.thread.slug,
        name: response.thread.name || "",
        deleted: false,
      },
      error: response.error,
    };
    if (threadResponse.error) {
      showToast(
        t("toast.errors.failed-create-thread", { error: threadResponse.error }),
        "error",
        {
          clear: true,
        }
      );
      setLoading(false);
      return;
    }
    navigate(
      paths.workspace.thread(workspace.slug, threadResponse.thread.slug)
    );
    setLoading(false);
  };

  const onClick = (): void => {
    checkAndNavigate(createNewThread, "thread");
  };

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={onClick}
        className="w-fit"
        disabled={loading}
      >
        {loading ? (
          <>
            <CircleNotch className=" animate-spin" />
            {t("sidebar.thread.starting-thread")}
          </>
        ) : (
          <>
            <LuPlus />
            {t("sidebar.thread.thread")}
          </>
        )}
      </Button>

      <InvoiceReferenceNavigationModal
        isOpen={showModal}
        onClose={handleCloseModal}
        onClearAndContinue={handleClearAndContinue}
        onKeepAndContinue={handleKeepAndContinue}
        destinationType={
          destinationType as "workspace" | "thread" | "location" | undefined
        }
      />
    </>
  );
}

interface DeleteAllThreadButtonProps {
  ctrlPressed: boolean;
  threads: SidebarThread[];
  onDelete: () => void;
  isDeleting: boolean;
}

function DeleteAllThreadButton({
  ctrlPressed: _ctrlPressed,
  threads,
  onDelete,
  isDeleting,
}: DeleteAllThreadButtonProps): JSX.Element | null {
  const { t } = useTranslation();
  const hasThreadsMarkedForDeletion =
    threads.filter((t) => t.deleted).length > 0;

  if (!hasThreadsMarkedForDeletion) return null;

  return (
    <Button
      onClick={onDelete}
      disabled={isDeleting}
      variant="outline"
      size="sm"
      className="w-fit"
    >
      <Trash weight="bold" />
      {t("sidebar.thread.delete")}
    </Button>
  );
}

export default React.memo(ThreadContainer, (prevProps, nextProps) => {
  // Only re-render if workspace changes
  return (
    prevProps.workspace?.id === nextProps.workspace?.id &&
    prevProps.workspace?.slug === nextProps.workspace?.slug
  );
});
