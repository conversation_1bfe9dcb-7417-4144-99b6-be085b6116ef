import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { MemoryRouter } from "react-router-dom";
import ActiveWorkspaces from "../index";
import useUser from "@/hooks/useUser";
import { useManageWorkspaceModal } from "@/components/Modals/ManageWorkspace";
import useWorkspaceStore from "@/stores/workspaceStore";
import {
  useIsDocumentDrafting,
  useSelectedModule,
  useDdShowAllWorkspaces,
  useSetDdShowAllWorkspaces,
} from "@/stores/userStore";
import useNavigationWithInvoiceCheck from "@/hooks/useNavigationWithInvoiceCheck";

// Mock dependencies
jest.mock("@/hooks/useUser");
jest.mock("@/components/Modals/ManageWorkspace");
jest.mock("@/stores/workspaceStore");
jest.mock("@/stores/userStore");
jest.mock("@/hooks/useNavigationWithInvoiceCheck");
jest.mock("react-i18next", () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

// Mock react-router-dom hooks
const mockNavigate = jest.fn();
const mockParams = { slug: "test-workspace" };

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useParams: () => mockParams,
  useNavigate: () => mockNavigate,
  useLocation: () => ({ pathname: "/workspace/test-workspace" }),
  useMatch: () => null,
}));

// Mock child components
jest.mock("../ThreadContainer", () => ({
  __esModule: true,
  default: ({ workspace }: { workspace: any }) => (
    <div data-testid={`thread-container-${workspace.slug}`}>
      Thread Container for {workspace.name}
    </div>
  ),
}));

jest.mock("../WorkspaceCreatorTooltip", () => ({
  __esModule: true,
  default: ({ userId }: { userId: string }) => (
    <div data-testid="workspace-creator-tooltip">Creator: {userId}</div>
  ),
}));

jest.mock("@/components/ShareButton", () => ({
  __esModule: true,
  default: ({ workspace, type: _type }: { workspace?: any; type?: string }) => (
    <button data-testid={`share-button-${workspace?.slug || "unknown"}`}>
      Share
    </button>
  ),
}));

jest.mock("@/components/Modals/InvoiceReferenceNavigationModal", () => ({
  __esModule: true,
  default: () => <div data-testid="invoice-modal">Invoice Modal</div>,
}));

describe("ActiveWorkspaces Component", () => {
  const mockUser = {
    id: "1",
    username: "testuser",
    email: "<EMAIL>",
    role: "admin",
  };

  const mockWorkspaces = [
    {
      id: "1",
      slug: "workspace-1",
      name: "Workspace 1",
      user_id: "1",
      created_at: "2024-01-01",
    },
    {
      id: "2",
      slug: "workspace-2",
      name: "Workspace 2",
      user_id: "2",
      created_at: "2024-01-02",
    },
    {
      id: "3",
      slug: "workspace-3",
      name: "Workspace 3",
      user_id: "1",
      created_at: "2024-01-03",
    },
  ];

  const mockManageWorkspaceModal = {
    showing: false,
    showModal: jest.fn(),
    hideModal: jest.fn(),
    refreshTrigger: false,
  };

  const mockNavigationCheck = {
    checkAndNavigate: jest.fn((callback) => callback()),
    showModal: false,
    destinationType: null,
    handleClearAndContinue: jest.fn(),
    handleKeepAndContinue: jest.fn(),
    handleCloseModal: jest.fn(),
  };

  const mockWorkspaceStore = {
    moduleWorkspaces: {
      default: mockWorkspaces,
    },
    loadingModule: {
      default: false,
    },
    fetchModuleWorkspaces: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useUser as jest.Mock).mockReturnValue({ user: mockUser });
    (useManageWorkspaceModal as jest.Mock).mockReturnValue(
      mockManageWorkspaceModal
    );
    (useWorkspaceStore as unknown as jest.Mock).mockImplementation((selector) =>
      selector(mockWorkspaceStore)
    );
    (useWorkspaceStore.getState as jest.Mock) = jest.fn(() => ({
      fetchModuleWorkspaces: mockWorkspaceStore.fetchModuleWorkspaces,
    }));
    (useIsDocumentDrafting as jest.Mock).mockReturnValue(false);
    (useSelectedModule as jest.Mock).mockReturnValue("default");
    (useDdShowAllWorkspaces as jest.Mock).mockReturnValue(true);
    (useSetDdShowAllWorkspaces as jest.Mock).mockReturnValue(jest.fn());
    (useNavigationWithInvoiceCheck as jest.Mock).mockReturnValue(
      mockNavigationCheck
    );
  });

  const renderActiveWorkspaces = (props = {}) => {
    return render(
      <MemoryRouter initialEntries={["/workspace/test-workspace"]}>
        <ActiveWorkspaces {...props} />
      </MemoryRouter>
    );
  };

  describe("Rendering", () => {
    it("should render all workspaces", () => {
      renderActiveWorkspaces();

      mockWorkspaces.forEach((workspace) => {
        expect(screen.getByText(workspace.name)).toBeInTheDocument();
      });
    });

    it("should show loading skeleton when loading", () => {
      (useWorkspaceStore as unknown as jest.Mock).mockImplementation(
        (selector) =>
          selector({
            ...mockWorkspaceStore,
            loadingModule: { default: true },
          })
      );

      renderActiveWorkspaces();

      // Check for loading skeleton elements
      const loadingElements = screen
        .getAllByRole("generic")
        .filter((el) => el.className.includes("animate-pulse"));
      expect(loadingElements.length).toBeGreaterThan(0);
    });

    it("should render empty state when no workspaces", () => {
      (useWorkspaceStore as unknown as jest.Mock).mockImplementation(
        (selector) =>
          selector({
            ...mockWorkspaceStore,
            moduleWorkspaces: { default: [] },
          })
      );

      const renderEmptyState = jest.fn(() => <div>No workspaces</div>);
      renderActiveWorkspaces({ renderEmptyState });

      expect(screen.getByText("No workspaces")).toBeInTheDocument();
      expect(renderEmptyState).toHaveBeenCalledWith(false);
    });

    it("should highlight active workspace", () => {
      mockParams.slug = "workspace-1";
      renderActiveWorkspaces();

      // Find the button for the active workspace
      const workspaceButton = screen.getByRole("button", {
        name: /Workspace 1/i,
      });
      // Active workspace uses 'secondary' variant
      expect(workspaceButton).toHaveClass("justify-start");
      expect(workspaceButton.parentElement?.parentElement).toBeTruthy();
    });
  });

  describe("Workspace Filtering", () => {
    it("should show all workspaces for admin users", () => {
      renderActiveWorkspaces();

      expect(screen.getByText("Workspace 1")).toBeInTheDocument();
      expect(screen.getByText("Workspace 2")).toBeInTheDocument();
      expect(screen.getByText("Workspace 3")).toBeInTheDocument();
    });

    it("should filter workspaces for non-default users in document drafting mode", () => {
      (useUser as jest.Mock).mockReturnValue({
        user: { ...mockUser, role: "admin" },
      });
      (useIsDocumentDrafting as jest.Mock).mockReturnValue(true);
      (useDdShowAllWorkspaces as jest.Mock).mockReturnValue(false);

      renderActiveWorkspaces();

      expect(screen.getByText("Workspace 1")).toBeInTheDocument();
      expect(screen.queryByText("Workspace 2")).not.toBeInTheDocument();
      expect(screen.getByText("Workspace 3")).toBeInTheDocument();
    });

    it("should toggle workspace visibility filter", async () => {
      const mockSetShowAll = jest.fn();
      (useSetDdShowAllWorkspaces as jest.Mock).mockReturnValue(mockSetShowAll);
      (useIsDocumentDrafting as jest.Mock).mockReturnValue(true);

      renderActiveWorkspaces();

      const toggleButton = screen.getByText("workspace.show-my");
      await userEvent.click(toggleButton);

      expect(mockSetShowAll).toHaveBeenCalledWith(false);
    });
  });

  describe("User Interactions", () => {
    it("should navigate to workspace on click", async () => {
      const user = userEvent.setup();
      // Set a different slug so clicking workspace-1 will trigger navigation
      mockParams.slug = "test-workspace";
      renderActiveWorkspaces();

      const workspaceButton = screen.getByRole("button", {
        name: /Workspace 1/i,
      });
      await user.click(workspaceButton);

      expect(mockNavigationCheck.checkAndNavigate).toHaveBeenCalled();
      // The navigation happens inside the callback passed to checkAndNavigate
      const callback = mockNavigationCheck.checkAndNavigate.mock.calls[0][0];
      callback();
      expect(mockNavigate).toHaveBeenCalledWith("/workspace/workspace-1");
    });

    it("should not navigate when clicking current workspace", async () => {
      const user = userEvent.setup();
      mockParams.slug = "workspace-1";
      renderActiveWorkspaces();

      const workspaceButton = screen.getByRole("button", {
        name: /Workspace 1/i,
      });
      await user.click(workspaceButton);

      expect(mockNavigationCheck.checkAndNavigate).not.toHaveBeenCalled();
    });

    it("should open manage workspace modal", async () => {
      renderActiveWorkspaces();

      const uploadButton = screen.getByTestId("upload-button-workspace-1");
      await userEvent.click(uploadButton);

      expect(mockNavigationCheck.checkAndNavigate).toHaveBeenCalled();
      // The modal opening happens inside the callback passed to checkAndNavigate
      const callback = mockNavigationCheck.checkAndNavigate.mock.calls[0][0];
      callback();
      expect(mockNavigate).toHaveBeenCalledWith("/workspace/workspace-1", {
        state: {
          openManageWorkspace: true,
          manageWorkspaceSlug: "workspace-1",
        },
      });
    });

    it("should navigate to workspace settings", async () => {
      renderActiveWorkspaces();

      const settingsButton = screen.getByTestId("settings-button-workspace-1");
      await userEvent.click(settingsButton);

      expect(mockNavigationCheck.checkAndNavigate).toHaveBeenCalled();
      // The navigation happens inside the callback passed to checkAndNavigate
      const callback = mockNavigationCheck.checkAndNavigate.mock.calls[0][0];
      callback();
      expect(mockNavigate).toHaveBeenCalledWith(
        "/workspace/workspace-1/settings/general-appearance"
      );
    });
  });

  describe("Document Drafting Features", () => {
    it("should show upload button in document drafting mode", async () => {
      (useIsDocumentDrafting as jest.Mock).mockReturnValue(true);
      renderActiveWorkspaces();

      // Hover over the workspace to show action buttons
      const workspace = screen.getByTestId("workspace-item-workspace-1");
      fireEvent.mouseEnter(workspace);

      await waitFor(() => {
        const uploadButton = screen.getByTestId("upload-button-workspace-1");
        expect(uploadButton).toBeInTheDocument();
      });
    });

    it("should navigate with manage workspace state on upload click", async () => {
      (useIsDocumentDrafting as jest.Mock).mockReturnValue(true);
      renderActiveWorkspaces();

      // Hover over the workspace to show action buttons
      const workspace = screen.getByTestId("workspace-item-workspace-1");
      fireEvent.mouseEnter(workspace);

      await waitFor(() => {
        expect(
          screen.getByTestId("upload-button-workspace-1")
        ).toBeInTheDocument();
      });

      const uploadButton = screen.getByTestId("upload-button-workspace-1");
      await userEvent.click(uploadButton);

      expect(mockNavigationCheck.checkAndNavigate).toHaveBeenCalled();
      // The navigation happens inside the callback passed to checkAndNavigate
      const callback = mockNavigationCheck.checkAndNavigate.mock.calls[0][0];
      callback();
      expect(mockNavigate).toHaveBeenCalledWith("/workspace/workspace-1", {
        state: {
          openManageWorkspace: true,
          manageWorkspaceSlug: "workspace-1",
        },
      });
    });

    it("should show workspace creator tooltip for non-owned workspaces", () => {
      (useIsDocumentDrafting as jest.Mock).mockReturnValue(true);
      (useDdShowAllWorkspaces as jest.Mock).mockReturnValue(true);
      renderActiveWorkspaces();

      // The tooltip should be rendered for workspace-2 (which is owned by user-2, not user-1)
      const tooltips = screen.getAllByTestId("workspace-creator-tooltip");
      expect(tooltips.length).toBeGreaterThan(0);

      // Find the tooltip for workspace owned by user 2
      const workspace2Tooltip = tooltips.find((tooltip) =>
        tooltip.textContent?.includes("Creator: 2")
      );
      expect(workspace2Tooltip).toBeInTheDocument();
    });
  });

  describe("Hover Effects", () => {
    it("should show icons on hover", async () => {
      renderActiveWorkspaces();

      const workspace = screen.getByTestId("workspace-item-workspace-1");
      // Find the hover wrapper inside the workspace item
      const hoverWrapper = workspace.querySelector(
        ".workspace-row-hover-wrapper"
      );
      const iconsContainer = screen.getByTestId("workspace-icons-workspace-1");

      // Initially icons should have opacity-0 class
      expect(iconsContainer).toHaveClass("opacity-0");

      // Mouse enter on the hover wrapper should show icons
      fireEvent.mouseEnter(hoverWrapper!);

      await waitFor(() => {
        expect(iconsContainer).toHaveClass("opacity-100");
      });

      // Mouse leave should hide icons
      fireEvent.mouseLeave(hoverWrapper!);

      await waitFor(() => {
        expect(iconsContainer).toHaveClass("opacity-0");
      });
    });
  });

  describe("Thread Container Integration", () => {
    it("should render ThreadContainer for active workspace only", () => {
      // Set the active workspace
      mockParams.slug = "workspace-1";
      renderActiveWorkspaces();

      // ThreadContainer should only be shown for the active workspace
      expect(
        screen.getByTestId(`thread-container-workspace-1`)
      ).toBeInTheDocument();

      // Other workspaces should not have ThreadContainer
      expect(
        screen.queryByTestId(`thread-container-workspace-2`)
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId(`thread-container-workspace-3`)
      ).not.toBeInTheDocument();
    });
  });

  describe("Share Button", () => {
    it("should render share button for each workspace on hover", async () => {
      const user = userEvent.setup();
      renderActiveWorkspaces();

      // Share buttons are only visible on hover
      const firstWorkspaceButton = screen.getByRole("button", {
        name: /Workspace 1/i,
      });

      // Hover over the workspace button's parent div to trigger hover state
      const workspaceRow =
        firstWorkspaceButton.parentElement?.parentElement?.parentElement;
      if (workspaceRow) {
        await user.hover(workspaceRow);
      }

      // Now the share button should be visible
      await waitFor(() => {
        // Look for the specific share button for workspace-1
        expect(
          screen.getByTestId("share-button-workspace-1")
        ).toBeInTheDocument();
      });
    });
  });

  describe("Module Selection", () => {
    it("should fetch workspaces when module changes", () => {
      renderActiveWorkspaces();

      expect(mockWorkspaceStore.fetchModuleWorkspaces).toHaveBeenCalled();
    });
  });

  describe("Edge Cases", () => {
    it("should handle missing user gracefully", () => {
      (useUser as jest.Mock).mockReturnValue({ user: null });

      renderActiveWorkspaces();

      // Should still render workspaces
      expect(screen.getByText("Workspace 1")).toBeInTheDocument();
    });

    it("should handle workspace without name", () => {
      const workspacesWithEmptyName = [{ ...mockWorkspaces[0], name: "" }];

      (useWorkspaceStore as unknown as jest.Mock).mockImplementation(
        (selector) =>
          selector({
            ...mockWorkspaceStore,
            moduleWorkspaces: { default: workspacesWithEmptyName },
          })
      );

      renderActiveWorkspaces();

      // The component renders the workspace even with empty name
      const workspaceItem = screen.getByTestId("workspace-item-workspace-1");
      expect(workspaceItem).toBeInTheDocument();

      // Check that the button exists even if the text is empty
      const buttons = screen.getAllByRole("button");
      const workspaceButton = buttons.find((btn) =>
        btn.querySelector(".truncate")
      );
      expect(workspaceButton).toBeInTheDocument();
    });
  });
});
