import React from "react";
import { render } from "@testing-library/react";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import ThreadContainer from "../ThreadContainer";
import "@testing-library/jest-dom";

// Mock child components
jest.mock("../ThreadContainer/ThreadItem", () => ({
  __esModule: true,
  default: ({ thread }) => <div data-testid="thread-item">{thread.name}</div>,
}));

// Mock react-router-dom hooks
jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useParams: () => ({ slug: "test-workspace" }),
  useNavigate: () => jest.fn(),
  useMatch: () => null,
}));

// Wrapper component for router context
const RouterWrapper = ({ children }) => (
  <BrowserRouter>{children}</BrowserRouter>
);

describe("ThreadContainer React.memo optimization", () => {
  const mockWorkspace = {
    id: 1,
    slug: "test-workspace",
    name: "Test Workspace",
    threads: [
      { id: 1, name: "Thread 1", slug: "thread-1" },
      { id: 2, name: "Thread 2", slug: "thread-2" },
    ],
  };

  const defaultProps = {
    workspace: mockWorkspace,
  };

  it("should render threads correctly", () => {
    const { container } = render(
      <RouterWrapper>
        <ThreadContainer {...defaultProps} />
      </RouterWrapper>
    );

    expect(container).toBeInTheDocument();
    // Note: The actual implementation may render differently
    // This test verifies the component renders without errors
  });

  it("should not re-render when workspace reference is same", () => {
    const { rerender } = render(
      <RouterWrapper>
        <ThreadContainer {...defaultProps} />
      </RouterWrapper>
    );

    // Re-render with same workspace reference
    expect(() => {
      rerender(
        <RouterWrapper>
          <ThreadContainer {...defaultProps} />
        </RouterWrapper>
      );
    }).not.toThrow();
  });

  it("should re-render when workspace id changes", () => {
    const { rerender } = render(
      <RouterWrapper>
        <ThreadContainer {...defaultProps} />
      </RouterWrapper>
    );

    const newWorkspace = {
      ...mockWorkspace,
      id: 2,
    };

    expect(() => {
      rerender(
        <RouterWrapper>
          <ThreadContainer workspace={newWorkspace} />
        </RouterWrapper>
      );
    }).not.toThrow();
  });

  it("should re-render when workspace slug changes", () => {
    const { rerender } = render(
      <RouterWrapper>
        <ThreadContainer {...defaultProps} />
      </RouterWrapper>
    );

    const newWorkspace = {
      ...mockWorkspace,
      slug: "different-workspace",
    };

    expect(() => {
      rerender(
        <RouterWrapper>
          <ThreadContainer workspace={newWorkspace} />
        </RouterWrapper>
      );
    }).not.toThrow();
  });

  it("should not re-render when workspace threads change but id/slug same", () => {
    const { rerender } = render(
      <RouterWrapper>
        <ThreadContainer {...defaultProps} />
      </RouterWrapper>
    );

    // Change threads array but keep id/slug same
    const updatedWorkspace = {
      ...mockWorkspace,
      threads: [{ id: 3, name: "Thread 3", slug: "thread-3" }],
    };

    expect(() => {
      rerender(
        <RouterWrapper>
          <ThreadContainer workspace={updatedWorkspace} />
        </RouterWrapper>
      );
    }).not.toThrow();
  });

  it("should handle null workspace gracefully", () => {
    // When workspace is null, the component should handle it without crashing
    const consoleSpy = jest
      .spyOn(console, "error")
      .mockImplementation(() => {});

    const { container } = render(
      <RouterWrapper>
        <ThreadContainer workspace={null} />
      </RouterWrapper>
    );

    expect(container).toBeInTheDocument();

    consoleSpy.mockRestore();
  });

  it("should handle workspace without threads", () => {
    const workspaceWithoutThreads = {
      id: 1,
      slug: "test-workspace",
      name: "Test Workspace",
      // No threads property
    };

    const { container } = render(
      <RouterWrapper>
        <ThreadContainer workspace={workspaceWithoutThreads} />
      </RouterWrapper>
    );

    expect(container).toBeInTheDocument();
    // Should not crash
  });
});
