import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import Sidebar from "../index";
import useUser from "@/hooks/useUser";
import { useModal } from "@/hooks/useModal";
import { useIsDocumentDrafting } from "@/stores/userStore";

// Mock dependencies
jest.mock("@/hooks/useUser");
jest.mock("@/hooks/useModal");
jest.mock("@/stores/userStore");
jest.mock("react-i18next", () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

// Mock child components
jest.mock("../ActiveWorkspaces", () => ({
  __esModule: true,
  default: ({
    renderEmptyState,
  }: {
    renderEmptyState: (hasWorkspaces: boolean) => React.ReactNode;
  }) => (
    <div data-testid="active-workspaces">
      Active Workspaces
      {renderEmptyState(false)}
    </div>
  ),
}));

jest.mock("@/components/ModuleSelection", () => ({
  __esModule: true,
  default: () => <div data-testid="module-selection">Module Selection</div>,
}));

jest.mock("@/components/CaseReference", () => ({
  __esModule: true,
  default: ({ user }: { user: any }) => (
    <div data-testid="case-reference">Case Reference for {user.username}</div>
  ),
}));

jest.mock("@/components/HeaderWorkspace/RecentUploads", () => ({
  __esModule: true,
  default: () => <div data-testid="recent-uploads">Recent Uploads</div>,
}));

jest.mock("@/components/Footer", () => ({
  __esModule: true,
  default: () => <div data-testid="footer">Footer</div>,
}));

jest.mock("@/components/Modals/NewWorkspace", () => ({
  __esModule: true,
  default: ({ onClose }: { isOpen: boolean; onClose: () => void }) => (
    <div data-testid="new-workspace-modal">
      New Workspace Modal
      <button onClick={onClose}>Close</button>
    </div>
  ),
}));

// Mock react-device-detect
jest.mock("react-device-detect", () => ({
  get isMobile() {
    return (global as any).isMobileDevice || false;
  },
}));

describe("Sidebar Component", () => {
  const mockUser = {
    id: 1,
    username: "testuser",
    email: "<EMAIL>",
    role: "admin",
  };

  const mockModal = {
    isOpen: false,
    openModal: jest.fn(),
    closeModal: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useUser as jest.Mock).mockReturnValue({ user: mockUser });
    (useModal as jest.Mock).mockReturnValue(mockModal);
    (useIsDocumentDrafting as jest.Mock).mockReturnValue(false);
    // Reset window size
    (global as any).innerWidth = 1024;
    // Reset isMobile to false by default
    (global as any).isMobileDevice = false;
  });

  const renderSidebar = () => {
    return render(
      <BrowserRouter>
        <Sidebar />
      </BrowserRouter>
    );
  };

  describe("Rendering", () => {
    it("should render sidebar with all components on desktop", () => {
      renderSidebar();

      expect(screen.getByTestId("module-selection")).toBeInTheDocument();
      expect(screen.getByTestId("active-workspaces")).toBeInTheDocument();
      expect(screen.getByTestId("footer")).toBeInTheDocument();
      expect(screen.getByText("new-workspace.title")).toBeInTheDocument();
    });

    it("should render additional components on mobile", async () => {
      (global as any).isMobileDevice = true;
      // Set large screen to ensure sidebar is visible
      (global as any).innerWidth = 1024;

      const { container } = renderSidebar();

      // First click the toggle button to show the sidebar since on mobile it starts hidden
      const toggleButton = container.querySelector('button[type="button"]');
      if (toggleButton) {
        fireEvent.click(toggleButton);
      }

      await waitFor(() => {
        expect(screen.getByTestId("case-reference")).toBeInTheDocument();
        expect(screen.getByTestId("recent-uploads")).toBeInTheDocument();
      });

      (global as any).isMobileDevice = false;
    });

    it("should show new workspace button for admin users", () => {
      renderSidebar();

      const newWorkspaceButton = screen.getByText("new-workspace.title");
      expect(newWorkspaceButton).toBeInTheDocument();
    });

    it("should hide new workspace button for default users when not document drafting", () => {
      (useUser as jest.Mock).mockReturnValue({
        user: { ...mockUser, role: "default" },
      });

      renderSidebar();

      expect(screen.queryByText("new-workspace.title")).not.toBeInTheDocument();
    });

    it("should show new workspace button for default users when document drafting is enabled", () => {
      (useUser as jest.Mock).mockReturnValue({
        user: { ...mockUser, role: "default" },
      });
      (useIsDocumentDrafting as jest.Mock).mockReturnValue(true);

      renderSidebar();

      expect(screen.getByText("new-workspace.title")).toBeInTheDocument();
    });
  });

  describe("Responsiveness", () => {
    it("should hide sidebar on mobile by default", async () => {
      (global as any).innerWidth = 500;
      renderSidebar();

      // Trigger resize event
      fireEvent(window, new Event("resize"));

      await waitFor(() => {
        expect(
          screen.queryByTestId("module-selection")
        ).not.toBeInTheDocument();
      });
    });

    it("should show sidebar on desktop", () => {
      (global as any).innerWidth = 1200;
      renderSidebar();

      expect(screen.getByTestId("module-selection")).toBeInTheDocument();
    });

    it("should toggle sidebar visibility on mobile", async () => {
      (global as any).innerWidth = 500;
      const { container } = renderSidebar();

      // Find toggle button
      const toggleButton = container.querySelector(
        'button[aria-label*="menu"], button:has(svg)'
      );
      if (toggleButton) {
        fireEvent.click(toggleButton);

        await waitFor(() => {
          expect(screen.getByTestId("module-selection")).toBeInTheDocument();
        });
      }
    });

    it("should handle window resize events", () => {
      renderSidebar();

      // Simulate resize to mobile
      (global as any).innerWidth = 500;
      fireEvent(window, new Event("resize"));

      // Simulate resize back to desktop
      (global as any).innerWidth = 1200;
      fireEvent(window, new Event("resize"));

      expect(screen.getByTestId("module-selection")).toBeInTheDocument();
    });
  });

  describe("User Interactions", () => {
    it("should open new workspace modal when button is clicked", async () => {
      const user = userEvent.setup();
      renderSidebar();

      const newWorkspaceButton = screen.getByText("new-workspace.title");
      await user.click(newWorkspaceButton);

      expect(mockModal.openModal).toHaveBeenCalledTimes(1);
    });

    it("should render empty state for users without workspaces", () => {
      (useUser as jest.Mock).mockReturnValue({
        user: { ...mockUser, role: "default" },
      });

      renderSidebar();

      expect(
        screen.getByText("workspace.no-workspace.title")
      ).toBeInTheDocument();
      expect(
        screen.getByText("workspace.no-workspace.description")
      ).toBeInTheDocument();
      expect(
        screen.getByText("workspace.no-workspace.contact-admin")
      ).toBeInTheDocument();
    });
  });

  describe("Component Integration", () => {
    it("should pass user prop to CaseReference on mobile", async () => {
      (global as any).isMobileDevice = true;
      // Set large screen to ensure sidebar is visible
      (global as any).innerWidth = 1024;

      const { container } = renderSidebar();

      // First click the toggle button to show the sidebar since on mobile it starts hidden
      const toggleButton = container.querySelector('button[type="button"]');
      if (toggleButton) {
        fireEvent.click(toggleButton);
      }

      await waitFor(() => {
        expect(
          screen.getByText(`Case Reference for ${mockUser.username}`)
        ).toBeInTheDocument();
      });
    });

    it("should render ActiveWorkspaces with empty state callback", () => {
      renderSidebar();

      expect(screen.getByTestId("active-workspaces")).toBeInTheDocument();
    });
  });

  describe("Modal Behavior", () => {
    it("should render NewWorkspaceModal when modal is open", () => {
      (useModal as jest.Mock).mockReturnValue({ ...mockModal, isOpen: true });

      renderSidebar();

      expect(screen.getByTestId("new-workspace-modal")).toBeInTheDocument();
    });

    it("should close modal when close button is clicked", async () => {
      const user = userEvent.setup();
      const closeModalMock = jest.fn();
      (useModal as jest.Mock).mockReturnValue({
        ...mockModal,
        isOpen: true,
        closeModal: closeModalMock,
      });

      renderSidebar();

      const closeButton = screen.getByText("Close");
      await user.click(closeButton);

      expect(closeModalMock).toHaveBeenCalledTimes(1);
    });
  });

  describe("Accessibility", () => {
    it("should have proper ARIA attributes", () => {
      renderSidebar();

      const sidebar = screen
        .getByTestId("module-selection")
        .closest('div[class*="overflow-y-auto"]');
      expect(sidebar).toBeInTheDocument();
      expect(sidebar).toHaveClass("overflow-y-auto");
    });

    it("should maintain focus within sidebar when toggling on mobile", async () => {
      (global as any).innerWidth = 500;
      const { container } = renderSidebar();

      const toggleButton = container.querySelector("button");
      if (toggleButton) {
        toggleButton.focus();
        expect(document.activeElement).toBe(toggleButton);
      }
    });
  });

  describe("Edge Cases", () => {
    it("should handle missing user gracefully", () => {
      (useUser as jest.Mock).mockReturnValue({ user: null });

      renderSidebar();

      expect(screen.getByTestId("module-selection")).toBeInTheDocument();
      // When user is null and isDocumentDrafting is false, button should be shown (based on component logic)
      expect(screen.getByText("new-workspace.title")).toBeInTheDocument();
    });

    it("should clean up event listeners on unmount", () => {
      const removeEventListenerSpy = jest.spyOn(window, "removeEventListener");

      const { unmount } = renderSidebar();
      unmount();

      expect(removeEventListenerSpy).toHaveBeenCalledWith(
        "resize",
        expect.any(Function)
      );
    });
  });
});
