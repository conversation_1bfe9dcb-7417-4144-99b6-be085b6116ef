import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import StyleAlignmentModal from "../index";
import System from "../../../../models/system";
import showToast from "../../../../utils/toast";
import type { UploadedFile } from "../MultiFileUploadButton";

// Mock the tokenizer utility to fix TextEncoder issue in Jest
jest.mock("../../../../utils/tokenizer", () => ({
  TokenManager: jest.fn().mockImplementation(() => ({
    countTokens: jest.fn(() => 100),
    getContextWindow: jest.fn(() => 4000),
    getAvailableTokens: jest.fn(() => 3900),
  })),
}));

// Mock the useUser hook
jest.mock("@/hooks/useUser", () => ({
  __esModule: true,
  default: jest.fn(() => ({
    user: {
      id: "1",
      username: "testuser",
      role: "admin",
    },
  })),
}));

// Mock the stores
const mockStyleProfiles = [
  {
    id: "1",
    name: "Legal Brief Style",
    instructions: "Use formal legal language with clear structure.",
    createdAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "2",
    name: "Contract Style",
    instructions: "Use precise contractual language.",
    createdAt: "2024-01-02T00:00:00Z",
  },
];

const mockUseStyleProfiles = jest.fn(() => mockStyleProfiles);
const mockUseAddStyleProfile = jest.fn();
const mockUseUpdateStyleProfile = jest.fn();
const mockUseDeleteStyleProfile = jest.fn();
const mockUseActiveStyleProfileId = jest.fn(() => "1");
const mockUseSetActiveStyleProfile = jest.fn();
const mockUseStyleAlignmentEnabled = jest.fn(() => false);
const mockUseSetStyleAlignmentEnabled = jest.fn();

jest.mock("../../../../stores/userStore", () => ({
  useStyleProfiles: () => mockUseStyleProfiles(),
  useAddStyleProfile: () => mockUseAddStyleProfile,
  useUpdateStyleProfile: () => mockUseUpdateStyleProfile,
  useDeleteStyleProfile: () => mockUseDeleteStyleProfile,
  useActiveStyleProfileId: () => mockUseActiveStyleProfileId(),
  useSetActiveStyleProfile: () => mockUseSetActiveStyleProfile,
  useStyleAlignmentEnabled: () => mockUseStyleAlignmentEnabled(),
  useSetStyleAlignmentEnabled: () => mockUseSetStyleAlignmentEnabled,
}));

// Mock System model
jest.mock("../../../../models/system", () => ({
  generateStyleProfile: jest.fn(),
}));

// Mock toast
jest.mock("../../../../utils/toast", () => jest.fn());

// Mock react-i18next
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        "user-menu.style-alignment": "Personal Style Alignment",
        "user-menu.style-enabled": "Enable Style Alignment",
        "user-menu.style-upload": "Upload Document Sample",
        "user-menu.style-generate": "Style Setting Generator",
        "user-menu.style-profile": "Style Profile",
        "user-menu.style-generated-success":
          "Style profile generated successfully",
        "user-menu.style-generation-failed": "Failed to generate style profile",
      };
      return translations[key] || key;
    },
  }),
}));

// Mock Button component
jest.mock("../../../Button", () => ({
  Button: function MockButton({
    children,
    onClick,
    disabled,
    className,
    isLoading,
    type,
  }: {
    children?: React.ReactNode;
    onClick?: React.MouseEventHandler<HTMLButtonElement>;
    disabled?: boolean;
    className?: string;
    isLoading?: boolean;
    type?: "button" | "submit" | "reset";
  }) {
    return (
      <button
        type={type || "button"}
        onClick={onClick}
        disabled={disabled}
        className={className}
        data-testid={children?.toString().toLowerCase().replace(/\s+/g, "-")}
      >
        {isLoading ? "Loading..." : children}
      </button>
    );
  },
}));

// Mock Toggle component
jest.mock("../../../ui/Toggle", () => {
  return function MockToggle({
    checked,
    onChange,
    disabled,
    id,
    name,
  }: {
    checked?: boolean;
    onChange?: React.ChangeEventHandler<HTMLInputElement>;
    disabled?: boolean;
    id?: string;
    name?: string;
  }) {
    return (
      <input
        type="checkbox"
        id={id}
        name={name}
        checked={checked}
        onChange={onChange}
        disabled={disabled}
        data-testid="style-alignment-toggle"
        aria-label="Style Alignment Toggle"
        title="Style Alignment Toggle"
      />
    );
  };
});

// Mock Phosphor icons
jest.mock("@phosphor-icons/react", () => ({
  X: () => <span data-testid="close-icon">X</span>,
  Trash: () => <span data-testid="trash-icon">Trash</span>,
  Eye: () => <span data-testid="eye-icon">Eye</span>,
  PencilSimple: () => <span data-testid="pencil-icon">Edit</span>,
}));

// Mock MultiFileUploadButton
jest.mock("../MultiFileUploadButton", () => {
  return function MockMultiFileUploadButton({
    onFilesChange,
  }: {
    onFilesChange?: (files: UploadedFile[]) => void;
  }) {
    return (
      <button
        data-testid="multi-file-upload-button"
        onClick={() =>
          onFilesChange &&
          onFilesChange([
            {
              id: 1,
              name: "test.docx",
              content: "test content",
              size: 123,
              uploadedAt: new Date(),
            } as UploadedFile,
          ])
        }
      >
        Upload Files
      </button>
    );
  };
});

describe("StyleAlignmentModal", () => {
  const mockHideModal = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (
      System.generateStyleProfile as jest.MockedFunction<
        typeof System.generateStyleProfile
      >
    ).mockResolvedValue({
      success: true,
      styleInstructions: "Generated style instructions",
    });
  });

  const renderComponent = () => {
    return render(<StyleAlignmentModal hideModal={mockHideModal} />);
  };

  describe("Basic Rendering", () => {
    test("renders modal with title", () => {
      renderComponent();
      expect(screen.getByText("Personal Style Alignment")).toBeInTheDocument();
    });

    test("renders toggle", () => {
      renderComponent();
      const toggle = screen.getByTestId("style-alignment-toggle");
      expect(toggle).toBeInTheDocument();
    });

    test("renders style profiles", () => {
      renderComponent();
      expect(screen.getByText("Legal Brief Style")).toBeInTheDocument();
      expect(screen.getByText("Contract Style")).toBeInTheDocument();
    });

    test("renders upload button", () => {
      renderComponent();
      expect(
        screen.getByTestId("multi-file-upload-button")
      ).toBeInTheDocument();
    });

    test("renders generate button", () => {
      renderComponent();
      expect(screen.getByTestId("style-setting-generator")).toBeInTheDocument();
    });
  });

  describe("Document Upload", () => {
    test("enables generate button after document upload", async () => {
      renderComponent();

      const generateButton = screen.getByTestId("style-setting-generator");
      expect(generateButton).toBeDisabled();

      const uploadButton = screen.getByTestId("multi-file-upload-button");
      fireEvent.click(uploadButton);

      await waitFor(() => {
        expect(generateButton).not.toBeDisabled();
      });
    });
  });

  describe("Style Generation", () => {
    test("generates style profile successfully", async () => {
      renderComponent();

      // Upload document first
      const uploadButton = screen.getByTestId("multi-file-upload-button");
      fireEvent.click(uploadButton);

      await waitFor(() => {
        const generateButton = screen.getByTestId("style-setting-generator");
        expect(generateButton).not.toBeDisabled();
        fireEvent.click(generateButton);
      });

      await waitFor(() => {
        expect(System.generateStyleProfile).toHaveBeenCalledWith(
          "test content"
        );
        expect(showToast).toHaveBeenCalledWith(
          "Style profile generated successfully",
          "success"
        );
      });
    });
  });

  describe("Toggle Functionality", () => {
    test("enables style alignment when toggle is checked", () => {
      renderComponent();

      const toggle = screen.getByTestId("style-alignment-toggle");
      fireEvent.click(toggle);

      expect(mockUseSetStyleAlignmentEnabled).toHaveBeenCalled();
    });
  });

  describe("Modal Actions", () => {
    test("closes modal when close button is clicked", () => {
      renderComponent();

      const closeIcon = screen.getByTestId("close-icon");
      if (closeIcon.parentElement) {
        fireEvent.click(closeIcon.parentElement);
      }

      expect(mockHideModal).toHaveBeenCalled();
    });
  });
});
