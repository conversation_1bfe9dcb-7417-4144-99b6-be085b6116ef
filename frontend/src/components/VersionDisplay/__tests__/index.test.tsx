import { render, screen, waitFor, act } from "@testing-library/react";
import "@testing-library/jest-dom";
import VersionDisplay from "../index";
import Version from "@/models/version";

// Mock the Version model
jest.mock("@/models/version", () => ({
  __esModule: true,
  default: {
    getVersion: jest.fn(),
  },
}));

// Mock react-i18next
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string, fallback?: string, options?: Record<string, any>) => {
      if (key === "version.tooltip-title") {
        return `Version ${options?.version || "{{version}}"}`;
      }
      return fallback || key;
    },
  }),
}));

// Mock SimpleTooltip component
jest.mock("@/components/ui/Tooltip", () => {
  return function MockSimpleTooltip({
    content,
    children,
    placement,
  }: {
    content: React.ReactNode;
    children: React.ReactNode;
    placement?: string;
  }) {
    return (
      <div data-testid="tooltip-wrapper" data-placement={placement}>
        {children}
        {/* Test-only style, not for production */}
        <div data-testid="tooltip-content" className="hidden">
          {content}
        </div>
      </div>
    );
  };
});

describe("VersionDisplay Component", () => {
  const mockGetVersion = Version.getVersion as jest.MockedFunction<
    typeof Version.getVersion
  >;

  beforeEach(() => {
    jest.clearAllMocks();
    // Ensure mock is reset to a clean state
    mockGetVersion.mockClear();
  });

  it("should render version display when data is loaded successfully", async () => {
    const mockVersionData = {
      version: "1.1.0",
      description:
        "Added new document builder features and improved chat performance",
    };

    mockGetVersion.mockResolvedValue(mockVersionData);

    await act(async () => {
      render(<VersionDisplay />);
    });

    // Wait for the mock to be called
    await waitFor(() => {
      expect(mockGetVersion).toHaveBeenCalled();
    });

    // Wait for the version to be displayed
    await waitFor(
      () => {
        expect(screen.getByText("v1.1.0")).toBeInTheDocument();
      },
      { timeout: 10000 }
    );

    expect(screen.getByTestId("tooltip-wrapper")).toBeInTheDocument();
  });

  it("should not render anything when loading", () => {
    mockGetVersion.mockImplementation(() => new Promise(() => {})); // Never resolves

    render(<VersionDisplay />);

    expect(screen.queryByText(/v\d+\.\d+\.\d+/)).not.toBeInTheDocument();
  });

  it("should not render anything when version data is null", async () => {
    mockGetVersion.mockResolvedValue(null);

    render(<VersionDisplay />);

    await waitFor(() => {
      expect(mockGetVersion).toHaveBeenCalled();
    });

    expect(screen.queryByText(/v\d+\.\d+\.\d+/)).not.toBeInTheDocument();
  });

  it("should not render anything when API call fails", async () => {
    mockGetVersion.mockRejectedValue(new Error("API Error"));

    render(<VersionDisplay />);

    await waitFor(() => {
      expect(mockGetVersion).toHaveBeenCalled();
    });

    expect(screen.queryByText(/v\d+\.\d+\.\d+/)).not.toBeInTheDocument();
    // Note: console.error is commented out in implementation
  });

  it("should have correct CSS classes for positioning and styling", async () => {
    const mockVersionData = {
      version: "1.1.0",
      description: "Test description",
    };

    mockGetVersion.mockResolvedValue(mockVersionData);

    await act(async () => {
      render(<VersionDisplay />);
    });

    await waitFor(
      () => {
        expect(screen.getByText("v1.1.0")).toBeInTheDocument();
      },
      { timeout: 10000 }
    );

    // Find the version text element
    const versionElement = screen.getByText("v1.1.0");

    // Check the outer container (grandparent) has fixed positioning
    const outerContainer = versionElement.parentElement?.parentElement;
    expect(outerContainer).toHaveClass("fixed", "bottom-4", "right-4", "z-40");

    // Check the version element has correct styling
    expect(versionElement).toHaveClass(
      "bg-background/80",
      "backdrop-blur-sm",
      "border",
      "border-border",
      "rounded-md",
      "px-2",
      "py-1",
      "text-sm",
      "text-muted-foreground",
      "hover:text-foreground",
      "hover:bg-background/90",
      "transition-all",
      "duration-200",
      "cursor-default",
      "select-none"
    );
  });

  it("should create tooltip content with correct structure", async () => {
    const mockVersionData = {
      version: "1.1.0",
      description: "Test description for tooltip",
    };

    mockGetVersion.mockResolvedValue(mockVersionData);

    await act(async () => {
      render(<VersionDisplay />);
    });

    await waitFor(
      () => {
        expect(screen.getByText("v1.1.0")).toBeInTheDocument();
      },
      { timeout: 10000 }
    );

    // Check that tooltip content is created (even if hidden)
    const tooltipContent = screen.getByTestId("tooltip-content");
    expect(tooltipContent).toBeInTheDocument();

    // Check tooltip placement
    const tooltipWrapper = screen.getByTestId("tooltip-wrapper");
    expect(tooltipWrapper).toHaveAttribute("data-placement", "top");
  });

  it("should call Version.getVersion on mount", async () => {
    const mockVersionData = {
      version: "1.0.0",
      description: "Test",
    };

    mockGetVersion.mockResolvedValue(mockVersionData);

    await act(async () => {
      render(<VersionDisplay />);
    });

    expect(mockGetVersion).toHaveBeenCalledTimes(1);

    await waitFor(
      () => {
        expect(screen.getByText("v1.0.0")).toBeInTheDocument();
      },
      { timeout: 10000 }
    );
  });

  it("should handle version data without description", async () => {
    const mockVersionData = {
      version: "2.0.0",
      description: "",
    };

    mockGetVersion.mockResolvedValue(mockVersionData);

    await act(async () => {
      render(<VersionDisplay />);
    });

    await waitFor(
      () => {
        expect(screen.getByText("v2.0.0")).toBeInTheDocument();
      },
      { timeout: 10000 }
    );

    // Component should still render even without description
    expect(screen.getByTestId("tooltip-wrapper")).toBeInTheDocument();
  });

  it("should format version number with v prefix", async () => {
    const mockVersionData = {
      version: "3.2.1",
      description: "Test version",
    };

    mockGetVersion.mockResolvedValue(mockVersionData);

    await act(async () => {
      render(<VersionDisplay />);
    });

    await waitFor(
      () => {
        expect(screen.getByText("v3.2.1")).toBeInTheDocument();
      },
      { timeout: 10000 }
    );

    // Should not show version without 'v' prefix
    expect(screen.queryByText("3.2.1")).not.toBeInTheDocument();
  });

  it("should use translation for tooltip title", async () => {
    const mockVersionData = {
      version: "1.1.0",
      description: "Test description",
    };

    mockGetVersion.mockResolvedValue(mockVersionData);

    await act(async () => {
      render(<VersionDisplay />);
    });

    await waitFor(
      () => {
        expect(screen.getByText("v1.1.0")).toBeInTheDocument();
      },
      { timeout: 10000 }
    );

    // The translation should be called with the correct key and version
    // This is tested indirectly through the mock implementation
    expect(screen.getByText("v1.1.0")).toBeInTheDocument();
  });
});
