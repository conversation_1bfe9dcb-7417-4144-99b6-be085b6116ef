import truncate from "truncate";
import { decode as H<PERSON><PERSON><PERSON><PERSON> } from "he";
import { memo, useState, useEffect, useRef, useMemo } from "react";
import Modal from "@/components/ui/Modal";
import React from "react";
import {
  FileText,
  Info,
  Gith<PERSON><PERSON>ogo,
  Link,
  Youtube<PERSON>ogo,
  Quotes,
} from "@phosphor-icons/react";
import { Tooltip } from "react-tooltip";
import { useTranslation } from "react-i18next";
import { toPercentString } from "@/utils/numbers";
import ConfluenceLogo from "@/media/dataConnectors/confluence.png";
import DocumentHighlight from "@/components/DocumentHighlight";
import Document from "@/models/document";
import { isCitationEnabled } from "@/utils/constants";
import { Button } from "@/components/Button";
import { TabButton } from "@/components/ui/TabButton";
import { useTextOverflow } from "@/hooks/useTextOverflow";
import { <PERSON><PERSON>he<PERSON>ronUp, <PERSON><PERSON>he<PERSON>ronDown } from "react-icons/lu";
import Markdown from "@/components/ui/Markdown";
import "./Citation.css";

interface CitationChunk {
  id: string;
  text: string;
  chunkSource: string;
  score: number | null;
}

interface CitationSource {
  id: string;
  title: string;
  text: string;
  chunkSource?: string;
  score?: number | null;
  url?: string;
}

interface CombinedSource {
  title: string;
  chunks: CitationChunk[];
  references: number;
  url?: string;
}

interface CitationsProps {
  sources?: CitationSource[];
  inline?: boolean;
  chatId?: string | null;
}

interface CitationProps {
  source: CombinedSource;
  onClick: () => void;
  isActive: boolean;
}

interface CitationDetailModalProps {
  sources: CombinedSource[];
  activeSource: CombinedSource;
  onSourceSelect: (source: CombinedSource) => void;
  onClose: () => void;
}

interface DocumentContent {
  content?: string;
}

interface ProcessedChunk {
  text: string;
  score: number;
}

interface ChunkSourceInfo {
  isUrl: boolean;
  text: string | null;
  href: string | null;
  icon: string;
}

type TabType = "Citation Chunks" | "Parent Document" | "Document Highlighting";

/**
 * Cleans problematic path prefixes to avoid routing issues
 * @param path - The file path to clean
 * @returns Cleaned path without problematic prefixes
 */
const cleanPathPrefix = (path: string): string => {
  if (!path) return path;

  // First check if this is an API URL and extract the path parameter
  const apiMatch = path.match(
    /\/document\/(pdf-contents|contents)\?path=([^&]+)/
  );
  if (apiMatch) {
    const decodedPath = decodeURIComponent(apiMatch[2]);
    // Return the cleaned path so we don't double-encode it
    return decodedPath;
  }

  // Handle /hotdir/ prefix for frontend-stored PDFs
  if (path.startsWith("/hotdir/")) {
    const newPath = path.substring("/hotdir/".length);
    return newPath;
  }

  // Handle /document/ prefix - make sure we don't strip pdf-contents endpoint
  if (
    path.startsWith("/document/") &&
    !path.includes("pdf-contents") &&
    !path.includes("pdf/")
  ) {
    const newPath = path.substring("/document/".length);
    return newPath;
  }

  return path;
};

/**
 * Combines citation sources with the same title to reduce redundancy
 * and group related chunks together for better organization.
 *
 * @param sources - Array of citation sources to combine
 * @returns Array of combined sources grouped by title with chunk deduplication
 *
 * @example
 * ```typescript
 * const sources = [
 *   { id: "1", title: "Document A", text: "First chunk", score: 0.9 },
 *   { id: "2", title: "Document A", text: "Second chunk", score: 0.8 },
 *   { id: "3", title: "Document B", text: "Third chunk", score: 0.7 }
 * ];
 * const combined = combineLikeSources(sources);
 * // Result: Two CombinedSource objects, one for "Document A" with 2 chunks,
 * // one for "Document B" with 1 chunk
 * ```
 */
function combineLikeSources(sources: CitationSource[]): CombinedSource[] {
  const combined: { [key: string]: CombinedSource } = {};
  sources.forEach((source) => {
    const { id, title, text, chunkSource = "", score = null } = source;

    // Skip empty text chunks
    if (!text) return;

    if (Object.hasOwn(combined, title)) {
      // Check if this chunk text already exists in the array to avoid duplicates
      const chunkExists = combined[title].chunks.some(
        (chunk) => chunk.text === text
      );
      if (!chunkExists) {
        combined[title].chunks.push({ id, text, chunkSource, score });
      }
      combined[title].references += 1;
    } else {
      combined[title] = {
        title,
        chunks: [{ id, text, chunkSource, score }],
        references: 1,
        url: source.url,
      };
    }
  });
  return Object.values(combined);
}

export default function Citations({
  sources = [],
  inline: _inline = false,
  chatId = null,
}: CitationsProps): JSX.Element | null {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [selectedSource, setSelectedSource] = useState<CombinedSource | null>(
    null
  );
  const { t } = useTranslation();

  // Don't render citations if no sources or if no chatId (which means streaming is still ongoing)
  if (!sources || sources.length === 0 || !chatId) return null;

  const combinedSources = combineLikeSources(sources);

  const handleOpenModal = (): void => {
    setSelectedSource(combinedSources[0]);
    setIsModalOpen(true);
  };

  const handleCloseModal = (): void => {
    setIsModalOpen(false);
  };

  const handleSourceSelect = (source: CombinedSource): void => {
    setSelectedSource(source);
  };

  return (
    <>
      <Button variant="outline" size="sm" onClick={handleOpenModal}>
        <Quotes size={16} />
        {t("citations.view")} ({combinedSources.length})
      </Button>
      {isModalOpen && selectedSource && (
        <CitationDetailModal
          sources={combinedSources}
          activeSource={selectedSource}
          onSourceSelect={handleSourceSelect}
          onClose={handleCloseModal}
        />
      )}
    </>
  );
}

const Citation = memo<CitationProps>(({ source, onClick, isActive }) => {
  const { title } = source;
  const [textRef, isOverflowing] = useTextOverflow();

  if (!title) return null;
  const chunkSourceInfo = parseChunkSource(source);
  const displayTitle = chunkSourceInfo?.text ?? title;
  const fullTitle = chunkSourceInfo?.text ?? title;
  const CitationIcon = Object.hasOwn(ICONS, chunkSourceInfo?.icon)
    ? ICONS[chunkSourceInfo.icon]
    : ICONS.file;

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={onClick}
      className={`w-full justify-start text-left mb-1 p-3 hover:bg-secondary rounded-md ${isActive ? "bg-secondary" : ""}`}
      title={fullTitle}
    >
      <CitationIcon />
      <div
        className={`citation-marquee-container flex-1 ${isOverflowing ? "overflow-needed" : ""}`}
      >
        <span
          ref={textRef}
          className="citation-marquee text-foreground text-sm font-medium  align-middle"
        >
          {displayTitle}
        </span>
      </div>
    </Button>
  );
});

Citation.displayName = "Citation";

/**
 * Removes document metadata headers from citation text chunks
 * @param text - The raw text chunk that may contain metadata headers
 * @returns Clean text content without metadata headers
 */
function omitChunkHeader(text: string): string {
  if (!text.startsWith("<document_metadata>")) return text;
  return text.split("</document_metadata>")[1]?.trim() || "";
}

/**
 * Formats document text by emphasizing lines that indicate full document loading
 * and properly handling line breaks for display
 *
 * @param text - The document text to format
 * @returns Array of formatted text elements with proper line breaks and emphasis
 */
function formatDocumentText(text: string): (string | JSX.Element)[] {
  // Find the line about document being fully loaded
  const lines = text.split("\n");
  const formattedLines = lines.map((line) => {
    // Italic the text if it matches any of the fully loaded document messages
    const fullyLoadedMessages = [
      "This document was fully loaded in the prompt",
      "Ten dokument został w pełni załadowany do zapytania",
      "Det här dokumentet lästes in i dess helhet",
      "Dette dokumentet ble fullt lastet inn i prompten",
      "Dieses Dokument wurde vollständig in den Prompt geladen",
      "Ce document a été entièrement chargé dans la requête",
      "Iyi nyandiko yuzuye yashyizwe mu ibaza",
    ];

    if (fullyLoadedMessages.some((msg) => line.includes(msg))) {
      return <em key={line}>{line}</em>;
    }
    return line;
  });

  // Join the lines back with newlines between them
  return formattedLines
    .map((line, _i) => (typeof line === "string" ? line : line))
    .reduce<(string | JSX.Element)[]>((acc, line, i) => {
      return acc.length ? [...acc, <br key={`br-${i}`} />, line] : [line];
    }, []);
}

function CitationDetailModal({
  sources,
  activeSource,
  onSourceSelect,
  onClose,
}: CitationDetailModalProps): JSX.Element | null {
  const [isDocumentLoading, setIsDocumentLoading] = useState<boolean>(false);
  const [isHighlightLoading, setIsHighlightLoading] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<TabType>("Citation Chunks");
  const [contents, setContents] = useState<string>("");
  const { t } = useTranslation();
  const modalRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const [currentHighlightIndex, setCurrentHighlightIndex] = useState<number>(0);
  const previousSourceRef = useRef<CombinedSource | null>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent): void => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    document.addEventListener("mouseup", handleClickOutside);

    return () => {
      document.removeEventListener("mouseup", handleClickOutside);
    };
  }, [onClose]);

  // Reset highlight index when changing sources
  useEffect(() => {
    setCurrentHighlightIndex(0);

    // Only set loading state when the source actually changes
    if (previousSourceRef.current?.url !== activeSource?.url) {
      if (activeTab === "Parent Document") {
        setIsDocumentLoading(true);
      } else if (activeTab === "Document Highlighting") {
        setIsHighlightLoading(true);
        // Reset after a short delay to allow the component to load
        const timer = setTimeout(() => setIsHighlightLoading(false), 800);
        return () => clearTimeout(timer);
      }
    }

    previousSourceRef.current = activeSource;
  }, [activeSource?.url, activeTab]); // Removed activeSource.chunks and activeSource to prevent loops

  // Add a separate effect to handle tab changes
  useEffect(() => {
    if (activeTab === "Document Highlighting") {
      setIsHighlightLoading(true);
      // Reset after a short delay to allow the component to load
      const timer = setTimeout(() => setIsHighlightLoading(false), 800);
      return () => clearTimeout(timer);
    }
  }, [activeTab]);

  const processChunks = useMemo(() => {
    if (!activeSource?.chunks) return [];

    return activeSource.chunks.reduce<ProcessedChunk[]>(
      (acc, { id: _id, text, score, chunkSource: _chunkSource }, _idx) => {
        const originalText = text?.trim() || "";
        const startsWithMeta = originalText.startsWith("<document_metadata>");

        if (!startsWithMeta) {
          return acc;
        }

        const cleanText = omitChunkHeader(originalText);

        if (!cleanText) {
          return acc;
        }

        const scorePercentage = Math.round(((score || 0) * 100) / 10) * 10;
        return [...acc, { text: cleanText, score: scorePercentage }];
      },
      []
    );
  }, [activeSource?.chunks]);

  useEffect(() => {
    async function loadDocument(): Promise<void> {
      if (!activeSource?.url || activeTab !== "Parent Document") return;

      try {
        // Get the document path from chunk source if available, otherwise use the URL
        let documentPath =
          activeSource.chunks?.[0]?.chunkSource?.split("://")?.[1] ||
          activeSource.url;

        // Return early if no valid path is found
        if (!documentPath) {
          setContents("");
          return;
        }

        // Clean up any problematic path prefixes
        const cleanedPath = cleanPathPrefix(documentPath);
        if (cleanedPath !== documentPath) {
          documentPath = cleanedPath;
        }

        const document: DocumentContent = await Document.contents(documentPath);
        if (document) {
          let documentContents = document.content || "";
          let _highlightCount = 0;

          processChunks.forEach(({ text, score }, _idx) => {
            if (!text || text.trim() === "") {
              return;
            }
            const escapedText = escapeRegExp(text);
            // Simplified regex:
            const regex = new RegExp(escapedText, "gim");
            documentContents = documentContents.replace(regex, (match) => {
              _highlightCount++;
              // Simplified replacement logic:
              return `<span class="citation-highlight score-${score}">${match}</span>`;
            });
          });
          setContents(documentContents);
        }
      } catch (error) {
        console.error(
          `Failed to load document content for path: ${documentPath}`,
          error
        );
        setContents(""); // Clear content on error
      } finally {
        setIsDocumentLoading(false);
      }
    }

    // Only load document when tab is active
    if (activeTab === "Parent Document") {
      setIsDocumentLoading(true);
      loadDocument();
    }
  }, [activeSource.url, processChunks, activeTab, activeSource.chunks]);

  const scrollToHighlight = (index: number): void => {
    const highlights =
      contentRef.current?.getElementsByClassName("citation-highlight");
    if (!highlights || !highlights.length) return;

    const highlight = highlights[index];
    if (highlight) {
      highlight.scrollIntoView({ behavior: "smooth", block: "center" });
      highlights[currentHighlightIndex]?.classList.remove("current-highlight");
      highlight.classList.add("current-highlight");
    }
  };

  const handlePrevHighlight = (): void => {
    const highlights =
      contentRef.current?.getElementsByClassName("citation-highlight");
    if (!highlights?.length) return;

    const newIndex =
      currentHighlightIndex > 0
        ? currentHighlightIndex - 1
        : highlights.length - 1;
    setCurrentHighlightIndex(newIndex);
    scrollToHighlight(newIndex);
  };

  const handleNextHighlight = (): void => {
    const highlights =
      contentRef.current?.getElementsByClassName("citation-highlight");
    if (!highlights?.length) return;

    const newIndex =
      currentHighlightIndex < highlights.length - 1
        ? currentHighlightIndex + 1
        : 0;
    setCurrentHighlightIndex(newIndex);
    scrollToHighlight(newIndex);
  };

  const handleTabClick = (tab: TabType): void => {
    // Don't do anything if we're already on this tab
    if (tab === activeTab) return;

    setActiveTab(tab);

    // Set loading state when switching tabs
    if (tab === "Parent Document" && contents === "") {
      setIsDocumentLoading(true);
    } else if (tab === "Document Highlighting") {
      setIsHighlightLoading(true);
      // Reset after a short delay to allow the component to load
      setTimeout(() => setIsHighlightLoading(false), 800);
    }
  };

  if (!activeSource || !activeSource.chunks) return null;
  const { references, title, chunks, url } = activeSource;
  const {
    isUrl: _isUrl,
    text: _webpageUrl,
    href: _linkTo,
  } = parseChunkSource(activeSource);

  // Get the document path using the same logic as JSON fetching
  let documentPath =
    activeSource.chunks?.[0]?.chunkSource?.split("://")?.[1] ||
    activeSource.url;

  // Clean up any problematic path prefixes
  if (documentPath) {
    const cleanedPath = cleanPathPrefix(documentPath);
    if (cleanedPath !== documentPath) {
      documentPath = cleanedPath;
    }
  }

  const modalTitle = (
    <div className="w-full flex items-center justify-center gap-2 text-lg font-semibold">
      {/* Display appropriate icon based on source type */}
      {(() => {
        const info = parseChunkSource(activeSource);
        const Icon = Object.hasOwn(ICONS, info?.icon)
          ? ICONS[info.icon]
          : ICONS.file;
        return <Icon className="w-5 h-5" weight="bold" />;
      })()}
      {truncate(title, 60)}
    </div>
  );

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      className="document-modal max-w-7xl h-[90vh]"
      title={modalTitle}
      footer={
        references > 1 ? (
          <div className="text-xs text-foreground">
            {t("citations.referenced")} {references} {t("citations.times")}
          </div>
        ) : null
      }
    >
      <div className="flex flex-col h-full">
        {/* Tab navigation - centered */}
        <div className="flex justify-center border-b flex-shrink-0">
          <TabButton
            active={activeTab === "Citation Chunks"}
            onClick={() => handleTabClick("Citation Chunks")}
            className="px-6 py-3 text-md"
          >
            <Quotes />
            {t("citations.chunk")}
          </TabButton>

          {isCitationEnabled() && (
            <>
              <TabButton
                active={activeTab === "Parent Document"}
                onClick={() => handleTabClick("Parent Document")}
                className="px-6 py-3 text-md"
              >
                <FileText />
                {t("citations.pdr-h")}
              </TabButton>

              <TabButton
                active={activeTab === "Document Highlighting"}
                onClick={() => handleTabClick("Document Highlighting")}
                disabled={!url && !documentPath}
                title={
                  !url && !documentPath
                    ? "No document available for highlighting"
                    : ""
                }
                className="px-6 py-3 text-md"
              >
                <Info />
                {t("citations.pdr")}
              </TabButton>
            </>
          )}
        </div>

        {/* Main content area with fixed height */}
        <div className="flex flex-1 min-h-0 overflow-hidden">
          {/* Left sidebar for source list */}
          <div className="w-80 border-r p-4 overflow-y-auto flex-shrink-0">
            <h3 className="text-md font-semibold mb-5 text-foreground">
              {t("citations.sources")}
            </h3>
            <div className="space-y-2">
              {sources.map((source, index) => (
                <Citation
                  key={index}
                  source={source}
                  onClick={() => onSourceSelect(source)}
                  isActive={source.title === activeSource.title}
                />
              ))}
            </div>
          </div>

          {/* Content container with tabs */}
          <div className="flex-1 relative flex flex-col min-h-0">
            {/* Citation Chunks Tab */}
            <div
              className={`absolute inset-0 p-4 overflow-y-auto citation-tab-content ${activeTab === "Citation Chunks" ? "block" : "hidden"}`}
            >
              {chunks.map(({ text, score }, idx) => (
                <div key={idx} className="mb-6">
                  <div className="flex flex-col w-full justify-start gap-y-1">
                    <h3 className="py-2 font-semibold text-foreground text-md">
                      {t("citations.citation")} {idx + 1}
                    </h3>
                    <p className="text-foreground whitespace-break-spaces">
                      {formatDocumentText(HTMLDecode(omitChunkHeader(text)))}
                    </p>

                    {!!score && (
                      <>
                        <div
                          data-tooltip-id="similarity-score"
                          data-tooltip-content={`This is the semantic similarity score of this chunk of text compared to your query calculated by the vector database.`}
                          className="flex items-center gap-x-1 mt-2 text-foreground"
                        >
                          <Info className="opacity-50" size={14} />
                          <p className="text-[12px] opacity-50">
                            {toPercentString(score)} {t("citations.match")}
                          </p>
                        </div>
                        <Tooltip
                          id="similarity-score"
                          place="top"
                          delayShow={100}
                        />
                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Parent Document Tab */}
            <div
              className={`absolute inset-0 overflow-y-auto citation-tab-content ${activeTab === "Parent Document" ? "block" : "hidden"}`}
            >
              <div className="h-full relative">
                {isDocumentLoading ? (
                  <div className="flex items-center justify-center p-4 h-full">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                  </div>
                ) : (
                  <>
                    {contents ? (
                      <>
                        <div className="fixed right-12 top-1/3 flex flex-col gap-2 z-10">
                          <Button
                            onClick={handlePrevHighlight}
                            className="p-0 rounded-full shadow-md"
                            size="icon"
                            variant="secondary"
                            title={t("citations.previous-highlight", {
                              defaultValue: "Previous highlight",
                            })}
                          >
                            <LuChevronUp />
                          </Button>
                          <Button
                            onClick={handleNextHighlight}
                            className="p-0 rounded-full shadow-md"
                            size="icon"
                            variant="secondary"
                            title={t("citations.next-highlight", {
                              defaultValue: "Next highlight",
                            })}
                          >
                            <LuChevronDown />
                          </Button>
                        </div>
                        <div
                          ref={contentRef}
                          className="text-foreground p-4 whitespace-pre-line"
                        >
                          <Markdown
                            content={contents}
                            className="whitespace-pre-line"
                          />
                        </div>
                      </>
                    ) : (
                      <div className="flex items-center justify-center p-4 h-full">
                        <p className="text-gray-500">
                          {t("citations.no-content")}
                        </p>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Document Highlighting Tab */}
            <div
              className={`absolute inset-0 citation-tab-content ${activeTab === "Document Highlighting" ? "block" : "hidden"}`}
            >
              {isHighlightLoading ? (
                <div className="flex items-center justify-center p-4 h-full">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                </div>
              ) : (
                <div className="h-full">
                  {url || documentPath ? (
                    <DocumentHighlight
                      url={url}
                      pathUrl={
                        documentPath
                          ? encodeURIComponent(documentPath)
                          : undefined
                      }
                      chunks={chunks}
                      loadContent={activeTab === "Document Highlighting"}
                      handleClose={() => {
                        setIsHighlightLoading(false);
                        handleTabClick("Citation Chunks");
                      }}
                    />
                  ) : (
                    <div className="flex items-center justify-center p-4 h-full">
                      <p className="text-gray-500">
                        {t("citations.no-document-available")}
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
}

/**
 * Parses chunk source information to determine display text, links, and appropriate icons
 * for different types of citation sources (websites, GitHub, Confluence, YouTube, etc.)
 *
 * @param source - Combined source containing title and chunks
 * @param source.title - The title of the source document
 * @param source.chunks - Array of chunks from the source
 * @returns ChunkSourceInfo with parsed URL, display text, and appropriate icon
 */
function parseChunkSource({
  title = "",
  chunks = [],
}: CombinedSource): ChunkSourceInfo {
  const nullResponse: ChunkSourceInfo = {
    isUrl: false,
    text: null,
    href: null,
    icon: "file",
  };

  if (
    !chunks.length ||
    (!chunks[0].chunkSource?.startsWith("link://") &&
      !chunks[0].chunkSource?.startsWith("confluence://") &&
      !chunks[0].chunkSource?.startsWith("github://"))
  )
    return nullResponse;
  try {
    const url = new URL(
      chunks[0].chunkSource.split("link://")[1] ||
        chunks[0].chunkSource.split("confluence://")[1] ||
        chunks[0].chunkSource.split("github://")[1]
    );
    let text = url.host + url.pathname;
    let icon = "link";

    if (url.host.includes("youtube.com")) {
      text = title;
      icon = "youtube";
    }

    if (url.host.includes("github.com")) {
      text = title;
      icon = "github";
    }

    if (url.host.includes("atlassian.net")) {
      text = title;
      icon = "confluence";
    }

    return {
      isUrl: true,
      href: url.toString(),
      text,
      icon,
    };
  } catch (error) {
    console.warn(
      "Failed to parse chunk source URL:",
      chunks[0]?.chunkSource,
      error
    );
  }
  return nullResponse;
}

// Patch to render Confluence icon as a element like we do with Phosphor
const ConfluenceIcon = ({
  ...props
}: React.ImgHTMLAttributes<HTMLImageElement>) => (
  <img src={ConfluenceLogo} {...props} alt="" />
);

const ICONS: { [key: string]: React.ComponentType<any> } = {
  file: FileText,
  link: Link,
  youtube: YoutubeLogo,
  github: GithubLogo,
  confluence: ConfluenceIcon,
};

/**
 * Escapes special regex characters in a string to make it safe for use in RegExp
 * @param string - The string to escape
 * @returns Escaped string safe for regex use
 */
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"); // $& means the whole matched string
}
