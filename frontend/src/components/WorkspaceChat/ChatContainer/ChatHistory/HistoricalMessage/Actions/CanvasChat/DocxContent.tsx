import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { FileDoc, Download, ArrowClockwise } from "@phosphor-icons/react";
import { Button } from "@/components/Button";
import showToast from "@/utils/toast";
import { API_BASE } from "@/utils/constants";
import { baseHeaders } from "@/utils/request";
import Preloader from "@/components/Preloader";
import Markdown from "@/components/ui/Markdown";

interface DocxContentProps {
  sessionId: string | null;
  originalFilename: string | null;
  docxContent: string | null;
  onEdit: (newContent: string | null, instructions?: string) => void;
  onReset: () => void;
  isProcessing: boolean;
  highlightedFilename: string | null;
}

export default function DocxContent({
  sessionId,
  originalFilename,
  docxContent,
  onEdit,
  onReset,
  isProcessing,
  highlightedFilename,
}: DocxContentProps) {
  const { t } = useTranslation();
  const [instructions, setInstructions] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState("");

  useEffect(() => {
    if (docxContent) {
      setEditedContent(docxContent);
    }
  }, [docxContent]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSaveEdit = () => {
    onEdit(editedContent);
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setEditedContent(docxContent || "");
    setIsEditing(false);
  };

  const handleProcessDocument = async () => {
    if (!instructions.trim()) {
      showToast(
        t("docx-edit.no-instructions", "Please enter editing instructions"),
        "error"
      );
      return;
    }

    try {
      onEdit(null, instructions);
    } catch (error) {
      showToast(
        t("docx-edit.process-error", "Error processing document: ") +
          ((error as Error).message || "Unknown error"),
        "error"
      );
    }
  };

  const handleDownload = async () => {
    if (!sessionId || !highlightedFilename) {
      showToast(
        t("docx-edit.no-document", "No document available for download"),
        "error"
      );
      return;
    }

    try {
      // Create download URL
      const downloadUrl = `${API_BASE}/docx-edit/download/${sessionId}/${highlightedFilename}`;

      // Fetch the file with proper authentication
      const response = await fetch(downloadUrl, {
        method: "GET",
        headers: baseHeaders(),
      });

      if (!response.ok) {
        throw new Error(`Error downloading file: ${response.statusText}`);
      }

      // Get the file blob
      const blob = await response.blob();

      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = highlightedFilename;
      document.body.appendChild(link);
      link.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);
    } catch (error) {
      showToast(
        t("docx-edit.download-error", "Error downloading document: ") +
          (error as Error).message,
        "error"
      );
    }
  };

  // If we're still waiting for the document content
  if (!docxContent && !isProcessing) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <FileDoc size={64} className="text-primary mb-4" />
        <p className="text-lg font-medium mb-2">{originalFilename}</p>
        <p className="text-sm text-foreground/60 mb-6 text-center">
          {t(
            "docx-edit.edit-instructions",
            "Enter instructions for how you want to edit the document."
          )}
        </p>
        <div className="w-full mb-4">
          <textarea
            value={instructions}
            onChange={(e) => setInstructions(e.target.value)}
            placeholder={t(
              "docx-edit.instructions-placeholder",
              "e.g., Fix grammatical errors, make the tone more formal, add a conclusion paragraph..."
            )}
            className="w-full h-40 p-3 border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
          />
        </div>
        <Button
          onClick={handleProcessDocument}
          disabled={!instructions.trim()}
          className="w-full"
        >
          {t("docx-edit.process-button", "Process Document")}
        </Button>
      </div>
    );
  }

  // If we're processing the document
  if (isProcessing) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <Preloader />
        <p className="mt-4 text-foreground/60">
          {t("docx-edit.processing", "Processing document...")}
        </p>
      </div>
    );
  }

  // If we have the highlighted document ready for download
  if (highlightedFilename) {
    return (
      <div className="flex flex-col h-full">
        <div className="flex-grow overflow-y-auto p-4">
          <div className="flex flex-col items-center justify-center p-6 bg-primary/10 rounded-lg mb-6">
            <FileDoc size={64} className="text-primary mb-3" />
            <p className="text-foreground font-medium mb-1">
              {highlightedFilename}
            </p>
            <p className="text-sm text-foreground/60 mb-4">
              {t(
                "docx-edit.changes-highlighted",
                "Document with highlighted changes"
              )}
            </p>
            <Button onClick={handleDownload}>
              <Download size={20} />
              {t("docx-edit.download-button", "Download Document")}
            </Button>
          </div>

          <div className="bg-card rounded-lg p-4 mb-4">
            <h3 className="text-lg font-medium mb-2">
              {t("docx-edit.instructions-used", "Instructions Used")}
            </h3>
            <p className="text-foreground/80 whitespace-pre-wrap">
              {instructions}
            </p>
          </div>
        </div>

        <div className="flex justify-center p-4 border-t">
          <Button variant="outline" onClick={onReset}>
            <ArrowClockwise size={20} />
            {t("docx-edit.start-over-button", "Start Over")}
          </Button>
        </div>
      </div>
    );
  }

  // If we have the document content for editing
  return (
    <div className="flex flex-col h-full">
      {isEditing ? (
        <div className="flex-grow p-4">
          <textarea
            value={editedContent}
            onChange={(e) => setEditedContent(e.target.value)}
            className="w-full h-full p-3 border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
          />
          <div className="flex justify-end gap-2 mt-4">
            <Button variant="outline" onClick={handleCancelEdit}>
              {t("button.cancel")}
            </Button>
            <Button onClick={handleSaveEdit}>{t("button.save")}</Button>
          </div>
        </div>
      ) : (
        <div className="flex-grow overflow-y-auto p-4">
          <div className="bg-background p-4 rounded-lg border">
            <Markdown content={docxContent} />
          </div>
          <div className="flex justify-end mt-4">
            <Button onClick={handleEdit}>{t("button.edit")}</Button>
          </div>
        </div>
      )}
    </div>
  );
}
