import React from "react";
import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import { jest } from "@jest/globals";
import "@testing-library/jest-dom";
import ManualWorkEstimator from "../index";
import System from "@/models/system";

// Type definitions for jest-dom matchers
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R;
      toBeDisabled(): R;
      toBeVisible(): R;
      toHaveTextContent(text: string | RegExp): R;
    }
  }
}

// Mock the System model
jest.mock("@/models/system", () => ({
  __esModule: true,
  default: {
    estimateManualWork: jest.fn(),
  },
}));

// Mock the toast utility
jest.mock("@/utils/toast", () => jest.fn());

// Mock react-i18next
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock UI components
jest.mock("@/components/ui/Modal", () => {
  return function MockModal({
    isOpen,
    onClose,
    title,
    children,
  }: {
    isOpen: boolean;
    onClose: () => void;
    title: string;
    children: React.ReactNode;
  }) {
    if (!isOpen) return null;
    return (
      <div data-testid="modal">
        <div data-testid="modal-title">{title}</div>
        <button onClick={onClose} data-testid="modal-close">
          Close
        </button>
        {children}
      </div>
    );
  };
});

jest.mock("@/components/ui/Markdown", () => {
  return function MockMarkdown({ content }: { content: string }) {
    return <div data-testid="markdown-content">{content}</div>;
  };
});

jest.mock("@/components/Button", () => ({
  Button: function MockButton({
    children,
    onClick,
    disabled,
    ...props
  }: {
    children: React.ReactNode;
    onClick?: () => void;
    disabled?: boolean;
    [key: string]: any;
  }) {
    return (
      <button onClick={onClick} disabled={disabled} {...props}>
        {children}
      </button>
    );
  },
}));

describe("ManualWorkEstimator", () => {
  const mockQuestion = "What are the legal implications?";
  const mockAnswer = "The legal implications include...";

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Rendering", () => {
    it("renders the time estimate button", () => {
      const mockUser = { role: "admin" };
      render(
        <ManualWorkEstimator
          question={mockQuestion}
          answer={mockAnswer}
          user={mockUser}
          threadSlug="test-thread"
          chatId="test-chat-id"
        />
      );

      expect(
        screen.getByText("manual-work-estimator.button")
      ).toBeInTheDocument();
    });

    it("renders button for manager users", () => {
      const mockUser = { role: "manager" };
      render(
        <ManualWorkEstimator
          question={mockQuestion}
          answer={mockAnswer}
          user={mockUser}
          threadSlug="test-thread"
          chatId="test-chat-id"
        />
      );

      expect(
        screen.getByText("manual-work-estimator.button")
      ).toBeInTheDocument();
    });
  });

  describe("Functionality", () => {
    it("calls estimateManualWork when button is clicked", async () => {
      const mockUser = { role: "admin" };
      const mockResponse = {
        success: true,
        result: {
          textResponse: "Estimated time: 2-3 hours",
          prompt: {
            systemPrompt: "System prompt",
            userContent: "User content",
            provider: "OpenAI",
            model: "gpt-4",
          },
        },
      };

      (System.estimateManualWork as jest.MockedFunction<any>).mockResolvedValue(
        mockResponse
      );

      render(
        <ManualWorkEstimator
          question={mockQuestion}
          answer={mockAnswer}
          user={mockUser}
          threadSlug="test-thread"
          chatId="test-chat-id"
        />
      );

      const button = screen.getByText("manual-work-estimator.button");
      fireEvent.click(button);

      await waitFor(() => {
        expect(System.estimateManualWork).toHaveBeenCalledWith({
          question: mockQuestion,
          answer: mockAnswer,
        });
      });
    });

    it("opens modal with results after successful estimation", async () => {
      const mockUser = { role: "admin" };
      const mockResponse = {
        success: true,
        result: {
          textResponse: "Estimated time: 2-3 hours",
        },
      };

      (System.estimateManualWork as jest.MockedFunction<any>).mockResolvedValue(
        mockResponse
      );

      render(
        <ManualWorkEstimator
          question={mockQuestion}
          answer={mockAnswer}
          user={mockUser}
          threadSlug="test-thread"
          chatId="test-chat-id"
        />
      );

      const button = screen.getByText("manual-work-estimator.button");

      await act(async () => {
        fireEvent.click(button);
      });

      await waitFor(
        () => {
          expect(screen.getByTestId("modal")).toBeInTheDocument();
          expect(screen.getByTestId("modal-title")).toHaveTextContent(
            "manual-work-estimator.title"
          );
          expect(screen.getByTestId("markdown-content")).toHaveTextContent(
            "Estimated time: 2-3 hours"
          );
        },
        { timeout: 5000 }
      );
    });
  });

  describe("Role-based Access Control", () => {
    it("shows prompt details button for admin users", async () => {
      const mockUser = { role: "admin" };
      const mockResponse = {
        success: true,
        result: {
          textResponse: "Estimated time: 2-3 hours",
          prompt: {
            systemPrompt: "System prompt",
            userContent: "User content",
            provider: "OpenAI",
            model: "gpt-4",
          },
        },
      };

      (System.estimateManualWork as jest.MockedFunction<any>).mockResolvedValue(
        mockResponse
      );

      render(
        <ManualWorkEstimator
          question={mockQuestion}
          answer={mockAnswer}
          user={mockUser}
          threadSlug="test-thread"
          chatId="test-chat-id"
        />
      );

      const button = screen.getByText("manual-work-estimator.button");

      await act(async () => {
        fireEvent.click(button);
      });

      await waitFor(
        () => {
          expect(
            screen.getByText("manual-work-estimator.show-prompt")
          ).toBeInTheDocument();
        },
        { timeout: 5000 }
      );
    });

    it("hides prompt details button for manager users", async () => {
      const mockUser = { role: "manager" };
      const mockResponse = {
        success: true,
        result: {
          textResponse: "Estimated time: 2-3 hours",
          prompt: {
            systemPrompt: "System prompt",
            userContent: "User content",
            provider: "OpenAI",
            model: "gpt-4",
          },
        },
      };

      (System.estimateManualWork as jest.MockedFunction<any>).mockResolvedValue(
        mockResponse
      );

      render(
        <ManualWorkEstimator
          question={mockQuestion}
          answer={mockAnswer}
          user={mockUser}
          threadSlug="test-thread"
          chatId="test-chat-id"
        />
      );

      const button = screen.getByText("manual-work-estimator.button");
      fireEvent.click(button);

      await waitFor(() => {
        expect(
          screen.queryByText("manual-work-estimator.show-prompt")
        ).not.toBeInTheDocument();
      });
    });
  });

  describe("Error Handling", () => {
    it("handles API errors gracefully", async () => {
      const mockUser = { role: "admin" };
      const mockError = new Error("API Error");

      (System.estimateManualWork as jest.MockedFunction<any>).mockRejectedValue(
        mockError
      );

      render(
        <ManualWorkEstimator
          question={mockQuestion}
          answer={mockAnswer}
          user={mockUser}
          threadSlug="test-thread"
          chatId="test-chat-id"
        />
      );

      const button = screen.getByText("manual-work-estimator.button");

      await act(async () => {
        fireEvent.click(button);
      });

      await waitFor(
        () => {
          expect(System.estimateManualWork).toHaveBeenCalled();
          // Modal should still open even on error
          expect(screen.getByTestId("modal")).toBeInTheDocument();
        },
        { timeout: 5000 }
      );
    });
  });
});
