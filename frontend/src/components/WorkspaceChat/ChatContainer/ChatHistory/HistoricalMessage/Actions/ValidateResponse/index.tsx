import { useState } from "react";
import System from "@/models/system";
import Modal from "@/components/ui/Modal";
import { GrValidate } from "react-icons/gr";
import { useTranslation } from "react-i18next";
import { CircleNotch } from "@phosphor-icons/react";
import Markdown from "@/components/ui/Markdown";
import showToast from "@/utils/toast";
import {
  PROMPT_INPUT_EVENT,
  SUBMIT_PROMPT_EVENT,
} from "@/components/WorkspaceChat/ChatContainer/PromptInput";
import { Button } from "@/components/Button";
import RenderMetrics from "@/components/WorkspaceChat/ChatContainer/ChatHistory/HistoricalMessage/Actions/RenderMetrics";
import { ChatMetrics } from "@/types";

interface ValidateResponseProps {
  answer: string;
  chatId?: string | number;
  llmInput?: string;
}

interface ValidationResult {
  textResponse?: string;
  metrics?: ChatMetrics;
  citations?: string[];
}

export default function ValidateResponse({
  answer,
  chatId,
  llmInput,
}: ValidateResponseProps) {
  const { t } = useTranslation();
  const [validationResult, setValidationResult] =
    useState<ValidationResult | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleValidateResponse = async () => {
    setIsLoading(true);
    try {
      const response = await System.validateResponse({
        answer,
        chatId,
        llmInput,
      });

      if (response.success) {
        setValidationResult(response.result);
      }
    } catch {
      showToast(t(`show-toast.validate-response-error`), "error");
    } finally {
      setIsLoading(false);
      setIsModalOpen(true);
    }
  };

  const handleAdjust = () => {
    if (validationResult?.textResponse) {
      const prefixedResult = `${t("validate-response.adjust-prefix")}${validationResult.textResponse}`;
      // Emit an event to update the prompt input and message state
      window.dispatchEvent(
        new CustomEvent(PROMPT_INPUT_EVENT, { detail: prefixedResult })
      );
      setIsModalOpen(false);
      requestAnimationFrame(() => {
        window.dispatchEvent(new CustomEvent(SUBMIT_PROMPT_EVENT));
      });
    } else {
      setIsModalOpen(false);
    }
  };

  const handleClose = () => {
    setIsModalOpen(false);
  };

  const {
    metrics,
    textResponse,
    citations: _citations = [],
  } = validationResult || {};

  const modalFooter = (
    <div className="flex items-center justify-between w-full">
      <div className="flex">
        <RenderMetrics metrics={metrics} />
      </div>

      <div className="flex gap-2">
        {textResponse && (
          <Button variant="outline" onClick={handleAdjust}>
            {t("validate-response.adjust-button")}
          </Button>
        )}

        <Button onClick={handleClose}>{t("button.ok")}</Button>
      </div>
    </div>
  );

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={handleValidateResponse}
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <CircleNotch className="animate-spin" />
            {t("validate-response.validating")}
          </>
        ) : (
          <>
            <GrValidate />
            {t("validate-response.button")}
          </>
        )}
      </Button>
      <Modal
        isOpen={isModalOpen}
        onClose={handleClose}
        title={t("validate-response.title")}
        className="md:max-w-[72rem] max-h-[56rem]"
        footer={modalFooter}
      >
        <div className="w-full px-2 overflow-y-auto max-h-[40rem]">
          {textResponse && (
            <div className="text-foreground font-normal text-base md:text-lg">
              <Markdown content={String(textResponse)} />
            </div>
          )}
        </div>
      </Modal>
    </>
  );
}
