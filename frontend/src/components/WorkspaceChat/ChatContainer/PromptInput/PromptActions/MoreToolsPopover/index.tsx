import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Tb<PERSON><PERSON>, TbSearch } from "react-icons/tb";
import { BsFillLightningChargeFill } from "react-icons/bs";
import { Button } from "@/components/Button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/Popover";
import Modal from "@/components/ui/Modal";
import PopoverItem from "@/components/ui/PopoverItem";
import PerformLegalQuestion from "../../../ChatHistory/PerformLegalQuestion";
import ImportMemo from "../../../ChatHistory/ImportMemo";
import SystemPromptEdit from "../../../ChatHistory/SystemPromptEdit";
import ImportMemoModal from "@/components/Modals/ImportMemo";
import SystemPromptEditModal from "../SystemPromptEditModal";
import AskLegalQuestModal from "@/components/Modals/AskLegalQuestion";

import { useParams } from "react-router-dom";
import {
  useSelectedFeatureCard,
  useSetSelectedFeatureCard,
} from "@/stores/userStore";
import { Workspace } from "@/types";
import System from "@/models/system";
import WorkspaceModel from "@/models/workspace";

interface MoreToolsPopoverProps {
  sendCommand: (command: string) => void;
  setIsLegalTemplateModalOpen: (open: boolean) => void;
  workspace: Workspace;
  useDeepSearch: boolean;
  setUseDeepSearch: (enabled: boolean) => void;
  isDocumentDrafting: boolean;
  storeUniversityMode: boolean;
}

export default function MoreToolsPopover({
  sendCommand,
  setIsLegalTemplateModalOpen,
  workspace,
  useDeepSearch,
  setUseDeepSearch,
  isDocumentDrafting,
  storeUniversityMode,
}: MoreToolsPopoverProps) {
  const { t } = useTranslation();
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [isPerformLegalTaskModalOpen, setIsPerformLegalTaskModalOpen] =
    useState(false);
  const [isImportMemoModalOpen, setIsImportMemoModalOpen] = useState(false);
  const [isSystemPromptEditModalOpen, setIsSystemPromptEditModalOpen] =
    useState(false);
  const { slug, threadSlug = "" } = useParams<{
    slug?: string;
    threadSlug?: string;
  }>();
  const selectedFeatureCard = useSelectedFeatureCard();
  const setSelectedFeatureCard = useSetSelectedFeatureCard();
  const [isLegalTaskEnabled, setIsLegalTaskEnabled] = useState(false);

  useEffect(() => {
    if (selectedFeatureCard === "complex-document-builder") {
      setIsPerformLegalTaskModalOpen(true);
      setSelectedFeatureCard(null);
    }
  }, [selectedFeatureCard, setSelectedFeatureCard]);

  useEffect(() => {
    const fetchLegalTaskStatus = async () => {
      try {
        const performLegalTaskResponse = await System.getPerformLegalTask();
        setIsLegalTaskEnabled(performLegalTaskResponse?.enabled);
      } catch (error) {
        console.error("Error fetching legal task status:", error);
      }
    };
    fetchLegalTaskStatus();
  }, []);

  const handleLegalTemplateClick = () => {
    setIsLegalTemplateModalOpen(true);
    setIsPopoverOpen(false);
  };

  const handlePerformLegalTaskClick = () => {
    setIsPerformLegalTaskModalOpen(true);
    setIsPopoverOpen(false);
  };

  const handleImportMemoClick = () => {
    setIsImportMemoModalOpen(true);
    setIsPopoverOpen(false);
  };

  const handleSystemPromptEditClick = () => {
    setIsSystemPromptEditModalOpen(true);
    setIsPopoverOpen(false);
  };

  const handleDeepSearchToggle = (enabled: boolean) => {
    setUseDeepSearch(enabled);
  };

  // Check if any tools are available
  const hasPerformLegalTask = isDocumentDrafting && isLegalTaskEnabled;
  const hasLegalTemplate = !storeUniversityMode;
  const hasImportMemo = isDocumentDrafting;
  const hasSystemPromptEdit =
    workspace?.type !== "document-drafting" && !storeUniversityMode;
  const hasDeepSearch = !isDocumentDrafting && !storeUniversityMode;

  const hasAnyTools =
    hasPerformLegalTask ||
    hasLegalTemplate ||
    hasImportMemo ||
    hasSystemPromptEdit ||
    hasDeepSearch;

  // Don't render the button if no tools are available
  if (!hasAnyTools) {
    return null;
  }

  return (
    <>
      <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm">
            <TbApps />
            {t("prompt-actions.more-tools")}
          </Button>
        </PopoverTrigger>

        <PopoverContent className="w-fit mb-2 p-4" align="start">
          <div className="flex flex-col gap-4">
            {hasPerformLegalTask && (
              <PerformLegalQuestion
                onPerformLegalTaskClick={handlePerformLegalTaskClick}
              />
            )}

            {/* Legal Template Button */}
            {hasLegalTemplate && (
              <PopoverItem
                icon={BsFillLightningChargeFill}
                heading={t("prompt-actions.legal-template-title")}
                description={t("prompt-actions.legal-template-description")}
                onClick={handleLegalTemplateClick}
              />
            )}

            {isDocumentDrafting && (
              <ImportMemo onImportMemoClick={handleImportMemoClick} />
            )}

            {/* System Prompt Edit */}
            <SystemPromptEdit
              onSystemPromptEditClick={handleSystemPromptEditClick}
              workspace={workspace}
              storeUniversityMode={storeUniversityMode}
            />

            {/* Web Search Toggle */}
            {hasDeepSearch && (
              <PopoverItem
                icon={TbSearch}
                heading={t("prompt-actions.deep-search-title")}
                description={t("prompt-actions.deep-search-description")}
                isToggle={true}
                checked={useDeepSearch}
                onToggleChange={handleDeepSearchToggle}
              />
            )}
          </div>
        </PopoverContent>
      </Popover>

      {/* Perform Legal Task Modal */}
      <Modal
        isOpen={isPerformLegalTaskModalOpen}
        onClose={() => setIsPerformLegalTaskModalOpen(false)}
        className="w-[400px] max-w-[90vw] max-h-[90vh] overflow-y-auto"
        title={t("performLegalTask.title")}
      >
        <div className="w-full flex flex-col">
          <AskLegalQuestModal
            sendCommand={async (
              command: string,
              _isLegalTask: boolean,
              _files: any[],
              attachments: any[],
              options: any
            ) => {
              // For CDB flow, we need to handle this specially
              if (options?.cdb) {
                // First, add the command to the input field
                sendCommand(command);

                // Then trigger the actual CDB flow using the workspace API
                try {
                  await WorkspaceModel.multiplexStream({
                    workspaceSlug: workspace.slug,
                    threadSlug: threadSlug || undefined,
                    prompt: command,
                    chatHandler: options.chatHandler,
                    attachments: attachments || [],
                    chatId: options.chatIdRef?.current || null,
                    isCanvasChat: false,
                    preventChatCreation: options.preventChatCreation || false,
                    cdb: true,
                    cdbOptions: options.cdbOptions || [],
                    abortController: options.abortController,
                    displayMessage: options.displayMessage || null,
                  });
                } catch (error) {
                  console.error("Error in CDB flow:", error);
                  if (options.chatHandler) {
                    options.chatHandler({
                      type: "abort",
                      error:
                        error instanceof Error
                          ? error.message
                          : "Unknown error",
                      close: true,
                    });
                  }
                }
              } else {
                // For non-CDB flows, just send the command normally
                sendCommand(command);
              }
            }}
            onClose={() => setIsPerformLegalTaskModalOpen(false)}
            threadSlug={threadSlug || ""}
          />
        </div>
      </Modal>

      {/* Import Memo Modal */}
      {isImportMemoModalOpen && (
        <ImportMemoModal
          isOpen={isImportMemoModalOpen}
          onClose={() => setIsImportMemoModalOpen(false)}
          workspaceSlug={slug || ""}
          threadSlug={threadSlug || ""}
        />
      )}

      {/* System Prompt Edit Modal */}
      {isSystemPromptEditModalOpen && (
        <SystemPromptEditModal
          isOpen={isSystemPromptEditModalOpen}
          onClose={() => setIsSystemPromptEditModalOpen(false)}
          workspace={{ openAiPrompt: (workspace as any)?.openAiPrompt }}
        />
      )}
    </>
  );
}
