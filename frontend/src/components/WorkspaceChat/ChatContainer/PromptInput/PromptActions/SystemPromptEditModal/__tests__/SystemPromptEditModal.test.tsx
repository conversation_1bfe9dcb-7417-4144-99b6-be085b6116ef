import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import { describe, it, expect, jest, beforeEach } from "@jest/globals";
import SystemPromptEditModal from "../index";

// Type definitions for jest-dom matchers
declare module "expect" {
  interface AsymmetricMatchers {
    toBeInTheDocument(): void;
    toBeDisabled(): void;
    toBeVisible(): void;
    toHaveTextContent(text: string | RegExp): void;
  }
  interface Matchers<R> {
    toBeInTheDocument(): R;
    toBeDisabled(): R;
    toBeVisible(): R;
    toHaveTextContent(text: string | RegExp): R;
  }
}

// Mock dependencies
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string, fallback?: string) => {
      const translations = {
        "system-prompt-edit.modal-title": "Edit System Prompt",
        "system-prompt-edit.modal-description":
          "Customize your system prompt to change how the AI responds",
      };
      return translations[key as keyof typeof translations] || fallback || key;
    },
  }),
}));

jest.mock("@/hooks/useUser", () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock("@/models/user", () => ({
  __esModule: true,
  default: {
    getCustomSystemPrompt: jest.fn(),
    setCustomSystemPrompt: jest.fn(),
    clearCustomSystemPrompt: jest.fn(),
  },
}));

jest.mock("@/utils/toast", () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock("@/components/ui/Modal", () => {
  return function MockModal({
    title,
    isOpen,
    onClose,
    children,
  }: {
    title: string;
    isOpen: boolean;
    onClose: () => void;
    children: React.ReactNode;
  }) {
    return isOpen ? (
      <div data-testid="modal" role="dialog">
        <h2>{title}</h2>
        <button onClick={onClose} data-testid="modal-close">
          Close
        </button>
        {children}
      </div>
    ) : null;
  };
});

jest.mock("@/components/ui/Label", () => {
  return function MockLabel({
    children,
    htmlFor,
  }: {
    children: React.ReactNode;
    htmlFor?: string;
  }) {
    return <label htmlFor={htmlFor}>{children}</label>;
  };
});

jest.mock("@/components/ui/Textarea", () => {
  const mockReact = require("react");
  const MockTextarea = mockReact.forwardRef(function MockTextarea(
    props: any,
    ref: any
  ) {
    const { onChange, onBlur, name, value, ...otherProps } = props;

    return mockReact.createElement("textarea", {
      ref,
      name,
      onChange,
      onBlur,
      value,
      "data-testid": "textarea",
      ...otherProps,
    });
  });

  // Set displayName for better React DevTools support
  MockTextarea.displayName = "MockTextarea";

  return MockTextarea;
});

jest.mock("@/components/ui/FormItem", () => {
  return function MockFormItem({ children }: { children: React.ReactNode }) {
    return <div data-testid="form-item">{children}</div>;
  };
});

jest.mock("@/components/Button", () => ({
  Button: ({
    children,
    onClick,
    disabled,
    type,
    ...props
  }: {
    children: React.ReactNode;
    onClick?: () => void;
    disabled?: boolean;
    type?: "reset" | "submit" | "button";
    [key: string]: any;
  }) => (
    <button onClick={onClick} disabled={disabled} type={type} {...props}>
      {children}
    </button>
  ),
}));

jest.mock("../PromptLibraryModal", () => {
  return function MockPromptLibraryModal({
    isOpen,
  }: {
    isOpen: boolean;
    onClose: () => void;
    currentPrompt?: string;
    onApplyPrompt?: (promptText: string) => void;
  }) {
    return isOpen ? (
      <div data-testid="prompt-library-modal">Prompt Library Modal</div>
    ) : null;
  };
});

const useUser = require("@/hooks/useUser").default as jest.MockedFunction<any>;
const User = require("@/models/user").default as {
  getCustomSystemPrompt: jest.MockedFunction<any>;
  setCustomSystemPrompt: jest.MockedFunction<any>;
  clearCustomSystemPrompt: jest.MockedFunction<any>;
};

describe("SystemPromptEditModal", () => {
  const mockUser = {
    id: 1,
    custom_system_prompt: null,
  };

  const mockSetUser = jest.fn() as jest.MockedFunction<any>;

  beforeEach(() => {
    jest.clearAllMocks();
    useUser.mockReturnValue({
      user: mockUser,
      setUser: mockSetUser,
    });
  });

  it("does not render when isOpen is false", () => {
    User.getCustomSystemPrompt.mockResolvedValue({
      success: true,
      prompt: "Test",
    });

    render(
      <SystemPromptEditModal isOpen={false} onClose={jest.fn() as () => void} />
    );
    expect(screen.queryByTestId("modal")).not.toBeInTheDocument();
  });

  it("renders modal when isOpen is true", () => {
    User.getCustomSystemPrompt.mockResolvedValue({
      success: true,
      prompt: "Test",
    });

    render(
      <SystemPromptEditModal isOpen={true} onClose={jest.fn() as () => void} />
    );

    expect(screen.getByTestId("modal")).toBeInTheDocument();
    expect(screen.getByText("Edit System Prompt")).toBeInTheDocument();
    expect(
      screen.getByText(
        "Customize your system prompt to change how the AI responds"
      )
    ).toBeInTheDocument();
  });

  it("shows loading spinner initially", () => {
    User.getCustomSystemPrompt.mockImplementation(
      () => new Promise(() => {}) // Never resolves
    );

    render(
      <SystemPromptEditModal isOpen={true} onClose={jest.fn() as () => void} />
    );

    expect(screen.getByRole("dialog")).toBeInTheDocument();
    expect(document.querySelector(".animate-spin")).toBeInTheDocument();
  });

  it("calls User.getCustomSystemPrompt when modal opens", () => {
    User.getCustomSystemPrompt.mockResolvedValue({
      success: true,
      prompt: "Test",
    });

    render(
      <SystemPromptEditModal isOpen={true} onClose={jest.fn() as () => void} />
    );

    expect(User.getCustomSystemPrompt).toHaveBeenCalled();
  });

  it("does not call User.getCustomSystemPrompt when modal is closed", () => {
    User.getCustomSystemPrompt.mockResolvedValue({
      success: true,
      prompt: "Test",
    });

    render(
      <SystemPromptEditModal isOpen={false} onClose={jest.fn() as () => void} />
    );

    expect(User.getCustomSystemPrompt).not.toHaveBeenCalled();
  });

  it("provides workspace prop to component", () => {
    User.getCustomSystemPrompt.mockResolvedValue({
      success: true,
      prompt: "Test",
    });

    const workspace = { openAiPrompt: "Test workspace prompt" };

    render(
      <SystemPromptEditModal
        isOpen={true}
        onClose={jest.fn() as () => void}
        workspace={workspace}
      />
    );

    expect(screen.getByTestId("modal")).toBeInTheDocument();
  });
});
