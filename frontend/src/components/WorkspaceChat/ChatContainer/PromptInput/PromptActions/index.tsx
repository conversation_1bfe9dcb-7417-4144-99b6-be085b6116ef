import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { HiOutlineLightBulb } from "react-icons/hi";
import { CircleNotch } from "@phosphor-icons/react";
import AttachItem from "../AttachItem";
import ResetCommandUser from "../SlashCommands/resetUser";
import SlashCommandsButton from "../SlashCommands";
import TextSizeButton from "../TextSizeMenu";
import FilesButton from "../FilesButton";
import CustomAIEngine from "./CustomAIEngine";
import { useIsDocumentDrafting } from "@/stores/userStore";
import SpeechToText from "../SpeechToText";
import { useUniversityMode } from "@/stores/settingsStore";
import MoreToolsPopover from "./MoreToolsPopover";
import { Button } from "@/components/Button";
import { Workspace } from "@/types";

interface AttachmentType {
  id: string;
  name: string;
  [key: string]: any;
}

interface PromptActionsProps {
  sendCommand: (command: string, value?: string) => void;
  isUpgradeAvailable: boolean;
  isLoading: boolean;
  handleUpgradeUserPrompt: () => void;
  showSlashCommand: boolean;
  setShowSlashCommand: (show: boolean) => void;
  promptInput: string;
  setIsFilesModalOpen: (open: boolean) => void;
  attachments: AttachmentType[];
  setIsLegalTemplateModalOpen: (open: boolean) => void;
  useDeepSearch: boolean;
  setUseDeepSearch: (useDeepSearch: boolean) => void;
  workspace: Workspace;
}

export default function PromptActions({
  sendCommand,
  isUpgradeAvailable,
  isLoading,
  handleUpgradeUserPrompt,
  showSlashCommand,
  setShowSlashCommand,
  promptInput,
  setIsFilesModalOpen,
  attachments,
  setIsLegalTemplateModalOpen,
  useDeepSearch,
  setUseDeepSearch,
  workspace,
}: PromptActionsProps): JSX.Element {
  const { t } = useTranslation();
  const [upgradeButtonDisabled, setUpgradeButtonDisabled] =
    useState<boolean>(true);
  const isDocumentDrafting = useIsDocumentDrafting();
  const storeUniversityMode = useUniversityMode();

  // Check if this is a streamLQA workspace (not document-drafting)
  useEffect(() => {
    setUpgradeButtonDisabled(
      isLoading ||
        promptInput.trim().length < 10 ||
        promptInput.trim().split(/\s+/).filter(Boolean).length < 3
    );
  }, [isLoading, promptInput]);

  const handleFilesButtonClick = (): void => {
    setIsFilesModalOpen(true);
  };

  return (
    <div className="flex items-center flex-wrap gap-2">
      <SpeechToText
        sendCommand={(text: string, autoSubmit: boolean) =>
          sendCommand(text, autoSubmit ? "submit" : undefined)
        }
      />
      <AttachItem />
      <ResetCommandUser
        sendCommand={(command: string, submit?: boolean) =>
          sendCommand(command, submit ? "submit" : undefined)
        }
      />
      <SlashCommandsButton
        showing={showSlashCommand}
        setShowSlashCommand={setShowSlashCommand}
      />
      <TextSizeButton />

      <MoreToolsPopover
        sendCommand={sendCommand}
        setIsLegalTemplateModalOpen={setIsLegalTemplateModalOpen}
        workspace={workspace}
        useDeepSearch={useDeepSearch}
        setUseDeepSearch={setUseDeepSearch}
        isDocumentDrafting={isDocumentDrafting}
        storeUniversityMode={storeUniversityMode}
      />

      {/* Upgrade User Prompt */}
      {isUpgradeAvailable && !storeUniversityMode && (
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleUpgradeUserPrompt}
          disabled={upgradeButtonDisabled}
        >
          {isLoading ? (
            <CircleNotch className="animate-spin" />
          ) : (
            <HiOutlineLightBulb />
          )}
          {isLoading
            ? t("workspace-chats.prompt.upgrading")
            : t("workspace-chats.prompt.upgrade")}
        </Button>
      )}

      {!isDocumentDrafting && !storeUniversityMode && <CustomAIEngine />}

      {/* Always render FilesButton, it will internally determine if it should display */}
      <FilesButton
        onClick={handleFilesButtonClick}
        filesCount={attachments?.length || 0}
      />
    </div>
  );
}
