import { useEffect, useState } from "react";
import { useIsAgentSessionActive } from "@/utils/chat/agent";
import AddPresetModal from "./AddPresetModal";
import EditPresetModal from "./EditPresetModal";
import { useModal } from "@/hooks/useModal";
import System from "@/models/system";
import { Plus } from "@phosphor-icons/react";
import showToast from "@/utils/toast";
import { useTranslation } from "react-i18next";
import { BsThreeDotsVertical } from "react-icons/bs";

export const CMD_REGEX = new RegExp(/[^a-zA-Z0-9_-]/g);

interface SlashPreset {
  id: string | number;
  command: string;
  prompt: string;
  description?: string;
}

interface SlashPresetsProps {
  setShowing: (showing: boolean) => void;
  sendCommand: (command: string, appendSpace: boolean) => void;
}

export default function SlashPresets({
  setShowing,
  sendCommand,
}: SlashPresetsProps) {
  const { t } = useTranslation();
  const isActiveAgentSession = useIsAgentSessionActive();
  const {
    isOpen: isAddModalOpen,
    openModal: openAddModal,
    closeModal: closeAddModal,
  } = useModal();
  const {
    isOpen: isEditModalOpen,
    openModal: openEditModal,
    closeModal: closeEditModal,
  } = useModal();
  const [presets, setPresets] = useState<SlashPreset[]>([]);
  const [selectedPreset, setSelectedPreset] = useState<SlashPreset | null>(
    null
  );

  useEffect(() => {
    fetchPresets();
  }, []);
  if (isActiveAgentSession) return null;

  const fetchPresets = async () => {
    const presets = await System.getSlashCommandPresets();
    setPresets(presets);
  };

  const handleSavePreset = async (preset: Omit<SlashPreset, "id">) => {
    const { error } = await System.createSlashCommandPreset(preset);
    if (error) {
      showToast(error, "error");
      return false;
    }

    fetchPresets();
    closeAddModal();
    return true;
  };

  const handleEditPreset = (preset: SlashPreset) => {
    setSelectedPreset(preset);
    openEditModal();
  };

  const handleUpdatePreset = async (preset: {
    id: string | number;
    command: string;
    prompt: string;
    description: string;
  }): Promise<boolean> => {
    const { error } = await System.updateSlashCommandPreset(preset.id, preset);

    if (error) {
      showToast(error, "error");
      return false;
    }

    fetchPresets();
    closeEditModalAndResetPreset();
    return true;
  };

  const handleDeletePreset = async (presetId: any) => {
    await System.deleteSlashCommandPreset(presetId);
    fetchPresets();
    closeEditModalAndResetPreset();
  };
  const closeEditModalAndResetPreset = () => {
    closeEditModal();
    setSelectedPreset(null);
  };

  return (
    <>
      {presets.map((preset) => (
        <button
          key={preset.id}
          className="w-full hover:cursor-pointer hover:opacity-90 px-2 py-1 rounded-md flex flex-row items-center justify-between gap-2 primary-bg"
          onClick={() => {
            setShowing(false);
            sendCommand(`${preset.command} `, false);
          }}
        >
          <div className="w-full flex-col text-left flex pointer-events-none">
            <div className="text-white text-sm font-bold">{preset.command}</div>
            <div className="text-white text-opacity-60 text-[10px]">
              {preset.description}
            </div>
          </div>
          <div
            className="text-white text-sm hover:cursor-pointer rounded-full mt-1 hover:bg-black/30 p-1"
            onClick={(e) => {
              e.stopPropagation();
              handleEditPreset(preset);
            }}
          >
            <BsThreeDotsVertical size={20} />
          </div>
        </button>
      ))}
      <button
        onClick={openAddModal}
        className="w-full hover:cursor-pointer px-2 py-1 primary-bg rounded-md flex flex-col justify-start"
      >
        <div className="w-full flex-row flex pointer-events-none items-center gap-2">
          <Plus size={20} />
          <div className="text-white text-[13px] font-medium pr-1">
            {t("presets.tooltip-add")}
          </div>
        </div>
      </button>
      <AddPresetModal
        isOpen={isAddModalOpen}
        onClose={closeAddModal}
        onSave={handleSavePreset}
      />
      {selectedPreset && (
        <EditPresetModal
          isOpen={isEditModalOpen}
          onClose={closeEditModalAndResetPreset}
          onSave={handleUpdatePreset}
          onDelete={handleDeletePreset}
          preset={selectedPreset}
        />
      )}
    </>
  );
}
