import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import { BrowserRouter, useParams } from "react-router-dom";
import UploadedFilesModal from "../UploadedFilesModal";
import {
  useThreadAttachments,
  useRemoveAttachment,
} from "@/stores/attachmentStore";
import { cleanupAttachmentApi } from "@/services/attachmentService";
import { useIsDocumentDrafting } from "@/stores/userStore";
import useUser from "@/hooks/useUser";
import { useTokenManagement } from "@/hooks/useTokenManagement";
import showToast from "@/utils/toast";
import { Workspace } from "@/types";

// Mock dependencies
jest.mock("react-i18next", () => {
  const originalModule = jest.requireActual("react-i18next");
  return {
    ...originalModule,
    useTranslation: () => ({
      t: (key: string, _defaultValue?: string) => {
        // Return the key itself for toast messages to match the actual behavior
        if (key.startsWith("show-toast.")) {
          return key;
        }
        // Return test-specific translation to avoid affecting the UI
        return `test.${key}`;
      },
      i18n: {
        changeLanguage: jest.fn(),
        language: "en",
      },
    }),
  };
});

const mockUseParams = useParams as jest.MockedFunction<typeof useParams>;

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useParams: jest.fn(() => ({ threadSlug: "test-thread-id" })),
}));

jest.mock("@/stores/attachmentStore", () => ({
  useThreadAttachments: jest.fn(),
  useRemoveAttachment: jest.fn(),
}));

jest.mock("@/services/attachmentService", () => ({
  cleanupAttachmentApi: jest.fn(),
}));

jest.mock("@/stores/userStore", () => ({
  useIsDocumentDrafting: jest.fn(),
}));

jest.mock("@/hooks/useUser", () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock("@/hooks/useTokenManagement", () => ({
  useTokenManagement: jest.fn(),
}));

jest.mock("@/utils/toast", () => ({
  __esModule: true,
  default: jest.fn(),
}));

// Mock Modal component to render its content directly
jest.mock("@/components/ui/Modal", () => ({
  __esModule: true,
  default: ({ isOpen, children, title }: any) =>
    isOpen ? (
      <div data-testid="modal">
        <div data-testid="modal-title">{title}</div>
        {children}
      </div>
    ) : null,
}));

describe("UploadedFilesModal", () => {
  const mockWorkspace: Workspace = {
    id: 1,
    name: "Test Workspace",
    slug: "test-workspace",
    chatModel: "gpt-4",
  };

  const mockUser = {
    id: 1,
    username: "testuser",
    role: "user",
  };

  const mockRemoveAttachment = jest.fn();
  const mockUpdateTokenLimits = jest.fn();

  const mockAttachments = [
    {
      uid: "1",
      id: "1",
      name: "test-document.pdf",
      type: "application/pdf",
      tokenCount: 1500,
      document: {
        location: "/path/to/test-document.pdf",
      },
    },
    {
      uid: "2",
      id: "2",
      name: "test-image.png",
      type: "image/png",
      tokenCount: 500,
      document: {
        location: "/path/to/test-image.png",
      },
    },
    {
      uid: "3",
      id: "3",
      name: "test-code.js",
      type: "application/javascript",
      tokenCount: 2000,
      document: {
        location: "/path/to/test-code.js",
      },
    },
  ];

  const mockContextLimits = {
    totalWindow: 10000,
    availableTokens: 6000,
  };

  const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
    workspace: mockWorkspace,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseParams.mockReturnValue({ threadSlug: "test-thread-id" } as any);
    (useThreadAttachments as jest.Mock).mockReturnValue(mockAttachments);
    (useRemoveAttachment as jest.Mock).mockReturnValue(mockRemoveAttachment);
    (useIsDocumentDrafting as jest.Mock).mockReturnValue(false);
    (useUser as jest.Mock).mockReturnValue({ user: mockUser });
    (useTokenManagement as jest.Mock).mockReturnValue({
      contextLimits: mockContextLimits,
      updateTokenLimits: mockUpdateTokenLimits,
    });
    (cleanupAttachmentApi as jest.Mock).mockResolvedValue({ success: true });
  });

  const renderWithRouter = (component: React.ReactElement) => {
    return render(<BrowserRouter>{component}</BrowserRouter>);
  };

  describe("Rendering", () => {
    it("should render when isOpen is true", () => {
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      expect(screen.getByTestId("modal")).toBeInTheDocument();
    });

    it("should not render when isOpen is false", () => {
      renderWithRouter(<UploadedFilesModal {...defaultProps} isOpen={false} />);
      expect(screen.queryByTestId("modal")).not.toBeInTheDocument();
    });

    it("should display modal title with total tokens", () => {
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      const title = screen.getByTestId("modal-title");
      expect(title).toHaveTextContent("test.workspace-chats.attached-files");
      expect(title).toHaveTextContent("test.workspace-chats.total-tokens");
      expect(title).toHaveTextContent("4,000");
    });

    it("should display context window information", () => {
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      expect(
        screen.getByText(/test.workspace-chats.context-window/)
      ).toBeInTheDocument();
      expect(screen.getByText(/10,000/)).toBeInTheDocument();
      expect(
        screen.getByText(/test.workspace-chats.remaining-tokens/)
      ).toBeInTheDocument();
      expect(screen.getByText(/6,000/)).toBeInTheDocument();
    });

    it("should render all attached files", () => {
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      expect(screen.getByText("test-document.pdf")).toBeInTheDocument();
      expect(screen.getByText("test-image.png")).toBeInTheDocument();
      expect(screen.getByText("test-code.js")).toBeInTheDocument();
    });

    it("should display token count for each file", () => {
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      expect(
        screen.getAllByText(/test.workspace-chats.token-count/)
      ).toHaveLength(3);
      expect(screen.getByText(/: 1,500/)).toBeInTheDocument();
      expect(screen.getByText(/: 500/)).toBeInTheDocument();
      expect(screen.getByText(/: 2,000/)).toBeInTheDocument();
    });

    it("should render correct file type icons", () => {
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      // Icons are rendered as components, check their parent containers
      const fileItems = screen
        .getAllByRole("button", { name: "" })
        .map((btn) => btn.parentElement?.parentElement);
      expect(fileItems).toHaveLength(3);
    });

    it("should render progress bar with correct width", () => {
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      const progressBar = screen
        .getByTestId("modal")
        .querySelector(".h-2.rounded-full.transition-all");
      expect(progressBar).toHaveStyle({ width: "40%" }); // 4000/10000 * 100
    });

    it("should render progress bar in red when over limit", () => {
      (useTokenManagement as jest.Mock).mockReturnValue({
        contextLimits: { totalWindow: 3000, availableTokens: -1000 },
        updateTokenLimits: mockUpdateTokenLimits,
      });
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      const progressBar = screen
        .getByTestId("modal")
        .querySelector(".h-2.rounded-full.transition-all");
      expect(progressBar).toHaveStyle({ backgroundColor: "#ef4444" });
    });
  });

  describe("File Removal", () => {
    it("should call cleanupAttachmentApi when removing a file", async () => {
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      const removeButtons = screen
        .getAllByRole("button")
        .filter((btn) => btn.querySelector("svg"));

      fireEvent.click(removeButtons[0]);

      await waitFor(() => {
        expect(cleanupAttachmentApi).toHaveBeenCalledWith(
          "/path/to/test-document.pdf"
        );
      });
    });

    it("should call removeAttachment after cleanup", async () => {
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      const removeButtons = screen
        .getAllByRole("button")
        .filter((btn) => btn.querySelector("svg"));

      fireEvent.click(removeButtons[0]);

      await waitFor(() => {
        expect(mockRemoveAttachment).toHaveBeenCalledWith(
          "test-thread-id",
          "1"
        );
      });
    });

    it("should show success toast on successful removal", async () => {
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      const removeButtons = screen
        .getAllByRole("button")
        .filter((btn) => btn.querySelector("svg"));

      fireEvent.click(removeButtons[0]);

      await waitFor(() => {
        expect(showToast).toHaveBeenCalledWith(
          "show-toast.file-removed",
          "success"
        );
      });
    });

    it("should show error toast on failed cleanup", async () => {
      (cleanupAttachmentApi as jest.Mock).mockResolvedValue({
        success: false,
        error: "Failed",
      });
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      const removeButtons = screen
        .getAllByRole("button")
        .filter((btn) => btn.querySelector("svg"));

      fireEvent.click(removeButtons[0]);

      await waitFor(() => {
        expect(showToast).toHaveBeenCalledWith(
          "show-toast.file-remove-error",
          "error"
        );
      });
    });

    it("should handle cleanup API exceptions", async () => {
      (cleanupAttachmentApi as jest.Mock).mockRejectedValue(
        new Error("Network error")
      );
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      const removeButtons = screen
        .getAllByRole("button")
        .filter((btn) => btn.querySelector("svg"));

      fireEvent.click(removeButtons[0]);

      await waitFor(() => {
        expect(showToast).toHaveBeenCalledWith(
          "show-toast.file-remove-error",
          "error"
        );
      });
    });

    it("should still remove attachment even if cleanup fails", async () => {
      (cleanupAttachmentApi as jest.Mock).mockRejectedValue(
        new Error("Network error")
      );
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      const removeButtons = screen
        .getAllByRole("button")
        .filter((btn) => btn.querySelector("svg"));

      fireEvent.click(removeButtons[0]);

      await waitFor(() => {
        expect(mockRemoveAttachment).toHaveBeenCalledWith(
          "test-thread-id",
          "1"
        );
      });
    });

    it("should close modal when removing last file", async () => {
      (useThreadAttachments as jest.Mock).mockReturnValue([mockAttachments[0]]);
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);

      // Find the remove button - it's the button with variant="destructive"
      const fileItem = screen
        .getByText("test-document.pdf")
        .closest(".flex.items-center.justify-between");
      const removeButton = fileItem?.querySelector("button");

      expect(removeButton).toBeInTheDocument();
      if (removeButton) {
        fireEvent.click(removeButton);
      }

      await waitFor(() => {
        expect(mockRemoveAttachment).toHaveBeenCalledWith(
          "test-thread-id",
          "1"
        );
      });

      // onClose is called synchronously after removeAttachment
      expect(defaultProps.onClose).toHaveBeenCalled();
    });

    it("should not close modal when multiple files remain", async () => {
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      const removeButtons = screen
        .getAllByRole("button")
        .filter((btn) => btn.querySelector("svg"));

      fireEvent.click(removeButtons[0]);

      await waitFor(() => {
        expect(mockRemoveAttachment).toHaveBeenCalled();
      });
      expect(defaultProps.onClose).not.toHaveBeenCalled();
    });

    it("should handle files without document location", async () => {
      const attachmentsWithoutLocation = [
        { ...mockAttachments[0], document: undefined },
      ];
      (useThreadAttachments as jest.Mock).mockReturnValue(
        attachmentsWithoutLocation
      );

      renderWithRouter(<UploadedFilesModal {...defaultProps} />);

      // Find the remove button
      const fileItem = screen
        .getByText("test-document.pdf")
        .closest(".flex.items-center.justify-between");
      const removeButton = fileItem?.querySelector("button");

      expect(removeButton).toBeInTheDocument();
      if (removeButton) {
        fireEvent.click(removeButton);
      }

      // Since there's no document location, cleanup should not be called
      expect(cleanupAttachmentApi).not.toHaveBeenCalled();

      // removeAttachment should be called immediately
      expect(mockRemoveAttachment).toHaveBeenCalledWith("test-thread-id", "1");

      // Success toast should be shown since no errors occurred
      await waitFor(() => {
        expect(showToast).toHaveBeenCalledWith(
          "show-toast.file-removed",
          "success"
        );
      });
    });
  });

  describe("Token Management", () => {
    it("should call updateTokenLimits when modal opens", () => {
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      expect(mockUpdateTokenLimits).toHaveBeenCalled();
    });

    it("should not call updateTokenLimits when modal is closed", () => {
      mockUpdateTokenLimits.mockClear();
      renderWithRouter(<UploadedFilesModal {...defaultProps} isOpen={false} />);
      expect(mockUpdateTokenLimits).not.toHaveBeenCalled();
    });

    it("should display zero remaining tokens correctly", () => {
      (useTokenManagement as jest.Mock).mockReturnValue({
        contextLimits: { totalWindow: 4000, availableTokens: 0 },
        updateTokenLimits: mockUpdateTokenLimits,
      });
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      expect(
        screen.getByText(/test.workspace-chats.remaining-tokens/)
      ).toBeInTheDocument();
      expect(screen.getByText(/\b0\b/)).toBeInTheDocument();
    });

    it("should handle negative available tokens", () => {
      (useTokenManagement as jest.Mock).mockReturnValue({
        contextLimits: { totalWindow: 3000, availableTokens: -1000 },
        updateTokenLimits: mockUpdateTokenLimits,
      });
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      expect(
        screen.getByText(/test.workspace-chats.remaining-tokens/)
      ).toBeInTheDocument();
      expect(screen.getByText(/\b0\b/)).toBeInTheDocument();
    });
  });

  describe("File Type Display", () => {
    const fileTypeTests = [
      { type: "application/pdf", expectedIcon: "PDF" },
      {
        type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        expectedIcon: "DOC",
      },
      { type: "text/html", expectedIcon: "HTML" },
      { type: "text/csv", expectedIcon: "CSV" },
      { type: "application/javascript", expectedIcon: "CODE" },
      { type: "image/jpeg", expectedIcon: "IMAGE" },
      { type: "text/plain", expectedIcon: "TEXT" },
    ];

    fileTypeTests.forEach(({ type }) => {
      it(`should render correct icon for ${type}`, () => {
        const singleAttachment = [
          {
            uid: "test",
            id: "test",
            name: `test.${type.split("/").pop()}`,
            type,
            tokenCount: 100,
            document: { location: "/test" },
          },
        ];
        (useThreadAttachments as jest.Mock).mockReturnValue(singleAttachment);

        renderWithRouter(<UploadedFilesModal {...defaultProps} />);
        expect(
          screen.getByText(`test.${type.split("/").pop()}`)
        ).toBeInTheDocument();
      });
    });
  });

  describe("Edge Cases", () => {
    it("should handle empty attachments list", () => {
      (useThreadAttachments as jest.Mock).mockReturnValue([]);
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      expect(screen.getByTestId("modal-title")).toHaveTextContent("0");
    });

    it("should handle attachments without tokenCount", () => {
      const attachmentsWithoutTokens = mockAttachments.map((att) => ({
        ...att,
        tokenCount: undefined,
      }));
      (useThreadAttachments as jest.Mock).mockReturnValue(
        attachmentsWithoutTokens
      );

      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      expect(screen.getByTestId("modal-title")).toHaveTextContent("0");
    });

    it("should handle missing thread ID", () => {
      mockUseParams.mockReturnValueOnce({ threadSlug: undefined } as any);
      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      expect(useThreadAttachments).toHaveBeenCalledWith("");
    });

    it("should handle attachments with uid but no id", () => {
      const attachmentsWithUidOnly = [{ ...mockAttachments[0], id: undefined }];
      (useThreadAttachments as jest.Mock).mockReturnValue(
        attachmentsWithUidOnly
      );

      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      expect(screen.getByText("test-document.pdf")).toBeInTheDocument();
    });
  });

  describe("Scrolling", () => {
    it("should handle overflow with many files", () => {
      const manyAttachments = Array.from({ length: 20 }, (_, i) => ({
        uid: `${i}`,
        id: `${i}`,
        name: `file-${i}.pdf`,
        type: "application/pdf",
        tokenCount: 100,
        document: { location: `/path/to/file-${i}.pdf` },
      }));
      (useThreadAttachments as jest.Mock).mockReturnValue(manyAttachments);

      renderWithRouter(<UploadedFilesModal {...defaultProps} />);
      const scrollContainer = screen
        .getByTestId("modal")
        .querySelector(".max-h-\\[60vh\\]");
      expect(scrollContainer).toHaveClass("overflow-y-auto");
    });
  });
});
