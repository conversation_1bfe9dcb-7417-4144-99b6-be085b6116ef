import {
  useState,
  useRef,
  useEffect,
  use<PERSON><PERSON>back,
  FormEvent,
  KeyboardEvent,
} from "react";
import { SlashCommands, useSlashCommands } from "./SlashCommands";
import { AvailableAgents, useAvailableAgents } from "./AgentMenu";
import { useTranslation } from "react-i18next";
import Workspace from "@/models/workspace";
import { useParams } from "react-router-dom";
import System from "@/models/system";
import showToast from "@/utils/toast";
import UploadedFilesModal from "./UploadedFilesModal";
import PromptActions from "./PromptActions";
import PromptSubmission from "./PromptSubmission";
import UpgradePromptModal from "./UpgradePromptModal";
import PromptTextarea from "./PromptTextarea";
import LegalTemplateModal from "@/components/Modals/LegalTemplateModal/index";
import { SUPPORTED_UPGRADE_PROVIDERS } from "@/utils/AiProviders/supportedLLMProviders";
import {
  useSelectedFeatureCard,
  useSetSelectedFeatureCard,
} from "@/stores/userStore";

export const PROMPT_INPUT_EVENT = "prompt-input-update";
export const SUBMIT_PROMPT_EVENT = "submit-prompt";

interface WorkspaceType {
  id: string;
  name: string;
  slug: string;
  [key: string]: unknown;
}

interface AttachmentType {
  id: string;
  name: string;
  type: string;
  size: number;
  [key: string]: unknown;
}

interface SubmitOptions {
  displayMessage?: string;
  settingsSuffix?: string;
  skipUserMessage?: boolean;
  existingChatId?: string;
}

interface ChangeEvent {
  target: {
    value: string;
  };
}

interface SystemSettings {
  LLM_PROVIDER_PU?: string;
  [key: string]: unknown;
}

interface PromptInputProps {
  workspace: WorkspaceType;
  submit: (prompt: string, options?: SubmitOptions) => void;
  onChange: (event: ChangeEvent) => void;
  inputDisabled: boolean;
  buttonDisabled: boolean;
  sendCommand: (command: string, value?: string) => void;
  attachments?: AttachmentType[];
  useDeepSearch: boolean;
  setUseDeepSearch: (useDeepSearch: boolean) => void;
  handleFiles: (files: FileList) => void;
  addUserMessageImmediately: (
    displayMessage: string,
    promptForLLM: string,
    attachmentsList: AttachmentType[]
  ) => string;
}

// Unused interface removed

interface UpgradePromptResponse {
  success: boolean;
  upgradedPrompt?: string;
}

export default function PromptInput({
  workspace,
  submit,
  onChange,
  inputDisabled,
  buttonDisabled,
  sendCommand,
  attachments = [],
  useDeepSearch,
  setUseDeepSearch,
  handleFiles,
  addUserMessageImmediately,
}: PromptInputProps): JSX.Element {
  const { t } = useTranslation();
  const selectedFeatureCard = useSelectedFeatureCard();
  const setSelectedFeatureCard = useSetSelectedFeatureCard();
  const [promptInput, setPromptInput] = useState<string>("");
  const { showAgents, setShowAgents } = useAvailableAgents();
  const { showSlashCommand, setShowSlashCommand } = useSlashCommands();
  const formRef = useRef<HTMLFormElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [upgradedPrompt, setUpgradedPrompt] = useState<string>("");
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [settings, setSettings] = useState<SystemSettings | undefined>();
  const [isFilesModalOpen, setIsFilesModalOpen] = useState<boolean>(false);
  const [isLegalTemplateModalOpen, setIsLegalTemplateModalOpen] =
    useState<boolean>(false);
  const { threadSlug } = useParams<{ threadSlug: string }>();

  useEffect(() => {
    if (selectedFeatureCard === "draft-from-template") {
      setIsLegalTemplateModalOpen(true);
      setSelectedFeatureCard(null);
    }
  }, [selectedFeatureCard, setSelectedFeatureCard]);

  useEffect(() => {
    async function fetchSettings(): Promise<void> {
      try {
        const settings = await System.keys();
        if (settings) {
          setSettings(settings);
        }
      } catch {
        // console.error("Error fetching settings:", error);
      }
    }
    fetchSettings();
  }, []);

  useEffect(() => {
    if (!inputDisabled && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [inputDisabled]);

  useEffect(() => {
    if (window) window.addEventListener(PROMPT_INPUT_EVENT, handlePromptUpdate);
    return () =>
      window?.removeEventListener(PROMPT_INPUT_EVENT, handlePromptUpdate);
  }, []);

  const handlePromptUpdate = (e: Event): void => {
    const customEvent = e as CustomEvent<string>;
    setPromptInput(customEvent?.detail ?? "");
  };

  const handleFilesWrapper = (files: File[]): void => {
    // Convert File[] to FileList-like structure for parent component
    const fileList = {
      item: (index: number) => files[index] || null,
      ...Array.from(files),
      length: files.length,
    } as unknown as FileList;
    handleFiles(fileList);
  };

  const checkForSlash = (e: React.ChangeEvent<HTMLTextAreaElement>): void => {
    const input = e.target.value;
    if (input === "/") setShowSlashCommand(true);
    if (showSlashCommand) setShowSlashCommand(false);
  };

  const checkForAt = (e: React.ChangeEvent<HTMLTextAreaElement>): void => {
    const input = e.target.value;
    if (input === "@") return setShowAgents(true);
    if (showAgents) return setShowAgents(false);
  };

  const captureEnter = async (
    event: KeyboardEvent<HTMLTextAreaElement>
  ): Promise<void> => {
    if (event.keyCode === 13 && !event.shiftKey) {
      event.preventDefault();
      if (!buttonDisabled && promptInput.trim()) {
        event.preventDefault();
        window.dispatchEvent(new CustomEvent(SUBMIT_PROMPT_EVENT));
      }
    }
  };

  const handleUpgradeUserPrompt = async (): Promise<void> => {
    setIsLoading(true);
    try {
      const uploadResponse = await Workspace.upgradeUserPrompt(promptInput);
      const data = uploadResponse.data as UpgradePromptResponse;
      if (data?.success) {
        setUpgradedPrompt(data.upgradedPrompt || "");
        setIsModalOpen(true);
      } else {
        showToast(t("show-toast.upgrade-answer-error"), "error");
      }
    } catch {
      // console.error("Error upgrading prompt:", error);
      showToast(t("show-toast.upgrade-answer-error"), "error");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmitUpgradedPrompt = (editedPrompt: string): void => {
    setIsModalOpen(false);
    submit(editedPrompt);
    setPromptInput("");
  };

  const handleDecline = (): void => {
    setIsModalOpen(false);
  };

  // Handler for the legal template modal
  // This function receives the generated prompt from the modal,
  // updates the input, and then submits it
  const handleLegalTemplateSubmit = useCallback(
    async (
      generatedPrompt: string,
      options: { documentType: string; settingsSuffix: string }
    ): Promise<void> => {
      const promptForLLM = generatedPrompt;
      const firstSentence =
        promptForLLM.split("\n")[0].trim().split(".")[0].trim() + ".";

      const newPromptText = promptInput
        ? `${promptInput}\n\n${generatedPrompt}`
        : generatedPrompt;

      // Show a toast notification
      showToast(t("legal-templates.submitting-template"), "info");

      // Call onChange to ensure parent state is updated, like in Upgrade modal flow
      onChange({ target: { value: newPromptText } });

      // Create the display message for the user
      const displayMessage =
        firstSentence || t("legal-templates.draft-template-display");

      // Immediately add the user message to chat history to show it before processing
      const attachmentsList: AttachmentType[] = [];
      const chatId = addUserMessageImmediately(
        displayMessage,
        promptForLLM,
        attachmentsList
      );

      // Now perform the background processing (prompt upgrade and LLM call)
      try {
        let upgradedPrompt = promptForLLM;
        try {
          const uploadResponse = await Workspace.upgradeUserPrompt(
            promptForLLM,
            "_TM"
          );
          const data = uploadResponse.data as UpgradePromptResponse;
          if (data?.success && data.upgradedPrompt) {
            upgradedPrompt = data.upgradedPrompt;
          }
        } catch {
          // console.error("Prompt upgrade failed, proceeding with original:");
        }

        // Use a custom submit that skips adding the user message again
        submit(upgradedPrompt, {
          displayMessage,
          settingsSuffix: options.settingsSuffix,
          skipUserMessage: true, // Flag to skip adding user message since we already did it
          existingChatId: chatId, // Use the same chatId
        });
      } catch {
        // console.error("Error submitting legal template:", error);
        showToast(t("legal-templates.submission-error"), "error");
      } finally {
        // Clear the local input state after submission
        setPromptInput("");
      }
    },
    [
      promptInput,
      setPromptInput,
      t,
      submit,
      onChange,
      addUserMessageImmediately,
    ]
  );

  const isUpgradeAvailable: boolean =
    !!settings?.LLM_PROVIDER_PU &&
    (SUPPORTED_UPGRADE_PROVIDERS.includes(settings.LLM_PROVIDER_PU) ||
      settings.LLM_PROVIDER_PU === "system-standard");

  return (
    <div className="relative">
      <UploadedFilesModal
        isOpen={isFilesModalOpen}
        onClose={() => setIsFilesModalOpen(false)}
        workspace={workspace}
      />
      <div>
        <SlashCommands
          showing={showSlashCommand}
          setShowing={setShowSlashCommand}
          sendCommand={sendCommand}
        />
        <AvailableAgents
          showing={showAgents}
          setShowing={setShowAgents}
          sendCommand={(command: string, appendSpace: boolean) => {
            sendCommand(command, appendSpace ? " " : "");
          }}
          promptRef={textareaRef}
        />
        <form
          onSubmit={(e: FormEvent<HTMLFormElement>) => {
            e.preventDefault();
            window.dispatchEvent(new CustomEvent(SUBMIT_PROMPT_EVENT));
          }}
          className="relative"
          ref={formRef}
        >
          <div className="flex flex-col items-center bg-background">
            <div className="flex flex-col gap-1 w-full mb-6 md:mb-12 pt-5 pb-3 border rounded-2xl bg-elevated shadow-sm">
              <PromptTextarea
                textareaRef={textareaRef}
                value={promptInput}
                onChange={(
                  e:
                    | React.ChangeEvent<HTMLTextAreaElement>
                    | { target: { value: string } }
                ) => {
                  onChange(e as React.ChangeEvent<HTMLTextAreaElement>);
                  setPromptInput(e.target.value);
                }}
                onKeyDown={captureEnter}
                disabled={inputDisabled}
                checkForSlash={checkForSlash}
                checkForAt={checkForAt}
                handleFiles={handleFilesWrapper}
              />
              <div className="flex items-end justify-between pl-3 pr-4">
                <PromptActions
                  sendCommand={sendCommand}
                  isUpgradeAvailable={isUpgradeAvailable}
                  isLoading={isLoading}
                  handleUpgradeUserPrompt={handleUpgradeUserPrompt}
                  showSlashCommand={showSlashCommand}
                  setShowSlashCommand={setShowSlashCommand}
                  promptInput={promptInput}
                  setIsFilesModalOpen={setIsFilesModalOpen}
                  attachments={attachments}
                  setIsLegalTemplateModalOpen={setIsLegalTemplateModalOpen}
                  useDeepSearch={useDeepSearch}
                  setUseDeepSearch={setUseDeepSearch}
                  workspace={workspace}
                />
                <PromptSubmission
                  promptInput={promptInput}
                  setPromptInput={setPromptInput}
                  buttonDisabled={buttonDisabled}
                  submit={async (prompt: string) => {
                    submit(prompt);
                  }}
                  onChange={onChange}
                  formRef={formRef}
                  textareaRef={textareaRef}
                  setShowSlashCommand={setShowSlashCommand}
                  setShowAgents={setShowAgents}
                  upgradedPrompt={upgradedPrompt}
                  isModalOpen={isModalOpen}
                  setIsModalOpen={setIsModalOpen}
                  threadSlug={threadSlug}
                />
              </div>
            </div>
          </div>
        </form>
      </div>

      <UpgradePromptModal
        isOpen={isModalOpen}
        originalPrompt={promptInput}
        upgradedPrompt={upgradedPrompt}
        onAccept={handleSubmitUpgradedPrompt}
        onDecline={handleDecline}
        onChange={onChange}
      />

      <LegalTemplateModal
        isOpen={isLegalTemplateModalOpen}
        onClose={() => setIsLegalTemplateModalOpen(false)}
        onSubmit={handleLegalTemplateSubmit}
      />
    </div>
  );
}
