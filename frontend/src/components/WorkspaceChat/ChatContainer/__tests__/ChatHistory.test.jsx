import { render, screen } from "@testing-library/react";
import { <PERSON><PERSON><PERSON><PERSON>outer } from "react-router-dom";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "../ChatHistory";
import "@testing-library/jest-dom";

// Mock the entire ManageWorkspace module to avoid SVG import issues
jest.mock("@/components/Modals/ManageWorkspace", () => ({
  __esModule: true,
  default: () => null,
  useManageWorkspaceModal: () => ({
    showing: false,
    hideModal: jest.fn(),
    refreshTrigger: false,
  }),
}));

// Mock Markdown component to avoid ESM issues
jest.mock("@/components/ui/Markdown", () => ({
  __esModule: true,
  default: ({ content }) => <div data-testid="markdown">{content}</div>,
}));

// Mock hooks and stores
jest.mock("@/stores/userStore", () => ({
  __esModule: true,
  useTextSize: () => "normal",
}));

// Create stable mock functions
const mockNavigate = jest.fn();

// Mock react-router-dom
jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useParams: () => ({ threadSlug: null }),
  useNavigate: () => mockNavigate,
}));

// Mock react-i18next
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key) => key,
    i18n: {
      changeLanguage: jest.fn(),
      language: "en",
    },
  }),
}));

// Mock system model to avoid fetch issues
jest.mock("@/models/system", () => ({
  __esModule: true,
  default: {
    keys: jest.fn().mockResolvedValue({
      ScrollSettings: {
        isEnabled: false,
        className: "",
      },
    }),
  },
}));

// Mock child components
jest.mock("../ChatHistory/HistoricalMessage", () => ({
  __esModule: true,
  default: ({ message, chat }) => (
    <div data-testid="historical-message">{message || chat?.content}</div>
  ),
}));

jest.mock("../ChatHistory/PromptReply", () => ({
  __esModule: true,
  default: ({ reply }) => <div data-testid="prompt-reply">{reply}</div>,
}));

// Wrapper component for Router context
const RouterWrapper = ({ children }) => (
  <BrowserRouter>{children}</BrowserRouter>
);

describe("ChatHistory React.memo optimization", () => {
  const mockWorkspace = {
    id: 1,
    slug: "test-workspace",
    name: "Test Workspace",
  };

  const mockHistory = [
    { role: "user", content: "Hello", uuid: "1" },
    { role: "assistant", content: "Hi there", uuid: "2" },
  ];

  const defaultProps = {
    history: mockHistory,
    workspace: mockWorkspace,
    sendCommand: jest.fn(),
    updateHistory: jest.fn(),
    regenerateAssistantMessage: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockNavigate.mockClear();
  });

  afterEach(() => {
    jest.clearAllTimers();
    jest.useRealTimers();
    jest.restoreAllMocks();
  });

  it("should render chat history correctly", () => {
    render(
      <RouterWrapper>
        <ChatHistory {...defaultProps} />
      </RouterWrapper>
    );

    // Both messages should be rendered as historical messages since they're not animating
    const messages = screen.getAllByTestId("historical-message");
    expect(messages).toHaveLength(2);
    expect(messages[0]).toHaveTextContent("Hello");
    expect(messages[1]).toHaveTextContent("Hi there");
  });

  it("should not re-render when props haven't changed", () => {
    // Since ChatHistory is memoized, we can't easily test render counts
    // This test validates that the component renders without errors
    const { rerender } = render(
      <RouterWrapper>
        <ChatHistory {...defaultProps} />
      </RouterWrapper>
    );

    // Re-render with same props
    expect(() => {
      rerender(
        <RouterWrapper>
          <ChatHistory {...defaultProps} />
        </RouterWrapper>
      );
    }).not.toThrow();
  });

  it("should re-render when history length changes", () => {
    const { rerender } = render(
      <RouterWrapper>
        <ChatHistory {...defaultProps} />
      </RouterWrapper>
    );

    const newHistory = [
      ...mockHistory,
      { role: "user", content: "New message", uuid: "3" },
    ];

    rerender(
      <RouterWrapper>
        <ChatHistory {...defaultProps} history={newHistory} />
      </RouterWrapper>
    );

    // Should show new message
    expect(screen.getAllByTestId("historical-message")).toHaveLength(3);
  });

  it("should re-render when workspace changes", () => {
    const { rerender } = render(
      <RouterWrapper>
        <ChatHistory {...defaultProps} />
      </RouterWrapper>
    );

    const newWorkspace = {
      id: 2,
      slug: "different-workspace",
      name: "Different Workspace",
    };

    // Re-render with different workspace
    expect(() => {
      rerender(<ChatHistory {...defaultProps} workspace={newWorkspace} />);
    }).not.toThrow();
  });

  it("should re-render when last message changes", () => {
    const { rerender } = render(
      <RouterWrapper>
        <ChatHistory {...defaultProps} />
      </RouterWrapper>
    );

    const updatedHistory = [
      mockHistory[0],
      { ...mockHistory[1], content: "Updated response" },
    ];

    rerender(
      <RouterWrapper>
        <ChatHistory {...defaultProps} history={updatedHistory} />
      </RouterWrapper>
    );

    // Should show updated message
    const messages = screen.getAllByTestId("historical-message");
    expect(messages).toHaveLength(2);
    expect(messages[1]).toHaveTextContent("Updated response");
  });

  it("should not re-render when non-memoized props change", () => {
    const { rerender } = render(
      <RouterWrapper>
        <ChatHistory {...defaultProps} />
      </RouterWrapper>
    );

    // Change callback functions (should not trigger re-render due to memo)
    expect(() => {
      rerender(
        <ChatHistory
          {...defaultProps}
          sendCommand={jest.fn()}
          updateHistory={jest.fn()}
        />
      );
    }).not.toThrow();
  });
});
