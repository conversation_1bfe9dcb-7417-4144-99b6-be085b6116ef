import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import AttachmentWrapper from "../Attachments";
import UploadedFilesModal from "../PromptInput/UploadedFilesModal";
import {
  useThreadAttachments,
  useAddAttachment,
} from "@/stores/attachmentStore";
import { processFileForAttachment } from "@/services/attachmentService";
import showToast from "@/utils/toast";
import { Workspace } from "@/types";

// Mock dependencies
jest.mock("react-i18next", () => {
  const originalModule = jest.requireActual("react-i18next");
  return {
    ...originalModule,
    useTranslation: () => ({
      t: (key: string, _defaultValue?: string) => {
        // Return a test-specific translation to avoid affecting the UI
        return `test.${key}`;
      },
      i18n: {
        changeLanguage: jest.fn(),
        language: "en",
      },
    }),
  };
});

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useParams: () => ({ threadSlug: "test-thread-id" }),
}));

jest.mock("@/stores/attachmentStore", () => ({
  useThreadAttachments: jest.fn(),
  useAddAttachment: jest.fn(),
  useRemoveAttachment: jest.fn(() => jest.fn()),
}));

jest.mock("@/services/attachmentService", () => ({
  processFileForAttachment: jest.fn(),
  cleanupAttachmentApi: jest.fn(),
}));

jest.mock("@/utils/toast", () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock("@/stores/userStore", () => ({
  useIsDocumentDrafting: jest.fn(() => false),
}));

jest.mock("@/hooks/useUser", () => ({
  __esModule: true,
  default: jest.fn(() => ({
    user: { id: 1, username: "testuser", role: "user" },
  })),
}));

jest.mock("@/hooks/useTokenManagement", () => ({
  useTokenManagement: jest.fn(() => ({
    contextLimits: { totalWindow: 10000, availableTokens: 8000 },
    updateTokenLimits: jest.fn(),
  })),
}));

// Mock Modal component to render its content directly
jest.mock("@/components/ui/Modal", () => ({
  __esModule: true,
  default: ({ isOpen, children, title }: any) =>
    isOpen ? (
      <div data-testid="modal">
        <div data-testid="modal-title">{title}</div>
        {children}
      </div>
    ) : null,
}));

describe("Drag and Drop File Upload Integration", () => {
  const mockWorkspace: Workspace = {
    id: 1,
    name: "Test Workspace",
    slug: "test-workspace",
    chatModel: "gpt-4",
  };

  const mockAddAttachment = jest.fn();
  let mockAttachments: any[] = [];

  beforeEach(() => {
    jest.clearAllMocks();
    mockAttachments = [];
    (useThreadAttachments as jest.Mock).mockImplementation(
      () => mockAttachments
    );
    (useAddAttachment as jest.Mock).mockReturnValue(mockAddAttachment);
    (processFileForAttachment as jest.Mock).mockResolvedValue({
      uid: "uploaded-1",
      file: {
        name: "test.pdf",
        type: "application/pdf",
      },
      tokenCount: 1500,
      status: "success",
      document: { location: "/uploaded/test.pdf" },
    });
  });

  const renderWithRouter = (component: React.ReactElement) => {
    return render(<BrowserRouter>{component}</BrowserRouter>);
  };

  const createDragEvent = (type: string, files: File[] = []) => {
    const event = new Event(type, { bubbles: true }) as any;
    event.preventDefault = jest.fn();
    event.stopPropagation = jest.fn();
    event.dataTransfer = {
      files,
      types: files.length > 0 ? ["Files"] : [],
    };
    return event;
  };

  const createFile = (name: string, type: string, content: string = "") => {
    return new File([content], name, { type });
  };

  describe("Complete Drag and Drop Flow", () => {
    const IntegrationTestComponent = () => {
      const [showModal, setShowModal] = React.useState(false);
      const [isUploading, setIsUploading] = React.useState(false);

      const handleFiles = async (files: File[]) => {
        setIsUploading(true);
        try {
          for (const file of files) {
            const result = await processFileForAttachment(
              file,
              "test-thread-id"
            );
            if (result.status === "success") {
              const attachment = {
                uid: result.uid,
                name: result.file.name,
                type: result.file.type,
                tokenCount: result.tokenCount,
                document: result.document,
              };
              mockAttachments.push(attachment);
              mockAddAttachment("test-thread-id", attachment);
              showToast(`File ${file.name} uploaded successfully`, "success");
            } else {
              showToast(`Failed to upload ${file.name}`, "error");
            }
          }
        } catch {
          showToast("Upload failed", "error");
        } finally {
          setIsUploading(false);
          if (files.length > 0) {
            setShowModal(true);
          }
        }
      };

      return (
        <div>
          <AttachmentWrapper
            handleFiles={handleFiles}
            isUploading={isUploading}
          >
            <div data-testid="chat-container">Chat Interface</div>
          </AttachmentWrapper>
          <button onClick={() => setShowModal(true)}>Show Files</button>
          <UploadedFilesModal
            isOpen={showModal}
            onClose={() => setShowModal(false)}
            workspace={mockWorkspace}
          />
        </div>
      );
    };

    it("should complete full drag and drop upload flow", async () => {
      const { container } = renderWithRouter(<IntegrationTestComponent />);
      const wrapper = container.querySelector(
        ".h-full.w-full.relative"
      ) as HTMLElement;

      // Verify initial state
      expect(screen.getByTestId("chat-container")).toBeInTheDocument();
      expect(screen.queryByTestId("modal")).not.toBeInTheDocument();

      // Start drag operation
      const testFile = createFile(
        "integration-test.pdf",
        "application/pdf",
        "test content"
      );
      const dragEnterEvent = createDragEvent("dragenter", [testFile]);
      fireEvent(wrapper, dragEnterEvent);

      // Verify drag overlay appears
      await waitFor(() => {
        const overlay = screen.getByText("test.modale.document.drag-drop");
        expect(overlay.parentElement?.parentElement).toHaveClass(
          "opacity-100",
          "visible"
        );
      });

      // Drop the file
      const dropEvent = createDragEvent("drop", [testFile]);
      fireEvent(wrapper, dropEvent);

      // Verify upload API was called
      await waitFor(() => {
        expect(processFileForAttachment).toHaveBeenCalledWith(
          testFile,
          "test-thread-id"
        );
      });

      // Verify success toast
      await waitFor(() => {
        expect(showToast).toHaveBeenCalledWith(
          "File integration-test.pdf uploaded successfully",
          "success"
        );
      });

      // Verify attachment was added to store
      expect(mockAddAttachment).toHaveBeenCalledWith("test-thread-id", {
        uid: "uploaded-1",
        name: "test.pdf",
        type: "application/pdf",
        tokenCount: 1500,
        document: { location: "/uploaded/test.pdf" },
      });

      // Verify modal opens automatically after upload
      await waitFor(() => {
        expect(screen.getByTestId("modal")).toBeInTheDocument();
        expect(screen.getByText("test.pdf")).toBeInTheDocument();
      });
    });

    it("should handle multiple file drag and drop", async () => {
      const { container } = renderWithRouter(<IntegrationTestComponent />);
      const wrapper = container.querySelector(
        ".h-full.w-full.relative"
      ) as HTMLElement;

      const files = [
        createFile("file1.pdf", "application/pdf"),
        createFile(
          "file2.docx",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        ),
        createFile("file3.png", "image/png"),
      ];

      // Mock different responses for each file
      (processFileForAttachment as jest.Mock)
        .mockResolvedValueOnce({
          uid: "1",
          file: {
            name: "file1.pdf",
            type: "application/pdf",
          },
          tokenCount: 1000,
          status: "success",
        })
        .mockResolvedValueOnce({
          uid: "2",
          file: {
            name: "file2.docx",
            type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          },
          tokenCount: 2000,
          status: "success",
        })
        .mockResolvedValueOnce({
          uid: "3",
          file: {
            name: "file3.png",
            type: "image/png",
          },
          tokenCount: 500,
          status: "success",
        });

      // Drop multiple files
      const dropEvent = createDragEvent("drop", files);
      fireEvent(wrapper, dropEvent);

      // Verify all files were uploaded
      await waitFor(() => {
        expect(processFileForAttachment).toHaveBeenCalledTimes(3);
        expect(showToast).toHaveBeenCalledWith(
          "File file1.pdf uploaded successfully",
          "success"
        );
        expect(showToast).toHaveBeenCalledWith(
          "File file2.docx uploaded successfully",
          "success"
        );
        expect(showToast).toHaveBeenCalledWith(
          "File file3.png uploaded successfully",
          "success"
        );
      });

      // Verify all attachments were added
      expect(mockAddAttachment).toHaveBeenCalledTimes(3);
    });

    it("should handle upload failures gracefully", async () => {
      const { container } = renderWithRouter(<IntegrationTestComponent />);
      const wrapper = container.querySelector(
        ".h-full.w-full.relative"
      ) as HTMLElement;

      (processFileForAttachment as jest.Mock).mockResolvedValue({
        success: false,
        error: "Upload failed",
      });

      const testFile = createFile("failed-upload.pdf", "application/pdf");
      const dropEvent = createDragEvent("drop", [testFile]);
      fireEvent(wrapper, dropEvent);

      await waitFor(() => {
        expect(showToast).toHaveBeenCalledWith(
          "Failed to upload failed-upload.pdf",
          "error"
        );
      });

      // Verify attachment was not added
      expect(mockAddAttachment).not.toHaveBeenCalled();
    });

    it("should handle network errors during upload", async () => {
      const { container } = renderWithRouter(<IntegrationTestComponent />);
      const wrapper = container.querySelector(
        ".h-full.w-full.relative"
      ) as HTMLElement;

      (processFileForAttachment as jest.Mock).mockRejectedValue(
        new Error("Network error")
      );

      const testFile = createFile("network-error.pdf", "application/pdf");
      const dropEvent = createDragEvent("drop", [testFile]);
      fireEvent(wrapper, dropEvent);

      await waitFor(() => {
        expect(showToast).toHaveBeenCalledWith("Upload failed", "error");
      });
    });
  });

  describe("Drag Events Behavior", () => {
    it("should show and hide overlay during drag lifecycle", async () => {
      const { container } = renderWithRouter(
        <AttachmentWrapper handleFiles={jest.fn()}>
          <div>Content</div>
        </AttachmentWrapper>
      );
      const wrapper = container.firstChild as HTMLElement;

      // Initially no overlay
      let overlay = screen.getByText("test.modale.document.drag-drop")
        .parentElement?.parentElement;
      expect(overlay).toHaveClass("opacity-0", "invisible");

      // Drag enter - overlay appears
      const dragEnterEvent = createDragEvent("dragenter", [
        createFile("test.pdf", "application/pdf"),
      ]);
      fireEvent(wrapper, dragEnterEvent);

      overlay = screen.getByText("test.modale.document.drag-drop").parentElement
        ?.parentElement;
      expect(overlay).toHaveClass("opacity-100", "visible");

      // Drag leave - overlay disappears
      const dragLeaveEvent = createDragEvent("dragleave");
      dragLeaveEvent.relatedTarget = null;
      fireEvent(wrapper, dragLeaveEvent);

      overlay = screen.getByText("test.modale.document.drag-drop").parentElement
        ?.parentElement;
      expect(overlay).toHaveClass("opacity-0", "invisible");
    });

    it("should maintain overlay during dragover", () => {
      const { container } = renderWithRouter(
        <AttachmentWrapper handleFiles={jest.fn()}>
          <div>Content</div>
        </AttachmentWrapper>
      );
      const wrapper = container.firstChild as HTMLElement;

      // Continuous dragging over the component
      const dragOverEvent = createDragEvent("dragover", [
        createFile("test.pdf", "application/pdf"),
      ]);
      fireEvent(wrapper, dragOverEvent);
      fireEvent(wrapper, dragOverEvent);
      fireEvent(wrapper, dragOverEvent);

      const overlay = screen.getByText("test.modale.document.drag-drop")
        .parentElement?.parentElement;
      expect(overlay).toHaveClass("opacity-100", "visible");
    });
  });

  describe("File Input Integration", () => {
    it("should handle file selection through hidden input", async () => {
      const mockHandleFiles = jest.fn();
      renderWithRouter(
        <AttachmentWrapper handleFiles={mockHandleFiles}>
          <div>Content</div>
        </AttachmentWrapper>
      );

      const fileInput = document.getElementById(
        "dnd-chat-file-uploader"
      ) as HTMLInputElement;

      const files = [
        createFile("input-test1.pdf", "application/pdf"),
        createFile(
          "input-test2.docx",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        ),
      ];

      Object.defineProperty(fileInput, "files", {
        value: files,
        writable: false,
      });

      fireEvent.change(fileInput);

      expect(mockHandleFiles).toHaveBeenCalledWith(files);
    });
  });

  describe("Modal Integration", () => {
    it("should display uploaded files in modal with correct token counts", async () => {
      mockAttachments = [
        {
          uid: "1",
          name: "document1.pdf",
          type: "application/pdf",
          tokenCount: 1500,
        },
        {
          uid: "2",
          name: "image1.png",
          type: "image/png",
          tokenCount: 800,
        },
      ];

      renderWithRouter(
        <UploadedFilesModal
          isOpen={true}
          onClose={jest.fn()}
          workspace={mockWorkspace}
        />
      );

      expect(screen.getByText("document1.pdf")).toBeInTheDocument();
      expect(screen.getByText("image1.png")).toBeInTheDocument();
      expect(
        screen.getAllByText(/test.workspace-chats.token-count/)
      ).toHaveLength(2);
      expect(screen.getByTestId("modal-title")).toHaveTextContent("2,300");
    });

    it("should update modal when new files are added", async () => {
      let testAttachments: any[] = [];

      // Mock useThreadAttachments to return our test attachments
      (useThreadAttachments as jest.Mock).mockImplementation(
        () => testAttachments
      );

      const TestComponent = () => {
        return (
          <UploadedFilesModal
            isOpen={true}
            onClose={jest.fn()}
            workspace={mockWorkspace}
          />
        );
      };

      const { rerender } = renderWithRouter(<TestComponent />);

      // Initially no files
      expect(screen.getByTestId("modal-title")).toHaveTextContent("0");

      // Update the attachments and rerender
      testAttachments = [
        {
          uid: "1",
          id: "1",
          name: "file1.pdf",
          type: "application/pdf",
          tokenCount: 1000,
        },
      ];

      rerender(<TestComponent />);

      // Check that the file appears
      expect(screen.getByText("file1.pdf")).toBeInTheDocument();
      expect(screen.getByTestId("modal-title")).toHaveTextContent("1,000");
    });
  });

  describe("Error Handling", () => {
    it("should handle drag events with malformed dataTransfer", () => {
      const { container } = renderWithRouter(
        <AttachmentWrapper handleFiles={jest.fn()}>
          <div>Content</div>
        </AttachmentWrapper>
      );
      const wrapper = container.firstChild as HTMLElement;

      const malformedEvent = new Event("dragenter", { bubbles: true }) as any;
      malformedEvent.preventDefault = jest.fn();
      malformedEvent.stopPropagation = jest.fn();
      malformedEvent.dataTransfer = null;

      // The event will have issues but the component should handle it gracefully
      try {
        fireEvent(wrapper, malformedEvent);
      } catch {
        // Expected - the event might cause issues but component should be resilient
      }

      // Verify component still renders correctly even after malformed event
      expect(screen.getByText("Content")).toBeInTheDocument();
    });

    it("should handle file operations when thread ID is missing", () => {
      jest.spyOn(require("react-router-dom"), "useParams").mockReturnValue({});

      const mockHandleFiles = jest.fn();
      const { container } = renderWithRouter(
        <AttachmentWrapper handleFiles={mockHandleFiles}>
          <div>Content</div>
        </AttachmentWrapper>
      );
      const wrapper = container.firstChild as HTMLElement;

      const testFile = createFile("no-thread.pdf", "application/pdf");
      const dropEvent = createDragEvent("drop", [testFile]);
      fireEvent(wrapper, dropEvent);

      expect(mockHandleFiles).toHaveBeenCalledWith([testFile]);
    });
  });
});
