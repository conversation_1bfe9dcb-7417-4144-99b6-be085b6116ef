import React, {
  useState,
  useEffect,
  useRef,
  FormEvent,
  ChangeEvent,
  useCallback,
} from "react";
import ChatH<PERSON><PERSON> from "./ChatHistory";
import PromptInput, { PROMPT_INPUT_EVENT } from "./PromptInput";
import Workspace from "@/models/workspace";
import { handleChatResponse } from "@/utils/chat";
import { useParams } from "react-router-dom";
import { v4 } from "uuid";
import AttachmentWrapper from "./Attachments";
import SpeechRecognition, {
  useSpeechRecognition,
} from "react-speech-recognition";
import {
  useClearAttachments,
  useThreadAttachments,
} from "@/stores/attachmentStore";
import {
  useRexorActiveReference,
  useRexorWriteOrUpdateTransaction,
} from "@/stores/rexorStore";
import { useAttachmentUploader } from "@/hooks/useAttachmentUploader";
import { useTranslation } from "react-i18next";
import PromptSuggestions from "./PromptSuggestions";
import ExamplePromptSubmitter from "./ExamplePromptSubmitter";
import {
  formatAttachmentsForApi,
  createDisplayMessage,
} from "@/utils/attachments";
import { useIsDocumentDrafting } from "@/stores/userStore";
import { useInvoiceLogging, useRexorLinkage } from "@/stores/settingsStore";
import useProgressStore from "@/stores/progressStore";
import useThreadProgress from "@/hooks/useThreadProgress";
import System from "@/models/system";
import showToast from "@/utils/toast";
import { extractTotalHours } from "@/stores/estimationStore";
import type { ChatMessage, Workspace as WorkspaceType } from "@/types";

interface ChatContainerProps {
  workspace: WorkspaceType & { suggestedMessages?: string[] };
  knownHistory?: ChatMessage[];
}

interface SendCommandOptions {
  chatHandler?: ((chatResult: any) => void) | null;
  chatId?: string | null;
  preventNewChat?: boolean;
  isCanvasChat?: boolean;
  preventChatCreation?: boolean;
  cdb?: boolean;
  abortController?: AbortController | null;
  displayMessage?: string | null;
  cdbOptions?: any[];
  chatIdRef?: React.MutableRefObject<string | null> | null;
  settingsSuffix?: string;
}

interface SubmitOptions {
  skipUserMessage?: boolean;
  existingChatId?: string;
  displayMessage?: string;
  settingsSuffix?: string;
  cdb?: boolean;
}

function createUserMessage(
  content: string,
  chatId: string,
  attachments: any[]
): ChatMessage {
  return {
    uuid: v4(),
    content,
    role: "user",
    chatId,
    attachments,
  };
}

function createAssistantMessage(
  _userMessage: string,
  attachments: any[],
  existingChatId: string | null = null
): ChatMessage {
  const uuid = existingChatId || v4();
  return {
    uuid,
    content: "",
    role: "assistant",
    pending: true,
    animate: true,
    sources: [],
    attachments,
  };
}

export default function ChatContainer({
  workspace,
  knownHistory = [],
}: ChatContainerProps) {
  const { threadSlug = "" } = useParams<{ threadSlug?: string }>();
  const [message, setMessage] = useState<string>("");
  const [loadingResponse, setLoadingResponse] = useState<boolean>(false);
  const [chatHistory, setChatHistory] = useState<ChatMessage[]>(knownHistory);
  const invoice_ref = useRexorActiveReference();
  const [, setIsDragging] = useState<boolean>(false);
  const [useDeepSearch, setUseDeepSearch] = useState<boolean>(false);
  const abortControllerRef = useRef<AbortController | null>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();
  const { isUploading, handleFiles } = useAttachmentUploader();
  const attachments = useThreadAttachments(threadSlug);
  const clearAttachmentsStore = useClearAttachments();
  const emptyChat = !chatHistory?.length;
  const writeOrUpdateTransaction = useRexorWriteOrUpdateTransaction();
  const invoiceLogging = useInvoiceLogging();
  const rexorLinkage = useRexorLinkage();

  const progress = useThreadProgress(threadSlug);
  const { startProcess } = useProgressStore();
  const isDocumentDrafting = useIsDocumentDrafting();

  useEffect(() => {
    if (progress.stepStatus === "error" && !progress.isActive) {
      setLoadingResponse(false);
    }
  }, [progress.stepStatus, progress.isActive]);

  const handleMessageChange = (
    event: ChangeEvent<HTMLInputElement> | null,
    value?: string
  ): void => {
    if (value !== undefined) {
      setMessage(value);
    } else if (event?.target.value !== undefined && event.target.value !== "") {
      setMessage(event.target.value);
    }
  };

  const { listening, resetTranscript } = useSpeechRecognition({
    clearTranscriptOnListen: true,
  });

  const handleEstimationAndTransaction = async (
    question: string,
    answer: string,
    chatId: string | null = null
  ): Promise<void> => {
    if (question && answer) {
      try {
        const { result } = await System.estimateManualWork({
          question,
          answer,
        });
        const totalHours = extractTotalHours(result.textResponse);

        if (totalHours && totalHours > 0) {
          await writeOrUpdateTransaction(totalHours, chatId);
        }
      } catch {
        // console.error("Error in handleEstimationAndTransaction:", error);
        showToast(
          "Could not process Rexor transaction. See console for details.",
          "error"
        );
      }
    }
  };

  const handleSubmit = async (
    event: FormEvent | string,
    options: SubmitOptions = {}
  ): Promise<boolean | void> => {
    if (event && typeof event === "object" && "preventDefault" in event) {
      event.preventDefault();
    }

    if (isDocumentDrafting && !options.cdb) {
      startProcess(threadSlug, 3, "documentDrafting");
    }

    const isEvent =
      typeof event === "object" && event !== null && "target" in event;
    const promptToSend = isEvent ? message : (event as string) || message;
    const messageToSend = promptToSend;
    const messageToDisplay = options.displayMessage || promptToSend;

    if (!promptToSend || promptToSend.trim() === "") {
      return false;
    }

    const attachmentsList = formatAttachmentsForApi(attachments as any, t);
    const displayMessageWithAttachments = createDisplayMessage(
      messageToDisplay,
      attachmentsList
    );

    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    const controller = new AbortController();
    abortControllerRef.current = controller;

    let baseHistory = chatHistory;
    if (progress.error) {
      const cancellationMessage: ChatMessage = {
        uuid: `cancelled-${Date.now()}`,
        type: "statusResponse",
        content: t("chatProgress.cancelled"),
        role: "assistant",
        closed: true,
        error: null,
        animate: false,
        pending: false,
      };

      baseHistory = [...chatHistory, cancellationMessage];

      progress.clearError();
    }

    let prevChatHistory: ChatMessage[];
    let assistantMessageUuid: string;

    if (options.skipUserMessage && options.existingChatId) {
      prevChatHistory = baseHistory;
      assistantMessageUuid = options.existingChatId;
    } else {
      const assistantMessage = createAssistantMessage(
        messageToSend,
        attachmentsList
      );
      const userMessage = createUserMessage(
        displayMessageWithAttachments,
        assistantMessage.uuid,
        attachmentsList
      );

      prevChatHistory = [...baseHistory, userMessage, assistantMessage];
      assistantMessageUuid = assistantMessage.uuid;

      setChatHistory(prevChatHistory);
    }

    handleMessageChange(null, "");
    setLoadingResponse(true);

    if (listening) {
      endTTSSession();
    }

    let assistantResponse = "";

    try {
      await Workspace.multiplexStream({
        workspaceSlug: workspace.slug,
        threadSlug,
        prompt: messageToSend,
        chatHandler: (chatResult: any) => {
          handleChatResponse(
            chatResult,
            setLoadingResponse,
            setChatHistory,
            null,
            threadSlug
          );
          if (chatResult.textResponse) {
            assistantResponse += chatResult.textResponse;
          }
        },
        attachments: attachmentsList,
        invoice_ref,
        chatId: assistantMessageUuid,
        abortController: controller,
        displayMessage: options.displayMessage,
        useDeepSearch: useDeepSearch,
        settingsSuffix: options.settingsSuffix || "",
      });

      // Clear attachments after successful API call
      clearAttachmentsStore(threadSlug);
    } catch (_error: any) {
      // console.error("Error in handleSubmit stream:", error);
      setChatHistory((prev) => {
        const updatedHistory = prev.map((msg) => {
          if (msg.uuid === assistantMessageUuid) {
            return {
              ...msg,
              error: _error.message || "An error occurred.",
              pending: false,
              animate: false,
              closed: true,
            };
          }
          return msg;
        });
        return updatedHistory;
      });
      setLoadingResponse(false);
    } finally {
      if (abortControllerRef.current === controller) {
        abortControllerRef.current = null;
      }

      if (invoiceLogging && rexorLinkage) {
        await handleEstimationAndTransaction(
          messageToSend,
          assistantResponse,
          assistantMessageUuid
        );
      }
    }
  };

  function endTTSSession(): void {
    SpeechRecognition.stopListening();
    resetTranscript();
  }

  const regenerateAssistantMessage = (chatId: string): void => {
    const messageIndex = chatHistory.findIndex(
      (msg) => msg.chatId === chatId && msg.role === "assistant"
    );

    if (messageIndex === -1) {
      // console.error("Could not find message with chatId:", chatId);
      return;
    }

    const userMessageIndex = messageIndex - 1;

    if (userMessageIndex < 0 || chatHistory[userMessageIndex].role !== "user") {
      // console.error("Could not find corresponding user message");
      return;
    }

    const userMessage = chatHistory[userMessageIndex];
    const updatedHistory = chatHistory.slice(0, messageIndex);

    setChatHistory([
      ...updatedHistory,
      createAssistantMessage(
        userMessage.content,
        userMessage.attachments || [],
        chatId as string
      ),
    ]);

    setLoadingResponse(true);

    Workspace.multiplexStream({
      workspaceSlug: workspace.slug,
      threadSlug,
      prompt: userMessage.content,
      chatId: chatId as string | null | undefined,
      invoice_ref,
      chatHandler: (chatResult: any) =>
        handleChatResponse(
          chatResult,
          setLoadingResponse,
          setChatHistory,
          updatedHistory,
          threadSlug
        ),
      attachments: userMessage?.attachments || [],
      isCanvasChat: false,
      preventChatCreation: true,
      cdb: false,
      abortController: undefined,
      useDeepSearch: useDeepSearch,
      displayMessage: null,
      settingsSuffix: "",
    }).catch((_e: any) => {
      // console.error("Error regenerating message:", e);
      setChatHistory((prev) =>
        prev.map((msg) =>
          msg.uuid === chatId && msg.pending
            ? {
                ...msg,
                error: _e.message || "Failed to regenerate response.",
                pending: false,
                animate: false,
                closed: true,
              }
            : msg
        )
      );
      setLoadingResponse(false);
    });
  };

  /**
   * Send a command to the LLM prompt input.
   * @param command - The command to send to the LLM
   * @param submit - Whether the command was submitted (default: false)
   * @param history - The history of the chat
   * @param attachments - The attachments to send to the LLM
   * @param options - Additional options for the command
   * @returns Whether the command was sent successfully or response text
   * Priority for attachments is handled by reducing the available context window
   * by the total number of tokens in all attachments. This ensures that:
   * 1. Attachments are always included first
   * 2. Remaining tokens are available for Dynamic PDR vector search
   * 3. A minimum context window is maintained for coherent responses
   */
  const sendCommand = async (
    command: string,
    submit: boolean = false,
    history: ChatMessage[] = [],
    attachments: any[] = [],
    options: SendCommandOptions = {
      chatHandler: null,
      chatId: null,
      preventNewChat: false,
      isCanvasChat: false,
      preventChatCreation: true,
      cdb: false,
      abortController: null,
      displayMessage: null,
      cdbOptions: [],
      chatIdRef: null,
    }
  ): Promise<boolean | { text: string }> => {
    if (!command || command === "") return false;
    if (!submit) {
      handleMessageChange(null, command);
      window.dispatchEvent(
        new CustomEvent(PROMPT_INPUT_EVENT, { detail: command })
      );
      return false;
    }

    if (loadingResponse) return false;
    setLoadingResponse(true);

    const abortController = options.abortController || new AbortController();
    if (!options.abortController) {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      abortControllerRef.current = abortController;
    }

    let response = "";
    if (options.preventNewChat) {
      // Add user message to chat history so user can see what was sent
      const commandWithAttachments =
        attachments.length > 0
          ? `${command}\n\n${attachments.map((att) => att.contentString).join("\n\n")}`
          : command;

      // Use displayMessage if provided, otherwise use the command
      const displayText = options.displayMessage || commandWithAttachments;

      const newUserMessage: ChatMessage = {
        content: displayText,
        role: "user",
        uuid: v4(), // Proper UUID for the message
      };

      // Add the user message to chat history
      setChatHistory((prevHistory) => [...prevHistory, newUserMessage]);

      setLoadingResponse(true);
      let resolveCloseSignal: (() => void) | undefined;
      const closeSignalPromise = new Promise<void>((resolve) => {
        resolveCloseSignal = resolve;
      });

      try {
        const multiplexStreamPromise = Workspace.multiplexStream({
          workspaceSlug: workspace.slug,
          threadSlug,
          prompt: command,
          chatId: options.chatId,
          invoice_ref,
          chatHandler: (chatResult: any) => {
            response += chatResult.textResponse || chatResult.response || "";
            if (options.chatHandler) {
              options.chatHandler(chatResult);
            }
            if (chatResult?.close === true && resolveCloseSignal) {
              resolveCloseSignal();
            }
          },
          isCanvasChat: options.isCanvasChat,
          preventChatCreation: options.preventChatCreation,
          cdb: options.cdb,
          cdbOptions: options.cdbOptions,
          abortController,
          useDeepSearch: useDeepSearch,
          displayMessage: options.displayMessage,
          settingsSuffix: options.settingsSuffix || "",
        });

        await Promise.all([multiplexStreamPromise, closeSignalPromise]);

        setLoadingResponse(false);

        // Add final response to chat history
        if (response.trim()) {
          if (options.chatId) {
            // Update existing message with chatId
            const updatedHistory = [...history];
            const targetIdx = history.findIndex(
              (msg) => msg.chatId === options.chatId && msg.role === "assistant"
            );
            if (targetIdx >= 0) {
              updatedHistory[targetIdx].content = response.trim();
              setChatHistory(updatedHistory);
            }
          } else {
            // Add new assistant message for final response (like CDB)
            const finalChatId = options.chatIdRef?.current || null;

            const finalMessage: ChatMessage = {
              content: response.trim(),
              role: "assistant",
              uuid: v4(),
              closed: true,
              animate: false,
              pending: false,
              sources: [],
              ...(finalChatId && { chatId: finalChatId }),
            };
            setChatHistory((prevHistory) => [...prevHistory, finalMessage]);
          }
        }
      } catch (_error: any) {
        if (_error.name === "AbortError") {
          // Silent abort handling
        } else {
          // console.error("Error in sendCommand:", error);
        }
        setLoadingResponse(false);
        throw _error;
      } finally {
        setLoadingResponse(false);
        if (
          !options.abortController &&
          abortControllerRef.current === abortController
        ) {
          abortControllerRef.current = null;
        }
      }

      return { text: response.trim() };
    }

    const commandWithAttachments =
      attachments.length > 0
        ? `${command}\n\n${attachments.map((att) => att.contentString).join("\n\n")}`
        : command;

    let prevChatHistory: ChatMessage[];
    if (history.length > 0) {
      prevChatHistory = [
        ...history,
        {
          uuid: v4(),
          content: "",
          role: "assistant" as const,
          pending: true,
          animate: true,
        },
      ];
    } else {
      prevChatHistory = [
        ...chatHistory,
        {
          uuid: v4(),
          content: commandWithAttachments,
          role: "user" as const,
        },
      ];
    }

    setChatHistory(prevChatHistory);
    handleMessageChange(null, "");
    setLoadingResponse(true);

    try {
      if (options.preventNewChat) {
        let response = "";
        await Workspace.multiplexStream({
          workspaceSlug: workspace.slug,
          threadSlug,
          prompt: commandWithAttachments,
          chatId: options.chatId,
          invoice_ref,
          chatHandler: (chatResult: any) => {
            response += chatResult.textResponse || chatResult.response || "";
          },
          isCanvasChat: options.isCanvasChat,
          preventChatCreation: options.preventChatCreation,
          cdb: options.cdb,
          cdbOptions: options.cdbOptions,
          useDeepSearch: useDeepSearch,
          displayMessage: options.displayMessage,
          settingsSuffix: options.settingsSuffix || "",
        });
        setLoadingResponse(false);
        return { text: response.trim() };
      } else {
        await Workspace.multiplexStream({
          workspaceSlug: workspace.slug,
          threadSlug,
          prompt: commandWithAttachments,
          invoice_ref,
          chatHandler: (chatResult: any) =>
            handleChatResponse(
              chatResult,
              setLoadingResponse,
              setChatHistory,
              history.length > 0 ? history : chatHistory.slice(0, -2),
              threadSlug
            ),
          isCanvasChat: options.isCanvasChat,
          preventChatCreation: options.preventChatCreation,
          cdb: options.cdb,
          cdbOptions: options.cdbOptions,
          abortController: options.abortController,
          useDeepSearch: useDeepSearch,
          displayMessage: options.displayMessage,
          settingsSuffix: options.settingsSuffix || "",
        });
      }
    } catch (_error: any) {
      // console.error("Error in sendCommand:", error);

      if (options.cdb && threadSlug) {
        progress.cancel();
      }

      setChatHistory((prev) => {
        // Polyfill for findLastIndex (ES2023)
        let lastPendingIndex = -1;
        for (let i = prev.length - 1; i >= 0; i--) {
          if (prev[i].role === "assistant" && prev[i].pending) {
            lastPendingIndex = i;
            break;
          }
        }
        if (lastPendingIndex === -1) return prev;

        return prev.map((msg, index) =>
          index === lastPendingIndex
            ? {
                ...msg,
                error: _error.message || "An error occurred.",
                pending: false,
                animate: false,
                closed: true,
              }
            : msg
        );
      });
      setLoadingResponse(false);
      throw _error;
    } finally {
      setLoadingResponse(false);
    }

    return { text: response.trim() };
  };

  // To prevent too many re-renders we remotely listen for updates from the parent
  // via an event cycle. Otherwise, using message as a prop leads to a re-render every
  // change on the input.
  const handlePromptUpdate = useCallback(
    (e: CustomEvent): void => {
      const newValue = e?.detail ?? "";
      if (message === newValue) return;

      setMessage(newValue);
      window.dispatchEvent(
        new CustomEvent(PROMPT_INPUT_EVENT, { detail: newValue })
      );
    },
    [message]
  );

  useEffect(() => {
    if (window) {
      window.addEventListener(
        PROMPT_INPUT_EVENT,
        handlePromptUpdate as EventListener
      );
    }
    return () => {
      window?.removeEventListener(
        PROMPT_INPUT_EVENT,
        handlePromptUpdate as EventListener
      );
    };
  }, [handlePromptUpdate]);

  useEffect(() => {
    const chatContainerElement = chatContainerRef.current;
    if (chatContainerElement) {
      chatContainerElement.style.opacity = "1";
    }
  }, []);

  useEffect(() => {
    const fetchInitialData = async (): Promise<void> => {
      try {
        // Only fetch thread history if we have a valid threadSlug
        if (
          threadSlug &&
          typeof threadSlug === "string" &&
          threadSlug.trim() !== ""
        ) {
          const foundHistory = await Workspace.threads.chatHistory(
            workspace.slug,
            threadSlug
          );
          setChatHistory(foundHistory);
        } else {
          // If no valid threadSlug, use knownHistory or empty array
          setChatHistory(knownHistory || []);
        }
      } catch {
        // console.error("Error fetching initial data:", error);
        setChatHistory(knownHistory);
      }
    };

    setChatHistory([]);
    fetchInitialData();
  }, [threadSlug, workspace.slug, knownHistory]);

  // Function to immediately add a user message to chat history (for templates)
  const addUserMessageImmediately = (
    displayMessage: string,
    promptForLLM: string,
    attachmentsList: any[] = []
  ): string => {
    const FormattedAttachments = formatAttachmentsForApi(attachmentsList, t);
    const displayMessageWithAttachments = createDisplayMessage(
      displayMessage,
      FormattedAttachments
    );
    const assistantMessage = createAssistantMessage(
      promptForLLM,
      FormattedAttachments
    );
    const userMessage = createUserMessage(
      displayMessageWithAttachments,
      assistantMessage.uuid,
      FormattedAttachments
    );

    setChatHistory((prev) => [
      ...prev,
      userMessage as ChatMessage,
      assistantMessage as ChatMessage,
    ]);
    return assistantMessage.uuid;
  };

  return (
    <div
      className="relative flex flex-col w-full h-full px-6 transition-opacity duration-300 delay-200 opacity-0"
      ref={chatContainerRef}
    >
      <ExamplePromptSubmitter
        handleSubmit={handleSubmit}
        workspace={workspace}
        chatHistory={chatHistory as any}
      />
      <AttachmentWrapper
        isUploading={isUploading}
        handleFiles={handleFiles}
        onDraggingChange={setIsDragging}
      >
        <ChatHistory
          history={chatHistory}
          workspace={workspace}
          sendCommand={sendCommand}
          updateHistory={setChatHistory}
          regenerateAssistantMessage={regenerateAssistantMessage as any}
        />

        <div className="sticky bottom-0 w-full max-w-[58rem] mx-auto">
          {emptyChat && (
            <PromptSuggestions
              suggestions={
                workspace?.suggestedMessages?.map((msg: string) => ({
                  heading: "",
                  message: msg,
                })) ?? []
              }
              handleSubmit={handleSubmit}
            />
          )}
          <PromptInput
            submit={handleSubmit}
            onChange={handleMessageChange as any}
            inputDisabled={false}
            buttonDisabled={loadingResponse}
            sendCommand={sendCommand as any}
            attachments={attachments as any}
            handleFiles={handleFiles}
            useDeepSearch={useDeepSearch}
            setUseDeepSearch={setUseDeepSearch}
            workspace={workspace as any}
            addUserMessageImmediately={addUserMessageImmediately}
          />
        </div>
      </AttachmentWrapper>
    </div>
  );
}
