import React from "react";
import { render, waitFor } from "@testing-library/react";
import { MemoryRouter, Route, Routes } from "react-router-dom";

// Mock all necessary modules before importing the component
jest.mock("@/utils/chat/agent", () => ({
  useIsAgentSessionActive: () => false,
  websocketURI: () => "ws://localhost:3001",
  AGENT_SESSION_START: "agentSessionStart",
  AGENT_SESSION_END: "agentSessionEnd",
}));

jest.mock("@/utils/paths", () => ({
  __esModule: true,
  default: {
    home: () => "/",
    workspace: {
      chat: (workspace: string) => `/workspace/${workspace}`,
      thread: (workspace: string, thread: string) =>
        `/workspace/${workspace}/t/${thread}`,
    },
  },
}));

jest.mock("@/utils/toast");

jest.mock("@/models/system", () => ({
  __esModule: true,
  default: {
    keys: jest.fn().mockResolvedValue({}),
    getSlashCommandPresets: jest.fn().mockResolvedValue([]),
    getPerformLegalTask: jest.fn().mockResolvedValue({
      success: true,
      isEnabled: false,
    }),
    createSlashCommandPreset: jest.fn().mockResolvedValue({ success: true }),
    updateSlashCommandPreset: jest.fn().mockResolvedValue({ success: true }),
    deleteSlashCommandPreset: jest.fn().mockResolvedValue({ success: true }),
  },
}));

jest.mock("@/hooks/useUser", () => ({
  __esModule: true,
  default: () => ({ user: null }),
}));

jest.mock("@/models/workspace", () => ({
  __esModule: true,
  default: {
    findBySlugAndModule: jest.fn(),
    findByModule: jest.fn(),
    chatHistory: jest.fn(),
    threads: {
      chatHistory: jest.fn(),
    },
  },
}));

// Mock Modal component
jest.mock("@/components/ui/Modal", () => ({
  __esModule: true,
  default: ({ children, isOpen }: any) =>
    isOpen ? <div data-testid="modal">{children}</div> : null,
}));
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      language: "en",
      changeLanguage: jest.fn(),
      languages: ["en", "fr", "de"],
      isInitialized: true,
    },
  }),
  Trans: ({ children }: any) => children,
  initReactI18next: {
    type: "3rdParty",
    init: jest.fn(),
  },
  I18nextProvider: ({ children }: any) => children,
}));

// Mock TTSProvider
jest.mock("../../contexts/TTSProvider", () => ({
  TTSProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  useWatchForAutoPlayAssistantTTSResponse: jest.fn(),
}));

// Mock attachment hooks and store
jest.mock("@/hooks/useAttachmentUploader", () => ({
  __esModule: true,
  useAttachmentUploader: () => ({
    isUploading: false,
    handleFiles: jest.fn(),
  }),
}));

jest.mock("@/stores/attachmentStore", () => ({
  __esModule: true,
  useThreadAttachments: () => [],
  useClearAttachments: () => jest.fn(),
  useRemoveAttachment: () => jest.fn(),
}));

// Mock progress hooks
jest.mock("@/hooks/useThreadProgress", () => ({
  __esModule: true,
  default: () => ({
    stepStatus: null,
    isActive: false,
  }),
  useIsProcessActive: () => false,
}));

// Mock progress store
jest.mock("@/stores/progressStore", () => ({
  __esModule: true,
  default: Object.assign(
    (selector: Function) => {
      const state = {
        startProcess: jest.fn(),
        threads: new Map(),
      };
      return selector ? selector(state) : state;
    },
    {
      getState: () => ({
        threads: new Map(),
        startProcess: jest.fn(),
      }),
    }
  ),
}));

// Mock userStore hooks
jest.mock("@/stores/userStore", () => ({
  __esModule: true,
  useIsDocumentDrafting: () => false,
  useTextSize: () => "normal",
  useSetTextSize: () => jest.fn(),
  useIsLegalQA: () => false,
  useIsCdbSearch: () => false,
  useIsCdbSearchActive: () => false,
  useUniversityMode: () => false,
  usePerformLegalTask: () => false,
  usePerformLegalTaskActive: () => false,
  useIsRexorActive: () => false,
  useDocumentDraftFlow: () => false,
  useIsReferenceFlow: () => false,
  useSelectedFeatureCard: () => null,
  useSetSelectedFeatureCard: () => jest.fn(),
}));

// Mock rexor hooks
jest.mock("@/stores/rexorStore", () => ({
  __esModule: true,
  default: (selector: Function) => {
    const state = {
      showLoginModal: false,
      setShowLoginModal: jest.fn(),
    };
    return selector ? selector(state) : state;
  },
  useRexorActiveReference: () => null,
  useRexorWriteOrUpdateTransaction: () => jest.fn(),
  useRexorGetSavedUsername: () => jest.fn().mockReturnValue("test-user"),
  useRexorLogin: () => jest.fn(),
  useRexorLogout: () => jest.fn(),
  useRexorCheckLoginStatus: () => jest.fn(),
  useRexorLoading: () => false,
}));

// Mock settingsStore
jest.mock("@/stores/settingsStore", () => ({
  __esModule: true,
  useSystemLanguage: () => "en",
  useInvoiceLogging: () => jest.fn(),
  useRexorLinkage: () => jest.fn(),
  useUniversityMode: () => false,
}));

// Mock navigate
const mockNavigate = jest.fn();
jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useNavigate: () => mockNavigate,
  useParams: () => ({ threadSlug: "test-thread" }),
  useLocation: () => ({ pathname: "/workspace/test-workspace/t/test-thread" }),
}));

// Import components after mocks
import WorkspaceChat from "../index";
import WorkspaceModel from "@/models/workspace";
import System from "@/models/system";

describe("WorkspaceChat 404 Error Handling", () => {
  const mockWorkspace = {
    slug: "test-workspace",
    name: "Test Workspace",
    id: 1,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    sessionStorage.clear();
  });

  it("should redirect to workspace on 404 thread error", async () => {
    // Mock the API call to throw a 404 error
    const error404 = new Error("Thread not found") as any;
    error404.status = 404;

    (WorkspaceModel.threads.chatHistory as jest.Mock).mockRejectedValueOnce(
      error404
    );
    (System.keys as jest.Mock).mockResolvedValueOnce({});

    render(
      <MemoryRouter
        initialEntries={["/workspace/test-workspace/t/test-thread"]}
      >
        <Routes>
          <Route
            path="/workspace/:slug/t/:threadSlug"
            element={
              <WorkspaceChat loading={false} workspace={mockWorkspace} />
            }
          />
        </Routes>
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith("/workspace/test-workspace", {
        replace: true,
      });
    });
  });

  it("should not redirect on non-404 errors", async () => {
    // Mock the API call to throw a different error
    const error500 = new Error("Server error") as any;
    error500.status = 500;

    (WorkspaceModel.threads.chatHistory as jest.Mock).mockRejectedValueOnce(
      error500
    );
    (System.keys as jest.Mock).mockResolvedValueOnce({});

    const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();

    render(
      <MemoryRouter
        initialEntries={["/workspace/test-workspace/t/test-thread"]}
      >
        <Routes>
          <Route
            path="/workspace/:slug/t/:threadSlug"
            element={
              <WorkspaceChat loading={false} workspace={mockWorkspace} />
            }
          />
        </Routes>
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        "Error loading chat history:",
        error500
      );
      expect(mockNavigate).not.toHaveBeenCalled();
    });

    consoleErrorSpy.mockRestore();
  });

  it("should not redirect on 404 errors when no threadSlug", async () => {
    // Mock useParams to return no threadSlug
    jest.mock("react-router-dom", () => ({
      ...jest.requireActual("react-router-dom"),
      useNavigate: () => mockNavigate,
      useParams: () => ({ threadSlug: null }),
      useLocation: () => ({ pathname: "/workspace/test-workspace" }),
    }));

    const error404 = new Error("Not found") as any;
    error404.status = 404;

    (WorkspaceModel.threads.chatHistory as jest.Mock).mockRejectedValueOnce(
      error404
    );
    (System.keys as jest.Mock).mockResolvedValueOnce({});

    render(
      <MemoryRouter initialEntries={["/workspace/test-workspace"]}>
        <Routes>
          <Route
            path="/workspace/:slug"
            element={
              <WorkspaceChat loading={false} workspace={mockWorkspace} />
            }
          />
        </Routes>
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(mockNavigate).not.toHaveBeenCalled();
    });
  });

  it("should handle successful thread loading", async () => {
    const mockChatHistory = [
      {
        id: "1",
        user: "Test user message",
        response: "Test assistant response",
        createdAt: new Date().toISOString(),
      },
    ];

    (WorkspaceModel.threads.chatHistory as jest.Mock).mockResolvedValueOnce(
      mockChatHistory
    );
    (System.keys as jest.Mock).mockResolvedValueOnce({});

    render(
      <MemoryRouter
        initialEntries={["/workspace/test-workspace/t/test-thread"]}
      >
        <Routes>
          <Route
            path="/workspace/:slug/t/:threadSlug"
            element={
              <WorkspaceChat loading={false} workspace={mockWorkspace} />
            }
          />
        </Routes>
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(mockNavigate).not.toHaveBeenCalled();
      expect(WorkspaceModel.threads.chatHistory).toHaveBeenCalledWith(
        mockWorkspace.slug,
        "test-thread"
      );
    });
  });

  it("should navigate without logging on 404 redirect", async () => {
    const error404 = new Error("Thread not found") as any;
    error404.status = 404;

    (WorkspaceModel.threads.chatHistory as jest.Mock).mockRejectedValueOnce(
      error404
    );
    (System.keys as jest.Mock).mockResolvedValueOnce({});

    render(
      <MemoryRouter
        initialEntries={["/workspace/test-workspace/t/test-thread"]}
      >
        <Routes>
          <Route
            path="/workspace/:slug/t/:threadSlug"
            element={
              <WorkspaceChat loading={false} workspace={mockWorkspace} />
            }
          />
        </Routes>
      </MemoryRouter>
    );

    await waitFor(() => {
      // Verify navigation happened without logging
      expect(mockNavigate).toHaveBeenCalledWith("/workspace/test-workspace", {
        replace: true,
      });
    });
  });

  it("should handle null workspace gracefully", async () => {
    render(
      <MemoryRouter
        initialEntries={["/workspace/test-workspace/t/test-thread"]}
      >
        <Routes>
          <Route
            path="/workspace/:slug/t/:threadSlug"
            element={<WorkspaceChat loading={false} workspace={null} />}
          />
        </Routes>
      </MemoryRouter>
    );

    // Should not crash and not make API calls
    expect(WorkspaceModel.threads.chatHistory).not.toHaveBeenCalled();
    expect(mockNavigate).not.toHaveBeenCalled();
  });

  it("should handle loading state correctly", () => {
    render(
      <MemoryRouter
        initialEntries={["/workspace/test-workspace/t/test-thread"]}
      >
        <Routes>
          <Route
            path="/workspace/:slug/t/:threadSlug"
            element={<WorkspaceChat loading={true} workspace={mockWorkspace} />}
          />
        </Routes>
      </MemoryRouter>
    );

    // Should show loading component (LoadingChat)
    expect(WorkspaceModel.threads.chatHistory).not.toHaveBeenCalled();
  });

  it("should preserve query parameters on redirect", async () => {
    // Update mock to include query params
    jest.mock("react-router-dom", () => ({
      ...jest.requireActual("react-router-dom"),
      useNavigate: () => mockNavigate,
      useParams: () => ({ threadSlug: "test-thread" }),
      useLocation: () => ({
        pathname: "/workspace/test-workspace/t/test-thread",
        search: "?tab=documents",
      }),
    }));

    const error404 = new Error("Thread not found") as any;
    error404.status = 404;

    (WorkspaceModel.threads.chatHistory as jest.Mock).mockRejectedValueOnce(
      error404
    );
    (System.keys as jest.Mock).mockResolvedValueOnce({});

    render(
      <MemoryRouter
        initialEntries={[
          "/workspace/test-workspace/t/test-thread?tab=documents",
        ]}
      >
        <Routes>
          <Route
            path="/workspace/:slug/t/:threadSlug"
            element={
              <WorkspaceChat loading={false} workspace={mockWorkspace} />
            }
          />
        </Routes>
      </MemoryRouter>
    );

    await waitFor(() => {
      // Should redirect without query params (as per implementation)
      expect(mockNavigate).toHaveBeenCalledWith("/workspace/test-workspace", {
        replace: true,
      });
    });
  });
});
