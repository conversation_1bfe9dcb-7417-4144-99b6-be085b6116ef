/**
 * CDB Flow Progress Integration Test
 *
 * This test ensures that the CDB flow from AskLegalQuestion modal properly
 * initiates progress tracking and receives progress updates.
 *
 * It tests the specific issue where the progress tracker shows but no
 * progress updates are received from the server.
 */

// Test constants - define these first before any mocks
const MOCK_THREAD_SLUG = "thread-abc123";

// Mock legalTasksStore
jest.mock("@/stores/legalTasksStore", () => ({
  __esModule: true,
  default: {
    getState: () => ({
      fetchLegalTasks: jest.fn().mockResolvedValue({}),
      getUniqueCategories: jest.fn().mockReturnValue(["Contract Review"]),
      getLegalTasksByCategory: jest.fn().mockReturnValue([
        {
          subCategory: "Review Contract",
          legalPrompt: "Review this contract for legal issues",
          legalTaskType: "mainDoc",
          description: "Test legal task",
        },
      ]),
    }),
  },
}));

// Mock System module
jest.mock("@/models/system", () => ({
  __esModule: true,
  default: {
    localFiles: jest.fn().mockResolvedValue({
      items: [{ name: "contract.json", type: "file" }],
    }),
    fetchGroupedLegalTasks: jest.fn().mockResolvedValue({
      success: true,
      data: [
        {
          name: "Contract Review",
          subCategories: [
            {
              name: "Review Contract",
              legalPrompt: "Review this contract for legal issues",
              legalTaskType: "mainDoc",
              description: "Test legal task",
            },
          ],
        },
      ],
    }),
    fetchSubCategories: jest.fn().mockResolvedValue({
      success: true,
      data: [
        {
          name: "Review Contract",
          legalPrompt: "Review this contract for legal issues",
          legalTaskType: "mainDoc",
          description: "Test legal task",
        },
      ],
    }),
    purgeDocumentBuilder: jest.fn().mockResolvedValue({}),
    deleteDocuments: jest.fn().mockResolvedValue({}),
  },
}));

// Mock Workspace model - now MOCK_WORKSPACE is defined before this
jest.mock("@/models/workspace", () => ({
  __esModule: true,
  default: {
    bySlug: jest.fn().mockResolvedValue({
      id: "workspace-123",
      name: "Test Workspace",
      slug: "test-workspace",
    }),
    multiplexStream: jest.fn().mockImplementation(({ chatHandler }) => {
      // Simulate progress updates that should be received
      setTimeout(() => {
        chatHandler({
          type: "cdbProgress",
          step: 1,
          totalSteps: 9,
          flowType: "main",
        });
      }, 100);
      setTimeout(() => {
        chatHandler({
          type: "cdbProgress",
          step: 2,
          totalSteps: 9,
          flowType: "main",
        });
      }, 200);
      setTimeout(() => {
        chatHandler({
          type: "finalizeResponseStream",
          chatId: "chat-123",
          close: true,
        });
      }, 300);

      return Promise.resolve(new AbortController());
    }),
  },
}));

// Import testing utilities first
import { waitFor, act } from "@testing-library/react";
import { renderHook } from "@testing-library/react";
import "@testing-library/jest-dom";

// Import components and hooks after mocks are defined
import useProgressStore from "@/stores/progressStore";
import useThreadProgress from "@/hooks/useThreadProgress";
import WorkspaceModel from "@/models/workspace";

// Mock useUser hook
jest.mock("@/hooks/useUser", () => ({
  __esModule: true,
  default: () => ({
    user: { id: "user-123", role: "admin" },
  }),
}));

// Mock react-router-dom
jest.mock("react-router-dom", () => ({
  useParams: () => ({ slug: "test-workspace" }),
}));

// Mock translations
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        "performLegalTask.duration-info": "This process may take some time",
        "performLegalTask.select-category": "Select a category",
        "performLegalTask.noTaskfund": "No tasks found",
        "performLegalTask.settings-button": "Settings",
        "performLegalTask.flow-settings-button": "Flow Settings",
        "common.confirmstart": "Confirm and Start",
      };
      return translations[key] || key;
    },
  }),
}));

// Mock the modals that are imported
jest.mock("@/components/Modals/LegalTasksSettingsModal", () => ({
  __esModule: true,
  default: () => null,
}));

jest.mock("@/components/Modals/FlowSettingsModal", () => ({
  __esModule: true,
  default: () => null,
}));

describe("CDB Flow Progress Integration", () => {
  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();

    // Reset progress store state
    act(() => {
      useProgressStore.setState({ threads: new Map() });
    });

    // Update the multiplexStream mock implementation for each test
    (WorkspaceModel.multiplexStream as jest.Mock).mockImplementation(
      ({ chatHandler }) => {
        // Simulate the server sending progress updates
        setTimeout(() => {
          chatHandler({
            type: "cdbProgress",
            step: 1,
            totalSteps: 9,
            flowType: "main",
            message: "Starting document analysis...",
          });
        }, 50);

        setTimeout(() => {
          chatHandler({
            type: "cdbProgress",
            step: 2,
            totalSteps: 9,
            flowType: "main",
            message: "Processing legal sections...",
          });
        }, 100);

        setTimeout(() => {
          chatHandler({
            type: "finalizeResponseStream",
            chatId: "chat-123",
            close: true,
          });
        }, 150);

        return Promise.resolve(new AbortController());
      }
    );
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it("should properly initialize progress tracking when CDB flow starts", async () => {
    const { result: progressHook } = renderHook(() =>
      useThreadProgress(MOCK_THREAD_SLUG)
    );

    // Initially no progress should be active
    expect(progressHook.current.isActive).toBe(false);

    // Start a CDB process
    act(() => {
      progressHook.current.start(9, "main");
    });

    // Progress should now be active
    expect(progressHook.current.isActive).toBe(true);
    expect(progressHook.current.totalSteps).toBe(9);
    expect(progressHook.current.flowType).toBe("main");
    expect(progressHook.current.currentStep).toBe(1);
  });

  it("should receive and process cdbProgress updates correctly", async () => {
    const { result: progressHook } = renderHook(() =>
      useThreadProgress(MOCK_THREAD_SLUG)
    );

    // Start the process
    act(() => {
      progressHook.current.start(9, "main");
    });

    // Simulate progress updates like those sent from server
    act(() => {
      progressHook.current.update({
        step: 1,
        status: "in_progress",
        message: "Starting document analysis...",
      });
    });

    expect(progressHook.current.currentStep).toBe(1);
    expect(progressHook.current.stepMessage).toBe(
      "Starting document analysis..."
    );
    expect(progressHook.current.stepStatus).toBe("in_progress");

    // Update to next step
    act(() => {
      progressHook.current.update({
        step: 2,
        status: "in_progress",
        message: "Processing legal sections...",
      });
    });

    expect(progressHook.current.currentStep).toBe(2);
    expect(progressHook.current.stepMessage).toBe(
      "Processing legal sections..."
    );
  });

  it("should handle the complete CDB flow lifecycle from modal", async () => {
    // This test is simplified to focus on core progress tracking functionality
    // without complex modal interactions

    const { result: progressHook } = renderHook(() =>
      useThreadProgress(MOCK_THREAD_SLUG)
    );

    // Mock the sendCommand function to simulate what happens in practice
    const mockSendCommand = jest
      .fn()
      .mockImplementation(
        async (_command, _isLegalTask, _files, _attachments, options) => {
          // Simulate the progress updates that should come from the server
          const { chatHandler } = options;

          if (chatHandler) {
            // Start progress tracking
            progressHook.current.start(9, "main");

            // Simulate server response sequence
            setTimeout(() => {
              chatHandler({
                type: "cdbProgress",
                step: 1,
                totalSteps: 9,
                flowType: "main",
              });
              progressHook.current.update({
                step: 1,
                status: "in_progress",
                message: "Starting document analysis...",
              });
            }, 10);

            setTimeout(() => {
              chatHandler({
                type: "cdbProgress",
                step: 2,
                totalSteps: 9,
                flowType: "main",
              });
              progressHook.current.update({
                step: 2,
                status: "in_progress",
                message: "Processing legal sections...",
              });
            }, 20);

            setTimeout(() => {
              chatHandler({
                type: "finalizeResponseStream",
                chatId: "chat-123",
                close: true,
              });
              progressHook.current.finish();
            }, 30);
          }

          return Promise.resolve();
        }
      );

    // Simulate starting a CDB task
    await act(async () => {
      await mockSendCommand("Review Contract", true, [], [], {
        cdb: true,
        cdbOptions: [
          "Review this contract for legal issues",
          null,
          "contract.json",
          "mainDoc",
          null,
        ],
        preventNewChat: true,
        preventChatCreation: false,
        chatHandler: (result: any) => {
          // Handle progress updates
          if (result.type === "cdbProgress") {
            progressHook.current.update({
              step: result.step,
              status: "in_progress",
            });
          }
        },
      });
    });

    // Verify that progress tracking was initiated
    expect(mockSendCommand).toHaveBeenCalledTimes(1);

    // Wait for progress updates
    await waitFor(() => {
      expect(progressHook.current.isActive).toBe(true);
      expect(progressHook.current.currentStep).toBeGreaterThan(0);
    });

    // Wait for completion
    await waitFor(
      () => {
        expect(progressHook.current.isActive).toBe(false);
        expect(progressHook.current.isCompleted).toBe(true);
      },
      { timeout: 100 }
    );

    // Check the call arguments
    const callArgs = mockSendCommand.mock.calls[0];
    expect(callArgs[1]).toBe(true); // isLegalTask
    expect(callArgs[4].cdb).toBe(true); // CDB enabled
    expect(callArgs[4].cdbOptions).toEqual([
      "Review this contract for legal issues", // legalPrompt
      null, // customInstructions (null when no custom instructions provided)
      "contract.json", // mainDocName
      "mainDoc", // legalTaskType
      null, // referenceFiles
    ]);
  });

  it("should handle progress tracking edge cases", async () => {
    const { result: progressHook } = renderHook(() =>
      useThreadProgress(MOCK_THREAD_SLUG)
    );

    // Test case 1: Progress update before initialization should be ignored
    act(() => {
      progressHook.current.update({
        step: 1,
        status: "in_progress",
      });
    });

    expect(progressHook.current.isActive).toBe(false);

    // Test case 2: Initialize progress
    act(() => {
      progressHook.current.start(9, "main");
    });

    expect(progressHook.current.isActive).toBe(true);

    // Test case 3: Invalid progress update (no step number)
    act(() => {
      progressHook.current.update({
        status: "in_progress",
        message: "Processing...",
      } as any);
    });

    // Should still be at step 1
    expect(progressHook.current.currentStep).toBe(1);

    // Test case 4: Cancel progress
    act(() => {
      progressHook.current.cancel();
    });

    expect(progressHook.current.isActive).toBe(false);
    expect(progressHook.current.stepStatus).toBe("error");
  });

  it("should handle server connection issues gracefully", async () => {
    const { result: progressHook } = renderHook(() =>
      useThreadProgress(MOCK_THREAD_SLUG)
    );

    // Mock a failed multiplexStream call
    (WorkspaceModel.multiplexStream as jest.Mock).mockImplementation(() => {
      return Promise.reject(new Error("Server connection failed"));
    });

    // Mock sendCommand that would normally be called from the modal
    const mockSendCommand = jest
      .fn()
      .mockImplementation(
        async (_command, _isLegalTask, _files, _attachments, options) => {
          try {
            // This would normally call workspace.multiplexStream
            await WorkspaceModel.multiplexStream({
              workspaceSlug: "test-workspace",
              threadSlug: MOCK_THREAD_SLUG,
              prompt: _command,
              chatHandler: options.chatHandler,
              cdb: options.cdb,
              cdbOptions: options.cdbOptions,
            } as any);
          } catch {
            // Simulate error handling
            if (options.chatHandler) {
              options.chatHandler({
                type: "abort",
                error: "Server connection failed",
                close: true,
              });
            }
          }
        }
      );

    // Start progress tracking
    act(() => {
      progressHook.current.start(9, "main");
    });

    expect(progressHook.current.isActive).toBe(true);

    // Simulate the error scenario
    await act(async () => {
      await mockSendCommand("Test Task", true, [], [], {
        cdb: true,
        cdbOptions: [],
        chatHandler: (result: any) => {
          if (result.type === "abort" && result.error) {
            progressHook.current.setError(result.error);
          }
        },
      });
    });

    // Progress should be in error state
    expect(progressHook.current.isActive).toBe(false);
    expect(progressHook.current.stepStatus).toBe("error");
    expect(progressHook.current.error).toBe("Server connection failed");
  });

  it("should handle multiple concurrent CDB flows correctly", async () => {
    const THREAD_1 = "thread-1";
    const THREAD_2 = "thread-2";

    const { result: progress1 } = renderHook(() => useThreadProgress(THREAD_1));
    const { result: progress2 } = renderHook(() => useThreadProgress(THREAD_2));

    // Start two different CDB flows
    act(() => {
      progress1.current.start(9, "main");
      progress2.current.start(8, "referenceFiles");
    });

    // Both should be active with different configurations
    expect(progress1.current.isActive).toBe(true);
    expect(progress1.current.totalSteps).toBe(9);
    expect(progress1.current.flowType).toBe("main");

    expect(progress2.current.isActive).toBe(true);
    expect(progress2.current.totalSteps).toBe(8);
    expect(progress2.current.flowType).toBe("referenceFiles");

    // Update progress for each flow independently
    act(() => {
      progress1.current.update({
        step: 2,
        status: "in_progress",
        message: "Flow 1 step 2",
      });

      progress2.current.update({
        step: 3,
        status: "complete",
        message: "Flow 2 step 3",
      });
    });

    // Each flow should maintain its own state
    expect(progress1.current.currentStep).toBe(2);
    expect(progress1.current.stepStatus).toBe("in_progress");
    expect(progress1.current.stepMessage).toBe("Flow 1 step 2");

    expect(progress2.current.currentStep).toBe(3);
    expect(progress2.current.stepStatus).toBe("complete");
    expect(progress2.current.stepMessage).toBe("Flow 2 step 3");

    // Finish one flow
    act(() => {
      progress1.current.finish();
    });

    expect(progress1.current.isActive).toBe(false);
    expect(progress1.current.isCompleted).toBe(true);

    // The other flow should still be active
    expect(progress2.current.isActive).toBe(true);
    expect(progress2.current.isCompleted).toBe(false);
  });

  it("should properly clean up stale progress data", async () => {
    const { result: progressHook } = renderHook(() =>
      useThreadProgress(MOCK_THREAD_SLUG)
    );

    // Start a process
    act(() => {
      progressHook.current.start(9, "main");
    });

    expect(progressHook.current.isActive).toBe(true);

    // Clear stale progress (simulating what the modal does before starting a new process)
    act(() => {
      progressHook.current.clearStaleProgress();
    });

    // After clearing, a fresh state should be available but not active
    expect(progressHook.current.isActive).toBe(false);

    // Start a new process (this is what would happen after clearing stale progress)
    act(() => {
      progressHook.current.start(9, "main");
    });

    expect(progressHook.current.isActive).toBe(true);
    expect(progressHook.current.currentStep).toBe(1);
  });
});
