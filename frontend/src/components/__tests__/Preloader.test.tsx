/// <reference types="@testing-library/jest-dom" />
import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "@jest/globals";
import "@testing-library/jest-dom";
import PreLoader, { FullScreenLoader } from "../Preloader";

describe("PreLoader Component", () => {
  describe("Default PreLoader", () => {
    it("should render with default size", () => {
      const { container } = render(<PreLoader />);
      const preloaderDiv = container.firstChild as HTMLElement;

      expect(preloaderDiv).toBeInTheDocument();
      expect(preloaderDiv).toHaveClass("h-16", "w-16", "spin");
    });

    it("should render with custom size", () => {
      const { container } = render(<PreLoader size="24" />);
      const preloaderDiv = container.firstChild as HTMLElement;

      expect(preloaderDiv).toBeInTheDocument();
      expect(preloaderDiv).toHaveClass("h-24", "w-24", "spin");
    });

    it("should render with string size prop", () => {
      const { container } = render(<PreLoader size="32" />);
      const preloaderDiv = container.firstChild as HTMLElement;

      expect(preloaderDiv).toBeInTheDocument();
      expect(preloaderDiv).toHaveClass("h-32", "w-32", "spin");
    });

    it("should render with small size", () => {
      const { container } = render(<PreLoader size="8" />);
      const preloaderDiv = container.firstChild as HTMLElement;

      expect(preloaderDiv).toBeInTheDocument();
      expect(preloaderDiv).toHaveClass("h-8", "w-8", "spin");
    });

    it("should render with empty size parameter", () => {
      const { container } = render(<PreLoader size="" />);
      const preloaderDiv = container.firstChild as HTMLElement;

      expect(preloaderDiv).toBeInTheDocument();
      // Empty string should still create classes h- w- with empty value
      expect(preloaderDiv).toHaveClass("spin");
    });

    it("should be a div element with proper structure", () => {
      const { container } = render(<PreLoader />);
      const preloaderDiv = container.firstChild as HTMLElement;

      expect(preloaderDiv).toBeInstanceOf(HTMLDivElement);
      expect(preloaderDiv.tagName).toBe("DIV");
    });

    it("should have consistent className structure", () => {
      const { container } = render(<PreLoader size="20" />);
      const preloaderDiv = container.firstChild as HTMLElement;

      // Should have exactly the expected classes
      const expectedClasses = ["h-20", "w-20", "spin"];
      expectedClasses.forEach((className) => {
        expect(preloaderDiv).toHaveClass(className);
      });

      // Check the full className to ensure no extra classes
      expect(preloaderDiv.className).toBe("h-20 w-20 spin");
    });

    it("should work with large size values", () => {
      const { container } = render(<PreLoader size="96" />);
      const preloaderDiv = container.firstChild as HTMLElement;

      expect(preloaderDiv).toHaveClass("h-96", "w-96", "spin");
    });

    it("should handle numeric-like string sizes", () => {
      const { container } = render(<PreLoader size="12" />);
      const preloaderDiv = container.firstChild as HTMLElement;

      expect(preloaderDiv).toHaveClass("h-12", "w-12", "spin");
    });

    it("should render multiple instances independently", () => {
      const { container } = render(
        <>
          <PreLoader size="8" />
          <PreLoader size="12" />
          <PreLoader size="16" />
        </>
      );

      const preloaders = container.querySelectorAll("div");
      expect(preloaders).toHaveLength(3);

      expect(preloaders[0]).toHaveClass("h-8", "w-8", "spin");
      expect(preloaders[1]).toHaveClass("h-12", "w-12", "spin");
      expect(preloaders[2]).toHaveClass("h-16", "w-16", "spin");
    });
  });

  describe("FullScreenLoader", () => {
    it("should render with correct structure and styling", () => {
      render(<FullScreenLoader />);

      const fullScreenLoader = screen.getByTestId("preloader");

      expect(fullScreenLoader).toBeInTheDocument();
      expect(fullScreenLoader).toHaveAttribute("id", "preloader");
    });

    it("should have fullscreen styling classes", () => {
      render(<FullScreenLoader />);

      const fullScreenLoader = document.getElementById("preloader");

      expect(fullScreenLoader).toHaveClass(
        "fixed",
        "left-0",
        "top-0",
        "z-999999",
        "flex",
        "h-screen",
        "w-screen",
        "items-center",
        "justify-center"
      );
    });

    it("should contain a spinning element", () => {
      const { container } = render(<FullScreenLoader />);

      const spinElement = container.querySelector(".spin.large");
      expect(spinElement).toBeInTheDocument();
      expect(spinElement).toHaveClass("spin", "large");
    });

    it("should be positioned as a fixed overlay", () => {
      render(<FullScreenLoader />);

      const fullScreenLoader = document.getElementById("preloader");

      expect(fullScreenLoader).toHaveClass("fixed");
      expect(fullScreenLoader).toHaveClass("left-0");
      expect(fullScreenLoader).toHaveClass("top-0");
    });

    it("should have high z-index for overlay", () => {
      render(<FullScreenLoader />);

      const fullScreenLoader = document.getElementById("preloader");

      expect(fullScreenLoader).toHaveClass("z-999999");
    });

    it("should cover full viewport", () => {
      render(<FullScreenLoader />);

      const fullScreenLoader = document.getElementById("preloader");

      expect(fullScreenLoader).toHaveClass("h-screen");
      expect(fullScreenLoader).toHaveClass("w-screen");
    });

    it("should center content", () => {
      render(<FullScreenLoader />);

      const fullScreenLoader = document.getElementById("preloader");

      expect(fullScreenLoader).toHaveClass("flex");
      expect(fullScreenLoader).toHaveClass("items-center");
      expect(fullScreenLoader).toHaveClass("justify-center");
    });

    it("should render spinner with correct classes", () => {
      const { container } = render(<FullScreenLoader />);

      const spinnerDiv = container.querySelector(".spin.large");
      expect(spinnerDiv).toBeInTheDocument();
      expect(spinnerDiv).toHaveClass("spin");
      expect(spinnerDiv).toHaveClass("large");
    });

    it("should be a single div container with nested spinner", () => {
      const { container } = render(<FullScreenLoader />);

      const mainDiv = container.firstChild as HTMLElement;
      expect(mainDiv).toBeInstanceOf(HTMLDivElement);
      expect(mainDiv.tagName).toBe("DIV");

      const nestedDiv = mainDiv.firstChild as HTMLElement;
      expect(nestedDiv).toBeInstanceOf(HTMLDivElement);
      expect(nestedDiv).toHaveClass("spin", "large");
    });

    it("should maintain structure when rendered multiple times", () => {
      const { container: container1 } = render(<FullScreenLoader />);
      const { container: container2 } = render(<FullScreenLoader />);

      const loader1 = container1.querySelector("#preloader");
      const loader2 = container2.querySelector("#preloader");

      expect(loader1).toHaveClass("fixed", "flex");
      expect(loader2).toHaveClass("fixed", "flex");

      // Both should have the same structure
      expect(loader1?.firstChild).toHaveClass("spin", "large");
      expect(loader2?.firstChild).toHaveClass("spin", "large");
    });
  });

  describe("Component Integration", () => {
    it("should render both components together", () => {
      const { container } = render(
        <>
          <PreLoader size="8" />
          <FullScreenLoader />
        </>
      );

      // Should have the PreLoader div
      const preloader = container.querySelector(".h-8.w-8.spin");
      expect(preloader).toBeInTheDocument();

      // Should have the FullScreenLoader div
      const fullScreenLoader = container.querySelector("#preloader");
      expect(fullScreenLoader).toBeInTheDocument();
    });

    it("should handle edge cases gracefully", () => {
      // Test with undefined size (should use default)
      const { container } = render(<PreLoader size={undefined as any} />);
      const preloaderDiv = container.firstChild as HTMLElement;

      expect(preloaderDiv).toHaveClass("h-16", "w-16", "spin");
    });

    it("should work with special characters in size", () => {
      // This tests how the component handles unusual size values
      const { container } = render(<PreLoader size="px-4" />);
      const preloaderDiv = container.firstChild as HTMLElement;

      // Should still create classes even with unusual values
      expect(preloaderDiv).toHaveClass("spin");
      expect(preloaderDiv.className).toContain("h-px-4");
      expect(preloaderDiv.className).toContain("w-px-4");
    });

    it("should be accessible for screen readers", () => {
      render(<FullScreenLoader />);

      const fullScreenLoader = document.getElementById("preloader");

      // The component should be visible and identifiable
      expect(fullScreenLoader).toBeInTheDocument();
      expect(fullScreenLoader).toHaveAttribute("id", "preloader");
    });
  });
});
