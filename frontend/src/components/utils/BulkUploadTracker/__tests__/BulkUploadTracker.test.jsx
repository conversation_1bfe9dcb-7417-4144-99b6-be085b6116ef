import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import BulkUploadTracker from "../index";
import Workspace from "@/models/workspace";

// Mock dependencies
jest.mock("@/models/workspace");
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key, defaultValue, options) => {
      if (options && options.count !== undefined) {
        return defaultValue.replace("{{count}}", options.count);
      }
      return defaultValue || key;
    },
  }),
}));

// Mock components
jest.mock("@/components/Button", () => ({
  Button: ({ children, onClick, disabled, isLoading, ...props }) => (
    <button onClick={onClick} disabled={disabled} {...props}>
      {isLoading ? "Loading..." : children}
    </button>
  ),
}));

jest.mock("@/components/Preloader", () => ({
  __esModule: true,
  default: () => <div data-testid="preloader">Loading...</div>,
}));

describe("BulkUploadTracker", () => {
  const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
    workspace: { slug: "test-workspace" },
    jobId: "test-job-id",
    onComplete: jest.fn(),
    onCancel: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  describe("Component Rendering", () => {
    it("should not render when isOpen is false", () => {
      render(<BulkUploadTracker {...defaultProps} isOpen={false} />);
      expect(
        screen.queryByText("Bulk Upload Progress")
      ).not.toBeInTheDocument();
    });

    it("should render loading state initially", () => {
      Workspace.getBulkUploadJobStatus.mockResolvedValue(null);
      render(<BulkUploadTracker {...defaultProps} />);

      expect(screen.getByTestId("preloader")).toBeInTheDocument();
      expect(screen.getByText("Loading upload status...")).toBeInTheDocument();
    });

    it("should render job status when loaded", async () => {
      const mockJobStatus = {
        status: "processing",
        totalFiles: 100,
        processedFiles: 50,
        failedFiles: 0,
        errors: [],
      };

      Workspace.getBulkUploadJobStatus.mockResolvedValue(mockJobStatus);
      render(<BulkUploadTracker {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText("Processing files...")).toBeInTheDocument();
        expect(screen.getByText("50%")).toBeInTheDocument();
        expect(screen.getByText("50 / 100")).toBeInTheDocument();
      });
    });
  });

  describe("Status Display", () => {
    it("should display correct status text for each status", async () => {
      const statuses = [
        { status: "initializing", text: "Initializing upload..." },
        { status: "processing", text: "Processing files..." },
        { status: "completed", text: "Upload completed!" },
        { status: "failed", text: "Upload failed" },
        { status: "cancelled", text: "Upload cancelled" },
      ];

      for (const { status, text } of statuses) {
        Workspace.getBulkUploadJobStatus.mockResolvedValue({
          status,
          totalFiles: 10,
          processedFiles: 5,
          failedFiles: 0,
          errors: [],
        });

        const { rerender } = render(<BulkUploadTracker {...defaultProps} />);

        await waitFor(() => {
          expect(screen.getByText(text)).toBeInTheDocument();
        });

        rerender(<div />); // Clean up between iterations
      }
    });

    it("should display correct icon for each status", async () => {
      const mockJobStatus = {
        status: "completed",
        totalFiles: 10,
        processedFiles: 10,
        failedFiles: 0,
        errors: [],
      };

      Workspace.getBulkUploadJobStatus.mockResolvedValue(mockJobStatus);
      render(<BulkUploadTracker {...defaultProps} />);

      await waitFor(() => {
        // Check for the icon SVG element with the green color class
        const iconContainer = screen.getByText(
          "Bulk Upload Progress"
        ).parentElement;
        const icon = iconContainer.querySelector("svg.text-green-500");
        expect(icon).toBeInTheDocument();
      });
    });
  });

  describe("Progress Tracking", () => {
    it("should calculate and display progress percentage correctly", async () => {
      const mockJobStatus = {
        status: "processing",
        totalFiles: 200,
        processedFiles: 50,
        failedFiles: 0,
        errors: [],
      };

      Workspace.getBulkUploadJobStatus.mockResolvedValue(mockJobStatus);
      render(<BulkUploadTracker {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText("25%")).toBeInTheDocument();
      });
    });

    it("should display elapsed time correctly", async () => {
      Workspace.getBulkUploadJobStatus.mockResolvedValue({
        status: "processing",
        totalFiles: 10,
        processedFiles: 5,
        failedFiles: 0,
        errors: [],
      });

      render(<BulkUploadTracker {...defaultProps} />);

      // Wait for initial render
      await waitFor(() => {
        expect(screen.getByText("0:00")).toBeInTheDocument();
      });

      // Advance timer by 65 seconds
      jest.advanceTimersByTime(65000);

      await waitFor(() => {
        expect(screen.getByText("1:05")).toBeInTheDocument();
      });
    });
  });

  describe("Error Handling", () => {
    it("should display failed files section when there are errors", async () => {
      const mockJobStatus = {
        status: "processing",
        totalFiles: 10,
        processedFiles: 8,
        failedFiles: 2,
        errors: [
          { filename: "file1.pdf", error: "Invalid format" },
          { filename: "file2.pdf", error: "File too large" },
        ],
      };

      Workspace.getBulkUploadJobStatus.mockResolvedValue(mockJobStatus);
      render(<BulkUploadTracker {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText(/Failed Files/)).toBeInTheDocument();
        expect(screen.getByText(/\(2\)/)).toBeInTheDocument();
        expect(
          screen.getByText("1. file1.pdf: Invalid format")
        ).toBeInTheDocument();
        expect(
          screen.getByText("2. file2.pdf: File too large")
        ).toBeInTheDocument();
      });
    });

    it("should show truncated error list for more than 10 errors", async () => {
      const errors = Array.from({ length: 15 }, (_, i) => ({
        filename: `file${i + 1}.pdf`,
        error: `Error ${i + 1}`,
      }));

      const mockJobStatus = {
        status: "processing",
        totalFiles: 20,
        processedFiles: 5,
        failedFiles: 15,
        errors,
      };

      Workspace.getBulkUploadJobStatus.mockResolvedValue(mockJobStatus);
      render(<BulkUploadTracker {...defaultProps} />);

      await waitFor(() => {
        // Should show first 10 errors
        expect(
          screen.getByText("10. file10.pdf: Error 10")
        ).toBeInTheDocument();
        expect(
          screen.queryByText("11. file11.pdf: Error 11")
        ).not.toBeInTheDocument();

        // Should show "and X more" message
        expect(screen.getByText("...and 5 more errors")).toBeInTheDocument();
      });
    });

    it("should handle API errors gracefully", async () => {
      Workspace.getBulkUploadJobStatus.mockResolvedValue({
        error: "Network error",
      });

      render(<BulkUploadTracker {...defaultProps} />);

      // Wait for the component to process the error and update state
      await waitFor(() => {
        // Verify the API was called at least once
        expect(Workspace.getBulkUploadJobStatus).toHaveBeenCalled();
      });

      // Wait for any state updates to complete
      await waitFor(() => {
        // The component should continue to show loading state since no valid status was received
        expect(screen.getByTestId("preloader")).toBeInTheDocument();
      });
    });
  });

  describe("Polling Behavior", () => {
    it("should poll for job status every 2 seconds", async () => {
      const mockJobStatus = {
        status: "processing",
        totalFiles: 10,
        processedFiles: 0,
        failedFiles: 0,
        errors: [],
      };

      Workspace.getBulkUploadJobStatus.mockResolvedValue(mockJobStatus);
      render(<BulkUploadTracker {...defaultProps} />);

      // Initial call
      expect(Workspace.getBulkUploadJobStatus).toHaveBeenCalledTimes(1);

      // Advance by 2 seconds
      jest.advanceTimersByTime(2000);
      await waitFor(() => {
        expect(Workspace.getBulkUploadJobStatus).toHaveBeenCalledTimes(2);
      });

      // Advance by another 2 seconds
      jest.advanceTimersByTime(2000);
      await waitFor(() => {
        expect(Workspace.getBulkUploadJobStatus).toHaveBeenCalledTimes(3);
      });
    });

    it("should stop polling when job is completed", async () => {
      Workspace.getBulkUploadJobStatus
        .mockResolvedValueOnce({
          status: "processing",
          totalFiles: 10,
          processedFiles: 5,
          failedFiles: 0,
          errors: [],
        })
        .mockResolvedValueOnce({
          status: "completed",
          totalFiles: 10,
          processedFiles: 10,
          failedFiles: 0,
          errors: [],
        });

      render(<BulkUploadTracker {...defaultProps} />);

      // Wait for initial render
      await waitFor(() => {
        expect(Workspace.getBulkUploadJobStatus).toHaveBeenCalledTimes(1);
      });

      // Advance to trigger second poll
      jest.advanceTimersByTime(2000);
      await waitFor(() => {
        expect(Workspace.getBulkUploadJobStatus).toHaveBeenCalledTimes(2);
        expect(screen.getByText("Upload completed!")).toBeInTheDocument();
      });

      // Advance more time to ensure no additional polling happens
      jest.advanceTimersByTime(4000);

      // Should still only have been called twice since polling stops on completion
      expect(Workspace.getBulkUploadJobStatus).toHaveBeenCalledTimes(2);
    });

    it("should call onComplete when job completes successfully", async () => {
      Workspace.getBulkUploadJobStatus
        .mockResolvedValueOnce({
          status: "processing",
          totalFiles: 10,
          processedFiles: 5,
          failedFiles: 0,
          errors: [],
        })
        .mockResolvedValueOnce({
          status: "completed",
          totalFiles: 10,
          processedFiles: 10,
          failedFiles: 0,
          errors: [],
        });

      render(<BulkUploadTracker {...defaultProps} />);

      // Wait for the initial call
      await waitFor(() => {
        expect(Workspace.getBulkUploadJobStatus).toHaveBeenCalledTimes(1);
      });

      // Advance time to trigger the second poll
      jest.advanceTimersByTime(2000);

      // Wait for completion callback
      await waitFor(() => {
        expect(defaultProps.onComplete).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe("User Interactions", () => {
    it("should call onClose when close button is clicked", async () => {
      const mockJobStatus = {
        status: "completed",
        totalFiles: 10,
        processedFiles: 10,
        failedFiles: 0,
        errors: [],
      };

      Workspace.getBulkUploadJobStatus.mockResolvedValue(mockJobStatus);
      render(<BulkUploadTracker {...defaultProps} />);

      await waitFor(() => {
        const closeButton = screen.getByRole("button", { name: "Close" });
        fireEvent.click(closeButton);
        expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
      });
    });

    it("should handle cancel action correctly", async () => {
      const mockJobStatus = {
        status: "processing",
        totalFiles: 10,
        processedFiles: 5,
        failedFiles: 0,
        errors: [],
      };

      Workspace.getBulkUploadJobStatus.mockResolvedValue(mockJobStatus);
      Workspace.cancelBulkUploadJob.mockResolvedValue({ success: true });

      render(<BulkUploadTracker {...defaultProps} />);

      await waitFor(() => {
        const cancelButton = screen.getByRole("button", {
          name: "Cancel Upload",
        });
        fireEvent.click(cancelButton);
      });

      await waitFor(() => {
        expect(Workspace.cancelBulkUploadJob).toHaveBeenCalledWith(
          "test-workspace",
          "test-job-id"
        );
        expect(defaultProps.onCancel).toHaveBeenCalledTimes(1);
        expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
      });
    });

    it("should disable cancel button while cancelling", async () => {
      const mockJobStatus = {
        status: "processing",
        totalFiles: 10,
        processedFiles: 5,
        failedFiles: 0,
        errors: [],
      };

      Workspace.getBulkUploadJobStatus.mockResolvedValue(mockJobStatus);
      Workspace.cancelBulkUploadJob.mockImplementation(
        () =>
          new Promise((resolve) =>
            setTimeout(() => resolve({ success: true }), 100)
          )
      );

      render(<BulkUploadTracker {...defaultProps} />);

      await waitFor(() => {
        const cancelButton = screen.getByRole("button", {
          name: "Cancel Upload",
        });
        fireEvent.click(cancelButton);
      });

      // Button should show loading state
      expect(screen.getByRole("button", { name: "Loading..." })).toBeDisabled();
    });

    it("should not allow closing modal while processing", async () => {
      const mockJobStatus = {
        status: "processing",
        totalFiles: 10,
        processedFiles: 5,
        failedFiles: 0,
        errors: [],
      };

      Workspace.getBulkUploadJobStatus.mockResolvedValue(mockJobStatus);
      render(<BulkUploadTracker {...defaultProps} />);

      await waitFor(() => {
        // Find the X button in the header by looking for the button with X icon
        const buttons = screen.getAllByRole("button");
        const closeButton = buttons.find((btn) => {
          // The close button is in the header and contains an X icon (SVG)
          return (
            btn.querySelector("svg") &&
            btn.parentElement.className.includes("border-b")
          );
        });
        expect(closeButton).toBeDisabled();
      });
    });
  });

  describe("Edge Cases", () => {
    it("should handle missing jobId gracefully", () => {
      render(<BulkUploadTracker {...defaultProps} jobId={null} />);
      expect(Workspace.getBulkUploadJobStatus).not.toHaveBeenCalled();
    });

    it("should handle missing workspace gracefully", () => {
      render(<BulkUploadTracker {...defaultProps} workspace={null} />);
      expect(Workspace.getBulkUploadJobStatus).not.toHaveBeenCalled();
    });

    it("should handle zero total files", async () => {
      const mockJobStatus = {
        status: "processing",
        totalFiles: 0,
        processedFiles: 0,
        failedFiles: 0,
        errors: [],
      };

      Workspace.getBulkUploadJobStatus.mockResolvedValue(mockJobStatus);
      render(<BulkUploadTracker {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText("0%")).toBeInTheDocument();
      });
    });
  });
});
