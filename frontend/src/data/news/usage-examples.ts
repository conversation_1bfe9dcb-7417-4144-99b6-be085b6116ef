/* eslint-disable no-console */
/**
 * Usage Examples for System News Async Initialization
 *
 * This file demonstrates the correct patterns for using the news system
 * to avoid race conditions with the async initialization.
 *
 * Note: Console statements are intentionally used in this file for
 * demonstration purposes.
 */

import {
  systemNewsItems,
  systemNewsInitialized,
  getActiveSystemNews,
  getSystemNewsForRoles,
  getSystemNewsById,
  reinitializeSystemNews,
  clearSystemNewsCache,
  refreshSystemNewsCache,
  getSystemNewsCacheStatus,
  setSystemNewsCacheTTL,
  validateSystemNewsCache,
} from "./index";

/**
 * PATTERN 1: PREFERRED - Use async functions (no race conditions)
 * These functions always fetch fresh data and handle caching internally.
 */
export const examplePreferredPattern = async () => {
  // ✅ GOOD: Always safe to use
  const activeNews = await getActiveSystemNews();
  const adminNews = await getSystemNewsForRoles(["admin"]);
  const specificNews = await getSystemNewsById("system-welcome-2024");

  // Example output: activeNews contains all currently active system news items
  console.log("Active news:", activeNews);
  // Example output: adminNews contains news items specifically for admin role
  console.log("Admin news:", adminNews);
  // Example output: specificNews contains the specific news item by ID
  console.log("Specific news:", specificNews);
};

/**
 * PATTERN 2: If you need the synchronous systemNewsItems array
 * Wait for initialization before accessing the synchronous array.
 */
export const exampleSynchronousPattern = async () => {
  // ✅ GOOD: Wait for initialization first
  await systemNewsInitialized();

  // Now safe to use the synchronous array
  console.log("System news count:", systemNewsItems.length);
  console.log("First news item:", systemNewsItems[0]);

  // You can now use systemNewsItems synchronously
  const highPriorityNews = systemNewsItems.filter(
    (item) => item.priority === "high"
  );
  console.log("High priority news:", highPriorityNews);
  // Example: highPriorityNews contains all news items with priority === "high"
};

/**
 * PATTERN 3: AVOID - Direct synchronous access (race condition)
 * This pattern can cause race conditions and should be avoided.
 */
export const exampleAvoidPattern = () => {
  // ❌ BAD: May be empty if accessed too early
  console.log("System news count:", systemNewsItems.length); // May be 0
  // ❌ BAD: May not have the expected data
  // This is the race condition we're fixing!
};

/**
 * PATTERN 4: Reinitializing for testing or cache refresh
 * Use this when you need to force a refresh of the news data.
 * The function uses atomic array replacement to prevent race conditions.
 */
export const exampleReinitializePattern = async () => {
  // ✅ GOOD: Force refresh of news data
  await reinitializeSystemNews();

  // Now systemNewsItems has fresh data
  console.log("Fresh news count:", systemNewsItems.length);

  // ✅ SAFE: Multiple concurrent calls are safe due to atomic replacement
  const promises = [
    reinitializeSystemNews(),
    reinitializeSystemNews(),
    reinitializeSystemNews(),
  ];
  await Promise.all(promises);

  // systemNewsItems will be in a consistent state
  console.log("After concurrent reinits:", systemNewsItems.length);
};

/**
 * PATTERN 5: Cache management and monitoring
 * Use these functions to manage and monitor the cache behavior.
 */
export const exampleCacheManagement = async () => {
  // ✅ GOOD: Check cache status
  const status = getSystemNewsCacheStatus();
  console.log("Cache status:", status);
  console.log("Cache expired:", status.expired);
  console.log("Time remaining:", status.remainingTtl, "ms");
  // Example: status contains { cached: boolean, expired: boolean, timestamp: number, ttl: number, remainingTtl: number }

  // ✅ GOOD: Manually clear cache when needed
  clearSystemNewsCache();
  console.log("Cache cleared");

  // ✅ GOOD: Force refresh cache and get fresh data
  const freshData = await refreshSystemNewsCache();
  console.log("Fresh data count:", freshData.length);
  // Example: freshData contains an array of all system news items after refresh

  // ✅ GOOD: Configure cache TTL for different scenarios
  setSystemNewsCacheTTL(10 * 60 * 1000); // 10 minutes
  console.log("Cache TTL set to 10 minutes");

  // ✅ GOOD: Validate cache integrity
  const validation = validateSystemNewsCache(true);
  if (!validation.valid) {
    console.log("Cache issues found:", validation.issues);
    console.log("Issues fixed:", validation.fixed);
  }
};

/**
 * PATTERN 6: Component usage with React hooks
 * Example of how to use this in React components.
 */
export const exampleReactPatternCode = `
// This would be inside a React component
import { useState, useEffect } from 'react';
import { getActiveSystemNews, systemNewsInitialized, systemNewsItems } from '@/data/news';

function NewsComponent() {
  const [news, setNews] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadNews = async () => {
      try {
        // ✅ GOOD: Use async functions
        const activeNews = await getActiveSystemNews();
        setNews(activeNews);
      } catch (error) {
        // Failed to load news: error
        console.error("Failed to load news:", error);
        setNews([]);
      } finally {
        setLoading(false);
      }
    };

    loadNews();
  }, []);

  // Alternative: If you need the synchronous array
  useEffect(() => {
    const loadSynchronousNews = async () => {
      try {
        // ✅ GOOD: Wait for initialization
        await systemNewsInitialized();
        setNews([...systemNewsItems]); // Copy the array
      } catch (error) {
        // Failed to load news: error
        console.error("Failed to load news:", error);
        setNews([]);
      } finally {
        setLoading(false);
      }
    };

    loadSynchronousNews();
  }, []);

  return { news, loading };
}
`;

/**
 * PATTERN 7: Error handling
 * Always handle potential errors when working with async operations.
 */
export const exampleErrorHandling = async () => {
  try {
    // ✅ GOOD: Proper error handling
    const news = await getActiveSystemNews();
    return news;
  } catch (error) {
    console.log("Failed to fetch news:", error);
    // Return fallback data or empty array
    return [];
  }
};

/**
 * Summary of Best Practices:
 *
 * 1. PREFER async functions (getActiveSystemNews, getSystemNewsForRoles, etc.)
 * 2. If you need systemNewsItems array, await systemNewsInitialized() first
 * 3. AVOID direct synchronous access to systemNewsItems without waiting
 * 4. Use reinitializeSystemNews() for testing or forced refresh
 * 5. Use cache management functions to monitor and control cache behavior
 * 6. Always handle errors in async operations
 * 7. In React components, use useEffect with async functions (see exampleReactPatternCode)
 * 8. Concurrent calls to reinitializeSystemNews() are safe due to atomic array replacement
 * 9. Cache automatically expires after TTL and can be configured as needed
 */
