import { renderHook, waitFor, act } from "@testing-library/react";
import { useInitialData } from "../useInitialData";
import useSystemSettingsStore from "@/stores/settingsStore";
import useWorkspaceStore from "@/stores/workspaceStore";

// Mock dependencies
jest.mock("@/stores/settingsStore", () => ({
  __esModule: true,
  default: jest.fn(),
}));
jest.mock("@/stores/workspaceStore", () => ({
  __esModule: true,
  default: jest.fn(),
}));
jest.mock("@/utils/request", () => ({
  userFromStorage: jest.fn(() => ({ id: 1 })),
  baseHeaders: jest.fn(() => ({ "Content-Type": "application/json" })),
}));
jest.mock("@/utils/constants", () => ({
  API_BASE: "http://localhost:3001",
}));

describe("useInitialData hook", () => {
  let mockSetMultipleSettings;
  let mockSetTabNames;
  let mockSetPopulatedWorkspaces;
  let mockInitializeSetting;
  let consoleErrorSpy;
  let originalConsoleError;

  beforeAll(() => {
    // Store the original console.error function
    originalConsoleError = console.error;

    // Suppress expected console errors in tests using jest.spyOn
    consoleErrorSpy = jest
      .spyOn(console, "error")
      .mockImplementation((...args) => {
        // Check if this is the error we want to suppress
        const firstArg = args[0];
        const message =
          typeof firstArg === "string"
            ? firstArg
            : firstArg?.toString?.() || "";

        if (message.includes("Error fetching initial data:")) {
          return; // Suppress this error
        }

        // Call the original console.error for other messages
        originalConsoleError.apply(console, args);
      });
  });

  afterAll(() => {
    if (consoleErrorSpy) {
      consoleErrorSpy.mockRestore();
    }
  });

  beforeEach(() => {
    jest.clearAllMocks();

    // Clear localStorage between tests
    localStorage.clear();

    // Mock store functions
    mockSetMultipleSettings = jest.fn();
    mockSetTabNames = jest.fn();
    mockSetPopulatedWorkspaces = jest.fn();
    mockInitializeSetting = jest.fn();

    // Mock useSystemSettingsStore
    useSystemSettingsStore.mockImplementation((selector) => {
      const state = {
        setMultipleSettings: mockSetMultipleSettings,
        setTabNames: mockSetTabNames,
        initializeSetting: mockInitializeSetting,
      };
      return selector ? selector(state) : state;
    });

    // Mock getState for direct calls
    useSystemSettingsStore.getState = jest.fn(() => ({
      setMultipleSettings: mockSetMultipleSettings,
      setTabNames: mockSetTabNames,
      initializeSetting: mockInitializeSetting,
    }));

    // Mock useWorkspaceStore
    useWorkspaceStore.mockImplementation((selector) => {
      const state = {
        setPopulatedWorkspaces: mockSetPopulatedWorkspaces,
      };
      return selector ? selector(state) : state;
    });

    // Mock getState for direct calls
    useWorkspaceStore.getState = jest.fn(() => ({
      setPopulatedWorkspaces: mockSetPopulatedWorkspaces,
    }));

    // Mock global fetch
    global.fetch = jest.fn();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it("should initialize with loading state", () => {
    const { result } = renderHook(() => useInitialData());
    expect(result.current.loading).toBe(true);
    expect(result.current.error).toBeNull();
  });

  it("should fetch and set initial data successfully", async () => {
    const mockData = {
      settings: {
        MultiUserMode: true,
        language: "en",
        palette: "dark",
        appName: "TestApp",
        tabNames: {
          tabName1: "Q&A",
          tabName2: "Drafting",
          tabName3: "Tab3",
        },
      },
      workspaces: [{ id: 1, name: "Test Workspace", slug: "test-workspace" }],
      user: { id: 1, username: "testuser" },
    };

    // Create a promise that we can resolve manually
    let resolveJsonPromise;
    const jsonPromise = new Promise((resolve) => {
      resolveJsonPromise = resolve;
    });

    global.fetch.mockResolvedValueOnce({
      ok: true,
      json: () => jsonPromise,
    });

    const { result } = renderHook(() => useInitialData());

    // Initially loading should be true
    expect(result.current.loading).toBe(true);

    // Resolve the JSON promise
    await act(async () => {
      resolveJsonPromise({ success: true, data: mockData });
      // Wait for the promise to be processed
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    // Now check the results
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeNull();

    // Verify store updates
    expect(mockInitializeSetting).toHaveBeenCalledWith("color-palette", "dark");
    expect(mockSetTabNames).toHaveBeenCalledWith({
      tabName1: "Q&A",
      tabName2: "Drafting",
      tabName3: "Tab3",
    });
    expect(mockSetPopulatedWorkspaces).toHaveBeenCalledWith(
      mockData.workspaces
    );
    expect(mockSetMultipleSettings).toHaveBeenCalledWith(mockData.settings);
  });

  it("should handle errors gracefully", async () => {
    global.fetch.mockResolvedValueOnce({
      ok: false,
      status: 500,
    });

    const { result } = renderHook(() => useInitialData());

    // Wait for the effect to run
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe("Failed to fetch initial data: 500");

    // Stores should not be updated on error
    expect(mockInitializeSetting).not.toHaveBeenCalled();
    expect(mockSetTabNames).not.toHaveBeenCalled();
    expect(mockSetPopulatedWorkspaces).not.toHaveBeenCalled();
  });

  it("should handle API error responses", async () => {
    // Create a promise that we can resolve manually
    let resolveJsonPromise;
    const jsonPromise = new Promise((resolve) => {
      resolveJsonPromise = resolve;
    });

    global.fetch.mockResolvedValueOnce({
      ok: true,
      json: () => jsonPromise,
    });

    const { result } = renderHook(() => useInitialData());

    // Resolve the JSON promise with an error
    await act(async () => {
      resolveJsonPromise({ success: false, error: "API Error" });
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe("API Error");

    // Stores should not be updated on error
    expect(mockInitializeSetting).not.toHaveBeenCalled();
    expect(mockSetTabNames).not.toHaveBeenCalled();
  });

  it("should handle partial data correctly", async () => {
    const mockData = {
      settings: {
        MultiUserMode: false,
        language: "en",
        // Missing palette and tabNames
      },
      workspaces: [],
      user: null,
    };

    // Mock fetch with immediate resolution
    global.fetch = jest.fn().mockResolvedValue({
      ok: true,
      json: jest.fn().mockResolvedValue({ success: true, data: mockData }),
    });

    const { result, rerender } = renderHook(() => useInitialData());

    // Initial state should be loading
    expect(result.current.loading).toBe(true);
    expect(result.current.error).toBe(null);

    // Wait for state updates with act
    await act(async () => {
      // Let all promises resolve
      await new Promise((resolve) => setTimeout(resolve, 100));
    });

    // Force a rerender to get latest state
    rerender();

    // Now check the final state
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);

    // Should still update stores with available data
    expect(mockInitializeSetting).not.toHaveBeenCalled(); // No palette
    expect(mockSetTabNames).not.toHaveBeenCalled(); // No tabNames
    expect(mockSetPopulatedWorkspaces).toHaveBeenCalledWith([]); // Empty workspaces
    expect(mockSetMultipleSettings).toHaveBeenCalledWith(mockData.settings);

    // Language should be stored in localStorage
    expect(localStorage.setItem).toHaveBeenCalledWith("language", "en");
  }, 10000); // Increase timeout to 10 seconds

  it("should only fetch data once on mount", async () => {
    global.fetch.mockResolvedValue({
      ok: true,
      json: async () => ({
        success: true,
        data: { settings: {}, workspaces: [], user: null },
      }),
    });

    const { rerender } = renderHook(() => useInitialData());

    // Wait for initial fetch to complete
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(1);
    });

    // Rerender should not trigger another fetch
    rerender();

    expect(global.fetch).toHaveBeenCalledTimes(1);
  });

  it("should handle network errors", async () => {
    // Mock fetch to reject with network error
    global.fetch = jest.fn().mockRejectedValue(new Error("Network error"));

    const { result, rerender } = renderHook(() => useInitialData());

    // Initial state should be loading
    expect(result.current.loading).toBe(true);
    expect(result.current.error).toBe(null);

    // Wait for state updates with act
    await act(async () => {
      // Let all promises resolve/reject
      await new Promise((resolve) => setTimeout(resolve, 100));
    });

    // Force a rerender to get latest state
    rerender();

    // Now check the final state
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe("Network error");

    // Stores should not be updated on error
    expect(mockInitializeSetting).not.toHaveBeenCalled();
    expect(mockSetTabNames).not.toHaveBeenCalled();
  }, 10000); // Increase timeout to 10 seconds
});
