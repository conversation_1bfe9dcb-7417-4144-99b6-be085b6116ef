import { act, renderHook } from "@testing-library/react";
import useThreadProgress, { useIsProcessActive } from "../useThreadProgress";
import useProgressStore from "@/stores/progressStore";

// Mock thread IDs for tests
const MOCK_THREAD_ID: string = "thread-123";
const MOCK_THREAD_ID_2: string = "thread-456";

describe("useThreadProgress", () => {
  beforeEach(() => {
    // Reset the store state completely before each test
    act(() => {
      useProgressStore.setState({ threads: new Map() });
    });

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe("hook initialization", () => {
    it("should return default values for non-existent thread", () => {
      const { result } = renderHook(() => useThreadProgress("non-existent"));

      expect(result.current.isActive).toBe(false);
      expect(result.current.currentStep).toBe(1);
      expect(result.current.totalSteps).toBe(7);
      expect(result.current.startTime).toBeNull();
      expect(result.current.flowType).toBeNull();
      expect(result.current.currentSubStep).toBeNull();
      expect(result.current.stepStatus).toBe("pending");
      expect(result.current.stepMessage).toBeNull();
      expect(result.current.stepDetails).toEqual([]);
      expect(result.current.error).toBeNull();
    });

    it("should return default values for empty thread slug", () => {
      const { result } = renderHook(() => useThreadProgress(""));

      expect(result.current.isActive).toBe(false);
      expect(result.current.currentStep).toBe(1);
    });

    it("should return default values for null thread slug", () => {
      const { result } = renderHook(() => useThreadProgress(null));

      expect(result.current.isActive).toBe(false);
      expect(result.current.currentStep).toBe(1);
    });

    it("should trim whitespace from thread slug", () => {
      // Start a process with a clean thread ID
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      // Use hook with whitespace around the same thread ID
      const { result } = renderHook(() =>
        useThreadProgress(`  ${MOCK_THREAD_ID}  `)
      );

      expect(result.current.isActive).toBe(true);
    });
  });

  describe("state reflection", () => {
    it("should reflect active thread state", () => {
      // Start a process
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID, 5, "cdb");
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      expect(result.current.isActive).toBe(true);
      expect(result.current.currentStep).toBe(1);
      expect(result.current.totalSteps).toBe(5);
      expect(result.current.flowType).toBe("cdb");
      expect(result.current.startTime).toBeGreaterThan(0);
    });

    it("should update when progress changes", () => {
      // Start a process
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      expect(result.current.currentStep).toBe(1);
      expect(result.current.stepStatus).toBe("pending");

      // Update progress
      act(() => {
        useProgressStore.getState().updateProgress(
          {
            step: 3,
            status: "in_progress",
            message: "Processing step 3",
          },
          MOCK_THREAD_ID
        );
      });

      expect(result.current.currentStep).toBe(3);
      expect(result.current.stepStatus).toBe("in_progress");
      expect(result.current.stepMessage).toBe("Processing step 3");
    });

    it("should reflect error state", () => {
      // Start a process
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      expect(result.current.error).toBeNull();
      expect(result.current.isActive).toBe(true);

      // Set error
      act(() => {
        useProgressStore.getState().setError(MOCK_THREAD_ID, "Test error");
      });

      expect(result.current.error).toBe("Test error");
      expect(result.current.isActive).toBe(false);
    });
  });

  describe("action methods", () => {
    it("should start a process", () => {
      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      act(() => {
        result.current.start(5, "cdb");
      });

      const storeState = useProgressStore.getState();
      const threadState = storeState.threads.get(MOCK_THREAD_ID);

      expect(threadState).toBeDefined();
      expect(threadState?.isActive).toBe(true);
      expect(threadState?.totalSteps).toBe(5);
      expect(threadState?.flowType).toBe("cdb");
    });

    it("should not start process with empty thread slug", () => {
      const { result } = renderHook(() => useThreadProgress(""));

      act(() => {
        result.current.start();
      });

      const storeState = useProgressStore.getState();
      expect(storeState.threads.size).toBe(0);
    });

    it("should update progress", () => {
      // Start a process first
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      act(() => {
        result.current.update({
          step: 2,
          status: "in_progress",
          message: "Step 2",
        });
      });

      expect(result.current.currentStep).toBe(2);
      expect(result.current.stepStatus).toBe("in_progress");
      expect(result.current.stepMessage).toBe("Step 2");
    });

    it("should finish a process", () => {
      // Start a process first
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      expect(result.current.isActive).toBe(true);

      act(() => {
        result.current.finish();
      });

      // After finishing, thread should still exist but marked as completed and inactive
      const storeState = useProgressStore.getState();
      expect(storeState.threads.has(MOCK_THREAD_ID)).toBe(true);

      const threadState = storeState.threads.get(MOCK_THREAD_ID);
      expect(threadState?.isActive).toBe(false);
      expect(threadState?.isCompleted).toBe(true);
      expect(threadState?.stepStatus).toBe("complete");
    });

    it("should cancel a process", () => {
      // Start a process first
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      const threadState = useProgressStore
        .getState()
        .threads.get(MOCK_THREAD_ID);
      const controller = threadState?.abortController;
      const abortSpy = controller ? jest.spyOn(controller, "abort") : null;

      act(() => {
        result.current.cancel();
      });

      if (abortSpy) {
        expect(abortSpy).toHaveBeenCalled();
      }
      expect(result.current.isActive).toBe(false);
      expect(result.current.stepStatus).toBe("error");
    });

    it("should set error", () => {
      // Start a process first
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      act(() => {
        result.current.setError("Test error message");
      });

      expect(result.current.error).toBe("Test error message");
      expect(result.current.isActive).toBe(false);
      expect(result.current.stepStatus).toBe("error");
    });

    it("should clear error", () => {
      // Start a process and set error first
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
        useProgressStore.getState().setError(MOCK_THREAD_ID, "Test error");
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      expect(result.current.error).toBe("Test error");

      act(() => {
        result.current.clearError();
      });

      const storeState = useProgressStore.getState();
      expect(storeState.threads.has(MOCK_THREAD_ID)).toBe(false);
    });

    it("should force cleanup", () => {
      // Start a process first
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      expect(result.current.isActive).toBe(true);

      act(() => {
        result.current.forceCleanup();
      });

      const storeState = useProgressStore.getState();
      expect(storeState.threads.has(MOCK_THREAD_ID)).toBe(false);
    });

    it("should clear stale progress", () => {
      // Start a process first
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      expect(result.current.isActive).toBe(true);

      act(() => {
        result.current.clearStaleProgress();
      });

      // After clearing stale progress, thread should exist but be reset to default state
      const storeState = useProgressStore.getState();
      expect(storeState.threads.has(MOCK_THREAD_ID)).toBe(true);

      const threadState = storeState.threads.get(MOCK_THREAD_ID);
      expect(threadState?.isActive).toBe(false);
      expect(threadState?.currentStep).toBe(1);
      expect(threadState?.stepStatus).toBe("pending");
    });

    it("should not perform clear stale progress with empty thread slug", () => {
      const { result } = renderHook(() => useThreadProgress(""));

      act(() => {
        result.current.clearStaleProgress();
      });

      const storeState = useProgressStore.getState();
      expect(storeState.threads.size).toBe(0);
    });
  });

  describe("abort signal handling", () => {
    it("should get abort signal for active process", () => {
      // Start a process first
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      const signal = result.current.getAbortSignal();
      expect(signal).toBeInstanceOf(AbortSignal);
      expect(signal?.aborted).toBe(false);
    });

    it("should return null for non-existent thread", () => {
      const { result } = renderHook(() => useThreadProgress("non-existent"));

      const signal = result.current.getAbortSignal();
      expect(signal).toBeNull();
    });

    it("should return null for empty thread slug", () => {
      const { result } = renderHook(() => useThreadProgress(""));

      const signal = result.current.getAbortSignal();
      expect(signal).toBeNull();
    });

    it("should reflect aborted signal after cancellation", () => {
      // Start a process first
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      const signalBefore = result.current.getAbortSignal();
      expect(signalBefore?.aborted).toBe(false);

      act(() => {
        result.current.cancel();
      });

      const signalAfter = result.current.getAbortSignal();
      expect(signalAfter?.aborted).toBe(true);
    });
  });

  describe("multiple threads independence", () => {
    it("should handle multiple independent threads", () => {
      const { result: result1 } = renderHook(() =>
        useThreadProgress(MOCK_THREAD_ID)
      );
      const { result: result2 } = renderHook(() =>
        useThreadProgress(MOCK_THREAD_ID_2)
      );

      // Start different processes
      act(() => {
        result1.current.start(5, "flow1");
        result2.current.start(8, "flow2");
      });

      expect(result1.current.isActive).toBe(true);
      expect(result1.current.totalSteps).toBe(5);
      expect(result1.current.flowType).toBe("flow1");

      expect(result2.current.isActive).toBe(true);
      expect(result2.current.totalSteps).toBe(8);
      expect(result2.current.flowType).toBe("flow2");

      // Update first thread
      act(() => {
        result1.current.update({ step: 3, status: "in_progress" });
      });

      expect(result1.current.currentStep).toBe(3);
      expect(result2.current.currentStep).toBe(1); // Should not affect second thread

      // Finish first thread
      act(() => {
        result1.current.finish();
      });

      expect(result1.current.isActive).toBe(false);
      expect(result2.current.isActive).toBe(true); // Second thread should still be active
    });
  });

  describe("step details and sub-steps handling", () => {
    it("should track step details correctly", () => {
      // Start a process
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID, 5);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      // Initially no step details
      expect(result.current.stepDetails).toEqual([]);

      // Update with step details
      act(() => {
        result.current.update({
          step: 1,
          status: "in_progress",
          message: "Processing step 1",
        });
      });

      expect(result.current.stepDetails).toHaveLength(1);
      expect(result.current.stepDetails[0]).toMatchObject({
        step: 1,
        status: "in_progress",
        message: "Processing step 1",
      });
    });

    it("should handle sub-steps correctly", () => {
      // Start a process
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      // Update with sub-step
      act(() => {
        result.current.update({
          step: 1,
          subStep: 2,
          status: "in_progress",
          message: "Sub-step 2 processing",
          label: "Sub-step label",
        });
      });

      expect(result.current.currentSubStep).toBe(2);
      expect(result.current.stepDetails).toHaveLength(1);
      expect(result.current.stepDetails[0].subTasks).toHaveLength(1);
      expect(result.current.stepDetails[0].subTasks?.[0]).toMatchObject({
        subStep: 2,
        status: "in_progress",
        message: "Sub-step 2 processing",
        label: "Sub-step label",
      });
    });

    it("should handle completion time correctly", () => {
      // Start a process
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      expect(result.current.completionTime).toBeNull();
      expect(result.current.isCompleted).toBe(false);

      // Finish the process
      act(() => {
        result.current.finish();
      });

      expect(result.current.isCompleted).toBe(true);
      expect(result.current.completionTime).toBeGreaterThan(0);
    });
  });

  describe("progress event validation", () => {
    it("should handle progress event with total", () => {
      // Start a process
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      // Update with total
      act(() => {
        result.current.update({
          step: 1,
          status: "in_progress",
          total: 10,
        });
      });

      expect(result.current.stepDetails).toHaveLength(1);
      expect(result.current.stepDetails[0].expectedTotal).toBe(10);
    });

    it("should handle progress event with progress value", () => {
      // Start a process
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      // Update with progress
      act(() => {
        result.current.update({
          step: 1,
          subStep: 1,
          progress: 50,
          message: "Half way done",
        });
      });

      expect(result.current.currentSubStep).toBe(1);
      expect(result.current.stepDetails).toHaveLength(1);
      expect(result.current.stepDetails[0].subTasks).toHaveLength(1);
    });

    it("should handle error in progress event", () => {
      // Start a process
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      // Update with error
      act(() => {
        result.current.update({
          step: 1,
          error: "Something went wrong",
          status: "error",
        });
      });

      expect(result.current.stepStatus).toBe("error");
    });
  });

  describe("edge cases", () => {
    it("should handle action calls with no valid thread slug", () => {
      const { result } = renderHook(() => useThreadProgress(""));

      // These should not throw errors
      act(() => {
        result.current.start();
        result.current.update({ step: 1 });
        result.current.finish();
        result.current.cancel();
        result.current.setError("error");
        result.current.clearError();
        result.current.forceCleanup();
        result.current.clearStaleProgress();
      });

      const storeState = useProgressStore.getState();
      expect(storeState.threads.size).toBe(0);
    });

    it("should handle action calls on non-existent thread", () => {
      const { result } = renderHook(() => useThreadProgress("non-existent"));

      // These should not throw errors
      act(() => {
        result.current.update({ step: 1 });
        result.current.finish();
        result.current.cancel();
        result.current.setError("error");
        result.current.clearError();
        result.current.forceCleanup();
        result.current.clearStaleProgress();
      });

      // No errors should occur
      expect(result.current.isActive).toBe(false);
    });
  });
});

describe("useIsProcessActive", () => {
  beforeEach(() => {
    // Reset the store state completely before each test
    act(() => {
      useProgressStore.setState({ threads: new Map() });
    });

    // Clear all mocks
    jest.clearAllMocks();
  });

  it("should return false for non-existent thread", () => {
    const { result } = renderHook(() => useIsProcessActive("non-existent"));
    expect(result.current).toBe(false);
  });

  it("should return false for empty thread slug", () => {
    const { result } = renderHook(() => useIsProcessActive(""));
    expect(result.current).toBe(false);
  });

  it("should return false for null thread slug", () => {
    const { result } = renderHook(() => useIsProcessActive(null));
    expect(result.current).toBe(false);
  });

  it("should return true for active process", () => {
    // Start a process
    act(() => {
      useProgressStore.getState().startProcess(MOCK_THREAD_ID);
    });

    const { result } = renderHook(() => useIsProcessActive(MOCK_THREAD_ID));
    expect(result.current).toBe(true);
  });

  it("should return false for inactive process", () => {
    // Start and then cancel a process
    act(() => {
      useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      useProgressStore.getState().cancelProcess(MOCK_THREAD_ID);
    });

    const { result } = renderHook(() => useIsProcessActive(MOCK_THREAD_ID));
    expect(result.current).toBe(false);
  });

  it("should update when process state changes", () => {
    const { result } = renderHook(() => useIsProcessActive(MOCK_THREAD_ID));

    expect(result.current).toBe(false);

    // Start process
    act(() => {
      useProgressStore.getState().startProcess(MOCK_THREAD_ID);
    });

    expect(result.current).toBe(true);

    // Cancel process
    act(() => {
      useProgressStore.getState().cancelProcess(MOCK_THREAD_ID);
    });

    expect(result.current).toBe(false);
  });

  it("should trim whitespace from thread slug", () => {
    // Start a process with a clean thread ID
    act(() => {
      useProgressStore.getState().startProcess(MOCK_THREAD_ID);
    });

    // Use hook with whitespace around the same thread ID
    const { result } = renderHook(() =>
      useIsProcessActive(`  ${MOCK_THREAD_ID}  `)
    );

    expect(result.current).toBe(true);
  });
});

describe("stress tests and boundary conditions", () => {
  beforeEach(() => {
    // Reset the store state completely before each test
    act(() => {
      useProgressStore.setState({ threads: new Map() });
    });

    // Clear all mocks
    jest.clearAllMocks();
  });

  it("should handle rapid state changes", () => {
    const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

    // Rapidly start, update, and manipulate state
    act(() => {
      result.current.start(10, "rapid-test");
      result.current.update({ step: 1, status: "in_progress" });
      result.current.update({ step: 2, status: "complete" });
      result.current.update({ step: 3, status: "in_progress", subStep: 1 });
      result.current.update({ step: 3, subStep: 2, status: "complete" });
      result.current.setError("test error");
      result.current.clearError();
    });

    // Final state should reflect the cleanup
    const storeState = useProgressStore.getState();
    expect(storeState.threads.has(MOCK_THREAD_ID)).toBe(false);
  });

  it("should handle undefined values gracefully", () => {
    const { result } = renderHook(() => useThreadProgress(undefined));

    // All methods should work without errors
    act(() => {
      result.current.start();
      result.current.update({ step: 1 });
      result.current.finish();
      result.current.cancel();
      result.current.setError("error");
      result.current.clearError();
      result.current.forceCleanup();
      result.current.clearStaleProgress();
    });

    expect(result.current.isActive).toBe(false);
    expect(result.current.getAbortSignal()).toBeNull();
  });

  it("should handle extreme values for totalSteps", () => {
    const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

    // Test with very large number
    act(() => {
      result.current.start(1000000, "large-steps");
    });

    expect(result.current.totalSteps).toBe(1000000);
    expect(result.current.flowType).toBe("large-steps");

    // Test with zero (should be normalized to at least 1 by the store)
    act(() => {
      result.current.start(0, "zero-steps");
    });

    expect(result.current.totalSteps).toBe(1); // Store normalizes to 1
  });

  it("should handle concurrent updates to the same step", () => {
    act(() => {
      useProgressStore.getState().startProcess(MOCK_THREAD_ID);
    });

    const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

    // Multiple updates to the same step with different properties
    act(() => {
      result.current.update({
        step: 1,
        status: "in_progress",
        message: "First message",
      });
      result.current.update({ step: 1, status: "complete", total: 5 });
      result.current.update({ step: 1, subStep: 1, label: "Sub-step 1" });
      result.current.update({
        step: 1,
        subStep: 2,
        label: "Sub-step 2",
        progress: 100,
      });
    });

    const stepDetails = result.current.stepDetails;
    expect(stepDetails).toHaveLength(1);
    expect(stepDetails[0].step).toBe(1);
    expect(stepDetails[0].status).toBe("complete");
    expect(stepDetails[0].expectedTotal).toBe(5);
    expect(stepDetails[0].subTasks).toHaveLength(2);
  });

  it("should handle memory cleanup for completed processes", () => {
    const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

    act(() => {
      result.current.start();
      result.current.finish();
    });

    expect(result.current.isCompleted).toBe(true);
    expect(result.current.isActive).toBe(false);

    // The store automatically cleans up completed processes after 5 seconds
    // We can't easily test the timeout, but we can verify the state is correct
    const storeState = useProgressStore.getState();
    expect(storeState.threads.has(MOCK_THREAD_ID)).toBe(true);

    const threadState = storeState.threads.get(MOCK_THREAD_ID);
    expect(threadState?.isCompleted).toBe(true);
    expect(threadState?.completionTime).toBeGreaterThan(0);
  });

  it("should maintain consistent function signatures and work correctly on rerender", () => {
    const { result, rerender } = renderHook(() =>
      useThreadProgress(MOCK_THREAD_ID)
    );

    // Functions should maintain their types and be callable
    expect(typeof result.current.start).toBe("function");
    expect(typeof result.current.update).toBe("function");
    expect(typeof result.current.finish).toBe("function");
    expect(typeof result.current.cancel).toBe("function");
    expect(typeof result.current.setError).toBe("function");
    expect(typeof result.current.clearError).toBe("function");
    expect(typeof result.current.forceCleanup).toBe("function");
    expect(typeof result.current.clearStaleProgress).toBe("function");
    expect(typeof result.current.getAbortSignal).toBe("function");

    // Start a process to test state consistency across rerenders
    act(() => {
      result.current.start(3, "rerender-test");
    });

    const activeStateBefore = result.current.isActive;
    const totalStepsBefore = result.current.totalSteps;
    const flowTypeBefore = result.current.flowType;

    // Rerender without changes
    rerender();

    // State should remain consistent after rerender
    expect(result.current.isActive).toBe(activeStateBefore);
    expect(result.current.totalSteps).toBe(totalStepsBefore);
    expect(result.current.flowType).toBe(flowTypeBefore);

    // Functions should still work after rerender
    act(() => {
      result.current.update({ step: 1, status: "in_progress" });
    });

    expect(result.current.currentStep).toBe(1);
    expect(result.current.stepStatus).toBe("in_progress");
  });

  it("should handle WebSocket-like real-time updates simulation", () => {
    const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

    // Simulate a WebSocket session with rapid updates
    act(() => {
      result.current.start(5, "websocket-simulation");
    });

    // Simulate receiving multiple WebSocket messages
    act(() => {
      // Step 1 starts
      result.current.update({
        step: 1,
        status: "starting",
        message: "Initializing...",
      });
      result.current.update({
        step: 1,
        status: "in_progress",
        message: "Processing...",
      });

      // Sub-steps within step 1
      result.current.update({
        step: 1,
        subStep: 1,
        status: "in_progress",
        label: "Loading data",
      });
      result.current.update({
        step: 1,
        subStep: 1,
        status: "complete",
        progress: 100,
      });
      result.current.update({
        step: 1,
        subStep: 2,
        status: "in_progress",
        label: "Validating",
      });
      result.current.update({
        step: 1,
        subStep: 2,
        status: "complete",
        progress: 100,
      });

      // Complete step 1 and move to step 2
      result.current.update({ step: 1, status: "complete" });
      result.current.update({
        step: 2,
        status: "in_progress",
        message: "Next phase...",
      });
    });

    expect(result.current.currentStep).toBe(2);
    expect(result.current.stepStatus).toBe("in_progress");
    expect(result.current.stepMessage).toBe("Next phase...");
    expect(result.current.stepDetails).toHaveLength(2);

    // Check step 1 has completed sub-tasks
    const step1Details = result.current.stepDetails.find((s) => s.step === 1);
    expect(step1Details?.subTasks).toHaveLength(2);
    expect(
      step1Details?.subTasks?.every((st) => st.status === "complete")
    ).toBe(true);
  });

  it("should handle error recovery scenarios", () => {
    const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

    act(() => {
      result.current.start(3, "error-recovery");
    });

    // Simulate error and recovery
    act(() => {
      result.current.update({ step: 1, status: "in_progress" });
      result.current.update({
        step: 1,
        status: "error",
        error: "Network timeout",
      });
      result.current.setError("Process failed");
    });

    expect(result.current.isActive).toBe(false);
    expect(result.current.stepStatus).toBe("error");
    expect(result.current.error).toBe("Process failed");

    // Simulate restart after error
    act(() => {
      result.current.clearError();
    });

    // After clearing error, thread should be removed
    expect(useProgressStore.getState().threads.has(MOCK_THREAD_ID)).toBe(false);

    // Can start fresh after error recovery
    act(() => {
      result.current.start(3, "retry");
    });

    expect(result.current.isActive).toBe(true);
    expect(result.current.error).toBeNull();
    expect(result.current.flowType).toBe("retry");
  });
});
