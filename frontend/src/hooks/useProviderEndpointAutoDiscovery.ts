import { useEffect, useState, ChangeEvent, useCallback } from "react";
import System from "@/models/system";
import showToast from "@/utils/toast";
import { useTranslation } from "react-i18next";

interface UseProviderEndpointAutoDiscoveryProps {
  provider: string | null;
  initialBasePath?: string;
  ENDPOINTS?: string[];
  moduleSuffix?: string;
}

interface EndpointResult {
  endpoint: string;
  models: any[];
}

interface UseProviderEndpointAutoDiscoveryReturn {
  autoDetecting: boolean;
  autoDetectAttempted: boolean;
  showAdvancedControls: boolean;
  setShowAdvancedControls: (value: boolean) => void;
  basePath: {
    value: string;
    set: (value: string) => void;
    onChange: (e: ChangeEvent<HTMLInputElement>) => void;
    onBlur: () => void;
  };
  basePathValue: {
    value: string;
    set: (value: string) => void;
  };
  handleAutoDetectClick: (e: React.MouseEvent) => void;
  runAutoDetect: (isInitialAttempt?: boolean) => Promise<void>;
}

export default function useProviderEndpointAutoDiscovery({
  provider = null,
  initialBasePath = "",
  ENDPOINTS = [],
  moduleSuffix = "",
}: UseProviderEndpointAutoDiscoveryProps): UseProviderEndpointAutoDiscoveryReturn {
  const { t } = useTranslation();
  const [loading, setLoading] = useState<boolean>(false);
  const [basePath, setBasePath] = useState<string>(initialBasePath);
  const [basePathValue, setBasePathValue] = useState<string>(initialBasePath);
  const [autoDetectAttempted, setAutoDetectAttempted] =
    useState<boolean>(false);
  const [showAdvancedControls, setShowAdvancedControls] =
    useState<boolean>(true);

  const autoDetect = useCallback(
    async (_isInitialAttempt: boolean = false): Promise<void> => {
      setLoading(true);
      setAutoDetectAttempted(true);
      const possibleEndpoints: Promise<EndpointResult>[] = [];
      ENDPOINTS.forEach((endpoint) => {
        possibleEndpoints.push(
          new Promise<EndpointResult>((resolve, reject) => {
            System.customModels(provider || "", null, endpoint, 2_000)
              .then((results) => {
                if (!results?.models || results.models.length === 0)
                  throw new Error("No models");
                resolve({ endpoint, models: results.models });
              })
              .catch(() => {
                reject(`${provider} @ ${endpoint} did not resolve.`);
              });
          })
        );
      });

      const { endpoint, models } = await Promise.any(possibleEndpoints)
        .then((resolved) => resolved)
        .catch(() => {
          // All endpoints failed to resolve
          return { endpoint: null, models: null };
        });

      if (models !== null) {
        setBasePath(endpoint);
        setBasePathValue(endpoint);

        // Persist the discovered base path to the environment
        if (provider && endpoint) {
          // Convert provider name to proper case (e.g., "lmstudio" -> "LMStudio")
          const providerKey =
            provider === "lmstudio"
              ? "LMStudio"
              : provider.charAt(0).toUpperCase() + provider.slice(1);
          const basePathKey = `${providerKey}BasePath${moduleSuffix}`;
          System.updateSystem({
            [basePathKey]: endpoint,
          }).catch((error) => {
            console.error(
              `Error saving auto-detected ${provider} base path:`,
              error
            );
          });
        }

        setLoading(false);
        showToast(t("show-toast.provider-endpoint-discovered"), "success", {
          clear: true,
        });
        setShowAdvancedControls(false);
        return;
      }

      setLoading(false);
      setShowAdvancedControls(true);
      showToast(t("show-toast.provider-endpoint-discovery-failed"), "info", {
        clear: true,
      });
    },
    [ENDPOINTS, provider, t]
  );

  const handleAutoDetectClick = useCallback(
    (e: React.MouseEvent): void => {
      e.preventDefault();
      autoDetect();
    },
    [autoDetect]
  );

  const handleBasePathChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>): void => {
      const value = e.target.value;
      setBasePathValue(value);
    },
    []
  );

  const handleBasePathBlur = useCallback(() => {
    setBasePath(basePathValue);

    // Persist the base path to the environment
    if (provider && basePathValue) {
      // Convert provider name to proper case (e.g., "lmstudio" -> "LMStudio")
      const providerKey =
        provider === "lmstudio"
          ? "LMStudio"
          : provider.charAt(0).toUpperCase() + provider.slice(1);
      const basePathKey = `${providerKey}BasePath${moduleSuffix}`;
      System.updateSystem({
        [basePathKey]: basePathValue,
      }).catch((error) => {
        console.error(`Error saving ${provider} base path:`, error);
      });
    }
  }, [basePathValue, provider, moduleSuffix]);

  useEffect(() => {
    if (!initialBasePath && !autoDetectAttempted) autoDetect(true);
  }, [initialBasePath, autoDetectAttempted, autoDetect]);

  return {
    autoDetecting: loading,
    autoDetectAttempted,
    showAdvancedControls,
    setShowAdvancedControls,
    basePath: {
      value: basePath,
      set: setBasePathValue,
      onChange: handleBasePathChange,
      onBlur: handleBasePathBlur,
    },
    basePathValue: {
      value: basePathValue,
      set: setBasePathValue,
    },
    handleAutoDetectClick,
    runAutoDetect: autoDetect,
  };
}
