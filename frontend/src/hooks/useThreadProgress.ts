import useProgressStore from "@/stores/progressStore";
import type { StepDetail } from "@/utils/progressUtils";

interface ThreadProgressEvent {
  step?: number;
  subStep?: number | null;
  status?: "pending" | "starting" | "in_progress" | "complete" | "error";
  message?: string | null;
  label?: string | null;
  total?: number;
  progress?: number;
  error?: any;
}

export interface UseThreadProgressReturn {
  isActive: boolean;
  currentStep: number;
  totalSteps: number;
  startTime: number | null;
  flowType: string | null;
  currentSubStep: number | null;
  stepStatus: string;
  stepMessage: string | null;
  stepDetails: StepDetail[];
  error: string | null;
  isCompleted: boolean;
  completionTime: number | null;
  start: (totalSteps?: number, flowType?: string | null) => void;
  update: (event: ThreadProgressEvent) => void;
  finish: () => void;
  cancel: () => void;
  setError: (errorMessage: string) => void;
  clearError: () => void;
  forceCleanup: () => void;
  clearStaleProgress: () => void;
  getAbortSignal: () => AbortSignal | null;
}

export default function useThreadProgress(
  threadSlug: string | null | undefined
): UseThreadProgressReturn {
  const safeThreadSlug =
    typeof threadSlug === "string" && threadSlug.trim()
      ? threadSlug.trim()
      : "";

  // Use multiple specific selectors to ensure React detects all changes
  const isActive = useProgressStore((state) =>
    safeThreadSlug
      ? state.threads.get(safeThreadSlug)?.isActive || false
      : false
  );
  const isCompleted = useProgressStore((state) =>
    safeThreadSlug
      ? state.threads.get(safeThreadSlug)?.isCompleted || false
      : false
  );
  const currentStep = useProgressStore((state) =>
    safeThreadSlug ? state.threads.get(safeThreadSlug)?.currentStep || 1 : 1
  );
  const totalSteps = useProgressStore((state) =>
    safeThreadSlug ? state.threads.get(safeThreadSlug)?.totalSteps || 7 : 7
  );
  const stepStatus = useProgressStore((state) =>
    safeThreadSlug
      ? state.threads.get(safeThreadSlug)?.stepStatus || "pending"
      : "pending"
  );
  const threadState = useProgressStore((state) =>
    safeThreadSlug ? state.threads.get(safeThreadSlug) : null
  );

  const start = (
    totalSteps: number = 7,
    flowType: string | null = null
  ): void => {
    if (!safeThreadSlug) return;
    useProgressStore
      .getState()
      .startProcess(safeThreadSlug, totalSteps, flowType);
  };

  const update = (event: ThreadProgressEvent): void => {
    if (!safeThreadSlug) return;
    useProgressStore.getState().updateProgress(event as any, safeThreadSlug);
  };

  const finish = (): void => {
    if (!safeThreadSlug) return;
    useProgressStore.getState().finishProcess(safeThreadSlug);
  };

  const cancel = (): void => {
    if (!safeThreadSlug) return;
    useProgressStore.getState().cancelProcess(safeThreadSlug);
  };

  const setError = (errorMessage: string): void => {
    if (!safeThreadSlug) return;
    useProgressStore.getState().setError(safeThreadSlug, errorMessage);
  };

  const clearError = (): void => {
    if (!safeThreadSlug) return;
    useProgressStore.getState().clearError(safeThreadSlug);
  };

  const forceCleanup = (): void => {
    if (!safeThreadSlug) return;
    useProgressStore.getState().forceCleanup(safeThreadSlug);
  };

  const clearStaleProgress = (): void => {
    if (!safeThreadSlug) return;
    useProgressStore.getState().clearStaleProgress(safeThreadSlug);
  };

  const getAbortSignal = (): AbortSignal | null => {
    if (!safeThreadSlug) return null;
    const controller = useProgressStore
      .getState()
      .getAbortController(safeThreadSlug);
    return controller?.signal || null;
  };

  return {
    isActive,
    currentStep,
    totalSteps,
    startTime: threadState?.startTime || null,
    flowType: threadState?.flowType || null,
    currentSubStep: threadState?.currentSubStep || null,
    stepStatus,
    stepMessage: threadState?.stepMessage || null,
    stepDetails: (threadState?.stepDetails || []) as unknown as StepDetail[],
    error: threadState?.error || null,
    isCompleted,
    completionTime: threadState?.completionTime || null,

    start,
    update,
    finish,
    cancel,
    setError,
    clearError,
    forceCleanup,
    clearStaleProgress,
    getAbortSignal,
  };
}

// Clean selector hook to check if a process is active for a specific thread
export function useIsProcessActive(
  threadSlug: string | null | undefined
): boolean {
  const safeThreadSlug =
    typeof threadSlug === "string" && threadSlug.trim()
      ? threadSlug.trim()
      : "";

  return useProgressStore((state) => {
    if (!safeThreadSlug) return false;
    const threadState = state.threads.get(safeThreadSlug);
    return threadState?.isActive || false;
  });
}
