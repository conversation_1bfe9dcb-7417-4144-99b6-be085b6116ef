import { useContext } from "react";
import { AuthContext } from "@/AuthContext";
import type { User, AuthContextType } from "@/types";

interface UseUserReturn {
  user: User | null;
  authToken: string | null;
  setUser: (user: User) => void;
}

export default function useUser(): UseUserReturn {
  const context = useContext(AuthContext);

  if (!context) {
    throw new Error("useUser must be used within AuthContext");
  }

  const typedContext = context as AuthContextType;

  return {
    ...typedContext.store,
    setUser: (user: User) => {
      // Update user while preserving the existing auth token
      typedContext.actions.updateUser(user, typedContext.store.authToken || "");
    },
  };
}
