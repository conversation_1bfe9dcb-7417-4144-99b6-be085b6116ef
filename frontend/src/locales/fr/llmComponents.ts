export default {
  // =========================
  // GENERIC PROVIDER SELECTION SETTINGS
  // =========================
  generic: {
    "base-url": "URL de base",
    "api-key": "Clé API",
    "api-key-placeholder": "Entrez votre clé API",
    "chat-model": "Nom du modèle de chat",
    "chat-model-placeholder": "ID du modèle utilisé pour les requêtes de chat",
    "token-window": "Fenêtre de tokens",
    "token-window-placeholder": "Limite de tokens (ex : 4096)",
    "max-tokens": "Tokens maximum",
    "max-tokens-placeholder": "Tokens maximum par requête (ex : 1024)",
    "embedding-deployment": "Nom du déploiement d'incorporation",
    "embedding-deployment-placeholder":
      "Nom du déploiement du modèle d'incorporation Azure OpenAI",
    "embedding-model": "Modèle d'incorporation",
    "embedding-model-placeholder": "Entrez le modèle d'incorporation",
    "max-embedding-chunk-length": "Longueur maximale du chunk d'incorporation",
    saving: "Sauvegarde...",
    "save-changes": "Sauvegarder les modifications",
    "workspace-update-error": "Erreur : {{error}}",
    "base-url-placeholder": "ex: https://proxy.openai.com",
    "password-mask-length": "12",
  },

  // =========================
  // GENERIC MODEL SELECTION SETTINGS
  // =========================
  model: {
    selection: "Sélection du modèle de chat",
    "embedding-selection": "Sélection du modèle d'incorporation",
    "enter-api-key":
      "Entrez une clé API valide pour afficher tous les modèles disponibles pour votre compte.",
    "enter-url": "Entrez d'abord l'URL",
    "your-models": "Vos modèles chargés",
    "available-models": "Modèles disponibles",
  },

  // =========================
  // ANTHROPIC
  // =========================
  anthropic: {
    "api-key": "Clé API Anthropic",
    "api-key-placeholder": "Entrez votre clé API Anthropic",
    "model-selection": "Sélection du modèle de chat",
  },

  // =========================
  // AZURE SETTINGS
  // =========================
  azure: {
    "service-endpoint": "Point de terminaison Azure",
    "service-endpoint-placeholder": "https://mon-azure.openai.azure.com",
    "api-key": "Clé API",
    "api-key-placeholder": "Clé API Azure OpenAI",
    "chat-deployment": "Nom du déploiement de chat",
    "chat-deployment-placeholder":
      "Nom du déploiement du modèle de chat Azure OpenAI",
    "token-limit": "Limite de tokens pour le modèle de chat",
  },
  azureai: {
    "service-endpoint": "Point de terminaison du service Azure",
    "api-key": "Clé API",
    "api-key-placeholder": "Clé API Azure OpenAI",
    "embedding-deployment-name": "Nom du déploiement d'incorporation",
    "embedding-deployment-name-placeholder":
      "Entrez le nom du déploiement d'incorporation",
  },

  // =========================
  // AWS BEDROCK SETTINGS
  // =========================
  bedrock: {
    "iam-warning":
      "Vous devez utiliser un utilisateur IAM correctement défini pour l'inférence.",
    "read-more":
      "En savoir plus sur l'utilisation d'AWS Bedrock dans cette instance",
    "access-id": "ID d'accès IAM AWS Bedrock",
    "access-id-placeholder": "ID d'accès utilisateur IAM AWS Bedrock",
    "access-key": "Clé d'accès IAM AWS Bedrock",
    "access-key-placeholder": "Clé d'accès utilisateur IAM AWS Bedrock",
    region: "Région AWS",
    "model-id": "ID du modèle",
    "model-id-placeholder": "ID du modèle AWS ex: meta.llama3.1-v0.1",
    "context-window": "Fenêtre de contexte du modèle",
    "context-window-placeholder": "Limite de fenêtre de contenu (ex: 4096)",
  },

  // =========================
  // COHERE
  // =========================
  cohere: {
    "api-key": "Clé API Cohere",
    "api-key-placeholder": "Entrez votre clé API Cohere",
    "model-preference": "Préférence de modèle",
    "model-selection": "Sélection de modèle",
  },

  // =========================
  // DEEPSEEK LLM SETTINGS
  // =========================
  deepseek: {
    "api-key": "Clé API DeepSeek",
    "api-key-placeholder": "Entrez votre clé API DeepSeek",
    "model-selection": "Sélection du modèle de chat",
    "loading-models": "-- chargement des modèles disponibles --",
  },

  // =========================
  // FIREWORKSAI LLM SELECTION
  // =========================
  fireworksai: {
    "api-key": "Clé API FireworksAI",
    "api-key-placeholder": "Entrez votre clé API FireworksAI",
    "model-selection": "Sélection du modèle de chat",
    "loading-models": "-- chargement des modèles disponibles --",
  },

  // =========================
  // GEMINI LLM OPTIONS
  // =========================
  gemini: {
    "api-key": "Clé API Google AI",
    "api-key-placeholder": "Clé API Google Gemini",
    "model-selection": "Sélection du modèle",
    "loading-models": "Chargement des modèles...",
    "manual-options": "Options manuelles",
    "safety-setting": "Paramètres de sécurité",
    experimental: "Expérimental",
    stable: "Stable",
    "safety-options": {
      none: "Aucun (par défaut)",
      "block-few": "Bloquer uniquement le haut risque",
      "block-some": "Bloquer les risques moyens et élevés",
      "block-most": "Bloquer la plupart du contenu",
    },
  },

  // =========================
  // GROQ LLM SELECTION
  // =========================
  groq: {
    "api-key": "Clé API Groq",
    "api-key-placeholder": "Entrez votre clé API Groq",
    "model-selection": "Sélection du modèle de chat",
    "loading-models": "-- chargement des modèles disponibles --",
    "enter-api-key":
      "Entrez une clé API valide pour afficher tous les modèles disponibles pour votre compte.",
    "available-models": "Modèles disponibles",
    "model-description":
      "Sélectionnez le modèle GroqAI que vous souhaitez utiliser pour vos conversations.",
  },

  // =========================
  // HUGGINGFACE LLM SETTINGS
  // =========================
  huggingface: {
    "inference-endpoint": "Point de terminaison d'inférence HuggingFace",
    "endpoint-placeholder": "https://example.endpoints.huggingface.cloud",
    "access-token": "Token d'accès HuggingFace",
    "token-placeholder": "Entrez votre token d'accès HuggingFace",
    "token-limit": "Limite de tokens du modèle",
    "token-limit-placeholder": "4096",
  },

  // =========================
  // KOBOLDCPP SETTINGS
  // =========================
  koboldcpp: {
    "show-advanced": "Afficher la saisie manuelle du point de terminaison",
    "hide-advanced": "Masquer la saisie manuelle du point de terminaison",
    "base-url": "URL de base KoboldCPP",
    "base-url-placeholder": "http://127.0.0.1:5000/v1",
    "base-url-desc": "Entrez l'URL où KoboldCPP est en cours d'exécution.",
    "auto-detect": "Détection automatique",
    "token-context-window": "Fenêtre de contexte de tokens",
    "token-window-placeholder": "4096",
    "token-window-desc":
      "Nombre maximum de tokens pour le contexte et la réponse.",
    model: "Modèle KoboldCPP",
    "loading-models": "-- chargement des modèles disponibles --",
    "enter-url": "Entrez d'abord l'URL KoboldCPP",
    "model-desc":
      "Sélectionnez le modèle KoboldCPP que vous souhaitez utiliser. Les modèles se chargeront après avoir entré une URL KoboldCPP valide.",
    "model-choose":
      "Choisissez le modèle KoboldCPP que vous souhaitez utiliser pour vos conversations.",
  },

  // =========================
  // LITELLM
  // =========================
  litellm: {
    "model-tooltip":
      "Assurez-vous de sélectionner un modèle d'incorporation valide. Les modèles de chat ne sont pas des modèles d'incorporation. Voir",
    "model-tooltip-link": "cette page",
    "model-tooltip-more": "pour plus d'informations.",
    "base-url": "URL de base",
    "base-url-placeholder": "ex: https://proxy.openai.com",
    "max-embedding-chunk-length": "Longueur maximale du chunk d'incorporation",
    "token-window": "Fenêtre de contexte de tokens",
    "token-window-placeholder": "Limite de fenêtre de contexte (ex: 4096)",
    "api-key": "Clé API",
    optional: "optionnel",
    "api-key-placeholder": "sk-maclésecrete",
    "model-selection": "Sélection du modèle de chat",
    "loading-models": "-- chargement des modèles disponibles --",
    "waiting-url": "-- en attente de l'URL --",
    "loaded-models": "Vos modèles chargés",
    "manage-embedding": "Gérer l'incorporation",
    "embedding-required":
      "Litellm nécessite un service d'incorporation pour être utilisé.",
  },

  // =========================
  // LMSTUDIO LLM SELECTION
  // =========================
  lmstudio: {
    "max-tokens": "Tokens maximum",
    "max-tokens-desc":
      "Nombre maximum de tokens pour le contexte et la réponse.",
    "show-advanced": "Afficher la saisie manuelle du point de terminaison",
    "hide-advanced": "Masquer la saisie manuelle du point de terminaison",
    "base-url": "URL de base LM Studio",
    "base-url-placeholder": "http://localhost:1234/v1",
    "base-url-desc": "Entrez l'URL où LM Studio est en cours d'exécution.",
    "auto-detect": "Détection automatique",
    model: "Modèle LM Studio",
    "model-loading": "-- chargement des modèles disponibles --",
    "model-url-first": "Entrez d'abord l'URL LM Studio",
    "model-desc":
      "Sélectionnez le modèle LM Studio que vous souhaitez utiliser. Les modèles se chargeront après avoir entré une URL LM Studio valide.",
    "model-choose":
      "Choisissez le modèle LM Studio que vous souhaitez utiliser pour vos conversations.",
    "model-loaded": "Modèles chargés",
    "embedding-required":
      "LMStudio comme votre LLM nécessite que vous configuriez un service d'incorporation à utiliser.",
    "manage-embedding": "Gérer l'incorporation",
    "max-embedding-chunk-length": "Longueur maximale du chunk d'incorporation",
  },

  // =========================
  // LOCALAI LLM SELECTION
  // =========================
  localai: {
    "token-window": "Fenêtre de contexte de tokens",
    "token-window-placeholder": "4096",
    "api-key": "Clé API Local AI",
    "api-key-optional": "optionnel",
    "api-key-placeholder": "sk-maclésecrete",
    "show-advanced": "Afficher les paramètres avancés",
    "hide-advanced": "Masquer les paramètres avancés",
    "base-url": "URL de base Local AI",
    "base-url-placeholder": "http://localhost:8080/v1",
    "base-url-help": "Entrez l'URL où LocalAI fonctionne.",
    "auto-detect": "Détection automatique",
    "max-embedding-chunk-length": "Longueur maximale du chunk d'incorporation",
    "embedding-required": "L'incorporation est requise pour LocalAI.",
    "manage-embedding": "Gérer l'incorporation",
    "model-selection": "Sélection du modèle de chat",
    "loading-models": "-- chargement des modèles disponibles --",
    "waiting-url": "-- en attente de l'URL --",
    "loaded-models": "Vos modèles chargés",
  },

  // =========================
  // MISTRAL LLM OPTIONS
  // =========================
  mistral: {
    "api-key": "Clé API Mistral",
    "api-key-placeholder": "Clé API Mistral",
    "model-selection": "Sélection du modèle de chat",
    "loading-models": "-- chargement des modèles disponibles --",
    "waiting-key": "-- en attente de la clé API --",
    "available-models": "Modèles Mistral disponibles",
  },

  // =========================
  // NATIVE LLM SETTINGS
  // =========================
  native: {
    "experimental-warning":
      "L'utilisation d'un LLM hébergé localement est expérimentale et peut présenter un comportement inattendu.",
    "model-desc":
      "Sélectionnez un modèle parmi vos modèles hébergés localement.",
    "token-desc": "Nombre maximum de tokens pour le contexte et la réponse.",
  },

  // =========================
  // OLLAMA LLM SELECTION
  // =========================
  ollamallmselection: {
    "max-tokens": "Tokens maximum",
    "max-tokens-desc":
      "Nombre maximum de tokens pour le contexte et la réponse.",
    "show-advanced": "Afficher la saisie manuelle du point de terminaison",
    "hide-advanced": "Masquer la saisie manuelle du point de terminaison",
    "base-url": "URL de base Ollama",
    "base-url-placeholder": "http://127.0.0.1:11434",
    "base-url-desc": "Entrez l'URL où Ollama est en cours d'exécution.",
    "auto-detect": "Détection automatique",
    "keep-alive": "Maintenir Ollama actif",
    "no-cache": "Pas de cache",
    "five-minutes": "5 minutes",
    "one-hour": "1 heure",
    forever: "Pour toujours",
    "keep-alive-desc": "Maintenir le modèle chargé en mémoire.",
    "learn-more": "En savoir plus",
    "performance-mode": "Mode de performance",
    "base-default": "Base (par défaut)",
    maximum: "Maximum",
    "performance-mode-desc": "Sélectionnez le mode de performance.",
    note: "Note :",
    "maximum-warning": "Le mode maximum consomme plus de ressources.",
    base: "Base",
    "base-desc": "Le mode base est le paramètre par défaut.",
    "maximum-desc":
      "Le mode maximum offre des performances plus élevées en utilisant la fenêtre de contexte complète jusqu'au nombre maximum de tokens.",
    model: "Modèle Ollama",
    "loading-models": "-- chargement des modèles disponibles --",
    "enter-url": "Entrez d'abord l'URL Ollama",
    "model-desc":
      "Sélectionnez le modèle Ollama que vous souhaitez utiliser. Les modèles se chargeront lorsqu'une URL valide sera entrée.",
    "model-choose":
      "Choisissez le modèle Ollama que vous souhaitez utiliser pour vos conversations.",
  },

  // =========================
  // OPENAI LLM SELECTION
  // =========================
  openai: {
    "api-key": "Clé API OpenAI",
    "api-key-placeholder": "Entrez votre clé API OpenAI",
    "api-key-invalid": "Format de clé API OpenAI non valide",
    "model-preference": "Préférence de modèle",
    "model-selection": "Sélection du modèle de chat",
    "loading-models": "-- chargement des modèles disponibles --",
    "model-divider": "──────────",
  },

  // =========================
  // OPENROUTER LLM SELECTION
  // =========================
  openrouter: {
    "api-key": "Clé API OpenRouter",
    "api-key-placeholder": "Entrez votre clé API OpenRouter",
    "model-selection": "Sélection du modèle de chat",
    "loading-models": "-- chargement des modèles disponibles --",
  },

  // =========================
  // PERPLEXITY LLM SELECTION
  // =========================
  perplexity: {
    "api-key": "Clé API Perplexity",
    "api-key-placeholder": "Entrez votre clé API Perplexity",
    "model-selection": "Sélection du modèle de chat",
    "loading-models": "-- chargement des modèles disponibles --",
    "available-models": "Modèles Perplexity disponibles",
  },

  // =========================
  // TEXTGENWEBUI COMPONENT
  // =========================
  textgenwebui: {
    "base-url": "URL de base",
    "base-url-placeholder": "http://127.0.0.1:5000/v1",
    "token-window": "Fenêtre de contexte de tokens",
    "token-window-placeholder": "Limite de fenêtre de contexte (ex: 4096)",
    "api-key": "Clé API (Optionnel)",
    "api-key-placeholder": "Clé API TextGen Web UI",
    "max-tokens": "Tokens maximum",
    "max-tokens-placeholder": "Tokens maximum par requête (ex: 1024)",
  },

  // =========================
  // TOGETHERAI
  // =========================
  togetherai: {
    "api-key": "Clé API TogetherAI",
    "api-key-placeholder": "Entrez votre clé API TogetherAI",
    "model-selection": "Sélection du modèle de chat",
    "loading-models": "-- chargement des modèles disponibles --",
  },

  // =========================
  // XAI
  // =========================
  xai: {
    "api-key": "Clé API xAI",
    "api-key-placeholder": "Clé API xAI",
    "model-selection": "Sélection du modèle de chat",
    "loading-models": "-- chargement des modèles disponibles --",
    "enter-api-key":
      "Entrez une clé API valide pour voir les modèles disponibles pour votre compte.",
    "available-models": "Modèles disponibles",
    "model-description":
      "Sélectionnez le modèle xAI que vous souhaitez utiliser pour vos conversations.",
  },
  // LLM Provider specific translations
  "llm-provider.textgenwebui":
    "Connecter à une instance Text Generation WebUI.",
  "llm-provider.litellm": "Connecter à n'importe quel LLM via LiteLLM.",
  "llm-provider.openai-generic":
    "Connecter à n'importe quel point de terminaison API OpenAI compatible.",
  "llm-provider.system-default": "Utiliser le modèle intégré.",
  // ----------------------------
  // Loading States
  // ----------------------------
  loading: {
    models: "-- chargement des modèles disponibles --",
    "waiting-url": "-- en attente de l'URL --",
    "waiting-api-key": "-- en attente de la clé API --",
    "waiting-models": "-- en attente des modèles --",
  },
} as const;
