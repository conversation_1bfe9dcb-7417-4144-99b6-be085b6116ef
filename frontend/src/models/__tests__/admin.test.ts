// Mock baseHeaders first, before importing Admin
jest.mock("@/utils/request", () => ({
  baseHeaders: () => ({
    Authorization: "Bearer mock-token",
    "Content-Type": "application/json",
  }),
}));

import Admin from "../admin";
import { API_BASE } from "@/utils/constants";

// Mock fetch globally
global.fetch = jest.fn() as jest.MockedFunction<typeof fetch>;

describe("Admin Model", () => {
  beforeEach(() => {
    (fetch as jest.MockedFunction<typeof fetch>).mockClear();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe("updateSystemPreferences", () => {
    it("should make POST request with correct headers and body", async () => {
      const mockResponse = {
        success: true,
        error: null,
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      } as Response);

      const updates = {
        dynamic_context_window_percentage: 70,
        attachment_context_percentage: 60,
      };

      const result = await Admin.updateSystemPreferences(updates);

      // Verify the fetch call
      expect(fetch).toHaveBeenCalledTimes(1);
      expect(fetch).toHaveBeenCalledWith(
        `${API_BASE}/admin/system-preferences`,
        {
          method: "POST",
          headers: {
            Authorization: "Bearer mock-token",
            "Content-Type": "application/json",
          },
          body: JSON.stringify(updates),
        }
      );

      // Verify the result
      expect(result).toEqual(mockResponse);
    });

    it("should make POST request for dynamic context window percentage only", async () => {
      const mockResponse = {
        success: true,
        error: null,
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      } as Response);

      const updates = {
        dynamic_context_window_percentage: 85,
      };

      const result = await Admin.updateSystemPreferences(updates);

      // Verify the fetch call
      expect(fetch).toHaveBeenCalledTimes(1);
      expect(fetch).toHaveBeenCalledWith(
        `${API_BASE}/admin/system-preferences`,
        {
          method: "POST",
          headers: {
            Authorization: "Bearer mock-token",
            "Content-Type": "application/json",
          },
          body: JSON.stringify(updates),
        }
      );

      expect(result).toEqual(mockResponse);
    });

    it("should handle empty updates object", async () => {
      const mockResponse = {
        success: true,
        error: null,
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      } as Response);

      const result = await Admin.updateSystemPreferences({});

      expect(fetch).toHaveBeenCalledTimes(1);
      expect(fetch).toHaveBeenCalledWith(
        `${API_BASE}/admin/system-preferences`,
        {
          method: "POST",
          headers: {
            Authorization: "Bearer mock-token",
            "Content-Type": "application/json",
          },
          body: JSON.stringify({}),
        }
      );

      expect(result).toEqual(mockResponse);
    });

    it("should handle undefined updates parameter", async () => {
      const mockResponse = {
        success: true,
        error: null,
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      } as Response);

      const result = await Admin.updateSystemPreferences();

      expect(fetch).toHaveBeenCalledTimes(1);
      expect(fetch).toHaveBeenCalledWith(
        `${API_BASE}/admin/system-preferences`,
        {
          method: "POST",
          headers: {
            Authorization: "Bearer mock-token",
            "Content-Type": "application/json",
          },
          body: JSON.stringify({}),
        }
      );

      expect(result).toEqual(mockResponse);
    });

    it("should handle network errors", async () => {
      const networkError = new Error("Network error");

      (fetch as jest.MockedFunction<typeof fetch>).mockRejectedValueOnce(
        networkError
      );

      const updates = {
        dynamic_context_window_percentage: 70,
      };

      const result = await Admin.updateSystemPreferences(updates);

      expect(fetch).toHaveBeenCalledTimes(1);
      expect(result).toEqual({
        success: false,
        error: "Network error",
      });
    });

    it("should handle server error responses", async () => {
      const errorResponse = {
        success: false,
        error: "Validation failed",
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: () => Promise.resolve(errorResponse),
      } as Response);

      const updates = {
        dynamic_context_window_percentage: 150, // Invalid value
      };

      const result = await Admin.updateSystemPreferences(updates);

      expect(fetch).toHaveBeenCalledTimes(1);
      expect(result).toEqual(errorResponse);
    });

    it("should handle multiple system preferences updates", async () => {
      const mockResponse = {
        success: true,
        error: null,
        details: {
          successful: [
            "dynamic_context_window_percentage",
            "attachment_context_percentage",
            "max_tokens_per_user",
          ],
          failed: [],
          skipped: [],
        },
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      } as Response);

      const updates = {
        dynamic_context_window_percentage: 75,
        attachment_context_percentage: 65,
        max_tokens_per_user: 1000,
      };

      const result = await Admin.updateSystemPreferences(updates);

      expect(fetch).toHaveBeenCalledTimes(1);
      expect(fetch).toHaveBeenCalledWith(
        `${API_BASE}/admin/system-preferences`,
        {
          method: "POST",
          headers: {
            Authorization: "Bearer mock-token",
            "Content-Type": "application/json",
          },
          body: JSON.stringify(updates),
        }
      );

      expect(result).toEqual(mockResponse);
    });

    it("should properly serialize different data types", async () => {
      const mockResponse = {
        success: true,
        error: null,
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      } as Response);

      const updates = {
        dynamic_context_window_percentage: 70,
        limit_user_messages: true,
        message_limit: 25,
        footer_data: "[]",
        support_email: "<EMAIL>",
      };

      await Admin.updateSystemPreferences(updates);

      expect(fetch).toHaveBeenCalledWith(
        `${API_BASE}/admin/system-preferences`,
        {
          method: "POST",
          headers: {
            Authorization: "Bearer mock-token",
            "Content-Type": "application/json",
          },
          body: JSON.stringify(updates),
        }
      );
    });

    it("should use POST method and not GET", async () => {
      // This test specifically ensures we're using POST and not accidentally GET
      const mockResponse = { success: true };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      } as Response);

      await Admin.updateSystemPreferences({
        dynamic_context_window_percentage: 70,
      });

      const fetchCall = (fetch as jest.MockedFunction<typeof fetch>).mock
        .calls[0];
      const [url, options] = fetchCall;

      expect(url).toBe(`${API_BASE}/admin/system-preferences`);
      expect(options?.method).toBe("POST");
      expect(options?.headers).toBeDefined();
      expect(options?.body).toBeDefined();
    });

    // Integration test to ensure the flow works end-to-end
    it("should handle the complete context window percentage save flow", async () => {
      const mockResponse = {
        success: true,
        error: null,
        details: {
          successful: ["dynamic_context_window_percentage"],
          failed: [],
          skipped: [],
        },
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      } as Response);

      // Simulate the exact call that would come from the frontend AdminSystem component
      const contextWindowPercentage = 70;
      const updates = {
        dynamic_context_window_percentage: contextWindowPercentage,
      };

      const result = await Admin.updateSystemPreferences(updates);

      // Verify the request was made correctly
      expect(fetch).toHaveBeenCalledTimes(1);
      expect(fetch).toHaveBeenCalledWith(
        `${API_BASE}/admin/system-preferences`,
        {
          method: "POST",
          headers: {
            Authorization: "Bearer mock-token",
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            dynamic_context_window_percentage: 70,
          }),
        }
      );

      // Verify the response indicates success
      expect(result.success).toBe(true);
      expect(result.error).toBeNull();
    });
  });
});
