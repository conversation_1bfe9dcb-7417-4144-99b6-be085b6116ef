import { API_BASE } from "@/utils/constants";
import { baseHeaders } from "@/utils/request";
import {
  AdminUser,
  OrganizationsResponse,
  OrganizationResponse,
  UserResponse,
  Invitation,
  InviteResponse,
  AdminWorkspace,
  WorkspaceResponse,
  ApiKeysResponse,
  ApiKeyResponse,
  PdrSettings,
  DDSettings,
  SystemPreferences,
  EnvironmentVariableResponse,
  EnvironmentUpdateResponse,
  AdminApiResponse,
} from "@/types/admin";

// Use AdminApiResponse as SuccessResponse
type SuccessResponse = AdminApiResponse;

const Admin = {
  // User Management
  users: async (): Promise<AdminUser[]> => {
    return await fetch(`${API_BASE}/admin/users`, { headers: baseHeaders() })
      .then(
        (res) =>
          res.json() as Promise<{
            users?: AdminUser[] | { users: AdminUser[]; total: number };
          }>
      )
      .then((res) => {
        const usersData = res?.users;
        // For v2 endpoint, usersData may be an object { users: [...], total }
        if (Array.isArray(usersData)) {
          return usersData;
        }
        if (usersData && Array.isArray(usersData.users)) {
          return usersData.users;
        }
        return [];
      })
      .catch(() => {
        // Error handling
        return [];
      });
  },

  newUser: async (data: any): Promise<UserResponse> => {
    return await fetch(`${API_BASE}/admin/users/new`, {
      method: "POST",
      headers: { ...baseHeaders(), "Content-Type": "application/json" },
      body: JSON.stringify(data),
    })
      .then((res) => res.json() as Promise<UserResponse>)
      .catch((e: Error) => {
        // Error handling
        return { user: null, error: e.message };
      });
  },

  updateUser: async (userId: string, data: any): Promise<SuccessResponse> => {
    return await fetch(`${API_BASE}/admin/user/${userId}`, {
      method: "POST",
      headers: { ...baseHeaders(), "Content-Type": "application/json" },
      body: JSON.stringify(data),
    })
      .then((res) => res.json() as Promise<SuccessResponse>)
      .catch((e: Error) => {
        // Error handling
        return { success: false, error: e.message };
      });
  },

  deleteUser: async (userId: string): Promise<SuccessResponse> => {
    return await fetch(`${API_BASE}/admin/user/${userId}`, {
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<SuccessResponse>)
      .catch((e: Error) => {
        // Error handling
        return { success: false, error: e.message };
      });
  },

  // Organization Management (Added)
  organizations: async (): Promise<OrganizationsResponse> => {
    try {
      const res = await fetch(`${API_BASE}/admin/organizations`, {
        headers: baseHeaders(),
      });
      const data = (await res.json()) as { organizations?: any[] };
      return { organizations: data.organizations || [], error: null };
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : "Unknown error";
      // Error fetching organizations
      return { organizations: [], error: errorMessage };
    }
  },

  createOrganization: async (name: string): Promise<OrganizationResponse> => {
    return await fetch(`${API_BASE}/admin/organization`, {
      method: "POST",
      headers: { ...baseHeaders(), "Content-Type": "application/json" },
      body: JSON.stringify({ name }),
    })
      .then((res) => res.json() as Promise<OrganizationResponse>) // Return the full response { organization: ... } or { error: ... }
      .catch((e: Error) => {
        // Error handling
        return { organization: null, error: e.message }; // Consistent error format
      });
  },

  getOrganization: async (
    organizationId: string
  ): Promise<OrganizationResponse> => {
    return await fetch(`${API_BASE}/admin/organization/${organizationId}`, {
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((e) => {
        // Error handling
        return { organization: null, error: e.message };
      });
  },

  updateOrganization: async (
    organizationId: string,
    data: { name: string }
  ): Promise<OrganizationResponse> => {
    return await fetch(`${API_BASE}/admin/organization/${organizationId}`, {
      method: "POST",
      headers: { ...baseHeaders(), "Content-Type": "application/json" },
      body: JSON.stringify(data),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((e) => {
        // Error handling
        return { organization: null, error: e.message };
      });
  },

  deleteOrganization: async (
    organizationId: string
  ): Promise<SuccessResponse> => {
    return await fetch(`${API_BASE}/admin/organization/${organizationId}`, {
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<SuccessResponse>)
      .catch((e: Error) => {
        // Error handling
        return { success: false, error: e.message };
      });
  },

  // Invitations
  invites: async (): Promise<Invitation[]> => {
    return await fetch(`${API_BASE}/admin/invites`, { headers: baseHeaders() })
      .then((res) => res.json() as Promise<{ invites?: Invitation[] }>)
      .then((res) => res?.invites || [])
      .catch(() => {
        // Error handling
        return [];
      });
  },

  newInvite: async ({
    role = null,
    workspaceIds = null,
    maxUsage = 1,
  }: {
    role?: string | null;
    workspaceIds?: string[] | null;
    maxUsage?: number;
  }): Promise<InviteResponse> => {
    return await fetch(`${API_BASE}/admin/invite/new`, {
      method: "POST",
      headers: { ...baseHeaders(), "Content-Type": "application/json" },
      body: JSON.stringify({
        role,
        workspaceIds,
        maxUsage,
      }),
    })
      .then((res) => res.json() as Promise<InviteResponse>)
      .catch((e: Error) => {
        // Error handling
        return { invite: null, error: e.message };
      });
  },

  disableInvite: async (inviteId: string): Promise<SuccessResponse> => {
    return await fetch(`${API_BASE}/admin/invite/${inviteId}`, {
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((e: Error) => {
        // Error handling
        return { success: false, error: e.message };
      });
  },

  // Workspaces Mgmt
  workspaces: async (): Promise<AdminWorkspace[]> => {
    return await fetch(`${API_BASE}/admin/workspaces`, {
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<{ workspaces?: AdminWorkspace[] }>)
      .then((res) => res?.workspaces || [])
      .catch(() => {
        // Error handling
        return [];
      });
  },

  workspaceUsers: async (workspaceId: string): Promise<AdminUser[]> => {
    return await fetch(`${API_BASE}/admin/workspaces/${workspaceId}/users`, {
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<any>)
      .then((res) => {
        const u = res?.users;
        if (Array.isArray(u)) {
          return u;
        }
        // handle nested shape { users: [...], total }
        if (u && Array.isArray(u.users)) {
          return u.users;
        }
        return [];
      })
      .catch(() => {
        // Error handling
        return [];
      });
  },

  linkedWorkspaces: async (workspaceId: string): Promise<AdminWorkspace[]> => {
    return await fetch(
      `${API_BASE}/admin/workspaces/${workspaceId}/linked-workspaces`,
      { headers: baseHeaders() }
    )
      .then(
        (res) => res.json() as Promise<{ linkedWorkspaces?: AdminWorkspace[] }>
      )
      .then((res) => res?.linkedWorkspaces || [])
      .catch(() => {
        // Error handling
        return [];
      });
  },

  newWorkspace: async (name: string): Promise<WorkspaceResponse> => {
    return await fetch(`${API_BASE}/admin/workspaces/new`, {
      method: "POST",
      headers: { ...baseHeaders(), "Content-Type": "application/json" },
      body: JSON.stringify({ name }),
    })
      .then((res) => res.json() as Promise<WorkspaceResponse>)
      .catch((e: Error) => {
        // Error handling
        return { workspace: null, error: e.message };
      });
  },

  updateUsersInWorkspace: async (
    workspaceId: string,
    userIds: string[] = []
  ): Promise<SuccessResponse> => {
    return await fetch(
      `${API_BASE}/admin/workspaces/${workspaceId}/update-users`,
      {
        method: "POST",
        headers: { ...baseHeaders(), "Content-Type": "application/json" },
        body: JSON.stringify({ userIds }),
      }
    )
      .then((res) => res.json() as Promise<any>)
      .catch((e: Error) => {
        // Error handling
        return { success: false, error: e.message };
      });
  },

  updateLinkedWorkspaces: async (
    workspaceId: string,
    linkedWorkspaces: string[] = []
  ): Promise<SuccessResponse> => {
    return await fetch(
      `${API_BASE}/admin/workspaces/${workspaceId}/update-linked-workspaces`,
      {
        method: "POST",
        headers: { ...baseHeaders(), "Content-Type": "application/json" },
        body: JSON.stringify({ linkedWorkspaces }),
      }
    )
      .then((res) => res.json() as Promise<any>)
      .catch((e: Error) => {
        // Error handling
        return { success: false, error: e.message };
      });
  },

  deleteWorkspace: async (workspaceId: string): Promise<SuccessResponse> => {
    return await fetch(`${API_BASE}/admin/workspaces/${workspaceId}`, {
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((e: Error) => {
        // Error handling
        return { success: false, error: e.message };
      });
  },

  flushVectorCachesWorkspace: async (
    workspaceId: string
  ): Promise<SuccessResponse> => {
    return await fetch(
      `${API_BASE}/admin/workspaces/vector-caches/${workspaceId}`,
      { headers: baseHeaders() }
    )
      .then((res) => res.json() as Promise<any>)
      .catch((e: Error) => {
        // Error handling
        return { success: false, error: e.message };
      });
  },

  // System Preferences
  systemPreferences: async (): Promise<SystemPreferences | null> => {
    return await fetch(`${API_BASE}/admin/system-preferences`, {
      headers: baseHeaders(),
    })
      .then((res) => {
        return res.json() as Promise<SystemPreferences>;
      })
      .then((data) => {
        return data;
      })
      .catch(() => {
        // Error fetching system preferences
        return null;
      });
  },

  getPdrSettings: async (): Promise<PdrSettings | null> => {
    return await fetch(`${API_BASE}/admin/pdr-settings`, {
      headers: baseHeaders(),
    })
      .then(
        (res) =>
          res.json() as Promise<{
            result?: {
              adjacentVector?: any;
              keepPdrVectors?: boolean;
              globalPdrOverride?: boolean;
            };
          }>
      )
      .then((res) => res?.result)
      .then((res) => ({
        adjacentVector: res?.adjacentVector || null,
        keepPdrVectors: res?.keepPdrVectors ?? false,
        globalPdrOverride: res?.globalPdrOverride ?? true,
      }))
      .catch(() => {
        // Error handling
        return null;
      });
  },

  getDDSettings: async (): Promise<DDSettings | null> => {
    return await fetch(`${API_BASE}/admin/dd-settings`, {
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<{ result?: DDSettings }>)
      .then((res) => res?.result)
      .then((res) => ({
        ddVectorEnabled: res?.ddVectorEnabled ?? false,
        ddMemoEnabled: res?.ddMemoEnabled ?? true,
        ddBaseEnabled: res?.ddBaseEnabled ?? true,
        ddLinkedWorkspaceImpact: res?.ddLinkedWorkspaceImpact ?? true,
        ddVectorTokenLimit: res?.ddVectorTokenLimit ?? 5000,
        ddMemoTokenLimit: res?.ddMemoTokenLimit ?? 3000,
        ddBaseTokenLimit: res?.ddBaseTokenLimit ?? 2000,
      }))
      .catch(() => {
        // Error handling
        return null;
      });
  },

  /**
   * Fetches system preferences by fields
   * @param {string[]} labels - Array of labels for settings
   * @returns {Promise<{settings: Object, error: string}>} - System preferences object
   */
  systemPreferencesByFields: async (labels: string[] = []): Promise<any> => {
    return await fetch(
      `${API_BASE}/admin/system-preferences-for?labels=${labels.join(",")}`,
      { headers: baseHeaders() }
    )
      .then((res) => res.json() as Promise<any>)
      .catch(() => {
        // Error handling
        return null;
      });
  },

  updateSystemPreferences: async (
    updates: Record<string, any> = {}
  ): Promise<SuccessResponse> => {
    return await fetch(`${API_BASE}/admin/system-preferences`, {
      method: "POST",
      headers: { ...baseHeaders(), "Content-Type": "application/json" },
      body: JSON.stringify(updates),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((e: Error) => {
        // Error handling
        return { success: false, error: e.message };
      });
  },

  defaultLoginUi: async (): Promise<{ settings: { login_ui: string } }> => {
    return await fetch(`${API_BASE}/admin/login-ui`, { headers: baseHeaders() })
      .then((res) => {
        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`);
        }
        return res.json().then((data) => {
          return {
            settings: {
              login_ui: data?.result?.loginUI || "ist-legal-general",
            },
          };
        });
      })
      .catch(() => {
        // Error fetching login UI
        return { settings: { login_ui: "ist-legal-general" } };
      });
  },

  selectDefaultLogin: async (updates: any): Promise<SuccessResponse> => {
    try {
      const res = await fetch(`${API_BASE}/admin/default-login`, {
        method: "POST",
        headers: { ...baseHeaders(), "Content-Type": "application/json" },
        body: JSON.stringify(updates),
      });
      const data = (await res.json()) as SuccessResponse;
      // Response logged
      return data; // Return { success, error } directly
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : "Unknown error";
      // Error updating login UI
      return { success: false, error: errorMessage };
    }
  },

  updatePdrSettings: async (
    settings: Partial<PdrSettings>
  ): Promise<SuccessResponse> => {
    return await fetch(`${API_BASE}/admin/update-pdr-settings`, {
      method: "POST",
      headers: { ...baseHeaders(), "Content-Type": "application/json" },
      body: JSON.stringify(settings),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((e: Error) => {
        // Error handling
        return { success: false, error: e.message };
      });
  },

  updateDDSettings: async (
    settings: Partial<DDSettings>
  ): Promise<SuccessResponse> => {
    return await fetch(`${API_BASE}/admin/update-dd-settings`, {
      method: "POST",
      headers: { ...baseHeaders(), "Content-Type": "application/json" },
      body: JSON.stringify(settings),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((e: Error) => {
        // Error handling
        return { success: false, error: e.message };
      });
  },

  getApiKeys: async function (): Promise<ApiKeysResponse> {
    return fetch(`${API_BASE}/admin/api-keys`, { headers: baseHeaders() })
      .then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText || "Error fetching api keys.");
        }
        return res.json();
      })
      .catch((e: any) => {
        // Error handling
        return { apiKeys: [], error: e.message };
      });
  },

  generateApiKey: async function (): Promise<ApiKeyResponse> {
    return fetch(`${API_BASE}/admin/generate-api-key`, {
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText || "Error generating api key.");
        }
        return res.json();
      })
      .catch((e: any) => {
        // Error handling
        return { apiKey: null, error: e.message };
      });
  },

  deleteApiKey: async function (apiKeyId: string = ""): Promise<boolean> {
    return fetch(`${API_BASE}/admin/delete-api-key/${apiKeyId}`, {
      headers: baseHeaders(),
    })
      .then((res) => res.ok)
      .catch(() => {
        // Error handling
        return false;
      });
  },

  updateAllLanceDBWorkspaces: async (
    data: any = {}
  ): Promise<SuccessResponse> => {
    return await fetch(`${API_BASE}/admin/workspaces/lancedb/update-all`, {
      method: "POST",
      headers: { ...baseHeaders(), "Content-Type": "application/json" },
      body: JSON.stringify(data),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((e: Error) => {
        // Error handling
        return { success: false, error: e.message };
      });
  },

  getEnvironmentVariable: async (
    key: string
  ): Promise<EnvironmentVariableResponse> => {
    try {
      const response = await fetch(`${API_BASE}/system/environment/${key}`, {
        headers: baseHeaders(),
      });

      if (!response.ok) {
        const errorData = (await response.json().catch(() => ({}))) as {
          error?: string;
        };
        // Error getting environment variable
        return {
          value: null,
          error: errorData.error || `HTTP Error: ${response.status}`,
        };
      }

      const result = (await response.json()) as EnvironmentVariableResponse;
      return result;
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : "Unknown error";
      // Error fetching environment variable
      return { value: null, error: errorMessage };
    }
  },

  updateEnvironmentVariable: async (
    data: any = {}
  ): Promise<EnvironmentUpdateResponse> => {
    try {
      // Ensure data has the expected structure
      const payload = data.updates ? data : { updates: data };

      // Ensure all values are strings
      if (payload.updates) {
        for (const key in payload.updates) {
          if (
            payload.updates[key] !== null &&
            payload.updates[key] !== undefined
          ) {
            payload.updates[key] = String(payload.updates[key]);
          }
        }
      }

      const response = await fetch(`${API_BASE}/system/update-env`, {
        method: "POST",
        headers: { ...baseHeaders(), "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });

      const result = (await response.json()) as {
        error?: string;
        newValues?: any;
      };

      if (!response.ok) {
        // Server error updating environment variables
        return { success: false, error: result.error || "Server error" };
      }

      if (result.error) {
        // Warning while updating environment variables
        return { success: false, error: result.error };
      }

      return { success: true, data: result.newValues };
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : "Unknown error";
      // Error updating environment variables
      return {
        success: false,
        error: errorMessage || "Failed to update environment variables",
      };
    }
  },
};

export default Admin;
