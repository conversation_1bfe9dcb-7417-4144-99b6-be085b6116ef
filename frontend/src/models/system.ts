import {
  API_BASE,
  AUTH_TIMESTAMP,
  AUTH_TOKEN,
  fullApiUrl,
} from "@/utils/constants";
import { baseHeaders, safeJsonParse } from "@/utils/request";
import DataConnector from "./dataConnector";
import LiveDocumentSync from "./experimental/liveSync";
import AgentPlugins from "./experimental/agentPlugins";
import useUserStore from "@/stores/userStore";
import useSystemSettingsStore from "@/stores/settingsStore";
// import showToast from "@/utils/toast"; // Removed - not used

// Type definitions
interface CacheKeys {
  footerIcons: string;
  supportEmail: string;
  customAppName: string;
  customParagraph: string;
  customWebsiteLink: string;
  customWebsiteVisible: string;
  websiteLink: string; // Alias for customWebsiteLink
  websiteVisible: string; // Alias for customWebsiteVisible
  BootInfo: string;
  CohereModels: string;
  OpenAiModels: string;
  AzureModels: string;
  AnthropicModels: string;
  GeminiModels: string;
  MistralModels: string;
  DeepSeekModels: string;
  FireworksAiModels: string;
  GroqModels: string;
  XAiModels: string;
  HuggingFaceModels: string;
  OllamaModels: string;
  TogetherAiModels: string;
  LMStudioModels: string;
  LocalAiModels: string;
  LiteLLMModels: string;
  GenericOpenAiModels: string;
  OpenRouterModels: string;
  KoboldCPPModels: string;
  TextGenWebUIModels: string;
  NativeLLMModels: string;
  PerplexityModels: string;
  JinaModels: string;
  VoyageAiModels: string;
  CohereEmbedderModels: string;
  OpenAiEmbedderModels: string;
  AzureEmbedderModels: string;
  GeminiEmbedderModels: string;
  LMStudioEmbedderModels: string;
  LocalAiEmbedderModels: string;
  OllamaEmbedderModels: string;
  LiteLLMEmbedderModels: string;
  GenericOpenAiEmbedderModels: string;
  NativeEmbedderModels: string;
  ChromaOptions: string;
  PineconeOptions: string;
  LanceDbOptions: string;
  WeaviateOptions: string;
  QDrantOptions: string;
  MilvusOptions: string;
  ZillizOptions: string;
  AstraDBOptions: string;
  OpenAiTTSModels: string;
  ElevenLabsTTSModels: string;
  PiperTTSModels: string;
  OpenAiTranscriptionModels: string;
  NativeTranscriptionModels: string;
  SystemSettings: string;
  WelcomeMessages: string;
  ExamplePrompts: string;
  ApiKeys: string;
  CustomModels: string;
  FeatureFlags: string;
  SlashCommandsPresets: string;
  RequestLegalAssistanceSettings: string;
  BrowserExtensionApiKeys: string;
  EventLogs: string;
  CollectorInfo: string;
  GetDocumentBuilder: string;
  GetCDBDocumentation: string;
  GetRetentionPolicies: string;
  GetRetentionPolicyRuns: string;
  GetCategories: string;
  GetDocumentBuilderPromptsConfig: string;
}

interface KeysResponse {
  results: {
    palette?: string;
    language?: string;
    MultiUserMode?: boolean;
    RequiresAuth?: boolean;
    LLMProvider?: string | null;
    VectorDB?: string | null;
    [key: string]: any;
  };
}

interface TokenResponse {
  valid: boolean;
  message?: string;
  token?: string;
  user?: any;
  error?: string;
}

interface SystemSettingsParams {
  systemPrompt?: string;
  vectorSearch?: string | number;
  validationPrompt?: string;
  canvasSystemPrompt?: string;
  canvasUploadSystemPrompt?: string;
  manualWorkEstimatorPrompt?: string;
  styleGenerationPrompt?: string;
  autoCodingPromptTemplate?: string;
}

interface SupportFunctionsParams {
  LLMProvider_SUPPORT?: string;
  OpenAiModelPref_SUPPORT?: string;
  AnthropicModelPref_SUPPORT?: string;
  GeminiLLMModelPref_SUPPORT?: string;
  supportFunctionPromptUpgrade?: string;
  supportFunctionValidation?: string;
  supportFunctionManualTime?: string;
}

interface LegalTaskParams {
  name: string;
  subCategory?: string;
  description: string;
  legalPrompt: string;
  legalTaskType: string;
}

// Directory structure types for local files
interface DirectoryItem {
  name: string;
  type: "file" | "folder";
  items?: DirectoryItem[];
  path?: string;
  id?: string;
  starred?: boolean;
  pinned?: boolean;
  pdr?: boolean;
  docId?: string;
  cached?: boolean;
  token_count_estimate?: number;
  [key: string]: any;
}

interface DirectoryStructure {
  name: string;
  type: "folder";
  items: DirectoryItem[];
}

const System = {
  cacheKeys: {
    footerIcons: "footer_links",
    supportEmail: "support_email",
    customAppName: "custom_app_name",
    customParagraph: "custom_paragraph",
    customWebsiteLink: "customWebsiteLink",
    customWebsiteVisible: "customWebsiteVisible",
    websiteLink: "customWebsiteLink", // Alias for backward compatibility
    websiteVisible: "customWebsiteVisible", // Alias for backward compatibility
    BootInfo: "boot-info",
    CohereModels: "cohere-models",
    OpenAiModels: "openai-models",
    AzureModels: "azure-models",
    AnthropicModels: "anthropic-models",
    GeminiModels: "gemini-models",
    MistralModels: "mistral-models",
    DeepSeekModels: "deepseek-models",
    FireworksAiModels: "fireworks-ai-models",
    GroqModels: "groq-ai-models",
    XAiModels: "xai-llm-models",
    HuggingFaceModels: "huggingface-models",
    OllamaModels: "ollama-models",
    TogetherAiModels: "together-ai-models",
    LMStudioModels: "lmstudio-models",
    LocalAiModels: "localai-models",
    LiteLLMModels: "litellm-models",
    GenericOpenAiModels: "generic-openai-models",
    OpenRouterModels: "openrouter-models",
    KoboldCPPModels: "koboldcpp-models",
    TextGenWebUIModels: "textgenwebui-models",
    NativeLLMModels: "native-llm-models",
    PerplexityModels: "perplexity-models",
    JinaModels: "jina-models",
    VoyageAiModels: "voyageai-models",
    CohereEmbedderModels: "cohere-embedder-models",
    OpenAiEmbedderModels: "openai-embedder-models",
    AzureEmbedderModels: "azure-embedder-models",
    GeminiEmbedderModels: "gemini-embedder-models",
    LMStudioEmbedderModels: "lmstudio-embedder-models",
    LocalAiEmbedderModels: "localai-embedder-models",
    OllamaEmbedderModels: "ollama-embedder-models",
    LiteLLMEmbedderModels: "litellm-embedder-models",
    GenericOpenAiEmbedderModels: "generic-openai-embedder-models",
    NativeEmbedderModels: "native-embedder-models",
    ChromaOptions: "chroma-options",
    PineconeOptions: "pinecone-options",
    LanceDbOptions: "lancedb-options",
    WeaviateOptions: "weaviate-options",
    QDrantOptions: "qdrant-options",
    MilvusOptions: "milvus-options",
    ZillizOptions: "zilliz-options",
    AstraDBOptions: "astradb-options",
    OpenAiTTSModels: "openai-tts-models",
    ElevenLabsTTSModels: "elevenlabs-tts-models",
    PiperTTSModels: "piper-tts-models",
    OpenAiTranscriptionModels: "openai-transcription-models",
    NativeTranscriptionModels: "native-transcription-models",
    SystemSettings: "system-settings",
    WelcomeMessages: "welcome-messages",
    ExamplePrompts: "example-prompts",
    ApiKeys: "api-keys",
    CustomModels: "custom-models",
    FeatureFlags: "feature-flags",
    SlashCommandsPresets: "slash-commands-presets",
    RequestLegalAssistanceSettings: "request-legal-assistance-settings",
    BrowserExtensionApiKeys: "browser-extension-api-keys",
    EventLogs: "event-logs",
    CollectorInfo: "collector-info",
    GetDocumentBuilder: "get-document-builder",
    GetCDBDocumentation: "get-cdb-documentation",
    GetRetentionPolicies: "get-retention-policies",
    GetRetentionPolicyRuns: "get-retention-policy-runs",
    GetCategories: "get-categories",
    GetDocumentBuilderPromptsConfig: "get-document-builder-prompts-config", // New cache key
  } as CacheKeys,

  ping: async function (): Promise<boolean> {
    return await fetch(`${API_BASE}/ping`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<{ online?: boolean }>)
      .then((res) => res?.online || false)
      .catch(() => false);
  },
  totalIndexes: async function (slug: string | null = null): Promise<number> {
    const url = new URL(`${fullApiUrl()}/system/system-vectors`);
    if (slug) url.searchParams.append("slug", encodeURIComponent(slug));
    return await fetch(url.toString(), {
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not find indexes.");
        return res.json() as Promise<{ vectorCount?: number }>;
      })
      .then((res) => res.vectorCount ?? 0)
      .catch(() => 0);
  },
  keys: async function (
    forceRefresh = false
  ): Promise<KeysResponse["results"] | null> {
    const baseUrl = API_BASE.startsWith("http")
      ? API_BASE
      : `${window.location.origin}${API_BASE}`;
    const url = new URL(`${baseUrl}/setup-complete`);
    if (forceRefresh) {
      url.searchParams.append("_t", Date.now().toString());
    }

    return await fetch(url.toString(), {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not find setup information.");
        return res.json() as Promise<KeysResponse>;
      })
      .then(async function (res) {
        const results = res.results;
        const { initializeSetting } = useSystemSettingsStore.getState();

        if (
          results.palette !== undefined &&
          results.palette !== null &&
          results.palette !== ""
        ) {
          initializeSetting("color-palette", results.palette);
        }

        if (!localStorage.getItem("language") && results.language) {
          localStorage.setItem("language", results.language);
        }

        return results;
      })
      .catch(() => null);
  },

  envSettings: async function (): Promise<Record<string, any> | null> {
    return await fetch(`${API_BASE}/system/env-settings`, {
      headers: baseHeaders(),
    })
      .then(async (res) => {
        if (!res.ok) {
          const errorText = await res.text();
          console.error(
            `Failed to fetch environment settings: ${res.status} ${res.statusText}`,
            errorText
          );
          throw new Error(
            `Could not fetch environment settings: ${res.status}`
          );
        }
        return res.json() as Promise<{ settings?: Record<string, any> }>;
      })
      .then((res) => res.settings || {})
      .catch((error: Error) => {
        console.error("Error fetching environment settings:", error);
        return null;
      });
  },
  localFiles: async function (
    workspaceId: string,
    forceRefresh: boolean = false
  ): Promise<DirectoryStructure | null> {
    const slugModule = useUserStore.getState().selectedModule;
    let url = `${API_BASE}/system/local-files/${slugModule}/${workspaceId}`;

    // Add cache-busting parameter if force refresh is requested
    if (forceRefresh) {
      url += `?_t=${Date.now()}`;
    }

    return await fetch(url, {
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not find setup information.");
        return res.json() as Promise<{ files?: DirectoryStructure }>;
      })
      .then((res) => res.files ?? null)
      .catch(() => null);
  },
  needsAuthCheck: function (): boolean {
    const lastAuthCheck = window.localStorage.getItem(AUTH_TIMESTAMP);
    if (!lastAuthCheck) return true;
    const expiresAtMs = Number(lastAuthCheck) + 60 * 15 * 1000; // expires in 15 minutes instead of 5
    return Number(new Date()) > expiresAtMs;
  },

  _checkingAuth: false, // Property to manage singleton behavior for checkAuth
  checkAuth: async function (
    currentToken: string | null = null
  ): Promise<boolean> {
    try {
      if (this._checkingAuth) return true;
      this._checkingAuth = true;

      const valid = await fetch(`${API_BASE}/system/check-token`, {
        headers: baseHeaders(currentToken),
      })
        .then((res) => res.ok)
        .catch((error: Error) => {
          // console.error("Auth check error:", error);
          return error.name !== "TypeError";
        });

      if (valid) {
        window.localStorage.setItem(
          AUTH_TIMESTAMP,
          Number(new Date()).toString()
        );
      }

      this._checkingAuth = false;
      return valid;
    } catch {
      this._checkingAuth = false;
      // console.error("Unexpected auth check error:", error);
      return true;
    }
  },
  requestToken: async function (body: any): Promise<TokenResponse> {
    try {
      const response = await fetch(`${API_BASE}/request-token`, {
        method: "POST",
        headers: {
          ...baseHeaders(),
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ...body }),
      });

      const data = (await response.json()) as TokenResponse;

      if (!response.ok) {
        // Backend returns error in the 'error' field
        console.error("Authentication failed:", data.error || "Unknown error");
        return { valid: false, message: data.error || "Authentication failed" };
      }

      // Success case - backend returns token and user
      if (data.token && data.user) {
        return { valid: true, token: data.token, user: data.user };
      }

      // Unexpected response format
      console.error("Unexpected response format:", data);
      return { valid: false, message: "Invalid response from server" };
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      console.error("Request failed:", errorMessage);
      return {
        valid: false,
        message: "Network error: Could not connect to server",
      };
    }
  },
  recoverAccount: async function (
    username: string,
    email: string
  ): Promise<any> {
    return await fetch(`${API_BASE}/system/recover-account`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ username, email }),
    })
      .then(async (res) => {
        const data = (await res.json()) as {
          success?: boolean;
          message?: string;
          error?: string;
        };
        if (!res.ok) {
          throw new Error(data.message || "Error recovering account.");
        }
        return data;
      })
      .catch((err: Error) => {
        // console.error(err);
        return { success: false, error: err.message };
      });
  },
  resetPassword: async function (
    token: string,
    newPassword: string,
    confirmPassword: string
  ): Promise<any> {
    return await fetch(`${API_BASE}/system/reset-password`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ token, newPassword, confirmPassword }),
    })
      .then(async (res) => {
        const data = (await res.json()) as {
          success?: boolean;
          message?: string;
          error?: string;
        };
        if (!res.ok) {
          throw new Error(data.message || "Error resetting password.");
        }
        return data;
      })
      .catch((err: Error) => {
        // console.error(err);
        return { success: false, error: err.message };
      });
  },
  checkDocumentProcessorOnline: async function (): Promise<boolean> {
    return await fetch(`${API_BASE}/system/document-processing-status`, {
      headers: baseHeaders(),
    })
      .then((res) => res.ok)
      .catch(() => false);
  },
  acceptedDocumentTypes: async function (): Promise<string[] | null> {
    return await fetch(`${API_BASE}/system/accepted-document-types`, {
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<{ types?: string[] }>)
      .then((res) => res?.types ?? null)
      .catch(() => null);
  },
  updateSystem: async function (data: any): Promise<any> {
    const payload = { updates: data };

    return await fetch(`${API_BASE}/system/update-env`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(payload),
    })
      .then(async (res) => {
        if (!res.ok) {
          const errorData = (await res
            .json()
            .catch(() => ({ error: "Unknown error" }))) as { error?: string };
          console.error("Update system error:", errorData);
          throw new Error(
            errorData.error || `HTTP error! status: ${res.status}`
          );
        }
        return res.json() as Promise<any>;
      })
      .catch((err: Error) => {
        // console.error("Error updating system settings:", err);
        return { newValues: null, error: err.message };
      });
  },
  resetLLMSettings: async function (moduleSuffix: string = ""): Promise<any> {
    return await fetch(`${API_BASE}/system/reset-llm-settings`, {
      method: "POST",
      headers: {
        ...baseHeaders(),
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ moduleSuffix }),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((err: Error) => {
        // console.error("Error resetting LLM settings:", err);
        return { success: false, error: err.message };
      });
  },
  updateSystemPassword: async function (data: any): Promise<any> {
    return await fetch(`${API_BASE}/system/update-password`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(data),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((err: Error) => {
        // console.error(err);
        return { success: false, error: err.message };
      });
  },
  setupMultiUser: async function (data: any): Promise<any> {
    return await fetch(`${API_BASE}/system/enable-multi-user`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(data),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((err: Error) => {
        // console.error(err);
        return { success: false, error: err.message };
      });
  },
  setupPublicUser: async function (data: any): Promise<any> {
    return await fetch(`${API_BASE}/system/enable-public-user`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(data),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((err: Error) => {
        // console.error(err);
        return { success: false, error: err.message };
      });
  },
  setupDocumentDrafting: async function (data: any): Promise<any> {
    return await fetch(`${API_BASE}/system/set-document-drafting`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(data),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((err: Error) => {
        // console.error(err);
        return { success: false, error: err.message };
      });
  },
  setDocumentDraftingPrompt: async function (data: any): Promise<any> {
    return await fetch(`${API_BASE}/system/set-document-drafting-prompt`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(data),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((err: Error) => {
        // console.error(err);
        return { success: false, error: err.message };
      });
  },
  setDefaultSettings: async function ({
    systemPrompt,
    vectorSearch,
    validationPrompt,
    canvasSystemPrompt,
    canvasUploadSystemPrompt,
    manualWorkEstimatorPrompt,
    styleGenerationPrompt,
    autoCodingPromptTemplate,
  }: SystemSettingsParams): Promise<any> {
    return await fetch(`${API_BASE}/system/set-default-settings`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({
        defaultPrompt: systemPrompt,
        vectorSearch: vectorSearch ? Number(vectorSearch) : null,
        validationPrompt,
        canvasSystemPrompt,
        canvasUploadSystemPrompt,
        manualWorkEstimatorPrompt,
        styleGenerationPrompt,
        autoCodingPromptTemplate,
      }),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((err: Error) => {
        // console.error(err);
        return { success: false, error: err.message };
      });
  },
  applyDefaultPromptToAllWorkspaces: async function (
    defaultPrompt: string
  ): Promise<any> {
    return await fetch(`${API_BASE}/system/apply-default-prompt`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({
        defaultPrompt,
      }),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((err: Error) => {
        // console.error(err);
        return { success: false, error: err.message };
      });
  },
  applyVectorSearchToAllWorkspaces: async function (
    vectorSearch: string | number
  ): Promise<any> {
    return await fetch(`${API_BASE}/system/apply-vector-search`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({
        vectorSearch: vectorSearch ? Number(vectorSearch) : null,
      }),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((err: Error) => {
        // console.error(err);
        return { success: false, error: err.message };
      });
  },
  setDocumentBuilder: async function ({
    summarySystemPrompt,
    summaryUserPrompt,
    relevanceSystemPrompt,
    relevanceUserPrompt,
    selectMainDocumentSystemPrompt,
    selectMainDocumentUserPrompt,
    topicsSectionsSystemPrompt,
    topicsSectionsUserPrompt,
    sectionSystemPrompt,
    sectionUserPrompt,
    sectionLegalIssuesSystemPrompt,
    sectionLegalIssuesUserPrompt,
  }: {
    summarySystemPrompt: string;
    summaryUserPrompt: string;
    relevanceSystemPrompt: string;
    relevanceUserPrompt: string;
    selectMainDocumentSystemPrompt: string;
    selectMainDocumentUserPrompt: string;
    topicsSectionsSystemPrompt: string;
    topicsSectionsUserPrompt: string;
    sectionSystemPrompt: string;
    sectionUserPrompt: string;
    sectionLegalIssuesSystemPrompt: string;
    sectionLegalIssuesUserPrompt: string;
  }): Promise<any> {
    return await fetch(`${API_BASE}/system/set-document-builder`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({
        summarySystemPrompt,
        summaryUserPrompt,
        relevanceSystemPrompt,
        relevanceUserPrompt,
        selectMainDocumentSystemPrompt,
        selectMainDocumentUserPrompt,
        topicsSectionsSystemPrompt,
        topicsSectionsUserPrompt,
        sectionSystemPrompt,
        sectionUserPrompt,
        sectionLegalIssuesSystemPrompt,
        sectionLegalIssuesUserPrompt,
      }),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((err: Error) => {
        // console.error(err);
        return { success: false, error: err.message };
      });
  },
  setSupportFunctionsLLM: async function ({
    LLMProvider_SUPPORT,
    OpenAiModelPref_SUPPORT,
    AnthropicModelPref_SUPPORT,
    GeminiLLMModelPref_SUPPORT,
    supportFunctionPromptUpgrade,
    supportFunctionValidation,
    supportFunctionManualTime,
  }: SupportFunctionsParams): Promise<any> {
    return await fetch(`${API_BASE}/system/support-functions-llm`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({
        LLMProvider_SUPPORT,
        OpenAiModelPref_SUPPORT,
        AnthropicModelPref_SUPPORT,
        GeminiLLMModelPref_SUPPORT,
        supportFunctionPromptUpgrade,
        supportFunctionValidation,
        supportFunctionManualTime,
      }),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((err: Error) => {
        // console.error("Error updating support functions LLM:", err);
        return { success: false, error: err.message };
      });
  },
  submitLegalTask: async function ({
    name,
    subCategory,
    description,
    legalPrompt,
    legalTaskType,
  }: LegalTaskParams): Promise<any> {
    try {
      const response = await fetch(`${API_BASE}/categories`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...baseHeaders(),
        },
        body: JSON.stringify({
          name,
          sub_category: subCategory,
          description,
          legal_task_prompt: legalPrompt,
          legalTaskType: legalTaskType,
        }),
      });
      const data = (await response.json()) as {
        success?: boolean;
        data?: any;
        error?: string;
      };
      if (data.success) {
        return { success: true, data: data.data };
      } else {
        // console.error("Error creating legal task:", data.error);
        return { success: false, error: data.error };
      }
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : "Unknown error";
      // console.error("Error during legal task creation:", e);
      return { success: false, error: errorMessage };
    }
  },
  fetchLegalTasks: async function (): Promise<any> {
    try {
      const response = await fetch(`${API_BASE}/categories`, {
        method: "GET",
        headers: baseHeaders(),
      });
      const data = (await response.json()) as {
        success?: boolean;
        data?: any;
        error?: string;
      };
      if (data.success) {
        try {
          const useLegalTasksStore = (await import("@/stores/legalTasksStore"))
            .default;
          useLegalTasksStore.setState({
            legalTasks: data.data,
            loading: false,
            error: null,
          });
        } catch {
          // console.error("Error updating legal tasks store:", _storeError);
        }
      }
      return data;
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : "Unknown error";
      // console.error("Error during API call:", e);
      return { success: false, error: errorMessage };
    }
  },
  deleteCategory: async function (id: string): Promise<any> {
    try {
      const response = await fetch(`${API_BASE}/categories/${id}`, {
        method: "DELETE",
        headers: baseHeaders(),
      });

      const data = (await response.json()) as {
        success?: boolean;
        error?: string;
      };

      if (data.success) {
        return { success: true };
      } else {
        // console.error("Error deleting legal task:", data.error);
        return { success: false, error: data.error };
      }
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : "Unknown error";
      // console.error("Error during legal task deletion:", e);
      return { success: false, error: errorMessage };
    }
  },

  fetchGroupedLegalTasks: async function (): Promise<any> {
    // Import the store dynamically to avoid circular dependencies
    const useLegalTasksStore = (await import("@/stores/legalTasksStore"))
      .default;

    try {
      // First, ensure we have the latest data
      await useLegalTasksStore.getState().fetchLegalTasks();

      // Get unique categories from the store
      const categories = useLegalTasksStore.getState().getUniqueCategories();
      const formattedCategories = categories.map((name: string) => ({ name }));

      return { success: true, data: formattedCategories };
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      // console.error("Error fetching grouped legal tasks:", err);
      return { success: false, error: errorMessage };
    }
  },

  fetchSubCategories: async function (name: string): Promise<any> {
    // Import the store dynamically to avoid circular dependencies
    const useLegalTasksStore = (await import("@/stores/legalTasksStore"))
      .default;

    try {
      // First, ensure we have the latest data
      await useLegalTasksStore.getState().fetchLegalTasks();

      // Get tasks for the selected category from the store
      const tasks = useLegalTasksStore.getState().getLegalTasksByCategory(name);

      if (tasks && tasks.length > 0) {
        // Format the tasks to match the expected API response format
        const formattedTasks = tasks.map((task: any) => ({
          name: task.subCategory || "Default",
          description: task.description,
          legalPrompt: task.legalPrompt,
          legalTaskType: task.legalTaskType,
        }));

        return { success: true, data: formattedTasks };
      } else {
        return { success: true, data: [] };
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      // console.error("Error fetching subcategories:", err);
      return { success: false, error: errorMessage };
    }
  },

  generateLegalTaskPrompt: async function (
    taskDescription: string,
    promptTemplate: string | null = null,
    legalTaskType: string = "noMainDoc"
  ): Promise<any> {
    try {
      const response = await fetch(`${API_BASE}/generate-legal-task-prompt`, {
        method: "POST",
        headers: baseHeaders(),
        body: JSON.stringify({
          taskDescription,
          promptTemplate,
          legalTaskType,
        }),
      });

      if (!response.ok) {
        const errorData = (await response.json()) as { error?: string };
        throw new Error(
          errorData.error || "Failed to generate legal task prompt"
        );
      }

      const data = (await response.json()) as { prompt?: string };
      return { success: true, prompt: data.prompt };
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      // console.error("Error generating legal task prompt:", error);
      return { success: false, error: errorMessage };
    }
  },

  setQuraButton: async function (data: any): Promise<any> {
    return await fetch(`${API_BASE}/system/enable-qura`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(data),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },
  isQura: async function (): Promise<any> {
    try {
      const response = await fetch(`${API_BASE}/system/qura`, {
        method: "GET",
        headers: baseHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = (await response.json()) as { qura?: boolean };
      const isQura = data?.qura;

      const isQuraStr = typeof isQura === "boolean" ? String(isQura) : "false";
      window.localStorage.setItem("qura", isQuraStr);
      return { qura: isQura };
    } catch {
      // console.error(e);
      return { qura: false };
    }
  },
  setPromptOutputLogging: async function (data: any): Promise<any> {
    return await fetch(`${API_BASE}/system/prompt-output-logging`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(data),
    })
      .then((res) => {
        if (!res.ok)
          throw new Error("Could not update prompt output logging setting.");
        return res.json() as Promise<any>;
      })
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },

  isPromptOutputLoggingEnabled: async function (): Promise<boolean> {
    return await fetch(`${API_BASE}/system/prompt-output-logging`, {
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok)
          throw new Error("Could not fetch prompt output logging status.");
        return res.json() as Promise<any>;
      })
      .then((res) => res.enabled)
      .catch(() => false);
  },

  getDeepSearchSettings: async function (): Promise<any> {
    return await fetch(`${API_BASE}/admin/system/deep-search-settings`, {
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not fetch DeepSearch settings.");
        return res.json() as Promise<any>;
      })
      .catch((_e: Error) => {
        // console.error(_e);
        return null;
      });
  },

  updateDeepSearchSettings: async function (settings: any): Promise<any> {
    return await fetch(`${API_BASE}/admin/system/deep-search-settings`, {
      method: "PUT",
      headers: baseHeaders(),
      body: JSON.stringify(settings),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not update DeepSearch settings.");
        return res.json() as Promise<any>;
      })
      .catch((e: Error) => {
        // console.error(e);
        throw e;
      });
  },
  getCopyOption: async function (): Promise<boolean> {
    return await fetch(`${API_BASE}/system/copyOption`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<any>)
      .then((res) => {
        // Handle both direct boolean response and boolean value
        return typeof res === "boolean" ? res : false;
      })
      .catch((_e: Error) => {
        // console.error(_e);
        return false;
      });
  },
  getPerformLegalTask: async function (): Promise<any> {
    try {
      const response = await fetch(`${API_BASE}/system/perform-legal-task`, {
        method: "GET",
        headers: baseHeaders(),
      });

      if (!response.ok) {
        throw new Error(
          "There was an issue with the server. Please try again later."
        );
      }

      const data = (await response.json()) as {
        enabled?: boolean;
        allowUserAccess?: boolean;
      };
      const isPerformLegalTaskEnabled = data?.enabled;
      const allowUserAccess = data?.allowUserAccess;
      return { enabled: isPerformLegalTaskEnabled, allowUserAccess };
    } catch {
      // console.error("Error fetching perform legal task setting:", e.message);
      return { enabled: false, allowUserAccess: false };
    }
  },
  setPerformLegalTask: async function (data: any): Promise<any> {
    return await fetch(`${API_BASE}/system/perform-legal-task`, {
      method: "POST",
      headers: {
        ...baseHeaders(),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    })
      .then(async (res) => {
        const responseData = (await res.json()) as any;
        if (!res.ok) {
          return {
            success: false,
            error: responseData.error || "Request failed",
          };
        }
        return responseData;
      })
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },
  getCanvasChat: async function (): Promise<any> {
    try {
      const response = await fetch(`${API_BASE}/system/canvas-chat`, {
        method: "GET",
        headers: baseHeaders(),
      });

      if (!response.ok) {
        throw new Error(
          "There was an issue with the server. Please try again later."
        );
      }

      const data = (await response.json()) as { enabled?: boolean };
      const isCanvasChatEnabled = data?.enabled;
      return { enabled: isCanvasChatEnabled };
    } catch {
      // console.error("Error fetching canvas chat setting:", e.message);
      return { enabled: false };
    }
  },
  setCanvasChat: async function (enabled: boolean): Promise<any> {
    return await fetch(`${API_BASE}/system/canvas-chat`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(enabled),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },
  submitFeedback: async function (data: any): Promise<any> {
    try {
      // Validate file size before upload (5MB limit)
      if (data.file && data.file.size > 5 * 1024 * 1024) {
        return {
          success: false,
          error: "File size exceeds 5MB limit",
        };
      }

      // Validate required fields
      if (!data.fullName?.trim() || !data.message?.trim()) {
        return {
          success: false,
          error: "Full name and message are required",
        };
      }

      const formData = new FormData();
      formData.append("fullName", data.fullName.trim());
      formData.append("message", data.message.trim());
      if (data.file) {
        formData.append("file", data.file);
      }

      const headers = { ...baseHeaders() } as Record<string, string>;
      delete headers["Content-Type"];

      const response = await fetch(`${API_BASE}/system/feedback`, {
        method: "POST",
        headers,
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.text();
        try {
          const jsonError = JSON.parse(errorData);
          throw new Error(jsonError.error || "Failed to submit feedback");
        } catch {
          throw new Error(
            `Server error (${response.status}): ${errorData || response.statusText}`
          );
        }
      }

      return { success: true };
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : "Unknown error";
      // console.error("Error submitting feedback:", e);
      return {
        success: false,
        error: errorMessage || "Failed to submit feedback. Please try again.",
      };
    }
  },
  getFeedback: async function (): Promise<any> {
    try {
      const response = await fetch(`${API_BASE}/system/feedback`, {
        method: "GET",
        headers: baseHeaders(),
      });
      if (!response.ok) {
        const errorData = (await response.json()) as { message?: string };
        throw new Error(errorData.message || "Failed to get feedback");
      }
      const responseData = (await response.json()) as {
        success?: boolean;
        feedback?: any[];
        message?: string;
      };
      if (!responseData.success) {
        throw new Error(responseData.message || "Failed to get feedback");
      }
      return { success: true, feedback: responseData.feedback };
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : "Unknown error";
      return { success: false, error: errorMessage };
    }
  },
  deleteFeedback: async function (id: string): Promise<any> {
    try {
      const response = await fetch(`${API_BASE}/system/feedback/${id}`, {
        method: "DELETE",
        headers: baseHeaders(),
      });
      if (!response.ok) {
        const errorData = (await response.json()) as { message?: string };
        throw new Error(errorData.message || "Failed to delete feedback");
      }
      const responseData = (await response.json()) as {
        success?: boolean;
        message?: string;
      };
      if (!responseData.success) {
        throw new Error(responseData.message || "Failed to delete feedback");
      }
      return { success: true };
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : "Unknown error";
      // console.error("Error deleting feedback:", e.message);
      return { success: false, error: errorMessage };
    }
  },
  setCitationButton: async function (data: any): Promise<any> {
    return await fetch(`${API_BASE}/system/citation`, {
      method: "POST",
      headers: {
        ...baseHeaders(),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },
  isCitationButton: async function (): Promise<boolean> {
    return await fetch(`${API_BASE}/system/citation`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<any>)
      .then(function (res) {
        window.localStorage.setItem("citationEnabled", res?.citation);
        return res?.citation;
      })
      .catch((_e: Error) => {
        // console.error(e);
        window.localStorage.setItem("citationEnabled", "true");
        return true;
      });
  },
  fetchCustomWebsiteLink: async function (): Promise<any> {
    try {
      const res = await fetch(`${API_BASE}/system/get-website-link`, {
        method: "GET",
        headers: baseHeaders(),
      });

      if (!res.ok) {
        // const _errorText = await res.text();
        // console.error("Fetch error response:", _errorText);
        throw new Error(`HTTP error! status: ${res.status}`);
      }

      const data = (await res.json()) as {
        websiteLink?: string;
        displayText?: string;
      };

      return {
        websiteLink: data?.websiteLink || "",
        displayText: data?.displayText || "Copyright IST 2024",
      };
    } catch {
      // console.error("Error fetching website link:", e);
      return { websiteLink: "", displayText: "Copyright IST 2024" }; // Ensure default displayText
    }
  },
  updateCustomWebsite: async function (data: any): Promise<any> {
    if (!data.websiteLink || !data.displayText) {
      // console.error("Missing required fields: websiteLink or displayText");
      return {
        success: false,
        error: "Missing required fields: websiteLink or displayText",
      };
    }

    try {
      // Send the API request
      const response = await fetch(`${API_BASE}/system/custom-website-link`, {
        method: "POST",
        headers: baseHeaders(),
        body: JSON.stringify({
          websiteLink: data.websiteLink,
          displayText: data.displayText,
        }),
      });

      // Handle non-OK responses from the API
      if (!response.ok) {
        // const _errorText = await response.text();
        // console.error("Update error response:", _errorText);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = (await response.json()) as {
        success?: boolean;
        error?: string;
      };
      return { success: result.success, error: result.error || null };
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      // console.error("Failed to update website link:", error.message);
      return {
        success: false,
        error: errorMessage || "Failed to update website link",
      };
    }
  },
  isMultiUserMode: async function (): Promise<boolean> {
    return await fetch(`${API_BASE}/system/multi-user-mode`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<any>)
      .then((res) => res?.multiUserMode)
      .catch((_e: Error) => {
        // console.error(e);
        return false;
      });
  },
  isPublicUserMode: async function (): Promise<boolean> {
    return await fetch(`${API_BASE}/system/public-user-mode`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<any>)
      .then((res) => res?.publicUserMode)
      .catch((_e: Error) => {
        // console.error(e);
        return false;
      });
  },
  getDocumentDrafting: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/document-drafting`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<any>)
      .then((res) => {
        const isDocumentDrafting = res?.isDocumentDrafting;
        const isDocumentDraftingLinking = res?.isDocumentDraftingLinking;
        window.localStorage.setItem("document-drafting", isDocumentDrafting);
        window.localStorage.setItem(
          "document-drafting-linking",
          isDocumentDraftingLinking
        );
        return res || {};
      })
      .catch((_e: Error) => {
        // console.error(e);
        return false;
      });
  },
  isDocumentDrafting: async function (): Promise<boolean> {
    return await fetch(`${API_BASE}/system/document-drafting`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<any>)
      .then((res) => {
        const isDocumentDrafting = res?.isDocumentDrafting;
        window.localStorage.setItem("document-drafting", isDocumentDrafting);
        return isDocumentDrafting;
      })
      .catch((_e: Error) => {
        // console.error(e);
        return false;
      });
  },
  getDocumentDraftingPrompt: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/get-document-drafting-prompt`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<any>)
      .then((res) => {
        return {
          documentDraftingPrompt: res?.documentDraftingPrompt || "",
          legalIssuesPrompt: res?.legalIssuesPrompt || "",
          memoPrompt: res?.memoPrompt || "",
          combinePrompt: res?.combinePrompt || "",
          defaultCombinePrompt: res?.defaultCombinePrompt || "",
          defaultDocumentDraftingPrompt:
            res?.defaultDocumentDraftingPrompt || "",
          defaultLegalIssuesPrompt: res?.defaultLegalIssuesPrompt || "",
          defaultMemoPrompt: res?.defaultMemoPrompt || "",
        };
      })
      .catch((_e: Error) => {
        // console.error(e);
        return {
          documentDraftingPrompt: "",
          legalIssuesPrompt: "",
          memoPrompt: "",
          combinePrompt: "",
          defaultCombinePrompt: "",
          defaultDocumentDraftingPrompt: "",
          defaultLegalIssuesPrompt: "",
          defaultMemoPrompt: "",
        };
      });
  },
  getDefaultSettings: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/get-default-settings`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<any>)
      .then((res) => ({
        // Actual prompt values
        systemPrompt: res?.systemPrompt || "",
        vectorSearchTopN: res?.vectorSearchTopN
          ? Number(res.vectorSearchTopN)
          : null,
        validationPrompt: res?.validationPrompt || "",
        canvasSystemPrompt: res?.canvasSystemPrompt || "",
        canvasUploadSystemPrompt: res?.canvasUploadSystemPrompt || "",
        manualWorkEstimatorPrompt: res?.manualWorkEstimatorPrompt || "",
        styleGenerationPrompt: res?.styleGenerationPrompt || "",
        autoCodingPromptTemplate: res?.autoCodingPromptTemplate || "",

        // Default prompt values for placeholders
        defaultSystemPrompt: res?.defaultSystemPrompt || "",
        defaultVectorSearchTopN: res?.defaultVectorSearchTopN || "",
        defaultValidationPrompt: res?.defaultValidationPrompt || "",
        defaultCanvasSystemPrompt: res?.defaultCanvasSystemPrompt || "",
        defaultCanvasUploadSystemPrompt:
          res?.defaultCanvasUploadSystemPrompt || "",
        defaultManualWorkEstimatorPrompt:
          res?.defaultManualWorkEstimatorPrompt || "",
        defaultStyleGenerationPrompt: res?.defaultStyleGenerationPrompt || "",
        defaultAutoCodingPromptTemplate:
          res?.defaultAutoCodingPromptTemplate || "",
      }))
      .catch((_e: Error) => {
        // console.error(e);
        return {
          // Actual prompt values
          systemPrompt: "",
          vectorSearchTopN: null,
          validationPrompt: "",
          canvasSystemPrompt: "",
          canvasUploadSystemPrompt: "",
          manualWorkEstimatorPrompt: "",
          styleGenerationPrompt: "",
          autoCodingPromptTemplate: "",

          // Default prompt values for placeholders
          defaultSystemPrompt: "",
          defaultVectorSearchTopN: "",
          defaultValidationPrompt: "",
          defaultCanvasSystemPrompt: "",
          defaultCanvasUploadSystemPrompt: "",
          defaultManualWorkEstimatorPrompt: "",
          defaultStyleGenerationPrompt: "",
          defaultAutoCodingPromptTemplate: "",
        };
      });
  },
  getDocumentBuilder: async function (
    _options: Record<string, any> = {}
  ): Promise<any> {
    // Modified: options no longer used for endpoint path
    // This function now fetches the structured list of prompts from the new endpoint.
    // The old `options` for flowType is not used by this new endpoint directly.
    // If flow-specific filtering is needed on frontend, it would be done after fetching all prompts.
    return await fetch(`${API_BASE}/system/document-builder-prompts`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<any>)
      .then((res) => {
        if (!res.success)
          throw new Error(
            res.error || "Failed to fetch document builder prompts config"
          );
        // The actual list of prompts is in res.prompts
        return res.prompts || [];
      })
      .catch((_e: Error) => {
        // console.error(
        //   "Error in getDocumentBuilder (fetching prompts config):",
        //   e
        // );
        // showToast should be handled by the component calling this, if needed.
        return []; // Return empty array on error
      });
  },
  deleteDocument: async function (name: string): Promise<boolean> {
    return await fetch(`${API_BASE}/system/remove-document`, {
      method: "DELETE",
      headers: baseHeaders(),
      body: JSON.stringify({ name }),
    })
      .then((res) => res.ok)
      .catch((_e: Error) => {
        // console.error(e);
        return false;
      });
  },
  deleteDocuments: async function (names: string[] = []): Promise<any> {
    return await fetch(`${API_BASE}/system/remove-documents`, {
      method: "DELETE",
      headers: baseHeaders(),
      body: JSON.stringify({ names }),
    })
      .then(async (res) => {
        const data = await res.json();
        if (res.status === 400) {
          // console.error("Invalid request:", data.error);
          return { success: false, error: data.error };
        }
        if (res.status === 500) {
          // console.error("Server error:", data.error, data.details);
          return { success: false, error: data.error, details: data.details };
        }
        if (res.status === 207) {
          // console.warn(
          //   "Partial success:",
          //   data.message,
          //   "Errors:",
          //   data.errors
          // );
          return {
            success: true,
            message: data.message,
            results: data.results,
            errors: data.errors,
          };
        }
        return { success: true, message: data.message, results: data.results };
      })
      .catch((_e: Error) => {
        // console.error("Network error during document deletion:", e);
        return {
          success: false,
          error: "Network error during document deletion",
        };
      });
  },
  deleteFolder: async function (name: string, slug: string): Promise<boolean> {
    return await fetch(`${API_BASE}/system/remove-folder`, {
      method: "DELETE",
      headers: baseHeaders(),
      body: JSON.stringify({ name, slug }),
    })
      .then((res) => res.ok)
      .catch((_e: Error) => {
        // console.error(e);
        return false;
      });
  },
  uploadPfp: async function (formData: FormData): Promise<any> {
    return await fetch(`${API_BASE}/system/upload-pfp`, {
      method: "POST",
      body: formData,
      headers: baseHeaders(null, false),
    }).then((res) => res.json() as Promise<any>);
  },
  uploadLogo: async function (formData: FormData): Promise<any> {
    const token = window.localStorage.getItem(AUTH_TOKEN);
    const headers: Record<string, string> = {};
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    return await fetch(`${API_BASE}/system/upload-logo`, {
      method: "POST",
      body: formData,
      headers: headers,
    }).then((res) => res.json() as Promise<any>);
  },
  uploadLogoDark: async function (formData: FormData): Promise<any> {
    const token = window.localStorage.getItem(AUTH_TOKEN);
    const headers: Record<string, string> = {};
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    return await fetch(`${API_BASE}/system/upload-logo-dark`, {
      method: "POST",
      body: formData,
      headers: headers,
    }).then((res) => res.json() as Promise<any>);
  },
  fetchCustomFooterIcons: async function (): Promise<any> {
    const cache = window.localStorage.getItem(this.cacheKeys.footerIcons);
    const parsedCache = cache
      ? safeJsonParse(cache, { data: [], lastFetched: 0 })
      : null;
    if (!parsedCache) return { footerData: [], error: null };
    const { data, lastFetched } = parsedCache;

    if (!!data && Date.now() - lastFetched < 3_600_000)
      return { footerData: data, error: null };

    const { footerData, error } = await fetch(
      `${API_BASE}/system/footer-data`,
      {
        method: "GET",
        cache: "no-cache",
        headers: baseHeaders(),
      }
    )
      .then((res) => res.json() as Promise<any>)
      .catch((err: Error) => {
        // console.log(err);
        return { footerData: [], error: err.message };
      });

    if (!footerData || !!error) return { footerData: [], error: null };

    const newData = safeJsonParse(footerData, []);
    window.localStorage.setItem(
      this.cacheKeys.footerIcons,
      JSON.stringify({ data: newData, lastFetched: Date.now() })
    );
    return { footerData: newData, error: null };
  },
  fetchSupportEmail: async function (): Promise<any> {
    const cache = window.localStorage.getItem(this.cacheKeys.supportEmail);
    const parsedCache = cache
      ? safeJsonParse(cache, { email: "", lastFetched: 0 })
      : null;
    if (!parsedCache) return { email: "", error: null };
    const { email, lastFetched } = parsedCache;

    if (!!email && Date.now() - lastFetched < 3_600_000)
      return { email: email, error: null };

    const { supportEmail, error } = await fetch(
      `${API_BASE}/system/support-email`,
      {
        method: "GET",
        cache: "no-cache",
        headers: baseHeaders(),
      }
    )
      .then((res) => res.json() as Promise<any>)
      .catch((err: Error) => {
        // console.log(err);
        return { email: "", error: err.message };
      });

    if (!supportEmail || !!error) return { email: "", error: null };
    window.localStorage.setItem(
      this.cacheKeys.supportEmail,
      JSON.stringify({ email: supportEmail, lastFetched: Date.now() })
    );
    return { email: supportEmail, error: null };
  },

  fetchCustomAppName: async function (): Promise<any> {
    const cache = window.localStorage.getItem(this.cacheKeys.customAppName);
    const parsedCache = cache
      ? safeJsonParse(cache, { appName: "", lastFetched: 0 })
      : null;
    if (!parsedCache) return { appName: "", error: null };
    const { appName, lastFetched } = parsedCache;

    if (!!appName && Date.now() - lastFetched < 3_600_000)
      return { appName: appName, error: null };

    const { customAppName, error } = await fetch(
      `${API_BASE}/system/custom-app-name`,
      {
        method: "GET",
        cache: "no-cache",
        headers: baseHeaders(),
      }
    )
      .then((res) => res.json() as Promise<any>)
      .catch((err: Error) => {
        // console.log(err);
        return { customAppName: "", error: err.message };
      });

    if (!customAppName || !!error) {
      window.localStorage.removeItem(this.cacheKeys.customAppName);
      return { appName: "", error: null };
    }

    window.localStorage.setItem(
      this.cacheKeys.customAppName,
      JSON.stringify({ appName: customAppName, lastFetched: Date.now() })
    );
    return { appName: customAppName, error: null };
  },

  fetchCustomParagraph: async function (): Promise<any> {
    const cache = window.localStorage.getItem(this.cacheKeys.customParagraph);
    const { paragraphText, lastFetched } = cache
      ? JSON.parse(cache)
      : { paragraphText: null, lastFetched: 0 };

    if (paragraphText && Date.now() - lastFetched < 3_600_000)
      return { paragraphText, error: null };

    return await fetch(`${API_BASE}/system/custom-paragraph`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not fetch custom paragraph.");
        return res.json() as Promise<any>;
      })
      .then((res) => {
        const newParagraphText = res.customParagraphText || null;
        window.localStorage.setItem(
          this.cacheKeys.customParagraph,
          JSON.stringify({
            paragraphText: newParagraphText,
            lastFetched: Date.now(),
          })
        );
        return { paragraphText: newParagraphText, error: null };
      })
      .catch((e: Error) => {
        // console.error(e);
        return { paragraphText: null, error: e.message };
      });
  },

  fetchPfp: async function (id: string): Promise<any> {
    return await fetch(`${API_BASE}/system/pfp/${id}`, {
      method: "GET",
      cache: "no-cache",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (res.ok && res.status !== 204) return res.blob();
        throw new Error("Failed to fetch pfp.");
      })
      .then((blob) => (blob ? URL.createObjectURL(blob) : null))
      .catch((_e: Error) => {
        // console.log(e);
        return null;
      });
  },
  removePfp: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/remove-pfp`, {
      method: "DELETE",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (res.ok) return { success: true, error: null };
        throw new Error("Failed to remove pfp.");
      })
      .catch((e: Error) => {
        // console.log(e);
        return { success: false, error: e.message };
      });
  },
  fetchLogo: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/logo`, {
      method: "GET",
      cache: "no-cache",
      headers: baseHeaders(),
    })
      .then(async (res) => {
        if (res.ok && res.status !== 204) {
          const isCustomLogo = res.headers.get("X-Is-Custom-Logo") === "true";
          const blob = await res.blob();
          const logoURL = URL.createObjectURL(blob);
          return { isCustomLogo, logoURL };
        }
        // 204 No Content is a valid response when no custom logo is set
        if (res.status === 204) {
          return { isCustomLogo: false, logoURL: null };
        }
        throw new Error("Failed to fetch logo!");
      })
      .catch((e: Error) => {
        // Only log actual errors, not expected 204 responses
        if (!e.message.includes("Failed to fetch logo!")) {
          // console.log(e);
        }
        return { isCustomLogo: false, logoURL: null };
      });
  },
  fetchLogoDark: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/logo-dark`, {
      method: "GET",
      cache: "no-cache",
      headers: baseHeaders(),
    })
      .then(async (res) => {
        if (res.ok && res.status !== 204) {
          const isCustomDarkModeLogo =
            res.headers.get("X-Is-Custom-Logo") === "true";
          const blob = await res.blob();
          const darkModeLogoURL = URL.createObjectURL(blob);
          return { isCustomDarkModeLogo, darkModeLogoURL };
        }
        // 204 No Content is a valid response when no custom logo is set
        if (res.status === 204) {
          return { isCustomDarkModeLogo: false, darkModeLogoURL: null };
        }
        throw new Error("Failed to fetch dark mode logo!");
      })
      .catch((e: Error) => {
        // Only log actual errors, not expected 204 responses
        if (!e.message.includes("Failed to fetch dark mode logo!")) {
          // console.log(e);
        }
        return { isCustomDarkModeLogo: false, darkModeLogoURL: null };
      });
  },
  isDefaultLogo: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/is-default-logo`, {
      method: "GET",
      cache: "no-cache",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Failed to get is default logo!");
        return res.json() as Promise<any>;
      })
      .then((res) => res?.isDefaultLogo)
      .catch((_e: Error) => {
        // console.log(e);
        return null;
      });
  },
  isDefaultDarkModeLogo: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/is-default-logo-dark`, {
      method: "GET",
      cache: "no-cache",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Failed to get is default logo!");
        return res.json() as Promise<any>;
      })
      .then((res) => res?.isDefaultLogo)
      .catch((_e: Error) => {
        // console.log(e);
        return null;
      });
  },
  removeCustomLogo: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/remove-logo`, {
      headers: baseHeaders(),
    })
      .then((res) => {
        if (res.ok) return { success: true, error: null };
        throw new Error("Error removing logo!");
      })
      .catch((e: Error) => {
        // console.log(e);
        return { success: false, error: e.message };
      });
  },
  removeCustomLogoDark: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/remove-logo-dark`, {
      headers: baseHeaders(),
    })
      .then((res) => {
        if (res.ok) return { success: true, error: null };
        throw new Error("Error removing logo!");
      })
      .catch((e: Error) => {
        // console.log(e);
        return { success: false, error: e.message };
      });
  },
  getWelcomeMessages: async function (): Promise<any> {
    try {
      const response = await fetch(`${API_BASE}/system/welcome-messages`, {
        method: "GET",
        headers: baseHeaders(),
      });

      if (!response.ok) {
        throw new Error("Could not fetch welcome messages.");
      }

      const messages = await response.json();
      return messages;
    } catch {
      // console.error("Error fetching welcome messages:", error);
      return { heading: "", text: "" };
    }
  },

  getOrganizationUsers: async function (): Promise<any> {
    try {
      const response = await fetch(`${API_BASE}/system/organization-users`, {
        method: "GET",
        headers: baseHeaders(),
      });

      if (!response.ok) {
        throw new Error("Could not fetch organization users.");
      }

      return (await response.json()) as any;
    } catch {
      // console.error("Error fetching organization users:", error);
      return { users: [] };
    }
  },
  getPromptExamples: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/prompt-examples`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not fetch prompt examples.");
        return res.json() as Promise<any>;
      })
      .catch(() => {
        return [];
      });
  },
  setPromptExamples: async function (examples: any[]): Promise<any> {
    return fetch(`${API_BASE}/system/set-prompt-examples`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(examples),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not save prompt examples.");
        return res.json() as Promise<any>;
      })
      .catch((e: Error) => {
        return { success: false, error: e.message };
      });
  },
  setWelcomeMessages: async function (messages: any[]): Promise<any> {
    try {
      const response = await fetch(`${API_BASE}/system/set-welcome-messages`, {
        method: "POST",
        headers: baseHeaders(),
        body: JSON.stringify(messages),
      });

      if (!response.ok) {
        throw new Error("Could not save welcome messages.");
      }

      const result = (await response.json()) as any;
      return { success: true, ...result };
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      // console.error("Error saving welcome messages:", error);
      return { success: false, error: errorMessage };
    }
  },
  getApiKeys: async function (): Promise<any> {
    return fetch(`${API_BASE}/system/api-keys`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText || "Error fetching api key.");
        }
        return res.json() as Promise<any>;
      })
      .catch((e: Error) => {
        // console.error(e);
        return { apiKey: null, error: e.message };
      });
  },
  generateApiKey: async function (): Promise<any> {
    return fetch(`${API_BASE}/system/generate-api-key`, {
      method: "POST",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText || "Error generating api key.");
        }
        return res.json() as Promise<any>;
      })
      .catch((e: Error) => {
        // console.error(e);
        return { apiKey: null, error: e.message };
      });
  },
  deleteApiKey: async function (): Promise<boolean> {
    return fetch(`${API_BASE}/system/api-key`, {
      method: "DELETE",
      headers: baseHeaders(),
    })
      .then((res) => res.ok)
      .catch((_e: Error) => {
        // console.error(e);
        return false;
      });
  },
  customModels: async function (
    provider: string,
    apiKey: string | null = null,
    basePath: string | null = null,
    timeout: number | null = null
  ): Promise<any> {
    const controller = new AbortController();
    if (timeout) {
      setTimeout(() => {
        controller.abort("Request timed out.");
      }, timeout);
    }

    // Use POST if we have a provider, apiKey, or basePath to send
    // Use GET only if we want to fetch all existing models without any parameters
    if (provider || apiKey || basePath) {
      return fetch(`${API_BASE}/system/custom-models`, {
        method: "POST",
        headers: baseHeaders(),
        signal: controller.signal,
        body: JSON.stringify({
          provider,
          apiKey,
          basePath,
        }),
      })
        .then(async (res) => {
          if (!res.ok) {
            const errorData = await res
              .json()
              .catch(() => ({ error: "Unknown error" }));
            console.error("Custom models error:", errorData);
            throw new Error(
              errorData.error ||
                res.statusText ||
                "Error finding custom models."
            );
          }
          return res.json() as Promise<any>;
        })
        .catch((e: Error) => {
          return { models: [], error: e.message };
        });
    } else {
      // Just fetch existing custom models with no parameters
      return fetch(`${API_BASE}/system/custom-models`, {
        method: "GET",
        headers: baseHeaders(),
        signal: controller.signal,
      })
        .then((res) => {
          if (!res.ok) {
            throw new Error(res.statusText || "Error finding custom models.");
          }
          return res.json() as Promise<any>;
        })
        .catch((e: Error) => {
          return { models: [], error: e.message };
        });
    }
  },
  chats: async function (
    offset: number = 0,
    filters: Record<string, any> = {}
  ): Promise<any> {
    return await fetch(`${API_BASE}/system/workspace-chats`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ offset, filters }),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((_e: Error) => {
        // console.error(e);
        return [];
      });
  },
  totalChatCount: async function (): Promise<number> {
    return await fetch(`${API_BASE}/system/workspace-chats-count`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<any>)
      .then((data) => data.totalChats || 0)
      .catch((_e: Error) => {
        // console.error(e);
        return 0;
      });
  },
  eventLogs: async function (offset: number = 0): Promise<any> {
    return await fetch(`${API_BASE}/system/event-logs`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ offset }),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((_e: Error) => {
        // console.error(e);
        return [];
      });
  },
  clearEventLogs: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/event-logs`, {
      method: "DELETE",
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },
  deleteChat: async function (chatId: string): Promise<any> {
    return await fetch(`${API_BASE}/system/workspace-chats/${chatId}`, {
      method: "DELETE",
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<any>)
      .then((data) => {
        if (data.deletedCount && data.totalDeleted) {
          fetch(`${API_BASE}/setup-complete`, {
            method: "GET",
            headers: baseHeaders(),
          }).catch(() => {
            // console.error("Failed to refresh settings after chat deletion:", e)
          });
        }
        return data;
      })
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },
  deleteOldChats: async function (
    timeframe: string,
    value: number
  ): Promise<any> {
    return await fetch(`${API_BASE}/system/delete-old-chats`, {
      method: "DELETE",
      headers: baseHeaders(),
      body: JSON.stringify({ timeframe, value }),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((e: Error) => {
        // console.error(e);
        return {
          success: false,
          error: e.message,
          deletedCount: 0,
          totalDeleted: 0,
        };
      });
  },
  getChatDeletionCount: async function (): Promise<number> {
    return await fetch(`${API_BASE}/setup-complete`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<any>)
      .then((data) => {
        const settings = data.results || {};
        return parseInt(settings.chat_deletion_count || "0", 10);
      })
      .catch((_e: Error) => {
        // console.error(e);
        return 0;
      });
  },
  exportChats: async function (
    type: string = "csv",
    chatType: string = "workspace",
    filters: Record<string, any> = {}
  ): Promise<string | null> {
    const queryParams = new URLSearchParams({
      type,
      chatType,
      filters: JSON.stringify({
        username: filters.user,
        referenceNumber: filters.referenceNumber,
        startDate: filters.startDate,
        endDate: filters.endDate,
      }),
    });

    try {
      const response = await fetch(
        `${API_BASE}/v1/system/export-chats?${queryParams}`,
        {
          headers: baseHeaders(),
        }
      );

      if (!response.ok) {
        // For 502 and other error responses that might return HTML
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("text/html")) {
          throw new Error("Server error. Please try again later.");
        }

        const error = await response.json().catch(() => ({
          message: "Failed to export chats",
        }));
        throw new Error(error.message);
      }

      return await response.text();
    } catch {
      // console.error("Error exporting chats:", error);
      return null;
    }
  },
  updateUser: async function (data: any): Promise<any> {
    return await fetch(`${API_BASE}/system/user`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(data),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },
  getCurrentUser: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/me`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not fetch current user data.");
        return res.json() as Promise<any>;
      })
      .catch((_e: Error) => {
        // console.error(e);
        return null;
      });
  },
  dataConnectors: DataConnector,

  getSlashCommandPresets: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/slash-command-presets`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not fetch slash command presets.");
        return res.json() as Promise<any>;
      })
      .then((res) => res.presets)
      .catch((_e: Error) => {
        // console.error(e);
        return [];
      });
  },

  createSlashCommandPreset: async function (presetData: any): Promise<any> {
    return await fetch(`${API_BASE}/system/slash-command-presets`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(presetData),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not create slash command preset.");
        return res.json() as Promise<any>;
      })
      .then((res) => {
        return { preset: res.preset, error: null };
      })
      .catch((e: Error) => {
        // console.error(e);
        return { preset: null, error: e.message };
      });
  },

  updateSlashCommandPreset: async function (
    presetId: any,
    presetData: any
  ): Promise<any> {
    return await fetch(`${API_BASE}/system/slash-command-presets/${presetId}`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(presetData),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not update slash command preset.");
        return res.json() as Promise<any>;
      })
      .then((res) => {
        return { preset: res.preset, error: null };
      })
      .catch(() => {
        return { preset: null, error: "Failed to update this command." };
      });
  },

  deleteSlashCommandPreset: async function (
    presetId: string
  ): Promise<boolean> {
    return await fetch(`${API_BASE}/system/slash-command-presets/${presetId}`, {
      method: "DELETE",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not delete slash command preset.");
        return true;
      })
      .catch((_e: Error) => {
        // console.error(e);
        return false;
      });
  },

  validateResponse: async function ({
    answer,
    chatId,
    llmInput,
  }: {
    answer: any;
    chatId: any;
    llmInput: any;
  }): Promise<any> {
    return await fetch(`${API_BASE}/system/validate-answer`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({
        answer,
        chatId,
        llmInput,
      }),
    })
      .then(async (res) => {
        if (!res.ok) {
          const error = await res.json();
          throw new Error(error.message || "Failed to validate response");
        }
        return res.json() as Promise<any>;
      })
      .catch((e: Error) => {
        return {
          success: false,
          error: e.message,
          validationResult: null,
        };
      });
  },

  estimateManualWork: async function ({
    question,
    answer,
  }: {
    question: string;
    answer: string;
  }): Promise<any> {
    return await fetch(`${API_BASE}/system/manual-work-estimate`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ question, answer }),
    })
      .then(async (res) => {
        if (!res.ok) {
          const error = await res.json();
          throw new Error(error.error || "Failed to estimate manual work");
        }
        return res.json() as Promise<any>;
      })
      .catch((e: Error) => {
        return { success: false, error: e.message };
      });
  },

  experimentalFeatures: {
    liveSync: LiveDocumentSync,
    agentPlugins: AgentPlugins,
  },

  cleanupTemporaryAttachment: async function (location: any) {
    try {
      const response = await fetch(`${API_BASE}/document/attachment-cleanup`, {
        method: "POST",
        headers: {
          ...baseHeaders(),
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ location }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to cleanup attachment");
      }

      return { success: true, error: null };
    } catch (error: any) {
      // console.error("Error in cleanupTemporaryAttachment:", error);
      return { success: false, error: error.message };
    }
  },
  updatePromptTemplate: async function (data: any): Promise<any> {
    return await fetch(`${API_BASE}/system/prompt-template`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(data),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },

  getPromptUpgradeTemplate: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/prompt-template`, {
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<any>)
      .then((res) => res.template)
      .catch((_e: Error) => {
        // console.error(e);
        return "";
      });
  },

  updateAutoCodingTemplate: async function (data: any): Promise<any> {
    return await fetch(`${API_BASE}/system/auto-coding-template`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(data),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },

  getAutoCodingTemplate: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/auto-coding-template`, {
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<any>)
      .then((res) => res.template)
      .catch((_e: Error) => {
        // console.error(e);
        return "";
      });
  },

  updateLegalTask: async function ({
    id,
    subCategory,
    description,
    legalPrompt,
    legalTaskType,
  }: {
    id: any;
    subCategory: any;
    description: any;
    legalPrompt: any;
    legalTaskType: any;
  }): Promise<any> {
    try {
      // Make a direct API call to update a legal task instead of using the store
      const response = await fetch(`${API_BASE}/categories/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          ...baseHeaders(),
        },
        body: JSON.stringify({
          sub_category: subCategory,
          description,
          legal_task_prompt: legalPrompt,
          legalTaskType: legalTaskType,
        }),
      });

      const data = await response.json();

      if (data.success) {
        return { success: true, data: data.data };
      } else {
        // console.error("Error updating legal task:", data.error);
        return { success: false, error: data.error };
      }
    } catch (error: any) {
      // console.error("Error updating legal task:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  },

  customAiSettings: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/custom-ai-settings`, {
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not fetch custom AI settings.");
        return res.json() as Promise<any>;
      })
      .then((res) => res.settings || {})
      .catch(() => {
        // console.error("Error fetching custom AI settings:", error);
        return {};
      });
  },

  userCustomAiSettings: async function (): Promise<any> {
    return await fetch(`${API_BASE}/user/custom-ai-settings`, {
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok)
          throw new Error("Could not fetch user custom AI settings.");
        return res.json() as Promise<any>;
      })
      .then((res) => res.settings || {})
      .catch(() => {
        // console.error("Error fetching user custom AI settings:", error);
        return {};
      });
  },

  updateUserCustomAiSettings: async function (data: any): Promise<any> {
    return await fetch(`${API_BASE}/user/custom-ai-settings`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(data),
    })
      .then((res) => res.json() as Promise<any>)
      .catch((error) => {
        // console.error("Error updating user custom AI settings:", error);
        return { success: false, error: error.message };
      });
  },

  updatePreferences: async function (
    updates: Record<string, any> = {}
  ): Promise<any> {
    return await fetch(`${API_BASE}/system/preferences`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(updates),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not update system preferences.");
        return res.json() as Promise<any>;
      })
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },

  // Custom Legal Templates methods (settings-backed)
  fetchCustomLegalTemplates: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/custom-legal-templates`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not fetch custom legal templates.");
        return res.json() as Promise<any>;
      })
      .then((body) => ({
        success: body.success,
        data: body.data || [],
        error: body.error,
      }))
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message, data: [] };
      });
  },

  fetchCustomLegalTemplateCategories: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/system-legal-template-categories`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok)
          throw new Error("Could not fetch custom legal template categories.");
        return res.json() as Promise<any>;
      })
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message, data: [] };
      });
  },

  createCustomLegalTemplate: async function (templateData: any): Promise<any> {
    return await fetch(`${API_BASE}/system/system-legal-templates`, {
      method: "POST",
      headers: {
        ...baseHeaders(),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(templateData),
    })
      .then(async (res) => {
        const body = await res.json();
        if (!res.ok)
          throw new Error(body?.error || "Failed to create template.");
        return body;
      })
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },

  deleteCustomLegalTemplate: async function (templateId: string): Promise<any> {
    return await fetch(
      `${API_BASE}/system/system-legal-templates/${templateId}`,
      {
        method: "DELETE",
        headers: baseHeaders(),
      }
    )
      .then(async (res) => {
        const body = await res.json();
        if (!res.ok)
          throw new Error(body?.error || "Failed to delete template.");
        return body;
      })
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },

  // Update an existing custom legal template
  updateCustomLegalTemplate: async function (
    templateId: any,
    templateData: any
  ): Promise<any> {
    return await fetch(
      `${API_BASE}/system/system-legal-templates/${templateId}`,
      {
        method: "PUT",
        headers: {
          ...baseHeaders(),
          "Content-Type": "application/json",
        },
        body: JSON.stringify(templateData),
      }
    )
      .then(async (res) => {
        const body = await res.json();
        if (!res.ok)
          throw new Error(body?.error || "Failed to update template.");
        return body;
      })
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },

  // Upload and process DOCX file for custom legal templates
  uploadDocxForTemplate: async function (file: File): Promise<any> {
    const formData = new FormData();
    formData.append("file", file);

    return await fetch(`${API_BASE}/docx-edit/upload-for-template`, {
      method: "POST",
      headers: {
        ...baseHeaders(),
        // Don't set Content-Type header as it's automatically set by the browser for FormData
      },
      body: formData,
    })
      .then(async (res) => {
        const data = await res.json();
        if (!res.ok) throw new Error(data.error || "Error uploading DOCX file");
        return data;
      })
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },

  // Organization-scoped legal templates
  fetchOrganizationLegalTemplates: async function (
    organizationId: any
  ): Promise<any> {
    return await fetch(
      `${API_BASE}/system/organization-legal-templates?organizationId=${encodeURIComponent(organizationId)}`,
      {
        method: "GET",
        headers: baseHeaders(),
      }
    )
      .then(async (res) => {
        if (!res.ok)
          throw new Error("Could not fetch organization legal templates.");
        return res.json() as Promise<any>;
      })
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message, data: [] };
      });
  },
  createOrganizationLegalTemplate: async function (
    templateData: any
  ): Promise<any> {
    return await fetch(`${API_BASE}/system/organization-legal-templates`, {
      method: "POST",
      headers: {
        ...baseHeaders(),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(templateData),
    })
      .then(async (res) => {
        const body = await res.json();
        if (!res.ok)
          throw new Error(
            body?.error || "Could not create organization legal template."
          );
        return body;
      })
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },
  updateOrganizationLegalTemplate: async function (
    templateId: any,
    templateData: any
  ): Promise<any> {
    return await fetch(
      `${API_BASE}/system/organization-legal-templates/${templateId}`,
      {
        method: "PUT",
        headers: {
          ...baseHeaders(),
          "Content-Type": "application/json",
        },
        body: JSON.stringify(templateData),
      }
    )
      .then(async (res) => {
        const body = await res.json();
        if (!res.ok)
          throw new Error(
            body?.error || "Could not update organization legal template."
          );
        return body;
      })
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },
  deleteOrganizationLegalTemplate: async function (
    templateId: any,
    organizationId: any
  ): Promise<any> {
    const url = `${API_BASE}/system/organization-legal-templates/${templateId}?organizationId=${encodeURIComponent(organizationId)}`;
    return await fetch(url, {
      method: "DELETE",
      headers: baseHeaders(),
    })
      .then(async (res) => {
        const body = await res.json();
        if (!res.ok)
          throw new Error(
            body?.error || "Could not delete organization legal template."
          );
        return body;
      })
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },

  // User-scoped legal templates
  fetchUserLegalTemplates: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/user-legal-templates`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then(async (res) => {
        if (!res.ok) throw new Error("Could not fetch user legal templates.");
        return res.json() as Promise<any>;
      })
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message, data: [] };
      });
  },
  createUserLegalTemplate: async function (templateData: any): Promise<any> {
    return await fetch(`${API_BASE}/system/user-legal-templates`, {
      method: "POST",
      headers: {
        ...baseHeaders(),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(templateData),
    })
      .then(async (res) => {
        const body = await res.json();
        if (!res.ok)
          throw new Error(
            body?.error || "Could not create user legal template."
          );
        return body;
      })
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },
  updateUserLegalTemplate: async function (
    templateId: any,
    templateData: any
  ): Promise<any> {
    return await fetch(
      `${API_BASE}/system/user-legal-templates/${templateId}`,
      {
        method: "PUT",
        headers: {
          ...baseHeaders(),
          "Content-Type": "application/json",
        },
        body: JSON.stringify(templateData),
      }
    )
      .then(async (res) => {
        const body = await res.json();
        if (!res.ok)
          throw new Error(
            body?.error || "Could not update user legal template."
          );
        return body;
      })
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },
  deleteUserLegalTemplate: async function (templateId: string): Promise<any> {
    return await fetch(
      `${API_BASE}/system/user-legal-templates/${templateId}`,
      {
        method: "DELETE",
        headers: baseHeaders(),
      }
    )
      .then(async (res) => {
        const body = await res.json();
        if (!res.ok)
          throw new Error(
            body?.error || "Could not delete user legal template."
          );
        return body;
      })
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },
  // Fetch user-scoped legal templates for a specified user (admin)
  fetchUserLegalTemplatesForUser: async function (
    userId: string
  ): Promise<any> {
    return await fetch(
      `${API_BASE}/system/user-legal-templates/${encodeURIComponent(userId)}`,
      {
        method: "GET",
        headers: baseHeaders(),
      }
    )
      .then(async (res) => {
        if (!res.ok) throw new Error("Could not fetch user legal templates.");
        return res.json() as Promise<any>;
      })
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message, data: [] };
      });
  },
  // Purge temporary Case Document Builder files via dedicated endpoint
  purgeDocumentBuilder: async function (): Promise<any> {
    try {
      const res = await fetch(`${API_BASE}/system/purge-document-builder`, {
        method: "POST",
        headers: baseHeaders(),
      });

      if (!res.ok) {
        const errText = await res.text();
        // console.error("Failed to purge document builder files", errText);
        return { success: false, error: errText };
      }

      return await res.json();
    } catch (e: any) {
      // console.error("Error purging document builder files", e);
      return { success: false, error: e.message };
    }
  },
  removeDocxTemplate: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/remove-docx-template`, {
      method: "DELETE",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Error removing template.");
        return { success: true, error: null };
      })
      .catch((e: Error) => {
        // console.error(e);
        return { success: false, error: e.message };
      });
  },
  fetchDocxTemplateInfo: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/docx-template-info`, {
      method: "GET",
      headers: baseHeaders(),
      cache: "no-cache",
    })
      .then((res) => res.json() as Promise<any>)
      .catch((_e: Error) => {
        // console.error(e);
        return { filename: null, displayFilename: null, hasTemplate: false };
      });
  },
  getCDBDocumentation: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/cdb-documentation`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then(async (res) => {
        if (!res.ok) {
          const errorData = await res.json().catch(() => ({
            error: "Failed to parse error from getCDBDocumentation",
          }));
          throw new Error(
            errorData.error ||
              `Failed to fetch CDB documentation: ${res.status}`
          );
        }
        return res.json() as Promise<any>;
      })
      .then((data) => {
        if (data.success) {
          return data.documentation;
        }
        throw new Error(
          data.error || "Failed to retrieve CDB documentation content."
        );
      })
      .catch((_e: Error) => {
        // console.error("Error in System.getCDBDocumentation:", e);
        // Return a minimal fallback documentation string on error to prevent crashes
        // This could also be an empty string or a more specific error indicator if preferred by the consuming code.
        return "# Case Document Builder (CDB) Overview\n\nError fetching detailed documentation. The CDB helps automate legal document generation.";
      });
  },
  getDocumentBuilderPrompts: async function (): Promise<any> {
    try {
      const res = await fetch(`${API_BASE}/system/document-builder-prompts`, {
        method: "GET",
        headers: baseHeaders(),
      });

      const data = await res.json();

      if (!res.ok || !data?.success) {
        throw new Error(data?.error || "Failed to fetch prompt configurations");
      }

      // Ensure we always return an array for downstream logic
      return Array.isArray(data.prompts) ? data.prompts : [];
    } catch {
      // console.error("System.getDocumentBuilderPrompts error:", error);
      return [];
    }
  },
  updateDocumentBuilderPrompts: async function (
    updates: Record<string, any> = {}
  ): Promise<any> {
    try {
      const res = await fetch(`${API_BASE}/system/document-builder-prompts`, {
        method: "POST",
        headers: baseHeaders(),
        body: JSON.stringify({ ...updates }),
      });

      const data = await res.json();
      return {
        success: res.ok && data?.success,
        error: data?.error || null,
      };
    } catch (error: any) {
      // console.error("System.updateDocumentBuilderPrompts error:", error);
      return { success: false, error: error.message };
    }
  },

  generateStyleProfile: async function (documentContent: any): Promise<any> {
    try {
      const res = await fetch(`${API_BASE}/system/generate-style-profile`, {
        method: "POST",
        headers: baseHeaders(),
        body: JSON.stringify({ documentContent }),
      });

      const data = await res.json();
      return {
        success: res.ok && data?.success,
        styleInstructions: data?.styleInstructions || null,
        tokenInfo: data?.tokenInfo || null,
        error: data?.error || null,
      };
    } catch (err: any) {
      // console.error("System.generateStyleProfile error:", err);
      return { success: false, error: err.message };
    }
  },

  getStyleGenerationPrompt: async function (): Promise<any> {
    try {
      const res = await fetch(`${API_BASE}/system/style-generation-prompt`, {
        method: "GET",
        headers: baseHeaders(),
      });

      const data = await res.json();
      return {
        success: res.ok && data?.success,
        prompt: data?.prompt || null,
        error: data?.error || null,
      };
    } catch (err: any) {
      // console.error("System.getStyleGenerationPrompt error:", err);
      return { success: false, error: err.message };
    }
  },

  getStyleGenerationContextWindow: async function (): Promise<any> {
    try {
      const res = await fetch(
        `${API_BASE}/system/style-generation-context-window`,
        {
          method: "GET",
          headers: baseHeaders(),
        }
      );

      const data = await res.json();
      return {
        success: res.ok && data?.success,
        contextWindow: data?.contextWindow || 0,
        availableTokens: data?.availableTokens || 0,
        reservedTokens: data?.reservedTokens || 0,
        error: data?.error || null,
      };
    } catch (err: any) {
      // console.error("System.getStyleGenerationContextWindow error:", err);
      return { success: false, error: err.message };
    }
  },

  // Enhanced System Report methods
  getSystemReports: async function (
    filters: Record<string, any> = {}
  ): Promise<any> {
    const params = new URLSearchParams();
    if (filters.type) params.append("type", filters.type);
    if (filters.status) params.append("status", filters.status);
    if (filters.severity) params.append("severity", filters.severity);

    const url = `${API_BASE}/system-reports${params.toString() ? `?${params.toString()}` : ""}`;

    return fetch(url, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not fetch system reports.");
        return res.json() as Promise<any>;
      })
      .catch((_e: Error) => {
        // console.error(e);
        return null;
      });
  },

  createSystemReport: async function (reportData: any): Promise<any> {
    return fetch(`${API_BASE}/system-reports`, {
      method: "POST",
      headers: {
        ...baseHeaders(),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(reportData),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not create system report.");
        return res.json() as Promise<any>;
      })
      .catch((_e: Error) => {
        // console.error(e);
        return null;
      });
  },

  getSystemReport: async function (id: any): Promise<any> {
    return fetch(`${API_BASE}/system-reports/${id}`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not fetch system report.");
        return res.json() as Promise<any>;
      })
      .catch((_e: Error) => {
        // console.error(e);
        return null;
      });
  },

  updateSystemReportStatus: async function (
    id: string,
    status: string,
    resolverUserId: string | null = null
  ): Promise<any> {
    const payload: any = { status };
    if (resolverUserId) payload.resolver_user_id = resolverUserId;

    return fetch(`${API_BASE}/system-reports/${id}/status`, {
      method: "PUT",
      headers: {
        ...baseHeaders(),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not update system report status.");
        return res.json() as Promise<any>;
      })
      .catch((_e: Error) => {
        // console.error(e);
        return null;
      });
  },

  assignSystemReportResolver: async function (
    id: any,
    resolverUserId: any
  ): Promise<any> {
    return fetch(`${API_BASE}/system-reports/${id}/assign`, {
      method: "PUT",
      headers: {
        ...baseHeaders(),
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ resolver_user_id: resolverUserId }),
    })
      .then((res) => {
        if (!res.ok)
          throw new Error("Could not assign resolver to system report.");
        return res.json() as Promise<any>;
      })
      .catch((_e: Error) => {
        // console.error(e);
        return null;
      });
  },

  updateIncidentDetails: async function (id: any, details: any): Promise<any> {
    return fetch(`${API_BASE}/system-reports/${id}/incident-details`, {
      method: "PUT",
      headers: {
        ...baseHeaders(),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(details),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not update incident details.");
        return res.json() as Promise<any>;
      })
      .catch((_e: Error) => {
        // console.error(e);
        return null;
      });
  },

  addResolutionComment: async function (
    id: any,
    resolutionComment: any
  ): Promise<any> {
    return fetch(`${API_BASE}/system-reports/${id}/resolution`, {
      method: "PUT",
      headers: {
        ...baseHeaders(),
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ resolution_comment: resolutionComment }),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not add resolution comment.");
        return res.json() as Promise<any>;
      })
      .catch((_e: Error) => {
        // console.error(e);
        return null;
      });
  },

  getSystemReportStatistics: async function (): Promise<any> {
    return fetch(`${API_BASE}/system-reports/statistics`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok)
          throw new Error("Could not fetch system report statistics.");
        return res.json() as Promise<any>;
      })
      .catch((_e: Error) => {
        // console.error(e);
        return null;
      });
  },

  addSystemReportMessage: async function (id: any, content: any): Promise<any> {
    return fetch(`${API_BASE}/system-reports/${id}/messages`, {
      method: "POST",
      headers: {
        ...baseHeaders(),
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ content }),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not add message to system report.");
        return res.json() as Promise<any>;
      })
      .catch((_e: Error) => {
        // console.error(e);
        return null;
      });
  },

  updateSystemReportDetails: async function (
    id: any,
    { title, description }: { title: any; description: any }
  ): Promise<any> {
    return fetch(`${API_BASE}/system-reports/${id}/details`, {
      method: "PUT",
      headers: {
        ...baseHeaders(),
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ title, description }),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not update system report details.");
        return res.json() as Promise<any>;
      })
      .catch((_e: Error) => {
        // console.error(e);
        return null;
      });
  },

  resolveSystemReport: async function (
    id: string,
    resolutionComment: string | null = null
  ): Promise<any> {
    const payload: any = {};
    if (resolutionComment) payload.resolution_comment = resolutionComment;

    return fetch(`${API_BASE}/system-reports/${id}/resolve`, {
      method: "PUT",
      headers: {
        ...baseHeaders(),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not resolve system report.");
        return res.json() as Promise<any>;
      })
      .catch((_e: Error) => {
        // console.error(e);
        return null;
      });
  },

  deleteSystemReport: async function (id: any): Promise<any> {
    return fetch(`${API_BASE}/system-reports/${id}`, {
      method: "DELETE",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not delete system report.");
        return res.json() as Promise<any>;
      })
      .catch((_e: Error) => {
        // console.error(e);
        return null;
      });
  },

  getSystemReportConstants: async function (): Promise<any> {
    return fetch(`${API_BASE}/system-reports/constants`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok)
          throw new Error("Could not fetch system report constants.");
        return res.json() as Promise<any>;
      })
      .catch((_e: Error) => {
        // console.error(e);
        return null;
      });
  },

  confirmSystemReportResolution: async function (id: any): Promise<any> {
    return fetch(`${API_BASE}/system-reports/${id}/confirm-resolution`, {
      method: "POST",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok)
          throw new Error("Could not confirm system report resolution.");
        return res.json() as Promise<any>;
      })
      .catch((_e: Error) => {
        // console.error(e);
        return null;
      });
  },

  rejectSystemReportResolution: async function (id: any): Promise<any> {
    return fetch(`${API_BASE}/system-reports/${id}/reject-resolution`, {
      method: "POST",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok)
          throw new Error("Could not reject system report resolution.");
        return res.json() as Promise<any>;
      })
      .catch((_e: Error) => {
        // console.error(e);
        return null;
      });
  },

  checkToken: async function (): Promise<any> {
    return await fetch(`${API_BASE}/system/check-token`, {
      headers: baseHeaders(),
    }).then((res) => res.json() as Promise<any>);
  },
};

export default System;
