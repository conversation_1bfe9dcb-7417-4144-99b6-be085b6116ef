/* eslint-disable unused-imports/no-unused-vars */

import { API_BASE } from "@/utils/constants";
import { baseHeaders } from "@/utils/request";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import WorkspaceThread from "@/models/workspaceThread";
import { v4 } from "uuid";
import useUserStore from "@/stores/userStore";
import useWorkspaceStore from "@/stores/workspaceStore";
import i18next from "../i18n";
import useStreamAbortStore from "@/stores/useStreamAbortStore";

// Type definitions
export interface WorkspaceData {
  name?: string;
  [key: string]: any;
}

interface WorkspaceResponse {
  workspace: Workspace | null;
  message: string;
}

interface WorkspaceUpdateOrderResponse {
  success: boolean;
  message: string;
}

export interface Workspace {
  id: string;
  slug: string;
  name: string;
  workspaceSlug?: string;
  documents?: Document[];
  workspace_documents?: Document[];
  [key: string]: any;
}

export interface Document {
  id: string;
  docpath?: string;
  docPath?: string;
  location?: string;
  [key: string]: any;
}

interface ChatResult {
  id: string;
  type: string;
  textResponse: string | null;
  sources: any[];
  close?: boolean;
  error?: string;
  [key: string]: any;
}

interface ChatLog {
  id: string;
  workspaceId?: string;
  [key: string]: any;
}

interface BulkJobResponse {
  success?: boolean;
  error?: string;
  [key: string]: any;
}

interface UploadResponse {
  response: Response;
  data: {
    success?: boolean;
    error?: string | null;
    document?: {
      id: string;
      location: string;
    } | null;
    originalname?: string;
    [key: string]: any;
  };
}

interface ShareOptions {
  userIds?: string[];
  shareWithOrg?: boolean;
}

interface ShareResponse {
  success: boolean;
  error?: string;
  [key: string]: any;
}

interface StreamChatOptions {
  workspaceSlug: string;
  threadSlug?: string | null;
  prompt: string;
  chatHandler: (result: ChatResult) => void;
  attachments?: any[];
  chatId?: string | null;
  isCanvasChat?: boolean;
  preventChatCreation?: boolean;
  cdb?: boolean;
  invoice_ref?: string | null;
  abortController?: AbortController | null;
  hasUploadedFile?: boolean;
  docxContent?: any;
  displayMessage?: string | null;
  useDeepSearch?: boolean;
  cdbOptions?: any[];
  settingsSuffix?: string;
}

const WorkspaceModel = {
  // Keep track of pending workspace creation requests
  _pendingCreations: new Map<string, Promise<WorkspaceResponse>>(),

  async new(data: WorkspaceData = {}): Promise<WorkspaceResponse> {
    const slugModule = useUserStore.getState().selectedModule;

    // Generate a unique key for this workspace creation request
    const requestKey = `${slugModule}_${data.name || "unnamed"}_${Date.now()}`;

    // Check if there's already a pending request for this workspace
    if (this._pendingCreations.has(requestKey)) {
      // Duplicate workspace creation prevented (log removed)
      return this._pendingCreations.get(requestKey)!;
    }

    // Create a promise for this request and store it
    const creationPromise = (async (): Promise<WorkspaceResponse> => {
      try {
        const { workspace, message } = await fetch(
          `${API_BASE}/workspace/new/${slugModule}`,
          { method: "POST", body: JSON.stringify(data), headers: baseHeaders() }
        )
          .then((res) => res.json() as Promise<WorkspaceResponse>)
          .catch((_) => {
            return { workspace: null, message: "Error fetching workspace" };
          });

        // Update cache via store action
        if (workspace) {
          useWorkspaceStore.getState().addWorkspace({
            ...workspace,
            type: workspace.type || slugModule,
          });
        }

        return { workspace, message };
      } finally {
        // Remove the pending request after a small delay
        setTimeout(() => {
          this._pendingCreations.delete(requestKey);
        }, 1000);
      }
    })();

    // Store the promise
    this._pendingCreations.set(requestKey, creationPromise);

    // Return the promise result
    return creationPromise;
  },

  async update(
    slug: string,
    data: WorkspaceData = {}
  ): Promise<WorkspaceResponse> {
    const { workspace, message } = await fetch(
      `${API_BASE}/workspace/${slug}/update`,
      { method: "POST", body: JSON.stringify(data), headers: baseHeaders() }
    )
      .then((res) => res.json() as Promise<WorkspaceResponse>)
      .catch((_) => {
        return { workspace: null, message: "Error fetching workspace" };
      });

    return { workspace, message };
  },

  async updateOrder(workspaces: any[]): Promise<WorkspaceUpdateOrderResponse> {
    const { success, message } = await fetch(
      `${API_BASE}/workspace/update-order`,
      {
        method: "POST",
        body: JSON.stringify(workspaces),
        headers: baseHeaders(),
      }
    )
      .then((res) => res.json() as Promise<WorkspaceUpdateOrderResponse>)
      .catch((_) => {
        return { success: false, message: "Error updating workspace order" };
      });

    return { success, message };
  },

  async all(): Promise<Workspace[]> {
    // Get the current module directly from localStorage for consistency
    const moduleSlug = useUserStore.getState().selectedModule;

    // Use a cache-busting query parameter
    const timestamp = Date.now();

    const workspaces = await fetch(
      `${API_BASE}/workspaces/${moduleSlug}?_=${timestamp}`,
      {
        method: "GET",
        headers: {
          ...baseHeaders(),
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      }
    )
      .then((res) => res.json() as Promise<{ workspaces?: Workspace[] }>)
      .then((res) => res.workspaces || [])
      .catch((_) => {
        // Error fetching workspaces (log removed)
        return [];
      });

    return workspaces;
  },

  async fromAllModules(): Promise<Workspace[]> {
    const moduleTypes = ["legal-qa", "document-drafting"];
    try {
      const workspaces = await Promise.all(
        moduleTypes.map((type) =>
          fetch(`${API_BASE}/workspaces/${type}`, {
            method: "GET",
            headers: baseHeaders(),
          })
            .then((res) => res.json() as Promise<{ workspaces?: Workspace[] }>)
            .then((res) => res.workspaces || [])
            .catch(() => [])
        )
      );
      return workspaces.flat();
    } catch (error) {
      // Error fetching all workspaces (log removed)
      return [];
    }
  },

  async getPopulatedWorkspaces(): Promise<Workspace[]> {
    try {
      const response = await fetch(`${API_BASE}/populated-workspaces`, {
        method: "GET",
        headers: baseHeaders(),
      });

      if (!response.ok) {
        throw new Error(
          `Error fetching populated workspaces: ${response.status}`
        );
      }

      const data = (await response.json()) as { workspaces?: Workspace[] };
      return data.workspaces || [];
    } catch (error) {
      // Error fetching populated workspaces (log removed)
      return [];
    }
  },

  async modifyEmbeddings(
    slug: string,
    changes: { adds?: string[]; deletes?: string[] } = {}
  ): Promise<any> {
    const response = await fetch(
      `${API_BASE}/workspace/${slug}/update-embeddings`,
      {
        method: "POST",
        body: JSON.stringify(changes), // contains 'adds' and 'deletes' keys that are arrays of filepaths
        headers: baseHeaders(),
      }
    )
      .then((res) => res.json() as Promise<any>)
      .catch((_) => {
        return { workspace: null, message: "Error modifying embeddings" };
      });

    return response;
  },

  // Bulk processing job tracking methods
  async getBulkJobStatus(
    slug: string,
    jobId: string
  ): Promise<BulkJobResponse> {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/bulk-job/${jobId}`,
        {
          method: "GET",
          headers: baseHeaders(),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return (await response.json()) as BulkJobResponse;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      // Error fetching bulk job status (log removed)
      return { error: errorMessage };
    }
  },

  async cancelBulkJob(slug: string, jobId: string): Promise<BulkJobResponse> {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/bulk-job/${jobId}/cancel`,
        {
          method: "POST",
          headers: baseHeaders(),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return (await response.json()) as BulkJobResponse;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      // Error cancelling bulk job (log removed)
      return { error: errorMessage };
    }
  },

  async getAllBulkJobs(): Promise<BulkJobResponse> {
    try {
      const response = await fetch(`${API_BASE}/workspace/bulk-jobs`, {
        method: "GET",
        headers: baseHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return (await response.json()) as BulkJobResponse;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      // Error fetching bulk jobs (log removed)
      return { error: errorMessage };
    }
  },

  async chatHistory(slug: string): Promise<any[]> {
    const history = await fetch(`${API_BASE}/workspace/${slug}/chats`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => res.json() as Promise<{ history?: any[] }>)
      .then((res) => res.history || [])
      .catch(() => []);
    return history;
  },

  async chatLog(chatId: string): Promise<ChatLog | null> {
    try {
      const response = await fetch(`${API_BASE}/workspace/chat-log/${chatId}`, {
        method: "GET",
        headers: baseHeaders(),
      });
      const data = (await response.json()) as { chatLog?: ChatLog };
      return data.chatLog || null;
    } catch (error) {
      // Error fetching chat log (log removed)
      return null;
    }
  },

  async legalTask(slug: string, legalTask: any): Promise<any> {
    return await fetch(`${API_BASE}/workspace/${slug}/legal-task`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(legalTask),
    }).then((res) => res.json() as Promise<any>);
  },

  async updateChatFeedback(
    chatId: string,
    slug: string,
    feedback: any
  ): Promise<boolean> {
    const result = await fetch(
      `${API_BASE}/workspace/${slug}/chat-feedback/${chatId}`,
      {
        method: "POST",
        headers: baseHeaders(),
        body: JSON.stringify({ feedback }),
      }
    )
      .then((res) => res.ok)
      .catch(() => false);
    return result;
  },

  async deleteChat(
    chatId: string,
    slug: string | null = null
  ): Promise<boolean | { success: boolean; error?: string }> {
    if (slug) {
      // Original workspace-specific delete chat
      return await fetch(
        `${API_BASE}/workspace/${slug}/delete-chat/${chatId}`,
        { method: "DELETE", headers: baseHeaders() }
      )
        .then((res) => res.ok)
        .catch(() => false);
    }
    // General workspace chat deletion
    return await fetch(`${API_BASE}/workspace/workspace-chats/${chatId}`, {
      method: "PUT",
      headers: baseHeaders(),
    })
      .then(
        (res) => res.json() as Promise<{ success?: boolean; error?: string }>
      )
      .then((data) => {
        // Ensure 'success' is always present and never undefined
        return { success: Boolean(data.success), error: data.error };
      })
      .catch((_) => {
        // Error deleting chat (log removed)
        return { success: false, error: "Error deleting chat" };
      });
  },

  async deleteChats(
    slug: string = "",
    chatIds: string[] = []
  ): Promise<boolean> {
    return await fetch(`${API_BASE}/workspace/${slug}/delete-chats`, {
      method: "DELETE",
      headers: baseHeaders(),
      body: JSON.stringify({ chatIds }),
    })
      .then((res) => {
        if (res.ok) return true;
        throw new Error("Failed to delete chats.");
      })
      .catch((_) => {
        // Error deleting chats (log removed)
        return false;
      });
  },

  async deleteEditedChats(
    slug: string = "",
    threadSlug: string = "",
    startingId: string
  ): Promise<boolean> {
    if (threadSlug)
      return this.threads._deleteEditedChats(slug, threadSlug, startingId);
    return this._deleteEditedChats(slug, startingId);
  },

  async updateChatResponse(
    slug: string = "",
    threadSlug: string = "",
    chatId: string,
    newText: string
  ): Promise<boolean> {
    if (threadSlug)
      return this.threads._updateChatResponse(
        slug,
        threadSlug,
        chatId,
        newText
      );
    return this._updateChatResponse(slug, chatId, newText);
  },

  async multiplexStream(options: StreamChatOptions): Promise<AbortController> {
    const {
      workspaceSlug,
      threadSlug = null,
      prompt,
      chatHandler,
      attachments = [],
      chatId = null,
      isCanvasChat = false,
      preventChatCreation = false,
      cdb = false,
      invoice_ref = null,
      abortController = undefined,
      hasUploadedFile = false,
      docxContent = null,
      displayMessage = null,
      useDeepSearch = false,
      cdbOptions = [],
      settingsSuffix = "",
    } = options;

    // Mark workspace as populated on any chat interaction
    try {
      const ws = await this.bySlug(workspaceSlug); // Fetch workspace details
      if (ws) {
        const { id, slug, name, type, ...rest } = ws;
        useWorkspaceStore.getState().markWorkspacePopulated({
          id,
          slug,
          name,
          type: type ?? "unknown",
          ...rest,
        });
      }
    } catch (_e) {
      // Error marking workspace populated (log removed)
    }

    let llmSelected = localStorage.getItem("selectedLLMOption");
    //This is for the binary llm selection, to make sure that the llm is set to 0
    //(first option) when the module is not document drafting or when the llm is not set.
    if (
      llmSelected === null ||
      localStorage.getItem("module") !== "document-drafting"
    ) {
      llmSelected = "0";
    }

    if (threadSlug)
      return this.threads.streamChat(
        { workspaceSlug, threadSlug },
        prompt,
        (msg: any) =>
          chatHandler({
            id: msg.id,
            type: msg.type,
            textResponse: msg.textResponse ?? null,
            sources: msg.sources ?? [],
            close: msg.close,
            error: msg.error,
            ...msg,
            threadSlug: msg.threadSlug ?? undefined,
          }),
        attachments,
        chatId ?? undefined,
        isCanvasChat,
        preventChatCreation,
        cdb ? "true" : "",
        llmSelected,
        invoice_ref ?? undefined,
        abortController ?? undefined,
        hasUploadedFile,
        docxContent,
        displayMessage,
        useDeepSearch,
        cdbOptions,
        settingsSuffix
      );

    return this.streamChat(
      { slug: workspaceSlug },
      prompt,
      chatHandler,
      attachments,
      chatId,
      isCanvasChat,
      preventChatCreation,
      cdb,
      llmSelected,
      invoice_ref,
      abortController ?? null,
      hasUploadedFile,
      docxContent,
      displayMessage,
      useDeepSearch,
      cdbOptions,
      settingsSuffix
    );
  },

  async streamChat(
    workspaceSlug: { slug: string },
    message: string,
    handleChat: (result: ChatResult) => void,
    attachments: any[],
    chatId: string | null,
    isCanvasChat: boolean,
    preventChatCreation: boolean,
    cdb: boolean,
    llmSelected: string | null,
    invoice_ref: string | null,
    passedAbortController: AbortController | null,
    hasUploadedFile: boolean = false,
    docxContent: any = null,
    displayMessage: string | null = null,
    useDeepSearch: boolean = false,
    cdbOptions: any[] = [],
    settingsSuffix: string = ""
  ): Promise<AbortController> {
    // Use passed abort controller or create a new one
    // Note: For workspace-level streaming, we don't have a specific threadSlug
    // so we use the passed controller or create a new one
    const localAbortController = passedAbortController || new AbortController();

    // Subscribe to the abort request timestamp from the Zustand store
    let lastSeenAbortTimestamp =
      useStreamAbortStore.getState().abortRequestTimestamp;
    const unsubscribeFromStore = useStreamAbortStore.subscribe(
      (state: any, _prevState: any) => {
        const newTimestamp = state.abortRequestTimestamp;
        if (newTimestamp !== null && newTimestamp !== lastSeenAbortTimestamp) {
          lastSeenAbortTimestamp = newTimestamp;
          localAbortController.abort();
          handleChat({
            id: v4(),
            type: "stopGeneration",
            textResponse: null,
            sources: [],
          });
        }
      }
    );

    const slugModule = useUserStore.getState().selectedModule;

    const requestUrl = `${API_BASE}/workspace/${workspaceSlug.slug}/stream-chat/${slugModule}?cdb=${cdb}`;
    const requestBody = {
      message,
      attachments,
      chatId,
      isCanvasChat,
      preventChatCreation,
      cdb,
      llmSelected,
      invoice_ref,
      hasUploadedFile,
      docxContent,
      displayMessage,
      useDeepSearch,
      cdbOptions,
      settings_suffix: settingsSuffix,
    };

    try {
      await fetchEventSource(requestUrl, {
        method: "POST",
        body: JSON.stringify(requestBody),
        headers: baseHeaders() as Record<string, string>,
        signal: localAbortController.signal,
        openWhenHidden: true,
        async onopen(response) {
          if (response.ok) {
            return; // everything's good
          } else if (
            response.status >= 400 &&
            response.status < 500 &&
            response.status !== 429
          ) {
            handleChat({
              id: v4(),
              type: "abort",
              textResponse: null,
              sources: [],
              close: true,
              error: `${i18next.t("errors.streaming.failed", { defaultValue: "An error occurred while streaming response." })} ${i18next.t("errors.streaming.code", { defaultValue: "Code", code: response.status })} ${response.status}`,
            });
            localAbortController.abort();
            throw new Error("Invalid Status code response.");
          } else {
            handleChat({
              id: v4(),
              type: "abort",
              textResponse: null,
              sources: [],
              close: true,
              error: `${i18next.t("errors.streaming.failed", { defaultValue: "An error occurred while streaming response." })} ${i18next.t("errors.streaming.unknown", { defaultValue: "Unknown Error." })}`,
            });
            localAbortController.abort();
            throw new Error("Unknown error");
          }
        },
        async onmessage(msg) {
          try {
            const chatResult = JSON.parse(msg.data);
            handleChat(chatResult);
          } catch (error) {
            // Error parsing message data
          }
        },
        onerror(err) {
          handleChat({
            id: v4(),
            type: "abort",
            textResponse: null,
            sources: [],
            close: true,
            error: `${i18next.t("errors.streaming.failed", { defaultValue: "An error occurred while streaming response." })} ${err.message}`,
          });
          localAbortController.abort(); // Ensure abort is called on error
          throw new Error(err.message || "Stream error");
        },
      });
    } catch (error: any) {
      if (error.name !== "AbortError") {
        // Only handle non-abort errors here, aborts are handled by the subscription
        handleChat({
          id: v4(),
          type: "abort",
          textResponse: error.message,
          sources: [],
        });
      }
    } finally {
      // IMPORTANT: Unsubscribe from the store when the stream function is done
      unsubscribeFromStore();
    }

    return localAbortController;
  },

  async bySlug(
    slug: string = "",
    includeDocuments: boolean = false
  ): Promise<Workspace | null> {
    // Extract any query parameters already in the slug
    let baseSlug = slug;
    let queryParams = `includeDocuments=${includeDocuments}`;

    if (slug.includes("?")) {
      const [slugPart, queryPart] = slug.split("?");
      baseSlug = slugPart;
      queryParams = `${queryPart}&includeDocuments=${includeDocuments}`;
    }

    // Add cache-busting parameter
    const timestamp = Date.now();
    queryParams = `${queryParams}&_=${timestamp}`;

    // Fetching workspace with slug and params

    const workspace = await fetch(
      `${API_BASE}/workspace/${baseSlug}?${queryParams}`,
      {
        headers: {
          ...baseHeaders(),
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      }
    )
      .then((res) => res.json() as Promise<{ workspace?: Workspace }>)
      .then((res) => res.workspace ?? null)
      .catch(() => null);
    return workspace;
  },

  async delete(slug: string): Promise<boolean> {
    const result = await fetch(`${API_BASE}/workspace/${slug}`, {
      method: "DELETE",
      headers: baseHeaders(),
    })
      .then((res) => res.ok)
      .catch(() => false);

    if (result) {
      useWorkspaceStore.getState().removeWorkspace(slug);
    }
    return result;
  },

  async wipeVectorDb(slug: string): Promise<boolean> {
    return await fetch(`${API_BASE}/workspace/${slug}/reset-vector-db`, {
      method: "DELETE",
      headers: baseHeaders(),
    })
      .then((res) => res.ok)
      .catch(() => false);
  },

  async uploadFile(slug: string, formData: FormData): Promise<UploadResponse> {
    const slugModule = useUserStore.getState().selectedModule;

    try {
      // Get auth header without Content-Type (let browser set it for multipart)
      const headers = baseHeaders();
      delete (headers as any)["Content-Type"]; // Remove Content-Type to let browser set boundary

      const response = await fetch(
        `${API_BASE}/workspace/${slug}/upload/${slugModule}`,
        { method: "POST", body: formData, headers }
      );

      const data = (await response.json()) as any;
      return { response, data };
    } catch (error: unknown) {
      // Handle network-level errors
      const fileName = formData.get("file") as File;
      const errorObj = error as Error;
      return {
        response: { ok: false } as Response,
        data: {
          error:
            errorObj.name === "TypeError" &&
            errorObj.message.includes("Failed to fetch")
              ? "ERR_INSUFFICIENT_RESOURCES"
              : errorObj.message,
          originalname: fileName?.name,
        },
      };
    }
  },

  async getTokenCount(slug: string): Promise<any> {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/token-count`,
        { method: "GET", headers: baseHeaders() }
      );
      const data = (await response.json()) as any;
      return data;
    } catch (error) {
      // Failed to fetch token count
      return null;
    }
  },

  // Bulk upload methods
  async startBulkUpload(
    slug: string,
    files: Array<{ file: File; [key: string]: any }>,
    slugModule: string
  ): Promise<{ error?: string; [key: string]: any }> {
    try {
      const formData = new FormData();

      // Add files as binary data, not base64
      files.forEach((fileData, index) => {
        // fileData contains the File object, not base64
        formData.append(`file-${index}`, fileData.file);
      });

      // Add metadata
      formData.append("moduleId", slugModule);
      formData.append("fileCount", files.length.toString());

      // Get auth header without Content-Type (let browser set it for multipart)
      const headers = baseHeaders();
      delete (headers as any)["Content-Type"]; // Remove Content-Type to let browser set boundary

      const response = await fetch(
        `${API_BASE}/workspace/${slug}/bulk-upload/${slugModule}`,
        {
          method: "POST",
          headers: headers,
          body: formData,
        }
      );

      if (!response.ok) {
        const data = (await response.json()) as { error?: string };
        throw new Error(data.error || "Failed to start bulk upload");
      }

      return (await response.json()) as any;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      // Error starting bulk upload
      return { error: errorMessage };
    }
  },

  async getBulkUploadJobStatus(
    slug: string,
    jobId: string
  ): Promise<{ error?: string; [key: string]: any }> {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/bulk-upload-job/${jobId}`,
        {
          method: "GET",
          headers: baseHeaders(),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return (await response.json()) as any;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      // Error fetching bulk upload job status
      return { error: errorMessage };
    }
  },

  async cancelBulkUploadJob(
    slug: string,
    jobId: string
  ): Promise<{ error?: string; [key: string]: any }> {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/bulk-upload-job/${jobId}/cancel`,
        {
          method: "POST",
          headers: baseHeaders(),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return (await response.json()) as any;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      // Error cancelling bulk upload job
      return { error: errorMessage };
    }
  },

  async uploadLink(slug: string, link: string): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append("link", link);

    // Get auth header without Content-Type (let browser set it for multipart)
    const headers = baseHeaders();
    delete (headers as any)["Content-Type"]; // Remove Content-Type to let browser set boundary

    return await fetch(`${API_BASE}/workspace/${slug}/upload-link`, {
      method: "POST",
      headers,
      body: formData,
    }).then((res) => res.json() as Promise<any>);
  },

  async getSuggestedMessages(slug: string): Promise<any[] | null> {
    return await fetch(`${API_BASE}/workspace/${slug}/suggested-messages`, {
      method: "GET",
      cache: "no-cache",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not fetch suggested messages.");
        return res.json();
      })
      .then((res) => res.suggestedMessages)
      .catch((_) => {
        // Error fetching suggested messages (log removed)
        return null;
      });
  },

  async setSuggestedMessages(
    slug: string,
    messages: any[]
  ): Promise<{ success: boolean; error?: string; [key: string]: any }> {
    return fetch(`${API_BASE}/workspace/${slug}/suggested-messages`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ messages }),
    })
      .then((res) => {
        if (!res.ok) {
          throw new Error(
            res.statusText || "Error setting suggested messages."
          );
        }
        return { success: true, ...(res.json() as any) };
      })
      .catch((_) => {
        // Error setting suggested messages (log removed)
        return { success: false, error: "Error setting suggested messages" };
      });
  },

  async setPinForDocument(
    slug: string,
    docPath: string,
    pinStatus: boolean,
    isFolder: boolean = false
  ): Promise<boolean> {
    return fetch(`${API_BASE}/workspace/${slug}/update-pin`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ docPath, pinStatus, isFolder }),
    })
      .then((res) => {
        if (!res.ok) {
          throw new Error(
            res.statusText || "Error setting pin status for document."
          );
        }
        return true;
      })
      .catch((_) => {
        // Error setting pin status for document (log removed)
        return false;
      });
  },

  async setPDRForDocument(
    slug: string,
    docPath: string,
    pdrStatus: boolean,
    isFolder: boolean = false
  ): Promise<boolean> {
    return fetch(`${API_BASE}/workspace/${slug}/update-pdr`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ docPath, pdrStatus, isFolder }),
    })
      .then((res) => {
        if (!res.ok) {
          throw new Error(
            res.statusText || "Error setting PDR status for document."
          );
        }
        return true;
      })
      .catch((_) => {
        // Error setting PDR status for document (log removed)
        return false;
      });
  },

  async setStarForDocument(
    slug: string,
    docPath: string,
    starStatus: boolean,
    isFolder: boolean = false,
    forceUpdate: boolean = false
  ): Promise<{ success: boolean; error?: string; [key: string]: any }> {
    return fetch(`${API_BASE}/workspace/${slug}/update-star`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ docPath, starStatus, isFolder, forceUpdate }),
    })
      .then((res) => {
        if (!res.ok) {
          throw new Error(
            res.statusText || "Error setting star status for document."
          );
        }
        return res.json() as Promise<any>;
      })
      .catch((error: Error) => {
        // Error setting star status for document (log removed)
        return { success: false, error: error.message };
      });
  },

  async reVectorizeDocument(
    slug: string,
    docId: string
  ): Promise<{ success: boolean; error?: string; [key: string]: any }> {
    return fetch(`${API_BASE}/workspace/${slug}/re-vectorize`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ docId }),
    })
      .then((res) => {
        if (!res.ok) {
          throw new Error(
            res.statusText || "Error updating document metadata."
          );
        }
        return res.json();
      })
      .catch((error: any) => {
        // Error updating document metadata (log removed)
        return { success: false, error: error.message };
      });
  },

  async ttsMessage(slug: string, chatId: string): Promise<string | null> {
    return await fetch(`${API_BASE}/workspace/${slug}/tts/${chatId}`, {
      method: "GET",
      cache: "no-cache",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (res.ok && res.status !== 204) return res.blob();
        throw new Error("Failed to fetch TTS.");
      })
      .then((blob) => (blob ? URL.createObjectURL(blob) : null))
      .catch((_) => {
        return null;
      });
  },

  async uploadPfp(
    formData: FormData,
    slug: string
  ): Promise<{ success: boolean; error: string | null }> {
    // Get auth header without Content-Type (let browser set it for multipart)
    const headers = baseHeaders();
    delete (headers as any)["Content-Type"]; // Remove Content-Type to let browser set boundary

    return await fetch(`${API_BASE}/workspace/${slug}/upload-pfp`, {
      method: "POST",
      body: formData,
      headers,
    })
      .then((res) => {
        if (!res.ok) throw new Error("Error uploading pfp.");
        return { success: true, error: null };
      })
      .catch((_) => {
        // Error uploading pfp (log removed)
        return { success: false, error: "Error uploading pfp" };
      });
  },

  async fetchPfp(slug: string): Promise<string | null> {
    return await fetch(`${API_BASE}/workspace/${slug}/pfp`, {
      method: "GET",
      cache: "no-cache",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (res.ok && res.status !== 204) return res.blob();
        throw new Error("Failed to fetch pfp.");
      })
      .then((blob) => (blob ? URL.createObjectURL(blob) : null))
      .catch((_) => {
        return null;
      });
  },

  async removePfp(
    slug: string
  ): Promise<{ success: boolean; error: string | null }> {
    return await fetch(`${API_BASE}/workspace/${slug}/remove-pfp`, {
      method: "DELETE",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (res.ok) return { success: true, error: null };
        throw new Error("Failed to remove pfp.");
      })
      .catch((_) => {
        // Error removing pfp (log removed)
        return { success: false, error: "Error removing pfp" };
      });
  },

  async _updateChatResponse(
    slug: string = "",
    chatId: string,
    newText: string
  ): Promise<boolean> {
    return await fetch(`${API_BASE}/workspace/${slug}/update-chat`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ chatId, newText }),
    })
      .then((res) => {
        if (res.ok) return true;
        throw new Error("Failed to update chat.");
      })
      .catch((_) => {
        // Error updating chat (log removed)
        return false;
      });
  },

  async _deleteEditedChats(
    slug: string = "",
    startingId: string
  ): Promise<boolean> {
    return await fetch(`${API_BASE}/workspace/${slug}/delete-edited-chats`, {
      method: "DELETE",
      headers: baseHeaders(),
      body: JSON.stringify({ startingId }),
    })
      .then((res) => {
        if (res.ok) return true;
        throw new Error("Failed to delete chats.");
      })
      .catch((_) => {
        // Error deleting edited chats (log removed)
        return false;
      });
  },

  async forkThread(
    slug: string = "",
    threadSlug: string | null = null,
    chatId: string | null = null
  ): Promise<string | null> {
    return await fetch(`${API_BASE}/workspace/${slug}/thread/fork`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ threadSlug, chatId }),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Failed to fork thread.");
        return res.json();
      })
      .then((data) => data.newThreadSlug)
      .catch((_) => {
        // Error forking thread (log removed)
        return null;
      });
  },

  /**
   * Uploads and embeds a single file in a single call into a workspace
   * @param {string} slug - workspace slug
   * @param {FormData} formData
   * @returns {Promise<{response: {ok: boolean}, data: {success: boolean, error: string|null, document: {id: string, location:string}|null}}>}
   */
  async uploadAndEmbedFile(
    slug: string,
    formData: FormData
  ): Promise<UploadResponse> {
    const slugModule = useUserStore.getState().selectedModule;

    // Get auth header without Content-Type (let browser set it for multipart)
    const headers = baseHeaders();
    delete (headers as any)["Content-Type"]; // Remove Content-Type to let browser set boundary

    const response = await fetch(
      `${API_BASE}/workspace/${slug}/upload-and-embed/${slugModule}`,
      { method: "POST", body: formData, headers }
    );

    const data = await response.json();
    return { response, data };
  },

  async upgradeUserPrompt(
    prompt: string,
    settings_suffix: string | null = null
  ): Promise<UploadResponse> {
    const body: { prompt: string; settings_suffix?: string } = { prompt };
    if (settings_suffix) {
      body.settings_suffix = settings_suffix;
    }

    const response = await fetch(`${API_BASE}/upgrade-prompt`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(body),
    });

    const data = await response.json();
    return { response, data };
  },

  async upgradeDeepSearchPrompt(prompt: string): Promise<UploadResponse> {
    const response = await fetch(`${API_BASE}/upgrade-deepsearch-prompt`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ prompt }),
    });

    const data = await response.json();
    return { response, data };
  },

  /**
   * Deletes and un-embeds a single file in a single call from a workspace
   * @param {string} slug - workspace slug
   * @param {string} documentLocation - location of file eg: custom-documents/my-file-uuid.json
   * @returns {Promise<boolean>}
   */
  async deleteAndUnembedFile(
    slug: string,
    documentLocation: string
  ): Promise<boolean> {
    const response = await fetch(
      `${API_BASE}/workspace/${slug}/remove-and-unembed`,
      {
        method: "DELETE",
        body: JSON.stringify({ documentLocation }),
        headers: baseHeaders(),
      }
    );
    return response.ok;
  },

  /**
   * Gets the vector count for a workspace from the stats endpoint
   * @param {string} slug - workspace slug
   * @returns {Promise<number>} - number of vectors in the workspace
   */
  async getVectorCount(slug: string): Promise<number> {
    try {
      const response = await fetch(`${API_BASE}/workspace/${slug}/stats`, {
        method: "GET",
        headers: baseHeaders(),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch workspace stats");
      }

      const data = await response.json();
      return data.stats?.vectorCount || 0;
    } catch (error) {
      // Error fetching vector count (log removed)
      return 0;
    }
  },

  /**
   * Gets model preferences (chat and embedding models) from workspace data
   * @param {string} slug - workspace slug
   * @returns {Promise<{chatModel: string | null, embeddingModel: string | null}>}
   */
  async getModelPreference(
    slug: string
  ): Promise<{ chatModel: string | null; embeddingModel: string | null }> {
    try {
      const workspace = await this.bySlug(slug);
      return {
        chatModel: workspace?.chatModel || null,
        embeddingModel: workspace?.embeddingModel || null,
      };
    } catch (error) {
      // Error fetching model preferences (log removed)
      return { chatModel: null, embeddingModel: null };
    }
  },

  /**
   * Resets a chat conversation by deleting messages after a specific chat ID
   * @param {string} slug - workspace slug
   * @param {string} chatId - chat ID to reset from
   * @returns {Promise<boolean>} - success status
   */
  async resetChat(slug: string, chatId: string): Promise<boolean> {
    // Reset chat by deleting edited chats starting from the given chatId
    return await this.deleteEditedChats(slug, "", chatId);
  },

  /**
   * Gets all documents in a folder for a workspace
   * @param {string} slug - workspace slug
   * @param {string} folderName - name of the folder
   * @returns {Promise<Array>} - array of documents in the folder
   */
  async getFolderDocuments(
    slug: string,
    folderName: string
  ): Promise<Document[]> {
    try {
      // First get the workspace with all documents
      const workspace = await this.bySlug(slug, true);

      // Check both possible document properties (documents or workspace_documents)
      const documents =
        workspace?.documents || workspace?.workspace_documents || [];

      if (!workspace || documents.length === 0) {
        // No workspace or documents found
        return [];
      }

      // Filter documents that belong to the specified folder
      // The folder path should be at the beginning of the document path
      const folderPath = folderName.endsWith("/")
        ? folderName
        : `${folderName}/`;

      // Looking for documents in folder

      // Additional debugging - document paths check

      const documentsInFolder = documents.filter((doc) => {
        const docPath = doc.docpath || doc.docPath;

        if (!docPath) return false;
        // Normalize paths for consistent comparison
        const normalizedDocPath = docPath.replace(/\\/g, "/");
        const normalizedFolderPath = folderPath.replace(/\\/g, "/");

        // Ensure folder path ends with / for proper comparison
        const folderPathWithSlash = normalizedFolderPath.endsWith("/")
          ? normalizedFolderPath
          : `${normalizedFolderPath}/`;

        // Check if document is in this folder (at any level)
        // This handles both direct children and nested files
        const isInFolder =
          normalizedDocPath.startsWith(folderPathWithSlash) ||
          normalizedDocPath.includes(`/${folderPathWithSlash}`);

        // Check if document is in folder
        return isInFolder;
      });

      // Found documents in folder
      return documentsInFolder;
    } catch (error) {
      // Error getting documents for folder
      return [];
    }
  },

  threads: WorkspaceThread,

  async getDocumentDraftingSuggestedMessages(slug: string): Promise<any[]> {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/document-drafting-suggested-messages`,
        { method: "GET", headers: baseHeaders() }
      );
      const result = await response.json();
      return result.messages || [];
    } catch (error) {
      // Error fetching document drafting suggested messages
      return [];
    }
  },

  async setDocumentDraftingSuggestedMessages(
    slug: string,
    messages: any[]
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/document-drafting-suggested-messages`,
        {
          method: "POST",
          headers: baseHeaders(),
          body: JSON.stringify({ messages }),
        }
      );
      const result = await response.json();
      return { success: response.ok, error: result.message };
    } catch (error: any) {
      // Error setting document drafting suggested messages
      return { success: false, error: error.message };
    }
  },

  async deleteWorkspace(workspaceId: string): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE}/workspace/${workspaceId}`, {
        method: "DELETE",
      });
      const success = response.ok;
      if (success) {
        useWorkspaceStore.getState().removeWorkspace(workspaceId);
      }
      return success;
    } catch (error) {
      // Error deleting workspace (log removed)
      return false;
    }
  },

  async deleteThread(workspaceId: string, threadId: string): Promise<boolean> {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${workspaceId}/thread/${threadId}`,
        { method: "DELETE" }
      );
      const success = response.ok;
      if (success) {
        window.dispatchEvent(
          new CustomEvent("THREAD_DELETED", {
            detail: { workspaceId, threadId },
          })
        );
      }
      return success;
    } catch (error) {
      // Error deleting thread (log removed)
      return false;
    }
  },

  // Sharing methods
  async getShareStatus(slug: string): Promise<ShareResponse> {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/share-status`,
        {
          method: "GET",
          headers: baseHeaders(),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch share status");
      }

      return await response.json();
    } catch (error: any) {
      // Error fetching workspace share status (log removed)
      return { success: false, error: error.message };
    }
  },

  async share(slug: string, options: ShareOptions): Promise<ShareResponse> {
    const { userIds = [], shareWithOrg = undefined } = options;
    try {
      const response = await fetch(`${API_BASE}/workspace/${slug}/share`, {
        method: "POST",
        headers: baseHeaders(),
        body: JSON.stringify({ userIds, shareWithOrg }),
      });

      if (!response.ok) {
        throw new Error("Failed to share workspace");
      }

      return await response.json();
    } catch (error: any) {
      // Error sharing workspace (log removed)
      return { success: false, error: error.message };
    }
  },

  async revokeShareUser(slug: string, userId: string): Promise<ShareResponse> {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/revoke-share`,
        {
          method: "POST",
          headers: baseHeaders(),
          body: JSON.stringify({ userId }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to revoke workspace share");
      }

      return await response.json();
    } catch (error: any) {
      // Error revoking workspace share (log removed)
      return { success: false, error: error.message };
    }
  },

  // Thread sharing methods
  async getThreadShareStatus(threadId: string): Promise<ShareResponse> {
    try {
      const thread = await WorkspaceThread.get(threadId);
      if (!thread) {
        throw new Error("Thread not found");
      }

      const response = await fetch(
        `${API_BASE}/workspace/${thread.workspaceSlug}/thread/${thread.slug}/share-status`,
        {
          method: "GET",
          headers: baseHeaders(),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch thread share status");
      }

      return await response.json();
    } catch (error: any) {
      // Error fetching thread share status (log removed)
      return { success: false, error: error.message };
    }
  },

  async shareThread(
    threadId: string,
    options: ShareOptions
  ): Promise<ShareResponse> {
    const { userIds = [], shareWithOrg = undefined } = options;
    try {
      const thread = await WorkspaceThread.get(threadId);
      if (!thread) {
        throw new Error("Thread not found");
      }

      const response = await fetch(
        `${API_BASE}/workspace/${thread.workspaceSlug}/thread/${thread.slug}/share`,
        {
          method: "POST",
          headers: baseHeaders(),
          body: JSON.stringify({ userIds, shareWithOrg }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to share thread");
      }

      return await response.json();
    } catch (error: any) {
      // Error sharing thread (log removed)
      return { success: false, error: error.message };
    }
  },

  async revokeThreadShareUser(
    threadId: string,
    userId: string
  ): Promise<ShareResponse> {
    try {
      const thread = await WorkspaceThread.get(threadId);
      if (!thread) {
        throw new Error("Thread not found");
      }

      const response = await fetch(
        `${API_BASE}/workspace/${thread.workspaceSlug}/thread/${thread.slug}/revoke-share`,
        {
          method: "POST",
          headers: baseHeaders(),
          body: JSON.stringify({ userId }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to revoke thread share");
      }

      return await response.json();
    } catch (error: any) {
      // Error revoking thread share (log removed)
      return { success: false, error: error.message };
    }
  },

  // API Key Management
  async generateApiKey(
    slug: string
  ): Promise<{ success: boolean; apiKey?: string; error?: string }> {
    try {
      const response = await fetch(`${API_BASE}/workspace/${slug}/api-key`, {
        method: "POST",
        headers: baseHeaders(),
      });

      if (!response.ok) {
        throw new Error("Failed to generate API key");
      }

      const data = await response.json();
      return { success: true, apiKey: data.apiKey };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  },

  async deleteApiKey(
    slug: string,
    keyId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/api-key/${keyId}`,
        {
          method: "DELETE",
          headers: baseHeaders(),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to delete API key");
      }

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  },

  async getApiKeys(
    slug: string
  ): Promise<{ success: boolean; apiKeys?: any[]; error?: string }> {
    try {
      // Note: This endpoint may need to be implemented in the backend
      const response = await fetch(`${API_BASE}/workspace/${slug}/api-keys`, {
        method: "GET",
        headers: baseHeaders(),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch API keys");
      }

      const data = await response.json();
      return { success: true, apiKeys: data.apiKeys || [] };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  },

  // Agent Memory Management (already implemented in backend)
  async getAgentMemory(
    slug: string,
    chatId: string
  ): Promise<{ success: boolean; memory?: any; error?: string }> {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/memory/${chatId}`,
        {
          method: "GET",
          headers: baseHeaders(),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch agent memory");
      }

      const data = await response.json();
      return { success: true, memory: data.memory };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  },

  async setAgentMemory(
    slug: string,
    chatId: string,
    memory: any
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/set-memory/${chatId}`,
        {
          method: "POST",
          headers: baseHeaders(),
          body: JSON.stringify({ memory }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to set agent memory");
      }

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  },

  async clearAgentMemory(
    slug: string,
    chatId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/memory/${chatId}`,
        {
          method: "DELETE",
          headers: baseHeaders(),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to clear agent memory");
      }

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  },

  async addAgentMemory(
    slug: string,
    chatId: string,
    memoryItem: any
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/memory/${chatId}/add`,
        {
          method: "POST",
          headers: baseHeaders(),
          body: JSON.stringify({ memoryItem }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to add agent memory");
      }

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  },

  async removeAgentMemory(
    slug: string,
    chatId: string,
    memoryId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/memory/${chatId}/remove/${memoryId}`,
        {
          method: "DELETE",
          headers: baseHeaders(),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to remove agent memory");
      }

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  },

  // Agent Configuration
  async getAgentConfig(
    slug: string
  ): Promise<{ success: boolean; config?: any; error?: string }> {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/agent-config`,
        {
          method: "GET",
          headers: baseHeaders(),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch agent config");
      }

      const data = await response.json();
      return { success: true, config: data.config };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  },

  async updateAgentConfig(
    slug: string,
    config: any
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/agent-config`,
        {
          method: "POST",
          headers: baseHeaders(),
          body: JSON.stringify({ config }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update agent config");
      }

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  },

  // Additional workspace stats and analytics methods
  async getWorkspaceAnalytics(
    slug: string
  ): Promise<{ success: boolean; analytics?: any; error?: string }> {
    try {
      // Use the existing stats endpoint for analytics data
      const response = await fetch(`${API_BASE}/workspace/${slug}/stats`, {
        method: "GET",
        headers: baseHeaders(),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch workspace analytics");
      }

      const data = await response.json();
      return {
        success: true,
        analytics: {
          documentCount: data.stats?.documentCount || 0,
          chatCount: data.stats?.chatCount || 0,
          userCount: data.stats?.userCount || 0,
          vectorCount: data.stats?.vectorCount || 0,
          storageSize: data.stats?.storageSize || 0,
        },
      };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  },

  // Clone workspace functionality
  async cloneWorkspace(
    slug: string,
    newName?: string
  ): Promise<{ success: boolean; newWorkspace?: any; error?: string }> {
    try {
      const response = await fetch(`${API_BASE}/workspace/${slug}/clone`, {
        method: "POST",
        headers: baseHeaders(),
        body: JSON.stringify({ newName }),
      });

      if (!response.ok) {
        throw new Error("Failed to clone workspace");
      }

      const data = await response.json();
      return { success: true, newWorkspace: data.workspace };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  },
};

export default WorkspaceModel;
