import PostgreS<PERSON>Logo from "./icons/postgresql.png";
import MyS<PERSON><PERSON>ogo from "./icons/mysql.png";
import MSSQL<PERSON>ogo from "./icons/mssql.png";
import { X } from "@phosphor-icons/react";
import { useConfirmation } from "@/hooks/useConfirmation";
import { useTranslation } from "react-i18next";
import Modal from "@/components/ui/Modal";
import { Button } from "@/components/Button";

export const DB_LOGOS: Record<string, string> = {
  postgresql: PostgreSQLLogo,
  mysql: MySQLLogo,
  "sql-server": MSSQLLogo,
};

interface Connection {
  database_id: string;
  engine: string;
}

interface DBConnectionProps {
  connection: Connection;
  onRemove: (databaseId: string) => void;
  setHasChanges: (hasChanges: boolean) => void;
}

export default function DBConnection({
  connection,
  onRemove,
  setHasChanges,
}: DBConnectionProps): JSX.Element {
  const { t } = useTranslation();
  const { database_id, engine } = connection;
  const {
    isConfirmationOpen,
    openConfirmation,
    closeConfirmation,
    handleConfirm,
  } = useConfirmation();

  function removeConfirmation(): void {
    openConfirmation(() => {
      onRemove(database_id);
      setHasChanges(true);
    });
  }

  return (
    <>
      <div className="flex gap-x-4 items-center">
        <img
          src={DB_LOGOS?.[engine] ?? ""}
          alt={`${engine} logo`}
          className="w-10 h-10 rounded-md"
        />
        <div className="flex w-full items-center justify-between">
          <div className="flex flex-col">
            <div className="text-sm font-semibold text-white">
              {database_id}
            </div>
            <div className="mt-1 text-xs text-[#D2D5DB]">{engine}</div>
          </div>
          <button
            type="button"
            onClick={removeConfirmation}
            className="border-none text-white/40 hover:text-red-500"
          >
            <X size={24} />
          </button>
        </div>
      </div>
      <Modal
        isOpen={isConfirmationOpen}
        onClose={closeConfirmation}
        title={t("sql.delete-connection")}
        footer={
          <>
            <Button variant="outline" onClick={closeConfirmation}>
              {t("common.cancel")}
            </Button>
            <Button variant="destructive" onClick={handleConfirm}>
              {t("button.delete")}
            </Button>
          </>
        }
      >
        <p className="text-foreground">
          {`Delete ${database_id} from the list of available SQL connections? This cannot be undone.`}
        </p>
      </Modal>
    </>
  );
}
