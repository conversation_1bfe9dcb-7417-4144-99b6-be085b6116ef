import { useState, FormEvent } from "react";
import Modal from "@/components/ui/Modal";
import { WarningOctagon } from "@phosphor-icons/react";
import { DB_LOGOS } from "./DBConnection";
import { Button } from "@/components/Button";
import { useTranslation } from "react-i18next";

interface ConnectionConfig {
  username: string | null;
  password: string | null;
  host: string | null;
  port: string | null;
  database: string | null;
}

interface AssembleConnectionStringParams {
  engine: string;
  username?: string;
  password?: string;
  host?: string;
  port?: string;
  database?: string;
}

interface ConnectionSubmissionData {
  engine: string;
  database_id: string;
  connectionString: string;
}

function assembleConnectionString({
  engine,
  username = "",
  password = "",
  host = "",
  port = "",
  database = "",
}: AssembleConnectionStringParams): string {
  if ([username, password, host, database].every((i) => !!i) === false)
    return `Please fill out all the fields above.`;
  switch (engine) {
    case "postgresql":
      return `postgres://${username}:${password}@${host}:${port}/${database}`;
    case "mysql":
      return `mysql://${username}:${password}@${host}:${port}/${database}`;
    case "sql-server":
      return `mssql://${username}:${password}@${host}:${port}/${database}`;
    default:
      return "";
  }
}

const DEFAULT_ENGINE = "postgresql";
const DEFAULT_CONFIG: ConnectionConfig = {
  username: null,
  password: null,
  host: null,
  port: null,
  database: null,
};

interface NewSQLConnectionProps {
  isOpen: boolean;
  closeModal: () => void;
  onSubmit: (data: ConnectionSubmissionData) => void;
}

export default function NewSQLConnection({
  isOpen,
  closeModal,
  onSubmit,
}: NewSQLConnectionProps): JSX.Element | null {
  const [engine, setEngine] = useState<string>(DEFAULT_ENGINE);
  const [config, setConfig] = useState<ConnectionConfig>(DEFAULT_CONFIG);
  const { t } = useTranslation();

  if (!isOpen) return null;

  function handleClose(): void {
    setEngine(DEFAULT_ENGINE);
    setConfig(DEFAULT_CONFIG);
    closeModal();
  }

  function onFormChange(): void {
    const formElement = document.getElementById("sql-connection-form");
    if (!formElement) return;
    const form = new FormData(formElement as HTMLFormElement);
    setConfig({
      username: (form.get("username") as string)?.trim() || null,
      password: (form.get("password") as string) || null,
      host: (form.get("host") as string)?.trim() || null,
      port: (form.get("port") as string)?.trim() || null,
      database: (form.get("database") as string)?.trim() || null,
    });
  }

  async function handleUpdate(e: FormEvent<HTMLFormElement>): Promise<boolean> {
    e.preventDefault();
    e.stopPropagation();
    const form = new FormData(e.target as HTMLFormElement);
    onSubmit({
      engine,
      database_id: (form.get("name") as string) || "",
      connectionString: assembleConnectionString({
        engine,
        username: config.username || "",
        password: config.password || "",
        host: config.host || "",
        port: config.port || "",
        database: config.database || "",
      }),
    });
    handleClose();
    return false;
  }

  // Cannot do nested forms, it will cause all sorts of issues, so we portal this out
  // to the parent container form so we don't have nested forms.
  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="New SQL Connection"
      footer={
        <>
          <Button variant="secondary" onClick={handleClose}>
            {t("button.cancel")}
          </Button>
          <Button form="sql-connection-form">{t("button.save")}</Button>
        </>
      }
    >
      <form
        id="sql-connection-form"
        onSubmit={handleUpdate}
        onChange={onFormChange}
      >
        <div className="py-[17px] px-[20px] flex flex-col gap-y-6">
          <p className="text-sm text-foreground">
            Add the connection information for your database below and it will
            be available for future SQL agent calls.
          </p>
          <div className="flex flex-col w-full">
            <div className="border border-red-800 p-4 rounded-lg flex items-center gap-x-2 text-sm text-red-400">
              <WarningOctagon size={28} className="shrink-0" />
              <p>
                <b>WARNING:</b> The SQL agent has been <i>instructed</i> to only
                perform non-modifying queries. This <b>does not</b> prevent a
                hallucination from still deleting data. Only connect with a user
                who has <b>READ_ONLY</b> permissions.
              </p>
            </div>

            <label className="text-foreground text-sm font-semibold block my-4">
              Select your SQL engine
            </label>
            <div className="grid md:grid-cols-4 gap-4 grid-cols-2">
              <DBEngine
                provider="postgresql"
                active={engine === "postgresql"}
                onClick={(): void => setEngine("postgresql")}
              />
              <DBEngine
                provider="mysql"
                active={engine === "mysql"}
                onClick={(): void => setEngine("mysql")}
              />
              <DBEngine
                provider="sql-server"
                active={engine === "sql-server"}
                onClick={(): void => setEngine("sql-server")}
              />
            </div>
          </div>

          <div className="flex flex-col w-full">
            <label className="text-foreground text-sm font-semibold block mb-4">
              Connection name
            </label>
            <input
              type="text"
              name="name"
              placeholder="a unique name to identify this SQL connection"
              required={true}
              autoComplete="off"
              spellCheck={false}
              className="dark-input-mdl w-full text-foreground  text-sm rounded-lg block p-2"
            />
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div className="flex flex-col">
              <label className="text-foreground text-sm font-semibold block mb-4">
                Database user
              </label>
              <input
                type="text"
                name="username"
                placeholder="root"
                required={true}
                autoComplete="off"
                spellCheck={false}
                className="dark-input-mdl w-full text-foreground  text-sm rounded-lg block p-2"
              />
            </div>
            <div className="flex flex-col">
              <label className="text-foreground text-sm font-semibold block mb-4">
                Database user password
              </label>
              <input
                type="text"
                name="password"
                placeholder="password123"
                required={true}
                autoComplete="off"
                spellCheck={false}
                className="dark-input-mdl w-full text-foreground  text-sm rounded-lg block p-2"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div className="sm:col-span-2">
              <label className="text-foreground text-sm font-semibold block mb-4">
                Server endpoint
              </label>
              <input
                type="text"
                name="host"
                placeholder="the hostname or endpoint for your database"
                required={true}
                autoComplete="off"
                spellCheck={false}
                className="dark-input-mdl w-full text-foreground  text-sm rounded-lg block p-2"
              />
            </div>
            <div>
              <label className="text-foreground text-sm font-semibold block mb-4">
                Port
              </label>
              <input
                type="text"
                name="port"
                placeholder="3306"
                required={false}
                autoComplete="off"
                spellCheck={false}
                className="dark-input-mdl w-full text-foreground  text-sm rounded-lg block p-2"
              />
            </div>
          </div>

          <div className="flex flex-col">
            <label className="text-foreground text-sm font-semibold block mb-4">
              Database
            </label>
            <input
              type="text"
              name="database"
              placeholder="the database the agent will interact with"
              required={true}
              autoComplete="off"
              spellCheck={false}
              className="dark-input-mdl w-full text-foreground  text-sm rounded-lg block p-2"
            />
          </div>
          <p className="text-white/40 text-sm">
            {assembleConnectionString({
              engine,
              username: config.username || "",
              password: config.password || "",
              host: config.host || "",
              port: config.port || "",
              database: config.database || "",
            })}
          </p>
        </div>
      </form>
    </Modal>
  );
}

interface DBEngineProps {
  provider: string;
  active: boolean;
  onClick: () => void;
}

function DBEngine({ provider, active, onClick }: DBEngineProps): JSX.Element {
  return (
    <Button
      type="button"
      onClick={onClick}
      className={`flex flex-col p-4 primary-bg rounded-lg w-fit ${
        active ? "!bg-blue-500/50" : ""
      }`}
      variant="outline"
    >
      <img
        src={DB_LOGOS[provider as keyof typeof DB_LOGOS]}
        className="h-[100px] rounded-md"
        alt={provider}
      />
    </Button>
  );
}
