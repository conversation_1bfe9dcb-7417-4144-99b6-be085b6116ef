import { useEffect, useState } from "react";
import DBConnection from "./DBConnection";
import { Plus, Database } from "@phosphor-icons/react";
import NewSQLConnection from "./NewConnectionModal";
import { useModal } from "@/hooks/useModal";
import Toggle from "@/components/ui/Toggle";
import SQLAgentImage from "@/media/agents/sql-agent.png";
import Admin from "@/models/admin";

interface Connection {
  database_id: string;
  engine: string;
  connectionString: string;
  action?: string;
}

interface NewConnectionData {
  engine: string;
  database_id: string;
  connectionString: string;
}

interface AgentSQLConnectorSelectionProps {
  skill: string;
  toggleSkill: (skill: string) => void;
  enabled?: boolean;
  setHasChanges: (hasChanges: boolean) => void;
}

export default function AgentSQLConnectorSelection({
  skill,
  toggleSkill,
  enabled = false,
  setHasChanges,
}: AgentSQLConnectorSelectionProps): JSX.Element {
  const { isOpen, openModal, closeModal } = useModal();
  const [connections, setConnections] = useState<Connection[]>([]);

  useEffect(() => {
    Admin.systemPreferencesByFields(["agent_sql_connections"])
      .then((res) => setConnections(res?.settings?.agent_sql_connections ?? []))
      .catch(() => setConnections([]));
  }, []);

  return (
    <>
      <div className="p-2">
        <div className="flex flex-col gap-y-[18px] max-w-[500px]">
          <div className="flex items-center gap-x-2">
            <Database size={24} className="text-foreground" weight="bold" />
            <label htmlFor="name" className="text-foreground text-md font-bold">
              SQL Agent
            </label>
            <div className="ml-auto">
              <Toggle
                checked={enabled}
                onChange={(): void => toggleSkill(skill)}
              />
            </div>
          </div>
          <img
            src={SQLAgentImage}
            alt="SQL Agent"
            className="w-full rounded-md"
          />
          <p className="text-foreground text-opacity-60 text-xs font-medium py-1.5">
            Enable your agent to be able to leverage SQL to answer you questions
            by connecting to various SQL database providers.
          </p>
          {enabled && (
            <>
              <input
                name="system::agent_sql_connections"
                type="hidden"
                value={JSON.stringify(connections)}
              />
              <input
                type="hidden"
                value={JSON.stringify(
                  connections.filter((conn) => conn.action !== "remove")
                )}
              />
              <div className="flex flex-col mt-2 gap-y-2">
                <p className="text-foreground font-semibold text-sm">
                  Your database connections
                </p>
                <div className="flex flex-col gap-y-3">
                  {connections
                    .filter((connection) => connection.action !== "remove")
                    .map((connection) => (
                      <DBConnection
                        key={connection.database_id}
                        connection={connection}
                        onRemove={(databaseId: string): void => {
                          setHasChanges(true);
                          setConnections((prev) =>
                            prev.map((conn) => {
                              if (conn.database_id === databaseId)
                                return { ...conn, action: "remove" };
                              return conn;
                            })
                          );
                        }}
                        setHasChanges={setHasChanges}
                      />
                    ))}
                  <button
                    type="button"
                    onClick={openModal}
                    className="w-fit relative flex h-[40px] items-center border-none hover:bg-slate-600/20 rounded-lg"
                  >
                    <div className="flex w-full gap-x-2 items-center p-4">
                      <div className="bg-zinc-600 p-2 rounded-lg h-[24px] w-[24px] flex items-center justify-center">
                        <Plus
                          weight="bold"
                          size={14}
                          className="shrink-0 text-slate-100"
                        />
                      </div>
                      <p className="text-left text-foreground text-sm">
                        New SQL connection
                      </p>
                    </div>
                  </button>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
      <NewSQLConnection
        isOpen={isOpen}
        closeModal={closeModal}
        onSubmit={(newDb: NewConnectionData): void =>
          setConnections((prev) => [...prev, { action: "add", ...newDb }])
        }
      />
    </>
  );
}
