interface SearchProvider {
  name: string;
  value: string;
  logo?: string;
  icon?: React.ReactNode;
  description: string;
}

interface SearchProviderItemProps {
  provider: SearchProvider;
  checked: boolean;
  onClick: () => void;
}

export default function SearchProviderItem({
  provider,
  checked,
  onClick,
}: SearchProviderItemProps): JSX.Element {
  const { name, value, logo, icon, description } = provider;
  return (
    <div
      onClick={onClick}
      className={`w-full p-2 rounded-md hover:cursor-pointer hover:bg-[#e2f3fa] ${
        checked ? "modal-list-Active" : "modal-list-Inactive"
      }`}
    >
      <input
        type="checkbox"
        value={value}
        className="peer hidden"
        checked={checked}
        readOnly={true}
        formNoValidate={true}
      />
      <div className="flex gap-x-4 items-center">
        {logo ? (
          <img
            src={logo}
            alt={`${name} logo`}
            className="w-10 h-10 rounded-md"
          />
        ) : icon ? (
          <div className="w-10 h-10 rounded-md flex items-center justify-center">
            {icon}
          </div>
        ) : null}
        <div className="flex flex-col">
          <div className="text-sm font-semibold text-foreground">{name}</div>
          <div className="mt-1 text-xs text-foreground">{description}</div>
        </div>
      </div>
    </div>
  );
}
