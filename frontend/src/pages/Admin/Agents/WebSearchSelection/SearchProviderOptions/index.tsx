interface SearchSettings {
  AgentGoogleSearchEngineId?: string;
  AgentGoogleSearchEngineKey?: string;
  AgentSearchApiKey?: string;
  AgentSearchApiEngine?: string;
  AgentSerperApiKey?: string;
  AgentBingSearchApiKey?: string;
  AgentSerplyApiKey?: string;
  AgentSearXNGApiUrl?: string;
  AgentTavilyApiKey?: string;
}

interface SearchProviderOptionsProps {
  settings?: SearchSettings;
}

export function GoogleSearchOptions({
  settings,
}: SearchProviderOptionsProps): JSX.Element {
  return (
    <>
      <p className="text-sm text-foreground my-2">
        You can get a free search engine & API key{" "}
        <a
          href="https://programmablesearchengine.google.com/controlpanel/create"
          target="_blank"
          rel="noreferrer"
          className="text-blue-500 underline"
        >
          from Google here.
        </a>
      </p>
      <div className="flex gap-x-4">
        <div className="flex flex-col w-full">
          <label className="text-foreground text-sm font-semibold block mb-4">
            Search engine ID
          </label>
          <input
            type="text"
            name="env::AgentGoogleSearchEngineId"
            className="border-none text-foreground  text-sm rounded-lg block w-full p-2.5"
            placeholder="Google Search Engine Id"
            defaultValue={settings?.AgentGoogleSearchEngineId}
            required={true}
            autoComplete="off"
            spellCheck={false}
          />
        </div>
        <div className="flex flex-col w-full">
          <label className="text-foreground text-sm font-semibold block mb-4">
            Programmatic Access API Key
          </label>
          <input
            type="password"
            name="env::AgentGoogleSearchEngineKey"
            className="border-none text-foreground  text-sm rounded-lg   block w-full p-2.5"
            placeholder="Google Search Engine API Key"
            defaultValue={
              settings?.AgentGoogleSearchEngineKey ? "*".repeat(20) : ""
            }
            required={true}
            autoComplete="off"
            spellCheck={false}
          />
        </div>
      </div>
    </>
  );
}

interface SearchApiEngine {
  name: string;
  value: string;
}

const SearchApiEngines: SearchApiEngine[] = [
  { name: "Google Search", value: "google" },
  { name: "Google Maps", value: "google_maps" },
  { name: "Google Shopping", value: "google_shopping" },
  { name: "Google News", value: "google_news" },
  { name: "Google Jobs", value: "google_jobs" },
  { name: "Google Scholar", value: "google_scholar" },
  { name: "Google Finance", value: "google_finance" },
  { name: "Google Patents", value: "google_patents" },
  { name: "YouTube", value: "youtube" },
  { name: "Bing", value: "bing" },
  { name: "Bing News", value: "bing_news" },
  { name: "Amazon Product Search", value: "amazon_search" },
  { name: "Baidu", value: "baidu" },
];

export function SearchApiOptions({
  settings,
}: SearchProviderOptionsProps): JSX.Element {
  return (
    <>
      <p className="text-sm text-foreground my-2">
        You can get a free API key{" "}
        <a
          href="https://www.searchapi.io/"
          target="_blank"
          rel="noreferrer"
          className="text-blue-500 underline"
        >
          from SearchApi.
        </a>
      </p>
      <div className="flex gap-x-4">
        <div className="flex flex-col w-60">
          <label className="text-white text-sm font-semibold block mb-3">
            API Key
          </label>
          <input
            type="password"
            name="env::AgentSearchApiKey"
            className="border-none bg-zinc-900 text-white placeholder:text-white/20 text-sm rounded-lg focus:outline-primary-button active:outline-primary-button outline-none block w-full p-2.5"
            placeholder="SearchApi API Key"
            defaultValue={settings?.AgentSearchApiKey ? "*".repeat(20) : ""}
            required={true}
            autoComplete="off"
            spellCheck={false}
          />
        </div>
        <div className="flex flex-col w-60">
          <label className="text-white text-sm font-semibold block mb-3">
            Engine
          </label>
          <select
            name="env::AgentSearchApiEngine"
            required={true}
            className="border-none bg-zinc-900 text-white placeholder:text-white/20 text-sm rounded-lg focus:outline-primary-button active:outline-primary-button outline-none block w-full p-2.5"
            defaultValue={settings?.AgentSearchApiEngine || "google"}
          >
            {SearchApiEngines.map(({ name, value }: SearchApiEngine) => (
              <option key={name} value={value}>
                {name}
              </option>
            ))}
          </select>
        </div>
      </div>
    </>
  );
}

export function SerperDotDevOptions({
  settings,
}: SearchProviderOptionsProps): JSX.Element {
  return (
    <>
      <p className="text-sm text-white my-2">
        You can get a free API key{" "}
        <a
          href="https://serper.dev"
          target="_blank"
          rel="noreferrer"
          className="text-blue-300 underline"
        >
          from Serper.dev.
        </a>
      </p>
      <div className="flex gap-x-4">
        <div className="flex flex-col w-full">
          <label className="text-foreground text-sm font-semibold block mb-4">
            API Key
          </label>
          <input
            type="password"
            name="env::AgentSerperApiKey"
            className="border-none text-foreground  text-sm rounded-lg   block w-full p-2.5"
            placeholder="Serper.dev API Key"
            defaultValue={settings?.AgentSerperApiKey ? "*".repeat(20) : ""}
            required={true}
            autoComplete="off"
            spellCheck={false}
          />
        </div>
      </div>
    </>
  );
}

export function BingSearchOptions({
  settings,
}: SearchProviderOptionsProps): JSX.Element {
  return (
    <>
      <p className="text-sm text-foreground my-2">
        You can get a Bing Web Search API subscription key{" "}
        <a
          href="https://portal.azure.com/"
          target="_blank"
          rel="noreferrer"
          className="text-blue-300 underline"
        >
          from the Azure portal.
        </a>
      </p>
      <div className="flex gap-x-4">
        <div className="flex flex-col w-full">
          <label className="text-foreground text-sm font-semibold block mb-4">
            API Key
          </label>
          <input
            type="password"
            name="env::AgentBingSearchApiKey"
            className="border-none text-foreground  text-sm rounded-lg   block w-full p-2.5"
            placeholder="Bing Web Search API Key"
            defaultValue={settings?.AgentBingSearchApiKey ? "*".repeat(20) : ""}
            required={true}
            autoComplete="off"
            spellCheck={false}
          />
        </div>
      </div>
      <p className="text-sm text-white my-2">
        To set up a Bing Web Search API subscription:
      </p>
      <ol className="list-decimal text-sm text-white ml-6">
        <li>
          Go to the Azure portal:{" "}
          <a
            href="https://portal.azure.com/"
            target="_blank"
            rel="noreferrer"
            className="text-blue-300 underline"
          >
            https://portal.azure.com/
          </a>
        </li>
        <li>Create a new Azure account or sign in with an existing one.</li>
        <li>
          Navigate to the &quot;Create a resource&quot; section and search for
          &quot;Bing Search v7&quot;.
        </li>
        <li>
          Select the &quot;Bing Search v7&quot; resource and create a new
          subscription.
        </li>
        <li>
          Choose the pricing tier that suits your needs (free tier available).
        </li>
        <li>Obtain the API key for your Bing Web Search subscription.</li>
      </ol>
    </>
  );
}

export function SerplySearchOptions({
  settings,
}: SearchProviderOptionsProps): JSX.Element {
  return (
    <>
      <p className="text-sm text-white/60 my-2">
        You can get a free API key{" "}
        <a
          href="https://serply.io"
          target="_blank"
          rel="noreferrer"
          className="text-blue-500 underline"
        >
          from Serply.io.
        </a>
      </p>
      <div className="flex gap-x-4">
        <div className="flex flex-col w-60">
          <label className="text-white text-sm font-semibold block mb-4">
            API Key
          </label>
          <input
            type="password"
            name="env::AgentSerplyApiKey"
            className="border-none bg-zinc-900 text-white placeholder:text-white/20 text-sm rounded-lg focus:border-white block w-full p-2.5"
            placeholder="Serply API Key"
            defaultValue={settings?.AgentSerplyApiKey ? "*".repeat(20) : ""}
            required={true}
            autoComplete="off"
            spellCheck={false}
          />
        </div>
      </div>
    </>
  );
}

export function SearXNGOptions({
  settings,
}: SearchProviderOptionsProps): JSX.Element {
  return (
    <div className="flex gap-x-4">
      <div className="flex flex-col w-60">
        <label className="text-white text-sm font-semibold block mb-4">
          SearXNG API base URL
        </label>
        <input
          type="url"
          name="env::AgentSearXNGApiUrl"
          className="border-none bg-zinc-900 text-white placeholder:text-white/20 text-sm rounded-lg focus:border-white block w-full p-2.5"
          placeholder="SearXNG API Key"
          defaultValue={settings?.AgentSearXNGApiUrl}
          required={true}
          autoComplete="off"
          spellCheck={false}
        />
      </div>
    </div>
  );
}

export function TavilySearchOptions({
  settings,
}: SearchProviderOptionsProps): JSX.Element {
  return (
    <>
      <p className="text-sm text-foreground my-2">
        You can get an API key{" "}
        <a
          href="https://tavily.com/"
          target="_blank"
          rel="noreferrer"
          className="text-blue-500 underline"
        >
          from Tavily.
        </a>
      </p>
      <div className="flex gap-x-4">
        <div className="flex flex-col w-60">
          <label className="text-foreground text-sm font-semibold block mb-3">
            API Key
          </label>
          <input
            type="password"
            name="env::AgentTavilyApiKey"
            className="dark-input-mdl w-full text-foreground  text-sm rounded-md block p-2"
            placeholder="Tavily API Key"
            defaultValue={settings?.AgentTavilyApiKey ? "*".repeat(20) : ""}
            required={true}
            autoComplete="off"
            spellCheck={false}
          />
        </div>
      </div>
    </>
  );
}
