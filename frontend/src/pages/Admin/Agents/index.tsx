import { useEffect, useRef, useState, FormEvent, ReactNode } from "react";
import Sidebar from "@/components/SettingsSidebar";
import { isMobile } from "react-device-detect";
import Admin from "@/models/admin";
import System from "@/models/system";
import showToast from "@/utils/toast";
import {
  ArrowLeft,
  CaretLeft,
  CaretRight,
  Plug,
  Robot,
} from "@phosphor-icons/react";
import ContextualSaveBar from "@/components/ContextualSaveBar";
import { castToType } from "@/utils/types";
import { FullScreenLoader } from "@/components/Preloader";
import { defaultSkills, configurableSkills } from "./skills";
import DefaultBadge from "./Badges/default";
import ImportedSkillList from "./Imported/SkillList";
import ImportedSkillConfig from "./Imported/ImportedSkillConfig";
import { Link } from "react-router-dom";
import { MCPServers<PERSON>ist, MCPServerHeader } from "./MCPServers";
import ServerPanel from "./MCPServers/ServerPanel";
import paths from "@/utils/paths";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import { useTranslation } from "react-i18next";
import React from "react";

interface Server {
  name: string;
  running: boolean;
  [key: string]: any;
}

interface SelectedSkill {
  hubId: string;
  name: string;
  description: string;
  author: string;
  author_url: string;
  active: boolean;
  setup_args?: Record<string, any>;
  imported?: boolean;
  [key: string]: any;
}

export default function AdminAgents(): JSX.Element {
  const { t } = useTranslation();
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const [settings, setSettings] = useState<Record<string, any>>({});
  const [selectedSkill, setSelectedSkill] = useState<
    string | SelectedSkill | null
  >(null);
  const [agentSkills, setAgentSkills] = useState<string[]>([]);
  const [importedSkills, setImportedSkills] = useState<SelectedSkill[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [showSkillModal, setShowSkillModal] = useState<boolean>(false);
  const formEl = useRef<HTMLFormElement>(null);

  // MCP Servers are lazy loaded to not block the UI thread
  const [mcpServers, setMcpServers] = useState<Server[]>([]);
  const [selectedMcpServer, setSelectedMcpServer] = useState<Server | null>(
    null
  );

  // Alert user if they try to leave the page with unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent): void => {
      if (hasChanges) {
        event.preventDefault();
        event.returnValue = "";
      }
    };
    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [hasChanges]);

  useEffect(() => {
    async function fetchSettings() {
      const _settings = (await System.keys()) as Record<string, any>;
      const _preferences = (await Admin.systemPreferencesByFields([
        "default_agent_skills",
        "imported_agent_skills",
      ])) as { settings: any };
      setSettings({ ..._settings, preferences: _preferences.settings });
      setAgentSkills(_preferences.settings?.default_agent_skills ?? []);
      setImportedSkills(_preferences.settings?.imported_agent_skills ?? []);
      setLoading(false);
    }
    fetchSettings();
  }, []);

  const toggleAgentSkill = (skillName: string): void => {
    setAgentSkills((prev) => {
      const updatedSkills = prev.includes(skillName)
        ? prev.filter((name) => name !== skillName)
        : [...prev, skillName];
      setHasChanges(true);
      return updatedSkills;
    });
  };

  const toggleMCP = (serverName: string): void => {
    setMcpServers((prev) => {
      return prev.map((server) => {
        if (server.name !== serverName) return server;
        return { ...server, running: !server.running };
      });
    });
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault();
    const data: {
      workspace: { [key: string]: any };
      system: { [key: string]: any };
      env: { [key: string]: any };
    } = {
      workspace: {},
      system: {},
      env: {},
    };

    const form = new FormData(formEl.current ?? undefined);
    for (const [key, value] of form.entries()) {
      if (key.startsWith("system::")) {
        const [_, label] = key.split("system::");
        data.system[label] = String(value);
        continue;
      }

      if (key.startsWith("env::")) {
        const [_, label] = key.split("env::");
        data.env[label] = String(value);
        continue;
      }
      data.workspace[key] = castToType(key, value);
    }

    const response = await Admin.updateSystemPreferences(data.system);
    const { success } = response as { success: boolean };
    await System.updateSystem(data.env);

    if (success) {
      const _settings = (await System.keys()) as Record<string, any>;
      const _preferences = (await Admin.systemPreferencesByFields([
        "default_agent_skills",
        "imported_agent_skills",
      ])) as { settings: any };
      setSettings({ ..._settings, preferences: _preferences.settings });
      setAgentSkills(_preferences.settings?.default_agent_skills ?? []);
      setImportedSkills(_preferences.settings?.imported_agent_skills ?? []);
      showToast(t("agents.preferences-saved"), "success", {
        clear: true,
      });
    } else {
      showToast(t("agents.preferences-failed"), "error", { clear: true });
    }

    setHasChanges(false);
  };

  // Update the click handlers to clear the other selection
  const handleDefaultSkillClick = (skill: string): void => {
    setSelectedMcpServer(null);
    setSelectedSkill(skill);
    if (isMobile) setShowSkillModal(true);
  };

  const handleMCPClick = (server: Server): void => {
    setSelectedSkill(null);
    setSelectedMcpServer(server);
    if (isMobile) setShowSkillModal(true);
  };

  const handleMCPServerDelete = (serverName: string): void => {
    setSelectedMcpServer(null);
    setMcpServers((prev) =>
      prev.filter((server) => server.name !== serverName)
    );
  };

  if (loading) {
    return (
      <div
        className={`relative md:ml-[2px] md:mr-[16px] md:my-[16px] md:rounded-[16px] w-full flex justify-center items-center ${isMobile ? "h-full" : "h-[calc(100%-32px)]"}`}
      >
        <FullScreenLoader />
      </div>
    );
  }

  if (isMobile) {
    return (
      <SkillLayout
        hasChanges={hasChanges}
        handleCancel={() => setHasChanges(false)}
        handleSubmit={handleSubmit}
      >
        <form
          onSubmit={handleSubmit}
          onChange={() => {
            if (
              selectedSkill &&
              typeof selectedSkill === "object" &&
              selectedSkill !== null &&
              "imported" in selectedSkill &&
              selectedSkill.imported
            ) {
              return;
            }
            setHasChanges(true);
          }}
          ref={formEl}
          className="flex flex-col w-full p-4 mt-10"
        >
          <input
            name="system::default_agent_skills"
            type="hidden"
            value={agentSkills.join(",")}
          />

          {/* Skill settings nav */}
          <div
            hidden={showSkillModal}
            className="flex flex-col gap-y-[18px] overflow-y-scroll no-scroll"
          >
            <div hidden={showSkillModal} className="flex flex-col gap-y-[18px]">
              <div className="text-foreground flex items-center gap-x-2">
                <Robot size={24} />
                <p className="text-lg font-medium">{t("agents.title")}</p>
              </div>
              {/* Default skills */}
              <SkillList
                isDefault={true}
                skills={defaultSkills}
                selectedSkill={
                  typeof selectedSkill === "string" ? selectedSkill : null
                }
                handleClick={handleDefaultSkillClick}
              />
              {/* Configurable skills */}
              <SkillList
                skills={configurableSkills}
                selectedSkill={
                  typeof selectedSkill === "string" ? selectedSkill : null
                }
                handleClick={handleDefaultSkillClick}
                activeSkills={agentSkills}
              />
              <div className="text-white flex items-center gap-x-2">
                <Plug size={24} />
                <p className="text-lg font-medium">
                  {t("agents.custom-skills")}
                </p>
              </div>
              <ImportedSkillList
                skills={importedSkills}
                selectedSkill={
                  typeof selectedSkill === "object" &&
                  selectedSkill !== null &&
                  "hubId" in selectedSkill
                    ? selectedSkill.hubId
                    : typeof selectedSkill === "string"
                      ? selectedSkill
                      : null
                }
                handleClick={(skill) => {
                  const found = importedSkills.find(
                    (s) => s.hubId === skill.hubId
                  );
                  if (found) setSelectedSkill(found);
                }}
              />
              <MCPServerHeader
                setMcpServers={setMcpServers}
                setSelectedMcpServer={setSelectedMcpServer}
              >
                {({ loadingMcpServers }) => {
                  return (
                    <MCPServersList
                      isLoading={loadingMcpServers}
                      servers={mcpServers}
                      selectedServer={selectedMcpServer}
                      handleClick={handleMCPClick}
                    />
                  );
                }}
              </MCPServerHeader>
            </div>
          </div>

          {/* Selected agent skill modal */}
          {showSkillModal && (
            <div className="fixed top-0 left-0 w-full h-full bg-sidebar z-30">
              <div className="flex flex-col h-full">
                <div className="flex items-center p-4">
                  <button
                    type="button"
                    onClick={() => {
                      setShowSkillModal(false);
                      setSelectedSkill("");
                    }}
                    className="text-foreground/60 hover:text-foreground transition-colors duration-200"
                  >
                    <div className="flex items-center text-sky-400">
                      <CaretLeft size={24} />
                      <div>{t("agents.back")}</div>
                    </div>
                  </button>
                </div>
                <div className="flex-1 overflow-y-auto p-4">
                  <div className="bg-[#303237] text-foreground rounded-xl p-4 overflow-y-scroll no-scroll">
                    {selectedMcpServer ? (
                      <ServerPanel
                        server={{
                          ...selectedMcpServer,
                          tools: selectedMcpServer.tools || [],
                        }}
                        toggleServer={toggleMCP}
                        onDelete={handleMCPServerDelete}
                      />
                    ) : selectedSkill &&
                      typeof selectedSkill === "object" &&
                      selectedSkill !== null &&
                      "hubId" in selectedSkill &&
                      "name" in selectedSkill &&
                      "description" in selectedSkill &&
                      "author" in selectedSkill &&
                      "author_url" in selectedSkill &&
                      "active" in selectedSkill ? (
                      <ImportedSkillConfig
                        key={selectedSkill.hubId}
                        selectedSkill={selectedSkill}
                        setImportedSkills={setImportedSkills}
                      />
                    ) : typeof selectedSkill === "string" &&
                      configurableSkills[selectedSkill] ? (
                      React.createElement(
                        configurableSkills[selectedSkill].component,
                        {
                          ...configurableSkills[selectedSkill],
                          settings,
                          toggleSkill: toggleAgentSkill,
                          enabled: agentSkills.includes(
                            configurableSkills[selectedSkill]?.skill
                          ),
                          setHasChanges,
                        }
                      )
                    ) : typeof selectedSkill === "string" &&
                      defaultSkills[selectedSkill] ? (
                      React.createElement(
                        defaultSkills[selectedSkill].component,
                        {
                          ...defaultSkills[selectedSkill],
                          settings,
                          toggleSkill: toggleAgentSkill,
                          enabled: agentSkills.includes(
                            defaultSkills[selectedSkill]?.skill ?? ""
                          ),
                          setHasChanges,
                        }
                      )
                    ) : (
                      <div className="flex flex-col items-center justify-center h-full text-foreground/60">
                        <Robot size={40} />
                        <p className="font-medium">
                          {t("agents.select-skill")}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </form>
      </SkillLayout>
    );
  }

  return (
    <SkillLayout
      hasChanges={hasChanges}
      handleCancel={() => setHasChanges(false)}
      handleSubmit={handleSubmit}
    >
      <form
        onSubmit={handleSubmit}
        onChange={() => {
          if (
            selectedSkill &&
            typeof selectedSkill === "object" &&
            selectedSkill !== null &&
            "imported" in selectedSkill &&
            selectedSkill.imported
          ) {
            return;
          }
          setHasChanges(true);
        }}
        ref={formEl}
        className="flex-1 flex gap-x-6 pt-3 pl-4"
      >
        <input
          name="system::default_agent_skills"
          type="hidden"
          value={agentSkills.join(",")}
        />

        {/* Skill settings nav */}
        <div className="flex flex-col gap-y-[18px]">
          {/* Default skills list */}
          <SkillList
            isDefault={true}
            skills={defaultSkills}
            selectedSkill={
              typeof selectedSkill === "string" ? selectedSkill : null
            }
            handleClick={handleDefaultSkillClick}
          />
          {/* Configurable skills */}
          <SkillList
            skills={configurableSkills}
            selectedSkill={
              typeof selectedSkill === "string" ? selectedSkill : null
            }
            handleClick={handleDefaultSkillClick}
            activeSkills={agentSkills}
          />
          <div className="text-white flex items-center gap-x-2">
            <Plug size={24} />
            <p className="text-lg font-medium">{t("agents.custom-skills")}</p>
          </div>
          <ImportedSkillList
            skills={importedSkills}
            selectedSkill={
              typeof selectedSkill === "object" &&
              selectedSkill !== null &&
              "hubId" in selectedSkill
                ? selectedSkill.hubId
                : typeof selectedSkill === "string"
                  ? selectedSkill
                  : null
            }
            handleClick={(skill) => {
              const found = importedSkills.find((s) => s.hubId === skill.hubId);
              if (found) setSelectedSkill(found);
            }}
          />

          <MCPServerHeader
            setMcpServers={setMcpServers}
            setSelectedMcpServer={setSelectedMcpServer}
          >
            {({ loadingMcpServers }) => {
              return (
                <MCPServersList
                  isLoading={loadingMcpServers}
                  servers={mcpServers}
                  selectedServer={selectedMcpServer}
                  handleClick={handleMCPClick}
                />
              );
            }}
          </MCPServerHeader>
        </div>

        {/* Selected agent skill setting panel */}
        <div className="flex-[2] flex flex-col gap-y-[18px] mt-5">
          <div className="deep-xl-input text-foreground rounded-xl flex-1 p-4 overflow-y-scroll no-scroll">
            {selectedMcpServer ? (
              <ServerPanel
                server={{
                  ...selectedMcpServer,
                  tools: selectedMcpServer.tools || [],
                }}
                toggleServer={toggleMCP}
                onDelete={handleMCPServerDelete}
              />
            ) : selectedSkill &&
              typeof selectedSkill === "object" &&
              selectedSkill !== null &&
              "hubId" in selectedSkill &&
              "name" in selectedSkill &&
              "description" in selectedSkill &&
              "author" in selectedSkill &&
              "author_url" in selectedSkill &&
              "active" in selectedSkill ? (
              <ImportedSkillConfig
                key={selectedSkill.hubId}
                selectedSkill={selectedSkill}
                setImportedSkills={setImportedSkills}
              />
            ) : typeof selectedSkill === "string" &&
              configurableSkills[selectedSkill] ? (
              React.createElement(configurableSkills[selectedSkill].component, {
                ...configurableSkills[selectedSkill],
                settings,
                toggleSkill: toggleAgentSkill,
                enabled: agentSkills.includes(
                  configurableSkills[selectedSkill]?.skill
                ),
                setHasChanges,
              })
            ) : typeof selectedSkill === "string" &&
              defaultSkills[selectedSkill] ? (
              React.createElement(defaultSkills[selectedSkill].component, {
                ...defaultSkills[selectedSkill],
                settings,
                toggleSkill: toggleAgentSkill,
                enabled: agentSkills.includes(
                  defaultSkills[selectedSkill]?.skill ?? ""
                ),
                setHasChanges,
              })
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-foreground/60">
                <Robot size={40} />
                <p className="font-medium">{t("agents.select-skill")}</p>
              </div>
            )}
          </div>
        </div>
      </form>
      <ContextualSaveBar
        showing={hasChanges}
        onSave={() => {
          if (formEl.current) {
            const event = new Event("submit", {
              bubbles: true,
              cancelable: true,
            });
            formEl.current.dispatchEvent(event);
          }
        }}
        onCancel={() => setHasChanges(false)}
      />
    </SkillLayout>
  );
}

interface SkillLayoutProps {
  children: ReactNode;
  hasChanges: boolean;
  handleSubmit: (e: FormEvent<HTMLFormElement>) => Promise<void>;
  handleCancel: () => void;
}

function SkillLayout({
  children,
  hasChanges: _hasChanges,
  handleCancel: _handleCancel,
}: SkillLayoutProps): JSX.Element {
  const { t } = useTranslation();
  return (
    <div
      id="workspace-agent-settings-container"
      className="bg-background overflow-hidden w-full h-full flex flex-col"
    >
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        <div className="relative rounded-md w-full h-full overflow-y-scroll">
          <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4">
            <div className="w-full flex flex-row items-center gap-3.5 gap-y-1 pb-3                                     border-bottom">
              <Link to={paths.home()}>
                <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
              </Link>
              <div className="flex flex-col justify-center items-start">
                <div className="flex flex-row gap-1 items-center text-foreground justify-center">
                  <Robot size={24} />
                  <p className="text-lg font-medium">{t("agents.title")}</p>
                </div>
                <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                  {t("agents.agent-skills")}
                </p>
              </div>
            </div>
            <div className="flex flex-col">{children}</div>
          </div>
        </div>
      </div>
    </div>
  );
}

interface SkillListProps {
  isDefault?: boolean;
  skills?: Record<string, any>;
  selectedSkill?: string | SelectedSkill | null;
  handleClick?: ((skill: string) => void) | null;
  activeSkills?: string[];
}

function SkillList({
  isDefault = false,
  skills = {},
  selectedSkill = null,
  handleClick = null,
  activeSkills = [],
}: SkillListProps): JSX.Element | null {
  const { t } = useTranslation();
  if (skills.length === 0) return null;

  return (
    <div
      className={`bg-white/5 text-foreground rounded-xl ${
        isMobile ? "w-full" : "min-w-[360px] w-fit"
      }`}
    >
      {Object.entries(skills).map(([skill, settings], index) => (
        <div
          key={skill}
          className={`py-3 px-4 flex items-center justify-between ${
            index === 0 ? "rounded-t-xl" : ""
          } ${
            index === Object.keys(skills).length - 1
              ? "rounded-b-xl"
              : "border-b border-gray-300"
          } cursor-pointer transition-all duration-300  hover:bg-white/5 ${
            selectedSkill === skill ? "border-gray-300" : ""
          }`}
          onClick={() => handleClick?.(skill)}
        >
          <div className="text-sm text-foreground">{settings.title}</div>
          <div className="flex items-center gap-x-2">
            {isDefault ? (
              <DefaultBadge title={skill} />
            ) : (
              <div className="text-sm text-foreground font-medium">
                {activeSkills.includes(skill)
                  ? t("agents.skill-status.on")
                  : t("agents.skill-status.off")}
              </div>
            )}
            <CaretRight size={14} weight="bold" className="text-foreground" />
          </div>
        </div>
      ))}
    </div>
  );
}
