import React from "react";
import { DefaultSkill, ConfigurableSkill } from "@/types/admin";
import AgentWebSearchSelection from "./WebSearchSelection";
import AgentSQLConnectorSelection from "./SQLConnectorSelection";
import GenericSkillPanel from "./GenericSkillPanel";
import DefaultSkillPanel from "./DefaultSkillPanel";
import {
  <PERSON>,
  File,
  Browser,
  ChartBar,
  FileMagnifyingGlass,
} from "@phosphor-icons/react";
import RAGImage from "@/media/agents/rag-memory.png";
import SummarizeImage from "@/media/agents/view-summarize.png";
import ScrapeWebsitesImage from "@/media/agents/scrape-websites.png";
import GenerateChartsImage from "@/media/agents/generate-charts.png";
import GenerateSaveImages from "@/media/agents/generate-save-files.png";

interface DefaultSkills {
  [key: string]: DefaultSkill;
}

interface ConfigurableSkills {
  [key: string]: ConfigurableSkill;
}

export const defaultSkills: DefaultSkills = {
  "rag-memory": {
    title: "RAG & long-term memory",
    description:
      'Allow the agent to leverage your local documents to answer a query or ask the agent to "remember" pieces of content for long-term memory retrieval.',
    component: DefaultSkillPanel as unknown as React.ComponentType<
      Record<string, unknown>
    >,
    icon: Brain,
    image: RAGImage,
    readonly: true,
  },
  "view-summarize": {
    title: "View & summarize documents",
    description:
      "Allow the agent to list and summarize the content of workspace files currently embedded.",
    component: DefaultSkillPanel as unknown as React.ComponentType<
      Record<string, unknown>
    >,
    icon: File,
    image: SummarizeImage,
    readonly: true,
  },
  "scrape-websites": {
    title: "Scrape websites",
    description: "Allow the agent to visit and scrape the content of websites.",
    component: DefaultSkillPanel as unknown as React.ComponentType<
      Record<string, unknown>
    >,
    icon: Browser,
    image: ScrapeWebsitesImage,
    readonly: true,
  },
};

export const configurableSkills: ConfigurableSkills = {
  "save-file-to-browser": {
    title: "Generate & save files to browser",
    description:
      "Enable the default agent to generate and write to files that can be saved to your computer.",
    component: GenericSkillPanel as unknown as React.ComponentType<
      Record<string, unknown>
    >,
    skill: "save-file-to-browser",
    icon: FileMagnifyingGlass,
    image: GenerateSaveImages,
  },
  "create-chart": {
    title: "Generate charts",
    description:
      "Enable the default agent to generate various types of charts from data provided or given in chat.",
    component: GenericSkillPanel as unknown as React.ComponentType<
      Record<string, unknown>
    >,
    skill: "create-chart",
    icon: ChartBar,
    image: GenerateChartsImage,
  },
  "web-browsing": {
    title: "Web Search",
    component: AgentWebSearchSelection as unknown as React.ComponentType<
      Record<string, unknown>
    >,
    skill: "web-browsing",
    description: "Enable web search capabilities for the agent",
  },
  "sql-agent": {
    title: "SQL Connector",
    component: AgentSQLConnectorSelection as unknown as React.ComponentType<
      Record<string, unknown>
    >,
    skill: "sql-agent",
    description: "Enable SQL database connectivity for the agent",
  },
};
