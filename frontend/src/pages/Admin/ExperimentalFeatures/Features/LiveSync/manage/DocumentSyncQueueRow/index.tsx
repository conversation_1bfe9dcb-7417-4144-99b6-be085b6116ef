import { useRef } from "react";
import { Trash } from "@phosphor-icons/react";
import { stripUuidAndJsonFromString } from "@/utils/fileHelpers";
import moment from "moment";
import System from "@/models/system";
import { Button } from "@/components/Button";

interface WorkspaceDoc {
  filename: string;
  docpath: string;
  workspace: {
    slug: string;
  };
}

// Queue interface that matches the API response structure
interface Queue {
  id: string;
  workspaceDoc: WorkspaceDoc;
  lastSyncedAt: string;
  nextSyncAt: string;
  createdAt: string;
  status?: string;
  error?: string;
  attempts?: number;
  maxAttempts?: number;
  [key: string]: any; // Allow additional properties from API
}

interface DocumentSyncQueueRowProps {
  queue: Queue;
}

export default function DocumentSyncQueueRow({
  queue,
}: DocumentSyncQueueRowProps): JSX.Element {
  const rowRef = useRef<HTMLTableRowElement>(null);

  const handleDelete = async (): Promise<void> => {
    rowRef?.current?.remove();
    await System.experimentalFeatures.liveSync.setWatchStatusForDocument(
      queue.workspaceDoc.workspace.slug,
      queue.workspaceDoc.docpath,
      false
    );
  };

  return (
    <>
      <tr
        ref={rowRef}
        className="bg-transparent text-white text-opacity-80 text-sm font-medium"
      >
        <td scope="row" className="px-6 py-4 whitespace-nowrap">
          {stripUuidAndJsonFromString(queue.workspaceDoc.filename)}
        </td>
        <td className="px-6 py-4">{moment(queue.lastSyncedAt).fromNow()}</td>
        <td className="px-6 py-4">
          {moment(queue.nextSyncAt).format("lll")}
          <i className="text-xs px-2">({moment(queue.nextSyncAt).fromNow()})</i>
        </td>
        <td className="px-6 py-4">{moment(queue.createdAt).format("lll")}</td>
        <td className="px-6 py-4 flex items-center gap-x-6">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleDelete}
            className="text-red-300"
          >
            <Trash className="h-5 w-5" />
          </Button>
        </td>
      </tr>
    </>
  );
}
