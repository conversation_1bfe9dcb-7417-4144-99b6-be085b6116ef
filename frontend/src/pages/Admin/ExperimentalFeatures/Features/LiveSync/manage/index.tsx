import { useEffect, useState } from "react";
import Sidebar from "@/components/Sidebar";
import * as Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import System from "@/models/system";
import DocumentSyncQueueRow from "./DocumentSyncQueueRow";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import { useTranslation } from "react-i18next";

interface WorkspaceDoc {
  filename: string;
  docpath: string;
  workspace: {
    slug: string;
  };
}

// Queue interface that matches the API response structure
interface Queue {
  id: string;
  workspaceDoc: WorkspaceDoc;
  lastSyncedAt: string;
  nextSyncAt: string;
  createdAt: string;
  status?: string;
  error?: string;
  attempts?: number;
  maxAttempts?: number;
  [key: string]: any; // Allow additional properties from API
}

export default function LiveDocumentSyncManager(): JSX.Element {
  const { t } = useTranslation();
  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        <div className="relative rounded-md w-full h-full overflow-y-scroll">
          <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4">
            <div className="w-full flex flex-col gap-y-1 pb-3 border-bottom">
              <div className="items-center flex gap-x-4">
                <p className="text-lg leading-6 font-bold text-white">
                  {t("experimental.live-sync.manage-title")}
                </p>
              </div>
              <p className="text-xs leading-[18px] font-base text-white text-opacity-60">
                {t("experimental.live-sync.manage-description")}
              </p>
            </div>
            <WatchedDocumentsContainer />
          </div>
        </div>
      </div>
    </div>
  );
}

function WatchedDocumentsContainer(): JSX.Element {
  const { t } = useTranslation();
  const [loading, setLoading] = useState<boolean>(true);
  const [queues, setQueues] = useState<Queue[]>([]);

  useEffect(() => {
    async function fetchData(): Promise<void> {
      const _queues = await System.experimentalFeatures.liveSync.queues();
      // Ensure _queues is an array and has the correct structure
      const validQueues = Array.isArray(_queues)
        ? _queues.filter(
            (queue) =>
              queue &&
              typeof queue.id === "string" &&
              queue.workspaceDoc &&
              typeof queue.workspaceDoc === "object"
          )
        : [];
      setQueues(validQueues as Queue[]);
      setLoading(false);
    }
    fetchData();
  }, []);

  if (loading) {
    return (
      <Skeleton.default
        height="80vh"
        width="100%"
        highlightColor="#3D4147"
        baseColor="#2C2F35"
        count={1}
        className="w-full p-4 rounded-b-2xl rounded-tr-2xl rounded-tl-sm mt-6"
        containerClassName="flex w-full"
      />
    );
  }

  return (
    <table className="w-full text-sm text-left rounded-lg mt-6">
      <thead className="text-white text-opacity-80 text-xs leading-[18px] font-bold uppercase border-white border-b border-opacity-60">
        <tr>
          <th scope="col" className="px-6 py-3 rounded-tl-lg">
            {t("experimental.live-sync.document-name")}
          </th>
          <th scope="col" className="px-6 py-3">
            {t("experimental.live-sync.last-synced")}
          </th>
          <th scope="col" className="px-6 py-3">
            {t("experimental.live-sync.next-refresh")}
          </th>
          <th scope="col" className="px-6 py-3">
            {t("experimental.live-sync.created-on")}
          </th>
          <th scope="col" className="px-6 py-3 rounded-tr-lg">
            {" "}
          </th>
        </tr>
      </thead>
      <tbody>
        {queues.map((queue: Queue) => (
          <DocumentSyncQueueRow key={queue.id} queue={queue} />
        ))}
      </tbody>
    </table>
  );
}
