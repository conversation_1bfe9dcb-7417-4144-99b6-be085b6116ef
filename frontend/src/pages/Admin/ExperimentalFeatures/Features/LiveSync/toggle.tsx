import System from "@/models/system";
import paths from "@/utils/paths";
import showToast from "@/utils/toast";
import { ArrowSquareOut } from "@phosphor-icons/react";
import { useState } from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Toggle from "@/components/ui/Toggle";

interface LiveSyncToggleProps {
  enabled?: boolean;
  onToggle: () => void;
}

export default function LiveSyncToggle({
  enabled = false,
  onToggle,
}: LiveSyncToggleProps): JSX.Element {
  const { t } = useTranslation();
  const [status, setStatus] = useState<boolean>(enabled);

  async function toggleFeatureFlag(
    newStatus: boolean
  ): Promise<boolean | void> {
    const updated =
      await System.experimentalFeatures.liveSync.toggleFeature(newStatus);
    if (!updated) {
      showToast(t("toast.experimental.update-failed"), "error", {
        clear: true,
      });
      return false;
    }

    setStatus(newStatus);
    showToast(
      t(`toast.experimental.live-sync.${newStatus ? "enabled" : "disabled"}`),
      "success",
      { clear: true }
    );
    onToggle();
  }

  return (
    <div className="p-4">
      <div className="flex flex-col gap-y-6 max-w-[500px]">
        <Toggle
          label={t("experimental.live-sync.auto-sync")}
          checked={status}
          onCheckedChange={toggleFeatureFlag}
          labelPosition="left"
        />
        <div className="flex flex-col space-y-4">
          <p className="text-foreground text-sm">
            {t("experimental.live-sync.sync-description")}
          </p>
          <p className="text-foreground text-sm">
            {t("experimental.live-sync.sync-workspace-note")}
          </p>
          <p className="text-foreground text-xs italic">
            {t("experimental.live-sync.sync-limitation")}
          </p>
        </div>
      </div>
      <div className="mt-8">
        <ul className="space-y-2">
          <li>
            <a
              href="https://docs.useanything.com/beta-preview/active-features/live-content-sync"
              target="_blank"
              className="text-sm text-blue-400 hover:underline flex items-center gap-x-1"
              rel="noreferrer"
            >
              <ArrowSquareOut size={14} />
              <span>{t("experimental.live-sync.documentation")}</span>
            </a>
          </li>
          <li>
            <Link
              to={paths.experimental.liveDocumentSync.manage()}
              className="text-sm text-blue-400 hover:underline"
            >
              {t("experimental.live-sync.manage-content")} &rarr;
            </Link>
          </li>
        </ul>
      </div>
    </div>
  );
}
