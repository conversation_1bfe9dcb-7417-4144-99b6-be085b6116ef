import React from "react";
import { ExperimentalFeature } from "@/types/admin";
import LiveSyncToggle from "./Features/LiveSync/toggle";

interface ConfigurableFeatures {
  [key: string]: ExperimentalFeature;
}

export const configurableFeatures: ConfigurableFeatures = {
  experimental_live_file_sync: {
    key: "experimental_live_file_sync",
    title: "Live Document Sync",
    component: LiveSyncToggle as unknown as React.ComponentType<
      Record<string, unknown>
    >,
    enabled: false,
  },
};
