import { useEffect, useState, FormEvent, ReactNode } from "react";
import Sidebar from "@/components/SettingsSidebar";
import { isMobile } from "react-device-detect";
import Admin from "@/models/admin";
import { FullScreenLoader } from "@/components/Preloader";
import { ArrowLeft, CaretRight, Flask } from "@phosphor-icons/react";
import { configurableFeatures } from "./features";
import Modal from "@/components/ui/Modal";
import paths from "@/utils/paths";
import showToast from "@/utils/toast";
import { Link } from "react-router-dom";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/Button";

interface FeatureSettings {
  title: string;
  key: string;
  component?: React.ComponentType<{
    enabled: boolean;
    feature: string;
    onToggle: () => void;
  }>;
}

interface FeatureFlags {
  [key: string]: boolean;
}

export default function ExperimentalFeatures(): JSX.Element {
  const { t } = useTranslation();
  const [featureFlags, setFeatureFlags] = useState<FeatureFlags>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [selectedFeature, setSelectedFeature] = useState<string>(
    "experimental_live_file_sync"
  );

  useEffect(() => {
    async function fetchSettings(): Promise<void> {
      setLoading(true);
      const response = await Admin.systemPreferences();
      if (response && response.settings) {
        const settings = response.settings as { feature_flags?: FeatureFlags };
        setFeatureFlags(settings.feature_flags ?? {});
      }
      setLoading(false);
    }
    fetchSettings();
  }, []);

  const refresh = async (): Promise<void> => {
    const response = await Admin.systemPreferences();
    if (response && response.settings) {
      const settings = response.settings as { feature_flags?: FeatureFlags };
      setFeatureFlags(settings.feature_flags ?? {});
    }
  };

  if (loading) {
    return (
      <div
        className={`relative md:ml-[2px] md:mr-[16px] md:my-[16px] md:rounded-[16px] w-full flex justify-center items-center ${
          isMobile ? "h-full" : "h-[calc(100%-32px)]"
        }`}
      >
        <FullScreenLoader />
      </div>
    );
  }

  return (
    <FeatureLayout>
      <div className="flex-1 flex gap-x-6 pt-3">
        {/* Feature settings nav */}
        <div className="flex flex-col gap-y-[18px]">
          <div className="text-foreground flex items-center gap-x-1">
            <Flask size={19} />
            <p className="text-md font-medium">{t("experimental.title")}</p>
          </div>
          {/* Feature list */}
          <FeatureList
            features={configurableFeatures as Record<string, FeatureSettings>}
            selectedFeature={selectedFeature}
            handleClick={setSelectedFeature}
            activeFeatures={Object.keys(featureFlags).filter(
              (flag) => featureFlags[flag]
            )}
          />
        </div>

        {/* Selected feature setting panel */}
        <FeatureVerification>
          <div className="flex-[2] flex flex-col gap-y-[18px] mt-10">
            <div className="deep-xl-input text-foreground rounded-xl flex-1 p-4">
              {selectedFeature ? (
                <SelectedFeatureComponent
                  feature={
                    configurableFeatures[selectedFeature] as FeatureSettings
                  }
                  settings={featureFlags}
                  refresh={refresh}
                />
              ) : (
                <div className="flex flex-col items-center justify-center h-full text-foreground">
                  <Flask size={40} />
                  <p className="font-medium">{t("experimental.description")}</p>
                </div>
              )}
            </div>
          </div>
        </FeatureVerification>
      </div>
    </FeatureLayout>
  );
}

interface FeatureLayoutProps {
  children: ReactNode;
}

function FeatureLayout({ children }: FeatureLayoutProps): JSX.Element {
  const { t } = useTranslation();
  return (
    <div
      id="workspace-feature-settings-container"
      className="overflow-hidden w-full h-full flex flex-col"
    >
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        <div className="bg-background relative rounded-md w-full h-full flex">
          <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4">
            <div className="w-full flex flex-row items-center gap-3.5 gap-y-1 pb-3">
              <Link to={paths.home()}>
                <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
              </Link>
              <div className="flex flex-col justify-center">
                <div className="flex flex-row gap-1 items-center text-foreground justify-center">
                  <p className="text-lg font-medium text-foreground">
                    {t("experimental.title")}
                  </p>
                </div>
                <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                  {t("experimental.description")}
                </p>
              </div>
            </div>
            <div className="flex flex-col border-top">{children}</div>
          </div>
        </div>
      </div>
    </div>
  );
}

interface FeatureListProps {
  features?: Record<string, FeatureSettings>;
  selectedFeature?: string | null;
  handleClick?: ((feature: string) => void) | null;
  activeFeatures?: string[];
}

function FeatureList({
  features = {},
  selectedFeature = null,
  handleClick = null,
  activeFeatures = [],
}: FeatureListProps): JSX.Element | null {
  const { t } = useTranslation();
  if (Object.keys(features).length === 0) return null;

  return (
    <div
      className={`bg-white/5 text-foreground rounded-xl ${
        isMobile ? "w-full" : "min-w-[360px] w-fit"
      }`}
    >
      {Object.entries(features).map(([feature, settings], index) => (
        <div
          key={feature}
          className={`py-3 px-4 flex items-center justify-between ${
            index === 0 ? "rounded-t-xl" : ""
          } ${
            index === Object.keys(features).length - 1
              ? "rounded-b-xl"
              : "border-b border-white/10"
          } cursor-pointer transition-all duration-300  hover:opacity-80 ${
            selectedFeature === feature ? "deep-xl-input" : ""
          }`}
          onClick={(): void => handleClick?.(feature)}
        >
          <div className="text-sm text-foreground">{settings.title}</div>
          <div className="flex items-center gap-x-2">
            <div className="text-sm text-foreground font-medium">
              {activeFeatures.includes(settings.key)
                ? t("common.on")
                : t("common.off")}
            </div>
            <CaretRight size={14} weight="bold" className="text-foreground" />
          </div>
        </div>
      ))}
    </div>
  );
}

interface SelectedFeatureComponentProps {
  feature: FeatureSettings;
  settings: FeatureFlags;
  refresh: () => Promise<void>;
}

function SelectedFeatureComponent({
  feature,
  settings,
  refresh,
}: SelectedFeatureComponentProps): JSX.Element | null {
  const Component = feature?.component;
  return Component ? (
    <Component
      enabled={settings[feature.key]}
      feature={feature.key}
      onToggle={refresh}
    />
  ) : null;
}

interface FeatureVerificationProps {
  children: ReactNode;
}

function FeatureVerification({
  children,
}: FeatureVerificationProps): JSX.Element {
  const { t } = useTranslation();

  function acceptTos(e: FormEvent<HTMLFormElement>): void {
    e.preventDefault();

    window.localStorage.setItem("terms_experimental_feature_set", "accepted");
    showToast(t("toast.experimental.feature-enabled"), "success");
    setTimeout(() => {
      window.location.reload();
    }, 2_500);
    return;
  }

  if (!window.localStorage.getItem("terms_experimental_feature_set")) {
    return (
      <>
        <Modal
          isOpen={true}
          preventClose={true}
          onClose={(): void => {}}
          footer={
            <Button type="submit" form="terms">
              {t("experimental.tos.accept")}
            </Button>
          }
        >
          <form onSubmit={acceptTos} className="relative" id="terms">
            <div className="relative bg-background rounded-lg shadow">
              <div className="flex items-start justify-between p-4 border-b rounded-t border-gray-500/50">
                <h3 className="text-xl font-semibold text-foreground">
                  {t("experimental.tos.title")}
                </h3>
              </div>
              <div className="p-6 space-y-6 flex h-full w-full">
                <div className="w-full flex flex-col gap-y-4 text-foreground">
                  <p>{t("experimental.tos.description")}</p>

                  <div>
                    <p>{t("experimental.tos.possibilities-title")}</p>
                    <ul className="list-disc ml-6 text-sm font-mono">
                      <li>{t("experimental.tos.possibilities.data-loss")}</li>
                      <li>
                        {t("experimental.tos.possibilities.quality-change")}
                      </li>
                      <li>
                        {t("experimental.tos.possibilities.storage-increase")}
                      </li>
                      <li>
                        {t(
                          "experimental.tos.possibilities.resource-consumption"
                        )}
                      </li>
                      <li>
                        {t("experimental.tos.possibilities.cost-increase")}
                      </li>
                      <li>
                        {t("experimental.tos.possibilities.potential-bugs")}
                      </li>
                    </ul>
                  </div>

                  <div>
                    <p>{t("experimental.tos.conditions-title")}</p>
                    <ul className="list-disc ml-6 text-sm font-mono">
                      <li>{t("experimental.tos.conditions.future-updates")}</li>
                      <li>{t("experimental.tos.conditions.stability")}</li>
                      <li>{t("experimental.tos.conditions.availability")}</li>
                      <li>{t("experimental.tos.conditions.privacy")}</li>
                      <li>{t("experimental.tos.conditions.changes")}</li>
                    </ul>
                  </div>

                  <p>
                    {t("experimental.tos.read-more")}{" "}
                    <a
                      href="https://docs.useanything.com/beta-preview/overview"
                      className="underline text-blue-400"
                    >
                      docs.useanything.com
                    </a>{" "}
                    {t("experimental.tos.contact")}{" "}
                    <a
                      href="mailto:<EMAIL>"
                      className="underline text-blue-500"
                    >
                      <EMAIL>
                    </a>
                  </p>
                </div>
              </div>
            </div>
          </form>
        </Modal>
        {children}
      </>
    );
  }
  return <>{children}</>;
}
