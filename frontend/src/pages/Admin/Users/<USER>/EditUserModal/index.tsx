import { useState, useEffect, useContext } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import Admin from "@/models/admin";
import System from "@/models/system";
import { RoleHintDisplay } from "../..";
import { useTranslation } from "react-i18next";
import Modal from "@/components/ui/Modal";
import { Button } from "@/components/Button";
import Input from "@/components/ui/Input";
import showToast from "@/utils/toast";
import Label from "@/components/ui/Label";
import { AuthContext } from "@/AuthContext";
import type {
  Organization,
  AdminUser as User,
  AdminUserRole as UserRole,
} from "@/types/admin";

interface FormData {
  username: string;
  password?: string;
  role: UserRole;
  economy_system_id?: string;
  organizationId?: string;
  newOrganizationName?: string;
}

interface EditUserModalProps {
  isOpen: boolean;
  currentUser?: User;
  user: User;
  closeModal: () => void;
}

// Zod schema definition
const formSchema = z
  .object({
    username: z
      .string()
      .min(2, "Username must be at least 2 characters")
      .max(100, "Username cannot be longer than 100 characters")
      .refine(
        (val) => {
          const specialUsernames = ["admin", "superuser", "manager"];
          return (
            specialUsernames.includes(val) || /^[^@]+@[^@]+\.[^@]+$/.test(val)
          );
        },
        {
          message:
            "Must be a valid email address or one of: admin, superuser, manager",
        }
      ),
    password: z
      .string()
      .optional()
      .refine((val) => !val || val.length >= 8, {
        message: "Password must be at least 8 characters if provided",
      }),
    role: z.enum(["default", "admin", "manager", "superuser"]),
    economy_system_id: z.string().optional(),
    organizationId: z.string().optional(),
    newOrganizationName: z.string().optional(),
  })
  .refine(
    (data) => {
      if (data.organizationId === "new" && !data.newOrganizationName) {
        return false;
      }
      return true;
    },
    {
      message:
        "New organization name is required when creating a new organization.",
      path: ["newOrganizationName"],
    }
  );

export default function EditUserModal({
  isOpen,
  currentUser,
  user,
  closeModal,
}: EditUserModalProps): JSX.Element {
  const { t } = useTranslation();
  const authContext = useContext(AuthContext);
  const store = authContext?.store;
  const actions = authContext?.actions;
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loadingOrgs, setLoadingOrgs] = useState<boolean>(true);
  const [showNewOrgInput, setShowNewOrgInput] = useState<boolean>(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
    reset,
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: user?.username || "",
      password: "",
      role: (user?.role as UserRole) || "default",
      economy_system_id: user?.economy_system_id || "",
      organizationId: user?.organization?.id || "none",
      newOrganizationName: "",
    },
  });

  const selectedOrgId = watch("organizationId");

  // Fetch organizations when modal opens
  useEffect(() => {
    async function fetchOrganizations(): Promise<void> {
      if (!isOpen) return;
      setLoadingOrgs(true);
      const orgRes = await Admin.organizations();
      const orgList = Array.isArray(orgRes)
        ? orgRes
        : Array.isArray(orgRes.organizations)
          ? orgRes.organizations
          : [];
      const error = Array.isArray(orgRes) ? null : orgRes.error;
      if (error) {
        showToast(
          t("organizations.fetch-error", "Failed to fetch organizations"),
          "error"
        );
      }
      setOrganizations(orgList as Organization[]);
      setLoadingOrgs(false);
    }
    fetchOrganizations();
  }, [isOpen, t]);

  // Reset form to user's current values + password empty when modal opens or user changes
  useEffect(() => {
    if (isOpen && user) {
      reset({
        username: user.username,
        password: "",
        role: (user.role as UserRole) || "default",
        economy_system_id: user.economy_system_id || "",
        organizationId: user.organization?.id || "none",
        newOrganizationName: "",
      });
      setShowNewOrgInput(false);
    } else if (!isOpen) {
      reset();
      setShowNewOrgInput(false);
    }
  }, [isOpen, user, reset]);

  // Show/hide new organization input based on selection
  useEffect(() => {
    setShowNewOrgInput(selectedOrgId === "new");
    if (selectedOrgId !== "new") {
      setValue("newOrganizationName", "");
    }
  }, [selectedOrgId, setValue]);

  const onSubmit = async (data: FormData): Promise<void> => {
    const payload: any = { ...data };

    // Don't send password if it's empty
    if (!payload.password) {
      delete payload.password;
    }

    // Prepare payload based on organization selection
    if (data.organizationId === "new") {
      payload.organizationId = null;
    } else if (data.organizationId === "none") {
      payload.organizationId = null;
      delete payload.newOrganizationName;
    } else {
      payload.organizationId = data.organizationId;
      delete payload.newOrganizationName;
    }

    const { success, error: updateError } = await Admin.updateUser(
      user.id,
      payload
    );
    if (success) {
      // Always refresh current user data if economy_system_id was updated
      // This ensures the current user sees their updated economy_system_id immediately
      if (
        store?.user &&
        (String(store.user.id) === String(user.id) ||
          payload.economy_system_id !== undefined)
      ) {
        const refreshedUser = await System.getCurrentUser();
        if (refreshedUser && actions?.updateUser && store?.authToken) {
          actions.updateUser(refreshedUser, store.authToken);
        }
      }

      showToast(
        t("show-toast.user-updated", "User updated successfully"),
        "success"
      );
      closeModal();
      window.location.reload();
    } else {
      showToast(
        updateError || t("user-setting.error", "Could not update user."),
        "error"
      );
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={closeModal}
      title={`${t("user-setting.edit")} ${user?.username || "User"}`}
      footer={
        <>
          <Button variant="secondary" onClick={closeModal}>
            {t("user-setting.cancel")}
          </Button>
          <Button form="edit-user-form" type="submit">
            {t("user-setting.update-user")}
          </Button>
        </>
      }
    >
      <form onSubmit={handleSubmit(onSubmit)} id="edit-user-form">
        <div className="p-6 space-y-4 flex h-full w-full">
          <div className="w-full flex flex-col gap-y-4">
            <div>
              <Label htmlFor="username">{t("user-setting.username")}</Label>
              <Input
                id="username"
                {...register("username")}
                type="email"
                placeholder={t("new-user.username-ph")}
                required={true}
                autoComplete="off"
              />
              {errors.username && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.username.message}
                </p>
              )}
              <p className="mt-1 text-[10px] text-foreground">
                {t("new-user.invalid-email")}
              </p>
            </div>
            <div>
              <Label htmlFor="password">{t("user-setting.new-password")}</Label>
              <Input
                id="password"
                {...register("password")}
                type="password"
                placeholder={t("user-setting.new-password")}
                autoComplete="new-password"
              />
              {errors.password && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.password.message}
                </p>
              )}
              <p className="mt-1 text-xs text-foreground">
                {t("user-setting.password-rule")}
              </p>
            </div>
            <div>
              <Label htmlFor="role">{t("user-setting.role")}</Label>
              <select
                id="role"
                {...register("role")}
                required={true}
                className="dark-input-mdl w-full text-foreground text-sm rounded-lg block p-2"
              >
                <option value="default">{t("user-setting.default")}</option>
                {currentUser?.role === "admin" && (
                  <option value="superuser">
                    {t("user-setting.superuser")}
                  </option>
                )}
                <option value="manager">{t("user-setting.manager")}</option>
                {currentUser?.role === "admin" && (
                  <option value="admin">{t("user-setting.admin")}</option>
                )}
              </select>
              {errors.role && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.role.message}
                </p>
              )}
              <RoleHintDisplay
                role={(() => {
                  const role = watch("role");
                  return role === "superuser" ? "superuser" : role;
                })()}
              />
            </div>

            <div>
              <Label htmlFor="organizationId">
                {t("organization.label", "Organization")}
              </Label>
              <select
                id="organizationId"
                {...register("organizationId")}
                className="dark-input-mdl w-full text-foreground text-sm rounded-lg block p-2"
                disabled={loadingOrgs}
              >
                <option value="none">{t("organization.none", "None")}</option>
                {organizations.map((org) => (
                  <option key={org.id} value={org.id.toString()}>
                    {org.name}
                  </option>
                ))}
                <option value="new">
                  {t("organization.create-new", "+ Create New Organization")}
                </option>
              </select>
              {errors.organizationId && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.organizationId.message}
                </p>
              )}
            </div>

            {showNewOrgInput && (
              <div>
                <Label htmlFor="newOrganizationName">
                  {t("organization.new-name", "New Organization Name")}
                </Label>
                <Input
                  id="newOrganizationName"
                  {...register("newOrganizationName")}
                  type="text"
                  placeholder={t(
                    "organization.new-name-ph",
                    "Enter new organization name"
                  )}
                  required={selectedOrgId === "new"}
                />
                {errors.newOrganizationName && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.newOrganizationName.message}
                  </p>
                )}
              </div>
            )}

            <div>
              <Label htmlFor="economy_system_id">
                {t("user-setting.economy-id")}
              </Label>
              <Input
                id="economy_system_id"
                {...register("economy_system_id")}
                type="text"
                placeholder={t("user-setting.economy-id-ph")}
                autoComplete="off"
              />
              {errors.economy_system_id && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.economy_system_id.message}
                </p>
              )}
              <p className="mt-1 text-xs text-foreground">
                {t("user-setting.economy-id-hint")}
              </p>
            </div>
          </div>
        </div>
      </form>
    </Modal>
  );
}
