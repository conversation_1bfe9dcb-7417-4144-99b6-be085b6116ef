import { useRef, useState } from "react";
import Admin from "@/models/admin";
import EditUserModal from "./EditUserModal";
import showToast from "@/utils/toast";
import { useModal } from "@/hooks/useModal";
import { PencilLine, Prohibit, Trash } from "@phosphor-icons/react";
import { useTranslation } from "react-i18next";
import { useConfirmation } from "@/hooks/useConfirmation";
import Modal from "@/components/ui/Modal";
import { Button } from "@/components/Button";

type UserRole = "admin" | "manager" | "superuser" | "default";

interface UserRowsProps {
  currUser?: any;
  user: any;
}

interface UpdateUserResponse {
  success: boolean;
  error?: string;
}

interface DeleteUserResponse {
  success: boolean;
  error?: string;
}

const ModMap: Record<UserRole, UserRole[]> = {
  admin: ["admin", "manager", "superuser", "default"],
  manager: ["manager", "superuser", "default"],
  superuser: ["superuser", "default"],
  default: [],
};

export default function UserRows({
  currUser,
  user,
}: UserRowsProps): JSX.Element {
  const { t } = useTranslation();
  const rowRef = useRef<HTMLTableRowElement>(null);
  const validRoles: UserRole[] = ["admin", "manager", "superuser", "default"];
  const currRole = validRoles.includes(currUser?.role)
    ? (currUser.role as UserRole)
    : "default";
  const canModify = ModMap[currRole].includes(user.role);
  const [suspended, setSuspended] = useState<boolean>(user.suspended === 1);
  const { isOpen, openModal, closeModal } = useModal();

  const {
    isConfirmationOpen: isDeleteConfirmOpen,
    openConfirmation: openDeleteConfirm,
    closeConfirmation: closeDeleteConfirm,
    handleConfirm: handleDeleteConfirm,
  } = useConfirmation();

  const {
    isConfirmationOpen: isSuspendConfirmOpen,
    openConfirmation: openSuspendConfirm,
    closeConfirmation: closeSuspendConfirm,
    handleConfirm: handleSuspendConfirm,
  } = useConfirmation();

  const handleSuspend = async (): Promise<void> => {
    openSuspendConfirm(async () => {
      try {
        const { success, error }: UpdateUserResponse = await Admin.updateUser(
          String(user.id),
          {
            suspended: suspended ? 0 : 1,
          }
        );
        if (!success) {
          showToast(
            t("toast.settings.user-update-failed", { error }),
            "error",
            {
              clear: true,
            }
          );
          return;
        }
        showToast(
          t(suspended ? "user.unsuspended" : "user.suspended"),
          "success",
          {
            clear: true,
          }
        );
        setSuspended(!suspended);
      } catch {
        // console.error("Error updating user:", _error);
        showToast("Failed to update user", "error", { clear: true });
      }
    });
  };

  const handleDelete = async (): Promise<void> => {
    openDeleteConfirm(async () => {
      try {
        const { success, error }: DeleteUserResponse = await Admin.deleteUser(
          String(user.id)
        );
        if (!success) {
          showToast(
            t("toast.settings.user-update-failed", { error }),
            "error",
            {
              clear: true,
            }
          );
          return;
        }
        rowRef?.current?.remove();
        showToast(t("show-toast.user-deleted"), "success", { clear: true });
      } catch {
        // console.error("Error deleting user:", _error);
        showToast("Failed to delete user", "error", { clear: true });
      }
    });
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <tr
      ref={rowRef}
      className="text-foreground border-b border-white border-opacity-60"
    >
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={suspended ? "line-through" : ""}>{user.username}</span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        {t(`user-setting.${user.role}`)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        {user.economy_system_id || "-"}
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        {user.organization?.name || "-"}
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        {formatDate(user.createdAt)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right">
        <div className="flex justify-end gap-x-4">
          {canModify && (
            <>
              <Button
                variant="ghost"
                size="icon"
                onClick={openModal}
                className="text-foreground hover:text-blue-500"
              >
                <PencilLine className="h-5 w-5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleSuspend}
                className="text-foreground hover:text-yellow-500"
              >
                <Prohibit className="h-5 w-5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleDelete}
                className="text-foreground hover:text-red-500"
              >
                <Trash className="h-5 w-5" />
              </Button>
            </>
          )}
        </div>
      </td>
      <EditUserModal
        isOpen={isOpen}
        currentUser={currUser}
        user={user}
        closeModal={closeModal}
      />
      <Modal
        isOpen={isDeleteConfirmOpen}
        onClose={closeDeleteConfirm}
        title={t("user.delete-title")}
        footer={
          <>
            <Button variant="outline" onClick={closeDeleteConfirm}>
              {t("common.cancel")}
            </Button>
            <Button variant="destructive" onClick={handleDeleteConfirm}>
              {t("button.delete")}
            </Button>
          </>
        }
      >
        <p className="text-foreground">
          {t("deleteConfirmation", { username: user.username })}
        </p>
      </Modal>
      <Modal
        isOpen={isSuspendConfirmOpen}
        onClose={closeSuspendConfirm}
        title={suspended ? t("user.unsuspend-title") : t("user.suspend-title")}
        footer={
          <>
            <Button variant="outline" onClick={closeSuspendConfirm}>
              {t("common.cancel")}
            </Button>
            <Button variant="destructive" onClick={handleSuspendConfirm}>
              {suspended ? t("button.unsuspend") : t("button.suspend")}
            </Button>
          </>
        }
      >
        <p className="text-foreground">
          {t("suspendConfirmation", { username: user.username })}
        </p>
      </Modal>
    </tr>
  );
}
