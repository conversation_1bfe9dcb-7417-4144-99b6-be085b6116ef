import React, { useEffect, useState, useCallback, useMemo } from "react";
import Sidebar from "@/components/SettingsSidebar";
import System from "@/models/system";
import paths from "@/utils/paths";
import showToast from "@/utils/toast";
import PreLoader from "@/components/Preloader";
import { ArrowLeft } from "@phosphor-icons/react";
import { Button } from "@/components/Button";
import { Link } from "react-router-dom";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import { MODEL_MAP } from "@/utils/AiProviders/modelMap";
import Admin from "@/models/admin";
import { getModelPrefKey } from "@/components/LLMSelection/LLMProviderConfig";
import BaseLLMPreference from "@/components/LLMSelection/BaseLLMPreference";
import { SUPPORTED_CUSTOM_USER_AI_PROVIDERS } from "@/utils/AiProviders/supportedLLMProviders";
import TabButton from "@/components/ui/TabButton";
import { parseModelReference } from "@/utils/modelReference";
import { useTranslation } from "react-i18next";

const CUSTOM_USER_AI_SUFFIX = [
  "_CUAI",
  "_CUAI2",
  "_CUAI3",
  "_CUAI4",
  "_CUAI5",
  "_CUAI6",
];

type SystemSettings = { [key: string]: any };
type SelectedLLMs = { [key: string]: string };

// Completely separated ModelReferenceForm component
function ModelReferenceForm({
  activeTab,
  selectedLLM,
  settings,
}: {
  activeTab: string;
  selectedLLM: string;
  settings: Record<string, any> | null;
}) {
  const { t } = useTranslation();

  const [modelRefState, setModelRefState] = useState({
    values: CUSTOM_USER_AI_SUFFIX.reduce(
      (acc: Record<string, { name: string; description: string }>, tab) => {
        // Initialize with name and description structure
        acc[tab] = { name: "", description: "" };
        return acc;
      },
      {}
    ),
    windowPercentages: CUSTOM_USER_AI_SUFFIX.reduce(
      (acc: Record<string, number>, tab) => {
        acc[tab] = 70;
        return acc;
      },
      {}
    ),
    hasChanges: false,
    isSaving: false,
  });

  // Get the appropriate key for the current tab
  const getModelRefKeyForTab = (tab: string) => {
    return tab === "_CUAI"
      ? "custom_model_reference"
      : `custom_model_reference_${tab.replace("_CUAI", "")}`;
  };

  const getDynamicContextWindowKey = (tab: string) => {
    return tab === "_CUAI"
      ? "custom_dynamic_context_window_percentage"
      : `custom_dynamic_context_window_percentage_${tab.replace("_CUAI", "")}`;
  };

  // Fetch fresh settings when tab changes
  useEffect(() => {
    async function fetchFreshSettings() {
      try {
        const freshSettings = await System.keys();

        // Update our state with fresh values from the server
        setModelRefState((prev) => {
          const newValues = { ...prev.values };
          const newPercentages = { ...prev.windowPercentages };

          CUSTOM_USER_AI_SUFFIX.forEach((suffix) => {
            const key = getModelRefKeyForTab(suffix);
            const contextWindowKey = getDynamicContextWindowKey(suffix);
            // Parse the stored value
            const rawRefValue = freshSettings?.[key] ?? "";
            newValues[suffix] = parseModelReference(rawRefValue);

            // Parse the context window value as an integer
            const rawValue = freshSettings?.[contextWindowKey];
            const parsedValue = rawValue ? parseInt(rawValue) : 70;
            newPercentages[suffix] = parsedValue;

            if (suffix === activeTab) {
              // console.log(
              //   `Updating slider value for ${suffix} from fresh settings:`,
              //   {
              //     contextWindowKey,
              //     rawValue,
              //     parsedValue,
              //   }
              // );
            }
          });

          return {
            ...prev,
            values: newValues,
            windowPercentages: newPercentages,
            hasChanges: false, // Reset changes since we just loaded fresh data
            isSaving: false,
          };
        });
      } catch {
        // console.error("Error fetching fresh settings for slider:");
      }
    }

    fetchFreshSettings();
  }, [activeTab]);

  const handleCustomModelRefChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { name: fieldName, value } = e.target; // 'name' or 'description'

    // Get the appropriate key for the current tab
    const modelRefKey = getModelRefKeyForTab(activeTab);
    // Get the current SAVED value from settings to compare against
    const currentRawValue = settings?.[modelRefKey] || "";

    // Update local state first
    setModelRefState((prev) => {
      const updatedValues = {
        ...prev.values,
        [activeTab]: {
          ...prev.values[activeTab],
          [fieldName]: value, // Update either 'name' or 'description'
        },
      };

      // Check if the updated object (as a JSON string) differs from the saved raw value
      const newObject = updatedValues[activeTab];
      const newValueStringified = JSON.stringify({
        name: (newObject.name || "").trim(),
        description: (newObject.description || "").trim(),
      });
      // Handle case where saved value might be an old string
      const currentComparableValue =
        typeof currentRawValue === "string" && currentRawValue.startsWith("{")
          ? currentRawValue // Already likely JSON
          : JSON.stringify({ name: currentRawValue || "", description: "" }); // Old string format

      const hasChanged = newValueStringified !== currentComparableValue;

      return {
        ...prev,
        values: updatedValues,
        hasChanges: hasChanged, // Set hasChanges based on comparison with saved value
      };
    });
  };

  const handleDynamicContextWindowChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = parseInt(e.target.value);
    // Ensure value is between 10 and 100
    const validValue = value < 10 ? 10 : value > 100 ? 100 : value;

    // Just update the value without setting hasChanges
    setModelRefState((prev) => {
      const dynamicContextWindowKey = getDynamicContextWindowKey(activeTab);
      // Parse the current value as integer for proper comparison
      const currentValueStr = settings?.[dynamicContextWindowKey];
      const _currentValue = currentValueStr ? parseInt(currentValueStr) : 70;
      // Use the current value but suppress unused variable warning
      void _currentValue;

      // console.log("Window percentage change:", {
      //   newValue: validValue,
      //   currentValue,
      //   currentValueRaw: settings?.[dynamicContextWindowKey],
      //   activeTab,
      // });

      return {
        ...prev,
        windowPercentages: {
          ...prev.windowPercentages,
          [activeTab]: validValue,
        },
        // Don't set hasChanges since we auto-save
      };
    });
  };

  const handleSliderRelease = () => {
    // console.log("Slider released - auto-submitting form");

    // Instead of trying to update settings directly, use normal form submission
    // which already has error handling and proper state updates
    handleModelRefSubmit();
  };

  const handleModelRefSubmit = async (
    event?: React.FormEvent<HTMLFormElement>
  ) => {
    if (event) {
      event.preventDefault();
    }

    setModelRefState((prev) => ({
      ...prev,
      isSaving: true,
    }));

    try {
      // Get values from our local state for the current tab
      const currentModelRefData = modelRefState.values[activeTab] || {
        name: "",
        description: "",
      };
      const name = (currentModelRefData.name || "").trim();
      const description = (currentModelRefData.description || "").trim();

      // Stringify the object for storage
      const valueToSave = JSON.stringify({ name, description });

      const contextWindowValue =
        modelRefState.windowPercentages[activeTab] || 70;

      // Get the appropriate keys for the current tab
      const modelRefKey = getModelRefKeyForTab(activeTab);
      const dynamicContextWindowKey = getDynamicContextWindowKey(activeTab);

      // Ensure we only save non-empty objects or clear the field if both are empty
      const updates = {
        [modelRefKey]: name || description ? valueToSave : "", // Save empty string if both are empty
        [dynamicContextWindowKey]: contextWindowValue,
      };

      // console.log("Sending update with values:", updates);

      const response = await Admin.updateSystemPreferences(updates);
      const { success, error } = response as {
        success: boolean;
        error?: string;
      };
      if (!success || error) {
        showToast(
          t("custom-user-ai.model-ref-save-failed", {
            error:
              (typeof error === "object" && error !== null && "message" in error
                ? (error as { message: string }).message
                : String(error)) || t("errors.unknown"),
          }),
          "error"
        );
        // Reload settings on error to revert UI
        const _settings = await System.envSettings();

        // Update our local state with the reloaded settings
        const newValues = { ...modelRefState.values };
        const newPercentages = { ...modelRefState.windowPercentages };

        CUSTOM_USER_AI_SUFFIX.forEach((suffix) => {
          const key = getModelRefKeyForTab(suffix);
          const contextWindowKey = getDynamicContextWindowKey(suffix);
          const rawRefValue = _settings?.[key] ?? "";
          newValues[suffix] = parseModelReference(rawRefValue); // Parse on reload
          newPercentages[suffix] = _settings?.[contextWindowKey] ?? 70;
        });

        setModelRefState((prev) => ({
          ...prev,
          values: newValues,
          windowPercentages: newPercentages,
          isSaving: false,
          hasChanges: false, // Reset changes on error after reload
        }));
      } else {
        showToast(
          t("custom-user-ai.model-ref-saved") ||
            t("show-toast.model-ref-saved"),
          "success"
        );
        // Fetch new settings for this component only
        const _settings = await System.envSettings();

        // Update our local state with the reloaded settings
        const newValues = { ...modelRefState.values };
        const newPercentages = { ...modelRefState.windowPercentages };

        CUSTOM_USER_AI_SUFFIX.forEach((suffix) => {
          const key = getModelRefKeyForTab(suffix);
          const contextWindowKey = getDynamicContextWindowKey(suffix);
          const rawRefValue = _settings?.[key] ?? "";
          newValues[suffix] = parseModelReference(rawRefValue); // Parse on success reload
          newPercentages[suffix] = _settings?.[contextWindowKey] ?? 70;
        });

        setModelRefState({
          values: newValues,
          windowPercentages: newPercentages,
          hasChanges: false,
          isSaving: false,
        });

        // console.log(`Custom Model Settings after save for ${activeTab}:`, {
        //   [modelRefKey]: _settings?.[modelRefKey] ?? null,
        //   [dynamicContextWindowKey]: _settings?.[dynamicContextWindowKey] ?? 70,
        // });
      }
    } catch (error) {
      // console.error("Error saving custom model settings:", error);
      showToast(
        t("custom-user-ai.model-ref-save-failed", {
          error:
            (error instanceof Error && error.message) ||
            (typeof error === "object" && error !== null && "message" in error
              ? (error as { message: string }).message
              : null) ||
            t("errors.unknown"),
        }),
        "error"
      );
      setModelRefState((prev) => ({
        ...prev,
        isSaving: false,
        // Keep hasChanges true on catch? Maybe reset? Let's reset for now.
        hasChanges: false,
      }));
    }
  };

  // Check if the current LLM selection is none to disable the form
  const isFormDisabled = selectedLLM === "none";

  // Get the current model name for placeholder
  const currentModelPlaceholder = useMemo(() => {
    if (!selectedLLM || selectedLLM === "none") return "";
    // Get current model from settings
    const modelPrefKey = getModelPrefKey(selectedLLM, activeTab);
    const currentModel = settings?.[modelPrefKey];
    // If we have a model value, use it as placeholder
    return currentModel || "";
  }, [settings, selectedLLM, activeTab]);

  return (
    <form onSubmit={handleModelRefSubmit} className="flex w-full flex-col mt-8">
      {/* Only show the form content if an LLM provider is selected */}
      {selectedLLM && selectedLLM !== "none" ? (
        <>
          <div className="w-full flex flex-row items-center gap-3.5 gap-y-1 pb-3 border-bottom">
            <div className="flex flex-col">
              <div className="text-base font-bold text-foreground">
                {t("custom-user-ai.custom-model-reference")}
              </div>
              <div className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                {t("custom-user-ai.custom-model-reference-description")}
              </div>
            </div>
          </div>

          <div className="flex flex-col w-full mt-2 gap-y-4">
            <div className="flex items-center gap-x-4">
              <div className="relative w-full max-w-[500px]">
                <label
                  htmlFor={`${getModelRefKeyForTab(activeTab)}-name`}
                  className="block mb-2 text-sm font-medium text-foreground/80"
                >
                  {t("custom-user-ai.custom-model-reference-name")}
                </label>{" "}
                <input
                  type="text"
                  id={`${getModelRefKeyForTab(activeTab)}-name`}
                  name="name"
                  placeholder={
                    currentModelPlaceholder ||
                    t("custom-user-ai.custom-model-reference-name-placeholder")
                  }
                  value={modelRefState.values[activeTab]?.name || ""}
                  disabled={isFormDisabled}
                  onChange={handleCustomModelRefChange}
                  className={`dark-input-mdl text-foreground focus:outline-none text-sm rounded-lg block w-full p-2.5 ${
                    isFormDisabled ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                />
              </div>
            </div>
            <div className="flex items-center gap-x-4">
              <div className="relative w-full max-w-[500px]">
                <label
                  htmlFor={`${getModelRefKeyForTab(activeTab)}-description`}
                  className="block mb-2 text-sm font-medium text-foreground/80"
                >
                  {t("custom-user-ai.custom-model-reference-description-label")}
                </label>
                <input
                  type="text"
                  id={`${getModelRefKeyForTab(activeTab)}-description`}
                  name="description"
                  placeholder={t(
                    "custom-user-ai.custom-model-reference-description-placeholder"
                  )}
                  value={modelRefState.values[activeTab]?.description || ""}
                  disabled={isFormDisabled}
                  onChange={handleCustomModelRefChange}
                  className={`dark-input-mdl text-foreground focus:outline-none text-sm rounded-lg block w-full p-2.5 ${
                    isFormDisabled ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                />
              </div>
            </div>

            {modelRefState.hasChanges && (
              <div className="flex justify-start max-w-[500px]">
                <Button
                  type="submit"
                  className="h-[36px] px-3 py-0.5 text-sm whitespace-nowrap min-w-[100px]"
                  disabled={modelRefState.isSaving}
                >
                  {modelRefState.isSaving
                    ? (t("custom-user-ai.saving") as string) ||
                      (t("validate-answer.saving") as string)
                    : (t("custom-user-ai.save-changes") as string) ||
                      (t("validate-answer.save-changes") as string)}
                </Button>
              </div>
            )}
          </div>

          {/* Dynamic Context Window Percentage - Show for all tabs */}
          <div className="flex flex-col mt-6">
            <div className="text-base font-bold text-foreground mb-2">
              {t("custom-user-ai.dynamic-context-window-percentage") as string}
            </div>
            <div className="flex flex-col items-start gap-y-2 w-full max-w-[640px]">
              {/* Wrapper div that prevents selection but allows slider interaction */}
              <div className="relative w-full h-14 mt-1 select-none pointer-events-auto z-[1]">
                {/* Min and Max labels */}
                <div className="flex w-full justify-between items-center px-1 mb-2">
                  <span className="text-xs text-foreground opacity-70">
                    10%
                  </span>
                  <span className="text-xs text-foreground opacity-70">
                    100%
                  </span>
                </div>
                {/* Percentage badge that moves with the slider */}
                <div
                  className="absolute top-7 -translate-x-1/2 z-10 transition-all duration-100 select-none pointer-events-none"
                  style={
                    {
                      "--slider-position": `${(((modelRefState.windowPercentages[activeTab] || 70) - 10) * 100) / 90}%`,
                      left: "var(--slider-position)",
                    } as React.CSSProperties
                  }
                >
                  <span className="flex items-center justify-center bg-primary text-white rounded-full px-3 py-1 text-sm font-medium min-w-[48px] shadow-sm select-none">
                    {modelRefState.windowPercentages[activeTab] || 70}%
                  </span>
                  <div className="w-2 h-2 bg-primary rotate-45 transform -translate-y-1 ml-[18px]"></div>
                </div>
                {/* Colored track overlay - must be pointer-events-none */}
                <div
                  className="absolute h-2 rounded-l-lg bg-primary pointer-events-none top-16 select-none z-0"
                  style={
                    {
                      "--track-width": `${(((modelRefState.windowPercentages[activeTab] || 70) - 10) * 100) / 90}%`,
                      width: "var(--track-width)",
                    } as React.CSSProperties
                  }
                ></div>
                {/* Range input */}
                <input
                  type="range"
                  key={`slider-${activeTab}`}
                  name={getDynamicContextWindowKey(activeTab)}
                  value={modelRefState.windowPercentages[activeTab] || 70}
                  onChange={handleDynamicContextWindowChange}
                  onMouseUp={handleSliderRelease}
                  onTouchEnd={handleSliderRelease}
                  disabled={isFormDisabled}
                  className={`w-full h-2 rounded-lg appearance-none cursor-pointer absolute top-16 z-10
                    ${isFormDisabled ? "opacity-50 cursor-not-allowed" : ""}
                    bg-secondary
                    accent-primary
                    hover:bg-accent
                    focus:outline-none focus:ring-0
                    transition-all
                    select-none
                    no-outline
                    cursor-grab active:cursor-grabbing
                    [&::-webkit-slider-thumb]:appearance-auto [&::-webkit-slider-thumb]:relative [&::-webkit-slider-thumb]:z-20
                    [&::-webkit-slider-thumb]:w-5 [&::-webkit-slider-thumb]:h-5 [&::-webkit-slider-thumb]:rounded-full
                    [&::-webkit-slider-thumb]:bg-primary [&::-webkit-slider-thumb]:border-2 [&::-webkit-slider-thumb]:border-white
                    [&::-webkit-slider-thumb]:select-none [&::-webkit-slider-thumb]:cursor-grab [&::-webkit-slider-thumb]:active:cursor-grabbing
                    [&::-moz-range-thumb]:appearance-auto [&::-moz-range-thumb]:relative [&::-moz-range-thumb]:z-20
                    [&::-moz-range-thumb]:w-5 [&::-moz-range-thumb]:h-5 [&::-moz-range-thumb]:rounded-full
                    [&::-moz-range-thumb]:bg-primary [&::-moz-range-thumb]:border-2 [&::-moz-range-thumb]:border-white
                    [&::-moz-range-thumb]:select-none [&::-moz-range-thumb]:cursor-grab [&::-moz-range-thumb]:active:cursor-grabbing
                    [&::-ms-thumb]:appearance-auto [&::-ms-thumb]:relative [&::-ms-thumb]:z-20
                    [&::-ms-thumb]:w-5 [&::-ms-thumb]:h-5 [&::-ms-thumb]:rounded-full
                    [&::-ms-thumb]:bg-primary [&::-ms-thumb]:border-2 [&::-ms-thumb]:border-white
                    [&::-ms-thumb]:select-none [&::-ms-thumb]:cursor-grab [&::-ms-thumb]:active:cursor-grabbing`}
                  min="10"
                  max="100"
                  step="1"
                  aria-label={
                    t(
                      "custom-user-ai.dynamic-context-window-percentage"
                    ) as string
                  }
                  title={
                    t(
                      "custom-user-ai.dynamic-context-window-percentage"
                    ) as string
                  }
                  placeholder={
                    t(
                      "custom-user-ai.dynamic-context-window-percentage"
                    ) as string
                  }
                  style={
                    {
                      WebkitTapHighlightColor: "transparent",
                      WebkitTouchCallout: "none",
                    } as React.CSSProperties
                  }
                />
              </div>
              <p className="text-xs text-foreground opacity-70 mt-8">
                {
                  t(
                    "custom-user-ai.dynamic-context-window-percentage-desc"
                  ) as string
                }
              </p>
            </div>
          </div>
        </>
      ) : (
        <div className="text-sm text-foreground opacity-70 italic mt-2">
          {
            t(
              "custom-user-ai.select-provider-first",
              "Please select an LLM provider to configure model settings"
            ) as string
          }
        </div>
      )}
    </form>
  );
}

export default function CustomUserAIPreference() {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<string>(CUSTOM_USER_AI_SUFFIX[0]);
  const [saving, setSaving] = useState<boolean>(false);
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [selectedLLMs, setSelectedLLMs] = useState<SelectedLLMs>(
    CUSTOM_USER_AI_SUFFIX.reduce((acc: SelectedLLMs, tab: string) => {
      acc[tab] = "none";
      return acc;
    }, {} as SelectedLLMs)
  );

  const getContextWindow = (provider: string, model: string) => {
    if (!provider || !model) return null;
    const normalizedProvider = provider.toLowerCase();
    if (!MODEL_MAP[normalizedProvider]) {
      return null;
    }
    const normalizedModel = model.trim().toLowerCase();

    // First try to get the context window for the specific model
    const contextWindow =
      MODEL_MAP[normalizedProvider]?.models?.[normalizedModel];
    if (contextWindow) {
      return contextWindow;
    }

    // If model specific context window not found, return the default context window
    return MODEL_MAP[normalizedProvider]?.defaults?.contextWindow || null;
  };

  const updateContextWindow = useCallback(
    (provider: string, modelValue: string) => {
      if (!provider || !modelValue) return;
      getContextWindow(provider, modelValue);
    },
    []
  );

  useEffect(() => {
    const currentSelectedLLM = selectedLLMs[activeTab];
    if (!currentSelectedLLM) return;

    const modelPrefKey = getModelPrefKey(currentSelectedLLM, activeTab);
    const currentModel = settings?.[modelPrefKey];
    if (currentModel) {
      updateContextWindow(currentSelectedLLM, currentModel);
    }
  }, [selectedLLMs, settings, updateContextWindow, activeTab]);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    if (event?.preventDefault) {
      event.preventDefault();
    }
    setSaving(true);
    try {
      const currentSelectedLLM = selectedLLMs[activeTab];
      const data: { [key: string]: any } = {
        [`LLM_PROVIDER${activeTab}`]:
          currentSelectedLLM === "none" ? null : currentSelectedLLM,
      };
      if (event?.target) {
        const formData = new FormData(event.target as HTMLFormElement);
        for (const [key, value] of formData.entries()) {
          if (typeof value === "string" && value && !/^\*+$/.test(value)) {
            data[key] = value;
          }
        }
      }

      // Handle model selection
      const modelPrefKey = getModelPrefKey(currentSelectedLLM, activeTab);
      const modelSelectElement = event?.target
        ? (event.target as HTMLFormElement).querySelector(
            `select[name="${modelPrefKey}"]`
          )
        : null;
      if (modelSelectElement) {
        data[modelPrefKey] = (modelSelectElement as HTMLSelectElement).value;
      }

      const response = await System.updateSystem(data);
      const { error } = response as { error?: string };
      if (error) {
        showToast(
          t("custom-user-ai.llm-settings-save-failed", {
            error:
              (typeof error === "object" && error !== null && "message" in error
                ? (error as { message: string }).message
                : String(error)) || (t("errors.unknown") as string),
          }) as string,
          "error"
        );
      } else {
        showToast(
          (t("custom-user-ai.llm-saved") as string) ||
            (t("show-toast.llm-saved") as string),
          "success"
        );
        const currentSettings = await System.envSettings();
        // Helper function to get API key field name based on provider
        const getApiKeyFieldName = (provider: string) => {
          if (!provider || provider === "none") return "";
          switch (provider.toLowerCase()) {
            case "openai":
              return `OpenAiKey${activeTab}`;
            case "azure":
              return `AzureOpenAiKey${activeTab}`;
            case "anthropic":
              return `AnthropicApiKey${activeTab}`;
            default:
              return `${provider.charAt(0).toUpperCase() + provider.slice(1)}LLMApiKey${activeTab}`;
          }
        };

        const logSettings: { [key: string]: any } = {
          LLMProvider: currentSettings?.[`LLM_PROVIDER${activeTab}`],
          ModelPref:
            currentSettings?.[getModelPrefKey(currentSelectedLLM, activeTab)],
          ApiKey: currentSettings?.[getApiKeyFieldName(currentSelectedLLM)]
            ? "******"
            : null,
        };

        // Add Gemini-specific settings if Gemini is selected
        if (currentSelectedLLM === "gemini" && currentSettings) {
          logSettings.SafetySetting =
            currentSettings[`GeminiSafetySetting${activeTab}`] || "Not set";
        }

        // console.log(
        //   `Current ENV Settings after save for ${activeTab}:`,
        //   logSettings
        // );
      }
    } catch (error: unknown) {
      // console.error("Error saving LLM settings:", error);
      showToast(
        t("custom-user-ai.llm-settings-save-failed", {
          error: (error as Error)?.message || (t("errors.unknown") as string),
        }) as string,
        "error"
      );
    } finally {
      setSaving(false);
      setHasChanges(false);
    }
  };

  // Add a loadSettings function to fetch fresh settings from the server
  const loadSettings = useCallback(async () => {
    setLoading(true);
    try {
      const _settings = await System.keys(true);

      if (!_settings) {
        throw new Error("Failed to fetch settings");
      }
      setSettings(_settings as SystemSettings);

      // Update all selected LLMs from settings
      const newSelectedLLMs: SelectedLLMs = {};

      CUSTOM_USER_AI_SUFFIX.forEach((suffix: string) => {
        newSelectedLLMs[suffix] =
          _settings?.[`LLM_PROVIDER${suffix}`] || "none";
      });

      setSelectedLLMs(newSelectedLLMs);

      // Update context window for the active tab
      const currentProvider = newSelectedLLMs[activeTab];
      if (currentProvider && currentProvider !== "none" && _settings) {
        const modelPrefKey = getModelPrefKey(currentProvider, activeTab);
        const currentModel = _settings[modelPrefKey];
        if (currentModel) {
          updateContextWindow(currentProvider, currentModel);
        }
      }
    } catch {
      // console.error("Error fetching settings:");
      showToast(
        (t("custom-user-ai.settings-fetch-failed") as string) ||
          (t("show-toast.settings-fetch-failed") as string),
        "error"
      );
    } finally {
      // Always set loading to false, regardless of success or failure
      setLoading(false);
    }
  }, [activeTab, t, updateContextWindow]);

  // Memoize the onLLMChange function using useCallback to prevent recreating it on every render
  const onLLMChange = useCallback(
    (newLLM: string | null, isReset = false) => {
      // Handle empty string as "none" to prevent issues
      const llmValue = !newLLM || newLLM === "" ? "none" : newLLM;
      setSelectedLLMs((prev: SelectedLLMs) => ({
        ...prev,
        [activeTab]: llmValue,
      }));
      // If this is a reset operation, make sure we don't show the save button
      if (isReset) {
        setHasChanges(false);
      }
      // Don't set hasChanges to true for auto-saved changes
      // LLM provider changes are auto-saved via BaseLLMPreference
      if (llmValue === "none") {
        // No need to update modelRefState here since it will update via its settings dependency
        const getModelRefKeyForTab = (tab: string) => {
          return tab === "_CUAI"
            ? "custom_model_reference"
            : `custom_model_reference_${tab.replace("_CUAI", "")}`;
        };
        const getDynamicContextWindowKey = (tab: string) => {
          return tab === "_CUAI"
            ? "custom_dynamic_context_window_percentage"
            : `custom_dynamic_context_window_percentage_${tab.replace("_CUAI", "")}`;
        };
        const modelRefKey = getModelRefKeyForTab(activeTab);
        const dynamicContextWindowKey = getDynamicContextWindowKey(activeTab);
        // Only update the specific model reference that's being cleared
        const updates: { [key: string]: any } = {
          [modelRefKey]: "",
          [dynamicContextWindowKey]: null,
        };
        Admin.updateSystemPreferences(updates)
          .then(() => {
            // After clearing, refresh settings to update all components
            loadSettings();
          })
          .catch((_error: unknown) => {
            // console.error("Silent update failed:", _error);
          });
      }
    },
    [activeTab, loadSettings]
  );

  useEffect(() => {
    async function fetchKeys() {
      try {
        const _settings = await System.keys();
        if (!_settings) {
          throw new Error("Failed to fetch settings");
        }
        setSettings(_settings);

        // Update all selected LLMs from settings
        const newSelectedLLMs: SelectedLLMs = {};

        CUSTOM_USER_AI_SUFFIX.forEach((suffix: string) => {
          newSelectedLLMs[suffix] =
            _settings?.[`LLM_PROVIDER${suffix}`] || "none";
        });

        setSelectedLLMs(newSelectedLLMs);
        setLoading(false);
      } catch {
        // console.error("Error fetching settings:");
        showToast(
          (t("custom-user-ai.settings-fetch-failed") as string) ||
            (t("show-toast.settings-fetch-failed") as string),
          "error"
        );
        // Reset to default state since we couldn't fetch settings
        setSelectedLLMs(
          CUSTOM_USER_AI_SUFFIX.reduce((acc: SelectedLLMs, tab: string) => {
            acc[tab] = "none";
            return acc;
          }, {} as SelectedLLMs)
        );
        setLoading(false);
      }
    }
    fetchKeys();
  }, [t]);

  const handleTabClick = (tab: string) => {
    setActiveTab(tab);
  };

  const getTabDisplayName = (suffix: string) => {
    switch (suffix) {
      case "_CUAI":
        return t("custom-user-ai.engine.custom-1") as string;
      case "_CUAI2":
        return t("custom-user-ai.engine.custom-2") as string;
      case "_CUAI3":
        return t("custom-user-ai.engine.custom-3") as string;
      case "_CUAI4":
        return t("custom-user-ai.engine.custom-4") as string;
      case "_CUAI5":
        return t("custom-user-ai.engine.custom-5") as string;
      case "_CUAI6":
        return t("custom-user-ai.engine.custom-6") as string;
      default:
        return suffix.replace("_", "");
    }
  };

  // Handle changes from BaseLLMPreference component
  const handleLLMPreferenceChange = useCallback(
    (args?: {
      isReset?: boolean;
      autoSaved?: boolean;
      llmChanged?: boolean;
      modelChanged?: boolean;
    }) => {
      if (args && args.isReset) {
        // Resets should be handled automatically without showing the save button
        setHasChanges(false);
      } else if (args && args.autoSaved) {
        // Auto-saved changes don't need the save button
        setHasChanges(false);
      } else if (args && args.llmChanged) {
        // LLM provider changes are auto-saved, don't show the save button
        setHasChanges(false);
      } else if (
        args &&
        args.modelChanged &&
        typeof updateContextWindow === "function"
      ) {
        // Model changes are usually auto-saved
        setHasChanges(false);
      } else {
        // Only set hasChanges for manual changes that require saving
        setHasChanges(true);
      }
    },
    [updateContextWindow]
  );

  // Memoize the BaseLLMPreference component
  const memoizedBaseLLMPreference = useMemo(() => {
    return (
      <BaseLLMPreference
        key={`custom-ai-llm-preference-${activeTab}`}
        settings={settings || {}}
        selectedLLM={selectedLLMs[activeTab]}
        supportedProviders={SUPPORTED_CUSTOM_USER_AI_PROVIDERS}
        onLLMChange={onLLMChange}
        moduleSuffix={activeTab}
        onChange={handleLLMPreferenceChange}
        updateContextWindow={updateContextWindow}
        loadSettings={loadSettings}
      />
    );
  }, [
    activeTab,
    settings,
    selectedLLMs,
    onLLMChange,
    handleLLMPreferenceChange,
    updateContextWindow,
    loadSettings,
  ]);

  // Add a helper function to handle the context window slider without text selection
  useEffect(() => {
    // Target the specific slider container div
    const sliderWrapper = document.querySelector(
      ".relative.w-full.h-14.mt-1.select-none"
    ) as HTMLElement | null;

    const preventSelection = (e: Event) => {
      // Allow interaction with the range input itself
      if ((e.target as HTMLInputElement).type !== "range") {
        e.preventDefault();
      }
    };

    if (sliderWrapper) {
      sliderWrapper.addEventListener("selectstart", preventSelection);
      // For Firefox
      (sliderWrapper.style as any).MozUserSelect = "none";
    }

    return () => {
      if (sliderWrapper) {
        sliderWrapper.removeEventListener("selectstart", preventSelection);
        (sliderWrapper.style as any).MozUserSelect = "";
      }
    };
  }, [activeTab]); // Re-apply when tab changes as the element might re-render

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        {loading ? (
          <div className="relative rounded-md w-full h-full overflow-y-scroll">
            <div className="w-full h-full flex justify-center items-center">
              <PreLoader />
            </div>
          </div>
        ) : (
          <div className="relative rounded-md w-full h-full overflow-y-scroll">
            <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4">
              <div className="w-full flex flex-row items-center gap-3.5 gap-y-1 pb-3 border-bottom">
                <Link to={paths.home()}>
                  <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
                </Link>
                <div className="flex flex-col">
                  <div className="flex gap-x-4 items-center">
                    <p className="text-lg leading-6 font-bold text-foreground">
                      {t("custom-user-ai.title") as string}
                    </p>
                  </div>
                  <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                    {t("custom-user-ai.description") as string}
                  </p>
                </div>
              </div>

              {/* Tab Navigation */}
              <div className="flex gap-x-6 border-b border-gray-200 mb-5">
                {CUSTOM_USER_AI_SUFFIX.map((suffix: string) => (
                  <TabButton
                    key={suffix}
                    active={activeTab === suffix}
                    onClick={() => handleTabClick(suffix)}
                    className="px-4 py-3 text-md"
                  >
                    {getTabDisplayName(suffix)}
                  </TabButton>
                ))}
              </div>

              {/* Tab Description */}
              <div className="mb-6">
                {CUSTOM_USER_AI_SUFFIX.includes(activeTab) && (
                  <div className="flex flex-col">
                    <h3 className="text-base font-semibold text-foreground">
                      {
                        t(
                          `custom-user-ai.engine.custom-${CUSTOM_USER_AI_SUFFIX.indexOf(activeTab) + 1}-title`,
                          `Custom Engine ${CUSTOM_USER_AI_SUFFIX.indexOf(activeTab) + 1}`
                        ) as string
                      }
                    </h3>
                    <p className="text-sm text-foreground text-opacity-70 mt-1">
                      {
                        t(
                          `custom-user-ai.engine.custom-${CUSTOM_USER_AI_SUFFIX.indexOf(activeTab) + 1}-description`,
                          `Configure settings for Custom Engine ${CUSTOM_USER_AI_SUFFIX.indexOf(activeTab) + 1}`
                        ) as string
                      }
                    </p>
                  </div>
                )}
              </div>

              {/* LLM Provider Form */}
              <form onSubmit={handleSubmit} className="flex w-full">
                <div className="flex flex-col w-full">
                  <div className="w-full justify-end flex absolute right-3">
                    {hasChanges && (
                      <Button type="submit" className="mr-0 -mb-14">
                        {saving
                          ? (t("custom-user-ai.saving") as string) ||
                            (t("validate-answer.saving") as string)
                          : (t("custom-user-ai.save-changes") as string) ||
                            (t("validate-answer.save-changes") as string)}
                      </Button>
                    )}
                  </div>
                  <div className="text-base font-bold text-foreground mt-3 mb-4">
                    {
                      t(
                        "custom-user-ai.llm-provider-selection",
                        "LLM Provider"
                      ) as string
                    }{" "}
                    - {getTabDisplayName(activeTab)}
                  </div>
                  {memoizedBaseLLMPreference}
                </div>
              </form>

              {/* Completely separate Model Reference Form component */}
              <ModelReferenceForm
                activeTab={activeTab}
                selectedLLM={selectedLLMs[activeTab]}
                settings={settings}
                key={`model-ref-form-${activeTab}`}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
