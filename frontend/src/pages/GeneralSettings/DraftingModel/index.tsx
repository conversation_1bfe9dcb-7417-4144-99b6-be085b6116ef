import { useEffect, useState, FormEvent, ChangeEvent } from "react";
import Sidebar from "@/components/SettingsSidebar";
import System from "@/models/system";
import paths from "@/utils/paths";
import showToast from "@/utils/toast";
import PreLoader from "@/components/Preloader";
import BaseLLMPreference from "@/components/LLMSelection/BaseLLMPreference";

import { ArrowLeft } from "@phosphor-icons/react";
import { Button } from "@/components/Button";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import HeaderWorkspace from "@/components/HeaderWorkspace";

interface Settings {
  LLM_PROVIDER_DD?: string;
  LLM_PROVIDER_DD_2?: string;
  BINARY_LLM_DD?: string;
  BINARY_LLM_USER_LEVEL_DD?: string;
  [key: string]: string | number | boolean | null | undefined;
}

export default function DraftingModel() {
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [settings, setSettings] = useState<Settings>({});
  const [loading, setLoading] = useState(true);

  const [selectedLLM, setSelectedLLM] = useState<string | null>(null);
  const [selectedSecondaryLLM, setSelectedSecondaryLLM] = useState<
    string | null
  >(null);
  const { t } = useTranslation();
  const [showSecondaryLLM, setShowSecondaryLLM] = useState(false);
  const [userLevel, setUserLevel] = useState(false);
  const systemEnabled = t("system.state.enabled");
  const systemDisabled = t("system.state.disabled");

  const handleSubmit = async (
    e?: FormEvent<HTMLFormElement>
  ): Promise<void> => {
    if (e && typeof e.preventDefault === "function") {
      e.preventDefault();
    }
    const form = e?.currentTarget;
    const data: Record<string, string | number | boolean> = {
      LLM_PROVIDER_DD: selectedLLM || "",
      LLM_PROVIDER_DD_2: selectedSecondaryLLM || "",
      BINARY_LLM_DD: showSecondaryLLM ? "on" : "off",
      BINARY_LLM_USER_LEVEL_DD: userLevel ? "on" : "off",
    };
    if (form) {
      const formData = new FormData(form);
      for (const [key, value] of formData.entries()) {
        // Convert File entries to string or skip them
        if (typeof value === "string") {
          data[key] = value;
        }
      }
    }
    const { error } = await System.updateSystem(data);
    setSaving(true);

    if (error) {
      showToast(t("toast.settings.llm-failed", { error }), "error");
    } else {
      showToast(t("show-toast.llm-saved"), "success");
    }
    setSaving(false);
    setHasChanges(!!error);
  };

  const handleShowSecondaryLLMChange = (e: ChangeEvent<HTMLInputElement>) => {
    setShowSecondaryLLM(e.target.checked);
    setHasChanges(true);
  };

  const handleUserLevelChange = (e: ChangeEvent<HTMLInputElement>) => {
    setUserLevel(e.target.checked);
    setHasChanges(true);
  };

  const loadSettings = async (): Promise<void> => {
    const _settings = (await System.keys(true)) as Settings;
    setSettings(_settings);
    setSelectedLLM(_settings?.LLM_PROVIDER_DD || null);
    setSelectedSecondaryLLM(_settings?.LLM_PROVIDER_DD_2 || null);
    setShowSecondaryLLM(_settings?.BINARY_LLM_DD === "on" ? true : false);
    setUserLevel(_settings?.BINARY_LLM_USER_LEVEL_DD === "on" ? true : false);
    setLoading(false);
    setHasChanges(false);
  };

  useEffect(() => {
    loadSettings();
  }, []); // Empty dependency array - only run once on mount

  const handleLLMChange = (newLLM: string) => {
    setSelectedLLM(newLLM);
  };

  const handleSecondaryLLMChange = (newLLM: string) => {
    setSelectedSecondaryLLM(newLLM);
  };

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        {loading ? (
          <div className="relative rounded-md w-full h-full overflow-y-scroll">
            <div className="w-full h-full flex justify-center items-center">
              <PreLoader />
            </div>
          </div>
        ) : (
          <div className="relative rounded-md w-full h-full overflow-y-scroll">
            <form onSubmit={handleSubmit} className="flex w-full">
              <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4">
                <div className="w-full flex flex-row items-center gap-3.5 gap-y-1 pb-3 border-bottom">
                  <Link to={paths.home()}>
                    <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
                  </Link>
                  <div className="flex flex-col">
                    <div className="flex gap-x-4 items-center">
                      <p className="text-lg leading-6 font-bold text-foreground">
                        {t("document-drafting.drafting-llm")}
                      </p>
                    </div>
                    <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                      {t("llm.description")}
                    </p>
                  </div>
                </div>
                <div className="w-full justify-end flex absolute right-6">
                  {hasChanges && (
                    <Button type="submit" className="mr-0 -mb-14">
                      {saving
                        ? t("document-drafting.saving")
                        : t("document-drafting.save")}
                    </Button>
                  )}
                </div>
                <div className="text-base font-bold text-foreground pt-3 mb-4 border-top">
                  {t("llm.provider")}
                  {!loading &&
                    (() => {
                      return (
                        <BaseLLMPreference
                          settings={settings || {}}
                          selectedLLM={selectedLLM}
                          onLLMChange={(llmValue: string | null) =>
                            handleLLMChange(llmValue || "")
                          }
                          moduleSuffix="_DD"
                          setHasChanges={setHasChanges}
                          loadSettings={loadSettings}
                        />
                      );
                    })()}
                </div>
                <div className="mb-4">
                  <div className="flex flex-col gap-y-1">
                    <h2 className="text-base leading-6 font-bold text-foreground">
                      {t("binary_llm_selection.secondary-llm-toggle")}
                    </h2>
                    <p className="text-xs leading-[18px] font-base text-foreground">
                      {t(
                        "binary_llm_selection.secondary-llm-toggle-description"
                      )}
                    </p>
                    <div className="mt-2">
                      <label className="relative inline-flex cursor-pointer items-center">
                        <input
                          type="checkbox"
                          name="show_secondary_llm"
                          checked={showSecondaryLLM}
                          onChange={handleShowSecondaryLLMChange}
                          className="peer sr-only"
                          aria-label={t(
                            "binary_llm_selection.secondary-llm-toggle"
                          )}
                        />
                        <div className="pointer-events-none peer h-6 w-11 rounded-full bg-stone-400 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:shadow-xl after:border after:border-gray-600 after:bg-white after:box-shadow-md after:transition-all after:content-[''] peer-checked:bg-selected-preference-gradient peer-checked:after:translate-x-full peer-checked:after:border-white"></div>
                      </label>
                    </div>
                  </div>
                  <small className="text-[12px] text-foreground">
                    {t("binary_llm_selection.secondary-llm-toggle-status")}
                    <b>{showSecondaryLLM ? systemEnabled : systemDisabled}</b>
                  </small>
                </div>

                {showSecondaryLLM && (
                  <>
                    <div className="mb-4">
                      <div className="flex flex-col gap-y-1">
                        <h2 className="text-base leading-6 font-bold text-foreground">
                          {t("binary_llm_selection.secondary-llm-user-level")}
                        </h2>
                        <p className="text-xs leading-[18px] font-base text-foreground">
                          {t(
                            "binary_llm_selection.secondary-llm-user-level-description"
                          )}
                        </p>
                        <div className="mt-2">
                          <label className="relative inline-flex cursor-pointer items-center">
                            <input
                              type="checkbox"
                              name="user_level"
                              checked={userLevel}
                              onChange={handleUserLevelChange}
                              className="peer sr-only"
                              aria-label={t(
                                "binary_llm_selection.secondary-llm-user-level"
                              )}
                            />
                            <div className="pointer-events-none peer h-6 w-11 rounded-full bg-stone-400 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:shadow-xl after:border after:border-gray-600 after:bg-white after:box-shadow-md after:transition-all after:content-[''] peer-checked:bg-selected-preference-gradient peer-checked:after:translate-x-full peer-checked:after:border-white"></div>
                          </label>
                        </div>
                      </div>
                      <small className="text-[12px] text-foreground">
                        {t("binary_llm_selection.secondary-llm-toggle-status")}
                        <b>{userLevel ? systemEnabled : systemDisabled}</b>
                      </small>
                    </div>

                    <div className="text-base font-bold text-foreground pt-6 mb-4">
                      {t("llm.secondary-provider")}
                    </div>
                    <div className="relative">
                      {!loading && (
                        <BaseLLMPreference
                          settings={settings || {}}
                          selectedLLM={selectedSecondaryLLM}
                          onLLMChange={(llmValue: string | null) =>
                            handleSecondaryLLMChange(llmValue || "")
                          }
                          moduleSuffix="_DD_2"
                          setHasChanges={setHasChanges}
                          loadSettings={loadSettings}
                        />
                      )}
                    </div>
                  </>
                )}
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  );
}
