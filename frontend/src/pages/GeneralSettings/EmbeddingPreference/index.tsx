import React, {
  useEffect,
  useState,
  useRef,
  FormEvent,
  ChangeEvent,
  KeyboardEvent,
  ReactNode,
} from "react";
import Sidebar from "@/components/SettingsSidebar";
import System from "@/models/system";
import paths from "@/utils/paths";
import showToast from "@/utils/toast";
import Open<PERSON>i<PERSON>ogo from "@/media/llmprovider/openai.png";
import AzureOpen<PERSON>iLogo from "@/media/llmprovider/azure.png";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/media/llmprovider/gemini.png";
import LocalAiLogo from "@/media/llmprovider/localai.png";
import Olla<PERSON><PERSON>ogo from "@/media/llmprovider/ollama.png";
import LMStudioLogo from "@/media/llmprovider/lmstudio.png";
import CohereLogo from "@/media/llmprovider/cohere.png";
import Voyage<PERSON>i<PERSON>ogo from "@/media/embeddingprovider/voyageai.png";
import <PERSON><PERSON><PERSON><PERSON> from "@/media/embeddingprovider/jina.png";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ogo from "@/media/llmprovider/generic-openai.png";
import { Gear } from "@phosphor-icons/react";

import PreLoader from "@/components/Preloader";
import ChangeWarningModal from "@/components/ChangeWarning";
import OpenAiOptions from "@/components/EmbeddingSelection/OpenAiOptions";
import AzureAiOptions from "@/components/EmbeddingSelection/AzureAiOptions";
import GeminiOptions from "@/components/EmbeddingSelection/GeminiOptions";
import LocalAiOptions from "@/components/EmbeddingSelection/LocalAiOptions";
import NativeEmbeddingOptions from "@/components/EmbeddingSelection/NativeEmbeddingOptions";
import OllamaEmbeddingOptions from "@/components/EmbeddingSelection/OllamaOptions";
import LMStudioEmbeddingOptions from "@/components/EmbeddingSelection/LMStudioOptions";
import CohereEmbeddingOptions from "@/components/EmbeddingSelection/CohereOptions";
import VoyageAiOptions from "@/components/EmbeddingSelection/VoyageAiOptions";
import JinaOptions from "@/components/EmbeddingSelection/JinaOptions";
import LiteLLMOptions from "@/components/EmbeddingSelection/LiteLLMOptions";
import GenericOpenAiEmbeddingOptions from "@/components/EmbeddingSelection/GenericAiOptions";
import Modal from "@/components/ui/Modal";

import EmbedderItem from "@/components/EmbeddingSelection/EmbedderItem";
import {
  ArrowLeft,
  CaretUpDown,
  MagnifyingGlass,
  X,
} from "@phosphor-icons/react";
import { useModal } from "@/hooks/useModal";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import LiteLLMLogo from "@/media/llmprovider/litellm.png";
import ContextualEmbeddingPreferance from "@/components/ContextualEmbeddingPreferance";
import { Button } from "@/components/Button";

interface Settings {
  EMBEDDING_ENGINE?: string;
  EmbeddingModelPref?: string;
  HasExistingEmbeddings?: boolean;
  HasCachedEmbeddings?: boolean;
  ContextualEmbedding?: boolean;
  ContextualSystemPrompt?: string;
  ContextualUserPrompt?: string;
  [key: string]: any;
}

interface EmbedderType {
  name: string;
  value: string;
  logo: string | ReactNode;
  options: (settings?: Settings) => ReactNode;
  description: string;
  requiredConfig?: string[];
}

interface SystemUpdateResponse {
  error?: string;
}

interface TranslationFunction {
  (key: string, options?: any): string;
}

export const getTranslatedEmbedders = (
  t: TranslationFunction
): EmbedderType[] => [
  {
    name: t("embeder.default.embedder"),
    value: "native",
    logo: <Gear size={40} weight="bold" className="text-foreground" />,
    options: () => <NativeEmbeddingOptions />,
    description: t("embeder.allm"),
  },
  {
    name: "OpenAI",
    value: "openai",
    logo: OpenAiLogo,
    options: (settings?: Settings) => <OpenAiOptions settings={settings} />,
    description: t("embeder.openai"),
    requiredConfig: ["OpenAiKey"],
  },
  {
    name: "Gemini",
    value: "gemini",
    logo: GeminiAiLogo,
    options: (settings?: Settings) => (
      <GeminiOptions settings={settings as any} />
    ),
    description: "Run powerful embedding models from Google AI.",
  },
  {
    name: "Azure OpenAI",
    value: "azure",
    logo: AzureOpenAiLogo,
    options: (settings?: Settings) => (
      <AzureAiOptions settings={settings as any} />
    ),
    description: t("embeder.azure"),
  },
  {
    name: "Local AI",
    value: "localai",
    logo: LocalAiLogo,
    options: (settings?: Settings) => (
      <LocalAiOptions settings={settings as any} />
    ),
    description: t("embeder.localai"),
  },
  {
    name: "Ollama",
    value: "ollama",
    logo: OllamaLogo,
    options: (settings?: Settings) => (
      <OllamaEmbeddingOptions settings={settings as any} />
    ),
    description: t("embeder.ollama"),
  },
  {
    name: "LM Studio",
    value: "lmstudio",
    logo: LMStudioLogo,
    options: (settings?: Settings) => (
      <LMStudioEmbeddingOptions settings={settings as any} />
    ),
    description: t("embeder.lmstudio"),
    requiredConfig: ["LMStudioBasePath"],
  },
  {
    name: "Cohere",
    value: "cohere",
    logo: CohereLogo,
    options: (settings?: Settings) => (
      <CohereEmbeddingOptions settings={settings} />
    ),
    description: t("embeder.cohere"),
  },
  {
    name: "Voyage AI",
    value: "voyageai",
    logo: VoyageAiLogo,
    options: (settings?: Settings) => (
      <VoyageAiOptions settings={settings as any} />
    ),
    description: t("embeder.voyageai"),
    requiredConfig: ["VoyageAiApiKey"],
  },
  {
    name: "Jina",
    value: "jina",
    logo: JinaLogo,
    options: (settings?: Settings) => (
      <JinaOptions settings={settings as any} />
    ),
    description: t("embeder.jina"),
    requiredConfig: ["JinaApiKey"],
  },
  {
    name: "LiteLLM",
    value: "litellm",
    logo: LiteLLMLogo,
    options: (settings?: Settings) => (
      <LiteLLMOptions settings={settings as any} />
    ),
    description: t("embeder.litellm"),
    requiredConfig: ["LiteLLMApiKey"],
  },
  {
    name: "Generic OpenAI",
    value: "generic-openai",
    logo: GenericOpenAiLogo,
    options: (settings?: Settings) => (
      <GenericOpenAiEmbeddingOptions settings={settings as any} />
    ),
    description: t("embeder.generic-openai"),
  },
];

export default function GeneralEmbeddingPreference(): JSX.Element {
  const [saving, setSaving] = useState<boolean>(false);
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const [hasEmbeddings, setHasEmbeddings] = useState<boolean>(false);
  const [hasCachedEmbeddings, setHasCachedEmbeddings] =
    useState<boolean>(false);
  const [settings, setSettings] = useState<Settings | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [filteredEmbedders, setFilteredEmbedders] = useState<EmbedderType[]>(
    []
  );
  const [selectedEmbedder, setSelectedEmbedder] = useState<string | null>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const { isOpen, openModal, closeModal } = useModal();
  const {
    isOpen: isProviderModalOpen,
    openModal: openProviderModal,
    closeModal: closeProviderModal,
  } = useModal();
  const { t } = useTranslation();
  const EMBEDDERS = React.useMemo(() => getTranslatedEmbedders(t), [t]);

  function embedderModelChanged(formEl: HTMLFormElement | null): boolean {
    try {
      if (!formEl) return false;
      const newModel = new FormData(formEl).get("EmbeddingModelPref") as
        | string
        | null;
      if (newModel === null) return false;

      // Map old model name to new model name for comparison
      let currentModel = settings?.EmbeddingModelPref;
      if (currentModel === "gemini-embedding-exp-03-07") {
        currentModel = "text-embedding-large-exp-03-07";
      }

      return currentModel !== newModel;
    } catch {
      // console.error("Error checking embedder model change");
    }
    return false;
  }

  const handleSubmit = async (
    e?: FormEvent<HTMLFormElement>
  ): Promise<void> => {
    // Create a synthetic event if none was provided
    const event = e || { preventDefault: () => {} };
    event.preventDefault();

    if (
      (selectedEmbedder !== settings?.EMBEDDING_ENGINE ||
        embedderModelChanged(
          document.getElementById("embedding-form") as HTMLFormElement
        )) &&
      hasChanges &&
      (hasEmbeddings || hasCachedEmbeddings)
    ) {
      openModal();
    } else {
      await handleSaveSettings();
    }
  };

  const handleSaveSettings = async (): Promise<void> => {
    setSaving(true);
    const form = document.getElementById("embedding-form") as HTMLFormElement;
    const settingsData: Record<string, any> = {};

    // Handle checkbox values first
    form.querySelectorAll('input[type="checkbox"]').forEach((checkbox) => {
      const checkboxElement = checkbox as HTMLInputElement;
      settingsData[checkboxElement.name] = checkboxElement.checked
        ? "true"
        : "false";
    });

    const formData = new FormData(form);
    settingsData.EMBEDDING_ENGINE = selectedEmbedder;
    for (const [key, value] of formData.entries()) {
      if (!Object.prototype.hasOwnProperty.call(settingsData, key)) {
        // Don't override checkbox values
        settingsData[key] = value;
      }
    }

    // Log Jina settings if Jina is selected
    if (selectedEmbedder === "jina") {
      // console.log("\x1b[36m[Jina Settings Saved]\x1b[0m");
      // console.log("- API Key:", settingsData.JinaApiKey ? "*".repeat(20) : "Not Set");
      // console.log("- Model:", settingsData.EmbeddingModelPref);
      // console.log("- Task:", settingsData.JinaTask);
      // console.log("- Dimensions:", settingsData.JinaDimensions);
      // console.log("- Late Chunking:", settingsData.JinaLateChunking);
      // console.log("- Embedding Type:", settingsData.JinaEmbeddingType);
    }

    try {
      const { error }: SystemUpdateResponse =
        await System.updateSystem(settingsData);
      if (error) {
        const errorMessage = error?.toString() || error;
        // Try to translate the error code first
        const translatedError = t(`errors.${errorMessage}`, {
          defaultValue: errorMessage,
        });
        showToast(
          t("show-toast.failed-save-embedding", { error: translatedError }),
          "error",
          {
            clear: true,
          }
        );
        setHasChanges(true);
      } else {
        showToast(t("show-toast.embedding-saved"), "success", { clear: true });
        setHasChanges(false);
      }
    } catch (err: any) {
      showToast(
        t("show-toast.failed-save-embedding", {
          error: t(`errors.${err.message}`, {
            defaultValue:
              err.message?.toString() || err.message || err.toString(),
          }),
        }),
        "error",
        { clear: true }
      );
      setHasChanges(true);
    } finally {
      setSaving(false);
      closeModal();
    }
  };

  const updateChoice = (selection: string): void => {
    setSearchQuery("");
    setSelectedEmbedder(selection);
    closeProviderModal();
    setHasChanges(true);
  };

  const handleXButton = (): void => {
    if (searchQuery.length > 0) {
      setSearchQuery("");
      if (searchInputRef.current) searchInputRef.current.value = "";
    }
  };

  useEffect(() => {
    async function fetchKeys(): Promise<void> {
      try {
        const _settings = (await System.keys()) as Settings;
        setSettings(_settings);
        setSelectedEmbedder(_settings?.EMBEDDING_ENGINE || "native");
        setHasEmbeddings(_settings?.HasExistingEmbeddings || false);
        setHasCachedEmbeddings(_settings?.HasCachedEmbeddings || false);
        setLoading(false);
      } catch {
        // console.error("Error fetching system keys");
        setLoading(false);
      }
    }
    fetchKeys();
  }, []);

  useEffect(() => {
    const filtered = EMBEDDERS.filter((embedder) =>
      embedder.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredEmbedders(filtered);
  }, [searchQuery, selectedEmbedder, EMBEDDERS]);

  const selectedEmbedderObject = EMBEDDERS.find(
    (embedder) => embedder.value === selectedEmbedder
  );

  const handleSearchInputChange = (e: ChangeEvent<HTMLInputElement>): void => {
    setSearchQuery(e.target.value);
  };

  const handleSearchKeyDown = (e: KeyboardEvent<HTMLInputElement>): void => {
    if (e.key === "Enter") e.preventDefault();
  };

  const handleOverlayClick = (): void => {
    setSearchQuery("");
  };

  const handleProviderModalOpen = (): void => {
    openProviderModal();
  };

  const handleFormChange = (): void => {
    setHasChanges(true);
  };

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        {loading ? (
          <div className="relative rounded-md w-full h-full overflow-y-scroll">
            <div className="w-full h-full flex justify-center items-center">
              <PreLoader />
            </div>
          </div>
        ) : (
          <div className="relative rounded-md w-full h-full overflow-y-scroll">
            <form
              id="embedding-form"
              onSubmit={handleSubmit}
              className="flex w-full"
            >
              <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4">
                <div className="w-full flex flex-row items-start gap-3.5 gap-y-1 pb-3 border-white border-b border-opacity-10">
                  <Link to={paths.home()}>
                    <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
                  </Link>
                  <div className="flex flex-col">
                    <div className="flex gap-x-4 items-center">
                      <p className="text-lg leading-6 font-bold text-foreground">
                        {t("embedding.title")}
                      </p>
                    </div>
                    <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                      {t("embedding.desc-start")}
                      <br />
                      {t("embedding.desc-end")}
                    </p>
                  </div>
                </div>
                <div className="w-full justify-end flex absolute right-5">
                  {hasChanges && (
                    <Button
                      type="submit"
                      className="mr-0 -mb-14"
                      disabled={saving}
                    >
                      {saving ? "Saving..." : "Save changes"}
                    </Button>
                  )}
                </div>
                <div className="text-base font-bold text-foreground pt-3 border-top pb-2">
                  {t("embedding.provider.title")}
                </div>
                <div className="relative">
                  {searchQuery.length > 0 && (
                    <div
                      className="fixed top-0 left-0 w-full h-full bg-black bg-opacity-70 backdrop-blur-sm z-10"
                      onClick={handleOverlayClick}
                    />
                  )}
                  {searchQuery.length > 0 ? (
                    <div className="absolute top-0 left-0 w-full max-w-[640px] max-h-[310px] overflow-auto white-scrollbar min-h-[64px] sidebar-block rounded-lg flex flex-col justify-between cursor-pointer z-20">
                      <div className="w-full flex flex-col gap-y-1">
                        <div className="flex items-center sticky top-0 border-b border-[#9CA3AF] mx-4 modal-search-block">
                          <MagnifyingGlass
                            size={20}
                            weight="bold"
                            className="absolute left-4 z-30 text-foreground -ml-4 my-2"
                          />
                          <input
                            type="text"
                            name="embedder-search"
                            autoComplete="off"
                            placeholder={t("embedding.provider.search-embed")}
                            className="-ml-4 my-2 bg-transparent z-20 pl-12 h-[38px] w-full px-4 py-1 text-sm outline-none   text-foreground  placeholder:font-medium"
                            ref={searchInputRef}
                            onChange={handleSearchInputChange}
                            onKeyDown={handleSearchKeyDown}
                          />
                          <X
                            size={20}
                            weight="bold"
                            className="cursor-pointer text-foreground hover:text-[#9CA3AF]"
                            onClick={handleXButton}
                          />
                        </div>
                        <div className="flex-1 pl-4 pr-2 flex flex-col gap-y-1 overflow-y-auto white-scrollbar pb-4">
                          {filteredEmbedders.map((embedder) => (
                            <EmbedderItem
                              key={embedder.name}
                              name={embedder.name}
                              value={embedder.value}
                              image={
                                typeof embedder.logo === "string"
                                  ? embedder.logo
                                  : ""
                              }
                              description={embedder.description}
                              checked={selectedEmbedder === embedder.value}
                              onClick={() => updateChoice(embedder.value)}
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <Button
                      className="w-full max-w-[640px] h-[64px] p-[14px] justify-between cursor-pointer primary-o-btn duration-300"
                      onClick={handleProviderModalOpen}
                    >
                      <div className="flex gap-x-4 items-center">
                        {typeof selectedEmbedderObject?.logo === "string" ? (
                          <img
                            src={selectedEmbedderObject.logo}
                            alt={`${selectedEmbedderObject.name} logo`}
                            className="w-10 h-10 rounded-md"
                          />
                        ) : (
                          <div className="w-10 h-10 rounded-md flex items-center justify-center">
                            {selectedEmbedderObject?.logo}
                          </div>
                        )}
                        <div className="flex flex-col text-left">
                          <div className="text-sm font-semibold text-foreground">
                            {selectedEmbedderObject?.name}
                          </div>
                          <div className="mt-1 text-xs text-foreground">
                            {selectedEmbedderObject?.description}
                          </div>
                        </div>
                      </div>
                      <CaretUpDown
                        size={24}
                        weight="bold"
                        className="text-foreground"
                      />
                    </Button>
                  )}
                </div>
                <div
                  onChange={handleFormChange}
                  className="mt-4 flex flex-col gap-y-1"
                >
                  {selectedEmbedder &&
                    EMBEDDERS.find(
                      (embedder) => embedder.value === selectedEmbedder
                    )?.options(settings || undefined)}

                  <div className="mt-8 border-t border-white/10 pt-8">
                    <ContextualEmbeddingPreferance
                      name="contextualEmbedding"
                      componentId="embedding-form"
                      defaultValue={settings?.ContextualEmbedding || false}
                      systemPrompt={settings?.ContextualSystemPrompt || ""}
                      userPrompt={settings?.ContextualUserPrompt || ""}
                    />
                  </div>
                </div>
              </div>
            </form>
          </div>
        )}
        <Modal
          isOpen={isProviderModalOpen}
          onClose={closeProviderModal}
          title={t("embedding.provider.select")}
          footer={
            <Button variant="secondary" onClick={closeProviderModal}>
              {t("common.cancel")}
            </Button>
          }
        >
          <div className="flex flex-col w-full h-full">
            <div className="flex items-center sticky top-[65px] border-b border-[#9CA3AF] mx-4 my-2 z-10 bg-background">
              <Button
                variant="ghost"
                size="icon"
                disabled={true}
                className="absolute left-0 ml-1 pointer-events-none"
              >
                <MagnifyingGlass
                  size={20}
                  weight="bold"
                  className="text-foreground/60"
                />
              </Button>
              <input
                type="text"
                name="embedder-search"
                autoComplete="off"
                placeholder={t("embedding.provider.search-embed")}
                className="my-2 bg-transparent z-20 pl-10 h-[38px] w-full px-4 py-1 text-sm outline-none text-foreground placeholder:font-medium"
                ref={searchInputRef}
                onChange={handleSearchInputChange}
                onKeyDown={handleSearchKeyDown}
              />
              {searchQuery.length > 0 && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleXButton}
                  className="absolute right-0 mr-1"
                >
                  <X
                    size={20}
                    weight="bold"
                    className="cursor-pointer text-foreground hover:text-[#9CA3AF]"
                  />
                </Button>
              )}
            </div>

            <div className="flex-1 pl-4 pr-2 flex flex-col gap-y-1 overflow-y-auto white-scrollbar pb-4 max-h-[50vh]">
              {filteredEmbedders.map((embedder) => (
                <EmbedderItem
                  key={embedder.name}
                  name={embedder.name}
                  value={embedder.value}
                  image={typeof embedder.logo === "string" ? embedder.logo : ""}
                  description={embedder.description}
                  checked={selectedEmbedder === embedder.value}
                  onClick={() => updateChoice(embedder.value)}
                />
              ))}
            </div>
          </div>
        </Modal>
        <ChangeWarningModal
          warningText={t("embedding.warning.switch-model")}
          onClose={closeModal}
          onConfirm={handleSaveSettings}
          isOpen={isOpen}
        />
      </div>
    </div>
  );
}
