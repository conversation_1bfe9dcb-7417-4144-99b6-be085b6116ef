import React, { useEffect, useState } from "react";
import Sidebar from "@/components/SettingsSidebar";
import PreLoader from "@/components/Preloader";
import Admin from "@/models/admin";
import showToast from "@/utils/toast";
import { numberWithCommas } from "@/utils/numbers";
import { Link } from "react-router-dom";
import paths from "@/utils/paths";
import { ArrowLeft } from "@phosphor-icons/react";
import { useTranslation } from "react-i18next";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import { Button } from "@/components/Button";

function isNullOrNaN(value: number | null): boolean {
  if (value === null) return true;
  return isNaN(value);
}

interface SplitterSettings {
  text_splitter_method?: string;
  text_splitter_chunk_size?: number;
  text_splitter_chunk_overlap?: number;
  text_splitter_jina_max_tokens?: number;
  text_splitter_jina_return_tokens?: boolean;
  text_splitter_jina_return_chunks?: boolean;
  max_embed_chunk_size?: number;
}

//Verify if Jina API needs to be stored in the environment variable for Collector and if so that this happens in this code

export default function EmbeddingTextSplitterPreference() {
  const [settings, setSettings] = useState<SplitterSettings>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const { t } = useTranslation();
  const [splitterMethod, setSplitterMethod] = useState("native");
  const [jinaApiKey, setJinaApiKey] = useState("");
  const [hasApiKey, setHasApiKey] = useState(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const form = new FormData(e.target as HTMLFormElement);

    const newMethod = form.get("text_splitter_method") || "native";
    const jinaApiKey = form.get("JINA_API_KEY");

    // If Jina is selected and no API key is provided, show error
    if (newMethod === "jina" && !jinaApiKey && !hasApiKey) {
      showToast(
        t("error.jina.api-key-required") || "Jina API key is required",
        "error"
      );
      return;
    }

    // If Jina is selected and API key doesn't start with "jina_", show error
    if (
      newMethod === "jina" &&
      jinaApiKey &&
      typeof jinaApiKey === "string" &&
      !jinaApiKey.startsWith("jina_") &&
      !hasApiKey
    ) {
      showToast(
        t("jina.api-key-format") || "Jina API key must start with 'jina_'",
        "error"
      );
      return;
    }

    const chunkSize = Number(form.get("text_splitter_chunk_size"));
    const chunkOverlap = Number(form.get("text_splitter_chunk_overlap"));
    const jinaMaxTokens = Number(form.get("text_splitter_jina_max_tokens"));
    const jinaReturnTokens =
      form.get("text_splitter_jina_return_tokens") === "on";
    const jinaReturnChunks =
      form.get("text_splitter_jina_return_chunks") === "on";

    // Validate only if user chooses "native"
    if (newMethod === "native" && chunkOverlap >= chunkSize) {
      showToast(t("error.native.chunk-overlap-too-large"), "error");
      return;
    }

    setSaving(true);

    // If using Jina, update the API key in environment only if the user has entered a new key
    if (newMethod === "jina" && jinaApiKey && !hasApiKey) {
      try {
        // console.log("Updating Jina API key...");
        const result = await Admin.updateEnvironmentVariable({
          newValues: {
            JINA_API_KEY: jinaApiKey ? String(jinaApiKey) : "",
          },
        });

        if (!result.success) {
          // console.error("Failed to update Jina API key:", result.error);
          showToast(
            t("error.jina.update-failed", { error: result.error }),
            "error"
          );
          setSaving(false);
          return;
        }

        // console.log("Jina API key updated successfully");
        // We've now updated the collector to automatically detect env changes
        showToast(t("show-toast.api-key-saved"), "success");
      } catch (error: unknown) {
        // console.error("Error updating Jina API key:", error);
        showToast(
          t("error.jina.update-error", {
            error: error instanceof Error ? error.message : t("error.unknown"),
          }),
          "error"
        );
        setSaving(false);
        return;
      }
    }

    try {
      const updateResult = await Admin.updateSystemPreferences({
        text_splitter_method: newMethod,
        // only save chunk size & overlap if 'native' is selected:
        text_splitter_chunk_size:
          newMethod === "native" && !isNullOrNaN(chunkSize)
            ? chunkSize
            : settings?.text_splitter_chunk_size,
        text_splitter_chunk_overlap:
          newMethod === "native" && !isNullOrNaN(chunkOverlap)
            ? chunkOverlap
            : settings?.text_splitter_chunk_overlap,
        // only save Jina settings if 'jina' is selected
        text_splitter_jina_max_tokens:
          newMethod === "jina" && !isNullOrNaN(jinaMaxTokens)
            ? jinaMaxTokens
            : settings?.text_splitter_jina_max_tokens,
        text_splitter_jina_return_tokens:
          newMethod === "jina"
            ? jinaReturnTokens
            : settings?.text_splitter_jina_return_tokens,
        text_splitter_jina_return_chunks:
          newMethod === "jina"
            ? jinaReturnChunks
            : settings?.text_splitter_jina_return_chunks,
      });

      if (!updateResult.success) {
        showToast(
          t("error.settings.update-failed", { error: updateResult.error }),
          "error"
        );
        setSaving(false);
        return;
      }

      setSaving(false);
      setHasChanges(false);
      showToast(t("show-toast.chunking-settings"), "success");

      if (newMethod === "jina") {
        setTimeout(() => {
          showToast(t("common.auto-environment-update"), "info", 6000 as any);
        }, 1000);
      }
    } catch (error: unknown) {
      // console.error("Error updating system preferences:", error);
      showToast(
        t("error.settings.general-update-failed", {
          error: error instanceof Error ? error.message : t("error.unknown"),
        }),
        "error"
      );
      setSaving(false);
    }
  };

  useEffect(() => {
    async function fetchSettings() {
      const _settings = (await Admin.systemPreferences())?.settings;
      setSettings((_settings as SplitterSettings) ?? {});
      setSplitterMethod((_settings as any)?.text_splitter_method || "native");

      // If Jina is selected, fetch the API key
      if ((_settings as any)?.text_splitter_method === "jina") {
        const { value } = await Admin.getEnvironmentVariable("JINA_API_KEY");
        setJinaApiKey(value || "");
        setHasApiKey(!!value);
      }

      setLoading(false);
    }
    fetchSettings();
  }, []);

  // Mark form as changed if user toggles method, etc.
  useEffect(() => {
    setHasChanges(true);

    // Fetch Jina API key when switching to Jina
    if (splitterMethod === "jina") {
      Admin.getEnvironmentVariable("JINA_API_KEY").then(({ value }) => {
        setJinaApiKey(value || "");
        setHasApiKey(!!value);
      });
    }
  }, [splitterMethod]);

  // Handle API key input change
  const handleApiKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // When the user types in the API key field, update the state
    // This indicates that the user is actively changing the key
    setJinaApiKey(e.target.value);
    setHasApiKey(false); // User is entering a new key
    setHasChanges(true);
  };

  // Handle API key input focus
  const handleApiKeyFocus = () => {
    if (hasApiKey) {
      // When focusing on a masked API key field, clear it to allow for new input
      // but don't actually clear the stored key until form submission
      setJinaApiKey("");
      setHasApiKey(false);
      setHasChanges(true);
    }
  };

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        {loading ? (
          <div className="relative rounded-md w-full h-full overflow-y-scroll">
            <div className="w-full h-full flex justify-center items-center">
              <PreLoader />
            </div>
          </div>
        ) : (
          <div className="relative rounded-md w-full h-full overflow-y-scroll">
            <form
              onSubmit={handleSubmit}
              onChange={() => setHasChanges(true)}
              className="flex w-full"
            >
              <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4">
                <div className="w-full flex flex-row items-start gap-3.5 gap-y-1 pb-3 border-white border-b border-opacity-10">
                  <Link to={paths.home()}>
                    <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
                  </Link>
                  <div className="flex flex-col">
                    <div className="flex gap-x-4 items-center">
                      <p className="text-lg leading-6 font-bold text-foreground">
                        {t("text.title")}
                      </p>
                    </div>
                    <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                      {t("text.desc-start")} <br />
                      {t("text.desc-end")}
                    </p>
                    <p className="text-xs leading-[18px] font-semibold text-foreground">
                      {t("text.warn-start")} <i>{t("text.warn-center")}</i>
                      {t("text.warn-end")}
                    </p>
                  </div>
                </div>
                <div className="w-full justify-end flex absolute right-5">
                  {hasChanges && (
                    <Button type="submit" className="mr-0 -mb-14 z-10">
                      {saving ? "Saving..." : "Save changes"}
                    </Button>
                  )}
                </div>

                {/* Text Splitter Method */}
                <div className="flex flex-col gap-y-4 pt-3 border-top">
                  <div className="flex flex-col max-w-[300px] mb-4">
                    <label className="normal-text text-sm font-semibold mb-1">
                      {t("text.method.title") || "Text Splitter Method"}
                    </label>
                    <select
                      name="text_splitter_method"
                      value={splitterMethod}
                      onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                        setSplitterMethod(e.target.value)
                      }
                      className="dark-input-mdl border-none text-foreground placeholder:text-foreground/20 text-sm rounded-lg block w-full p-2.5"
                      title={t("text.method.title") || "Text Splitter Method"}
                    >
                      <option value="native">Native</option>
                      <option value="jina">Jina Segmenter</option>
                    </select>
                    <p className="text-xs normal-text mt-1">
                      {splitterMethod === "native"
                        ? t("text.method.native-explain") ||
                          "Use local chunk size & overlap for splitting."
                        : t("text.method.jina-explain") ||
                          "Delegate chunking/segmenting to Jina's built-in method."}
                    </p>
                  </div>
                  {/* For 'native', we show chunk size & overlap */}
                  {splitterMethod === "native" && (
                    <>
                      <div className="flex flex-col max-w-[300px]">
                        <div className="flex flex-col gap-y-2 mb-4">
                          <label className="normal-text text-sm font-semibold block">
                            {t("text.method.size.title") || "Chunk Size"}
                          </label>
                          <p className="text-xs normal-text">
                            {t("text.method.size.description") ||
                              "The maximum number of tokens per chunk."}
                          </p>
                        </div>
                        <input
                          type="number"
                          name="text_splitter_chunk_size"
                          min={1}
                          max={settings?.max_embed_chunk_size || 1000}
                          onWheel={(e) => e?.currentTarget?.blur()}
                          className="dark-input-mdl border-none normal-text placeholder:normal-text/20 text-sm rounded-lg block w-full p-2.5"
                          placeholder={`Max length (1-${settings?.max_embed_chunk_size || 1000} tokens)`}
                          defaultValue={
                            isNullOrNaN(
                              settings?.text_splitter_chunk_size ?? null
                            )
                              ? Math.min(
                                  1000,
                                  settings?.max_embed_chunk_size || 1000
                                )
                              : Math.min(
                                  Number(settings?.text_splitter_chunk_size),
                                  settings?.max_embed_chunk_size || 1000
                                )
                          }
                          required={true}
                          autoComplete="off"
                        />
                        <p className="text-xs text-foreground mt-2">
                          {t("text.size.recommend")}{" "}
                          <b>
                            {numberWithCommas(
                              settings?.max_embed_chunk_size || 1000
                            )}
                          </b>
                          {" tokens"}.
                        </p>
                      </div>

                      <div className="flex flex-col max-w-[300px] mt-8">
                        <div className="flex flex-col gap-y-2 mb-4">
                          <label className="normal-text text-sm font-semibold block">
                            {t("text.overlap.title")}
                          </label>
                          <p className="text-xs normal-text">
                            {t("text.overlap.description")}
                          </p>
                        </div>
                        <input
                          type="number"
                          name="text_splitter_chunk_overlap"
                          min={0}
                          onWheel={(e) => e?.currentTarget?.blur()}
                          placeholder="Max overlap of text chunks"
                          className="dark-input-mdl normal-text placeholder:normal-text/20 text-sm rounded-lg block w-full p-2.5"
                          defaultValue={
                            isNullOrNaN(
                              settings?.text_splitter_chunk_overlap ?? null
                            )
                              ? 20
                              : Number(settings?.text_splitter_chunk_overlap)
                          }
                          required={true}
                          autoComplete="off"
                        />
                      </div>
                    </>
                  )}

                  {/* For 'jina', show API key and max chunk tokens */}
                  {splitterMethod === "jina" && (
                    <div className="flex flex-col max-w-[300px] mt-4">
                      <div className="mb-6">
                        <label className="normal-text text-sm font-semibold block">
                          {t("text.method.jina.api_key") || "Jina API Key"}
                        </label>
                        <div className="relative">
                          {hasApiKey ? (
                            <div
                              className="dark-input-mdl normal-text placeholder:normal-text/20 text-sm rounded-lg block w-full p-2.5 mt-1 cursor-text"
                              onClick={handleApiKeyFocus}
                            >
                              {"*".repeat(20)}
                            </div>
                          ) : (
                            <input
                              type="password"
                              name="JINA_API_KEY"
                              className="dark-input-mdl normal-text placeholder:normal-text/20 text-sm rounded-lg block w-full p-2.5 mt-1"
                              placeholder={
                                t("jina.api-key-placeholder") ||
                                "Enter your Jina API key (starts with jina_)"
                              }
                              value={jinaApiKey}
                              onChange={handleApiKeyChange}
                              required={splitterMethod === "jina" && !hasApiKey}
                              minLength={10}
                            />
                          )}
                          {/* This hidden input ensures the form submission includes the API key value
                              even when the visible field shows asterisks */}
                          <input
                            type="hidden"
                            name="JINA_API_KEY"
                            value={jinaApiKey}
                          />
                        </div>
                        <p className="text-xs normal-text mt-2">
                          {t("text.method.jina.api_key_desc") ||
                            "Required for using Jina's segmentation service. The key will be stored in your environment."}
                        </p>
                      </div>
                      <p className="text-sm normal-text font-semibold mb-1">
                        {t("text.method.jina-info") || "Jina chunking active."}
                      </p>

                      <label className="normal-text text-sm font-semibold block mt-2">
                        {t("text.method.jina.max_tokens") ||
                          "Jina: Max Tokens per Chunk"}
                      </label>
                      <input
                        type="number"
                        name="text_splitter_jina_max_tokens"
                        min={1}
                        max={2000}
                        onWheel={(e) => e?.currentTarget?.blur()}
                        placeholder="Default 1000"
                        className="dark-input-mdl normal-text placeholder:normal-text/20 text-sm rounded-lg block w-full p-2.5 mt-1"
                        defaultValue={
                          isNullOrNaN(
                            settings?.text_splitter_jina_max_tokens ?? null
                          )
                            ? 1000
                            : Number(settings?.text_splitter_jina_max_tokens)
                        }
                        required={true}
                        autoComplete="off"
                      />
                      <p className="text-xs normal-text mt-2">
                        {t("text.method.jina.max_tokens_desc") ||
                          "Defines the max tokens in each chunk for Jina's segmenter (maximum 2000 tokens)."}
                      </p>
                      <div className="mt-4">
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            name="text_splitter_jina_return_tokens"
                            className="form-checkbox"
                            defaultChecked={
                              settings?.text_splitter_jina_return_tokens ?? true
                            }
                          />
                          <span className="normal-text text-sm">
                            {t("text.method.jina.return_tokens") ||
                              "Return token information"}
                          </span>
                        </label>
                        <p className="text-xs normal-text mt-1 ml-6">
                          {t("text.method.jina.return_tokens_desc") ||
                            "Include token count and tokenizer information in the response."}
                        </p>
                      </div>

                      <div className="mt-4">
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            name="text_splitter_jina_return_chunks"
                            className="form-checkbox"
                            defaultChecked={
                              settings?.text_splitter_jina_return_chunks ?? true
                            }
                          />
                          <span className="normal-text text-sm">
                            {t("text.method.jina.return_chunks") ||
                              "Return chunk information"}
                          </span>
                        </label>
                        <p className="text-xs normal-text mt-1 ml-6">
                          {t("text.method.jina.return_chunks_desc") ||
                            "Include chunk positions and metadata in the response."}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  );
}
