import React, { useEffect, useState, use<PERSON><PERSON>back, FormEvent } from "react";
import Sidebar from "@/components/SettingsSidebar";
import System from "@/models/system";
import paths from "@/utils/paths";
import showToast from "@/utils/toast";
import PreLoader from "@/components/Preloader";
import { ArrowLeft } from "@phosphor-icons/react";
import { Link, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import { MODEL_MAP } from "@/utils/AiProviders/modelMap";
import BaseLLMPreference from "@/components/LLMSelection/BaseLLMPreference";
import { SUPPORTED_UPGRADE_PROVIDERS } from "@/utils/AiProviders/supportedLLMProviders";
import PromptTemplateEditor from "@/components/PromptTemplateEditor";
import AutoCodingTemplateEditor from "@/components/AutoCodingTemplateEditor";
import { getModelPref<PERSON><PERSON> } from "@/components/LLMSelection/LLMProviderConfig";
import { Button } from "@/components/Button";
import Toggle from "@/components/ui/Toggle";

interface LLMPreferenceConfig {
  moduleSuffix: string;
  titleKey: string;
  descriptionKey: string;
  systemKey: string;
  supportedProviders: string[] | null;
  renderAdditionalContent?: () => React.ReactNode;
}

interface Settings {
  [key: string]: any;
  disableValidationPrompt?: boolean | string;
}

interface Params {
  type?: string;
}

const LLM_PREFERENCE_CONFIGS: Record<string, LLMPreferenceConfig> = {
  general: {
    moduleSuffix: "",
    titleKey: "llm.title",
    descriptionKey: "llm.description",
    systemKey: "LLM_PROVIDER",
    supportedProviders: null,
  },
  validate: {
    moduleSuffix: "_VA",
    titleKey: "validate-answer.title",
    descriptionKey: "validate-answer.description",
    systemKey: "LLM_PROVIDER_VA",
    supportedProviders: null,
  },
  cdb: {
    moduleSuffix: "_CDB",
    titleKey: "cdb-llm-preference.title",
    descriptionKey: "cdb-llm-preference.description",
    systemKey: "LLM_PROVIDER_CDB",
    supportedProviders: null,
  },
  template: {
    moduleSuffix: "_TM",
    titleKey: "template-llm-preference.title",
    descriptionKey: "template-llm-preference.description",
    systemKey: "LLM_PROVIDER_TM",
    supportedProviders: null,
  },
  prompt_upgrade: {
    moduleSuffix: "_PU",
    titleKey: "llm.prompt-upgrade.title",
    descriptionKey: "llm.prompt-upgrade.description",
    systemKey: "LLM_PROVIDER_PU",
    supportedProviders: SUPPORTED_UPGRADE_PROVIDERS,
    renderAdditionalContent: () => <PromptTemplateEditor />,
  },
  auto_coding: {
    moduleSuffix: "_AC",
    titleKey: "llm.auto-coding.title",
    descriptionKey: "llm.auto-coding.description",
    systemKey: "LLM_PROVIDER_AC",
    supportedProviders: SUPPORTED_UPGRADE_PROVIDERS,
    renderAdditionalContent: () => <AutoCodingTemplateEditor />,
  },
};

export default function GenericLLMPreference() {
  const { type = "general" } = useParams() as Params;
  const config = LLM_PREFERENCE_CONFIGS[type] || LLM_PREFERENCE_CONFIGS.general;

  const [saving, setSaving] = useState<boolean>(false);
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const [settings, setSettings] = useState<Settings | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [selectedLLM, setSelectedLLM] = useState<string | null>(null);
  const [disableValidationPrompt, setDisableValidationPrompt] =
    useState<boolean>(false);
  const { t } = useTranslation();

  const [_selectedModelWindow, setSelectedModelWindow] = useState<
    number | null
  >(null);
  const modelPrefKey = getModelPrefKey(selectedLLM || "", config.moduleSuffix);

  const getContextWindow = (provider: string, model: string): number | null => {
    if (!provider || !model) return null;
    const normalizedProvider = provider.toLowerCase();

    if (!MODEL_MAP[normalizedProvider as keyof typeof MODEL_MAP]) {
      return null;
    }

    // First try to get the context window for the specific model
    const providerConfig =
      MODEL_MAP[normalizedProvider as keyof typeof MODEL_MAP];
    const contextWindow =
      providerConfig && "models" in providerConfig
        ? (providerConfig.models as any)?.[model]
        : undefined;
    if (contextWindow) {
      return contextWindow;
    }

    // If model specific context window not found, return the default context window
    return (
      MODEL_MAP[normalizedProvider as keyof typeof MODEL_MAP]?.defaults
        ?.contextWindow || null
    );
  };

  const updateContextWindow = useCallback(
    (provider: string, modelValue: string): void => {
      if (!provider || !modelValue) return;
      const contextWindow = getContextWindow(provider, modelValue);
      setSelectedModelWindow(contextWindow);
    },
    []
  );

  useEffect(() => {
    if (!selectedLLM) return;
    const currentModel = settings?.[modelPrefKey];

    if (currentModel) {
      updateContextWindow(selectedLLM, currentModel);
    }
  }, [modelPrefKey, selectedLLM, settings, updateContextWindow]);

  const handleSubmit = async (
    event?: FormEvent<HTMLFormElement>
  ): Promise<void> => {
    if (event?.preventDefault) {
      event.preventDefault();
    }

    if (saving) return;
    setSaving(true);

    try {
      // Don't include the LLM provider in the save data since it's already saved by BaseLLMPreference
      const data: Record<string, any> = {};

      // Get form data but exclude LLM-related fields that are auto-saved
      const formData = event?.target
        ? new FormData(event.target as HTMLFormElement)
        : new FormData();
      for (const [key, value] of formData.entries()) {
        // Skip LLM provider and model preferences as they're auto-saved
        if (!key.includes("LLM_PROVIDER") && !key.includes("ModelPref")) {
          data[key] = value;
        }
      }

      // Handle validation-specific settings
      if (type === "validate") {
        const prefResponse = await System.updatePreferences({
          disableValidationPrompt,
        });
        if (!prefResponse.success) {
          throw new Error(prefResponse.error || "Failed to update preferences");
        }
      }

      // Only update system if we have non-LLM data to save
      if (Object.keys(data).length > 0) {
        const envResponse = await System.updateSystem(data);
        if (envResponse.error) {
          throw new Error(envResponse.error);
        }
      }

      showToast(t("show-toast.llm-saved"), "success");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      showToast(
        t("show-toast.llm-settings-save-failed", { error: errorMessage }),
        "error"
      );
    } finally {
      setSaving(false);
      setHasChanges(false);
    }
  };

  const handleLLMChange = (newLLM: string | null): void => {
    setSelectedLLM(newLLM);
    // Don't set hasChanges here since LLM changes are auto-saved
  };

  const loadSettings = useCallback(
    async (forceRefresh = false): Promise<void> => {
      setLoading(true);

      try {
        const _settings = (await System.keys(forceRefresh)) as Settings;
        setSettings(_settings);
        setSelectedLLM(_settings?.[config.systemKey]);
        if (type === "validate") {
          const val = _settings?.disableValidationPrompt;
          setDisableValidationPrompt(val === true || val === "true");
        }
      } catch (error) {
        console.error("Error loading settings:", error);
      } finally {
        setLoading(false);
      }
    },
    [config.systemKey, type]
  );

  useEffect(() => {
    loadSettings();
    return () => {
      setSelectedLLM(null);
      setSelectedModelWindow(null);
      setHasChanges(false);
      setDisableValidationPrompt(false);
    };
  }, [type, loadSettings]);

  // Handle changes from BaseLLMPreference
  const handleBaseLLMChange = useCallback((args?: any) => {
    // Don't show save button for LLM changes as they're auto-saved
    if (args?.llmChanged || args?.autoSaved || args?.isReset) {
      return;
    }
    // Only show save button for other changes
    setHasChanges(true);
  }, []);

  const componentKey = `llm-preference-${type}`;

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        {loading ? (
          <div className="relative rounded-md w-full h-full overflow-y-scroll">
            <div className="w-full h-full flex justify-center items-center">
              <PreLoader />
            </div>
          </div>
        ) : (
          <div className="relative rounded-md w-full h-full overflow-y-scroll">
            <form className="flex w-full" onSubmit={handleSubmit}>
              <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4">
                <div className="w-full flex flex-row items-center gap-3.5 gap-y-1 pb-3 border-bottom">
                  <Link to={paths.home()}>
                    <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
                  </Link>
                  <div className="flex flex-col">
                    <div className="flex gap-x-4 items-center">
                      <p className="text-lg leading-6 font-bold text-foreground">
                        {t(config.titleKey)}
                      </p>
                    </div>
                    <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                      {t(config.descriptionKey)}
                    </p>
                  </div>
                </div>
                <div className="w-full justify-end flex absolute right-3">
                  {hasChanges && (
                    <Button
                      type="button"
                      onClick={() => handleSubmit()}
                      className="mr-0 -mb-14"
                    >
                      {saving ? "Saving..." : "Save changes"}
                    </Button>
                  )}
                </div>
                <div className="text-base font-bold text-foreground mt-3 mb-4">
                  {t("llm.provider")}
                  <BaseLLMPreference
                    key={componentKey}
                    settings={settings || {}}
                    selectedLLM={selectedLLM}
                    onLLMChange={handleLLMChange}
                    moduleSuffix={config.moduleSuffix}
                    supportedProviders={config.supportedProviders}
                    onChange={handleBaseLLMChange}
                    setHasChanges={null}
                    loadSettings={null}
                    updateContextWindow={updateContextWindow}
                    allowSystemStandard={
                      type === "validate" ||
                      type === "prompt_upgrade" ||
                      type === "template"
                    }
                  />
                </div>

                <div className="border-b border-stroke mb-4"></div>

                {type === "validate" && (
                  <div className="py-4">
                    <Toggle
                      label={t("llm.validation-prompt.disable.label")}
                      description={t(
                        "llm.validation-prompt.disable.description"
                      )}
                      checked={disableValidationPrompt}
                      onCheckedChange={(checked) => {
                        setDisableValidationPrompt(checked);
                        setHasChanges(true);
                      }}
                    />
                  </div>
                )}

                <div className="py-4 flex flex-col gap-y-4">
                  {config.renderAdditionalContent &&
                    config.renderAdditionalContent()}
                </div>
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  );
}
