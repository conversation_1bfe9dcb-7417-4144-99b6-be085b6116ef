import { useEffect, useState, ChangeEvent, FormEvent } from "react";
import Sidebar from "@/components/SettingsSidebar";
import PreLoader from "@/components/Preloader";
import Admin from "@/models/admin";
import showToast from "@/utils/toast";
import { Link } from "react-router-dom";
import paths from "@/utils/paths";
import { ArrowLeft } from "@phosphor-icons/react";
import { useTranslation } from "react-i18next";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import { Button } from "@/components/Button";
import Toggle from "@/components/ui/Toggle";
import type { PdrSettings as PdrSettingsType } from "@/types/admin";

function isNullOrNaN(value: unknown): boolean {
  if (value === null) return true;
  return isNaN(Number(value));
}

export default function PdrSettings() {
  const [settings, setSettings] = useState<Partial<PdrSettingsType>>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const { t } = useTranslation();

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const form = new FormData(e.target as HTMLFormElement);

    try {
      setSaving(true);
      await Admin.updatePdrSettings({
        adjacentVector: {
          enabled: true,
          threshold: undefined,
          maxDistance: undefined,
          configuration: {
            adjacentVector: isNullOrNaN(form.get("adjacent_vector_limit"))
              ? 1
              : Number(form.get("adjacent_vector_limit")),
          },
        },
        keepPdrVectors: form.get("keep_pdr_vectors") === "on",
        globalPdrOverride: form.get("global_pdr_override") === "on",
      });
      setSaving(false);
      setHasChanges(false);
      showToast(t("pdr-settings.toast-success"), "success");
    } catch {
      // console.error("Error updating PDR settings");
      showToast(t("pdr-settings.toast-fail"), "error");
    }
  };

  useEffect(() => {
    async function fetchSettings() {
      const settings = await Admin.getPdrSettings();
      setSettings(settings ?? {});
      setLoading(false);
    }
    fetchSettings();
  }, []);

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        {loading ? (
          <div className="relative rounded-md w-full h-full overflow-y-scroll">
            <div className="w-full h-full flex justify-center items-center">
              <PreLoader />
            </div>
          </div>
        ) : (
          <div className="relative rounded-md w-full h-full overflow-y-scroll">
            <form
              onSubmit={handleSubmit}
              onChange={() => setHasChanges(true)}
              className="flex w-full"
            >
              <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4">
                <div className="w-full flex flex-row items-start gap-3.5 gap-y-1 pb-3 border-white border-b border-opacity-10">
                  <Link to={paths.home()}>
                    <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
                  </Link>
                  <div className="flex flex-col">
                    <div className="flex gap-x-4 items-center">
                      <p className="text-lg leading-6 font-bold text-foreground">
                        {t("pdr-settings.title")}
                      </p>
                    </div>
                    <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                      {t("pdr-settings.description")} <br />
                      {t("pdr-settings.desc-end")}
                    </p>
                  </div>
                </div>
                <div className="w-full justify-end flex absolute right-5">
                  {hasChanges && (
                    <Button type="submit" className="mr-0 -mb-14 z-10">
                      {saving ? "Saving..." : "Save changes"}
                    </Button>
                  )}
                </div>

                <div className="flex flex-col gap-y-4 mt-8">
                  <div className="flex flex-col max-w-[300px]">
                    <div className="flex flex-col gap-y-2 mb-4">
                      <label className="text-foreground text-sm font-semibold block">
                        {t("pdr-settings.global-override.title")}
                      </label>
                      <p className="text-xs text-foreground">
                        {t("pdr-settings.global-override.description")}
                      </p>
                    </div>
                    <Toggle
                      name="global_pdr_override"
                      checked={settings?.globalPdrOverride ?? true}
                      onChange={(e: ChangeEvent<HTMLInputElement>) => {
                        setSettings({
                          ...settings,
                          globalPdrOverride: e.target.checked,
                        });
                        setHasChanges(true);
                      }}
                    />
                    <small className="text-[12px] text-foreground mt-2">
                      {t("pdr-settings.global-override.title")}:{" "}
                      <b>
                        {settings?.globalPdrOverride
                          ? t("system.state.enabled")
                          : t("system.state.disabled")}
                      </b>
                    </small>
                  </div>
                </div>

                <div className="flex flex-col gap-y-4 mt-8">
                  <div className="flex flex-col max-w-[300px]">
                    <div className="flex flex-col gap-y-2 mb-4">
                      <label className="text-foreground text-sm font-semibold block">
                        {t("pdr-settings.adjacent-vector-limit")}
                      </label>
                      <p className="text-xs text-foreground">
                        {t("pdr-settings.adjacent-vector-limit-desc")}
                      </p>
                    </div>
                    <input
                      type="number"
                      name="adjacent_vector_limit"
                      min={0}
                      onWheel={(e) => e?.currentTarget?.blur()}
                      placeholder={t(
                        "pdr-settings.adjacent-vector-limit-placeholder"
                      )}
                      className="dark-input-mdl text-foreground placeholder:text-foreground/20 text-sm rounded-lg block w-full p-2.5"
                      defaultValue={
                        isNullOrNaN(settings?.adjacentVector)
                          ? 1
                          : Number(settings?.adjacentVector)
                      }
                      required={true}
                      autoComplete="off"
                    />
                  </div>
                </div>

                <div className="flex flex-col gap-y-4 mt-8">
                  <div className="flex flex-col max-w-[300px]">
                    <div className="flex flex-col gap-y-2 mb-4">
                      <label className="text-foreground text-sm font-semibold block">
                        {t("pdr-settings.keep-pdr-vectors")}
                      </label>
                      <p className="text-xs text-foreground">
                        {t("pdr-settings.keep-pdr-vectors-desc")}
                      </p>
                    </div>
                    <Toggle
                      name="keep_pdr_vectors"
                      checked={settings?.keepPdrVectors ?? false}
                      onChange={(e: ChangeEvent<HTMLInputElement>) => {
                        setSettings({
                          ...settings,
                          keepPdrVectors: e.target.checked,
                        });
                        setHasChanges(true);
                      }}
                    />
                    <small className="text-[12px] text-foreground mt-2">
                      {t("pdr-settings.keep-pdr-vectors")}:{" "}
                      <b>
                        {settings?.keepPdrVectors
                          ? t("system.state.enabled")
                          : t("system.state.disabled")}
                      </b>
                    </small>
                  </div>
                </div>
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  );
}
