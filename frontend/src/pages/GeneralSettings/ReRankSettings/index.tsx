import { useEffect, useState } from "react";
import Sidebar from "@/components/SettingsSidebar";
import showToast from "@/utils/toast";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import { useTranslation } from "react-i18next";
import { ArrowLeft } from "@phosphor-icons/react";
import { Link } from "react-router-dom";
import paths from "@/utils/paths";
import Admin from "@/models/admin";
import Toggle from "@/components/ui/Toggle";

export default function ReRankSettings() {
  const [settings, setSettings] = useState({
    enable_lancedb_rerank: "off",
    rerank_vector_count: "50",
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const { t } = useTranslation();
  const systemEnabled = t("system.state.enabled");
  const systemDisabled = t("system.state.disabled");

  useEffect(() => {
    async function fetchSettings() {
      try {
        const { settings: fetchedSettings } =
          await Admin.systemPreferencesByFields([
            "enable_lancedb_rerank",
            "rerank_vector_count",
          ]);
        setSettings({
          enable_lancedb_rerank:
            fetchedSettings?.enable_lancedb_rerank || "off",
          rerank_vector_count: fetchedSettings?.rerank_vector_count || "50",
        });
        setLoading(false);
      } catch {
        // console.error("Failed to fetch settings");
        showToast(t("settings.fetch-error"), "error");
        setLoading(false);
      }
    }
    fetchSettings();
  }, [t]);

  const handleToggle = () => {
    setSettings((prev) => ({
      ...prev,
      enable_lancedb_rerank: prev.enable_lancedb_rerank === "on" ? "off" : "on",
    }));
    setHasChanges(true);
  };

  const handleVectorCountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === "" || /^\d+$/.test(value)) {
      setSettings((prev) => ({
        ...prev,
        rerank_vector_count: value,
      }));
      setHasChanges(true);
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setSaving(true);
    try {
      const { success, error } = await Admin.updateSystemPreferences({
        enable_lancedb_rerank: settings.enable_lancedb_rerank,
        rerank_vector_count: settings.rerank_vector_count || "50",
      });
      if (success) {
        showToast(t("common.savesuccess"), "success");
        setHasChanges(false);
      } else {
        throw new Error(error || "Failed to save settings");
      }
    } catch {
      // console.error("Failed to save settings");
      showToast(t("common.saveerror"), "error");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="overflow-hidden w-full h-full flex flex-col">
        <HeaderWorkspace />
        <div className="flex h-[calc(100vh-var(--header-height))]">
          <Sidebar />
          <div className="flex-1 flex flex-col h-full overflow-y-auto bg-background">
            <div className="w-full h-full flex justify-center items-center">
              <div className="loading-spinner"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        <div className="flex-1 flex flex-col h-full overflow-y-auto bg-background">
          <div className="relative rounded-md w-full h-full overflow-y-scroll">
            <form
              onSubmit={handleSubmit}
              className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4"
            >
              {/* Header Section */}
              <div className="w-full flex flex-row items-center gap-3.5 gap-y-1 pb-4">
                <Link to={paths.settings.system()}>
                  <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
                </Link>
                <div className="flex flex-col">
                  <div className="items-center">
                    <p className="text-lg leading-6 font-bold text-foreground">
                      {t("system.rerank.title")}
                    </p>
                  </div>
                  <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                    {t("system.rerank.description")}
                  </p>
                </div>
              </div>

              {/* Save Changes Button */}
              {hasChanges && (
                <div className="flex absolute right-4 top-2 justify-end text-md">
                  <button
                    type="submit"
                    disabled={saving}
                    className="mt-3 mr-0 px-4 py-2 primary-bg text-white rounded hover:bg-blue-700 disabled:opacity-50"
                  >
                    {saving ? t("button.saving") : t("button.save")}
                  </button>
                </div>
              )}

              {/* Re-rank Settings Section */}
              <div className="flex flex-col gap-y-4 pt-3 border-top">
                <div className="flex flex-col max-w-[300px]">
                  <div className="flex flex-col gap-y-2 mb-4">
                    <label className="text-foreground text-sm font-semibold block">
                      {t("system.rerank.enable-title")} (
                      {t("system.rerank.lancedb-only")})
                    </label>
                    <p className="text-xs text-foreground">
                      {t("system.rerank.enable-description")}{" "}
                      {t("system.rerank.lancedb-notice")}
                    </p>
                  </div>
                  <div className="mt-2">
                    <Toggle
                      checked={Boolean(settings.enable_lancedb_rerank === "on")}
                      onChange={handleToggle}
                      disabled={saving}
                    />
                  </div>
                  <small className="text-[12px] text-foreground mt-1">
                    {t("system.rerank.status")}:{" "}
                    <b>
                      {settings.enable_lancedb_rerank === "on"
                        ? systemEnabled
                        : systemDisabled}
                    </b>
                  </small>
                </div>

                {/* Vector Count Input */}
                {settings.enable_lancedb_rerank === "on" && (
                  <div className="flex flex-col max-w-[300px] mt-8">
                    <div className="flex flex-col gap-y-2 mb-4">
                      <label className="text-foreground text-sm font-semibold block">
                        {t("system.rerank.vector-count-title")}
                      </label>
                      <p className="text-xs text-foreground">
                        {t("system.rerank.vector-count-description")}
                      </p>
                    </div>
                    <input
                      type="number"
                      name="rerank_vector_count"
                      min={1}
                      onWheel={(e) => e?.currentTarget?.blur()}
                      value={settings.rerank_vector_count}
                      onChange={handleVectorCountChange}
                      className="dark-input-mdl border-none text-foreground placeholder:text-foreground/20 text-sm rounded-lg block w-full p-2.5"
                      placeholder="50"
                    />
                  </div>
                )}
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
