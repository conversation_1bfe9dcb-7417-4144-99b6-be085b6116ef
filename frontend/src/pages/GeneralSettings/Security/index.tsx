import React, { useEffect, useState } from "react";
import Sidebar from "@/components/SettingsSidebar";
import showToast from "@/utils/toast";
import System from "@/models/system";
import paths from "@/utils/paths";
import { AUTH_TIMESTAMP, AUTH_TOKEN, AUTH_USER } from "@/utils/constants";
import PreLoader from "@/components/Preloader";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { ArrowLeft } from "@phosphor-icons/react";
import Toggle from "@/components/ui/Toggle";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import { Button } from "@/components/Button";

export default function GeneralSecurity(): React.ReactElement {
  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        <div className="relative rounded-md w-full h-full overflow-y-scroll">
          <UserMode />
          <PasswordProtection />
        </div>
      </div>
    </div>
  );
}

function UserMode(): React.ReactElement {
  const { t } = useTranslation();
  const [saving, setSaving] = useState<boolean>(false);
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const [useMultiUserMode, setUseMultiUserMode] = useState<boolean>(false);
  const [usePublicUserMode, setUsePublicUserMode] = useState<boolean>(false);
  const [multiUserModeEnabled, setMultiUserModeEnabled] =
    useState<boolean>(false);
  const [publicUserModeEnabled, setPublicUserModeEnabled] =
    useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);

  const handleSubmit = async (
    e: React.FormEvent<HTMLFormElement> | null
  ): Promise<void> => {
    setSaving(true);
    setHasChanges(false);

    if (e && !multiUserModeEnabled && useMultiUserMode) {
      e.preventDefault();

      const form = new FormData(e.currentTarget);
      const data = {
        username: form.get("username") as string,
        password: form.get("password") as string,
      };

      const { success, error } = await System.setupMultiUser(data);
      if (success) {
        showToast(t("show-toast.multiuser-enabled"), "success");
        setSaving(false);
        setTimeout(() => {
          window.localStorage.removeItem(AUTH_USER);
          window.localStorage.removeItem(AUTH_TOKEN);
          window.localStorage.removeItem(AUTH_TIMESTAMP);
          window.location.href = paths.settings.users();
        }, 2_000);
        return;
      }

      showToast(t("toast.settings.multi-user-failed", { error }), "error");
      setSaving(false);
      return;
    }

    const data = {
      publicUserMode: multiUserModeEnabled ? usePublicUserMode : false,
      username: "publicuser",
      password: "publicuser",
    };

    const { success, error } = await System.setupPublicUser(data);
    if (success) {
      if (usePublicUserMode) {
        showToast(t("show-toast.publicuser-enabled"), "success");
      } else {
        showToast(t("show-toast.publicuser-disabled"), "success");
      }
      setSaving(false);
      setPublicUserModeEnabled(usePublicUserMode);
      return;
    }

    showToast(t("toast.settings.public-user-failed", { error }), "error");
    setSaving(false);
  };

  useEffect(() => {
    async function fetchData(): Promise<void> {
      try {
        setLoading(true);
        const multiUserModeEnabled = await System.isMultiUserMode();
        setUseMultiUserMode(multiUserModeEnabled);
        setMultiUserModeEnabled(multiUserModeEnabled);

        const publicUserModeEnabled = await System.isPublicUserMode();
        setUsePublicUserMode(publicUserModeEnabled);
        setPublicUserModeEnabled(publicUserModeEnabled);
      } catch {
        // console.error("Error fetching data");
        // Handle error (e.g., show an error message to the user)
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="transition-all duration-500 relative md:ml-[2px] md:mr-[8px] md:my-[16px] md:rounded-[26px] p-[18px] overflow-y-scroll">
        <div className="h-full flex justify-center items-center">
          <PreLoader />
        </div>
      </div>
    );
  }

  return (
    <form
      onSubmit={handleSubmit}
      onChange={() => setHasChanges(true)}
      className=""
    >
      <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[86px] md:py-4">
        <div className="w-full flex flex-row items-center gap-3.5 gap-y-1 pb-3">
          <Link to={paths.home()}>
            <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
          </Link>
          <div className="flex flex-col">
            <div className="flex gap-x-4 items-center">
              <p className="text-lg leading-6 font-bold text-foreground">
                {t("multi.title")}
              </p>
            </div>
            <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
              {t("multi.description")}
            </p>
          </div>
        </div>
        {hasChanges && (
          <div className="flex justify-end absolute right-5">
            <Button type="submit" className="mr-0 -mb-20">
              {saving
                ? t("security.button.saving")
                : t("security.button.save-changes")}
            </Button>
          </div>
        )}
        <div className="relative w-full max-h-full border-top">
          <div className="relative rounded-lg">
            <div className="flex items-start justify-between px-6 pt-3"></div>
            <div className="space-y-6 flex h-full w-full">
              <div className="w-full flex flex-col gap-y-1">
                <div className="border-bottom pb-3">
                  <label
                    htmlFor="multi-enable-checkbox"
                    className="mb-2.5 block font-medium text-foreground"
                  >
                    {t("multi.enable.is-enable")}
                  </label>
                  <label className="relative inline-flex cursor-not-allowed items-center">
                    <input
                      id="multi-enable-checkbox"
                      type="checkbox"
                      checked={true}
                      onChange={() => {}}
                      disabled={true}
                      className="peer sr-only"
                    />
                    <div className="peer h-6 w-11 rounded-full bg-selected-preference-gradient after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:shadow-xl after:border after:border-white after:bg-white after:box-shadow-md after:transition-all after:content-[''] peer-checked:bg-selected-preference-gradient peer-checked:after:translate-x-full peer-checked:after:border-white"></div>
                  </label>
                  <p className="mt-2 text-xs text-gray-500">
                    {t("security.multi-user-mode-permanent")}
                  </p>
                </div>
                <div className="w-full flex flex-col gap-y-2 my-2">
                  <div className="w-80">
                    <label className="block mb-3 font-medium text-foreground">
                      <span>{t("multi.enable.username")}</span>
                      <input
                        id="username"
                        className="dark-input-mdl text-foreground text-sm rounded-lg block w-full p-2"
                        name="username"
                        type="text"
                        placeholder={
                          (t("multi.enable.username-placeholder") as string) ||
                          "Enter username"
                        }
                        title={
                          (t("multi.enable.username") as string) || "Username"
                        }
                        minLength={2}
                        required={true}
                        autoComplete="off"
                        disabled={multiUserModeEnabled}
                        defaultValue={multiUserModeEnabled ? "********" : ""}
                      />
                    </label>
                  </div>
                  <div className="mt-4 w-80">
                    <label
                      htmlFor="password"
                      className="block mb-3 font-medium text-foreground"
                    >
                      {t("multi.enable.password")}
                    </label>
                    <input
                      className="dark-input-mdl text-foreground text-sm rounded-lg block w-full p-2"
                      name="password"
                      type="text"
                      placeholder={t("multi.enable.password-placeholder")}
                      minLength={8}
                      required={true}
                      autoComplete="off"
                      defaultValue={multiUserModeEnabled ? "********" : ""}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center justify-between space-x-14 border-bottom pb-3">
              <p className="text-foreground text-xs rounded-lg w-96">
                {t("multi.enable.description")}
              </p>
            </div>

            {useMultiUserMode && (
              <div className="mt-3">
                <label className="mb-2.5 block font-medium text-foreground">
                  {publicUserModeEnabled
                    ? t("public-mode.enabled")
                    : t("public-mode.enable")}
                </label>
                <Toggle
                  checked={usePublicUserMode}
                  onChange={() => setUsePublicUserMode(!usePublicUserMode)}
                />

                <div className="flex items-center justify-between space-x-14">
                  <p className="text-foreground text-xs rounded-lg w-96">
                    {t("security.public-workspace.access-description")}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </form>
  );
}

const PW_REGEX = new RegExp(/^[a-zA-Z0-9_\-!@$%^&*();]+$/);

function PasswordProtection(): React.ReactElement | null {
  const [saving, setSaving] = useState<boolean>(false);
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const [multiUserModeEnabled, setMultiUserModeEnabled] =
    useState<boolean>(false);
  const [usePassword, setUsePassword] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const { t } = useTranslation();

  const handleSubmit = async (
    e: React.FormEvent<HTMLFormElement>
  ): Promise<void> => {
    e.preventDefault();
    if (multiUserModeEnabled) return;
    const form = new FormData(e.currentTarget);
    const password = form.get("password") as string;

    if (!PW_REGEX.test(password)) {
      showToast(t("security.password-validation.restricted-chars"), "error");
      setSaving(false);
      return;
    }

    setSaving(true);
    setHasChanges(false);
    const data = {
      usePassword,
      newPassword: password,
    };

    const { success, error } = await System.updateSystemPassword(data);
    if (success) {
      showToast(t("show-toast.page-refresh"), "success");
      setSaving(false);
      setTimeout(() => {
        window.localStorage.removeItem(AUTH_USER);
        window.localStorage.removeItem(AUTH_TOKEN);
        window.localStorage.removeItem(AUTH_TIMESTAMP);
        window.location.reload();
      }, 3_000);
      return;
    } else {
      showToast(t("toast.settings.password-failed", { error }), "error");
      setSaving(false);
    }
  };

  useEffect(() => {
    async function fetchIsMultiUserMode(): Promise<void> {
      setLoading(true);
      const multiUserModeEnabled = await System.isMultiUserMode();
      const settings = await System.keys();
      setMultiUserModeEnabled(multiUserModeEnabled);
      setUsePassword(!!settings?.RequiresAuth);
      setLoading(false);
    }
    fetchIsMultiUserMode();
  }, []);

  if (loading) {
    return (
      <div className=" transition-all duration-500 relative md:ml-[2px] md:mr-[8px] md:my-[16px] md:rounded-[26px] p-[18px] overflow-y-scroll">
        <div className="w-full h-full flex justify-center items-center">
          <PreLoader />
        </div>
      </div>
    );
  }

  if (multiUserModeEnabled) return null;

  return (
    <form
      onSubmit={handleSubmit}
      onChange={() => setHasChanges(true)}
      className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4"
    >
      <div className="w-full flex flex-col gap-y-1 pb-6">
        <div className="w-full flex flex-col gap-y-1">
          <div className="items-center flex gap-x-4">
            <p className="text-lg leading-6 font-bold text-foreground">
              {t("multi.password.title")}
            </p>
          </div>
          <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
            {t("multi.password.description")}
          </p>
        </div>
        {hasChanges && (
          <div className="flex justify-end">
            <Button type="submit" className="mt-3 mr-0 -mb-20">
              {saving
                ? t("security.button.saving")
                : t("security.button.save-changes")}
            </Button>
          </div>
        )}
        <div className="relative w-full max-h-full">
          <div className="relative rounded-lg">
            <div className="flex items-start justify-between px-6 py-4"></div>
            <div className="space-y-6 flex h-full w-full">
              <div className="w-full flex flex-col gap-y-4">
                <div className="">
                  <label
                    htmlFor="password-protection-checkbox"
                    className="mb-2.5 block font-medium text-foreground"
                  >
                    {t("multi.instance.title")}
                  </label>

                  <label className="relative inline-flex cursor-pointer items-center">
                    <input
                      id="password-protection-checkbox"
                      type="checkbox"
                      onChange={() => setUsePassword(!usePassword)}
                      checked={usePassword}
                      className="peer sr-only"
                    />
                    <div className="peer h-6 w-11 rounded-full bg-stone-400 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:shadow-xl after:border after:border-gray-600 after:bg-white after:box-shadow-md after:transition-all after:content-[''] peer-checked:bg-selected-preference-gradient peer-checked:after:translate-x-full peer-checked:after:border-white"></div>
                  </label>
                </div>
                {usePassword && (
                  <div className="w-full flex flex-col gap-y-2 my-">
                    <div className="w-80">
                      <label className="block mb-3 font-medium text-foreground">
                        <span>{t("multi.instance.password")}</span>
                        <input
                          id="password"
                          name="password"
                          type="text"
                          className="dark-input-mdl text-foreground text-sm rounded-lg block w-full p-2.5  focus:outline-none"
                          placeholder={
                            (t(
                              "multi.enable.password-placeholder"
                            ) as string) || "Enter password"
                          }
                          title={
                            (t("multi.instance.password") as string) ||
                            "Password"
                          }
                          minLength={8}
                          required={true}
                          autoComplete="off"
                          defaultValue={usePassword ? "********" : ""}
                        />
                      </label>
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center justify-between space-x-14 pt-1">
              <p className="text-foreground text-xs rounded-lg w-96">
                {t("multi.instance.description")}
              </p>
            </div>
          </div>
        </div>
      </div>
    </form>
  );
}
