import React, { useEffect, useState, useCallback } from "react";
import Sidebar from "@/components/SettingsSidebar";
import System from "@/models/system";
import paths from "@/utils/paths";
import showToast from "@/utils/toast";
import PreLoader from "@/components/Preloader";
import { ArrowLeft } from "@phosphor-icons/react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import BaseLLMPreference from "@/components/LLMSelection/BaseLLMPreference";
import { Button } from "@/components/Button";
import Toggle from "@/components/ui/Toggle";
import type { SystemSettings } from "@/types";

const SUPPORT_FUNCTIONS = [
  {
    key: "promptUpgrade",
    settingKey: "supportFunctionPromptUpgrade",
    titleKey: "support-functions-llm.prompt-upgrade.title",
    descriptionKey: "support-functions-llm.prompt-upgrade.description",
  },
  {
    key: "validation",
    settingKey: "supportFunctionValidation",
    titleKey: "support-functions-llm.validation.title",
    descriptionKey: "support-functions-llm.validation.description",
  },
  {
    key: "manualTime",
    settingKey: "supportFunctionManualTime",
    titleKey: "support-functions-llm.manual-time.title",
    descriptionKey: "support-functions-llm.manual-time.description",
  },
];

// Mapping from provider names to their corresponding settings keys
const PROVIDER_MODEL_PREFERENCE_MAPPING: Record<string, string> = {
  openai: "OpenAiModelPref_SUPPORT",
  anthropic: "AnthropicModelPref_SUPPORT",
  gemini: "GeminiLLMModelPref_SUPPORT",
  groq: "GroqModelPref_SUPPORT",
  deepseek: "DeepSeekModelPref_SUPPORT",
  lmstudio: "LMStudioModelPref_SUPPORT",
  ollama: "OllamaLLMModelPref_SUPPORT",
  localai: "LocalAiModelPref_SUPPORT",
  azure: "AzureOpenAiModelPref_SUPPORT",
  koboldcpp: "KoboldCPPModelPref_SUPPORT",
  togetherai: "TogetherAiModelPref_SUPPORT",
  openrouter: "OpenRouterModelPref_SUPPORT",
  mistral: "MistralModelPref_SUPPORT",
  perplexity: "PerplexityModelPref_SUPPORT",
  textgenwebui: "TextGenWebUIModelPref_SUPPORT",
  "generic-openai": "GenericOpenAiModelPref_SUPPORT",
  bedrock: "BedrockModelPref_SUPPORT",
  fireworksai: "FireworksAiModelPref_SUPPORT",
  xai: "XAiModelPref_SUPPORT",
};

interface SupportFunctionToggles {
  promptUpgrade: boolean;
  validation: boolean;
  manualTime: boolean;
  [key: string]: boolean;
}

export default function SupportFunctionsLLM() {
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedLLM, setSelectedLLM] = useState<string | null>(null);
  const [supportFunctionToggles, setSupportFunctionToggles] =
    useState<SupportFunctionToggles>({
      promptUpgrade: false,
      validation: false,
      manualTime: false,
    });
  const { t } = useTranslation();

  const handleSubmit = async (event?: React.FormEvent<HTMLFormElement>) => {
    if (event?.preventDefault) {
      event.preventDefault();
    }

    if (saving) return;
    setSaving(true);

    try {
      const data: { [key: string]: any } = {
        supportFunctionPromptUpgrade:
          supportFunctionToggles.promptUpgrade || false,
        supportFunctionValidation: supportFunctionToggles.validation || false,
        supportFunctionManualTime: supportFunctionToggles.manualTime || false,
      };

      // Always include LLM provider if it has changed
      if (selectedLLM !== settings?.LLMProvider_SUPPORT) {
        data.LLMProvider_SUPPORT = selectedLLM;
      }

      // Always include model preferences for the selected provider if they exist
      if (selectedLLM && selectedLLM !== "system-standard") {
        const modelPrefKey = PROVIDER_MODEL_PREFERENCE_MAPPING[selectedLLM];
        if (modelPrefKey && settings?.[modelPrefKey] !== undefined) {
          data[modelPrefKey] = settings[modelPrefKey];
        }
      }

      const response = await System.setSupportFunctionsLLM(data);
      if (!response.success) {
        throw new Error(response.error || "Failed to update settings");
      }

      showToast(t("support-functions-llm.save-success"), "success");
    } catch (error: unknown) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      showToast(
        t("support-functions-llm.save-error", { error: errorMsg }),
        "error"
      );
    } finally {
      setSaving(false);
      setHasChanges(false);
    }
  };

  const handleLLMChange = (newLLM: string | null) => {
    setSelectedLLM(newLLM);
    setHasChanges(true);
  };

  const handleToggleChange = (functionKey: string, checked: boolean) => {
    setSupportFunctionToggles((prev) => ({
      ...prev,
      [functionKey]: checked,
    }));
    setHasChanges(true);
  };

  const loadSettings = useCallback(
    async (forceRefresh = false) => {
      setLoading(true);

      if (setHasChanges) {
        setHasChanges(false);
      }

      try {
        const _settings = await System.keys(forceRefresh);
        setSettings(_settings as SystemSettings);
        const llmProvider = _settings?.LLMProvider_SUPPORT ?? null;
        setSelectedLLM(llmProvider);

        // Load support function toggle states
        const toggles: SupportFunctionToggles = {
          promptUpgrade: false,
          validation: false,
          manualTime: false,
        };
        SUPPORT_FUNCTIONS.forEach((func) => {
          const value = _settings?.[func.settingKey];
          toggles[func.key] = value === true || value === "true";
        });
        setSupportFunctionToggles(toggles);
      } catch {
        // console.error("Error loading settings:");
      } finally {
        setLoading(false);
      }
    },
    [setHasChanges]
  );

  useEffect(() => {
    loadSettings();
    return () => {
      setSelectedLLM(null);
      setSupportFunctionToggles({
        promptUpgrade: false,
        validation: false,
        manualTime: false,
      });
      setHasChanges(false);
    };
  }, [loadSettings]);

  const componentKey = `support-functions-llm`;

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        {loading ? (
          <div className="relative rounded-md w-full h-full overflow-y-scroll">
            <div className="w-full h-full flex justify-center items-center">
              <PreLoader />
            </div>
          </div>
        ) : (
          <div className="relative rounded-md w-full h-full overflow-y-scroll">
            <form className="flex w-full">
              <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4">
                <div className="w-full flex flex-row items-center gap-3.5 gap-y-1 pb-3 border-bottom">
                  <Link to={paths.home()}>
                    <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
                  </Link>
                  <div className="flex flex-col">
                    <div className="flex gap-x-4 items-center">
                      <p className="text-lg leading-6 font-bold text-foreground">
                        {t("support-functions-llm.title")}
                      </p>
                    </div>
                    <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                      {t("support-functions-llm.description")}
                    </p>
                  </div>
                </div>
                <div className="w-full justify-end flex absolute right-3">
                  {hasChanges && (
                    <Button
                      type="button"
                      onClick={(e) => handleSubmit(e as any)}
                      className="mr-0 -mb-14"
                    >
                      {saving ? t("common.saving") : t("common.save")}
                    </Button>
                  )}
                </div>

                <div className="text-base font-bold text-foreground mt-3 mb-4">
                  {t("llm.provider")}
                  <BaseLLMPreference
                    key={componentKey}
                    settings={settings ?? undefined}
                    selectedLLM={selectedLLM}
                    onLLMChange={handleLLMChange}
                    moduleSuffix="_SUPPORT"
                    supportedProviders={null}
                    setHasChanges={setHasChanges}
                    loadSettings={loadSettings}
                    updateContextWindow={() => {}}
                    allowSystemStandard={true}
                  />
                </div>

                <div className="border-b border-stroke mb-4"></div>

                <div className="text-base font-bold text-foreground mb-4">
                  {t("support-functions-llm.functions.title")}
                </div>

                <div className="py-4 flex flex-col gap-y-6">
                  {SUPPORT_FUNCTIONS.map((func) => (
                    <Toggle
                      key={func.key}
                      label={t(func.titleKey)}
                      description={t(func.descriptionKey)}
                      checked={supportFunctionToggles[func.key] || false}
                      onCheckedChange={(checked: boolean) =>
                        handleToggleChange(func.key, checked)
                      }
                    />
                  ))}
                </div>
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  );
}
