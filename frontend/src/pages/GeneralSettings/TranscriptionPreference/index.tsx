import React, {
  useEffect,
  useState,
  useRef,
  FormEvent,
  ChangeEvent,
  KeyboardEvent,
} from "react";
import Sidebar from "@/components/SettingsSidebar";
import System from "@/models/system";
import paths from "@/utils/paths";
import showToast from "@/utils/toast";
import PreLoader from "@/components/Preloader";
import OpenAiLogo from "@/media/llmprovider/openai.png";
import OpenAiWhisperOptions from "@/components/TranscriptionSelection/OpenAiOptions";
import NativeTranscriptionOptions from "@/components/TranscriptionSelection/NativeTranscriptionOptions";
import LLMItem from "@/components/LLMSelection/LLMItem";
import {
  ArrowLeft,
  CaretUpDown,
  MagnifyingGlass,
  X,
} from "@phosphor-icons/react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import { Button } from "@/components/Button";

// Base64 encoded gear icon SVG
const GEAR_ICON =
  "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCAyNTYgMjU2IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxwYXRoIGZpbGw9ImN1cnJlbnRDb2xvciIgZD0iTTIxNiAxMjhhODggODggMCAxIDEtODgtODggODguMSA4OC4xIDAgMCAxIDg4IDg4Wm0tODggNzJhNzIgNzIgMCAxIDAgLTcyLTcyIDcyLjEgNzIuMSAwIDAgMCA3MiA3MlptODgtNzJhOTkuNyA5OS43IDAgMCAwLTUuNi0yOC4zTDIzNCA4NmwtMjgtNDguNS0yMy42IDEzLjZhMTAwLjMgMTAwLjMgMCAwIDAtMjQuNC0xNC4xVjIwSDEwMnYxNi4xYTEwMC4zIDEwMC4zIDAgMCAwLTI0LjQgMTQuMUw1NCAzNi42IDI2IDg1LjFsMjMuNiAxMy42QTk5LjcgOTkuNyAwIDAgMCA0NCAxMjdhOTkuNyA5OS43IDAgMCAwIDUuNiAyOC4zTDI2IDE2OC45bDI4IDQ4LjUgMjMuNi0xMy42YTEwMC4zIDEwMC4zIDAgMCAwIDI0LjQgMTQuMVYyMzRoNTZ2LTE2LjFhMTAwLjMgMTAwLjMgMCAwIDAgMjQuNC0xNC4xbDIzLjYgMTMuNiAyOC00OC41LTIzLjYtMTMuNkE5OS43IDk5LjcgMCAwIDAgMjE2IDEyN1oiLz48L3N2Zz4=";

interface Provider {
  name: (t: any) => string;
  value: string;
  logo: string;
  options: (settings: any) => React.ReactNode;
  description: (t: any) => string;
}

const PROVIDERS: Provider[] = [
  {
    name: (t) => t("transcription.openai-name"),
    value: "openai",
    logo: OpenAiLogo,
    options: (settings) => <OpenAiWhisperOptions settings={settings} />,
    description: (t) => t("transcription.openai-desc"),
  },
  {
    name: (t) => t("transcription.default-built-in"),
    value: "local",
    logo: GEAR_ICON,
    options: (settings) => <NativeTranscriptionOptions settings={settings} />,
    description: (t) => t("transcription.default-built-in-desc"),
  },
];

export default function TranscriptionModelPreference() {
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [settings, setSettings] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredProviders, setFilteredProviders] = useState<Provider[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<string | null>(null);
  const [searchMenuOpen, setSearchMenuOpen] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const { t } = useTranslation();

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const form = e.target as HTMLFormElement;
    const data: Record<string, any> = { WHISPER_PROVIDER: selectedProvider };
    const formData = new FormData(form);

    for (const [key, value] of formData.entries()) data[key] = value;
    const { error } = await System.updateSystem(data);
    setSaving(true);

    if (error) {
      showToast(
        t("toast.settings.transcription-save-failed", { error }),
        "error"
      );
    } else {
      showToast(t("show-toast.transcription-saved"), "success");
    }
    setSaving(false);
    setHasChanges(!!error);
  };

  const updateProviderChoice = (selection: string) => {
    setSearchQuery("");
    setSelectedProvider(selection);
    setSearchMenuOpen(false);
    setHasChanges(true);
  };

  const handleXButton = (): void => {
    if (searchQuery.length > 0) {
      setSearchQuery("");
      if (searchInputRef.current) searchInputRef.current.value = "";
    } else {
      setSearchMenuOpen(!searchMenuOpen);
    }
  };

  useEffect(() => {
    async function fetchKeys(): Promise<void> {
      const _settings = await System.keys();
      setSettings(_settings);
      setSelectedProvider(_settings?.WHISPER_PROVIDER || "local");
      setLoading(false);
    }
    fetchKeys();
  }, []);

  useEffect(() => {
    const filtered = PROVIDERS.filter((provider) => {
      const providerName =
        typeof provider.name === "function" ? provider.name(t) : provider.name;
      return providerName.toLowerCase().includes(searchQuery.toLowerCase());
    });
    setFilteredProviders(filtered);
  }, [searchQuery, selectedProvider, t]);

  const selectedProviderObject = PROVIDERS.find(
    (provider) => provider.value === selectedProvider
  ) as Provider;

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        {loading ? (
          <div className="relative rounded-md w-full h-full overflow-y-scroll">
            <div className="w-full h-full flex justify-center items-center">
              <PreLoader />
            </div>
          </div>
        ) : (
          <div className="relative rounded-md w-full h-full overflow-y-scroll">
            <form onSubmit={handleSubmit} className="flex w-full">
              <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4">
                <div className="w-full flex flex-row items-center gap-3.5 gap-y-1 pb-2">
                  <Link to={paths.home()}>
                    <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
                  </Link>
                  <div className="flex flex-col">
                    <div className="flex gap-x-4 items-center">
                      <p className="text-lg leading-6 font-bold text-foreground">
                        {t("transcription.title")}
                      </p>
                    </div>
                    <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                      {t("transcription.description")}
                    </p>
                  </div>
                </div>
                <div className="w-full justify-end flex absolute right-5">
                  {hasChanges && (
                    <Button type="submit" className="mr-0 -mb-14">
                      {saving ? "Saving..." : "Save changes"}
                    </Button>
                  )}
                </div>
                <div className="text-base font-bold text-foreground mt-3 pt-3 border-top">
                  {t("transcription.provider")}
                </div>
                <div className="relative">
                  {searchMenuOpen && (
                    <div
                      className="fixed top-0 left-0 w-full h-full bg-black bg-opacity-70 backdrop-blur-sm z-10"
                      onClick={(): void => setSearchMenuOpen(false)}
                    />
                  )}
                  {searchMenuOpen ? (
                    <div className="absolute top-0 left-0 w-full max-w-[640px] max-h-[310px] overflow-auto white-scrollbar min-h-[64px] sidebar-block rounded-lg flex flex-col justify-between cursor-pointer z-20">
                      <div className="w-full flex flex-col gap-y-1">
                        <div className="flex items-center sticky top-0 border-b border-[#9CA3AF] mx-4 modal-search-block">
                          <MagnifyingGlass
                            size={20}
                            weight="bold"
                            className="absolute left-4 z-30 text-foreground -ml-4 my-2"
                          />
                          <input
                            type="text"
                            name="provider-search"
                            autoComplete="off"
                            placeholder={t("transcription.search-audio")}
                            className="-ml-4 my-2 bg-transparent z-20 pl-12 h-[38px] w-full px-4 py-1 text-sm outline-none   text-foreground  placeholder:font-medium"
                            onChange={(e: ChangeEvent<HTMLInputElement>) =>
                              setSearchQuery(e.target.value)
                            }
                            ref={searchInputRef}
                            onKeyDown={(e: KeyboardEvent<HTMLInputElement>) => {
                              if (e.key === "Enter") e.preventDefault();
                            }}
                          />
                          <X
                            size={20}
                            weight="bold"
                            className="cursor-pointer text-foreground hover:text-[#9CA3AF]"
                            onClick={handleXButton}
                          />
                        </div>
                        <div className="flex-1 pl-4 pr-2 flex flex-col gap-y-1 overflow-y-auto white-scrollbar pb-4">
                          {filteredProviders.map((provider) => (
                            <LLMItem
                              key={provider.value}
                              name={
                                typeof provider.name === "function"
                                  ? provider.name(t)
                                  : provider.name
                              }
                              value={provider.value}
                              logo={provider.logo}
                              description={
                                typeof provider.description === "function"
                                  ? provider.description(t)
                                  : provider.description
                              }
                              checked={selectedProvider === provider.value}
                              onClick={(): void =>
                                updateProviderChoice(provider.value)
                              }
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <Button
                      className="w-full max-w-[640px] h-[64px] p-[14px] justify-between cursor-pointer primary-o-btn duration-300"
                      onClick={(): void => setSearchMenuOpen(true)}
                    >
                      <div className="flex gap-x-4 items-center">
                        <img
                          src={selectedProviderObject?.logo || ""}
                          alt={`${typeof selectedProviderObject?.name === "function" ? selectedProviderObject.name(t) : "Provider"} logo`}
                          className="w-10 h-10 rounded-md"
                        />
                        <div className="flex flex-col text-left">
                          <div className="text-sm font-semibold text-foreground">
                            {selectedProviderObject &&
                            typeof selectedProviderObject.name === "function"
                              ? selectedProviderObject.name(t)
                              : ""}
                          </div>
                          <div className="mt-1 text-xs text-foreground">
                            {selectedProviderObject &&
                            typeof selectedProviderObject.description ===
                              "function"
                              ? selectedProviderObject.description(t)
                              : ""}
                          </div>
                        </div>
                      </div>
                      <CaretUpDown
                        size={24}
                        weight="bold"
                        className="text-foreground"
                      />
                    </Button>
                  )}
                </div>
                <div
                  onChange={(): void => setHasChanges(true)}
                  className="mt-4 flex flex-col gap-y-1"
                >
                  {selectedProvider &&
                    PROVIDERS.find(
                      (provider) => provider.value === selectedProvider
                    )?.options(settings)}
                </div>
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  );
}
