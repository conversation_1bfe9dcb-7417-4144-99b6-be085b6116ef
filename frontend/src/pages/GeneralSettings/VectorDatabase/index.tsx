import React, { useState, useEffect, useRef, useMemo } from "react";
import Sidebar from "@/components/SettingsSidebar";
import System from "@/models/system";
import paths from "@/utils/paths";
import showToast from "@/utils/toast";
import Chroma<PERSON>ogo from "@/media/vectordbs/chroma.png";
import <PERSON>cone<PERSON>ogo from "@/media/vectordbs/pinecone.png";
import LanceDb<PERSON>ogo from "@/media/vectordbs/lancedb.png";
import WeaviateLogo from "@/media/vectordbs/weaviate.png";
import QDrantLogo from "@/media/vectordbs/qdrant.png";
import MilvusLogo from "@/media/vectordbs/milvus.png";
import ZillizLogo from "@/media/vectordbs/zilliz.png";
import AstraDBLogo from "@/media/vectordbs/astraDB.png";
import PreLoader from "@/components/Preloader";
import ChangeWarningModal from "@/components/ChangeWarning";
import {
  ArrowLeft,
  CaretUpDown,
  MagnifyingGlass,
  X,
} from "@phosphor-icons/react";
import LanceDBOptions from "@/components/VectorDBSelection/LanceDBOptions";
import ChromaDBOptions from "@/components/VectorDBSelection/ChromaDBOptions";
import PineconeDBOptions from "@/components/VectorDBSelection/PineconeDBOptions";
import QDrantDBOptions from "@/components/VectorDBSelection/QDrantDBOptions";
import WeaviateDBOptions from "@/components/VectorDBSelection/WeaviateDBOptions";
import VectorDBItem from "@/components/VectorDBSelection/VectorDBItem";
import MilvusDBOptions from "@/components/VectorDBSelection/MilvusDBOptions";
import ZillizCloudOptions from "@/components/VectorDBSelection/ZillizCloudOptions";
import { useModal } from "@/hooks/useModal";
import AstraDBOptions from "@/components/VectorDBSelection/AstraDBOptions";
import { Button } from "@/components/Button";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import HeaderWorkspace from "@/components/HeaderWorkspace";

interface VectorDBSettings {
  VECTOR_DB?: string;
  HasExistingEmbeddings?: boolean;
  [key: string]: any;
}

interface VectorDBOption {
  name: string;
  value: string;
  logo: string;
  options: JSX.Element;
  description: string;
}

export default function GeneralVectorDatabase() {
  const [saving, setSaving] = useState<boolean>(false);
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const [hasEmbeddings, setHasEmbeddings] = useState<boolean>(false);
  const [settings, setSettings] = useState<VectorDBSettings>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [filteredVDBs, setFilteredVDBs] = useState<VectorDBOption[]>([]);
  const [selectedVDB, setSelectedVDB] = useState<string | undefined>(undefined);
  const [searchMenuOpen, setSearchMenuOpen] = useState<boolean>(false);
  const searchInputRef = useRef<HTMLInputElement | null>(null);
  const { isOpen, openModal, closeModal } = useModal();
  const { t } = useTranslation();

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (selectedVDB !== settings?.VECTOR_DB && hasChanges && hasEmbeddings) {
      openModal();
    } else {
      await handleSaveSettings();
    }
  };

  const handleSaveSettings = async () => {
    setSaving(true);
    const form = document.getElementById(
      "vectordb-form"
    ) as HTMLFormElement | null;
    if (!form) return;
    const settingsData: VectorDBSettings = {};
    const formData = new FormData(form);
    settingsData.VECTOR_DB = selectedVDB;
    for (const [key, value] of formData.entries())
      settingsData[key as string] = value;

    const { error } = await System.updateSystem(settingsData);
    if (error) {
      showToast(t("toast.settings.vector-db-failed", { error }), "error");
      setHasChanges(true);
    } else {
      showToast(t("show-toast.vector-saved"), "success");
      setHasChanges(false);
    }
    setSaving(false);
    closeModal();
  };

  const updateVectorChoice = (selection: string) => {
    setSearchQuery("");
    setSelectedVDB(selection);
    setSearchMenuOpen(false);
    setHasChanges(true);
  };

  const handleXButton = () => {
    if (searchQuery.length > 0) {
      setSearchQuery("");
      if (searchInputRef.current) searchInputRef.current.value = "";
    } else {
      setSearchMenuOpen(!searchMenuOpen);
    }
  };

  const VECTOR_DBS = useMemo(
    () => [
      {
        name: "LanceDB",
        value: "lancedb",
        logo: LanceDbLogo,
        options: <LanceDBOptions />,
        description: t("vectordb.lancedb"),
      },
      {
        name: "Chroma",
        value: "chroma",
        logo: ChromaLogo,
        options: <ChromaDBOptions settings={settings as any} />,
        description: t("vectordb.chroma"),
      },
      {
        name: "Pinecone",
        value: "pinecone",
        logo: PineconeLogo,
        options: <PineconeDBOptions settings={settings as any} />,
        description: t("vectordb.pinecone"),
      },
      {
        name: "Zilliz Cloud",
        value: "zilliz",
        logo: ZillizLogo,
        options: <ZillizCloudOptions settings={settings as any} />,
        description: t("vectordb.zilliz"),
      },
      {
        name: "QDrant",
        value: "qdrant",
        logo: QDrantLogo,
        options: <QDrantDBOptions settings={settings as any} />,
        description: t("vectordb.qdrant"),
      },
      {
        name: "Weaviate",
        value: "weaviate",
        logo: WeaviateLogo,
        options: <WeaviateDBOptions settings={settings as any} />,
        description: t("vectordb.weaviate"),
      },
      {
        name: "Milvus",
        value: "milvus",
        logo: MilvusLogo,
        options: <MilvusDBOptions settings={settings as any} />,
        description: t("vectordb.milvus"),
      },
      {
        name: "AstraDB",
        value: "astra",
        logo: AstraDBLogo,
        options: <AstraDBOptions settings={settings as any} />,
        description: t("vectordb.astra"),
      },
    ],
    [settings, t]
  );

  useEffect(() => {
    async function fetchKeys() {
      const _settings = await System.keys();
      if (_settings) {
        const { VECTOR_DB, ...rest } = _settings;
        const safeSettings = {
          ...rest,
          ...(typeof VECTOR_DB === "string" ? { VECTOR_DB } : {}),
        };
        setSettings(safeSettings);
      } else {
        setSettings({});
      }
      setSelectedVDB(_settings?.VECTOR_DB || "lancedb");
      setHasEmbeddings(_settings?.HasExistingEmbeddings || false);
      setLoading(false);
    }
    fetchKeys();
  }, []);

  useEffect(() => {
    const filtered = VECTOR_DBS.filter((vdb) =>
      vdb.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredVDBs(filtered);
  }, [searchQuery, selectedVDB, VECTOR_DBS]);

  const selectedVDBObject = VECTOR_DBS.find((vdb) => vdb.value === selectedVDB);

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        {loading ? (
          <div className="relative rounded-md w-full h-full overflow-y-scroll">
            <div className="w-full h-full flex justify-center items-center">
              <PreLoader />
            </div>
          </div>
        ) : (
          <div className="relative rounded-md w-full h-full overflow-y-scroll">
            <form
              id="vectordb-form"
              onSubmit={handleSubmit}
              className="flex w-full"
            >
              <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4">
                <div className="w-full flex flex-row items-start gap-3.5 gap-y-1 pb-3 border-bottom">
                  <Link to={paths.home()}>
                    <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
                  </Link>
                  <div className="flex flex-col">
                    <div className="flex gap-x-4 items-center">
                      <p className="text-lg leading-6 font-bold text-foreground">
                        {t("vector.title")}
                      </p>
                    </div>
                    <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                      {t("vector.description")}
                    </p>
                  </div>
                </div>
                <div className="w-full justify-end flex absolute right-5">
                  {hasChanges && (
                    <Button type="submit" className="mr-0 -mb-14">
                      {saving ? t("common.saving") : t("common.save")}
                    </Button>
                  )}
                </div>
                <div className="text-base font-bold text-foreground pt-3 pb-2">
                  {t("vector.provider.title")}
                </div>
                <div className="relative">
                  {searchMenuOpen && (
                    <div
                      className="fixed top-0 left-0 w-full h-full bg-black bg-opacity-70 backdrop-blur-sm z-10"
                      onClick={() => setSearchMenuOpen(false)}
                    />
                  )}
                  {searchMenuOpen ? (
                    <div className="absolute top-0 left-0 w-full max-w-[640px] max-h-[310px] overflow-auto white-scrollbar min-h-[64px] sidebar-block rounded-md flex flex-col justify-between cursor-pointer z-20">
                      <div className="w-full flex flex-col gap-y-1">
                        <div className="flex items-center sticky top-0 border-b border-[#9CA3AF] mx-4 modal-search-block">
                          <MagnifyingGlass
                            size={20}
                            weight="bold"
                            className="absolute left-4 z-30 text-foreground -ml-4 my-2"
                          />
                          <input
                            type="text"
                            name="vdb-search"
                            autoComplete="off"
                            placeholder={t("vector.provider.search-db")}
                            className="-ml-4 my-2 bg-transparent z-20 pl-12 h-[38px] w-full px-4 py-1 text-sm outline-none   text-foreground  placeholder:font-medium"
                            onChange={(e) => setSearchQuery(e.target.value)}
                            ref={searchInputRef}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") e.preventDefault();
                            }}
                          />
                          <Button
                            className="modal-close-button"
                            onClick={handleXButton}
                            variant="ghost"
                          >
                            <X
                              size={20}
                              weight="bold"
                              className="cursor-pointer text-foreground hover:text-red-700"
                            />
                          </Button>
                        </div>
                        <div className="flex-1 pl-4 pr-2 flex flex-col gap-y-1 overflow-y-auto white-scrollbar pb-4">
                          {filteredVDBs.map((vdb) => (
                            <VectorDBItem
                              key={vdb.name}
                              name={vdb.name}
                              value={vdb.value}
                              image={vdb.logo}
                              description={vdb.description}
                              checked={selectedVDB === vdb.value}
                              onClick={() => updateVectorChoice(vdb.value)}
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <Button
                      type="button"
                      onClick={() => setSearchMenuOpen(!searchMenuOpen)}
                      className="w-full max-w-[640px] h-[64px] sidebar-block rounded-md flex items-center justify-between p-[1rem] cursor-pointer hover:bg-opacity-80 border border-input"
                    >
                      <div className="flex gap-x-4 items-center">
                        <img
                          src={selectedVDBObject?.logo}
                          alt={`${selectedVDBObject?.name} logo`}
                          className="w-10 h-10 rounded-md"
                        />
                        <div className="flex flex-col text-left">
                          <div className="text-sm font-semibold text-foreground">
                            {selectedVDBObject?.name}
                          </div>
                          <div className="mt-1 text-xs text-description">
                            {selectedVDBObject?.description}
                          </div>
                        </div>
                      </div>
                      <CaretUpDown className="ml-2 text-foreground" size={24} />
                    </Button>
                  )}
                </div>
                <div
                  onChange={() => setHasChanges(true)}
                  className="mt-1 flex flex-col gap-y-1"
                >
                  {selectedVDB &&
                    VECTOR_DBS.find((vdb) => vdb.value === selectedVDB)
                      ?.options}
                </div>
              </div>
            </form>
          </div>
        )}
        <ChangeWarningModal
          warningText={t("vector.warning.switch-db")}
          onClose={closeModal}
          onConfirm={handleSaveSettings}
          isOpen={isOpen}
        />
      </div>
    </div>
  );
}
