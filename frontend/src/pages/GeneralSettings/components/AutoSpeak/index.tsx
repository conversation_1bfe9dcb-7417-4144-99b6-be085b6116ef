import React, { useState, useEffect } from "react";
import Appearance from "@/models/appearance";
import { useTranslation } from "react-i18next";
import Toggle from "@/components/ui/Toggle";

export default function AutoSpeak(): React.ReactElement {
  const [saving, setSaving] = useState<boolean>(false);
  const [autoPlayAssistantTtsResponse, setAutoPlayAssistantTtsResponse] =
    useState<boolean>(false);
  const { t } = useTranslation();

  const handleChange = async (
    e: React.ChangeEvent<HTMLInputElement>
  ): Promise<void> => {
    const newValue = e.target.checked;
    setAutoPlayAssistantTtsResponse(newValue);
    setSaving(true);
    try {
      await Appearance.updateSettings({
        autoPlayAssistantTtsResponse: newValue,
      });
    } catch {
      // Failed to update appearance settings
      setAutoPlayAssistantTtsResponse(!newValue);
    }
    setSaving(false);
  };

  useEffect(() => {
    function fetchSettings(): void {
      const settings = Appearance.getSettings();
      setAutoPlayAssistantTtsResponse(
        settings.autoPlayAssistantTtsResponse ?? false
      );
    }
    fetchSettings();
  }, []);

  return (
    <div className="flex flex-col gap-y-0.5 my-4">
      <h2 className="text-sm leading-6 font-semibold text-foreground">
        {t("chat-ui-settings.auto_speak.title")}
      </h2>
      <p className="text-xs text-foreground text-opacity-60">
        {t("chat-ui-settings.auto_speak.description")}
      </p>
      <div className="flex items-center gap-x-4">
        <Toggle
          id="auto_speak"
          name="auto_speak"
          checked={autoPlayAssistantTtsResponse}
          onChange={handleChange}
          disabled={saving}
        />
      </div>
    </div>
  );
}
