import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import System from "@/models/system";
import showToast from "@/utils/toast";
import SettingSlider from "./SettingSlider";

interface SystemSettings {
  scroll_bottom_threshold?: number;
  scroll_streaming_disable_threshold?: number;
  scroll_auto_scroll_threshold?: number;
  [key: string]: any;
}

export default function ScrollThresholds(): React.ReactElement {
  const { t } = useTranslation();
  const [saving, setSaving] = useState<boolean>(false);
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [scrollBottomThreshold, setScrollBottomThreshold] =
    useState<number>(10);
  const [scrollStreamingDisableThreshold, setScrollStreamingDisableThreshold] =
    useState<number>(50);
  const [scrollAutoScrollThreshold, setScrollAutoScrollThreshold] =
    useState<number>(30);

  useEffect(() => {
    async function fetchSettings(): Promise<void> {
      try {
        const _settings = await System.keys();
        setSettings(_settings);
        setScrollBottomThreshold(_settings?.scroll_bottom_threshold || 10);
        setScrollStreamingDisableThreshold(
          _settings?.scroll_streaming_disable_threshold || 50
        );
        setScrollAutoScrollThreshold(
          _settings?.scroll_auto_scroll_threshold || 30
        );
      } catch {
        // Failed to fetch settings
        showToast(
          t(
            "scroll-threshold-settings.fetch-error",
            "Failed to load scroll settings."
          ),
          "error"
        );
      }
    }
    fetchSettings();
  }, [t]);

  const handleSubmit = async (
    e: React.FormEvent<HTMLFormElement>
  ): Promise<void> => {
    e.preventDefault();
    setSaving(true);
    try {
      await System.updateSystem({
        scroll_bottom_threshold: scrollBottomThreshold,
        scroll_streaming_disable_threshold: scrollStreamingDisableThreshold,
        scroll_auto_scroll_threshold: scrollAutoScrollThreshold,
      });
      showToast(t("scroll-threshold-settings.saved"), "success");
    } catch {
      showToast(t("scroll-threshold-settings.error"), "error");
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="w-full flex flex-col gap-y-1 py-6 border-white light:border-theme-sidebar-border border-b-2 border-opacity-10">
      <div className="flex flex-col gap-y-1">
        <h2 className="text-base leading-6 font-bold text-foreground">
          {t("scroll-threshold-settings.title")}
        </h2>
        <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
          {t("scroll-threshold-settings.description")}
        </p>
      </div>
      {settings && (
        <form onSubmit={handleSubmit} className="flex flex-col gap-y-4">
          <SettingSlider
            title="scroll-threshold-settings.bottom-threshold.title"
            description="scroll-threshold-settings.bottom-threshold.description"
            value={scrollBottomThreshold}
            min={0}
            max={1000}
            step={5}
            onChange={(e) => setScrollBottomThreshold(Number(e.target.value))}
          />
          <SettingSlider
            title="scroll-threshold-settings.streaming-disable-threshold.title"
            description="scroll-threshold-settings.streaming-disable-threshold.description"
            value={scrollStreamingDisableThreshold}
            min={0}
            max={1000}
            step={5}
            onChange={(e) =>
              setScrollStreamingDisableThreshold(Number(e.target.value))
            }
          />
          <SettingSlider
            title="scroll-threshold-settings.auto-scroll-threshold.title"
            description="scroll-threshold-settings.auto-scroll-threshold.description"
            value={scrollAutoScrollThreshold}
            min={0}
            max={1000}
            step={5}
            onChange={(e) =>
              setScrollAutoScrollThreshold(Number(e.target.value))
            }
          />
          <div className="flex justify-start">
            <button
              type="submit"
              disabled={saving}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary"
            >
              {saving
                ? t("scroll-threshold-settings.saving")
                : t("scroll-threshold-settings.save-changes")}
            </button>
          </div>
        </form>
      )}
    </div>
  );
}
