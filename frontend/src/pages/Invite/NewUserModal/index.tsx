import { useState } from "react";
import Invite from "@/models/invite";
import paths from "@/utils/paths";
import { useParams } from "react-router-dom";
import { AUTH_TOKEN, AUTH_USER } from "@/utils/constants";
import System from "@/models/system";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/Button";
import logo from "@/media/logo/ISTLogo.png";

export default function NewUserModal() {
  const { code } = useParams<{ code: string }>();
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const { t } = useTranslation();

  const handleCreate = async (e: React.FormEvent<HTMLFormElement>) => {
    setError(null);
    setLoading(true);
    e.preventDefault();
    const data: Record<string, FormDataEntryValue> = {};
    const form = new FormData(e.currentTarget);
    for (const [key, value] of form.entries()) {
      data[key] = value;
    }

    if (!code) {
      setError("Invalid invitation code");
      setLoading(false);
      return;
    }

    const { success, error } = await Invite.acceptInvite(code, data);
    if (success) {
      const { valid, user, token, message } = await System.requestToken(data);
      if (valid && !!token && !!user) {
        window.localStorage.setItem(AUTH_USER, JSON.stringify(user));
        window.localStorage.setItem(AUTH_TOKEN, token);
        window.location.href = paths.home();
      } else {
        setError(message || "Authentication failed");
      }
      setLoading(false);
      return;
    }
    setError(error || "Failed to accept invitation");
    setLoading(false);
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background">
      <div
        className={`bg-card flex flex-col items-center rounded-lg shadow-lg max-w-lg w-full mx-4 pt-6 transition-opacity duration-300 ${
          loading ? "opacity-50" : "opacity-100"
        }`}
      >
        <img src={logo} alt="IST Legal Logo" className="w-40" />
        <div className="p-6">
          <h2 className="text-xl font-semibold text-foreground mb-6 text-center">
            {t("invitation.newUser.title")}
          </h2>
          <form onSubmit={handleCreate} id="invite-form">
            <div className="space-y-6">
              <div>
                <label
                  htmlFor="email-input"
                  className="block mb-2 text-sm font-medium text-foreground"
                >
                  {t("common.placeholder.email")}
                </label>
                <input
                  id="email-input"
                  name="email"
                  type="email"
                  placeholder={t("common.placeholder.email")}
                  minLength={2}
                  required={true}
                  autoComplete="email"
                  aria-required="true"
                  aria-invalid={error ? "true" : "false"}
                  aria-describedby={error ? "form-error" : undefined}
                  className="dark-input-mdl w-full text-foreground text-sm rounded-lg block p-3"
                />
              </div>
              <div>
                <label
                  htmlFor="password-input"
                  className="block mb-2 text-sm font-medium text-foreground"
                >
                  {t("invitation.newUser.passwordLabel")}
                </label>
                <input
                  id="password-input"
                  name="password"
                  type="password"
                  placeholder={t("common.placeholder.password")}
                  minLength={8}
                  required={true}
                  autoComplete="new-password"
                  aria-required="true"
                  aria-invalid={error ? "true" : "false"}
                  aria-describedby={error ? "form-error" : undefined}
                  className="dark-input-mdl w-full text-foreground text-sm rounded-lg block p-3"
                />
              </div>

              {error && (
                <p
                  id="form-error"
                  className="text-red-500 text-sm"
                  role="alert"
                >
                  <b>Error:</b> {error}
                </p>
              )}
              <p className="text-xs md:text-sm alert info-alert">
                {t("invitation.newUser.description")}
              </p>
            </div>
            <div className="mt-6 flex justify-center">
              <Button
                type="submit"
                form="invite-form"
                disabled={loading}
                aria-label={
                  loading ? t("common.loading") : t("invitation.accept-button")
                }
              >
                {loading ? t("common.loading") : t("invitation.accept-button")}
              </Button>
            </div>
          </form>
        </div>
      </div>
      <p className="text-[12px] md:text-sm absolute bottom-3 text-foreground">
        &copy; {new Date().getFullYear()} IST Legal
      </p>
    </div>
  );
}
