import { render, screen } from "@testing-library/react";
import { MemoryRouter } from "react-router-dom";
import Login from "../index";
import { usePasswordModal } from "@/components/Modals/Password";
import paths from "@/utils/paths";

// Mock dependencies
jest.mock("@/components/Modals/Password", () => ({
  __esModule: true,
  default: () => {
    const React = require("react");
    return React.createElement(
      "div",
      { "data-testid": "password-modal" },
      "Password Modal"
    );
  },
  usePasswordModal: jest.fn(),
}));

jest.mock("@/components/Preloader", () => ({
  FullScreenLoader: () => {
    const React = require("react");
    return React.createElement(
      "div",
      { "data-testid": "loader" },
      "Loading..."
    );
  },
}));

// Create a mock function that can be controlled per test
const mockQueryGet = jest.fn();

jest.mock("@/hooks/useQuery", () => ({
  __esModule: true,
  default: () => ({
    get: mockQueryGet,
  }),
}));

const mockNavigate = jest.fn();
jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  Navigate: ({ to }: { to: string }) => {
    mockNavigate(to);
    return null;
  },
}));

describe("Login Page", () => {
  const mockUsePasswordModal = usePasswordModal as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    // Default mock behavior
    mockQueryGet.mockImplementation((param: string) =>
      param === "nt" ? "true" : null
    );
  });

  describe("Loading state", () => {
    it("should show loader when loading is true", () => {
      mockUsePasswordModal.mockReturnValue({
        loading: true,
        requiresAuth: true,
        mode: "single",
      });

      render(
        <MemoryRouter>
          <Login />
        </MemoryRouter>
      );

      expect(screen.getByTestId("loader")).toBeInTheDocument();
      expect(screen.getByText("Loading...")).toBeInTheDocument();
    });
  });

  describe("Authentication states", () => {
    it("should redirect to home when requiresAuth is false", () => {
      mockUsePasswordModal.mockReturnValue({
        loading: false,
        requiresAuth: false,
        mode: "single",
      });

      render(
        <MemoryRouter>
          <Login />
        </MemoryRouter>
      );

      expect(mockNavigate).toHaveBeenCalledWith(paths.home());
    });

    it("should show password modal when authentication is required", () => {
      mockUsePasswordModal.mockReturnValue({
        loading: false,
        requiresAuth: true,
        mode: "multi",
      });

      render(
        <MemoryRouter>
          <Login />
        </MemoryRouter>
      );

      expect(screen.getByTestId("password-modal")).toBeInTheDocument();
      expect(screen.getByText("Password Modal")).toBeInTheDocument();
    });
  });

  describe("Query parameter handling", () => {
    it("should pass true to usePasswordModal when nt query param is present", () => {
      mockQueryGet.mockImplementation((param: string) =>
        param === "nt" ? "true" : null
      );

      mockUsePasswordModal.mockReturnValue({
        loading: false,
        requiresAuth: true,
        mode: "single",
      });

      render(
        <MemoryRouter initialEntries={["/login?nt=true"]}>
          <Login />
        </MemoryRouter>
      );

      expect(mockUsePasswordModal).toHaveBeenCalledWith(true);
    });

    it("should pass false to usePasswordModal when nt query param is not present", () => {
      mockQueryGet.mockImplementation(() => null);

      mockUsePasswordModal.mockReturnValue({
        loading: false,
        requiresAuth: true,
        mode: "single",
      });

      render(
        <MemoryRouter initialEntries={["/login"]}>
          <Login />
        </MemoryRouter>
      );

      expect(mockUsePasswordModal).toHaveBeenCalledWith(false);
    });
  });

  describe("Modal modes", () => {
    it("should render password modal with single mode", () => {
      mockUsePasswordModal.mockReturnValue({
        loading: false,
        requiresAuth: true,
        mode: "single",
      });

      render(
        <MemoryRouter>
          <Login />
        </MemoryRouter>
      );

      expect(screen.getByTestId("password-modal")).toBeInTheDocument();
    });

    it("should render password modal with multi mode", () => {
      mockUsePasswordModal.mockReturnValue({
        loading: false,
        requiresAuth: true,
        mode: "multi",
      });

      render(
        <MemoryRouter>
          <Login />
        </MemoryRouter>
      );

      expect(screen.getByTestId("password-modal")).toBeInTheDocument();
    });
  });

  describe("Integration scenarios", () => {
    it("should handle complete authentication flow", () => {
      // Initially loading
      mockUsePasswordModal.mockReturnValue({
        loading: true,
        requiresAuth: true,
        mode: "single",
      });

      const { rerender } = render(
        <MemoryRouter>
          <Login />
        </MemoryRouter>
      );

      expect(screen.getByTestId("loader")).toBeInTheDocument();

      // Loading complete, auth required
      mockUsePasswordModal.mockReturnValue({
        loading: false,
        requiresAuth: true,
        mode: "multi",
      });

      rerender(
        <MemoryRouter>
          <Login />
        </MemoryRouter>
      );

      expect(screen.queryByTestId("loader")).not.toBeInTheDocument();
      expect(screen.getByTestId("password-modal")).toBeInTheDocument();

      // Auth successful, redirect
      mockUsePasswordModal.mockReturnValue({
        loading: false,
        requiresAuth: false,
        mode: "multi",
      });

      rerender(
        <MemoryRouter>
          <Login />
        </MemoryRouter>
      );

      expect(mockNavigate).toHaveBeenCalledWith(paths.home());
    });
  });
});
