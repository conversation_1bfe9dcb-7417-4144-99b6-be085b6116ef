// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import "@testing-library/jest-dom";

// Add TextEncoder/TextDecoder polyfill for Jest environment
import { TextEncoder, TextDecoder } from "util";
global.TextEncoder = TextEncoder as any;
global.TextDecoder = TextDecoder as any;

// Mock scrollTo and scrollIntoView for elements
Element.prototype.scrollTo = jest.fn();
Element.prototype.scrollIntoView = jest.fn();

// Mock HTMLElement properties that jsdom doesn't implement
Object.defineProperty(HTMLElement.prototype, "scrollTo", {
  configurable: true,
  value: jest.fn(),
});

// Suppress React 18 act warnings during tests
/* eslint-disable no-console */
const originalError = console.error;
const originalWarn = console.warn;

beforeAll(() => {
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === "string" &&
      args[0].includes("Warning: An update to")
    ) {
      return;
    }
    if (typeof args[0] === "string" && args[0].includes("act(...)")) {
      return;
    }
    originalError.call(console, ...args);
  };

  console.warn = (...args: any[]) => {
    if (
      typeof args[0] === "string" &&
      args[0].includes("Warning: An update to")
    ) {
      return;
    }
    if (typeof args[0] === "string" && args[0].includes("act(...)")) {
      return;
    }
    // Suppress React Router v7 deprecation warnings
    if (
      typeof args[0] === "string" &&
      (args[0].includes("React Router Future Flag Warning") ||
        args[0].includes("v7_startTransition") ||
        args[0].includes("v7_relativeSplatPath"))
    ) {
      return;
    }
    originalWarn.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
  console.warn = originalWarn;

  // Clean up any remaining timers
  jest.clearAllTimers();
  jest.useRealTimers();

  // Clear all mocks
  jest.clearAllMocks();
  jest.restoreAllMocks();

  // Force cleanup of any pending operations
  if (global.gc) {
    global.gc();
  }
});
/* eslint-enable no-console */

// Mock constants.js to handle import.meta.env
jest.mock("@/utils/constants", () => ({
  API_BASE: "/api",
  AUTH_USER: "istlegal_user",
  AUTH_TOKEN: "istlegal_authToken",
  AUTH_TIMESTAMP: "istlegal_authTimestamp",
  COMPLETE_QUESTIONNAIRE: "istlegal_completed_questionnaire",
  APPEARANCE_SETTINGS: "istlegal_appearance_settings",
  USER_BACKGROUND_COLOR: "bg-historical-msg-user",
  AI_BACKGROUND_COLOR: "bg-historical-msg-system",
  MODULE_LEGAL_QA: "legal-qa",
  MODULE_DOCUMENT_DRAFTING: "document-drafting",
  OLLAMA_COMMON_URLS: [
    "http://127.0.0.1:11434",
    "http://host.docker.internal:11434",
    "http://**********:11434",
  ],
  LMSTUDIO_COMMON_URLS: [
    "http://localhost:1234/v1",
    "http://127.0.0.1:1234/v1",
    "http://host.docker.internal:1234/v1",
    "http://**********:1234/v1",
  ],
  KOBOLDCPP_COMMON_URLS: [
    "http://127.0.0.1:5000/v1",
    "http://localhost:5000/v1",
    "http://host.docker.internal:5000/v1",
    "http://**********:5000/v1",
  ],
  LOCALAI_COMMON_URLS: [
    "http://127.0.0.1:8080/v1",
    "http://localhost:8080/v1",
    "http://host.docker.internal:8080/v1",
    "http://**********:8080/v1",
  ],
  fullApiUrl: jest.fn().mockReturnValue("/api"),
  POPUP_BROWSER_EXTENSION_EVENT: "NEW_BROWSER_EXTENSION_CONNECTION",
  isQura: jest.fn().mockReturnValue(false),
  isCitationEnabled: jest.fn().mockReturnValue(false),
  validateReferenceNumberWithStore: jest.fn().mockReturnValue(true),
}));

// Mock window.matchMedia
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: jest.fn().mockImplementation((query: string) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock localStorage
const localStorageMock = (function () {
  let store: Record<string, string> = {};
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value.toString();
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
  };
})();

Object.defineProperty(window, "localStorage", {
  value: localStorageMock,
});

// Create portal root for Modals
const portalRoot = document.createElement("div");
portalRoot.id = "theme-wrapper";
document.body.appendChild(portalRoot);

// Mock immer middleware for zustand
jest.mock("zustand/middleware/immer", () => ({
  immer: (config: any) => (set: any, get: any, api: any) => {
    return config(
      (fn: any) => {
        if (typeof fn === "function") {
          return set((state: any) => {
            // Create a deep copy of the state
            const newState = JSON.parse(JSON.stringify(state));
            fn(newState);
            return newState;
          });
        } else {
          return set(fn);
        }
      },
      get,
      api
    );
  },
}));
