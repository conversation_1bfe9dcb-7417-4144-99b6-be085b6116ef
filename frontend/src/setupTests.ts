// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import "@testing-library/jest-dom";

// Add TextEncoder/TextDecoder polyfill for Jest environment
import { TextEncoder, TextDecoder } from "util";

// Polyfills for MSW and jest environment
Object.assign(global, {
  TextEncoder,
  TextDecoder,
  Response: class Response {
    body: any;
    status: number;
    statusText: string;
    headers: Map<string, string>;

    constructor(body: any, init: any = {}) {
      this.body = body;
      this.status = init.status || 200;
      this.statusText = init.statusText || "OK";
      this.headers = new Map(Object.entries(init.headers || {}));
    }
    json() {
      return Promise.resolve(JSON.parse(this.body));
    }
    text() {
      return Promise.resolve(this.body);
    }
  },
  Request: class Request {
    url: string;
    method: string;
    headers: Map<string, string>;

    constructor(input: any, init: any = {}) {
      this.url = input;
      this.method = init.method || "GET";
      this.headers = new Map(Object.entries(init.headers || {}));
    }
  },
  Headers: Map,
  fetch: jest.fn(),
  BroadcastChannel: class BroadcastChannel {
    name: string;

    constructor(name: string) {
      this.name = name;
    }
    postMessage() {}
    close() {}
    addEventListener() {}
    removeEventListener() {}
  },
  MessageChannel: class MessageChannel {
    port1 = { postMessage: jest.fn(), addEventListener: jest.fn() };
    port2 = { postMessage: jest.fn(), addEventListener: jest.fn() };
  },
});

// Mock scrollTo and scrollIntoView for elements
Element.prototype.scrollTo = jest.fn();
Element.prototype.scrollIntoView = jest.fn();

// Mock HTMLElement properties that jsdom doesn't implement
Object.defineProperty(HTMLElement.prototype, "scrollTo", {
  configurable: true,
  value: jest.fn(),
});

// Import shared console warning suppression utility
import { setupConsoleWarningSuppressions } from "./utils/testHelpers/consoleWarnings";

// Set up console warning suppression
let consoleSuppressions: { restore: () => void } | null = null;

beforeAll(() => {
  consoleSuppressions = setupConsoleWarningSuppressions();
});

afterAll(() => {
  if (consoleSuppressions) {
    consoleSuppressions.restore();
  }

  // Clean up any remaining timers
  jest.clearAllTimers();
  jest.useRealTimers();

  // Clear all mocks
  jest.clearAllMocks();
  jest.restoreAllMocks();

  // Force cleanup of any pending operations
  if (global.gc) {
    global.gc();
  }
});

// Add memory cleanup between test suites
afterEach(() => {
  // Clear all timers after each test
  jest.clearAllTimers();
  jest.useRealTimers();

  // Clear all mocks after each test
  jest.clearAllMocks();
  jest.restoreAllMocks();

  // Remove all event listeners
  const eventTarget = window as EventTarget;
  if ((eventTarget as any)._events) {
    (eventTarget as any)._events = {};
  }

  // Clear all window event listeners
  const windowListeners = (window as any).eventListeners || {};
  Object.keys(windowListeners).forEach((event) => {
    delete windowListeners[event];
  });

  // Clear localStorage
  localStorageMock.clear();

  // Clean up DOM
  document.body.innerHTML = "";
  document.head.innerHTML = "";

  // Re-create portal root for next test
  const portalRoot = document.createElement("div");
  portalRoot.id = "theme-wrapper";
  document.body.appendChild(portalRoot);

  // Clear module cache for problematic modules
  jest.resetModules();

  // Clean up React Fiber internals if they exist
  const root = document.getElementById("root");
  if (root && (root as any)._reactRootContainer) {
    (root as any)._reactRootContainer = null;
  }

  // Force garbage collection if available
  if (global.gc) {
    global.gc();
  }
});

// Mock constants.js to handle import.meta.env
jest.mock("@/utils/constants", () => ({
  API_BASE: "/api",
  AUTH_USER: "istlegal_user",
  AUTH_TOKEN: "istlegal_authToken",
  AUTH_TIMESTAMP: "istlegal_authTimestamp",
  COMPLETE_QUESTIONNAIRE: "istlegal_completed_questionnaire",
  APPEARANCE_SETTINGS: "istlegal_appearance_settings",
  USER_BACKGROUND_COLOR: "bg-historical-msg-user",
  AI_BACKGROUND_COLOR: "bg-historical-msg-system",
  MODULE_LEGAL_QA: "legal-qa",
  MODULE_DOCUMENT_DRAFTING: "document-drafting",
  OLLAMA_COMMON_URLS: [
    "http://127.0.0.1:11434",
    "http://host.docker.internal:11434",
    "http://**********:11434",
  ],
  LMSTUDIO_COMMON_URLS: [
    "http://localhost:1234/v1",
    "http://127.0.0.1:1234/v1",
    "http://host.docker.internal:1234/v1",
    "http://**********:1234/v1",
  ],
  KOBOLDCPP_COMMON_URLS: [
    "http://127.0.0.1:5000/v1",
    "http://localhost:5000/v1",
    "http://host.docker.internal:5000/v1",
    "http://**********:5000/v1",
  ],
  LOCALAI_COMMON_URLS: [
    "http://127.0.0.1:8080/v1",
    "http://localhost:8080/v1",
    "http://host.docker.internal:8080/v1",
    "http://**********:8080/v1",
  ],
  fullApiUrl: jest.fn().mockReturnValue("/api"),
  POPUP_BROWSER_EXTENSION_EVENT: "NEW_BROWSER_EXTENSION_CONNECTION",
  isQura: jest.fn().mockReturnValue(false),
  isCitationEnabled: jest.fn().mockReturnValue(false),
  validateReferenceNumberWithStore: jest.fn().mockReturnValue(true),
}));

// Mock window.matchMedia
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: jest.fn().mockImplementation((query: string) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock localStorage
const localStorageMock = (function () {
  let store: Record<string, string> = {};
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value.toString();
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
  };
})();

Object.defineProperty(window, "localStorage", {
  value: localStorageMock,
});

// Create portal root for Modals
const portalRoot = document.createElement("div");
portalRoot.id = "theme-wrapper";
document.body.appendChild(portalRoot);

// Mock immer middleware for zustand
jest.mock("zustand/middleware/immer", () => ({
  immer: (config: any) => (set: any, get: any, api: any) => {
    return config(
      (fn: any) => {
        if (typeof fn === "function") {
          return set((state: any) => {
            // Create a deep copy of the state
            const newState = JSON.parse(JSON.stringify(state));
            fn(newState);
            return newState;
          });
        } else {
          return set(fn);
        }
      },
      get,
      api
    );
  },
}));
