import {
  useAttachmentStore,
  useThreadAttachments,
  useAddAttachment,
  useRemoveAttachment,
  useClearAttachments,
  useSetAttachmentsForThread,
} from "../attachmentStore";
import { act, renderHook } from "@testing-library/react";

// Mock threadId for tests
const MOCK_THREAD_ID = "thread-123";
const MOCK_THREAD_ID_2 = "thread-456";

describe("attachmentStore", () => {
  beforeEach(() => {
    // Reset the store state completely before each test
    act(() => {
      useAttachmentStore.setState({ attachmentsByThread: {} });
    });

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe("basic state management", () => {
    it("should initialize with empty attachmentsByThread object", () => {
      const { result } = renderHook(() => useAttachmentStore());
      expect(result.current.attachmentsByThread).toEqual({});
    });

    it("should add an attachment to a specific thread", () => {
      const attachment = {
        uid: "1",
        name: "test.pdf",
        type: "application/pdf",
      };
      const { result } = renderHook(() => useAttachmentStore());

      act(() => {
        result.current.addAttachment(MOCK_THREAD_ID, attachment);
      });

      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toEqual([
        attachment,
      ]);
      // Ensure other threads are unaffected
      expect(
        result.current.attachmentsByThread[MOCK_THREAD_ID_2]
      ).toBeUndefined();
    });

    it("should remove an attachment by uid from a specific thread", () => {
      const attachment1 = {
        uid: "1",
        name: "test1.pdf",
        type: "application/pdf",
      };
      const attachment2 = {
        uid: "2",
        name: "test2.pdf",
        type: "application/pdf",
      };
      const { result } = renderHook(() => useAttachmentStore());

      // Add attachments to two different threads
      act(() => {
        result.current.addAttachment(MOCK_THREAD_ID, attachment1);
        result.current.addAttachment(MOCK_THREAD_ID, attachment2);
        result.current.addAttachment(MOCK_THREAD_ID_2, attachment1); // Add to another thread
      });

      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toHaveLength(
        2
      );
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID_2]).toHaveLength(
        1
      );

      // Remove from the first thread
      act(() => {
        result.current.removeAttachment(MOCK_THREAD_ID, attachment1.uid);
      });

      // Verify removal from the correct thread
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toHaveLength(
        1
      );
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID][0]).toEqual(
        attachment2
      );
      // Verify the other thread is unaffected
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID_2]).toHaveLength(
        1
      );
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID_2][0]).toEqual(
        attachment1
      );
    });

    it("should remove the thread key if the last attachment is removed", () => {
      const attachment = {
        uid: "1",
        name: "test.pdf",
        type: "application/pdf",
      };
      const { result } = renderHook(() => useAttachmentStore());

      act(() => {
        result.current.addAttachment(MOCK_THREAD_ID, attachment);
      });
      expect(result.current.attachmentsByThread).toHaveProperty(MOCK_THREAD_ID);

      act(() => {
        result.current.removeAttachment(MOCK_THREAD_ID, attachment.uid);
      });

      expect(result.current.attachmentsByThread).not.toHaveProperty(
        MOCK_THREAD_ID
      );
    });

    it("should clear all attachments for a specific thread", () => {
      const attachment1 = {
        uid: "1",
        name: "test1.pdf",
        type: "application/pdf",
      };
      const attachment2 = {
        uid: "2",
        name: "test2.pdf",
        type: "application/pdf",
      };
      const { result } = renderHook(() => useAttachmentStore());

      // Add attachments to two different threads
      act(() => {
        result.current.addAttachment(MOCK_THREAD_ID, attachment1);
        result.current.addAttachment(MOCK_THREAD_ID, attachment2);
        result.current.addAttachment(MOCK_THREAD_ID_2, attachment1);
      });

      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toHaveLength(
        2
      );
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID_2]).toHaveLength(
        1
      );

      // Clear attachments for the first thread
      act(() => {
        result.current.clearAttachments(MOCK_THREAD_ID);
      });

      // Verify the thread is cleared and the key is removed
      expect(result.current.attachmentsByThread).not.toHaveProperty(
        MOCK_THREAD_ID
      );
      // Verify the other thread is unaffected
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID_2]).toHaveLength(
        1
      );
    });

    it("should set/replace attachments for a specific thread", () => {
      const initialAttachment = {
        uid: "1",
        name: "initial.pdf",
        type: "application/pdf",
      };
      const newAttachments = [
        { uid: "2", name: "new1.png", type: "image/png" },
        { uid: "3", name: "new2.jpg", type: "image/jpeg" },
      ];

      const { result } = renderHook(() => useAttachmentStore());

      // Add initial attachment
      act(() => {
        result.current.addAttachment(MOCK_THREAD_ID, initialAttachment);
      });
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toEqual([
        initialAttachment,
      ]);

      // Set new attachments, replacing the old ones
      act(() => {
        result.current.setAttachmentsForThread(MOCK_THREAD_ID, newAttachments);
      });

      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toEqual(
        newAttachments
      );
    });
  });

  describe("store structure", () => {
    it("should have the correct state structure and actions", () => {
      const store = useAttachmentStore.getState();

      // Verify that the custom event system remnants are gone
      expect((store as any).listeners).toBeUndefined();
      expect((store as any).subscribe).toBeUndefined();
      expect((store as any).notify).toBeUndefined();
      expect((store as any).lastAction).toBeUndefined(); // Check removed properties
      expect((store as any).lastActionData).toBeUndefined();
      expect((store as any).pasteAttachment).toBeUndefined();
      expect((store as any).attachments).toBeUndefined(); // Check old state property removed

      // Verify that the store has the expected structure
      expect(store).toHaveProperty("attachmentsByThread");
      expect(store).toHaveProperty("addAttachment");
      expect(store).toHaveProperty("removeAttachment");
      expect(store).toHaveProperty("clearAttachments");
      expect(store).toHaveProperty("setAttachmentsForThread"); // Check new action
    });
  });

  describe("selector hooks", () => {
    it("should provide attachments for a specific thread via useThreadAttachments hook", () => {
      const attachment = {
        uid: "1",
        name: "test.pdf",
        type: "application/pdf",
      };
      const attachmentOtherThread = {
        uid: "2",
        name: "other.pdf",
        type: "application/pdf",
      };

      act(() => {
        useAttachmentStore.getState().addAttachment(MOCK_THREAD_ID, attachment);
        useAttachmentStore
          .getState()
          .addAttachment(MOCK_THREAD_ID_2, attachmentOtherThread);
      });

      // Test getting attachments for the specific thread
      const { result: threadAttachmentsResult } = renderHook(() =>
        useThreadAttachments(MOCK_THREAD_ID)
      );
      expect(threadAttachmentsResult.current).toEqual([attachment]);

      // Test getting attachments for another thread
      const { result: otherThreadAttachmentsResult } = renderHook(() =>
        useThreadAttachments(MOCK_THREAD_ID_2)
      );
      expect(otherThreadAttachmentsResult.current).toEqual([
        attachmentOtherThread,
      ]);

      // Test getting attachments for a thread with no attachments (should return empty array)
      const { result: emptyThreadResult } = renderHook(() =>
        useThreadAttachments("non-existent-thread")
      );
      expect(emptyThreadResult.current).toEqual([]);
    });

    it("should provide addAttachment via useAddAttachment hook", () => {
      const attachment = {
        uid: "1",
        name: "test.pdf",
        type: "application/pdf",
      };

      const { result: addAttachmentResult } = renderHook(() =>
        useAddAttachment()
      );
      const { result: attachmentsResult } = renderHook(() =>
        useThreadAttachments(MOCK_THREAD_ID)
      );

      act(() => {
        addAttachmentResult.current(MOCK_THREAD_ID, attachment);
      });

      expect(attachmentsResult.current).toEqual([attachment]);
    });

    it("should provide removeAttachment via useRemoveAttachment hook", () => {
      const attachment1 = {
        uid: "1",
        name: "test1.pdf",
        type: "application/pdf",
      };
      const attachment2 = {
        uid: "2",
        name: "test2.pdf",
        type: "application/pdf",
      };

      act(() => {
        useAttachmentStore
          .getState()
          .addAttachment(MOCK_THREAD_ID, attachment1);
        useAttachmentStore
          .getState()
          .addAttachment(MOCK_THREAD_ID, attachment2);
      });

      const { result: removeAttachmentResult } = renderHook(() =>
        useRemoveAttachment()
      );
      const { result: attachmentsResult } = renderHook(() =>
        useThreadAttachments(MOCK_THREAD_ID)
      );

      act(() => {
        removeAttachmentResult.current(MOCK_THREAD_ID, attachment1.uid);
      });

      expect(attachmentsResult.current).toEqual([attachment2]);
    });

    it("should provide clearAttachments via useClearAttachments hook", () => {
      const attachment = {
        uid: "1",
        name: "test.pdf",
        type: "application/pdf",
      };

      act(() => {
        useAttachmentStore.getState().addAttachment(MOCK_THREAD_ID, attachment);
      });

      const { result: clearAttachmentsResult } = renderHook(() =>
        useClearAttachments()
      );
      const { result: attachmentsResult } = renderHook(() =>
        useThreadAttachments(MOCK_THREAD_ID)
      );

      expect(attachmentsResult.current).toHaveLength(1);

      act(() => {
        clearAttachmentsResult.current(MOCK_THREAD_ID);
      });

      expect(attachmentsResult.current).toHaveLength(0);
      // Also check the main store state to ensure the key was removed
      expect(
        useAttachmentStore.getState().attachmentsByThread
      ).not.toHaveProperty(MOCK_THREAD_ID);
    });

    it("should provide setAttachmentsForThread via useSetAttachmentsForThread hook", () => {
      const initialAttachment = {
        uid: "1",
        name: "initial.pdf",
        type: "application/pdf",
      };
      const newAttachments = [
        { uid: "2", name: "new1.png", type: "image/png" },
        { uid: "3", name: "new2.jpg", type: "image/jpeg" },
      ];

      act(() => {
        useAttachmentStore
          .getState()
          .addAttachment(MOCK_THREAD_ID, initialAttachment);
      });

      const { result: setAttachmentsResult } = renderHook(() =>
        useSetAttachmentsForThread()
      );
      const { result: attachmentsResult } = renderHook(() =>
        useThreadAttachments(MOCK_THREAD_ID)
      );

      expect(attachmentsResult.current).toEqual([initialAttachment]);

      act(() => {
        setAttachmentsResult.current(MOCK_THREAD_ID, newAttachments);
      });

      expect(attachmentsResult.current).toEqual(newAttachments);
    });
  });
});
