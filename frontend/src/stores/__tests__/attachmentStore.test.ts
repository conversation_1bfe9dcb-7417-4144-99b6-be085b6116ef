import {
  useAttachmentStore,
  useThreadAttachments,
  useAddAttachment,
  useRemoveAttachment,
  useClearAttachments,
  useSetAttachmentsForThread,
} from "../attachmentStore";
import { act, renderHook } from "@testing-library/react";

// Mock threadId for tests
const MOCK_THREAD_ID = "thread-123";
const MOCK_THREAD_ID_2 = "thread-456";

describe("attachmentStore", () => {
  beforeEach(() => {
    // Reset the store state completely before each test
    act(() => {
      useAttachmentStore.setState({ attachmentsByThread: {} });
    });

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe("basic state management", () => {
    it("should initialize with empty attachmentsByThread object", () => {
      const { result } = renderHook(() => useAttachmentStore());
      expect(result.current.attachmentsByThread).toEqual({});
    });

    it("should add an attachment to a specific thread", () => {
      const attachment = {
        uid: "1",
        name: "test.pdf",
        type: "application/pdf",
      };
      const { result } = renderHook(() => useAttachmentStore());

      act(() => {
        result.current.addAttachment(MOCK_THREAD_ID, attachment);
      });

      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toEqual([
        attachment,
      ]);
      // Ensure other threads are unaffected
      expect(
        result.current.attachmentsByThread[MOCK_THREAD_ID_2]
      ).toBeUndefined();
    });

    it("should remove an attachment by uid from a specific thread", () => {
      const attachment1 = {
        uid: "1",
        name: "test1.pdf",
        type: "application/pdf",
      };
      const attachment2 = {
        uid: "2",
        name: "test2.pdf",
        type: "application/pdf",
      };
      const { result } = renderHook(() => useAttachmentStore());

      // Add attachments to two different threads
      act(() => {
        result.current.addAttachment(MOCK_THREAD_ID, attachment1);
        result.current.addAttachment(MOCK_THREAD_ID, attachment2);
        result.current.addAttachment(MOCK_THREAD_ID_2, attachment1); // Add to another thread
      });

      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toHaveLength(
        2
      );
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID_2]).toHaveLength(
        1
      );

      // Remove from the first thread
      act(() => {
        result.current.removeAttachment(MOCK_THREAD_ID, attachment1.uid);
      });

      // Verify removal from the correct thread
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toHaveLength(
        1
      );
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID][0]).toEqual(
        attachment2
      );
      // Verify the other thread is unaffected
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID_2]).toHaveLength(
        1
      );
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID_2][0]).toEqual(
        attachment1
      );
    });

    it("should remove the thread key if the last attachment is removed", () => {
      const attachment = {
        uid: "1",
        name: "test.pdf",
        type: "application/pdf",
      };
      const { result } = renderHook(() => useAttachmentStore());

      act(() => {
        result.current.addAttachment(MOCK_THREAD_ID, attachment);
      });
      expect(result.current.attachmentsByThread).toHaveProperty(MOCK_THREAD_ID);

      act(() => {
        result.current.removeAttachment(MOCK_THREAD_ID, attachment.uid);
      });

      expect(result.current.attachmentsByThread).not.toHaveProperty(
        MOCK_THREAD_ID
      );
    });

    it("should clear all attachments for a specific thread", () => {
      const attachment1 = {
        uid: "1",
        name: "test1.pdf",
        type: "application/pdf",
      };
      const attachment2 = {
        uid: "2",
        name: "test2.pdf",
        type: "application/pdf",
      };
      const { result } = renderHook(() => useAttachmentStore());

      // Add attachments to two different threads
      act(() => {
        result.current.addAttachment(MOCK_THREAD_ID, attachment1);
        result.current.addAttachment(MOCK_THREAD_ID, attachment2);
        result.current.addAttachment(MOCK_THREAD_ID_2, attachment1);
      });

      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toHaveLength(
        2
      );
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID_2]).toHaveLength(
        1
      );

      // Clear attachments for the first thread
      act(() => {
        result.current.clearAttachments(MOCK_THREAD_ID);
      });

      // Verify the thread is cleared and the key is removed
      expect(result.current.attachmentsByThread).not.toHaveProperty(
        MOCK_THREAD_ID
      );
      // Verify the other thread is unaffected
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID_2]).toHaveLength(
        1
      );
    });

    it("should set/replace attachments for a specific thread", () => {
      const initialAttachment = {
        uid: "1",
        name: "initial.pdf",
        type: "application/pdf",
      };
      const newAttachments = [
        { uid: "2", name: "new1.png", type: "image/png" },
        { uid: "3", name: "new2.jpg", type: "image/jpeg" },
      ];

      const { result } = renderHook(() => useAttachmentStore());

      // Add initial attachment
      act(() => {
        result.current.addAttachment(MOCK_THREAD_ID, initialAttachment);
      });
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toEqual([
        initialAttachment,
      ]);

      // Set new attachments, replacing the old ones
      act(() => {
        result.current.setAttachmentsForThread(MOCK_THREAD_ID, newAttachments);
      });

      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toEqual(
        newAttachments
      );
    });
  });

  describe("store structure", () => {
    it("should have the correct state structure and actions", () => {
      const store = useAttachmentStore.getState();

      // Verify that the custom event system remnants are gone
      expect((store as any).listeners).toBeUndefined();
      expect((store as any).subscribe).toBeUndefined();
      expect((store as any).notify).toBeUndefined();
      expect((store as any).lastAction).toBeUndefined(); // Check removed properties
      expect((store as any).lastActionData).toBeUndefined();
      expect((store as any).pasteAttachment).toBeUndefined();
      expect((store as any).attachments).toBeUndefined(); // Check old state property removed

      // Verify that the store has the expected structure
      expect(store).toHaveProperty("attachmentsByThread");
      expect(store).toHaveProperty("addAttachment");
      expect(store).toHaveProperty("removeAttachment");
      expect(store).toHaveProperty("clearAttachments");
      expect(store).toHaveProperty("setAttachmentsForThread"); // Check new action
    });
  });

  describe("edge cases and error handling", () => {
    it("should handle removing attachment from non-existent thread", () => {
      const { result } = renderHook(() => useAttachmentStore());

      // Try to remove from a thread that doesn't exist - should not throw error
      expect(() => {
        act(() => {
          result.current.removeAttachment(
            "non-existent-thread",
            "non-existent-uid"
          );
        });
      }).not.toThrow();

      // State should remain unchanged
      expect(result.current.attachmentsByThread).toEqual({});
    });

    it("should handle removing non-existent attachment from existing thread", () => {
      const attachment = {
        uid: "1",
        name: "test.pdf",
        type: "application/pdf",
      };
      const { result } = renderHook(() => useAttachmentStore());

      // Add an attachment first
      act(() => {
        result.current.addAttachment(MOCK_THREAD_ID, attachment);
      });

      // Try to remove a non-existent attachment
      act(() => {
        result.current.removeAttachment(MOCK_THREAD_ID, "non-existent-uid");
      });

      // Original attachment should still be there
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toEqual([
        attachment,
      ]);
    });

    it("should handle clearing attachments from non-existent thread", () => {
      const { result } = renderHook(() => useAttachmentStore());

      // Try to clear from a thread that doesn't exist - should not throw error
      expect(() => {
        act(() => {
          result.current.clearAttachments("non-existent-thread");
        });
      }).not.toThrow();

      // State should remain unchanged
      expect(result.current.attachmentsByThread).toEqual({});
    });

    it("should handle setting empty attachments array for thread", () => {
      const attachment = {
        uid: "1",
        name: "test.pdf",
        type: "application/pdf",
      };
      const { result } = renderHook(() => useAttachmentStore());

      // Add an attachment first
      act(() => {
        result.current.addAttachment(MOCK_THREAD_ID, attachment);
      });
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toHaveLength(
        1
      );

      // Set empty array
      act(() => {
        result.current.setAttachmentsForThread(MOCK_THREAD_ID, []);
      });

      // Thread should have empty array (not be deleted)
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toEqual([]);
      expect(result.current.attachmentsByThread).toHaveProperty(MOCK_THREAD_ID);
    });

    it("should handle duplicate attachment UIDs within same thread", () => {
      const attachment1 = {
        uid: "1",
        name: "test1.pdf",
        type: "application/pdf",
      };
      const attachment2 = {
        uid: "1", // Same UID
        name: "test2.pdf",
        type: "application/pdf",
      };
      const { result } = renderHook(() => useAttachmentStore());

      // Add both attachments (should allow duplicates)
      act(() => {
        result.current.addAttachment(MOCK_THREAD_ID, attachment1);
        result.current.addAttachment(MOCK_THREAD_ID, attachment2);
      });

      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toHaveLength(
        2
      );

      // Remove by UID should remove both since they have the same UID
      act(() => {
        result.current.removeAttachment(MOCK_THREAD_ID, "1");
      });

      // Should remove all attachments with that UID, so thread should be deleted
      expect(result.current.attachmentsByThread).not.toHaveProperty(
        MOCK_THREAD_ID
      );
    });
  });

  describe("attachment data validation and types", () => {
    it("should handle attachments with optional properties", () => {
      const minimalAttachment = {
        uid: "minimal",
        name: "minimal.txt",
        type: "text/plain",
      };
      const fullAttachment = {
        uid: "full",
        name: "full.pdf",
        type: "application/pdf",
        size: 1024,
        url: "https://example.com/file.pdf",
        customProperty: "custom value",
      };
      const { result } = renderHook(() => useAttachmentStore());

      act(() => {
        result.current.addAttachment(MOCK_THREAD_ID, minimalAttachment);
        result.current.addAttachment(MOCK_THREAD_ID, fullAttachment);
      });

      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toHaveLength(
        2
      );
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID][0]).toEqual(
        minimalAttachment
      );
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID][1]).toEqual(
        fullAttachment
      );
      expect(
        result.current.attachmentsByThread[MOCK_THREAD_ID][1].customProperty
      ).toBe("custom value");
    });

    it("should handle various file types and sizes", () => {
      const attachments = [
        { uid: "1", name: "document.pdf", type: "application/pdf", size: 2048 },
        { uid: "2", name: "image.jpg", type: "image/jpeg", size: 5120 },
        { uid: "3", name: "video.mp4", type: "video/mp4", size: 10485760 },
        {
          uid: "4",
          name: "archive.zip",
          type: "application/zip",
          size: 1024000,
        },
        {
          uid: "5",
          name: "spreadsheet.xlsx",
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        },
      ];
      const { result } = renderHook(() => useAttachmentStore());

      act(() => {
        attachments.forEach((attachment) => {
          result.current.addAttachment(MOCK_THREAD_ID, attachment);
        });
      });

      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toHaveLength(
        5
      );
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toEqual(
        attachments
      );
    });
  });

  describe("performance scenarios", () => {
    it("should handle large numbers of attachments efficiently", () => {
      const { result } = renderHook(() => useAttachmentStore());
      const largeAttachmentList = Array.from({ length: 1000 }, (_, i) => ({
        uid: `attachment-${i}`,
        name: `file-${i}.pdf`,
        type: "application/pdf",
        size: Math.floor(Math.random() * 10000),
      }));

      // Test adding many attachments
      const startTime = performance.now();
      act(() => {
        largeAttachmentList.forEach((attachment) => {
          result.current.addAttachment(MOCK_THREAD_ID, attachment);
        });
      });
      const addTime = performance.now() - startTime;

      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toHaveLength(
        1000
      );
      expect(addTime).toBeLessThan(1000); // Should complete within 1 second

      // Test removing attachments efficiently
      const removeStartTime = performance.now();
      act(() => {
        // Remove every 10th attachment
        for (let i = 0; i < 1000; i += 10) {
          result.current.removeAttachment(MOCK_THREAD_ID, `attachment-${i}`);
        }
      });
      const removeTime = performance.now() - removeStartTime;

      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toHaveLength(
        900
      );
      expect(removeTime).toBeLessThan(1000); // Should complete within 1 second
    });

    it("should handle multiple threads with many attachments", () => {
      const { result } = renderHook(() => useAttachmentStore());
      const numberOfThreads = 50;
      const attachmentsPerThread = 20;

      const startTime = performance.now();
      act(() => {
        for (
          let threadIndex = 0;
          threadIndex < numberOfThreads;
          threadIndex++
        ) {
          const threadId = `thread-${threadIndex}`;
          for (
            let attachIndex = 0;
            attachIndex < attachmentsPerThread;
            attachIndex++
          ) {
            result.current.addAttachment(threadId, {
              uid: `${threadId}-attachment-${attachIndex}`,
              name: `file-${attachIndex}.pdf`,
              type: "application/pdf",
            });
          }
        }
      });
      const totalTime = performance.now() - startTime;

      expect(Object.keys(result.current.attachmentsByThread)).toHaveLength(
        numberOfThreads
      );
      Object.values(result.current.attachmentsByThread).forEach(
        (attachments) => {
          expect(attachments).toHaveLength(attachmentsPerThread);
        }
      );
      expect(totalTime).toBeLessThan(2000); // Should complete within 2 seconds
    });
  });

  describe("persistence and state management", () => {
    it("should maintain state structure after operations", () => {
      const { result } = renderHook(() => useAttachmentStore());
      const attachment = {
        uid: "test-persistence",
        name: "test.pdf",
        type: "application/pdf",
      };

      // Add attachment
      act(() => {
        result.current.addAttachment(MOCK_THREAD_ID, attachment);
      });

      // Verify state structure remains intact
      const state = result.current;
      expect(typeof state.attachmentsByThread).toBe("object");
      expect(typeof state.addAttachment).toBe("function");
      expect(typeof state.removeAttachment).toBe("function");
      expect(typeof state.clearAttachments).toBe("function");
      expect(typeof state.setAttachmentsForThread).toBe("function");

      // Verify the store's persistence configuration
      const storeConfig = useAttachmentStore.persist;
      expect(storeConfig).toBeDefined();
    });

    it("should handle concurrent state updates correctly", () => {
      const { result } = renderHook(() => useAttachmentStore());
      const attachments = [
        { uid: "1", name: "file1.pdf", type: "application/pdf" },
        { uid: "2", name: "file2.pdf", type: "application/pdf" },
        { uid: "3", name: "file3.pdf", type: "application/pdf" },
      ];

      // Simulate concurrent updates
      act(() => {
        result.current.addAttachment(MOCK_THREAD_ID, attachments[0]);
        result.current.addAttachment(MOCK_THREAD_ID_2, attachments[1]);
        result.current.addAttachment(MOCK_THREAD_ID, attachments[2]);
        result.current.removeAttachment(MOCK_THREAD_ID, attachments[0].uid);
      });

      expect(result.current.attachmentsByThread[MOCK_THREAD_ID]).toHaveLength(
        1
      );
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID][0]).toEqual(
        attachments[2]
      );
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID_2]).toHaveLength(
        1
      );
      expect(result.current.attachmentsByThread[MOCK_THREAD_ID_2][0]).toEqual(
        attachments[1]
      );
    });
  });

  describe("selector hooks", () => {
    it("should provide attachments for a specific thread via useThreadAttachments hook", () => {
      const attachment = {
        uid: "1",
        name: "test.pdf",
        type: "application/pdf",
      };
      const attachmentOtherThread = {
        uid: "2",
        name: "other.pdf",
        type: "application/pdf",
      };

      act(() => {
        useAttachmentStore.getState().addAttachment(MOCK_THREAD_ID, attachment);
        useAttachmentStore
          .getState()
          .addAttachment(MOCK_THREAD_ID_2, attachmentOtherThread);
      });

      // Test getting attachments for the specific thread
      const { result: threadAttachmentsResult } = renderHook(() =>
        useThreadAttachments(MOCK_THREAD_ID)
      );
      expect(threadAttachmentsResult.current).toEqual([attachment]);

      // Test getting attachments for another thread
      const { result: otherThreadAttachmentsResult } = renderHook(() =>
        useThreadAttachments(MOCK_THREAD_ID_2)
      );
      expect(otherThreadAttachmentsResult.current).toEqual([
        attachmentOtherThread,
      ]);

      // Test getting attachments for a thread with no attachments (should return empty array)
      const { result: emptyThreadResult } = renderHook(() =>
        useThreadAttachments("non-existent-thread")
      );
      expect(emptyThreadResult.current).toEqual([]);
    });

    it("should provide addAttachment via useAddAttachment hook", () => {
      const attachment = {
        uid: "1",
        name: "test.pdf",
        type: "application/pdf",
      };

      const { result: addAttachmentResult } = renderHook(() =>
        useAddAttachment()
      );
      const { result: attachmentsResult } = renderHook(() =>
        useThreadAttachments(MOCK_THREAD_ID)
      );

      act(() => {
        addAttachmentResult.current(MOCK_THREAD_ID, attachment);
      });

      expect(attachmentsResult.current).toEqual([attachment]);
    });

    it("should provide removeAttachment via useRemoveAttachment hook", () => {
      const attachment1 = {
        uid: "1",
        name: "test1.pdf",
        type: "application/pdf",
      };
      const attachment2 = {
        uid: "2",
        name: "test2.pdf",
        type: "application/pdf",
      };

      act(() => {
        useAttachmentStore
          .getState()
          .addAttachment(MOCK_THREAD_ID, attachment1);
        useAttachmentStore
          .getState()
          .addAttachment(MOCK_THREAD_ID, attachment2);
      });

      const { result: removeAttachmentResult } = renderHook(() =>
        useRemoveAttachment()
      );
      const { result: attachmentsResult } = renderHook(() =>
        useThreadAttachments(MOCK_THREAD_ID)
      );

      act(() => {
        removeAttachmentResult.current(MOCK_THREAD_ID, attachment1.uid);
      });

      expect(attachmentsResult.current).toEqual([attachment2]);
    });

    it("should provide clearAttachments via useClearAttachments hook", () => {
      const attachment = {
        uid: "1",
        name: "test.pdf",
        type: "application/pdf",
      };

      act(() => {
        useAttachmentStore.getState().addAttachment(MOCK_THREAD_ID, attachment);
      });

      const { result: clearAttachmentsResult } = renderHook(() =>
        useClearAttachments()
      );
      const { result: attachmentsResult } = renderHook(() =>
        useThreadAttachments(MOCK_THREAD_ID)
      );

      expect(attachmentsResult.current).toHaveLength(1);

      act(() => {
        clearAttachmentsResult.current(MOCK_THREAD_ID);
      });

      expect(attachmentsResult.current).toHaveLength(0);
      // Also check the main store state to ensure the key was removed
      expect(
        useAttachmentStore.getState().attachmentsByThread
      ).not.toHaveProperty(MOCK_THREAD_ID);
    });

    it("should provide setAttachmentsForThread via useSetAttachmentsForThread hook", () => {
      const initialAttachment = {
        uid: "1",
        name: "initial.pdf",
        type: "application/pdf",
      };
      const newAttachments = [
        { uid: "2", name: "new1.png", type: "image/png" },
        { uid: "3", name: "new2.jpg", type: "image/jpeg" },
      ];

      act(() => {
        useAttachmentStore
          .getState()
          .addAttachment(MOCK_THREAD_ID, initialAttachment);
      });

      const { result: setAttachmentsResult } = renderHook(() =>
        useSetAttachmentsForThread()
      );
      const { result: attachmentsResult } = renderHook(() =>
        useThreadAttachments(MOCK_THREAD_ID)
      );

      expect(attachmentsResult.current).toEqual([initialAttachment]);

      act(() => {
        setAttachmentsResult.current(MOCK_THREAD_ID, newAttachments);
      });

      expect(attachmentsResult.current).toEqual(newAttachments);
    });
  });

  describe("TypeScript type safety", () => {
    it("should enforce correct attachment interface", () => {
      const { result } = renderHook(() => useAttachmentStore());

      // Test that required properties are enforced
      const validAttachment = {
        uid: "valid",
        name: "valid.pdf",
        type: "application/pdf",
        size: 1024,
        customField: "allowed", // Additional properties are allowed
      };

      act(() => {
        result.current.addAttachment(MOCK_THREAD_ID, validAttachment);
      });

      expect(result.current.attachmentsByThread[MOCK_THREAD_ID][0]).toEqual(
        validAttachment
      );
    });

    it("should maintain type safety for hook return values", () => {
      const { result: threadAttachmentsResult } = renderHook(() =>
        useThreadAttachments(MOCK_THREAD_ID)
      );
      const { result: addAttachmentResult } = renderHook(() =>
        useAddAttachment()
      );
      const { result: removeAttachmentResult } = renderHook(() =>
        useRemoveAttachment()
      );
      const { result: clearAttachmentsResult } = renderHook(() =>
        useClearAttachments()
      );
      const { result: setAttachmentsResult } = renderHook(() =>
        useSetAttachmentsForThread()
      );

      // Verify return types match expected interfaces
      expect(Array.isArray(threadAttachmentsResult.current)).toBe(true);
      expect(typeof addAttachmentResult.current).toBe("function");
      expect(typeof removeAttachmentResult.current).toBe("function");
      expect(typeof clearAttachmentsResult.current).toBe("function");
      expect(typeof setAttachmentsResult.current).toBe("function");
    });
  });

  describe("EMPTY_ATTACHMENTS constant", () => {
    it("should return the same empty array reference for non-existent threads", () => {
      const { result: result1 } = renderHook(() =>
        useThreadAttachments("non-existent-1")
      );
      const { result: result2 } = renderHook(() =>
        useThreadAttachments("non-existent-2")
      );

      // Should return the same reference to optimize React re-renders
      expect(result1.current).toBe(result2.current);
      expect(result1.current).toEqual([]);
    });

    it("should not affect the empty reference when modifying returned arrays", () => {
      const { result } = renderHook(() => useThreadAttachments("non-existent"));
      const emptyArray = result.current;

      // This should not affect the original EMPTY_ATTACHMENTS constant
      const modifiedArray = [
        ...emptyArray,
        { uid: "test", name: "test.pdf", type: "application/pdf" },
      ];

      expect(emptyArray).toEqual([]);
      expect(modifiedArray).toHaveLength(1);

      // Getting the empty array again should still return the same reference
      const { result: result2 } = renderHook(() =>
        useThreadAttachments("another-non-existent")
      );
      expect(result2.current).toBe(emptyArray);
    });
  });
});
