import { act, renderHook } from "@testing-library/react";
import useProgressStore from "../progressStore";

// Mock thread IDs for tests
const MOCK_THREAD_ID = "thread-123";
const MOCK_THREAD_ID_2 = "thread-456";
const MOCK_THREAD_ID_3 = "thread-789";

// Mock Date.now for consistent testing
const ORIGINAL_DATE_NOW = Date.now;

describe("progressStore", () => {
  beforeEach(() => {
    // Reset the store state completely before each test
    act(() => {
      useProgressStore.setState({ threads: new Map() });
    });

    // Clear all mocks
    jest.clearAllMocks();

    // Reset Date.now mock
    Date.now = ORIGINAL_DATE_NOW;
  });

  afterEach(() => {
    // Restore original Date.now
    Date.now = ORIGINAL_DATE_NOW;
  });

  describe("basic state management", () => {
    it("should initialize with empty threads Map", () => {
      const { result } = renderHook(() => useProgressStore());
      expect(result.current.threads).toEqual(new Map());
    });

    it("should start a process with default parameters", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState).toBeDefined();
      expect(threadState?.isActive).toBe(true);
      expect(threadState?.currentStep).toBe(1);
      expect(threadState?.totalSteps).toBe(7);
      expect(threadState?.startTime).toBeGreaterThan(0);
      expect(threadState?.flowType).toBeNull();
      expect(threadState?.stepStatus).toBe("pending");
      expect(threadState?.stepDetails).toEqual([]);
      expect(threadState?.abortController).toBeInstanceOf(AbortController);
    });

    it("should start a process with custom parameters", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID, 5, "cdb");
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.totalSteps).toBe(5);
      expect(threadState?.flowType).toBe("cdb");
    });

    it("should abort existing process when starting a new one for the same thread", () => {
      const { result } = renderHook(() => useProgressStore());

      // Start first process
      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      const firstController =
        result.current.threads.get(MOCK_THREAD_ID)?.abortController!;
      const abortSpy = jest.spyOn(firstController, "abort");

      // Start second process for same thread
      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      expect(abortSpy).toHaveBeenCalled();

      // Verify new controller is different
      const secondController =
        result.current.threads.get(MOCK_THREAD_ID)?.abortController;
      expect(secondController).not.toBe(firstController);
    });

    it("should ignore invalid thread slugs in startProcess", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(null as any);
        result.current.startProcess("");
        result.current.startProcess(123 as any);
      });

      expect(result.current.threads.size).toBe(0);
    });
  });

  describe("progress updates", () => {
    beforeEach(() => {
      const { result } = renderHook(() => useProgressStore());
      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });
    });

    it("should update progress with step information", () => {
      const { result } = renderHook(() => useProgressStore());

      const updateEvent = {
        step: 2,
        status: "in_progress" as const,
        message: "Processing step 2",
      };

      act(() => {
        result.current.updateProgress(updateEvent, MOCK_THREAD_ID);
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.currentStep).toBe(2);
      expect(threadState?.stepStatus).toBe("in_progress");
      expect(threadState?.stepMessage).toBe("Processing step 2");
      expect(threadState?.lastUpdate).toBeGreaterThan(0);
    });

    it("should track step details", () => {
      const { result } = renderHook(() => useProgressStore());

      const updateEvent = {
        step: 1,
        status: "in_progress" as const,
        message: "Starting step 1",
      };

      act(() => {
        result.current.updateProgress(updateEvent, MOCK_THREAD_ID);
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.stepDetails).toHaveLength(1);
      expect(threadState?.stepDetails[0]).toMatchObject({
        step: 1,
        status: "in_progress",
        message: "Starting step 1",
        subTasks: [],
      });
    });

    it("should handle sub-tasks", () => {
      const { result } = renderHook(() => useProgressStore());

      const updateEvent = {
        step: 1,
        subStep: 1,
        status: "in_progress" as const,
        message: "Sub-task 1",
      };

      act(() => {
        result.current.updateProgress(updateEvent, MOCK_THREAD_ID);
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.stepDetails[0].subTasks).toHaveLength(1);
      expect(threadState?.stepDetails[0].subTasks[0]).toMatchObject({
        subStep: 1,
        status: "in_progress",
        message: "Sub-task 1",
      });
    });

    it("should ignore malformed update events", () => {
      const { result } = renderHook(() => useProgressStore());

      const initialState = result.current.threads.get(MOCK_THREAD_ID);

      act(() => {
        result.current.updateProgress(
          { step: 1, status: "error" as const },
          MOCK_THREAD_ID
        ); // Missing step
        result.current.updateProgress(null as any, MOCK_THREAD_ID); // Null event
      });

      const finalState = result.current.threads.get(MOCK_THREAD_ID);
      expect(finalState?.currentStep).toBe(initialState?.currentStep);
    });

    it("should ignore updates for inactive threads", () => {
      const { result } = renderHook(() => useProgressStore());

      // Cancel the process first
      act(() => {
        result.current.cancelProcess(MOCK_THREAD_ID);
      });

      const updateEvent = { step: 2, status: "in_progress" as const };

      act(() => {
        result.current.updateProgress(updateEvent, MOCK_THREAD_ID);
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.currentStep).toBe(1); // Should not update
    });
  });

  describe("process lifecycle", () => {
    it("should finish and mark process as completed", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID, 5, "test");
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(true);

      act(() => {
        result.current.finishProcess(MOCK_THREAD_ID);
      });

      // Thread should still exist but marked as completed
      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(true);

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.isActive).toBe(false);
      expect(threadState?.isCompleted).toBe(true);
      expect(threadState?.completionTime).toBeGreaterThan(0);
      expect(threadState?.stepStatus).toBe("complete");
      expect(threadState?.currentStep).toBe(5); // Should be at the last step
    });

    it("should remove completed process after delay", async () => {
      jest.useFakeTimers();
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      act(() => {
        result.current.finishProcess(MOCK_THREAD_ID);
      });

      // Thread should still exist immediately after finishing
      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(true);
      expect(result.current.threads.get(MOCK_THREAD_ID)?.isCompleted).toBe(
        true
      );

      // Fast forward time by 5 seconds (the cleanup delay)
      act(() => {
        jest.advanceTimersByTime(5000);
      });

      // Now the thread should be removed
      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(false);

      jest.useRealTimers();
    });

    it("should not remove completed process after delay if it becomes active again", async () => {
      jest.useFakeTimers();
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      act(() => {
        result.current.finishProcess(MOCK_THREAD_ID);
      });

      // Make the thread non-completed before the timeout
      act(() => {
        const threads = new Map(result.current.threads);
        const threadState = threads.get(MOCK_THREAD_ID);
        if (threadState) {
          threadState.isCompleted = false;
          threads.set(MOCK_THREAD_ID, threadState);
          useProgressStore.setState({ threads });
        }
      });

      // Fast forward time by 5 seconds (the cleanup delay)
      act(() => {
        jest.advanceTimersByTime(5000);
      });

      // Thread should still exist since it's no longer completed
      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(true);

      jest.useRealTimers();
    });

    it("should cancel a process", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      const controller =
        result.current.threads.get(MOCK_THREAD_ID)?.abortController!;
      const abortSpy = jest.spyOn(controller, "abort");

      act(() => {
        result.current.cancelProcess(MOCK_THREAD_ID);
      });

      expect(abortSpy).toHaveBeenCalled();

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.isActive).toBe(false);
      expect(threadState?.stepStatus).toBe("error");
    });

    it("should set error state", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      act(() => {
        result.current.setError(MOCK_THREAD_ID, "Test error message");
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.isActive).toBe(false);
      expect(threadState?.stepStatus).toBe("error");
      expect(threadState?.error).toBe("Test error message");
    });

    it("should clear error and remove thread", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
        result.current.setError(MOCK_THREAD_ID, "Test error");
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(true);

      act(() => {
        result.current.clearError(MOCK_THREAD_ID);
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(false);
    });
  });

  describe("utility methods", () => {
    it("should get abort controller for a thread", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      const controller = result.current.getAbortController(MOCK_THREAD_ID);
      expect(controller).toBeInstanceOf(AbortController);
    });

    it("should return null for non-existent thread controller", () => {
      const { result } = renderHook(() => useProgressStore());

      const controller = result.current.getAbortController("non-existent");
      expect(controller).toBeNull();
    });

    it("should get thread state", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      const threadState = result.current.getThreadState(MOCK_THREAD_ID);
      expect(threadState).toBeDefined();
      expect(threadState?.isActive).toBe(true);
    });

    it("should return null for non-existent thread state", () => {
      const { result } = renderHook(() => useProgressStore());

      const threadState = result.current.getThreadState("non-existent");
      expect(threadState).toBeNull();
    });

    it("should force cleanup a thread", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(true);

      act(() => {
        result.current.forceCleanup(MOCK_THREAD_ID);
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(false);
    });
  });

  describe("cleanup functionality", () => {
    it("should cleanup stale processes", () => {
      const { result } = renderHook(() => useProgressStore());

      // Create a process first
      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      // Then manually set an old start time
      act(() => {
        const threads = new Map(result.current.threads);
        const threadState = threads.get(MOCK_THREAD_ID);
        if (threadState) {
          threadState.startTime = Date.now() - 11 * 60 * 1000;
          threads.set(MOCK_THREAD_ID, threadState);
          useProgressStore.setState({ threads });
        }
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(true);

      act(() => {
        result.current.cleanupStaleProcesses(10); // 10 minutes max age
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(false);
    });

    it("should not cleanup recent processes", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(true);

      act(() => {
        result.current.cleanupStaleProcesses(10); // 10 minutes max age
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(true);
    });
  });

  describe("multiple threads", () => {
    it("should handle multiple independent threads", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
        result.current.startProcess(MOCK_THREAD_ID_2, 5, "different");
      });

      expect(result.current.threads.size).toBe(2);

      const thread1 = result.current.threads.get(MOCK_THREAD_ID);
      const thread2 = result.current.threads.get(MOCK_THREAD_ID_2);

      expect(thread1?.totalSteps).toBe(7);
      expect(thread1?.flowType).toBeNull();
      expect(thread2?.totalSteps).toBe(5);
      expect(thread2?.flowType).toBe("different");
    });

    it("should update threads independently", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
        result.current.startProcess(MOCK_THREAD_ID_2);
      });

      act(() => {
        result.current.updateProgress(
          { step: 2, status: "in_progress" },
          MOCK_THREAD_ID
        );
        result.current.updateProgress(
          { step: 3, status: "complete" },
          MOCK_THREAD_ID_2
        );
      });

      const thread1 = result.current.threads.get(MOCK_THREAD_ID);
      const thread2 = result.current.threads.get(MOCK_THREAD_ID_2);

      expect(thread1?.currentStep).toBe(2);
      expect(thread1?.stepStatus).toBe("in_progress");
      expect(thread2?.currentStep).toBe(3);
      expect(thread2?.stepStatus).toBe("complete");
    });

    it("should finish threads independently", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
        result.current.startProcess(MOCK_THREAD_ID_2);
      });

      act(() => {
        result.current.finishProcess(MOCK_THREAD_ID);
      });

      // First thread should be completed but still exist
      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(true);
      const thread1 = result.current.threads.get(MOCK_THREAD_ID);
      expect(thread1?.isCompleted).toBe(true);
      expect(thread1?.isActive).toBe(false);

      // Second thread should still be active
      expect(result.current.threads.has(MOCK_THREAD_ID_2)).toBe(true);
      const thread2 = result.current.threads.get(MOCK_THREAD_ID_2);
      expect(thread2?.isActive).toBe(true);
      expect(thread2?.isCompleted).toBe(false);
    });
  });

  describe("error handling", () => {
    it("should handle invalid thread slugs gracefully", () => {
      const { result } = renderHook(() => useProgressStore());

      // These should not throw errors
      act(() => {
        result.current.updateProgress({ step: 1 }, null as any);
        result.current.finishProcess("");
        result.current.cancelProcess(undefined as any);
        result.current.setError(123 as any, "error");
        result.current.clearError("");
      });

      expect(result.current.threads.size).toBe(0);
    });

    it("should handle operations on non-existent threads", () => {
      const { result } = renderHook(() => useProgressStore());

      // These should not throw errors
      act(() => {
        result.current.updateProgress({ step: 1 }, "non-existent");
        result.current.finishProcess("non-existent");
        result.current.cancelProcess("non-existent");
        result.current.setError("non-existent", "error");
        result.current.clearError("non-existent");
      });

      expect(result.current.threads.size).toBe(0);
    });
  });

  describe("comprehensive progress update scenarios", () => {
    beforeEach(() => {
      const { result } = renderHook(() => useProgressStore());
      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });
    });

    it("should handle progress updates with total parameter", () => {
      const { result } = renderHook(() => useProgressStore());

      const updateEvent = {
        step: 1,
        total: 50,
        status: "in_progress" as const,
        message: "Processing with total",
      };

      act(() => {
        result.current.updateProgress(updateEvent, MOCK_THREAD_ID);
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.stepDetails[0].expectedTotal).toBe(50);
    });

    it("should handle progress with 100% completion auto-status", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.updateProgress(
          { step: 1, subStep: 1, message: "Starting" },
          MOCK_THREAD_ID
        );
      });

      act(() => {
        result.current.updateProgress(
          { step: 1, subStep: 1, progress: 100 },
          MOCK_THREAD_ID
        );
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.stepDetails[0].subTasks[0].status).toBe("complete");
    });

    it("should handle progress with error auto-status", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.updateProgress(
          { step: 1, subStep: 1, message: "Starting" },
          MOCK_THREAD_ID
        );
      });

      act(() => {
        result.current.updateProgress(
          { step: 1, subStep: 1, progress: -2 },
          MOCK_THREAD_ID
        );
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.stepDetails[0].subTasks[0].status).toBe("error");
    });

    it("should handle progress with error object", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.updateProgress(
          { step: 1, subStep: 1, message: "Starting" },
          MOCK_THREAD_ID
        );
      });

      act(() => {
        result.current.updateProgress(
          { step: 1, subStep: 1, error: new Error("Test error") },
          MOCK_THREAD_ID
        );
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.stepDetails[0].subTasks[0].status).toBe("error");
    });

    it("should handle sub-task auto-completion with label only updates", () => {
      const { result } = renderHook(() => useProgressStore());

      // Set up initial sub-task
      act(() => {
        result.current.updateProgress(
          {
            step: 1,
            subStep: 1,
            status: "in_progress" as const,
            message: "Processing",
            label: "Initial label",
          },
          MOCK_THREAD_ID
        );
      });

      // Update with new label only (should auto-complete)
      act(() => {
        result.current.updateProgress(
          { step: 1, subStep: 1, label: "New label" },
          MOCK_THREAD_ID
        );
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.stepDetails[0].subTasks[0].status).toBe("complete");
      expect(threadState?.stepDetails[0].subTasks[0].label).toBe("New label");
    });

    it("should handle sub-task sorting", () => {
      const { result } = renderHook(() => useProgressStore());

      // Add sub-tasks in reverse order
      act(() => {
        result.current.updateProgress(
          { step: 1, subStep: 3, message: "Third" },
          MOCK_THREAD_ID
        );
      });

      act(() => {
        result.current.updateProgress(
          { step: 1, subStep: 1, message: "First" },
          MOCK_THREAD_ID
        );
      });

      act(() => {
        result.current.updateProgress(
          { step: 1, subStep: 2, message: "Second" },
          MOCK_THREAD_ID
        );
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      const subTasks = threadState?.stepDetails[0].subTasks;
      expect(subTasks?.map((st) => st.subStep)).toEqual([1, 2, 3]);
      expect(subTasks?.map((st) => st.message)).toEqual([
        "First",
        "Second",
        "Third",
      ]);
    });

    it("should update existing step details without losing data", () => {
      const { result } = renderHook(() => useProgressStore());

      // Initial step
      act(() => {
        result.current.updateProgress(
          { step: 1, status: "in_progress" as const, message: "Initial" },
          MOCK_THREAD_ID
        );
      });

      // Update same step with new status only
      act(() => {
        result.current.updateProgress(
          { step: 1, status: "complete" as const },
          MOCK_THREAD_ID
        );
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.stepDetails[0].status).toBe("complete");
      expect(threadState?.stepDetails[0].message).toBe("Initial"); // Should preserve existing message
    });

    it("should handle updates with no status preserving current sub-task status", () => {
      const { result } = renderHook(() => useProgressStore());

      // Set up sub-task with specific status
      act(() => {
        result.current.updateProgress(
          {
            step: 1,
            subStep: 1,
            status: "starting" as const,
            message: "Starting",
          },
          MOCK_THREAD_ID
        );
      });

      // Update without status (should move from pending to in_progress)
      act(() => {
        result.current.updateProgress(
          { step: 1, subStep: 1, message: "Updated message" },
          MOCK_THREAD_ID
        );
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.stepDetails[0].subTasks[0].status).toBe("starting"); // Should preserve status
      expect(threadState?.stepDetails[0].subTasks[0].message).toBe(
        "Updated message"
      );
    });

    it("should handle status transition from pending to in_progress when no status provided", () => {
      const { result } = renderHook(() => useProgressStore());

      // Create sub-task with pending status
      act(() => {
        result.current.updateProgress(
          {
            step: 1,
            subStep: 1,
            status: "pending" as const,
            message: "Pending",
          },
          MOCK_THREAD_ID
        );
      });

      // Update without status (should move from pending to in_progress)
      act(() => {
        result.current.updateProgress(
          { step: 1, subStep: 1, message: "Now processing" },
          MOCK_THREAD_ID
        );
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.stepDetails[0].subTasks[0].status).toBe(
        "in_progress"
      );
    });

    it("should handle invalid step numbers gracefully", () => {
      const { result } = renderHook(() => useProgressStore());

      const initialState = result.current.threads.get(MOCK_THREAD_ID);

      act(() => {
        result.current.updateProgress(
          { step: "invalid" as any, status: "error" as const },
          MOCK_THREAD_ID
        );
      });

      // State should remain unchanged
      const finalState = result.current.threads.get(MOCK_THREAD_ID);
      expect(finalState?.currentStep).toBe(initialState?.currentStep);
      expect(finalState?.stepDetails).toEqual(initialState?.stepDetails);
    });
  });

  describe("advanced cleanup scenarios", () => {
    it("should cleanup based on lastUpdate when available", () => {
      const { result } = renderHook(() => useProgressStore());

      // Mock Date.now to control time
      const mockNow = 1640995200000; // Fixed timestamp
      Date.now = jest.fn(() => mockNow);

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      // Update the thread to set lastUpdate
      act(() => {
        result.current.updateProgress(
          { step: 1, status: "in_progress" as const },
          MOCK_THREAD_ID
        );
      });

      // Mock time passage of 15 minutes
      Date.now = jest.fn(() => mockNow + 15 * 60 * 1000);

      act(() => {
        result.current.cleanupStaleProcesses(10); // 10 minutes max age
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(false);
    });

    it("should not cleanup if no threads exist", () => {
      const { result } = renderHook(() => useProgressStore());

      // Should not throw error when no threads exist
      act(() => {
        result.current.cleanupStaleProcesses();
      });

      expect(result.current.threads.size).toBe(0);
    });

    it("should return same state when no cleanup needed", () => {
      const { result } = renderHook(() => useProgressStore());

      // Create a recent thread
      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      const initialState = result.current.threads;

      // Cleanup with default threshold should not affect recent threads
      act(() => {
        result.current.cleanupStaleProcesses();
      });

      // Should return the same state reference when no changes needed
      expect(result.current.threads).toBe(initialState);
    });

    it("should handle cleanup with mixed thread ages", () => {
      const { result } = renderHook(() => useProgressStore());

      const mockNow = 1640995200000;
      Date.now = jest.fn(() => mockNow);

      // Create three threads at different times
      act(() => {
        result.current.startProcess(MOCK_THREAD_ID); // Recent
      });

      Date.now = jest.fn(() => mockNow - 5 * 60 * 1000); // 5 minutes ago
      act(() => {
        result.current.startProcess(MOCK_THREAD_ID_2); // Medium age
      });

      Date.now = jest.fn(() => mockNow - 15 * 60 * 1000); // 15 minutes ago
      act(() => {
        result.current.startProcess(MOCK_THREAD_ID_3); // Old
      });

      // Reset to current time for cleanup
      Date.now = jest.fn(() => mockNow);

      act(() => {
        result.current.cleanupStaleProcesses(10); // 10 minutes threshold
      });

      // Only recent and medium-age threads should remain
      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(true);
      expect(result.current.threads.has(MOCK_THREAD_ID_2)).toBe(true);
      expect(result.current.threads.has(MOCK_THREAD_ID_3)).toBe(false);
    });

    it("should handle clearStaleProgress correctly", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID, 5, "test");
      });

      const oldController =
        result.current.threads.get(MOCK_THREAD_ID)?.abortController;
      const abortSpy = jest.spyOn(oldController!, "abort");

      act(() => {
        result.current.clearStaleProgress(MOCK_THREAD_ID);
      });

      expect(abortSpy).toHaveBeenCalled();

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.isActive).toBe(false);
      expect(threadState?.currentStep).toBe(1);
      expect(threadState?.totalSteps).toBe(7); // Should reset to default
      expect(threadState?.stepDetails).toEqual([]);
      expect(threadState?.abortController).not.toBe(oldController); // New controller
    });

    it("should handle clearStaleProgress for non-existent thread", () => {
      const { result } = renderHook(() => useProgressStore());

      // Should not throw error
      act(() => {
        result.current.clearStaleProgress("non-existent");
      });

      expect(result.current.threads.size).toBe(0);
    });
  });

  describe("edge cases and boundary conditions", () => {
    it("should handle zero and negative totalSteps in startProcess", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID, 0);
      });

      expect(result.current.threads.get(MOCK_THREAD_ID)?.totalSteps).toBe(1); // Should enforce minimum

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID_2, -5);
      });

      expect(result.current.threads.get(MOCK_THREAD_ID_2)?.totalSteps).toBe(1); // Should enforce minimum
    });

    it("should handle very large totalSteps", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID, 999999);
      });

      expect(result.current.threads.get(MOCK_THREAD_ID)?.totalSteps).toBe(
        999999
      );
    });

    it("should handle subStep value of 0", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      act(() => {
        result.current.updateProgress(
          { step: 1, subStep: 0, message: "Zero sub-step" },
          MOCK_THREAD_ID
        );
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.stepDetails[0].subTasks).toHaveLength(1);
      expect(threadState?.stepDetails[0].subTasks[0].subStep).toBe(0);
    });

    it("should handle null values in clearError for invalid thread", () => {
      const { result } = renderHook(() => useProgressStore());

      // Should not throw error for null/undefined thread
      act(() => {
        result.current.clearError(null as any);
        result.current.clearError(undefined as any);
      });

      expect(result.current.threads.size).toBe(0);
    });

    it("should handle forceCleanup for thread without abortController", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      // Manually remove abortController to simulate edge case
      act(() => {
        const threads = new Map(result.current.threads);
        const threadState = threads.get(MOCK_THREAD_ID);
        if (threadState) {
          (threadState as any).abortController = null;
          threads.set(MOCK_THREAD_ID, threadState);
          useProgressStore.setState({ threads });
        }
      });

      // Should not throw error
      act(() => {
        result.current.forceCleanup(MOCK_THREAD_ID);
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(false);
    });

    it("should handle forceCleanup for non-existent thread", () => {
      const { result } = renderHook(() => useProgressStore());

      const initialState = result.current.threads;

      // Should not throw error and should return same state
      act(() => {
        result.current.forceCleanup("non-existent");
      });

      expect(result.current.threads).toBe(initialState);
      expect(result.current.threads.size).toBe(0);
    });
  });

  describe("performance and state consistency", () => {
    it("should handle rapid sequential updates", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      // Perform rapid updates
      act(() => {
        for (let i = 1; i <= 100; i++) {
          result.current.updateProgress(
            { step: (i % 10) + 1, subStep: i, message: `Update ${i}` },
            MOCK_THREAD_ID
          );
        }
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.lastUpdate).toBeGreaterThan(0);
      expect(threadState?.stepDetails).toBeDefined();
    });

    it("should maintain state consistency during concurrent operations", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
        result.current.startProcess(MOCK_THREAD_ID_2);
      });

      // Perform concurrent operations
      act(() => {
        result.current.updateProgress({ step: 1 }, MOCK_THREAD_ID);
        result.current.updateProgress({ step: 2 }, MOCK_THREAD_ID_2);
        result.current.setError(MOCK_THREAD_ID, "Error 1");
        result.current.cancelProcess(MOCK_THREAD_ID_2);
      });

      expect(result.current.threads.get(MOCK_THREAD_ID)?.error).toBe("Error 1");
      expect(result.current.threads.get(MOCK_THREAD_ID_2)?.stepStatus).toBe(
        "error"
      );
    });

    it("should handle large number of sub-tasks efficiently", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      const startTime = performance.now();

      // Add many sub-tasks
      act(() => {
        for (let i = 1; i <= 1000; i++) {
          result.current.updateProgress(
            { step: 1, subStep: i, message: `Sub-task ${i}` },
            MOCK_THREAD_ID
          );
        }
      });

      const endTime = performance.now();
      const duration = endTime - startTime;

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.stepDetails[0].subTasks).toHaveLength(1000);
      expect(duration).toBeLessThan(1000); // Should complete within 1 second
    });
  });

  describe("TypeScript type safety and interfaces", () => {
    it("should handle all StepStatus values correctly", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      const statusValues: Array<
        "pending" | "starting" | "in_progress" | "complete" | "error"
      > = ["pending", "starting", "in_progress", "complete", "error"];

      statusValues.forEach((status, index) => {
        act(() => {
          result.current.updateProgress(
            { step: index + 1, status, message: `Status: ${status}` },
            MOCK_THREAD_ID
          );
        });
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.stepDetails).toHaveLength(5);
      statusValues.forEach((status, index) => {
        expect(threadState?.stepDetails[index].status).toBe(status);
      });
    });

    it("should maintain proper interface structure for ThreadState", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID, 5, "test-flow");
      });

      const threadState = result.current.getThreadState(MOCK_THREAD_ID);

      // Verify all required ThreadState properties exist
      expect(threadState).toHaveProperty("isActive");
      expect(threadState).toHaveProperty("currentStep");
      expect(threadState).toHaveProperty("totalSteps");
      expect(threadState).toHaveProperty("startTime");
      expect(threadState).toHaveProperty("flowType");
      expect(threadState).toHaveProperty("currentSubStep");
      expect(threadState).toHaveProperty("totalSubSteps");
      expect(threadState).toHaveProperty("stepStatus");
      expect(threadState).toHaveProperty("stepMessage");
      expect(threadState).toHaveProperty("stepDetails");
      expect(threadState).toHaveProperty("abortController");
      expect(threadState).toHaveProperty("error");
      expect(threadState).toHaveProperty("isCompleted");
      expect(threadState).toHaveProperty("completionTime");

      // Verify types
      expect(typeof threadState?.isActive).toBe("boolean");
      expect(typeof threadState?.currentStep).toBe("number");
      expect(typeof threadState?.totalSteps).toBe("number");
      expect(typeof threadState?.startTime).toBe("number");
      expect(typeof threadState?.flowType).toBe("string");
      expect(threadState?.abortController).toBeInstanceOf(AbortController);
      expect(Array.isArray(threadState?.stepDetails)).toBe(true);
    });

    it("should handle ProgressEvent interface properties correctly", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      // Test all optional ProgressEvent properties
      const fullProgressEvent = {
        step: 1,
        subStep: 1,
        status: "in_progress" as const,
        message: "Test message",
        label: "Test label",
        total: 100,
        progress: 50,
        error: new Error("Test error"),
      };

      act(() => {
        result.current.updateProgress(fullProgressEvent, MOCK_THREAD_ID);
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState?.stepDetails[0].expectedTotal).toBe(100);
      expect(threadState?.stepDetails[0].subTasks[0].message).toBe(
        "Test message"
      );
      expect(threadState?.stepDetails[0].subTasks[0].label).toBe("Test label");
    });
  });

  describe("auto-cleanup interval", () => {
    it("should have auto-cleanup interval set up", () => {
      // Since the setInterval is called at module load, we can't easily test it
      // but we can verify the cleanupStaleProcesses function exists and works
      const store = useProgressStore.getState();
      expect(typeof store.cleanupStaleProcesses).toBe("function");
    });

    it("should call cleanupStaleProcesses in interval", () => {
      // Test the function that would be called by the interval
      renderHook(() => useProgressStore());

      // Mock the state getter to simulate the interval call
      const cleanupSpy = jest.spyOn(
        useProgressStore.getState(),
        "cleanupStaleProcesses"
      );

      // Call the function that the interval would call
      act(() => {
        useProgressStore.getState().cleanupStaleProcesses();
      });

      expect(cleanupSpy).toHaveBeenCalled();

      cleanupSpy.mockRestore();
    });
  });

  describe("store state structure", () => {
    it("should have correct initial store structure", () => {
      const store = useProgressStore.getState();

      expect(store).toHaveProperty("threads");
      expect(store.threads).toBeInstanceOf(Map);
      expect(store).toHaveProperty("startProcess");
      expect(store).toHaveProperty("updateProgress");
      expect(store).toHaveProperty("finishProcess");
      expect(store).toHaveProperty("cancelProcess");
      expect(store).toHaveProperty("getAbortController");
      expect(store).toHaveProperty("getThreadState");
      expect(store).toHaveProperty("setError");
      expect(store).toHaveProperty("clearError");
      expect(store).toHaveProperty("cleanupStaleProcesses");
      expect(store).toHaveProperty("forceCleanup");
      expect(store).toHaveProperty("clearStaleProgress");

      // Verify all methods are functions
      expect(typeof store.startProcess).toBe("function");
      expect(typeof store.updateProgress).toBe("function");
      expect(typeof store.finishProcess).toBe("function");
      expect(typeof store.cancelProcess).toBe("function");
      expect(typeof store.getAbortController).toBe("function");
      expect(typeof store.getThreadState).toBe("function");
      expect(typeof store.setError).toBe("function");
      expect(typeof store.clearError).toBe("function");
      expect(typeof store.cleanupStaleProcesses).toBe("function");
      expect(typeof store.forceCleanup).toBe("function");
      expect(typeof store.clearStaleProgress).toBe("function");
    });
  });
});
