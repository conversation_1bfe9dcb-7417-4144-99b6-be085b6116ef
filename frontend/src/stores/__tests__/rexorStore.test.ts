import { act } from "react";
import useRexorStore from "../rexorStore";
import { writeArticleTransaction } from "@/services/rexorService";
import { getLatestEstimationForThread } from "@/stores/estimationStore";

jest.mock("@/services/rexorService");
jest.mock("@/stores/estimationStore");

jest.mock("i18next", () => ({
  t: (key: string, options?: { hours?: number; [key: string]: unknown }) => {
    if (key === "rexor.estimated-manual-time") {
      return ` * Estimated manual time: ${options?.hours} hours`;
    }
    return key; // Return the key itself for other translations
  },
}));

describe("rexorStore - writeNewArticleTransaction", () => {
  const initialStoreState = useRexorStore.getState();

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
    // Reset store to initial state and set a mock token for auth-dependent actions
    act(() => {
      useRexorStore.setState(
        { ...initialStoreState, token: "test-token" },
        true
      );
    });
  });

  it("should append estimation to invoice text if totalHours is provided", async () => {
    const transactionData = { InvoiceText: "Initial text" };
    const totalHours = 5;
    (writeArticleTransaction as jest.Mock).mockResolvedValue({
      UID: "txn-123",
    });

    await act(async () => {
      await useRexorStore
        .getState()
        .writeNewArticleTransaction(transactionData, totalHours);
    });

    expect(writeArticleTransaction).toHaveBeenCalledWith(
      expect.objectContaining({
        InvoiceText: expect.stringContaining(
          `* Estimated manual time: ${totalHours} hours`
        ),
      }),
      "test-token" // Action should now be able to pull this from state
    );
  });

  it("should use original invoice text when totalHours is null", async () => {
    const transactionData = { InvoiceText: "Original text" };
    (writeArticleTransaction as jest.Mock).mockResolvedValue({
      UID: "txn-123",
    });

    await act(async () => {
      await useRexorStore
        .getState()
        .writeNewArticleTransaction(transactionData, null);
    });

    expect(writeArticleTransaction).toHaveBeenCalledWith(
      expect.objectContaining({
        InvoiceText: "Original text",
      }),
      "test-token"
    );
    expect(getLatestEstimationForThread).not.toHaveBeenCalled();
  });

  it("should not add estimation text if totalHours is 0 or less", async () => {
    const transactionData = { InvoiceText: "No estimation needed" };
    (writeArticleTransaction as jest.Mock).mockResolvedValue({
      UID: "txn-123",
    });

    await act(async () => {
      await useRexorStore
        .getState()
        .writeNewArticleTransaction(transactionData, 0);
    });

    expect(writeArticleTransaction).toHaveBeenCalledWith(
      expect.objectContaining({
        InvoiceText: "No estimation needed",
      }),
      "test-token"
    );
  });

  it("should update the store with the new transaction UID on success", async () => {
    const transactionData = { InvoiceText: "Finalizing report" };
    const mockResponse = { UID: "new-txn-456" };
    (writeArticleTransaction as jest.Mock).mockResolvedValue(mockResponse);

    expect(useRexorStore.getState().transactionUID).toBeNull();

    await act(async () => {
      await useRexorStore
        .getState()
        .writeNewArticleTransaction(transactionData, 2);
    });

    expect(useRexorStore.getState().transactionUID).toBe(mockResponse.UID);
  });

  it("should handle auth errors by showing login modal", async () => {
    const authError = new Error("Authentication failed") as Error & {
      status: number;
    };
    authError.status = 401;
    (writeArticleTransaction as jest.Mock).mockRejectedValue(authError);
    const setShowLoginModal = jest.spyOn(
      useRexorStore.getState(),
      "setShowLoginModal"
    );

    // Set a token to ensure the action gets past the initial check
    act(() => {
      useRexorStore.setState({ token: "test-token-to-fail" });
    });

    await act(async () => {
      await useRexorStore
        .getState()
        .writeNewArticleTransaction({ InvoiceText: "test" }, 1);
    });

    expect(setShowLoginModal).toHaveBeenCalledWith(true);
    setShowLoginModal.mockRestore();
  });
});
