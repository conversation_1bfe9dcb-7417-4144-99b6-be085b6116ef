import { act, waitFor } from "@testing-library/react";
import useSettingsStore from "../settingsStore";

describe("settingsStore", () => {
  beforeEach(() => {
    // Reset store state before each test
    useSettingsStore.setState({
      values: {},
      lastFetched: {},
    });
  });

  afterEach(() => {
    // Clear all timers to prevent memory leaks from debounced operations
    jest.clearAllTimers();
    jest.useRealTimers();
  });

  describe("setMultipleSettings", () => {
    it("should update multiple settings at once", async () => {
      const settings = {
        MultiUserMode: true,
        language: "en",
        palette: "dark",
        appName: "TestApp",
      };

      act(() => {
        useSettingsStore.getState().setMultipleSettings(settings);
      });

      // Wait for debounced update to complete (300ms debounce + buffer)
      await waitFor(
        () => {
          const state = useSettingsStore.getState();
          expect(state.values.MultiUserMode).toBe(true);
          expect(state.values.language).toBe("en");
          expect(state.values["color-palette"]).toBe("dark");
          expect(state.values.customAppName).toBe("TestApp");
        },
        { timeout: 500 }
      );

      // Check lastFetched timestamps
      const state = useSettingsStore.getState();
      const now = Date.now();
      expect(state.lastFetched.MultiUserMode).toBeGreaterThan(now - 1000);
      expect(state.lastFetched.language).toBeGreaterThan(now - 1000);
    });

    it("should handle empty settings object", () => {
      const initialState = useSettingsStore.getState();

      act(() => {
        useSettingsStore.getState().setMultipleSettings({});
      });

      expect(useSettingsStore.getState()).toEqual(initialState);
    });

    it("should map setting names correctly", async () => {
      act(() => {
        useSettingsStore.getState().setMultipleSettings({
          palette: "light",
          appName: "MyApp",
        });
      });

      await waitFor(
        () => {
          const state = useSettingsStore.getState();
          expect(state.values["color-palette"]).toBe("light");
          expect(state.values.customAppName).toBe("MyApp");
        },
        { timeout: 500 }
      );
    });
  });

  describe("setTabNames", () => {
    it("should update all tab names", () => {
      const tabNames = {
        tabName1: "Legal Q&A",
        tabName2: "Document Drafting",
        tabName3: "Custom Tab",
      };

      act(() => {
        useSettingsStore.getState().setTabNames(tabNames);
      });

      const state = useSettingsStore.getState();
      expect(state.values.tabName1).toBe("Legal Q&A");
      expect(state.values.tabName2).toBe("Document Drafting");
      expect(state.values.tabName3).toBe("Custom Tab");

      // Check lastFetched
      const now = Date.now();
      expect(state.lastFetched.tabName1).toBeGreaterThan(now - 1000);
      expect(state.lastFetched.tabName2).toBeGreaterThan(now - 1000);
      expect(state.lastFetched.tabName3).toBeGreaterThan(now - 1000);
    });

    it("should handle partial tab names", () => {
      act(() => {
        useSettingsStore.getState().setTabNames({
          tabName1: "Only First Tab",
        });
      });

      const state = useSettingsStore.getState();
      expect(state.values.tabName1).toBe("Only First Tab");
      expect(state.values.tabName2).toBeUndefined();
    });
  });

  describe("integration with existing methods", () => {
    it("should work alongside setSetting", async () => {
      // First set multiple settings
      act(() => {
        useSettingsStore.getState().setMultipleSettings({
          MultiUserMode: true,
          language: "en",
        });
      });

      // Wait for debounced update
      await waitFor(
        () => {
          const state = useSettingsStore.getState();
          expect(state.values.MultiUserMode).toBe(true);
          expect(state.values.language).toBe("en");
        },
        { timeout: 500 }
      );

      // Then update single setting
      act(() => {
        useSettingsStore.getState().setSetting("language", "fr");
      });

      const state = useSettingsStore.getState();
      expect(state.values.MultiUserMode).toBe(true);
      expect(state.values.language).toBe("fr");
    });

    it("should preserve existing settings when updating", async () => {
      // Set initial settings
      act(() => {
        useSettingsStore.getState().setSetting("existingSetting", "value");
      });

      // Update with new settings
      act(() => {
        useSettingsStore.getState().setMultipleSettings({
          MultiUserMode: true,
          language: "en",
        });
      });

      await waitFor(
        () => {
          const state = useSettingsStore.getState();
          expect(state.values.existingSetting).toBe("value");
          expect(state.values.MultiUserMode).toBe(true);
          expect(state.values.language).toBe("en");
        },
        { timeout: 500 }
      );
    });
  });
});
