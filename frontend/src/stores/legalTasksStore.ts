import { create } from "zustand";
import System from "@/models/system";

interface LegalTask {
  id: string | number;
  name: string;
  sub_category?: string;
  subCategory?: string;
  description?: string;
  legalPrompt?: string;
  legalTaskType?: string;
}

interface CreateLegalTaskData {
  name: string;
  subCategory?: string;
  description?: string;
  legalPrompt?: string;
  legalTaskType?: string;
}

interface UpdateLegalTaskData {
  id: string | number;
  subCategory?: string;
  description?: string;
  legalPrompt?: string;
  legalTaskType?: string;
}

interface LegalTasksState {
  legalTasks: LegalTask[];
  loading: boolean;
  error: string | null;
  fetchLegalTasks: () => Promise<LegalTask[]>;
  createLegalTask: (
    data: CreateLegalTaskData,
    onSuccess?: (data: any) => void
  ) => Promise<{ success: boolean; data?: any; error?: string }>;
  updateLegalTask: (
    data: UpdateLegalTaskData,
    onSuccess?: (data: any) => void
  ) => Promise<{ success: boolean; data?: any; error?: string }>;
  deleteLegalTask: (
    id: string | number,
    onSuccess?: () => void
  ) => Promise<{ success: boolean; error?: string }>;
  getLegalTaskById: (id: string | number) => LegalTask | null;
  getLegalTasksByCategory: (category: string) => LegalTask[];
  getUniqueCategories: () => string[];
  getSubcategoriesForCategory: (category: string) => string[];
  reset: () => void;
}

/**
 * Centralized store for managing legal tasks
 * This store handles all operations related to legal tasks including:
 * - Fetching all legal tasks
 * - Creating new legal tasks
 * - Updating existing legal tasks
 * - Deleting legal tasks
 */
const useLegalTasksStore = create<LegalTasksState>((set, get) => ({
  // State
  legalTasks: [],
  loading: false,
  error: null,

  // Actions
  fetchLegalTasks: async () => {
    set({ loading: true, error: null });
    try {
      const response = await System.fetchLegalTasks();
      if (response.success) {
        set({ legalTasks: response.data, loading: false });
        return response.data;
      } else {
        set({ error: response.error, loading: false });
        return [];
      }
    } catch (error: any) {
      set({ error: error.message, loading: false });
      return [];
    }
  },

  createLegalTask: async (
    { name, subCategory, description, legalPrompt, legalTaskType },
    onSuccess
  ) => {
    set({ loading: true, error: null });
    try {
      const result = await System.submitLegalTask({
        name,
        subCategory: subCategory || "",
        description: description || "",
        legalPrompt: legalPrompt || "",
        legalTaskType: legalTaskType || "",
      });

      if (result.success) {
        // Refresh the legal tasks list
        await get().fetchLegalTasks();
        set({ loading: false });

        if (typeof onSuccess === "function") {
          onSuccess(result.data);
        }

        return { success: true, data: result.data };
      } else {
        set({ error: result.error, loading: false });
        return { success: false, error: result.error };
      }
    } catch (error: any) {
      set({ error: error.message, loading: false });
      return { success: false, error: error.message };
    }
  },

  updateLegalTask: async (
    { id, subCategory, description, legalPrompt, legalTaskType },
    onSuccess
  ) => {
    set({ loading: true, error: null });
    try {
      const result = await System.updateLegalTask({
        id,
        subCategory,
        description,
        legalPrompt,
        legalTaskType,
      });

      if (result.success) {
        // Refresh the legal tasks list
        await get().fetchLegalTasks();
        set({ loading: false });

        if (typeof onSuccess === "function") {
          onSuccess(result.data);
        }

        return { success: true, data: result.data };
      } else {
        set({ error: result.error, loading: false });
        return { success: false, error: result.error };
      }
    } catch (error: any) {
      set({ error: error.message, loading: false });
      return { success: false, error: error.message };
    }
  },

  deleteLegalTask: async (id, onSuccess) => {
    set({ loading: true, error: null });
    try {
      const result = await System.deleteCategory(id as string);

      if (result.success) {
        // Update the local state by removing the deleted task
        const updatedTasks = get().legalTasks.filter((task) => task.id !== id);
        set({ legalTasks: updatedTasks, loading: false });

        if (typeof onSuccess === "function") {
          onSuccess();
        }

        return { success: true };
      } else {
        set({ error: result.error, loading: false });
        return { success: false, error: result.error };
      }
    } catch (error: any) {
      set({ error: error.message, loading: false });
      return { success: false, error: error.message };
    }
  },

  // Helper function to get a legal task by ID
  getLegalTaskById: (id) => {
    return get().legalTasks.find((task) => task.id === id) || null;
  },

  // Helper function to get legal tasks by category
  getLegalTasksByCategory: (category) => {
    return get().legalTasks.filter((task) => task.name === category);
  },

  // Helper function to get all unique categories
  getUniqueCategories: () => {
    const categories = get().legalTasks.map((task) => task.name);
    return [...new Set(categories)].sort();
  },

  // Helper function to get all subcategories for a given category
  getSubcategoriesForCategory: (category) => {
    const tasks = get().getLegalTasksByCategory(category);
    const subcategories = tasks
      .map((task) => task.sub_category)
      .filter(Boolean) as string[];
    return [...new Set(subcategories)].sort();
  },

  // Reset the store state
  reset: () => {
    set({
      legalTasks: [],
      loading: false,
      error: null,
    });
  },
}));

export default useLegalTasksStore;
