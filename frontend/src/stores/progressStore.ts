import { create } from "zustand";

type StepStatus = "pending" | "starting" | "in_progress" | "complete" | "error";

interface SubTask {
  subStep: number;
  status: StepStatus;
  message: string | null;
  label: string | null;
  startTime: number;
}

interface StepDetail {
  step: number;
  status: StepStatus;
  message: string | null;
  subTasks: SubTask[];
  expectedTotal: number | null;
  startTime: number;
}

interface ThreadState {
  isActive: boolean;
  currentStep: number;
  totalSteps: number;
  startTime: number | null;
  flowType: string | null;
  currentSubStep: number | null;
  totalSubSteps: number | null;
  stepStatus: StepStatus;
  stepMessage: string | null;
  stepDetails: StepDetail[];
  abortController: AbortController;
  error: string | null;
  isCompleted: boolean;
  completionTime: number | null;
  lastUpdate?: number;
}

interface ProgressEvent {
  step: number;
  subStep?: number | null;
  status?: StepStatus;
  message?: string | null;
  label?: string | null;
  total?: number;
  progress?: number;
  error?: unknown;
}

interface ProgressState {
  threads: Map<string, ThreadState>;
  startProcess: (
    threadSlug: string,
    totalSteps?: number,
    flowType?: string | null
  ) => void;
  updateProgress: (event: ProgressEvent, threadSlug: string) => void;
  finishProcess: (threadSlug: string) => void;
  cancelProcess: (threadSlug: string) => void;
  getAbortController: (threadSlug: string) => AbortController | null;
  getThreadState: (threadSlug: string) => ThreadState | null;
  setError: (threadSlug: string, errorMessage: string) => void;
  clearError: (threadSlug: string) => void;
  cleanupStaleProcesses: (maxAgeMinutes?: number) => void;
  forceCleanup: (threadSlug: string) => void;
  clearStaleProgress: (threadSlug: string) => void;
}

const createThreadState = (): ThreadState => ({
  isActive: false,
  currentStep: 1,
  totalSteps: 7,
  startTime: null,
  flowType: null,
  currentSubStep: null,
  totalSubSteps: null,
  stepStatus: "pending",
  stepMessage: null,
  stepDetails: [],
  abortController: new AbortController(),
  error: null,
  isCompleted: false,
  completionTime: null,
});

const useProgressStore = create<ProgressState>((set, get) => ({
  threads: new Map(),

  startProcess: (threadSlug, totalSteps = 7, flowType = null) => {
    if (!threadSlug || typeof threadSlug !== "string") {
      return;
    }

    set((state) => {
      if (state.threads.has(threadSlug)) {
        const existingState = state.threads.get(threadSlug);
        if (existingState?.abortController) {
          existingState.abortController.abort();
        }
      }

      const newThreadState = {
        ...createThreadState(),
        isActive: true,
        totalSteps: Math.max(1, totalSteps),
        startTime: Date.now(),
        flowType,
        stepDetails: [],
      };

      const newThreads = new Map(state.threads);
      newThreads.set(threadSlug, newThreadState);

      return { threads: newThreads };
    });
  },

  updateProgress: (event, threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string" || !event) {
      return;
    }

    set((state) => {
      const newThreads = new Map(state.threads);
      const threadState = newThreads.get(threadSlug);

      if (!threadState || !threadState.isActive) {
        return state;
      }

      const { step, subStep, status, message, label } = event;

      if (typeof step !== "number") {
        return state;
      }

      const updatedStepDetails = [...(threadState.stepDetails || [])];

      let stepDetailIndex = updatedStepDetails.findIndex(
        (detail) => detail.step === step
      );
      if (stepDetailIndex === -1) {
        updatedStepDetails.push({
          step,
          status: status || "pending",
          message: message || null,
          subTasks: [],
          expectedTotal: null,
          startTime: Date.now(),
        });
        stepDetailIndex = updatedStepDetails.length - 1;
      } else {
        updatedStepDetails[stepDetailIndex] = {
          ...updatedStepDetails[stepDetailIndex],
          status: status || updatedStepDetails[stepDetailIndex].status,
          message: message || updatedStepDetails[stepDetailIndex].message,
        };
      }

      if (event.total && typeof event.total === "number") {
        const stepDetail = updatedStepDetails[stepDetailIndex];
        stepDetail.expectedTotal = event.total;
      }

      if (subStep !== undefined && subStep !== null) {
        const stepDetail = updatedStepDetails[stepDetailIndex];

        const subTaskIndex = stepDetail.subTasks.findIndex(
          (subTask) => subTask.subStep === subStep
        );

        if (subTaskIndex === -1) {
          stepDetail.subTasks.push({
            subStep,
            status: status || "in_progress",
            message: message || null,
            label: label || null,
            startTime: Date.now(),
          });
          stepDetail.subTasks.sort((a, b) => a.subStep - b.subStep);
        } else {
          let newStatus = status;

          if (!newStatus) {
            const currentStatus = stepDetail.subTasks[subTaskIndex].status;
            const currentSubTask = stepDetail.subTasks[subTaskIndex];

            if (event.progress === 100) {
              newStatus = "complete";
            } else if (event.progress === -2 || event.error) {
              newStatus = "error";
            } else if (currentStatus === "pending") {
              newStatus = "in_progress";
            } else if (
              currentStatus === "in_progress" &&
              currentSubTask.message &&
              currentSubTask.label &&
              event.label &&
              !event.message &&
              !event.progress
            ) {
              newStatus = "complete";
            } else {
              newStatus = currentStatus;
            }
          }

          stepDetail.subTasks[subTaskIndex] = {
            ...stepDetail.subTasks[subTaskIndex],
            status: newStatus,
            message: message || stepDetail.subTasks[subTaskIndex].message,
            label: label || stepDetail.subTasks[subTaskIndex].label,
            startTime:
              stepDetail.subTasks[subTaskIndex].startTime || Date.now(),
          };
        }
      }

      newThreads.set(threadSlug, {
        ...threadState,
        currentStep: step || threadState.currentStep,
        currentSubStep: subStep ?? null,
        stepStatus: status || threadState.stepStatus,
        stepMessage: message || threadState.stepMessage,
        stepDetails: updatedStepDetails,
        lastUpdate: Date.now(),
      });

      return { threads: newThreads };
    });
  },

  finishProcess: (threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string") return;

    set((state) => {
      const newThreads = new Map(state.threads);
      const threadState = newThreads.get(threadSlug);

      if (!threadState) return state;

      newThreads.set(threadSlug, {
        ...threadState,
        isActive: false,
        isCompleted: true,
        completionTime: Date.now(),
        stepStatus: "complete",
        currentStep: threadState.totalSteps,
      });

      return { threads: newThreads };
    });

    setTimeout(() => {
      set((state) => {
        const newThreads = new Map(state.threads);
        const threadState = newThreads.get(threadSlug);

        if (threadState?.isCompleted) {
          newThreads.delete(threadSlug);
          return { threads: newThreads };
        }
        return state;
      });
    }, 5000);
  },

  cancelProcess: (threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string") return;

    set((state) => {
      const newThreads = new Map(state.threads);
      const threadState = newThreads.get(threadSlug);

      if (!threadState) return state;

      if (threadState.abortController) {
        threadState.abortController.abort();
      }

      newThreads.set(threadSlug, {
        ...threadState,
        isActive: false,
        stepStatus: "error",
      });

      return { threads: newThreads };
    });
  },

  getAbortController: (threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string") return null;

    const state = get();
    const threadState = state.threads.get(threadSlug);
    return threadState?.abortController || null;
  },

  getThreadState: (threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string") return null;

    const state = get();
    return state.threads.get(threadSlug) || null;
  },

  setError: (threadSlug, errorMessage) => {
    if (!threadSlug || typeof threadSlug !== "string") return;

    set((state) => {
      const newThreads = new Map(state.threads);
      const threadState = newThreads.get(threadSlug);

      if (!threadState) return state;

      newThreads.set(threadSlug, {
        ...threadState,
        isActive: false,
        stepStatus: "error",
        error: errorMessage,
      });

      return { threads: newThreads };
    });
  },

  clearError: (threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string") return;

    set((state) => {
      const newThreads = new Map(state.threads);
      if (!newThreads.has(threadSlug)) return state;

      newThreads.delete(threadSlug);
      return { threads: newThreads };
    });
  },

  cleanupStaleProcesses: (maxAgeMinutes = 10) => {
    const now = Date.now();
    const maxAge = maxAgeMinutes * 60 * 1000;

    set((state) => {
      const newThreads = new Map(state.threads);
      let hasChanges = false;

      for (const [threadSlug, threadState] of Array.from(
        newThreads.entries()
      )) {
        const lastActivityTime =
          threadState.lastUpdate || threadState.startTime || now;
        const age = now - lastActivityTime;

        if (age > maxAge) {
          newThreads.delete(threadSlug);
          hasChanges = true;
        }
      }

      return hasChanges ? { threads: newThreads } : state;
    });
  },

  forceCleanup: (threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string") return;

    set((state) => {
      const newThreads = new Map(state.threads);
      if (newThreads.has(threadSlug)) {
        const threadState = newThreads.get(threadSlug);
        if (threadState?.abortController) {
          threadState.abortController.abort();
        }
        newThreads.delete(threadSlug);
        return { threads: newThreads };
      }
      return state;
    });
  },

  clearStaleProgress: (threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string") return;

    set((state) => {
      const newThreads = new Map(state.threads);
      const threadState = newThreads.get(threadSlug);

      if (threadState) {
        if (threadState.abortController) {
          threadState.abortController.abort();
        }

        newThreads.set(threadSlug, {
          ...createThreadState(),
          abortController: new AbortController(),
        });

        return { threads: newThreads };
      }

      return state;
    });
  },
}));

// Auto-cleanup stale processes every 5 minutes
setInterval(
  () => {
    useProgressStore.getState().cleanupStaleProcesses();
  },
  5 * 60 * 1000
);

export default useProgressStore;
