import { create } from "zustand";
import { persist } from "zustand/middleware";
import {
  loginToRexor,
  registerProject,
  writeArticleTransaction,
  getInvoiceStatus,
} from "@/services/rexorService";
import showToast from "@/utils/toast";
import {
  hashCredentials,
  verifyCredentials,
  getUsernameFromHash,
} from "@/utils/encryption";
import { t } from "i18next";

interface RexorError extends Error {
  status?: number;
}

interface ProjectData {
  UID?: string;
  [key: string]: any;
}

interface TransactionData {
  InvoiceText?: string;
  [key: string]: any;
}

interface RexorState {
  // Authentication state
  token: string | null;
  isLoggedIn: boolean;
  loading: boolean;
  error: string | null;
  actionToRetry: (() => Promise<void>) | null;
  credentialHash: string | null;

  // Project and transaction state
  registeredProject: ProjectData | null;
  transactionUID: string | null;
  activeReference: string | null;
  showLoginModal: boolean;
  lastProcessedChatId: string | null;
  retryCount: number;

  // Actions
  setShowLoginModal: (value: boolean) => void;
  isAuthorizationError: (error: any) => boolean;
  login: (
    username: string,
    password: string,
    saveCredentials?: boolean
  ) => Promise<string | null>;
  logout: () => void;
  handleAuthError: (actionToRetry?: (() => Promise<void>) | null) => void;
  checkLoginStatus: () => void;
  registerNewProject: (projectData: ProjectData) => Promise<any>;
  writeNewArticleTransaction: (
    transactionData: TransactionData,
    totalHours?: number | null
  ) => Promise<any>;
  writeOrUpdateTransaction: (
    totalHours?: number | null,
    chatId?: string | null
  ) => Promise<any>;
  setRegisteredProject: (project: ProjectData) => void;
  setActiveReference: (reference: string) => void;
  clearActiveReference: () => void;
  clearError: () => void;
  verifyStoredCredentials: (
    username: string,
    password: string
  ) => Promise<boolean>;
  getSavedUsername: () => string | null;
}

const useRexorStore = create<RexorState>()(
  persist<RexorState>(
    (set, get) => ({
      // Authentication state
      token: null,
      isLoggedIn: false,
      loading: false,
      error: null,
      actionToRetry: null,

      credentialHash: null,

      // Project and transaction state
      registeredProject: null,
      transactionUID: null,
      activeReference: null,
      showLoginModal: false,
      lastProcessedChatId: null,
      retryCount: 0,

      setShowLoginModal: (value) => set({ showLoginModal: value }),

      isAuthorizationError: (error) => {
        const errorMsg = error.message || "";

        // Check for HTTP status codes
        if (error.status === 401 || error.status === 403) {
          return true;
        }

        // Check for status codes in error message
        if (errorMsg.includes("401") || errorMsg.includes("403")) {
          return true;
        }

        // Check for authorization-related error messages
        if (
          errorMsg.includes("Unauthorized") ||
          errorMsg.includes("Authorization has been denied") ||
          errorMsg.includes("Token might be invalid") ||
          errorMsg.includes("Authentication failed")
        ) {
          return true;
        }

        return false;
      },

      login: async (username, password, saveCredentials = false) => {
        set({ loading: true, error: null });

        try {
          const data = await loginToRexor(username, password);

          if (data.success && data.access_token) {
            let credentialHash: string | null = null;

            if (saveCredentials) {
              try {
                credentialHash = await hashCredentials(username, password);
              } catch {
                credentialHash = null;
              }
            }

            set({
              token: data.access_token,
              isLoggedIn: true,
              credentialHash,
              loading: false,
              error: null,
            });

            const { actionToRetry } = get();
            if (actionToRetry) {
              set({ actionToRetry: null });
              get().setShowLoginModal(false);
              await actionToRetry();
            }

            return data.access_token;
          } else {
            set({
              error: "No token received in response",
              loading: false,
              isLoggedIn: false,
            });
            return null;
          }
        } catch (err: any) {
          set({ error: err.message, loading: false, isLoggedIn: false });
          return null;
        }
      },

      logout: () => {
        set({
          token: null,
          isLoggedIn: false,
          credentialHash: null,
          registeredProject: null,
          transactionUID: null,
          activeReference: null,
          error: null,
          actionToRetry: null,
          lastProcessedChatId: null,
          retryCount: 0,
        });
      },

      handleAuthError: (actionToRetry = null) => {
        set({ token: null, isLoggedIn: false, actionToRetry });
        showToast(t("rexor.auth-failed"), "error");
        get().setShowLoginModal(true);
      },

      checkLoginStatus: () => {
        const state = get();
        set({
          isLoggedIn: !!state.token,
        });
      },

      registerNewProject: async (projectData) => {
        const { isAuthorizationError, handleAuthError } = get();
        try {
          const { token } = get();
          if (!token) {
            throw new Error("User is not logged in");
          }
          const projectResponse = await registerProject(projectData, token);

          set({
            registeredProject: projectResponse,
            error: null,
          });

          return projectResponse;
        } catch (err: any) {
          set({ error: err.message });
          if (isAuthorizationError(err)) {
            handleAuthError(() => get().registerNewProject(projectData));
            const authError: RexorError = new Error("Authentication failed");
            authError.status = 401;
            throw authError;
          }
          throw err;
        }
      },

      writeNewArticleTransaction: async (
        transactionData,
        totalHours = null
      ) => {
        const { isAuthorizationError, handleAuthError } = get();
        try {
          const { token } = get();
          if (!token) throw new Error("User is not logged in");
          const newTransactionData = { ...transactionData };
          let invoiceText =
            newTransactionData.InvoiceText || t("rexor.invoice-text");

          if (totalHours && totalHours > 0) {
            const estimationText = t("rexor.estimated-manual-time", {
              hours: totalHours,
            });

            invoiceText += estimationText;
          }

          newTransactionData.InvoiceText = invoiceText;
          const transactionResponse = await writeArticleTransaction(
            newTransactionData,
            token
          );

          set({
            transactionUID: transactionResponse.UID,
            registeredProject: {
              ...get().registeredProject,
              UID: transactionResponse.UID,
            } as ProjectData,
            error: null,
          });

          return transactionResponse;
        } catch (err: any) {
          set({ error: err.message });

          if (isAuthorizationError(err)) {
            handleAuthError(() =>
              get().writeNewArticleTransaction(transactionData, totalHours)
            );
            return null;
          }

          throw err;
        }
      },

      writeOrUpdateTransaction: async (totalHours = null, chatId = null) => {
        const {
          registeredProject,
          isLoggedIn,
          isAuthorizationError,
          handleAuthError,
        } = get();

        if (!isLoggedIn) {
          get().setShowLoginModal(true);
          return;
        }
        if (!registeredProject) {
          return;
        }

        // Check if we've already processed this chat ID to prevent duplicate increments
        const state = get();
        if (chatId && state.lastProcessedChatId === chatId) {
          return state.transactionUID;
        }

        // Check retry limit to prevent infinite loops
        if (state.retryCount >= 3) {
          set({ retryCount: 0 } as Partial<RexorState>);
          return null;
        }

        let transactionData: TransactionData | undefined;
        try {
          const { token } = get();
          // If no UID, we are creating a new transaction.
          if (!registeredProject.UID) {
            transactionData = { ...registeredProject };
          } else {
            // We are updating an existing transaction.
            const status = await getInvoiceStatus(
              registeredProject.UID,
              token!
            );
            const currentTransaction = Array.isArray(status)
              ? status[0]
              : status;

            if (
              currentTransaction &&
              currentTransaction.InvoiceStatus === "ReadyForInvoiceBasis"
            ) {
              transactionData = {
                ...currentTransaction,
                Number: Number(currentTransaction.Number) + 1,
                InvoicedNumber: Number(currentTransaction.InvoicedNumber) + 1,
              };
            } else {
              // If not ready for update, just use the base project data to create a new one.
              transactionData = { ...registeredProject };
            }
          }
        } catch (error: any) {
          set({ error: error.message });

          if (isAuthorizationError(error)) {
            set(
              (state) =>
                ({ retryCount: state.retryCount + 1 }) as Partial<RexorState>
            );
            handleAuthError(() =>
              get().writeOrUpdateTransaction(totalHours, chatId)
            );
          } else {
            if (chatId) {
              set({
                lastProcessedChatId: chatId,
                retryCount: 0,
              } as Partial<RexorState>);
            }
          }

          return null;
        }

        // If we got here, transactionData is ready.
        // writeNewArticleTransaction will handle its own retries.
        if (!transactionData) {
          set({ error: "Transaction data is not available" });
          return null;
        }

        try {
          const result = await get().writeNewArticleTransaction(
            transactionData,
            totalHours
          );

          // Store the processed chat ID to prevent duplicate processing
          if (chatId && result) {
            set({
              lastProcessedChatId: chatId,
              retryCount: 0,
            } as Partial<RexorState>);
          }

          return result;
        } catch (error: any) {
          // writeNewArticleTransaction already handles authorization errors
          // and returns null for those. This catch is for non-auth errors.
          set({ error: error.message });
          return null;
        }
      },

      setRegisteredProject: (project) => {
        set({ registeredProject: project });
      },

      setActiveReference: (reference) => {
        set({ activeReference: reference });
      },

      clearActiveReference: () => {
        set({ activeReference: null });
      },

      clearError: () => {
        set({ error: null });
      },

      verifyStoredCredentials: async (username, password) => {
        const { credentialHash } = get();
        if (!credentialHash) {
          return false;
        }

        try {
          return await verifyCredentials(username, password, credentialHash);
        } catch {
          set({ credentialHash: null });
          return false;
        }
      },

      getSavedUsername: () => {
        const { credentialHash } = get();
        if (!credentialHash) {
          return null;
        }

        try {
          return getUsernameFromHash(credentialHash);
        } catch {
          set({ credentialHash: null });
          return null;
        }
      },
    }),
    {
      name: "rexor-store",
      partialize: (state) =>
        ({
          token: state.token,
          isLoggedIn: state.isLoggedIn,
          credentialHash: state.credentialHash,
          registeredProject: state.registeredProject,
          transactionUID: state.transactionUID,
          activeReference: state.activeReference,
          lastProcessedChatId: state.lastProcessedChatId,
          retryCount: state.retryCount,
        }) as any,
    }
  )
);

export default useRexorStore;

// Exported selectors for specific state pieces
export const useRexorToken = () => useRexorStore((state) => state.token);
export const useRexorIsLoggedIn = () =>
  useRexorStore((state) => state.isLoggedIn);
export const useRexorLoading = () => useRexorStore((state) => state.loading);
export const useRexorError = () => useRexorStore((state) => state.error);
export const useRexorVerifyStoredCredentials = () =>
  useRexorStore((state) => state.verifyStoredCredentials);
export const useRexorGetSavedUsername = () =>
  useRexorStore((state) => state.getSavedUsername);
export const useRexorRegisteredProject = () =>
  useRexorStore((state) => state.registeredProject);
export const useRexorActiveReference = () =>
  useRexorStore((state) => state.activeReference);

// Exported actions
export const useRexorLogin = () => useRexorStore((state) => state.login);
export const useRexorLogout = () => useRexorStore((state) => state.logout);
export const useRexorCheckLoginStatus = () =>
  useRexorStore((state) => state.checkLoginStatus);
export const useRexorRegisterProject = () =>
  useRexorStore((state) => state.registerNewProject);
export const useRexorWriteOrUpdateTransaction = () =>
  useRexorStore((state) => state.writeOrUpdateTransaction);
export const useRexorSetRegisteredProject = () =>
  useRexorStore((state) => state.setRegisteredProject);
export const useRexorSetActiveReference = () =>
  useRexorStore((state) => state.setActiveReference);
export const useRexorClearActiveReference = () =>
  useRexorStore((state) => state.clearActiveReference);
export const useRexorClearError = () =>
  useRexorStore((state) => state.clearError);
