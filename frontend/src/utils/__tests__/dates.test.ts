/// <reference types="@testing-library/jest-dom" />

// Mock date-fns format function to have predictable outputs
jest.mock("date-fns", () => {
  const mockFormat = jest.fn((date: Date, formatString: string) => {
    // Ensure date is valid
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
      return "invalid-date";
    }

    if (formatString === "d") {
      // For testing, return a predictable day value based on the date
      const day = date.getDate(); // Use getDate() instead of getUTCDate() for local time
      return day.toString();
    }
    if (formatString === "yyyy") {
      // For testing, return a predictable year value
      const year = date.getFullYear(); // Use getFullYear() instead of getUTCFullYear() for local time
      return year.toString();
    }
    if (formatString === "MMMM") {
      // Return English month names for fallback testing
      const months = [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
      ];
      const result = months[date.getMonth()];
      return result;
    }
    return formatString;
  });

  return {
    format: mockFormat,
    __mockFormat: mockFormat, // Export for testing
  };
});

// Import after the mock is set up
import { getLocalizedDate } from "../dates";

describe("dates utility functions", () => {
  describe("getLocalizedDate", () => {
    const mockTranslationFunction = jest.fn();
    const { __mockFormat: mockFormat } = require("date-fns");

    beforeEach(() => {
      mockTranslationFunction.mockClear();
      mockFormat.mockClear();

      // Reset the mock implementation
      mockFormat.mockImplementation((date: Date, formatString: string) => {
        // Ensure date is valid
        if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
          return "invalid-date";
        }

        if (formatString === "d") {
          // For testing, return a predictable day value based on the date
          const day = date.getDate(); // Use getDate() instead of getUTCDate() for local time
          return day.toString();
        }
        if (formatString === "yyyy") {
          // For testing, return a predictable year value
          const year = date.getFullYear(); // Use getFullYear() instead of getUTCFullYear() for local time
          return year.toString();
        }
        if (formatString === "MMMM") {
          // Return English month names for fallback testing
          const months = [
            "January",
            "February",
            "March",
            "April",
            "May",
            "June",
            "July",
            "August",
            "September",
            "October",
            "November",
            "December",
          ];
          const result = months[date.getMonth()];
          return result;
        }
        return formatString;
      });
    });

    describe("valid date inputs", () => {
      it("should format a simple date with localized month", () => {
        const testDate = new Date("2023-01-15T12:00:00.000Z"); // January 15, 2023 UTC
        mockTranslationFunction.mockReturnValue("janvier");

        const result = getLocalizedDate(testDate, mockTranslationFunction);

        expect(result).toBe("15 janvier 2023");
        expect(mockTranslationFunction).toHaveBeenCalledWith("month.1", {
          defaultValue: "January",
        });
      });

      it("should handle single digit days correctly", () => {
        const testDate = new Date(2023, 5, 5); // June 5, 2023
        mockTranslationFunction.mockReturnValue("juin");

        const result = getLocalizedDate(testDate, mockTranslationFunction);

        expect(result).toBe("5 juin 2023");
        expect(mockTranslationFunction).toHaveBeenCalledWith("month.6", {
          defaultValue: "June",
        });
      });

      it("should handle double digit days correctly", () => {
        const testDate = new Date(2023, 11, 25); // December 25, 2023
        mockTranslationFunction.mockReturnValue("décembre");

        const result = getLocalizedDate(testDate, mockTranslationFunction);

        expect(result).toBe("25 décembre 2023");
        expect(mockTranslationFunction).toHaveBeenCalledWith("month.12", {
          defaultValue: "December",
        });
      });

      it("should handle all months correctly", () => {
        const months = [
          { num: 1, name: "January", localized: "janvier" },
          { num: 2, name: "February", localized: "février" },
          { num: 3, name: "March", localized: "mars" },
          { num: 4, name: "April", localized: "avril" },
          { num: 5, name: "May", localized: "mai" },
          { num: 6, name: "June", localized: "juin" },
          { num: 7, name: "July", localized: "juillet" },
          { num: 8, name: "August", localized: "août" },
          { num: 9, name: "September", localized: "septembre" },
          { num: 10, name: "October", localized: "octobre" },
          { num: 11, name: "November", localized: "novembre" },
          { num: 12, name: "December", localized: "décembre" },
        ];

        months.forEach(({ num, name, localized }) => {
          const testDate = new Date(2023, num - 1, 1);
          mockTranslationFunction.mockReturnValue(localized);

          const result = getLocalizedDate(testDate, mockTranslationFunction);

          expect(result).toBe(`1 ${localized} 2023`);
          expect(mockTranslationFunction).toHaveBeenCalledWith(`month.${num}`, {
            defaultValue: name,
          });
          mockTranslationFunction.mockClear();
        });
      });

      it("should fallback to English month name when translation is not available", () => {
        const testDate = new Date(2023, 6, 4); // July 4, 2023
        // Mock translation function to return the defaultValue (fallback behavior)
        mockTranslationFunction.mockImplementation(
          (key, options) => options?.defaultValue || key
        );

        const result = getLocalizedDate(testDate, mockTranslationFunction);

        expect(result).toBe("4 July 2023");
        expect(mockTranslationFunction).toHaveBeenCalledWith("month.7", {
          defaultValue: "July",
        });
      });

      it("should handle leap year dates", () => {
        const testDate = new Date(2024, 1, 29); // February 29, 2024 (leap year)
        mockTranslationFunction.mockReturnValue("février");

        const result = getLocalizedDate(testDate, mockTranslationFunction);

        expect(result).toBe("29 février 2024");
        expect(mockTranslationFunction).toHaveBeenCalledWith("month.2", {
          defaultValue: "February",
        });
      });

      it("should handle different years correctly", () => {
        const years = [1900, 1999, 2000, 2001, 2023, 2024, 2025, 9999];

        years.forEach((year) => {
          const testDate = new Date(year, 0, 1); // January 1st of each year
          mockTranslationFunction.mockReturnValue("janvier");

          const result = getLocalizedDate(testDate, mockTranslationFunction);

          expect(result).toBe(`1 janvier ${year}`);
          mockTranslationFunction.mockClear();
        });
      });

      it("should handle edge case dates", () => {
        // Test first day of year
        const firstDay = new Date(2023, 0, 1);
        mockTranslationFunction.mockReturnValue("janvier");
        expect(getLocalizedDate(firstDay, mockTranslationFunction)).toBe(
          "1 janvier 2023"
        );

        // Test last day of year
        const lastDay = new Date(2023, 11, 31);
        mockTranslationFunction.mockReturnValue("décembre");
        expect(getLocalizedDate(lastDay, mockTranslationFunction)).toBe(
          "31 décembre 2023"
        );

        // Test last day of February in non-leap year
        const febLast = new Date(2023, 1, 28);
        mockTranslationFunction.mockReturnValue("février");
        expect(getLocalizedDate(febLast, mockTranslationFunction)).toBe(
          "28 février 2023"
        );
      });

      it("should handle dates with time components (should ignore time)", () => {
        const testDate = new Date(2023, 5, 15, 14, 30, 45, 123); // June 15, 2023 14:30:45.123
        mockTranslationFunction.mockReturnValue("juin");

        const result = getLocalizedDate(testDate, mockTranslationFunction);

        expect(result).toBe("15 juin 2023");
        expect(mockTranslationFunction).toHaveBeenCalledWith("month.6", {
          defaultValue: "June",
        });
      });
    });

    describe("null and undefined inputs", () => {
      it("should return empty string for null input", () => {
        const result = getLocalizedDate(null, mockTranslationFunction);

        expect(result).toBe("");
        expect(mockTranslationFunction).not.toHaveBeenCalled();
      });

      it("should return empty string for undefined input", () => {
        const result = getLocalizedDate(undefined, mockTranslationFunction);

        expect(result).toBe("");
        expect(mockTranslationFunction).not.toHaveBeenCalled();
      });
    });

    describe("translation function behavior", () => {
      it("should work with translation function that returns the key when no translation found", () => {
        const testDate = new Date(2023, 2, 10); // March 10, 2023
        const keyReturningTranslation = (key: string) => key;

        const result = getLocalizedDate(testDate, keyReturningTranslation);

        expect(result).toBe("10 month.3 2023");
      });

      it("should work with translation function that returns empty string", () => {
        const testDate = new Date(2023, 2, 10); // March 10, 2023
        const emptyTranslation = () => "";

        const result = getLocalizedDate(testDate, emptyTranslation);

        expect(result).toBe("10  2023");
      });

      it("should work with translation function that throws", () => {
        const testDate = new Date(2023, 2, 10); // March 10, 2023
        const throwingTranslation = () => {
          throw new Error("Translation error");
        };

        // The function should not catch translation errors, let them bubble up
        expect(() => getLocalizedDate(testDate, throwingTranslation)).toThrow(
          "Translation error"
        );
      });

      it("should handle translation function with complex options", () => {
        const testDate = new Date(2023, 8, 5); // September 5, 2023
        const complexTranslation = jest.fn(
          (key: string, options?: { defaultValue?: string }) => {
            // Simulate a translation function that uses the defaultValue in a complex way
            return options?.defaultValue?.toUpperCase() || key.toUpperCase();
          }
        );

        const result = getLocalizedDate(testDate, complexTranslation);

        expect(result).toBe("5 SEPTEMBER 2023");
        expect(complexTranslation).toHaveBeenCalledWith("month.9", {
          defaultValue: "September",
        });
      });
    });

    describe("edge cases and error scenarios", () => {
      it("should handle invalid Date objects", () => {
        const invalidDate = new Date("invalid");
        mockTranslationFunction.mockReturnValue("janvier");

        // The function will still try to process invalid dates
        // date-fns format might handle this differently, but our mock will proceed
        const result = getLocalizedDate(invalidDate, mockTranslationFunction);

        // Since we're mocking date-fns, this will depend on how Date methods behave with invalid dates
        expect(typeof result).toBe("string");
      });

      it("should handle extreme dates", () => {
        // Test with very early date (JavaScript Date constructor interprets years 0-99 as 1900-1999)
        const earlyDate = new Date(1901, 0, 1); // January 1, 1901
        mockTranslationFunction.mockReturnValue("janvier");

        const result = getLocalizedDate(earlyDate, mockTranslationFunction);

        expect(result).toBe("1 janvier 1901");

        // Test with very future date
        const futureDate = new Date(9999, 11, 31); // December 31, 9999
        mockTranslationFunction.mockReturnValue("décembre");

        const result2 = getLocalizedDate(futureDate, mockTranslationFunction);

        expect(result2).toBe("31 décembre 9999");
      });

      it("should maintain consistent output format", () => {
        const testDate = new Date(2023, 3, 7); // April 7, 2023
        mockTranslationFunction.mockReturnValue("avril");

        const result1 = getLocalizedDate(testDate, mockTranslationFunction);
        const result2 = getLocalizedDate(testDate, mockTranslationFunction);

        expect(result1).toBe(result2);
        expect(result1).toBe("7 avril 2023");
      });

      it("should handle timezone-independent formatting", () => {
        // Create dates that might be affected by timezone
        const date1 = new Date(2023, 5, 15, 0, 0, 0); // Midnight
        const date2 = new Date(2023, 5, 15, 23, 59, 59); // End of day

        mockTranslationFunction.mockReturnValue("juin");

        const result1 = getLocalizedDate(date1, mockTranslationFunction);
        const result2 = getLocalizedDate(date2, mockTranslationFunction);

        // Both should format to the same date regardless of time
        expect(result1).toBe("15 juin 2023");
        expect(result2).toBe("15 juin 2023");
      });
    });

    describe("performance and consistency", () => {
      it("should handle many calls efficiently", () => {
        const testDate = new Date(2023, 5, 15);
        mockTranslationFunction.mockReturnValue("juin");

        const startTime = Date.now();

        // Call the function many times
        for (let i = 0; i < 1000; i++) {
          getLocalizedDate(testDate, mockTranslationFunction);
        }

        const endTime = Date.now();
        const duration = endTime - startTime;

        // Should complete 1000 operations in reasonable time
        expect(duration).toBeLessThan(1000);
      });

      it("should be consistent across multiple calls with same input", () => {
        const testDate = new Date(2023, 9, 31); // October 31, 2023
        mockTranslationFunction.mockReturnValue("octobre");

        const results = Array.from({ length: 10 }, () =>
          getLocalizedDate(testDate, mockTranslationFunction)
        );

        // All results should be identical
        const firstResult = results[0];
        results.forEach((result) => {
          expect(result).toBe(firstResult);
        });

        expect(firstResult).toBe("31 octobre 2023");
      });

      it("should handle concurrent usage", async () => {
        const dates = Array.from(
          { length: 50 },
          (_, i) => new Date(2023, i % 12, (i % 28) + 1)
        );
        mockTranslationFunction.mockImplementation(
          (key) => `month-${key.split(".")[1]}`
        );

        const promises = dates.map((date) =>
          Promise.resolve(getLocalizedDate(date, mockTranslationFunction))
        );

        const results = await Promise.all(promises);

        expect(results).toHaveLength(50);
        results.forEach((result) => {
          expect(typeof result).toBe("string");
          expect(result).toMatch(/^\d{1,2} month-\d{1,2} 2023$/);
        });
      });
    });

    describe("internationalization scenarios", () => {
      it("should work with different language translations", () => {
        const testDate = new Date(2023, 7, 15); // August 15, 2023

        // Test with different language translations
        const languages = [
          { lang: "English", translation: "August" },
          { lang: "French", translation: "août" },
          { lang: "German", translation: "August" },
          { lang: "Spanish", translation: "agosto" },
          { lang: "Italian", translation: "agosto" },
          { lang: "Swedish", translation: "augusti" },
          { lang: "Norwegian", translation: "august" },
        ];

        languages.forEach(({ translation }) => {
          mockTranslationFunction.mockReturnValue(translation);

          const result = getLocalizedDate(testDate, mockTranslationFunction);

          expect(result).toBe(`15 ${translation} 2023`);
          mockTranslationFunction.mockClear();
        });
      });

      it("should handle special characters in month names", () => {
        const testDate = new Date(2023, 2, 8); // March 8, 2023

        // Test with month names containing special characters
        const specialMonths = [
          "märz",
          "décembre",
          "février",
          "août",
          "июль",
          "Äprill",
        ];

        specialMonths.forEach((monthName) => {
          mockTranslationFunction.mockReturnValue(monthName);

          const result = getLocalizedDate(testDate, mockTranslationFunction);

          expect(result).toBe(`8 ${monthName} 2023`);
          mockTranslationFunction.mockClear();
        });
      });

      it("should handle empty translation results gracefully", () => {
        const testDate = new Date(2023, 4, 20); // May 20, 2023
        mockTranslationFunction.mockReturnValue("");

        const result = getLocalizedDate(testDate, mockTranslationFunction);

        expect(result).toBe("20  2023");
      });

      it("should handle very long month names", () => {
        const testDate = new Date(2023, 8, 12); // September 12, 2023
        const longMonthName = "VeryLongMonthNameThatExceedsNormalLength";
        mockTranslationFunction.mockReturnValue(longMonthName);

        const result = getLocalizedDate(testDate, mockTranslationFunction);

        expect(result).toBe(`12 ${longMonthName} 2023`);
      });
    });

    describe("TypeScript type safety", () => {
      it("should handle proper TypeScript types", () => {
        const testDate = new Date(2023, 10, 5); // November 5, 2023

        // Test with properly typed translation function
        const typedTranslation = (
          key: string,
          options?: { defaultValue?: string }
        ): string => {
          return options?.defaultValue || key;
        };

        const result = getLocalizedDate(testDate, typedTranslation);

        expect(result).toBe("5 November 2023");
        expect(typeof result).toBe("string");
      });

      it("should work with optional parameters in translation function", () => {
        const testDate = new Date(2023, 1, 14); // February 14, 2023

        // Test translation function that only takes key parameter
        const simpleTranslation = (key: string): string =>
          key.replace("month.", "month-");

        const result = getLocalizedDate(testDate, simpleTranslation);

        expect(result).toBe("14 month-2 2023");
      });
    });
  });
});
