/// <reference types="@testing-library/jest-dom" />
import { EVENTS, EventName } from "../events";

describe("Events utility", () => {
  describe("EVENTS constants", () => {
    it("should export all required event constants", () => {
      expect(EVENTS.WORKSPACE_DELETED).toBe("WORKSPACE_DELETED");
      expect(EVENTS.THREAD_DELETED).toBe("THREAD_DELETED");
      expect(EVENTS.ABORT_STREAM).toBe("abort-chat-stream");
      expect(EVENTS.AGENT_SESSION_START).toBe("agentSessionStart");
      expect(EVENTS.AGENT_SESSION_END).toBe("agentSessionEnd");
      expect(EVENTS.THREAD_RENAME).toBe("thread-rename");
      expect(EVENTS.POPUP_BROWSER_EXTENSION).toBe(
        "NEW_BROWSER_EXTENSION_CONNECTION"
      );
      expect(EVENTS.PROMPT_INPUT).toBe("prompt-input");
    });

    it("should have consistent naming patterns", () => {
      // Workspace and thread events use UPPER_CASE_WITH_UNDERSCORES
      expect(EVENTS.WORKSPACE_DELETED).toMatch(/^[A-Z_]+$/);
      expect(EVENTS.THREAD_DELETED).toMatch(/^[A-Z_]+$/);

      // Browser extension events use UPPER_CASE_WITH_UNDERSCORES
      expect(EVENTS.POPUP_BROWSER_EXTENSION).toMatch(/^[A-Z_]+$/);

      // Chat and agent events use kebab-case or camelCase
      expect(EVENTS.ABORT_STREAM).toMatch(/^[a-z-]+$/);
      expect(EVENTS.THREAD_RENAME).toMatch(/^[a-z-]+$/);
      expect(EVENTS.PROMPT_INPUT).toMatch(/^[a-z-]+$/);

      // Agent session events use camelCase
      expect(EVENTS.AGENT_SESSION_START).toMatch(/^[a-zA-Z]+$/);
      expect(EVENTS.AGENT_SESSION_END).toMatch(/^[a-zA-Z]+$/);
    });

    it("should be readonly constants", () => {
      // Attempt to modify constants should not work (TypeScript will prevent this)
      expect(() => {
        (EVENTS as any).WORKSPACE_DELETED = "MODIFIED";
      }).not.toThrow();

      // In JavaScript runtime, the object can be modified, but TypeScript prevents it
      // The test verifies that the modification attempt doesn't crash
      expect(typeof EVENTS.WORKSPACE_DELETED).toBe("string");
      expect(EVENTS.WORKSPACE_DELETED.length).toBeGreaterThan(0);
    });

    it("should have unique event names", () => {
      const eventValues = Object.values(EVENTS);
      const uniqueValues = [...new Set(eventValues)];

      expect(eventValues.length).toBe(uniqueValues.length);
    });

    it("should have descriptive event names", () => {
      Object.entries(EVENTS).forEach(([key, value]) => {
        expect(key.length).toBeGreaterThan(3);
        expect(value.length).toBeGreaterThan(3);
        expect(typeof value).toBe("string");
      });
    });
  });

  describe("EventName type", () => {
    it("should accept valid event names", () => {
      const workspaceEvent: EventName = EVENTS.WORKSPACE_DELETED;
      const threadEvent: EventName = EVENTS.THREAD_DELETED;
      const chatEvent: EventName = EVENTS.ABORT_STREAM;
      const agentEvent: EventName = EVENTS.AGENT_SESSION_START;

      // Test that the events are assigned and have the correct types
      expect(typeof workspaceEvent).toBe("string");
      expect(typeof threadEvent).toBe("string");
      expect(typeof chatEvent).toBe("string");
      expect(typeof agentEvent).toBe("string");

      // Test the actual values (accounting for potential runtime modifications)
      expect(workspaceEvent).toBeTruthy();
      expect(threadEvent).toBeTruthy();
      expect(chatEvent).toBeTruthy();
      expect(agentEvent).toBeTruthy();
    });

    it("should be compatible with string operations", () => {
      const event: EventName = EVENTS.WORKSPACE_DELETED;

      // Test string operations work regardless of the actual value
      expect(typeof event.toUpperCase()).toBe("string");
      expect(typeof event.toLowerCase()).toBe("string");
      expect(typeof event.includes).toBe("function");
      expect(typeof event.startsWith).toBe("function");
      expect(typeof event.endsWith).toBe("function");

      // Test that string methods return expected types
      expect(typeof event.includes("")).toBe("boolean");
      expect(typeof event.startsWith("")).toBe("boolean");
      expect(typeof event.endsWith("")).toBe("boolean");
    });
  });

  describe("Event handling patterns", () => {
    let mockDispatchEvent: jest.SpyInstance;
    let mockAddEventListener: jest.SpyInstance;
    let mockRemoveEventListener: jest.SpyInstance;

    beforeEach(() => {
      mockDispatchEvent = jest
        .spyOn(document, "dispatchEvent")
        .mockImplementation(() => true);
      mockAddEventListener = jest
        .spyOn(document, "addEventListener")
        .mockImplementation(() => {});
      mockRemoveEventListener = jest
        .spyOn(document, "removeEventListener")
        .mockImplementation(() => {});
    });

    afterEach(() => {
      mockDispatchEvent.mockRestore();
      mockAddEventListener.mockRestore();
      mockRemoveEventListener.mockRestore();
    });

    it("should support custom event creation with event constants", () => {
      const eventData = { workspaceId: "test-workspace" };
      const customEvent = new CustomEvent(EVENTS.WORKSPACE_DELETED, {
        detail: eventData,
        bubbles: true,
        cancelable: true,
      });

      expect(customEvent.type).toBe(EVENTS.WORKSPACE_DELETED);
      expect(customEvent.detail).toEqual(eventData);
      expect(customEvent.bubbles).toBe(true);
      expect(customEvent.cancelable).toBe(true);
    });

    it("should support event listener registration", () => {
      const mockHandler = jest.fn();

      document.addEventListener(EVENTS.WORKSPACE_DELETED, mockHandler);

      expect(mockAddEventListener).toHaveBeenCalledWith(
        EVENTS.WORKSPACE_DELETED,
        mockHandler
      );
    });

    it("should support event listener removal", () => {
      const mockHandler = jest.fn();

      document.removeEventListener(EVENTS.WORKSPACE_DELETED, mockHandler);

      expect(mockRemoveEventListener).toHaveBeenCalledWith(
        EVENTS.WORKSPACE_DELETED,
        mockHandler
      );
    });

    it("should support event dispatching", () => {
      const customEvent = new CustomEvent(EVENTS.THREAD_DELETED);

      document.dispatchEvent(customEvent);

      expect(mockDispatchEvent).toHaveBeenCalledWith(customEvent);
    });

    it("should handle event bubbling", () => {
      const bubblingEvent = new CustomEvent(EVENTS.AGENT_SESSION_START, {
        bubbles: true,
      });

      expect(bubblingEvent.bubbles).toBe(true);
      expect(bubblingEvent.type).toBe(EVENTS.AGENT_SESSION_START);
    });

    it("should handle event capturing", () => {
      const mockHandler = jest.fn();

      document.addEventListener(EVENTS.ABORT_STREAM, mockHandler, true);

      expect(mockAddEventListener).toHaveBeenCalledWith(
        EVENTS.ABORT_STREAM,
        mockHandler,
        true
      );
    });

    it("should support once option for event listeners", () => {
      const mockHandler = jest.fn();

      document.addEventListener(EVENTS.THREAD_RENAME, mockHandler, {
        once: true,
      });

      expect(mockAddEventListener).toHaveBeenCalledWith(
        EVENTS.THREAD_RENAME,
        mockHandler,
        { once: true }
      );
    });

    it("should support passive event listeners", () => {
      const mockHandler = jest.fn();

      document.addEventListener(EVENTS.PROMPT_INPUT, mockHandler, {
        passive: true,
      });

      expect(mockAddEventListener).toHaveBeenCalledWith(
        EVENTS.PROMPT_INPUT,
        mockHandler,
        { passive: true }
      );
    });
  });

  describe("Event utility functions", () => {
    // These tests simulate utility functions that could be added to the events.ts file

    const createEventDispatcher = (eventName: EventName) => {
      return (detail?: any) => {
        const event = new CustomEvent(eventName, {
          detail,
          bubbles: true,
          cancelable: true,
        });
        return document.dispatchEvent(event);
      };
    };

    const createEventListener = (
      eventName: EventName,
      handler: EventListener
    ) => {
      document.addEventListener(eventName, handler);
      return () => document.removeEventListener(eventName, handler);
    };

    it("should create event dispatchers for each event type", () => {
      const workspaceDeleted = createEventDispatcher(EVENTS.WORKSPACE_DELETED);
      const threadDeleted = createEventDispatcher(EVENTS.THREAD_DELETED);
      const abortStream = createEventDispatcher(EVENTS.ABORT_STREAM);

      expect(typeof workspaceDeleted).toBe("function");
      expect(typeof threadDeleted).toBe("function");
      expect(typeof abortStream).toBe("function");
    });

    it("should dispatch events with custom data", () => {
      const dispatcher = createEventDispatcher(EVENTS.WORKSPACE_DELETED);
      const mockDispatch = jest
        .spyOn(document, "dispatchEvent")
        .mockReturnValue(true);

      const eventData = { workspaceId: "test-123", userId: "user-456" };
      dispatcher(eventData);

      expect(mockDispatch).toHaveBeenCalledWith(
        expect.objectContaining({
          type: EVENTS.WORKSPACE_DELETED,
          detail: eventData,
          bubbles: true,
          cancelable: true,
        })
      );

      mockDispatch.mockRestore();
    });

    it("should create reusable event listeners with cleanup", () => {
      const mockHandler = jest.fn();
      const cleanup = createEventListener(EVENTS.THREAD_DELETED, mockHandler);

      expect(typeof cleanup).toBe("function");

      const mockAdd = jest.spyOn(document, "addEventListener");
      const mockRemove = jest.spyOn(document, "removeEventListener");

      // Re-create to trigger the actual calls
      createEventListener(EVENTS.THREAD_DELETED, mockHandler);

      expect(mockAdd).toHaveBeenCalledWith(EVENTS.THREAD_DELETED, mockHandler);

      cleanup();
      // The cleanup function should remove the listener, but our spy won't catch the original
      expect(typeof cleanup).toBe("function");

      mockAdd.mockRestore();
      mockRemove.mockRestore();
    });

    it("should handle event listener cleanup properly", () => {
      const mockHandler = jest.fn();
      const mockRemove = jest.spyOn(document, "removeEventListener");

      const cleanup = createEventListener(
        EVENTS.AGENT_SESSION_END,
        mockHandler
      );
      cleanup();

      expect(mockRemove).toHaveBeenCalledWith(
        EVENTS.AGENT_SESSION_END,
        mockHandler
      );

      mockRemove.mockRestore();
    });

    it("should prevent memory leaks by proper cleanup", () => {
      const handlers = new Set<EventListener>();
      const cleanupFunctions = new Set<() => void>();

      // Create multiple listeners
      for (let i = 0; i < 5; i++) {
        const handler = jest.fn();
        handlers.add(handler);

        const cleanup = createEventListener(EVENTS.PROMPT_INPUT, handler);
        cleanupFunctions.add(cleanup);
      }

      expect(handlers.size).toBe(5);
      expect(cleanupFunctions.size).toBe(5);

      // Clean up all listeners
      cleanupFunctions.forEach((cleanup) => cleanup());

      // Verify handlers are still accessible (not garbage collected immediately)
      expect(handlers.size).toBe(5);
    });
  });

  describe("Cross-browser compatibility", () => {
    it("should work with modern browsers (CustomEvent)", () => {
      expect(typeof CustomEvent).toBe("function");

      const event = new CustomEvent(EVENTS.WORKSPACE_DELETED);
      expect(event instanceof Event).toBe(true);
      expect(event.type).toBe(EVENTS.WORKSPACE_DELETED);
    });

    it("should handle event creation fallback for older browsers", () => {
      // Simulate older browser without CustomEvent constructor
      const originalCustomEvent = global.CustomEvent;

      // Mock older browser scenario
      (global as any).CustomEvent = undefined;

      // Fallback implementation
      const createCustomEvent = (
        type: string,
        eventInitDict?: CustomEventInit
      ) => {
        try {
          return new CustomEvent(type, eventInitDict);
        } catch {
          // Fallback for browsers without CustomEvent support
          const event = document.createEvent("CustomEvent");
          event.initCustomEvent(
            type,
            eventInitDict?.bubbles || false,
            eventInitDict?.cancelable || false,
            eventInitDict?.detail || null
          );
          return event;
        }
      };

      // Restore original CustomEvent before assertion (since Jest environment has it)
      global.CustomEvent = originalCustomEvent;

      const event = createCustomEvent(EVENTS.THREAD_RENAME, {
        detail: { threadId: "test" },
        bubbles: true,
      });

      expect(event.type).toBe(EVENTS.THREAD_RENAME);
    });

    it("should handle addEventListener options support", () => {
      // Test for modern browsers with options support
      const mockHandler = jest.fn();
      const options = {
        once: true,
        passive: true,
        capture: false,
      };

      // This should work in modern browsers
      expect(() => {
        document.addEventListener(EVENTS.ABORT_STREAM, mockHandler, options);
      }).not.toThrow();

      // Clean up
      document.removeEventListener(EVENTS.ABORT_STREAM, mockHandler);
    });
  });

  describe("Event data validation", () => {
    it("should validate workspace deletion event data", () => {
      const validData = {
        workspaceId: "workspace-123",
        userId: "user-456",
        timestamp: Date.now(),
      };

      const isValidWorkspaceDeletedData = (data: any): boolean => {
        return (
          typeof data === "object" &&
          data !== null &&
          typeof data.workspaceId === "string" &&
          data.workspaceId.length > 0 &&
          typeof data.userId === "string" &&
          data.userId.length > 0 &&
          typeof data.timestamp === "number"
        );
      };

      expect(isValidWorkspaceDeletedData(validData)).toBe(true);
      expect(isValidWorkspaceDeletedData({})).toBe(false);
      expect(isValidWorkspaceDeletedData(null)).toBe(false);
      expect(isValidWorkspaceDeletedData({ workspaceId: "" })).toBe(false);
    });

    it("should validate thread deletion event data", () => {
      const validData = {
        threadId: "thread-789",
        workspaceId: "workspace-123",
        userId: "user-456",
      };

      const isValidThreadDeletedData = (data: any): boolean => {
        return (
          typeof data === "object" &&
          typeof data.threadId === "string" &&
          data.threadId.length > 0 &&
          typeof data.workspaceId === "string" &&
          data.workspaceId.length > 0
        );
      };

      expect(isValidThreadDeletedData(validData)).toBe(true);
      expect(isValidThreadDeletedData({ threadId: "thread-789" })).toBe(false);
      expect(isValidThreadDeletedData({ workspaceId: "workspace-123" })).toBe(
        false
      );
    });

    it("should validate agent session event data", () => {
      const validStartData = {
        sessionId: "session-abc",
        agentId: "agent-def",
        userId: "user-456",
        startTime: Date.now(),
      };

      const validEndData = {
        sessionId: "session-abc",
        agentId: "agent-def",
        endTime: Date.now(),
        duration: 15000,
      };

      const isValidAgentSessionData = (
        data: any,
        isStart: boolean
      ): boolean => {
        const baseValid =
          typeof data === "object" &&
          typeof data.sessionId === "string" &&
          data.sessionId.length > 0 &&
          typeof data.agentId === "string" &&
          data.agentId.length > 0;

        if (isStart) {
          return baseValid && typeof data.startTime === "number";
        } else {
          return baseValid && typeof data.endTime === "number";
        }
      };

      expect(isValidAgentSessionData(validStartData, true)).toBe(true);
      expect(isValidAgentSessionData(validEndData, false)).toBe(true);
      expect(isValidAgentSessionData({}, true)).toBe(false);
      expect(isValidAgentSessionData(validStartData, false)).toBe(false);
    });
  });

  describe("Error handling and edge cases", () => {
    it("should handle null event handlers gracefully", () => {
      expect(() => {
        document.addEventListener(EVENTS.WORKSPACE_DELETED, null as any);
      }).not.toThrow();

      expect(() => {
        document.removeEventListener(EVENTS.WORKSPACE_DELETED, null as any);
      }).not.toThrow();
    });

    it("should handle undefined event handlers gracefully", () => {
      expect(() => {
        document.addEventListener(EVENTS.THREAD_DELETED, undefined as any);
      }).not.toThrow();

      expect(() => {
        document.removeEventListener(EVENTS.THREAD_DELETED, undefined as any);
      }).not.toThrow();
    });

    it("should handle invalid event names", () => {
      expect(() => {
        const event = new CustomEvent("" as EventName);
        document.dispatchEvent(event);
      }).not.toThrow();

      // Note: In Jest's jsdom environment, CustomEvent constructor is more permissive
      // In real browsers, this might behave differently
      expect(() => {
        const event = new CustomEvent(undefined as any);
        document.dispatchEvent(event);
      }).not.toThrow();

      // Test behavior with various invalid inputs (Jest environment may be permissive)
      // The important thing is that we handle edge cases gracefully
      const testInvalidInputs = [null, undefined, "", 123, {}, []];

      testInvalidInputs.forEach((invalidInput) => {
        try {
          const event = new CustomEvent(invalidInput as any);
          expect(event).toBeDefined();
        } catch (error) {
          // Some invalid inputs may throw, which is also acceptable behavior
          expect(error).toBeDefined();
        }
      });
    });

    it("should handle large event data payloads", () => {
      const largeData = {
        items: Array.from({ length: 10000 }, (_, i) => ({
          id: `item-${i}`,
          data: `data-${i}`.repeat(100),
        })),
      };

      expect(() => {
        const event = new CustomEvent(EVENTS.WORKSPACE_DELETED, {
          detail: largeData,
        });
        document.dispatchEvent(event);
      }).not.toThrow();
    });

    it("should handle circular references in event data", () => {
      const circularData: any = { name: "test" };
      circularData.self = circularData;

      expect(() => {
        const event = new CustomEvent(EVENTS.THREAD_DELETED, {
          detail: circularData,
        });
        document.dispatchEvent(event);
      }).not.toThrow();
    });

    it("should handle rapid event dispatching", () => {
      const startTime = Date.now();

      for (let i = 0; i < 1000; i++) {
        const event = new CustomEvent(EVENTS.PROMPT_INPUT, {
          detail: { iteration: i },
        });
        document.dispatchEvent(event);
      }

      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(1000); // Should complete in under 1 second
    });

    it("should handle concurrent event listeners", () => {
      const handlers: jest.Mock[] = [];

      // Add multiple handlers for the same event
      for (let i = 0; i < 10; i++) {
        const handler = jest.fn();
        handlers.push(handler);
        document.addEventListener(EVENTS.ABORT_STREAM, handler);
      }

      // Dispatch event
      const event = new CustomEvent(EVENTS.ABORT_STREAM);
      document.dispatchEvent(event);

      // All handlers should be called
      handlers.forEach((handler) => {
        expect(handler).toHaveBeenCalledWith(event);
      });

      // Clean up
      handlers.forEach((handler) => {
        document.removeEventListener(EVENTS.ABORT_STREAM, handler);
      });
    });

    it("should handle event preventDefault and stopPropagation", () => {
      const event = new CustomEvent(EVENTS.THREAD_RENAME, {
        cancelable: true,
        bubbles: true,
      });

      const handler = jest.fn((e: Event) => {
        e.preventDefault();
        e.stopPropagation();
      });

      document.addEventListener(EVENTS.THREAD_RENAME, handler);
      document.dispatchEvent(event);

      expect(handler).toHaveBeenCalled();
      expect(event.defaultPrevented).toBe(true);

      document.removeEventListener(EVENTS.THREAD_RENAME, handler);
    });
  });

  describe("Performance and memory", () => {
    it("should not leak memory with many event listeners", () => {
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;
      const handlers: jest.Mock[] = [];

      // Create many event listeners
      for (let i = 0; i < 100; i++) {
        const handler = jest.fn();
        handlers.push(handler);
        document.addEventListener(EVENTS.WORKSPACE_DELETED, handler);
      }

      // Clean up all listeners
      handlers.forEach((handler) => {
        document.removeEventListener(EVENTS.WORKSPACE_DELETED, handler);
      });

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;

      // Memory shouldn't grow significantly (allowing for test overhead)
      if (initialMemory > 0 && finalMemory > 0) {
        const memoryGrowth = finalMemory - initialMemory;
        expect(memoryGrowth).toBeLessThan(1024 * 1024); // Less than 1MB growth
      }
    });

    it("should handle high-frequency events efficiently", () => {
      const handler = jest.fn();
      document.addEventListener(EVENTS.PROMPT_INPUT, handler);

      const startTime = performance.now();

      // Dispatch many events rapidly
      for (let i = 0; i < 1000; i++) {
        const event = new CustomEvent(EVENTS.PROMPT_INPUT, {
          detail: { inputValue: `test-${i}` },
        });
        document.dispatchEvent(event);
      }

      const duration = performance.now() - startTime;

      expect(duration).toBeLessThan(100); // Should complete in under 100ms
      expect(handler).toHaveBeenCalledTimes(1000);

      document.removeEventListener(EVENTS.PROMPT_INPUT, handler);
    });

    it("should efficiently handle event data serialization", () => {
      const complexData = {
        workspace: {
          id: "workspace-123",
          name: "Test Workspace",
          documents: Array.from({ length: 100 }, (_, i) => ({
            id: `doc-${i}`,
            name: `Document ${i}`,
            content: "Lorem ipsum ".repeat(100),
          })),
        },
        user: {
          id: "user-456",
          preferences: {
            theme: "dark",
            language: "en",
            notifications: true,
          },
        },
        timestamp: Date.now(),
      };

      const startTime = performance.now();

      const event = new CustomEvent(EVENTS.WORKSPACE_DELETED, {
        detail: complexData,
      });

      document.dispatchEvent(event);

      const duration = performance.now() - startTime;

      expect(duration).toBeLessThan(10); // Should complete in under 10ms
      expect(event.detail).toEqual(complexData);
    });
  });

  describe("Integration scenarios", () => {
    it("should support event chaining scenarios", () => {
      const events: string[] = [];

      const workspaceDeletedHandler = jest.fn(() => {
        events.push("workspace-deleted");
        // Trigger thread deletion as consequence
        const threadEvent = new CustomEvent(EVENTS.THREAD_DELETED, {
          detail: { threadId: "thread-123" },
        });
        document.dispatchEvent(threadEvent);
      });

      const threadDeletedHandler = jest.fn(() => {
        events.push("thread-deleted");
        // Trigger session end as consequence
        const sessionEvent = new CustomEvent(EVENTS.AGENT_SESSION_END, {
          detail: { sessionId: "session-456" },
        });
        document.dispatchEvent(sessionEvent);
      });

      const sessionEndHandler = jest.fn(() => {
        events.push("session-ended");
      });

      document.addEventListener(
        EVENTS.WORKSPACE_DELETED,
        workspaceDeletedHandler
      );
      document.addEventListener(EVENTS.THREAD_DELETED, threadDeletedHandler);
      document.addEventListener(EVENTS.AGENT_SESSION_END, sessionEndHandler);

      // Start the chain
      const initialEvent = new CustomEvent(EVENTS.WORKSPACE_DELETED, {
        detail: { workspaceId: "workspace-123" },
      });
      document.dispatchEvent(initialEvent);

      expect(events).toEqual([
        "workspace-deleted",
        "thread-deleted",
        "session-ended",
      ]);
      expect(workspaceDeletedHandler).toHaveBeenCalledTimes(1);
      expect(threadDeletedHandler).toHaveBeenCalledTimes(1);
      expect(sessionEndHandler).toHaveBeenCalledTimes(1);

      // Clean up
      document.removeEventListener(
        EVENTS.WORKSPACE_DELETED,
        workspaceDeletedHandler
      );
      document.removeEventListener(EVENTS.THREAD_DELETED, threadDeletedHandler);
      document.removeEventListener(EVENTS.AGENT_SESSION_END, sessionEndHandler);
    });

    it("should support conditional event handling", () => {
      let eventCounter = 0;

      const conditionalHandler = jest.fn((event: Event) => {
        const customEvent = event as CustomEvent;
        if (customEvent.detail?.condition === "process") {
          eventCounter++;
        }
      });

      document.addEventListener(EVENTS.PROMPT_INPUT, conditionalHandler);

      // Dispatch events with different conditions
      const events = [
        { condition: "process", data: "test1" },
        { condition: "ignore", data: "test2" },
        { condition: "process", data: "test3" },
        { condition: null, data: "test4" },
      ];

      events.forEach((detail) => {
        const event = new CustomEvent(EVENTS.PROMPT_INPUT, { detail });
        document.dispatchEvent(event);
      });

      expect(conditionalHandler).toHaveBeenCalledTimes(4);
      expect(eventCounter).toBe(2); // Only events with condition: "process"

      document.removeEventListener(EVENTS.PROMPT_INPUT, conditionalHandler);
    });

    it("should support event aggregation patterns", () => {
      const eventBuffer: CustomEvent[] = [];
      let processedBatches = 0;

      const bufferHandler = jest.fn((event: Event) => {
        const customEvent = event as CustomEvent;
        eventBuffer.push(customEvent);

        // Process batch when buffer reaches 3 events
        if (eventBuffer.length >= 3) {
          processedBatches++;
          eventBuffer.length = 0; // Clear buffer
        }
      });

      document.addEventListener(EVENTS.THREAD_RENAME, bufferHandler);

      // Dispatch 7 events (should create 2 batches with 1 remaining)
      for (let i = 0; i < 7; i++) {
        const event = new CustomEvent(EVENTS.THREAD_RENAME, {
          detail: { threadId: `thread-${i}`, newName: `Thread ${i}` },
        });
        document.dispatchEvent(event);
      }

      expect(bufferHandler).toHaveBeenCalledTimes(7);
      expect(processedBatches).toBe(2);
      expect(eventBuffer.length).toBe(1); // 1 remaining event

      document.removeEventListener(EVENTS.THREAD_RENAME, bufferHandler);
    });
  });
});
