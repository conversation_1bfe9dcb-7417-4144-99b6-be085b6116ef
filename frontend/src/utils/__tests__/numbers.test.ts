/// <reference types="@testing-library/jest-dom" />
import {
  numberWithCommas,
  nFormatter,
  dollarFormat,
  toPercentString,
  humanFileSize,
  milliToHms,
} from "../numbers";

describe("numbers utility functions", () => {
  describe("numberWithCommas", () => {
    it("should format integers with commas", () => {
      expect(numberWithCommas(1000)).toBe("1,000");
      expect(numberWithCommas(1234567)).toBe("1,234,567");
      expect(numberWithCommas(999)).toBe("999");
    });

    it("should format floating point numbers with commas", () => {
      expect(numberWithCommas(1000.5)).toBe("1,000.5");
      expect(numberWithCommas(1234567.89)).toBe("1,234,567.89");
      expect(numberWithCommas(999.99)).toBe("999.99");
    });

    it("should handle string inputs", () => {
      expect(numberWithCommas("1000")).toBe("1,000");
      expect(numberWithCommas("1234567")).toBe("1,234,567");
      expect(numberWithCommas("999")).toBe("999");
    });

    it("should handle zero and negative numbers", () => {
      expect(numberWithCommas(0)).toBe("0");
      expect(numberWithCommas(-1000)).toBe("-1,000");
      expect(numberWithCommas(-1234567)).toBe("-1,234,567");
    });

    it("should handle small numbers", () => {
      expect(numberWithCommas(1)).toBe("1");
      expect(numberWithCommas(12)).toBe("12");
      expect(numberWithCommas(123)).toBe("123");
    });

    it("should handle decimal numbers with multiple groups", () => {
      // The regex also adds commas to decimal places, which is the actual behavior
      expect(numberWithCommas(1000000.123456)).toBe("1,000,000.123,456");
      expect(numberWithCommas(12345678.9)).toBe("12,345,678.9");
    });

    it("should handle very large numbers", () => {
      expect(numberWithCommas(1000000000)).toBe("1,000,000,000");
      expect(numberWithCommas(123456789012345)).toBe("123,456,789,012,345");
    });

    it("should handle string representations of decimals", () => {
      expect(numberWithCommas("1000.50")).toBe("1,000.50");
      expect(numberWithCommas("999.99")).toBe("999.99");
    });

    it("should handle edge cases", () => {
      expect(numberWithCommas("")).toBe("");
      expect(numberWithCommas("abc")).toBe("abc");
      expect(numberWithCommas("1,000")).toBe("1,000"); // Already formatted
    });
  });

  describe("nFormatter (compact notation)", () => {
    it("should format small numbers as-is", () => {
      expect(nFormatter(0)).toBe("0");
      expect(nFormatter(1)).toBe("1");
      expect(nFormatter(999)).toBe("999");
    });

    it("should format thousands with K", () => {
      expect(nFormatter(1000)).toBe("1K");
      expect(nFormatter(1500)).toBe("1.5K");
      expect(nFormatter(12000)).toBe("12K");
      expect(nFormatter(999000)).toBe("999K");
    });

    it("should format millions with M", () => {
      expect(nFormatter(1000000)).toBe("1M");
      expect(nFormatter(1500000)).toBe("1.5M");
      expect(nFormatter(12000000)).toBe("12M");
    });

    it("should format billions with B", () => {
      expect(nFormatter(1000000000)).toBe("1B");
      expect(nFormatter(1500000000)).toBe("1.5B");
    });

    it("should handle negative numbers", () => {
      expect(nFormatter(-1000)).toBe("-1K");
      expect(nFormatter(-1500000)).toBe("-1.5M");
    });

    it("should handle decimal inputs", () => {
      expect(nFormatter(1234.5)).toBe("1.2K");
      expect(nFormatter(1999.9)).toBe("2K");
    });

    it("should handle very large numbers", () => {
      const trillion = 1000000000000;
      const result = nFormatter(trillion);
      expect(result).toBeTruthy();
      expect(typeof result).toBe("string");
    });
  });

  describe("dollarFormat", () => {
    it("should format integers as currency", () => {
      expect(dollarFormat(0)).toBe("$0.00");
      expect(dollarFormat(1)).toBe("$1.00");
      expect(dollarFormat(1000)).toBe("$1,000.00");
      expect(dollarFormat(1234567)).toBe("$1,234,567.00");
    });

    it("should format decimals as currency", () => {
      expect(dollarFormat(1.5)).toBe("$1.50");
      expect(dollarFormat(999.99)).toBe("$999.99");
      expect(dollarFormat(1234.56)).toBe("$1,234.56");
    });

    it("should handle negative amounts", () => {
      expect(dollarFormat(-100)).toBe("-$100.00");
      expect(dollarFormat(-1234.56)).toBe("-$1,234.56");
    });

    it("should handle very small amounts", () => {
      expect(dollarFormat(0.01)).toBe("$0.01");
      expect(dollarFormat(0.001)).toBe("$0.00"); // Rounds to nearest cent
    });

    it("should handle very large amounts", () => {
      expect(dollarFormat(1000000)).toBe("$1,000,000.00");
      expect(dollarFormat(*********.99)).toBe("$999,999,999.99");
    });

    it("should handle fractional cents correctly", () => {
      expect(dollarFormat(1.234)).toBe("$1.23"); // Rounds down
      expect(dollarFormat(1.235)).toBe("$1.24"); // Rounds up (banker's rounding)
      expect(dollarFormat(1.999)).toBe("$2.00");
    });

    it("should handle zero and near-zero values", () => {
      expect(dollarFormat(0)).toBe("$0.00");
      expect(dollarFormat(0.004)).toBe("$0.00");
      expect(dollarFormat(0.005)).toBe("$0.01");
    });
  });

  describe("toPercentString", () => {
    it("should convert decimals to percentages", () => {
      expect(toPercentString(0.5)).toBe("50%");
      expect(toPercentString(0.75)).toBe("75%");
      expect(toPercentString(1.0)).toBe("100%");
      expect(toPercentString(0)).toBe("0%");
    });

    it("should handle null input", () => {
      expect(toPercentString(null)).toBe("");
      expect(toPercentString()).toBe(""); // Default null
    });

    it("should handle NaN input", () => {
      expect(toPercentString(NaN)).toBe("");
      expect(toPercentString(Number.NaN)).toBe("");
    });

    it("should handle decimal places parameter", () => {
      expect(toPercentString(0.1234, 0)).toBe("12%");
      expect(toPercentString(0.1234, 1)).toBe("12.0%");
      expect(toPercentString(0.1234, 2)).toBe("12.00%");
    });

    it("should handle values over 100%", () => {
      expect(toPercentString(1.5)).toBe("150%");
      expect(toPercentString(2.0)).toBe("200%");
      expect(toPercentString(10.0)).toBe("1000%");
    });

    it("should handle negative percentages", () => {
      expect(toPercentString(-0.25)).toBe("-25%");
      expect(toPercentString(-1.0)).toBe("-100%");
    });

    it("should handle very small percentages", () => {
      expect(toPercentString(0.001)).toBe("0%"); // Rounds to 0
      expect(toPercentString(0.01)).toBe("1%");
      expect(toPercentString(0.001, 1)).toBe("0.0%");
    });

    it("should handle edge cases with decimals", () => {
      expect(toPercentString(0.999)).toBe("100%"); // Rounds to 100
      expect(toPercentString(0.994)).toBe("99%");
      expect(toPercentString(0.995)).toBe("100%"); // Rounds up
    });

    it("should handle Infinity and -Infinity", () => {
      // Infinity * 100 = Infinity, which toString() becomes "Infinity%"
      expect(toPercentString(Infinity)).toBe("Infinity%");
      expect(toPercentString(-Infinity)).toBe("-Infinity%");
    });
  });

  describe("humanFileSize", () => {
    describe("binary (default) mode", () => {
      it("should format bytes correctly", () => {
        expect(humanFileSize(0)).toBe("0 B");
        expect(humanFileSize(512)).toBe("512 B");
        expect(humanFileSize(1023)).toBe("1023 B");
      });

      it("should format KiB correctly", () => {
        expect(humanFileSize(1024)).toBe("1.0 KiB");
        expect(humanFileSize(1536)).toBe("1.5 KiB");
        expect(humanFileSize(2048)).toBe("2.0 KiB");
      });

      it("should format MiB correctly", () => {
        expect(humanFileSize(1024 * 1024)).toBe("1.0 MiB");
        expect(humanFileSize(1024 * 1024 * 1.5)).toBe("1.5 MiB");
      });

      it("should format GiB correctly", () => {
        expect(humanFileSize(1024 * 1024 * 1024)).toBe("1.0 GiB");
        expect(humanFileSize(1024 * 1024 * 1024 * 2.5)).toBe("2.5 GiB");
      });

      it("should handle negative sizes", () => {
        expect(humanFileSize(-1024)).toBe("-1.0 KiB");
        expect(humanFileSize(-512)).toBe("-512 B");
      });
    });

    describe("SI (decimal) mode", () => {
      it("should format bytes correctly", () => {
        expect(humanFileSize(0, true)).toBe("0 B");
        expect(humanFileSize(512, true)).toBe("512 B");
        expect(humanFileSize(999, true)).toBe("999 B");
      });

      it("should format kB correctly", () => {
        expect(humanFileSize(1000, true)).toBe("1.0 kB");
        expect(humanFileSize(1500, true)).toBe("1.5 kB");
        expect(humanFileSize(2000, true)).toBe("2.0 kB");
      });

      it("should format MB correctly", () => {
        expect(humanFileSize(1000000, true)).toBe("1.0 MB");
        expect(humanFileSize(1500000, true)).toBe("1.5 MB");
      });

      it("should format GB correctly", () => {
        expect(humanFileSize(1000000000, true)).toBe("1.0 GB");
      });
    });

    describe("decimal places parameter", () => {
      it("should handle 0 decimal places", () => {
        expect(humanFileSize(1536, false, 0)).toBe("2 KiB");
        expect(humanFileSize(1024, false, 0)).toBe("1 KiB");
      });

      it("should handle 2 decimal places", () => {
        expect(humanFileSize(1536, false, 2)).toBe("1.50 KiB");
        expect(humanFileSize(1024, false, 2)).toBe("1.00 KiB");
      });

      it("should handle 3 decimal places", () => {
        expect(humanFileSize(1024 + 102, false, 3)).toBe("1.100 KiB");
      });
    });

    describe("edge cases", () => {
      it("should handle very large sizes", () => {
        const yottabyte = 1024 ** 8;
        const result = humanFileSize(yottabyte);
        expect(result).toContain("YiB");
      });

      it("should handle sizes larger than YiB", () => {
        const veryLarge = 1024 ** 9; // Larger than YiB
        const result = humanFileSize(veryLarge);
        expect(result).toContain("YiB"); // Should cap at YiB
      });

      it("should handle fractional bytes input", () => {
        expect(humanFileSize(1023.7)).toBe("1023.7 B");
        expect(humanFileSize(1024.5)).toBe("1.0 KiB");
      });

      it("should be consistent between binary and SI for small values", () => {
        expect(humanFileSize(500)).toBe("500 B");
        expect(humanFileSize(500, true)).toBe("500 B");
      });
    });
  });

  describe("milliToHms", () => {
    it("should format milliseconds to HMS", () => {
      expect(milliToHms(0)).toBe("");
      expect(milliToHms(1000)).toBe("1.00s"); // 1 second
      expect(milliToHms(60000)).toBe("1m "); // 1 minute (seconds < 0.01 are omitted)
      expect(milliToHms(3600000)).toBe("1h "); // 1 hour (minutes and seconds are 0)
    });

    it("should handle string inputs", () => {
      expect(milliToHms("1000")).toBe("1.00s");
      expect(milliToHms("60000")).toBe("1m "); // seconds = 0.00, omitted
    });

    it("should handle default parameter", () => {
      expect(milliToHms()).toBe(""); // Default 0
    });

    it("should format complex durations", () => {
      expect(milliToHms(3661000)).toBe("1h 1m 1.00s"); // 1h 1m 1s
      expect(milliToHms(3723456)).toBe("1h 2m 3.46s"); // 1h 2m 3.456s
    });

    it("should handle fractional seconds", () => {
      expect(milliToHms(1500)).toBe("1.50s"); // 1.5 seconds
      expect(milliToHms(100)).toBe("0.10s"); // 0.1 seconds
      expect(milliToHms(10)).toBe("0.01s"); // 0.01 seconds
    });

    it("should omit very small seconds", () => {
      expect(milliToHms(5)).toBe(""); // Less than 0.01s
      expect(milliToHms(9)).toBe(""); // Less than 0.01s
    });

    it("should handle only minutes", () => {
      expect(milliToHms(120000)).toBe("2m "); // 2 minutes (no seconds)
      expect(milliToHms(90500)).toBe("1m 30.50s"); // 1m 30.5s
    });

    it("should handle only hours", () => {
      expect(milliToHms(7200000)).toBe("2h "); // 2 hours (no minutes or seconds)
    });

    it("should handle mixed units", () => {
      expect(milliToHms(4505000)).toBe("1h 15m 5.00s"); // 1h 15m 5s
      expect(milliToHms(7323000)).toBe("2h 2m 3.00s"); // 2h 2m 3s
    });

    it("should handle large durations", () => {
      expect(milliToHms(86400000)).toBe("24h "); // 24 hours (no minutes or seconds)
      expect(milliToHms(90061000)).toBe("25h 1m 1.00s"); // 25h 1m 1s
    });

    it("should handle zero duration", () => {
      expect(milliToHms(0)).toBe("");
    });

    it("should handle negative durations gracefully", () => {
      const result = milliToHms(-1000);
      expect(typeof result).toBe("string");
      // Behavior with negative might vary, but should not crash
    });

    it("should handle very large numbers", () => {
      const veryLarge = *********000; // Very large milliseconds
      const result = milliToHms(veryLarge);
      expect(result).toContain("h");
      expect(typeof result).toBe("string");
    });

    it("should handle non-numeric strings gracefully", () => {
      const result = milliToHms("not a number");
      expect(typeof result).toBe("string");
      // parseFloat("not a number") returns NaN, which should be handled
    });

    it("should handle edge case durations", () => {
      expect(milliToHms(59999)).toBe("60.00s"); // Rounds to 60.00s (59.999 rounds up)
      expect(milliToHms(3599999)).toBe("59m 60.00s"); // 3599.999s = 59m 59.999s, rounds to 60s
    });
  });

  describe("performance and edge cases", () => {
    it("should handle many calls efficiently", () => {
      const startTime = Date.now();

      for (let i = 0; i < 1000; i++) {
        numberWithCommas(i * 1000);
        nFormatter(i * 1000);
        dollarFormat(i);
        toPercentString(i / 100);
        humanFileSize(i * 1024);
        milliToHms(i * 1000);
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete 6000 operations in reasonable time
      expect(duration).toBeLessThan(1000);
    });

    it("should be consistent across multiple calls", () => {
      const testValue = 12345.67;

      expect(numberWithCommas(testValue)).toBe(numberWithCommas(testValue));
      expect(nFormatter(testValue)).toBe(nFormatter(testValue));
      expect(dollarFormat(testValue)).toBe(dollarFormat(testValue));
      expect(toPercentString(testValue / 100)).toBe(
        toPercentString(testValue / 100)
      );
      expect(humanFileSize(testValue)).toBe(humanFileSize(testValue));
      expect(milliToHms(testValue)).toBe(milliToHms(testValue));
    });

    it("should handle concurrent usage", async () => {
      const promises = Array.from({ length: 100 }, (_, i) =>
        Promise.resolve([
          numberWithCommas(i * 1000),
          nFormatter(i * 1000),
          dollarFormat(i),
          toPercentString(i / 100),
          humanFileSize(i * 1024),
          milliToHms(i * 1000),
        ])
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(100);
      results.forEach((resultSet) => {
        expect(resultSet).toHaveLength(6);
        resultSet.forEach((result) => {
          expect(typeof result).toBe("string");
        });
      });
    });
  });
});
