/// <reference types="@testing-library/jest-dom" />
import { getTextSizeClass, TextSize } from "../textSize";

describe("textSize utility functions", () => {
  describe("getTextSizeClass", () => {
    describe("valid text sizes", () => {
      it("should return correct class for small size", () => {
        expect(getTextSizeClass("small")).toBe("text-[12px]");
      });

      it("should return correct class for normal size", () => {
        expect(getTextSizeClass("normal")).toBe("text-[14px]");
      });

      it("should return correct class for large size", () => {
        expect(getTextSizeClass("large")).toBe("text-[18px]");
      });

      it("should return correct class for larger size", () => {
        expect(getTextSizeClass("larger")).toBe("text-[20px]");
      });

      it("should return correct class for default size", () => {
        expect(getTextSizeClass("default")).toBe("text-[14px]");
      });
    });

    describe("default behavior", () => {
      it("should return default class for unknown size", () => {
        // TypeScript would normally prevent this, but testing runtime behavior
        expect(getTextSizeClass("unknown" as TextSize)).toBe("text-[14px]");
      });

      it("should return default class for undefined size", () => {
        expect(getTextSizeClass(undefined as any)).toBe("text-[14px]");
      });

      it("should return default class for null size", () => {
        expect(getTextSizeClass(null as any)).toBe("text-[14px]");
      });

      it("should return default class for empty string", () => {
        expect(getTextSizeClass("" as TextSize)).toBe("text-[14px]");
      });
    });

    describe("case sensitivity", () => {
      it("should be case sensitive and default to text-[14px] for incorrect case", () => {
        expect(getTextSizeClass("Small" as TextSize)).toBe("text-[14px]");
        expect(getTextSizeClass("LARGE" as TextSize)).toBe("text-[14px]");
        expect(getTextSizeClass("Normal" as TextSize)).toBe("text-[14px]");
      });
    });

    describe("type safety", () => {
      it("should work with all valid TextSize values", () => {
        const validSizes: TextSize[] = [
          "small",
          "normal",
          "large",
          "larger",
          "default",
        ];

        validSizes.forEach((size) => {
          const result = getTextSizeClass(size);
          expect(typeof result).toBe("string");
          expect(result).toMatch(/^text-\[\d+px\]$/);
        });
      });
    });

    describe("return value format", () => {
      it("should always return Tailwind CSS class format", () => {
        const sizes: TextSize[] = [
          "small",
          "normal",
          "large",
          "larger",
          "default",
        ];

        sizes.forEach((size) => {
          const className = getTextSizeClass(size);
          expect(className).toMatch(/^text-\[\d+px\]$/);
          expect(className.startsWith("text-[")).toBe(true);
          expect(className.endsWith("px]")).toBe(true);
        });
      });

      it("should return consistent class names", () => {
        // Test that multiple calls return the same result
        expect(getTextSizeClass("small")).toBe(getTextSizeClass("small"));
        expect(getTextSizeClass("large")).toBe(getTextSizeClass("large"));
        expect(getTextSizeClass("normal")).toBe(getTextSizeClass("normal"));
      });
    });

    describe("edge cases", () => {
      it("should handle special characters in size parameter", () => {
        expect(getTextSizeClass("small-special" as TextSize)).toBe(
          "text-[14px]"
        );
        expect(getTextSizeClass("large@" as TextSize)).toBe("text-[14px]");
        expect(getTextSizeClass("normal123" as TextSize)).toBe("text-[14px]");
      });

      it("should handle numeric-like strings", () => {
        expect(getTextSizeClass("1" as TextSize)).toBe("text-[14px]");
        expect(getTextSizeClass("14" as TextSize)).toBe("text-[14px]");
        expect(getTextSizeClass("18px" as TextSize)).toBe("text-[14px]");
      });

      it("should handle whitespace in size parameter", () => {
        expect(getTextSizeClass(" small" as TextSize)).toBe("text-[14px]");
        expect(getTextSizeClass("large " as TextSize)).toBe("text-[14px]");
        expect(getTextSizeClass(" normal " as TextSize)).toBe("text-[14px]");
      });
    });

    describe("pixel size progression", () => {
      it("should have logical pixel size progression", () => {
        const smallPx = parseInt(
          getTextSizeClass("small").match(/\d+/)?.[0] || "0"
        );
        const normalPx = parseInt(
          getTextSizeClass("normal").match(/\d+/)?.[0] || "0"
        );
        const largePx = parseInt(
          getTextSizeClass("large").match(/\d+/)?.[0] || "0"
        );
        const largerPx = parseInt(
          getTextSizeClass("larger").match(/\d+/)?.[0] || "0"
        );

        expect(smallPx).toBeLessThan(normalPx);
        expect(normalPx).toBeLessThan(largePx);
        expect(largePx).toBeLessThan(largerPx);

        // Verify specific values
        expect(smallPx).toBe(12);
        expect(normalPx).toBe(14);
        expect(largePx).toBe(18);
        expect(largerPx).toBe(20);
      });

      it("should have default size same as normal", () => {
        expect(getTextSizeClass("default")).toBe(getTextSizeClass("normal"));
      });
    });

    describe("performance and consistency", () => {
      it("should handle many calls efficiently", () => {
        const startTime = Date.now();

        for (let i = 0; i < 1000; i++) {
          getTextSizeClass("large");
          getTextSizeClass("small");
          getTextSizeClass("normal");
        }

        const endTime = Date.now();
        const duration = endTime - startTime;

        // Should complete 3000 calls in less than 100ms
        expect(duration).toBeLessThan(100);
      });

      it("should be a pure function", () => {
        // Same input should always produce same output
        const size: TextSize = "large";
        const result1 = getTextSizeClass(size);
        const result2 = getTextSizeClass(size);
        const result3 = getTextSizeClass(size);

        expect(result1).toBe(result2);
        expect(result2).toBe(result3);
      });
    });

    describe("integration with CSS", () => {
      it("should return valid Tailwind CSS arbitrary value classes", () => {
        const sizes: TextSize[] = [
          "small",
          "normal",
          "large",
          "larger",
          "default",
        ];

        sizes.forEach((size) => {
          const className = getTextSizeClass(size);

          // Should be a valid arbitrary value class
          expect(className).toMatch(/^text-\[\d+px\]$/);

          // Extract pixel value
          const pxValue = className.match(/\[(\d+)px\]/)?.[1];
          expect(pxValue).toBeDefined();
          expect(parseInt(pxValue!)).toBeGreaterThan(0);
        });
      });

      it("should work well with Tailwind CSS class combinations", () => {
        const baseClasses = "font-medium text-gray-900";
        const sizeClass = getTextSizeClass("large");
        const combinedClasses = `${baseClasses} ${sizeClass}`;

        expect(combinedClasses).toBe("font-medium text-gray-900 text-[18px]");
        expect(combinedClasses.split(" ")).toHaveLength(3);
      });
    });
  });

  describe("TextSize type", () => {
    it("should include all expected size options", () => {
      // This test ensures the type is defined correctly
      const validSizes: TextSize[] = [
        "small",
        "normal",
        "large",
        "larger",
        "default",
      ];

      // Each size should produce a valid class
      validSizes.forEach((size) => {
        expect(() => getTextSizeClass(size)).not.toThrow();
        expect(getTextSizeClass(size)).toBeTruthy();
      });
    });
  });
});
