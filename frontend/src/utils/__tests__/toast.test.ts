import { describe, it, expect, jest, beforeEach } from "@jest/globals";
import { toast } from "react-toastify";
import showToast from "../toast";

// Mock react-toastify
jest.mock("react-toastify", () => {
  const mockToastFunction = jest.fn();
  return {
    toast: Object.assign(mockToastFunction, {
      success: jest.fn(),
      error: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      dismiss: jest.fn(),
    }),
  };
});

// Create a properly typed mock
const mockToast = toast as jest.MockedFunction<typeof toast> & {
  success: jest.MockedFunction<typeof toast.success>;
  error: jest.MockedFunction<typeof toast.error>;
  info: jest.MockedFunction<typeof toast.info>;
  warn: jest.MockedFunction<typeof toast.warn>;
  dismiss: jest.MockedFunction<typeof toast.dismiss>;
};

describe("showToast Utility Function", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Basic Functionality", () => {
    it("should call toast.success with correct message and options for success type", () => {
      const message = "Operation completed successfully";
      const expectedOptions = {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      };

      showToast(message, "success");

      expect(mockToast.success).toHaveBeenCalledWith(message, expectedOptions);
      expect(mockToast.success).toHaveBeenCalledTimes(1);
    });

    it("should call toast.error with correct message and options for error type", () => {
      const message = "Something went wrong";
      const expectedOptions = {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      };

      showToast(message, "error");

      expect(mockToast.error).toHaveBeenCalledWith(message, expectedOptions);
      expect(mockToast.error).toHaveBeenCalledTimes(1);
    });

    it("should call toast.info with correct message and options for info type", () => {
      const message = "Information message";
      const expectedOptions = {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      };

      showToast(message, "info");

      expect(mockToast.info).toHaveBeenCalledWith(message, expectedOptions);
      expect(mockToast.info).toHaveBeenCalledTimes(1);
    });

    it("should call toast.warn with correct message and options for warning type", () => {
      const message = "Warning message";
      const expectedOptions = {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      };

      showToast(message, "warning");

      expect(mockToast.warn).toHaveBeenCalledWith(message, expectedOptions);
      expect(mockToast.warn).toHaveBeenCalledTimes(1);
    });

    it("should call default toast function with correct message and options for default type", () => {
      const message = "Default message";
      const expectedOptions = {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      };

      showToast(message, "default");

      expect(
        mockToast as jest.MockedFunction<typeof toast>
      ).toHaveBeenCalledWith(message, expectedOptions);
    });

    it("should default to default toast type when no type is specified", () => {
      const message = "Default message";
      const expectedOptions = {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      };

      showToast(message);

      expect(
        mockToast as jest.MockedFunction<typeof toast>
      ).toHaveBeenCalledWith(message, expectedOptions);
    });
  });

  describe("Custom Options", () => {
    it("should merge custom options with default options", () => {
      const message = "Custom options message";
      const customOptions = {
        position: "bottom-left" as const,
        autoClose: 3000,
        hideProgressBar: true,
      };

      const expectedOptions = {
        position: "bottom-left",
        autoClose: 3000,
        hideProgressBar: true,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      };

      showToast(message, "success", customOptions);

      expect(mockToast.success).toHaveBeenCalledWith(message, expectedOptions);
    });

    it("should override all default options with custom options", () => {
      const message = "Override all options";
      const customOptions = {
        position: "bottom-center" as const,
        autoClose: 10000,
        hideProgressBar: true,
        closeOnClick: false,
        pauseOnHover: false,
        draggable: false,
      };

      showToast(message, "info", customOptions);

      expect(mockToast.info).toHaveBeenCalledWith(message, customOptions);
    });

    it("should handle partial custom options", () => {
      const message = "Partial options";
      const customOptions = {
        autoClose: 1000,
      };

      const expectedOptions = {
        position: "top-right",
        autoClose: 1000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      };

      showToast(message, "warning", customOptions);

      expect(mockToast.warn).toHaveBeenCalledWith(message, expectedOptions);
    });
  });

  describe("Clear Functionality", () => {
    it("should call toast.dismiss when clear option is true", () => {
      const message = "Clear previous toasts";
      const options = { clear: true };

      showToast(message, "success", options);

      expect(mockToast.dismiss).toHaveBeenCalledTimes(1);
      expect(mockToast.success).toHaveBeenCalledTimes(1);
    });

    it("should not call toast.dismiss when clear option is false", () => {
      const message = "Do not clear previous toasts";
      const options = { clear: false };

      showToast(message, "error", options);

      expect(mockToast.dismiss).not.toHaveBeenCalled();
      expect(mockToast.error).toHaveBeenCalledTimes(1);
    });

    it("should not call toast.dismiss when clear option is not provided", () => {
      const message = "No clear option";

      showToast(message, "info");

      expect(mockToast.dismiss).not.toHaveBeenCalled();
      expect(mockToast.info).toHaveBeenCalledTimes(1);
    });

    it("should call toast.dismiss before showing toast when clear is true", () => {
      const message = "Clear and show";
      const options = { clear: true };

      showToast(message, "success", options);

      // Check that dismiss was called before the toast
      const dismissCall = mockToast.dismiss.mock.calls[0];
      const successCall = mockToast.success.mock.calls[0];

      expect(dismissCall).toBeDefined();
      expect(successCall).toBeDefined();
    });
  });

  describe("Edge Cases and Error Handling", () => {
    it("should handle empty string message", () => {
      const message = "";
      const expectedOptions = {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      };

      showToast(message, "success");

      expect(mockToast.success).toHaveBeenCalledWith(message, expectedOptions);
    });

    it("should handle very long messages", () => {
      const longMessage = "A".repeat(1000);
      const expectedOptions = {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      };

      showToast(longMessage, "error");

      expect(mockToast.error).toHaveBeenCalledWith(
        longMessage,
        expectedOptions
      );
    });

    it("should handle messages with special characters", () => {
      const specialMessage =
        "Test with <script>alert('xss')</script> & symbols";
      const expectedOptions = {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      };

      showToast(specialMessage, "warning");

      expect(mockToast.warn).toHaveBeenCalledWith(
        specialMessage,
        expectedOptions
      );
    });

    it("should handle multi-line messages", () => {
      const multiLineMessage = "Line 1\nLine 2\nLine 3";
      const expectedOptions = {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      };

      showToast(multiLineMessage, "info");

      expect(mockToast.info).toHaveBeenCalledWith(
        multiLineMessage,
        expectedOptions
      );
    });

    it("should handle null or undefined options gracefully", () => {
      const message = "Null options test";
      const expectedOptions = {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      };

      showToast(message, "success", null as any);

      expect(mockToast.success).toHaveBeenCalledWith(message, expectedOptions);
    });
  });

  describe("Toast Type Variations", () => {
    it("should handle all toast types correctly", () => {
      const message = "Type test";
      const types: Array<"success" | "error" | "info" | "warning" | "default"> =
        ["success", "error", "info", "warning", "default"];

      types.forEach((type) => {
        jest.clearAllMocks();
        showToast(message, type);

        switch (type) {
          case "success":
            expect(mockToast.success).toHaveBeenCalledTimes(1);
            break;
          case "error":
            expect(mockToast.error).toHaveBeenCalledTimes(1);
            break;
          case "info":
            expect(mockToast.info).toHaveBeenCalledTimes(1);
            break;
          case "warning":
            expect(mockToast.warn).toHaveBeenCalledTimes(1);
            break;
          case "default":
            expect(
              mockToast as jest.MockedFunction<typeof toast>
            ).toHaveBeenCalledTimes(1);
            break;
        }
      });
    });
  });

  describe("Options Validation", () => {
    it("should preserve all valid ToastOptions", () => {
      const message = "Options validation test";
      const customOptions = {
        position: "top-center" as const,
        autoClose: 2000,
        hideProgressBar: true,
        closeOnClick: false,
        pauseOnHover: false,
        draggable: false,
        progress: 0.5,
        toastId: "unique-id",
        onOpen: jest.fn(),
        onClose: jest.fn(),
        className: "custom-class",
        bodyClassName: "custom-body-class",
        progressClassName: "custom-progress-class",
        transition: "slide" as any,
        role: "alert" as const,
        theme: "dark" as const,
      };

      showToast(message, "success", customOptions);

      expect(mockToast.success).toHaveBeenCalledWith(message, customOptions);
    });

    it("should handle custom options with clear flag", () => {
      const message = "Custom options with clear";
      const customOptions = {
        position: "bottom-right" as const,
        autoClose: 3000,
        clear: true,
      };

      // The clear option will be included in the options passed to toast
      const expectedOptions = {
        position: "bottom-right",
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        clear: true,
      };

      showToast(message, "error", customOptions);

      expect(mockToast.dismiss).toHaveBeenCalledTimes(1);
      expect(mockToast.error).toHaveBeenCalledWith(message, expectedOptions);
    });
  });

  describe("Performance and Memory", () => {
    it("should handle multiple consecutive calls efficiently", () => {
      const baseMessage = "Performance test";

      for (let i = 0; i < 10; i++) {
        showToast(`${baseMessage} ${i}`, "info");
      }

      expect(mockToast.info).toHaveBeenCalledTimes(10);
    });

    it("should handle rapid calls with clear option", () => {
      const message = "Rapid clear test";
      const options = { clear: true };

      for (let i = 0; i < 5; i++) {
        showToast(`${message} ${i}`, "success", options);
      }

      expect(mockToast.dismiss).toHaveBeenCalledTimes(5);
      expect(mockToast.success).toHaveBeenCalledTimes(5);
    });
  });

  describe("Integration with React-Toastify", () => {
    it("should return void", () => {
      const result = showToast("Test message", "success");
      expect(result).toBeUndefined();
    });

    it("should not throw errors when toast functions are called", () => {
      expect(() => {
        showToast("Test message", "success");
      }).not.toThrow();

      expect(() => {
        showToast("Test message", "error");
      }).not.toThrow();

      expect(() => {
        showToast("Test message", "info");
      }).not.toThrow();

      expect(() => {
        showToast("Test message", "warning");
      }).not.toThrow();

      expect(() => {
        showToast("Test message", "default");
      }).not.toThrow();
    });
  });

  describe("Default Options Configuration", () => {
    it("should use correct default position", () => {
      showToast("Position test", "success");

      const calledOptions = mockToast.success.mock.calls[0][1];
      expect(calledOptions?.position).toBe("top-right");
    });

    it("should use correct default autoClose time", () => {
      showToast("AutoClose test", "success");

      const calledOptions = mockToast.success.mock.calls[0][1];
      expect(calledOptions?.autoClose).toBe(5000);
    });

    it("should use correct default interaction settings", () => {
      showToast("Interaction test", "success");

      const calledOptions = mockToast.success.mock.calls[0][1];
      expect(calledOptions?.hideProgressBar).toBe(false);
      expect(calledOptions?.closeOnClick).toBe(true);
      expect(calledOptions?.pauseOnHover).toBe(true);
      expect(calledOptions?.draggable).toBe(true);
    });
  });

  describe("Type Safety and TypeScript Integration", () => {
    it("should work with all valid toast types", () => {
      // These should not cause TypeScript errors
      expect(() => {
        showToast("Test", "success");
        showToast("Test", "error");
        showToast("Test", "info");
        showToast("Test", "warning");
        showToast("Test", "default");
      }).not.toThrow();
    });

    it("should handle options with proper typing", () => {
      const options = {
        position: "top-left" as const,
        autoClose: 3000,
        hideProgressBar: true,
        clear: true,
      };

      expect(() => {
        showToast("Type safety test", "success", options);
      }).not.toThrow();
    });
  });
});
