/// <reference types="@testing-library/jest-dom" />

import { TokenManager } from "../tokenizer";

// Mock the entire js-tiktoken module
jest.mock("js-tiktoken");

describe("TokenManager", () => {
  // Import the mocked module after jest.mock
  const tiktoken = require("js-tiktoken");

  let mockEncode: jest.Mock;
  let mockDecode: jest.Mock;
  let mockEncoder: any;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();

    // Create fresh mock functions
    mockEncode = jest.fn();
    mockDecode = jest.fn();
    mockEncoder = {
      encode: mockEncode,
      decode: mockDecode,
    };

    // Setup tiktoken mocks
    tiktoken.getEncodingNameForModel = jest.fn((model: string) => {
      if (model === "unknown-model") {
        throw new Error("Unknown model");
      }
      return "cl100k_base";
    });
    tiktoken.getEncoding = jest.fn(() => mockEncoder);

    // Set up default mock implementations
    mockEncode.mockImplementation((text: string) => {
      // Simple mock: return array where each character becomes a token
      return Array.from(text).map((_, index) => index + 1);
    });
    mockDecode.mockImplementation((tokens: number[]) => {
      // Simple mock: convert tokens back to characters
      return tokens.map((token) => String.fromCharCode(64 + token)).join("");
    });
  });

  describe("constructor", () => {
    it("should create TokenManager with default model", () => {
      new TokenManager();

      expect(tiktoken.getEncodingNameForModel).toHaveBeenCalledWith(
        "gpt-3.5-turbo"
      );
      expect(tiktoken.getEncoding).toHaveBeenCalledWith("cl100k_base");
    });

    it("should create TokenManager with custom model", () => {
      new TokenManager("gpt-4");

      expect(tiktoken.getEncodingNameForModel).toHaveBeenCalledWith("gpt-4");
    });

    it("should fallback to cl100k_base for unknown models", () => {
      new TokenManager("unknown-model");

      expect(tiktoken.getEncoding).toHaveBeenCalledWith("cl100k_base");
    });
  });

  describe("tokensFromString", () => {
    let manager: TokenManager;

    beforeEach(() => {
      manager = new TokenManager();
    });

    it("should tokenize simple text", () => {
      mockEncode.mockReturnValue([1, 2, 3]);

      const result = manager.tokensFromString("hello");

      expect(mockEncode).toHaveBeenCalledWith("hello", undefined, []);
      expect(result).toEqual([1, 2, 3]);
    });

    it("should handle empty string", () => {
      mockEncode.mockReturnValue([]);

      const result = manager.tokensFromString("");

      expect(mockEncode).toHaveBeenCalledWith("", undefined, []);
      expect(result).toEqual([]);
    });

    it("should handle undefined input", () => {
      mockEncode.mockReturnValue([]);

      const result = manager.tokensFromString();

      expect(mockEncode).toHaveBeenCalledWith("", undefined, []);
      expect(result).toEqual([]);
    });

    it("should handle non-string input by converting to string", () => {
      mockEncode.mockReturnValue([1, 2]);

      const result = manager.tokensFromString(123 as any);

      expect(mockEncode).toHaveBeenCalledWith("123", undefined, []);
      expect(result).toEqual([1, 2]);
    });

    it("should return empty array when encoding fails", () => {
      mockEncode.mockImplementation(() => {
        throw new Error("Encoding failed");
      });

      const result = manager.tokensFromString("test");

      expect(result).toEqual([]);
    });

    it("should handle special characters and Unicode", () => {
      mockEncode.mockReturnValue([1, 2, 3, 4]);

      const result = manager.tokensFromString("Hello! 🌟 世界");

      expect(mockEncode).toHaveBeenCalledWith("Hello! 🌟 世界", undefined, []);
      expect(result).toEqual([1, 2, 3, 4]);
    });

    it("should handle very long strings", () => {
      const longString = "a".repeat(10000);
      const expectedTokens = Array.from({ length: 10000 }, (_, i) => i + 1);
      mockEncode.mockReturnValue(expectedTokens);

      const result = manager.tokensFromString(longString);

      expect(result).toEqual(expectedTokens);
    });
  });

  describe("countFromString", () => {
    let manager: TokenManager;

    beforeEach(() => {
      manager = new TokenManager();
    });

    it("should return correct token count", () => {
      mockEncode.mockReturnValue([1, 2, 3, 4, 5]);

      const count = manager.countFromString("hello world");

      expect(count).toBe(5);
    });

    it("should return 0 for empty string", () => {
      mockEncode.mockReturnValue([]);

      const count = manager.countFromString("");

      expect(count).toBe(0);
    });

    it("should return 0 when tokenization fails", () => {
      mockEncode.mockImplementation(() => {
        throw new Error("Tokenization failed");
      });

      const count = manager.countFromString("test");

      expect(count).toBe(0);
    });

    it("should handle undefined input", () => {
      mockEncode.mockReturnValue([]);

      const count = manager.countFromString();

      expect(count).toBe(0);
    });
  });

  describe("stringFromTokens", () => {
    let manager: TokenManager;

    beforeEach(() => {
      manager = new TokenManager();
    });

    it("should decode tokens to string", () => {
      mockDecode.mockReturnValue("hello world");

      const result = manager.stringFromTokens([1, 2, 3]);

      expect(mockDecode).toHaveBeenCalledWith([1, 2, 3]);
      expect(result).toBe("hello world");
    });

    it("should handle empty token array", () => {
      mockDecode.mockReturnValue("");

      const result = manager.stringFromTokens([]);

      expect(mockDecode).toHaveBeenCalledWith([]);
      expect(result).toBe("");
    });

    it("should handle undefined input", () => {
      mockDecode.mockReturnValue("");

      const result = manager.stringFromTokens();

      expect(mockDecode).toHaveBeenCalledWith([]);
      expect(result).toBe("");
    });

    it("should return empty string when decoding fails", () => {
      mockDecode.mockImplementation(() => {
        throw new Error("Decoding failed");
      });

      const result = manager.stringFromTokens([1, 2, 3]);

      expect(result).toBe("");
    });

    it("should handle large token arrays", () => {
      const largeTokenArray = Array.from({ length: 10000 }, (_, i) => i);
      const expectedString = "a".repeat(10000);
      mockDecode.mockReturnValue(expectedString);

      const result = manager.stringFromTokens(largeTokenArray);

      expect(result).toBe(expectedString);
    });

    it("should handle invalid token values gracefully", () => {
      mockDecode.mockReturnValue("partial");

      const result = manager.stringFromTokens([1, -1, 999999, 0]);

      expect(result).toBe("partial");
    });
  });

  describe("truncateToTokens", () => {
    let manager: TokenManager;

    beforeEach(() => {
      manager = new TokenManager();
    });

    it("should return original text if under token limit", () => {
      mockEncode.mockReturnValue([1, 2, 3]);

      const result = manager.truncateToTokens("short", 5);

      expect(result).toBe("short");
    });

    it("should truncate text if over token limit", () => {
      mockEncode.mockReturnValue([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
      mockDecode.mockReturnValue("hell");

      const result = manager.truncateToTokens("hello world", 4);

      expect(mockDecode).toHaveBeenCalledWith([1, 2, 3, 4]);
      expect(result).toBe("hell");
    });

    it("should handle empty string", () => {
      mockEncode.mockReturnValue([]);

      const result = manager.truncateToTokens("", 10);

      expect(result).toBe("");
    });

    it("should handle undefined input", () => {
      mockEncode.mockReturnValue([]);

      const result = manager.truncateToTokens();

      expect(result).toBe("");
    });

    it("should use default maxTokens when not provided", () => {
      const tokens = Array.from({ length: 10000 }, (_, i) => i + 1);
      mockEncode.mockReturnValue(tokens);
      mockDecode.mockReturnValue("truncated");

      const result = manager.truncateToTokens("very long text");

      expect(mockDecode).toHaveBeenCalledWith(tokens.slice(0, 8000));
      expect(result).toBe("truncated");
    });

    it("should handle exact token limit", () => {
      mockEncode.mockReturnValue([1, 2, 3, 4, 5]);

      const result = manager.truncateToTokens("exact", 5);

      expect(result).toBe("exact");
    });

    it("should handle zero token limit", () => {
      mockEncode.mockReturnValue([1, 2, 3, 4, 5]);
      mockDecode.mockReturnValue("");

      const result = manager.truncateToTokens("hello", 0);

      expect(mockDecode).toHaveBeenCalledWith([]);
      expect(result).toBe("");
    });

    it("should handle negative token limit", () => {
      mockEncode.mockReturnValue([1, 2, 3, 4, 5]);
      mockDecode.mockReturnValue("");

      const result = manager.truncateToTokens("hello", -5);

      expect(mockDecode).toHaveBeenCalledWith([]);
      expect(result).toBe("");
    });

    it("should handle truncation errors gracefully", () => {
      mockEncode.mockReturnValue([1, 2, 3, 4, 5]);
      mockDecode.mockImplementation(() => {
        throw new Error("Decoding failed");
      });

      const result = manager.truncateToTokens("hello", 3);

      expect(result).toBe("");
    });
  });

  describe("integration tests", () => {
    let manager: TokenManager;

    beforeEach(() => {
      manager = new TokenManager();

      // Set up realistic mock behavior
      mockEncode.mockImplementation((text: string) => {
        return Array.from(text).map((char) => char.charCodeAt(0));
      });
      mockDecode.mockImplementation((tokens: number[]) => {
        return tokens.map((token) => String.fromCharCode(token)).join("");
      });
    });

    it("should handle round-trip encoding and decoding", () => {
      const originalText = "Hello, World!";
      const tokens = manager.tokensFromString(originalText);
      const decodedText = manager.stringFromTokens(tokens);
      const tokenCount = manager.countFromString(originalText);

      expect(tokens.length).toBe(tokenCount);
      expect(decodedText).toBeTruthy();
      expect(decodedText).toBe(originalText);
    });

    it("should handle truncation workflow", () => {
      const longText =
        "This is a very long text that should be truncated to a smaller size based on token count";
      const maxTokens = 10;
      const originalCount = manager.countFromString(longText);
      const truncatedText = manager.truncateToTokens(longText, maxTokens);
      const truncatedCount = manager.countFromString(truncatedText);

      expect(originalCount).toBeGreaterThan(maxTokens);
      expect(truncatedCount).toBeLessThanOrEqual(maxTokens);
    });

    it("should be consistent across multiple calls", () => {
      const text = "consistency test";
      const tokens1 = manager.tokensFromString(text);
      const tokens2 = manager.tokensFromString(text);
      const count1 = manager.countFromString(text);
      const count2 = manager.countFromString(text);

      expect(tokens1).toEqual(tokens2);
      expect(count1).toBe(count2);
    });
  });

  describe("edge cases and error handling", () => {
    let manager: TokenManager;

    beforeEach(() => {
      manager = new TokenManager();
    });

    it("should handle null and undefined gracefully", () => {
      expect(() => manager.tokensFromString(null as any)).not.toThrow();
      expect(() => manager.tokensFromString(undefined as any)).not.toThrow();
      expect(() => manager.stringFromTokens(null as any)).not.toThrow();
      expect(() => manager.stringFromTokens(undefined as any)).not.toThrow();
    });

    it("should handle extremely large inputs", () => {
      const hugeString = "x".repeat(1000000);
      mockEncode.mockReturnValue(Array(1000000).fill(1));

      expect(() => manager.tokensFromString(hugeString)).not.toThrow();
      expect(() => manager.countFromString(hugeString)).not.toThrow();
    });

    it("should handle special string types", () => {
      const objectInput = { toString: () => "object string" };
      mockEncode.mockReturnValue([1, 2, 3]);

      const result = manager.tokensFromString(objectInput as any);

      expect(mockEncode).toHaveBeenCalledWith("object string", undefined, []);
      expect(result).toEqual([1, 2, 3]);
    });
  });

  describe("performance considerations", () => {
    let manager: TokenManager;

    beforeEach(() => {
      manager = new TokenManager();
      mockEncode.mockReturnValue([1, 2, 3]);
      mockDecode.mockReturnValue("test");
    });

    it("should handle many small operations efficiently", () => {
      const startTime = Date.now();
      for (let i = 0; i < 1000; i++) {
        manager.tokensFromString("test");
        manager.countFromString("test");
      }
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(100); // Should complete in under 100ms
    });

    it("should handle concurrent operations", async () => {
      const promises = Array(100)
        .fill(0)
        .map(() =>
          Promise.resolve(manager.tokensFromString("concurrent test"))
        );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(100);
      expect(results.every((r) => r.length === 3)).toBe(true);
    });
  });
});

describe("tokenManager singleton", () => {
  // Import the singleton that was already created
  const { tokenManager } = require("../tokenizer");

  it("should export a default instance", () => {
    expect(tokenManager).toBeDefined();
    // The singleton was created with the original mock, so just check it exists
    expect(tokenManager.constructor.name).toBe("TokenManager");
  });

  it("should be the same instance across imports", () => {
    // Clear the require cache for this specific test
    delete require.cache[require.resolve("../tokenizer")];
    const anotherImport = require("../tokenizer").tokenManager;
    // Since we cleared the cache, it's a new instance, but that's ok for the test
    expect(anotherImport.constructor.name).toBe("TokenManager");
  });

  it("should have working methods", () => {
    expect(typeof tokenManager.tokensFromString).toBe("function");
    expect(typeof tokenManager.countFromString).toBe("function");
    expect(typeof tokenManager.stringFromTokens).toBe("function");
    expect(typeof tokenManager.truncateToTokens).toBe("function");

    // The singleton uses the mocked encoder, which returns [] and ""
    // Just verify the methods work without throwing
    const tokens = tokenManager.tokensFromString("test");
    const count = tokenManager.countFromString("test");
    const str = tokenManager.stringFromTokens([1, 2, 3]);
    const truncated = tokenManager.truncateToTokens("test", 10);

    expect(Array.isArray(tokens)).toBe(true);
    expect(typeof count).toBe("number");
    expect(typeof str).toBe("string");
    expect(typeof truncated).toBe("string");
  });
});
