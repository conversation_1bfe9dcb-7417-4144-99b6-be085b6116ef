/// <reference types="@testing-library/jest-dom" />
import { castToType } from "../types";

describe("castToType utility function", () => {
  describe("openAiTemp casting", () => {
    it("should cast string numbers to number", () => {
      const result = castToType("openAiTemp", "0.7");
      expect(result).toBe(0.7);
      expect(typeof result).toBe("number");
    });

    it("should cast integer strings to number", () => {
      const result = castToType("openAiTemp", "1");
      expect(result).toBe(1);
      expect(typeof result).toBe("number");
    });

    it("should cast already number values", () => {
      const result = castToType("openAiTemp", 0.5);
      expect(result).toBe(0.5);
      expect(typeof result).toBe("number");
    });

    it("should cast boolean to number", () => {
      expect(castToType("openAiTemp", true)).toBe(1);
      expect(castToType("openAiTemp", false)).toBe(0);
    });

    it("should cast null to 0", () => {
      const result = castToType("openAiTemp", null);
      expect(result).toBe(0);
    });

    it("should cast undefined to NaN", () => {
      const result = castToType("openAiTemp", undefined);
      expect(result).toBeNaN();
    });

    it("should cast non-numeric strings to NaN", () => {
      const result = castToType("openAiTemp", "not a number");
      expect(result).toBeNaN();
    });

    it("should cast empty string to 0", () => {
      const result = castToType("openAiTemp", "");
      expect(result).toBe(0);
    });

    it("should cast arrays to NaN", () => {
      const result = castToType("openAiTemp", [1, 2, 3]);
      expect(result).toBeNaN();
    });

    it("should cast objects to NaN", () => {
      const result = castToType("openAiTemp", { value: 0.5 });
      expect(result).toBeNaN();
    });
  });

  describe("openAiHistory casting", () => {
    it("should cast string numbers to number", () => {
      const result = castToType("openAiHistory", "10");
      expect(result).toBe(10);
      expect(typeof result).toBe("number");
    });

    it("should cast floating point strings to number", () => {
      const result = castToType("openAiHistory", "5.5");
      expect(result).toBe(5.5);
      expect(typeof result).toBe("number");
    });

    it("should cast negative numbers", () => {
      const result = castToType("openAiHistory", "-3");
      expect(result).toBe(-3);
    });

    it("should cast zero", () => {
      const result = castToType("openAiHistory", "0");
      expect(result).toBe(0);
    });

    it("should handle scientific notation", () => {
      const result = castToType("openAiHistory", "1e3");
      expect(result).toBe(1000);
    });

    it("should cast boolean values", () => {
      expect(castToType("openAiHistory", true)).toBe(1);
      expect(castToType("openAiHistory", false)).toBe(0);
    });
  });

  describe("similarityThreshold casting", () => {
    it("should cast string numbers using parseFloat", () => {
      const result = castToType("similarityThreshold", "0.85");
      expect(result).toBe(0.85);
      expect(typeof result).toBe("number");
    });

    it("should cast numbers directly", () => {
      const result = castToType("similarityThreshold", 0.75);
      expect(result).toBe(0.75);
    });

    it("should handle decimal precision", () => {
      const result = castToType("similarityThreshold", "0.123456789");
      expect(result).toBe(0.123456789);
    });

    it("should handle scientific notation", () => {
      const result = castToType("similarityThreshold", "1.5e-2");
      expect(result).toBe(0.015);
    });

    it("should parse only the numeric part of mixed strings", () => {
      const result = castToType("similarityThreshold", "0.8 threshold");
      expect(result).toBe(0.8);
    });

    it("should cast non-numeric strings to NaN", () => {
      const result = castToType("similarityThreshold", "not a number");
      expect(result).toBeNaN();
    });

    it("should handle null and undefined", () => {
      // parseFloat(String(null)) = parseFloat("null") = NaN
      expect(castToType("similarityThreshold", null)).toBeNaN();
      expect(castToType("similarityThreshold", undefined)).toBeNaN();
    });

    it("should cast boolean values", () => {
      // parseFloat(String(true)) = parseFloat("true") = NaN
      // parseFloat(String(false)) = parseFloat("false") = NaN
      expect(castToType("similarityThreshold", true)).toBeNaN();
      expect(castToType("similarityThreshold", false)).toBeNaN();
    });
  });

  describe("topN casting", () => {
    it("should cast string integers to number", () => {
      const result = castToType("topN", "5");
      expect(result).toBe(5);
      expect(typeof result).toBe("number");
    });

    it("should cast floating point strings (loses decimal precision)", () => {
      const result = castToType("topN", "5.7");
      expect(result).toBe(5.7);
    });

    it("should cast large numbers", () => {
      const result = castToType("topN", "1000000");
      expect(result).toBe(1000000);
    });

    it("should handle edge case numbers", () => {
      expect(castToType("topN", "0")).toBe(0);
      expect(castToType("topN", "-1")).toBe(-1);
      expect(castToType("topN", "Infinity")).toBe(Infinity);
      expect(castToType("topN", "-Infinity")).toBe(-Infinity);
    });

    it("should cast arrays and objects", () => {
      // Number([5]) = 5 (single element array becomes the element)
      expect(castToType("topN", [5])).toBe(5);
      // Number([1,2,3]) = NaN (multiple elements)
      expect(castToType("topN", [1, 2, 3])).toBeNaN();
      // Number({n: 5}) = NaN (objects become NaN)
      expect(castToType("topN", { n: 5 })).toBeNaN();
    });
  });

  describe("unknown keys (no casting)", () => {
    it("should return value as-is for unknown string keys", () => {
      const originalValue = "some string";
      const result = castToType("unknownKey", originalValue);

      expect(result).toBe(originalValue);
      expect(typeof result).toBe("string");
    });

    it("should return objects as-is for unknown keys", () => {
      const originalValue = { test: "value" };
      const result = castToType("unknownKey", originalValue);

      expect(result).toBe(originalValue);
      expect(result).toEqual({ test: "value" });
    });

    it("should return arrays as-is for unknown keys", () => {
      const originalValue = [1, 2, 3];
      const result = castToType("unknownKey", originalValue);

      expect(result).toBe(originalValue);
      expect(result).toEqual([1, 2, 3]);
    });

    it("should return boolean as-is for unknown keys", () => {
      expect(castToType("unknownKey", true)).toBe(true);
      expect(castToType("unknownKey", false)).toBe(false);
    });

    it("should return null as-is for unknown keys", () => {
      const result = castToType("unknownKey", null);
      expect(result).toBe(null);
    });

    it("should return undefined as-is for unknown keys", () => {
      const result = castToType("unknownKey", undefined);
      expect(result).toBe(undefined);
    });

    it("should handle empty string key", () => {
      const originalValue = "test";
      const result = castToType("", originalValue);

      expect(result).toBe(originalValue);
    });

    it("should handle special characters in key", () => {
      const originalValue = 123;
      const result = castToType(
        "key-with-dashes_and_underscores",
        originalValue
      );

      expect(result).toBe(originalValue);
    });
  });

  describe("edge cases and error handling", () => {
    it("should handle all castable keys correctly", () => {
      const castableKeys = [
        "openAiTemp",
        "openAiHistory",
        "similarityThreshold",
        "topN",
      ];

      castableKeys.forEach((key) => {
        const result = castToType(key, "1.5");
        expect(typeof result).toBe("number");
        expect(result).toBe(1.5);
      });
    });

    it("should handle case sensitivity (keys are case sensitive)", () => {
      // These should NOT be cast because they don't match exactly
      expect(castToType("OpenAiTemp", "1.5")).toBe("1.5");
      expect(castToType("OPENAITEMP", "1.5")).toBe("1.5");
      expect(castToType("openAITemp", "1.5")).toBe("1.5");
    });

    it("should handle special numeric values", () => {
      expect(castToType("openAiTemp", "NaN")).toBeNaN();
      expect(castToType("openAiTemp", "Infinity")).toBe(Infinity);
      expect(castToType("openAiTemp", "-Infinity")).toBe(-Infinity);
    });

    it("should handle very large numbers", () => {
      const largeNumber = "9007199254740991"; // Number.MAX_SAFE_INTEGER
      const result = castToType("topN", largeNumber);
      expect(typeof result).toBe("number");
      expect(result).toBe(9007199254740991);
    });

    it("should handle very small numbers", () => {
      const smallNumber = "0.00000000001";
      const result = castToType("similarityThreshold", smallNumber);
      expect(typeof result).toBe("number");
      expect(result).toBe(0.00000000001);
    });

    it("should handle whitespace in values", () => {
      expect(castToType("openAiTemp", "  1.5  ")).toBe(1.5);
      // parseFloat handles leading numbers but stops at non-numeric characters
      expect(castToType("similarityThreshold", "0.8 extra")).toBe(0.8);
      // Actual tab and newline characters (not escaped strings)
      expect(castToType("similarityThreshold", "\t0.8\n")).toBe(0.8);
    });

    it("should handle hexadecimal strings", () => {
      expect(castToType("topN", "0x10")).toBe(16);
      expect(castToType("openAiHistory", "0xFF")).toBe(255);
    });

    it("should handle binary strings", () => {
      expect(castToType("topN", "0b1010")).toBe(10);
    });

    it("should handle octal strings", () => {
      expect(castToType("topN", "0o777")).toBe(511);
    });
  });

  describe("type safety and generics", () => {
    it("should maintain type information for unknown keys", () => {
      interface TestType {
        name: string;
        value: number;
      }

      const testObject: TestType = { name: "test", value: 42 };
      const result = castToType<TestType>("unknownKey", testObject);

      // TypeScript should infer this as TestType | number
      expect(result).toEqual(testObject);
    });

    it("should return number for known keys regardless of generic type", () => {
      const result = castToType<string>("openAiTemp", "1.5");

      // Despite generic being string, result should be number due to casting
      expect(typeof result).toBe("number");
      expect(result).toBe(1.5);
    });

    it("should handle function values for unknown keys", () => {
      const fn = () => "test";
      const result = castToType("unknownKey", fn);

      expect(result).toBe(fn);
      expect(typeof result).toBe("function");
    });

    it("should handle Symbol values for unknown keys", () => {
      const sym = Symbol("test");
      const result = castToType("unknownKey", sym);

      expect(result).toBe(sym);
      expect(typeof result).toBe("symbol");
    });

    it("should handle Date objects for unknown keys", () => {
      const date = new Date();
      const result = castToType("unknownKey", date);

      expect(result).toBe(date);
      expect(result instanceof Date).toBe(true);
    });
  });

  describe("performance and consistency", () => {
    it("should be consistent across multiple calls", () => {
      const key = "openAiTemp";
      const value = "0.7";

      const result1 = castToType(key, value);
      const result2 = castToType(key, value);
      const result3 = castToType(key, value);

      expect(result1).toBe(result2);
      expect(result2).toBe(result3);
    });

    it("should handle many calls efficiently", () => {
      const startTime = Date.now();

      for (let i = 0; i < 10000; i++) {
        castToType("openAiTemp", "0.5");
        castToType("unknownKey", "value");
        castToType("topN", i.toString());
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete 30k operations in reasonable time
      expect(duration).toBeLessThan(1000);
    });

    it("should not modify original values", () => {
      const originalValue = "1.5";
      const result = castToType("openAiTemp", originalValue);

      expect(originalValue).toBe("1.5"); // Original should be unchanged
      expect(result).toBe(1.5); // Result should be cast
    });

    it("should handle concurrent usage", async () => {
      const promises = Array.from({ length: 100 }, (_, i) =>
        Promise.resolve(castToType("topN", i.toString()))
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(100);
      results.forEach((result, index) => {
        expect(result).toBe(index);
      });
    });
  });
});
