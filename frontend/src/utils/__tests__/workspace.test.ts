/// <reference types="@testing-library/jest-dom" />
import {
  hasWorkspaceTranslation,
  getWorkspaceTranslatedName,
  sortWorkspaceNames,
  sortWorkspaces,
} from "../workspace";

// Mock the translations module
jest.mock("../../locales/en/workspaceViewAndButtons", () => ({
  __esModule: true,
  default: {
    "workspace-names": {
      personal: "Personal Workspace",
      shared: "Shared Workspace",
      "legal-docs": "Legal Documents",
      templates: "Document Templates",
    },
  },
}));

describe("workspace utility functions", () => {
  // Mock translation function
  const mockT = jest.fn();

  beforeEach(() => {
    // Reset and set up mock implementation for each test
    mockT.mockReset();
    mockT.mockImplementation((key: string) => {
      const translations: Record<string, string> = {
        "workspace-names.personal": "Personal Workspace",
        "workspace-names.shared": "Shared Workspace",
        "workspace-names.legal-docs": "Legal Documents",
        "workspace-names.templates": "Document Templates",
      };
      return translations[key] || key;
    });
  });

  describe("hasWorkspaceTranslation", () => {
    it("should return true for workspace names that have translations", () => {
      expect(hasWorkspaceTranslation("personal")).toBe(true);
      expect(hasWorkspaceTranslation("shared")).toBe(true);
      expect(hasWorkspaceTranslation("legal-docs")).toBe(true);
      expect(hasWorkspaceTranslation("templates")).toBe(true);
    });

    it("should return false for workspace names that don't have translations", () => {
      expect(hasWorkspaceTranslation("unknown-workspace")).toBe(false);
      expect(hasWorkspaceTranslation("custom-workspace")).toBe(false);
      expect(hasWorkspaceTranslation("")).toBe(false);
    });

    it("should handle special characters and numbers", () => {
      expect(hasWorkspaceTranslation("workspace-123")).toBe(false);
      expect(hasWorkspaceTranslation("workspace@special")).toBe(false);
    });

    it("should be case sensitive", () => {
      expect(hasWorkspaceTranslation("Personal")).toBe(false);
      expect(hasWorkspaceTranslation("PERSONAL")).toBe(false);
    });
  });

  describe("getWorkspaceTranslatedName", () => {
    it("should return translated names for workspaces that have translations", () => {
      // Clear mock to ensure clean state
      mockT.mockClear();

      // Test personal workspace
      const result = getWorkspaceTranslatedName("personal", mockT);
      expect(result).toBe("Personal Workspace");

      expect(getWorkspaceTranslatedName("shared", mockT)).toBe(
        "Shared Workspace"
      );
      expect(getWorkspaceTranslatedName("legal-docs", mockT)).toBe(
        "Legal Documents"
      );
      expect(getWorkspaceTranslatedName("templates", mockT)).toBe(
        "Document Templates"
      );
    });

    it("should return original names for workspaces that don't have translations", () => {
      expect(getWorkspaceTranslatedName("unknown-workspace", mockT)).toBe(
        "unknown-workspace"
      );
      expect(getWorkspaceTranslatedName("custom-workspace", mockT)).toBe(
        "custom-workspace"
      );
      expect(getWorkspaceTranslatedName("", mockT)).toBe("");
    });

    it("should call the translation function with correct key for translated workspaces", () => {
      getWorkspaceTranslatedName("personal", mockT);
      expect(mockT).toHaveBeenCalledWith("workspace-names.personal");

      getWorkspaceTranslatedName("shared", mockT);
      expect(mockT).toHaveBeenCalledWith("workspace-names.shared");
    });

    it("should not call the translation function for non-translated workspaces", () => {
      mockT.mockClear(); // Clear any previous calls
      getWorkspaceTranslatedName("unknown-workspace", mockT);
      expect(mockT).not.toHaveBeenCalled();
    });

    it("should handle edge cases", () => {
      expect(getWorkspaceTranslatedName("", mockT)).toBe("");
      expect(getWorkspaceTranslatedName("   ", mockT)).toBe("   ");
    });
  });

  describe("sortWorkspaceNames", () => {
    it("should sort workspace names alphabetically by their translated names", () => {
      const workspaces = ["templates", "personal", "legal-docs", "shared"];
      const sorted = sortWorkspaceNames(workspaces, mockT);

      expect(sorted).toEqual(["templates", "legal-docs", "personal", "shared"]);
    });

    it("should sort mixed translated and non-translated workspace names", () => {
      const workspaces = ["zzz-custom", "personal", "aaa-custom", "shared"];
      const sorted = sortWorkspaceNames(workspaces, mockT);

      // Expected order: aaa-custom, Personal Workspace, Shared Workspace, zzz-custom
      expect(sorted).toEqual([
        "aaa-custom",
        "personal",
        "shared",
        "zzz-custom",
      ]);
    });

    it("should not mutate the original array", () => {
      const workspaces = ["templates", "personal", "legal-docs"];
      const originalCopy = [...workspaces];

      sortWorkspaceNames(workspaces, mockT);

      expect(workspaces).toEqual(originalCopy);
    });

    it("should handle empty array", () => {
      const result = sortWorkspaceNames([], mockT);
      expect(result).toEqual([]);
    });

    it("should handle single item array", () => {
      const result = sortWorkspaceNames(["personal"], mockT);
      expect(result).toEqual(["personal"]);
    });

    it("should handle duplicate workspace names", () => {
      const workspaces = ["personal", "shared", "personal"];
      const sorted = sortWorkspaceNames(workspaces, mockT);

      expect(sorted).toEqual(["personal", "personal", "shared"]);
    });

    it("should handle case-sensitive sorting", () => {
      const workspaces = ["workspace-Z", "workspace-a", "personal"];
      const sorted = sortWorkspaceNames(workspaces, mockT);

      // Depends on locale comparison, but should be consistent
      expect(sorted.length).toBe(3);
      expect(sorted).toContain("personal");
    });
  });

  describe("sortWorkspaces", () => {
    const mockWorkspaces = [
      { name: "templates", id: 1, users: ["user1"] },
      { name: "personal", id: 2, users: ["user2"] },
      { name: "legal-docs", id: 3, users: ["user3"] },
      { name: "shared", id: 4, users: ["user4"] },
    ];

    it("should sort workspace objects alphabetically by their translated names", () => {
      const sorted = sortWorkspaces(mockWorkspaces, mockT);

      expect(sorted.map((w) => w.name)).toEqual([
        "templates",
        "legal-docs",
        "personal",
        "shared",
      ]);
      expect(sorted.map((w) => w.id)).toEqual([1, 3, 2, 4]);
    });

    it("should sort mixed translated and non-translated workspace objects", () => {
      const workspaces = [
        { name: "zzz-custom", id: 1 },
        { name: "personal", id: 2 },
        { name: "aaa-custom", id: 3 },
        { name: "shared", id: 4 },
      ];

      const sorted = sortWorkspaces(workspaces, mockT);

      expect(sorted.map((w) => w.name)).toEqual([
        "aaa-custom",
        "personal",
        "shared",
        "zzz-custom",
      ]);
    });

    it("should not mutate the original array", () => {
      const originalCopy = JSON.parse(JSON.stringify(mockWorkspaces));

      sortWorkspaces(mockWorkspaces, mockT);

      expect(mockWorkspaces).toEqual(originalCopy);
    });

    it("should preserve all object properties", () => {
      const workspaces = [
        { name: "shared", id: 1, users: ["user1"], extra: "data" },
        { name: "personal", id: 2, users: ["user2"], extra: "more" },
      ];

      const sorted = sortWorkspaces(workspaces, mockT);

      expect(sorted[0]).toEqual({
        name: "personal",
        id: 2,
        users: ["user2"],
        extra: "more",
      });
      expect(sorted[1]).toEqual({
        name: "shared",
        id: 1,
        users: ["user1"],
        extra: "data",
      });
    });

    it("should handle empty array", () => {
      const result = sortWorkspaces([], mockT);
      expect(result).toEqual([]);
    });

    it("should handle single item array", () => {
      const workspaces = [{ name: "personal", id: 1 }];
      const result = sortWorkspaces(workspaces, mockT);

      expect(result).toEqual([{ name: "personal", id: 1 }]);
    });

    it("should handle objects with same names", () => {
      const workspaces = [
        { name: "personal", id: 1 },
        { name: "shared", id: 2 },
        { name: "personal", id: 3 },
      ];

      const sorted = sortWorkspaces(workspaces, mockT);

      expect(sorted.map((w) => w.name)).toEqual([
        "personal",
        "personal",
        "shared",
      ]);
      expect(sorted.map((w) => w.id)).toEqual([1, 3, 2]);
    });

    it("should handle workspaces with missing or undefined name property", () => {
      const workspaces = [
        { name: "personal", id: 1 },
        { name: "", id: 3 },
      ];

      // Should not throw an error for empty string names
      expect(() => sortWorkspaces(workspaces, mockT)).not.toThrow();

      // But should throw for undefined names
      const workspacesWithUndefined = [
        { name: "personal", id: 1 },
        { name: undefined as any, id: 2 },
      ];

      expect(() => sortWorkspaces(workspacesWithUndefined, mockT)).toThrow();
    });
  });

  describe("edge cases and error handling", () => {
    it("should throw errors for null translation function when workspace has translation", () => {
      // Should throw when translation is needed (workspace has translation)
      expect(() =>
        getWorkspaceTranslatedName("personal", null as any)
      ).toThrow();
      expect(() =>
        sortWorkspaceNames(["personal", "shared"], null as any)
      ).toThrow();
      expect(() =>
        sortWorkspaces([{ name: "personal" }, { name: "shared" }], null as any)
      ).toThrow();
    });

    it("should not throw for null translation function when workspace has no translation", () => {
      // Should not throw when no translation is needed (workspace has no translation)
      expect(() =>
        getWorkspaceTranslatedName("unknown-workspace", null as any)
      ).not.toThrow();
      expect(() =>
        sortWorkspaceNames(["unknown-workspace"], null as any)
      ).not.toThrow();
      expect(() =>
        sortWorkspaces([{ name: "unknown-workspace" }], null as any)
      ).not.toThrow();
    });

    it("should handle very long workspace names", () => {
      const longName = "a".repeat(1000);
      expect(hasWorkspaceTranslation(longName)).toBe(false);
      expect(getWorkspaceTranslatedName(longName, mockT)).toBe(longName);
    });

    it("should handle workspace names with special Unicode characters", () => {
      const unicodeName = "workspace-🚀-测试";
      expect(hasWorkspaceTranslation(unicodeName)).toBe(false);
      expect(getWorkspaceTranslatedName(unicodeName, mockT)).toBe(unicodeName);
    });

    it("should maintain stable sorting", () => {
      const workspaces = [
        { name: "same", id: 1 },
        { name: "same", id: 2 },
        { name: "same", id: 3 },
      ];

      const sorted1 = sortWorkspaces(workspaces, mockT);
      const sorted2 = sortWorkspaces(workspaces, mockT);

      expect(sorted1.map((w) => w.id)).toEqual(sorted2.map((w) => w.id));
    });
  });
});
