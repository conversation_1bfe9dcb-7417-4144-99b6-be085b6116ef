import { handleChatResponse } from "./index";

// Mock the progress store
const mockUpdateProgress = jest.fn();
const mockFinishProcess = jest.fn();
const mockSetError = jest.fn();
const mockGetThreadState = jest.fn().mockReturnValue(null);
const mockStartProcess = jest.fn();

jest.mock("@/stores/progressStore", () => ({
  __esModule: true,
  default: {
    getState: jest.fn(() => ({
      updateProgress: mockUpdateProgress,
      finishProcess: mockFinishProcess,
      setError: mockSetError,
      getThreadState: mockGetThreadState,
      startProcess: mockStartProcess,
    })),
  },
}));

// Mock showToast
jest.mock("@/utils/toast", () => ({
  __esModule: true,
  default: jest.fn(),
}));

// Mock i18next
jest.mock("i18next", () => ({
  t: (key: string) => key,
}));

// Mock other dependencies
jest.mock("@/components/Sidebar/ActiveWorkspaces/ThreadContainer", () => ({
  THREAD_RENAME_EVENT: "thread-rename",
}));

jest.mock("@/components/contexts/TTSProvider", () => ({
  emitAssistantMessageCompleteEvent: jest.fn(),
}));

jest.mock("../tokenizer", () => ({
  tokenManager: {
    encode: jest.fn().mockReturnValue([]),
  },
}));

jest.mock("uuid", () => ({
  v4: jest.fn(() => "mock-uuid"),
}));

describe("Chat Completion Handling", () => {
  let mockSetLoadingResponse: jest.Mock;
  let mockSetChatHistory: jest.Mock;
  let threadSlug: string;

  beforeEach(() => {
    mockSetLoadingResponse = jest.fn();
    mockSetChatHistory = jest.fn();
    threadSlug = "test-thread";

    // Reset all mocks
    jest.clearAllMocks();
    mockGetThreadState.mockReturnValue(null);
  });

  describe("Document Drafting Completion", () => {
    test("should handle final document textResponse correctly", () => {
      const finalDocument =
        "# Legal Analysis\n\nThis is the final document content.\n\n## Section 1\nDetailed analysis...\n\n## Conclusion\nFinal thoughts.";
      const chatResult = {
        uuid: "doc-uuid-123",
        type: "textResponse",
        textResponse: finalDocument,
        sources: [],
        close: true,
        error: false,
        chatId: "saved-chat-456",
      };

      handleChatResponse(
        chatResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        [],
        threadSlug
      );

      // Should set loading to false
      expect(mockSetLoadingResponse).toHaveBeenCalledWith(false);

      // Should add the complete document to chat history
      expect(mockSetChatHistory).toHaveBeenCalledWith(expect.any(Function));

      // Test the chat history update function
      const updateFunction = mockSetChatHistory.mock.calls[0][0] as (
        prev: any[]
      ) => any[];
      const newHistory = updateFunction([]);

      expect(newHistory).toHaveLength(1);
      expect(newHistory[0]).toEqual({
        uuid: "doc-uuid-123",
        content: finalDocument,
        type: "message", // Implementation converts textResponse to message type
        role: "assistant",
        error: false,
        closed: true,
        pending: false,
        animate: false,
        sources: [],
        chatId: "saved-chat-456",
        metrics: {},
      });

      // Should finish the process
      setTimeout(() => {
        expect(mockFinishProcess).toHaveBeenCalledWith(threadSlug);
      }, 150);
    });

    test("should handle document completion with sources", () => {
      const finalDocument =
        "# Legal Analysis with Sources\n\nThis document references multiple sources.";
      const sources = [
        {
          id: "doc1",
          text: "Source content 1",
          title: "Document 1",
          url: "/docs/doc1.pdf",
          similarity: 0.95,
        },
        {
          id: "doc2",
          text: "Source content 2",
          title: "Document 2",
          url: "/docs/doc2.pdf",
          similarity: 0.87,
        },
      ];

      const chatResult = {
        uuid: "doc-with-sources-456",
        type: "textResponse",
        textResponse: finalDocument,
        sources: sources,
        close: true,
        error: false,
        chatId: "saved-chat-789",
        metrics: {
          totalTokens: 1500,
          processingTime: 45000,
        },
      };

      handleChatResponse(
        chatResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        [],
        threadSlug
      );

      const updateFunction = mockSetChatHistory.mock.calls[0][0] as (
        prev: any[]
      ) => any[];
      const newHistory = updateFunction([]);

      expect(newHistory[0]).toMatchObject({
        uuid: "doc-with-sources-456",
        content: finalDocument,
        sources: sources,
        chatId: "saved-chat-789",
        metrics: {
          totalTokens: 1500,
          processingTime: 45000,
        },
        closed: true,
        animate: false,
      });
    });

    test("should handle document completion in existing chat history", () => {
      const existingHistory = [
        {
          uuid: "user-msg-1",
          content: "Please draft a legal analysis",
          role: "user",
        },
      ];

      const finalDocument =
        "# Legal Analysis Response\n\nHere is the requested analysis.";
      const chatResult = {
        uuid: "response-uuid",
        type: "textResponse",
        textResponse: finalDocument,
        sources: [],
        close: true,
        error: false,
        chatId: "chat-123",
      };

      handleChatResponse(
        chatResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        [],
        threadSlug
      );

      const updateFunction = mockSetChatHistory.mock.calls[0][0] as (
        prev: any[]
      ) => any[];
      const newHistory = updateFunction(existingHistory);

      expect(newHistory).toHaveLength(2);
      expect(newHistory[0]).toEqual(existingHistory[0]); // User message unchanged
      expect(newHistory[1]).toMatchObject({
        content: finalDocument,
        role: "assistant",
        closed: true,
        chatId: "chat-123",
      });
    });

    test("should handle large document content correctly", () => {
      // Create a large document (simulating real document generation)
      const largeSections = Array.from({ length: 10 }, (_, i) =>
        `## Section ${i + 1}\n\nThis is section ${i + 1} with detailed content. `.repeat(
          50
        )
      );
      const largeDocument = `# Comprehensive Legal Analysis\n\n${largeSections.join("\n\n")}`;

      const chatResult = {
        uuid: "large-doc-uuid",
        type: "textResponse",
        textResponse: largeDocument,
        sources: [],
        close: true,
        error: false,
        chatId: "large-chat-id",
      };

      handleChatResponse(
        chatResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        [],
        threadSlug
      );

      const updateFunction = mockSetChatHistory.mock.calls[0][0] as (
        prev: any[]
      ) => any[];
      const newHistory = updateFunction([]);

      expect(newHistory[0].content).toBe(largeDocument);
      expect(newHistory[0].content.length).toBeGreaterThan(1000);
      expect(newHistory[0].closed).toBe(true);
    });
  });

  describe("Error Handling in Completion", () => {
    test("should handle completion with error flag", () => {
      const chatResult = {
        uuid: "error-uuid",
        type: "textResponse",
        textResponse: "An error occurred during document generation.",
        sources: [],
        close: true,
        error: true,
        chatId: undefined,
      };

      handleChatResponse(
        chatResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        [],
        threadSlug
      );

      const updateFunction = mockSetChatHistory.mock.calls[0][0] as (
        prev: any[]
      ) => any[];
      const newHistory = updateFunction([]);

      expect(newHistory[0]).toMatchObject({
        content: "An error occurred during document generation.",
        error: true,
        closed: true,
      });
    });

    test("should handle completion without content", () => {
      const chatResult = {
        uuid: "empty-uuid",
        type: "textResponse",
        textResponse: "",
        sources: [],
        close: true,
        error: false,
        chatId: "empty-chat",
      };

      handleChatResponse(
        chatResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        [],
        threadSlug
      );

      const updateFunction = mockSetChatHistory.mock.calls[0][0] as (
        prev: any[]
      ) => any[];
      const newHistory = updateFunction([]);

      expect(newHistory[0]).toMatchObject({
        content: "",
        closed: true,
        chatId: "empty-chat",
      });
    });
  });

  describe("Progress Integration", () => {
    test("should finish progress when document completion occurs", () => {
      const chatResult = {
        uuid: "progress-uuid",
        type: "textResponse",
        textResponse: "Document content",
        close: true,
        error: false,
        chatId: "progress-chat",
      };

      handleChatResponse(
        chatResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        [],
        threadSlug
      );

      // Should finish progress after timeout
      setTimeout(() => {
        expect(mockFinishProcess).toHaveBeenCalledWith(threadSlug);
      }, 150);
    });

    test("should not finish progress when close is false", () => {
      const chatResult = {
        uuid: "non-final-uuid",
        type: "textResponse",
        textResponse: "Partial content",
        close: false,
        error: false,
        chatId: "partial-chat",
      };

      handleChatResponse(
        chatResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        [],
        threadSlug
      );

      // Should not finish progress for non-final responses
      expect(mockFinishProcess).not.toHaveBeenCalled();
    });
  });

  describe("Integration with Other Message Types", () => {
    test("should handle textResponse completion after progress updates", () => {
      // Simulate progress updates first
      const progressUpdate = {
        type: "progress",
        step: 7,
        textResponse: "Document generation complete", // Use textResponse, not message
        totalSteps: 7,
        flowType: "main",
      };

      handleChatResponse(
        progressUpdate,
        mockSetLoadingResponse,
        mockSetChatHistory,
        [],
        threadSlug
      );

      // Then handle the final document
      const finalDocument = {
        uuid: "final-uuid",
        type: "textResponse",
        textResponse: "# Final Document\n\nContent here.",
        close: true,
        error: false,
        chatId: "final-chat",
      };

      handleChatResponse(
        finalDocument,
        mockSetLoadingResponse,
        mockSetChatHistory,
        [],
        threadSlug
      );

      // Progress should have been updated with transformed object
      expect(mockUpdateProgress).toHaveBeenCalledWith(
        {
          step: 7,
          subStep: undefined,
          status: undefined,
          message: "Document generation complete",
          label: undefined,
          total: 7,
          progress: undefined,
          error: undefined,
        },
        threadSlug
      );

      // Document should be added to chat
      expect(mockSetChatHistory).toHaveBeenCalledTimes(1); // Only called for textResponse
    });

    test("should distinguish between streaming chunks and final completion", () => {
      // This should NOT trigger completion handling
      const streamingChunk = {
        uuid: "stream-uuid",
        type: "textResponseChunk",
        textResponse: "Partial content...",
        close: false,
        error: false,
      };

      handleChatResponse(
        streamingChunk,
        mockSetLoadingResponse,
        mockSetChatHistory,
        [],
        threadSlug
      );

      // Should not finish process for streaming chunks
      expect(mockFinishProcess).not.toHaveBeenCalled();

      // This SHOULD trigger completion handling
      const finalResponse = {
        uuid: "final-uuid",
        type: "textResponse",
        textResponse: "Complete document content",
        close: true,
        error: false,
        chatId: "final-chat",
      };

      handleChatResponse(
        finalResponse,
        mockSetLoadingResponse,
        mockSetChatHistory,
        [],
        threadSlug
      );

      // Should finish process for final response
      setTimeout(() => {
        expect(mockFinishProcess).toHaveBeenCalledWith(threadSlug);
      }, 150);
    });
  });
});
