import i18next from "i18next";
import showToast from "@/utils/toast";
import { tokenManager } from "../tokenizer";
import { v4 } from "uuid";
import useProgressStore from "@/stores/progressStore";
import { ChatMessage, ChatSource, ChatMetrics, Workspace } from "@/types";

interface ChatStreamResult {
  type: string;
  error?: string | boolean;
  textResponse?: string;
  uuid?: string;
  action?: string;
  close?: boolean;
  sources?: ChatSource[];
  chatId?: string | number;
  metrics?: ChatMetrics;
  step?: number;
  subStep?: number | null;
  totalSteps?: number;
  flowType?: string;
}

type SetLoadingResponse = (loading: boolean) => void;
type SetChatHistory = React.Dispatch<React.SetStateAction<ChatMessage[]>>;
type RemHistory = unknown; // This parameter seems unused in the function

// Function to handle chat response updates
export function handleChatResponse(
  chatResult: ChatStreamResult,
  setLoadingResponse: SetLoadingResponse,
  setChatHistory: SetChatHistory,
  _remHistory: RemHistory,
  threadSlug: string
): void {
  const data = chatResult;
  const { updateProgress, finishProcess, setError, getThreadState } =
    useProgressStore.getState();

  if (data.type === "abort" || data.error) {
    setLoadingResponse(false);

    const threadState = getThreadState(threadSlug);
    const isProcessError = !!threadState;

    setError(threadSlug, data.textResponse || String(data.error));

    if (isProcessError) {
      setChatHistory((prev) => {
        const newHistory = [...prev];
        const lastMsgIndex = newHistory.length - 1;
        if (lastMsgIndex >= 0 && newHistory[lastMsgIndex].pending) {
          return newHistory.slice(0, lastMsgIndex);
        }
        return newHistory;
      });
    } else {
      setChatHistory((prev) => {
        const newHistory = [...prev];
        const lastMsgIndex = newHistory.length - 1;
        if (lastMsgIndex >= 0 && newHistory[lastMsgIndex].pending) {
          newHistory[lastMsgIndex] = {
            ...newHistory[lastMsgIndex],
            content:
              data.textResponse ||
              "An error occurred while streaming the response.",
            error: true,
            closed: true,
            pending: false,
          };
          return newHistory;
        }
        return [
          ...newHistory,
          {
            uuid: data.uuid || v4(),
            content:
              data.textResponse ||
              "An error occurred while streaming the response.",
            type:
              data.type === "rechartVisualize" || data.type === "statusResponse"
                ? data.type
                : "message",
            role: "assistant",
            error: true,
            closed: true,
            sources: [],
          },
        ];
      });
    }
    return;
  }

  if (data.type === "stopGeneration") {
    // [StreamAbort] Handling stopGeneration - finalizing current response
    setLoadingResponse(false);

    setChatHistory((prev) => {
      const newHistory = [...prev];
      // Find last index manually since findLastIndex requires ES2023
      let lastMsgIndex = -1;
      for (let i = newHistory.length - 1; i >= 0; i--) {
        if (
          newHistory[i].role === "assistant" &&
          (newHistory[i].pending || newHistory[i].animate)
        ) {
          lastMsgIndex = i;
          break;
        }
      }

      if (lastMsgIndex >= 0) {
        newHistory[lastMsgIndex] = {
          ...newHistory[lastMsgIndex],
          closed: true,
          pending: false,
          animate: false,
        };
      }

      return newHistory;
    });

    if (threadSlug) finishProcess(threadSlug);
    return;
  }

  if (data.type === "progress" || data.type === "cdbProgress") {
    // Use threadSlug if available, otherwise use chatId or UUID for workspace-level chats
    const progressId = threadSlug || data.chatId || data.uuid || "";
    const threadState = getThreadState(String(progressId));

    if (!threadState || !threadState.isActive) {
      const { startProcess } = useProgressStore.getState();
      startProcess(String(progressId), data.totalSteps, data.flowType);
    }
    // Ensure data has required step property for progress update
    if (typeof data.step === "number") {
      // Create a proper ProgressEvent object from ChatResult data
      const progressEvent: {
        step: number;
        subStep?: number | null;
        status?: "pending" | "starting" | "in_progress" | "complete" | "error";
        message?: string | null;
        label?: string | null;
        total?: number;
        progress?: number;
        error?: unknown;
      } = {
        step: data.step,
        subStep: data.subStep,
        status: undefined, // ChatResult doesn't have status, let store handle it
        message: data.textResponse || null,
        label: undefined, // ChatResult doesn't have label
        total: data.totalSteps,
        progress: undefined, // ChatResult doesn't have progress
        error: data.error,
      };

      updateProgress(progressEvent, String(progressId));
    } else {
      // No valid step number in progress data
    }
    return;
  }

  if (data.type === "process_complete") {
    const progressId = threadSlug || data.chatId || data.uuid || "";
    if (progressId) finishProcess(String(progressId));
    return;
  }

  if (data.type === "textResponse") {
    // Handle reset_chat action
    if (data.action === "reset_chat") {
      setLoadingResponse(false);

      // Clear chat history
      setChatHistory([]);

      // Show toast message if provided
      if (data.textResponse && data.textResponse.startsWith("show-toast.")) {
        const toastKey = data.textResponse;
        showToast(i18next.t(toastKey), "success");
      }

      if (threadSlug) {
        setTimeout(() => finishProcess(threadSlug), 100);
      }
      return;
    }

    // Handle complete text responses (like from CDB flows)
    setChatHistory((prevChatHistory) => {
      return [
        ...prevChatHistory,
        {
          uuid: data.uuid || v4(),
          content: data.textResponse || "",
          type:
            data.type === "rechartVisualize" || data.type === "statusResponse"
              ? data.type
              : "message",
          role: "assistant",
          error: data.error || false,
          closed: data.close || false,
          pending: false,
          animate: !data.close,
          sources: data.sources || [],
          chatId: data.chatId,
          metrics: data.metrics || {},
        },
      ];
    });

    // Handle completion
    if (data.close) {
      setLoadingResponse(false);
      if (threadSlug) {
        setTimeout(() => finishProcess(threadSlug), 100);
      }
    }
    return;
  }

  if (
    data.type === "textResponseChunk" ||
    data.type === "finalizeResponseStream"
  ) {
    console.log(`[CDB DEBUG] Frontend received ${data.type} with UUID: ${data.uuid}, close: ${data.close}`);
    setChatHistory((prevChatHistory) => {
      console.log(`[CDB DEBUG] Looking for message with UUID: ${data.uuid} in ${prevChatHistory.length} messages`);
      let chatIdx = prevChatHistory.findIndex(
        (chat) => chat.uuid === data.uuid
      );
      console.log(`[CDB DEBUG] Found exact UUID match at index: ${chatIdx}`);
      if (chatIdx === -1) {
        // Find last index manually since findLastIndex requires ES2023
        for (let i = prevChatHistory.length - 1; i >= 0; i--) {
          if (
            prevChatHistory[i].role === "assistant" &&
            prevChatHistory[i].pending === true
          ) {
            console.log(`[CDB DEBUG] Found pending assistant message at index: ${i} with UUID: ${prevChatHistory[i].uuid}`);
            chatIdx = i;
            break;
          }
        }
      }

      if (chatIdx === -1) {
        // For finalizeResponseStream (like CDB), create a new assistant message if none exists
        if (data.type === "finalizeResponseStream") {
          console.log(`[CDB DEBUG] Creating new assistant message for finalizeResponseStream with UUID: ${data.uuid}`);
          const newAssistantMessage: ChatMessage = {
            uuid: data.uuid || v4(),
            content: data.textResponse || "",
            type: "message",
            role: "assistant",
            error: data.error || false,
            closed: data.close || true,
            pending: false,
            animate: false,
            sources: data.sources || [],
            chatId: data.chatId,
            metrics: data.metrics || {},
          };
          return [...prevChatHistory, newAssistantMessage];
        }
        // Stream chunk received but no matching or pending message found
        return prevChatHistory;
      }

      const existingMessage = prevChatHistory[chatIdx];

      // Handle content for both textResponseChunk and finalizeResponseStream
      // textResponseChunk: concatenate incremental content
      // finalizeResponseStream: use complete content if provided, otherwise keep existing
      const newContent =
        data.type === "textResponseChunk"
          ? (existingMessage.content || "") + (data.textResponse || "")
          : data.type === "finalizeResponseStream" && data.textResponse
            ? data.textResponse
            : existingMessage.content || "";

      const updatedMessage: ChatMessage = {
        ...existingMessage,
        uuid: data.uuid || existingMessage.uuid,
        content: newContent,
        sources:
          data.sources && data.sources.length > 0
            ? data.sources
            : existingMessage.sources,
        type:
          data.type === "rechartVisualize" || data.type === "statusResponse"
            ? data.type
            : "message",
        role: "assistant",
        error: data.error,
        closed: data.close,
        pending: false,
        animate: !data.close,
        chatId: data.chatId || existingMessage.chatId,
        metrics: data.metrics || existingMessage.metrics,
      };

      const newHistory = [...prevChatHistory];
      newHistory[chatIdx] = updatedMessage;
      return newHistory;
    });

    // Ensure progress is finished when streaming completes
    if (data.close || data.type === "finalizeResponseStream") {
      setLoadingResponse(false);
      if (threadSlug) {
        // Use a small delay to ensure the UI updates before cleaning up progress
        setTimeout(() => finishProcess(threadSlug), 100);
      } else {
        // For workspace-level chats, we still need to clean up progress
        // Use the chatId or UUID as the progress identifier
        const progressId = data.chatId || data.uuid;
        if (progressId) {
          setTimeout(() => finishProcess(String(progressId)), 100);
        }
      }
    }
  }
}

export function chatPrompt(workspace: Workspace | null): string {
  return (
    workspace?.openAiPrompt ??
    "Given the following conversation, relevant context, and a follow up question, reply with an answer to the current question the user is asking. Return only your response to the question given the above information following the users instructions as needed."
  );
}

export function chatQueryRefusalResponse(workspace: Workspace | null): string {
  return (
    workspace?.queryRefusalResponse ??
    "There is no relevant information in this workspace to answer your query."
  );
}

export function truncatePrompt(
  prompt: string,
  maxTokens: number = 8000
): string {
  return tokenManager.truncateToTokens(prompt, maxTokens);
}
