/// <reference types="@testing-library/jest-dom" />
import { aggregateFolderStatus } from "../folderStatus";

describe("aggregateFolderStatus", () => {
  describe("basic functionality", () => {
    it("should return default false values for empty folder", () => {
      const result = aggregateFolderStatus({ items: [] });

      expect(result).toEqual({
        starred: false,
        pinned: false,
        pdr: false,
      });
    });

    it("should return default false values for null folder", () => {
      const result = aggregateFolderStatus(null);

      expect(result).toEqual({
        starred: false,
        pinned: false,
        pdr: false,
      });
    });

    it("should return default false values for undefined folder", () => {
      const result = aggregateFolderStatus(undefined);

      expect(result).toEqual({
        starred: false,
        pinned: false,
        pdr: false,
      });
    });

    it("should return default false values for folder without items", () => {
      const result = aggregateFolderStatus({});

      expect(result).toEqual({
        starred: false,
        pinned: false,
        pdr: false,
      });
    });

    it("should return default false values for folder with non-array items", () => {
      const result = aggregateFolderStatus({ items: "not-an-array" as any });

      expect(result).toEqual({
        starred: false,
        pinned: false,
        pdr: false,
      });
    });
  });

  describe("single file scenarios", () => {
    it("should detect starred file", () => {
      const folder = {
        items: [{ starred: true }],
      };

      const result = aggregateFolderStatus(folder);

      expect(result).toEqual({
        starred: true,
        pinned: false,
        pdr: false,
      });
    });

    it("should detect pinned file", () => {
      const folder = {
        items: [{ pinned: true }],
      };

      const result = aggregateFolderStatus(folder);

      expect(result).toEqual({
        starred: false,
        pinned: true,
        pdr: false,
      });
    });

    it("should detect pdr file", () => {
      const folder = {
        items: [{ pdr: true }],
      };

      const result = aggregateFolderStatus(folder);

      expect(result).toEqual({
        starred: false,
        pinned: false,
        pdr: true,
      });
    });

    it("should detect multiple properties on single file", () => {
      const folder = {
        items: [{ starred: true, pinned: true, pdr: true }],
      };

      const result = aggregateFolderStatus(folder);

      expect(result).toEqual({
        starred: true,
        pinned: true,
        pdr: true,
      });
    });

    it("should handle file with no special properties", () => {
      const folder = {
        items: [{}],
      };

      const result = aggregateFolderStatus(folder);

      expect(result).toEqual({
        starred: false,
        pinned: false,
        pdr: false,
      });
    });

    it("should handle file with explicitly false properties", () => {
      const folder = {
        items: [{ starred: false, pinned: false, pdr: false }],
      };

      const result = aggregateFolderStatus(folder);

      expect(result).toEqual({
        starred: false,
        pinned: false,
        pdr: false,
      });
    });
  });

  describe("multiple files scenarios", () => {
    it("should aggregate properties from multiple files", () => {
      const folder = {
        items: [{ starred: true }, { pinned: true }, { pdr: true }],
      };

      const result = aggregateFolderStatus(folder);

      expect(result).toEqual({
        starred: true,
        pinned: true,
        pdr: true,
      });
    });

    it("should return true if any file has the property", () => {
      const folder = {
        items: [
          { starred: false },
          { starred: false },
          { starred: true }, // Only one is starred
          { starred: false },
        ],
      };

      const result = aggregateFolderStatus(folder);

      expect(result.starred).toBe(true);
    });

    it("should handle mixed properties across files", () => {
      const folder = {
        items: [
          { starred: true, pinned: false },
          { starred: false, pdr: true },
          { pinned: true },
          {},
        ],
      };

      const result = aggregateFolderStatus(folder);

      expect(result).toEqual({
        starred: true,
        pinned: true,
        pdr: true,
      });
    });

    it("should handle all files having no special properties", () => {
      const folder = {
        items: [{}, { starred: false, pinned: false, pdr: false }, {}],
      };

      const result = aggregateFolderStatus(folder);

      expect(result).toEqual({
        starred: false,
        pinned: false,
        pdr: false,
      });
    });

    it("should handle large number of files", () => {
      const items = Array.from({ length: 1000 }, (_, index) => ({
        starred: index === 500, // Only one file is starred
        pinned: false,
        pdr: false,
      }));

      const folder = { items };
      const result = aggregateFolderStatus(folder);

      expect(result).toEqual({
        starred: true,
        pinned: false,
        pdr: false,
      });
    });
  });

  describe("truthy/falsy value handling", () => {
    it("should treat truthy values as true", () => {
      const folder = {
        items: [{ starred: "yes" as any, pinned: 1 as any, pdr: {} as any }],
      };

      const result = aggregateFolderStatus(folder);

      expect(result).toEqual({
        starred: true,
        pinned: true,
        pdr: true,
      });
    });

    it("should treat falsy values as false", () => {
      const folder = {
        items: [
          { starred: 0 as any, pinned: "" as any, pdr: null as any },
          { starred: undefined, pinned: false, pdr: 0 as any },
        ],
      };

      const result = aggregateFolderStatus(folder);

      expect(result).toEqual({
        starred: false,
        pinned: false,
        pdr: false,
      });
    });

    it("should handle mix of truthy and falsy values", () => {
      const folder = {
        items: [
          { starred: 0 as any }, // falsy
          { starred: "true" as any }, // truthy
          { starred: false }, // falsy
        ],
      };

      const result = aggregateFolderStatus(folder);

      expect(result.starred).toBe(true);
    });
  });

  describe("edge cases", () => {
    it("should handle files with extra properties", () => {
      const folder = {
        items: [
          {
            starred: true,
            pinned: false,
            pdr: true,
            extraProperty: "should be ignored",
            id: 123,
            name: "test-file",
          } as any,
        ],
      };

      const result = aggregateFolderStatus(folder);

      expect(result).toEqual({
        starred: true,
        pinned: false,
        pdr: true,
      });
    });

    it("should handle empty object files", () => {
      const folder = {
        items: [{}, {}, {}],
      };

      const result = aggregateFolderStatus(folder);

      expect(result).toEqual({
        starred: false,
        pinned: false,
        pdr: false,
      });
    });

    it("should handle files with only some properties defined", () => {
      const folder = {
        items: [
          { starred: true }, // missing pinned and pdr
          { pinned: true }, // missing starred and pdr
          { pdr: true }, // missing starred and pinned
        ],
      };

      const result = aggregateFolderStatus(folder);

      expect(result).toEqual({
        starred: true,
        pinned: true,
        pdr: true,
      });
    });

    it("should maintain immutability of input", () => {
      const originalFolder = {
        items: [{ starred: true }],
      };
      const originalItems = [...originalFolder.items];

      aggregateFolderStatus(originalFolder);

      // Original folder should not be modified
      expect(originalFolder.items).toEqual(originalItems);
      expect(originalFolder).toEqual({
        items: [{ starred: true }],
      });
    });

    it("should throw error when items contain null or undefined", () => {
      const folderWithNull = {
        items: [{ starred: true }, null as any],
      };

      const folderWithUndefined = {
        items: [{ starred: true }, undefined as any],
      };

      // Should throw error when trying to access properties of null/undefined
      expect(() => aggregateFolderStatus(folderWithNull)).toThrow();
      expect(() => aggregateFolderStatus(folderWithUndefined)).toThrow();
    });

    it("should handle folder with non-object items that have properties", () => {
      const folder = {
        items: [
          { starred: true },
          "invalid-item" as any, // strings don't have starred/pinned/pdr properties
          42 as any, // numbers don't have starred/pinned/pdr properties
          { pinned: true },
        ],
      };

      // Should process valid items and ignore invalid ones (undefined properties are falsy)
      const result = aggregateFolderStatus(folder);

      expect(result.starred).toBe(true);
      expect(result.pinned).toBe(true);
      expect(result.pdr).toBe(false);
    });

    it("should handle circular references gracefully", () => {
      const circularItem: any = { starred: true };
      circularItem.self = circularItem;

      const folder = {
        items: [circularItem],
      };

      // Should not throw error due to circular reference in aggregation
      expect(() => aggregateFolderStatus(folder)).not.toThrow();

      const result = aggregateFolderStatus(folder);
      expect(result.starred).toBe(true);
    });
  });

  describe("performance and stress tests", () => {
    it("should handle very large arrays efficiently", () => {
      const items = Array.from({ length: 10000 }, (_, index) => ({
        starred: index % 1000 === 0,
        pinned: index % 2000 === 0,
        pdr: index % 3000 === 0,
      }));

      const folder = { items };

      const startTime = Date.now();
      const result = aggregateFolderStatus(folder);
      const endTime = Date.now();

      // Should complete in reasonable time (less than 500ms for 10k items)
      // Note: In CI/test environments, this may take longer than in production
      expect(endTime - startTime).toBeLessThan(500);

      expect(result).toEqual({
        starred: true,
        pinned: true,
        pdr: true,
      });
    });

    it("should have consistent results for same input", () => {
      const folder = {
        items: [
          { starred: true, pinned: false },
          { starred: false, pinned: true, pdr: true },
        ],
      };

      const result1 = aggregateFolderStatus(folder);
      const result2 = aggregateFolderStatus(folder);
      const result3 = aggregateFolderStatus(folder);

      expect(result1).toEqual(result2);
      expect(result2).toEqual(result3);
    });
  });
});
