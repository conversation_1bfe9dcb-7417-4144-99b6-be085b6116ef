/**
 * Type declarations for console warning suppression utilities
 */

export interface ConsoleSuppressionResult {
  restore: () => void;
}

/**
 * Sets up console warning suppression for tests
 * @returns Object with restore function to cleanup spies
 */
export function setupConsoleWarningSuppressions(): ConsoleSuppressionResult;

/**
 * Sets up console suppression for specific patterns
 * @param patterns - Array of patterns to suppress
 * @returns Object with restore function
 */
export function suppressConsolePatterns(
  patterns: string[]
): ConsoleSuppressionResult;
