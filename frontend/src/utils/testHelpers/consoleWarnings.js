/**
 * Shared test utility for suppressing expected console warnings
 * Uses jest.spyOn() as recommended approach for mocking console methods
 */

// List of warning patterns to suppress
const WARNING_PATTERNS = [
  "Warning: An update to",
  "act(...)",
  "The current testing environment is not configured to support act",
  "Warning: Each child in a list should have a unique",
  "React Router Future Flag Warning",
  "v7_startTransition",
  "v7_relativeSplatPath",
  "Error fetching initial data:",
];

/**
 * Sets up console warning suppression for tests
 * @returns {Object} Object with restore function to cleanup spies
 */
export function setupConsoleWarningSuppressions() {
  // Store original console methods
  const originalError = console.error;
  const originalWarn = console.warn;

  // Create spies that filter messages
  const errorSpy = jest
    .spyOn(console, "error")
    .mockImplementation((...args) => {
      const message = args[0]?.toString?.() || "";

      // Check if this is a warning we want to suppress
      const shouldSuppress = WARNING_PATTERNS.some((pattern) =>
        message.includes(pattern)
      );

      if (!shouldSuppress) {
        // Call the original implementation for non-suppressed errors
        originalError.apply(console, args);
      }
    });

  const warnSpy = jest.spyOn(console, "warn").mockImplementation((...args) => {
    const message = args[0]?.toString?.() || "";

    // Check if this is a warning we want to suppress
    const shouldSuppress = WARNING_PATTERNS.some((pattern) =>
      message.includes(pattern)
    );

    if (!shouldSuppress) {
      // Call the original implementation for non-suppressed warnings
      originalWarn.apply(console, args);
    }
  });

  // Return cleanup function
  return {
    restore: () => {
      errorSpy.mockRestore();
      warnSpy.mockRestore();
    },
  };
}

/**
 * Sets up console suppression for specific patterns
 * @param {string[]} patterns - Array of patterns to suppress
 * @returns {Object} Object with restore function
 */
export function suppressConsolePatterns(patterns) {
  // Store original console methods
  const originalError = console.error;
  const originalWarn = console.warn;

  const errorSpy = jest
    .spyOn(console, "error")
    .mockImplementation((...args) => {
      const message = args[0]?.toString?.() || "";

      const shouldSuppress = patterns.some((pattern) =>
        message.includes(pattern)
      );

      if (!shouldSuppress) {
        originalError.apply(console, args);
      }
    });

  const warnSpy = jest.spyOn(console, "warn").mockImplementation((...args) => {
    const message = args[0]?.toString?.() || "";

    const shouldSuppress = patterns.some((pattern) =>
      message.includes(pattern)
    );

    if (!shouldSuppress) {
      originalWarn.apply(console, args);
    }
  });

  return {
    restore: () => {
      errorSpy.mockRestore();
      warnSpy.mockRestore();
    },
  };
}
