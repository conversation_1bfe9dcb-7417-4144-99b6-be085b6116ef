import { act, cleanup } from "@testing-library/react";

/**
 * Utility function to wrap async operations in act() for testing
 * This ensures React state updates are properly batched and warnings are avoided
 */
export const actAsync = async <T>(fn: () => Promise<T>): Promise<T> => {
  let result: T;
  await act(async () => {
    result = await fn();
  });
  return result!;
};

/**
 * Enhanced cleanup function that ensures all React components are properly unmounted
 * and all side effects are cleaned up to prevent memory leaks in tests.
 */
export const enhancedCleanup = async () => {
  // Wait for any pending React updates
  await act(async () => {
    await new Promise((resolve) => setTimeout(resolve, 0));
  });

  // Run React Testing Library cleanup
  cleanup();

  // Clean up any dangling timers
  jest.clearAllTimers();

  // Clear any pending promises
  await new Promise((resolve) => setImmediate(resolve));

  // Force garbage collection if available
  if (global.gc) {
    global.gc();
  }
};

/**
 * Wrapper for test suites that ensures proper cleanup after each test
 */
export const withCleanup = (testFn: () => void) => {
  afterEach(async () => {
    await enhancedCleanup();
  });

  testFn();
};

/**
 * Helper to run a test with automatic cleanup
 */
export const testWithCleanup = (
  name: string,
  fn: () => void | Promise<void>
) => {
  test(name, async () => {
    try {
      await fn();
    } finally {
      await enhancedCleanup();
    }
  });
};

/**
 * Wait for all pending promises and state updates to complete
 * Useful for components that perform async operations on mount
 */
export const waitForAsyncOperations = async (
  timeout: number = 1000
): Promise<void> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      act(() => {
        resolve();
      });
    }, timeout);
  });
};

/**
 * Mock API response helper for testing
 */
export const mockApiResponse = <T>(
  data: T,
  success: boolean = true
): Promise<{ success: boolean; data?: T; error?: string }> => {
  return Promise.resolve({
    success,
    ...(success ? { data } : { error: "Mock error" }),
  });
};

/**
 * Mock User.getCustomSystemPrompt for testing
 */
export const mockGetCustomSystemPrompt = (prompt: string | null = null) => {
  return mockApiResponse({ prompt });
};

/**
 * Mock User.setCustomSystemPrompt for testing
 */
export const mockSetCustomSystemPrompt = (success: boolean = true) => {
  return mockApiResponse({}, success);
};

/**
 * Mock User.clearCustomSystemPrompt for testing
 */
export const mockClearCustomSystemPrompt = (success: boolean = true) => {
  return mockApiResponse({}, success);
};

/**
 * Common test setup for React components that perform async operations
 */
export const setupAsyncTest = () => {
  // This function can be used to setup common test utilities
  // Currently simplified to avoid console method references
};
