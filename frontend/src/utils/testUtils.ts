import { act } from "@testing-library/react";

/**
 * Utility function to wrap async operations in act() for testing
 * This ensures React state updates are properly batched and warnings are avoided
 */
export const actAsync = async <T>(fn: () => Promise<T>): Promise<T> => {
  let result: T;
  await act(async () => {
    result = await fn();
  });
  return result!;
};

/**
 * Wait for all pending promises and state updates to complete
 * Useful for components that perform async operations on mount
 */
export const waitForAsyncOperations = async (
  timeout: number = 1000
): Promise<void> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      act(() => {
        resolve();
      });
    }, timeout);
  });
};

/**
 * Mock API response helper for testing
 */
export const mockApiResponse = <T>(
  data: T,
  success: boolean = true
): Promise<{ success: boolean; data?: T; error?: string }> => {
  return Promise.resolve({
    success,
    ...(success ? { data } : { error: "Mock error" }),
  });
};

/**
 * Mock User.getCustomSystemPrompt for testing
 */
export const mockGetCustomSystemPrompt = (prompt: string | null = null) => {
  return mockApiResponse({ prompt });
};

/**
 * Mock User.setCustomSystemPrompt for testing
 */
export const mockSetCustomSystemPrompt = (success: boolean = true) => {
  return mockApiResponse({}, success);
};

/**
 * Mock User.clearCustomSystemPrompt for testing
 */
export const mockClearCustomSystemPrompt = (success: boolean = true) => {
  return mockApiResponse({}, success);
};

/**
 * Common test setup for React components that perform async operations
 */
export const setupAsyncTest = () => {
  // This function can be used to setup common test utilities
  // Currently simplified to avoid console method references
};
