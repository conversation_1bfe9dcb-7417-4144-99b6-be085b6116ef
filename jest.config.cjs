/**
 * Root Jest Configuration
 *
 * Orchestrates testing for all projects in the monorepo.
 * Uses environment variable to determine which configuration to use.
 * 
 * Coverage collection is disabled by default and only enabled when:
 * - COLLECT_COVERAGE=true environment variable is set
 * - Use npm run test:coverage to generate coverage reports
 */

const isCI = process.env.CI === "true" || process.env.GITHUB_ACTIONS === "true";

module.exports = {
  projects: [
    "<rootDir>/frontend/jest.config.cjs",
    isCI
      ? "<rootDir>/server/jest.config.ci.js"
      : "<rootDir>/server/jest.config.local.js",
    // If collector needs a specific Jest setup in the future, add its path here.
  ],
  // Run projects sequentially to avoid cross-project test interference
  maxWorkers: 1,
  // Global teardown to force cleanup
  globalTeardown: "<rootDir>/jest.globalTeardown.cjs",
  // Global test timeout
  testTimeout: isCI ? 15000 : 10000,
  // Force exit after tests complete
  forceExit: true,
  // Coverage settings (disabled by default, enabled via environment variable)
  collectCoverage: process.env.COLLECT_COVERAGE === "true",
  coverageProvider: "v8",
  // Reporters for CI integration (only in CI)
  reporters: isCI
    ? [
        "default",
        [
          "jest-junit",
          {
            outputDirectory: "<rootDir>/test-results",
            outputName: "junit.xml",
          },
        ],
      ]
    : ["default"],
};
