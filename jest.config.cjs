/**
 * Root Jest Configuration
 *
 * Orchestrates testing for all projects in the monorepo.
 * Uses environment variable to determine which configuration to use.
 *
 * Coverage collection is disabled by default and only enabled when:
 * - COLLECT_COVERAGE=true environment variable is set
 * - Use npm run test:coverage to generate coverage reports
 */

const isCI = process.env.CI === "true" || process.env.GITHUB_ACTIONS === "true";

module.exports = {
  projects: [
    "<rootDir>/frontend/jest.config.cjs",
    isCI
      ? "<rootDir>/server/jest.config.ci.js"
      : "<rootDir>/server/jest.config.local.js",
    // If collector needs a specific Jest setup in the future, add its path here.
  ],
  // Run projects sequentially to avoid cross-project test interference
  maxWorkers: isCI ? 4 : 4,
  // Memory management
  workerIdleMemoryLimit: isCI ? "512MB" : "2GB",
  // Global setup and teardown to force cleanup
  globalSetup: "<rootDir>/frontend/jest.globalSetup.cjs",
  globalTeardown: "<rootDir>/jest.globalTeardown.cjs",
  // Global test timeout
  testTimeout: isCI ? 15000 : 10000,
  // Detect open handles in CI to catch hanging tests
  detectOpenHandles: isCI,
  // Coverage settings (disabled by default, enabled via environment variable)
  collectCoverage: process.env.COLLECT_COVERAGE === "true",
  coverageProvider: "v8",
  // Coverage collection patterns
  collectCoverageFrom: [
    "server/**/*.{js,ts}",
    "frontend/src/**/*.{js,ts,tsx}",
    "!**/__tests__/**",
    "!**/node_modules/**",
    "!**/dist/**",
    "!**/coverage/**",
    "!**/tests/e2e/**",
    "!**/playwright/**",
    "!**/*.d.ts",
    "!**/jest.config.*",
    "!**/babel.config.*",
    "!**/vite.config.*",
    "!**/tailwind.config.*",
    "!**/postcss.config.*",
  ],
  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 75,
      functions: 75,
      lines: 75,
      statements: 75,
    },
  },
  // Coverage reporting
  coverageReporters: ["text", "lcov", "html", "json"],
  coverageDirectory: "<rootDir>/coverage",
  // Exclude E2E and Playwright tests from Jest runs
  testPathIgnorePatterns: [
    "/node_modules/",
    "/dist/",
    "/__tests__/fixtures/",
    "\\.slow\\.test\\.[jt]s$",
    "\\.skip$",
    "/tests/e2e/",
    "tests/e2e/",
    "e2e/",
    "playwright/",
    "\\.e2e\\.test\\.",
    "\\.playwright\\.test\\.",
  ],
  // Reporters for CI integration (only in CI)
  reporters: isCI
    ? [
        "default",
        [
          "jest-junit",
          {
            outputDirectory: "<rootDir>/test-results",
            outputName: "junit.xml",
          },
        ],
      ]
    : ["default"],
};
