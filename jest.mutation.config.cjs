/**
 * Jest Configuration for Mutation Testing
 *
 * This configuration is specifically designed for Stryker mutation testing
 * and handles the TypeScript files in the server directory.
 */

module.exports = {
  preset: "ts-jest",
  testEnvironment: "node",
  rootDir: ".",
  roots: ["<rootDir>/server"],

  // Test file patterns - Focus on eventLogs for analysis
  testMatch: ["<rootDir>/server/models/__tests__/eventLogs.test.ts"],

  // Setup files
  setupFilesAfterEnv: ["<rootDir>/server/jest.setup.ts"],

  // TypeScript transformation
  transform: {
    "^.+\\.[tj]s$": [
      "ts-jest",
      {
        diagnostics: false,
        tsconfig: "server/tsconfig.json",
      },
    ],
  },

  // Module resolution
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/server/$1",
  },

  // Ignore patterns
  testPathIgnorePatterns: [
    "/node_modules/",
    "/dist/",
    "/build/",
    "/stryker-tmp/",
    "/coverage/",
  ],

  // Module directories
  moduleDirectories: ["node_modules", "<rootDir>/server"],

  // Coverage settings (minimal for mutation testing)
  collectCoverage: false,

  // Timeout
  testTimeout: 30000,

  // Performance
  maxWorkers: 1,
};
