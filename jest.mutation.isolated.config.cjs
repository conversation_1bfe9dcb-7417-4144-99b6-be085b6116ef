/**
 * Isolated Jest Configuration for Mutation Testing
 * This configuration ensures proper test isolation to prevent conflicts
 */

module.exports = {
  preset: "ts-jest",
  testEnvironment: "node",
  rootDir: ".",
  roots: ["<rootDir>/server"],

  // Test file patterns
  testMatch: [
    "<rootDir>/server/**/__tests__/*.test.ts",
    "<rootDir>/server/**/__tests__/*.test.js",
  ],

  // Use isolated setup file
  setupFilesAfterEnv: ["<rootDir>/server/jest.isolated.setup.ts"],

  // TypeScript transformation
  transform: {
    "^.+\\.[tj]s$": [
      "ts-jest",
      {
        diagnostics: false,
        tsconfig: "server/tsconfig.json",
        isolatedModules: true,
      },
    ],
  },

  // Module resolution
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/server/$1",
  },

  // Ignore patterns
  testPathIgnorePatterns: [
    "/node_modules/",
    "/dist/",
    "/build/",
    "/stryker-tmp/",
    "/coverage/",
  ],

  // Module directories
  moduleDirectories: ["node_modules", "<rootDir>/server"],

  // Coverage settings
  collectCoverage: false,

  // Test isolation settings
  resetMocks: true,
  clearMocks: true,
  restoreMocks: true,
  resetModules: true,

  // Run tests in band to avoid conflicts
  maxWorkers: 1,

  // Timeout
  testTimeout: 30000,

  // Globals
  globals: {
    "ts-jest": {
      isolatedModules: true,
    },
  },
};
