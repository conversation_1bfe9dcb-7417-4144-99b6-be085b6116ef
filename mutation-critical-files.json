{"description": "Top 20 critical files for mutation testing", "files": [{"source": "server/models/systemSettings.ts", "test": "server/models/__tests__/systemSettings.test.ts", "importance": "CRITICAL - System configuration and feature flags"}, {"source": "server/models/userClass.ts", "test": "server/models/__tests__/user.test.ts", "importance": "CRITICAL - User authentication and authorization"}, {"source": "server/models/workspace.ts", "test": "server/models/__tests__/workspace.test.ts", "importance": "CRITICAL - Multi-tenant workspace isolation"}, {"source": "server/models/documents.ts", "test": "server/models/__tests__/documents.test.ts", "importance": "CRITICAL - Document security and storage"}, {"source": "server/models/workspaceChats.ts", "test": "server/models/__tests__/workspaceChats.test.ts", "importance": "HIGH - Chat history and persistence"}, {"source": "server/endpoints/admin.ts", "test": "server/endpoints/__tests__/admin.test.ts", "importance": "CRITICAL - Administrative functions"}, {"source": "server/endpoints/system.ts", "test": "server/endpoints/__tests__/system-simple.test.ts", "importance": "CRITICAL - System settings and configuration"}, {"source": "server/endpoints/chat.ts", "test": "server/endpoints/__tests__/chat-simple.test.ts", "importance": "CRITICAL - Core chat functionality"}, {"source": "server/endpoints/workspaces.ts", "test": "server/endpoints/__tests__/workspaces.test.ts", "importance": "HIGH - Workspace management"}, {"source": "server/endpoints/document.ts", "test": "server/endpoints/__tests__/document.test.ts", "importance": "HIGH - Document operations"}, {"source": "server/models/organization.ts", "test": "server/models/__tests__/organization.test.ts", "importance": "HIGH - Multi-tenant organization management"}, {"source": "server/models/apiKeys.ts", "test": "server/models/__tests__/apiKeys.test.ts", "importance": "CRITICAL - API authentication"}, {"source": "server/models/eventLogs.ts", "test": "server/models/__tests__/eventLogs.test.ts", "importance": "MEDIUM - Audit logging"}, {"source": "server/endpoints/embedManagement.ts", "test": "server/endpoints/__tests__/embedManagement.test.ts", "importance": "HIGH - RAG and embedding management"}, {"source": "server/endpoints/workspaceThreads.ts", "test": "server/endpoints/__tests__/workspaceThreads.test.ts", "importance": "MEDIUM - Chat threading"}, {"source": "server/endpoints/workspaceDocuments.ts", "test": "server/endpoints/__tests__/workspaceDocuments.test.ts", "importance": "HIGH - Document workspace association"}, {"source": "server/endpoints/user.ts", "test": "server/endpoints/__tests__/user.test.ts", "importance": "HIGH - User profile management"}, {"source": "server/endpoints/requestLegalAssistance.ts", "test": "server/endpoints/__tests__/requestLegalAssistance.test.ts", "importance": "MEDIUM - Legal assistance workflows"}, {"source": "server/endpoints/generateLegalTaskPrompt.ts", "test": "server/endpoints/__tests__/generateLegalTaskPrompt.test.ts", "importance": "MEDIUM - Legal task generation"}, {"source": "server/endpoints/autoCodePrompt.ts", "test": "server/endpoints/__tests__/autoCodePrompt.test.ts", "importance": "MEDIUM - Auto code generation"}]}