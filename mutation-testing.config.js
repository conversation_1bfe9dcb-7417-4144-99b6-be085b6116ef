/**
 * Advanced Mutation Testing Configuration for ISTLegal
 * This configuration provides comprehensive mutation testing for critical components
 * with performance optimization and detailed reporting.
 */

module.exports = {
  // Core mutation testing settings
  mutationTesting: {
    // Critical security components - highest priority
    security: {
      files: [
        "server/utils/middleware/authenticatedUserOnly.ts",
        "server/utils/middleware/validWorkspace.ts",
        "server/utils/middleware/requireAdminRole.ts"
      ],
      mutators: [
        "ArithmeticOperator",
        "ArrayDeclaration",
        "BlockStatement",
        "BooleanLiteral",
        "ConditionalExpression",
        "EqualityOperator",
        "LogicalOperator",
        "MethodExpression",
        "ObjectLiteral",
        "StringLiteral",
        "UnaryOperator",
        "UpdateOperator"
      ],
      thresholds: {
        high: 95,
        low: 90,
        break: 85
      },
      timeout: 45000
    },

    // Core business logic - high priority
    coreLogic: {
      files: [
        "server/models/systemSettings.ts",
        "server/models/eventLogs.ts",
        "server/models/workspaceChats.ts"
      ],
      mutators: [
        "ArithmeticOperator",
        "ArrayDeclaration",
        "BooleanLiteral",
        "ConditionalExpression",
        "EqualityOperator",
        "LogicalOperator",
        "MethodExpression",
        "ObjectLiteral",
        "StringLiteral"
      ],
      thresholds: {
        high: 90,
        low: 85,
        break: 80
      },
      timeout: 60000
    },

    // AI providers - medium priority
    aiProviders: {
      files: ["server/utils/AiProviders/cohere/index.ts"],
      mutators: [
        "ArithmeticOperator",
        "BooleanLiteral",
        "ConditionalExpression",
        "EqualityOperator",
        "LogicalOperator",
        "MethodExpression",
        "StringLiteral"
      ],
      thresholds: {
        high: 85,
        low: 80,
        break: 75
      },
      timeout: 90000
    },

    // Vector database providers - medium priority
    vectorDatabases: {
      files: [
        "server/utils/vectorDbProviders/chroma/index.ts",
        "server/utils/vectorDbProviders/pinecone/index.ts",
        "server/utils/vectorDbProviders/qdrant/index.ts"
      ],
      mutators: [
        "ArithmeticOperator",
        "BooleanLiteral",
        "ConditionalExpression",
        "EqualityOperator",
        "LogicalOperator",
        "MethodExpression",
        "StringLiteral"
      ],
      thresholds: {
        high: 85,
        low: 80,
        break: 75
      },
      timeout: 120000
    },

    // Frontend utilities - lower priority
    frontend: {
      files: [
        "frontend/src/utils/workspace.ts",
        "frontend/src/utils/numbers.ts",
        "frontend/src/utils/events.ts",
        "frontend/src/stores/attachmentStore.ts",
        "frontend/src/stores/progressStore.ts"
      ],
      mutators: [
        "ArithmeticOperator",
        "BooleanLiteral",
        "ConditionalExpression",
        "EqualityOperator",
        "LogicalOperator",
        "StringLiteral"
      ],
      thresholds: {
        high: 80,
        low: 75,
        break: 70
      },
      timeout: 30000
    }
  },

  // Performance optimization settings
  performance: {
    maxConcurrentTestRunners: Math.max(
      1,
      Math.floor(require("os").cpus().length / 2)
    ),
    timeoutFactor: 1.5,
    disableTypeChecks: true,
    coverageAnalysis: "perTest",
    clearTextReporter: {
      allowColor: true,
      allowEmojis: true,
      logTests: false
    }
  },

  // Reporting configuration
  reporting: {
    reporters: ["html", "clear-text", "progress", "json"],
    htmlReporter: {
      baseDir: "reports/mutation-testing",
      files: ["mutation-report.html"]
    },
    jsonReporter: {
      fileName: "reports/mutation-testing/mutation-report.json"
    }
  },

  // Test configuration
  testConfiguration: {
    testRunner: "jest",
    testFramework: "jest",
    coverageAnalysis: "perTest",
    tempDirName: "stryker-tmp",
    cleanTempDir: "always"
  },

  // Ignored patterns
  ignorePatterns: [
    "**/node_modules/**",
    "**/dist/**",
    "**/build/**",
    "**/*.test.ts",
    "**/*.test.js",
    "**/coverage/**",
    "**/reports/**",
    "**/*.d.ts",
    "**/migrations/**",
    "**/seeds/**"
  ],

  // Git integration
  gitIntegration: {
    enabled: true,
    baseline: "develop",
    compareWith: "HEAD"
  }
}
