{"name": "ist-legal", "version": "1.1.3", "description": "IST Legal platform.", "main": "index.js", "type": "module", "author": "IST Legal", "license": "Copyright", "engines": {"node": ">=20"}, "scripts": {"prepare": "git config core.hooksPath .husky", "lint": "(npm run lint:server && npm run lint:frontend) || (echo '<PERSON><PERSON> failed' && exit 1)", "lint:server": "cd server && npm run lint", "lint:frontend": "cd frontend && npm run lint", "check-translations": "node scripts/verifyLocaleFiles.mjs", "verify:translations": "node scripts/verifyLocaleFiles.mjs", "test:ci": "CI=true jest --forceExit --detectOpenHandles", "setup": "cd server && npm install && cd ../collector && npm install && cd ../frontend && npm install && cd .. && npm run setup:envs && npm run prisma:setup && echo \"Please run npm run dev:server, npm run dev:collector, and npm run dev:frontend in separate terminal tabs.\"", "setup:envs": "node scripts/copy-env.js", "setup:codex": "node scripts/setup-codex.js", "dev:server": "cd server && npm run dev", "dev:collector": "cd collector && npm run dev", "dev:frontend": "cd frontend && npm run dev", "prisma:generate": "cd server && npx prisma generate", "prisma:migrate": "cd server && npx prisma migrate dev --name init", "prisma:seed": "cd server && npx prisma db seed", "prisma:setup": "npm run prisma:generate && npm run prisma:migrate && npm run prisma:seed", "prisma:reset": "node scripts/reset-db.js && npm run prisma:migrate", "prisma:devupdate": "cd server/prisma && npx prisma db push", "prod:server": "cd server && npm start", "prod:frontend": "cd frontend && npm run build", "generate:cloudformation": "node cloud-deployments/aws/cloudformation/generate.mjs", "generate::gcp_deployment": "node cloud-deployments/gcp/deployment/generate.mjs", "version:info": "node scripts/version-management.js info", "version:sync": "node scripts/version-management.js sync", "version:bump:patch": "node scripts/version-management.js bump patch", "version:bump:minor": "node scripts/version-management.js bump minor", "version:bump:major": "node scripts/version-management.js bump major", "version:set": "node scripts/version-management.js set", "version:release": "node scripts/version-management.js release", "version:tag": "node scripts/version-management.js tag", "test": "NODE_ENV=test npx jest --forceExit", "test:fast": "NODE_ENV=test npx jest --testPathPattern='(unit|spec)' --maxWorkers=4", "test:unit": "NODE_ENV=test npx jest --testPathPattern='(unit|spec)' --maxWorkers=4", "test:integration": "NODE_ENV=test npx jest --testPathPattern='(integration|int\\.|e2e)'", "test:watch": "NODE_ENV=test npx jest --watch", "test:coverage": "NODE_ENV=test COLLECT_COVERAGE=true npx jest --forceExit", "test:pre-push": "./scripts/pre-push-tests.sh", "test:cleanup": "cd server && node scripts/cleanup-test-logs.js", "test:cleanup:stats": "cd server && node scripts/cleanup-test-logs.js --stats", "test:validate": "node server/scripts/validate-test-environment.js", "test:validate:fix": "node server/scripts/validate-test-environment.js --fix", "test:comprehensive": "./scripts/test-with-cleanup.sh"}, "private": false, "devDependencies": {"@jest/globals": "^29.7.0", "@prisma/client": "^6.11.1", "@types/jest": "^30.0.0", "cursor-tools": "latest", "eslint": "^9.25.1", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-junit": "^16.0.0", "lint-staged": "^15.5.1", "node-mocks-http": "^1.11.0", "prettier": "^3.6.2", "prisma": "^6.11.1", "recast": "^0.23.11", "ts-jest": "^29.4.0"}, "dependencies": {"@ladjs/graceful": "^4.0.0", "bcrypt": "^5.1.1", "bree": "^9.2.3", "docxtemplater": "^3.43.0", "glob": "^11.0.3", "immer": "^10.1.1", "js-tiktoken": "^1.0.16", "pizzip": "^3.0.6", "remark-docx": "^0.1.6", "remark-parse": "^10.0.2", "sharp": "^0.34.2", "unified": "^11.0.5"}, "lint-staged": {"**/*.{js,jsx,ts,tsx,json,css,md}": ["prettier --write"], "server/**/*.{js,jsx,ts,tsx}": ["cd server && npx eslint --max-warnings 0", "cd server && npx prisma generate"], "frontend/**/*.{js,jsx,ts,tsx}": ["cd frontend && npx eslint --max-warnings 0"], "collector/**/*.{js,jsx,ts,tsx}": ["cd collector && npx eslint --max-warnings 0"], "server/**/*.{ts,tsx}": ["cd server && npx tsc --noEmit"], "frontend/**/*.{ts,tsx}": ["cd frontend && npx tsc --noEmit --skipL<PERSON><PERSON><PERSON>ck"], "collector/**/*.{ts,tsx}": ["cd collector && npx tsc --noEmit"]}}