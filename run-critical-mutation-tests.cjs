#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to run mutation testing on the 20 most critical files
 * This script runs tests in batches to avoid memory issues and provide better progress tracking
 */

const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");

// Ensure we're in the right directory
process.chdir(path.resolve(__dirname));

console.log("🧬 Starting Critical Files Mutation Testing...\n");
console.log("📊 This will test the 5 most critical model files:\n");
console.log("  1. systemSettings.ts - System configuration");
console.log("  2. userClass.ts - Authentication & authorization");
console.log("  3. workspace.ts - Multi-tenant isolation");
console.log("  4. documents.ts - Document security");
console.log("  5. apiKeys.ts - API authentication\n");

// Clean up any previous runs
console.log("🧹 Cleaning up previous runs...");
if (fs.existsSync("stryker-tmp")) {
  execSync("rm -rf stryker-tmp", { stdio: "inherit" });
}

// Ensure reports directory exists
if (!fs.existsSync("reports/mutation")) {
  fs.mkdirSync("reports/mutation", { recursive: true });
}

// Run mutation testing
console.log("🚀 Running mutation tests on critical files...\n");
const startTime = Date.now();

try {
  execSync("npx stryker run --logLevel info ./stryker-batch1.conf.json", {
    stdio: "inherit",
    env: { ...process.env, NODE_ENV: "test" },
  });
} catch (error) {
  console.error("❌ Error during mutation testing:", error.message);
}

const endTime = Date.now();
const duration = Math.round((endTime - startTime) / 1000);

// Check for mutation report
const reportPath = "reports/mutation/batch1-mutation-report.html";
const jsonReportPath = "reports/mutation/batch1-mutation-report.json";

if (fs.existsSync(reportPath)) {
  console.log("\n✅ HTML mutation report generated:", reportPath);
}

if (fs.existsSync(jsonReportPath)) {
  console.log("✅ JSON mutation report generated:", jsonReportPath);

  // Parse and display summary
  try {
    const report = JSON.parse(fs.readFileSync(jsonReportPath, "utf8"));
    console.log("\n📊 Mutation Testing Summary:");
    console.log("─".repeat(60));

    if (report.files) {
      let totalMutants = 0;
      let totalKilled = 0;
      let totalSurvived = 0;
      let totalTimeout = 0;

      Object.entries(report.files).forEach(([file, data]) => {
        const fileName = path.basename(file);
        const score = data.mutationScore || 0;
        const killed = data.killedMutants || 0;
        const survived = data.survivedMutants || 0;
        const timeout = data.timedOutMutants || 0;
        const total = killed + survived + timeout;

        totalMutants += total;
        totalKilled += killed;
        totalSurvived += survived;
        totalTimeout += timeout;

        console.log(`\n📄 ${fileName}`);
        console.log(`   Mutation Score: ${score.toFixed(2)}%`);
        console.log(
          `   Mutants: ${total} (Killed: ${killed}, Survived: ${survived}, Timeout: ${timeout})`
        );
      });

      const overallScore =
        totalMutants > 0 ? (totalKilled / totalMutants) * 100 : 0;
      console.log("\n" + "─".repeat(60));
      console.log(`Overall Mutation Score: ${overallScore.toFixed(2)}%`);
      console.log(`Total Mutants: ${totalMutants}`);
      console.log(`Total Killed: ${totalKilled}`);
      console.log(`Total Survived: ${totalSurvived}`);
      console.log(`Total Timeout: ${totalTimeout}`);
    }
  } catch {
    console.log("Could not parse JSON report for summary");
  }
}

console.log("\n⏱️  Total execution time:", duration, "seconds");
console.log("\n🏁 Critical files mutation testing complete!");
console.log("\n💡 Next steps:");
console.log("   - Review survived mutants in the HTML report");
console.log("   - Add tests to kill survived mutants");
console.log("   - Run full 20-file test with stryker-critical.conf.json");
