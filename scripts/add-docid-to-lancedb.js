// scripts/add-docid-to-lancedb.js
import "dotenv/config"; // Ensure environment variables are loaded
import lancedb from "@lancedb/lancedb"; // Use official LanceDB client
import { PrismaClient } from "@prisma/client";
import path from "path";
import glob from "glob";

// --- Configuration ---
// Base path for LanceDB storage: default to server/storage directory
const baseStoragePath = path.resolve(
  process.env.STORAGE_DIR || "./server/storage"
);
const lanceDbDirPattern = path.join(baseStoragePath, "lancedb", "*.lance");
const prisma = new PrismaClient();

// Use 'title' based on codebase analysis. This field should contain the document path.
const LANCEDB_SOURCE_PATH_FIELD = "title";

async function findLanceDBTables() {
  return new Promise((resolve, reject) => {
    glob(lanceDbDirPattern, (err, files) => {
      if (err) {
        return reject(err);
      }
      resolve(files.map((f) => path.resolve(f))); // Ensure absolute paths
    });
  });
}

// Cache for Prisma lookups to reduce DB hits
const docPathToIdCache = new Map();

async function getDocIdFromPath(docPath) {
  if (docPathToIdCache.has(docPath)) {
    return docPathToIdCache.get(docPath);
  }

  try {
    const documentPathEntry = await prisma.document_paths.findUnique({
      where: { path: docPath },
      select: { id: true }, // Only select the ID
    });

    if (!documentPathEntry) {
      console.warn(
        `    Warning: Could not find docId for path "${docPath}" in document_paths.`
      );
      docPathToIdCache.set(docPath, null); // Cache null result
      return null;
    }

    docPathToIdCache.set(docPath, documentPathEntry.id);
    return documentPathEntry.id;
  } catch (_error) {
    console.error(`    Error querying Prisma for path "${docPath}":`, _error);
    docPathToIdCache.set(docPath, null); // Cache error as null
    return null;
  }
}

async function updateTableMetadata(tablePath) {
  console.log(`\nProcessing table: ${tablePath}`);
  let db; // will hold the LanceDB client
  let table;
  let recordsToUpdate = 0;
  let recordsSkipped = 0;
  let errorCount = 0;

  try {
    const containingDir = path.dirname(tablePath);
    const tableName = path.basename(tablePath);
    console.log(`  Connecting to LanceDB at: ${containingDir}`);
    console.log(`  Opening table: ${tableName}`);

    db = await lancedb.connect(containingDir); // Connect to the directory containing the .lance folder
    table = await db.openTable(tableName);

    const schema = table.schema;

    // --- Check if docId already exists ---
    if (schema.fields.some((field) => field.name === "docId")) {
      console.log(`  Skipping table: 'docId' column already exists.`);
      // Optional: Add verification logic here if needed
      return { updated: 0, skipped: -1, errors: 0 }; // Indicate skipped table
    }

    // --- Check if the source path field exists ---
    if (
      !schema.fields.some((field) => field.name === LANCEDB_SOURCE_PATH_FIELD)
    ) {
      console.error(
        `  Skipping table: Source path field '${LANCEDB_SOURCE_PATH_FIELD}' not found in schema.`
      );
      return { updated: 0, skipped: 0, errors: 1 }; // Indicate error
    }

    // --- Fetch all existing records with the source path ---
    console.log(
      `  Fetching existing records (requesting field: '${LANCEDB_SOURCE_PATH_FIELD}')...`
    );
    // Select only necessary fields if possible (vector for merging + source path)
    const records = await table
      .search()
      .select(["vector", LANCEDB_SOURCE_PATH_FIELD]) // Adjust if 'vector' isn't the merge key
      .execute();
    console.log(`  Found ${records.length} records.`);

    if (records.length === 0) {
      console.log("  Skipping empty table.");
      return { updated: 0, skipped: 0, errors: 0 };
    }

    // --- Prepare data for update ---
    console.log(`  Looking up docIds in Prisma for unique source paths...`);
    const dataToUpdate = [];
    const uniquePaths = new Set(
      records.map((r) => r[LANCEDB_SOURCE_PATH_FIELD]).filter(Boolean)
    );
    console.log(`  Found ${uniquePaths.size} unique source paths to look up.`);

    // Pre-fetch all docIds for efficiency
    for (const docPath of uniquePaths) {
      await getDocIdFromPath(docPath); // Populate cache
    }
    console.log(`  Finished Prisma lookups (populated cache).`);

    for (const record of records) {
      const sourcePath = record[LANCEDB_SOURCE_PATH_FIELD];
      if (!sourcePath) {
        console.warn(
          `  Skipping record: Missing source path field ('${LANCEDB_SOURCE_PATH_FIELD}') value.`
        );
        recordsSkipped++;
        continue;
      }

      const docId = await getDocIdFromPath(sourcePath); // Use cached value

      if (!docId) {
        console.warn(
          `  Skipping record: Could not find docId for source path "${sourcePath}".`
        );
        recordsSkipped++;
        continue;
      }

      // Prepare the record with the new docId
      // Keep existing data (like the vector) needed for the merge
      dataToUpdate.push({
        ...record, // Include existing data needed for merge (e.g., vector)
        docId: docId,
      });
      recordsToUpdate++;
    }

    if (dataToUpdate.length === 0) {
      console.log("  No records found to update after processing.");
      return { updated: 0, skipped: recordsSkipped, errors: errorCount };
    }

    // --- Add the new column and merge data ---
    console.log(
      `  Attempting to add 'docId' column and merge ${dataToUpdate.length} records...`
    );

    // Define the schema extension - Use string type for Prisma IDs (UUIDs)
    const _newSchema = table.schema.addField({ name: "docId", type: "string" });

    // Use mergeInsert based on a unique identifier (vector is common)
    await table
      .mergeInsert("vector") // <-- Ensure 'vector' is the correct merge key
      .whenMatchedUpdateAll() // Update matched records
      .whenNotMatchedInsertAll() // Should not happen if we fetched all first
      .execute(dataToUpdate);

    console.log(
      `  Successfully updated metadata for ${recordsToUpdate} records.`
    );

    // Return success result from try block
    return {
      updated: recordsToUpdate,
      skipped: recordsSkipped,
      errors: errorCount,
    };
  } catch (_error) {
    console.error(`  Error processing table ${tablePath}:`, _error);
    errorCount++; // Count the whole table as an error

    // Return error result from catch block
    return {
      updated: recordsToUpdate,
      skipped: recordsSkipped,
      errors: errorCount,
    };
  } finally {
    // Proper cleanup - no return statements in finally!
    try {
      if (db && typeof db.close === "function") {
        await db.close();
        console.log(`  Closed LanceDB connection for ${tablePath}`);
      }
    } catch (cleanupError) {
      console.warn(
        `  Warning: Error closing LanceDB connection:`,
        cleanupError
      );
    }

    console.log(
      `  Finished table ${tablePath}. Updated: ${recordsToUpdate}, Skipped: ${recordsSkipped}, Errors: ${errorCount}`
    );
  }
}

async function main() {
  console.log("Starting LanceDB docId metadata update script...");
  try {
    await prisma.$connect();
    console.log("Connected to Prisma.");
  } catch (error) {
    console.error("Failed to connect to Prisma:", error);
    process.exit(1);
  }

  const tables = await findLanceDBTables();
  if (tables.length === 0) {
    console.log("No LanceDB tables found matching the pattern.");
    await prisma.$disconnect();
    return;
  }

  console.log(`Found ${tables.length} potential LanceDB tables.`);

  let totalUpdated = 0;
  let totalSkipped = 0;
  let totalErrors = 0;

  for (const tablePath of tables) {
    const result = await updateTableMetadata(tablePath);
    if (result) {
      // Ensure result is not undefined in case of early exit
      totalUpdated += result.updated;
      totalSkipped += result.skipped;
      totalErrors += result.errors;
    } else {
      totalErrors++; // Count as error if function returned unexpectedly
    }
  }

  console.log("\n--- Script Summary ---");
  console.log(`Processed ${tables.length} tables.`);
  console.log(`Total records updated with docId: ${totalUpdated}`);
  console.log(
    `Total records skipped (missing path, Prisma lookup failed, etc.): ${totalSkipped}`
  );
  console.log(`Total errors (table level): ${totalErrors}`);
  console.log("--------------------");

  await prisma.$disconnect();
  console.log("Disconnected from Prisma. Script finished.");
}

main().catch((e) => {
  console.error("Script failed with a critical error:", e);
  prisma.$disconnect().finally(() => process.exit(1));
});
