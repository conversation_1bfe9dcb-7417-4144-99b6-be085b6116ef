import { promises as fs } from "fs";

const paths = [
  { src: "./frontend/.env.example", dest: "./frontend/.env" },
  { src: "./server/.env.example", dest: "./server/.env.development" },
  { src: "./collector/.env.example", dest: "./collector/.env" },
  { src: "./docker/.env.example", dest: "./docker/.env" },
];

const copyEnvFiles = async () => {
  for (const { src, dest } of paths) {
    try {
      try {
        await fs.access(dest);
        console.log(`File ${dest} already exists, skipping...`);
      } catch {
        await fs.copyFile(src, dest);
        console.log(`Copied ${src} to ${dest}`);
      }
    } catch (_error) {
      console.error(`Error processing ${src} -> ${dest}: ${_error.message}`);
    }
  }
  console.log("All ENV files copied!");
};

copyEnvFiles();
