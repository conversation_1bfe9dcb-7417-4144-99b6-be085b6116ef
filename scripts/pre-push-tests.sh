#!/bin/bash

echo "Running comprehensive tests before push..."
echo "========================================="

# Run all linting
echo "Running linting checks..."
npm run lint || {
  echo "❌ <PERSON><PERSON> failed"
  exit 1
}
echo "✅ <PERSON><PERSON> passed"

# Run locale verification
echo "Verifying locale files..."
npm run verify:translations || {
  echo "❌ Locale verification failed"
  exit 1
}
echo "✅ Locale files verified"

# Run all tests (not just unit tests)
echo "Running all tests (unit + integration)..."
npm test || {
  echo "❌ Tests failed"
  exit 1
}
echo "✅ All tests passed"

# Run TypeScript type checking
echo "Running TypeScript type checking..."
cd server && npm run typecheck && cd .. || {
  echo "❌ TypeScript type checking failed"
  exit 1
}
echo "✅ TypeScript checks passed"

echo ""
echo "========================================="
echo "✅ All pre-push checks passed! Safe to push."
echo "========================================="