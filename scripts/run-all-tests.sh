#!/bin/bash
# Comprehensive Test Suite Runner
# Runs all tests including <PERSON><PERSON>, <PERSON><PERSON><PERSON> mutation tests, and Playwright E2E tests

set -e

echo "🧪 Running Comprehensive Test Suite"
echo "=================================="
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Track test results
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run a test and track results
run_test() {
    local test_name="$1"
    local test_command="$2"

    print_status "Running $test_name..."
    echo "Command: $test_command"
    echo

    if eval "$test_command"; then
        print_success "$test_name passed"
        ((TESTS_PASSED++))
    else
        print_error "$test_name failed"
        ((TESTS_FAILED++))
        if [[ "$3" == "continue" ]]; then
            print_warning "Continuing with remaining tests..."
        else
            exit 1
        fi
    fi
    echo
}

# Check if we're in CI mode
if [[ "$CI" == "true" ]]; then
    print_status "Running in CI mode with stricter settings"
    CI_FLAG="CI=true"
else
    CI_FLAG=""
fi

# Pre-test cleanup
print_status "Performing pre-test cleanup..."
npm run test:cleanup || print_warning "Pre-test cleanup failed, continuing..."

# 1. Run Jest tests (unit + integration)
run_test "Jest Tests (Unit + Integration)" "$CI_FLAG npm test" "continue"

# 2. Run Stryker mutation tests
print_status "Running Stryker mutation tests..."
print_warning "Mutation tests can take a long time to complete..."

# Run mutation tests in parallel if not in CI
if [[ "$CI" == "true" ]]; then
    run_test "Stryker Mutation Tests (CI mode)" "$CI_FLAG npm run test:mutation:ci" "continue"
else
    # Run different mutation test suites
    run_test "Stryker Security Tests" "npm run test:mutation:security" "continue"
    run_test "Stryker Core Tests" "npm run test:mutation:core" "continue"
    run_test "Stryker AI Provider Tests" "npm run test:mutation:ai" "continue"
    run_test "Stryker Vector DB Tests" "npm run test:mutation:vector" "continue"
fi

# 3. Run Playwright E2E tests
print_status "Starting Playwright E2E tests..."
print_warning "E2E tests require the application to be running"

# Check if server is running on port 3001 (as configured in playwright.config.mjs)
if ! curl -s http://localhost:3001 > /dev/null 2>&1; then
    print_warning "Server not detected on port 3001. Starting server for E2E tests..."
    print_status "Starting development server in background..."
    npm run dev:server &
    SERVER_PID=$!

    # Wait for server to start
    print_status "Waiting for server to start..."
    for i in {1..30}; do
        if curl -s http://localhost:3001 > /dev/null 2>&1; then
            print_success "Server started successfully"
            break
        fi
        if [[ $i -eq 30 ]]; then
            print_error "Server failed to start within 30 seconds"
            exit 1
        fi
        sleep 1
    done
else
    print_success "Server already running on port 3001"
    SERVER_PID=""
fi

# Run E2E tests
run_test "Playwright E2E Tests" "npm run test:e2e" "continue"

# Cleanup server if we started it
if [[ -n "$SERVER_PID" ]]; then
    print_status "Stopping development server..."
    kill $SERVER_PID 2>/dev/null || true
fi

# Post-test cleanup
print_status "Performing post-test cleanup..."
npm run test:cleanup || print_warning "Post-test cleanup failed"

# Final summary
echo
echo "=================================="
echo "🧪 Test Suite Summary"
echo "=================================="
echo -e "${GREEN}✅ Tests Passed: $TESTS_PASSED${NC}"
echo -e "${RED}❌ Tests Failed: $TESTS_FAILED${NC}"

if [[ $TESTS_FAILED -eq 0 ]]; then
    print_success "All test suites completed successfully!"
    exit 0
else
    print_error "Some test suites failed. Please review the output above."
    exit 1
fi
