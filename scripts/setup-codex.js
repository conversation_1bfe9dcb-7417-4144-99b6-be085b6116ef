#!/usr/bin/env node
import { execSync } from "child_process";
import { dirname, join } from "path";
import { fileURLToPath } from "url";
import { existsSync } from "fs";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Check for skip tests flag from command line args or environment variable
const skipTests =
  process.argv.includes("--skip-tests") || process.env.SKIP_TESTS === "true";

if (skipTests) {
  console.log("Skipping tests as requested...");
}

function run(command, cwd) {
  console.log(`Running "${command}" in ${cwd}`);
  try {
    execSync(command, { cwd, stdio: "inherit" });
  } catch (_error) {
    console.error(`Error executing "${command}" in ${cwd}:`, _error.message);
    console.error("Continuing with next command...");
  }
}

const dirs = ["server", "collector", "frontend"];

for (const dir of dirs) {
  const full = join(__dirname, "..", dir);

  // Check if directory exists before running commands
  if (!existsSync(full)) {
    console.warn(`Directory "${dir}" does not exist at ${full}, skipping...`);
    continue;
  }

  console.log(`\nProcessing directory: ${dir}`);
  run("npm install", full);

  if (!skipTests) {
    run("npm test", full);
  }
}

console.log("Codex setup complete.");
