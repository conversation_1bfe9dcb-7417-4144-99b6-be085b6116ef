# Testing Documentation

## Overview

The IST Legal platform uses multiple testing strategies to ensure code quality and reliability:

1. **Unit Tests** - Test individual functions and components in isolation
2. **Integration Tests** - Test interactions between different parts of the system
3. **End-to-End (E2E) Tests** - Test complete user workflows with <PERSON>wright

## Testing Stack

- **Jest** - Primary testing framework for unit and integration tests
- **Playwright** - Browser automation for E2E tests
- **Supertest** - HTTP assertion library for API testing
- **React Testing Library** - Component testing for frontend
- **Stryker** - Mutation testing for test quality

## Running Tests

### All Tests

```bash
npm test                # Run all Jest tests
npm run test:e2e       # Run Playwright E2E tests
```

### Specific Test Types

```bash
npm run test:unit      # Unit tests only
npm run test:integration # Integration tests
npm run test:coverage  # With coverage report
npm run test:mutation  # Mutation testing
```

### Watch Mode

```bash
npm run test:watch     # Re-run tests on file changes
```

## Test File Naming

- **Unit tests**: `*.test.ts` or `*.spec.ts`
- **Integration tests**: `*.int.test.ts` or `*.integration.test.ts`
- **E2E tests**: `*.spec.ts` in `e2e-playwright/` directory

## Writing Tests

### Unit Test Example

```typescript
import { validateUsername } from "../utils/validation";

describe("validateUsername", () => {
  it("should accept valid usernames", () => {
    expect(validateUsername("user123")).toBe(true);
    expect(validateUsername("test.user")).toBe(true);
  });

  it("should reject invalid usernames", () => {
    expect(validateUsername("User123")).toBe(false); // No uppercase
    expect(validateUsername("user!")).toBe(false); // No special chars
  });
});
```

### Integration Test Example

```typescript
import request from "supertest";
import app from "../index";

describe("API Endpoints", () => {
  it("should return user data", async () => {
    const response = await request(app)
      .get("/api/user/1")
      .set("Authorization", "Bearer valid-token");

    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty("username");
  });
});
```

### E2E Test Example

```typescript
import { test, expect } from "@playwright/test";

test("user can login", async ({ page }) => {
  await page.goto("http://localhost:3000");
  await page.fill('input[name="email"]', "<EMAIL>");
  await page.fill('input[name="password"]', "password");
  await page.click('button[type="submit"]');

  await expect(page).toHaveURL("http://localhost:3000/dashboard");
});
```

## Test Coverage

Aim for:

- **80%+ overall coverage** for critical paths
- **90%+ coverage** for utility functions
- **100% coverage** for security-critical code

Check coverage with:

```bash
npm run test:coverage
```

## Best Practices

1. **Test Behavior, Not Implementation**
   - Focus on what the code does, not how it does it
   - Tests should survive refactoring

2. **Use Descriptive Test Names**

   ```typescript
   // Good
   it("should return 404 when user is not found");

   // Bad
   it("test user endpoint");
   ```

3. **Follow AAA Pattern**
   - **Arrange**: Set up test data
   - **Act**: Execute the function
   - **Assert**: Verify the result

4. **Mock External Dependencies**

   ```typescript
   jest.mock("../utils/database");
   jest.mock("../services/email");
   ```

5. **Test Edge Cases**
   - Empty inputs
   - Null/undefined values
   - Boundary conditions
   - Error scenarios

6. **Keep Tests Fast**
   - Mock database calls
   - Mock external API calls
   - Use test data factories

7. **Test in Isolation**
   - Each test should be independent
   - Use `beforeEach` and `afterEach` for setup/cleanup

## Database Testing

When testing with Prisma:

```typescript
import { PrismaClient } from "@prisma/client";
import { mockDeep, mockReset } from "jest-mock-extended";

jest.mock("../utils/prisma", () => ({
  __esModule: true,
  default: mockDeep<PrismaClient>(),
}));

beforeEach(() => {
  mockReset(prisma);
});
```

## Continuous Integration

Tests run automatically on:

- Pull requests
- Push to develop branch
- Pre-push hooks (locally)

CI runs:

1. Linting
2. Type checking
3. Unit tests
4. Integration tests
5. E2E tests (on staging)

## Debugging Tests

### Jest Tests

```bash
# Run specific test file
npm test -- user.test.ts

# Run tests matching pattern
npm test -- --testNamePattern="should validate"

# Debug mode
node --inspect-brk ./node_modules/.bin/jest --runInBand
```

### Playwright Tests

```bash
# Debug mode
npx playwright test --debug

# Headed mode (see browser)
npx playwright test --headed

# UI mode
npx playwright test --ui
```

## Common Issues

1. **"Cannot find module" errors**
   - Check import paths
   - Ensure TypeScript paths are configured

2. **Timeout errors**
   - Increase timeout: `jest.setTimeout(10000)`
   - Check for missing `async/await`

3. **Database connection errors**
   - Tests should mock database
   - Check `NODE_ENV=test`

4. **Flaky tests**
   - Add proper waits in E2E tests
   - Mock time-dependent functions
   - Check for race conditions

## Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [Playwright Documentation](https://playwright.dev/docs/intro)
- [Testing Library](https://testing-library.com/docs/)
- [Supertest Documentation](https://github.com/visionmedia/supertest)
