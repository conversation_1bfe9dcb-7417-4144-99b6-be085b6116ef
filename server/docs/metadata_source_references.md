# Metadata-Based Source References

## Overview

The system has been updated to use metadata-based source references instead of numbered source references (like "SOURCE 1", "SOURCE 2") to provide more meaningful and user-friendly source citations in AI responses. The system uses a bracket format to clearly delineate source boundaries and automatically strips UUIDs from filenames for cleaner references.

## Implementation

### Core Functions

#### `buildSystemPrompt` (server/utils/helpers/chat/convertTo.js)

This function constructs system prompts for chat exports and uses the following metadata hierarchy:

```javascript
const rawMeta =
  source.title ||
  source.name ||
  source.url ||
  source.docSource ||
  source.docAuthor ||
  source.id ||
  `Document ${i + 1}`;

// Strip UUIDs from the metadata to create cleaner references
const meta = stripUuidFromText(rawMeta);
```

**Output Format**: `[{meta}]:\n{text}\n[END {meta}]\n\n`

#### `formatContextTexts` (server/utils/helpers/index.js)

This function formats context texts for AI providers and uses the same metadata hierarchy:

```javascript
const rawMeta =
  ctx.meta ||
  ctx.title ||
  ctx.name ||
  ctx.url ||
  ctx.docSource ||
  ctx.docAuthor ||
  ctx.id ||
  `Document ${i + 1}`;

// Strip UUIDs from the metadata to create cleaner references
const meta = stripUuidFromText(rawMeta);
```

**Output Format**: `[{meta}]:\n{text}\n[END {meta}]`

#### `stripUuidFromText` (server/utils/helpers/index.js & server/utils/helpers/chat/convertTo.js)

This utility function removes UUID patterns from text to create cleaner metadata references:

```javascript
function stripUuidFromText(text) {
  if (!text || typeof text !== "string") return text;

  // Remove UUID patterns (8-4-4-4-12 format) and surrounding hyphens
  return text
    .replace(
      /-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi,
      ""
    )
    .replace(
      /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}-?/gi,
      ""
    )
    .replace(/--+/g, "-") // Replace multiple consecutive hyphens with single hyphen
    .replace(/^-|-$/g, ""); // Remove leading/trailing hyphens
}
```

### Metadata Hierarchy

The system prioritizes metadata fields in the following order:

1. **`title`** - Document title or filename (highest priority)
2. **`name`** - Alternative name field
3. **`url`** - Document URL or path
4. **`docSource`** - Source description
5. **`docAuthor`** - Document author
6. **`id`** - Document ID
7. **`Document ${i + 1}`** - Numbered fallback (lowest priority)

**Note**: All metadata fields are automatically processed through `stripUuidFromText()` to remove UUID patterns and create cleaner references.

### Benefits

- **More Meaningful References**: Users see actual document titles instead of generic numbers
- **Better User Experience**: Easier to identify and verify sources
- **Clear Source Boundaries**: Bracket format clearly marks start and end of each source
- **Clean Filenames**: UUIDs are automatically stripped from filenames for better readability
- **Consistent Formatting**: Unified approach across all AI providers
- **Graceful Fallback**: Still provides references even when metadata is limited

## Examples

### Before (Numbered References)

```text
[SOURCE 1]:
This document contains contract terms...
[END SOURCE 1]

[SOURCE 2]:
Policy document content...
[END SOURCE 2]
```

### After (Metadata References with Brackets and UUID Stripping)

```text
[Employment Contract Template.pdf]:
This document contains contract terms...
[END Employment Contract Template.pdf]

[Company Policy Manual.docx]:
Policy document content...
[END Company Policy Manual.docx]
```

### UUID Stripping Examples

- **Before**: `Legal-Contract-abc123de-f456-7890-abcd-ef1234567890.pdf`
- **After**: `Legal-Contract.pdf`

- **Before**: `Policy-Manual-12345678-1234-1234-1234-123456789012.docx`
- **After**: `Policy-Manual.docx`

## Validation

The validation system has been updated to check for proper metadata-based references within bracket format. The validation prompt now verifies that responses contain "proper source references using meaningful metadata (such as document titles, filenames, or other descriptive identifiers) within bracket format [Source Name] rather than generic numbered references."

## Translation Updates

Legacy translation keys for numbered sources have been removed from all locale files:

- Removed: `start: "SOURCE {{index}}"`
- Removed: `end: "END SOURCE {{index}}"`
- Kept: `header: "Source(s)"` (still used for context headers)

## Testing

The implementation includes comprehensive test coverage for:

- Metadata hierarchy prioritization
- Fallback behavior when metadata is unavailable
- Mixed object and string context handling
- Header inclusion/exclusion options
- Multiple source handling
- Bracket format consistency
- **UUID stripping functionality**
- **Edge cases for UUID patterns**
- **Multiple hyphen cleanup**

## Migration Notes

This change is backward compatible as it only affects the formatting of source references, not the underlying data structure. Existing documents and contexts will automatically benefit from the improved referencing system with clear bracket-delimited source boundaries and clean filenames without UUIDs.
