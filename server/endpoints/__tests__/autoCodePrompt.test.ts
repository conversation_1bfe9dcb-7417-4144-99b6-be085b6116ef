import request from "supertest";
import express from "express";
import { setupAutoCodePromptEndpoints } from "../autoCodePrompt";
import { SystemReport } from "../../models/systemReport";
import { validatedRequest } from "../../utils/middleware/validatedRequest";
import autoCodingPromptGenerator from "../../utils/helpers/autoCodingPromptGenerator";
import slackNotifier from "../../utils/notifications/slack";
import { UserRole } from "../../types/shared";

// Mock all dependencies
jest.mock("../../models/systemReport", () => ({
  SystemReport: {
    get: jest.fn(),
  },
}));
jest.mock("../../utils/middleware/validatedRequest");
jest.mock("../../utils/helpers/autoCodingPromptGenerator");
jest.mock("../../utils/notifications/slack", () => ({
  __esModule: true,
  default: {
    isEnabled: jest.fn(),
    isAutoCodingEnabled: jest.fn(),
    postAutoCodingPrompt: jest.fn(),
    notifyUsersToRepost: jest.fn(),
  },
}));

describe("Auto Code Prompt Endpoints", () => {
  let app: express.Application;
  const apiRouter = express.Router();

  const mockAdmin = {
    id: 1,
    role: UserRole.ADMIN,
    username: "admin",
    email: "<EMAIL>",
  };

  const mockManager = {
    id: 2,
    role: UserRole.MANAGER,
    username: "manager",
    email: "<EMAIL>",
  };

  const mockUser = {
    id: 3,
    role: UserRole.DEFAULT,
    username: "user",
    email: "<EMAIL>",
  };

  const mockReport = {
    id: 1,
    title: "Test System Report",
    description: "Test description",
    type: "BUG",
    severity: "HIGH",
    affected_service: "AUTHENTICATION",
    userId: 3,
    resolver_user_id: null,
    createdAt: new Date(),
    users: ["<EMAIL>"],
  };

  beforeEach(() => {
    jest.clearAllMocks();

    app = express();
    app.use(express.json());

    // Mock middleware to pass through
    (validatedRequest as jest.Mock).mockImplementation(
      (req: any, res: any, next: any) => {
        // Set default user for tests
        res.locals = res.locals || {};
        res.locals.user = mockAdmin; // Default to admin user
        next();
      }
    );

    app.use("/api", apiRouter);

    // Setup endpoints
    setupAutoCodePromptEndpoints(apiRouter);

    // Default mocks
    (SystemReport.get as jest.Mock).mockResolvedValue(mockReport);
    (autoCodingPromptGenerator.generatePrompt as jest.Mock).mockResolvedValue(
      "Generated auto-code prompt"
    );
    (slackNotifier.isEnabled as jest.Mock).mockReturnValue(true);
    (slackNotifier.isAutoCodingEnabled as jest.Mock).mockResolvedValue(true);
    (slackNotifier.postAutoCodingPrompt as jest.Mock).mockResolvedValue({
      success: true,
    });
    (slackNotifier.notifyUsersToRepost as jest.Mock).mockResolvedValue({
      success: true,
    });
  });

  describe("POST /api/system-report/:id/generate-auto-code-prompt", () => {
    it("should generate auto-code prompt successfully for admin", async () => {
      const response = await request(app)
        .post("/api/system-report/1/generate-auto-code-prompt")
        .send({ postToSlack: true });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(SystemReport.get).toHaveBeenCalledWith(1);
      expect(autoCodingPromptGenerator.generatePrompt).toHaveBeenCalledWith({
        ...mockReport,
        affected_service: "AUTHENTICATION",
        severity: "HIGH",
      });
    });

    it("should handle report not found", async () => {
      (SystemReport.get as jest.Mock).mockResolvedValue(null);

      const response = await request(app)
        .post("/api/system-report/999/generate-auto-code-prompt")
        .send({});

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe("System report not found");
    });

    it("should validate report ID parameter", async () => {
      const response = await request(app)
        .post("/api/system-report/invalid/generate-auto-code-prompt")
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe("Invalid system report ID");
    });

    it("should require authentication", async () => {
      // Mock validatedRequest to not set a user for this test
      (validatedRequest as jest.Mock).mockImplementationOnce(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          // Don't set user to simulate unauthenticated request
          next();
        }
      );

      const response = await request(app)
        .post("/api/system-report/1/generate-auto-code-prompt")
        .send({});

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe("Authentication required");
    });

    it("should enforce permissions for report access", async () => {
      // Change user to mockUser for this test
      (validatedRequest as jest.Mock).mockImplementationOnce(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          res.locals.user = mockUser;
          next();
        }
      );

      const differentUserReport = {
        ...mockReport,
        userId: 999,
        resolver_user_id: 888,
      };
      (SystemReport.get as jest.Mock).mockResolvedValue(differentUserReport);

      const response = await request(app)
        .post("/api/system-report/1/generate-auto-code-prompt")
        .send({});

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe(
        "Insufficient permissions to generate auto-code prompt for this report"
      );
    });

    it("should allow report owner to generate prompt", async () => {
      // Change user to mockUser for this test
      (validatedRequest as jest.Mock).mockImplementationOnce(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          res.locals.user = mockUser;
          next();
        }
      );

      const userOwnedReport = {
        ...mockReport,
        userId: 3, // mockUser.id
        resolver_user_id: null,
      };
      (SystemReport.get as jest.Mock).mockResolvedValue(userOwnedReport);

      const response = await request(app)
        .post("/api/system-report/1/generate-auto-code-prompt")
        .send({});

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.prompt).toBe("Generated auto-code prompt");
    });

    it("should allow resolver to generate prompt", async () => {
      // Change user to mockUser for this test
      (validatedRequest as jest.Mock).mockImplementationOnce(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          res.locals.user = mockUser;
          next();
        }
      );

      const resolvedReport = {
        ...mockReport,
        userId: 999,
        resolver_user_id: 3, // mockUser.id
      };
      (SystemReport.get as jest.Mock).mockResolvedValue(resolvedReport);

      const response = await request(app)
        .post("/api/system-report/1/generate-auto-code-prompt")
        .send({});

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it("should handle prompt generation failure", async () => {
      (autoCodingPromptGenerator.generatePrompt as jest.Mock).mockResolvedValue(
        null
      );

      const response = await request(app)
        .post("/api/system-report/1/generate-auto-code-prompt")
        .send({});

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe("Failed to generate auto-coding prompt");
    });

    it("should handle Slack posting with user notifications", async () => {
      const response = await request(app)
        .post("/api/system-report/1/generate-auto-code-prompt")
        .send({
          postToSlack: true,
          notifyUsersEnabled: true,
          notifyUsers: [1, 2, 3],
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(slackNotifier.postAutoCodingPrompt).toHaveBeenCalled();
      expect(slackNotifier.notifyUsersToRepost).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 1,
          title: "Test System Report",
        }),
        "Generated auto-code prompt",
        ["1", "2", "3"]
      );
    });

    it("should skip Slack posting when postToSlack is false", async () => {
      const response = await request(app)
        .post("/api/system-report/1/generate-auto-code-prompt")
        .send({ postToSlack: false });

      expect(response.status).toBe(200);
      expect(response.body.slack.autoCoding.skipped).toBe(true);
      expect(slackNotifier.postAutoCodingPrompt).not.toHaveBeenCalled();
    });

    it("should handle Slack posting errors gracefully", async () => {
      (slackNotifier.postAutoCodingPrompt as jest.Mock).mockRejectedValue(
        new Error("Slack API error")
      );

      const response = await request(app)
        .post("/api/system-report/1/generate-auto-code-prompt")
        .send({ postToSlack: true });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.slack.autoCoding.success).toBe(false);
      expect(response.body.slack.autoCoding.error).toBe("Slack API error");
    });

    it("should handle user notification errors gracefully", async () => {
      (slackNotifier.notifyUsersToRepost as jest.Mock).mockRejectedValue(
        new Error("User notification failed")
      );

      const response = await request(app)
        .post("/api/system-report/1/generate-auto-code-prompt")
        .send({
          postToSlack: true,
          notifyUsersEnabled: true,
          notifyUsers: [1, 2],
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.userNotifications.success).toBe(false);
      expect(response.body.userNotifications.error).toBe(
        "User notification failed"
      );
    });

    it("should validate notifyUsers parameter", async () => {
      const response = await request(app)
        .post("/api/system-report/1/generate-auto-code-prompt")
        .send({ notifyUsers: "invalid" });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe(
        "notifyUsers must be an array of user IDs"
      );
    });

    it("should handle internal server errors", async () => {
      (SystemReport.get as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      const response = await request(app)
        .post("/api/system-report/1/generate-auto-code-prompt")
        .send({});

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe(
        "Internal server error while generating auto-code prompt"
      );
    });
  });

  describe("POST /api/system-reports/generate-auto-code-prompts", () => {
    it("should batch generate prompts successfully", async () => {
      (SystemReport.get as jest.Mock)
        .mockResolvedValueOnce({ ...mockReport, id: 1 })
        .mockResolvedValueOnce({ ...mockReport, id: 2 });

      const response = await request(app)
        .post("/api/system-reports/generate-auto-code-prompts")
        .send({
          reportIds: [1, 2],
          postToSlack: true,
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.processed).toBe(2);
      expect(response.body.errorCount).toBe(0);
      expect(response.body.results).toHaveLength(2);
    });

    it("should validate reportIds parameter", async () => {
      const response = await request(app)
        .post("/api/system-reports/generate-auto-code-prompts")
        .send({ reportIds: "invalid" });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe("reportIds must be a non-empty array");
    });

    it("should enforce maximum batch size", async () => {
      const reportIds = Array.from({ length: 11 }, (_, i) => i + 1);
      const response = await request(app)
        .post("/api/system-reports/generate-auto-code-prompts")
        .send({ reportIds });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe(
        "Maximum 10 reports can be processed at once"
      );
    });

    it("should handle mixed success and failure results", async () => {
      (SystemReport.get as jest.Mock)
        .mockResolvedValueOnce({ ...mockReport, id: 1 })
        .mockResolvedValueOnce(null); // Report not found

      const response = await request(app)
        .post("/api/system-reports/generate-auto-code-prompts")
        .send({ reportIds: [1, 999] });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.processed).toBe(1);
      expect(response.body.errorCount).toBe(1);
      expect(response.body.errors).toHaveLength(1);
      expect(response.body.errors[0]).toEqual({
        reportId: 999,
        error: "Report not found",
      });
    });

    it("should handle permission errors in batch", async () => {
      // Change user to mockUser for this test
      (validatedRequest as jest.Mock).mockImplementationOnce(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          res.locals.user = mockUser;
          next();
        }
      );

      const restrictedReport = {
        ...mockReport,
        id: 1,
        userId: 999,
        resolver_user_id: 888,
      };
      (SystemReport.get as jest.Mock).mockResolvedValue(restrictedReport);

      const response = await request(app)
        .post("/api/system-reports/generate-auto-code-prompts")
        .send({ reportIds: [1] });

      expect(response.status).toBe(200);
      expect(response.body.processed).toBe(0);
      expect(response.body.errorCount).toBe(1);
      expect(response.body.errors[0].error).toBe("Insufficient permissions");
    });

    it("should handle invalid report IDs in batch", async () => {
      const response = await request(app)
        .post("/api/system-reports/generate-auto-code-prompts")
        .send({ reportIds: ["invalid", "abc"] });

      expect(response.status).toBe(200);
      expect(response.body.processed).toBe(0);
      expect(response.body.errorCount).toBe(2);
      expect(response.body.errors).toEqual([
        { reportId: "invalid", error: "Invalid report ID" },
        { reportId: "abc", error: "Invalid report ID" },
      ]);
    });

    it("should handle prompt generation failures in batch", async () => {
      (autoCodingPromptGenerator.generatePrompt as jest.Mock).mockResolvedValue(
        null
      );

      const response = await request(app)
        .post("/api/system-reports/generate-auto-code-prompts")
        .send({ reportIds: [1] });

      expect(response.status).toBe(200);
      expect(response.body.processed).toBe(0);
      expect(response.body.errorCount).toBe(1);
      expect(response.body.errors[0].error).toBe("Failed to generate prompt");
    });
  });

  describe("GET /api/auto-code-prompts/settings", () => {
    it("should return settings for admin users", async () => {
      const response = await request(app).get(
        "/api/auto-code-prompts/settings"
      );

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.settings).toEqual({
        slackIntegrationEnabled: true,
        slackAutoCodingEnabled: true,
        supportedServices: [
          "AUTHENTICATION",
          "DOCUMENT_MANAGEMENT",
          "CHAT_SYSTEM",
          "SEARCH",
          "ADMIN",
          "UI_UX",
          "OTHER",
        ],
        defaultRepository: "RahSwe/ISTLegal",
        defaultBranch: "develop",
      });
    });

    it("should return settings for manager users", async () => {
      // Change user to mockManager for this test
      (validatedRequest as jest.Mock).mockImplementationOnce(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          res.locals.user = mockManager;
          next();
        }
      );

      const response = await request(app).get(
        "/api/auto-code-prompts/settings"
      );

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it("should deny access to regular users", async () => {
      // Change user to mockUser for this test
      (validatedRequest as jest.Mock).mockImplementationOnce(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          res.locals.user = mockUser;
          next();
        }
      );

      const response = await request(app).get(
        "/api/auto-code-prompts/settings"
      );

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe(
        "Insufficient permissions to view auto-coding settings"
      );
    });

    it("should require authentication", async () => {
      // Mock validatedRequest to not set a user for this test
      (validatedRequest as jest.Mock).mockImplementationOnce(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          // Don't set user to simulate unauthenticated request
          next();
        }
      );

      const response = await request(app).get(
        "/api/auto-code-prompts/settings"
      );

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe("Authentication required");
    });

    it("should use environment variables for repository settings", async () => {
      const originalRepo = process.env.CURSOR_DEFAULT_REPO;
      const originalBranch = process.env.CURSOR_DEFAULT_BRANCH;

      process.env.CURSOR_DEFAULT_REPO = "custom/repo";
      process.env.CURSOR_DEFAULT_BRANCH = "main";

      const response = await request(app).get(
        "/api/auto-code-prompts/settings"
      );

      expect(response.body.settings.defaultRepository).toBe("custom/repo");
      expect(response.body.settings.defaultBranch).toBe("main");

      // Restore original values
      if (originalRepo !== undefined) {
        process.env.CURSOR_DEFAULT_REPO = originalRepo;
      } else {
        delete process.env.CURSOR_DEFAULT_REPO;
      }
      if (originalBranch !== undefined) {
        process.env.CURSOR_DEFAULT_BRANCH = originalBranch;
      } else {
        delete process.env.CURSOR_DEFAULT_BRANCH;
      }
    });

    it("should handle Slack service errors gracefully", async () => {
      (slackNotifier.isAutoCodingEnabled as jest.Mock).mockRejectedValue(
        new Error("Slack service error")
      );

      const response = await request(app).get(
        "/api/auto-code-prompts/settings"
      );

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
    });
  });

  describe("POST /api/system-report/:id/preview-auto-code-prompt", () => {
    it("should generate preview successfully", async () => {
      const response = await request(app).post(
        "/api/system-report/1/preview-auto-code-prompt"
      );

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.preview.prompt).toBe("Generated auto-code prompt");
      expect(response.body.preview.reportId).toBe(1);
      expect(response.body.preview.reportTitle).toBe("Test System Report");
      expect(response.body.preview.documentationSummary).toEqual({
        cursorRules: 0,
        backendDocs: 0,
        frontendDocs: 0,
        relevantFiles: 0,
      });
    });

    it("should validate report ID for preview", async () => {
      const response = await request(app).post(
        "/api/system-report/invalid/preview-auto-code-prompt"
      );

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe("Invalid system report ID");
    });

    it("should handle report not found for preview", async () => {
      (SystemReport.get as jest.Mock).mockResolvedValue(null);

      const response = await request(app).post(
        "/api/system-report/999/preview-auto-code-prompt"
      );

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe("System report not found");
    });

    it("should require authentication for preview", async () => {
      // Mock validatedRequest to not set a user for this test
      (validatedRequest as jest.Mock).mockImplementationOnce(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          // Don't set user to simulate unauthenticated request
          next();
        }
      );

      const response = await request(app).post(
        "/api/system-report/1/preview-auto-code-prompt"
      );

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe("Authentication required");
    });

    it("should handle preview generation errors", async () => {
      (autoCodingPromptGenerator.generatePrompt as jest.Mock).mockRejectedValue(
        new Error("Preview generation failed")
      );

      const response = await request(app).post(
        "/api/system-report/1/preview-auto-code-prompt"
      );

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe(
        "Internal server error while previewing auto-code prompt"
      );
    });
  });

  describe("Edge cases and error handling", () => {
    it("should handle concurrent requests", async () => {
      const requests = Array.from({ length: 3 }, () =>
        request(app)
          .post("/api/system-report/1/generate-auto-code-prompt")
          .send({})
      );

      const responses = await Promise.all(requests);

      responses.forEach((response) => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });
    });

    it("should handle very large notifyUsers arrays", async () => {
      const largeNotifyUsers = Array.from({ length: 100 }, (_, i) => i + 1);
      const response = await request(app)
        .post("/api/system-report/1/generate-auto-code-prompt")
        .send({
          notifyUsers: largeNotifyUsers,
          notifyUsersEnabled: true,
        });

      expect(response.status).toBe(200);
      expect(slackNotifier.notifyUsersToRepost).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(String),
        largeNotifyUsers.map(String)
      );
    });

    it("should handle missing optional fields in report", async () => {
      const incompleteReport = {
        ...mockReport,
        type: null,
        severity: null,
        affected_service: null,
      };
      (SystemReport.get as jest.Mock).mockResolvedValue(incompleteReport);

      const response = await request(app)
        .post("/api/system-report/1/generate-auto-code-prompt")
        .send({});

      expect(response.status).toBe(200);
      expect(autoCodingPromptGenerator.generatePrompt).toHaveBeenCalledWith({
        ...incompleteReport,
        affected_service: "OTHER",
        severity: undefined,
      });
    });

    it("should handle null and undefined request body fields", async () => {
      const response = await request(app)
        .post("/api/system-report/1/generate-auto-code-prompt")
        .send({
          postToSlack: null,
          notifyUsers: undefined,
          notifyUsersEnabled: null,
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it("should handle empty batch request", async () => {
      const response = await request(app)
        .post("/api/system-reports/generate-auto-code-prompts")
        .send({ reportIds: [] });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe("reportIds must be a non-empty array");
    });

    it("should handle malformed JSON in request body", async () => {
      const response = await request(app)
        .post("/api/system-report/1/generate-auto-code-prompt")
        .type("json")
        .send('{"invalid": json}');

      // Express will handle malformed JSON with 400 status
      expect(response.status).toBe(400);
    });
  });

  describe("setupAutoCodePromptEndpoints", () => {
    it("should handle null router gracefully", () => {
      expect(() => {
        setupAutoCodePromptEndpoints(null as any);
      }).not.toThrow();
    });

    it("should register all required endpoints", () => {
      const mockRouter = {
        post: jest.fn(),
        get: jest.fn(),
      };

      setupAutoCodePromptEndpoints(mockRouter as any);

      expect(mockRouter.post).toHaveBeenCalledTimes(3);
      expect(mockRouter.get).toHaveBeenCalledTimes(1);

      // Verify endpoint paths
      const postCalls = mockRouter.post.mock.calls.map((call) => call[0]);
      const getCalls = mockRouter.get.mock.calls.map((call) => call[0]);

      expect(postCalls).toContain(
        "/system-report/:id/generate-auto-code-prompt"
      );
      expect(postCalls).toContain("/system-reports/generate-auto-code-prompts");
      expect(postCalls).toContain(
        "/system-report/:id/preview-auto-code-prompt"
      );
      expect(getCalls).toContain("/auto-code-prompts/settings");
    });
  });
});
