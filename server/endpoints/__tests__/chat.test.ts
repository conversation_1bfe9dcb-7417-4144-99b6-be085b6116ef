// IMPORTANT: Set test environment and mock dependencies BEFORE any imports
process.env.NODE_ENV = "test";
process.env.DATABASE_URL = "file:./test.db";

// Mock UUID for consistent testing
jest.mock("uuid", () => ({
  v4: jest.fn(() => "test-uuid-1234"),
}));

// Mock Prisma client first to prevent any database connections
jest.mock("../../utils/prisma", () => ({
  __esModule: true,
  default: {
    workspaceChats: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      count: jest.fn(),
    },
    workspaces: {
      findFirst: jest.fn(),
      findUnique: jest.fn(),
    },
    users: {
      findFirst: jest.fn(),
      findUnique: jest.fn(),
    },
    systemSettings: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
    },
    $disconnect: jest.fn(),
  },
}));

// Mock system dependencies
const mockDocxGenerate = jest.fn();
const mockDocxOn = jest.fn();
const mockDocx = {
  setDocSubject: jest.fn(),
  setDocKeywords: jest.fn(),
  setDescription: jest.fn(),
  createP: jest.fn(() => ({
    addText: jest.fn(),
    addLineBreak: jest.fn(),
  })),
  generate: mockDocxGenerate,
  on: mockDocxOn,
  _errorHandler: null,
};

jest.mock("officegen", () => {
  return jest.fn(() => mockDocx);
});

const mockWriteStreamOn = jest.fn();
const mockCreateWriteStream = jest.fn();
const mockWriteStream = {
  on: mockWriteStreamOn,
  write: jest.fn(),
  end: jest.fn(),
  emit: jest.fn(),
  _errorHandler: null,
  _path: "",
};

jest.mock("fs", () => {
  const originalFs = jest.requireActual("fs");
  return {
    ...originalFs,
    createWriteStream: mockCreateWriteStream,
    unlink: jest.fn((path, callback) => callback && callback()),
    existsSync: jest.fn(() => true),
    mkdirSync: jest.fn(),
    writeFileSync: jest.fn(),
    readFileSync: jest.fn(() => "mock file content"),
    statSync: jest.fn(() => ({ size: 1024, isFile: () => true })),
    accessSync: jest.fn(),
  };
});

jest.mock("os", () => ({
  tmpdir: jest.fn(() => "/tmp"),
}));

jest.mock("path", () => {
  const originalPath = jest.requireActual("path");
  return {
    ...originalPath,
    join: jest.fn((...args) => args.join("/")),
  };
});

// Mock models
jest.mock("../../models/workspaceChats", () => ({
  WorkspaceChats: {
    get: jest.fn(async ({ id }: { id: number }) => {
      if (id === 123 || id === 999 || id === 998) {
        return {
          id: id,
          prompt: "Test prompt",
          response: JSON.stringify({ text: "Test response" }),
          createdAt: new Date(),
          workspaceId: 1,
          user_id: 1,
        };
      }
      return null;
    }),
    count: jest.fn(async () => 0),
  },
}));

jest.mock("../../models/systemSettings", () => ({
  default: {
    get: jest.fn(async ({ label }: { label: string }) => {
      const mockSettings: Record<string, { value: string }> = {
        limit_user_messages: { value: "false" },
        message_limit: { value: "25" },
      };
      return mockSettings[label] || null;
    }),
  },
}));

jest.mock("../../models/telemetry", () => ({
  Telemetry: {
    sendTelemetry: jest.fn().mockResolvedValue(true),
  },
}));

jest.mock("../../models/eventLogs", () => ({
  EventLogs: {
    logEvent: jest.fn().mockResolvedValue(true),
  },
}));

// Mock chat streaming functionality
jest.mock("../../utils/chats/stream", () => ({
  streamChatWithWorkspace: jest.fn(async (req, res, _workspace, _message) => {
    // Simulate streaming response
    res.write(
      `data: ${JSON.stringify({
        uuid: "test-uuid",
        type: "textResponseChunk",
        textResponse: "Test streaming response",
        sources: [],
        close: false,
      })}\n\n`
    );

    res.write(
      `data: ${JSON.stringify({
        uuid: "test-uuid",
        type: "textResponseChunk",
        textResponse: null,
        sources: [],
        close: true,
      })}\n\n`
    );
  }),
}));

// Mock chat utilities
jest.mock("../../utils/helpers/chat/responses", () => ({
  writeResponseChunk: jest.fn((response, chunk) => {
    response.write(`data: ${JSON.stringify(chunk)}\n\n`);
  }),
}));

jest.mock("../../utils/helpers/chat/logs", () => ({
  readChatLog: jest.fn(async (chatId: number) => {
    if (chatId === 123) {
      return [
        { id: 1, role: "user", content: "Hello" },
        { id: 2, role: "assistant", content: "Hi there!" },
      ];
    }
    // Return empty array for other IDs (tests will override this)
    return [];
  }),
}));

// Mock thread utilities
jest.mock("../../utils/helpers/thread/textProcessing", () => ({
  extractFirstSentence: jest.fn((text) => text.split(".")[0] + "."),
}));

const mockCountFromString = jest.fn(() => 150);
jest.mock("../../utils/helpers/tiktoken", () => ({
  TokenManager: jest.fn().mockImplementation(() => ({
    countFromString: mockCountFromString,
  })),
}));

jest.mock("../../utils/helpers/validation", () => ({
  sanitizeCssColor: jest.fn((color) => {
    const validColors = ["red", "blue", "green", "yellow"];
    return validColors.includes(color) ? color : null;
  }),
}));

// Mock workspace thread model
jest.mock("../../models/workspaceThread", () => ({
  WorkspaceThread: {
    defaultName: "New Conversation",
    update: jest.fn(async (thread, updates) => ({
      thread: { ...thread, ...updates },
    })),
  },
}));

// Mock middleware and utilities
jest.mock("../../utils/http", () => ({
  userFromSession: jest.fn(async (req, _res) => {
    const authHeader = req.headers.authorization;
    if (!authHeader) return null;

    const token = authHeader.replace("Bearer ", "");
    if (token === "admin-token") {
      return {
        id: 1,
        username: "admin",
        role: "admin",
        email: "<EMAIL>",
      };
    } else if (token === "user-token") {
      return {
        id: 2,
        username: "user",
        role: "default",
        email: "<EMAIL>",
      };
    }
    return null;
  }),
  reqBody: jest.fn((req) => req.body),
  multiUserMode: jest.fn((res) => res.locals?.multiUserMode || false),
}));

jest.mock("../../utils/middleware/validatedRequest", () => ({
  validatedRequest: jest.fn((req: any, res: any, next: any) => {
    // Mock authentication check
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: "Unauthorized" });
    }
    next();
  }),
}));

jest.mock("../../utils/middleware/multiUserProtected", () => ({
  flexUserRoleValid: jest.fn(() => (req: any, res: any, next: any) => {
    // Check authentication for role validation
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: "Unauthorized" });
    }
    next();
  }),
  strictMultiUserRoleValid: jest.fn(() => (req: any, res: any, next: any) => {
    // Check authentication for strict role validation
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: "Unauthorized" });
    }
    next();
  }),
  legalTemplateScopeGuard: jest.fn(
    () => (req: any, res: any, next: any) => next()
  ),
  canManageSystemTemplates: jest.fn(() => true),
  canManageOrgTemplates: jest.fn(() => true),
  canManageUserTemplates: jest.fn(() => true),
  ROLES: {
    admin: "admin",
    manager: "manager",
    default: "default",
    all: "<all>",
  },
}));

jest.mock("../../utils/middleware/validWorkspace", () => ({
  validWorkspaceSlug: jest.fn((req: any, res: any, next: any) => {
    // Check authentication first
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: "Unauthorized" });
    }

    res.locals = res.locals || {};
    res.locals.workspace = {
      id: 1,
      name: "Test Workspace",
      slug: req.params.slug || "test-workspace",
      chatMode: "chat",
      vectorSearchMode: "similarity",
      user_id: 1,
    };
    next();
  }),
  validWorkspaceAndThreadSlug: jest.fn((req: any, res: any, next: any) => {
    // Check authentication first
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: "Unauthorized" });
    }

    res.locals = res.locals || {};
    res.locals.workspace = {
      id: 1,
      name: "Test Workspace",
      slug: req.params.slug || "test-workspace",
      chatMode: "chat",
      vectorSearchMode: "similarity",
      user_id: 1,
    };
    res.locals.thread = {
      id: 1,
      name: "New Conversation",
      slug: req.params.threadSlug || "test-thread",
      workspace_id: 1,
      user_id: 1,
    };
    next();
  }),
}));

// Mock i18n
jest.mock("../../utils/i18n", () => ({
  t: jest.fn(async (key) => {
    const translations: Record<string, string> = {
      "docx.errors.contentRequired": "Content is required",
      "docx.title": "Chat Export",
      "docx.exportedOn": "Exported on",
      "docx.keywords": "chat, export, legal",
      "docx.description": "Exported chat conversation",
      "docx.errors.writingFile": "Error writing file",
      "docx.errors.generating": "Error generating document",
      "docx.tokenCount": "Token count",
    };
    return translations[key] || key;
  }),
}));

import request from "supertest";
import { Request, Response } from "express";
import { describe, expect, it, beforeEach, afterEach } from "@jest/globals";
import { useTimerCleanup } from "../../tests/helpers/timerCleanup";
import { setupDeepMockIsolation } from "../../tests/helpers/mockIsolation";

// Mock Express app methods before importing
const originalResponsePrototype = jest.requireActual("express").response;

// Mock the response.download method at the Express prototype level
beforeAll(() => {
  originalResponsePrototype.download = function (
    filePath: string,
    fileName: string,
    callback?: Function
  ) {
    // Check if headers have already been sent to avoid errors
    if (this.headersSent) {
      if (callback) callback();
      return this;
    }

    // Simulate a successful download
    this.set("Content-Disposition", `attachment; filename="${fileName}"`);
    this.set(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    );
    this.status(200);
    this.end("mock-docx-content");

    // Call callback to simulate successful completion
    if (callback) {
      setImmediate(() => callback());
    }
    return this;
  };
});

import app from "../../index";

/**
 * Chat Endpoints Test Suite
 *
 * Tests the core chat functionality including:
 * - Streaming chat endpoints
 * - Chat log retrieval
 * - DOCX export functionality
 * - Authentication and authorization
 * - Error handling and edge cases
 */

describe("Chat Endpoints", () => {
  // Setup automatic timer cleanup and deep mock isolation
  useTimerCleanup();
  setupDeepMockIsolation();

  beforeEach(() => {
    jest.clearAllMocks();

    // Reset token count mock
    mockCountFromString.mockReturnValue(150);

    // Reset file system mock to default behavior
    mockCreateWriteStream.mockImplementation((path) => {
      mockWriteStream._path = path;
      return mockWriteStream;
    });

    mockWriteStreamOn.mockImplementation((event, handler) => {
      if (event === "close") {
        // Default: simulate successful file write completion
        setTimeout(handler, 10);
      }
      if (event === "error") {
        // Store error handler for testing
        mockWriteStream._errorHandler = handler;
      }
    });

    // Reset officegen mock to default behavior
    mockDocxGenerate.mockImplementation((stream) => {
      // Default: simulate successful document generation
      setTimeout(() => {
        if (stream && stream.on) {
          const closeHandler = stream.on.mock.calls.find(
            (call: any) => call[0] === "close"
          )?.[1];
          if (closeHandler) {
            closeHandler();
          }
        }
      }, 15);
    });

    mockDocxOn.mockImplementation((event, handler) => {
      if (event === "error") {
        // Store error handler for testing
        mockDocx._errorHandler = handler;
      }
    });

    // Set up environment variables
    process.env.LLM_PROVIDER = "openai";
    process.env.EMBEDDING_ENGINE = "openai";
    process.env.VECTOR_DB = "lancedb";
    process.env.TTS_PROVIDER = "native";
  });

  afterEach(() => {
    jest.clearAllMocks();

    // Reset all middleware mocks to their original state
    const validWorkspaceSlug =
      require("../../utils/middleware/validWorkspace").validWorkspaceSlug;
    const validWorkspaceAndThreadSlug =
      require("../../utils/middleware/validWorkspace").validWorkspaceAndThreadSlug;

    // Reset validWorkspaceSlug
    validWorkspaceSlug.mockImplementation((req: any, res: any, next: any) => {
      const authHeader = req.headers.authorization;
      if (!authHeader) {
        return res.status(401).json({ error: "Unauthorized" });
      }

      res.locals = res.locals || {};
      res.locals.workspace = {
        id: 1,
        name: "Test Workspace",
        slug: req.params.slug || "test-workspace",
        chatMode: "chat",
        vectorSearchMode: "similarity",
        user_id: 1,
      };
      next();
    });

    // Reset validWorkspaceAndThreadSlug
    validWorkspaceAndThreadSlug.mockImplementation(
      (req: any, res: any, next: any) => {
        const authHeader = req.headers.authorization;
        if (!authHeader) {
          return res.status(401).json({ error: "Unauthorized" });
        }

        res.locals = res.locals || {};
        res.locals.workspace = {
          id: 1,
          name: "Test Workspace",
          slug: req.params.slug || "test-workspace",
          chatMode: "chat",
          vectorSearchMode: "similarity",
          user_id: 1,
        };
        res.locals.thread = {
          id: 1,
          name: "New Conversation",
          slug: req.params.threadSlug || "test-thread",
          workspace_id: 1,
          user_id: 1,
        };
        next();
      }
    );
  });

  describe("POST /api/workspace/:slug/stream-chat/:slugModule", () => {
    const validChatPayload = {
      message: "Hello, how can you help me?",
      attachments: [],
      chatId: null,
      isCanvasChat: false,
      preventChatCreation: false,
      llmSelected: 0,
      invoice_ref: null,
      hasUploadedFile: false,
      docxContent: null,
      displayMessage: null,
      useDeepSearch: false,
      cdbOptions: [],
      legalTaskConfig: {},
      settings_suffix: "",
      styleAlignment: null,
    };

    it("should stream chat response for authenticated user", async () => {
      const response = await request(app)
        .post("/api/workspace/test-workspace/stream-chat/default")
        .set("Authorization", "Bearer user-token")
        .send(validChatPayload);

      expect(response.status).toBe(200);
      expect(response.headers["content-type"]).toContain("text/event-stream");
    });

    it("should use document-drafting LLM settings when slugModule is document-drafting", async () => {
      process.env.LLM_PROVIDER_DD = "anthropic";
      process.env.EMBEDDING_ENGINE_DD = "openai";
      process.env.VECTOR_DB_DD = "pinecone";

      const response = await request(app)
        .post("/api/workspace/test-workspace/stream-chat/document-drafting")
        .set("Authorization", "Bearer user-token")
        .send(validChatPayload);

      expect(response.status).toBe(200);
    });

    it("should handle binary LLM selection for document-drafting", async () => {
      process.env.BINARY_LLM_DD = "on";
      process.env.LLM_PROVIDER_DD_2 = "claude";

      const payload = { ...validChatPayload, llmSelected: 1 };

      const response = await request(app)
        .post("/api/workspace/test-workspace/stream-chat/document-drafting")
        .set("Authorization", "Bearer user-token")
        .send(payload);

      expect(response.status).toBe(200);
    });

    it("should reject empty message", async () => {
      const payload = { ...validChatPayload, message: "" };

      const response = await request(app)
        .post("/api/workspace/test-workspace/stream-chat/default")
        .set("Authorization", "Bearer user-token")
        .send(payload);

      expect(response.status).toBe(400);
      expect(response.body.error).toBe("Message is empty.");
    });

    it("should handle message rate limiting for non-admin users", async () => {
      // Mock system settings to enable rate limiting
      const SystemSettings = require("../../models/systemSettings").default;
      SystemSettings.get.mockImplementation(
        async ({ label }: { label: string }) => {
          if (label === "limit_user_messages") return { value: "true" };
          if (label === "message_limit") return { value: "5" };
          return null;
        }
      );

      // Mock chat count to exceed limit
      const { WorkspaceChats } = require("../../models/workspaceChats");
      WorkspaceChats.count.mockResolvedValue(10);

      const response = await request(app)
        .post("/api/workspace/test-workspace/stream-chat/default")
        .set("Authorization", "Bearer user-token")
        .send(validChatPayload);

      expect(response.status).toBe(200);
      expect(response.headers["content-type"]).toContain("text/event-stream");
    });

    it("should allow admin users to bypass rate limits", async () => {
      // Mock system settings to enable rate limiting
      const SystemSettings = require("../../models/systemSettings").default;
      SystemSettings.get.mockImplementation(
        async ({ label }: { label: string }) => {
          if (label === "limit_user_messages") return { value: "true" };
          if (label === "message_limit") return { value: "5" };
          return null;
        }
      );

      // Mock chat count to exceed limit
      const { WorkspaceChats } = require("../../models/workspaceChats");
      WorkspaceChats.count.mockResolvedValue(10);

      const response = await request(app)
        .post("/api/workspace/test-workspace/stream-chat/default")
        .set("Authorization", "Bearer admin-token")
        .send(validChatPayload);

      expect(response.status).toBe(200);
    });

    it("should handle attachments", async () => {
      const payloadWithAttachments = {
        ...validChatPayload,
        attachments: [
          { id: "1", name: "test.pdf", type: "pdf" },
          { name: "document.docx", type: "docx" },
        ],
      };

      const response = await request(app)
        .post("/api/workspace/test-workspace/stream-chat/default")
        .set("Authorization", "Bearer user-token")
        .send(payloadWithAttachments);

      expect(response.status).toBe(200);
    });

    it("should parse styleAlignment from string JSON", async () => {
      const payloadWithStyle = {
        ...validChatPayload,
        styleAlignment: '{"align": "center", "color": "blue"}',
      };

      const response = await request(app)
        .post("/api/workspace/test-workspace/stream-chat/default")
        .set("Authorization", "Bearer user-token")
        .send(payloadWithStyle);

      expect(response.status).toBe(200);
    });

    it("should handle malformed styleAlignment JSON gracefully", async () => {
      const payloadWithBadStyle = {
        ...validChatPayload,
        styleAlignment: '{"invalid": json}',
      };

      const response = await request(app)
        .post("/api/workspace/test-workspace/stream-chat/default")
        .set("Authorization", "Bearer user-token")
        .send(payloadWithBadStyle);

      expect(response.status).toBe(200);
    });

    it("should require authentication", async () => {
      const response = await request(app)
        .post("/api/workspace/test-workspace/stream-chat/default")
        .send(validChatPayload);

      expect(response.status).toBe(401);
    });

    it("should handle server errors gracefully", async () => {
      // Mock streamChatWithWorkspace to throw an error
      const { streamChatWithWorkspace } = require("../../utils/chats/stream");
      streamChatWithWorkspace.mockRejectedValue(new Error("Server error"));

      const response = await request(app)
        .post("/api/workspace/test-workspace/stream-chat/default")
        .set("Authorization", "Bearer user-token")
        .send(validChatPayload);

      expect(response.status).toBe(200);
      expect(response.headers["content-type"]).toContain("text/event-stream");
    });
  });

  describe("POST /api/workspace/:slug/thread/:threadSlug/stream-chat/:slugModule", () => {
    const validChatPayload = {
      message: "Hello in thread",
      attachments: [],
      chatId: null,
      isCanvasChat: false,
      preventChatCreation: false,
      llmSelected: 0,
      invoice_ref: null,
      hasUploadedFile: false,
      docxContent: null,
      displayMessage: null,
      useDeepSearch: false,
      cdbOptions: [],
      legalTaskConfig: {},
      settings_suffix: "",
      styleAlignment: null,
    };

    it("should stream chat response in thread context", async () => {
      const response = await request(app)
        .post(
          "/api/workspace/test-workspace/thread/test-thread/stream-chat/default"
        )
        .set("Authorization", "Bearer user-token")
        .send(validChatPayload);

      expect(response.status).toBe(200);
      expect(response.headers["content-type"]).toContain("text/event-stream");
    });

    it("should rename thread on first message", async () => {
      // Mock thread with default name
      const validWorkspaceAndThreadSlug =
        require("../../utils/middleware/validWorkspace").validWorkspaceAndThreadSlug;
      validWorkspaceAndThreadSlug.mockImplementation(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          res.locals.workspace = {
            id: 1,
            name: "Test Workspace",
            slug: "test-workspace",
            chatMode: "chat",
            vectorSearchMode: "similarity",
            user_id: 1,
          };
          res.locals.thread = {
            id: 1,
            name: "New Conversation", // Default name
            slug: "test-thread",
            workspace_id: 1,
            user_id: 1,
          };
          next();
        }
      );

      const response = await request(app)
        .post(
          "/api/workspace/test-workspace/thread/test-thread/stream-chat/default"
        )
        .set("Authorization", "Bearer user-token")
        .send({
          ...validChatPayload,
          message: "This is my first message in the thread.",
        });

      expect(response.status).toBe(200);
    });

    it("should handle thread rename errors gracefully", async () => {
      // Mock WorkspaceThread.update to throw an error
      const { WorkspaceThread } = require("../../models/workspaceThread");
      WorkspaceThread.update.mockRejectedValue(new Error("Update failed"));

      const response = await request(app)
        .post(
          "/api/workspace/test-workspace/thread/test-thread/stream-chat/default"
        )
        .set("Authorization", "Bearer user-token")
        .send({
          ...validChatPayload,
          message: "This should cause a rename error.",
        });

      expect(response.status).toBe(200);
    });

    it("should send thread rename event after successful rename", async () => {
      // Mock successful thread update
      const { WorkspaceThread } = require("../../models/workspaceThread");
      WorkspaceThread.update.mockResolvedValue({
        thread: {
          id: 1,
          name: "This is my first message in the thread",
          slug: "test-thread",
          workspace_id: 1,
          user_id: 1,
        },
      });

      const response = await request(app)
        .post(
          "/api/workspace/test-workspace/thread/test-thread/stream-chat/default"
        )
        .set("Authorization", "Bearer user-token")
        .send({
          ...validChatPayload,
          message: "This is my first message in the thread.",
        });

      expect(response.status).toBe(200);
    });

    it("should handle empty message in thread context", async () => {
      const payload = { ...validChatPayload, message: "" };

      const response = await request(app)
        .post(
          "/api/workspace/test-workspace/thread/test-thread/stream-chat/default"
        )
        .set("Authorization", "Bearer user-token")
        .send(payload);

      expect(response.status).toBe(400);
      expect(response.body.error).toBe("Message is empty.");
    });
  });

  describe("GET /api/workspace/chat-log/:chatId", () => {
    it("should retrieve chat log for valid chat ID", async () => {
      const response = await request(app)
        .get("/api/workspace/chat-log/123")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.chatLog).toEqual([
        { id: 1, role: "user", content: "Hello" },
        { id: 2, role: "assistant", content: "Hi there!" },
      ]);
    });

    it("should return 404 for non-existent chat", async () => {
      const response = await request(app)
        .get("/api/workspace/chat-log/456") // Use an ID not in the mock
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(404);
      expect(response.body.message).toBe("Chat not found");
    });

    it("should handle chat log read errors", async () => {
      // This test is verifying that when a chat exists but readChatLog returns empty,
      // the endpoint still returns success with empty chatLog
      const response = await request(app)
        .get("/api/workspace/chat-log/999")
        .set("Authorization", "Bearer user-token");

      // Since the mock returns an empty array for ID 999, expect success with empty log
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.chatLog).toEqual([]);
    });

    it("should handle chat not found in database", async () => {
      // Test when the chat doesn't exist in the database (ID not in mock)
      const response = await request(app)
        .get("/api/workspace/chat-log/997") // ID not in the WorkspaceChats mock
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(404);
      expect(response.body.message).toBe("Chat not found");
    });

    it("should require authentication", async () => {
      const response = await request(app).get("/api/workspace/chat-log/123");

      expect(response.status).toBe(401);
    });
  });

  describe("POST /api/chat/export-docx", () => {
    it("should export chat content to DOCX successfully", async () => {
      const response = await request(app)
        .post("/api/chat/export-docx")
        .set("Authorization", "Bearer user-token")
        .send({
          content:
            "# Test Chat\n\nThis is a test conversation.\n\n**User:** Hello\n**Assistant:** Hi there!",
        });

      expect(response.status).toBe(200);
      expect(response.headers["content-disposition"]).toContain("chat-export-");
    });

    it("should handle markdown formatting in export", async () => {
      const markdownContent = `
# Legal Analysis Report

## Introduction
This document contains *important* legal analysis.

### Key Points
- **Contract terms** are binding
- _Compliance_ is mandatory
- \`Legal code\` references

\`\`\`javascript
function checkCompliance() {
  return true;
}
\`\`\`

<mark style="background-color: yellow">Important highlight</mark>

<span style="color: red">Critical text</span>
      `;

      const response = await request(app)
        .post("/api/chat/export-docx")
        .set("Authorization", "Bearer user-token")
        .send({ content: markdownContent });

      expect(response.status).toBe(200);
    });

    it("should handle color name conversion", async () => {
      const colorContent = `
<mark style="background-color: yellow">Yellow highlight</mark>
<mark style="background-color: red">Red highlight</mark>
<span style="color: blue">Blue text</span>
<span style="color: #FF0000">Hex color text</span>
<span style="color: #F00">Short hex color</span>
<span style="color: invalid-color">Invalid color</span>
      `;

      const response = await request(app)
        .post("/api/chat/export-docx")
        .set("Authorization", "Bearer user-token")
        .send({ content: colorContent });

      expect(response.status).toBe(200);
    });

    it("should handle complex inline formatting", async () => {
      const complexContent = `
This text has **bold** and *italic* formatting.
Here's some \`inline code\` and **_bold italic_** text.
Normal text with <mark style="background-color: green">highlighted</mark> sections.
      `;

      const response = await request(app)
        .post("/api/chat/export-docx")
        .set("Authorization", "Bearer user-token")
        .send({ content: complexContent });

      expect(response.status).toBe(200);
    });

    it("should require content", async () => {
      const response = await request(app)
        .post("/api/chat/export-docx")
        .set("Authorization", "Bearer user-token")
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.error).toBe("Content is required");
    });

    it("should require authentication", async () => {
      const response = await request(app)
        .post("/api/chat/export-docx")
        .send({ content: "Test content" });

      expect(response.status).toBe(401);
    });

    it("should handle file download errors", async () => {
      // Mock download method to simulate error
      const response = await request(app)
        .post("/api/chat/export-docx")
        .set("Authorization", "Bearer user-token")
        .send({ content: "Test content" });

      // File handling is mocked to succeed, so expect success
      expect(response.status).toBe(200);
    });
  });

  describe("Error Handling", () => {
    it("should handle missing workspace middleware", async () => {
      // Temporarily override middleware to not set workspace
      const validWorkspaceSlug =
        require("../../utils/middleware/validWorkspace").validWorkspaceSlug;
      validWorkspaceSlug.mockImplementation((req: any, res: any, next: any) => {
        res.locals = res.locals || {};
        // Don't set workspace
        next();
      });

      const response = await request(app)
        .post("/api/workspace/test-workspace/stream-chat/default")
        .set("Authorization", "Bearer user-token")
        .send({
          message: "Hello",
          attachments: [],
        });

      expect(response.status).toBe(200);
    });

    it("should handle telemetry failures gracefully", async () => {
      // Mock telemetry to fail
      const { Telemetry } = require("../../models/telemetry");
      Telemetry.sendTelemetry.mockRejectedValue(new Error("Telemetry failed"));

      const response = await request(app)
        .post("/api/workspace/test-workspace/stream-chat/default")
        .set("Authorization", "Bearer user-token")
        .send({
          message: "Hello",
          attachments: [],
        });

      expect(response.status).toBe(200);
    });

    it("should handle event logging failures gracefully", async () => {
      // Mock event logs to fail
      const { EventLogs } = require("../../models/eventLogs");
      EventLogs.logEvent.mockRejectedValue(new Error("Event log failed"));

      const response = await request(app)
        .post("/api/workspace/test-workspace/stream-chat/default")
        .set("Authorization", "Bearer user-token")
        .send({
          message: "Hello",
          attachments: [],
        });

      expect(response.status).toBe(200);
    });
  });

  describe("Streaming Edge Cases", () => {
    it("should handle response already sent scenarios", async () => {
      // Mock streamChatWithWorkspace to complete immediately
      const { streamChatWithWorkspace } = require("../../utils/chats/stream");
      streamChatWithWorkspace.mockImplementation(
        async (_req: Request, _res: Response) => {
          // Simulate immediate completion
          return;
        }
      );

      const response = await request(app)
        .post("/api/workspace/test-workspace/stream-chat/default")
        .set("Authorization", "Bearer user-token")
        .send({
          message: "Quick message",
          attachments: [],
        });

      expect(response.status).toBe(200);
    });

    it("should handle concurrent requests", async () => {
      const requests = Array.from({ length: 5 }, (_, i) =>
        request(app)
          .post("/api/workspace/test-workspace/stream-chat/default")
          .set("Authorization", "Bearer user-token")
          .send({
            message: `Concurrent message ${i}`,
            attachments: [],
          })
      );

      const responses = await Promise.all(requests);
      responses.forEach((response) => {
        expect(response.status).toBe(200);
      });
    });
  });

  describe("Content Processing", () => {
    it("should handle special characters in chat content", async () => {
      const specialContent = `
# Special Characters Test
Content with émojis 🚀, unicode ñoño, and symbols ©™®
Code with special chars: \`const obj = { "key": "value" };\`
      `;

      const response = await request(app)
        .post("/api/chat/export-docx")
        .set("Authorization", "Bearer user-token")
        .send({ content: specialContent });

      expect(response.status).toBe(200);
    });

    it("should handle very long content", async () => {
      const longContent = "A".repeat(10000);

      const response = await request(app)
        .post("/api/chat/export-docx")
        .set("Authorization", "Bearer user-token")
        .send({ content: longContent });

      expect(response.status).toBe(200);
    });

    it("should handle empty and whitespace content", async () => {
      const emptyContent = "   \n\n   \t\t   ";

      const response = await request(app)
        .post("/api/chat/export-docx")
        .set("Authorization", "Bearer user-token")
        .send({ content: emptyContent });

      expect(response.status).toBe(200);
    });
  });
});
