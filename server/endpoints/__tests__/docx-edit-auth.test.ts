import request from "supertest";
import express from "express";
import docxEditEndpoints from "../api/docx-edit";
import { validatedRequest } from "../../utils/middleware/validatedRequest";
import { userFromSession } from "../../utils/http";
import { handleFileUpload } from "../../utils/files/multer";
import type { Router } from "express";

// Mock all dependencies
jest.mock("../../utils/middleware/validatedRequest");
jest.mock("../../utils/http");
jest.mock("../../utils/files/multer");
jest.mock("../../utils/docx/editWithLLM");
jest.mock("../../utils/docx/compareAndHighlight");
jest.mock("../../utils/docx/docxToMarkdown");
jest.mock("../../utils/docx/textToDocx");
jest.mock("../../utils/docx/templateRenderer");
jest.mock("../../utils/docx/mergeDocxWithTemplate");
jest.mock("../../utils/i18n");
jest.mock("../../models/systemSettings");
jest.mock("@langchain/community/document_loaders/fs/docx");
jest.mock("uuid");
jest.mock("fs");
jest.mock("node-fetch");

describe("DOCX Edit Endpoints - Authentication Tests", () => {
  let app: express.Application;
  let apiRouter: Router;

  // Set default timeout for all tests
  jest.setTimeout(10000);

  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetModules(); // Reset module cache to prevent leaks between tests

    // Set up basic mocks
    const fs = require("fs");
    fs.existsSync = jest.fn().mockReturnValue(true);
    fs.mkdirSync = jest.fn().mockReturnValue(undefined);
    fs.copyFileSync = jest.fn().mockReturnValue(undefined);
    fs.unlinkSync = jest.fn().mockReturnValue(undefined);
    fs.readdirSync = jest.fn().mockReturnValue(["original.docx"]);
    fs.writeFileSync = jest.fn().mockReturnValue(undefined);
    fs.rmSync = jest.fn().mockReturnValue(undefined);
    fs.createReadStream = jest.fn().mockReturnValue({
      pipe: jest.fn(),
    });

    app = express();
    apiRouter = express.Router();
    app.use(express.json());
    app.use("/api", apiRouter);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
    jest.resetModules();
    // Force garbage collection if possible
    if (global.gc) {
      global.gc();
    }
  });

  describe("Development vs Production Authentication", () => {
    const endpoints = [
      { method: "delete", path: "/api/docx-edit/cleanup/test-session" },
      {
        method: "post",
        path: "/api/docx-edit/process",
        body: { sessionId: "test", instructions: "edit" },
      },
      { method: "get", path: "/api/docx-edit/content/test-session" },
      { method: "get", path: "/api/docx-edit/download/test-session/file.docx" },
      {
        method: "post",
        path: "/api/docx-edit/text-to-docx",
        body: { text: "content" },
      },
      {
        method: "post",
        path: "/api/docx-edit/text-to-docx-template",
        body: { text: "content", templateSessionId: "test" },
      },
    ];

    it("should skip authentication in development mode", async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "development";

      // Initialize endpoints in development mode
      docxEditEndpoints(apiRouter);

      // validatedRequest should not be called for any endpoint
      for (const endpoint of endpoints) {
        let _response;
        if (endpoint.method === "get") {
          _response = await request(app).get(endpoint.path);
        } else if (endpoint.method === "post") {
          _response = await request(app)
            .post(endpoint.path)
            .send(endpoint.body);
        } else if (endpoint.method === "delete") {
          _response = await request(app).delete(endpoint.path);
        }

        // Should not call validatedRequest middleware
        expect(validatedRequest).not.toHaveBeenCalled();
        jest.clearAllMocks();
      }

      process.env.NODE_ENV = originalEnv;
    });

    it("should require authentication in production mode", async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "production";

      // Mock validatedRequest to simulate auth failure
      (validatedRequest as jest.Mock).mockImplementation((req, res) => {
        res.status(401).json({ error: "No auth token found." });
      });

      // Initialize endpoints in production mode
      docxEditEndpoints(apiRouter);

      // All endpoints should require authentication
      for (const endpoint of endpoints) {
        let response;
        if (endpoint.method === "get") {
          response = await request(app).get(endpoint.path);
        } else if (endpoint.method === "post") {
          response = await request(app).post(endpoint.path).send(endpoint.body);
        } else if (endpoint.method === "delete") {
          response = await request(app).delete(endpoint.path);
        }

        expect(response!.status).toBe(401);
        expect(response!.body.error).toBe("No auth token found.");
        expect(validatedRequest).toHaveBeenCalled();
        jest.clearAllMocks();
      }

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe("File upload endpoints authentication", () => {
    const fileUploadEndpoints = [
      { path: "/api/docx-edit/upload", fieldname: "file" },
      { path: "/api/docx-edit/upload-for-template", fieldname: "file" },
    ];

    beforeEach(() => {
      // Mock file upload middleware
      (handleFileUpload as jest.Mock).mockImplementation((req, res, next) => {
        req.file = {
          originalname: "test.docx",
          path: "/tmp/test.docx",
          fieldname: "file",
          encoding: "7bit",
          mimetype:
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          size: 1000,
          destination: "/tmp",
          filename: "test.docx",
          buffer: Buffer.from("test"),
          stream: {} as any,
        };
        next();
      });
    });

    it("should require user session for file uploads in production", async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "production";

      // Mock validatedRequest to pass through
      (validatedRequest as jest.Mock).mockImplementation((req, res, next) =>
        next()
      );

      // Mock userFromSession to fail
      (userFromSession as jest.Mock).mockRejectedValue(
        new Error("Invalid session")
      );

      // Initialize endpoints
      docxEditEndpoints(apiRouter);

      for (const endpoint of fileUploadEndpoints) {
        const response = await request(app)
          .post(endpoint.path)
          .attach(endpoint.fieldname, Buffer.from("test"), "test.docx");

        expect(response.status).toBe(500);
        expect(userFromSession).toHaveBeenCalled();
        jest.clearAllMocks();
      }

      process.env.NODE_ENV = originalEnv;
    });

    it("should skip user session check in development mode", async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "development";

      // Initialize endpoints
      docxEditEndpoints(apiRouter);

      for (const endpoint of fileUploadEndpoints) {
        const _response = await request(app)
          .post(endpoint.path)
          .attach(endpoint.fieldname, Buffer.from("test"), "test.docx");

        // Should not call userFromSession
        expect(userFromSession).not.toHaveBeenCalled();
        jest.clearAllMocks();
      }

      process.env.NODE_ENV = originalEnv;
    }, 10000); // Increase timeout to 10 seconds
  });

  describe("Token validation", () => {
    beforeEach(() => {
      // Initialize endpoints in production mode
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "production";
      docxEditEndpoints(apiRouter);
      process.env.NODE_ENV = originalEnv;
    });

    it("should accept valid authentication token", async () => {
      // Mock validatedRequest to pass through with valid token
      (validatedRequest as jest.Mock).mockImplementation((req, res, next) => {
        req.user = { id: 1, username: "testuser" };
        next();
      });

      const response = await request(app)
        .get("/api/docx-edit/content/test-session")
        .set("Authorization", "Bearer valid-token");

      // Should pass authentication (will fail on file not found)
      expect(response.status).not.toBe(401);
      expect(validatedRequest).toHaveBeenCalled();
    });

    it("should reject invalid authentication token", async () => {
      // Mock validatedRequest to reject invalid token
      (validatedRequest as jest.Mock).mockImplementation((req, res) => {
        res.status(401).json({ error: "Invalid token" });
      });

      const response = await request(app)
        .get("/api/docx-edit/content/test-session")
        .set("Authorization", "Bearer invalid-token");

      expect(response.status).toBe(401);
      expect(response.body.error).toBe("Invalid token");
    });

    it("should reject requests without authorization header", async () => {
      // Mock validatedRequest to check for missing auth
      (validatedRequest as jest.Mock).mockImplementation((req, res) => {
        res.status(401).json({ error: "No authorization header" });
      });

      const response = await request(app).get(
        "/api/docx-edit/content/test-session"
      );

      expect(response.status).toBe(401);
      expect(response.body.error).toBe("No authorization header");
    });
  });

  describe("Middleware order and execution", () => {
    it("should execute middleware in correct order for uploads", async () => {
      const middlewareOrder: string[] = [];

      // Mock middleware to track execution order
      (validatedRequest as jest.Mock).mockImplementation((req, res, next) => {
        middlewareOrder.push("validatedRequest");
        next();
      });

      (handleFileUpload as jest.Mock).mockImplementation((req, res, next) => {
        middlewareOrder.push("handleFileUpload");
        req.file = {
          originalname: "test.docx",
          path: "/tmp/test.docx",
          fieldname: "file",
          encoding: "7bit",
          mimetype:
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          size: 1000,
          destination: "/tmp",
          filename: "test.docx",
          buffer: Buffer.from("test"),
          stream: {} as any,
        };
        next();
      });

      (userFromSession as jest.Mock).mockImplementation(() => {
        middlewareOrder.push("userFromSession");
        return Promise.resolve({ id: 1, username: "testuser" });
      });

      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "production";
      docxEditEndpoints(apiRouter);

      await request(app).post("/api/docx-edit/upload");

      // Verify middleware execution order
      expect(middlewareOrder).toEqual([
        "validatedRequest",
        "handleFileUpload",
        "userFromSession",
      ]);

      process.env.NODE_ENV = originalEnv;
    });

    it("should short-circuit on middleware failure", async () => {
      let _handlerCalled = false;

      // Mock validatedRequest to fail
      (validatedRequest as jest.Mock).mockImplementation((req, res) => {
        res.status(401).json({ error: "Auth failed" });
      });

      // Mock other middleware - should not be called
      (handleFileUpload as jest.Mock).mockImplementation(
        (_req, _res, _next) => {
          throw new Error("handleFileUpload should not be called");
        }
      );

      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "production";
      docxEditEndpoints(apiRouter);

      const response = await request(app).post("/api/docx-edit/upload");

      expect(response.status).toBe(401);
      expect(response.body.error).toBe("Auth failed");
      expect(handleFileUpload).not.toHaveBeenCalled();

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe("Environment-specific behavior", () => {
    it("should use different LLM settings in development", async () => {
      const originalEnv = process.env.NODE_ENV;
      const originalOpenModel = process.env.OPEN_AI_MODEL_PREF;

      try {
        process.env.NODE_ENV = "development";
        process.env.OPEN_AI_MODEL_PREF = "gpt-3.5-turbo";

        // Clear and re-setup mocks for this specific test
        jest.resetModules();
        jest.clearAllMocks();

        // Re-mock editDocxWithLLM specifically for this test
        const mockEditDocxWithLLM = jest.fn().mockResolvedValue({
          originalText: "original",
          editedText: "edited",
        });
        jest.doMock("../../utils/docx/editWithLLM", () => ({
          editDocxWithLLM: mockEditDocxWithLLM,
        }));

        // Re-mock other required modules
        jest.doMock("../../utils/docx/compareAndHighlight", () => ({
          compareAndHighlightDocx: jest.fn().mockResolvedValue(undefined),
          createHighlightedDocxFilename: jest
            .fn()
            .mockReturnValue("highlighted.docx"),
        }));

        jest.doMock("fs", () => ({
          existsSync: jest.fn().mockReturnValue(true),
          readdirSync: jest.fn().mockReturnValue(["original.docx"]),
          writeFileSync: jest.fn().mockReturnValue(undefined),
          mkdirSync: jest.fn().mockReturnValue(undefined),
        }));

        // Import the endpoint function after setting up mocks
        const docxEditEndpoints = require("../api/docx-edit").default;
        docxEditEndpoints(apiRouter);

        const _response = await request(app)
          .post("/api/docx-edit/process")
          .send({
            sessionId: "test-session",
            instructions: "edit this",
            provider: "anthropic",
            model: "claude-2",
          });

        // In development, should force OpenAI provider
        expect(mockEditDocxWithLLM).toHaveBeenCalledWith(
          expect.any(String),
          "edit this",
          {
            provider: "openai",
            model: "gpt-3.5-turbo",
          }
        );
      } finally {
        process.env.NODE_ENV = originalEnv;
        process.env.OPEN_AI_MODEL_PREF = originalOpenModel;
      }
    });
  });
});
