import request from "supertest";
import express from "express";
import * as fs from "fs";
import * as path from "path";
import docxEditEndpoints from "../api/docx-edit";
import type { Router } from "express";
import { Readable } from "stream";

// Calculate the correct storage path for development mode
const ENDPOINT_DIR = path.resolve(__dirname, "../api/docx-edit");
const STORAGE_PATH = path.resolve(ENDPOINT_DIR, "../../../storage/documents");
const DOCX_EDIT_PATH = path.join(STORAGE_PATH, "docx-edit");

// Mock all dependencies
jest.mock("../../utils/middleware/validatedRequest", () => ({
  validatedRequest: jest.fn((req, res, next) => next()),
}));

jest.mock("../../utils/http", () => ({
  reqBody: jest.fn((req) => req.body),
  userFromSession: jest.fn().mockResolvedValue({ id: 1, role: "admin" }),
}));

jest.mock("../../utils/files/multer", () => ({
  handleFileUpload: jest.fn((req, res, next) => next()),
}));

jest.mock("../../utils/docx/editWithLLM", () => ({
  editDocxWithLLM: jest.fn(),
}));

jest.mock("../../utils/docx/compareAndHighlight", () => ({
  compareAndHighlightDocx: jest.fn(),
  createHighlightedDocxFilename: jest.fn((filename) =>
    filename.replace(".docx", "_highlighted.docx")
  ),
}));

jest.mock("../../utils/docx/docxToMarkdown", () => ({
  docxToMarkdown: jest.fn(),
}));

jest.mock("../../utils/docx/textToDocx", () => ({
  textToDocx: jest.fn(),
  createDocxFilename: jest.fn(() => "document_" + Date.now() + ".docx"),
}));

jest.mock("../../utils/docx/templateRenderer", () => ({
  renderDocxTemplate: jest.fn(),
}));

jest.mock("../../models/systemSettings");

jest.mock("@langchain/community/document_loaders/fs/docx", () => ({
  DocxLoader: jest.fn().mockImplementation(() => ({
    load: jest.fn().mockResolvedValue([{ pageContent: "test content" }]),
  })),
}));

jest.mock("uuid", () => ({
  v4: jest.fn(() => "test-session-id"),
}));

jest.mock("../../utils/i18n", () => ({
  tSync: jest.fn((key) => key),
}));

// Mock the require() for mergeDocxWithTemplate
jest.mock("../../utils/docx/mergeDocxWithTemplate", () => ({
  mergeDocxWithTemplate: jest.fn(),
}));

// Import mocked functions
import { handleFileUpload } from "../../utils/files/multer";
import { editDocxWithLLM } from "../../utils/docx/editWithLLM";
import { docxToMarkdown } from "../../utils/docx/docxToMarkdown";
import { textToDocx } from "../../utils/docx/textToDocx";
import { compareAndHighlightDocx } from "../../utils/docx/compareAndHighlight";
import { renderDocxTemplate } from "../../utils/docx/templateRenderer";
import SystemSettings from "../../models/systemSettings";
import { v4 as uuidv4 } from "uuid";

describe("DOCX Edit Endpoints - Integration Tests", () => {
  let app: express.Application;
  let apiRouter: Router;
  let originalNodeEnv: string | undefined;

  beforeAll(() => {
    // Save original NODE_ENV
    originalNodeEnv = process.env.NODE_ENV;
    // Set environment to development to use relative paths
    process.env.NODE_ENV = "development";

    // Create storage directories
    fs.mkdirSync(DOCX_EDIT_PATH, { recursive: true });
  });

  afterAll(() => {
    // Restore NODE_ENV
    process.env.NODE_ENV = originalNodeEnv;

    // Clean up storage
    if (fs.existsSync(STORAGE_PATH)) {
      fs.rmSync(STORAGE_PATH, { recursive: true, force: true });
    }
  });

  beforeEach(() => {
    jest.clearAllMocks();
    app = express();
    apiRouter = express.Router();
    app.use(express.json());
    app.use("/api", apiRouter);

    // Initialize endpoints
    docxEditEndpoints(apiRouter);
  });

  afterEach(() => {
    // Clean up all session directories after each test
    if (fs.existsSync(DOCX_EDIT_PATH)) {
      const sessions = fs.readdirSync(DOCX_EDIT_PATH);
      sessions.forEach((session) => {
        const sessionPath = path.join(DOCX_EDIT_PATH, session);
        if (fs.statSync(sessionPath).isDirectory()) {
          fs.rmSync(sessionPath, { recursive: true, force: true });
        }
      });
    }
  });

  describe("Upload endpoint", () => {
    it("should successfully upload a DOCX file", async () => {
      const mockFile = {
        originalname: "test.docx",
        path: "/tmp/test.docx",
        fieldname: "file",
        encoding: "7bit",
        mimetype:
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        size: 1000,
        destination: "/tmp",
        filename: "test.docx",
        buffer: Buffer.from("test"),
        stream: new Readable({ read() {} }),
      };

      // Mock file handling
      (handleFileUpload as jest.Mock).mockImplementation((req, res, next) => {
        req.file = mockFile;
        next();
      });

      // Create a temp file
      const tempPath = path.join(STORAGE_PATH, "temp.docx");
      fs.writeFileSync(tempPath, "test content");
      mockFile.path = tempPath;

      const response = await request(app).post("/api/docx-edit/upload");

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.sessionId).toBe("test-session-id");
      expect(response.body.data.filename).toBe("test.docx");

      // Verify session directory was created
      const sessionDir = path.join(DOCX_EDIT_PATH, "test-session-id");
      expect(fs.existsSync(sessionDir)).toBe(true);
      expect(fs.existsSync(path.join(sessionDir, "original.docx"))).toBe(true);

      // Clean up temp file
      if (fs.existsSync(tempPath)) {
        fs.unlinkSync(tempPath);
      }
    });

    it("should reject non-DOCX files", async () => {
      const mockFile = {
        originalname: "test.pdf",
        path: "/tmp/test.pdf",
        fieldname: "file",
        encoding: "7bit",
        mimetype: "application/pdf",
        size: 1000,
        destination: "/tmp",
        filename: "test.pdf",
        buffer: Buffer.from("test"),
        stream: new Readable({ read() {} }),
      };

      (handleFileUpload as jest.Mock).mockImplementation((req, res, next) => {
        req.file = mockFile;
        next();
      });

      const response = await request(app).post("/api/docx-edit/upload");

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe("docxEdit.errors.processingError");
    });
  });

  describe("Content retrieval endpoint", () => {
    it("should retrieve document content as markdown", async () => {
      const sessionId = "content-test-session";
      const sessionDir = path.join(DOCX_EDIT_PATH, sessionId);

      // Create session with file
      fs.mkdirSync(sessionDir, { recursive: true });
      fs.writeFileSync(path.join(sessionDir, "original.docx"), "test content");

      // Mock markdown conversion
      (docxToMarkdown as jest.Mock).mockResolvedValue(
        "# Test Document\n\nThis is test content."
      );

      const response = await request(app).get(
        `/api/docx-edit/content/${sessionId}`
      );

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.content).toContain("Test Document");
    });

    it("should handle empty documents", async () => {
      const sessionId = "empty-content-session";
      const sessionDir = path.join(DOCX_EDIT_PATH, sessionId);

      fs.mkdirSync(sessionDir, { recursive: true });
      fs.writeFileSync(path.join(sessionDir, "original.docx"), "");

      // Mock empty content
      (docxToMarkdown as jest.Mock).mockResolvedValue("");

      const response = await request(app).get(
        `/api/docx-edit/content/${sessionId}`
      );

      expect(response.status).toBe(404);
      expect(response.body.error).toBe("docxEdit.errors.noContent");
    });

    it("should return 404 for non-existent session", async () => {
      const response = await request(app).get(
        "/api/docx-edit/content/non-existent"
      );

      expect(response.status).toBe(404);
      expect(response.body.error).toBe("docxEdit.errors.sessionNotFound");
    });
  });

  describe("Process endpoint", () => {
    it("should process a document with LLM", async () => {
      const sessionId = "process-test-session";
      const sessionDir = path.join(DOCX_EDIT_PATH, sessionId);

      // Create session with file
      fs.mkdirSync(sessionDir, { recursive: true });
      fs.writeFileSync(path.join(sessionDir, "original.docx"), "test content");

      // Mock LLM processing
      (editDocxWithLLM as jest.Mock).mockResolvedValue({
        originalText: "Original content",
        editedText: "Edited content with changes",
      });

      (compareAndHighlightDocx as jest.Mock).mockResolvedValue(undefined);

      const response = await request(app).post("/api/docx-edit/process").send({
        sessionId,
        instructions: "Make all headings bold",
        provider: "openai",
        model: "gpt-4",
      });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.highlightedFilename).toBe(
        "original_highlighted.docx"
      );

      // Verify edited text was saved
      expect(fs.existsSync(path.join(sessionDir, "edited.txt"))).toBe(true);
    });

    it("should require session ID and instructions", async () => {
      const response = await request(app).post("/api/docx-edit/process").send({
        instructions: "Test",
      });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe("docxEdit.errors.processingError");
    });
  });

  describe("Text to DOCX conversion", () => {
    it("should convert text to DOCX with system template", async () => {
      // Mock uuidv4 for this test
      (uuidv4 as jest.Mock).mockReturnValue("text-to-docx-session");

      // Mock system template
      (SystemSettings.get as jest.Mock).mockResolvedValue({
        value: "system-template.docx",
      });

      // Create template file in the correct location
      const assetsPath = path.resolve(ENDPOINT_DIR, "../../../storage/assets");
      fs.mkdirSync(assetsPath, { recursive: true });
      fs.writeFileSync(
        path.join(assetsPath, "system-template.docx"),
        "template"
      );

      (textToDocx as jest.Mock).mockResolvedValue(undefined);
      const {
        mergeDocxWithTemplate,
      } = require("../../utils/docx/mergeDocxWithTemplate");
      (mergeDocxWithTemplate as jest.Mock).mockImplementation(() => {});

      const response = await request(app)
        .post("/api/docx-edit/text-to-docx")
        .send({
          text: "# My Document\n\nThis is content",
          title: "Test Document",
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.sessionId).toBe("text-to-docx-session");
      expect(response.body.data.filename).toBeDefined();

      // Clean up only the test template file, not the entire assets directory
      const testTemplatePath = path.join(assetsPath, "system-template.docx");
      if (fs.existsSync(testTemplatePath)) {
        fs.unlinkSync(testTemplatePath);
      }
    });

    it("should convert text to DOCX without template", async () => {
      // Mock uuidv4 for this test
      (uuidv4 as jest.Mock).mockReturnValue("text-no-template-session");

      // No system template
      (SystemSettings.get as jest.Mock).mockResolvedValue(null);
      (textToDocx as jest.Mock).mockResolvedValue(undefined);

      const response = await request(app)
        .post("/api/docx-edit/text-to-docx")
        .send({
          text: "# My Document\n\nThis is content",
          title: "Test Document",
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.sessionId).toBe("text-no-template-session");
    });
  });

  describe("Template-based conversion", () => {
    it("should convert text using uploaded template", async () => {
      // Mock uuidv4 for this test
      (uuidv4 as jest.Mock).mockReturnValue("new-template-session");

      const templateSessionId = "template-session";
      const templateDir = path.join(DOCX_EDIT_PATH, templateSessionId);

      // Create template session
      fs.mkdirSync(templateDir, { recursive: true });
      fs.writeFileSync(path.join(templateDir, "original.docx"), "template");

      (renderDocxTemplate as jest.Mock).mockResolvedValue(undefined);

      const response = await request(app)
        .post("/api/docx-edit/text-to-docx-template")
        .send({
          text: "Document content",
          title: "Template Document",
          templateSessionId,
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.sessionId).toBe("new-template-session");
    });
  });

  describe("Download endpoint", () => {
    it("should download processed file", async () => {
      const sessionId = "download-test-session";
      const filename = "test_highlighted.docx";
      const sessionDir = path.join(DOCX_EDIT_PATH, sessionId);

      // Create session with file
      fs.mkdirSync(sessionDir, { recursive: true });
      fs.writeFileSync(path.join(sessionDir, filename), "processed content");

      const response = await request(app).get(
        `/api/docx-edit/download/${sessionId}/${filename}`
      );

      expect(response.status).toBe(200);
      expect(response.headers["content-disposition"]).toContain(filename);
      expect(response.headers["content-type"]).toBe(
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      );
    });

    it("should return 404 for missing file", async () => {
      const response = await request(app).get(
        "/api/docx-edit/download/missing/file.docx"
      );

      expect(response.status).toBe(404);
    });
  });

  describe("Cleanup endpoint", () => {
    it("should clean up session directory", async () => {
      const sessionId = "cleanup-test-session";
      const sessionDir = path.join(DOCX_EDIT_PATH, sessionId);

      // Create session
      fs.mkdirSync(sessionDir, { recursive: true });
      fs.writeFileSync(path.join(sessionDir, "test.docx"), "content");

      const response = await request(app).delete(
        `/api/docx-edit/cleanup/${sessionId}`
      );

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(fs.existsSync(sessionDir)).toBe(false);
    });

    it("should return 404 for non-existent session", async () => {
      const response = await request(app).delete(
        "/api/docx-edit/cleanup/non-existent"
      );

      expect(response.status).toBe(404);
      expect(response.body.error).toBe("Session not found");
    });
  });
});
