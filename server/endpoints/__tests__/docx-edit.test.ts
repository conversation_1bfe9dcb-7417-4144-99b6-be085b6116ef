import request from "supertest";
import express from "express";
import * as fs from "fs";
import docxEditEndpoints from "../api/docx-edit";
import { validatedRequest } from "../../utils/middleware/validatedRequest";
import { userFromSession } from "../../utils/http";
import { handleFileUpload } from "../../utils/files/multer";
import { editDocxWithLLM } from "../../utils/docx/editWithLLM";
import { compareAndHighlightDocx } from "../../utils/docx/compareAndHighlight";
import { docxToMarkdown } from "../../utils/docx/docxToMarkdown";
import { textToDocx } from "../../utils/docx/textToDocx";
import { renderDocxTemplate } from "../../utils/docx/templateRenderer";
import SystemSettings from "../../models/systemSettings";
import { DocxLoader } from "@langchain/community/document_loaders/fs/docx";
import type { Router } from "express";
import { Readable } from "stream";

// Type definitions for better type safety
interface MockFile {
  originalname: string;
  path: string;
  fieldname: string;
  encoding: string;
  mimetype: string;
  size: number;
  destination: string;
  filename: string;
  buffer: Buffer;
  stream: Readable;
}

// Mock all dependencies
jest.mock("../../utils/middleware/validatedRequest");
jest.mock("../../utils/http", () => ({
  userFromSession: jest.fn(),
  reqBody: jest.fn((req) => req.body),
}));
jest.mock("../../utils/files/multer");
jest.mock("../../utils/docx/editWithLLM");
jest.mock("../../utils/docx/compareAndHighlight", () => ({
  compareAndHighlightDocx: jest.fn(),
  createHighlightedDocxFilename: jest.fn(
    (filename) => `highlighted_${filename}`
  ),
}));
jest.mock("../../utils/docx/docxToMarkdown");
jest.mock("../../utils/docx/textToDocx", () => ({
  textToDocx: jest.fn(),
  createDocxFilename: jest.fn(() => "generated_document.docx"),
}));
jest.mock("../../utils/docx/templateRenderer");
jest.mock("../../utils/docx/mergeDocxWithTemplate", () => jest.fn());
jest.mock("../../models/systemSettings");
jest.mock("@langchain/community/document_loaders/fs/docx");
jest.mock("../../utils/i18n", () => ({
  tSync: jest.fn((key) => {
    const translations: Record<string, string> = {
      "docxEdit.errors.processingError": "Processing error",
      "docxEdit.success.uploaded": "DOCX file uploaded successfully",
      "docxEdit.errors.sessionNotFound": "Session not found",
      "docxEdit.errors.fileNotFound": "File not found",
      "docxEdit.errors.noContent": "No text content found in the document",
      "docxEdit.success.edited": "DOCX file edited successfully",
      "docxEdit.success.textToDocx": "Text converted to DOCX successfully",
      "docxEdit.success.cleaned": "Session cleaned up successfully",
      "docxEdit.errors.missingParams": "Missing required parameters",
      "docxEdit.success.contentExtracted":
        "Content extracted from DOCX file successfully",
      "docx.canvasDocumentTitle": "Canvas Document",
    };
    return translations[key] || key;
  }),
}));
jest.mock("uuid", () => ({
  v4: jest.fn(() => "test-session-id"),
}));

// Mock fs module
jest.mock("fs", () => ({
  ...jest.requireActual("fs"),
  existsSync: jest.fn(),
  mkdirSync: jest.fn(),
  copyFileSync: jest.fn(),
  unlinkSync: jest.fn(),
  writeFileSync: jest.fn(),
  readdirSync: jest.fn(),
  rmSync: jest.fn(),
  createReadStream: jest.fn(),
}));

describe("DOCX Edit Endpoints", () => {
  let app: express.Application;
  let apiRouter: Router;
  const mockFile: MockFile = {
    originalname: "test.docx",
    path: "/tmp/test.docx",
    fieldname: "file",
    encoding: "7bit",
    mimetype:
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    size: 1000,
    destination: "/tmp",
    filename: "test.docx",
    buffer: Buffer.from("test"),
    stream: new Readable(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    app = express();
    apiRouter = express.Router();
    app.use(express.json());
    app.use("/api", apiRouter);

    // Mock middleware to pass through by default
    (validatedRequest as jest.Mock).mockImplementation((req, res, next) =>
      next()
    );
    (handleFileUpload as jest.Mock).mockImplementation((req, res, next) => {
      req.file = mockFile;
      next();
    });
    (userFromSession as jest.Mock).mockResolvedValue({
      id: 1,
      username: "testuser",
    });

    // Mock fs functions with default behaviors
    (fs.existsSync as jest.Mock).mockReturnValue(true);
    (fs.mkdirSync as jest.Mock).mockImplementation(() => {});
    (fs.copyFileSync as jest.Mock).mockImplementation(() => {});
    (fs.unlinkSync as jest.Mock).mockImplementation(() => {});
    (fs.writeFileSync as jest.Mock).mockImplementation(() => {});
    (fs.readdirSync as jest.Mock).mockReturnValue(["original.docx"]);
    (fs.rmSync as jest.Mock).mockImplementation(() => {});

    // Initialize endpoints
    docxEditEndpoints(apiRouter);
  });

  describe("DELETE /api/docx-edit/cleanup/:sessionId", () => {
    it("should require authentication in production mode", async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "production";

      (validatedRequest as jest.Mock).mockImplementation((req, res) => {
        res.status(401).json({ error: "No auth token found." });
      });

      const response = await request(app).delete(
        "/api/docx-edit/cleanup/test-session-id"
      );

      expect(response.status).toBe(401);
      expect(response.body.error).toBe("No auth token found.");
      expect(validatedRequest).toHaveBeenCalled();

      process.env.NODE_ENV = originalEnv;
    });

    it("should not require authentication in development mode", async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "development";

      // Re-initialize endpoints in development mode
      const devApp = express();
      const devApiRouter = express.Router();
      devApp.use(express.json());
      devApp.use("/api", devApiRouter);
      docxEditEndpoints(devApiRouter);

      const response = await request(devApp).delete(
        "/api/docx-edit/cleanup/test-session-id"
      );

      expect(response.status).toBe(200);
      expect(validatedRequest).not.toHaveBeenCalled();

      process.env.NODE_ENV = originalEnv;
    });

    it("should return 400 if sessionId is missing", async () => {
      const response = await request(app).delete("/api/docx-edit/cleanup/");

      expect(response.status).toBe(404); // Express returns 404 for missing route params
    });

    it("should return 404 if session directory does not exist", async () => {
      (fs.existsSync as jest.Mock).mockReturnValue(false);

      const response = await request(app).delete(
        "/api/docx-edit/cleanup/test-session-id"
      );

      expect(response.status).toBe(404);
      expect(response.body.error).toBe("Session not found");
    });

    it("should successfully clean up session directory", async () => {
      const response = await request(app).delete(
        "/api/docx-edit/cleanup/test-session-id"
      );

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain("cleaned up successfully");
      expect(fs.rmSync).toHaveBeenCalledWith(
        expect.stringContaining("test-session-id"),
        { recursive: true, force: true }
      );
    });

    it("should handle errors during cleanup", async () => {
      (fs.rmSync as jest.Mock).mockImplementation(() => {
        throw new Error("Failed to delete");
      });

      const response = await request(app).delete(
        "/api/docx-edit/cleanup/test-session-id"
      );

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain("Failed to delete");
    });
  });

  describe("POST /api/docx-edit/process", () => {
    const validProcessRequest = {
      sessionId: "test-session-id",
      instructions: "Make the text bold",
      provider: "openai",
      model: "gpt-4",
    };

    it("should require authentication", async () => {
      (validatedRequest as jest.Mock).mockImplementation((req, res) => {
        res.status(401).json({ error: "No auth token found." });
      });

      const response = await request(app)
        .post("/api/docx-edit/process")
        .send(validProcessRequest);

      expect(response.status).toBe(401);
      expect(response.body.error).toBe("No auth token found.");
    });

    it("should return 400 if sessionId is missing", async () => {
      const response = await request(app)
        .post("/api/docx-edit/process")
        .send({ instructions: "Make bold" });

      expect(response.status).toBe(400);
      expect(response.body.error).toContain("Processing error");
    });

    it("should return 400 if instructions are missing", async () => {
      const response = await request(app)
        .post("/api/docx-edit/process")
        .send({ sessionId: "test-session-id" });

      expect(response.status).toBe(400);
      expect(response.body.error).toContain("Processing error");
    });

    it("should return 404 if session directory does not exist", async () => {
      (fs.existsSync as jest.Mock).mockReturnValue(false);

      const response = await request(app)
        .post("/api/docx-edit/process")
        .send(validProcessRequest);

      expect(response.status).toBe(404);
      expect(response.body.error).toBe("Session not found");
    });

    it("should return 404 if original DOCX file not found", async () => {
      (fs.readdirSync as jest.Mock).mockReturnValue(["other.txt"]);

      const response = await request(app)
        .post("/api/docx-edit/process")
        .send(validProcessRequest);

      expect(response.status).toBe(404);
      expect(response.body.error).toBe("File not found");
    });

    it("should successfully process DOCX file", async () => {
      (editDocxWithLLM as jest.Mock).mockResolvedValue({
        originalText: "Original text",
        editedText: "Edited text",
      });
      (compareAndHighlightDocx as jest.Mock).mockResolvedValue(undefined);

      const response = await request(app)
        .post("/api/docx-edit/process")
        .send(validProcessRequest);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain("edited successfully");
      expect(response.body.data).toEqual({
        sessionId: "test-session-id",
        highlightedFilename: expect.stringContaining("highlighted"),
      });

      expect(editDocxWithLLM).toHaveBeenCalledWith(
        expect.any(String),
        "Make the text bold",
        { provider: "openai", model: "gpt-4" }
      );
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        expect.stringContaining("edited.txt"),
        "Edited text"
      );
    });

    it("should handle errors during processing", async () => {
      (editDocxWithLLM as jest.Mock).mockRejectedValue(new Error("LLM error"));

      const response = await request(app)
        .post("/api/docx-edit/process")
        .send(validProcessRequest);

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain("LLM error");
    });
  });

  describe("POST /api/docx-edit/upload", () => {
    it("should require authentication", async () => {
      (validatedRequest as jest.Mock).mockImplementation((req, res) => {
        res.status(401).json({ error: "No auth token found." });
      });

      const response = await request(app)
        .post("/api/docx-edit/upload")
        .attach("file", Buffer.from("test"), "test.docx");

      expect(response.status).toBe(401);
      expect(response.body.error).toBe("No auth token found.");
    });

    it("should return 400 if no file provided", async () => {
      (handleFileUpload as jest.Mock).mockImplementation((req, res, next) => {
        req.file = undefined;
        next();
      });

      const response = await request(app).post("/api/docx-edit/upload");

      expect(response.status).toBe(400);
      expect(response.body.error).toBe("No file provided");
    });

    it("should return 400 for invalid file type", async () => {
      (handleFileUpload as jest.Mock).mockImplementation((req, res, next) => {
        req.file = { ...mockFile, originalname: "test.pdf" };
        next();
      });

      const response = await request(app).post("/api/docx-edit/upload");

      expect(response.status).toBe(400);
      expect(response.body.error).toContain("Processing error");
    });

    it("should successfully upload DOCX file", async () => {
      const response = await request(app).post("/api/docx-edit/upload");

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain("uploaded successfully");
      expect(response.body.data).toEqual({
        sessionId: "test-session-id",
        filename: "test.docx",
      });

      expect(fs.mkdirSync).toHaveBeenCalled();
      expect(fs.copyFileSync).toHaveBeenCalled();
      expect(fs.unlinkSync).toHaveBeenCalled();
    });

    it("should accept DOTX files", async () => {
      (handleFileUpload as jest.Mock).mockImplementation((req, res, next) => {
        req.file = { ...mockFile, originalname: "template.dotx" };
        next();
      });

      const response = await request(app).post("/api/docx-edit/upload");

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it("should handle upload errors", async () => {
      (fs.copyFileSync as jest.Mock).mockImplementation(() => {
        throw new Error("Copy failed");
      });

      const response = await request(app).post("/api/docx-edit/upload");

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain("Copy failed");
    });
  });

  describe("GET /api/docx-edit/content/:sessionId", () => {
    it("should require authentication", async () => {
      (validatedRequest as jest.Mock).mockImplementation((req, res) => {
        res.status(401).json({ error: "No auth token found." });
      });

      const response = await request(app).get(
        "/api/docx-edit/content/test-session-id"
      );

      expect(response.status).toBe(401);
      expect(response.body.error).toBe("No auth token found.");
    });

    it("should return 404 if session directory does not exist", async () => {
      (fs.existsSync as jest.Mock).mockReturnValue(false);

      const response = await request(app).get(
        "/api/docx-edit/content/test-session-id"
      );

      expect(response.status).toBe(404);
      expect(response.body.error).toBe("Session not found");
    });

    it("should return 404 if original DOCX file not found", async () => {
      (fs.readdirSync as jest.Mock).mockReturnValue([]);

      const response = await request(app).get(
        "/api/docx-edit/content/test-session-id"
      );

      expect(response.status).toBe(404);
      expect(response.body.error).toBe("File not found");
    });

    it("should successfully return markdown content", async () => {
      (docxToMarkdown as jest.Mock).mockResolvedValue(
        "# Document Content\n\nThis is markdown"
      );

      const response = await request(app).get(
        "/api/docx-edit/content/test-session-id"
      );

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.content).toBe(
        "# Document Content\n\nThis is markdown"
      );
    });

    it("should fallback to DocxLoader if markdown conversion fails", async () => {
      (docxToMarkdown as jest.Mock).mockRejectedValue(
        new Error("Markdown conversion failed")
      );
      const mockLoader = {
        load: jest
          .fn()
          .mockResolvedValue([
            { pageContent: "Page 1 content" },
            { pageContent: "Page 2 content" },
          ]),
      };
      (DocxLoader as unknown as jest.Mock).mockImplementation(() => mockLoader);

      const response = await request(app).get(
        "/api/docx-edit/content/test-session-id"
      );

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.content).toBe(
        "Page 1 content\n\nPage 2 content\n\n"
      );
    });

    it("should return 404 if no content found", async () => {
      (docxToMarkdown as jest.Mock).mockResolvedValue("");

      const response = await request(app).get(
        "/api/docx-edit/content/test-session-id"
      );

      expect(response.status).toBe(404);
      expect(response.body.error).toBe("No text content found in the document");
    });

    it("should handle errors during content retrieval", async () => {
      (docxToMarkdown as jest.Mock).mockRejectedValue(new Error("Read error"));
      (DocxLoader as unknown as jest.Mock).mockImplementation(() => {
        throw new Error("Loader error");
      });

      const response = await request(app).get(
        "/api/docx-edit/content/test-session-id"
      );

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain("Loader error");
    });
  });

  describe("GET /api/docx-edit/download/:sessionId/:filename", () => {
    const mockStream = {
      pipe: jest.fn(),
      on: jest.fn(),
    };

    beforeEach(() => {
      (fs.createReadStream as jest.Mock).mockReturnValue(mockStream);
    });

    it("should require authentication", async () => {
      (validatedRequest as jest.Mock).mockImplementation((req, res) => {
        res.status(401).json({ error: "No auth token found." });
      });

      const response = await request(app).get(
        "/api/docx-edit/download/test-session-id/test.docx"
      );

      expect(response.status).toBe(401);
      expect(response.body.error).toBe("No auth token found.");
    });

    it("should return 404 if session directory does not exist", async () => {
      (fs.existsSync as jest.Mock).mockReturnValueOnce(false);

      const response = await request(app).get(
        "/api/docx-edit/download/test-session-id/test.docx"
      );

      expect(response.status).toBe(404);
      expect(response.body.error).toBe("Session not found");
    });

    it("should return 404 if file does not exist", async () => {
      (fs.existsSync as jest.Mock)
        .mockReturnValueOnce(true) // session dir exists
        .mockReturnValueOnce(false); // file does not exist

      const response = await request(app).get(
        "/api/docx-edit/download/test-session-id/test.docx"
      );

      expect(response.status).toBe(404);
      expect(response.body.error).toBe("File not found");
    });

    it("should successfully stream file for download", async () => {
      // Mock the stream to immediately end
      mockStream.pipe = jest.fn().mockImplementation((res) => {
        res.end();
        return res;
      });

      const response = await request(app).get(
        "/api/docx-edit/download/test-session-id/test.docx"
      );

      expect(response.status).toBe(200);
      expect(response.headers["content-disposition"]).toBe(
        'attachment; filename="test.docx"'
      );
      expect(response.headers["content-type"]).toBe(
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      );
      expect(fs.createReadStream).toHaveBeenCalled();
      expect(mockStream.pipe).toHaveBeenCalled();
    });

    it("should handle errors during download", async () => {
      (fs.createReadStream as jest.Mock).mockImplementation(() => {
        throw new Error("Stream error");
      });

      const response = await request(app).get(
        "/api/docx-edit/download/test-session-id/test.docx"
      );

      expect(response.status).toBe(500);
      // The download endpoint returns JSON with error details
      expect(response.body).toBeDefined();
      if (response.body.error) {
        expect(response.body.error).toContain("Stream error");
      }
    });
  });

  describe("POST /api/docx-edit/text-to-docx-template", () => {
    const validTemplateRequest = {
      text: "This is the document content",
      title: "Test Document",
      templateSessionId: "template-session-id",
    };

    it("should require authentication", async () => {
      (validatedRequest as jest.Mock).mockImplementation((req, res) => {
        res.status(401).json({ error: "No auth token found." });
      });

      const response = await request(app)
        .post("/api/docx-edit/text-to-docx-template")
        .send(validTemplateRequest);

      expect(response.status).toBe(401);
      expect(response.body.error).toBe("No auth token found.");
    });

    it("should return 400 if text is missing", async () => {
      const response = await request(app)
        .post("/api/docx-edit/text-to-docx-template")
        .send({ templateSessionId: "template-id" });

      expect(response.status).toBe(400);
      expect(response.body.error).toContain("Missing required parameters");
    });

    it("should return 400 if templateSessionId is missing", async () => {
      const response = await request(app)
        .post("/api/docx-edit/text-to-docx-template")
        .send({ text: "Content" });

      expect(response.status).toBe(400);
      expect(response.body.error).toContain("Missing required parameters");
    });

    it("should return 404 if template session not found", async () => {
      (fs.existsSync as jest.Mock).mockReturnValue(false);

      const response = await request(app)
        .post("/api/docx-edit/text-to-docx-template")
        .send(validTemplateRequest);

      expect(response.status).toBe(404);
      expect(response.body.error).toBe("Session not found");
    });

    it("should return 404 if template file not found", async () => {
      (fs.readdirSync as jest.Mock).mockReturnValue(["other.txt"]);

      const response = await request(app)
        .post("/api/docx-edit/text-to-docx-template")
        .send(validTemplateRequest);

      expect(response.status).toBe(404);
      expect(response.body.error).toBe("File not found");
    });

    it("should successfully create DOCX from template", async () => {
      (renderDocxTemplate as jest.Mock).mockResolvedValue(undefined);

      const response = await request(app)
        .post("/api/docx-edit/text-to-docx-template")
        .send(validTemplateRequest);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain("converted to DOCX successfully");
      expect(response.body.data).toEqual({
        sessionId: "test-session-id",
        filename: expect.stringContaining(".docx"),
      });

      expect(renderDocxTemplate as jest.Mock).toHaveBeenCalledWith(
        expect.any(String),
        { body: "This is the document content", title: "Test Document" },
        expect.any(String)
      );
    });

    it("should handle DOTX templates", async () => {
      (fs.readdirSync as jest.Mock).mockReturnValue(["original.dotx"]);
      (renderDocxTemplate as jest.Mock).mockResolvedValue(undefined);

      const response = await request(app)
        .post("/api/docx-edit/text-to-docx-template")
        .send(validTemplateRequest);

      expect(response.status).toBe(200);
      expect(response.body.data.filename).toContain(".dotx");
    });

    it("should handle errors during template rendering", async () => {
      (renderDocxTemplate as jest.Mock).mockRejectedValue(
        new Error("Template error")
      );

      const response = await request(app)
        .post("/api/docx-edit/text-to-docx-template")
        .send(validTemplateRequest);

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain("Template error");
    });
  });

  describe("POST /api/docx-edit/text-to-docx", () => {
    const validTextRequest = {
      text: "This is the document content",
      title: "Test Document",
    };

    it("should require authentication", async () => {
      (validatedRequest as jest.Mock).mockImplementation((req, res) => {
        res.status(401).json({ error: "No auth token found." });
      });

      const response = await request(app)
        .post("/api/docx-edit/text-to-docx")
        .send(validTextRequest);

      expect(response.status).toBe(401);
      expect(response.body.error).toBe("No auth token found.");
    });

    it("should return 400 if text is missing", async () => {
      const response = await request(app)
        .post("/api/docx-edit/text-to-docx")
        .send({ title: "Title" });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe("Missing required parameters");
    });

    it("should successfully convert text to DOCX without system template", async () => {
      (SystemSettings.get as jest.Mock).mockResolvedValue(null);
      (textToDocx as jest.Mock).mockResolvedValue(undefined);

      const response = await request(app)
        .post("/api/docx-edit/text-to-docx")
        .send(validTextRequest);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain("converted to DOCX successfully");
      expect(response.body.data).toEqual({
        sessionId: "test-session-id",
        filename: expect.stringContaining(".docx"),
      });

      expect(textToDocx as jest.Mock).toHaveBeenCalledWith(
        "This is the document content",
        expect.any(String),
        { title: "Test Document" }
      );
    });

    it("should use system template if configured", async () => {
      (SystemSettings.get as jest.Mock).mockResolvedValue({
        value: "system-template.docx",
      });
      (textToDocx as jest.Mock).mockResolvedValue(undefined);

      // mergeDocxWithTemplate is already mocked at the top level
      const mergeDocxWithTemplate = require("../../utils/docx/mergeDocxWithTemplate");

      const response = await request(app)
        .post("/api/docx-edit/text-to-docx")
        .send(validTextRequest);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // Verify textToDocx was called for creating the body
      expect(textToDocx).toHaveBeenCalled();

      // Verify merge was called
      expect(mergeDocxWithTemplate).toHaveBeenCalled();

      // Verify temporary file cleanup
      expect(fs.unlinkSync).toHaveBeenCalled();
    });

    it("should use default title if not provided", async () => {
      (SystemSettings.get as jest.Mock).mockResolvedValue(null);
      (textToDocx as jest.Mock).mockResolvedValue(undefined);

      const response = await request(app)
        .post("/api/docx-edit/text-to-docx")
        .send({ text: "Content" });

      expect(response.status).toBe(200);
      expect(textToDocx as jest.Mock).toHaveBeenCalledWith(
        "Content",
        expect.any(String),
        { title: expect.any(String) }
      );
    });

    it("should handle errors during conversion", async () => {
      (SystemSettings.get as jest.Mock).mockResolvedValue(null);
      (textToDocx as jest.Mock).mockRejectedValue(
        new Error("Conversion error")
      );

      const response = await request(app)
        .post("/api/docx-edit/text-to-docx")
        .send(validTextRequest);

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain("Conversion error");
    });
  });

  describe("Error handling and edge cases", () => {
    it("should handle null router gracefully", () => {
      expect(() => docxEditEndpoints(null as unknown as Router)).not.toThrow();
    });

    it("should create docx-edit directory if it does not exist", () => {
      (fs.existsSync as jest.Mock).mockReturnValue(false);

      const newApp = express();
      const newRouter = express.Router();
      newApp.use("/api", newRouter);

      docxEditEndpoints(newRouter);

      expect(fs.mkdirSync).toHaveBeenCalledWith(
        expect.stringContaining("docx-edit"),
        { recursive: true }
      );
    });
  });
});
