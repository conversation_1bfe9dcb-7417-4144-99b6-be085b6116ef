import request from "supertest";
import express from "express";
import {
  addGenerateLegalTaskPromptEndpoint,
  generateLegalTaskPrompt,
} from "../generateLegalTaskPrompt";
import { validatedRequest } from "../../utils/middleware/validatedRequest";
import { flexUserRoleValid } from "../../utils/middleware/multiUserProtected";
import { userFromSession } from "../../utils/http";
import { Workspace } from "../../models/workspace";

// Mock all dependencies
jest.mock("../../utils/helpers", () => ({
  getLLMProvider: jest.fn(),
}));
jest.mock("../../utils/middleware/validatedRequest");
jest.mock("../../utils/middleware/multiUserProtected");
jest.mock("../../utils/http");
jest.mock("../../models/workspace");
jest.mock("../../utils/helpers/supportFunctions", () => ({
  getPromptUpgradeLLM: jest.fn(),
}));

// Import after mocking
import { getLLMProvider } from "../../utils/helpers";
import { getPromptUpgradeLLM } from "../../utils/helpers/supportFunctions";

describe("Generate Legal Task Prompt", () => {
  let app: express.Application;
  const apiRouter = express.Router();

  const mockUser = {
    id: 1,
    username: "testuser",
    role: "admin",
  };

  const mockWorkspace = {
    id: 1,
    chatProvider: "openai",
    chatModel: "gpt-4",
  };

  const mockLLMProvider = {
    model: "gpt-4",
    promptWindowLimit: jest.fn().mockReturnValue(4096),
    isValidChatCompletionModel: jest.fn().mockReturnValue(true),
    getChatCompletion: jest.fn().mockResolvedValue({
      textResponse: "Generated legal task prompt for the given description.",
    }),
    constructor: { name: "OpenAI" },
  };

  let originalEnv: typeof process.env;

  beforeEach(() => {
    // Store original environment
    originalEnv = { ...process.env };

    // Set up test environment variables
    process.env.LLM_PROVIDER = "openai";
    process.env.LLM_PROVIDER_PU = "openai";
    process.env.LLM_PROVIDER_SUPPORT = "openai";

    jest.clearAllMocks();

    app = express();
    app.use(express.json());
    app.use("/api", apiRouter);

    // Mock middleware to pass through
    (validatedRequest as jest.Mock).mockImplementation((req, res, next) =>
      next()
    );
    (flexUserRoleValid as jest.Mock).mockReturnValue(
      (req: any, res: any, next: any) => next()
    );
    (userFromSession as jest.Mock).mockResolvedValue(mockUser);

    // Reset mock LLM provider for each test
    mockLLMProvider.getChatCompletion.mockReset();
    mockLLMProvider.getChatCompletion.mockResolvedValue({
      textResponse: "Generated legal task prompt for the given description.",
    });
    mockLLMProvider.isValidChatCompletionModel.mockReset();
    mockLLMProvider.isValidChatCompletionModel.mockReturnValue(true);
    mockLLMProvider.promptWindowLimit.mockReset();
    mockLLMProvider.promptWindowLimit.mockReturnValue(4096);

    // Mock LLM provider functions
    (getLLMProvider as jest.Mock).mockReturnValue(mockLLMProvider);
    (getPromptUpgradeLLM as jest.Mock).mockImplementation(
      async (provider) => provider || mockLLMProvider
    );
    (Workspace.get as jest.Mock).mockResolvedValue(mockWorkspace);

    // Initialize endpoints
    addGenerateLegalTaskPromptEndpoint(apiRouter);
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
    jest.clearAllMocks();
  });

  describe("POST /api/generate-legal-task-prompt", () => {
    // Re-initialize app for each test to avoid middleware conflicts
    beforeEach(() => {
      app = express();
      app.use(express.json());
      app.use("/api", apiRouter);
      addGenerateLegalTaskPromptEndpoint(apiRouter);
    });
    it("should generate legal task prompt with task description", async () => {
      const requestBody = {
        taskDescription:
          "Create a contract review prompt for employment agreements",
        legalTaskType: "noMainDoc",
      };

      const response = await request(app)
        .post("/api/generate-legal-task-prompt")
        .send(requestBody);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.prompt).toBe(
        "Generated legal task prompt for the given description."
      );
      expect(mockLLMProvider.getChatCompletion).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({ role: "system" }),
          expect.objectContaining({
            role: "user",
            content: requestBody.taskDescription,
          }),
        ]),
        expect.objectContaining({
          temperature: 0.2,
          maxTokens: expect.any(Number),
        })
      );
    });

    it("should generate legal task prompt with prompt template", async () => {
      const requestBody = {
        promptTemplate:
          "Analyze the provided legal documents for compliance issues",
        legalTaskType: "referenceFiles",
      };

      const response = await request(app)
        .post("/api/generate-legal-task-prompt")
        .send(requestBody);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.prompt).toBeDefined();
      expect(mockLLMProvider.getChatCompletion).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            content: expect.stringContaining("Reference Files Flow"),
          }),
          expect.objectContaining({ content: requestBody.promptTemplate }),
        ]),
        expect.any(Object)
      );
    });

    it("should use workspace LLM when requested", async () => {
      const requestBody = {
        taskDescription: "Review contract terms",
        workspaceId: "1",
        useWorkspaceLLM: true,
        legalTaskType: "mainDoc",
      };

      const response = await request(app)
        .post("/api/generate-legal-task-prompt")
        .send(requestBody);

      expect(response.status).toBe(200);
      expect(Workspace.get).toHaveBeenCalledWith({ id: 1 });
      expect(getLLMProvider).toHaveBeenCalledWith({
        provider: mockWorkspace.chatProvider,
        model: mockWorkspace.chatModel,
      });
    });

    it("should handle missing task description and prompt template", async () => {
      const response = await request(app)
        .post("/api/generate-legal-task-prompt")
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe(
        "Task description or prompt template is required"
      );
    });

    it("should handle workspace not found", async () => {
      (Workspace.get as jest.Mock).mockResolvedValue(null);

      const requestBody = {
        taskDescription: "Review contract terms",
        workspaceId: "999",
        useWorkspaceLLM: true,
      };

      const response = await request(app)
        .post("/api/generate-legal-task-prompt")
        .send(requestBody);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe("Workspace not found.");
    });

    it("should handle LLM provider errors", async () => {
      mockLLMProvider.getChatCompletion.mockRejectedValue(
        new Error("LLM API error")
      );

      const requestBody = {
        taskDescription: "Review contract terms",
      };

      const response = await request(app)
        .post("/api/generate-legal-task-prompt")
        .send(requestBody);

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain("LLM API error");
    });

    it("should handle different legal task types", async () => {
      const taskTypes = ["mainDoc", "noMainDoc", "referenceFiles", "custom"];

      for (const taskType of taskTypes) {
        const requestBody = {
          taskDescription: `Test task for ${taskType}`,
          legalTaskType: taskType,
        };

        const response = await request(app)
          .post("/api/generate-legal-task-prompt")
          .send(requestBody);

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);

        // Verify the correct system message was used
        const systemMessage = mockLLMProvider.getChatCompletion.mock.calls.find(
          (call) => call[0][1].content === requestBody.taskDescription
        )?.[0]?.[0]?.content;

        if (taskType === "mainDoc") {
          expect(systemMessage).toContain("Main Document Flow");
        } else if (taskType === "noMainDoc") {
          expect(systemMessage).toContain("No Main Document Flow");
        } else if (taskType === "referenceFiles") {
          expect(systemMessage).toContain("Reference Files Flow");
        } else {
          expect(systemMessage).toContain(
            "legal expert who creates effective prompts"
          );
        }
      }
    });

    it("should require appropriate user roles", async () => {
      // Mock the flexUserRoleValid to return a middleware that rejects
      (flexUserRoleValid as jest.Mock).mockReturnValueOnce(
        (req: any, res: any, _next: any) => {
          res.status(403).json({ error: "Insufficient permissions" });
          // Don't call next() when sending response
        }
      );

      // Create fresh router and app with the new middleware mock
      const freshApiRouter = express.Router();
      const freshApp = express();
      freshApp.use(express.json());
      freshApp.use("/api", freshApiRouter);
      addGenerateLegalTaskPromptEndpoint(freshApiRouter);

      const response = await request(freshApp)
        .post("/api/generate-legal-task-prompt")
        .send({ taskDescription: "Test task" });

      expect(response.status).toBe(403);
    });

    it("should handle authentication requirement", async () => {
      (validatedRequest as jest.Mock).mockImplementationOnce((req, res) => {
        res.status(401).json({ error: "No auth token found." });
        // Don't call next() when sending response
      });

      // Create fresh router and app with the new middleware mock
      const freshApiRouter = express.Router();
      const freshApp = express();
      freshApp.use(express.json());
      freshApp.use("/api", freshApiRouter);
      addGenerateLegalTaskPromptEndpoint(freshApiRouter);

      const response = await request(freshApp)
        .post("/api/generate-legal-task-prompt")
        .send({ taskDescription: "Test task" });

      expect(response.status).toBe(401);
    });
  });

  describe("generateLegalTaskPrompt function", () => {
    it("should generate prompt with provided LLM provider", async () => {
      const result = await generateLegalTaskPrompt(
        "Test task description",
        mockLLMProvider as any,
        "noMainDoc"
      );

      expect(result).toBe(
        "Generated legal task prompt for the given description."
      );
      expect(mockLLMProvider.getChatCompletion).toHaveBeenCalled();
    });

    it("should use environment LLM_PROVIDER_PU when no provider given", async () => {
      const result = await generateLegalTaskPrompt(
        "Test task description",
        null,
        "mainDoc"
      );

      expect(result).toBe(
        "Generated legal task prompt for the given description."
      );
      expect(getLLMProvider).toHaveBeenCalledWith({
        provider: "openai",
        settings_suffix: "_PU",
      });
    });

    it("should fallback to LLM_PROVIDER when LLM_PROVIDER_PU fails", async () => {
      (getLLMProvider as jest.Mock)
        .mockReturnValueOnce(null) // First call fails
        .mockReturnValueOnce(mockLLMProvider); // Second call succeeds

      const result = await generateLegalTaskPrompt(
        "Test task description",
        null,
        "noMainDoc"
      );

      expect(result).toBe(
        "Generated legal task prompt for the given description."
      );
      expect(getLLMProvider).toHaveBeenCalledTimes(2);
    });

    it("should handle system-standard provider", async () => {
      process.env.LLM_PROVIDER_PU = "system-standard";
      process.env.LLM_PROVIDER = "openai";

      const result = await generateLegalTaskPrompt(
        "Test task description",
        null,
        "noMainDoc"
      );

      expect(result).toBe(
        "Generated legal task prompt for the given description."
      );
      expect(getLLMProvider).toHaveBeenCalledWith({ provider: "openai" });
    });

    it("should throw error when no LLM provider available", async () => {
      (getLLMProvider as jest.Mock).mockReturnValue(null);
      (getPromptUpgradeLLM as jest.Mock).mockResolvedValue(null);

      await expect(
        generateLegalTaskPrompt("Test task description", null, "noMainDoc")
      ).rejects.toThrow("Could not initialize LLM provider");
    });

    it("should throw error for invalid chat completion model", async () => {
      mockLLMProvider.isValidChatCompletionModel.mockReturnValue(false);

      await expect(
        generateLegalTaskPrompt(
          "Test task description",
          mockLLMProvider as any,
          "noMainDoc"
        )
      ).rejects.toThrow("is not valid for chat completion");
    });

    it("should use correct temperature and token limits", async () => {
      await generateLegalTaskPrompt(
        "Test task description",
        mockLLMProvider as any,
        "noMainDoc"
      );

      expect(mockLLMProvider.getChatCompletion).toHaveBeenCalledWith(
        expect.any(Array),
        expect.objectContaining({
          temperature: 0.2,
          maxTokens: Math.floor(4096 * 0.6), // 60% of prompt window limit
        })
      );
    });

    it("should handle empty LLM response", async () => {
      mockLLMProvider.getChatCompletion.mockResolvedValue({ textResponse: "" });

      const result = await generateLegalTaskPrompt(
        "Test task description",
        mockLLMProvider as any,
        "noMainDoc"
      );

      expect(result).toBe("");
    });

    it("should handle null LLM response", async () => {
      mockLLMProvider.getChatCompletion.mockResolvedValue(null);

      const result = await generateLegalTaskPrompt(
        "Test task description",
        mockLLMProvider as any,
        "noMainDoc"
      );

      expect(result).toBe("");
    });

    it("should use support LLM when available", async () => {
      const supportLLM = { ...mockLLMProvider };
      (getPromptUpgradeLLM as jest.Mock).mockResolvedValue(supportLLM);

      await generateLegalTaskPrompt("Test task description", null, "noMainDoc");

      expect(getPromptUpgradeLLM).toHaveBeenCalled();
    });
  });

  describe("Edge cases and error handling", () => {
    it("should handle very long task descriptions", async () => {
      const longDescription = "x".repeat(10000);

      const response = await request(app)
        .post("/api/generate-legal-task-prompt")
        .send({ taskDescription: longDescription });

      expect(response.status).toBe(200);
      expect(mockLLMProvider.getChatCompletion).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({ content: longDescription }),
        ]),
        expect.any(Object)
      );
    });

    it("should handle special characters in task description", async () => {
      const specialCharsDescription =
        "Review contract with § symbols, €1000 clause, and 'quotes'";

      const response = await request(app)
        .post("/api/generate-legal-task-prompt")
        .send({ taskDescription: specialCharsDescription });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it("should handle malformed JSON request", async () => {
      const response = await request(app)
        .post("/api/generate-legal-task-prompt")
        .set("Content-Type", "application/json")
        .send("invalid json");

      expect(response.status).toBe(400);
    });

    it("should handle missing environment variables", async () => {
      delete process.env.LLM_PROVIDER;
      delete process.env.LLM_PROVIDER_PU;
      (getLLMProvider as jest.Mock).mockReturnValue(null);
      (getPromptUpgradeLLM as jest.Mock).mockResolvedValue(null);

      const response = await request(app)
        .post("/api/generate-legal-task-prompt")
        .send({ taskDescription: "Test task" });

      expect(response.status).toBe(500);
      expect(response.body.error).toContain(
        "Could not initialize LLM provider"
      );
    });

    it("should handle workspace LLM provider initialization failure", async () => {
      (getLLMProvider as jest.Mock).mockReturnValue(null);
      (getPromptUpgradeLLM as jest.Mock).mockResolvedValue(null);

      const response = await request(app)
        .post("/api/generate-legal-task-prompt")
        .send({
          taskDescription: "Test task",
          workspaceId: "1",
          useWorkspaceLLM: true,
        });

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
    });

    it("should handle invalid workspace ID", async () => {
      const response = await request(app)
        .post("/api/generate-legal-task-prompt")
        .send({
          taskDescription: "Test task",
          workspaceId: "invalid",
          useWorkspaceLLM: true,
        });

      expect(response.status).toBe(200); // Still generates prompt with default LLM
    });

    it("should handle concurrent requests", async () => {
      const requests = Array(5)
        .fill(null)
        .map(() =>
          request(app)
            .post("/api/generate-legal-task-prompt")
            .send({ taskDescription: "Concurrent test task" })
        );

      const responses = await Promise.all(requests);
      responses.forEach((response) => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });
    });

    it("should handle LLM timeout errors", async () => {
      mockLLMProvider.getChatCompletion.mockRejectedValue(
        new Error("Request timeout")
      );

      const response = await request(app)
        .post("/api/generate-legal-task-prompt")
        .send({ taskDescription: "Test task" });

      expect(response.status).toBe(500);
      expect(response.body.error).toContain("Request timeout");
    });

    it("should handle LLM rate limiting", async () => {
      const rateLimitError = new Error("Rate limit exceeded");
      rateLimitError.name = "RateLimitError";
      mockLLMProvider.getChatCompletion.mockRejectedValue(rateLimitError);

      const response = await request(app)
        .post("/api/generate-legal-task-prompt")
        .send({ taskDescription: "Test task" });

      expect(response.status).toBe(500);
      expect(response.body.error).toContain("Rate limit exceeded");
    });

    it("should handle database connection errors", async () => {
      (Workspace.get as jest.Mock).mockRejectedValue(
        new Error("Database connection failed")
      );

      const response = await request(app)
        .post("/api/generate-legal-task-prompt")
        .send({
          taskDescription: "Test task",
          workspaceId: "1",
          useWorkspaceLLM: true,
        });

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
    });
  });

  describe("Legal task types validation", () => {
    it("should handle all supported legal task types", async () => {
      const taskTypes = [
        { type: "mainDoc", expectedContent: "Main Document Flow" },
        { type: "noMainDoc", expectedContent: "No Main Document Flow" },
        { type: "referenceFiles", expectedContent: "Reference Files Flow" },
        {
          type: "custom",
          expectedContent: "legal expert who creates effective prompts",
        },
      ];

      for (const { type, expectedContent } of taskTypes) {
        jest.clearAllMocks();

        const response = await request(app)
          .post("/api/generate-legal-task-prompt")
          .send({
            taskDescription: `Test for ${type}`,
            legalTaskType: type,
          });

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);

        // Verify correct system message was used
        const calls = mockLLMProvider.getChatCompletion.mock.calls;
        const systemMessage = calls[0]?.[0]?.[0]?.content;
        expect(systemMessage).toContain(expectedContent);
      }
    });

    it("should default to noMainDoc when no legal task type provided", async () => {
      const response = await request(app)
        .post("/api/generate-legal-task-prompt")
        .send({ taskDescription: "Test without task type" });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      const systemMessage =
        mockLLMProvider.getChatCompletion.mock.calls[0]?.[0]?.[0]?.content;
      expect(systemMessage).toContain("No Main Document Flow");
    });

    it("should handle undefined legal task type", async () => {
      const response = await request(app)
        .post("/api/generate-legal-task-prompt")
        .send({
          taskDescription: "Test with undefined task type",
          legalTaskType: undefined,
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });
  });

  describe("Provider fallback logic", () => {
    it("should warn when LLM_PROVIDER_PU is not set", async () => {
      delete process.env.LLM_PROVIDER_PU;
      const consoleSpy = jest.spyOn(console, "warn").mockImplementation();

      await generateLegalTaskPrompt("Test task", null, "noMainDoc");

      expect(consoleSpy).toHaveBeenCalledWith(
        "LLM_PROVIDER_PU not set. Falling back to LLM_PROVIDER for prompt upgrade."
      );

      consoleSpy.mockRestore();
    });

    it("should warn when LLM_PROVIDER_PU fails to initialize", async () => {
      process.env.LLM_PROVIDER_PU = "invalid-provider";
      (getLLMProvider as jest.Mock)
        .mockReturnValueOnce(null) // PU provider fails
        .mockReturnValueOnce(mockLLMProvider); // Fallback succeeds

      const consoleSpy = jest.spyOn(console, "warn").mockImplementation();

      await generateLegalTaskPrompt("Test task", null, "noMainDoc");

      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to initialize LLM_PROVIDER_PU ('invalid-provider'). Attempting fallback."
      );

      consoleSpy.mockRestore();
    });

    it("should handle system-standard in fallback scenario", async () => {
      process.env.LLM_PROVIDER_PU = "invalid-provider";
      process.env.LLM_PROVIDER = "system-standard";
      (getLLMProvider as jest.Mock)
        .mockReturnValueOnce(null) // PU provider fails
        .mockReturnValueOnce(mockLLMProvider); // Fallback with system-standard succeeds

      const consoleSpy = jest.spyOn(console, "log").mockImplementation();

      await generateLegalTaskPrompt("Test task", null, "noMainDoc");

      expect(consoleSpy).toHaveBeenCalledWith(
        "GenerateLegalTaskPrompt fallback using system-standard, resolving to default LLM."
      );

      consoleSpy.mockRestore();
    });
  });
});
