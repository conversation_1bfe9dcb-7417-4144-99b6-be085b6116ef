import request from "supertest";
import { jest } from "@jest/globals";
import express from "express";
import { newsEndpoints } from "../news";
import { NewsMessage } from "../../models/newsMessage";
import { validatedRequest } from "../../utils/middleware/validatedRequest";
import { requireAdminRole } from "../../utils/middleware/requireAdminRole";
import { FilteredUser } from "../../types/models";
import type {
  NewsMessageData,
  DismissR<PERSON>ult,
  CreateResult,
  UpdateResult,
  DeleteResult,
} from "../../models/newsMessage";

// Mock dependencies
jest.mock("../../models/newsMessage");
jest.mock("../../utils/middleware/validatedRequest");
jest.mock("../../utils/middleware/requireAdminRole");
jest.mock("../../data/systemNewsItems", () => ({
  getActiveSystemNews: jest.fn().mockReturnValue([
    { id: "system-1", title: "System News 1", priority: "high" },
    { id: "system-2", title: "System News 2", priority: "medium" },
  ]),
  getSystemNewsForRoles: jest
    .fn()
    .mockReturnValue([
      { id: "system-1", title: "System News 1", priority: "high" },
    ]),
}));

// Mock request/response locals - using partial type since test doesn't need all fields
const mockUser = {
  id: 1,
  username: "testuser",
  email: "<EMAIL>",
  role: "admin",
  pfpFilename: null,
  suspended: 0,
} as FilteredUser;

// Setup express app with mocked middleware
const setupApp = () => {
  const app = express();
  app.use(express.json());

  // Mock validatedRequest middleware
  (validatedRequest as jest.MockedFunction<any>).mockImplementation(
    (_req: any, res: any, next: any) => {
      res.locals.user = mockUser;
      next();
    }
  );

  // Mock requireAdminRole middleware
  (requireAdminRole as jest.MockedFunction<any>).mockImplementation(
    (_req: any, _res: any, next: any) => {
      next();
    }
  );

  // Register endpoints
  newsEndpoints(app);

  return app;
};

describe("News Endpoints", () => {
  let app: express.Express;
  const mockedNewsMessage = NewsMessage as jest.Mocked<typeof NewsMessage>;

  beforeEach(() => {
    jest.clearAllMocks();
    app = setupApp();
  });

  describe("GET /news/unread", () => {
    it("should return unread news for the current user", async () => {
      const mockUnreadNews: NewsMessageData[] = [
        {
          id: 1,
          title: "News 1",
          content: "Content 1",
          priority: "high" as const,
          is_active: true,
          target_roles: '["admin"]',
          expires_at: null,
          created_by: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 2,
          title: "News 2",
          content: "Content 2",
          priority: "medium" as const,
          is_active: true,
          target_roles: null,
          expires_at: null,
          created_by: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];
      mockedNewsMessage.getUnreadForUser.mockResolvedValue(mockUnreadNews);

      const response = await request(app).get("/news/unread");

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.news).toHaveLength(2);

      // Check the structure of the first news item, allowing for date serialization
      expect(response.body.news[0]).toMatchObject({
        id: 1,
        title: "News 1",
        content: "Content 1",
        priority: "high",
        is_active: true,
        target_roles: '["admin"]',
        expires_at: null,
        created_by: 1,
      });

      // Check that dates exist (could be Date objects or strings)
      expect(response.body.news[0].createdAt).toBeDefined();
      expect(response.body.news[0].updatedAt).toBeDefined();
      expect(mockedNewsMessage.getUnreadForUser).toHaveBeenCalledWith(
        mockUser.id
      );
    });

    it("should handle errors when fetching unread news", async () => {
      mockedNewsMessage.getUnreadForUser.mockRejectedValue(
        new Error("Database error")
      );

      const response = await request(app).get("/news/unread");

      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        success: false,
        message: "Failed to fetch unread news",
      });
    });
  });

  describe("GET /news/dismissed-system", () => {
    it("should return dismissed system news IDs", async () => {
      const mockDismissedIds = ["system-1", "system-3"];
      mockedNewsMessage.getDismissedSystemNewsIds.mockResolvedValue(
        mockDismissedIds
      );

      const response = await request(app).get("/news/dismissed-system");

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        dismissedIds: mockDismissedIds,
      });
      expect(mockedNewsMessage.getDismissedSystemNewsIds).toHaveBeenCalledWith(
        mockUser.id
      );
    });

    it("should handle errors when fetching dismissed system news", async () => {
      mockedNewsMessage.getDismissedSystemNewsIds.mockRejectedValue(
        new Error("Database error")
      );

      const response = await request(app).get("/news/dismissed-system");

      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        success: false,
        message: "Failed to fetch dismissed system news",
      });
    });
  });

  describe("POST /news/:newsId/view", () => {
    it("should mark news as viewed successfully", async () => {
      const mockDismissal = {
        id: 1,
        user_id: 1,
        news_id: "1",
        dismissed_at: new Date(),
      };
      mockedNewsMessage.markAsViewed.mockResolvedValue({
        dismissal: mockDismissal,
        message: null,
      } as DismissResult);

      const response = await request(app).post("/news/1/view");

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        dismissal: expect.objectContaining({ id: 1 }),
      });
      expect(mockedNewsMessage.markAsViewed).toHaveBeenCalledWith(
        mockUser.id,
        1
      );
    });

    it("should return 400 for invalid newsId", async () => {
      const response = await request(app).post("/news/invalid/view");

      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        success: false,
        message: "newsId must be a valid positive integer",
      });
    });

    it("should return error message from model", async () => {
      mockedNewsMessage.markAsViewed.mockResolvedValue({
        dismissal: null,
        message: "News not found",
      } as DismissResult);

      const response = await request(app).post("/news/999/view");

      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        success: false,
        message: "News not found",
      });
    });

    it("should handle server errors", async () => {
      mockedNewsMessage.markAsViewed.mockRejectedValue(
        new Error("Database error")
      );

      const response = await request(app).post("/news/1/view");

      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        success: false,
        message: "Failed to mark news as viewed",
      });
    });
  });

  describe("POST /news/:newsId/dismiss", () => {
    it("should dismiss news successfully", async () => {
      const mockDismissal = {
        id: 1,
        user_id: 1,
        news_id: "1",
        dismissed_at: new Date(),
      };
      mockedNewsMessage.dismiss.mockResolvedValue({
        dismissal: mockDismissal,
        message: null,
      } as DismissResult);

      const response = await request(app).post("/news/1/dismiss");

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        dismissal: expect.objectContaining({ id: 1 }),
      });
      expect(mockedNewsMessage.dismiss).toHaveBeenCalledWith(
        mockUser.id,
        1,
        false
      );
    });

    it("should return 400 for invalid newsId", async () => {
      const response = await request(app).post("/news/-1/dismiss");

      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        success: false,
        message: "newsId must be a valid positive integer",
      });
    });

    it("should handle errors", async () => {
      mockedNewsMessage.dismiss.mockRejectedValue(new Error("Database error"));

      const response = await request(app).post("/news/1/dismiss");

      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        success: false,
        message: "Failed to dismiss news",
      });
    });
  });

  describe("POST /news/system/:systemNewsId/dismiss", () => {
    it("should dismiss system news with numeric ID", async () => {
      const mockDismissal = {
        id: 1,
        user_id: 1,
        news_id: "100",
        dismissed_at: new Date(),
      };
      mockedNewsMessage.dismiss.mockResolvedValue({
        dismissal: mockDismissal,
        message: null,
      } as DismissResult);

      const response = await request(app).post("/news/system/100/dismiss");

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        message: "System news dismissed successfully",
        dismissal: expect.objectContaining({ id: 1 }),
      });
      expect(mockedNewsMessage.dismiss).toHaveBeenCalledWith(
        mockUser.id,
        100,
        true
      );
    });

    it("should dismiss system news with string ID", async () => {
      const mockDismissal = {
        id: 1,
        user_id: 1,
        news_id: "system-news-1",
        dismissed_at: new Date(),
      };
      mockedNewsMessage.dismiss.mockResolvedValue({
        dismissal: mockDismissal,
        message: null,
      } as DismissResult);

      const response = await request(app).post(
        "/news/system/system-news-1/dismiss"
      );

      expect(response.status).toBe(200);
      expect(mockedNewsMessage.dismiss).toHaveBeenCalledWith(
        mockUser.id,
        "system-news-1",
        true
      );
    });

    it("should return 400 for invalid systemNewsId", async () => {
      const response = await request(app).post("/news/system/inv@lid/dismiss");

      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        success: false,
        message:
          "systemNewsId must be a valid positive integer or a valid string identifier",
      });
    });
  });

  describe("GET /news/system", () => {
    it("should return system news list", async () => {
      const response = await request(app).get("/news/system");

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        systemNews: [
          { id: "system-1", title: "System News 1", priority: "high" },
          { id: "system-2", title: "System News 2", priority: "medium" },
        ],
      });
    });

    it("should handle errors when fetching system news", async () => {
      // Import the mocked module to access its methods
      const { getActiveSystemNews } = await import(
        "../../data/systemNewsItems"
      );

      // Temporarily make the function throw an error
      const mockGetActiveSystemNews =
        getActiveSystemNews as jest.MockedFunction<typeof getActiveSystemNews>;
      mockGetActiveSystemNews.mockImplementationOnce(() => {
        throw new Error("Module load error");
      });

      const response = await request(app).get("/news/system");

      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        success: false,
        message: "Failed to fetch system news",
      });
    });
  });

  describe("GET /news/all-active", () => {
    it("should return all active news sorted by priority", async () => {
      const mockDatabaseNews = [
        {
          id: 1,
          title: "DB News 1",
          content: "Content 1",
          priority: "low" as const,
          is_active: true,
          target_roles: null,
          expires_at: null,
          created_by: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
          isSystemNews: false,
          isDismissed: false,
          dismissedAt: null,
        },
        {
          id: 2,
          title: "DB News 2",
          content: "Content 2",
          priority: "urgent" as const,
          is_active: true,
          target_roles: null,
          expires_at: null,
          created_by: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
          isSystemNews: false,
          isDismissed: false,
          dismissedAt: null,
        },
      ];
      mockedNewsMessage.getAllActiveForUser.mockResolvedValue(mockDatabaseNews);
      mockedNewsMessage.getDismissedNewsIds.mockResolvedValue({
        system: ["system-2"],
        local: [],
      });

      const response = await request(app).get("/news/all-active");

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.news).toHaveLength(3); // 2 DB + 1 system news
      expect(response.body.news[0].priority).toBe("urgent"); // Highest priority first
    });

    it("should handle errors", async () => {
      mockedNewsMessage.getAllActiveForUser.mockRejectedValue(
        new Error("Database error")
      );

      const response = await request(app).get("/news/all-active");

      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        success: false,
        message: "Failed to fetch all active news",
      });
    });
  });

  describe("Admin Endpoints", () => {
    describe("GET /admin/news", () => {
      it("should return all news for admin", async () => {
        const mockAllNews: NewsMessageData[] = [
          {
            id: 1,
            title: "News 1",
            content: "Content 1",
            priority: "high" as const,
            is_active: true,
            target_roles: '["admin", "user"]',
            expires_at: null,
            created_by: 1,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            id: 2,
            title: "News 2",
            content: "Content 2",
            priority: "medium" as const,
            is_active: false,
            target_roles: null,
            expires_at: new Date(),
            created_by: 2,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ];
        mockedNewsMessage.getAll.mockResolvedValue(mockAllNews);

        const response = await request(app).get("/admin/news");

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.news).toHaveLength(2);
        expect(response.body.news[0]).toHaveProperty("isActive", true);
        expect(response.body.news[0]).toHaveProperty("targetRoles", [
          "admin",
          "user",
        ]);
      });

      it("should handle errors", async () => {
        mockedNewsMessage.getAll.mockRejectedValue(new Error("Database error"));

        const response = await request(app).get("/admin/news");

        expect(response.status).toBe(500);
        expect(response.body).toEqual({
          success: false,
          message: "Failed to fetch news",
        });
      });
    });

    describe("POST /admin/news", () => {
      it("should create news successfully", async () => {
        const mockCreatedNews: NewsMessageData = {
          id: 1,
          title: "New News",
          content: "New Content",
          priority: "high" as const,
          is_active: true,
          target_roles: '["admin"]',
          expires_at: null,
          created_by: mockUser.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        mockedNewsMessage.create.mockResolvedValue({
          newsMessage: mockCreatedNews,
          message: null,
        } as CreateResult);

        const response = await request(app)
          .post("/admin/news")
          .send({
            title: "New News",
            content: "New Content",
            priority: "high",
            targetRoles: ["admin"],
          });

        expect(response.status).toBe(201);
        expect(response.body).toEqual({
          success: true,
          newsMessage: expect.objectContaining({
            title: "New News",
            isActive: true,
            targetRoles: ["admin"],
          }),
        });
      });

      it("should return 400 for missing required fields", async () => {
        const response = await request(app)
          .post("/admin/news")
          .send({ title: "Only Title" });

        expect(response.status).toBe(400);
        expect(response.body).toEqual({
          success: false,
          message: "Title and content are required",
        });
      });

      it("should handle creation errors", async () => {
        mockedNewsMessage.create.mockResolvedValue({
          newsMessage: null,
          message: "Creation failed",
        } as CreateResult);

        const response = await request(app).post("/admin/news").send({
          title: "New News",
          content: "New Content",
        });

        expect(response.status).toBe(400);
        expect(response.body).toEqual({
          success: false,
          message: "Creation failed",
        });
      });

      it("should handle server errors", async () => {
        mockedNewsMessage.create.mockRejectedValue(new Error("Database error"));

        const response = await request(app).post("/admin/news").send({
          title: "New News",
          content: "New Content",
        });

        expect(response.status).toBe(500);
        expect(response.body).toEqual({
          success: false,
          message: "Failed to create news",
        });
      });
    });

    describe("PUT /admin/news/:newsId", () => {
      it("should update news successfully", async () => {
        const mockUpdatedNews: NewsMessageData = {
          id: 1,
          title: "Updated News",
          content: "Updated Content",
          priority: "high" as const,
          is_active: true,
          target_roles: null,
          expires_at: null,
          created_by: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        mockedNewsMessage.update.mockResolvedValue({
          newsMessage: mockUpdatedNews,
          message: null,
        } as UpdateResult);

        const response = await request(app).put("/admin/news/1").send({
          title: "Updated News",
          content: "Updated Content",
        });

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.newsMessage).toMatchObject({
          id: 1,
          title: "Updated News",
          content: "Updated Content",
          priority: "high",
          is_active: true,
          target_roles: null,
          expires_at: null,
          created_by: 1,
        });
        expect(response.body.newsMessage.createdAt).toBeDefined();
        expect(response.body.newsMessage.updatedAt).toBeDefined();
        expect(mockedNewsMessage.update).toHaveBeenCalledWith(1, {
          title: "Updated News",
          content: "Updated Content",
        });
      });

      it("should return 400 for invalid newsId", async () => {
        const response = await request(app).put("/admin/news/invalid").send({
          title: "Updated",
        });

        expect(response.status).toBe(400);
        expect(response.body).toEqual({
          success: false,
          message: "newsId must be a valid positive integer",
        });
      });

      it("should return 404 when news not found", async () => {
        mockedNewsMessage.update.mockResolvedValue({
          newsMessage: null,
          message: null,
        } as UpdateResult);

        const response = await request(app).put("/admin/news/999").send({
          title: "Updated",
        });

        expect(response.status).toBe(404);
        expect(response.body).toEqual({
          success: false,
          message: "News message not found",
        });
      });

      it("should handle update errors", async () => {
        mockedNewsMessage.update.mockResolvedValue({
          newsMessage: null,
          message: "Update failed",
        } as UpdateResult);

        const response = await request(app).put("/admin/news/1").send({
          title: "Updated",
        });

        expect(response.status).toBe(400);
        expect(response.body).toEqual({
          success: false,
          message: "Update failed",
        });
      });
    });

    describe("DELETE /admin/news/:newsId", () => {
      it("should delete news successfully", async () => {
        mockedNewsMessage.delete.mockResolvedValue({
          success: true,
          message: null,
        } as DeleteResult);

        const response = await request(app).delete("/admin/news/1");

        expect(response.status).toBe(200);
        expect(response.body).toEqual({ success: true });
        expect(mockedNewsMessage.delete).toHaveBeenCalledWith(1);
      });

      it("should return 400 for invalid newsId", async () => {
        const response = await request(app).delete("/admin/news/0");

        expect(response.status).toBe(400);
        expect(response.body).toEqual({
          success: false,
          message: "newsId must be a valid positive integer",
        });
      });

      it("should handle deletion errors", async () => {
        mockedNewsMessage.delete.mockRejectedValue(new Error("Database error"));

        const response = await request(app).delete("/admin/news/1");

        expect(response.status).toBe(500);
        expect(response.body).toEqual({
          success: false,
          message: "Failed to delete news",
        });
      });
    });
  });
});
