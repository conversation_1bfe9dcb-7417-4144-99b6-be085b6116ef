import request from "supertest";
import express, { Request, Response, NextFunction } from "express";

// Create a minimal test setup
const app = express();
const router = express.Router();
app.use(express.json());
app.use("/api", router);

// Mock user for authentication
const mockUser = {
  id: 1,
  username: "testuser",
  email: "<EMAIL>",
  role: "admin",
};

// Mock middleware
const mockValidatedRequest = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  res.locals.user = mockUser;
  next();
};

const mockFlexUserRoleValid =
  (_roles: any[]) => (req: Request, res: Response, next: NextFunction) => {
    next();
  };

// Mock SystemSettings
const mockSystemSettings = {
  get: jest.fn(),
};

// Mock EventLogs
const mockEventLogs = {
  logEvent: jest.fn(),
};

// Mock fetch for Slack API calls
const mockFetch = jest.fn();
global.fetch = mockFetch as any;

// Implement the endpoints directly for testing
router.post(
  "/system/test-slack-webhook/bug-report",
  [mockValidatedRequest, mockFlexUserRoleValid([])],
  async (_req: Request, res: Response): Promise<void> => {
    try {
      // Get Slack settings
      const enabledSetting = await mockSystemSettings.get({
        label: "slack_system_reports_enabled",
      });
      const webhookUrlSetting = await mockSystemSettings.get({
        label: "slack_bug_report_webhook_url",
      });
      const instanceNameSetting = await mockSystemSettings.get({
        label: "slack_instance_name",
      });

      if (enabledSetting?.value !== "true") {
        res.status(400).json({
          success: false,
          error: "Slack integration is not enabled",
        });
        return;
      }

      const webhookUrl = webhookUrlSetting?.value;
      if (!webhookUrl) {
        res.status(400).json({
          success: false,
          error: "Bug report webhook URL is not configured",
        });
        return;
      }

      const instanceName = instanceNameSetting?.value || "ISTLegal";

      // Send test message to Slack
      const testMessage = {
        text: `Test bug report from ${instanceName}`,
        blocks: [
          {
            type: "header",
            text: {
              type: "plain_text",
              text: "🐛 Test Bug Report",
              emoji: true,
            },
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `This is a test bug report from *${instanceName}*`,
            },
          },
        ],
      };

      const response = await fetch(webhookUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(testMessage),
      });

      if (!response.ok) {
        throw new Error(`Slack API returned ${response.status}`);
      }

      await mockEventLogs.logEvent(
        "slack_webhook_test",
        {
          type: "bug_report",
          instanceName,
        },
        res.locals.user?.id
      );

      res.status(200).json({
        success: true,
        message: "Test message sent successfully",
      });
    } catch (error: unknown) {
      // Error testing bug report Slack webhook
      res.status(500).json({
        success: false,
        error:
          (error as Error).message || "Failed to send test message to Slack",
      });
    }
  }
);

router.post(
  "/system/test-slack-webhook/autocoding",
  [mockValidatedRequest, mockFlexUserRoleValid([])],
  async (_req: Request, res: Response): Promise<void> => {
    try {
      // Get Slack settings
      const enabledSetting = await mockSystemSettings.get({
        label: "slack_system_reports_enabled",
      });
      const webhookUrlSetting = await mockSystemSettings.get({
        label: "slack_autocoding_webhook_url",
      });
      const instanceNameSetting = await mockSystemSettings.get({
        label: "slack_instance_name",
      });

      if (enabledSetting?.value !== "true") {
        res.status(400).json({
          success: false,
          error: "Slack integration is not enabled",
        });
        return;
      }

      const webhookUrl = webhookUrlSetting?.value;
      if (!webhookUrl) {
        res.status(400).json({
          success: false,
          error: "Autocoding webhook URL is not configured",
        });
        return;
      }

      const instanceName = instanceNameSetting?.value || "ISTLegal";

      // Send test message to Slack
      const testMessage = {
        text: `Test autocoding notification from ${instanceName}`,
        blocks: [
          {
            type: "header",
            text: {
              type: "plain_text",
              text: "🤖 Test Autocoding Notification",
              emoji: true,
            },
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `This is a test autocoding notification from *${instanceName}*`,
            },
          },
        ],
      };

      const response = await fetch(webhookUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(testMessage),
      });

      if (!response.ok) {
        throw new Error(`Slack API returned ${response.status}`);
      }

      await mockEventLogs.logEvent(
        "slack_webhook_test",
        {
          type: "autocoding",
          instanceName,
        },
        res.locals.user?.id
      );

      res.status(200).json({
        success: true,
        message: "Test message sent successfully",
      });
    } catch (error: unknown) {
      // Error testing autocoding Slack webhook
      res.status(500).json({
        success: false,
        error:
          (error as Error).message || "Failed to send test message to Slack",
      });
    }
  }
);

describe("Slack Webhook Test Endpoints", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockFetch.mockClear();
  });

  describe("POST /api/system/test-slack-webhook/bug-report", () => {
    it("should send test bug report webhook successfully", async () => {
      mockSystemSettings.get.mockImplementation(async ({ label }: any) => {
        switch (label) {
          case "slack_system_reports_enabled":
            return { label, value: "true" };
          case "slack_bug_report_webhook_url":
            return {
              label,
              value: "https://hooks.slack.com/services/test/bug/webhook",
            };
          case "slack_instance_name":
            return { label, value: "Test Instance" };
          default:
            return null;
        }
      });

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
      } as any);

      const response = await request(app).post(
        "/api/system/test-slack-webhook/bug-report"
      );

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        message: "Test message sent successfully",
      });

      expect(mockFetch).toHaveBeenCalledWith(
        "https://hooks.slack.com/services/test/bug/webhook",
        expect.objectContaining({
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: expect.stringContaining("Test bug report from Test Instance"),
        })
      );

      expect(mockEventLogs.logEvent).toHaveBeenCalledWith(
        "slack_webhook_test",
        {
          type: "bug_report",
          instanceName: "Test Instance",
        },
        mockUser.id
      );
    });

    it("should return 400 when Slack integration is disabled", async () => {
      mockSystemSettings.get.mockImplementation(async ({ label }: any) => {
        switch (label) {
          case "slack_system_reports_enabled":
            return { label, value: "false" };
          default:
            return null;
        }
      });

      const response = await request(app).post(
        "/api/system/test-slack-webhook/bug-report"
      );

      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        success: false,
        error: "Slack integration is not enabled",
      });

      expect(mockFetch).not.toHaveBeenCalled();
    });

    it("should return 400 when webhook URL is not configured", async () => {
      mockSystemSettings.get.mockImplementation(async ({ label }: any) => {
        switch (label) {
          case "slack_system_reports_enabled":
            return { label, value: "true" };
          case "slack_bug_report_webhook_url":
            return null;
          default:
            return null;
        }
      });

      const response = await request(app).post(
        "/api/system/test-slack-webhook/bug-report"
      );

      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        success: false,
        error: "Bug report webhook URL is not configured",
      });

      expect(mockFetch).not.toHaveBeenCalled();
    });

    it("should handle Slack API errors", async () => {
      mockSystemSettings.get.mockImplementation(async ({ label }: any) => {
        switch (label) {
          case "slack_system_reports_enabled":
            return { label, value: "true" };
          case "slack_bug_report_webhook_url":
            return {
              label,
              value: "https://hooks.slack.com/services/test/bug/webhook",
            };
          case "slack_instance_name":
            return { label, value: "Test Instance" };
          default:
            return null;
        }
      });

      mockFetch.mockResolvedValue({
        ok: false,
        status: 404,
      } as any);

      const response = await request(app).post(
        "/api/system/test-slack-webhook/bug-report"
      );

      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        success: false,
        error: "Slack API returned 404",
      });
    });
  });

  describe("POST /api/system/test-slack-webhook/autocoding", () => {
    it("should send test autocoding webhook successfully", async () => {
      mockSystemSettings.get.mockImplementation(async ({ label }: any) => {
        switch (label) {
          case "slack_system_reports_enabled":
            return { label, value: "true" };
          case "slack_autocoding_webhook_url":
            return {
              label,
              value: "https://hooks.slack.com/services/test/autocoding/webhook",
            };
          case "slack_instance_name":
            return { label, value: "Test Instance" };
          default:
            return null;
        }
      });

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
      } as any);

      const response = await request(app).post(
        "/api/system/test-slack-webhook/autocoding"
      );

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        message: "Test message sent successfully",
      });

      expect(mockFetch).toHaveBeenCalledWith(
        "https://hooks.slack.com/services/test/autocoding/webhook",
        expect.objectContaining({
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: expect.stringContaining(
            "Test autocoding notification from Test Instance"
          ),
        })
      );

      expect(mockEventLogs.logEvent).toHaveBeenCalledWith(
        "slack_webhook_test",
        {
          type: "autocoding",
          instanceName: "Test Instance",
        },
        mockUser.id
      );
    });

    it("should return 400 when autocoding webhook URL is not configured", async () => {
      mockSystemSettings.get.mockImplementation(async ({ label }: any) => {
        switch (label) {
          case "slack_system_reports_enabled":
            return { label, value: "true" };
          case "slack_autocoding_webhook_url":
            return null;
          default:
            return null;
        }
      });

      const response = await request(app).post(
        "/api/system/test-slack-webhook/autocoding"
      );

      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        success: false,
        error: "Autocoding webhook URL is not configured",
      });

      expect(mockFetch).not.toHaveBeenCalled();
    });

    it("should use default instance name when not configured", async () => {
      mockSystemSettings.get.mockImplementation(async ({ label }: any) => {
        switch (label) {
          case "slack_system_reports_enabled":
            return { label, value: "true" };
          case "slack_autocoding_webhook_url":
            return {
              label,
              value: "https://hooks.slack.com/services/test/autocoding/webhook",
            };
          case "slack_instance_name":
            return null;
          default:
            return null;
        }
      });

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
      } as any);

      const response = await request(app).post(
        "/api/system/test-slack-webhook/autocoding"
      );

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        message: "Test message sent successfully",
      });

      expect(mockEventLogs.logEvent).toHaveBeenCalledWith(
        "slack_webhook_test",
        {
          type: "autocoding",
          instanceName: "ISTLegal", // Default value
        },
        mockUser.id
      );
    });
  });
});
