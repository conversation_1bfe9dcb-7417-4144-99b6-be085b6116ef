import express, { Express } from "express";
import request from "supertest";
import { systemReportEndpoints } from "../systemReport";
import { SystemReport } from "../../models/systemReport";
import { validatedRequest } from "../../utils/middleware/validatedRequest";
import { managerOrAdmin } from "../../utils/middleware/managerOrAdmin";

// Mock dependencies
jest.mock("../../models/systemReport", () => ({
  SystemReport: {
    getAll: jest.fn(),
    getAllForUser: jest.fn(),
    get: jest.fn(),
    getUserReportById: jest.fn(),
    create: jest.fn(),
    updateTitleAndDescription: jest.fn(),
    updateStatus: jest.fn(),
    addMessage: jest.fn(),
    delete: jest.fn(),
    addResolutionComment: jest.fn(),
    confirmResolution: jest.fn(),
    rejectResolution: jest.fn(),
    getStatistics: jest.fn(),
    TYPES: {
      INCIDENT: "incident",
      BUG: "bug",
      FEATURE_REQUEST: "feature_request",
    },
    STATUSES: {
      OPEN: "open",
      IN_PROGRESS: "in_progress",
      RESOLVED: "resolved",
      CLOSED: "closed",
    },
    SEVERITIES: {
      LOW: "low",
      MEDIUM: "medium",
      HIGH: "high",
      CRITICAL: "critical",
    },
    SERVICES: {
      AUTHENTICATION: "authentication",
      DATABASE: "database",
      API: "api",
      FRONTEND: "frontend",
    },
    ALLOWED_TRANSITIONS: {
      open: ["in_progress", "resolved", "closed"],
      in_progress: ["resolved", "closed"],
      resolved: ["closed", "in_progress"],
      closed: [],
    },
  },
}));

jest.mock("../../utils/middleware/validatedRequest", () => ({
  validatedRequest: jest.fn((req, res, next) => {
    res.locals.user = { id: 1, role: "admin", username: "testuser" };
    next();
  }),
}));

jest.mock("../../utils/middleware/reportViewerOrAdmin", () => ({
  reportViewerOrAdmin: jest.fn((req, res, next) => next()),
}));

jest.mock("../../utils/middleware/reportOwnerOrAdmin", () => ({
  reportOwnerOrAdmin: jest.fn((req, res, next) => next()),
}));

jest.mock("../../utils/middleware/authenticatedUserOnly", () => ({
  authenticatedUserOnly: jest.fn((req, res, next) => next()),
}));

jest.mock("../../utils/middleware/managerOrAdmin", () => ({
  managerOrAdmin: jest.fn((req, res, next) => next()),
}));

jest.mock("@prisma/client", () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    $connect: jest.fn(),
    $disconnect: jest.fn(),
  })),
}));

describe("System Report Endpoints", () => {
  let app: Express;

  // Suppress console.error for expected errors in these tests
  let originalConsoleError: typeof console.error;

  beforeAll(() => {
    originalConsoleError = console.error;
    console.error = jest.fn();
  });

  afterAll(() => {
    console.error = originalConsoleError;
  });

  beforeEach(() => {
    jest.clearAllMocks();

    // Create Express app and register endpoints
    app = express();
    app.use(express.json());
    const apiRouter = express.Router();
    app.use("/api", apiRouter);
    systemReportEndpoints(app, apiRouter);
  });

  const mockReport = {
    id: 1,
    title: "Test Report",
    description: "Test description",
    type: "bug",
    severity: "medium",
    affected_service: "api",
    status: "open",
    userId: 1,
    resolver_user_id: null,
    resolution_comment: null,
    resolution_confirmed: false,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  };

  describe("GET /api/system-reports", () => {
    it("should retrieve all reports for admin/manager users", async () => {
      (validatedRequest as jest.Mock).mockImplementation((req, res, next) => {
        res.locals.user = { id: 1, role: "admin", username: "admin" };
        next();
      });

      const mockReports = [mockReport, { ...mockReport, id: 2 }];
      (SystemReport.getAll as jest.Mock).mockResolvedValue(mockReports);

      const response = await request(app).get("/api/system-reports");

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        data: expect.arrayContaining([
          expect.objectContaining({ id: 1 }),
          expect.objectContaining({ id: 2 }),
        ]),
      });

      expect(SystemReport.getAll).toHaveBeenCalledWith({});
    });

    it("should retrieve only user's reports for regular users", async () => {
      (validatedRequest as jest.Mock).mockImplementation((req, res, next) => {
        res.locals.user = { id: 1, role: "user", username: "testuser" };
        next();
      });

      const userReports = [mockReport];
      (SystemReport.getAllForUser as jest.Mock).mockResolvedValue(userReports);

      const response = await request(app).get("/api/system-reports");

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(1);
      expect(SystemReport.getAllForUser).toHaveBeenCalledWith(1, {});
    });

    it("should filter reports by type, status, and severity", async () => {
      (validatedRequest as jest.Mock).mockImplementation((req, res, next) => {
        res.locals.user = { id: 1, role: "admin", username: "admin" };
        next();
      });

      (SystemReport.getAll as jest.Mock).mockResolvedValue([mockReport]);

      const response = await request(app)
        .get("/api/system-reports")
        .query({ type: "bug", status: "open", severity: "medium" });

      expect(response.status).toBe(200);
      expect(SystemReport.getAll).toHaveBeenCalledWith({
        type: "bug",
        status: "open",
        severity: "medium",
      });
    });

    it("should handle database errors", async () => {
      (SystemReport.getAll as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      const response = await request(app).get("/api/system-reports");

      expect(response.status).toBe(500);
    });
  });

  describe("POST /api/system-reports", () => {
    const validReportData = {
      title: "New Bug Report",
      description: "Description of the bug",
      type: "bug",
      severity: "high",
      affected_service: "api",
    };

    it("should create a new report successfully", async () => {
      const createdReport = { ...mockReport, ...validReportData };
      (SystemReport.create as jest.Mock).mockResolvedValue(createdReport);

      const response = await request(app)
        .post("/api/system-reports")
        .send(validReportData);

      expect(response.status).toBe(201);
      expect(response.body).toMatchObject({
        success: true,
        data: expect.objectContaining({
          title: validReportData.title,
          description: validReportData.description,
        }),
      });

      expect(SystemReport.create).toHaveBeenCalledWith({
        ...validReportData,
        userId: 1,
      });
    });

    it("should validate required fields", async () => {
      const response = await request(app)
        .post("/api/system-reports")
        .send({ title: "Only title" });

      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: "Title and description are required",
      });
    });

    it("should validate report type", async () => {
      const response = await request(app)
        .post("/api/system-reports")
        .send({ ...validReportData, type: "invalid_type" });

      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: "Invalid type",
      });
    });

    it("should validate severity for incident reports", async () => {
      const response = await request(app)
        .post("/api/system-reports")
        .send({
          ...validReportData,
          type: "incident",
          severity: "invalid_severity",
        });

      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: "Invalid severity",
      });
    });

    it("should validate affected service for incident reports", async () => {
      const response = await request(app)
        .post("/api/system-reports")
        .send({
          ...validReportData,
          type: "incident",
          affected_service: "invalid_service",
        });

      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: "Invalid affected service",
      });
    });

    it("should handle creation failures", async () => {
      (SystemReport.create as jest.Mock).mockResolvedValue(null);

      const response = await request(app)
        .post("/api/system-reports")
        .send(validReportData);

      expect(response.status).toBe(500);
      expect(response.body).toMatchObject({
        success: false,
        error: "Failed to create report",
      });
    });
  });

  describe("GET /api/system-reports/constants", () => {
    it("should return all system report constants", async () => {
      const response = await request(app).get("/api/system-reports/constants");

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        constants: {
          STATUSES: expect.objectContaining({
            OPEN: "open",
            IN_PROGRESS: "in_progress",
            RESOLVED: "resolved",
            CLOSED: "closed",
          }),
          SEVERITIES: expect.objectContaining({
            LOW: "low",
            MEDIUM: "medium",
            HIGH: "high",
            CRITICAL: "critical",
          }),
          SERVICES: expect.any(Object),
          ALLOWED_TRANSITIONS: expect.any(Object),
          TYPES: expect.any(Object),
        },
      });
    });

    it("should handle errors gracefully", async () => {
      // Instead of mocking Object.entries globally, let's simulate an error
      // by mocking something within the SystemReport module
      const originalConsoleError = console.error;
      console.error = jest.fn(); // Suppress error logging during test

      // Create a new app instance to avoid affecting other tests
      const testApp = express();
      testApp.use(express.json());
      const testRouter = express.Router();
      testApp.use("/api", testRouter);

      // Mock Object.entries just for this request by patching the prototype temporarily
      const originalObjectEntries = Object.entries;

      try {
        // Register a modified version of the endpoint that will fail
        testRouter.get("/system-reports/constants", async (req, res) => {
          try {
            // Simulate an internal error
            throw new Error("Simulated internal error");
          } catch (error) {
            console.error("Error in constants endpoint:", error);
            res.status(500).json({
              success: false,
              error: "Internal server error",
            });
          }
        });

        const response = await request(testApp).get(
          "/api/system-reports/constants"
        );

        expect(response.status).toBe(500);
        expect(response.body).toMatchObject({
          success: false,
          error: "Internal server error",
        });
      } finally {
        // Restore console.error
        console.error = originalConsoleError;
        // Ensure Object.entries is restored (though we didn't modify it)
        Object.entries = originalObjectEntries;
      }
    });
  });

  describe("GET /api/system-reports/statistics", () => {
    it("should return statistics for managers/admins", async () => {
      const mockStatistics = {
        totalReports: 100,
        openReports: 20,
        resolvedReports: 70,
        closedReports: 10,
        averageResolutionTime: 48,
      };
      (SystemReport.getStatistics as jest.Mock).mockResolvedValue(
        mockStatistics
      );

      const response = await request(app).get("/api/system-reports/statistics");

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        statistics: mockStatistics,
      });
    });

    it("should deny access to non-managers/admins", async () => {
      (managerOrAdmin as jest.Mock).mockImplementation((req, res, _next) => {
        res.status(403).json({ error: "Forbidden" });
      });

      const response = await request(app).get("/api/system-reports/statistics");

      expect(response.status).toBe(403);
    });

    it("should handle statistics fetch failures", async () => {
      // Reset the middleware mock to allow access
      (managerOrAdmin as jest.Mock).mockImplementation((req, res, next) =>
        next()
      );
      (SystemReport.getStatistics as jest.Mock).mockResolvedValue(null);

      const response = await request(app).get("/api/system-reports/statistics");

      expect(response.status).toBe(500);
      expect(response.body).toMatchObject({
        success: false,
        error: "Failed to fetch statistics",
      });
    });
  });

  describe("GET /api/system-reports/:id", () => {
    it("should retrieve a specific report for admin/manager", async () => {
      (validatedRequest as jest.Mock).mockImplementation((req, res, next) => {
        res.locals.user = { id: 2, role: "admin", username: "admin" };
        next();
      });

      (SystemReport.get as jest.Mock).mockResolvedValue(mockReport);

      const response = await request(app).get("/api/system-reports/1");

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        data: expect.objectContaining({ id: 1 }),
      });

      expect(SystemReport.get).toHaveBeenCalledWith(1);
    });

    it("should retrieve only user's own report for regular users", async () => {
      (validatedRequest as jest.Mock).mockImplementation((req, res, next) => {
        res.locals.user = { id: 1, role: "user", username: "testuser" };
        next();
      });

      (SystemReport.getUserReportById as jest.Mock).mockResolvedValue(
        mockReport
      );

      const response = await request(app).get("/api/system-reports/1");

      expect(response.status).toBe(200);
      expect(SystemReport.getUserReportById).toHaveBeenCalledWith(1, 1);
    });

    it("should return 404 for non-existent report", async () => {
      (SystemReport.get as jest.Mock).mockResolvedValue(null);
      (SystemReport.getUserReportById as jest.Mock).mockResolvedValue(null);

      const response = await request(app).get("/api/system-reports/999");

      expect(response.status).toBe(404);
    });

    it("should handle invalid report ID", async () => {
      const response = await request(app).get("/api/system-reports/invalid");

      expect(response.status).toBe(404); // Invalid ID results in NaN, which returns no report (404)
    });
  });

  describe("PUT /api/system-reports/:id", () => {
    it("should update report title and description", async () => {
      const updateData = {
        title: "Updated Title",
        description: "Updated Description",
      };
      const updatedReport = { ...mockReport, ...updateData };
      (SystemReport.updateTitleAndDescription as jest.Mock).mockResolvedValue(
        updatedReport
      );

      const response = await request(app)
        .put("/api/system-reports/1")
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        data: expect.objectContaining(updateData),
      });

      expect(SystemReport.updateTitleAndDescription).toHaveBeenCalledWith(
        1,
        updateData
      );
    });

    it("should require at least one field to update", async () => {
      const response = await request(app).put("/api/system-reports/1").send({});

      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: "At least one field must be provided for update",
      });
    });

    it("should handle non-existent report", async () => {
      (SystemReport.updateTitleAndDescription as jest.Mock).mockResolvedValue(
        null
      );

      const response = await request(app)
        .put("/api/system-reports/999")
        .send({ title: "New Title" });

      expect(response.status).toBe(404);
      expect(response.body).toMatchObject({
        success: false,
        error: "Report not found",
      });
    });

    it("should validate report ID", async () => {
      const response = await request(app)
        .put("/api/system-reports/invalid")
        .send({ title: "New Title" });

      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: "Invalid report ID",
      });
    });
  });

  describe("PUT /api/system-reports/:id/status", () => {
    it("should update report status", async () => {
      const updatedReport = { ...mockReport, status: "in_progress" };
      (SystemReport.updateStatus as jest.Mock).mockResolvedValue(updatedReport);

      const response = await request(app)
        .put("/api/system-reports/1/status")
        .send({ status: "in_progress" });

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        data: expect.objectContaining({ status: "in_progress" }),
      });

      expect(SystemReport.updateStatus).toHaveBeenCalledWith(
        1,
        "in_progress",
        1
      );
    });

    it("should validate status is required", async () => {
      const response = await request(app)
        .put("/api/system-reports/1/status")
        .send({});

      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: "Status is required",
      });
    });

    it("should validate status value", async () => {
      const response = await request(app)
        .put("/api/system-reports/1/status")
        .send({ status: "invalid_status" });

      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: "Invalid status",
      });
    });

    it("should handle invalid status transitions", async () => {
      (SystemReport.updateStatus as jest.Mock).mockResolvedValue(null);

      const response = await request(app)
        .put("/api/system-reports/1/status")
        .send({ status: "closed" });

      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: "Failed to update status. Check if transition is valid.",
      });
    });
  });

  describe("POST /api/system-reports/:id/messages", () => {
    it("should add a message to a report", async () => {
      const mockMessage = {
        id: 1,
        userId: 1,
        createdAt: new Date(),
      };
      (SystemReport.addMessage as jest.Mock).mockResolvedValue(mockMessage);

      const response = await request(app)
        .post("/api/system-reports/1/messages")
        .send({ content: "This is a test message" });

      expect(response.status).toBe(201);
      expect(response.body).toMatchObject({
        success: true,
        data: expect.objectContaining({
          id: 1,
          userId: 1,
        }),
      });

      expect(SystemReport.addMessage).toHaveBeenCalledWith(
        1,
        "This is a test message",
        1
      );
    });

    it("should validate content is required", async () => {
      const response = await request(app)
        .post("/api/system-reports/1/messages")
        .send({});

      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: "Content is required",
      });
    });

    it("should trim whitespace from content", async () => {
      const mockMessage = {
        id: 1,
        userId: 1,
        createdAt: new Date(),
      };
      (SystemReport.addMessage as jest.Mock).mockResolvedValue(mockMessage);

      await request(app)
        .post("/api/system-reports/1/messages")
        .send({ content: "  Trimmed message  " });

      expect(SystemReport.addMessage).toHaveBeenCalledWith(
        1,
        "Trimmed message",
        1
      );
    });

    it("should handle non-existent report", async () => {
      (SystemReport.addMessage as jest.Mock).mockResolvedValue(null);

      const response = await request(app)
        .post("/api/system-reports/999/messages")
        .send({ content: "Message" });

      expect(response.status).toBe(404);
      expect(response.body).toMatchObject({
        success: false,
        error: "Report not found",
      });
    });
  });

  describe("DELETE /api/system-reports/:id", () => {
    it("should delete a report", async () => {
      (SystemReport.delete as jest.Mock).mockResolvedValue(mockReport);

      const response = await request(app).delete("/api/system-reports/1");

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        message: "Report deleted successfully",
        data: {
          deletedReport: expect.objectContaining({
            id: mockReport.id,
            title: mockReport.title,
            description: mockReport.description,
            type: mockReport.type,
            severity: mockReport.severity,
            affected_service: mockReport.affected_service,
            status: mockReport.status,
            userId: mockReport.userId,
          }),
        },
      });

      expect(SystemReport.delete).toHaveBeenCalledWith(1);
    });

    it("should handle non-existent report", async () => {
      (SystemReport.delete as jest.Mock).mockResolvedValue(null);

      const response = await request(app).delete("/api/system-reports/999");

      expect(response.status).toBe(404);
      expect(response.body).toMatchObject({
        success: false,
        error: "Report not found",
      });
    });

    it("should validate report ID", async () => {
      const response = await request(app).delete("/api/system-reports/invalid");

      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: "Invalid report ID",
      });
    });
  });

  describe("POST /api/system-reports/:id/resolve", () => {
    it("should resolve a report with comment", async () => {
      const reportWithComment = {
        ...mockReport,
        resolution_comment: "Issue has been fixed",
      };
      const resolvedReport = {
        ...reportWithComment,
        status: "resolved",
        resolver_user_id: 1,
      };

      (SystemReport.addResolutionComment as jest.Mock).mockResolvedValue(
        reportWithComment
      );
      (SystemReport.updateStatus as jest.Mock).mockResolvedValue(
        resolvedReport
      );

      const response = await request(app)
        .post("/api/system-reports/1/resolve")
        .send({ resolution_comment: "Issue has been fixed" });

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        data: expect.objectContaining({
          status: "resolved",
          resolution_comment: "Issue has been fixed",
        }),
      });

      expect(SystemReport.addResolutionComment).toHaveBeenCalledWith(
        1,
        "Issue has been fixed"
      );
      expect(SystemReport.updateStatus).toHaveBeenCalledWith(1, "resolved", 1);
    });

    it("should require resolution comment", async () => {
      const response = await request(app)
        .post("/api/system-reports/1/resolve")
        .send({});

      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: "Resolution comment is required",
      });
    });

    it("should trim resolution comment", async () => {
      (SystemReport.addResolutionComment as jest.Mock).mockResolvedValue(
        mockReport
      );
      (SystemReport.updateStatus as jest.Mock).mockResolvedValue(mockReport);

      await request(app)
        .post("/api/system-reports/1/resolve")
        .send({ resolution_comment: "  Trimmed comment  " });

      expect(SystemReport.addResolutionComment).toHaveBeenCalledWith(
        1,
        "Trimmed comment"
      );
    });
  });

  describe("POST /api/system-reports/:id/confirm-resolution", () => {
    it("should confirm resolution by original reporter", async () => {
      const resolvedReport = {
        ...mockReport,
        status: "resolved",
        resolver_user_id: 2,
        resolution_comment: "Fixed",
      };
      const confirmedReport = {
        ...resolvedReport,
        resolution_confirmed: true,
      };

      (SystemReport.get as jest.Mock).mockResolvedValue(resolvedReport);
      (SystemReport.confirmResolution as jest.Mock).mockResolvedValue(
        confirmedReport
      );

      const response = await request(app).post(
        "/api/system-reports/1/confirm-resolution"
      );

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        message: "Resolution confirmed successfully",
        data: {
          report: expect.objectContaining({ resolution_confirmed: true }),
        },
      });

      expect(SystemReport.confirmResolution).toHaveBeenCalledWith(1, 1);
    });

    it("should auto-confirm when reporter resolves their own report", async () => {
      const selfResolvedReport = {
        ...mockReport,
        status: "resolved",
        resolver_user_id: 1, // Same as userId
      };
      const confirmedReport = {
        ...selfResolvedReport,
        resolution_confirmed: true,
      };

      (SystemReport.get as jest.Mock).mockResolvedValue(selfResolvedReport);
      (SystemReport.confirmResolution as jest.Mock).mockResolvedValue(
        confirmedReport
      );

      const response = await request(app).post(
        "/api/system-reports/1/confirm-resolution"
      );

      expect(response.status).toBe(200);
      expect(response.body.message).toBe(
        "Resolution auto-confirmed successfully"
      );
    });

    it("should deny confirmation by non-reporter", async () => {
      const report = { ...mockReport, userId: 2 }; // Different user
      (SystemReport.get as jest.Mock).mockResolvedValue(report);

      const response = await request(app).post(
        "/api/system-reports/1/confirm-resolution"
      );

      expect(response.status).toBe(403);
      expect(response.body).toMatchObject({
        success: false,
        error: "Only the original reporter can confirm resolution",
      });
    });

    it("should require report to be in resolved state", async () => {
      (SystemReport.get as jest.Mock).mockResolvedValue(mockReport); // status: "open"

      const response = await request(app).post(
        "/api/system-reports/1/confirm-resolution"
      );

      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: "Report must be in resolved state to confirm",
      });
    });

    it("should prevent double confirmation", async () => {
      const alreadyConfirmed = {
        ...mockReport,
        status: "resolved",
        resolution_confirmed: true,
      };
      (SystemReport.get as jest.Mock).mockResolvedValue(alreadyConfirmed);

      const response = await request(app).post(
        "/api/system-reports/1/confirm-resolution"
      );

      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: "Resolution already confirmed",
      });
    });
  });

  describe("POST /api/system-reports/:id/reject-resolution", () => {
    it("should reject resolution by original reporter", async () => {
      const resolvedReport = {
        ...mockReport,
        status: "resolved",
        resolver_user_id: 2,
        resolution_comment: "Fixed",
        resolution_confirmed: false,
      };
      const rejectedReport = {
        ...resolvedReport,
        status: "in_progress",
        resolution_confirmed: false,
      };

      (SystemReport.get as jest.Mock).mockResolvedValue(resolvedReport);
      (SystemReport.rejectResolution as jest.Mock).mockResolvedValue(
        rejectedReport
      );

      const response = await request(app).post(
        "/api/system-reports/1/reject-resolution"
      );

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        message: "Resolution rejected successfully",
        data: {
          report: expect.objectContaining({ status: "in_progress" }),
        },
      });

      expect(SystemReport.rejectResolution).toHaveBeenCalledWith(1, 1);
    });

    it("should deny rejection by non-reporter", async () => {
      const report = { ...mockReport, userId: 2, status: "resolved" };
      (SystemReport.get as jest.Mock).mockResolvedValue(report);

      const response = await request(app).post(
        "/api/system-reports/1/reject-resolution"
      );

      expect(response.status).toBe(403);
      expect(response.body).toMatchObject({
        success: false,
        error: "Only the original reporter can reject resolution",
      });
    });

    it("should require report to be in resolved state", async () => {
      (SystemReport.get as jest.Mock).mockResolvedValue(mockReport); // status: "open"

      const response = await request(app).post(
        "/api/system-reports/1/reject-resolution"
      );

      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: "Report must be in resolved state to reject",
      });
    });

    it("should prevent rejection of confirmed resolutions", async () => {
      const confirmedReport = {
        ...mockReport,
        status: "resolved",
        resolution_confirmed: true,
      };
      (SystemReport.get as jest.Mock).mockResolvedValue(confirmedReport);

      const response = await request(app).post(
        "/api/system-reports/1/reject-resolution"
      );

      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: "Cannot reject already confirmed resolution",
      });
    });

    it("should handle rejection failures", async () => {
      const resolvedReport = {
        ...mockReport,
        status: "resolved",
        resolution_confirmed: false,
      };
      (SystemReport.get as jest.Mock).mockResolvedValue(resolvedReport);
      (SystemReport.rejectResolution as jest.Mock).mockResolvedValue(null);

      const response = await request(app).post(
        "/api/system-reports/1/reject-resolution"
      );

      expect(response.status).toBe(500);
      expect(response.body).toMatchObject({
        success: false,
        error: "Failed to reject resolution",
      });
    });
  });

  describe("Edge Cases and Error Handling", () => {
    it("should handle invalid app parameter", () => {
      expect(() => systemReportEndpoints(null as any)).not.toThrow();
    });

    it("should handle database connection errors", async () => {
      (SystemReport.getAll as jest.Mock).mockRejectedValue(
        new Error("Database connection failed")
      );
      (SystemReport.getAllForUser as jest.Mock).mockRejectedValue(
        new Error("Database connection failed")
      );

      const response = await request(app).get("/api/system-reports");

      expect(response.status).toBe(500);
    });

    it("should handle malformed JSON in requests", async () => {
      const response = await request(app)
        .post("/api/system-reports")
        .send("invalid json")
        .set("Content-Type", "application/json");

      expect(response.status).toBe(400);
    });
  });
});
