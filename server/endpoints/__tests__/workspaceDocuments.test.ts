// External dependencies
import express from "express";
import request from "supertest";
import type {
  workspaces as PrismaWorkspace,
  workspace_documents as PrismaWorkspaceDocument,
} from "@prisma/client";

// Internal modules
import { workspaceDocumentEndpoints } from "../workspaceDocuments";
import { Document } from "../../models/documents";
import { Workspace } from "../../models/workspace";
import { isDocumentProperlyVectorized } from "../../utils/helpers/vectorizationCheck";

// Mock the required modules
jest.mock("../../models/documents", () => ({
  Document: {
    get: jest.fn(),
    where: jest.fn(),
    update: jest.fn(),
  },
}));

jest.mock("../../models/workspace", () => ({
  Workspace: {
    get: jest.fn(),
  },
}));

jest.mock("../../utils/helpers/vectorizationCheck", () => ({
  isDocumentProperlyVectorized: jest.fn(),
}));

// Mock the middleware
jest.mock("../../utils/middleware/validApiKey", () => ({
  validApiKey: (
    _req: express.Request,
    _res: express.Response,
    next: express.NextFunction
  ) => next(),
}));

// Extended workspace type to include documents
type WorkspaceWithDocuments = PrismaWorkspace & {
  documents?: PrismaWorkspaceDocument[];
};

// Response types
interface UpdateStarResponse {
  message: string;
  updatedCount: number;
  requiresReVectorization: boolean;
  requiresMetadataUpdate: boolean;
}

interface ErrorResponse {
  error?: string;
  message?: string;
}

// Helper functions to create mock objects
function createMockWorkspace(
  overrides: Partial<WorkspaceWithDocuments> = {}
): WorkspaceWithDocuments {
  const now = new Date();
  return {
    id: 123,
    slug: "test-workspace",
    name: "Test Workspace",
    type: "default",
    order: 0,
    createdAt: now,
    lastUpdatedAt: now,
    user_id: 1,
    pfpFilename: null,
    pdr: false,
    sharedWithOrg: false,
    vectorTag: null,
    openAiTemp: null,
    openAiHistory: 20,
    openAiPrompt: null,
    similarityThreshold: 0.25,
    chatProvider: null,
    chatModel: null,
    embeddingProvider: null,
    embeddingModel: null,
    topN: 4,
    chatMode: "chat",
    chatType: "private",
    agentProvider: null,
    agentModel: null,
    queryRefusalResponse: null,
    vectorSearchMode: null,
    hasMessages: false,
    documents: undefined,
    ...overrides,
  };
}

function createMockDocument(
  overrides: Partial<PrismaWorkspaceDocument> = {}
): PrismaWorkspaceDocument {
  const now = new Date();
  return {
    id: 123,
    createdAt: now,
    lastUpdatedAt: now,
    workspaceId: 123,
    pdr: false,
    docId: "doc-id-123",
    filename: "doc.json",
    docpath: "path/to/doc.json",
    metadata: null,
    pinned: false,
    watched: false,
    starred: false,
    ...overrides,
  };
}

// Test setup

describe("Workspace Document Endpoints", () => {
  let app: express.Express;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();

    // Create a new Express app for each test
    app = express();
    app.use(express.json());

    // Create a router and register the endpoints
    const router = express.Router();
    workspaceDocumentEndpoints(router);
    app.use("/api", router);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
    // Clean up any remaining async operations
    jest.runOnlyPendingTimers();
  });

  describe("POST /workspace/:slug/update-star", () => {
    it("should update star status for a document that is properly vectorized", async () => {
      // Mock workspace and document
      const workspace = createMockWorkspace();
      const document = createMockDocument();

      // Set up mocks
      const mockWorkspaceGet = Workspace.get as jest.MockedFunction<
        typeof Workspace.get
      >;
      mockWorkspaceGet.mockResolvedValue(workspace);

      const mockDocumentGet = Document.get as jest.MockedFunction<
        typeof Document.get
      >;
      mockDocumentGet.mockResolvedValue(document);

      const mockIsDocumentProperlyVectorized =
        isDocumentProperlyVectorized as jest.MockedFunction<
          typeof isDocumentProperlyVectorized
        >;
      mockIsDocumentProperlyVectorized.mockResolvedValue(true);

      const mockDocumentUpdate = Document.update as jest.MockedFunction<
        typeof Document.update
      >;
      mockDocumentUpdate.mockResolvedValue({
        document: { ...document, starred: true },
        message: null,
      });

      // Make request
      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "/path/to/document.pdf",
          starStatus: true,
        })
        .expect(200);

      // Assert response
      expect(response.status).toBe(200);
      const body = response.body as UpdateStarResponse;
      expect(body).toMatchObject({
        message: "Document star status updated successfully",
        updatedCount: 1,
        requiresReVectorization: false,
        requiresMetadataUpdate: false,
      });

      // Verify mocks were called correctly
      expect(Workspace.get).toHaveBeenCalledWith({ slug: "test-workspace" });
      expect(Document.get).toHaveBeenCalledWith({
        workspaceId: 123,
        docpath: "/path/to/document.pdf",
      });
      expect(isDocumentProperlyVectorized).toHaveBeenCalled();
      expect(Document.update).toHaveBeenCalled();
    });

    it("should return 404 if workspace not found", async () => {
      // Mock workspace not found
      (
        Workspace.get as jest.MockedFunction<typeof Workspace.get>
      ).mockResolvedValue(null);

      // Make request
      const response = await request(app)
        .post("/api/workspace/non-existent/update-star")
        .send({
          docPath: "/path/to/document.pdf",
          starStatus: true,
        })
        .expect(404);

      // Assert response
      expect(response.status).toBe(404);
      const errorBody = response.body as ErrorResponse;
      expect(errorBody).toMatchObject({
        message: "Workspace not found",
      });
    });

    it("should return requiresReVectorization flag if document is not properly vectorized", async () => {
      // Mock workspace and document
      const workspace = createMockWorkspace();
      const document = createMockDocument();

      // Set up mocks
      const mockWorkspaceGet = Workspace.get as jest.MockedFunction<
        typeof Workspace.get
      >;
      mockWorkspaceGet.mockResolvedValue(workspace);
      const mockDocumentGet = Document.get as jest.MockedFunction<
        typeof Document.get
      >;
      mockDocumentGet.mockResolvedValue(document);
      (
        isDocumentProperlyVectorized as jest.MockedFunction<
          typeof isDocumentProperlyVectorized
        >
      ).mockResolvedValue(false);

      // Make request
      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "/path/to/document.pdf",
          starStatus: true,
        })
        .expect(200);

      // Assert response
      expect(response.status).toBe(200);
      const updateResponse = response.body as UpdateStarResponse;
      expect(updateResponse).toMatchObject({
        message: "Document requires re-vectorization before it can be starred",
        requiresReVectorization: true,
        requiresMetadataUpdate: true,
        docId: "doc-id-123",
        docPath: "path/to/doc.json",
      });

      // Verify document was NOT updated
      expect(Document.update).not.toHaveBeenCalled();
    });

    it("should allow force update even if document is not properly vectorized", async () => {
      // Mock workspace and document
      const workspace = createMockWorkspace();
      const document = createMockDocument();

      // Set up mocks
      const mockWorkspaceGet = Workspace.get as jest.MockedFunction<
        typeof Workspace.get
      >;
      mockWorkspaceGet.mockResolvedValue(workspace);
      const mockDocumentGet = Document.get as jest.MockedFunction<
        typeof Document.get
      >;
      mockDocumentGet.mockResolvedValue(document);
      const mockDocumentUpdate = Document.update as jest.MockedFunction<
        typeof Document.update
      >;
      mockDocumentUpdate.mockResolvedValue({
        document: { ...document, starred: true },
        message: null,
      });

      // Make request with forceUpdate
      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "/path/to/document.pdf",
          starStatus: true,
          forceUpdate: true,
        })
        .expect(200);

      // Assert response
      expect(response.status).toBe(200);
      const updateResponse = response.body as UpdateStarResponse;
      expect(updateResponse).toMatchObject({
        message: "Document star status updated successfully",
        updatedCount: 1,
        requiresReVectorization: false,
        requiresMetadataUpdate: false,
      });

      // Verify document was updated even though vectorization check was bypassed
      expect(Document.update).toHaveBeenCalledWith(123, { starred: true });
      // Verify vectorization check was not called due to forceUpdate
      expect(isDocumentProperlyVectorized).not.toHaveBeenCalled();
    });
  });
});
