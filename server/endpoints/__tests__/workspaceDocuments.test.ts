// External dependencies
import express from "express";
import request from "supertest";
import type {
  workspaces as PrismaWorkspace,
  workspace_documents as PrismaWorkspaceDocument,
} from "@prisma/client";

// Internal modules
import { workspaceDocumentEndpoints } from "../workspaceDocuments";
import { Document } from "../../models/documents";
import { Workspace } from "../../models/workspace";
import {
  isDocumentProperlyVectorized,
  reVectorizeDocument,
} from "../../utils/helpers/vectorizationCheck";

// Mock the required modules
jest.mock("../../models/documents", () => ({
  Document: {
    get: jest.fn(),
    where: jest.fn(),
    update: jest.fn(),
  },
}));

jest.mock("../../models/workspace", () => ({
  Workspace: {
    get: jest.fn(),
  },
}));

jest.mock("../../utils/helpers/vectorizationCheck", () => ({
  isDocumentProperlyVectorized: jest.fn(),
  reVectorizeDocument: jest.fn(),
}));

// Mock the middleware
jest.mock("../../utils/middleware/validApiKey", () => ({
  validApiKey: (
    _req: express.Request,
    _res: express.Response,
    next: express.NextFunction
  ) => next(),
}));

// Extended workspace type to include documents
type WorkspaceWithDocuments = PrismaWorkspace & {
  documents?: PrismaWorkspaceDocument[];
};

// Response types
interface UpdateStarResponse {
  message: string;
  updatedCount: number;
  requiresReVectorization: boolean;
  requiresMetadataUpdate: boolean;
}

interface ErrorResponse {
  error?: string;
  message?: string;
}

// Helper functions to create mock objects
function createMockWorkspace(
  overrides: Partial<WorkspaceWithDocuments> = {}
): WorkspaceWithDocuments {
  const now = new Date();
  return {
    id: 123,
    slug: "test-workspace",
    name: "Test Workspace",
    type: "default",
    order: 0,
    createdAt: now,
    lastUpdatedAt: now,
    user_id: 1,
    pfpFilename: null,
    pdr: false,
    sharedWithOrg: false,
    vectorTag: null,
    openAiTemp: null,
    openAiHistory: 20,
    openAiPrompt: null,
    similarityThreshold: 0.25,
    chatProvider: null,
    chatModel: null,
    embeddingProvider: null,
    embeddingModel: null,
    topN: 4,
    chatMode: "chat",
    chatType: "private",
    agentProvider: null,
    agentModel: null,
    queryRefusalResponse: null,
    vectorSearchMode: null,
    hasMessages: false,
    documents: undefined,
    ...overrides,
  };
}

function createMockDocument(
  overrides: Partial<PrismaWorkspaceDocument> = {}
): PrismaWorkspaceDocument {
  const now = new Date();
  return {
    id: 123,
    createdAt: now,
    lastUpdatedAt: now,
    workspaceId: 123,
    pdr: false,
    docId: "doc-id-123",
    filename: "doc.json",
    docpath: "path/to/doc.json",
    metadata: null,
    pinned: false,
    watched: false,
    starred: false,
    ...overrides,
  };
}

// Test setup

describe("Workspace Document Endpoints", () => {
  let app: express.Express;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();

    // Create a new Express app for each test
    app = express();
    app.use(express.json());

    // Create a router and register the endpoints
    const router = express.Router();
    workspaceDocumentEndpoints(router);
    app.use("/api", router);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
    // Clean up any remaining async operations
    jest.runOnlyPendingTimers();
  });

  describe("POST /workspace/:slug/update-star", () => {
    it("should update star status for a document that is properly vectorized", async () => {
      // Mock workspace and document
      const workspace = createMockWorkspace();
      const document = createMockDocument();

      // Set up mocks
      const mockWorkspaceGet = Workspace.get as jest.MockedFunction<
        typeof Workspace.get
      >;
      mockWorkspaceGet.mockResolvedValue(workspace);

      const mockDocumentGet = Document.get as jest.MockedFunction<
        typeof Document.get
      >;
      mockDocumentGet.mockResolvedValue(document);

      const mockIsDocumentProperlyVectorized =
        isDocumentProperlyVectorized as jest.MockedFunction<
          typeof isDocumentProperlyVectorized
        >;
      mockIsDocumentProperlyVectorized.mockResolvedValue(true);

      const mockDocumentUpdate = Document.update as jest.MockedFunction<
        typeof Document.update
      >;
      mockDocumentUpdate.mockResolvedValue({
        document: { ...document, starred: true },
        message: null,
      });

      // Make request
      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "/path/to/document.pdf",
          starStatus: true,
        })
        .expect(200);

      // Assert response
      expect(response.status).toBe(200);
      const body = response.body as UpdateStarResponse;
      expect(body).toMatchObject({
        message: "Document star status updated successfully",
        updatedCount: 1,
        requiresReVectorization: false,
        requiresMetadataUpdate: false,
      });

      // Verify mocks were called correctly
      expect(Workspace.get).toHaveBeenCalledWith({ slug: "test-workspace" });
      expect(Document.get).toHaveBeenCalledWith({
        workspaceId: 123,
        docpath: "/path/to/document.pdf",
      });
      expect(isDocumentProperlyVectorized).toHaveBeenCalled();
      expect(Document.update).toHaveBeenCalled();
    });

    it("should return 404 if workspace not found", async () => {
      // Mock workspace not found
      (
        Workspace.get as jest.MockedFunction<typeof Workspace.get>
      ).mockResolvedValue(null);

      // Make request
      const response = await request(app)
        .post("/api/workspace/non-existent/update-star")
        .send({
          docPath: "/path/to/document.pdf",
          starStatus: true,
        })
        .expect(404);

      // Assert response
      expect(response.status).toBe(404);
      const errorBody = response.body as ErrorResponse;
      expect(errorBody).toMatchObject({
        message: "Workspace not found",
      });
    });

    it("should return requiresReVectorization flag if document is not properly vectorized", async () => {
      // Mock workspace and document
      const workspace = createMockWorkspace();
      const document = createMockDocument();

      // Set up mocks
      const mockWorkspaceGet = Workspace.get as jest.MockedFunction<
        typeof Workspace.get
      >;
      mockWorkspaceGet.mockResolvedValue(workspace);
      const mockDocumentGet = Document.get as jest.MockedFunction<
        typeof Document.get
      >;
      mockDocumentGet.mockResolvedValue(document);
      (
        isDocumentProperlyVectorized as jest.MockedFunction<
          typeof isDocumentProperlyVectorized
        >
      ).mockResolvedValue(false);

      // Make request
      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "/path/to/document.pdf",
          starStatus: true,
        })
        .expect(200);

      // Assert response
      expect(response.status).toBe(200);
      const updateResponse = response.body as UpdateStarResponse;
      expect(updateResponse).toMatchObject({
        message: "Document requires re-vectorization before it can be starred",
        requiresReVectorization: true,
        requiresMetadataUpdate: true,
        docId: "doc-id-123",
        docPath: "path/to/doc.json",
      });

      // Verify document was NOT updated
      expect(Document.update).not.toHaveBeenCalled();
    });

    it("should allow force update even if document is not properly vectorized", async () => {
      // Mock workspace and document
      const workspace = createMockWorkspace();
      const document = createMockDocument();

      // Set up mocks
      const mockWorkspaceGet = Workspace.get as jest.MockedFunction<
        typeof Workspace.get
      >;
      mockWorkspaceGet.mockResolvedValue(workspace);
      const mockDocumentGet = Document.get as jest.MockedFunction<
        typeof Document.get
      >;
      mockDocumentGet.mockResolvedValue(document);
      const mockDocumentUpdate = Document.update as jest.MockedFunction<
        typeof Document.update
      >;
      mockDocumentUpdate.mockResolvedValue({
        document: { ...document, starred: true },
        message: null,
      });

      // Make request with forceUpdate
      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "/path/to/document.pdf",
          starStatus: true,
          forceUpdate: true,
        })
        .expect(200);

      // Assert response
      expect(response.status).toBe(200);
      const updateResponse = response.body as UpdateStarResponse;
      expect(updateResponse).toMatchObject({
        message: "Document star status updated successfully",
        updatedCount: 1,
        requiresReVectorization: false,
        requiresMetadataUpdate: false,
      });

      // Verify document was updated even though vectorization check was bypassed
      expect(Document.update).toHaveBeenCalledWith(123, { starred: true });
      // Verify vectorization check was not called due to forceUpdate
      expect(isDocumentProperlyVectorized).not.toHaveBeenCalled();
    });

    it("should return 404 if document not found", async () => {
      // Mock workspace
      const workspace = createMockWorkspace();
      (
        Workspace.get as jest.MockedFunction<typeof Workspace.get>
      ).mockResolvedValue(workspace);

      // Mock document not found
      (
        Document.get as jest.MockedFunction<typeof Document.get>
      ).mockResolvedValue(null);

      // Make request
      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "/path/to/non-existent.pdf",
          starStatus: true,
        })
        .expect(404);

      // Assert response
      expect(response.body).toMatchObject({
        message: "Document not found",
      });
    });

    it("should return 400 if docPath is missing", async () => {
      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          starStatus: true,
        })
        .expect(400);

      expect(response.body).toMatchObject({
        message: "Invalid document path",
        error: "Document path is required",
      });
    });

    it("should return 400 if docPath is empty", async () => {
      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "   ",
          starStatus: true,
        })
        .expect(400);

      expect(response.body).toMatchObject({
        message: "Invalid document path",
        error: "Document path cannot be empty",
      });
    });

    it("should update all documents in a folder", async () => {
      // Mock workspace
      const workspace = createMockWorkspace();
      (
        Workspace.get as jest.MockedFunction<typeof Workspace.get>
      ).mockResolvedValue(workspace);

      // Mock documents in folder
      const documents = [
        createMockDocument({
          id: 1,
          docId: "doc1",
          docpath: "folder/doc1.pdf",
        }),
        createMockDocument({
          id: 2,
          docId: "doc2",
          docpath: "folder/doc2.pdf",
        }),
        createMockDocument({
          id: 3,
          docId: "doc3",
          docpath: "folder/subfolder/doc3.pdf",
        }),
      ];

      (
        Document.where as jest.MockedFunction<typeof Document.where>
      ).mockResolvedValue(documents);

      (
        isDocumentProperlyVectorized as jest.MockedFunction<
          typeof isDocumentProperlyVectorized
        >
      ).mockResolvedValue(true);

      (
        Document.update as jest.MockedFunction<typeof Document.update>
      ).mockResolvedValue({
        document: documents[0],
        message: null,
      });

      // Make request
      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "folder",
          starStatus: true,
          isFolder: true,
        })
        .expect(200);

      // Assert response
      expect(response.body).toMatchObject({
        message: "Updated 3 documents in folder",
        updatedCount: 3,
      });

      // Verify all documents were updated
      expect(Document.update).toHaveBeenCalledTimes(3);
      expect(Document.update).toHaveBeenCalledWith(1, { starred: true });
      expect(Document.update).toHaveBeenCalledWith(2, { starred: true });
      expect(Document.update).toHaveBeenCalledWith(3, { starred: true });
    });

    it("should skip non-vectorized documents in folder update without forceUpdate", async () => {
      // Mock workspace
      const workspace = createMockWorkspace();
      (
        Workspace.get as jest.MockedFunction<typeof Workspace.get>
      ).mockResolvedValue(workspace);

      // Mock documents in folder
      const documents = [
        createMockDocument({
          id: 1,
          docId: "doc1",
          docpath: "folder/doc1.pdf",
        }),
        createMockDocument({
          id: 2,
          docId: "doc2",
          docpath: "folder/doc2.pdf",
        }),
      ];

      (
        Document.where as jest.MockedFunction<typeof Document.where>
      ).mockResolvedValue(documents);

      // First document is vectorized, second is not
      (
        isDocumentProperlyVectorized as jest.MockedFunction<
          typeof isDocumentProperlyVectorized
        >
      )
        .mockResolvedValueOnce(true)
        .mockResolvedValueOnce(false);

      (
        Document.update as jest.MockedFunction<typeof Document.update>
      ).mockResolvedValue({
        document: documents[0],
        message: null,
      });

      // Make request
      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "folder",
          starStatus: true,
          isFolder: true,
        })
        .expect(200);

      // Assert response
      expect(response.body).toMatchObject({
        message: "Updated 1 documents in folder",
        updatedCount: 1,
      });

      // Verify only vectorized document was updated
      expect(Document.update).toHaveBeenCalledTimes(1);
      expect(Document.update).toHaveBeenCalledWith(1, { starred: true });
    });

    it("should return message when no documents found in folder", async () => {
      // Mock workspace
      const workspace = createMockWorkspace();
      (
        Workspace.get as jest.MockedFunction<typeof Workspace.get>
      ).mockResolvedValue(workspace);

      // Mock no documents in folder
      (
        Document.where as jest.MockedFunction<typeof Document.where>
      ).mockResolvedValue([]);

      // Make request
      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "empty-folder",
          starStatus: true,
          isFolder: true,
        })
        .expect(200);

      // Assert response
      expect(response.body).toMatchObject({
        message: "No documents found in the specified folder",
        updatedCount: 0,
      });
    });

    it("should handle errors during document update", async () => {
      // Mock workspace and document
      const workspace = createMockWorkspace();
      const document = createMockDocument();

      (
        Workspace.get as jest.MockedFunction<typeof Workspace.get>
      ).mockResolvedValue(workspace);
      (
        Document.get as jest.MockedFunction<typeof Document.get>
      ).mockResolvedValue(document);
      (
        isDocumentProperlyVectorized as jest.MockedFunction<
          typeof isDocumentProperlyVectorized
        >
      ).mockResolvedValue(true);

      // Mock update error
      (
        Document.update as jest.MockedFunction<typeof Document.update>
      ).mockRejectedValue(new Error("Database error"));

      // Make request
      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "/path/to/document.pdf",
          starStatus: true,
        })
        .expect(500);

      // Assert response
      expect(response.body).toMatchObject({
        message: "Database error",
      });
    });
  });

  describe("POST /workspace/:slug/re-vectorize", () => {
    it("should successfully re-vectorize a document", async () => {
      // Mock workspace and document
      const workspace = createMockWorkspace();
      const document = createMockDocument();

      (
        Workspace.get as jest.MockedFunction<typeof Workspace.get>
      ).mockResolvedValue(workspace);
      (
        Document.get as jest.MockedFunction<typeof Document.get>
      ).mockResolvedValue(document);
      (
        reVectorizeDocument as jest.MockedFunction<typeof reVectorizeDocument>
      ).mockResolvedValue(true);

      // Make request
      const response = await request(app)
        .post("/api/workspace/test-workspace/re-vectorize")
        .send({
          docId: "doc-id-123",
        })
        .expect(200);

      // Assert response
      expect(response.body).toMatchObject({
        success: true,
        message: "Document re-vectorized successfully",
        docId: "doc-id-123",
        docPath: "path/to/doc.json",
      });

      // Verify mocks
      expect(Workspace.get).toHaveBeenCalledWith({ slug: "test-workspace" });
      expect(Document.get).toHaveBeenCalledWith({
        workspaceId: 123,
        docId: "doc-id-123",
      });
      expect(reVectorizeDocument).toHaveBeenCalledWith(
        "test-workspace",
        "doc-id-123",
        document
      );
    });

    it("should return 404 if workspace not found", async () => {
      (
        Workspace.get as jest.MockedFunction<typeof Workspace.get>
      ).mockResolvedValue(null);

      const response = await request(app)
        .post("/api/workspace/non-existent/re-vectorize")
        .send({
          docId: "doc-id-123",
        })
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        message: "Workspace not found",
      });
    });

    it("should return 400 if docId is missing", async () => {
      const workspace = createMockWorkspace();
      (
        Workspace.get as jest.MockedFunction<typeof Workspace.get>
      ).mockResolvedValue(workspace);

      const response = await request(app)
        .post("/api/workspace/test-workspace/re-vectorize")
        .send({})
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        message: "Document ID is required",
      });
    });

    it("should return 404 if document not found", async () => {
      const workspace = createMockWorkspace();
      (
        Workspace.get as jest.MockedFunction<typeof Workspace.get>
      ).mockResolvedValue(workspace);
      (
        Document.get as jest.MockedFunction<typeof Document.get>
      ).mockResolvedValue(null);

      const response = await request(app)
        .post("/api/workspace/test-workspace/re-vectorize")
        .send({
          docId: "non-existent-doc",
        })
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        message: "Document not found",
      });
    });

    it("should handle re-vectorization failure", async () => {
      const workspace = createMockWorkspace();
      const document = createMockDocument();

      (
        Workspace.get as jest.MockedFunction<typeof Workspace.get>
      ).mockResolvedValue(workspace);
      (
        Document.get as jest.MockedFunction<typeof Document.get>
      ).mockResolvedValue(document);
      (
        reVectorizeDocument as jest.MockedFunction<typeof reVectorizeDocument>
      ).mockResolvedValue(false);

      const response = await request(app)
        .post("/api/workspace/test-workspace/re-vectorize")
        .send({
          docId: "doc-id-123",
        })
        .expect(500);

      expect(response.body).toMatchObject({
        success: false,
        message: "Failed to re-vectorize document",
        docId: "doc-id-123",
        docPath: "path/to/doc.json",
      });
    });

    it("should handle errors during re-vectorization", async () => {
      const workspace = createMockWorkspace();
      const document = createMockDocument();

      (
        Workspace.get as jest.MockedFunction<typeof Workspace.get>
      ).mockResolvedValue(workspace);
      (
        Document.get as jest.MockedFunction<typeof Document.get>
      ).mockResolvedValue(document);
      (
        reVectorizeDocument as jest.MockedFunction<typeof reVectorizeDocument>
      ).mockRejectedValue(new Error("Vectorization service error"));

      const response = await request(app)
        .post("/api/workspace/test-workspace/re-vectorize")
        .send({
          docId: "doc-id-123",
        })
        .expect(500);

      expect(response.body).toMatchObject({
        success: false,
        message: "Vectorization service error",
      });
    });
  });

  describe("Edge Cases and Logging", () => {
    let consoleLogSpy: jest.SpyInstance;
    let consoleErrorSpy: jest.SpyInstance;

    beforeEach(() => {
      consoleLogSpy = jest.spyOn(console, "log").mockImplementation();
      consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();
    });

    afterEach(() => {
      consoleLogSpy.mockRestore();
      consoleErrorSpy.mockRestore();
    });

    it("should log appropriate messages during update-star operation", async () => {
      const workspace = createMockWorkspace();
      const document = createMockDocument();

      (
        Workspace.get as jest.MockedFunction<typeof Workspace.get>
      ).mockResolvedValue(workspace);
      (
        Document.get as jest.MockedFunction<typeof Document.get>
      ).mockResolvedValue(document);
      (
        isDocumentProperlyVectorized as jest.MockedFunction<
          typeof isDocumentProperlyVectorized
        >
      ).mockResolvedValue(true);
      (
        Document.update as jest.MockedFunction<typeof Document.update>
      ).mockResolvedValue({
        document: { ...document, starred: true },
        message: null,
      });

      await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "/path/to/document.pdf",
          starStatus: true,
        });

      // Verify logging occurred
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining("[HANDLER HIT] /update-star")
      );
      expect(consoleLogSpy).toHaveBeenCalledWith(
        "[UPDATE-STAR] Request details:",
        expect.any(Object)
      );
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining("[UPDATE-STAR] Successfully updated document")
      );
    });

    it("should handle nested folder paths correctly", async () => {
      const workspace = createMockWorkspace();
      (
        Workspace.get as jest.MockedFunction<typeof Workspace.get>
      ).mockResolvedValue(workspace);

      // Mock documents with various path structures
      const documents = [
        createMockDocument({ id: 1, docpath: "parent/target-folder/doc1.pdf" }),
        createMockDocument({ id: 2, docpath: "target-folder/doc2.pdf" }),
        createMockDocument({ id: 3, docpath: "other/target-folder/doc3.pdf" }),
        createMockDocument({
          id: 4,
          docpath: "target-folder-similar/doc4.pdf",
        }), // Should not match
      ];

      (
        Document.where as jest.MockedFunction<typeof Document.where>
      ).mockResolvedValue(documents);

      (
        isDocumentProperlyVectorized as jest.MockedFunction<
          typeof isDocumentProperlyVectorized
        >
      ).mockResolvedValue(true);

      (
        Document.update as jest.MockedFunction<typeof Document.update>
      ).mockResolvedValue({
        document: documents[0],
        message: null,
      });

      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "target-folder",
          starStatus: true,
          isFolder: true,
        })
        .expect(200);

      // Should update only documents 1, 2, and 3 (not 4)
      expect(response.body.updatedCount).toBe(3);
      expect(Document.update).toHaveBeenCalledTimes(3);
    });

    it("should handle paths with backslashes", async () => {
      const workspace = createMockWorkspace();
      const document = createMockDocument({ docpath: "folder\\document.pdf" });

      (
        Workspace.get as jest.MockedFunction<typeof Workspace.get>
      ).mockResolvedValue(workspace);
      (
        Document.get as jest.MockedFunction<typeof Document.get>
      ).mockResolvedValue(document);
      (
        isDocumentProperlyVectorized as jest.MockedFunction<
          typeof isDocumentProperlyVectorized
        >
      ).mockResolvedValue(true);
      (
        Document.update as jest.MockedFunction<typeof Document.update>
      ).mockResolvedValue({
        document: { ...document, starred: true },
        message: null,
      });

      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "folder\\document.pdf",
          starStatus: true,
        })
        .expect(200);

      expect(response.body.message).toBe(
        "Document star status updated successfully"
      );
    });
  });
});
