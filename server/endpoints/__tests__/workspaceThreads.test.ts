// IMPORTANT: Set test environment and mock dependencies BEFORE any imports
process.env.NODE_ENV = "test";
process.env.DATABASE_URL = "file:./test.db";

// Mock multiUserProtected middleware FIRST to prevent import errors
jest.mock("../../utils/middleware/multiUserProtected", () => ({
  __esModule: true,
  ROLES: {
    all: "<all>",
    admin: "admin",
    manager: "manager",
    superuser: "superuser",
    default: "default",
  },
  strictMultiUserRoleValid: jest.fn(() =>
    jest.fn((req: any, res: any, next: any) => {
      res.locals.multiUserMode = true;
      next();
    })
  ),
  flexUserRoleValid: jest.fn(() =>
    jest.fn((req: any, res: any, next: any) => {
      res.locals.multiUserMode = true;
      next();
    })
  ),
  legalTemplateScopeGuard: jest.fn(() =>
    jest.fn((req: any, res: any, next: any) => {
      res.locals.multiUserMode = true;
      next();
    })
  ),
  canManageSystemTemplates: jest.fn(() => true),
  canManageOrgTemplates: jest.fn(() => true),
  canManageUserTemplates: jest.fn(() => true),
  isMultiUserSetup: jest.fn((req: any, res: any, next: any) => next()),
}));

// Mock Prisma client first to prevent any database connections
jest.mock("../../utils/prisma", () => ({
  __esModule: true,
  default: {
    workspace_threads: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    workspaces: {
      findFirst: jest.fn(),
      findUnique: jest.fn(),
    },
    users: {
      findFirst: jest.fn(),
      findUnique: jest.fn(),
    },
    workspace_chats: {
      findMany: jest.fn(),
      delete: jest.fn(),
      createMany: jest.fn(),
    },
    thread_shares: {
      findMany: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
    },
    $disconnect: jest.fn(),
  },
}));

// Mock middleware modules before any imports that might use them
jest.mock("../../utils/middleware/validatedRequest", () => ({
  validatedRequest: jest.fn((req: any, res: any, next: any) => next()),
}));

jest.mock("../../utils/middleware/validWorkspace", () => ({
  validWorkspaceSlug: jest.fn((req: any, res: any, next: any) => {
    res.locals.workspace = {
      id: 1,
      name: "Test Workspace",
      slug: "test-workspace",
    };
    next();
  }),
  validWorkspaceAndThreadSlug: jest.fn((req: any, res: any, next: any) => {
    res.locals.workspace = {
      id: 1,
      name: "Test Workspace",
      slug: "test-workspace",
    };
    res.locals.thread = {
      id: 1,
      name: "Test Thread",
      slug: "test-thread",
      workspace_id: 1,
      user_id: 1,
      createdAt: new Date(),
      lastUpdatedAt: new Date(),
      sharedWithOrg: false,
    };
    next();
  }),
}));

// Mock HTTP utilities
jest.mock("../../utils/http", () => ({
  multiUserMode: jest.fn(() => true),
  userFromSession: jest.fn().mockResolvedValue({
    id: 1,
    username: "test-user",
    role: "admin",
    organizationId: 1,
  }),
  reqBody: jest.fn((req: any) => req.body || {}),
}));

// Mock models
jest.mock("../../models/workspaceThread", () => ({
  WorkspaceThread: {
    new: jest.fn(async (workspace, userId) => ({
      thread: {
        id: 1,
        name: "New Conversation",
        slug: "new-conversation-1",
        workspace_id: workspace.id,
        user_id: userId,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
        sharedWithOrg: false,
      },
      message: null,
    })),
    where: jest.fn(async (criteria) => {
      const mockThreads = [
        {
          id: 1,
          name: "Thread 1",
          slug: "thread-1",
          workspace_id: criteria.workspace_id,
          user_id: criteria.user_id,
          createdAt: new Date("2024-01-01"),
          lastUpdatedAt: new Date("2024-01-03"),
          sharedWithOrg: false,
        },
        {
          id: 2,
          name: "Thread 2",
          slug: "thread-2",
          workspace_id: criteria.workspace_id,
          user_id: criteria.user_id,
          createdAt: new Date("2024-01-02"),
          lastUpdatedAt: new Date("2024-01-02"),
          sharedWithOrg: false,
        },
      ];
      return mockThreads;
    }),
    get: jest.fn(async (criteria) => {
      // Handle thread lookups - be flexible with criteria
      if (criteria.slug === "test-thread" || criteria.id === 1) {
        return {
          id: 1,
          name: "Test Thread",
          slug: "test-thread",
          workspace_id: 1,
          user_id: 1,
          createdAt: new Date(),
          lastUpdatedAt: new Date(),
          sharedWithOrg: false,
        };
      }
      if (criteria.id === 999) {
        return null; // Not found
      }
      // Default fallback for other criteria
      return {
        id: criteria.id || 1,
        name: "Test Thread",
        slug: criteria.slug || "test-thread",
        workspace_id: criteria.workspace_id || 1,
        user_id: 1,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
        sharedWithOrg: false,
      };
    }),
    update: jest.fn(async (thread, updates) => ({
      thread: { ...thread, ...updates },
    })),
    delete: jest.fn(async (_criteria) => {
      // Mock successful deletion
      return true;
    }),
    fork: jest.fn(async (thread, userId) => ({
      id: thread.id + 100,
      name: `${thread.name} (Fork)`,
      slug: `${thread.slug}-fork`,
      workspace_id: thread.workspace_id,
      user_id: userId,
      createdAt: new Date(),
      lastUpdatedAt: new Date(),
      sharedWithOrg: false,
    })),
  },
}));

jest.mock("../../models/workspaceChats", () => ({
  WorkspaceChats: {
    forThread: jest.fn(async (threadId) => {
      if (threadId === 1) {
        return [
          {
            id: 1,
            prompt: "Hello",
            response: JSON.stringify({ text: "Hi there!" }),
            createdAt: new Date(),
            workspaceThreadId: threadId,
            user_id: 1,
          },
          {
            id: 2,
            prompt: "How are you?",
            response: JSON.stringify({ text: "I'm doing well!" }),
            createdAt: new Date(),
            workspaceThreadId: threadId,
            user_id: 1,
          },
        ];
      }
      return [];
    }),
    delete: jest.fn(async () => true),
    bulkCreate: jest.fn(async (chatsData: any) => ({
      chats: chatsData.map((data: any, index: number) => ({
        id: index + 1,
        workspaceId: data.workspaceId,
        prompt: data.prompt,
        response: data.response,
        user_id: data.user?.id,
        threadId: data.threadId,
        createdAt: new Date(),
      })),
    })),
  },
}));

jest.mock("../../models/workspace", () => ({
  Workspace: {
    get: jest.fn(async ({ slug }) => {
      if (slug === "test-workspace") {
        return {
          id: 1,
          name: "Test Workspace",
          slug: "test-workspace",
          user_id: 1,
          createdAt: new Date(),
        };
      }
      return null;
    }),
    getWithUser: jest.fn(async (user, criteria) => {
      if (criteria.slug === "test-workspace") {
        return {
          id: 1,
          name: "Test Workspace",
          slug: "test-workspace",
          user_id: 1,
          createdAt: new Date(),
        };
      }
      return null;
    }),
  },
}));

jest.mock("../../models/threadShare", () => ({
  ThreadShare: {
    getThreadIdsSharedWithUser: jest.fn(async (userId) => {
      if (userId === 1) {
        return [3, 4]; // Shared thread IDs
      }
      return [];
    }),
    hasAccess: jest.fn(async (threadId, userId) => {
      // User 1 has access to threads 1, 3, 4
      // User 2 has access to thread 2
      if (userId === 1) return [1, 3, 4].includes(threadId);
      if (userId === 2) return threadId === 2;
      return false;
    }),
    create: jest.fn(async (threadId, userId) => ({
      id: 1,
      threadId,
      userId,
      createdAt: new Date(),
    })),
  },
}));

jest.mock("../../models/user", () => ({
  User: {
    get: jest.fn(async ({ username }: { username: string }) => {
      const users: Record<string, any> = {
        testuser: {
          id: 2,
          username: "testuser",
          email: "<EMAIL>",
          role: "default",
        },
        admin: {
          id: 1,
          username: "admin",
          email: "<EMAIL>",
          role: "admin",
        },
      };
      return users[username] || null;
    }),
  },
}));

jest.mock("../../models/telemetry", () => ({
  Telemetry: {
    sendTelemetry: jest.fn().mockResolvedValue(true),
  },
}));

jest.mock("../../models/eventLogs", () => ({
  EventLogs: {
    logEvent: jest.fn().mockResolvedValue(true),
  },
}));

jest.mock("../../models/systemSettings", () => ({
  __esModule: true,
  default: {
    isMultiUserMode: jest.fn(async () => true), // Enable multi-user mode for tests
  },
}));

jest.mock("../../models/workspaceShare", () => ({
  WorkspaceShare: {
    hasAccess: jest.fn(async (_workspaceId, _userId) => {
      // Mock shared access - can be customized per test
      return false;
    }),
  },
}));

// Mock chat utilities
jest.mock("../../utils/helpers/chat/responses", () => ({
  convertToChatHistory: jest.fn(async (options: any, chats: any) => {
    return chats.map((chat: any) => ({
      id: chat.id,
      role: chat.prompt ? "user" : "assistant",
      content: chat.prompt || JSON.parse(chat.response || "{}").text,
      createdAt: chat.createdAt,
    }));
  }),
}));

// Mock middleware and utilities
jest.mock("../../utils/http", () => ({
  userFromSession: jest.fn(async (req, _res) => {
    const authHeader = req.headers.authorization;
    if (!authHeader) return null;

    const token = authHeader.replace("Bearer ", "");
    if (token === "admin-token") {
      return {
        id: 1,
        username: "admin",
        role: "admin",
        email: "<EMAIL>",
      };
    } else if (token === "user-token") {
      return {
        id: 2,
        username: "user",
        role: "default",
        email: "<EMAIL>",
      };
    } else if (token === "user2-token") {
      return {
        id: 3,
        username: "user2",
        role: "default",
        email: "<EMAIL>",
      };
    }
    return null;
  }),
  reqBody: jest.fn((req) => req.body),
  multiUserMode: jest.fn((res) => res.locals?.multiUserMode !== false),
}));

jest.mock("../../utils/middleware/validatedRequest", () => ({
  validatedRequest: jest.fn((req: any, res: any, next: any) => next()),
}));

jest.mock("../../utils/middleware/multiUserProtected", () => ({
  flexUserRoleValid: jest.fn(() => (req: any, res: any, next: any) => next()),
  strictMultiUserRoleValid: jest.fn(
    () => (req: any, res: any, next: any) => next()
  ),
  legalTemplateScopeGuard: jest.fn(
    () => (req: any, res: any, next: any) => next()
  ),
  ROLES: {
    admin: "admin",
    manager: "manager",
    default: "default",
    all: "<all>",
  },
}));

jest.mock("../../utils/middleware/validWorkspace", () => ({
  validWorkspaceSlug: jest.fn((req: any, res: any, next: any) => {
    res.locals = res.locals || {};
    res.locals.workspace = {
      id: 1,
      name: "Test Workspace",
      slug: req.params.slug || "test-workspace",
      user_id: 1,
    };
    next();
  }),
  validWorkspaceAndThreadSlug: jest.fn((req: any, res: any, next: any) => {
    res.locals = res.locals || {};
    res.locals.workspace = {
      id: 1,
      name: "Test Workspace",
      slug: req.params.slug || "test-workspace",
      user_id: 1,
    };
    res.locals.thread = {
      id: 1,
      name: "Test Thread",
      slug: req.params.threadSlug || "test-thread",
      workspace_id: 1,
      user_id: 1,
    };
    next();
  }),
}));

import request from "supertest";
import { describe, expect, it, beforeEach, afterEach } from "@jest/globals";
import app from "../../index";

/**
 * Workspace Threads Endpoints Test Suite
 *
 * Tests thread management functionality including:
 * - Thread creation and retrieval
 * - Thread updates and deletion
 * - Thread sharing and forking
 * - Chat history management
 * - Bulk operations
 * - Thread pinning (placeholder)
 * - Authentication and authorization
 */

describe("Workspace Threads Endpoints", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Reset middleware mocks
    const {
      validWorkspaceAndThreadSlug,
    } = require("../../utils/middleware/validWorkspace");
    validWorkspaceAndThreadSlug.mockImplementation(
      (req: any, res: any, next: any) => {
        res.locals = res.locals || {};
        res.locals.workspace = {
          id: 1,
          name: "Test Workspace",
          slug: req.params.slug || "test-workspace",
          user_id: 1,
        };
        res.locals.thread = {
          id: 1,
          name: "Test Thread",
          slug: req.params.threadSlug || "test-thread",
          workspace_id: 1,
          user_id: 1,
        };
        next();
      }
    );

    // Set up environment variables
    process.env.LLM_PROVIDER = "openai";
    process.env.EMBEDDING_ENGINE = "openai";
    process.env.VECTOR_DB = "lancedb";
    process.env.TTS_PROVIDER = "native";
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("POST /api/workspace/:slug/thread/new", () => {
    it("should create new thread successfully", async () => {
      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/new")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(200);
      expect(response.body.thread).toMatchObject({
        id: 1,
        name: "New Conversation",
        slug: "new-conversation-1",
        workspace_id: 1,
        user_id: 2,
      });
    });

    it("should use document-drafting LLM settings", async () => {
      process.env.LLM_PROVIDER_DD = "anthropic";
      process.env.EMBEDDING_ENGINE_DD = "openai";
      process.env.VECTOR_DB_DD = "pinecone";

      const response = await request(app)
        .post("/api/workspace/document-drafting/thread/new")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(200);
    });

    it("should send telemetry for thread creation", async () => {
      const { Telemetry } = require("../../models/telemetry");

      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/new")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(200);
      expect(Telemetry.sendTelemetry).toHaveBeenCalledWith(
        "workspace_thread_created",
        expect.objectContaining({
          multiUserMode: true,
          LLMSelection: "openai",
        }),
        "2"
      );
    });

    it("should log event for thread creation", async () => {
      const { EventLogs } = require("../../models/eventLogs");

      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/new")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(200);
      expect(EventLogs.logEvent).toHaveBeenCalledWith(
        "workspace_thread_created",
        { workspaceName: "Test Workspace" },
        2
      );
    });

    it("should require authentication", async () => {
      const response = await request(app).post(
        "/api/workspace/test-workspace/thread/new"
      );

      expect(response.status).toBe(401);
    });

    it("should handle creation errors", async () => {
      // Mock WorkspaceThread.new to throw error
      const { WorkspaceThread } = require("../../models/workspaceThread");
      WorkspaceThread.new.mockRejectedValue(new Error("Creation failed"));

      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/new")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(500);
    });
  });

  describe("GET /api/workspace/:slug/threads", () => {
    it("should list user's threads", async () => {
      const response = await request(app)
        .get("/api/workspace/test-workspace/threads")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(200);
      expect(response.body.threads).toHaveLength(2);
      expect(response.body.threads[0]).toMatchObject({
        id: 1,
        name: "Thread 1",
        workspace_id: 1,
      });
    });

    it("should include shared threads in multi-user mode", async () => {
      // Mock shared threads
      const { WorkspaceThread } = require("../../models/workspaceThread");
      WorkspaceThread.get
        .mockResolvedValueOnce({
          id: 3,
          name: "Shared Thread 1",
          slug: "shared-thread-1",
          workspace_id: 1,
          user_id: 3,
          createdAt: new Date("2024-01-03"),
          lastUpdatedAt: new Date("2024-01-04"),
          sharedWithOrg: false,
        })
        .mockResolvedValueOnce({
          id: 4,
          name: "Shared Thread 2",
          slug: "shared-thread-2",
          workspace_id: 1,
          user_id: 3,
          createdAt: new Date("2024-01-04"),
          lastUpdatedAt: new Date("2024-01-05"),
          sharedWithOrg: false,
        });

      const response = await request(app)
        .get("/api/workspace/test-workspace/threads")
        .set("Authorization", "Bearer admin-token");

      expect(response.status).toBe(200);
      expect(response.body.threads.length).toBeGreaterThanOrEqual(2);
    });

    it("should sort threads by lastUpdatedAt", async () => {
      const response = await request(app)
        .get("/api/workspace/test-workspace/threads")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(200);
      const threads = response.body.threads;

      // Should be sorted newest first
      for (let i = 1; i < threads.length; i++) {
        const current = new Date(threads[i].lastUpdatedAt);
        const previous = new Date(threads[i - 1].lastUpdatedAt);
        expect(current.getTime()).toBeLessThanOrEqual(previous.getTime());
      }
    });

    it("should require authentication", async () => {
      const response = await request(app).get(
        "/api/workspace/test-workspace/threads"
      );

      expect(response.status).toBe(401);
    });

    it("should return 401 for unauthenticated user", async () => {
      // Mock userFromSession to return null
      const { userFromSession } = require("../../utils/http");
      userFromSession.mockResolvedValueOnce(null);

      const response = await request(app)
        .get("/api/workspace/test-workspace/threads")
        .set("Authorization", "Bearer invalid-token");

      expect(response.status).toBe(401);
    });

    it("should handle database errors", async () => {
      // Mock WorkspaceThread.where to throw error
      const { WorkspaceThread } = require("../../models/workspaceThread");
      WorkspaceThread.where.mockRejectedValue(new Error("Database error"));

      const response = await request(app)
        .get("/api/workspace/test-workspace/threads")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(500);
    });
  });

  describe("DELETE /api/workspace/:slug/thread/:threadSlug", () => {
    it("should delete thread successfully for owner", async () => {
      const response = await request(app)
        .delete("/api/workspace/test-workspace/thread/test-thread")
        .set("Authorization", "Bearer admin-token"); // Owner

      expect(response.status).toBe(200);
    });

    it("should delete thread for admin even if not owner", async () => {
      // Mock thread owned by different user
      const validWorkspaceAndThreadSlug =
        require("../../utils/middleware/validWorkspace").validWorkspaceAndThreadSlug;
      validWorkspaceAndThreadSlug.mockImplementation(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          res.locals.workspace = {
            id: 1,
            name: "Test Workspace",
            slug: "test-workspace",
          };
          res.locals.thread = {
            id: 1,
            name: "Test Thread",
            slug: "test-thread",
            workspace_id: 1,
            user_id: 3, // Different user
          };
          next();
        }
      );

      const response = await request(app)
        .delete("/api/workspace/test-workspace/thread/test-thread")
        .set("Authorization", "Bearer admin-token");

      expect(response.status).toBe(200);
    });

    it("should prevent non-owner from deleting thread", async () => {
      // Mock thread owned by different user
      const validWorkspaceAndThreadSlug =
        require("../../utils/middleware/validWorkspace").validWorkspaceAndThreadSlug;
      validWorkspaceAndThreadSlug.mockImplementation(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          res.locals.workspace = {
            id: 1,
            name: "Test Workspace",
            slug: "test-workspace",
          };
          res.locals.thread = {
            id: 1,
            name: "Test Thread",
            slug: "test-thread",
            workspace_id: 1,
            user_id: 1, // Different user
          };
          next();
        }
      );

      const response = await request(app)
        .delete("/api/workspace/test-workspace/thread/test-thread")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(403);
      expect(response.body.error).toBe("Unauthorized to delete this thread");
    });

    it("should return 404 for non-existent thread", async () => {
      const validWorkspaceAndThreadSlug =
        require("../../utils/middleware/validWorkspace").validWorkspaceAndThreadSlug;
      validWorkspaceAndThreadSlug.mockImplementation(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          res.locals.workspace = {
            id: 1,
            name: "Test Workspace",
            slug: "test-workspace",
          };
          res.locals.thread = null;
          next();
        }
      );

      const response = await request(app)
        .delete("/api/workspace/test-workspace/thread/non-existent")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(404);
      expect(response.body.error).toBe("Thread not found");
    });

    it("should log deletion event", async () => {
      const { EventLogs } = require("../../models/eventLogs");

      const response = await request(app)
        .delete("/api/workspace/test-workspace/thread/test-thread")
        .set("Authorization", "Bearer admin-token");

      expect(response.status).toBe(200);
      expect(EventLogs.logEvent).toHaveBeenCalledWith(
        "workspace_thread_deleted",
        {
          workspaceName: "Test Workspace",
          threadName: "Test Thread",
        },
        1
      );
    });

    it("should handle deletion errors", async () => {
      // Mock WorkspaceThread.delete to throw error
      const { WorkspaceThread } = require("../../models/workspaceThread");
      WorkspaceThread.delete.mockRejectedValue(new Error("Delete failed"));

      const response = await request(app)
        .delete("/api/workspace/test-workspace/thread/test-thread")
        .set("Authorization", "Bearer admin-token");

      expect(response.status).toBe(500);
    });
  });

  describe("DELETE /api/workspace/:slug/threads", () => {
    it("should delete all user threads", async () => {
      const { WorkspaceThread } = require("../../models/workspaceThread");

      // Mock successful deletion
      WorkspaceThread.delete.mockResolvedValue(true);

      const response = await request(app)
        .delete("/api/workspace/test-workspace/threads")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(200);
    });

    it("should require authentication", async () => {
      const response = await request(app).delete(
        "/api/workspace/test-workspace/threads"
      );

      expect(response.status).toBe(401);
    });

    it("should return 401 for unauthenticated user", async () => {
      const { userFromSession } = require("../../utils/http");
      userFromSession.mockResolvedValueOnce(null);

      const response = await request(app)
        .delete("/api/workspace/test-workspace/threads")
        .set("Authorization", "Bearer invalid-token");

      expect(response.status).toBe(401);
    });

    it("should log bulk deletion event", async () => {
      const { EventLogs } = require("../../models/eventLogs");
      const { WorkspaceThread } = require("../../models/workspaceThread");

      // Mock successful deletion
      WorkspaceThread.delete.mockResolvedValue(true);

      const response = await request(app)
        .delete("/api/workspace/test-workspace/threads")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(200);
      expect(EventLogs.logEvent).toHaveBeenCalledWith(
        "workspace_threads_deleted",
        { workspaceName: "Test Workspace" },
        2
      );
    });

    it("should handle deletion errors", async () => {
      const { WorkspaceThread } = require("../../models/workspaceThread");
      WorkspaceThread.delete.mockRejectedValue(new Error("Delete failed"));

      const response = await request(app)
        .delete("/api/workspace/test-workspace/threads")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(500);
    });
  });

  describe("GET /api/workspace/:slug/thread/:threadSlug/chats", () => {
    it("should retrieve chat history for thread owner", async () => {
      const response = await request(app)
        .get("/api/workspace/test-workspace/thread/test-thread/chats")
        .set("Authorization", "Bearer admin-token");

      expect(response.status).toBe(200);
      expect(response.body.history).toHaveLength(2);
    });

    it("should allow access to shared threads", async () => {
      // Mock thread access for user 2
      const { ThreadShare } = require("../../models/threadShare");
      ThreadShare.hasAccess.mockResolvedValue(true);

      const response = await request(app)
        .get("/api/workspace/test-workspace/thread/test-thread/chats")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(200);
    });

    it("should allow admin access to any thread", async () => {
      const validWorkspaceAndThreadSlug =
        require("../../utils/middleware/validWorkspace").validWorkspaceAndThreadSlug;
      validWorkspaceAndThreadSlug.mockImplementation(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          res.locals.workspace = {
            id: 1,
            name: "Test Workspace",
            slug: "test-workspace",
          };
          res.locals.thread = {
            id: 1,
            name: "Test Thread",
            slug: "test-thread",
            workspace_id: 1,
            user_id: 3, // Different user
          };
          next();
        }
      );

      const response = await request(app)
        .get("/api/workspace/test-workspace/thread/test-thread/chats")
        .set("Authorization", "Bearer admin-token");

      expect(response.status).toBe(200);
    });

    it("should deny access to unauthorized users", async () => {
      // Mock thread owned by different user with no sharing
      const validWorkspaceAndThreadSlug =
        require("../../utils/middleware/validWorkspace").validWorkspaceAndThreadSlug;
      validWorkspaceAndThreadSlug.mockImplementation(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          res.locals.workspace = {
            id: 1,
            name: "Test Workspace",
            slug: "test-workspace",
          };
          res.locals.thread = {
            id: 1,
            name: "Test Thread",
            slug: "test-thread",
            workspace_id: 1,
            user_id: 1, // Different user
          };
          next();
        }
      );

      const { ThreadShare } = require("../../models/threadShare");
      ThreadShare.hasAccess.mockResolvedValue(false);

      const response = await request(app)
        .get("/api/workspace/test-workspace/thread/test-thread/chats")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(403);
      expect(response.body.error).toBe("Unauthorized to access this thread");
    });

    it("should return 404 for non-existent thread", async () => {
      const validWorkspaceAndThreadSlug =
        require("../../utils/middleware/validWorkspace").validWorkspaceAndThreadSlug;
      validWorkspaceAndThreadSlug.mockImplementation(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          res.locals.workspace = {
            id: 1,
            name: "Test Workspace",
            slug: "test-workspace",
          };
          res.locals.thread = null;
          next();
        }
      );

      const response = await request(app)
        .get("/api/workspace/test-workspace/thread/non-existent/chats")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(404);
      expect(response.body.error).toBe("Thread not found");
    });

    it("should handle chat retrieval errors", async () => {
      const { WorkspaceChats } = require("../../models/workspaceChats");
      WorkspaceChats.forThread.mockRejectedValue(new Error("Database error"));

      const response = await request(app)
        .get("/api/workspace/test-workspace/thread/test-thread/chats")
        .set("Authorization", "Bearer admin-token");

      expect(response.status).toBe(500);
    });
  });

  describe("POST /api/workspace/:slug/thread/:threadSlug/update", () => {
    it("should update thread name and slug", async () => {
      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/update")
        .set("Authorization", "Bearer admin-token")
        .send({
          name: "Updated Thread Name",
          slug: "updated-thread-slug",
        });

      expect(response.status).toBe(200);
      expect(response.body.thread).toMatchObject({
        name: "Updated Thread Name",
        slug: "updated-thread-slug",
      });
    });

    it("should allow partial updates", async () => {
      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/update")
        .set("Authorization", "Bearer admin-token")
        .send({
          name: "New Name Only",
        });

      expect(response.status).toBe(200);
      expect(response.body.thread.name).toBe("New Name Only");
    });

    it("should prevent non-owner updates", async () => {
      const validWorkspaceAndThreadSlug =
        require("../../utils/middleware/validWorkspace").validWorkspaceAndThreadSlug;
      validWorkspaceAndThreadSlug.mockImplementation(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          res.locals.workspace = {
            id: 1,
            name: "Test Workspace",
            slug: "test-workspace",
          };
          res.locals.thread = {
            id: 1,
            name: "Test Thread",
            slug: "test-thread",
            workspace_id: 1,
            user_id: 1, // Different user
          };
          next();
        }
      );

      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/update")
        .set("Authorization", "Bearer user-token")
        .send({ name: "Unauthorized Update" });

      expect(response.status).toBe(403);
      expect(response.body.error).toBe("Unauthorized to update this thread");
    });

    it("should return 404 for non-existent thread", async () => {
      const validWorkspaceAndThreadSlug =
        require("../../utils/middleware/validWorkspace").validWorkspaceAndThreadSlug;
      validWorkspaceAndThreadSlug.mockImplementation(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          res.locals.workspace = {
            id: 1,
            name: "Test Workspace",
            slug: "test-workspace",
          };
          res.locals.thread = null;
          next();
        }
      );

      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/non-existent/update")
        .set("Authorization", "Bearer user-token")
        .send({ name: "New Name" });

      expect(response.status).toBe(404);
      expect(response.body.error).toBe("Thread not found");
    });

    it("should handle update errors", async () => {
      const { WorkspaceThread } = require("../../models/workspaceThread");
      WorkspaceThread.update.mockRejectedValue(new Error("Update failed"));

      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/update")
        .set("Authorization", "Bearer admin-token")
        .send({ name: "New Name" });

      expect(response.status).toBe(500);
    });
  });

  describe("DELETE /api/workspace/:slug/thread/:threadSlug/chats", () => {
    it("should clear thread chats for owner", async () => {
      const response = await request(app)
        .delete("/api/workspace/test-workspace/thread/test-thread/chats")
        .set("Authorization", "Bearer admin-token");

      expect(response.status).toBe(200);
    });

    it("should prevent non-owner from clearing chats", async () => {
      const validWorkspaceAndThreadSlug =
        require("../../utils/middleware/validWorkspace").validWorkspaceAndThreadSlug;
      validWorkspaceAndThreadSlug.mockImplementation(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          res.locals.workspace = {
            id: 1,
            name: "Test Workspace",
            slug: "test-workspace",
          };
          res.locals.thread = {
            id: 1,
            name: "Test Thread",
            slug: "test-thread",
            workspace_id: 1,
            user_id: 1, // Different user
          };
          next();
        }
      );

      const response = await request(app)
        .delete("/api/workspace/test-workspace/thread/test-thread/chats")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(403);
      expect(response.body.error).toBe("Unauthorized to clear this thread");
    });

    it("should return 404 for non-existent thread", async () => {
      const validWorkspaceAndThreadSlug =
        require("../../utils/middleware/validWorkspace").validWorkspaceAndThreadSlug;
      validWorkspaceAndThreadSlug.mockImplementation(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          res.locals.workspace = {
            id: 1,
            name: "Test Workspace",
            slug: "test-workspace",
          };
          res.locals.thread = null;
          next();
        }
      );

      const response = await request(app)
        .delete("/api/workspace/test-workspace/thread/non-existent/chats")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(404);
      expect(response.body.error).toBe("Thread not found");
    });

    it("should handle deletion errors", async () => {
      const { WorkspaceChats } = require("../../models/workspaceChats");
      WorkspaceChats.delete.mockRejectedValue(new Error("Delete failed"));

      const response = await request(app)
        .delete("/api/workspace/test-workspace/thread/test-thread/chats")
        .set("Authorization", "Bearer admin-token");

      expect(response.status).toBe(500);
    });
  });

  describe("POST /api/workspace/:slug/thread/:threadSlug/share", () => {
    it("should share thread with user", async () => {
      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/share")
        .set("Authorization", "Bearer admin-token")
        .send({ username: "testuser" });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe("Thread shared successfully");
    });

    it("should prevent non-owner from sharing", async () => {
      const validWorkspaceAndThreadSlug =
        require("../../utils/middleware/validWorkspace").validWorkspaceAndThreadSlug;
      validWorkspaceAndThreadSlug.mockImplementation(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          res.locals.workspace = {
            id: 1,
            name: "Test Workspace",
            slug: "test-workspace",
          };
          res.locals.thread = {
            id: 1,
            name: "Test Thread",
            slug: "test-thread",
            workspace_id: 1,
            user_id: 1, // Different user
          };
          next();
        }
      );

      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/share")
        .set("Authorization", "Bearer user-token")
        .send({ username: "testuser" });

      expect(response.status).toBe(403);
      expect(response.body.error).toBe("Unauthorized to share this thread");
    });

    it("should require username", async () => {
      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/share")
        .set("Authorization", "Bearer admin-token")
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.error).toBe("Username is required");
    });

    it("should return 404 for non-existent user", async () => {
      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/share")
        .set("Authorization", "Bearer admin-token")
        .send({ username: "nonexistent" });

      expect(response.status).toBe(404);
      expect(response.body.error).toBe("User not found");
    });

    it("should log sharing event", async () => {
      const { EventLogs } = require("../../models/eventLogs");

      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/share")
        .set("Authorization", "Bearer admin-token")
        .send({ username: "testuser" });

      expect(response.status).toBe(200);
      expect(EventLogs.logEvent).toHaveBeenCalledWith(
        "workspace_thread_shared",
        {
          workspaceName: "Test Workspace",
          threadName: "Test Thread",
          sharedWith: "testuser",
        },
        1
      );
    });

    it("should handle sharing errors", async () => {
      const { ThreadShare } = require("../../models/threadShare");
      ThreadShare.create.mockRejectedValue(new Error("Share failed"));

      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/share")
        .set("Authorization", "Bearer admin-token")
        .send({ username: "testuser" });

      expect(response.status).toBe(500);
    });
  });

  describe("POST /api/workspace/:slug/thread/:threadSlug/fork", () => {
    it("should fork thread for authorized user", async () => {
      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/fork")
        .set("Authorization", "Bearer admin-token");

      expect(response.status).toBe(200);
      expect(response.body.thread).toMatchObject({
        id: 101, // Original ID + 100
        name: "Test Thread (Fork)",
        slug: "test-thread-fork",
        user_id: 1,
      });
    });

    it("should allow forking shared threads", async () => {
      const { ThreadShare } = require("../../models/threadShare");
      ThreadShare.hasAccess.mockResolvedValue(true);

      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/fork")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(200);
    });

    it("should prevent unauthorized forking", async () => {
      const validWorkspaceAndThreadSlug =
        require("../../utils/middleware/validWorkspace").validWorkspaceAndThreadSlug;
      validWorkspaceAndThreadSlug.mockImplementation(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          res.locals.workspace = {
            id: 1,
            name: "Test Workspace",
            slug: "test-workspace",
          };
          res.locals.thread = {
            id: 1,
            name: "Test Thread",
            slug: "test-thread",
            workspace_id: 1,
            user_id: 1, // Different user
          };
          next();
        }
      );

      const { ThreadShare } = require("../../models/threadShare");
      ThreadShare.hasAccess.mockResolvedValue(false);

      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/fork")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(403);
      expect(response.body.error).toBe("Unauthorized to fork this thread");
    });

    it("should log forking event", async () => {
      const { EventLogs } = require("../../models/eventLogs");

      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/fork")
        .set("Authorization", "Bearer admin-token");

      expect(response.status).toBe(200);
      expect(EventLogs.logEvent).toHaveBeenCalledWith(
        "workspace_thread_forked",
        {
          workspaceName: "Test Workspace",
          originalThreadName: "Test Thread",
          forkedThreadName: "Test Thread (Fork)",
        },
        1
      );
    });

    it("should handle forking errors", async () => {
      const { WorkspaceThread } = require("../../models/workspaceThread");
      WorkspaceThread.fork.mockRejectedValue(new Error("Fork failed"));

      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/fork")
        .set("Authorization", "Bearer admin-token");

      expect(response.status).toBe(500);
    });
  });

  describe("GET /api/workspace/:slug/thread/:threadId", () => {
    it("should get thread by ID for authorized user", async () => {
      const response = await request(app)
        .get("/api/workspace/test-workspace/thread/1")
        .set("Authorization", "Bearer admin-token");

      expect(response.status).toBe(200);
      expect(response.body.thread).toMatchObject({
        id: 1,
        name: "Test Thread",
        workspace_id: 1,
      });
    });

    it("should return 400 for invalid parameters", async () => {
      const response = await request(app)
        .get("/api/workspace/test-workspace/thread/")
        .set("Authorization", "Bearer admin-token");

      expect(response.status).toBe(404); // Route not found
    });

    it("should return 404 for non-existent workspace", async () => {
      const { Workspace } = require("../../models/workspace");
      Workspace.get.mockResolvedValueOnce(null);

      const response = await request(app)
        .get("/api/workspace/non-existent/thread/1")
        .set("Authorization", "Bearer admin-token");

      expect(response.status).toBe(404);
      expect(response.body.error).toBe("Workspace not found.");
    });

    it("should return 404 for non-existent thread", async () => {
      const response = await request(app)
        .get("/api/workspace/test-workspace/thread/999")
        .set("Authorization", "Bearer admin-token");

      expect(response.status).toBe(404);
      expect(response.body.error).toBe("Thread not found.");
    });

    it("should prevent unauthorized access", async () => {
      const { WorkspaceThread } = require("../../models/workspaceThread");
      WorkspaceThread.get.mockResolvedValue({
        id: 1,
        name: "Test Thread",
        workspace_id: 1,
        user_id: 1, // Different user
      });

      const { ThreadShare } = require("../../models/threadShare");
      ThreadShare.hasAccess.mockResolvedValue(false);

      const response = await request(app)
        .get("/api/workspace/test-workspace/thread/1")
        .set("Authorization", "Bearer user-token");

      expect(response.status).toBe(403);
      expect(response.body.error).toBe("Unauthorized to access this thread.");
    });

    it("should handle database errors", async () => {
      const { Workspace } = require("../../models/workspace");
      Workspace.get.mockRejectedValue(new Error("Database error"));

      const response = await request(app)
        .get("/api/workspace/test-workspace/thread/1")
        .set("Authorization", "Bearer admin-token");

      expect(response.status).toBe(500);
      expect(response.body.error).toBe(
        "Internal server error occurred while fetching thread."
      );
    });
  });

  describe("POST /api/workspace/:slug/thread/:threadSlug/insert-history", () => {
    const validMessages = [
      { role: "user", content: "Hello" },
      { role: "assistant", content: "Hi there!" },
      { role: "user", content: "How are you?" },
      { role: "assistant", content: "I'm doing well!" },
    ];

    it("should insert chat history successfully", async () => {
      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/insert-history")
        .set("Authorization", "Bearer admin-token")
        .send({ messages: validMessages });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.count).toBe(2); // 2 chat pairs
    });

    it("should validate message structure", async () => {
      const invalidMessages = [
        { role: "user" }, // Missing content
        { content: "Missing role" }, // Missing role
      ];

      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/insert-history")
        .set("Authorization", "Bearer admin-token")
        .send({ messages: invalidMessages });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe(
        "Each message must have role and content"
      );
    });

    it("should validate message roles", async () => {
      const invalidRoleMessages = [
        { role: "invalid", content: "Invalid role" },
      ];

      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/insert-history")
        .set("Authorization", "Bearer admin-token")
        .send({ messages: invalidRoleMessages });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe(
        "Invalid role. Must be user, assistant, or system"
      );
    });

    it("should require messages array", async () => {
      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/insert-history")
        .set("Authorization", "Bearer admin-token")
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.error).toBe("No messages provided");
    });

    it("should prevent non-owner from inserting history", async () => {
      const validWorkspaceAndThreadSlug =
        require("../../utils/middleware/validWorkspace").validWorkspaceAndThreadSlug;
      validWorkspaceAndThreadSlug.mockImplementation(
        (req: any, res: any, next: any) => {
          res.locals = res.locals || {};
          res.locals.workspace = {
            id: 1,
            name: "Test Workspace",
            slug: "test-workspace",
          };
          res.locals.thread = {
            id: 1,
            name: "Test Thread",
            slug: "test-thread",
            workspace_id: 1,
            user_id: 1, // Different user
          };
          next();
        }
      );

      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/insert-history")
        .set("Authorization", "Bearer user-token")
        .send({ messages: validMessages });

      expect(response.status).toBe(403);
      expect(response.body.error).toBe("Unauthorized to modify this thread");
    });

    it("should handle bulk creation errors", async () => {
      const { WorkspaceChats } = require("../../models/workspaceChats");
      WorkspaceChats.bulkCreate.mockResolvedValue({
        chats: null,
        message: "Creation failed",
      });

      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/insert-history")
        .set("Authorization", "Bearer admin-token")
        .send({ messages: validMessages });

      expect(response.status).toBe(500);
      expect(response.body.error).toBe("Creation failed");
    });

    it("should log history import event", async () => {
      const { EventLogs } = require("../../models/eventLogs");
      const { WorkspaceChats } = require("../../models/workspaceChats");

      // Mock successful bulk creation with proper return format
      WorkspaceChats.bulkCreate.mockResolvedValue({
        chats: [
          {
            id: 1,
            prompt: "Hello",
            response: JSON.stringify({ text: "Hi there!" }),
          },
          {
            id: 2,
            prompt: "How are you?",
            response: JSON.stringify({ text: "I'm doing well!" }),
          },
        ],
      });

      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/insert-history")
        .set("Authorization", "Bearer admin-token")
        .send({ messages: validMessages });

      expect(response.status).toBe(200);
      expect(EventLogs.logEvent).toHaveBeenCalledWith(
        "workspace_thread_history_imported",
        {
          workspaceName: "Test Workspace",
          threadName: "Test Thread",
          messageCount: 2,
        },
        1
      );
    });

    it("should handle missing pairs gracefully", async () => {
      const { WorkspaceChats } = require("../../models/workspaceChats");

      // Mock successful bulk creation for odd messages
      WorkspaceChats.bulkCreate.mockResolvedValue({
        chats: [
          {
            id: 1,
            prompt: "Hello",
            response: JSON.stringify({ text: "Response" }),
          },
        ],
      });

      const oddMessages = [
        { role: "user", content: "Hello" },
        { role: "user", content: "Another user message" },
        { role: "assistant", content: "Response" },
      ];

      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/insert-history")
        .set("Authorization", "Bearer admin-token")
        .send({ messages: oddMessages });

      expect(response.status).toBe(200);
    });
  });

  describe("Placeholder Endpoints", () => {
    describe("POST /api/workspace/:slug/threads/update-order", () => {
      it("should accept thread order updates (placeholder)", async () => {
        // Mock the WorkspaceThread.get to return proper threads
        const { WorkspaceThread } = require("../../models/workspaceThread");
        WorkspaceThread.get.mockImplementation(
          async ({ id }: { id: number }) => {
            if ([1, 2, 3].includes(id)) {
              return {
                id,
                name: `Thread ${id}`,
                slug: `thread-${id}`,
                workspace_id: 1,
                user_id: 1, // Same as authenticated user
                createdAt: new Date(),
                lastUpdatedAt: new Date(),
                sharedWithOrg: false,
              };
            }
            return null;
          }
        );

        const response = await request(app)
          .post("/api/workspace/test-workspace/threads/update-order")
          .set("Authorization", "Bearer admin-token") // Use admin token for consistency
          .send({ threadIds: [1, 2, 3] });

        expect(response.status).toBe(200);
        expect(response.body.message).toBe("Thread order updated successfully");
        expect(response.body.note).toBe(
          "Database schema update required for full implementation"
        );
      });

      it("should validate thread IDs", async () => {
        const response = await request(app)
          .post("/api/workspace/test-workspace/threads/update-order")
          .set("Authorization", "Bearer user-token")
          .send({ threadIds: ["invalid", "ids"] });

        expect(response.status).toBe(400);
        expect(response.body.error).toBe("Invalid thread ID format");
      });

      it("should require authentication", async () => {
        const response = await request(app)
          .post("/api/workspace/test-workspace/threads/update-order")
          .send({ threadIds: [1, 2, 3] });

        expect(response.status).toBe(401);
      });
    });

    describe("POST /api/workspace/:slug/thread/:threadSlug/pin", () => {
      it("should pin thread (placeholder)", async () => {
        const response = await request(app)
          .post("/api/workspace/test-workspace/thread/test-thread/pin")
          .set("Authorization", "Bearer admin-token");

        expect(response.status).toBe(200);
        expect(response.body.message).toBe("Thread pinned successfully");
        expect(response.body.note).toBe(
          "Database schema update required for full implementation"
        );
      });

      it("should prevent non-owner from pinning", async () => {
        const validWorkspaceAndThreadSlug =
          require("../../utils/middleware/validWorkspace").validWorkspaceAndThreadSlug;
        validWorkspaceAndThreadSlug.mockImplementation(
          (req: any, res: any, next: any) => {
            res.locals = res.locals || {};
            res.locals.workspace = {
              id: 1,
              name: "Test Workspace",
              slug: "test-workspace",
            };
            res.locals.thread = {
              id: 1,
              name: "Test Thread",
              slug: "test-thread",
              workspace_id: 1,
              user_id: 1, // Different user
            };
            next();
          }
        );

        const response = await request(app)
          .post("/api/workspace/test-workspace/thread/test-thread/pin")
          .set("Authorization", "Bearer user-token");

        expect(response.status).toBe(403);
        expect(response.body.error).toBe("Unauthorized to pin this thread");
      });
    });

    describe("DELETE /api/workspace/:slug/thread/:threadSlug/pin", () => {
      it("should unpin thread (placeholder)", async () => {
        const response = await request(app)
          .delete("/api/workspace/test-workspace/thread/test-thread/pin")
          .set("Authorization", "Bearer admin-token");

        expect(response.status).toBe(200);
        expect(response.body.message).toBe("Thread unpinned successfully");
        expect(response.body.note).toBe(
          "Database schema update required for full implementation"
        );
      });
    });

    describe("DELETE /api/workspace/:slug/thread-bulk-delete", () => {
      it("should delete threads in bulk", async () => {
        const response = await request(app)
          .delete("/api/workspace/test-workspace/thread-bulk-delete")
          .set("Authorization", "Bearer user-token")
          .send({ slugs: ["thread-1", "thread-2"] });

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });

      it("should require slugs array", async () => {
        const response = await request(app)
          .delete("/api/workspace/test-workspace/thread-bulk-delete")
          .set("Authorization", "Bearer user-token")
          .send({});

        expect(response.status).toBe(400);
        expect(response.body.error).toBe("No thread slugs provided");
      });

      it("should handle workspace not found", async () => {
        const { Workspace } = require("../../models/workspace");
        Workspace.getWithUser.mockResolvedValueOnce(null);
        Workspace.get.mockResolvedValueOnce(null);

        const response = await request(app)
          .delete("/api/workspace/non-existent/thread-bulk-delete")
          .set("Authorization", "Bearer user-token")
          .send({ slugs: ["thread-1"] });

        expect(response.status).toBe(404);
        expect(response.body.error).toBe("Workspace not found");
      });
    });
  });

  describe("Error Handling", () => {
    it("should handle missing middleware setup", async () => {
      // Test with minimal middleware setup
      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/new")
        .set("Authorization", "Bearer user-token");

      // Should still work with basic setup
      expect([200, 500]).toContain(response.status);
    });

    it("should handle malformed request bodies", async () => {
      const response = await request(app)
        .post("/api/workspace/test-workspace/thread/test-thread/update")
        .set("Authorization", "Bearer admin-token")
        .set("Content-Type", "application/json")
        .send("invalid json");

      expect(response.status).toBe(400);
    });

    it("should handle concurrent thread operations", async () => {
      const operations = [
        request(app)
          .post("/api/workspace/test-workspace/thread/new")
          .set("Authorization", "Bearer user-token"),
        request(app)
          .get("/api/workspace/test-workspace/threads")
          .set("Authorization", "Bearer user-token"),
        request(app)
          .post("/api/workspace/test-workspace/thread/test-thread/update")
          .set("Authorization", "Bearer admin-token")
          .send({ name: "Concurrent Update" }),
      ];

      const responses = await Promise.all(operations);

      // All operations should complete without hanging
      responses.forEach((response) => {
        expect(response.status).toBeDefined();
      });
    });

    it("should handle database connection failures", async () => {
      // Mock all database operations to fail
      const { WorkspaceThread } = require("../../models/workspaceThread");
      const { WorkspaceChats } = require("../../models/workspaceChats");
      const { ThreadShare } = require("../../models/threadShare");

      WorkspaceThread.new.mockRejectedValue(new Error("Database unavailable"));
      WorkspaceThread.where.mockRejectedValue(
        new Error("Database unavailable")
      );
      WorkspaceChats.forThread.mockRejectedValue(
        new Error("Database unavailable")
      );
      ThreadShare.hasAccess.mockRejectedValue(
        new Error("Database unavailable")
      );

      const operations = [
        request(app)
          .post("/api/workspace/test-workspace/thread/new")
          .set("Authorization", "Bearer user-token"),
        request(app)
          .get("/api/workspace/test-workspace/threads")
          .set("Authorization", "Bearer user-token"),
        request(app)
          .get("/api/workspace/test-workspace/thread/test-thread/chats")
          .set("Authorization", "Bearer admin-token"),
      ];

      const responses = await Promise.all(operations);

      // All should return 500 status
      responses.forEach((response) => {
        expect(response.status).toBe(500);
      });
    });
  });
});
