import { Express, Request, Response, Router } from "express";
import { ParamsDictionary } from "express-serve-static-core";
import { <PERSON><PERSON><PERSON><PERSON> } from "../models/apiKeys";
import {
  invites as PrismaInvite,
  workspaces as PrismaWorkspace,
  api_keys as PrismaA<PERSON><PERSON><PERSON>,
  Organization as PrismaOrganization,
} from "@prisma/client";
import { Document } from "../models/documents";
import { EventLogs } from "../models/eventLogs";
import { Invite, InviteWithUsers } from "../models/invite";
import SystemSettings from "../models/systemSettings";
import { Telemetry } from "../models/telemetry";
import { User } from "../models/user";
import { UserToken } from "../models/userToken";
import { DocumentVectors } from "../models/vectors";
import { Workspace } from "../models/workspace";
import { WorkspaceChats } from "../models/workspaceChats";
import {
  getVectorDbClass,
  getEmbeddingEngineSelection,
} from "../utils/helpers";
import {
  validRoleSelection,
  canModifyAdmin,
  validCanModify,
  type UserWithRole,
} from "../utils/helpers/admin";
import { reqBody, userFromSession, safeJsonParse } from "../utils/http";
import {
  strictMultiUserRoleValid,
  flexUserRoleValid,
  ROLES,
} from "../utils/middleware/multiUserProtected";
import { validatedRequest } from "../utils/middleware/validatedRequest";
import ImportedPlugin from "../utils/agents/imported";
import { clearTranslationCache } from "../utils/i18n";
import { Organization } from "../models/organization";
import { UserRole } from "../types/shared";
import { FilteredUser } from "../types/models";

// Helper function to convert FilteredUser to UserWithRole
function toUserWithRole(user: FilteredUser | null): UserWithRole {
  if (!user) throw new Error("User is required");
  return {
    ...user,
    role: user.role as UserRole,
  } as UserWithRole;
}

// Admin endpoint interfaces
interface AdminUserResponse {
  users: FilteredUser[];
  total?: number;
}

interface CreateUserRequestBody {
  username?: string;
  password?: string;
  role?: UserRole;
  email?: string;
  workspaceIds?: number[];
  maxUsage?: string | number;
  [key: string]: string | number | boolean | null | undefined | number[];
}

interface CreateUserResponse {
  user: FilteredUser | null;
  error?: string;
}

interface UpdateUserRequestBody {
  username?: string;
  password?: string;
  role?: UserRole;
  email?: string;
  suspended?: number;
  [key: string]: string | number | boolean | null | undefined;
}

interface UpdateUserResponse {
  success: boolean;
  error?: string;
}

interface DeleteUserResponse {
  success: boolean;
  error: string | null;
}

interface AdminInvitesResponse {
  invites: InviteWithUsers[];
}

interface CreateInviteRequestBody {
  workspaceIds?: number[];
  maxUsage?: string | number;
}

interface CreateInviteResponse {
  invite: PrismaInvite | null;
  error?: string;
}

interface DeleteInviteResponse {
  success: boolean;
  error?: string;
}

interface AdminWorkspacesResponse {
  workspaces: PrismaWorkspace[];
}

interface LinkedWorkspacesResponse {
  linkedWorkspaces: {
    id: number;
    primaryWorkspaceId: number;
    linkedWorkspaceId: number;
  }[];
}

interface WorkspaceUsersResponse {
  users: { id: number; user_id: number; workspace_id: number }[];
}

interface CreateWorkspaceRequestBody {
  name: string;
}

interface CreateWorkspaceResponse {
  workspace: PrismaWorkspace | null;
  error?: string;
}

interface UpdateWorkspaceUsersRequestBody {
  userIds: string[];
}

interface UpdateWorkspaceUsersResponse {
  success: boolean;
  error?: string;
}

interface UpdateLinkedWorkspacesRequestBody {
  linkedWorkspaces: string[];
}

interface UpdateLinkedWorkspacesResponse {
  success: boolean;
  error?: string;
}

interface DeleteWorkspaceResponse {
  success: boolean;
  error: string | null;
}

interface LoginUIResponse {
  result: {
    loginUI: string;
  };
}

interface UpdateLoginUIRequestBody {
  login_ui: string;
}

interface UpdateLoginUIResponse {
  success: boolean;
  error: string | null;
}

interface SystemPreferencesResponse {
  settings: Record<string, unknown>;
}

interface UpdateSystemPreferencesResponse {
  success: boolean;
  error: string | null;
}

interface PDRSettingsResponse {
  result: {
    adjacentVector: string | null;
    keepPdrVectors: boolean;
    globalPdrOverride: boolean;
  };
}

interface UpdatePDRSettingsRequestBody {
  adjacent_vector_limit?: string;
  keep_pdr_vectors?: string;
  global_pdr_override?: string;
}

interface DDSettingsResponse {
  result: {
    ddVectorEnabled: boolean;
    ddMemoEnabled: boolean;
    ddBaseEnabled: boolean;
    ddLinkedWorkspaceImpact: boolean;
    ddVectorTokenLimit: number;
    ddMemoTokenLimit: number;
    ddBaseTokenLimit: number;
    sectionLegalIssuesSystemPrompt: string;
    sectionLegalIssuesUserPrompt: string;
    documentDraftingMemoPrompt: string;
  };
}

interface UpdateDDSettingsRequestBody {
  dd_vector_enabled?: string;
  dd_memo_enabled?: string;
  dd_base_enabled?: string;
  dd_linked_workspace_impact?: string;
  dd_vector_token_limit?: string | number;
  dd_memo_token_limit?: string | number;
  dd_base_token_limit?: string | number;
  section_legal_issues_system_prompt?: string;
  section_legal_issues_user_prompt?: string;
  document_drafting_memo_prompt?: string;
}

interface ApiKeysResponse {
  apiKeys: PrismaApiKey[];
  error: string | null;
}

interface GenerateApiKeyResponse {
  apiKey: PrismaApiKey | null;
  error?: string;
}

interface UpdateWorkspacesLanceDBRequestBody {
  vectorSearchMode: string;
}

interface UpdateWorkspacesLanceDBResponse {
  success: boolean;
  count?: number;
  error?: string;
}

interface OrganizationsResponse {
  organizations?: PrismaOrganization[];
  error?: string;
}

interface CreateOrganizationRequestBody {
  name: string;
}

interface CreateOrganizationResponse {
  organization?: PrismaOrganization;
  error?: string;
}

interface GetOrganizationResponse {
  organization?: PrismaOrganization;
  error?: string;
}

interface UpdateOrganizationRequestBody {
  name?: string;
}

interface UpdateOrganizationResponse {
  organization?: PrismaOrganization;
  error?: string;
}

interface DeleteOrganizationResponse {
  success: boolean;
  error?: string;
}

export function adminEndpoints(app: Express, apiRouter?: Router): void {
  if (!app) return;

  const router = apiRouter || app;

  router.get(
    "/admin/users",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin, ROLES.manager])],
    async (
      _request: Request,
      response: Response<AdminUserResponse>
    ): Promise<void> => {
      try {
        const usersData = await User.where();
        response.status(200).json(usersData);
        return;
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
        return;
      }
    }
  );

  router.post(
    "/admin/users/new",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin, ROLES.manager])],
    async (
      request: Request<
        ParamsDictionary,
        CreateUserResponse,
        CreateUserRequestBody
      >,
      response: Response<CreateUserResponse>
    ): Promise<void> => {
      try {
        const currUser = await userFromSession(request, response);
        if (!currUser) {
          response.status(401).json({ user: null, error: "Unauthorized" });
          return;
        }
        const newUserParams: CreateUserRequestBody = reqBody(request);
        const roleValidation = validRoleSelection(
          toUserWithRole(currUser),
          newUserParams
        );

        if (!roleValidation.valid) {
          response
            .status(200)
            .json({ user: null, error: roleValidation.error ?? undefined });
          return;
        }

        if (!newUserParams.password) {
          response.status(200).json({
            user: null,
            error: "Password is required for creating a user",
          });
          return;
        }

        const { user: newUser, error } = await User.create({
          ...newUserParams,
          username: newUserParams.username || "",
          password: newUserParams.password,
        });
        if (newUser) {
          await EventLogs.logEvent(
            "user_created",
            {
              userName: newUser.username,
              createdBy: currUser.username || "Unknown",
            },
            currUser.id
          );
        }

        response
          .status(200)
          .json({ user: newUser ?? null, error: error ?? undefined });
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
        return;
      }
    }
  );

  router.post(
    "/admin/user/:id",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin, ROLES.manager])],
    async (
      request: Request<
        { id: string },
        UpdateUserResponse,
        UpdateUserRequestBody
      >,
      response: Response<UpdateUserResponse>
    ): Promise<void> => {
      try {
        const currUser = await userFromSession(request, response);
        if (!currUser) {
          response.status(401).json({ success: false, error: "Unauthorized" });
          return;
        }
        const { id } = request.params;
        const updates: UpdateUserRequestBody = reqBody(request);
        const user: FilteredUser | null = await User.get({ id: Number(id) });

        if (!user) {
          response
            .status(404)
            .json({ success: false, error: "User not found" });
          return;
        }

        const canModify = validCanModify(
          toUserWithRole(currUser),
          toUserWithRole(user)
        );
        if (!canModify.valid) {
          response
            .status(200)
            .json({ success: false, error: canModify.error ?? undefined });
          return;
        }

        const roleValidation = validRoleSelection(
          toUserWithRole(currUser),
          updates
        );
        if (!roleValidation.valid) {
          response
            .status(200)
            .json({ success: false, error: roleValidation.error ?? undefined });
          return;
        }

        const validAdminRoleModification = await canModifyAdmin(
          toUserWithRole(user),
          updates
        );
        if (!validAdminRoleModification.valid) {
          response.status(200).json({
            success: false,
            error: validAdminRoleModification.error ?? undefined,
          });
          return;
        }

        const { success, error } = await User.update(id, updates);
        response
          .status(200)
          .json({ success: success ?? false, error: error ?? undefined });
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
        return;
      }
    }
  );

  router.delete(
    "/admin/user/:id",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin, ROLES.manager])],
    async (
      request: Request<{ id: string }>,
      response: Response<DeleteUserResponse>
    ): Promise<void> => {
      try {
        const currUser = await userFromSession(request, response);
        if (!currUser) {
          response.status(401).json({ success: false, error: "Unauthorized" });
          return;
        }
        const { id } = request.params;
        const user: FilteredUser | null = await User.get({ id: Number(id) });

        if (!user) {
          response
            .status(404)
            .json({ success: false, error: "User not found" });
          return;
        }

        const canModify = validCanModify(
          toUserWithRole(currUser),
          toUserWithRole(user)
        );
        if (!canModify.valid) {
          response
            .status(200)
            .json({ success: false, error: canModify.error ?? null });
          return;
        }

        await User.delete({ id: Number(id) });
        await EventLogs.logEvent(
          "user_deleted",
          {
            userName: user.username,
            deletedBy: currUser.username,
          },
          currUser.id
        );
        response.status(200).json({ success: true, error: null });
        return;
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
        return;
      }
    }
  );

  router.get(
    "/admin/invites",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin, ROLES.manager])],
    async (
      _request: Request,
      response: Response<AdminInvitesResponse>
    ): Promise<void> => {
      try {
        const invites = await Invite.whereWithUsers();
        response.status(200).json({ invites });
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
      }
    }
  );

  router.post(
    "/admin/invite/new",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin, ROLES.manager])],
    async (
      request: Request<
        ParamsDictionary,
        CreateInviteResponse,
        CreateInviteRequestBody
      >,
      response: Response<CreateInviteResponse>
    ): Promise<void> => {
      try {
        const user = await userFromSession(request, response);
        if (!user) {
          response.status(401).json({ invite: null, error: "Unauthorized" });
          return;
        }
        const body: CreateInviteRequestBody = reqBody(request);
        const { invite, error } = await Invite.create({
          createdByUserId: user.id,
          workspaceIds: body?.workspaceIds ?? [],
          maxUsage: body?.maxUsage ? parseInt(String(body.maxUsage)) : 1,
        });

        if (invite) {
          await EventLogs.logEvent(
            "invite_created",
            {
              inviteCode: invite.code,
              createdBy: response?.locals?.user?.username ?? undefined,
              maxUsage: invite.maxUsage || 1,
            },
            response?.locals?.user?.id ?? undefined
          );
        }
        response.status(200).json({ invite, error: error ?? undefined });
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
      }
    }
  );

  router.delete(
    "/admin/invite/:id",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin, ROLES.manager])],
    async (
      request: Request<{ id: string }>,
      response: Response<DeleteInviteResponse>
    ): Promise<void> => {
      try {
        const { id } = request.params;
        const { success, error } = await Invite.deactivate(id);
        await EventLogs.logEvent(
          "invite_deleted",
          { deletedBy: response?.locals?.user?.username ?? undefined },
          response?.locals?.user?.id ?? undefined
        );
        response
          .status(200)
          .json({ success: success ?? false, error: error ?? undefined });
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
      }
    }
  );

  router.get(
    "/admin/workspaces",
    [
      validatedRequest,
      strictMultiUserRoleValid([
        ROLES.admin,
        ROLES.manager,
        ROLES.superuser,
        ROLES.default,
      ]),
    ],
    async (
      request: Request,
      response: Response<AdminWorkspacesResponse>
    ): Promise<void> => {
      try {
        const user = await userFromSession(request, response);
        if (!user) {
          response.status(401).json({ workspaces: [] });
          return;
        }
        const workspaces = await Workspace.whereWithUsersLinkedWorkspaces(user);
        response.status(200).json({ workspaces });
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
      }
    }
  );

  router.get(
    "/admin/workspaces/:workspaceId/linked-workspaces",
    [
      validatedRequest,
      strictMultiUserRoleValid([
        ROLES.admin,
        ROLES.manager,
        ROLES.superuser,
        ROLES.default,
      ]),
    ],
    async (
      request: Request<{ workspaceId: string }>,
      response: Response<LinkedWorkspacesResponse>
    ): Promise<void> => {
      try {
        const { workspaceId } = request.params;
        const linkedWorkspaces = await Workspace.linkedWorkspaces(workspaceId);
        response.status(200).json({ linkedWorkspaces });
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
      }
    }
  );

  router.get(
    "/admin/workspaces/:workspaceId/users",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin, ROLES.manager])],
    async (
      request: Request<{ workspaceId: string }>,
      response: Response<WorkspaceUsersResponse>
    ): Promise<void> => {
      try {
        const { workspaceId } = request.params;
        const users = await Workspace.workspaceUsers(workspaceId);
        response.status(200).json({ users });
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
      }
    }
  );

  router.post(
    "/admin/workspaces/new",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin, ROLES.manager])],
    async (
      request: Request<
        ParamsDictionary,
        CreateWorkspaceResponse,
        CreateWorkspaceRequestBody
      >,
      response: Response<CreateWorkspaceResponse>
    ): Promise<void> => {
      try {
        const user = await userFromSession(request, response);
        if (!user) {
          response.status(401).json({ workspace: null, error: "Unauthorized" });
          return;
        }
        const { name } = reqBody(request);
        const { workspace, message: error } = await Workspace.new(
          String(name),
          user.id
        );
        response
          .status(200)
          .json({ workspace: workspace ?? null, error: error ?? undefined });
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
        return;
      }
    }
  );

  router.post(
    "/admin/workspaces/:workspaceId/update-users",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin, ROLES.manager])],
    async (
      request: Request<
        { workspaceId: string },
        UpdateWorkspaceUsersResponse,
        UpdateWorkspaceUsersRequestBody
      >,
      response: Response<UpdateWorkspaceUsersResponse>
    ): Promise<void> => {
      try {
        const { workspaceId } = request.params;
        const { userIds } = reqBody<UpdateWorkspaceUsersRequestBody>(request);
        const { success, error } = await Workspace.updateUsers(
          workspaceId,
          userIds.map(Number)
        );
        response
          .status(200)
          .json({ success: success ?? false, error: error ?? undefined });
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
        return;
      }
    }
  );

  router.post(
    "/admin/workspaces/:workspaceId/update-linked-workspaces",
    [
      validatedRequest,
      strictMultiUserRoleValid([
        ROLES.admin,
        ROLES.manager,
        ROLES.superuser,
        ROLES.default,
      ]),
    ],
    async (
      request: Request<
        { workspaceId: string },
        UpdateLinkedWorkspacesResponse,
        UpdateLinkedWorkspacesRequestBody
      >,
      response: Response<UpdateLinkedWorkspacesResponse>
    ): Promise<void> => {
      try {
        const { workspaceId } = request.params;
        const { linkedWorkspaces } =
          reqBody<UpdateLinkedWorkspacesRequestBody>(request);
        const { success, error } = await Workspace.updateLinkedWorkspaces(
          workspaceId,
          linkedWorkspaces.map(Number)
        );
        response
          .status(200)
          .json({ success: success ?? false, error: error ?? undefined });
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
        return;
      }
    }
  );

  router.delete(
    "/admin/workspaces/:id",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin, ROLES.manager])],
    async (
      request: Request<{ id: string }>,
      response: Response<DeleteWorkspaceResponse>
    ): Promise<void> => {
      try {
        const { id } = request.params;
        const user = await userFromSession(request, response);
        if (!user) {
          response.sendStatus(401).end();
          return;
        }
        const VectorDb = getVectorDbClass();
        const workspace = await Workspace.get({ id: Number(id) });
        if (!workspace) {
          response.sendStatus(404).end();
          return;
        }

        await WorkspaceChats.delete({ workspaceId: Number(workspace.id) });
        await DocumentVectors.deleteForWorkspace(
          Number(workspace.id),
          user,
          workspace.slug
        );
        await Document.delete({ workspaceId: Number(workspace.id) });
        await Workspace.delete({ id: Number(workspace.id) });
        if (workspace.type === "document-drafting") {
          const documentDraftingSlug = `document-drafting/user-${workspace.user_id}_${workspace.slug}`;
          await Document.deleteStorage(documentDraftingSlug);
        } else {
          await Document.deleteStorage(workspace.slug);
        }
        try {
          await VectorDb.deleteVectorsInNamespace(
            await VectorDb.connect().then((res) => res.client),
            workspace.slug
          );
        } catch (e: unknown) {
          console.error((e as Error).message);
        }

        response.status(200).json({ success: true, error: null });
        return;
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
        return;
      }
    }
  );

  router.delete(
    "/admin/workspaces/vector-caches/:id",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin, ROLES.manager])],
    async (
      request: Request<{ id: string }>,
      response: Response<DeleteWorkspaceResponse>
    ): Promise<void> => {
      try {
        const { id } = request.params;
        const user = await userFromSession(request, response);
        if (!user) {
          response.sendStatus(401).end();
          return;
        }
        const VectorDb = getVectorDbClass();
        const workspace = await Workspace.get({ id: Number(id) });
        if (!workspace) {
          response.sendStatus(404).end();
          return;
        }

        await DocumentVectors.deleteForWorkspace(
          Number(workspace.id),
          user,
          workspace.slug
        );
        await Document.delete({ workspaceId: Number(workspace.id) });
        try {
          await VectorDb.deleteVectorsInNamespace(
            await VectorDb.connect().then((res) => res.client),
            workspace.slug
          );
        } catch (e: unknown) {
          console.error((e as Error).message);
        }

        response.status(200).json({ success: true, error: null });
        return;
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
        return;
      }
    }
  );

  // Get the default login UI
  router.get(
    "/admin/login-ui",
    async (
      _request: Request,
      response: Response<LoginUIResponse>
    ): Promise<void> => {
      try {
        const loginUISetting = await SystemSettings.get({
          label: "login_ui",
        });

        const result = {
          loginUI: loginUISetting?.value ?? "ist-legal-general",
        };

        response.status(200).json({ result });
        return;
      } catch (e: unknown) {
        console.error("Error fetching login UI:", e);
        response.sendStatus(500);
        return;
      }
    }
  );

  // Post new login UI
  router.post(
    "/admin/default-login",
    [validatedRequest, flexUserRoleValid([ROLES.admin, ROLES.manager])],
    async (
      request: Request<
        ParamsDictionary,
        UpdateLoginUIResponse,
        UpdateLoginUIRequestBody
      >,
      response: Response<UpdateLoginUIResponse>
    ): Promise<void> => {
      try {
        const updates: unknown = request.body;
        const parsedUpdates: unknown =
          typeof updates === "string" ||
          Object.prototype.toString.call(updates) === "[object String]"
            ? JSON.parse(updates as string)
            : updates;
        const loginUi = (parsedUpdates as UpdateLoginUIRequestBody)?.[
          "login_ui"
        ];
        if (!loginUi || typeof loginUi !== "string") {
          console.log("Validation failed: login_ui is missing or invalid");
          response.status(400).json({
            success: false,
            error: "login_ui is required and must be a string",
          });
          return;
        }

        // Validate against allowed options
        const validOptions = [
          "ist-legal-rwanda",
          "tender-flow",
          "ist-legal-general",
        ];
        if (!validOptions.includes(loginUi)) {
          console.log("Invalid login_ui value:", loginUi);
          response.status(400).json({
            success: false,
            error: `Invalid login_ui value: ${loginUi}`,
          });
          return;
        }

        await SystemSettings._updateSettings({ login_ui: loginUi });
        response.status(200).json({ success: true, error: null });
        return;
      } catch (e: unknown) {
        console.error("Error updating login UI:", e);
        response.status(500).json({
          success: false,
          error: (e as Error).message || "Server error",
        });
      }
    }
  );

  // System preferences but only by array of labels
  router.get(
    "/admin/system-preferences-for",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.admin, ROLES.manager, ROLES.superuser]),
    ],
    async (
      request: Request,
      response: Response<SystemPreferencesResponse>
    ): Promise<void> => {
      try {
        const requestedSettings: Record<string, unknown> = {};
        const labels = (request.query.labels as string)?.split(",") ?? [];
        const needEmbedder = [
          "text_splitter_chunk_size",
          "max_embed_chunk_size",
        ];
        const noRecord = [
          "max_embed_chunk_size",
          "agent_sql_connections",
          "imported_agent_skills",
          "feature_flags",
          "meta_page_title",
          "meta_page_favicon",
        ];
        for (const label of labels) {
          // Skip any settings that are not explicitly defined as public
          if (!SystemSettings.publicFields.includes(label)) continue;
          // Only get the embedder if the setting actually needs it
          const embedder = needEmbedder.includes(label)
            ? getEmbeddingEngineSelection()
            : null;
          // Only get the record from db if the setting actually needs it
          const setting = noRecord.includes(label)
            ? null
            : await SystemSettings.get({ label });
          switch (label) {
            case "limit_user_messages":
              requestedSettings[label] = (setting?.value ?? 0) === "true";
              break;
            case "message_limit":
              requestedSettings[label] = setting?.value
                ? Number(setting.value)
                : 10;
              break;
            case "footer_data":
              requestedSettings[label] = setting?.value ?? JSON.stringify([]);
              break;
            case "support_email":
              requestedSettings[label] = (setting?.value ?? false) || null;
              break;
            case "text_splitter_chunk_size":
              requestedSettings[label] =
                (setting?.value ?? false) ||
                embedder?.embeddingMaxChunkLength ||
                null;
              break;
            case "text_splitter_chunk_overlap":
              requestedSettings[label] = (setting?.value ?? false) || null;
              break;
            case "text_splitter_method":
              requestedSettings[label] = (setting?.value ?? false) || "native";
              break;
            case "text_splitter_jina_max_tokens":
              requestedSettings[label] = (setting?.value ?? false) || 1000;
              break;
            case "text_splitter_jina_return_tokens":
              requestedSettings[label] =
                (setting?.value ?? 0) === "true" || setting?.value === "true";
              break;
            case "text_splitter_jina_return_chunks":
              requestedSettings[label] =
                (setting?.value ?? 0) === "true" || setting?.value === "true";
              break;
            case "max_embed_chunk_size":
              requestedSettings[label] =
                (embedder?.embeddingMaxChunkLength ?? false) || 1000;
              break;
            case "agent_search_provider":
              requestedSettings[label] = (setting?.value ?? false) || null;
              break;
            case "agent_sql_connections":
              requestedSettings[label] =
                await SystemSettings.brief.agent_sql_connections();
              break;
            case "default_agent_skills":
              requestedSettings[label] = safeJsonParse(
                setting?.value ?? null,
                []
              );
              break;
            case "imported_agent_skills":
              requestedSettings[label] = ImportedPlugin.listImportedPlugins();
              break;
            case "custom_app_name":
              requestedSettings[label] = (setting?.value ?? false) || null;
              break;
            case "feature_flags":
              requestedSettings[label] =
                (await SystemSettings.getFeatureFlags()) || {};
              break;
            case "meta_page_title":
              requestedSettings[label] =
                await SystemSettings.getValueOrFallback({ label }, null);
              break;
            case "meta_page_favicon":
              requestedSettings[label] =
                await SystemSettings.getValueOrFallback({ label }, null);
              break;
            case "max_tokens_per_user":
              requestedSettings[label] =
                await SystemSettings.getValueOrFallback({ label }, null);
              break;
            case "enable_lancedb_rerank":
              requestedSettings[label] =
                await SystemSettings.getValueOrFallback({ label }, null);
              break;
            case "dynamic_context_window_percentage":
              requestedSettings[label] =
                await SystemSettings.getDynamicContextSettings();
              break;
            case "university_mode":
              requestedSettings[label] =
                (await SystemSettings.get({ label: "university_mode" }))
                  ?.value || "false";
              break;
            default:
              break;
          }
        }
        response.status(200).json({ settings: requestedSettings });
        return;
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
        return;
      }
    }
  );

  // TODO: Delete this endpoint
  // DEPRECATED - use /admin/system-preferences-for instead with ?labels=... comma separated string of labels
  router.get(
    "/admin/system-preferences",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.admin, ROLES.manager, ROLES.superuser]),
    ],
    async (
      _request: Request,
      response: Response<SystemPreferencesResponse>
    ): Promise<void> => {
      try {
        const embedder = getEmbeddingEngineSelection();
        const settings: Record<string, unknown> = {
          limit_user_messages:
            (await SystemSettings.get({ label: "limit_user_messages" }))
              ?.value || "false",
          message_limit:
            (await SystemSettings.get({ label: "message_limit" }))?.value ||
            "25",
          footer_data:
            (await SystemSettings.get({ label: "footer_data" }))?.value || null,
          support_email:
            (await SystemSettings.get({ label: "support_email" }))?.value ||
            null,
          text_splitter_chunk_size:
            (await SystemSettings.get({ label: "text_splitter_chunk_size" }))
              ?.value || "1500",
          text_splitter_chunk_overlap:
            (await SystemSettings.get({ label: "text_splitter_chunk_overlap" }))
              ?.value || "20",
          text_splitter_method:
            (await SystemSettings.get({ label: "text_splitter_method" }))
              ?.value || "native",
          text_splitter_jina_max_tokens:
            (
              await SystemSettings.get({
                label: "text_splitter_jina_max_tokens",
              })
            )?.value || "1000",
          text_splitter_jina_return_tokens:
            (
              await SystemSettings.get({
                label: "text_splitter_jina_return_tokens",
              })
            )?.value || "true",
          text_splitter_jina_return_chunks:
            (
              await SystemSettings.get({
                label: "text_splitter_jina_return_chunks",
              })
            )?.value || "true",
          max_embed_chunk_size:
            (embedder?.embeddingMaxChunkLength ?? false) || 1000,
          agent_search_provider:
            (await SystemSettings.get({ label: "agent_search_provider" }))
              ?.value || null,
          agent_sql_connections:
            (await SystemSettings.get({ label: "agent_sql_connections" }))
              ?.value || "[]",
          default_agent_skills:
            (await SystemSettings.get({ label: "default_agent_skills" }))
              ?.value || "[]",
          imported_agent_skills:
            (await SystemSettings.get({ label: "imported_agent_skills" }))
              ?.value || "[]",
          custom_app_name:
            (await SystemSettings.get({ label: "custom_app_name" }))?.value ||
            null,
          language:
            (await SystemSettings.get({ label: "language" }))?.value || null,
          palette:
            (await SystemSettings.get({ label: "palette" }))?.value || null,
          feature_flags: (await SystemSettings.getFeatureFlags()) || {},
          meta_page_title: await SystemSettings.getValueOrFallback(
            { label: "meta_page_title" },
            null
          ),
          meta_page_favicon: await SystemSettings.getValueOrFallback(
            { label: "meta_page_favicon" },
            null
          ),
          custom_paragraph_text:
            (await SystemSettings.get({ label: "custom_paragraph_text" }))
              ?.value || null,
          max_tokens_per_user: await SystemSettings.getValueOrFallback(
            { label: "max_tokens_per_user" },
            null
          ),
          enable_lancedb_rerank: await SystemSettings.getValueOrFallback(
            { label: "enable_lancedb_rerank" },
            null
          ),
          dynamic_context_window_percentage:
            await SystemSettings.getDynamicContextSettings(),
          university_mode:
            (await SystemSettings.get({ label: "university_mode" }))?.value ||
            "false",
        };
        response.status(200).json({ settings });
        return;
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
        return;
      }
    }
  );

  router.post(
    "/admin/system-preferences",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.admin, ROLES.manager, ROLES.superuser]),
    ],
    async (
      request: Request<
        ParamsDictionary,
        UpdateSystemPreferencesResponse,
        Record<string, unknown>
      >,
      response: Response<UpdateSystemPreferencesResponse>
    ): Promise<void> => {
      try {
        const updates = reqBody(request);
        await SystemSettings.updateSettings(updates);

        // If max_tokens_per_user was changed, enforce the new limit across all users
        if ("max_tokens_per_user" in updates) {
          await UserToken.enforceMaxTokensLimitForAllUsers();
        }

        // If language was changed, clear the translation cache
        if ("language" in updates) {
          clearTranslationCache();
        }

        response.status(200).json({ success: true, error: null });
        return;
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
        return;
      }
    }
  );

  router.get(
    "/admin/pdr-settings",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.admin, ROLES.manager, ROLES.superuser]),
    ],
    async (
      _request: Request,
      response: Response<PDRSettingsResponse>
    ): Promise<void> => {
      try {
        const [adjacentVector, keepPdrVectors, globalPdrOverride] =
          await Promise.all([
            SystemSettings.get({
              label: "adjacent_vector_limit",
            }),
            SystemSettings.get({
              label: "keep_pdr_vectors",
            }),
            SystemSettings.get({
              label: "global_pdr_override",
            }),
          ]);
        const result = {
          adjacentVector: adjacentVector?.value ?? null,
          keepPdrVectors: (keepPdrVectors?.value ?? 0) === "true",
          globalPdrOverride:
            (globalPdrOverride?.value ?? 0) === "false" ? false : true,
        };
        response.status(200).json({ result });
        return;
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
        return;
      }
    }
  );

  router.get(
    "/admin/dd-settings",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.admin, ROLES.manager, ROLES.superuser]),
    ],
    async (
      _request: Request,
      response: Response<DDSettingsResponse>
    ): Promise<void> => {
      try {
        const [
          vectorEnabled,
          memoEnabled,
          baseEnabled,
          linkedWorkspaceImpact,
          vectorTokenLimit,
          memoTokenLimit,
          baseTokenLimit,
          sectionLegalIssuesSystemSetting,
          sectionLegalIssuesUserSetting,
          memoPromptSetting,
        ] = await Promise.all([
          SystemSettings.get({ label: "dd_vector_enabled" }),
          SystemSettings.get({ label: "dd_memo_enabled" }),
          SystemSettings.get({ label: "dd_base_enabled" }),
          SystemSettings.get({ label: "dd_linked_workspace_impact" }),
          SystemSettings.get({ label: "dd_vector_token_limit" }),
          SystemSettings.get({ label: "dd_memo_token_limit" }),
          SystemSettings.get({ label: "dd_base_token_limit" }),
          SystemSettings.get({ label: "section_legal_issues_system_prompt" }),
          SystemSettings.get({ label: "section_legal_issues_user_prompt" }),
          SystemSettings.get({ label: "document_drafting_memo_prompt" }),
        ]);

        const result = {
          ddVectorEnabled: (vectorEnabled?.value ?? 0) === "true",
          ddMemoEnabled: (memoEnabled?.value ?? 0) === "true",
          ddBaseEnabled: (baseEnabled?.value ?? 0) === "true",
          ddLinkedWorkspaceImpact:
            (linkedWorkspaceImpact?.value ?? 0) === "true",
          ddVectorTokenLimit: vectorTokenLimit?.value
            ? Number(vectorTokenLimit.value)
            : 5000,
          ddMemoTokenLimit: memoTokenLimit?.value
            ? Number(memoTokenLimit.value)
            : 3000,
          ddBaseTokenLimit: baseTokenLimit?.value
            ? Number(baseTokenLimit.value)
            : 2000,
          sectionLegalIssuesSystemPrompt:
            (sectionLegalIssuesSystemSetting?.value ?? false) || "",
          sectionLegalIssuesUserPrompt:
            (sectionLegalIssuesUserSetting?.value ?? false) || "",
          documentDraftingMemoPrompt: (memoPromptSetting?.value ?? false) || "",
        };

        response.status(200).json({ result });
        return;
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
        return;
      }
    }
  );

  router.post(
    "/admin/update-pdr-settings",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.admin, ROLES.manager, ROLES.superuser]),
    ],
    async (
      request: Request<
        ParamsDictionary,
        UpdateSystemPreferencesResponse,
        UpdatePDRSettingsRequestBody
      >,
      response: Response<UpdateSystemPreferencesResponse>
    ): Promise<void> => {
      try {
        const settings = reqBody(request);
        await SystemSettings._updateSettings({
          adjacent_vector_limit: settings.adjacent_vector_limit,
          keep_pdr_vectors: settings.keep_pdr_vectors,
          global_pdr_override: settings.global_pdr_override,
        });
        response.status(200).json({ success: true, error: null });
        return;
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
        return;
      }
    }
  );

  router.post(
    "/admin/update-dd-settings",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.admin, ROLES.manager, ROLES.superuser]),
    ],
    async (
      request: Request<
        ParamsDictionary,
        UpdateSystemPreferencesResponse,
        UpdateDDSettingsRequestBody
      >,
      response: Response<UpdateSystemPreferencesResponse>
    ): Promise<void> => {
      try {
        const settings = reqBody(request);
        await SystemSettings._updateSettings({
          dd_vector_enabled: settings.dd_vector_enabled,
          dd_memo_enabled: settings.dd_memo_enabled,
          dd_base_enabled: settings.dd_base_enabled,
          dd_linked_workspace_impact: settings.dd_linked_workspace_impact,
          dd_vector_token_limit: settings.dd_vector_token_limit,
          dd_memo_token_limit: settings.dd_memo_token_limit,
          dd_base_token_limit: settings.dd_base_token_limit,
          section_legal_issues_system_prompt:
            settings.section_legal_issues_system_prompt,
          section_legal_issues_user_prompt:
            settings.section_legal_issues_user_prompt,
          document_drafting_memo_prompt: settings.document_drafting_memo_prompt,
        });
        response.status(200).json({ success: true, error: null });
        return;
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
        return;
      }
    }
  );

  router.get(
    "/admin/api-keys",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin])],
    async (
      _request: Request,
      response: Response<ApiKeysResponse>
    ): Promise<void> => {
      try {
        const apiKeys = await ApiKey.whereWithUser({});
        response.status(200).json({
          apiKeys,
          error: null,
        });
      } catch (error: unknown) {
        console.error(error);
        response.status(500).json({
          apiKeys: [],
          error: "Could not find an API Keys.",
        });
      }
    }
  );

  router.post(
    "/admin/generate-api-key",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin])],
    async (
      request: Request,
      response: Response<GenerateApiKeyResponse>
    ): Promise<void> => {
      try {
        const user = await userFromSession(request, response);
        if (!user) {
          response.status(401).json({ apiKey: null, error: "Unauthorized" });
          return;
        }
        const { apiKey, error } = await ApiKey.create(user.id);

        await Telemetry.sendTelemetry("api_key_created");
        await EventLogs.logEvent(
          "api_key_created",
          { createdBy: (user?.username ?? false) || "Unknown" },
          user?.id
        );
        response.status(200).json({
          apiKey,
          error: error ?? undefined,
        });
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
        return;
      }
    }
  );

  router.delete(
    "/admin/delete-api-key/:id",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin])],
    async (
      request: Request<{ id: string }>,
      response: Response
    ): Promise<void> => {
      try {
        const { id } = request.params;
        await ApiKey.delete({ id: Number(id) });

        await EventLogs.logEvent(
          "api_key_deleted",
          { deletedBy: response?.locals?.user?.username ?? undefined },
          response?.locals?.user?.id ?? undefined
        );
        response.status(200).end();
      } catch (e: unknown) {
        console.error(e);
        response.sendStatus(500);
        return;
      }
    }
  );

  router.post(
    "/admin/workspaces/lancedb/update-all",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin])],
    async (
      request: Request<
        ParamsDictionary,
        UpdateWorkspacesLanceDBResponse,
        UpdateWorkspacesLanceDBRequestBody
      >,
      response: Response<UpdateWorkspacesLanceDBResponse>
    ): Promise<void> => {
      try {
        const { vectorSearchMode } = reqBody(request);
        const { count, message } = await Workspace.updateMany(
          {},
          { vectorSearchMode: String(vectorSearchMode) }
        );
        response
          .status(200)
          .json({ success: true, count, error: message ?? undefined });
      } catch (e: unknown) {
        console.error(e);
        response
          .status(500)
          .json({ success: false, error: (e as Error).message });
      }
    }
  );

  router.get(
    "/admin/organizations",
    [validatedRequest, flexUserRoleValid([ROLES.admin, ROLES.manager])],
    async (
      _request: Request,
      response: Response<OrganizationsResponse>
    ): Promise<void> => {
      try {
        const { organizations, error } = await Organization.getAll();
        if (error) {
          console.error("Failed to fetch organizations:", error);
          response.status(500).json({ error });
          return;
        }
        response.status(200).json({ organizations });
        return;
      } catch (e: unknown) {
        console.error("Failed to fetch organizations:", e);
        response.status(500).json({ error: "Failed to fetch organizations." });
        return;
      }
    }
  );

  router.post(
    "/admin/organization",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin, ROLES.manager])],
    async (
      request: Request<
        ParamsDictionary,
        CreateOrganizationResponse,
        CreateOrganizationRequestBody
      >,
      response: Response<CreateOrganizationResponse>
    ): Promise<void> => {
      try {
        const { name } = reqBody(request);
        const { organization, error } = await Organization.create(String(name));
        response.status(200).json({
          organization: organization ?? undefined,
          error: error ?? undefined,
        });
      } catch (e: unknown) {
        console.error("Failed to create organization:", e);
        response.status(500).json({ error: "Failed to create organization." });
        return;
      }
    }
  );

  // Get individual organization
  router.get(
    "/admin/organization/:id",
    [validatedRequest, flexUserRoleValid([ROLES.admin, ROLES.manager])],
    async (
      request: Request<{ id: string }>,
      response: Response<GetOrganizationResponse>
    ): Promise<void> => {
      try {
        const { id } = request.params;
        const organizationId = parseInt(id, 10);

        if (isNaN(organizationId)) {
          response.status(400).json({ error: "Invalid organization ID" });
          return;
        }

        const organization = await Organization.get({ id: organizationId });

        if (!organization) {
          response.status(404).json({ error: "Organization not found" });
          return;
        }

        response.status(200).json({ organization });
      } catch (e: unknown) {
        console.error("Failed to fetch organization:", e);
        response.status(500).json({ error: "Failed to fetch organization." });
        return;
      }
    }
  );

  // Update individual organization
  router.put(
    "/admin/organization/:id",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin, ROLES.manager])],
    async (
      request: Request<
        { id: string },
        UpdateOrganizationResponse,
        UpdateOrganizationRequestBody
      >,
      response: Response<UpdateOrganizationResponse>
    ): Promise<void> => {
      try {
        const { id } = request.params;
        const organizationId = parseInt(id, 10);

        if (isNaN(organizationId)) {
          response.status(400).json({ error: "Invalid organization ID" });
          return;
        }

        const updates = reqBody(request);

        if (
          !updates.name ||
          typeof updates.name !== "string" ||
          updates.name.trim() === ""
        ) {
          response.status(400).json({ error: "Organization name is required" });
          return;
        }

        // Check if organization exists
        const existingOrganization = await Organization.get({
          id: organizationId,
        });
        if (!existingOrganization) {
          response.status(404).json({ error: "Organization not found" });
          return;
        }

        const { organization, error } = await Organization.update(
          organizationId,
          updates
        );

        if (error) {
          response.status(400).json({ error });
          return;
        }

        response.status(200).json({ organization: organization ?? undefined });
      } catch (e: unknown) {
        console.error("Failed to update organization:", e);
        response.status(500).json({ error: "Failed to update organization." });
        return;
      }
    }
  );

  // Delete individual organization
  router.delete(
    "/admin/organization/:id",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin, ROLES.manager])],
    async (
      request: Request<{ id: string }>,
      response: Response<DeleteOrganizationResponse>
    ): Promise<void> => {
      try {
        const { id } = request.params;
        const organizationId = parseInt(id, 10);

        if (isNaN(organizationId)) {
          response.status(400).json({
            success: false,
            error: "Invalid organization ID",
          });
          return;
        }

        // Check if organization exists
        const existingOrganization = await Organization.get({
          id: organizationId,
        });
        if (!existingOrganization) {
          response.status(404).json({
            success: false,
            error: "Organization not found",
          });
          return;
        }

        const { success, error } = await Organization.delete(organizationId);

        if (!success) {
          response
            .status(400)
            .json({ success: false, error: error ?? undefined });
          return;
        }

        response.status(200).json({ success: true });
      } catch (e: unknown) {
        console.error("Failed to delete organization:", e);
        response.status(500).json({
          success: false,
          error: "Failed to delete organization.",
        });
        return;
      }
    }
  );

  // Token management endpoints for security administration
  router.get(
    "/admin/users/:userId/tokens",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin])],
    async (
      request: Request<{ userId: string }>,
      response: Response
    ): Promise<void> => {
      try {
        const { userId } = request.params;
        const tokens = await UserToken.findByUserId(Number(userId));

        // Return sanitized token information (no actual token values)
        const sanitizedTokens = tokens.map((token) => ({
          id: token.id,
          device_info: token.device_info,
          last_used: token.last_used,
          createdAt: token.createdAt,
        }));

        response.status(200).json({ tokens: sanitizedTokens });
      } catch (e: unknown) {
        console.error("Failed to fetch user tokens:", e);
        response.status(500).json({ error: "Failed to fetch user tokens." });
      }
    }
  );

  router.delete(
    "/admin/users/:userId/tokens/:tokenId",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin])],
    async (
      request: Request<{ userId: string; tokenId: string }>,
      response: Response
    ): Promise<void> => {
      try {
        const { userId, tokenId } = request.params;
        const user = await userFromSession(request, response);
        if (!user) {
          response.sendStatus(401);
          return;
        }

        // Verify the token belongs to the specified user
        const tokens = await UserToken.findByUserId(Number(userId));
        const targetToken = tokens.find((t) => t.id === Number(tokenId));

        if (!targetToken) {
          response
            .status(404)
            .json({ error: "Token not found for this user." });
          return;
        }

        await UserToken.delete({ id: Number(tokenId) });

        response.status(200).json({
          success: true,
          message: "Token revoked successfully.",
        });
      } catch (e: unknown) {
        console.error("Failed to revoke token:", e);
        response.status(500).json({ error: "Failed to revoke token." });
      }
    }
  );

  router.delete(
    "/admin/users/:userId/tokens",
    [validatedRequest, strictMultiUserRoleValid([ROLES.admin])],
    async (
      request: Request<{ userId: string }>,
      response: Response
    ): Promise<void> => {
      try {
        const { userId } = request.params;
        const user = await userFromSession(request, response);
        if (!user) {
          response.sendStatus(401);
          return;
        }

        const result = await UserToken.deleteAllUserTokens(Number(userId));

        response.status(200).json({
          success: true,
          message: `Revoked ${result.count} tokens for user.`,
        });
      } catch (e: unknown) {
        console.error("Failed to revoke all user tokens:", e);
        response
          .status(500)
          .json({ error: "Failed to revoke all user tokens." });
      }
    }
  );
}
