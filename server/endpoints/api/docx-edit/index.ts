import { Request, Response } from "express";
import { handleFileUpload } from "../../../utils/files/multer";
import { editDocxWithLLM } from "../../../utils/docx/editWithLLM";
import {
  compareAndHighlightDocx,
  createHighlightedDocxFilename,
} from "../../../utils/docx/compareAndHighlight";
import { DocxLoader } from "@langchain/community/document_loaders/fs/docx";
import { docxToMarkdown } from "../../../utils/docx/docxToMarkdown";
import { textToDocx, createDocxFilename } from "../../../utils/docx/textToDocx";
import { reqBody, userFromSession } from "../../../utils/http";
import { validatedRequest } from "../../../utils/middleware/validatedRequest";
import * as fs from "fs";
import * as path from "path";
import { v4 as uuidv4 } from "uuid";
import { tSync } from "../../../utils/i18n";
import { renderDocxTemplate } from "../../../utils/docx/templateRenderer";
const mergeDocxWithTemplate = require("../../../utils/docx/mergeDocxWithTemplate");
import SystemSettings from "../../../models/systemSettings";
import { Router } from "express";
import {
  DocxUploadResponse,
  DocxProcessRequest,
  DocxProcessResponse,
  DocxContentResponse,
  TextToDocxRequest,
  TextToDocxResponse,
  DocxTemplateUploadResponse,
  DocxCleanupResponse,
} from "../../../types/api";

// Define paths for storing documents
const documentsPath =
  process.env.NODE_ENV === "development"
    ? path.resolve(__dirname, "../../../storage/documents")
    : path.resolve(process.env.STORAGE_DIR!, `documents`);

// Define path for storing temporary DOCX files
const docxEditPath = path.join(documentsPath, "docx-edit");

// Extended Request interface for session parameters
interface SessionRequest extends Request {
  params: {
    sessionId?: string;
    filename?: string;
  };
}

function docxEditEndpoints(router: Router): void {
  if (!router) return;

  // Ensure the docx-edit directory exists
  if (!fs.existsSync(docxEditPath)) {
    fs.mkdirSync(docxEditPath, { recursive: true });
  }

  // Endpoint to upload a DOCX file for editing
  router.post(
    "/docx-edit/upload",
    process.env.NODE_ENV === "development"
      ? [handleFileUpload]
      : [validatedRequest, handleFileUpload],
    async (request: Request, response: Response): Promise<void> => {
      try {
        const file = request.file;
        // In development mode, we don't need a valid user
        if (process.env.NODE_ENV !== "development") {
          await userFromSession(request as Request);
        }

        if (!file) {
          response.status(400).json({
            success: false,
            error: "No file provided",
          } as DocxUploadResponse);
          return;
        }

        // Validate file type (.docx or .dotx)
        const fileExt = path.extname(file.originalname).toLowerCase();
        const allowedExts = [".docx", ".dotx"];
        if (!allowedExts.includes(fileExt)) {
          // Clean up the uploaded file
          if (fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
          }

          response.status(400).json({
            success: false,
            error:
              tSync("docxEdit.errors.processingError") ||
              "Only DOCX/DOTX files are supported for editing",
          } as DocxUploadResponse);
          return;
        }

        // Create a unique session ID for this editing session
        const sessionId = uuidv4();

        // Create a directory for this editing session
        const sessionDir = path.join(docxEditPath, sessionId);
        fs.mkdirSync(sessionDir, { recursive: true });

        // Move the uploaded file to the session directory
        const originalFilePath = path.join(sessionDir, "original" + fileExt);
        fs.copyFileSync(file.path, originalFilePath);

        // Clean up the uploaded file
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }

        response.status(200).json({
          success: true,
          message:
            tSync("docxEdit.success.uploaded") ||
            "DOCX file uploaded successfully",
          data: {
            sessionId,
            filename: file.originalname,
          },
        } as DocxUploadResponse);
      } catch (error: unknown) {
        console.error("Error uploading DOCX for editing:", error);
        response.status(500).json({
          success: false,
          error: "Error uploading DOCX file: " + (error as Error).message,
        } as DocxUploadResponse);
      }
    }
  );

  // Endpoint to edit a DOCX file with LLM
  router.post(
    "/docx-edit/process",
    process.env.NODE_ENV === "development" ? [] : [validatedRequest],
    async (request: Request, response: Response): Promise<void> => {
      try {
        const { sessionId, instructions, provider, model }: DocxProcessRequest =
          reqBody(request);

        if (!sessionId || !instructions) {
          response.status(400).json({
            success: false,
            error:
              tSync("docxEdit.errors.processingError") ||
              "Session ID and instructions are required",
          } as DocxProcessResponse);
          return;
        }

        // Check if the session directory exists
        const sessionDir = path.join(docxEditPath, sessionId);
        if (!fs.existsSync(sessionDir)) {
          response.status(404).json({
            success: false,
            error:
              tSync("docxEdit.errors.sessionNotFound") || "Session not found",
          } as DocxProcessResponse);
          return;
        }

        // Find the original DOCX file
        const files = fs.readdirSync(sessionDir);
        const originalFile = files.find(
          (file) => file.startsWith("original") && file.endsWith(".docx")
        );

        if (!originalFile) {
          response.status(404).json({
            success: false,
            error:
              tSync("docxEdit.errors.fileNotFound") ||
              "Original DOCX file not found",
          } as DocxProcessResponse);
          return;
        }

        const originalFilePath = path.join(sessionDir, originalFile);

        // Edit the DOCX file with LLM
        // Force OpenAI provider for DOCX editing in development mode
        const editOptions =
          process.env.NODE_ENV === "development"
            ? {
                provider: "openai",
                model: process.env.OPEN_MODEL_PREF || "gpt-4",
              }
            : { provider, model };

        console.log(
          "Using LLM provider:",
          editOptions.provider,
          "with model:",
          editOptions.model
        );

        const { originalText, editedText } = await editDocxWithLLM(
          originalFilePath,
          instructions,
          editOptions
        );

        // Generate a highlighted DOCX file
        const highlightedFilename = createHighlightedDocxFilename(originalFile);
        const highlightedFilePath = path.join(sessionDir, highlightedFilename);

        await compareAndHighlightDocx(
          originalText,
          editedText,
          highlightedFilePath
        );

        // Save the edited text for reference
        fs.writeFileSync(path.join(sessionDir, "edited.txt"), editedText);

        response.status(200).json({
          success: true,
          message:
            tSync("docxEdit.success.edited") || "DOCX file edited successfully",
          data: {
            sessionId,
            highlightedFilename,
          },
        } as DocxProcessResponse);
      } catch (error: unknown) {
        console.error("Error editing DOCX file:", error);
        console.error("Error stack:", (error as Error).stack);
        response.status(500).json({
          success: false,
          error: "Error editing DOCX file: " + (error as Error).message,
          stack:
            process.env.NODE_ENV === "development"
              ? (error as Error).stack
              : undefined,
        } as DocxProcessResponse);
      }
    }
  );

  // Endpoint to get document content
  router.get(
    "/docx-edit/content/:sessionId",
    process.env.NODE_ENV === "development" ? [] : [validatedRequest],
    async (request: SessionRequest, response: Response): Promise<void> => {
      try {
        const { sessionId } = request.params;

        if (!sessionId) {
          response.status(400).json({
            success: false,
            error:
              tSync("docxEdit.errors.processingError") ||
              "Session ID is required",
          } as DocxContentResponse);
          return;
        }

        // Check if the session directory exists
        const sessionDir = path.join(docxEditPath, sessionId);
        if (!fs.existsSync(sessionDir)) {
          response.status(404).json({
            success: false,
            error:
              tSync("docxEdit.errors.sessionNotFound") || "Session not found",
          } as DocxContentResponse);
          return;
        }

        // Find the original DOCX file
        const files = fs.readdirSync(sessionDir);
        const originalFile = files.find(
          (file) => file.startsWith("original") && file.endsWith(".docx")
        );

        if (!originalFile) {
          response.status(404).json({
            success: false,
            error:
              tSync("docxEdit.errors.fileNotFound") ||
              "Original DOCX file not found",
          } as DocxContentResponse);
          return;
        }

        const originalFilePath = path.join(sessionDir, originalFile);

        try {
          // Convert DOCX to markdown
          const markdownContent = await docxToMarkdown(originalFilePath);

          if (!markdownContent.trim()) {
            response.status(404).json({
              success: false,
              error:
                tSync("docxEdit.errors.noContent") ||
                "No text content found in the document",
            } as DocxContentResponse);
            return;
          }

          response.status(200).json({
            success: true,
            content: markdownContent,
          } as DocxContentResponse);
        } catch (error: unknown) {
          console.error("Error converting DOCX to markdown:", error);

          // Fallback to the original method if markdown conversion fails
          const loader = new DocxLoader(originalFilePath);
          const docs = await loader.load();

          // Combine all document content
          let originalText = "";
          for (const doc of docs) {
            if (doc.pageContent && doc.pageContent.length) {
              originalText += doc.pageContent + "\n\n";
            }
          }

          if (!originalText.trim()) {
            response.status(404).json({
              success: false,
              error:
                tSync("docxEdit.errors.noContent") ||
                "No text content found in the document",
            } as DocxContentResponse);
            return;
          }

          response.status(200).json({
            success: true,
            content: originalText,
          } as DocxContentResponse);
        }
      } catch (error: unknown) {
        console.error("Error getting document content:", error);
        response.status(500).json({
          success: false,
          error: "Error getting document content: " + (error as Error).message,
        } as DocxContentResponse);
      }
    }
  );

  // Endpoint to download the edited DOCX file
  router.get(
    "/docx-edit/download/:sessionId/:filename",
    process.env.NODE_ENV === "development" ? [] : [validatedRequest],
    async (request: SessionRequest, response: Response): Promise<void> => {
      try {
        const { sessionId, filename } = request.params;

        if (!sessionId || !filename) {
          response.status(400).json({
            success: false,
            error:
              tSync("docxEdit.errors.processingError") ||
              "Session ID and filename are required",
          });
          return;
        }

        // Check if the session directory exists
        const sessionDir = path.join(docxEditPath, sessionId);
        if (!fs.existsSync(sessionDir)) {
          response.status(404).json({
            success: false,
            error:
              tSync("docxEdit.errors.sessionNotFound") || "Session not found",
          });
          return;
        }

        // Check if the file exists
        const filePath = path.join(sessionDir, filename);
        if (!fs.existsSync(filePath)) {
          response.status(404).json({
            success: false,
            error: tSync("docxEdit.errors.fileNotFound") || "File not found",
          });
          return;
        }

        // Set headers for file download
        response.setHeader(
          "Content-Disposition",
          `attachment; filename="${filename}"`
        );
        response.setHeader(
          "Content-Type",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        );

        // Stream the file to the client
        const fileStream = fs.createReadStream(filePath);
        fileStream.pipe(response);
      } catch (error: unknown) {
        console.error("Error downloading edited DOCX file:", error);
        response.status(500).json({
          success: false,
          error:
            "Error downloading edited DOCX file: " + (error as Error).message,
        });
      }
    }
  );

  // Endpoint to convert text to DOCX (auto-uses system template if configured)
  router.post(
    "/docx-edit/text-to-docx",
    process.env.NODE_ENV === "development" ? [] : [validatedRequest],
    async (request: Request, response: Response): Promise<void> => {
      try {
        const { text, title }: TextToDocxRequest = reqBody(request);

        if (!text) {
          response.status(400).json({
            success: false,
            error:
              tSync("docxEdit.errors.missingParams") ||
              "Text content is required",
          } as TextToDocxResponse);
          return;
        }

        // Determine if a global system template exists
        const systemTemplateSetting = await SystemSettings.get({
          label: "docx_system_template",
        });

        const canvasTitle =
          title || tSync("docx.canvasDocumentTitle") || "Canvas Document";

        // Create a new session directory
        const sessionId = uuidv4();
        const sessionDir = path.join(docxEditPath, sessionId);
        fs.mkdirSync(sessionDir, { recursive: true });

        let filename: string | undefined;
        let outputPath: string;

        if (systemTemplateSetting?.value) {
          // Use the system template
          const storagePath =
            process.env.NODE_ENV === "development"
              ? path.resolve(__dirname, "../../../storage/assets")
              : path.resolve(process.env.STORAGE_DIR!, "assets");
          const templatePath = path.join(
            storagePath,
            systemTemplateSetting.value
          );

          if (fs.existsSync(templatePath)) {
            const filenameBase = createDocxFilename();
            filename = filenameBase; // always .docx now

            // 1. Convert markdown -> standalone DOCX (body only)
            const bodyPath = path.join(sessionDir, "_body.docx");
            await textToDocx(text, bodyPath, { title: canvasTitle });

            // 2. Merge body into template (preserves template header/footer/styles)
            outputPath = path.join(sessionDir, filename);
            mergeDocxWithTemplate(templatePath, bodyPath, outputPath);

            // Clean up temporary body file
            if (fs.existsSync(bodyPath)) {
              fs.unlinkSync(bodyPath);
            }
          }
        }

        // If filename not set (no system template) fallback to vanilla textToDocx
        if (!filename) {
          filename = createDocxFilename();
          outputPath = path.join(sessionDir, filename);
          await textToDocx(text, outputPath, { title: canvasTitle });
        }

        // Ensure filename is defined at this point
        if (!filename) {
          throw new Error("Failed to generate filename");
        }

        response.status(200).json({
          success: true,
          message:
            tSync("docxEdit.success.textToDocx") ||
            "Text converted to DOCX successfully",
          data: {
            sessionId,
            filename,
          },
        } as TextToDocxResponse);
      } catch (error: unknown) {
        console.error("Error converting text to DOCX:", error);
        response.status(500).json({
          success: false,
          error: "Error converting text to DOCX: " + (error as Error).message,
        } as TextToDocxResponse);
      }
    }
  );

  // Endpoint to upload a DOCX file for template content
  router.post(
    "/docx-edit/upload-for-template",
    process.env.NODE_ENV === "development"
      ? [handleFileUpload]
      : [validatedRequest, handleFileUpload],
    async (request: Request, response: Response): Promise<void> => {
      try {
        const file = request.file;
        // In development mode, we don't need a valid user
        if (process.env.NODE_ENV !== "development") {
          await userFromSession(request as Request);
        }

        if (!file) {
          response.status(400).json({
            success: false,
            error: "No file provided",
          } as DocxTemplateUploadResponse);
          return;
        }

        // Validate file type (.docx or .dotx)
        const fileExt = path.extname(file.originalname).toLowerCase();
        const allowedExts = [".docx"];
        if (!allowedExts.includes(fileExt)) {
          // Clean up the uploaded file
          if (fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
          }

          response.status(400).json({
            success: false,
            error:
              tSync("docxEdit.errors.processingError") ||
              "Only DOCX files are supported for templates",
          } as DocxTemplateUploadResponse);
          return;
        }

        // Create a unique session ID for this editing session
        const sessionId = uuidv4();

        // Create a directory for this editing session
        const sessionDir = path.join(docxEditPath, sessionId);
        fs.mkdirSync(sessionDir, { recursive: true });

        // Move the uploaded file to the session directory
        const originalFilePath = path.join(sessionDir, "original" + fileExt);
        fs.copyFileSync(file.path, originalFilePath);

        // Clean up the uploaded file
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }

        try {
          // Convert DOCX to markdown
          const markdownContent = await docxToMarkdown(originalFilePath);

          if (!markdownContent.trim()) {
            response.status(404).json({
              success: false,
              error:
                tSync("docxEdit.errors.noContent") ||
                "No text content found in the document",
            } as DocxTemplateUploadResponse);
            return;
          }

          response.status(200).json({
            success: true,
            message:
              tSync("docxEdit.success.contentExtracted") ||
              "Content extracted from DOCX file successfully",
            data: {
              sessionId,
              filename: file.originalname,
              content: markdownContent,
            },
          } as DocxTemplateUploadResponse);
        } catch (error: unknown) {
          console.error("Error converting DOCX to markdown:", error);

          // Fallback to the original method if markdown conversion fails
          const loader = new DocxLoader(originalFilePath);
          const docs = await loader.load();

          // Combine all document content
          let originalText = "";
          for (const doc of docs) {
            if (doc.pageContent && doc.pageContent.length) {
              originalText += doc.pageContent + "\n\n";
            }
          }

          if (!originalText.trim()) {
            response.status(404).json({
              success: false,
              error:
                tSync("docxEdit.errors.noContent") ||
                "No text content found in the document",
            } as DocxTemplateUploadResponse);
            return;
          }

          response.status(200).json({
            success: true,
            message:
              tSync("docxEdit.success.contentExtracted") ||
              "Content extracted from DOCX file successfully",
            data: {
              sessionId,
              filename: file.originalname,
              content: originalText,
            },
          } as DocxTemplateUploadResponse);
        }
      } catch (error: unknown) {
        console.error("Error uploading DOCX for template:", error);
        response.status(500).json({
          success: false,
          error: "Error uploading DOCX file: " + (error as Error).message,
        } as DocxTemplateUploadResponse);
      }
    }
  );

  // Endpoint to clean up a session
  router.delete(
    "/docx-edit/cleanup/:sessionId",
    process.env.NODE_ENV === "development" ? [] : [validatedRequest],
    async (request: SessionRequest, response: Response): Promise<void> => {
      try {
        const { sessionId } = request.params;

        if (!sessionId) {
          response.status(400).json({
            success: false,
            error: "Session ID is required",
          } as DocxCleanupResponse);
          return;
        }

        // Check if the session directory exists
        const sessionDir = path.join(docxEditPath, sessionId);
        if (!fs.existsSync(sessionDir)) {
          response.status(404).json({
            success: false,
            error: "Session not found",
          } as DocxCleanupResponse);
          return;
        }

        // Delete the session directory
        fs.rmSync(sessionDir, { recursive: true, force: true });

        response.status(200).json({
          success: true,
          message:
            tSync("docxEdit.success.cleaned") ||
            "Session cleaned up successfully",
        } as DocxCleanupResponse);
      } catch (error: unknown) {
        console.error("Error cleaning up session:", error);
        response.status(500).json({
          success: false,
          error: "Error cleaning up session: " + (error as Error).message,
        } as DocxCleanupResponse);
      }
    }
  );

  // Endpoint to convert text to DOCX **USING A TEMPLATE**
  router.post(
    "/docx-edit/text-to-docx-template",
    process.env.NODE_ENV === "development" ? [] : [validatedRequest],
    async (request: Request, response: Response): Promise<void> => {
      try {
        const { text, title, templateSessionId }: TextToDocxRequest =
          reqBody(request);

        if (!text || !templateSessionId) {
          response.status(400).json({
            success: false,
            error:
              tSync("docxEdit.errors.missingParams") ||
              "Text content and templateSessionId are required",
          } as TextToDocxResponse);
          return;
        }

        // Locate the template file
        const templateDir = path.join(docxEditPath, templateSessionId);
        if (!fs.existsSync(templateDir)) {
          response.status(404).json({
            success: false,
            error:
              tSync("docxEdit.errors.sessionNotFound") ||
              "Template session not found",
          } as TextToDocxResponse);
          return;
        }

        const templateFile = fs
          .readdirSync(templateDir)
          .find(
            (f) =>
              f.startsWith("original") &&
              (f.endsWith(".docx") || f.endsWith(".dotx"))
          );

        if (!templateFile) {
          response.status(404).json({
            success: false,
            error:
              tSync("docxEdit.errors.fileNotFound") ||
              "Template template file not found",
          } as TextToDocxResponse);
          return;
        }

        const templatePath = path.join(templateDir, templateFile);

        // Create a new session for the rendered document
        const sessionId = uuidv4();
        const sessionDir = path.join(docxEditPath, sessionId);
        fs.mkdirSync(sessionDir, { recursive: true });

        // Generate output filename with correct extension
        const templateExt = path.extname(templateFile).toLowerCase();
        const filenameBase = createDocxFilename();
        const filename =
          templateExt === ".dotx"
            ? filenameBase.replace(/.docx$/, ".dotx")
            : filenameBase;
        const outputPath = path.join(sessionDir, filename);

        const canvasTitle =
          title || tSync("docx.canvasDocumentTitle") || "Canvas Document";

        // Render template -> DOCX
        await renderDocxTemplate(
          templatePath,
          { body: text, title: canvasTitle },
          outputPath
        );

        response.status(200).json({
          success: true,
          message:
            tSync("docxEdit.success.textToDocx") ||
            "Text converted to DOCX successfully",
          data: {
            sessionId,
            filename,
          },
        } as TextToDocxResponse);
      } catch (error: unknown) {
        console.error("Error converting text + template to DOCX:", error);
        response.status(500).json({
          success: false,
          error:
            "Error converting text to DOCX with template: " +
            (error as Error).message,
        } as TextToDocxResponse);
      }
    }
  );
}

export default docxEditEndpoints;
