/**
 * Rexor API proxy endpoints
 * Provides proxy endpoints to forward requests to the external Rexor API
 * using configured settings from SystemSettings
 */

import { Request, Response, Router } from "express";
import express from "express";
import { validatedRequest } from "../../../utils/middleware/validatedRequest";
import SystemSettings from "../../../models/systemSettings";
import { reqBody } from "../../../utils/http";

interface RexorAuthRequest {
  username: string;
  password: string;
}

interface RexorProjectRequest {
  access_token: string;
  projectData: {
    ApiTable: string;
    ApiSelect: string;
    ApiJoin: string;
    ApiWhere: string;
  };
}

interface RexorTransactionData {
  ProjectUID: string;
  ResourceUID: string;
  ProjectActivityUID: string;
  ArticleUID: string;
  Number: number;
  InvoicedNumber: number;
  Invoiceable: number;
  InvoiceText: string;
  RegistrationDate: string;
  Origin: number;
  InvoiceStatus: string;
  Status: number;
  [key: string]: string | number | boolean | null;
}

interface RexorTransactionRequest {
  access_token: string;
  transactionData: RexorTransactionData;
}

// Rexor API Response Types
interface RexorAuthResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token?: string;
  scope?: string;
  error?: string;
  error_description?: string;
}

interface RexorProjectResponse {
  id?: string;
  uid?: string;
  status?: string;
  message?: string;
  error?: string;
  error_description?: string;
  [key: string]: unknown;
}

interface RexorTransactionStatusResponse {
  uid: string;
  status: string;
  message?: string;
  error?: string;
  error_description?: string;
  [key: string]: unknown;
}

interface RexorTransactionResponse {
  uid?: string;
  transactionId?: string;
  status?: string;
  message?: string;
  error?: string;
  error_description?: string;
  [key: string]: unknown;
}

// Middleware to parse URL-encoded bodies specifically for rexor endpoints
const parseUrlEncodedBody = express.urlencoded({
  limit: "3GB",
  extended: true,
});

export function rexorProxyEndpoints(apiRouter: Router): void {
  if (!apiRouter) return;

  // POST /api/system/rexor/auth - Login to Rexor
  apiRouter.post(
    "/system/rexor/auth",
    parseUrlEncodedBody,
    async (request: Request, response: Response): Promise<void> => {
      try {
        // Get Rexor settings
        const settings = await SystemSettings.getRexorApiSettings();

        if (!settings.authUrl) {
          response.status(503).json({
            error: "Rexor authentication URL not configured",
            details: "Please configure Rexor API settings in system settings",
          });
          return;
        }

        const { username, password } = request.body as RexorAuthRequest;

        if (!username || !password) {
          response.status(400).json({
            error: "Missing required fields",
            details: "Username and password are required",
          });
          return;
        }

        // Determine which client ID to use based on environment
        const clientId =
          process.env.NODE_ENV === "production"
            ? settings.clientIdProd
            : settings.clientIdDev;

        if (!clientId) {
          response.status(503).json({
            error: "Rexor client ID not configured",
            details: "Please configure Rexor client ID in system settings",
          });
          return;
        }

        // Forward authentication request to Rexor
        const authResponse = await fetch(settings.authUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            Host: settings.apiHost || "",
          },
          body: new URLSearchParams({
            grant_type: "password",
            username,
            password,
            client_id: clientId,
          }).toString(),
        });

        const authData = (await authResponse.json()) as RexorAuthResponse;

        if (!authResponse.ok) {
          response.status(authResponse.status).json({
            error: authData?.error || "Authentication failed",
            details:
              authData?.error_description ||
              "Failed to authenticate with Rexor",
          });
          return;
        }

        // Return the authentication response
        response.status(200).json(authData);
      } catch (error) {
        console.error("Error in Rexor auth proxy:", error);
        response.status(500).json({
          error: "Internal server error",
          details:
            error instanceof Error ? error.message : "Unknown error occurred",
        });
      }
    }
  );

  // POST /api/system/rexor/projects - Register project in Rexor
  apiRouter.post(
    "/system/rexor/projects",
    [validatedRequest],
    async (request: Request, response: Response): Promise<void> => {
      try {
        const settings = await SystemSettings.getRexorApiSettings();

        if (!settings.apiBaseUrl) {
          response.status(503).json({
            error: "Rexor API base URL not configured",
            details: "Please configure Rexor API settings in system settings",
          });
          return;
        }

        const body = reqBody(request) as RexorProjectRequest;

        if (!body.access_token || !body.projectData) {
          response.status(400).json({
            error: "Missing required fields",
            details: "access_token and projectData are required",
          });
          return;
        }

        // Forward project registration request to Rexor
        const projectUrl = `${settings.apiBaseUrl}/api/data`;
        const projectResponse = await fetch(projectUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${body.access_token}`,
            Host: settings.apiHost || "",
          },
          body: JSON.stringify(body.projectData),
        });

        const projectData =
          (await projectResponse.json()) as RexorProjectResponse;

        if (!projectResponse.ok) {
          response.status(projectResponse.status).json({
            error: projectData?.error || "Project registration failed",
            details:
              projectData?.error_description ||
              "Failed to register project in Rexor",
          });
          return;
        }

        response.status(200).json(projectData);
      } catch (error) {
        console.error("Error in Rexor projects proxy:", error);
        response.status(500).json({
          error: "Internal server error",
          details:
            error instanceof Error ? error.message : "Unknown error occurred",
        });
      }
    }
  );

  // GET /api/system/rexor/transaction-status/:uid - Get transaction status
  apiRouter.get(
    "/system/rexor/transaction-status/:uid",
    [validatedRequest],
    async (request: Request, response: Response): Promise<void> => {
      try {
        const settings = await SystemSettings.getRexorApiSettings();

        if (!settings.apiBaseUrl) {
          response.status(503).json({
            error: "Rexor API base URL not configured",
            details: "Please configure Rexor API settings in system settings",
          });
          return;
        }

        const { uid } = request.params;
        const authHeader = request.headers.authorization;

        if (!uid) {
          response.status(400).json({
            error: "Missing transaction UID",
            details: "Transaction UID is required",
          });
          return;
        }

        if (!authHeader || !authHeader.startsWith("Bearer ")) {
          response.status(401).json({
            error: "Missing or invalid authorization",
            details: "Bearer token is required",
          });
          return;
        }

        // Forward status request to Rexor
        const statusUrl = `${settings.apiBaseUrl}/api/transaction/${uid}/status`;
        const statusResponse = await fetch(statusUrl, {
          method: "GET",
          headers: {
            Authorization: authHeader,
            Host: settings.apiHost || "",
          },
        });

        const statusData =
          (await statusResponse.json()) as RexorTransactionStatusResponse;

        if (!statusResponse.ok) {
          response.status(statusResponse.status).json({
            error: statusData?.error || "Failed to get transaction status",
            details:
              statusData?.error_description ||
              "Failed to retrieve transaction status from Rexor",
          });
          return;
        }

        response.status(200).json(statusData);
      } catch (error) {
        console.error("Error in Rexor transaction status proxy:", error);
        response.status(500).json({
          error: "Internal server error",
          details:
            error instanceof Error ? error.message : "Unknown error occurred",
        });
      }
    }
  );

  // POST /api/system/rexor/article-expense-transaction - Create article expense transaction
  apiRouter.post(
    "/system/rexor/article-expense-transaction",
    [validatedRequest],
    async (request: Request, response: Response): Promise<void> => {
      try {
        const settings = await SystemSettings.getRexorApiSettings();

        if (!settings.apiBaseUrl) {
          response.status(503).json({
            error: "Rexor API base URL not configured",
            details: "Please configure Rexor API settings in system settings",
          });
          return;
        }

        const body = reqBody(request) as RexorTransactionRequest;

        if (!body.access_token || !body.transactionData) {
          response.status(400).json({
            error: "Missing required fields",
            details: "access_token and transactionData are required",
          });
          return;
        }

        // Validate required transaction fields
        const requiredFields = [
          "ProjectUID",
          "ResourceUID",
          "ProjectActivityUID",
          "ArticleUID",
        ];
        for (const field of requiredFields) {
          if (!body.transactionData[field]) {
            response.status(400).json({
              error: "Missing required transaction field",
              details: `${field} is required in transactionData`,
            });
            return;
          }
        }

        // Forward transaction request to Rexor
        const transactionUrl = `${settings.apiBaseUrl}/api/transactions/article-expense`;
        const transactionResponse = await fetch(transactionUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${body.access_token}`,
            Host: settings.apiHost || "",
          },
          body: JSON.stringify(body.transactionData),
        });

        const transactionData =
          (await transactionResponse.json()) as RexorTransactionResponse;

        if (!transactionResponse.ok) {
          response.status(transactionResponse.status).json({
            error: transactionData?.error || "Transaction creation failed",
            details:
              transactionData?.error_description ||
              "Failed to create article expense transaction in Rexor",
          });
          return;
        }

        response.status(200).json(transactionData);
      } catch (error) {
        console.error(
          "Error in Rexor article expense transaction proxy:",
          error
        );
        response.status(500).json({
          error: "Internal server error",
          details:
            error instanceof Error ? error.message : "Unknown error occurred",
        });
      }
    }
  );
}
