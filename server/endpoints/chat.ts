import { v4 as uuidv4 } from "uuid";
import { Request, Response, Router } from "express";
import { reqBody, userFromSession, multiUserMode } from "../utils/http";
import { validatedRequest } from "../utils/middleware/validatedRequest";
import { WorkspaceChats } from "../models/workspaceChats";
import SystemSettings from "../models/systemSettings";
import { Telemetry } from "../models/telemetry";
import { streamChatWithWorkspace } from "../utils/chats/stream";
import {
  ROLES,
  flexUserRoleValid,
} from "../utils/middleware/multiUserProtected";
import { EventLogs } from "../models/eventLogs";
import {
  validWorkspaceAndThreadSlug,
  validWorkspaceSlug,
} from "../utils/middleware/validWorkspace";
import { writeResponseChunk } from "../utils/helpers/chat/responses";
import { readChatLog } from "../utils/helpers/chat/logs";
import { WorkspaceThread } from "../models/workspaceThread";
import officegen from "officegen";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";
import * as i18n from "../utils/i18n";
import { TokenManager } from "../utils/helpers/tiktoken";
import { extractFirstSentence } from "../utils/helpers/thread/textProcessing";
import { sanitizeCssColor } from "../utils/helpers/validation";
import { ExpressApp } from "../types/shared";
import {
  ChatRequestBody,
  StreamResponseChunk,
  DocxExportRequest,
  DocxExportResponse,
  ChatLogResponse,
  ChatAttachment,
} from "../types/api";
// Define custom types for officegen since it doesn't export proper types
interface DocxTextOptions {
  bold?: boolean;
  italic?: boolean;
  font_size?: number;
  font_face?: string;
  color?: string;
  highlight?: string;
  shading?: {
    fill: string;
    color: string;
  };
}

interface DocxParagraph {
  addText(text: string, options?: DocxTextOptions): void;
  addLineBreak(): void;
}

interface DocxDocument {
  setDocSubject(_subject: string): void;
  setDocKeywords(_keywords: string): void;
  setDescription(_description: string): void;
  createP(): DocxParagraph;
  generate(_stream: NodeJS.WritableStream): void;
  on(_event: string, _handler: (err: Error) => void): void;
}

function chatEndpoints(app: ExpressApp, apiRouter?: Router): void {
  if (!app) return;

  // Use apiRouter for API routes if provided
  const router = apiRouter || app;

  router.post(
    "/workspace/:slug/stream-chat/:slugModule",
    [validatedRequest, flexUserRoleValid([ROLES.all]), validWorkspaceSlug],
    async (request: Request, response: Response): Promise<void> => {
      try {
        // Parse and validate message early, then start stream headers
        const requestBody = reqBody(request) as ChatRequestBody;

        const {
          message,
          attachments = [] as ChatAttachment[],
          chatId = null,
          isCanvasChat = false,
          preventChatCreation = false,
          llmSelected = 0,
          invoice_ref = null,
          hasUploadedFile = false,
          docxContent = null,
          displayMessage = null,
          useDeepSearch = false,
          cdbOptions = [],
          legalTaskConfig = {},
          settings_suffix: settingsSuffix = "",
          styleAlignment = null,
        } = requestBody;

        if (!message?.length) {
          response.status(400).json({
            uuid: uuidv4(),
            type: "abort",
            textResponse: null,
            sources: [],
            close: true,
            error: !message?.length ? "Message is empty." : "",
          } as StreamResponseChunk);
          return;
        }

        // Authenticate and check rate limits BEFORE setting SSE headers
        const user = await userFromSession(request);
        const { slugModule = null } = request.params;
        const workspace = response.locals.workspace;

        if (multiUserMode(response) && user && user.role !== ROLES.admin) {
          const limitMessagesSetting = await SystemSettings.get({
            label: "limit_user_messages",
          });
          const limitMessages = (limitMessagesSetting?.value ?? 0) === "true";

          if (limitMessages) {
            const messageLimitSetting = await SystemSettings.get({
              label: "message_limit",
            });
            const systemLimit = Number(messageLimitSetting?.value);

            if (systemLimit) {
              const currentChatCount = await WorkspaceChats.count({
                user_id: user!.id,
                createdAt: {
                  gte: new Date(new Date().getTime() - 24 * 60 * 60 * 1000),
                },
              });

              if (currentChatCount >= systemLimit) {
                // Set SSE headers before writing the rate limit error
                response.setHeader("Cache-Control", "no-cache");
                response.setHeader("Content-Type", "text/event-stream");
                response.setHeader("Access-Control-Allow-Origin", "*");
                response.setHeader("Connection", "keep-alive");
                response.flushHeaders();

                writeResponseChunk(response, {
                  uuid: uuidv4(),
                  type: "abort",
                  textResponse: null,
                  sources: [],
                  close: true,
                  error: `You have met your maximum 24 hour chat quota of ${systemLimit} chats set by the instance administrators. Try again later.`,
                });
                response.end();
                return;
              }
            }
          }
        }

        // Set SSE headers after all validation checks pass
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Content-Type", "text/event-stream");
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Connection", "keep-alive");
        response.flushHeaders();

        let LLMSelection: string;
        let Embedder: string;
        let VectorDbSelection: string;
        let finalSettingsSuffix = settingsSuffix || "";

        if (slugModule === "document-drafting") {
          if (llmSelected === 1 && process.env.BINARY_LLM_DD === "on") {
            LLMSelection = process.env.LLM_PROVIDER_DD_2 || "";
            finalSettingsSuffix = "_DD_2";
          } else {
            LLMSelection = process.env.LLM_PROVIDER_DD || "";
            finalSettingsSuffix = "_DD";
          }
          Embedder = process.env.EMBEDDING_ENGINE_DD || "";
          VectorDbSelection = process.env.VECTOR_DB_DD || "";
        } else {
          LLMSelection = process.env.LLM_PROVIDER!;
          Embedder = process.env.EMBEDDING_ENGINE!;
          VectorDbSelection = process.env.VECTOR_DB!;
        }

        // Parse styleAlignment if it's a string
        let parsedStyleAlignment = null;
        if (styleAlignment) {
          try {
            parsedStyleAlignment =
              typeof styleAlignment === "string"
                ? JSON.parse(styleAlignment)
                : styleAlignment;
          } catch {
            parsedStyleAlignment = null;
          }
        }

        await streamChatWithWorkspace(
          request,
          response,
          workspace,
          message,
          workspace?.chatMode,
          user,
          null,
          (attachments as ChatAttachment[]).map(
            (att: ChatAttachment, index: number) => ({
              ...att,
              id: att.id || att.name || `attachment-${index}`,
            })
          ),
          chatId || "",
          isCanvasChat,
          preventChatCreation,
          finalSettingsSuffix,
          invoice_ref || undefined,
          workspace?.vectorSearchMode,
          hasUploadedFile,
          docxContent,
          displayMessage,
          useDeepSearch,
          cdbOptions,
          legalTaskConfig,
          parsedStyleAlignment
        );

        await Telemetry.sendTelemetry("sent_chat", {
          multiUserMode: multiUserMode(response),
          LLMSelection: LLMSelection || "openai",
          Embedder: Embedder || "inherit",
          VectorDbSelection: VectorDbSelection || "lancedb",
          multiModal:
            Array.isArray(attachments) && (attachments?.length ?? 0) !== 0,
          TTSSelection: process.env.TTS_PROVIDER || "native",
        });

        await EventLogs.logEvent(
          "sent_chat",
          {
            workspaceName: workspace?.name,
            chatModel: (workspace?.chatModel ?? false) || "System Default",
          },
          user?.id
        );
        response.end();
      } catch (e) {
        console.error(e);
        writeResponseChunk(response, {
          uuid: uuidv4(),
          type: "abort",
          textResponse: null,
          sources: [],
          close: true,
          error: e instanceof Error ? (e as Error).message : String(e),
        });
        response.end();
      }
    }
  );

  router.post(
    "/workspace/:slug/thread/:threadSlug/stream-chat/:slugModule",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.all]),
      validWorkspaceAndThreadSlug,
    ],
    async (request: Request, response: Response): Promise<void> => {
      try {
        // Early parse and validation
        const {
          message,
          attachments = [] as ChatAttachment[],
          chatId = null,
          isCanvasChat = false,
          preventChatCreation = false,
          llmSelected = 0,
          invoice_ref = null,
          hasUploadedFile = false,
          docxContent = null,
          displayMessage = null,
          useDeepSearch = false,
          cdbOptions = [],
          legalTaskConfig = {},
          settings_suffix: settingsSuffix = "",
          styleAlignment = null,
        }: ChatRequestBody = reqBody(request);

        if (!message?.length) {
          response.status(400).json({
            uuid: uuidv4(),
            type: "abort",
            textResponse: null,
            sources: [],
            close: true,
            error: !message?.length ? "Message is empty." : "",
          } as StreamResponseChunk);
          return;
        }

        // Authenticate and check rate limits BEFORE setting SSE headers
        const user = await userFromSession(request);
        const { slugModule = null } = request.params;
        const workspace = response.locals.workspace;
        let thread = response.locals.thread;

        if (multiUserMode(response) && user && user.role !== ROLES.admin) {
          const limitMessagesSetting = await SystemSettings.get({
            label: "limit_user_messages",
          });
          const limitMessages = (limitMessagesSetting?.value ?? 0) === "true";

          if (limitMessages) {
            const messageLimitSetting = await SystemSettings.get({
              label: "message_limit",
            });
            const systemLimit = Number(messageLimitSetting?.value);

            if (systemLimit) {
              // Chat qty includes all threads because any user can freely
              // create threads and would bypass this rule.
              const currentChatCount = await WorkspaceChats.count({
                user_id: user!.id,
                createdAt: {
                  gte: new Date(new Date().getTime() - 24 * 60 * 60 * 1000),
                },
              });

              if (currentChatCount >= systemLimit) {
                // Set SSE headers before writing the rate limit error
                response.setHeader("Cache-Control", "no-cache");
                response.setHeader("Content-Type", "text/event-stream");
                response.setHeader("Access-Control-Allow-Origin", "*");
                response.setHeader("Connection", "keep-alive");
                response.flushHeaders();

                writeResponseChunk(response, {
                  uuid: uuidv4(),
                  type: "abort",
                  textResponse: null,
                  sources: [],
                  close: true,
                  error: `You have met your maximum 24 hour chat quota of ${systemLimit} chats set by the instance administrators. Try again later.`,
                });
                response.end();
                return;
              }
            }
          }
        }

        // Set SSE headers after all validation checks pass
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Content-Type", "text/event-stream");
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Connection", "keep-alive");
        response.flushHeaders();

        let LLMSelection: string;
        let Embedder: string;
        let VectorDbSelection: string;
        let finalSettingsSuffix = settingsSuffix || "";

        if (slugModule === "document-drafting") {
          if (llmSelected === 1 && process.env.BINARY_LLM_DD === "on") {
            LLMSelection = process.env.LLM_PROVIDER_DD_2 || "";
            finalSettingsSuffix = "_DD_2";
          } else {
            LLMSelection = process.env.LLM_PROVIDER_DD || "";
            finalSettingsSuffix = "_DD";
          }
          Embedder = process.env.EMBEDDING_ENGINE_DD || "";
          VectorDbSelection = process.env.VECTOR_DB_DD || "";
        } else {
          LLMSelection = process.env.LLM_PROVIDER!;
          Embedder = process.env.EMBEDDING_ENGINE!;
          VectorDbSelection = process.env.VECTOR_DB!;
        }

        // Parse styleAlignment if it's a string
        let parsedStyleAlignment = null;
        if (styleAlignment) {
          try {
            parsedStyleAlignment =
              typeof styleAlignment === "string"
                ? JSON.parse(styleAlignment)
                : styleAlignment;
          } catch {
            parsedStyleAlignment = null;
          }
        }

        // The thread slug endpoint needs to first check if thread needs renaming
        // before streaming content
        if (thread && thread.name === WorkspaceThread.defaultName) {
          try {
            // Rename thread on first message
            const { thread: updatedThread } = await WorkspaceThread.update(
              thread,
              {
                name: extractFirstSentence(message || ""),
              }
            );

            if (updatedThread) {
              // Update the local thread variable with the new name
              thread = updatedThread;
              // After updating locally, we'll send a thread rename notification
              // during the stream to ensure the client knows about it
            }
          } catch (error) {
            console.error("Error in pre-stream thread renaming:", error);
          }
        }

        const processedAttachments = (attachments as ChatAttachment[]).map(
          (att: ChatAttachment, index: number) => ({
            ...att,
            id: att.id || att.name || `attachment-${index}`,
          })
        );

        await streamChatWithWorkspace(
          request,
          response,
          workspace,
          message,
          workspace?.chatMode,
          user,
          thread,
          processedAttachments,
          chatId || "",
          isCanvasChat,
          preventChatCreation,
          finalSettingsSuffix,
          invoice_ref || undefined,
          workspace?.vectorSearchMode,
          hasUploadedFile,
          docxContent,
          displayMessage,
          useDeepSearch,
          cdbOptions,
          legalTaskConfig,
          parsedStyleAlignment
        );

        // Send the rename_thread event after stream is sent but before ending
        if (thread && thread.name !== WorkspaceThread.defaultName) {
          try {
            // Send thread rename as a custom event
            response.write(
              `data: ${JSON.stringify({
                action: "rename_thread",
                thread: {
                  slug: thread.slug,
                  name: thread.name,
                },
              })}\n\n`
            );
          } catch (error) {
            console.error("Error sending thread rename event:", error);
          }
        }

        await Telemetry.sendTelemetry("sent_chat", {
          multiUserMode: multiUserMode(response),
          LLMSelection: LLMSelection || "openai",
          Embedder: Embedder || "inherit",
          VectorDbSelection: VectorDbSelection || "lancedb",
          multiModal:
            Array.isArray(attachments) && (attachments?.length ?? 0) !== 0,
          TTSSelection: process.env.TTS_PROVIDER || "native",
        });

        await EventLogs.logEvent(
          "sent_chat",
          {
            workspaceName: workspace.name,
            thread: thread.name,
            chatModel: (workspace?.chatModel ?? false) || "System Default",
          },
          user?.id
        );
        response.end();
      } catch (e) {
        console.error(e);
        writeResponseChunk(response, {
          uuid: uuidv4(),
          type: "abort",
          textResponse: null,
          sources: [],
          close: true,
          error: e instanceof Error ? (e as Error).message : String(e),
        });
        response.end();
      }
    }
  );

  router.get(
    "/workspace/chat-log/:chatId",
    [validatedRequest, flexUserRoleValid([ROLES.all])],
    async (request: Request, response: Response): Promise<void> => {
      try {
        const { chatId } = request.params;
        const chat = await WorkspaceChats.get({ id: Number(chatId) });
        if (!chat) {
          response.status(404).json({
            message: "Chat not found",
          });
          return;
        }
        const chatLog = await readChatLog(Number(chatId));
        response.json({
          success: true,
          chatLog,
        } as ChatLogResponse);
      } catch (error) {
        console.error("Error retrieving chat log:", error);
        const errorMessage =
          error instanceof Error ? (error as Error).message : String(error);
        response.status(errorMessage.includes("not found") ? 404 : 500).json({
          success: false,
          message: errorMessage,
        } as ChatLogResponse);
      }
    }
  );

  router.post(
    "/chat/export-docx",
    [validatedRequest, flexUserRoleValid([ROLES.all])],
    async (request: Request, response: Response): Promise<void> => {
      try {
        await userFromSession(request);
        const { content }: DocxExportRequest = reqBody(request);

        if (!content) {
          response.status(400).json({
            error: await i18n.t("docx.errors.contentRequired"),
          } as DocxExportResponse);
          return;
        }

        // Get translations first
        const [
          titleText,
          exportedOnText,
          keywordsText,
          descriptionText,
          errorWritingText,
          errorGeneratingText,
          tokenCountText,
        ] = await Promise.all([
          i18n.t("docx.title"),
          i18n.t("docx.exportedOn"),
          i18n.t("docx.keywords"),
          i18n.t("docx.description"),
          i18n.t("docx.errors.writingFile"),
          i18n.t("docx.errors.generating"),
          i18n.t("docx.tokenCount"),
        ]);

        // Create Word document with better formatting
        const docx = officegen({ type: "docx" });

        // Set document properties
        docx.setDocSubject(titleText);
        docx.setDocKeywords(keywordsText);
        docx.setDescription(descriptionText);

        // Add a title
        const title = docx.createP();
        title.addText(titleText, {
          bold: true,
          font_size: 14,
          font_face: "Calibri",
        });
        title.addLineBreak();
        title.addLineBreak();

        // Add timestamp
        const timestamp = docx.createP();
        timestamp.addText(
          `${exportedOnText}: ${new Date().toLocaleString("en-US", {
            year: "numeric",
            month: "numeric",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit",
            hour12: false,
          })}`,
          {
            font_size: 10,
            font_face: "Calibri",
          }
        );
        timestamp.addLineBreak();
        timestamp.addLineBreak();

        // Add token count
        const tokenManager = new TokenManager();
        const tokenCount = tokenManager.countFromString(content);
        const tokenInfo = docx.createP();
        tokenInfo.addText(`${tokenCountText}: ${tokenCount}`, {
          font_size: 10,
          font_face: "Calibri",
        });
        tokenInfo.addLineBreak();
        tokenInfo.addLineBreak();

        // Add content with proper formatting
        convertMarkdownToDocx(docx, content);

        // Create temporary file
        const tempFilePath = path.join(os.tmpdir(), `${uuidv4()}.docx`);
        const output = fs.createWriteStream(tempFilePath);

        // Handle potential errors during generation
        output.on("error", (err) => {
          console.error("Error writing temporary file:", err);
          response.status(500).json({
            error: errorWritingText,
          } as DocxExportResponse);
          return;
        });

        docx.on("error", (err) => {
          console.error("Error generating document:", err);
          response.status(500).json({
            error: errorGeneratingText,
          } as DocxExportResponse);
          return;
        });

        // Generate and send the document
        docx.generate(output);

        output.on("close", () => {
          response.download(
            tempFilePath,
            `chat-export-${new Date().toLocaleDateString().replace(/\//g, "-")}.docx`,
            (err) => {
              // Clean up temporary file
              fs.unlink(tempFilePath, (unlinkErr) => {
                if (unlinkErr) {
                  console.error("Error deleting temporary file:", unlinkErr);
                }
              });

              if (err) {
                console.error("Error sending file:", err);
                response.status(500).json({
                  error: errorWritingText,
                } as DocxExportResponse);
                return;
              }
            }
          );
        });
      } catch (error) {
        console.error("Error in export-docx endpoint:", error);
        const errorMessage =
          error instanceof Error ? (error as Error).message : String(error);
        response.status(500).json({
          error: errorMessage,
          details: errorMessage,
        } as DocxExportResponse);
      }
    }
  );
}

// Convert markdown to docx formatting
function convertMarkdownToDocx(docx: DocxDocument, markdown: string): void {
  const lines = markdown.split("\n");
  let inCodeBlock = false;

  for (const line of lines) {
    // Skip empty lines
    if (!line.trim()) {
      const p = docx.createP();
      p.addLineBreak();
      continue;
    }

    // Handle code blocks
    if (line.startsWith("```")) {
      inCodeBlock = !inCodeBlock;
      if (!inCodeBlock) {
        const p = docx.createP();
        p.addLineBreak();
      }
      continue;
    }

    if (inCodeBlock) {
      const p = docx.createP();
      p.addText(line, {
        font_face: "Courier New",
        font_size: 10,
        color: "666666",
      });
      continue;
    }

    // Handle headers
    if (line.startsWith("#")) {
      const level = (line.match(/^#+/) || [])[0]?.length || 1;
      const text = line.replace(/^#+\s+/, "");
      const p = docx.createP();
      p.addText(text, {
        bold: true,
        font_size: 20 - level * 2,
        font_face: "Calibri",
      });
      p.addLineBreak();
      continue;
    }

    // Handle lists
    if (line.match(/^(\s*[-*+]|\s*\d+\.)\s/)) {
      const text = line.replace(/^(\s*[-*+]|\s*\d+\.)\s+/, "");
      const p = docx.createP();

      // Process inline formatting in list items without forcing bold
      processInlineFormatting(p, text);
      continue;
    }

    // Handle regular paragraphs with inline formatting
    const p = docx.createP();
    processInlineFormatting(p, line);
  }
}

// Helper function to convert color names to hex values
function colorNameToHex(color: string): string {
  const sanitzedColor = sanitizeCssColor(color);
  if (!sanitzedColor) return "000000"; // Return black for invalid input

  const colorMap: Record<string, string> = {
    yellow: "FFFF00",
    red: "FF0000",
    green: "00FF00",
    blue: "0000FF",
    orange: "FFA500",
    pink: "FFC0CB",
    purple: "800080",
    cyan: "00FFFF",
    brown: "A52A2A",
    gray: "808080",
    black: "000000",
    white: "FFFFFF",
  };

  const cleanColor = sanitzedColor.trim().toLowerCase();

  // Return from map if a valid color name
  if (colorMap[cleanColor]) return colorMap[cleanColor];

  // Check if it is a valid hex color format.
  // Must be 3 or 6 characters, with an optional '#' at the start.
  if (/^#?([0-9a-f]{3}){1,2}$/i.test(cleanColor)) {
    return cleanColor.replace("#", "");
  }

  return "000000"; // Default to black if invalid format
}

// Process inline markdown and HTML formatting
function processInlineFormatting(p: DocxParagraph, text: string): void {
  // First, handle HTML tags by converting them to special tokens
  let processedText = text;

  // Handle <mark> tags for highlighting
  processedText = processedText.replace(
    /<mark\s+style="background-color:\s*([^"]+)"[^>]*>(.*?)<\/mark>/gi,
    (_match, color, content) => {
      const hexColor = colorNameToHex(color);
      return `{{HIGHLIGHT:${hexColor}:${content}}}`;
    }
  );

  // Handle <span> tags for text colors
  processedText = processedText.replace(
    /<span\s+style="color:\s*([^"]+)"[^>]*>(.*?)<\/span>/gi,
    (_match, color, content) => {
      const hexColor = colorNameToHex(color);
      return `{{COLOR:${hexColor}:${content}}}`;
    }
  );

  // Now process the text with tokens
  let currentText = "";
  let inBold = false;
  let inItalic = false;
  let inCode = false;

  // Helper function to escape special characters (but not our tokens)
  const escapeSpecialChars = (str: string): string => {
    return str.replace(/[<>]/g, (char) => {
      return char === "<" ? "&lt;" : "&gt;";
    });
  };

  // Helper function to add text with current formatting
  const addFormattedText = (
    textToAdd: string,
    extraOptions: DocxTextOptions = {}
  ): void => {
    if (!textToAdd) return;

    p.addText(escapeSpecialChars(textToAdd), {
      font_face: inCode ? "Courier New" : "Calibri",
      font_size: 11,
      bold: inBold,
      italic: inItalic,
      color: inCode ? "666666" : "000000",
      ...extraOptions,
    });
  };

  for (let i = 0; i < processedText.length; i++) {
    // Check for highlight tokens
    if (processedText.substring(i).startsWith("{{HIGHLIGHT:")) {
      // Add any accumulated text first
      if (currentText) {
        addFormattedText(currentText);
        currentText = "";
      }

      // Find the end of the token
      const tokenEnd = processedText.indexOf("}}", i);
      if (tokenEnd !== -1) {
        const token = processedText.substring(i, tokenEnd + 2);
        const match = token.match(/{{HIGHLIGHT:([^:]+):([^}]+)}}/);
        if (match) {
          const [, color, content] = match;
          // Add highlighted text
          addFormattedText(content, {
            highlight: color,
            shading: {
              fill: color,
              color: "auto",
            },
          });
          i = tokenEnd + 1; // Skip past the token
          continue;
        }
      }
    }

    // Check for color tokens
    if (processedText.substring(i).startsWith("{{COLOR:")) {
      // Add any accumulated text first
      if (currentText) {
        addFormattedText(currentText);
        currentText = "";
      }

      // Find the end of the token
      const tokenEnd = processedText.indexOf("}}", i);
      if (tokenEnd !== -1) {
        const token = processedText.substring(i, tokenEnd + 2);
        const match = token.match(/{{COLOR:([^:]+):([^}]+)}}/);
        if (match) {
          const [, color, content] = match;
          // Add colored text
          addFormattedText(content, { color: color });
          i = tokenEnd + 1; // Skip past the token
          continue;
        }
      }
    }

    // Handle markdown formatting
    if (processedText[i] === "`" && !inBold && !inItalic) {
      if (currentText) {
        addFormattedText(currentText);
        currentText = "";
      }
      inCode = !inCode;
      continue;
    }

    if (processedText[i] === "*" || processedText[i] === "_") {
      const isDouble = processedText[i + 1] === processedText[i];

      if (currentText) {
        addFormattedText(currentText);
        currentText = "";
      }

      if (isDouble) {
        inBold = !inBold;
        i++; // Skip next character
      } else {
        inItalic = !inItalic;
      }
      continue;
    }

    currentText += processedText[i];
  }

  // Add any remaining text
  if (currentText) {
    addFormattedText(currentText);
  }
}

export { chatEndpoints };
