import { Request, Response, Router } from "express";
import { Document } from "../models/documents";
import { reqBody, userFromSession } from "../utils/http";
import {
  flexUserRoleValid,
  ROLES,
} from "../utils/middleware/multiUserProtected";
import type { AuthenticatedUser } from "../types/shared";
import { validatedRequest } from "../utils/middleware/validatedRequest";
import * as fs from "fs";
import * as fsPromises from "fs/promises";
import * as path from "path";
import {
  handleAttachmentUpload,
  getUserDocumentPathName,
} from "../utils/files/multer";
import { CollectorApi } from "../utils/collectorApi";
import prisma from "../utils/prisma";
import { ExpressApp } from "../types/shared";
import type { FilteredUser } from "../types/models";
import { documentsPath, isWithin, normalizePath } from "../utils/files";
import { findActualFilePath } from "../utils/files/findActualFilePath";
import { v4 as uuidv4 } from "uuid";

// Document-related interfaces
interface CreateFolderBody {
  name: string;
}

interface RenameFileBody {
  oldName: string;
  newName: string;
}

interface MoveFilesBody {
  files: Array<{
    from: string;
    to: string;
  }>;
}

interface AttachmentCleanupBody {
  location: string;
}

interface FileUploadRequest extends Request {
  file?: Express.Multer.File;
}

interface PathInfo {
  path: string;
  description: string;
  type?: string;
  relativePath?: string;
}

interface ResolvePathResponse {
  success: boolean;
  serverPath?: string;
  frontendPath?: string;
  checkedPaths: Array<{ path: string; description: string }>;
  error?: string;
}

interface DocumentContentsResponse {
  content?: string;
  path?: string;
  error?: string;
}

interface AttachmentProcessResponse {
  success: boolean;
  document?: {
    id: string;
    location: string;
    content: string;
    originalFilename?: string;
  };
  error?: string;
}

// Type for document JSON content
interface DocumentJSON {
  id: string;
  docId?: string;
  docpath?: string;
  filename?: string;
  metadata?: string | null;
  pageContent?: string;
  content?: string;
}

// Type for move file operation
interface MoveFileOperation {
  from: string;
  to: string;
}

// Type for database document
interface DatabaseDocument {
  docpath: string;
}

function getUserDocumentPath(
  user: FilteredUser | null,
  isDocumentDrafting: boolean = false,
  workspaceId?: string
): string {
  const name = getUserDocumentPathName(
    user as AuthenticatedUser | null,
    isDocumentDrafting,
    workspaceId
  );
  let userDocumentPath = documentsPath;
  if (name?.length > 0) {
    userDocumentPath = path.join(userDocumentPath, name);
  }
  return path.normalize(userDocumentPath);
}

function documentEndpoints(app: ExpressApp, apiRouter?: Router): void {
  if (!app) return;

  // Use apiRouter for API routes if provided
  const router = apiRouter || app;

  // Shared function to find and serve PDF files from both server and frontend locations
  const findAndServePdfFile = async (
    req: Request,
    res: Response,
    pdfPath: string
  ): Promise<void> => {
    console.log(`PDF request received for path: ${pdfPath}`);
    const originalPath = pdfPath;
    let filesToTry: PathInfo[] = [];
    const allCheckedPaths: Array<{ path: string; description: string }> = [];

    // Extract potentially workspace and filename info
    const filename = path.basename(pdfPath);

    // Configure paths for checks
    const documentsRootPath = documentsPath;

    // In production Docker environments, the frontend hotdir may not exist
    // Check if we're in a Docker/production environment
    const isProduction =
      process.env.NODE_ENV === "production" ||
      process.env.DOCKER_ENV === "true" ||
      !fs.existsSync(path.resolve(__dirname, "../../frontend"));

    // Frontend hotdir path - only use if frontend directory exists
    let frontendHotdirPath: string | null = null;
    if (!isProduction) {
      const potentialFrontendPath = path.resolve(
        __dirname,
        "../../frontend/public/hotdir"
      );
      if (fs.existsSync(potentialFrontendPath)) {
        frontendHotdirPath = potentialFrontendPath;
        console.log("Using frontend hotdir path:", frontendHotdirPath);
      }
    }

    // Server-side hotdir paths (these should exist in production)
    const serverPublicHotdirPath = path.resolve(__dirname, "../public/hotdir");
    const serverPdfviewPath = path.resolve(
      __dirname,
      "../storage/documents/pdfview"
    );

    // Additional production paths - check if we have a hotdir in storage
    const storageHotdirPath = path.resolve(__dirname, "../storage/hotdir");

    console.log("Path configuration:", {
      isProduction,
      documentsRootPath,
      frontendHotdirPath: frontendHotdirPath || "N/A (production)",
      serverPublicHotdirPath,
      serverPdfviewPath,
      storageHotdirPath,
    });

    // Keep track of what's been found in DB
    let dbDocumentFound = false;
    let jsonPath: string | null = null;

    // Try to resolve document from database first
    try {
      // Check if there's a document record with this ID or filename
      const document = await prisma.workspace_documents.findFirst({
        where: {
          OR: [
            { docId: pdfPath },
            { filename: pdfPath },
            { docpath: { contains: pdfPath } },
          ],
        },
        select: {
          id: true,
          docId: true,
          docpath: true,
          filename: true,
          metadata: true,
          workspaceId: true,
        },
      });

      if (document) {
        dbDocumentFound = true;
        console.log("Document found in database:", document.docId);

        try {
          // Try to parse the metadata for more info
          const metadata = JSON.parse(document.metadata || "{}");

          // Check if we have a PDF path in the document
          if (metadata.pdfPath) {
            console.log("Document has PDF path:", metadata.pdfPath);
            // If it's a server/pdfview path
            if (metadata.pdfPath.startsWith("/pdfview/")) {
              const pdfviewRelativePath = metadata.pdfPath.replace(
                "/pdfview/",
                ""
              );
              const pdfviewPath = path.join(
                serverPdfviewPath,
                pdfviewRelativePath
              );
              console.log("Checking pdfview path:", pdfviewPath);
              filesToTry.push({
                path: pdfviewPath,
                description: "Server pdfview path from metadata",
                type: "server-pdfview",
              });
            }
            // If it's a hotdir path
            else if (metadata.pdfPath.startsWith("/hotdir/")) {
              const hotdirRelativePath = metadata.pdfPath.replace(
                "/hotdir/",
                ""
              );

              // Try frontend hotdir first (if available)
              if (frontendHotdirPath) {
                const frontendPath = path.join(
                  frontendHotdirPath,
                  hotdirRelativePath
                );
                console.log(
                  "Checking frontend hotdir path from metadata:",
                  frontendPath
                );
                filesToTry.push({
                  path: frontendPath,
                  description: "Frontend hotdir path from metadata",
                  type: "frontend",
                });
              }

              // Also try server-side hotdir locations
              const serverHotdirPath = path.join(
                serverPublicHotdirPath,
                hotdirRelativePath
              );
              console.log(
                "Checking server hotdir path from metadata:",
                serverHotdirPath
              );
              filesToTry.push({
                path: serverHotdirPath,
                description: "Server hotdir path from metadata",
                type: "server-hotdir",
              });

              // Try storage hotdir
              const storageHotdirFile = path.join(
                storageHotdirPath,
                hotdirRelativePath
              );
              console.log(
                "Checking storage hotdir path from metadata:",
                storageHotdirFile
              );
              filesToTry.push({
                path: storageHotdirFile,
                description: "Storage hotdir path from metadata",
                type: "storage-hotdir",
              });
            }
          }

          // Also check if we have serverPdfPath in the document
          if (metadata.serverPdfPath) {
            console.log(
              "Document has server PDF path:",
              metadata.serverPdfPath
            );
            filesToTry.push({
              path: metadata.serverPdfPath,
              description: "Direct server PDF path from metadata",
              type: "server-direct",
            });
          }

          // Add frontendPath if available (for backwards compatibility)
          if (metadata.frontendPath && frontendHotdirPath) {
            const frontendHotdirFile = metadata.frontendPath.replace(
              "/hotdir/",
              ""
            );
            const frontendFullPath = path.join(
              frontendHotdirPath,
              frontendHotdirFile
            );
            console.log(
              "Checking frontend path from metadata:",
              frontendFullPath
            );
            filesToTry.push({
              path: frontendFullPath,
              description: "Frontend path from metadata",
              type: "frontend",
            });
          }
        } catch (metadataError) {
          console.warn(
            "Error parsing document metadata:",
            (metadataError as Error).message
          );
        }

        // Try to look up the JSON file path
        // ... existing code ...

        // Get path to the PDF based on JSON location
        jsonPath = document.docpath;

        // Handle both .pdf.json and .json extensions
        let derivedPdfPath: string;
        if (jsonPath.endsWith(".pdf.json")) {
          derivedPdfPath = jsonPath.replace(/\.pdf\.json$/, "");
        } else {
          derivedPdfPath = jsonPath.replace(/\.json$/, "");
        }

        console.log("Derived PDF path from JSON:", derivedPdfPath);
        filesToTry.push({
          path: derivedPdfPath,
          description: "Path derived from JSON document",
          type: "server",
        });

        // Also try the directory where the JSON is located
        const jsonDir = path.dirname(jsonPath);
        const dirPdfPath = path.join(jsonDir, filename);

        console.log("Also checking directory of JSON:", dirPdfPath);
        filesToTry.push({
          path: dirPdfPath,
          description: "Directory of JSON document with filename",
          type: "server",
        });
      }
    } catch (dbError) {
      console.warn(
        "Error looking up JSON document:",
        (dbError as Error).message
      );
    }

    // Check in server pdfview directory with just the filename (no extension)
    const baseFilename = path.parse(filename).name;
    const pdfviewPath = path.join(serverPdfviewPath, baseFilename);
    console.log("Checking server pdfview with base filename:", pdfviewPath);
    filesToTry.push({
      path: pdfviewPath,
      description: "Server pdfview with base filename (no extension)",
      type: "server-pdfview",
    });

    // Also check with full filename in pdfview
    const fullPdfviewPath = path.join(serverPdfviewPath, filename);
    console.log("Checking server pdfview with full filename:", fullPdfviewPath);
    filesToTry.push({
      path: fullPdfviewPath,
      description: "Server pdfview with full filename",
      type: "server-pdfview",
    });

    // 1. Try in the frontend hotdir first (if available in development)
    if (frontendHotdirPath) {
      const hotdirPath = path.join(frontendHotdirPath, filename);
      console.log("Checking frontend hotdir:", hotdirPath);
      filesToTry.push({
        path: hotdirPath,
        description: "Frontend hotdir with filename only",
        type: "frontend",
      });
    }

    // 2. Try in server public hotdir (for Docker environment)
    const serverHotdirPath = path.join(serverPublicHotdirPath, filename);
    console.log("Checking server public hotdir:", serverHotdirPath);
    filesToTry.push({
      path: serverHotdirPath,
      description: "Server public hotdir with filename only",
      type: "server-public",
    });

    // 3. Try in storage hotdir (production Docker environment)
    const storageHotdirFile = path.join(storageHotdirPath, filename);
    console.log("Checking storage hotdir:", storageHotdirFile);
    filesToTry.push({
      path: storageHotdirFile,
      description: "Storage hotdir with filename only",
      type: "storage-hotdir",
    });

    // 4. Check collector patterns as additional fallback
    // Try various collector patterns that might be used in production
    const collectorPatterns = [
      filename,
      pdfPath,
      // Try with different extensions
      filename.replace(/\.pdf$/, ""),
      filename.replace(/\.json$/, ".pdf"),
    ];

    for (const pattern of collectorPatterns) {
      // Try in pdfview directory
      const collectorPdfviewPath = path.join(serverPdfviewPath, pattern);
      console.log("Checking collector pdfview pattern:", collectorPdfviewPath);
      filesToTry.push({
        path: collectorPdfviewPath,
        description: `Collector pdfview pattern (${pattern})`,
        type: "collector-pdfview",
      });

      // Try in storage hotdir with pattern
      const collectorStoragePath = path.join(storageHotdirPath, pattern);
      console.log("Checking collector storage pattern:", collectorStoragePath);
      filesToTry.push({
        path: collectorStoragePath,
        description: `Collector storage pattern (${pattern})`,
        type: "collector-storage",
      });
    }

    // Also try the full path in hotdir locations
    if (pdfPath !== filename) {
      // Frontend hotdir with full path (if available)
      if (frontendHotdirPath) {
        const fullHotdirPath = path.join(frontendHotdirPath, pdfPath);
        console.log("Checking frontend hotdir with full path:", fullHotdirPath);
        filesToTry.push({
          path: fullHotdirPath,
          description: "Frontend hotdir with full path",
          type: "frontend",
        });
      }

      // Server hotdir with full path
      const fullServerHotdirPath = path.join(serverPublicHotdirPath, pdfPath);
      console.log(
        "Checking server public hotdir with full path:",
        fullServerHotdirPath
      );
      filesToTry.push({
        path: fullServerHotdirPath,
        description: "Server public hotdir with full path",
        type: "server-public",
      });

      // Storage hotdir with full path
      const fullStorageHotdirPath = path.join(storageHotdirPath, pdfPath);
      console.log(
        "Checking storage hotdir with full path:",
        fullStorageHotdirPath
      );
      filesToTry.push({
        path: fullStorageHotdirPath,
        description: "Storage hotdir with full path",
        type: "storage-hotdir",
      });
    }

    // 5. Helper function to extract workspace information
    const extractWorkspaceFromPath = (docPath: string): string | null => {
      // Try several methods to identify the workspace

      // Method 1: Look for pattern /documents/WORKSPACE/...
      const documentsMatch = docPath.match(/documents\/([^/]+)/);
      if (documentsMatch && documentsMatch[1]) {
        return documentsMatch[1];
      }

      // Method 2: First segment if path contains slashes
      if (docPath.includes("/")) {
        const firstSegment = docPath.split("/")[0];
        if (firstSegment && firstSegment?.length > 0) {
          return firstSegment;
        }
      }

      return null;
    };

    // 6. Try direct path with workspace handling
    let workspace = extractWorkspaceFromPath(pdfPath);

    if (workspace) {
      console.log(`Identified workspace: ${workspace} from path: ${pdfPath}`);

      // Try with explicit workspace path: /documents/WORKSPACE/filename.pdf
      const workspaceDocPath = path.join(documentsPath, workspace, filename);
      filesToTry.push({
        path: workspaceDocPath,
        description: `Workspace root (${workspace}/${filename})`,
        type: "server",
      });

      // Try in custom-documents directory
      const customDocsPath = path.join(
        documentsPath,
        workspace,
        "custom-documents",
        filename
      );
      filesToTry.push({
        path: customDocsPath,
        description: `Workspace custom-documents (${workspace}/custom-documents/${filename})`,
        type: "server",
      });

      // Try to extract subdirectory structure
      if (pdfPath.includes("/")) {
        // Extract path after workspace
        const pathAfterWorkspace = pdfPath.substring(
          pdfPath.indexOf(workspace) + workspace.length
        );

        if (pathAfterWorkspace.includes("/")) {
          // Get everything between workspace/ and filename
          const subdirPath = pathAfterWorkspace.substring(
            1,
            pathAfterWorkspace.lastIndexOf("/")
          );

          if (subdirPath) {
            const fullSubdirPath = path.join(
              documentsPath,
              workspace,
              subdirPath,
              filename
            );
            filesToTry.push({
              path: fullSubdirPath,
              description: `Workspace subdirectory (${workspace}/${subdirPath}/${filename})`,
              type: "server",
            });
          }
        }
      }
    } else {
      console.log("Could not identify workspace from path:", pdfPath);

      // Try direct path as fallback
      filesToTry.push({
        path: path.join(documentsPath, pdfPath),
        description: "Direct path",
        type: "server",
      });

      // If we can't determine the workspace, try checking in each workspace directory
      try {
        const workspaces = fs
          .readdirSync(documentsPath)
          .filter((item) =>
            fs.statSync(path.join(documentsPath, item)).isDirectory()
          )
          .filter((dir) => !["attachments", "temp", "logs"].includes(dir));

        console.log(`Found ${workspaces.length} potential workspaces to check`);

        for (const ws of workspaces) {
          // Try in workspace root
          const wsPath = path.join(documentsPath, ws, filename);
          filesToTry.push({
            path: wsPath,
            description: `Potential workspace root (${ws}/${filename})`,
            type: "server",
          });

          // Try in custom-documents subdirectory
          const wsCustomPath = path.join(
            documentsPath,
            ws,
            "custom-documents",
            filename
          );
          filesToTry.push({
            path: wsCustomPath,
            description: `Potential workspace custom documents (${ws}/custom-documents/${filename})`,
            type: "server",
          });
        }
      } catch (err) {
        console.warn(
          "Error listing workspace directories:",
          (err as Error).message
        );
      }
    }

    // Log all paths we're going to check
    console.log(
      `Attempting to locate PDF with ${filesToTry.length} possible paths:`
    );
    filesToTry.forEach((item, index) => {
      console.log(`Path ${index + 1}: ${item.description} - ${item.path}`);
    });

    // Try each possible file path
    for (const { path: tryPath, description } of Array.from(
      new Set(filesToTry)
    )) {
      // Remove duplicates
      let candidatePath = tryPath;

      // Log for debugging
      console.log(`Checking ${description}:`, candidatePath);
      allCheckedPaths.push({ path: candidatePath, description });

      // Validate the path is within documents directory or allowed hotdir locations (security check)
      const isWithinDocs = isWithin(documentsPath, candidatePath);
      const isWithinFrontendHotdir =
        frontendHotdirPath && candidatePath.startsWith(frontendHotdirPath);
      const isWithinServerHotdir = candidatePath.startsWith(
        serverPublicHotdirPath
      );
      const isWithinStorageHotdir = candidatePath.startsWith(storageHotdirPath);
      const isWithinPdfview = candidatePath.startsWith(serverPdfviewPath);

      const isAllowedPath =
        isWithinDocs ||
        isWithinFrontendHotdir ||
        isWithinServerHotdir ||
        isWithinStorageHotdir ||
        isWithinPdfview;

      if (!isAllowedPath) {
        console.warn(
          "Rejected path outside allowed directories:",
          candidatePath,
          {
            isWithinDocs,
            isWithinFrontendHotdir,
            isWithinServerHotdir,
            isWithinStorageHotdir,
            isWithinPdfview,
            documentsPath,
            frontendHotdirPath: frontendHotdirPath || "N/A",
            serverPublicHotdirPath,
            storageHotdirPath,
            serverPdfviewPath,
          }
        );
        continue;
      }

      // If file doesn't exist, log to debug
      if (!fs.existsSync(candidatePath)) {
        console.log(`File not found: ${candidatePath}`);
        continue;
      }

      // Log file stats to debug
      const stats = fs.statSync(candidatePath);
      console.log(`File found: ${candidatePath}`, {
        size: stats.size,
        isFile: stats.isFile(),
        isDirectory: stats.isDirectory(),
        created: stats.birthtime,
        modified: stats.mtime,
        location: isWithinFrontendHotdir
          ? "frontend-hotdir"
          : isWithinServerHotdir
            ? "server-hotdir"
            : isWithinStorageHotdir
              ? "storage-hotdir"
              : "server-docs",
      });

      // Check the file extension and content before serving
      const fileExt = path.extname(candidatePath).toLowerCase();
      if (fileExt !== ".pdf") {
        console.warn(
          `File extension is not .pdf but ${fileExt}, checking content...`
        );

        // If extension doesn't match, check file content (first few bytes)
        try {
          const fd = fs.openSync(candidatePath, "r");
          const buffer = Buffer.alloc(5);
          fs.readSync(fd, buffer, 0, 5, 0);
          fs.closeSync(fd);

          // Check if it starts with %PDF-
          const isActuallyPdf = buffer.toString().startsWith("%PDF-");
          if (!isActuallyPdf) {
            console.warn(
              `File does not appear to be a PDF based on content check: ${buffer.toString()}`
            );
            continue; // Skip this file and try next one
          } else {
            console.log("File content indicates a valid PDF despite extension");
          }
        } catch (contentError) {
          console.error("Error checking file content type:", contentError);
          // Continue anyway since we found the file
        }
      }

      console.log("PDF file found, serving directly:", candidatePath);

      // Enable CORS for all origins
      res.header("Access-Control-Allow-Origin", "*");
      res.header("Access-Control-Allow-Methods", "GET, OPTIONS");
      res.header(
        "Access-Control-Allow-Headers",
        "Origin, X-Requested-With, Content-Type, Accept"
      );

      // If this is a preflight OPTIONS request, respond with 200 OK
      if (req.method === "OPTIONS") {
        res.status(200).send();
        return;
      }

      // Set correct content type for PDF
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        `inline; filename="${path.basename(candidatePath)}"`
      );

      // Send the file
      try {
        res.sendFile(candidatePath);
        return;
      } catch (sendError) {
        console.error("Error sending file:", sendError);
        const fileStream = fs.createReadStream(candidatePath);
        fileStream.pipe(res);
        return;
      }
    }

    // If we get here, we couldn't find the PDF file
    console.log("PDF file not found for any of the tried paths");
    console.log("Summary of search:");
    console.log(`- Original path: ${originalPath}`);
    console.log(`- Database document found: ${dbDocumentFound}`);
    console.log(`- Total paths checked: ${allCheckedPaths.length}`);
    console.log(
      "- All checked paths:",
      allCheckedPaths.map((p) => `${p.description}: ${p.path}`).join("\n  ")
    );

    res.status(404).json({
      error: "PDF file not found",
      originalPath: originalPath,
      checkedPaths: allCheckedPaths,
      summary: {
        totalPathsChecked: allCheckedPaths.length,
        dbDocumentFound: dbDocumentFound,
        searchedLocations: [
          "Frontend hotdir",
          "Server hotdir",
          "Storage hotdir",
          "Server pdfview directory",
          "Document storage directories",
          "Workspace-specific directories",
        ],
      },
    });
  };

  router.post(
    "/document/create-folder/:slug",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.admin, ROLES.manager, ROLES.superuser]),
    ],
    async (
      request: Request<{ slug: string }, unknown, CreateFolderBody>,
      response: Response
    ): Promise<void> => {
      try {
        const { slug } = request.params;
        const user = await userFromSession(request, response);
        const { name, isDocumentDrafting } = reqBody(request);
        const documentsPathUser = getUserDocumentPath(
          user,
          Boolean(isDocumentDrafting),
          slug as string
        );
        const storagePath = path.join(documentsPathUser, name as string);
        if (
          !isWithin(path.resolve(documentsPathUser), path.resolve(storagePath))
        )
          throw new Error("Invalid folder name.");

        if (fs.existsSync(storagePath)) {
          response.status(500).json({
            success: false,
            message: "Folder by that name already exists",
          });
          return;
        }

        fs.mkdirSync(storagePath, { recursive: true });
        response.status(200).json({ success: true, message: null });
      } catch (error: unknown) {
        console.error(error);
        response.status(500).json({
          success: false,
          message: `Failed to create folder: ${(error as Error).message} `,
        });
      }
    }
  );

  router.post(
    "/document/move-files/:slug",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.admin, ROLES.manager, ROLES.superuser]),
    ],
    async (
      request: Request<{ slug: string }, unknown, MoveFilesBody>,
      response: Response
    ): Promise<void> => {
      try {
        const { slug } = request.params;
        const { files, isDocumentDrafting } = reqBody(request);
        const fileOperations = files as MoveFileOperation[];

        console.log(
          `[moveDocumentFiles] Request to move ${fileOperations.length} files in workspace: ${slug}`
        );
        console.log(
          `[moveDocumentFiles] Files requested:`,
          fileOperations.map((f: MoveFileOperation) => ({
            from: f.from,
            to: f.to,
          }))
        );

        const docpaths = fileOperations.map(
          ({ from }: MoveFileOperation) => from
        );
        const documents: DatabaseDocument[] = [];
        for (const docpath of docpaths) {
          const docs = (await Document.where({
            docpath: docpath,
          })) as DatabaseDocument[];
          documents.push(...docs);
        }

        const embeddedFiles = documents.map(
          (doc: DatabaseDocument) => doc.docpath
        );
        const moveableFiles = fileOperations.filter(
          ({ from }: MoveFileOperation) => !embeddedFiles.includes(from)
        );

        const user = await userFromSession(request, response);
        const documentsPathUser = getUserDocumentPath(
          user,
          Boolean(isDocumentDrafting),
          slug as string
        );

        const movePromises = moveableFiles.map(
          async ({ from, to }: MoveFileOperation) => {
            // Extract just the filename from the path
            const filename = from.split("/").pop();
            if (!filename) {
              throw new Error(`Invalid file path: ${from}`);
            }

            // Try to find the actual current location of the file
            const actualPath = await findActualFilePath(
              documentsPathUser,
              "",
              filename
            );

            if (!actualPath) {
              console.error(
                `[moveDocumentFiles] File not found anywhere in workspace: ${filename}`
              );
              throw new Error(`File not found: ${filename}`);
            }

            // Use the actual path if different from requested path
            const sourcePath = path.join(documentsPathUser, actualPath);
            const destinationPath = path.join(
              documentsPathUser,
              normalizePath(to)
            );

            console.log(`[moveDocumentFiles] Moving file:
            Requested From: ${from}
            Actual From: ${actualPath}
            To: ${to}
            Source Path: ${sourcePath}
            Destination Path: ${destinationPath}`);

            return new Promise<void>((resolve, reject) => {
              if (
                !isWithin(documentsPath, sourcePath) ||
                !isWithin(documentsPath, destinationPath)
              )
                return reject("Invalid file location");

              // Check if source file exists (should always exist now)
              fs.access(sourcePath, fs.constants.F_OK, (accessErr) => {
                if (accessErr) {
                  console.error(
                    `[moveDocumentFiles] Source file does not exist: ${sourcePath}`
                  );
                  return reject(new Error(`Source file not found: ${from}`));
                }

                // Ensure destination directory exists
                const destinationDir = path.dirname(destinationPath);
                fs.mkdir(destinationDir, { recursive: true }, (mkdirErr) => {
                  if (mkdirErr) {
                    console.error(
                      `[moveDocumentFiles] Error creating directory ${destinationDir}:`,
                      mkdirErr
                    );
                    return reject(mkdirErr);
                  }

                  fs.rename(sourcePath, destinationPath, (err) => {
                    if (err) {
                      console.error(
                        `[moveDocumentFiles] Error moving file ${from} to ${to}:`,
                        err
                      );
                      reject(err);
                    } else {
                      console.log(
                        `[moveDocumentFiles] Successfully moved ${actualPath} to ${to}`
                      );
                      resolve();
                    }
                  });
                });
              });
            });
          }
        );

        Promise.all(movePromises)
          .then(() => {
            const unmovableCount = fileOperations.length - moveableFiles.length;
            if (unmovableCount > 0) {
              response.status(200).json({
                success: true,
                message: `${unmovableCount}/${fileOperations.length} files not moved. Unembed them from all workspaces.`,
              });
            } else {
              response.status(200).json({
                success: true,
                message: null,
              });
            }
          })
          .catch((err) => {
            console.error("Error moving files:", err);
            response
              .status(500)
              .json({ success: false, message: "Failed to move some files." });
          });
      } catch (error: unknown) {
        console.error(error);
        response
          .status(500)
          .json({ success: false, message: "Failed to move files." });
      }
    }
  );

  router.post(
    "/document/attachment-process",
    handleAttachmentUpload,
    async (
      request: FileUploadRequest,
      response: Response<AttachmentProcessResponse>
    ): Promise<void> => {
      try {
        const file = request.file;
        if (!file) {
          response.status(400).json({
            success: false,
            error: "No file provided",
          });
          return;
        }

        // Get skip embedding flag from headers
        const skipEmbedding = request.headers["x-skip-embedding"] === "true";
        console.log("Skip embedding flag:", skipEmbedding);

        // Validate file type
        const fileExt = path.extname(file.originalname).toLowerCase();
        const isPDF = file.mimetype === "application/pdf" || fileExt === ".pdf";

        if (isPDF && (!file.size || file.size === 0)) {
          response.status(400).json({
            success: false,
            error: "PDF file appears to be empty or corrupted",
          });
          return;
        }

        // Ensure storage directories exist
        const attachmentsPath = path.join(documentsPath, "attachments");
        if (!fs.existsSync(attachmentsPath)) {
          fs.mkdirSync(attachmentsPath, { recursive: true });
        }

        // Create a new instance of CollectorApi
        const collector = new CollectorApi();

        // Log file details for debugging
        console.log("[attachment-process] Processing file:", {
          originalname: file.originalname,
          path: file.path,
          size: file.size,
          mimetype: file.mimetype,
        });

        // Check if collector is online
        const isOnline = await collector.online();
        if (!isOnline) {
          console.error("[attachment-process] Collector service is not online");
          response.status(500).json({
            success: false,
            error: "Document processing service is not available",
          });
          return;
        }

        // Check if file exists at the path before processing
        console.log(
          "[attachment-process] Checking file existence at:",
          file.path
        );
        if (!fs.existsSync(file.path)) {
          console.error(
            "[attachment-process] File not found at path:",
            file.path
          );
          response.status(400).json({
            success: false,
            error: "Uploaded file not found",
          });
          return;
        }

        // Copy file to collector hotdir
        const collectorHotdirPath = path.resolve(
          process.cwd(),
          "../collector/hotdir"
        );

        // Ensure collector hotdir exists
        if (!fs.existsSync(collectorHotdirPath)) {
          fs.mkdirSync(collectorHotdirPath, { recursive: true });
        }

        // Generate unique filename to avoid conflicts
        const uniqueId = uuidv4();
        const fileExtension = path.extname(file.originalname);
        const baseFileName = path.basename(file.originalname, fileExtension);
        const uniqueFileName = `${baseFileName}-${uniqueId}${fileExtension}`;
        const collectorFilePath = path.join(
          collectorHotdirPath,
          uniqueFileName
        );

        console.log(
          "[attachment-process] Copying file to collector hotdir:",
          collectorFilePath
        );

        try {
          // Copy the uploaded file to collector's hotdir
          fs.copyFileSync(file.path, collectorFilePath);
          console.log(
            "[attachment-process] File copied to collector hotdir successfully"
          );
        } catch (copyError) {
          console.error(
            "[attachment-process] Error copying file to collector hotdir:",
            copyError
          );
          response.status(500).json({
            success: false,
            error: "Failed to prepare file for processing",
          });
          return;
        }

        // Process the file using collector's processDocument method with the unique filename
        const result = await collector.processDocument(
          uniqueFileName,
          "attachments"
        );

        // Clean up the file from collector hotdir after processing
        try {
          if (fs.existsSync(collectorFilePath)) {
            fs.unlinkSync(collectorFilePath);
            console.log(
              "[attachment-process] Cleaned up file from collector hotdir"
            );
          }
        } catch (cleanupError) {
          console.error(
            "[attachment-process] Error cleaning up collector hotdir file:",
            cleanupError
          );
          // Continue processing as this is not critical
        }

        if (result === false || !result) {
          console.error("[attachment-process] No result from collector");
          response.status(400).json({
            success: false,
            error: "Failed to process attachment",
          });
          return;
        }

        console.log("[attachment-process] Collector result:", {
          success: result.success,
          reason: result.reason,
          documents: result.documents ? result.documents.length : 0,
        });

        // Note: The collector handles file cleanup after processing

        if (!result.success) {
          console.error(
            "[attachment-process] Processing failed:",
            result.reason
          );
          response.status(400).json({
            success: false,
            error: result.reason || "Failed to process attachment",
          });
          return;
        }

        // Log the documents array to debug
        console.log(
          "[attachment-process] Documents from collector:",
          result.documents
        );

        if (!result.documents || result.documents.length === 0) {
          console.error(
            "[attachment-process] No documents returned from collector"
          );
          response.status(400).json({
            success: false,
            error: "No documents processed",
          });
          return;
        }

        // Ensure the document location is relative to documentsPath
        const collectorDocument = result.documents[0];
        const location = collectorDocument.metadata?.location as string;
        if (!location) {
          console.error(
            "[attachment-process] Document has no location:",
            collectorDocument
          );
          response.status(400).json({
            success: false,
            error: "Processed document has no location",
          });
          return;
        }

        const relativePath = location.replace(documentsPath + path.sep, "");
        const documentPath = path.join(documentsPath, relativePath);

        // Validate the path is within documentsPath
        if (!isWithin(documentsPath, documentPath)) {
          console.error("Invalid document path:", {
            documentsPath,
            location,
            documentPath,
          });
          response.status(400).json({
            success: false,
            error: "Invalid document location",
          });
          return;
        }

        // Read the processed document content
        let documentContent: string;
        try {
          documentContent = await fsPromises.readFile(documentPath, "utf8");
        } catch {
          response.status(400).json({
            success: false,
            error: "Processed document not found",
          });
          return;
        }
        let document: DocumentJSON;
        try {
          document = JSON.parse(documentContent) as DocumentJSON;
        } catch (error) {
          console.error("Error parsing document content:", error);
          response.status(400).json({
            success: false,
            error: "Failed to parse processed document",
          });
          return;
        }

        // For PDFs, ensure we have valid content
        if (
          isPDF &&
          (!document.pageContent || document.pageContent.length < 10)
        ) {
          response.status(400).json({
            success: false,
            error:
              "Failed to extract content from PDF. The file may be password protected or corrupted.",
          });
          return;
        }

        // Clean up the temporary uploaded file
        try {
          if (file.path && fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
            console.log(
              "[attachment-process] Cleaned up temporary uploaded file"
            );
          }
        } catch (cleanupError) {
          console.error(
            "[attachment-process] Error cleaning up temporary file:",
            cleanupError
          );
          // Continue as this is not critical
        }

        response.status(200).json({
          success: true,
          document: {
            id: document.id,
            location: location,
            content: document.pageContent || document.content || "",
            originalFilename: file.originalname,
          },
        });
      } catch (error: unknown) {
        // Clean up the file in case of any error
        if (request.file && fs.existsSync(request.file.path)) {
          fs.unlinkSync(request.file.path);
        }
        console.error(
          "[attachment-process] Error processing attachment:",
          error
        );
        console.error("[attachment-process] Error details:", {
          message: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
        });
        response.status(500).json({
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Internal server error processing attachment",
        });
      }
    }
  );

  router.post(
    "/document/attachment-cleanup",
    async (
      request: Request<{}, {}, AttachmentCleanupBody>,
      response: Response
    ): Promise<void> => {
      try {
        const { location } = reqBody(request) as AttachmentCleanupBody;
        if (!location) {
          response.status(400).json({
            success: false,
            error: "No location provided",
          });
          return;
        }

        // Define paths - note that __dirname is in server/endpoints/api/document
        const frontendHotdir = path.resolve(
          __dirname,
          "../../../frontend/public/hotdir"
        );
        const serverDocsPath = path.join(documentsPath, "attachments");

        // Remove attachments/ prefix but keep the full filename for now
        const cleanLocation = location.replace(/^attachments\//, "");

        // Get the base filename without .json if it exists
        const baseFilename = cleanLocation.replace(/\.json$/, "");

        // Define all possible filenames
        const possibleFiles = [
          baseFilename, // e.g. doc.pdf
          baseFilename + ".json", // e.g. doc.pdf.json
          cleanLocation, // Original filename as-is
        ];

        let deleted = false;
        for (const filename of possibleFiles) {
          const frontendPath = path.resolve(frontendHotdir, filename);
          const serverPath = path.resolve(serverDocsPath, filename);

          // Check and delete from frontend hotdir
          try {
            await fsPromises.unlink(frontendPath);
            deleted = true;
          } catch {
            // File doesn't exist, ignore
          }

          // Check and delete from server docs
          try {
            await fsPromises.unlink(serverPath);
            deleted = true;
          } catch {
            // File doesn't exist, ignore
          }
        }

        if (!deleted) {
          console.warn("No files found to cleanup:", {
            location,
            possibleFiles,
            frontendHotdir,
            serverDocsPath,
          });
        }

        response.status(200).json({ success: true });
      } catch (error: unknown) {
        console.error("Error cleaning up attachment:", error);
        response.status(500).json({
          success: false,
          error: (error as Error).message,
        });
      }
    }
  );

  router.post(
    "/document/rename-folder/:slug",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.admin, ROLES.manager, ROLES.superuser]),
    ],
    async (
      request: Request<{ slug: string }, unknown, RenameFileBody>,
      response: Response
    ): Promise<void> => {
      try {
        const { slug } = request.params;
        const { oldName, newName, isDocumentDrafting } = reqBody(request);
        const user = await userFromSession(request, response);
        const documentsPathUser = getUserDocumentPath(
          user,
          Boolean(isDocumentDrafting),
          slug as string
        );
        const oldFolderPath = path.join(documentsPathUser, oldName as string);
        const newFolderPath = path.join(documentsPathUser, newName as string);

        if (!fs.existsSync(oldFolderPath)) {
          response.status(404).json({
            success: false,
            message: "Folder not found",
          });
          return;
        }

        if (fs.existsSync(newFolderPath)) {
          response.status(400).json({
            success: false,
            message: "A folder with the new name already exists",
          });
          return;
        }
        await Document.updateDocpathAndMetadata(oldFolderPath, newFolderPath);
        fs.renameSync(oldFolderPath, newFolderPath);
        response.status(200).json({ success: true, message: null });
        return;
      } catch (error: unknown) {
        console.error(error);
        response.status(500).json({
          success: false,
          message: `Failed to rename folder: ${(error as Error).message}`,
        });
        return;
      }
    }
  );

  router.get(
    "/document/contents",
    [
      validatedRequest,
      flexUserRoleValid([
        ROLES.default,
        ROLES.manager,
        ROLES.admin,
        ROLES.superuser,
      ]),
    ],
    async (
      req: Request<
        Record<string, never>,
        DocumentContentsResponse,
        Record<string, never>,
        { path?: string }
      >,
      res: Response<DocumentContentsResponse>
    ): Promise<void> => {
      let { path: docPath } = req.query;
      console.log("Document contents request for path:", docPath);

      // Get the content blob
      try {
        if (!docPath) {
          console.log("Missing path parameter");
          res.status(400).json({ error: "path parameter is required" });
          return;
        }

        if (docPath.startsWith("/hotdir/")) {
          docPath = docPath.substring("/hotdir/".length);
          console.log("Adjusted path from hotdir:", docPath);
        }

        console.log(
          "Looking up document with filename ending with:",
          docPath + ".json"
        );
        const document = await Document.contents(docPath);
        if (!document) {
          console.log("Document not found for path:", docPath);
          res.status(404).json({ error: "Document not found" });
          return;
        }

        console.log("Document found, returning content for path:", docPath);

        // Check if path ends with .pdf and this is a direct file request
        if (docPath.toLowerCase().endsWith(".pdf")) {
          console.log(
            "PDF file requested, checking if we should return raw file"
          );
          const filePath = path.join(documentsPath, docPath);

          // Check if this file exists and if so, serve the actual PDF instead of JSON
          if (fs.existsSync(filePath)) {
            console.log("Serving raw PDF file from:", filePath);
            res.sendFile(filePath);
            return;
          }
        }

        res.json({
          content: document.content,
          path: docPath,
        });
      } catch (error: unknown) {
        console.error("Error reading document contents:", error);
        res.status(500).json({ error: "Error reading document contents" });
      }
    }
  );

  router.get(
    "/document/pdf-contents",
    [
      validatedRequest,
      flexUserRoleValid([
        ROLES.default,
        ROLES.manager,
        ROLES.admin,
        ROLES.superuser,
      ]),
    ],
    async (
      req: Request<
        Record<string, never>,
        unknown,
        Record<string, never>,
        { path?: string }
      >,
      res: Response
    ): Promise<void> => {
      try {
        let { path: requestPath } = req.query;
        console.log("PDF document request for path:", requestPath);
        console.log("Request headers:", req.headers);

        if (!requestPath) {
          console.log("Missing path parameter");
          res.status(400).json({ error: "path parameter is required" });
          return;
        }

        // Clean up the path if it includes problematic prefixes
        if (requestPath.startsWith("/hotdir/")) {
          requestPath = requestPath.substring("/hotdir/".length);
          console.log("Cleaned path from /hotdir/ prefix:", requestPath);
        }

        // Also clean up document prefix
        if (requestPath.startsWith("/document/")) {
          requestPath = requestPath.substring("/document/".length);
          console.log("Cleaned path from /document/ prefix:", requestPath);
        }

        return findAndServePdfFile(req, res, requestPath);
      } catch (error: unknown) {
        console.error("Error serving PDF document:", error);
        res.status(500).json({ error: "Error serving PDF document" });
      }
    }
  );

  // Simple endpoint for PDF files to avoid query parameter encoding issues
  router.get(
    "/document/pdf/:filename",
    [
      validatedRequest,
      flexUserRoleValid([
        ROLES.default,
        ROLES.manager,
        ROLES.admin,
        ROLES.superuser,
      ]),
    ],
    async (
      req: Request<{ filename: string }>,
      res: Response
    ): Promise<void> => {
      try {
        const { filename } = req.params;
        console.log("PDF direct document request for filename:", filename);

        if (!filename) {
          console.log("Missing filename parameter");
          res.status(400).json({ error: "filename parameter is required" });
          return;
        }

        await findAndServePdfFile(req, res, filename);
      } catch (error: unknown) {
        console.error("Error serving PDF document:", error);
        res.status(500).json({ error: "Internal server error" });
      }
    }
  );

  // Direct PDF contents endpoint to avoid query parameter encoding issues
  router.get(
    "/document/pdf-contents/:filename",
    [
      validatedRequest,
      flexUserRoleValid([
        ROLES.default,
        ROLES.manager,
        ROLES.admin,
        ROLES.superuser,
      ]),
    ],
    async (
      req: Request<{ filename: string }>,
      res: Response
    ): Promise<void> => {
      try {
        const { filename } = req.params;
        console.log("PDF contents request with direct filename:", filename);

        if (!filename) {
          console.log("Missing filename parameter");
          res.status(400).json({ error: "filename parameter is required" });
          return;
        }

        await findAndServePdfFile(req, res, filename);
      } catch (error: unknown) {
        console.error("Error serving PDF file:", error);
        res.status(500).json({ error: "Error serving PDF file" });
      }
    }
  );

  // Endpoint to resolve PDF paths
  router.get(
    "/document/resolve-pdf-path",
    [
      validatedRequest,
      flexUserRoleValid([
        ROLES.default,
        ROLES.manager,
        ROLES.admin,
        ROLES.superuser,
      ]),
    ],
    async (
      req: Request<
        Record<string, never>,
        ResolvePathResponse,
        Record<string, never>,
        { path?: string }
      >,
      res: Response<ResolvePathResponse>
    ): Promise<void> => {
      const { path: rawDocumentPath } = req.query;
      console.log("Resolving PDF path for (raw):", rawDocumentPath);

      if (!rawDocumentPath) {
        res.status(400).json({
          success: false,
          error: "Missing path parameter",
          checkedPaths: [],
        });
        return;
      }

      // Decode URL-encoded path characters
      const documentPath = decodeURIComponent(rawDocumentPath);
      console.log("Resolving PDF path for (decoded):", documentPath);

      // Extract filename from path
      const filename = path.basename(documentPath);
      console.log("Extracted filename:", filename);

      const pathsToCheck: Array<{
        path: string;
        description: string;
        relativePath: string;
      }> = [];

      // Get the first segment as the workspace
      let workspace: string | null = null;
      if (documentPath.includes("/")) {
        workspace = documentPath.split("/")[0];
        console.log("Extracted workspace from first segment:", workspace);
      }

      if (workspace) {
        // Log to verify the extracted workspace
        console.log(`Working with workspace: ${workspace}`);

        // 1. Original path with full structure
        const originalPath = path.join(documentsPath, documentPath);
        pathsToCheck.push({
          path: originalPath,
          description: `Full original path (${documentPath})`,
          relativePath: documentPath,
        });

        // 2. Check in workspace root
        const workspaceRootPath = path.join(documentsPath, workspace, filename);
        pathsToCheck.push({
          path: workspaceRootPath,
          description: `Workspace root (${workspace}/${filename})`,
          relativePath: `${workspace}/${filename}`,
        });

        // 3. Check in custom-documents
        const customDocsPath = path.join(
          documentsPath,
          workspace,
          "custom-documents",
          filename
        );
        pathsToCheck.push({
          path: customDocsPath,
          description: `Workspace custom-documents (${workspace}/custom-documents/${filename})`,
          relativePath: `${workspace}/custom-documents/${filename}`,
        });

        // 4. Extract subdirectory if present
        const pathParts = documentPath.split("/");
        if (pathParts.length > 2) {
          // We have subdirectories between workspace and filename
          const subDirParts = pathParts.slice(1, -1); // Skip workspace and filename
          const subDir = subDirParts.join("/");

          if (subDir) {
            console.log(`Found subdirectory: ${subDir}`);

            const subDirPath = path.join(
              documentsPath,
              workspace,
              subDir,
              filename
            );
            pathsToCheck.push({
              path: subDirPath,
              description: `Subdirectory path (${workspace}/${subDir}/${filename})`,
              relativePath: `${workspace}/${subDir}/${filename}`,
            });
          }
        }
      } else {
        // If we can't identify the workspace, use the full path
        console.log("Could not identify workspace from path");

        if (!documentPath) {
          res.status(400).json({
            success: false,
            error: "Invalid document path",
            checkedPaths: [],
          });
          return;
        }

        const directPath = path.join(documentsPath, documentPath);
        pathsToCheck.push({
          path: directPath,
          description: "Direct path (no workspace identified)",
          relativePath: documentPath,
        });

        // Also try checking each workspace directory
        try {
          const potentialWorkspaces = fs
            .readdirSync(documentsPath)
            .filter((item) => {
              try {
                return fs
                  .statSync(path.join(documentsPath, item))
                  .isDirectory();
              } catch {
                return false;
              }
            })
            .filter((dir) => !["attachments", "temp", "logs"].includes(dir));

          console.log(
            `Checking ${potentialWorkspaces.length} potential workspaces`
          );

          for (const ws of potentialWorkspaces) {
            // Check workspace root
            const wsPath = path.join(documentsPath, ws, filename);
            pathsToCheck.push({
              path: wsPath,
              description: `Potential workspace root (${ws}/${filename})`,
              relativePath: `${ws}/${filename}`,
            });

            // Check custom-documents
            const wsCustomPath = path.join(
              documentsPath,
              ws,
              "custom-documents",
              filename
            );
            pathsToCheck.push({
              path: wsCustomPath,
              description: `Potential workspace custom-documents (${ws}/custom-documents/${filename})`,
              relativePath: `${ws}/custom-documents/${filename}`,
            });
          }
        } catch (err) {
          console.warn(
            "Error scanning workspace directories:",
            (err as Error).message
          );
        }
      }

      console.log(`Checking ${pathsToCheck.length} paths for PDF`);

      // Create arrays to track the paths we've checked
      const checkedPaths: Array<{ path: string; description: string }> = [];
      let resolvedPdfPath: string | null = null;
      let resolvedServerPath: string | null = null;
      let resolvedFrontendPath: string | null = null;
      let successfulRelativePath: string | null = null; // Variable to store the correct relativePath

      // Check each path for the PDF file
      for (const { path: pdfPath, description, relativePath } of pathsToCheck) {
        console.log(`Checking ${description}:`, pdfPath);
        checkedPaths.push({ path: pdfPath, description });

        // Make sure the path is within our documents directory for security
        if (!isWithin(documentsPath, pdfPath)) {
          console.warn("Path is outside documents directory:", pdfPath);
          continue;
        }

        // Check if the file exists
        if (fs.existsSync(pdfPath)) {
          console.log("PDF file found at:", pdfPath);

          // Check if it's actually a PDF
          try {
            const fd = fs.openSync(pdfPath, "r");
            const buffer = Buffer.alloc(5);
            fs.readSync(fd, buffer, 0, 5, 0);
            fs.closeSync(fd);

            if (!buffer.toString().startsWith("%PDF-")) {
              console.warn("File exists but is not a PDF:", pdfPath);
              continue;
            }
          } catch (err) {
            console.warn(
              "Error checking if file is PDF:",
              (err as Error).message
            );
            continue;
          }

          // Save the resolved paths
          resolvedPdfPath = pdfPath;
          resolvedServerPath = pdfPath;
          successfulRelativePath = relativePath; // Capture the relativePath from the successful check

          // For frontend path, try to determine the correct URL
          // The frontend path should be /document/PDF_FILENAME or similar service path
          resolvedFrontendPath = `/document/${successfulRelativePath}`;

          break;
        } else {
          console.log("PDF file not found at:", pdfPath);
        }
      }

      // If we found a PDF, return success response
      if (resolvedPdfPath) {
        if (!successfulRelativePath) {
          // This case should ideally not be hit if resolvedPdfPath is set,
          // but as a fallback, try to derive it if somehow missed.
          console.warn(
            "successfulRelativePath was not set, attempting to derive from resolvedServerPath and documentsPath."
          );
          if (
            resolvedServerPath &&
            documentsPath &&
            resolvedServerPath.startsWith(documentsPath)
          ) {
            successfulRelativePath = resolvedServerPath
              .substring(documentsPath.length)
              .replace(/^\\|^\//, "");
          } else {
            // Ultimate fallback: use basename. This might be incorrect for nested structures.
            console.error(
              "Could not derive successfulRelativePath. Using basename as a potentially incorrect fallback."
            );
            successfulRelativePath = path.basename(
              resolvedServerPath || "unknown.pdf"
            );
          }
        }

        res.json({
          success: true,
          serverPath: successfulRelativePath, // Use the captured/derived relative path
          frontendPath: resolvedFrontendPath!, // This uses successfulRelativePath prefixed with /document/
          checkedPaths: checkedPaths,
        });
        return;
      } else {
        console.log("PDF not found in any expected location");
        res.status(404).json({
          success: false,
          error: "PDF not found in any expected location",
          checkedPaths: checkedPaths,
        });
        return;
      }
    }
  );
}

export { documentEndpoints, getUserDocumentPathName };
