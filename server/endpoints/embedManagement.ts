import type { Express, Request, Response, Router } from "express";
import { EmbedChats } from "../models/embedChats";
import {
  EmbedConfig,
  EmbedConfigCreateData,
  EmbedConfigUpdateData,
} from "../models/embedConfig";
import { EventLogs } from "../models/eventLogs";
import { reqBody, userFromSession } from "../utils/http";
import { validEmbedConfigId } from "../utils/middleware/embedMiddleware";
import {
  flexUserRoleValid,
  ROLES,
} from "../utils/middleware/multiUserProtected";
import { validatedRequest } from "../utils/middleware/validatedRequest";

// Helper for typing locals user
interface LocalsUser {
  id: number;
  role: string;
}

interface LocalsWithUser {
  user?: LocalsUser;
}

const getUser = (res: Response): LocalsUser | undefined => {
  const locals = res.locals as LocalsWithUser;
  return locals.user;
};

export function embedManagementEndpoints(
  app: Express,
  apiRouter?: Router
): void {
  if (!app) return;

  const router = apiRouter || app;

  // Get all embeds
  router.get(
    "/embeds",
    [validatedRequest, flexUserRoleValid([ROLES.admin])],
    async (_: Request, res: Response) => {
      try {
        const embeds = await EmbedConfig.whereWithWorkspace({}, null, {
          createdAt: "desc",
        });
        res.status(200).json({ embeds });
      } catch (e) {
        console.error(e);
        res.sendStatus(500).end();
      }
    }
  );

  // Get single embed by ID
  router.get(
    "/embed/:embedId",
    [validatedRequest, flexUserRoleValid([ROLES.admin]), validEmbedConfigId],
    async (req: Request, res: Response) => {
      try {
        const { embedId } = req.params;
        const embed = await EmbedConfig.get({ id: Number(embedId) });

        if (!embed) {
          res.status(404).json({
            success: false,
            error: "Embed configuration not found",
          });
          return;
        }

        res.status(200).json({
          success: true,
          embed,
        });
      } catch (e) {
        console.error(e);
        res.status(500).json({
          success: false,
          error: "Internal server error",
        });
      }
    }
  );

  // Create new embed
  router.post(
    "/embeds/new",
    [validatedRequest, flexUserRoleValid([ROLES.admin])],
    async (req: Request, res: Response) => {
      try {
        const user = await userFromSession(req, res);
        const data = reqBody(req) as EmbedConfigCreateData;
        const { embed, message: error } = await EmbedConfig.new(data, user?.id);
        if (embed) {
          await EventLogs.logEvent(
            "embed_created",
            { embedId: embed.id },
            user?.id
          );
        }
        res.status(200).json({ embed, error });
      } catch (e) {
        console.error(e);
        res.sendStatus(500).end();
      }
    }
  );

  // Update embed configuration
  router.post(
    "/embed/update/:embedId",
    [validatedRequest, flexUserRoleValid([ROLES.admin]), validEmbedConfigId],
    async (req: Request, res: Response) => {
      try {
        const user = await userFromSession(req, res);
        const { embedId } = req.params;
        const updates = reqBody(req) as EmbedConfigUpdateData;
        const { success, error } = await EmbedConfig.update(
          Number(embedId),
          updates
        );
        await EventLogs.logEvent("embed_updated", { embedId }, user?.id);
        res.status(200).json({ success, error });
      } catch (e) {
        console.error(e);
        res.sendStatus(500).end();
      }
    }
  );

  // Delete embed configuration
  router.delete(
    "/embed/:embedId",
    [validatedRequest, flexUserRoleValid([ROLES.admin]), validEmbedConfigId],
    async (req: Request, res: Response) => {
      try {
        const { embedId } = req.params;
        await EmbedConfig.delete({ id: Number(embedId) });
        await EventLogs.logEvent(
          "embed_deleted",
          { embedId },
          getUser(res)?.id
        );
        res.status(200).json({ success: true, error: null });
      } catch (e) {
        console.error(e);
        res.sendStatus(500).end();
      }
    }
  );

  // List embed chats
  router.post(
    "/embed/chats",
    [validatedRequest, flexUserRoleValid([ROLES.admin])],
    async (req: Request, res: Response) => {
      try {
        const { offset = 0, limit = 20 } = reqBody(req) as {
          offset?: number;
          limit?: number;
        };
        const embedChats = await EmbedChats.whereWithEmbedAndWorkspace(
          {},
          limit,
          { id: "desc" },
          offset * limit
        );
        const totalChats = await EmbedChats.count();
        const hasPages = totalChats > (offset + 1) * limit;
        res.status(200).json({ chats: embedChats, hasPages, totalChats });
      } catch (e) {
        console.error(e);
        res.sendStatus(500).end();
      }
    }
  );

  // Delete an embed chat
  router.delete(
    "/embed/chats/:chatId",
    [validatedRequest, flexUserRoleValid([ROLES.admin])],
    async (req: Request, res: Response) => {
      try {
        const { chatId } = req.params;
        await EmbedChats.delete({ id: Number(chatId) });
        res.status(200).json({ success: true, error: null });
      } catch (e) {
        console.error(e);
        res.sendStatus(500).end();
      }
    }
  );
}
