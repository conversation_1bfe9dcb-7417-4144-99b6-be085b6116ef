import { Request, Response, Router } from "express";
import { DocumentSyncQueue } from "../../models/documentSyncQueue";
import { Document } from "../../models/documents";
import { EventLogs } from "../../models/eventLogs";
import SystemSettings from "../../models/systemSettings";
import { Telemetry } from "../../models/telemetry";
import { reqBody } from "../../utils/http";
import { featureFlagEnabled } from "../../utils/middleware/featureFlagEnabled";
import {
  flexUserRoleValid,
  ROLES,
} from "../../utils/middleware/multiUserProtected";
import { validWorkspaceSlug } from "../../utils/middleware/validWorkspace";
import { validatedRequest } from "../../utils/middleware/validatedRequest";

interface ToggleLiveSyncRequest {
  updatedStatus?: boolean | string;
}

interface UpdateWatchStatusRequest {
  docPath: string;
  watchStatus?: boolean;
}

function liveSyncEndpoints(app: Router): void {
  if (!app) return;

  app.post(
    "/experimental/toggle-live-sync",
    [validatedRequest, flexUserRoleValid([ROLES.admin])],
    async (request: Request, response: Response): Promise<void> => {
      try {
        const { updatedStatus = false } = reqBody(
          request
        ) as ToggleLiveSyncRequest;
        const newStatus =
          SystemSettings.validations.experimental_live_file_sync(updatedStatus);
        const currentStatus =
          (await SystemSettings.get({ label: "experimental_live_file_sync" }))
            ?.value || "disabled";

        if (currentStatus === newStatus) {
          response
            .status(200)
            .json({ liveSyncEnabled: newStatus === "enabled" });
          return;
        }

        // Already validated earlier - so can hot update.
        await SystemSettings._updateSettings({
          experimental_live_file_sync: newStatus,
        });

        if (newStatus === "enabled") {
          await Telemetry.sendTelemetry("experimental_feature_enabled", {
            feature: "live_file_sync",
          });
          await EventLogs.logEvent("experimental_feature_enabled", {
            feature: "live_file_sync",
          });
          DocumentSyncQueue.bootWorkers();
        } else {
          DocumentSyncQueue.killWorkers();
        }

        response.status(200).json({ liveSyncEnabled: newStatus === "enabled" });
      } catch (e) {
        console.error(e);
        response.status(500).end();
      }
    }
  );

  app.get(
    "/experimental/live-sync/queues",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.admin]),
      featureFlagEnabled(DocumentSyncQueue.featureKey),
    ],
    async (_: Request, response: Response) => {
      const queues = await DocumentSyncQueue.where(
        {},
        null,
        { createdAt: "asc" },
        {
          workspace_documents: {
            include: {
              workspaces: true,
            },
          },
        }
      );
      response.status(200).json({ queues });
    }
  );

  // Should be in workspace routes, but is here for now.
  app.post(
    "/workspace/:slug/update-watch-status",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.admin, ROLES.manager]),
      validWorkspaceSlug,
      featureFlagEnabled(DocumentSyncQueue.featureKey),
    ],
    async (request: Request, response: Response): Promise<void> => {
      try {
        const { docPath, watchStatus = false } = reqBody(
          request
        ) as UpdateWatchStatusRequest;
        const workspace = response.locals.workspace;

        const document = await Document.get({
          workspaceId: workspace.id,
          docpath: docPath,
        });

        if (!document) {
          response.sendStatus(404).end();
          return;
        }

        await DocumentSyncQueue.toggleWatchStatus(document, watchStatus);
        response.status(200).end();
      } catch (error) {
        console.error("Error processing the watch status update:", error);
        response.status(500).end();
      }
    }
  );
}

export { liveSyncEndpoints };
