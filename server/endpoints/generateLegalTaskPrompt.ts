import { Request, Response, Router } from "express";
import { getLL<PERSON>rovider } from "../utils/helpers";
import { validatedRequest } from "../utils/middleware/validatedRequest";
import {
  flexUserRoleValid,
  ROLES,
} from "../utils/middleware/multiUserProtected";
import { reqBody } from "../utils/http";
import { Workspace } from "../models/workspace";
import { getPromptUpgradeLLM } from "../utils/helpers/supportFunctions";

// Legal task prompt interfaces
type LegalTaskType = "mainDoc" | "noMainDoc" | "referenceFiles" | string;

interface GenerateLegalTaskPromptRequestBody {
  taskDescription?: string;
  promptTemplate?: string;
  workspaceId?: string;
  useWorkspaceLLM?: boolean;
  legalTaskType?: LegalTaskType;
}

interface GenerateLegalTaskPromptResponse {
  success: boolean;
  prompt?: string;
  error?: string;
}

/**
 * Generate a legal task prompt.
 * @param taskDescription - The description of the legal task.
 * @param llmProvider - Optional LLM provider instance to use. If not provided, env vars are used.
 * @param legalTaskType - The type of legal task.
 * @returns The generated legal task prompt.
 */
async function generateLegalTaskPrompt(
  taskDescription: string,
  llmProviderToUse: ReturnType<typeof getLLMProvider> | null = null,
  legalTaskType: LegalTaskType = "noMainDoc"
): Promise<string> {
  if (!llmProviderToUse) {
    // Try LLM_PROVIDER_PU first (existing logic)
    let providerToTry = process.env.LLM_PROVIDER_PU;

    // Handle "system-standard" case - resolve to actual base LLM provider
    if (providerToTry === "system-standard") {
      console.log(
        "GenerateLegalTaskPrompt using system-standard, resolving to default LLM."
      );
      const actualProvider = process.env.LLM_PROVIDER ?? "openai";
      llmProviderToUse = getLLMProvider({
        provider: actualProvider,
      } as Parameters<typeof getLLMProvider>[0]);
    } else if (providerToTry) {
      llmProviderToUse = getLLMProvider({
        provider: providerToTry,
        settings_suffix: "_PU",
      });
      if (!llmProviderToUse) {
        console.warn(
          `Failed to initialize LLM_PROVIDER_PU ('${providerToTry}'). Attempting fallback.`
        );
      }
    }

    // If PU provider failed or wasn't set, try the default provider
    if (!llmProviderToUse) {
      const puEnvVar = process.env.LLM_PROVIDER_PU; // Get it again for the warning
      if (!puEnvVar) {
        // PU was not set at all
        console.warn(
          "LLM_PROVIDER_PU not set. Falling back to LLM_PROVIDER for prompt upgrade."
        );
      }

      providerToTry = process.env.LLM_PROVIDER;

      // Handle "system-standard" case for fallback as well
      if (providerToTry === "system-standard") {
        console.log(
          "GenerateLegalTaskPrompt fallback using system-standard, resolving to default LLM."
        );
        const actualProvider = process.env.LLM_PROVIDER ?? "openai";
        llmProviderToUse = getLLMProvider({
          provider: actualProvider,
        });
      } else if (providerToTry) {
        llmProviderToUse = getLLMProvider({
          provider: providerToTry,
        } as Parameters<typeof getLLMProvider>[0]);
        if (!llmProviderToUse) {
          console.warn(
            `Failed to initialize LLM_PROVIDER ('${providerToTry}') as fallback.`
          );
        }
      }
    }

    // Now check if support function is enabled and use support LLM if available
    llmProviderToUse = await getPromptUpgradeLLM(llmProviderToUse);
  }

  if (!llmProviderToUse) {
    const providerIdentifier = `Configured prompt upgrade LLMs (Support: ${process.env.LLM_PROVIDER_SUPPORT || "not set"}, PU: ${process.env.LLM_PROVIDER_PU || "not set"}, Default: ${process.env.LLM_PROVIDER || "not set"})`;
    console.error(
      `Generate Legal Task Prompt: Could not initialize LLM provider. Identifier: ${providerIdentifier}`
    );
    throw new Error(
      `Could not initialize LLM provider from ${providerIdentifier}`
    );
  }

  if (llmProviderToUse) {
    if (!llmProviderToUse.isValidChatCompletionModel(llmProviderToUse.model)) {
      throw new Error(
        `${llmProviderToUse.model} is not valid for chat completion with ${llmProviderToUse.constructor.name}!`
      );
    }

    // Determine system message based on the chosen legalTaskType
    let systemMessage: string;
    if (legalTaskType === "mainDoc") {
      systemMessage =
        "You are a legal expert specializing in document-centric analysis and drafting. Your task is to create optimized prompts for the Main Document Flow, which centers all analysis around a primary document. Generate clear, specific prompts that:\n\n" +
        "1. Direct the system to thoroughly analyze the main document's structure, content, and legal context\n" +
        "2. Instruct how supporting documents should supplement (not replace) the main document's analysis\n" +
        "3. Specify how sections should be derived from and anchored to the main document\n" +
        "4. Ensure the final output maintains focus on the main document while incorporating relevant supporting material\n\n" +
        "The resulting prompt should be comprehensive yet focused, enabling the 9-stage pipeline to produce a document that truly centers on the main document. IMPORTANT: Detect the language of the user task description and generate the prompt in the same detected language.";
    } else if (legalTaskType === "noMainDoc") {
      systemMessage =
        "You are a legal expert specializing in multi-source document synthesis. Your task is to create optimized prompts for the No Main Document Flow, which synthesizes information from multiple sources without a central document. Generate clear, specific prompts that:\n\n" +
        "1. Direct the system to analyze all documents with equal weight and importance\n" +
        "2. Instruct how to create comprehensive section structures from collective document analysis\n" +
        "3. Specify how to ensure all relevant information from all sources is incorporated\n" +
        "4. Guide the creation of well-structured outputs that synthesize rather than prioritize sources\n\n" +
        "The resulting prompt should enable the 8-stage pipeline to produce comprehensive documents that effectively combine insights from all available sources. IMPORTANT: Detect the language of the user task description and generate the prompt in the same detected language.";
    } else if (legalTaskType === "referenceFiles") {
      systemMessage =
        "You are a legal expert specializing in regulatory compliance and comparative analysis. Your task is to create optimized prompts for the Reference Files Flow, which performs compliance analysis by comparing documents against reference standards. Generate clear, structured meta-prompts that:\n\n" +
        "1. Direct the system to clearly distinguish between reference files (standards/rules) and review files (documents to be analyzed)\n" +
        "2. Instruct how to extract compliance criteria and benchmarks from reference documents\n" +
        "3. Specify how to systematically analyze review documents for compliance gaps, violations, and risks\n" +
        "4. Guide the creation of structured compliance reports with specific references and actionable findings\n\n" +
        "The resulting prompt should enable the 8-stage compliance pipeline to produce detailed reports that highlight deviations, ensure regulatory alignment, and provide clear compliance guidance. Focus on creating the prompt, not performing the analysis. IMPORTANT: Match the language of the inputs and generate only the prompt.";
    } else {
      systemMessage =
        "You are a legal expert who creates effective prompts for legal professionals. Your task is to generate clear, specific, and well-structured prompts that work optimally with the Case Document Builder system. Consider the specific workflow requirements and ensure the prompt guides the system to produce high-quality legal analysis. IMPORTANT: Detect the language of the user task description and generate the prompt in the same detected language.";
    }

    const messages = [
      { role: "system" as const, content: systemMessage },
      { role: "user" as const, content: taskDescription },
    ];
    const maxTokens = Math.floor(llmProviderToUse.promptWindowLimit() * 0.6); // Use 60% of available context for larger response
    const temperature = 0.2; // Slightly creative while still being reliable

    try {
      const response = await llmProviderToUse.getChatCompletion(messages, {
        temperature,
        maxTokens,
      });
      return response?.textResponse || "";
    } catch (error: unknown) {
      console.error("Error generating legal task prompt with LLM:", error);
      throw error;
    }
  }

  throw new Error("No LLM provider available for legal task prompt generation");
}

/**
 * Add the generate legal task prompt endpoint to the application.
 * @param apiRouter - The Express router instance.
 */
function addGenerateLegalTaskPromptEndpoint(apiRouter: Router): void {
  if (!apiRouter) return;

  apiRouter.post(
    "/generate-legal-task-prompt",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.admin, ROLES.manager, ROLES.superuser]),
    ],
    async (
      request: Request,
      response: Response<GenerateLegalTaskPromptResponse>
    ): Promise<void> => {
      try {
        // Extract both the task description, full prompt template, and flags
        // controlling workspace LLM usage and main document requirement
        const {
          taskDescription,
          promptTemplate,
          workspaceId,
          useWorkspaceLLM,
          legalTaskType,
        } = reqBody(request) as GenerateLegalTaskPromptRequestBody;

        const inputForLLM = promptTemplate || taskDescription;
        let llmInstanceForUpgrade: ReturnType<typeof getLLMProvider> | null =
          null;

        if (useWorkspaceLLM && workspaceId) {
          const workspace = await Workspace.get({ id: Number(workspaceId) });
          if (!workspace) {
            response
              .status(404)
              .json({ success: false, error: "Workspace not found." });
            return;
          }
          // Resolve LLM provider based on workspace settings
          // This might involve more complex logic if credentials/specific settings are needed
          llmInstanceForUpgrade = getLLMProvider({
            provider: workspace.chatProvider as string,
            model: workspace.chatModel as string,
          });
        }

        if (!inputForLLM) {
          response.status(400).json({
            success: false,
            error: "Task description or prompt template is required",
          });
          return;
        }

        const prompt = await generateLegalTaskPrompt(
          inputForLLM,
          llmInstanceForUpgrade,
          legalTaskType || "noMainDoc"
        );

        response.status(200).json({ success: true, prompt });
      } catch (error: unknown) {
        console.error("GenerateLegalTaskPrompt endpoint error:", error);
        // Return the actual error message in development to aid debugging
        response.status(500).json({
          success: false,
          error: (error as Error).message || "Internal Server Error",
        });
      }
    }
  );
}

export { addGenerateLegalTaskPromptEndpoint, generateLegalTaskPrompt };
