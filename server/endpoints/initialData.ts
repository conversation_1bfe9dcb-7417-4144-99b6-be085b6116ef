import { SystemSettings } from "../models/systemSettings";
import { Workspace } from "../models/workspace";
import { validatedRequest } from "../utils/middleware/validatedRequest";
import { userFromSession } from "../utils/http";
import { v4 as uuidv4 } from "uuid";
import { ExpressApp } from "../types/shared";
import { Request, Response } from "express";
import { workspaces as PrismaWorkspace } from "@prisma/client";

/**
 * Wraps a promise with a timeout
 * @param promise - The promise to wrap
 * @param timeoutMs - Timeout in milliseconds
 * @param operation - Description of the operation for error messages
 * @param fallbackValue - Value to return if timeout occurs
 * @returns Promise that resolves with result or fallback value
 */
async function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  operation: string,
  fallbackValue: T
): Promise<T> {
  let timeoutId: NodeJS.Timeout | undefined;

  const timeoutPromise = new Promise<T>((resolve) => {
    timeoutId = setTimeout(() => {
      console.warn(
        `[withTimeout] Operation timed out after ${timeoutMs}ms: ${operation}`
      );
      resolve(fallbackValue);
    }, timeoutMs);
  });

  try {
    const result = await Promise.race([promise, timeoutPromise]);
    if (timeoutId) clearTimeout(timeoutId);
    return result;
  } catch (error) {
    if (timeoutId) clearTimeout(timeoutId);
    console.error(`[withTimeout] Error in operation ${operation}:`, error);
    return fallbackValue;
  }
}

export function initialDataEndpoints(app: ExpressApp): void {
  if (!app) return;

  // Optimized endpoint that returns only essential data for initial page load
  app.get(
    "/api/initial-data",
    [validatedRequest],
    async (request: Request, response: Response): Promise<void> => {
      const requestId = uuidv4();
      try {
        const startTime = Date.now();
        console.log(`[${requestId}] Initial data request started`);

        const user = await userFromSession(request, response);

        // Define timeout values - can be adjusted via environment variables
        const SETTING_TIMEOUT_MS = parseInt(
          process.env.INITIAL_DATA_SETTING_TIMEOUT_MS || "2000"
        ); // 2 seconds for individual settings
        const WORKSPACE_TIMEOUT_MS = parseInt(
          process.env.INITIAL_DATA_WORKSPACE_TIMEOUT_MS || "3000"
        ); // 3 seconds for workspace query
        const OVERALL_TIMEOUT_MS = parseInt(
          process.env.INITIAL_DATA_OVERALL_TIMEOUT_MS || "5000"
        ); // 5 seconds for all operations

        // Wrap the entire operation in a timeout
        const result = await withTimeout(
          (async () => {
            // First fetch multiUserMode as it's needed for workspace query
            const multiUserMode = await withTimeout(
              SystemSettings.isMultiUserMode(),
              SETTING_TIMEOUT_MS,
              "fetching multiUserMode",
              false
            );

            // Fetch only essential settings in parallel
            const [language, palette, appName, tabNames, workspaces] =
              await Promise.all([
                withTimeout(
                  SystemSettings.getValueOrFallback(
                    { label: "language" },
                    null
                  ),
                  SETTING_TIMEOUT_MS,
                  "fetching language",
                  null
                ),
                withTimeout(
                  SystemSettings.getValueOrFallback(
                    { label: "color-palette" },
                    ""
                  ),
                  SETTING_TIMEOUT_MS,
                  "fetching color palette",
                  ""
                ),
                withTimeout(
                  SystemSettings.getValueOrFallback(
                    { label: "customAppName" },
                    process.env.APP_NAME || "ISTLegal"
                  ),
                  SETTING_TIMEOUT_MS,
                  "fetching app name",
                  process.env.APP_NAME || "ISTLegal"
                ),
                withTimeout(
                  Promise.all([
                    SystemSettings.getValueOrFallback(
                      { label: "tabName1" },
                      "Legal Q&A"
                    ),
                    SystemSettings.getValueOrFallback(
                      { label: "tabName2" },
                      "Document Drafting"
                    ),
                    SystemSettings.getValueOrFallback(
                      { label: "tabName3" },
                      "Tab 3"
                    ),
                  ]).then(([tab1, tab2, tab3]) => ({
                    tabName1: tab1,
                    tabName2: tab2,
                    tabName3: tab3,
                  })),
                  SETTING_TIMEOUT_MS,
                  "fetching tab names",
                  {
                    tabName1: "Legal Q&A",
                    tabName2: "Document Drafting",
                    tabName3: "Tab 3",
                  }
                ),
                user
                  ? withTimeout(
                      Workspace.getRecentWorkspacesForHome(
                        user,
                        multiUserMode,
                        10
                      ),
                      WORKSPACE_TIMEOUT_MS,
                      "fetching workspaces",
                      [] as PrismaWorkspace[]
                    )
                  : ([] as PrismaWorkspace[]),
              ]);

            return {
              multiUserMode,
              language,
              palette,
              appName,
              tabNames,
              workspaces,
            };
          })(),
          OVERALL_TIMEOUT_MS,
          "fetching all initial data",
          {
            multiUserMode: false,
            language: null,
            palette: "",
            appName: process.env.APP_NAME || "ISTLegal",
            tabNames: {
              tabName1: "Legal Q&A",
              tabName2: "Document Drafting",
              tabName3: "Tab 3",
            },
            workspaces: [] as PrismaWorkspace[],
          }
        );

        // Build minimal response with only essential data
        const essentialData = {
          settings: {
            MultiUserMode: result.multiUserMode,
            language: result.language,
            palette: result.palette,
            appName: result.appName,
            tabNames: result.tabNames,
            // Add other essential settings as needed
          },
          workspaces: result.workspaces.map((ws) => ({
            id: ws.id,
            name: ws.name,
            slug: ws.slug,
            type: ws.type,
            lastUpdatedAt: ws.lastUpdatedAt,
            createdAt: ws.createdAt,
            // Minimal workspace data - no need for full thread data initially
          })),
          user: user
            ? {
                id: user.id,
                username: user.username,
                role: user.role,
                pfpFilename: user.pfpFilename,
              }
            : null,
          performance: {
            duration: Date.now() - startTime,
            timestamp: new Date().toISOString(),
            requestId: requestId,
          },
        };

        // Set cache headers for performance
        response.set({
          "Cache-Control": "private, max-age=300", // Cache for 5 minutes
          ETag: `"${Date.now()}"`, // Simple ETag for now
        });

        console.log(
          `[${requestId}] Initial data request completed in ${essentialData.performance.duration}ms`
        );

        response.status(200).json({
          success: true,
          data: essentialData,
        });
      } catch (error) {
        console.error(`[${requestId}] Error fetching initial data:`, error);

        // Return minimal fallback data on error
        response.status(500).json({
          success: false,
          error: "Failed to fetch initial data",
          data: {
            settings: {
              MultiUserMode: false,
              language: null,
              palette: "",
              appName: process.env.APP_NAME || "ISTLegal",
              tabNames: {
                tabName1: "Legal Q&A",
                tabName2: "Document Drafting",
                tabName3: "Tab 3",
              },
            },
            workspaces: [] as PrismaWorkspace[],
            user: null,
          },
        });
      }
    }
  );
}
