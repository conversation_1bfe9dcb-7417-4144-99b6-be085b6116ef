import type { Express, Request, Response, Router } from "express";
import { NewsMessage, NewsPriority } from "../models/newsMessage";
import { validatedRequest } from "../utils/middleware/validatedRequest";
import { requireAdminRole } from "../utils/middleware/requireAdminRole";
import { reqBody, safeJsonParse } from "../utils/http";
import { FilteredUser } from "../types/models";
import { ApiResponse } from "../types/shared";

// ----------------- Validation helpers -----------------
function validateNewsId(
  newsId: string | undefined
): { isValid: false; error: string } | { isValid: true; value: number } {
  if (!newsId) return { isValid: false, error: "newsId is required" };
  const parsedId = parseInt(newsId, 10);
  if (isNaN(parsedId) || parsedId <= 0)
    return {
      isValid: false,
      error: "newsId must be a valid positive integer",
    };
  return { isValid: true, value: parsedId };
}

function validateSystemNewsId(
  systemNewsId: string | undefined
):
  | { isValid: false; error: string }
  | { isValid: true; value: number | string } {
  if (!systemNewsId)
    return { isValid: false, error: "systemNewsId is required" };
  const parsedId = parseInt(systemNewsId, 10);
  if (!isNaN(parsedId) && parsedId > 0)
    return { isValid: true, value: parsedId };
  if (
    typeof systemNewsId === "string" &&
    /^[a-zA-Z0-9\-_]+$/.test(systemNewsId.trim())
  )
    return { isValid: true, value: systemNewsId.trim() };
  return {
    isValid: false,
    error:
      "systemNewsId must be a valid positive integer or a valid string identifier",
  };
}

// ----------------- Core -----------------

// News API response interfaces
interface NewsResponse extends ApiResponse<unknown[]> {
  news?: unknown[];
}

interface UnreadNewsResponse extends ApiResponse<unknown[]> {
  news?: unknown[];
}

interface DismissedNewsResponse extends ApiResponse<string[]> {
  dismissedIds?: string[];
}

interface SystemNewsResponse extends ApiResponse<unknown[]> {
  systemNews?: unknown[];
}

interface NewsViewResponse extends ApiResponse<unknown> {
  dismissal?: unknown;
}

interface NewsDismissResponse extends ApiResponse<unknown> {
  dismissal?: unknown;
}

interface AdminNewsResponse extends ApiResponse<unknown[]> {
  news?: unknown[];
}

interface CreateNewsRequest {
  title: string;
  content: string;
  priority?: NewsPriority;
  targetRoles?: string[];
  expiresAt?: string;
}

interface CreateNewsResponse extends ApiResponse<unknown> {
  newsMessage?: unknown;
}

interface UpdateNewsResponse extends ApiResponse<unknown> {
  newsMessage?: unknown;
}

export function newsEndpoints(app: Express, apiRouter?: Router): void {
  if (!app) return;

  // Use apiRouter for API routes if provided
  const router = apiRouter || app;

  // Get unread news for current user
  router.get(
    "/news/unread",
    [validatedRequest],
    async (_req: Request, res: Response<UnreadNewsResponse>): Promise<void> => {
      try {
        const user = res.locals.user as FilteredUser;
        const unreadNews = await NewsMessage.getUnreadForUser(user.id);
        res.status(200).json({ success: true, news: unreadNews });
      } catch (_error) {
        console.error("Error fetching unread news:", _error);
        res
          .status(500)
          .json({ success: false, message: "Failed to fetch unread news" });
      }
    }
  );

  // Get dismissed system news IDs
  router.get(
    "/news/dismissed-system",
    [validatedRequest],
    async (
      _req: Request,
      res: Response<DismissedNewsResponse>
    ): Promise<void> => {
      try {
        const user = res.locals.user as FilteredUser;
        const dismissedIds = await NewsMessage.getDismissedSystemNewsIds(
          user.id
        );
        res.status(200).json({ success: true, dismissedIds });
      } catch (_error) {
        console.error("Error fetching dismissed system news:", _error);
        res.status(500).json({
          success: false,
          message: "Failed to fetch dismissed system news",
        });
      }
    }
  );

  // Mark news as viewed
  router.post(
    "/news/:newsId/view",
    [validatedRequest],
    async (
      req: Request<{ newsId: string }>,
      res: Response<NewsViewResponse>
    ): Promise<void> => {
      try {
        const user = res.locals.user as FilteredUser;
        const { newsId } = req.params;
        const validation = validateNewsId(newsId);
        if (!validation.isValid) {
          res.status(400).json({ success: false, message: validation.error });
          return;
        }
        const { dismissal, message } = await NewsMessage.markAsViewed(
          user.id,
          validation.value
        );
        if (message) {
          res.status(400).json({ success: false, message });
          return;
        }
        res.status(200).json({ success: true, dismissal });
      } catch (_error) {
        console.error("Error marking news as viewed:", _error);
        res
          .status(500)
          .json({ success: false, message: "Failed to mark news as viewed" });
      }
    }
  );

  // Dismiss news
  router.post(
    "/news/:newsId/dismiss",
    [validatedRequest],
    async (
      req: Request<{ newsId: string }>,
      res: Response<NewsDismissResponse>
    ): Promise<void> => {
      try {
        const user = res.locals.user as FilteredUser;
        const { newsId } = req.params;
        const validation = validateNewsId(newsId);
        if (!validation.isValid) {
          res.status(400).json({ success: false, message: validation.error });
          return;
        }
        const { dismissal, message } = await NewsMessage.dismiss(
          user.id,
          validation.value,
          false
        );
        if (message) {
          res.status(400).json({ success: false, message });
          return;
        }
        res.status(200).json({ success: true, dismissal });
      } catch (_error) {
        console.error("Error dismissing news:", _error);
        res
          .status(500)
          .json({ success: false, message: "Failed to dismiss news" });
      }
    }
  );

  // Dismiss system news
  router.post(
    "/news/system/:systemNewsId/dismiss",
    [validatedRequest],
    async (
      req: Request<{ systemNewsId: string }>,
      res: Response<NewsDismissResponse>
    ): Promise<void> => {
      try {
        const user = res.locals.user as FilteredUser;
        const { systemNewsId } = req.params;
        const validation = validateSystemNewsId(systemNewsId);
        if (!validation.isValid) {
          res.status(400).json({ success: false, message: validation.error });
          return;
        }
        const { dismissal, message } = await NewsMessage.dismiss(
          user.id,
          validation.value,
          true
        );
        if (message) {
          res.status(400).json({ success: false, message });
          return;
        }
        res.status(200).json({
          success: true,
          message: "System news dismissed successfully",
          dismissal,
        });
      } catch (_error) {
        console.error("Error dismissing system news:", _error);
        res
          .status(500)
          .json({ success: false, message: "Failed to dismiss system news" });
      }
    }
  );

  // System news list
  router.get(
    "/news/system",
    async (_req: Request, res: Response<SystemNewsResponse>): Promise<void> => {
      try {
        const { getActiveSystemNews } = await import("../data/systemNewsItems");
        const systemNews = getActiveSystemNews();
        res.status(200).json({ success: true, systemNews });
      } catch (_error) {
        console.error("Error fetching system news:", _error);
        res
          .status(500)
          .json({ success: false, message: "Failed to fetch system news" });
      }
    }
  );

  // All active news
  router.get(
    "/news/all-active",
    [validatedRequest],
    async (_req: Request, res: Response<NewsResponse>): Promise<void> => {
      try {
        const user = res.locals.user as FilteredUser;
        const databaseNews = await NewsMessage.getAllActiveForUser(user.id);
        const dismissedData = await NewsMessage.getDismissedNewsIds(user.id);
        const dismissedSystemIds: string[] = dismissedData.system || [];
        const { getSystemNewsForRoles } = await import(
          "../data/systemNewsItems"
        );
        const userRoles = user.role ? [user.role] : [];
        const activeSystemNews = getSystemNewsForRoles(userRoles);
        const systemNewsWithStatus = activeSystemNews.map((n) => ({
          ...n,
          isSystemNews: true,
          isDismissed: dismissedSystemIds.includes(n.id),
          dismissedAt: dismissedSystemIds.includes(n.id) ? new Date() : null,
        }));
        const allNews = [...databaseNews, ...systemNewsWithStatus];
        const priorityOrder: Record<string, number> = {
          urgent: 4,
          high: 3,
          medium: 2,
          low: 1,
        };
        allNews.sort((a, b) => {
          const diff =
            (priorityOrder[b.priority] || 0) - (priorityOrder[a.priority] || 0);
          if (diff !== 0) return diff;
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        });
        res.status(200).json({ success: true, news: allNews });
      } catch (_error) {
        console.error("Error fetching all active news:", _error);
        res
          .status(500)
          .json({ success: false, message: "Failed to fetch all active news" });
      }
    }
  );

  // ----------------- Admin -----------------

  router.get(
    "/admin/news",
    [validatedRequest, requireAdminRole],
    async (_req: Request, res: Response<AdminNewsResponse>): Promise<void> => {
      try {
        const allNews = await NewsMessage.getAll();
        const transformed = allNews.map((n) => ({
          ...n,
          isActive: n.is_active,
          targetRoles: n.target_roles
            ? safeJsonParse(n.target_roles, null)
            : null,
          expiresAt: n.expires_at,
          createdBy: n.created_by,
        }));
        res.status(200).json({ success: true, news: transformed });
      } catch (_error) {
        console.error("Error fetching all news:", _error);
        res
          .status(500)
          .json({ success: false, message: "Failed to fetch news" });
      }
    }
  );

  router.post(
    "/admin/news",
    [validatedRequest, requireAdminRole],
    async (
      req: Request<never, CreateNewsResponse, CreateNewsRequest>,
      res: Response<CreateNewsResponse>
    ): Promise<void> => {
      try {
        const user = res.locals.user as FilteredUser;
        const { title, content, priority, targetRoles, expiresAt } = reqBody(
          req
        ) as CreateNewsRequest;
        if (!title || !content) {
          res.status(400).json({
            success: false,
            message: "Title and content are required",
          });
          return;
        }
        const { newsMessage, message } = await NewsMessage.create({
          title,
          content,
          priority,
          targetRoles,
          expiresAt: expiresAt ? new Date(expiresAt) : null,
          createdBy: user.id,
        });
        if (message) {
          res.status(400).json({ success: false, message });
          return;
        }
        const transformed = newsMessage
          ? {
              ...newsMessage,
              isActive: newsMessage.is_active,
              targetRoles: newsMessage.target_roles
                ? safeJsonParse(newsMessage.target_roles, null)
                : null,
              expiresAt: newsMessage.expires_at,
              createdBy: newsMessage.created_by,
            }
          : null;
        res.status(201).json({ success: true, newsMessage: transformed });
      } catch (_error) {
        console.error("Error creating news:", _error);
        res
          .status(500)
          .json({ success: false, message: "Failed to create news" });
      }
    }
  );

  router.put(
    "/admin/news/:newsId",
    [validatedRequest, requireAdminRole],
    async (
      req: Request<
        { newsId: string },
        UpdateNewsResponse,
        Partial<CreateNewsRequest>
      >,
      res: Response<UpdateNewsResponse>
    ): Promise<void> => {
      try {
        const { newsId } = req.params;
        const validation = validateNewsId(newsId);
        if (!validation.isValid) {
          res.status(400).json({ success: false, message: validation.error });
          return;
        }
        const updates = reqBody(req);
        const { newsMessage, message } = await NewsMessage.update(
          validation.value,
          updates
        );
        if (message) {
          res.status(400).json({ success: false, message });
          return;
        }
        if (!newsMessage) {
          res
            .status(404)
            .json({ success: false, message: "News message not found" });
          return;
        }
        res.status(200).json({ success: true, newsMessage });
      } catch (_error) {
        console.error("Error updating news:", _error);
        res
          .status(500)
          .json({ success: false, message: "Failed to update news" });
      }
    }
  );

  router.delete(
    "/admin/news/:newsId",
    [validatedRequest, requireAdminRole],
    async (
      req: Request<{ newsId: string }>,
      res: Response<ApiResponse>
    ): Promise<void> => {
      try {
        const { newsId } = req.params;
        const validation = validateNewsId(newsId);
        if (!validation.isValid) {
          res.status(400).json({ success: false, message: validation.error });
          return;
        }
        await NewsMessage.delete(validation.value);
        res.status(200).json({ success: true });
      } catch (_error) {
        console.error("Error deleting news:", _error);
        res
          .status(500)
          .json({ success: false, message: "Failed to delete news" });
      }
    }
  );
}
