import { Request, Response } from "express";
import { reqBody, userFromSession } from "../utils/http";
import { validatedRequest } from "../utils/middleware/validatedRequest";
import { EventLogs } from "../models/eventLogs";
import {
  SESClient,
  SendEmailCommand,
  SendEmailCommandInput,
} from "@aws-sdk/client-ses";
import { Router } from "express";
import { Workspace } from "../models/workspace";
import { WorkspaceChats } from "../models/workspaceChats";
const SystemSettings = require("../models/systemSettings").default;
import { getLLMProvider } from "../utils/helpers";
import { validApiKey } from "../utils/middleware/validApiKey";

// Legal assistance request interfaces
interface LegalAssistanceRequestBody {
  lawFirmName?: string;
  email: string;
  partyName: string;
  partyOrgId: string;
  opposingPartyName?: string;
  opposingPartyOrgId?: string;
}

// Chat-based legal assistance interfaces
interface ChatLegalAssistanceRequestBody {
  message: string;
  mode: string;
  assistanceType: string;
  attachments?: string[];
}

interface SESCredentials {
  accessKeyId: string;
  secretAccessKey: string;
}

// System settings type for rate limiting
interface RateLimitSettings {
  limit_user_messages?: boolean;
  message_limit?: number;
  [key: string]: unknown;
}

// Where clause type for WorkspaceChats queries
interface WorkspaceChatsWhereClause {
  workspaceId: number;
  metadata?: { contains: string };
  [key: string]: unknown;
}

// Chat type with metadata field
interface ChatWithMetadata {
  id: number;
  metadata?: string | null;
  [key: string]: unknown;
}

/**
 * Creates a SES client with the configured credentials
 * @returns {SESClient} The configured SES client
 */
function createSESClient(): SESClient {
  return new SESClient({
    region: process.env.AWS_SES_REGION || "eu-north-1",
    credentials: {
      accessKeyId: process.env.AWS_SES_ACCESS_KEY_ID!,
      secretAccessKey: process.env.AWS_SES_SECRET_ACCESS_KEY!,
    } as SESCredentials,
  });
}

/**
 * Sends an email using AWS SES
 * @param {SendEmailCommandInput} params - Email parameters
 * @returns {Promise<Object>} The result of the send operation
 */
async function sendEmail(params: SendEmailCommandInput) {
  const sesClient = createSESClient();
  const sendEmailCommand = new SendEmailCommand(params);
  return await sesClient.send(sendEmailCommand);
}

function requestLegalAssistanceEndpoints(router: Router): void {
  if (!router) return;

  router.post(
    "/request-legal-assistance",
    [validatedRequest],
    async (request: Request, response: Response): Promise<void> => {
      console.log("SENDING REQUEST");
      try {
        const {
          lawFirmName,
          email,
          partyName,
          partyOrgId,
          opposingPartyName,
          opposingPartyOrgId,
        }: LegalAssistanceRequestBody = reqBody(request);
        const user = await userFromSession(request);

        // Validate required fields
        if (!partyName || !partyOrgId) {
          response.status(400).json({
            success: false,
            error: "Party name and organization ID are required",
          });
          return;
        }

        if (!email) {
          response.status(400).json({
            success: false,
            error: "Recipient email is not configured",
          });
          return;
        }

        // Prepare email parameters
        const params: SendEmailCommandInput = {
          Source: process.env.AWS_SES_SENDER_EMAIL || "<EMAIL>",
          Destination: {
            ToAddresses: [email as string],
          },
          Message: {
            Subject: {
              Data: `Legal Assistance Request for ${lawFirmName || "Law Firm"}`,
            },
            Body: {
              Text: {
                Data: `
Legal Assistance Request Details:
------------------------------
Requesting User: ${user ? `${user.username}` : "Anonymous"}
Timestamp: ${new Date().toLocaleString("sv-SE")}

Request for Conflict Check:
Party: ${partyName}
Organization ID: ${partyOrgId}
${opposingPartyName ? `Opposing Party: ${opposingPartyName}\n` : ""}${opposingPartyOrgId ? `Opposing Party Organization ID: ${opposingPartyOrgId}\n` : ""}
`,
              },
              Html: {
                Data: `
<h2>Legal Assistance Request</h2>
<p><strong>Recipient:</strong> ${lawFirmName || "Law Firm"}</p>
<p><strong>Requesting User:</strong> ${user ? `${user.username}` : "Anonymous"}</p>
<p><strong>Timestamp:</strong> ${new Date().toLocaleString("sv-SE")}</p>

<h3>Request for Conflict Check:</h3>
<p><strong>Requesting Party:</strong> ${partyName}</p>
<p><strong>Organization ID:</strong> ${partyOrgId}</p>
${opposingPartyName ? `<p><strong>Opposing Party:</strong> ${opposingPartyName}</p>\n` : ""}
${opposingPartyOrgId ? `<p><strong>Opposing Party Organization ID:</strong> ${opposingPartyOrgId}</p>\n` : ""}
`,
              },
            },
          },
        };

        // Send email
        const result = await sendEmail(params);

        // Log the event
        await EventLogs.logEvent(
          "legal_assistance_request",
          {
            user: user ? user.username : "Anonymous",
            email,
            lawFirmName,
            partyName,
            partyOrgId,
            opposingPartyName,
            opposingPartyOrgId,
            messageId: result.MessageId,
            timestamp: new Date().toISOString(),
          },
          user?.id || 0
        );

        response.status(200).json({
          success: true,
          message: "Legal assistance request sent successfully",
          messageId: result.MessageId,
        });
      } catch (error) {
        console.error("Error sending legal assistance request:", error);
        response.status(500).json({
          success: false,
          error:
            (error as Error).message ||
            "Failed to send legal assistance request",
        });
      }
    }
  );

  // Chat-based legal assistance endpoints
  router.post(
    "/workspace/:slug/request-legal-assistance",
    [validatedRequest, validApiKey],
    async (request: Request, response: Response): Promise<void> => {
      try {
        const { slug } = request.params;
        const {
          message,
          mode: _mode,
          assistanceType,
          attachments = [],
        }: ChatLegalAssistanceRequestBody = reqBody(request);
        const _user = await userFromSession(request);

        // Validate required fields
        if (!message || message.trim() === "") {
          response.status(400).json({
            success: false,
            error: "Message is required",
          });
          return;
        }

        const validAssistanceTypes = [
          "legal_advice",
          "document_review",
          "contract_analysis",
          "case_research",
        ];
        if (!validAssistanceTypes.includes(assistanceType)) {
          response.status(400).json({
            success: false,
            error: "Invalid assistance type",
          });
          return;
        }

        // Get workspace
        const workspace = await Workspace.get({ slug });
        if (!workspace) {
          response.status(404).json({
            success: false,
            error: "Workspace not found",
          });
          return;
        }

        // Check rate limiting
        const settings = await SystemSettings.currentSettings();
        const settingsData = settings as RateLimitSettings;
        if (settingsData?.limit_user_messages) {
          const messageCount = await WorkspaceChats.count({
            workspaceId: workspace.id,
          });
          if (messageCount >= (settingsData.message_limit || 10)) {
            response.status(429).json({
              success: false,
              error: "Message limit reached",
            });
            return;
          }
        }

        // Get LLM provider
        const llmProvider = getLLMProvider({
          provider: workspace.chatProvider || "default",
          model: workspace.chatModel || "default",
        });

        if (!llmProvider) {
          throw new Error("LLM provider not available");
        }

        // Prepare system prompt based on assistance type
        const systemPrompts = {
          legal_advice:
            "You are a legal assistant providing general legal guidance.",
          document_review:
            "You are a legal assistant specializing in document review.",
          contract_analysis:
            "You are a legal assistant specializing in contract analysis.",
          case_research:
            "You are a legal assistant specializing in case research.",
        };

        const systemPrompt =
          systemPrompts[assistanceType as keyof typeof systemPrompts];

        const prompt = [
          { role: "system" as const, content: systemPrompt },
          { role: "user" as const, content: message },
        ];

        // Get LLM response
        const llmResponse = await llmProvider.getChatCompletion(prompt, {
          temperature: 0.7,
          maxTokens: 1000,
        });

        if (!llmResponse) {
          throw new Error("Failed to get LLM response");
        }

        // Save chat to database
        const chatResult = await WorkspaceChats.new({
          workspaceId: workspace.id,
          prompt: message,
          response: {
            text: llmResponse.textResponse,
            sources: [],
            type: "legal_assistance",
            assistanceType,
          },
          // Note: user_id not included as it's not part of WorkspaceChatData interface
          threadId: null,
          metrics: { assistanceType, attachments },
        });

        response.status(200).json({
          success: true,
          chatId: chatResult.chat?.id,
          response: llmResponse.textResponse,
          assistanceType,
        });
      } catch (error) {
        console.error("Error processing legal assistance request:", error);
        response.status(500).json({
          success: false,
          error: "Failed to process legal assistance request",
        });
      }
    }
  );

  router.get(
    "/workspace/:slug/legal-assistance-history",
    [validatedRequest],
    async (request: Request, response: Response): Promise<void> => {
      try {
        const { slug } = request.params;
        const { limit = 20, offset = 0, assistanceType } = request.query;

        // Validate query parameters
        if (
          (limit && isNaN(Number(limit))) ||
          (offset && (isNaN(Number(offset)) || Number(offset) < 0))
        ) {
          response.status(400).json({
            success: false,
            error: "Invalid query parameters",
          });
          return;
        }

        const workspace = await Workspace.get({ slug });
        if (!workspace) {
          response.status(404).json({
            success: false,
            error: "Workspace not found",
          });
          return;
        }

        const whereClause: WorkspaceChatsWhereClause = {
          workspaceId: workspace.id,
        };

        if (assistanceType) {
          whereClause.metadata = { contains: String(assistanceType) };
        }

        const history = await WorkspaceChats.where(
          whereClause,
          Number(limit),
          { createdAt: "desc" },
          Number(offset)
        );

        response.status(200).json({
          success: true,
          history,
          pagination: {
            limit: Number(limit),
            offset: Number(offset),
          },
        });
      } catch (error) {
        console.error("Error retrieving legal assistance history:", error);
        response.status(500).json({
          success: false,
          error: "Failed to retrieve legal assistance history",
        });
      }
    }
  );

  router.delete(
    "/workspace/:slug/legal-assistance/:chatId",
    [validatedRequest],
    async (request: Request, response: Response): Promise<void> => {
      try {
        const { slug, chatId } = request.params;

        const workspace = await Workspace.get({ slug });
        if (!workspace) {
          response.status(404).json({
            success: false,
            error: "Workspace not found",
          });
          return;
        }

        const chat = await WorkspaceChats.get({ id: Number(chatId) });
        if (!chat) {
          response.status(404).json({
            success: false,
            error: "Chat not found",
          });
          return;
        }

        // Verify it's a legal assistance chat
        let metadata: { assistanceType?: string } = {};
        try {
          const chatData = chat as ChatWithMetadata;
          metadata = chatData.metadata ? JSON.parse(chatData.metadata) : {};
        } catch {
          metadata = {};
        }

        if (!metadata.assistanceType) {
          response.status(400).json({
            success: false,
            error: "Not a legal assistance chat",
          });
          return;
        }

        await WorkspaceChats.delete({ id: Number(chatId) });

        response.status(200).json({
          success: true,
          message: "Legal assistance record deleted successfully",
        });
      } catch (error) {
        console.error("Error deleting legal assistance record:", error);
        response.status(500).json({
          success: false,
          error: "Failed to delete legal assistance record",
        });
      }
    }
  );
}

export { requestLegalAssistanceEndpoints };
