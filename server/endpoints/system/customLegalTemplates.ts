import { Request, Response, Router } from "express";
import SystemSettings from "../../models/systemSettings";
import { EventLogs } from "../../models/eventLogs";
import { userFromSession } from "../../utils/http";
import { legalTemplateScopeGuard } from "../../utils/middleware/multiUserProtected";

// Interface definitions for legal templates
interface LegalTemplate {
  id: string;
  category: string;
  documentType: string;
  templateFormatting: string;
  templateContent: string;
  templateDescription: string;
  createdAt: string;
  createdBy: number;
  updatedAt?: string;
  updatedBy?: number;
  customInputEnabled: boolean;
  customInputs: CustomInput[];
}

interface CustomInput {
  id: string;
  label: string;
  type: string;
  required: boolean;
  placeholder?: string;
}

interface CreateLegalTemplateRequest extends Request {
  body: {
    category: string;
    documentType: string;
    templateDescription?: string;
    templateContent?: string;
    templateFormatting?: string;
    customInputEnabled?: boolean;
    customInputs?: CustomInput[];
  };
}

interface UpdateLegalTemplateRequest extends Request {
  params: {
    id: string;
  };
  body: {
    category: string;
    documentType: string;
    templateDescription?: string;
    templateContent?: string;
    templateFormatting?: string;
    customInputEnabled?: boolean;
    customInputs?: CustomInput[];
  };
}

interface DeleteLegalTemplateRequest extends Request {
  params: {
    id: string;
  };
}

interface CategoryOption {
  value: string;
  label: string;
}

interface LegalTemplateResponse {
  success: boolean;
  data?: LegalTemplate | LegalTemplate[] | CategoryOption[];
  message?: string;
  error?: string;
}

/**
 * Initialize the custom_legal_templates setting if it doesn't exist
 */
async function initializeCustomLegalTemplates(): Promise<void> {
  try {
    // Check if the setting already exists
    const existingSetting = await SystemSettings.get({
      label: "custom_legal_templates",
    });

    // If it doesn't exist, create it with an empty array
    if (!existingSetting) {
      await SystemSettings.updateSettings({
        custom_legal_templates: JSON.stringify([]),
      });
      console.log("Initialized custom_legal_templates setting");
    }
  } catch (_error) {
    console.error("Error initializing custom_legal_templates setting:", _error);
  }
}

function customLegalTemplatesEndpoints(router: Router): void {
  // Initialize the custom_legal_templates setting
  initializeCustomLegalTemplates();

  /**
   * @swagger
   * /system/custom-legal-templates:
   *   get:
   *     summary: Get all custom legal templates
   *     tags: [System]
   *     responses:
   *       200:
   *         description: List of custom legal templates
   */
  router.get(
    "/system/custom-legal-templates",
    legalTemplateScopeGuard("system"),
    async (req: Request, res: Response): Promise<void> => {
      try {
        // Check if user is admin, manager, or superuser
        const user = await userFromSession(req);
        if (
          !user ||
          (user.role !== "admin" &&
            user.role !== "manager" &&
            user.role !== "superuser")
        ) {
          res.status(403).json({
            success: false,
            error:
              "Unauthorized. Admin, manager, or superuser access required.",
          });
          return;
        }

        // Get custom legal templates using getValueOrFallback
        const templates: LegalTemplate[] =
          (await SystemSettings.getValueOrFallback(
            { label: "custom_legal_templates" },
            []
          )) as LegalTemplate[];

        res.status(200).json({
          success: true,
          data: templates,
        });
      } catch (error: unknown) {
        console.error(
          "Error fetching custom legal templates:",
          (error as Error).message
        );
        res.status(500).json({
          success: false,
          error: "Failed to fetch custom legal templates.",
        });
      }
    }
  );

  // Alias route for API compatibility
  router.get(
    "/custom-legal-templates",
    legalTemplateScopeGuard("system"),
    async (req: Request, res: Response): Promise<void> => {
      try {
        // Check if user is admin, manager, or superuser
        const user = await userFromSession(req);
        if (
          !user ||
          (user.role !== "admin" &&
            user.role !== "manager" &&
            user.role !== "superuser")
        ) {
          res.status(403).json({
            success: false,
            error:
              "Unauthorized. Admin, manager, or superuser access required.",
          });
          return;
        }

        // Get custom legal templates using getValueOrFallback
        let templates: LegalTemplate[] = [];
        try {
          const rawTemplates = await SystemSettings.getValueOrFallback(
            { label: "custom_legal_templates" },
            []
          );
          // Ensure we have an array
          if (Array.isArray(rawTemplates)) {
            templates = rawTemplates as LegalTemplate[];
          } else if (typeof rawTemplates === "string") {
            // Try to parse if it's a JSON string
            templates = JSON.parse(rawTemplates) as LegalTemplate[];
          } else {
            templates = [];
          }
        } catch (parseError) {
          console.error("Error parsing templates:", parseError);
          templates = [];
        }

        res.status(200).json({
          templates: templates,
        });
      } catch (error: unknown) {
        console.error(
          "Error fetching custom legal templates:",
          (error as Error).message
        );
        res.status(500).json({
          success: false,
          error: "Failed to fetch custom legal templates.",
        });
      }
    }
  );

  /**
   * @swagger
   * /system/custom-legal-template-categories:
   *   get:
   *     summary: Get all custom legal template categories
   *     tags: [System]
   *     responses:
   *       200:
   *         description: List of custom legal template categories
   */
  router.get(
    "/system/custom-legal-template-categories",
    legalTemplateScopeGuard("system"),
    async (req: Request, res: Response): Promise<void> => {
      try {
        // Check if user is admin, manager, or superuser
        const user = await userFromSession(req);
        if (
          !user ||
          (user.role !== "admin" &&
            user.role !== "manager" &&
            user.role !== "superuser")
        ) {
          res.status(403).json({
            success: false,
            error:
              "Unauthorized. Admin, manager, or superuser access required.",
          });
          return;
        }

        // Get custom legal templates using getValueOrFallback
        const templates: LegalTemplate[] =
          (await SystemSettings.getValueOrFallback(
            { label: "custom_legal_templates" },
            []
          )) as LegalTemplate[];

        // Extract unique categories and format them for the frontend Select component
        const uniqueCategories = Array.from(
          new Set(templates.map((t) => t.category))
        );
        const formattedCategories: CategoryOption[] = uniqueCategories.map(
          (category) => ({
            value: category,
            label: category,
          })
        );

        res.status(200).json({
          success: true,
          data: formattedCategories,
        });
      } catch (error: unknown) {
        console.error(
          "Error fetching custom legal template categories:",
          (error as Error).message
        );
        res.status(500).json({
          success: false,
          error: "Failed to fetch custom legal template categories.",
        });
      }
    }
  );

  /**
   * @swagger
   * /system/custom-legal-templates:
   *   post:
   *     summary: Create a new custom legal template
   *     tags: [System]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               category:
   *                 type: string
   *               documentType:
   *                 type: string
   *               templateDescription:
   *                 type: string
   *               templateContent:
   *                 type: string
   *               templateFormatting:
   *                 type: string
   *               customInputEnabled:
   *                 type: boolean
   *               customInputs:
   *                 type: array
   *                 items:
   *                   type: object
   *     responses:
   *       201:
   *         description: Custom legal template created successfully
   */
  router.post(
    "/system/custom-legal-templates",
    legalTemplateScopeGuard("system"),
    async (req: CreateLegalTemplateRequest, res: Response): Promise<void> => {
      try {
        // Authentication
        const user = await userFromSession(req);
        if (
          !user ||
          (user.role !== "admin" &&
            user.role !== "manager" &&
            user.role !== "superuser")
        ) {
          res.status(403).json({
            success: false,
            error:
              "Unauthorized. Admin, manager, or superuser access required.",
          });
          return;
        }

        // Access properties directly from req.body
        const trimmedCategory = req.body?.category?.trim();
        const trimmedDocType = req.body?.documentType?.trim();
        const templateFormatting = req.body.templateFormatting || "";

        if (!trimmedCategory || !trimmedDocType) {
          res.status(400).json({
            success: false,
            error: "Category and document type are required.",
          });
          return;
        }

        // Get existing custom legal templates using getValueOrFallback
        const templates: LegalTemplate[] =
          (await SystemSettings.getValueOrFallback(
            { label: "custom_legal_templates" },
            []
          )) as LegalTemplate[];

        // Check if a template with the same category and document type already exists
        const existingTemplateIndex = templates.findIndex(
          (t) =>
            t.category === trimmedCategory && t.documentType === trimmedDocType
        );

        if (existingTemplateIndex !== -1) {
          res.status(400).json({
            success: false,
            error:
              "A template with this category and document type already exists.",
          });
          return;
        }

        // Create a new template
        const newTemplate: LegalTemplate = {
          id: Date.now().toString(), // Simple unique ID
          category: trimmedCategory,
          documentType: trimmedDocType,
          templateFormatting: templateFormatting,
          templateContent: req.body.templateContent || "",
          templateDescription: req.body.templateDescription || "",
          createdAt: new Date().toISOString(),
          createdBy: user.id,
          // Custom input fields support
          customInputEnabled: !!req.body.customInputEnabled,
          customInputs: Array.isArray(req.body.customInputs)
            ? req.body.customInputs
            : [],
        };

        // Add the new template to the array
        templates.push(newTemplate);

        // Update the system setting
        await SystemSettings.updateSettings({
          custom_legal_templates: JSON.stringify(templates),
        });

        await EventLogs.logEvent("custom_legal_template_created", {
          category: trimmedCategory,
          documentType: trimmedDocType,
          userId: user.id,
        });

        res.status(201).json({
          success: true,
          message: "Custom legal template created successfully.",
          data: newTemplate,
        });
      } catch (error: unknown) {
        console.error(
          "Error creating custom legal template:",
          (error as Error).message
        );
        res.status(500).json({
          success: false,
          error: "Failed to create custom legal template.",
        });
      }
    }
  );

  /**
   * @swagger
   * /system/custom-legal-templates/{id}:
   *   put:
   *     summary: Update a custom legal template
   *     tags: [System]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               category:
   *                 type: string
   *               documentType:
   *                 type: string
   *               templateDescription:
   *                 type: string
   *               templateContent:
   *                 type: string
   *               templateFormatting:
   *                 type: string
   *               customInputEnabled:
   *                 type: boolean
   *               customInputs:
   *                 type: array
   *     responses:
   *       200:
   *         description: Custom legal template updated successfully
   */
  router.put(
    "/system/custom-legal-templates/:id",
    legalTemplateScopeGuard("system"),
    async (req: UpdateLegalTemplateRequest, res: Response): Promise<void> => {
      try {
        // Check if user is admin, manager, or superuser
        const user = await userFromSession(req);
        if (
          !user ||
          (user.role !== "admin" &&
            user.role !== "manager" &&
            user.role !== "superuser")
        ) {
          res.status(403).json({
            success: false,
            error:
              "Unauthorized. Admin, manager, or superuser access required.",
          });
          return;
        }

        const { id } = req.params;

        // Access properties directly from req.body
        const trimmedCategory = req.body?.category?.trim();
        const trimmedDocType = req.body?.documentType?.trim();
        const templateFormatting = req.body.templateFormatting || "";

        if (!trimmedCategory || !trimmedDocType) {
          res.status(400).json({
            success: false,
            error: "Category and document type are required.",
          });
          return;
        }

        // Get existing custom legal templates using getValueOrFallback
        const templates: LegalTemplate[] =
          (await SystemSettings.getValueOrFallback(
            { label: "custom_legal_templates" },
            []
          )) as LegalTemplate[];

        // Find the template to update
        const templateIndex = templates.findIndex((t) => t.id === id);

        if (templateIndex === -1) {
          res.status(404).json({
            success: false,
            error: "Custom legal template not found.",
          });
          return;
        }

        // Check if another template with the same category and document type already exists
        // (excluding the current template being updated)
        const duplicateIndex = templates.findIndex(
          (t, index) =>
            index !== templateIndex &&
            t.category === trimmedCategory &&
            t.documentType === trimmedDocType
        );

        if (duplicateIndex !== -1) {
          res.status(400).json({
            success: false,
            error:
              "Another template with this category and document type already exists.",
          });
          return;
        }

        // Store original template for logging
        const originalTemplate = { ...templates[templateIndex] };

        // Update the template
        templates[templateIndex] = {
          ...templates[templateIndex],
          category: trimmedCategory,
          documentType: trimmedDocType,
          templateContent: req.body.templateContent || "",
          templateFormatting: templateFormatting,
          templateDescription: req.body.templateDescription || "",
          updatedAt: new Date().toISOString(),
          updatedBy: user.id,
          // Custom input fields support
          customInputEnabled: !!req.body.customInputEnabled,
          customInputs: Array.isArray(req.body.customInputs)
            ? req.body.customInputs
            : [],
        };

        // Update the system setting
        await SystemSettings.updateSettings({
          custom_legal_templates: JSON.stringify(templates),
        });

        await EventLogs.logEvent("custom_legal_template_updated", {
          id,
          originalCategory: originalTemplate.category,
          originalDocumentType: originalTemplate.documentType,
          newCategory: trimmedCategory,
          newDocumentType: trimmedDocType,
          userId: user.id,
        });

        res.status(200).json({
          success: true,
          message: "Custom legal template updated successfully.",
          data: templates[templateIndex],
        });
      } catch (error: unknown) {
        console.error(
          "Error updating custom legal template:",
          (error as Error).message
        );
        res.status(500).json({
          success: false,
          error: "Failed to update custom legal template.",
        });
      }
    }
  );

  /**
   * @swagger
   * /system/custom-legal-templates/{id}:
   *   delete:
   *     summary: Delete a custom legal template
   *     tags: [System]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *     responses:
   *       200:
   *         description: Custom legal template deleted successfully
   */
  router.delete(
    "/system/custom-legal-templates/:id",
    legalTemplateScopeGuard("system"),
    async (req: DeleteLegalTemplateRequest, res: Response): Promise<void> => {
      try {
        // Check if user is admin, manager, or superuser
        const user = await userFromSession(req);
        if (
          !user ||
          (user.role !== "admin" &&
            user.role !== "manager" &&
            user.role !== "superuser")
        ) {
          res.status(403).json({
            success: false,
            error:
              "Unauthorized. Admin, manager, or superuser access required.",
          });
          return;
        }

        const { id } = req.params;

        // Get existing custom legal templates using getValueOrFallback
        const templates: LegalTemplate[] =
          (await SystemSettings.getValueOrFallback(
            { label: "custom_legal_templates" },
            []
          )) as LegalTemplate[];

        // Find the template to delete
        const templateIndex = templates.findIndex((t) => t.id === id);

        if (templateIndex === -1) {
          res.status(404).json({
            success: false,
            error: "Custom legal template not found.",
          });
          return;
        }

        // Store template info for logging
        const deletedTemplate = templates[templateIndex];

        // Remove the template
        templates.splice(templateIndex, 1);

        // Update the system setting
        await SystemSettings.updateSettings({
          custom_legal_templates: JSON.stringify(templates),
        });

        await EventLogs.logEvent("custom_legal_template_deleted", {
          category: deletedTemplate.category,
          documentType: deletedTemplate.documentType,
          userId: user.id,
        });

        res.status(200).json({
          success: true,
          message: "Custom legal template deleted successfully.",
        });
      } catch (error: unknown) {
        console.error(
          "Error deleting custom legal template:",
          (error as Error).message
        );
        res.status(500).json({
          success: false,
          error: "Failed to delete custom legal template.",
        });
      }
    }
  );

  /**
   * GET /system/legal-templates-list
   * List all legal templates
   */
  router.get(
    "/system/legal-templates-list",
    legalTemplateScopeGuard("system"),
    async (req: Request, res: Response): Promise<void> => {
      try {
        // Check if user is admin, manager, or superuser
        const user = await userFromSession(req);
        if (
          !user ||
          (user.role !== "admin" &&
            user.role !== "manager" &&
            user.role !== "superuser")
        ) {
          res.status(403).json({
            success: false,
            error:
              "Unauthorized. Admin, manager, or superuser access required.",
          });
          return;
        }

        // Get custom legal templates using getValueOrFallback
        const templates: LegalTemplate[] =
          (await SystemSettings.getValueOrFallback(
            { label: "custom_legal_templates" },
            []
          )) as LegalTemplate[];

        res.status(200).json({
          success: true,
          templates: templates,
        });
      } catch (error: unknown) {
        console.error(
          "Error fetching legal templates list:",
          (error as Error).message
        );
        res.status(500).json({
          success: false,
          error: "Failed to fetch legal templates list.",
        });
      }
    }
  );

  /**
   * DELETE /system/delete-legal-template/:templateId
   * Delete a legal template by ID
   */
  router.delete(
    "/system/delete-legal-template/:templateId",
    legalTemplateScopeGuard("system"),
    async (req: Request, res: Response): Promise<void> => {
      try {
        // Check if user is admin, manager, or superuser
        const user = await userFromSession(req);
        if (
          !user ||
          (user.role !== "admin" &&
            user.role !== "manager" &&
            user.role !== "superuser")
        ) {
          res.status(403).json({
            success: false,
            error:
              "Unauthorized. Admin, manager, or superuser access required.",
          });
          return;
        }

        const { templateId } = req.params;

        // Get existing custom legal templates using getValueOrFallback
        const templates: LegalTemplate[] =
          (await SystemSettings.getValueOrFallback(
            { label: "custom_legal_templates" },
            []
          )) as LegalTemplate[];

        // Find the template to delete
        const templateIndex = templates.findIndex((t) => t.id === templateId);

        if (templateIndex === -1) {
          res.status(404).json({
            success: false,
            error: "Legal template not found.",
          });
          return;
        }

        // Store template info for logging
        const deletedTemplate = templates[templateIndex];

        // Remove the template
        templates.splice(templateIndex, 1);

        // Update the system setting
        await SystemSettings.updateSettings({
          custom_legal_templates: JSON.stringify(templates),
        });

        await EventLogs.logEvent("legal_template_deleted", {
          templateId,
          category: deletedTemplate.category,
          documentType: deletedTemplate.documentType,
          userId: user.id,
        });

        res.status(200).json({
          success: true,
          message: "Legal template deleted successfully.",
        });
      } catch (error: unknown) {
        console.error(
          "Error deleting legal template:",
          (error as Error).message
        );
        res.status(500).json({
          success: false,
          error: "Failed to delete legal template.",
        });
      }
    }
  );

  /**
   * POST /system/custom-legal-templates-toggle
   * Toggle custom legal templates on/off
   */
  router.post(
    "/system/custom-legal-templates-toggle",
    legalTemplateScopeGuard("system"),
    async (req: Request, res: Response): Promise<void> => {
      try {
        // Check if user is admin, manager, or superuser
        const user = await userFromSession(req);
        if (
          !user ||
          (user.role !== "admin" &&
            user.role !== "manager" &&
            user.role !== "superuser")
        ) {
          res.status(403).json({
            success: false,
            error:
              "Unauthorized. Admin, manager, or superuser access required.",
          });
          return;
        }

        const { enabled } = req.body;
        if (typeof enabled !== "boolean") {
          res.status(400).json({
            success: false,
            error: "Enabled must be a boolean value.",
          });
          return;
        }

        await SystemSettings.updateSettings({
          custom_legal_templates_enabled: enabled.toString(),
        });

        await EventLogs.logEvent("custom_legal_templates_toggled", {
          enabled,
          userId: user.id,
        });

        res.status(200).json({
          success: true,
          message: `Custom legal templates ${enabled ? "enabled" : "disabled"} successfully.`,
          enabled,
        });
      } catch (error: unknown) {
        console.error(
          "Error toggling custom legal templates:",
          (error as Error).message
        );
        res.status(500).json({
          success: false,
          error: "Failed to toggle custom legal templates.",
        });
      }
    }
  );

  /**
   * GET /system/custom-legal-templates-setting
   * Get custom legal templates enabled setting
   */
  router.get(
    "/system/custom-legal-templates-setting",
    async (req: Request, res: Response): Promise<void> => {
      try {
        const enabledSetting = await SystemSettings.get({
          label: "custom_legal_templates_enabled",
        });

        const enabled = enabledSetting?.value === "true";

        res.status(200).json({
          success: true,
          enabled,
        });
      } catch (error: unknown) {
        console.error(
          "Error getting custom legal templates setting:",
          (error as Error).message
        );
        res.status(500).json({
          success: false,
          error: "Failed to get custom legal templates setting.",
        });
      }
    }
  );
}

export { customLegalTemplatesEndpoints, initializeCustomLegalTemplates };
export type { LegalTemplate, CustomInput, LegalTemplateResponse };
