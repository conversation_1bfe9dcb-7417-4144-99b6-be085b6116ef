/**
 * Endpoints for managing user custom AI settings
 */
import { Request, Response, Router } from "express";
import { reqBody, userFromSession } from "../utils/http";
import { validatedRequest } from "../utils/middleware/validatedRequest";
import prisma from "../utils/prisma";
import { User } from "../models/user";
import SystemSettings from "../models/systemSettings";

// Interface definitions for user custom AI settings endpoints
interface UserCustomAISettings {
  id: number;
  custom_ai_userselected: boolean;
  custom_ai_selected_engine: string;
}

interface SystemSetting {
  label: string;
  value: string | null;
}

interface CustomAISettingsResponse {
  success: boolean;
  settings?: {
    id: number;
    custom_ai_userselected: boolean;
    custom_ai_selected_engine: string;
    activeConfiguration: boolean;
    custom_model_reference?: string;
    [key: string]: unknown;
  };
  message?: string;
  error?: string;
  updates?: Partial<UserCustomAISettings>;
}

interface UpdateCustomAIRequest extends Request {
  body: {
    custom_ai_userselected?: boolean;
    custom_ai_selected_engine?: string;
  };
}

interface AuthenticatedUser {
  id: number;
  username: string;
  role: string;
}

function userCustomAiSettingsEndpoints(router: Router): void {
  if (!router) {
    console.error(
      "Router object is undefined in userCustomAiSettingsEndpoints"
    );
    return;
  }

  /**
   * Get the user's custom AI settings
   * Returns the user's custom_ai_userselected and custom_ai_selected_engine fields,
   * along with any related custom AI settings from the system_settings table
   */
  const getUserCustomAiSettings = async (
    request: Request,
    response: Response
  ): Promise<void> => {
    console.log("Custom AI settings endpoint hit");
    try {
      const user = (await userFromSession(
        request,
        response
      )) as AuthenticatedUser | null;
      if (!user || !user.id) {
        response.status(401).json({
          success: false,
          error: "Unauthorized",
        } as CustomAISettingsResponse);
        return;
      }

      // Get the user's custom AI settings from the database
      const userSettings = await prisma.users.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          custom_ai_userselected: true,
          custom_ai_selected_engine: true,
        },
      });

      if (!userSettings) {
        response.status(404).json({
          success: false,
          error: "User not found",
        } as CustomAISettingsResponse);
        return;
      }

      // Get any related custom AI settings from system_settings
      const cuaiSuffix = userSettings.custom_ai_selected_engine || "_CUAI";
      const allSystemSettings: SystemSetting[] = await SystemSettings.where();

      // Filter system settings to only include those related to the user's custom AI engine
      // And exclude default values that haven't been explicitly set
      const relevantSettings: Record<string, string> = {};
      for (const setting of allSystemSettings) {
        // Only include settings that are explicitly set (not defaults)
        // and related to the current custom AI engine suffix
        if (
          setting.label.endsWith(cuaiSuffix) &&
          setting.value !== null &&
          setting.value !== undefined &&
          setting.value !== ""
        ) {
          // Skip settings with default values that might trigger false positives
          if (
            setting.label === `LLM_PROVIDER${cuaiSuffix}` &&
            setting.value === "none"
          ) {
            continue;
          }

          relevantSettings[setting.label] = setting.value;
        }
      }

      // Add the custom model reference for better frontend display
      // Format must match systemSettings.js: custom_model_reference_2, custom_model_reference_3, etc.
      const modelRefKey =
        cuaiSuffix === "_CUAI"
          ? "custom_model_reference"
          : `custom_model_reference_${cuaiSuffix.replace("_CUAI", "")}`;
      const modelRef = allSystemSettings.find(
        (s: SystemSetting) => s.label === modelRefKey
      );
      if (modelRef && modelRef.value) {
        relevantSettings.custom_model_reference = modelRef.value;
      }

      // Check if we have a valid provider configuration
      const hasValidProvider =
        !!relevantSettings[`LLM_PROVIDER${cuaiSuffix}`] &&
        relevantSettings[`LLM_PROVIDER${cuaiSuffix}`] !== "none";

      // If user has custom_ai_userselected but no valid provider is configured,
      // this could lead to errors, so we'll set an additional flag
      const activeConfiguration =
        hasValidProvider || !userSettings.custom_ai_userselected;

      response.status(200).json({
        success: true,
        settings: {
          ...userSettings,
          ...relevantSettings,
          activeConfiguration,
        },
      } as CustomAISettingsResponse);
    } catch (error: unknown) {
      console.error("Error fetching user custom AI settings:", error);
      response.status(500).json({
        success: false,
        error: "Failed to fetch user custom AI settings",
      } as CustomAISettingsResponse);
    }
  };

  // Register both paths for backward compatibility
  router.get(
    "/system/custom-ai-settings",
    [validatedRequest],
    getUserCustomAiSettings
  );
  router.get(
    "/user/custom-ai-settings",
    [validatedRequest],
    getUserCustomAiSettings
  );

  /**
   * Update the user's custom AI settings
   * Allows updating the custom_ai_userselected and custom_ai_selected_engine fields
   */
  const updateUserCustomAiSettings = async (
    request: UpdateCustomAIRequest,
    response: Response
  ): Promise<void> => {
    try {
      const user = (await userFromSession(
        request,
        response
      )) as AuthenticatedUser | null;
      if (!user || !user.id) {
        response.status(401).json({
          success: false,
          error: "Unauthorized",
        } as CustomAISettingsResponse);
        return;
      }

      const { custom_ai_userselected, custom_ai_selected_engine } =
        reqBody(request);
      const updates: Partial<UserCustomAISettings> = {};

      // Validate and process custom_ai_userselected if provided
      if (custom_ai_userselected !== undefined) {
        updates.custom_ai_userselected =
          User.validations.custom_ai_userselected(custom_ai_userselected);
      }

      // Validate and process custom_ai_selected_engine if provided
      if (custom_ai_selected_engine !== undefined) {
        // Use the validation function we added to the User model
        updates.custom_ai_selected_engine =
          User.validations.custom_ai_selected_engine(custom_ai_selected_engine);
      }

      // If no valid updates, return an error
      if (Object.keys(updates).length === 0) {
        response.status(400).json({
          success: false,
          error: "No valid updates provided",
        } as CustomAISettingsResponse);
        return;
      }

      // Update the user in the database
      try {
        await prisma.users.update({
          where: { id: user.id },
          data: updates,
        });

        response.status(200).json({
          success: true,
          message: "Custom AI settings updated successfully",
          updates,
        } as CustomAISettingsResponse);
      } catch (dbError: unknown) {
        console.error("Database error updating user settings:", dbError);
        response.status(500).json({
          success: false,
          error: "Failed to update user settings in database",
        } as CustomAISettingsResponse);
      }
    } catch (error: unknown) {
      console.error("Error updating user custom AI settings:", error);
      response.status(500).json({
        success: false,
        error: "Failed to update user custom AI settings",
      } as CustomAISettingsResponse);
    }
  };

  // Register both paths for backward compatibility
  router.post(
    "/system/custom-ai-settings",
    [validatedRequest],
    updateUserCustomAiSettings
  );
  router.post(
    "/user/custom-ai-settings",
    [validatedRequest],
    updateUserCustomAiSettings
  );
}

export { userCustomAiSettingsEndpoints };
export type { UserCustomAISettings, CustomAISettingsResponse };
