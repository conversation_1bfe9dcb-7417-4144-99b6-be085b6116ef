/**
 * Endpoints for managing user custom system prompts
 */
import { Request, Response, Router } from "express";
import { reqBody, userFromSession } from "../utils/http";
import { validatedRequest } from "../utils/middleware/validatedRequest";
import prisma from "../utils/prisma";
import { User } from "../models/user";

import { getLQAPromptsSync } from "../utils/i18n/prompts";

// Interface definitions for user custom system prompt endpoints
interface UserCustomSystemPromptData {
  id: number;
  custom_system_prompt: string | null;
}

interface CustomSystemPromptResponse {
  success: boolean;
  customPrompt?: string;
  defaultPrompt?: string;
  message?: string;
  error?: string;
}

interface UpdateCustomSystemPromptRequest extends Request {
  body: {
    customPrompt: string;
  };
}

interface AuthenticatedUser {
  id: number;
  username: string;
  role: string;
}

function userCustomSystemPromptEndpoints(router: Router): void {
  if (!router || typeof router.get !== "function") {
    console.error(
      "Router object is not a valid router in userCustomSystemPromptEndpoints"
    );
    return;
  }

  /**
   * Get the user's custom system prompt
   * Returns the user's custom_system_prompt field and the default system prompt
   */
  router.get(
    "/user/custom-system-prompt",
    [validatedRequest],
    async (request: Request, response: Response): Promise<void> => {
      try {
        const user = (await userFromSession(
          request,
          response
        )) as AuthenticatedUser | null;

        if (!user || !user.id) {
          response.status(401).json({
            success: false,
            error: "Unauthorized",
          } as CustomSystemPromptResponse);
          return;
        }

        // Get the user's custom system prompt
        const userSettings: UserCustomSystemPromptData | null = await User._get(
          { id: user.id }
        );

        if (!userSettings) {
          response.status(404).json({
            success: false,
            error: "User not found",
          } as CustomSystemPromptResponse);
          return;
        }

        const lqaPrompts = getLQAPromptsSync();
        response.status(200).json({
          success: true,
          customPrompt: userSettings.custom_system_prompt || "",
          defaultPrompt: lqaPrompts.systemPrompt,
        } as CustomSystemPromptResponse);
      } catch (error: unknown) {
        console.error(
          "Error in GET /user/custom-system-prompt:",
          (error as Error).message
        );
        response.status(500).json({
          success: false,
          error: "Internal server error",
        } as CustomSystemPromptResponse);
      }
    }
  );

  /**
   * Update the user's custom system prompt
   * Allows setting or clearing the custom_system_prompt field
   */
  router.post(
    "/user/custom-system-prompt",
    [validatedRequest],
    async (
      request: UpdateCustomSystemPromptRequest,
      response: Response
    ): Promise<void> => {
      try {
        const user = (await userFromSession(
          request,
          response
        )) as AuthenticatedUser | null;
        if (!user || !user.id) {
          response.status(401).json({
            success: false,
            error: "Unauthorized",
          } as CustomSystemPromptResponse);
          return;
        }

        const { customPrompt } = reqBody(request);

        // Validate the custom prompt using the User model validation
        let validatedPrompt: string | null;
        try {
          validatedPrompt = User.custom_system_prompt(customPrompt);
        } catch (validationError: unknown) {
          response.status(400).json({
            success: false,
            error: (validationError as Error).message,
          } as CustomSystemPromptResponse);
          return;
        }

        // Update the user in the database
        try {
          await prisma.users.update({
            where: { id: user.id },
            data: { custom_system_prompt: validatedPrompt },
          });

          response.status(200).json({
            success: true,
            message: validatedPrompt
              ? "Custom system prompt updated successfully"
              : "Custom system prompt cleared successfully",
            customPrompt: validatedPrompt || "",
          } as CustomSystemPromptResponse);
        } catch (dbError: unknown) {
          console.error(
            "Database error updating user custom system prompt:",
            dbError
          );
          response.status(500).json({
            success: false,
            error: "Failed to update custom system prompt in database",
          } as CustomSystemPromptResponse);
        }
      } catch (error: unknown) {
        console.error("Error updating user custom system prompt:", error);
        response.status(500).json({
          success: false,
          error: "Failed to update user custom system prompt",
        } as CustomSystemPromptResponse);
      }
    }
  );

  /**
   * Delete the user's custom system prompt
   * Clears the custom_system_prompt field
   */
  router.delete(
    "/user/custom-system-prompt",
    [validatedRequest],
    async (request: Request, response: Response): Promise<void> => {
      try {
        const user = (await userFromSession(
          request,
          response
        )) as AuthenticatedUser | null;
        if (!user || !user.id) {
          response.status(401).json({
            success: false,
            error: "Unauthorized",
          } as CustomSystemPromptResponse);
          return;
        }

        // Clear the custom system prompt
        try {
          await prisma.users.update({
            where: { id: user.id },
            data: { custom_system_prompt: null },
          });

          response.status(200).json({
            success: true,
            message: "Custom system prompt cleared successfully",
          } as CustomSystemPromptResponse);
        } catch (dbError: unknown) {
          console.error(
            "Database error clearing user custom system prompt:",
            dbError
          );
          response.status(500).json({
            success: false,
            error: "Failed to clear custom system prompt in database",
          } as CustomSystemPromptResponse);
        }
      } catch (error: unknown) {
        console.error("Error clearing user custom system prompt:", error);
        response.status(500).json({
          success: false,
          error: "Failed to clear user custom system prompt",
        } as CustomSystemPromptResponse);
      }
    }
  );
}

export { userCustomSystemPromptEndpoints };
export type { UserCustomSystemPromptData, CustomSystemPromptResponse };
