import { Request, Response, Router } from "express";
import { workspace_threads as PrismaWorkspaceThread } from "@prisma/client";
import { multiUserMode, userFromSession, reqBody } from "../utils/http";
import { validatedRequest } from "../utils/middleware/validatedRequest";
import { Telemetry } from "../models/telemetry";
import {
  flexUserRoleValid,
  ROLES,
} from "../utils/middleware/multiUserProtected";
import { EventLogs } from "../models/eventLogs";
import { WorkspaceThread } from "../models/workspaceThread";
import {
  validWorkspaceSlug,
  validWorkspaceAndThreadSlug,
} from "../utils/middleware/validWorkspace";
import { WorkspaceChats } from "../models/workspaceChats";
import { convertToChatHistory } from "../utils/helpers/chat/responses";
import { Workspace } from "../models/workspace";
import { ExpressApp } from "../types/shared";
import { FilteredUser } from "../types/models";
import { ThreadShare } from "../models/threadShare";
import { User } from "../models/user";
import { ChatMessage, ChatRecord } from "../utils/helpers/chat/responses";

// Interface definitions for workspace thread endpoints
interface WorkspaceThreadParams {
  slug: string;
  threadSlug?: string;
  threadId?: string;
}

interface ThreadData {
  id: number;
  name: string;
  slug: string;
  workspace_id: number;
  user_id?: number;
  createdAt: Date;
  lastUpdatedAt: Date;
  sharedWithOrg: boolean;
}

interface WorkspaceData {
  id: number;
  name: string;
  slug: string;
  user_id?: number;
}

interface UserData {
  id: number;
  username: string;
  role: string;
  organizationId?: number;
}

interface ChatData {
  id: number;
  prompt: string;
  response: string;
  createdAt: Date;
  workspaceThreadId: number;
  user_id?: number;
}

interface ThreadResponse {
  thread?: ThreadData;
  message?: string;
  error?: string;
}

interface ThreadsListResponse {
  threads?: ThreadData[];
  error?: string;
}

interface ChatHistoryResponse {
  history?: ChatMessage[];
  error?: string;
}

// Request body interfaces
interface ThreadUpdateBody {
  name?: string;
  slug?: string;
}

interface ThreadShareBody {
  username: string;
}

interface InsertHistoryMessage {
  role: string;
  content: string;
  userId?: number | null;
}

interface InsertHistoryBody {
  messages: InsertHistoryMessage[];
}

// New interfaces for the missing endpoints
interface ThreadOrderUpdateBody {
  threadIds: number[];
}

interface ThreadPinResponse {
  message: string;
  thread?: ThreadData;
}

// Extended Response interface with typed locals
interface ValidatedResponse extends Response {
  locals: {
    workspace: WorkspaceData;
    thread?: ThreadData;
    user?: FilteredUser;
    multiUserMode?: boolean;
  };
}

function workspaceThreadEndpoints(app: ExpressApp, apiRouter?: Router): void {
  if (!app) return;

  const router = apiRouter || app;

  router.post(
    "/workspace/:slug/thread/new",
    [validatedRequest, flexUserRoleValid([ROLES.all]), validWorkspaceSlug],
    async (
      request: Request<Pick<WorkspaceThreadParams, "slug">>,
      response: Response
    ): Promise<void> => {
      try {
        const user: FilteredUser | null = await userFromSession(
          request,
          response
        );
        const { slug = null } = request.params;
        const workspace = (response as ValidatedResponse).locals.workspace;

        if (!user) {
          response.status(401).json({ error: "Unauthorized" });
          return;
        }
        const { thread, message } = await WorkspaceThread.new(
          workspace as Parameters<typeof WorkspaceThread.new>[0],
          user?.id
        );

        let LLMSelection: string;
        let Embedder: string;
        let VectorDbSelection: string;

        if (slug === "document-drafting") {
          LLMSelection = process.env.LLM_PROVIDER_DD || "openai";
          Embedder = process.env.EMBEDDING_ENGINE_DD || "inherit";
          VectorDbSelection = process.env.VECTOR_DB_DD || "lancedb";
        } else {
          LLMSelection = process.env.LLM_PROVIDER || "openai";
          Embedder = process.env.EMBEDDING_ENGINE || "inherit";
          VectorDbSelection = process.env.VECTOR_DB || "lancedb";
        }

        await Telemetry.sendTelemetry(
          "workspace_thread_created",
          {
            multiUserMode: multiUserMode(response),
            LLMSelection,
            Embedder,
            VectorDbSelection,
            TTSSelection: process.env.TTS_PROVIDER || "native",
          },
          user?.id ? String(user.id) : undefined
        );

        await EventLogs.logEvent(
          "workspace_thread_created",
          {
            workspaceName: workspace?.name || "Unknown Workspace",
          },
          user?.id
        );

        response.status(200).json({ thread, message } as ThreadResponse);
      } catch (e) {
        const error = e as Error;
        console.error(error.message, error);
        response.sendStatus(500).end();
      }
    }
  );

  router.get(
    "/workspace/:slug/threads",
    [validatedRequest, flexUserRoleValid([ROLES.all]), validWorkspaceSlug],
    async (
      request: Request<Pick<WorkspaceThreadParams, "slug">>,
      response: Response
    ): Promise<void> => {
      try {
        const user: FilteredUser | null = await userFromSession(
          request,
          response
        );
        const workspace = (response as ValidatedResponse).locals.workspace;

        if (!user) {
          response.status(401).json({ error: "Unauthorized" });
          return;
        }

        // Always start with threads the user owns
        const userThreads: ThreadData[] = (await WorkspaceThread.where({
          workspace_id: workspace.id,
          user_id: user.id,
        })) as unknown as ThreadData[];

        let allThreads = [...userThreads];

        // Add shared threads if in multi-user mode
        if (multiUserMode(response)) {
          // Get thread IDs shared with the user
          const sharedThreadIds = await ThreadShare.getThreadIdsSharedWithUser(
            user.id
          );

          // Fetch the actual thread data for shared threads
          const sharedThreads: ThreadData[] = [];
          for (const threadId of sharedThreadIds) {
            const thread = await WorkspaceThread.get({ id: threadId });
            if (thread && thread.workspace_id === workspace.id) {
              sharedThreads.push(thread as ThreadData);
            }
          }

          // Filter out duplicates and add shared threads
          const userThreadIds = new Set(
            userThreads.map((t: ThreadData) => t.id)
          );
          const uniqueSharedThreads = sharedThreads.filter(
            (t: ThreadData) => !userThreadIds.has(t.id)
          );

          allThreads = [...allThreads, ...uniqueSharedThreads];
        }

        // Sort threads by last updated (most recent first)
        allThreads.sort(
          (a: ThreadData, b: ThreadData) =>
            new Date(b.lastUpdatedAt).getTime() -
            new Date(a.lastUpdatedAt).getTime()
        );

        response
          .status(200)
          .json({ threads: allThreads } as ThreadsListResponse);
      } catch (e) {
        const error = e as Error;
        console.error(error.message, error);
        response.sendStatus(500).end();
      }
    }
  );

  router.delete(
    "/workspace/:slug/thread/:threadSlug",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.all]),
      validWorkspaceAndThreadSlug,
    ],
    async (
      request: Request<Pick<WorkspaceThreadParams, "slug" | "threadSlug">>,
      response: Response
    ): Promise<void> => {
      try {
        const user: FilteredUser | null = await userFromSession(
          request,
          response
        );
        const workspace = (response as ValidatedResponse).locals.workspace;
        const thread = (response as ValidatedResponse).locals.thread;

        if (!thread) {
          response.status(404).json({ error: "Thread not found" });
          return;
        }

        // Check if user owns the thread or has appropriate permissions
        if (thread.user_id !== user?.id && user?.role !== ROLES.admin) {
          response
            .status(403)
            .json({ error: "Unauthorized to delete this thread" });
          return;
        }

        await WorkspaceThread.delete({ id: thread.id });
        await EventLogs.logEvent(
          "workspace_thread_deleted",
          {
            workspaceName: workspace?.name || "Unknown Workspace",
            threadName: thread.name,
          },
          user?.id
        );

        response.sendStatus(200).end();
      } catch (e) {
        const error = e as Error;
        console.error(error.message, error);
        response.sendStatus(500).end();
      }
    }
  );

  router.delete(
    "/workspace/:slug/threads",
    [validatedRequest, flexUserRoleValid([ROLES.all]), validWorkspaceSlug],
    async (
      request: Request<Pick<WorkspaceThreadParams, "slug">>,
      response: Response
    ): Promise<void> => {
      try {
        const user: FilteredUser | null = await userFromSession(
          request,
          response
        );
        const workspace = (response as ValidatedResponse).locals.workspace;

        if (!user) {
          response.status(401).json({ error: "Unauthorized" });
          return;
        }

        await WorkspaceThread.delete({
          workspace_id: workspace.id,
          user_id: user.id,
        });
        await EventLogs.logEvent(
          "workspace_threads_deleted",
          {
            workspaceName: workspace?.name || "Unknown Workspace",
          },
          user?.id
        );

        response.sendStatus(200).end();
      } catch (e) {
        const error = e as Error;
        console.error(error.message, error);
        response.sendStatus(500).end();
      }
    }
  );

  router.get(
    "/workspace/:slug/thread/:threadSlug/chats",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.all]),
      validWorkspaceAndThreadSlug,
    ],
    async (
      request: Request<Pick<WorkspaceThreadParams, "slug" | "threadSlug">>,
      response: Response
    ): Promise<void> => {
      try {
        const user: FilteredUser | null = await userFromSession(
          request,
          response
        );
        const thread = (response as ValidatedResponse).locals.thread;

        if (!thread) {
          response.status(404).json({ error: "Thread not found" });
          return;
        }

        // Check if user has access to this thread
        const hasAccess =
          thread.user_id === user?.id ||
          (await ThreadShare.hasAccess(thread.id, user?.id || 0)) ||
          user?.role === ROLES.admin;

        if (!hasAccess) {
          response
            .status(403)
            .json({ error: "Unauthorized to access this thread" });
          return;
        }

        const chats = await WorkspaceChats.forThread(thread.id);
        const history = await convertToChatHistory({}, chats as ChatRecord[]);

        response.status(200).json({ history } as ChatHistoryResponse);
      } catch (e) {
        const error = e as Error;
        console.error(error.message, error);
        response.sendStatus(500).end();
      }
    }
  );

  router.post(
    "/workspace/:slug/thread/:threadSlug/update",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.all]),
      validWorkspaceAndThreadSlug,
    ],
    async (
      request: Request<
        Pick<WorkspaceThreadParams, "slug" | "threadSlug">,
        unknown,
        ThreadUpdateBody
      >,
      response: Response
    ): Promise<void> => {
      try {
        const user: FilteredUser | null = await userFromSession(
          request,
          response
        );
        const { name = null, slug = null } = reqBody(
          request
        ) as ThreadUpdateBody;
        const thread = (response as ValidatedResponse).locals.thread;

        if (!thread) {
          response.status(404).json({ error: "Thread not found" });
          return;
        }

        // Check if user owns the thread
        if (thread.user_id !== user?.id && user?.role !== ROLES.admin) {
          response
            .status(403)
            .json({ error: "Unauthorized to update this thread" });
          return;
        }

        const updates: Partial<ThreadData> = {};
        if (name) updates.name = name;
        if (slug) updates.slug = slug;

        const updatedThread = await WorkspaceThread.update(
          thread as PrismaWorkspaceThread,
          updates
        );

        response
          .status(200)
          .json({ thread: updatedThread.thread } as ThreadResponse);
      } catch (e) {
        const error = e as Error;
        console.error(error.message, error);
        response.sendStatus(500).end();
      }
    }
  );

  router.delete(
    "/workspace/:slug/thread/:threadSlug/chats",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.all]),
      validWorkspaceAndThreadSlug,
    ],
    async (
      request: Request<Pick<WorkspaceThreadParams, "slug" | "threadSlug">>,
      response: Response
    ): Promise<void> => {
      try {
        const user: FilteredUser | null = await userFromSession(
          request,
          response
        );
        const thread = (response as ValidatedResponse).locals.thread;

        if (!thread) {
          response.status(404).json({ error: "Thread not found" });
          return;
        }

        // Check if user owns the thread
        if (thread.user_id !== user?.id && user?.role !== ROLES.admin) {
          response
            .status(403)
            .json({ error: "Unauthorized to clear this thread" });
          return;
        }

        await WorkspaceChats.delete({ workspaceThreadId: thread.id });

        response.sendStatus(200).end();
      } catch (e) {
        const error = e as Error;
        console.error(error.message, error);
        response.sendStatus(500).end();
      }
    }
  );

  router.post(
    "/workspace/:slug/thread/:threadSlug/share",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.all]),
      validWorkspaceAndThreadSlug,
    ],
    async (
      request: Request<
        Pick<WorkspaceThreadParams, "slug" | "threadSlug">,
        unknown,
        ThreadShareBody
      >,
      response: Response
    ): Promise<void> => {
      try {
        const user: FilteredUser | null = await userFromSession(
          request,
          response
        );
        const { username } = reqBody(request) as ThreadShareBody;
        const thread = (response as ValidatedResponse).locals.thread;
        const workspace = (response as ValidatedResponse).locals.workspace;

        if (!thread) {
          response.status(404).json({ error: "Thread not found" });
          return;
        }

        // Check if user owns the thread
        if (thread.user_id !== user?.id && user?.role !== ROLES.admin) {
          response
            .status(403)
            .json({ error: "Unauthorized to share this thread" });
          return;
        }

        if (!username) {
          response.status(400).json({ error: "Username is required" });
          return;
        }

        // Find the user to share with
        const targetUser = await User.get({ username });

        if (!targetUser) {
          response.status(404).json({ error: "User not found" });
          return;
        }

        // Share the thread
        await ThreadShare.create(thread.id, targetUser.id);

        await EventLogs.logEvent(
          "workspace_thread_shared",
          {
            workspaceName: workspace?.name || "Unknown Workspace",
            threadName: thread.name,
            sharedWith: username,
          },
          user?.id
        );

        response.status(200).json({ message: "Thread shared successfully" });
      } catch (e) {
        const error = e as Error;
        console.error(error.message, error);
        response.sendStatus(500).end();
      }
    }
  );

  router.post(
    "/workspace/:slug/thread/:threadSlug/fork",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.all]),
      validWorkspaceAndThreadSlug,
    ],
    async (
      request: Request<Pick<WorkspaceThreadParams, "slug" | "threadSlug">>,
      response: Response
    ): Promise<void> => {
      try {
        const user: FilteredUser | null = await userFromSession(
          request,
          response
        );
        const thread = (response as ValidatedResponse).locals.thread;
        const workspace = (response as ValidatedResponse).locals.workspace;

        if (!thread) {
          response.status(404).json({ error: "Thread not found" });
          return;
        }

        // Check if user has access to this thread
        const hasAccess =
          thread.user_id === user?.id ||
          (await ThreadShare.hasAccess(thread.id, user?.id || 0)) ||
          user?.role === ROLES.admin;

        if (!hasAccess) {
          response
            .status(403)
            .json({ error: "Unauthorized to fork this thread" });
          return;
        }

        // Create a forked thread
        const forkedThread = await WorkspaceThread.fork(
          thread as PrismaWorkspaceThread,
          user?.id
        );

        await EventLogs.logEvent(
          "workspace_thread_forked",
          {
            workspaceName: workspace?.name || "Unknown Workspace",
            originalThreadName: thread.name,
            forkedThreadName: forkedThread.name,
          },
          user?.id
        );

        response.status(200).json({ thread: forkedThread } as ThreadResponse);
      } catch (e) {
        const error = e as Error;
        console.error(error.message, error);
        response.sendStatus(500).end();
      }
    }
  );

  router.get(
    "/workspace/:slug/thread/:threadId",
    [validatedRequest, flexUserRoleValid([ROLES.all])],
    async (request: Request, response: Response): Promise<void> => {
      try {
        const user: FilteredUser | null = await userFromSession(
          request,
          response
        );
        const { slug, threadId } = request.params;

        if (!slug || !threadId) {
          response.status(400).json({
            error: "Workspace slug and thread ID are required.",
          });
          return;
        }

        // Get workspace
        const workspace = await Workspace.get({ slug });
        if (!workspace) {
          response.status(404).json({
            error: "Workspace not found.",
          });
          return;
        }

        // Get thread by ID
        const thread = await WorkspaceThread.get({
          id: parseInt(threadId),
          workspace_id: workspace.id,
        });

        if (!thread) {
          response.status(404).json({
            error: "Thread not found.",
          });
          return;
        }

        // Check if user has access to this thread
        const hasAccess =
          thread.user_id === user?.id ||
          (await ThreadShare.hasAccess(thread.id, user?.id || 0)) ||
          user?.role === ROLES.admin;

        if (!hasAccess) {
          response.status(403).json({
            error: "Unauthorized to access this thread.",
          });
          return;
        }

        response.status(200).json({ thread } as ThreadResponse);
      } catch (e: unknown) {
        const error = e as Error;
        console.error(
          `Error fetching thread ${request.params.threadId}:`,
          error
        );
        response.status(500).json({
          error: "Internal server error occurred while fetching thread.",
        });
      }
    }
  );

  router.post(
    "/workspace/:slug/thread/:threadSlug/insert-history",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.all]),
      validWorkspaceAndThreadSlug,
    ],
    async (
      request: Request<
        Pick<WorkspaceThreadParams, "slug" | "threadSlug">,
        unknown,
        InsertHistoryBody
      >,
      response: Response
    ): Promise<void> => {
      try {
        const user: FilteredUser | null = await userFromSession(
          request,
          response
        );
        const { messages } = reqBody(request) as InsertHistoryBody;
        const thread = (response as ValidatedResponse).locals.thread;
        const workspace = (response as ValidatedResponse).locals.workspace;

        if (!thread) {
          response.status(404).json({ error: "Thread not found" });
          return;
        }

        // Check if user owns the thread or has appropriate permissions
        if (thread.user_id !== user?.id && user?.role !== ROLES.admin) {
          response
            .status(403)
            .json({ error: "Unauthorized to modify this thread" });
          return;
        }

        // Validate messages
        if (!messages || !Array.isArray(messages) || messages.length === 0) {
          response.status(400).json({ error: "No messages provided" });
          return;
        }

        // Validate message structure
        for (const msg of messages) {
          if (!msg.role || !msg.content) {
            response
              .status(400)
              .json({ error: "Each message must have role and content" });
            return;
          }
          if (!["user", "assistant", "system"].includes(msg.role)) {
            response.status(400).json({
              error: "Invalid role. Must be user, assistant, or system",
            });
            return;
          }
        }

        // Prepare chat data for bulk creation (used only for typing)
        const _chatsData = messages.map((msg) => ({
          workspaceId: workspace.id,
          prompt: msg.role === "user" ? msg.content : "",
          response:
            msg.role === "assistant" ? { text: msg.content } : { text: "" },
          user: user ? { id: user.id } : null,
          threadId: thread.id,
          include: true,
        }));

        // Create chat entries in pairs (user + assistant)
        const chatPairs: typeof _chatsData = [];
        for (let i = 0; i < messages.length; i += 2) {
          const userMsg = messages[i];
          const assistantMsg = messages[i + 1];

          if (userMsg && userMsg.role === "user") {
            const chatData = {
              workspaceId: workspace.id,
              prompt: userMsg.content,
              response:
                assistantMsg && assistantMsg.role === "assistant"
                  ? { text: assistantMsg.content, type: "chat" }
                  : { text: "[No response]", type: "chat" },
              user: user ? { id: user.id } : null,
              threadId: thread.id,
              include: true,
            };
            chatPairs.push(chatData);
          }
        }

        if (chatPairs.length === 0) {
          response.status(400).json({ error: "No valid message pairs found" });
          return;
        }

        // Insert all chat pairs
        const result = await WorkspaceChats.bulkCreate(chatPairs);

        if (!result.chats) {
          response
            .status(500)
            .json({ error: result.message || "Failed to insert chat history" });
          return;
        }

        // The thread's lastUpdatedAt will be automatically updated by the database
        // when new chats are added

        // Log the event
        await EventLogs.logEvent(
          "workspace_thread_history_imported",
          {
            workspaceName: workspace?.name || "Unknown Workspace",
            threadName: thread.name,
            messageCount: result.chats.length,
          },
          user?.id
        );

        response.status(200).json({
          success: true,
          message: "Chat history imported successfully",
          count: result.chats.length,
        });
      } catch (e) {
        const error = e as Error;
        console.error("Error inserting chat history:", error);
        response.status(500).json({
          error: "Failed to insert chat history",
          details: error.message,
        });
      }
    }
  );

  /**
   * POST /workspace/:slug/threads/update-order
   * Update the display order of threads within a workspace
   */
  router.post(
    "/workspace/:slug/threads/update-order",
    [validatedRequest, flexUserRoleValid([ROLES.all]), validWorkspaceSlug],
    async (
      request: Request<
        Pick<WorkspaceThreadParams, "slug">,
        unknown,
        ThreadOrderUpdateBody
      >,
      response: Response
    ): Promise<void> => {
      try {
        const user: FilteredUser | null = await userFromSession(
          request,
          response
        );
        const { threadIds } = reqBody(request) as ThreadOrderUpdateBody;
        const workspace = (response as ValidatedResponse).locals.workspace;

        if (!user) {
          response.status(401).json({ error: "Unauthorized" });
          return;
        }

        if (!threadIds || !Array.isArray(threadIds) || threadIds.length === 0) {
          response.status(400).json({ error: "Thread IDs array is required" });
          return;
        }

        // Validate that all thread IDs are numbers
        if (!threadIds.every((id) => typeof id === "number" && id > 0)) {
          response.status(400).json({ error: "Invalid thread ID format" });
          return;
        }

        // Verify that all threads exist and belong to the workspace
        const threads = await Promise.all(
          threadIds.map((id) =>
            WorkspaceThread.get({ id, workspace_id: workspace.id })
          )
        );

        if (threads.some((thread) => !thread)) {
          response.status(404).json({ error: "One or more threads not found" });
          return;
        }

        // Check if user owns all threads or has admin role
        const allThreadsOwnedByUser = threads.every(
          (thread) => thread!.user_id === user.id || user.role === ROLES.admin
        );

        if (!allThreadsOwnedByUser) {
          response.status(403).json({
            error: "Unauthorized to reorder threads owned by other users",
          });
          return;
        }

        // NOTE: Database schema update required
        // The workspace_threads table needs an 'order' field (INTEGER) to support this functionality
        // For now, we'll simulate the operation and log the event

        // TODO: When database schema is updated, implement actual ordering:
        // await Promise.all(
        //   threadIds.map((threadId, index) =>
        //     prisma.workspace_threads.update({
        //       where: { id: threadId },
        //       data: { order: index + 1 }
        //     })
        //   )
        // );

        await EventLogs.logEvent(
          "workspace_threads_reordered",
          {
            workspaceName: workspace?.name || "Unknown Workspace",
            threadCount: threadIds.length,
          },
          user.id
        );

        response.status(200).json({
          message: "Thread order updated successfully",
          note: "Database schema update required for full implementation",
        });
      } catch (e) {
        const error = e as Error;
        console.error("Error updating thread order:", error.message, error);
        response.status(500).json({
          error: "Failed to update thread order",
          details: error.message,
        });
      }
    }
  );

  /**
   * POST /workspace/:slug/thread/:threadSlug/pin
   * Pin a thread to the top of the thread list
   */
  router.post(
    "/workspace/:slug/thread/:threadSlug/pin",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.all]),
      validWorkspaceAndThreadSlug,
    ],
    async (
      request: Request<Pick<WorkspaceThreadParams, "slug" | "threadSlug">>,
      response: Response
    ): Promise<void> => {
      try {
        const user: FilteredUser | null = await userFromSession(
          request,
          response
        );
        const thread = (response as ValidatedResponse).locals.thread;
        const workspace = (response as ValidatedResponse).locals.workspace;

        if (!thread) {
          response.status(404).json({ error: "Thread not found" });
          return;
        }

        // Check if user owns the thread or has admin role
        if (thread.user_id !== user?.id && user?.role !== ROLES.admin) {
          response.status(403).json({
            error: "Unauthorized to pin this thread",
          });
          return;
        }

        // NOTE: Database schema update required
        // The workspace_threads table needs a 'pinned' field (BOOLEAN DEFAULT false) to support this functionality
        // For now, we'll simulate the operation and log the event

        // TODO: When database schema is updated, implement actual pinning:
        // const updatedThread = await prisma.workspace_threads.update({
        //   where: { id: thread.id },
        //   data: { pinned: true }
        // });

        await EventLogs.logEvent(
          "workspace_thread_pinned",
          {
            workspaceName: workspace?.name || "Unknown Workspace",
            threadName: thread.name,
          },
          user?.id
        );

        response.status(200).json({
          message: "Thread pinned successfully",
          thread,
          note: "Database schema update required for full implementation",
        } as ThreadPinResponse);
      } catch (e) {
        const error = e as Error;
        console.error("Error pinning thread:", error.message, error);
        response.status(500).json({
          error: "Failed to pin thread",
          details: error.message,
        });
      }
    }
  );

  /**
   * DELETE /workspace/:slug/thread-bulk-delete
   * Delete multiple threads in bulk
   */
  router.delete(
    "/workspace/:slug/thread-bulk-delete",
    [validatedRequest, flexUserRoleValid([ROLES.all]), validWorkspaceSlug],
    async (
      request: Request<
        Pick<WorkspaceThreadParams, "slug">,
        {},
        { slugs: string[] }
      >,
      response: Response
    ): Promise<void> => {
      try {
        const user: FilteredUser | null = await userFromSession(
          request,
          response
        );
        const { slug } = request.params;
        const { slugs = [] } = reqBody(request);

        // Validate input
        if (!Array.isArray(slugs) || slugs.length === 0) {
          response.status(400).json({ error: "No thread slugs provided" });
          return;
        }

        const workspace = multiUserMode(response)
          ? user
            ? await Workspace.getWithUser(user, { slug })
            : null
          : await Workspace.get({ slug });

        if (!workspace) {
          response.status(404).json({ error: "Workspace not found" });
          return;
        }

        // Delete all threads with the provided slugs
        const deletePromises = slugs.map(async (threadSlug) => {
          try {
            const thread = await WorkspaceThread.get({
              slug: threadSlug,
              workspace_id: Number(workspace.id),
            });

            if (thread) {
              await WorkspaceThread.delete(thread);
              await EventLogs.logEvent(
                "workspace_thread_deleted",
                {
                  workspaceName: workspace.name || "Unknown Workspace",
                  threadName: thread.name,
                },
                user?.id
              );
            }
          } catch (error) {
            console.error(`Failed to delete thread ${threadSlug}:`, error);
          }
        });

        await Promise.all(deletePromises);

        response.status(200).json({ success: true });
      } catch (e) {
        const error = e as Error;
        console.error("Error deleting threads in bulk:", error.message, error);
        response.status(500).json({
          error: "Failed to delete threads",
          details: error.message,
        });
      }
    }
  );

  /**
   * DELETE /workspace/:slug/thread/:threadSlug/pin
   * Unpin a thread from the top of the thread list
   */
  router.delete(
    "/workspace/:slug/thread/:threadSlug/pin",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.all]),
      validWorkspaceAndThreadSlug,
    ],
    async (
      request: Request<Pick<WorkspaceThreadParams, "slug" | "threadSlug">>,
      response: Response
    ): Promise<void> => {
      try {
        const user: FilteredUser | null = await userFromSession(
          request,
          response
        );
        const thread = (response as ValidatedResponse).locals.thread;
        const workspace = (response as ValidatedResponse).locals.workspace;

        if (!thread) {
          response.status(404).json({ error: "Thread not found" });
          return;
        }

        // Check if user owns the thread or has admin role
        if (thread.user_id !== user?.id && user?.role !== ROLES.admin) {
          response.status(403).json({
            error: "Unauthorized to unpin this thread",
          });
          return;
        }

        // NOTE: Database schema update required
        // The workspace_threads table needs a 'pinned' field (BOOLEAN DEFAULT false) to support this functionality
        // For now, we'll simulate the operation and log the event

        // TODO: When database schema is updated, implement actual unpinning:
        // const updatedThread = await prisma.workspace_threads.update({
        //   where: { id: thread.id },
        //   data: { pinned: false }
        // });

        await EventLogs.logEvent(
          "workspace_thread_unpinned",
          {
            workspaceName: workspace?.name || "Unknown Workspace",
            threadName: thread.name,
          },
          user?.id
        );

        response.status(200).json({
          message: "Thread unpinned successfully",
          thread,
          note: "Database schema update required for full implementation",
        } as ThreadPinResponse);
      } catch (e) {
        const error = e as Error;
        console.error("Error unpinning thread:", error.message, error);
        response.status(500).json({
          error: "Failed to unpin thread",
          details: error.message,
        });
      }
    }
  );
}

export { workspaceThreadEndpoints };
export type {
  ThreadData,
  WorkspaceData,
  UserData,
  ChatData,
  ThreadResponse,
  ThreadsListResponse,
  ChatHistoryResponse,
  ThreadOrderUpdateBody,
  ThreadPinResponse,
};
