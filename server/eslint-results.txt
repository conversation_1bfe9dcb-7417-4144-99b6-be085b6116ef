
/Users/<USER>/GitHub/I_produktion/ISTLegal/server/__mocks__/@prisma/client.ts
  0:0  error  Parsing error: "parserOptions.project" has been provided for @typescript-eslint/parser.
The file was not found in any of the provided project(s): __mocks__/@prisma/client.ts

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/babel.config.js
  0:0  error  Parsing error: "parserOptions.project" has been provided for @typescript-eslint/parser.
The file was not found in any of the provided project(s): babel.config.js

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/babel.config.ts
  0:0  error  Parsing error: "parserOptions.project" has been provided for @typescript-eslint/parser.
The file was not found in any of the provided project(s): babel.config.ts

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/__tests__/workspaceDocuments.test.js
  1:17  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
  2:17  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
  3:40  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
  4:22  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
  5:23  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
  9:5   error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/admin.ts
    32:10  error    'ApiResponse' is defined but never used                                                                                                                                                                                                                                                                                                                                                    @typescript-eslint/no-unused-vars
    36:10  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
    46:18  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
    50:9   warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
    60:18  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
    74:12  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
    83:11  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
    93:15  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
    97:21  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   101:10  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   109:14  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   152:28  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   203:12  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   208:11  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   223:19  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   232:18  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   236:11  error    'UserRequest' is defined but never used                                                                                                                                                                                                                                                                                                                                                    @typescript-eslint/no-unused-vars
   266:24  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   414:24  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   489:13  error    'workspaces' is never reassigned. Use 'const' instead                                                                                                                                                                                                                                                                                                                                      prefer-const
   546:24  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   745:24  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   756:37  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   803:49  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   821:15  error    'embedder' is never reassigned. Use 'const' instead                                                                                                                                                                                                                                                                                                                                        prefer-const
   825:15  error    'setting' is never reassigned. Use 'const' instead                                                                                                                                                                                                                                                                                                                                         prefer-const
  1040:9   error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1042:24  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
  1177:9   error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1206:9   error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1319:9   error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1367:9   error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/api/admin/system.ts
   15:11  error    'DeepSearchSettings' is defined but never used                             @typescript-eslint/no-unused-vars
   32:11  error    'RexorApiSettings' is defined but never used                               @typescript-eslint/no-unused-vars
   71:23  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
   94:20  error    Do not access Object.prototype method 'hasOwnProperty' from target object  no-prototype-builtins
  126:21  error    Do not access Object.prototype method 'hasOwnProperty' from target object  no-prototype-builtins
  152:23  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
  173:23  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
  222:23  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/api/auth/index.ts
  5:11  error  An interface declaring no members is equivalent to its supertype  @typescript-eslint/no-empty-object-type

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/api/categories.ts
  180:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/api/document/index.ts
   53:11  error    'DocumentListResponse' is defined but never used  @typescript-eslint/no-unused-vars
   54:15  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
   71:20  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  225:19  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  269:19  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  318:19  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  371:19  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  476:19  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  618:19  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  675:19  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  725:19  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  790:19  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  849:51  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  894:19  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/api/docx-edit/index.ts
    1:8   error    'express' is defined but never used            @typescript-eslint/no-unused-vars
   22:3   error    'DocxUploadRequest' is defined but never used  @typescript-eslint/no-unused-vars
   79:15  error    'user' is assigned a value but never used      @typescript-eslint/no-unused-vars
  138:23  warning  Unexpected any. Specify a different type       @typescript-eslint/no-explicit-any
  244:23  warning  Unexpected any. Specify a different type       @typescript-eslint/no-explicit-any
  327:25  warning  Unexpected any. Specify a different type       @typescript-eslint/no-explicit-any
  358:23  warning  Unexpected any. Specify a different type       @typescript-eslint/no-explicit-any
  423:23  warning  Unexpected any. Specify a different type       @typescript-eslint/no-explicit-any
  488:43  error    A `require()` style import is forbidden        @typescript-eslint/no-require-imports
  517:23  warning  Unexpected any. Specify a different type       @typescript-eslint/no-explicit-any
  537:15  error    'user' is assigned a value but never used      @typescript-eslint/no-unused-vars
  612:25  warning  Unexpected any. Specify a different type       @typescript-eslint/no-explicit-any
  651:23  warning  Unexpected any. Specify a different type       @typescript-eslint/no-explicit-any
  697:23  warning  Unexpected any. Specify a different type       @typescript-eslint/no-explicit-any
  796:23  warning  Unexpected any. Specify a different type       @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/api/embed/index.ts
   67:64  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  144:62  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  223:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/api/index.ts
  17:58  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/api/openai/index.ts
   74:15  error    'LLMProvider' is never reassigned. Use 'const' instead  prefer-const
   88:19  warning  Unexpected any. Specify a different type                @typescript-eslint/no-explicit-any
  226:19  warning  Unexpected any. Specify a different type                @typescript-eslint/no-explicit-any
  293:19  warning  Unexpected any. Specify a different type                @typescript-eslint/no-explicit-any
  371:19  warning  Unexpected any. Specify a different type                @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/api/system/index.ts
    2:10  error    'EventLogs' is defined but never used                       @typescript-eslint/no-unused-vars
    7:3   error    'prepareWorkspaceChatsForExport' is defined but never used  @typescript-eslint/no-unused-vars
  186:11  error    'type' is never reassigned. Use 'const' instead             prefer-const
  187:11  error    'chatType' is never reassigned. Use 'const' instead         prefer-const
  201:28  warning  Unexpected any. Specify a different type                    @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/api/userManagement/index.ts
   92:19  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  130:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/api/workspace/index.ts
   27:14  warning  Unexpected any. Specify a different type           @typescript-eslint/no-explicit-any
   32:14  warning  Unexpected any. Specify a different type           @typescript-eslint/no-explicit-any
   36:15  warning  Unexpected any. Specify a different type           @typescript-eslint/no-explicit-any
   44:18  warning  Unexpected any. Specify a different type           @typescript-eslint/no-explicit-any
   48:14  warning  Unexpected any. Specify a different type           @typescript-eslint/no-explicit-any
   53:12  warning  Unexpected any. Specify a different type           @typescript-eslint/no-explicit-any
   79:12  warning  Unexpected any. Specify a different type           @typescript-eslint/no-explicit-any
  833:11  error    'LLMSelection' is assigned a value but never used  @typescript-eslint/no-unused-vars
  998:11  error    'LLMSelection' is assigned a value but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/api/workspaceThread/index.ts
  26:11  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  35:11  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  40:12  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  59:12  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/autoCodePrompt.ts
  21:11  error  'BatchAutoCodePromptRequest' is defined but never used   @typescript-eslint/no-unused-vars
  42:11  error  'BatchAutoCodePromptResponse' is defined but never used  @typescript-eslint/no-unused-vars
  51:11  error  'AutoCodeSettings' is defined but never used             @typescript-eslint/no-unused-vars
  59:11  error  'PreviewResponse' is defined but never used              @typescript-eslint/no-unused-vars

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/browserExtension.ts
  30:29  warning  Unexpected any. Specify a different type             @typescript-eslint/no-explicit-any
  35:29  warning  Unexpected any. Specify a different type             @typescript-eslint/no-explicit-any
  49:11  error    'CheckConnectionResponse' is defined but never used  @typescript-eslint/no-unused-vars
  51:16  warning  Unexpected any. Specify a different type             @typescript-eslint/no-explicit-any
  56:11  error    'ApiKeysResponse' is defined but never used          @typescript-eslint/no-unused-vars
  58:13  warning  Unexpected any. Specify a different type             @typescript-eslint/no-explicit-any
  62:11  error    'CreateApiKeyResponse' is defined but never used     @typescript-eslint/no-unused-vars
  67:11  error    'SuccessResponse' is defined but never used          @typescript-eslint/no-unused-vars
  72:11  error    'WorkspacesResponse' is defined but never used       @typescript-eslint/no-unused-vars
  73:16  warning  Unexpected any. Specify a different type             @typescript-eslint/no-explicit-any
  77:7   error    'getUser' is assigned a value but never used         @typescript-eslint/no-unused-vars

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/chat.ts
  192:19  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  336:27  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  375:27  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  399:19  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  432:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  533:34  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  541:32  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  556:19  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  558:51  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  574:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/document.ts
    77:9   warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
    81:29  error    A `require()` style import is forbidden                                                                                                                                                                                                                                                                                                                                                    @typescript-eslint/no-require-imports
    91:54  error    A `require()` style import is forbidden                                                                                                                                                                                                                                                                                                                                                    @typescript-eslint/no-require-imports
   102:9   error    'filesToTry' is never reassigned. Use 'const' instead                                                                                                                                                                                                                                                                                                                                      prefer-const
   476:9   error    'workspace' is never reassigned. Use 'const' instead                                                                                                                                                                                                                                                                                                                                       prefer-const
   591:46  error    'type' is assigned a value but never used                                                                                                                                                                                                                                                                                                                                                  @typescript-eslint/no-unused-vars
   595:11  error    'candidatePath' is never reassigned. Use 'const' instead                                                                                                                                                                                                                                                                                                                                   prefer-const
   763:42  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   804:42  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   813:51  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   966:18  error    'error' is defined but never used                                                                                                                                                                                                                                                                                                                                                          @typescript-eslint/no-unused-vars
   972:23  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
  1022:24  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1022:28  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1063:20  error    'error' is defined but never used                                                                                                                                                                                                                                                                                                                                                          @typescript-eslint/no-unused-vars
  1071:20  error    'error' is defined but never used                                                                                                                                                                                                                                                                                                                                                          @typescript-eslint/no-unused-vars
  1103:42  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1143:20  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1143:50  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1178:37  error    A `require()` style import is forbidden                                                                                                                                                                                                                                                                                                                                                    @typescript-eslint/no-require-imports
  1201:25  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1201:29  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1201:33  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1282:20  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1282:45  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1394:24  error    'e' is defined but never used                                                                                                                                                                                                                                                                                                                                                              @typescript-eslint/no-unused-vars

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/docxLlmProcessor.ts
  2:26  error  'BaseLLMProvider' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/embed/index.ts
  29:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  33:12  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/embedManagement.ts
  19:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/experimental/imported-agent-plugins.ts
  15:27  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/generateLegalTaskPrompt.ts
   56:18  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
  231:9   error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/mcpServers.ts
    3:31  error    A `require()` style import is forbidden   @typescript-eslint/no-require-imports
   10:8   error    'path' is defined but never used          @typescript-eslint/no-unused-vars
   75:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   97:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  124:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  149:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  163:24  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  209:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  239:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/news.ts
  152:31  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  199:63  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  212:26  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  212:34  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  238:45  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/recent-uploads.ts
   42:36  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   42:63  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   71:20  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   71:24  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  101:40  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
  101:67  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/requestLegalAssistance.ts
  66:24  error  The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/system.ts
    6:16  error    'uuidv4' is defined but never used                                         @typescript-eslint/no-unused-vars
   10:3   error    A `require()` style import is forbidden                                    @typescript-eslint/no-require-imports
   12:3   error    A `require()` style import is forbidden                                    @typescript-eslint/no-require-imports
   16:10  error    'viewLocalFiles' is defined but never used                                 @typescript-eslint/no-unused-vars
   16:26  error    'normalizePath' is defined but never used                                  @typescript-eslint/no-unused-vars
   16:41  error    'isWithin' is defined but never used                                       @typescript-eslint/no-unused-vars
   18:3   error    'purgeDocument' is defined but never used                                  @typescript-eslint/no-unused-vars
   19:3   error    'purgeFolder' is defined but never used                                    @typescript-eslint/no-unused-vars
   20:3   error    'cleanOldDocxSessionFiles' is defined but never used                       @typescript-eslint/no-unused-vars
   22:10  error    'getVectorDbClass' is defined but never used                               @typescript-eslint/no-unused-vars
   23:10  error    'updateENV' is defined but never used                                      @typescript-eslint/no-unused-vars
   23:21  error    'dumpENV' is defined but never used                                        @typescript-eslint/no-unused-vars
   23:30  error    'KEY_MAPPING' is defined but never used                                    @typescript-eslint/no-unused-vars
   27:3   error    'reqBody' is defined but never used                                        @typescript-eslint/no-unused-vars
   28:3   error    'makeJWT' is defined but never used                                        @typescript-eslint/no-unused-vars
   29:3   error    'userFromSession' is defined but never used                                @typescript-eslint/no-unused-vars
   30:3   error    'multiUserMode' is defined but never used                                  @typescript-eslint/no-unused-vars
   31:3   error    'queryParams' is defined but never used                                    @typescript-eslint/no-unused-vars
   35:10  error    'handleAssetUpload' is defined but never used                              @typescript-eslint/no-unused-vars
   35:29  error    'handlePfpUpload' is defined but never used                                @typescript-eslint/no-unused-vars
   39:10  error    'User' is defined but never used                                           @typescript-eslint/no-unused-vars
   40:10  error    'UserToken' is defined but never used                                      @typescript-eslint/no-unused-vars
   41:10  error    'Telemetry' is defined but never used                                      @typescript-eslint/no-unused-vars
   42:10  error    'PromptExamples' is defined but never used                                 @typescript-eslint/no-unused-vars
   43:10  error    'ApiKey' is defined but never used                                         @typescript-eslint/no-unused-vars
   44:10  error    'WorkspaceChats' is defined but never used                                 @typescript-eslint/no-unused-vars
   45:23  error    'AuditLog' is defined but never used                                       @typescript-eslint/no-unused-vars
   46:10  error    'SlashCommandPresets' is defined but never used                            @typescript-eslint/no-unused-vars
   47:10  error    'BrowserExtensionApiKey' is defined but never used                         @typescript-eslint/no-unused-vars
   48:10  error    'Workspace' is defined but never used                                      @typescript-eslint/no-unused-vars
   49:10  error    'Category' is defined but never used                                       @typescript-eslint/no-unused-vars
   50:10  error    'Feedback' is defined but never used                                       @typescript-eslint/no-unused-vars
   51:10  error    'EventLogs' is defined but never used                                      @typescript-eslint/no-unused-vars
   54:10  error    'validatedRequest' is defined but never used                               @typescript-eslint/no-unused-vars
   56:3   error    'flexUserRoleValid' is defined but never used                              @typescript-eslint/no-unused-vars
   57:3   error    'ROLES' is defined but never used                                          @typescript-eslint/no-unused-vars
   58:3   error    'isMultiUserSetup' is defined but never used                               @typescript-eslint/no-unused-vars
   62:10  error    'fetchPfp' is defined but never used                                       @typescript-eslint/no-unused-vars
   62:20  error    'determinePfpFilepath' is defined but never used                           @typescript-eslint/no-unused-vars
   64:3   error    'getDefaultFilenameLight' is defined but never used                        @typescript-eslint/no-unused-vars
   65:3   error    'getDefaultFilenameDark' is defined but never used                         @typescript-eslint/no-unused-vars
   66:3   error    'determineLogoLightFilepath' is defined but never used                     @typescript-eslint/no-unused-vars
   67:3   error    'determineLogoDarkFilepath' is defined but never used                      @typescript-eslint/no-unused-vars
   68:3   error    'fetchLogo' is defined but never used                                      @typescript-eslint/no-unused-vars
   69:3   error    'validFilenameLight' is defined but never used                             @typescript-eslint/no-unused-vars
   70:3   error    'validFilenameDark' is defined but never used                              @typescript-eslint/no-unused-vars
   71:3   error    'renameLogoFile' is defined but never used                                 @typescript-eslint/no-unused-vars
   72:3   error    'removeCustomLogoLight' is defined but never used                          @typescript-eslint/no-unused-vars
   73:3   error    'removeCustomLogoDark' is defined but never used                           @typescript-eslint/no-unused-vars
   74:3   error    'LOGO_LIGHT' is defined but never used                                     @typescript-eslint/no-unused-vars
   75:3   error    'LOGO_DARK' is defined but never used                                      @typescript-eslint/no-unused-vars
   79:10  error    'exportChatsAsType' is defined but never used                              @typescript-eslint/no-unused-vars
   83:3   error    'recoverAccount' is defined but never used                                 @typescript-eslint/no-unused-vars
   84:3   error    'resetPassword' is defined but never used                                  @typescript-eslint/no-unused-vars
   85:3   error    'generateRecoveryCodes' is defined but never used                          @typescript-eslint/no-unused-vars
   89:10  error    'EncryptionManager' is defined but never used                              @typescript-eslint/no-unused-vars
   92:8   error    'prisma' is defined but never used                                         @typescript-eslint/no-unused-vars
   95:10  error    'getLLMProvider' is defined but never used                                 @typescript-eslint/no-unused-vars
   97:3   error    'getManualTimeLLM' is defined but never used                               @typescript-eslint/no-unused-vars
   98:3   error    'getValidationLLM' is defined but never used                               @typescript-eslint/no-unused-vars
  102:10  error    't' is defined but never used                                              @typescript-eslint/no-unused-vars
  105:10  error    'getCustomModels' is defined but never used                                @typescript-eslint/no-unused-vars
  109:3   error    'DEFAULT_COMBINE_PROMPT' is defined but never used                         @typescript-eslint/no-unused-vars
  110:3   error    'DEFAULT_DOCUMENT_DRAFTING_PROMPT' is defined but never used               @typescript-eslint/no-unused-vars
  111:3   error    'DEFAULT_LEGAL_ISSUES_PROMPT' is defined but never used                    @typescript-eslint/no-unused-vars
  112:3   error    'DEFAULT_MEMO_PROMPT' is defined but never used                            @typescript-eslint/no-unused-vars
  116:34  error    A `require()` style import is forbidden                                    @typescript-eslint/no-require-imports
  117:9   error    'exportedLegalPrompts' is assigned a value but never used                  @typescript-eslint/no-unused-vars
  117:34  error    'LEGAL_DRAFTING_PROMPTS' is assigned a value but never used                @typescript-eslint/no-unused-vars
  119:10  error    'PROMPT_MAPPINGS' is defined but never used                                @typescript-eslint/no-unused-vars
  122:10  error    'DocumentManager' is defined but never used                                @typescript-eslint/no-unused-vars
  123:31  error    'Queue' is defined but never used                                          @typescript-eslint/no-unused-vars
  126:10  error    'CollectorApi' is defined but never used                                   @typescript-eslint/no-unused-vars
  129:22  error    'ApiResponse' is defined but never used                                    @typescript-eslint/no-unused-vars
  132:11  error    'SystemSettingsRequest' is defined but never used                          @typescript-eslint/no-unused-vars
  133:18  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
  136:11  error    'SystemSettingsResponse' is defined but never used                         @typescript-eslint/no-unused-vars
  137:14  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
  141:11  error    'VectorCountResponse' is defined but never used                            @typescript-eslint/no-unused-vars
  145:11  error    'UpdateEnvRequest' is defined but never used                               @typescript-eslint/no-unused-vars
  146:18  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
  149:11  error    'UpdateEnvResponse' is defined but never used                              @typescript-eslint/no-unused-vars
  150:29  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
  154:11  error    'RemoveDocumentsRequest' is defined but never used                         @typescript-eslint/no-unused-vars
  158:11  error    'RemoveDocumentsResponse' is defined but never used                        @typescript-eslint/no-unused-vars
  163:11  error    'LogoUploadRequest' is defined but never used                              @typescript-eslint/no-unused-vars
  168:19  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
  200:7   error    'upload' is assigned a value but never used                                @typescript-eslint/no-unused-vars
  218:7   error    'withRexorSettings' is assigned a value but never used                     @typescript-eslint/no-unused-vars
  235:10  error    'isValidApiKeyFormat' is defined but never used                            @typescript-eslint/no-unused-vars
  245:16  error    'handleRexorApiCall' is defined but never used                             @typescript-eslint/no-unused-vars
  247:13  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
  248:10  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
  249:12  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
  265:19  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
  274:26  error    'app' is defined but never used. Allowed unused args must match /^_/u      @typescript-eslint/no-unused-vars
  277:5   error    'DEFAULT_SYSTEM_PROMPT' is assigned a value but never used                 @typescript-eslint/no-unused-vars
  278:5   error    'DEFAULT_VALIDATION_PROMPT' is assigned a value but never used             @typescript-eslint/no-unused-vars
  279:5   error    'DEFAULT_VECTOR_SEARCH_TOP_N' is assigned a value but never used           @typescript-eslint/no-unused-vars
  280:7   error    A `require()` style import is forbidden                                    @typescript-eslint/no-require-imports
  282:5   error    'DEFAULT_CANVAS_SYSTEM_PROMPT' is assigned a value but never used          @typescript-eslint/no-unused-vars
  283:5   error    'DEFAULT_CANVAS_UPLOAD_SYSTEM_PROMPT' is assigned a value but never used   @typescript-eslint/no-unused-vars
  284:7   error    A `require()` style import is forbidden                                    @typescript-eslint/no-require-imports
  287:9   error    'DEFAULT_MANUAL_WORK_ESTIMATOR_PROMPT' is assigned a value but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/system/customLegalTemplates.ts
  144:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  205:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  343:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  501:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  589:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/system/systemLegalTemplates.ts
   14:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   25:20  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   38:20  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   92:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  153:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  216:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  254:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  279:54  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  286:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/system/userLegalTemplates.ts
   18:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   35:20  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   48:20  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   86:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  110:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  169:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  201:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  246:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  271:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  299:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/systemReport.ts
    9:26  error    'adminOnly' is defined but never used                                                                                                                                                                                                                                                                                                                                                      @typescript-eslint/no-unused-vars
   63:20  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   63:24  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   63:28  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   68:24  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   98:20  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   98:24  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  258:36  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  304:36  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  339:36  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  372:36  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  403:36  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "a(node:98466) ESLintRCWarning: You are using an eslintrc configuration file, which is deprecated and support will be removed in v10.0.0. Please migrate to an eslint.config.js file. See https://eslint.org/docs/latest/use/configure/migration-guide for details. An eslintrc configuration file is used because you have the ESLINT_USE_FLAT_CONFIG environment variable set to false. If you want to use an eslint.config.js file, remove the environment variable. If you want to find the location of the eslintrc configuration file, use the --debug flag.
(Use `node --trace-warnings ...` to show where the warning was created)
ny object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  437:36  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  527:36  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/upgradeDeepSearchPrompt.ts
   5:15  error    'BaseLLMProvider' is defined but never used  @typescript-eslint/no-unused-vars
  30:20  warning  Unexpected any. Specify a different type     @typescript-eslint/no-explicit-any
  86:19  warning  Unexpected any. Specify a different type     @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/upgradePrompt.ts
  139:10  error  'processResponse' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/userCustomAiSettings.ts
   31:20  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  161:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  235:27  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  242:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/userCustomSystemPrompt.ts
    9:10  error    'SystemSettings' is defined but never used  @typescript-eslint/no-unused-vars
   86:23  warning  Unexpected any. Specify a different type    @typescript-eslint/no-explicit-any
  129:35  warning  Unexpected any. Specify a different type    @typescript-eslint/no-explicit-any
  151:27  warning  Unexpected any. Specify a different type    @typescript-eslint/no-explicit-any
  161:23  warning  Unexpected any. Specify a different type    @typescript-eslint/no-explicit-any
  203:27  warning  Unexpected any. Specify a different type    @typescript-eslint/no-explicit-any
  213:23  warning  Unexpected any. Specify a different type    @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/userPromptLibrary.ts
  101:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  149:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  207:35  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  213:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  269:35  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  275:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  323:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/utils.ts
   97:12  error  'e' is defined but never used            @typescript-eslint/no-unused-vars
  200:28  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/workspaceDocuments.ts
   56:14  warning  Unexpected any. Specify a different type                @typescript-eslint/no-explicit-any
  134:23  warning  Unexpected any. Specify a different type                @typescript-eslint/no-explicit-any
  161:11  error    'starStatus' is never reassigned. Use 'const' instead   prefer-const
  162:11  error    'isFolder' is never reassigned. Use 'const' instead     prefer-const
  163:11  error    'forceUpdate' is never reassigned. Use 'const' instead  prefer-const
  287:29  warning  Unexpected any. Specify a different type                @typescript-eslint/no-explicit-any
  379:23  warning  Unexpected any. Specify a different type                @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/workspaceThreads.ts
    6:3   error    'safeJsonParse' is defined but never used        @typescript-eslint/no-unused-vars
   21:10  error    'users' is defined but never used                @typescript-eslint/no-unused-vars
   23:10  error    't' is defined but never used                    @typescript-eslint/no-unused-vars
  110:11  error    'ValidatedRequest' is defined but never used     @typescript-eslint/no-unused-vars
  178:19  warning  Unexpected any. Specify a different type         @typescript-eslint/no-explicit-any
  201:26  error    A `require()` style import is forbidden          @typescript-eslint/no-require-imports
  202:33  error    A `require()` style import is forbidden          @typescript-eslint/no-require-imports
  203:15  error    'userInfo' is assigned a value but never used    @typescript-eslint/no-unused-vars
  241:19  warning  Unexpected any. Specify a different type         @typescript-eslint/no-explicit-any
  261:17  error    'threadSlug' is assigned a value but never used  @typescript-eslint/no-unused-vars
  289:19  warning  Unexpected any. Specify a different type         @typescript-eslint/no-explicit-any
  322:19  warning  Unexpected any. Specify a different type         @typescript-eslint/no-explicit-any
  347:33  error    A `require()` style import is forbidden          @typescript-eslint/no-require-imports
  364:19  warning  Unexpected any. Specify a different type         @typescript-eslint/no-explicit-any
  407:19  warning  Unexpected any. Specify a different type         @typescript-eslint/no-explicit-any
  442:19  warning  Unexpected any. Specify a different type         @typescript-eslint/no-explicit-any
  485:26  error    A `require()` style import is forbidden          @typescript-eslint/no-require-imports
  494:33  error    A `require()` style import is forbidden          @typescript-eslint/no-require-imports
  512:19  warning  Unexpected any. Specify a different type         @typescript-eslint/no-explicit-any
  538:33  error    A `require()` style import is forbidden          @typescript-eslint/no-require-imports
  565:19  warning  Unexpected any. Specify a different type         @typescript-eslint/no-explicit-any
  610:33  error    A `require()` style import is forbidden          @typescript-eslint/no-require-imports
  624:19  warning  Unexpected any. Specify a different type         @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/workspaces.ts
     3:29  error    'NextFunction' is defined but never used                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-unused-vars
    60:18  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   141:15  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   172:9   error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   193:25  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   301:9   error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   384:17  error    'filename' is assigned a value but never used                                                                                                                                                                                                                                                                                                                                              @typescript-eslint/no-unused-vars
   387:44  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   408:31  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   474:47  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   475:36  error    A `require()` style import is forbidden                                                                                                                                                                                                                                                                                                                                                    @typescript-eslint/no-require-imports
   487:50  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   509:42  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   513:47  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   516:36  error    A `require()` style import is forbidden                                                                                                                                                                                                                                                                                                                                                    @typescript-eslint/no-require-imports
   558:42  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   562:47  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   565:36  error    A `require()` style import is forbidden                                                                                                                                                                                                                                                                                                                                                    @typescript-eslint/no-require-imports
   577:26  error    'error' is assigned a value but never used                                                                                                                                                                                                                                                                                                                                                 @typescript-eslint/no-unused-vars
   607:42  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   639:31  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   663:9   error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   688:33  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   699:33  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   739:14  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   763:13  error    A `require()` style import is forbidden                                                                                                                                                                                                                                                                                                                                                    @typescript-eslint/no-require-imports
   816:13  error    A `require()` style import is forbidden                                                                                                                                                                                                                                                                                                                                                    @typescript-eslint/no-require-imports
   848:13  error    A `require()` style import is forbidden                                                                                                                                                                                                                                                                                                                                                    @typescript-eslint/no-require-imports
   871:42  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   871:46  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   908:31  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
   928:42  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   928:46  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   957:31  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
  1011:42  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1011:46  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1034:42  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1034:46  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1068:42  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1074:47  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
  1132:42  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1138:47  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
  1159:42  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1168:47  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
  1199:58  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1207:44  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
  1236:47  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
  1295:42  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1331:42  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1340:47  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
  1365:35  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
  1409:42  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1418:47  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
  1433:35  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
  1497:47  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
  1691:11  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1692:11  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1760:31  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
  1767:31  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
  1798:9   error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1831:42  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  1836:47  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
  1867:50  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                                                                                                   @typescript-eslint/no-explicit-any
  1935:42  error    The `{}` ("empty object") type allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/index.ts
    7:1   error    Expected an assignment or function call and instead saw an expression  @typescript-eslint/no-unused-expressions
   12:19  error    'Express' is defined but never used                                    @typescript-eslint/no-unused-vars
   84:21  error    A `require()` style import is forbidden                                @typescript-eslint/no-require-imports
  164:29  error    A `require()` style import is forbidden                                @typescript-eslint/no-require-imports
  209:19  warning  Unexpected any. Specify a different type                               @typescript-eslint/no-explicit-any
  214:17  warning  Unexpected any. Specify a different type                               @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/jest.config.js
  0:0  error  Parsing error: "parserOptions.project" has been provided for @typescript-eslint/parser.
The file was not found in any of the provided project(s): jest.config.js

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/jest.config.ts
  0:0  error  Parsing error: "parserOptions.project" has been provided for @typescript-eslint/parser.
The file was not found in any of the provided project(s): jest.config.ts

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/jest.setup.js
  0:0  error  Parsing error: "parserOptions.project" has been provided for @typescript-eslint/parser.
The file was not found in any of the provided project(s): jest.setup.js

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/jest.setup.ts
  0:0  error  Parsing error: "parserOptions.project" has been provided for @typescript-eslint/parser.
The file was not found in any of the provided project(s): jest.setup.ts

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/jobs/__tests__/bulk-document-processor.test.ts
  0:0  error  Parsing error: "parserOptions.project" has been provided for @typescript-eslint/parser.
The file was not found in any of the provided project(s): jobs/__tests__/bulk-document-processor.test.ts

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/jobs/bulk-document-processor.ts
    1:10  error    'Document' is defined but never used              @typescript-eslint/no-unused-vars
    2:10  error    'Workspace' is defined but never used             @typescript-eslint/no-unused-vars
  398:17  error    'pageContent' is assigned a value but never used  @typescript-eslint/no-unused-vars
  568:27  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  569:27  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/Feedback.ts
   22:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   28:39  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   28:47  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   29:41  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   29:49  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   30:28  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   30:36  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   32:42  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   33:67  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   55:19  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   70:19  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   83:50  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   83:56  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   94:37  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   94:43  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  120:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  126:59  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  138:74  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  140:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  149:14  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  162:25  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  169:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  204:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  222:67  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  224:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/__tests__/apiKeys.test.js
  1:20  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
  2:16  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/__tests__/apiKeys.test.ts
  0:0  error  Parsing error: "parserOptions.project" has been provided for @typescript-eslint/parser.
The file was not found in any of the provided project(s): models/__tests__/apiKeys.test.ts

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/apiKeys.ts
   22:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   30:24  error    A `require()` style import is forbidden   @typescript-eslint/no-require-imports
   45:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   63:72  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   65:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   83:67  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   85:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  103:58  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  105:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  127:25  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  131:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  153:25  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  202:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/browserExtensionApiKey.ts
   20:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   63:24  error    A `require()` style import is forbidden   @typescript-eslint/no-require-imports
   81:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  122:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  139:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  165:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  199:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  220:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/cacheData.ts
  10:32  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  16:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  23:13  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  25:14  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  34:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  40:35  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  46:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  53:13  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  55:14  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  64:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  70:34  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  76:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/category.ts
   30:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   38:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   59:42  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   59:50  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   64:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   64:37  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  125:37  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  125:48  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  156:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  204:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  227:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  237:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  262:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  280:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  289:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/documentSyncQueue.ts
   30:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   43:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   87:16  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  151:28  error    A `require()` style import is forbidden   @typescript-eslint/no-require-imports
  158:19  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  177:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  192:28  error    A `require()` style import is forbidden   @typescript-eslint/no-require-imports
  198:19  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  205:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  223:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  237:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  257:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  273:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  283:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  318:14  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  319:33  error    A `require()` style import is forbidden   @typescript-eslint/no-require-imports

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/documentSyncRun.ts
  17:27  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  24:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  32:43  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  46:28  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/documents.ts
   16:18  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
   42:14  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
   58:14  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
   95:18  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  100:42  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  101:58  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  104:42  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  109:15  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  110:15  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  111:14  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  112:16  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  114:16  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  120:16  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  126:16  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  128:15  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  131:16  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  166:51  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  181:14  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  191:21  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  201:21  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  209:41  error    A `require()` style import is forbidden           @typescript-eslint/no-require-imports
  221:21  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  230:59  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  255:21  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  275:21  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  284:14  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  285:14  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  286:13  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  287:14  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  319:21  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  326:16  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  331:11  error    'VectorDb' is assigned a value but never used     @typescript-eslint/no-unused-vars
  332:72  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  343:11  error    A `require()` style import is forbidden           @typescript-eslint/no-require-imports
  361:23  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  378:16  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  384:13  error    'fileData' is assigned a value but never used     @typescript-eslint/no-unused-vars
  384:26  error    A `require()` style import is forbidden           @typescript-eslint/no-require-imports
  431:16  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  433:15  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  435:26  error    A `require()` style import is forbidden           @typescript-eslint/no-require-imports
  490:17  error    'pageContent' is assigned a value but never used  @typescript-eslint/no-unused-vars
  526:23  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  579:38  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  593:23  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  611:37  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  624:40  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  643:40  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  670:16  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  705:37  error    A `require()` style import is forbidden           @typescript-eslint/no-require-imports
  710:37  error    A `require()` style import is forbidden           @typescript-eslint/no-require-imports
  724:25  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  748:25  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  794:21  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  820:21  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  838:21  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  863:21  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  874:26  error    A `require()` style import is forbidden           @typescript-eslint/no-require-imports
  884:26  error    A `require()` style import is forbidden           @typescript-eslint/no-require-imports
  892:26  error    A `require()` style import is forbidden           @typescript-eslint/no-require-imports
  939:21  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/embedChats.ts
   37:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  101:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  126:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  149:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  166:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  178:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  198:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  229:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  241:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/embedConfig.ts
   52:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   60:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  119:39  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  119:60  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  217:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  234:20  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  245:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  260:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  278:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  290:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  308:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  332:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/eventLogs.ts
   12:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   22:30  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   36:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   56:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   76:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   98:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  116:17  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  121:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  133:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  145:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/invite.ts
   32:57  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
   43:18  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
   58:69  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
   63:24  error    A `require()` style import is forbidden                @typescript-eslint/no-require-imports
   82:21  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
   97:21  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  119:11  error    'usersArray' is never reassigned. Use 'const' instead  prefer-const
  149:33  error    A `require()` style import is forbidden                @typescript-eslint/no-require-imports
  150:37  error    A `require()` style import is forbidden                @typescript-eslint/no-require-imports
  152:25  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  155:23  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  159:19  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  167:21  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  185:71  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  187:21  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  205:66  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  207:21  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  225:57  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  227:21  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  249:25  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  253:21  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  262:14  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  295:52  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  319:21  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/linkedWorkspaces.ts
   16:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   55:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   78:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   96:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  113:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  129:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  139:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  148:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/newsMessage.ts
    3:3   error    'users' is defined but never used         @typescript-eslint/no-unused-vars
  150:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  169:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  182:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  240:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  253:24  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  288:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  301:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  337:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  362:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  384:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  423:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  506:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/organization.ts
  18:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/organizationLegalTemplate.ts
   10:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   18:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   57:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   70:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   78:65  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   91:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  102:34  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  118:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  132:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/passwordRecovery.ts
    4:15  error    'recovery_codes' is defined but never used         @typescript-eslint/no-unused-vars
    4:31  error    'password_reset_tokens' is defined but never used  @typescript-eslint/no-unused-vars
   24:18  warning  Unexpected any. Specify a different type           @typescript-eslint/no-explicit-any
   87:21  warning  Unexpected any. Specify a different type           @typescript-eslint/no-explicit-any
  105:21  warning  Unexpected any. Specify a different type           @typescript-eslint/no-explicit-any
  118:21  warning  Unexpected any. Specify a different type           @typescript-eslint/no-explicit-any
  131:21  warning  Unexpected any. Specify a different type           @typescript-eslint/no-explicit-any
  140:21  warning  Unexpected any. Specify a different type           @typescript-eslint/no-explicit-any
  170:21  warning  Unexpected any. Specify a different type           @typescript-eslint/no-explicit-any
  183:21  warning  Unexpected any. Specify a different type           @typescript-eslint/no-explicit-any
  192:21  warning  Unexpected any. Specify a different type           @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/promptExamples.ts
   37:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   65:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   89:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  120:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  139:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/slashCommandsPresets.ts
   37:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   75:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   91:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  116:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  139:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  155:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  167:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/systemLegalTemplate.ts
    9:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   17:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   54:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   62:59  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   73:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   81:59  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   94:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  105:34  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  121:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  133:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/systemReport.ts
  120:17  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  266:20  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  310:20  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  474:25  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  577:25  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  649:25  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  725:25  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  879:5   error    'rejectingUserId' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/systemSettings.ts
    16:29  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
   212:23  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
   223:46  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
   241:28  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
   244:29  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
   247:16  error    'e' is defined but never used                                              @typescript-eslint/no-unused-vars
   252:40  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
   266:43  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
   279:35  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
   291:45  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
   337:16  error    'e' is defined but never used                                              @typescript-eslint/no-unused-vars
   353:16  error    'e' is defined but never used                                              @typescript-eslint/no-unused-vars
   515:16  error    'e' is defined but never used                                              @typescript-eslint/no-unused-vars
   521:20  error    'e' is defined but never used                                              @typescript-eslint/no-unused-vars
   768:38  error    A `require()` style import is forbidden                                    @typescript-eslint/no-require-imports
  1704:17  error    Do not access Object.prototype method 'hasOwnProperty' from target object  no-prototype-builtins
  1846:28  error    A `require()` style import is forbidden                                    @typescript-eslint/no-require-imports
  2311:17  error    'connectionString' is assigned a value but never used                      @typescript-eslint/no-unused-vars

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/telemetry.ts
   6:11  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  11:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  27:17  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  64:24  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  66:25  error    A `require()` style import is forbidden   @typescript-eslint/no-require-imports

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/threadShare.ts
   71:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   86:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  108:14  error    'error' is defined but never used         @typescript-eslint/no-unused-vars
  108:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  126:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  168:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/user.ts
   13:3   error    'EventLogEntry' is defined but never used                                  @typescript-eslint/no-unused-vars
   36:26  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
   43:19  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
   47:18  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
   56:37  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
   59:31  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
   66:40  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
   78:35  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
   87:29  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
   98:50  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
   98:56  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
  106:9   error    Unexpected lexical declaration in case block                               no-case-declarations
  114:41  warning  Une