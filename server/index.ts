// Apply module patches for compatibility before loading anything else
// This provides a mock implementation of cron-validate to avoid circular dependencies
import "./utils/modulePatches";

import dotenv from "dotenv";

process.env.NODE_ENV === "development"
  ? dotenv.config({ path: `.env.${process.env.NODE_ENV}` })
  : dotenv.config();

// Set default for TS_NODE_PROJECT if not already set (development only)
// This ensures ts-node uses the correct TypeScript configuration during local development
// In production/containers, TypeScript is already compiled so this is not needed
if (!process.env.TS_NODE_PROJECT && process.env.NODE_ENV === "development") {
  process.env.TS_NODE_PROJECT = "tsconfig.dev.json";
}

import "./utils/logger";
import express from "express";
import cors from "cors";
import * as path from "path";
import { reqBody } from "./utils/http";
import { systemEndpoints } from "./endpoints/system";
import { workspaceEndpoints } from "./endpoints/workspaces";
import { chatEndpoints } from "./endpoints/chat";
import { embeddedEndpoints } from "./endpoints/embed";
import { embedManagementEndpoints } from "./endpoints/embedManagement";
import { getVectorDbClass } from "./utils/helpers";
import { adminEndpoints } from "./endpoints/admin";
import { apiAdminSystemEndpoints } from "./endpoints/api/admin/system";
import { rexorProxyEndpoints } from "./endpoints/api/system/rexorProxy";
import { inviteEndpoints } from "./endpoints/invite";
import { utilEndpoints } from "./endpoints/utils";
import { developerEndpoints } from "./endpoints/api";
import { extensionEndpoints } from "./endpoints/extensions";
import { bootHTTP, bootSSL } from "./utils/boot";
import { workspaceThreadEndpoints } from "./endpoints/workspaceThreads";
import { documentEndpoints } from "./endpoints/document";
import { agentWebsocket } from "./endpoints/agentWebsocket";
import { experimentalEndpoints } from "./endpoints/experimental";
import { browserExtensionEndpoints } from "./endpoints/browserExtension";
import { mcpServersEndpoints } from "./endpoints/mcpServers";
import { updatePrompts } from "./endpoints/upgradePrompt";
import { updateDeepSearchPrompts } from "./endpoints/upgradeDeepSearchPrompt";
import { workspaceDocumentEndpoints } from "./endpoints/workspaceDocuments";
import { addGenerateLegalTaskPromptEndpoint } from "./endpoints/generateLegalTaskPrompt";
import { addDocxLlmProcessorEndpoint } from "./endpoints/docxLlmProcessor";
import { Cron } from "croner";
import { cleanOldLogs } from "./utils/cleanLogs";
import { categoryEndpoints } from "./endpoints/api/categories";
import { addGetCDBDocumentationEndpoint } from "./endpoints/getCDBDocumentation";
import { userCustomAiSettingsEndpoints } from "./endpoints/userCustomAiSettings";
import { userCustomSystemPromptEndpoints } from "./endpoints/userCustomSystemPrompt";
import { userPromptLibraryEndpoints } from "./endpoints/userPromptLibrary";
import { userEndpoints } from "./endpoints/user";
import { recentUploadsEndpoints } from "./endpoints/recent-uploads";
import docxEditEndpoints from "./endpoints/api/docx-edit";
import {
  customLegalTemplatesEndpoints,
  initializeCustomLegalTemplates,
} from "./endpoints/system/customLegalTemplates";
import { organizationLegalTemplatesEndpoints } from "./endpoints/system/organizationLegalTemplates";
import { userLegalTemplatesEndpoints } from "./endpoints/system/userLegalTemplates";
import { systemLegalTemplatesEndpoints } from "./endpoints/system/systemLegalTemplates";
import { requestLegalAssistanceEndpoints } from "./endpoints/requestLegalAssistance";
import { newsEndpoints } from "./endpoints/news";
import { systemReportEndpoints } from "./endpoints/systemReport";
import { setupAutoCodePromptEndpoints } from "./endpoints/autoCodePrompt";
import { initialDataEndpoints } from "./endpoints/initialData";
import { ExpressApp } from "./types/shared";

const app = express() as ExpressApp;
const apiRouter = express.Router();
const FILE_LIMIT = "3GB";

app.use(cors({ origin: true }));

// Body parsing middleware - skip for multipart/form-data (handled by multer)
app.use((req, res, next) => {
  const contentType = req.headers["content-type"] || "";

  // Skip body parsing for multipart form data (file uploads) - let multer handle it
  if (contentType.includes("multipart/form-data")) {
    return next();
  }

  // Handle JSON content type specifically
  if (contentType.includes("application/json")) {
    express.json({ limit: FILE_LIMIT })(req, res, (err) => {
      if (err) {
        // JSON parsing failed - return 400 Bad Request
        return res.status(400).json({
          error: "Invalid JSON format",
          message: "Request body contains malformed JSON",
        });
      }
      next();
    });
  } else if (contentType.includes("application/x-www-form-urlencoded")) {
    // Handle URL-encoded content type specifically
    express.urlencoded({ limit: FILE_LIMIT, extended: true })(
      req,
      res,
      (err) => {
        if (err) {
          // URL-encoded parsing failed - return 400 Bad Request
          return res.status(400).json({
            error: "Invalid URL-encoded format",
            message: "Request body contains malformed URL-encoded data",
          });
        }
        next();
      }
    );
  } else {
    // For other content types, use text parsing
    express.text({ limit: FILE_LIMIT })(req, res, (textErr) => {
      if (textErr) {
        // If text parsing fails, just continue
        next();
      } else {
        next();
      }
    });
  }
});

if (process.env.NODE_ENV !== "test") {
  new Cron("0 0 * * *", () => {
    console.log("Running scheduled log cleanup...");
    cleanOldLogs();
  });
}

if (process.env.ENABLE_HTTPS) {
  const port = process.env.SERVER_PORT ? Number(process.env.SERVER_PORT) : 3001;
  bootSSL(app, port);
} else {
  // Load WebSockets in non-SSL mode
  try {
    const expressWs = require("@mintplex-labs/express-ws");
    expressWs.default(app);
  } catch (error) {
    console.error("Failed to load express-ws:", error);
  }
}

app.use("/api", apiRouter);

// Log registered routes in production for debugging
if (process.env.NODE_ENV === "production") {
  console.log("[PRODUCTION] API Router mounted at /api");
}

// =============================================================================
// ENDPOINTS THAT REQUIRE FULL EXPRESS APP (ExpressApp)
// =============================================================================
// These endpoints need WebSocket support, file uploads, or app-level middleware
//
// IMPORTANT: These endpoints MUST be passed the full Express app instance (not apiRouter)
// because they use features like:
// - WebSocket support (app.ws)
// - File upload handling with multer middleware
// - App-level middleware configuration
// - Direct route registration bypassing the /api prefix

// Core system endpoints that require full app context
systemEndpoints(app, apiRouter);
workspaceEndpoints(app, apiRouter);
workspaceThreadEndpoints(app, apiRouter);
chatEndpoints(app, apiRouter);
inviteEndpoints(app, apiRouter);
documentEndpoints(app, apiRouter);
initialDataEndpoints(app);

// WebSocket-dependent endpoints (MUST use app, not apiRouter)
agentWebsocket(app);

// =============================================================================
// ENDPOINTS THAT REQUIRE LEGACY EXPRESS (Express)
// =============================================================================
// These endpoints use the legacy Express interface

// Admin and management endpoints
adminEndpoints(app, apiRouter);
embedManagementEndpoints(app, apiRouter);
utilEndpoints(apiRouter);

// =============================================================================
// ENDPOINTS THAT CAN USE API ROUTER (Router)
// =============================================================================
// These endpoints work with standard Router functionality and are registered
// with the /api prefix. They only need basic HTTP routing capabilities:
// - Standard HTTP methods (GET, POST, PUT, DELETE)
// - Route parameters and query strings
// - Request/response handling
// - Express middleware (but not app-level configuration)

// User customization endpoints (can use apiRouter)
userCustomSystemPromptEndpoints(apiRouter);
userPromptLibraryEndpoints(apiRouter);
userEndpoints(apiRouter);

// Standard API endpoints that work with Router
extensionEndpoints(apiRouter);
experimentalEndpoints(apiRouter);
recentUploadsEndpoints(apiRouter);

// =============================================================================
// HYBRID ENDPOINTS (Both app and router)
// =============================================================================
developerEndpoints(app, apiRouter);

// Initialize custom legal templates setting
initializeCustomLegalTemplates();

// =============================================================================
// ADDITIONAL ENDPOINTS THAT REQUIRE FULL EXPRESS APP (ExpressApp)
// =============================================================================

// Legal templates endpoints requiring app context
organizationLegalTemplatesEndpoints(app);
requestLegalAssistanceEndpoints(apiRouter);
workspaceDocumentEndpoints(apiRouter);
newsEndpoints(app, apiRouter);
systemReportEndpoints(app, apiRouter);
setupAutoCodePromptEndpoints(apiRouter);

// API-level endpoints that require app context
apiAdminSystemEndpoints(app, apiRouter);
categoryEndpoints(apiRouter);
addDocxLlmProcessorEndpoint(app, apiRouter);
docxEditEndpoints(apiRouter);
mcpServersEndpoints(app, apiRouter);

// =============================================================================
// ADDITIONAL ENDPOINTS THAT CAN USE API ROUTER (Router)
// =============================================================================

// Template management endpoints
customLegalTemplatesEndpoints(apiRouter);
systemLegalTemplatesEndpoints(apiRouter);
userLegalTemplatesEndpoints(apiRouter);

// Prompt management endpoints
updatePrompts(apiRouter);
updateDeepSearchPrompts(apiRouter);
addGenerateLegalTaskPromptEndpoint(apiRouter);

// User settings endpoints
userCustomAiSettingsEndpoints(apiRouter);

// Rexor proxy endpoints
rexorProxyEndpoints(apiRouter);

// Documentation endpoints
addGetCDBDocumentationEndpoint(apiRouter);

// =============================================================================
// EXTERNALLY FACING ENDPOINTS (Direct app registration)
// =============================================================================
// These endpoints are registered directly on the app, not through the API router

// Externally facing embedder endpoints
embeddedEndpoints(app);

// Externally facing browser extension endpoints (plus internal API endpoints)
browserExtensionEndpoints(app, apiRouter);

// Conditional setup for static serving and MetaGenerator
if (process.env.NODE_ENV !== "development" && process.env.NODE_ENV !== "test") {
  console.log("[PRODUCTION] Setting up static serving and routes...");

  const { MetaGenerator } = require("./utils/boot/MetaGenerator");
  const IndexPage = new MetaGenerator();

  // In production, use absolute path for static files
  const staticPath =
    process.env.IST_LEGAL_RUNTIME === "docker"
      ? "/app/server/public"
      : path.resolve(__dirname, "public");

  // Use conditional static middleware to avoid conflicts with API routes
  app.use((req, res, next) => {
    // Skip static file serving for API routes
    if (req.path.startsWith("/api/")) {
      return next();
    }

    // Apply static file serving for non-API routes
    express.static(staticPath, {
      extensions: ["js"],
      setHeaders: (res) => {
        // Disable I-framing of entire site UI
        res.removeHeader("X-Powered-By");
        res.setHeader("X-Frame-Options", "DENY");
      },
      // Important: Don't serve index.html for API routes
      index: false,
    })(req, res, next);
  });

  app.get("/", function (_, response) {
    console.log("[PRODUCTION] Root route hit");
    IndexPage.generate(response);
  });

  app.get("/robots.txt", function (_, response) {
    response.type("text/plain");
    response.send("User-agent: *\nDisallow: /").end();
  });

  // Catch-all for SPA routing - serve frontend for all non-API routes
  app.all("*", function (request, response) {
    // Don't send 404 for API routes - those should have been handled by apiRouter
    if (request.path.startsWith("/api/")) {
      // Enhanced error logging with request context
      const errorContext = {
        timestamp: new Date().toISOString(),
        method: request.method,
        path: request.path,
        query: request.query,
        userAgent: request.headers["user-agent"],
        ip: request.ip || request.connection.remoteAddress,
        referer: request.headers.referer,
        // Add content type for debugging
        contentType: request.headers["content-type"],
      };

      console.error(
        "[PRODUCTION] Unhandled API route:",
        JSON.stringify(errorContext)
      );

      response.status(404).json({
        error: "API endpoint not found",
        path: request.path,
        method: request.method,
        timestamp: errorContext.timestamp,
      });
    } else {
      // For SPA routing: serve the frontend index.html for all non-API routes
      // This allows client-side routing (React Router) to handle paths like /login
      console.log("[PRODUCTION] SPA route hit:", request.path);
      IndexPage.generate(response);
    }
  });
} else if (process.env.NODE_ENV === "test") {
  console.log(
    "[TEST ENV] MetaGenerator and static file serving for production skipped."
  );

  // Catch-all 404 for test environment
  // Note: This only affects the main application, not unit tests that create their own Express instances
  app.all("*", function (_, response) {
    response.sendStatus(404);
  });
} else {
  // This is the existing NODE_ENV === "development" block
  // Debug route for development connections to vectorDBs
  apiRouter.post("/v/:command", async (request, response): Promise<void> => {
    try {
      const VectorDb = getVectorDbClass();
      const { command } = request.params;
      if (!Object.getOwnPropertyNames(VectorDb).includes(command)) {
        response.status(500).json({
          message: "invalid interface command",
          commands: Object.getOwnPropertyNames(VectorDb),
        });
        return;
      }

      try {
        const body = reqBody(request);
        const resBody = await (VectorDb as unknown as Record<string, Function>)[
          command
        ](body);
        response.status(200).json({ ...resBody });
        return;
      } catch (e: unknown) {
        // console.error(e)
        console.error(JSON.stringify(e));
        response.status(500).json({ error: (e as Error).message });
        return;
      }
    } catch (e: unknown) {
      console.error((e as Error).message, e);
      response.sendStatus(500);
      return;
    }
  });

  // Catch-all 404 for development
  app.all("*", function (_, response) {
    response.sendStatus(404);
  });
}

// In non-https mode we need to boot at the end since the server has not yet
// started and is `.listen`ing.
// bootHTTP is already conditional internally for NODE_ENV === 'test'
if (!process.env.ENABLE_HTTPS) {
  const port = process.env.SERVER_PORT ? Number(process.env.SERVER_PORT) : 3001;
  bootHTTP(app, port);
}

export default app; // Export the app instance for testing and other uses
