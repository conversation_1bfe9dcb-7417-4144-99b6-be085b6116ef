/**
 * Jest Configuration for GitHub Actions CI
 *
 * Optimized for:
 * - Complete test coverage
 * - Reliability over speed
 * - CI environment constraints
 *
 * Features:
 * - Conservative parallelization for CI
 * - Full type checking
 * - Coverage reporting
 * - Extended timeouts for slower tests
 */

const path = require("path");

module.exports = {
  preset: "ts-jest",
  testEnvironment: "node",
  rootDir: path.resolve(__dirname),
  testMatch: [
    "<rootDir>/endpoints/**/*.test.[jt]s",
    "<rootDir>/models/**/*.test.[jt]s",
    "<rootDir>/utils/**/*.test.[jt]s",
    "<rootDir>/tests/**/*.test.[jt]s",
  ],
  setupFilesAfterEnv: ["<rootDir>/tests/setup.ts", "<rootDir>/jest.setup.ts"],
  globalTeardown: "<rootDir>/tests/cleanup.ts",

  // CI-optimized settings
  maxWorkers: 1, // Sequential execution to avoid port conflicts
  workerIdleMemoryLimit: "1GB",

  // Full TypeScript checking in CI
  transform: {
    "^.+\\.tsx?$": [
      "ts-jest",
      {
        diagnostics: true,
        tsconfig: "tsconfig.test.json",
      },
    ],
  },

  // Module resolution
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/$1",
  },
  moduleDirectories: ["node_modules", "<rootDir>"],

  // Coverage collection for CI
  coverageDirectory: "<rootDir>/coverage",
  collectCoverageFrom: [
    "endpoints/**/*.{js,ts}",
    "models/**/*.{js,ts}",
    "utils/**/*.{js,ts}",
    "!**/*.test.{js,ts}",
    "!**/node_modules/**",
    "!**/dist/**",
  ],
  coverageThreshold: {
    global: {
      branches: 60,
      functions: 60,
      lines: 60,
      statements: 60,
    },
  },

  // Cache directory for CI
  cache: true,
  cacheDirectory: "<rootDir>/.jest-cache-ci",

  // No test ordering in CI - run all tests

  // Ignore patterns
  testPathIgnorePatterns: [
    "/node_modules/",
    "/dist/",
    "/__tests__/fixtures/",
    "\\.skip$",
    "/tests/e2e/",
    "tests/e2e/",
    "e2e/",
    "playwright/",
    "\\.e2e\\.test\\.",
    "\\.playwright\\.test\\.",
  ],

  // CI-specific settings
  detectOpenHandles: true,

  // Global timeout - moved to globalSetup
};
