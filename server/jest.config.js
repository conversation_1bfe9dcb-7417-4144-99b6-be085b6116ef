module.exports = {
  transform: {
    "^.+\\.tsx?$": [
      "ts-jest",
      {
        tsconfig: "tsconfig.test.json"
      }
    ],
    "^.+\\.jsx?$": "babel-jest"
  },
  testEnvironment: "node",
  maxWorkers: 1, // Limit to 1 worker to avoid Prisma client initialization issues
  moduleFileExtensions: ["ts", "tsx", "js", "jsx", "json"],
  testMatch: ["**/__tests__/**/*.[jt]s?(x)", "**/?(*.)+(spec|test).[jt]s?(x)"],
  testPathIgnorePatterns: [
    "/node_modules/",
    "/dist/",
    "\\.skip\\.ts$",
    "\\.skip\\.js$",
    "/e2e-playwright/"
  ],
  setupFiles: ["<rootDir>/tests/setup.ts"],
  globalTeardown: "<rootDir>/tests/cleanup.ts",
  collectCoverageFrom: [
    "**/*.{ts,tsx,js,jsx}",
    "!**/node_modules/**",
    "!**/coverage/**",
    "!**/dist/**",
    "!jest.config.js",
    "!jest.setup.js",
    "!**/tests/setup.ts",
    "!**/tests/cleanup.ts"
  ],
  setupFilesAfterEnv: ["<rootDir>/jest.setup.ts"],
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/$1"
  },
  transformIgnorePatterns: ["node_modules/(?!(.*\\.mjs$))"],
  // Performance optimizations
  testTimeout: 30000, // 30 second timeout per test
  forceExit: true, // Force exit when tests complete
  detectOpenHandles: true, // Detect handles that keep the process running
  workerIdleMemoryLimit: "1GB" // Limit worker memory
}
