/**
 * Jest Configuration for Local Development and Pre-commit Testing
 *
 * Optimized for:
 * - Fast feedback during development
 * - Pre-commit hooks
 * - Local debugging
 *
 * Features:
 * - Parallel execution with 75% CPU cores
 * - Type checking disabled for speed
 * - Smart test ordering
 * - 10-second timeout for fast tests
 */

const path = require("path");

module.exports = {
  preset: "ts-jest",
  testEnvironment: "node",
  rootDir: path.resolve(__dirname),
  testMatch: [
    "<rootDir>/endpoints/**/*.test.[jt]s",
    "<rootDir>/models/**/*.test.[jt]s",
    "<rootDir>/utils/**/*.test.[jt]s",
    "<rootDir>/tests/**/*.test.[jt]s",
  ],
  setupFilesAfterEnv: ["<rootDir>/tests/setup.ts", "<rootDir>/jest.setup.ts"],
  globalTeardown: "<rootDir>/tests/cleanup.ts",

  // Performance optimizations - use single worker to avoid port conflicts
  maxWorkers: 1,

  // TypeScript optimizations
  transform: {
    "^.+\\.tsx?$": [
      "ts-jest",
      {
        diagnostics: false,
        tsconfig: "tsconfig.test.json",
      },
    ],
  },

  // Module resolution
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/$1",
  },
  moduleDirectories: ["node_modules", "<rootDir>"],

  // Coverage disabled for speed

  // Cache for faster reruns
  cache: true,
  cacheDirectory: "<rootDir>/.jest-cache",

  // Test ordering - commented out due to deprecation
  // testSequencer: "<rootDir>/tests/testSequencer.js",

  // Ignore patterns
  testPathIgnorePatterns: [
    "/node_modules/",
    "/dist/",
    "/__tests__/fixtures/",
    "\\.slow\\.test\\.[jt]s$",
    "\\.skip$",
  ],

  // Detect open handles
  detectOpenHandles: false,
};
