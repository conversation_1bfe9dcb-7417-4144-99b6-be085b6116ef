/**
 * Jest Configuration for Local Development and Pre-commit Testing
 *
 * Optimized for:
 * - Fast feedback during development
 * - Pre-commit hooks
 * - Local debugging
 *
 * Features:
 * - Parallel execution with 75% CPU cores
 * - Type checking disabled for speed
 * - Smart test ordering
 * - 10-second timeout for fast tests
 */

const path = require("path");

module.exports = {
  preset: "ts-jest",
  testEnvironment: "node",
  rootDir: path.resolve(__dirname),
  testMatch: ["<rootDir>/**/*.test.[jt]s"],
  setupFilesAfterEnv: ["<rootDir>/tests/setup.ts", "<rootDir>/jest.setup.ts"],
  globalTeardown: "<rootDir>/tests/cleanup.ts",
  maxWorkers: "75%", // Use 75% of available cores for local development
  transform: {
    "^.+\\.[tj]s$": [
      "ts-jest",
      {
        diagnostics: false,
        tsconfig: "tsconfig.test.json",
      },
    ],
  },
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/$1",
  },
  moduleDirectories: ["node_modules", "<rootDir>"],
  cache: true,
  cacheDirectory: "<rootDir>/.jest-cache",
  testPathIgnorePatterns: [
    "/node_modules/",
    "/dist/",
    "/__tests__/fixtures/",
    "\\.slow\\.test\\.[jt]s$",
    "\\.skip$",
    "/tests/e2e/",
    "tests/e2e/",
    "e2e/",
    "playwright/",
    "\\.e2e\\.test\\.",
    "\\.playwright\\.test\\.",
  ],
  detectOpenHandles: false,
};
