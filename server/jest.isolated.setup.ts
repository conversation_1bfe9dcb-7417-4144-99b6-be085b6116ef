/**
 * Isolated Jest setup for mutation testing
 * This setup ensures proper test isolation when running multiple test files
 */

import * as path from "path";

// Set test environment variables
process.env.NODE_ENV = "test";
process.env.JWT_SECRET = "test-jwt-secret-for-testing-only";
process.env.DATABASE_URL = "file:./test.db";
process.env.STORAGE_DIR = path.join(__dirname, "./storage");

// Mock API keys for embedding engines
process.env.OPEN_AI_KEY = "test-openai-key";
process.env.OPENAI_API_KEY = "test-openai-key";
process.env.EMBEDDING_BASE_PATH = "http://localhost:8080";
process.env.EMBEDDING_MODEL_PREF = "test-model";
process.env.VECTOR_DB = "lancedb";
process.env.EMBEDDING_ENGINE = "native";

// Disable console output during tests to reduce noise
global.console = {
  ...console,
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
};

// Clear all mocks before each test
beforeEach(() => {
  jest.clearAllMocks();
  jest.resetModules();
});

// Restore all mocks after each test
afterEach(() => {
  jest.restoreAllMocks();
});

// Clean up after all tests
afterAll(() => {
  jest.clearAllMocks();
  jest.resetModules();
  jest.restoreAllMocks();
});
