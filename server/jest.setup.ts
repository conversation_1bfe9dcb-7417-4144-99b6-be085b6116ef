/* eslint-env jest */
/* global jest */

// Import global mocks FIRST before any other code
import "./tests/globalMocks";

// Global Jest setup file to prevent async operations after test teardown

// Set a reasonable test timeout
jest.setTimeout(60000); // Increased to 60 seconds for comprehensive cleanup

// Store original timer functions FIRST before any modifications
const originalSetTimeout = global.setTimeout;
const originalSetInterval = global.setInterval;
const originalClearTimeout = global.clearTimeout;
const originalClearInterval = global.clearInterval;

// Add a global timeout for test cleanup
global.setTimeout = ((callback: () => void, delay: number) => {
  // Limit timeout delays in tests to prevent hanging
  const maxDelay = 30000; // 30 seconds max
  const actualDelay = Math.min(delay, maxDelay);
  return originalSetTimeout(callback, actualDelay);
}) as typeof setTimeout;

// Clean up any pending timers after each test
afterEach(() => {
  // Restore original timer functions in case they were mocked
  global.setTimeout = originalSetTimeout;
  global.setInterval = originalSetInterval;
  global.clearTimeout = originalClearTimeout;
  global.clearInterval = originalClearInterval;

  jest.clearAllTimers();

  // Force cleanup of any EventEmitter instances that might cause hanging
  // This is critical for preventing test timeouts
  try {
    // Clear any process event listeners that tests might have added
    const eventNames = ["uncaughtException", "unhandledRejection", "warning"];
    eventNames.forEach((eventName) => {
      if (process.listenerCount(eventName) > 0) {
        process.removeAllListeners(eventName);
      }
    });
  } catch (error) {
    // Ignore cleanup errors
  }
});

// Force clean up after all tests complete
afterAll(async () => {
  // Clear all timers first
  jest.clearAllTimers();
  jest.useRealTimers();

  // Import and run Prisma cleanup
  try {
    const { cleanupPrisma } = await import("./tests/prismaCleanup");
    await cleanupPrisma();
  } catch (error) {
    console.log("Prisma cleanup skipped:", error);
  }

  // Force close all remaining handles that could prevent Jest from exiting
  try {
    // Close all active timers and intervals
    if (typeof global.clearInterval === "function") {
      // Clear any intervals that might still be running
      for (let i = 1; i < 10000; i++) {
        try {
          clearInterval(i);
          clearTimeout(i);
        } catch {
          // Ignore errors - interval might not exist
        }
      }
    }

    // Force exit any remaining process handlers
    if (process.listenerCount("SIGINT") > 0) {
      process.removeAllListeners("SIGINT");
    }
    if (process.listenerCount("SIGTERM") > 0) {
      process.removeAllListeners("SIGTERM");
    }
    if (process.listenerCount("SIGUSR2") > 0) {
      process.removeAllListeners("SIGUSR2");
    }
    if (process.listenerCount("exit") > 0) {
      process.removeAllListeners("exit");
    }
    if (process.listenerCount("beforeExit") > 0) {
      process.removeAllListeners("beforeExit");
    }

    // Force close any remaining file descriptors or streams
    if (process.stdin && typeof process.stdin.destroy === "function") {
      process.stdin.destroy();
    }
  } catch (error) {
    // Ignore cleanup errors
    console.log("Cleanup error (ignored):", error);
  }

  // Wait a bit to ensure all async operations complete
  await new Promise((resolve) => originalSetTimeout(resolve, 100));

  // Clear all mocks
  jest.clearAllMocks();
  jest.restoreAllMocks();

  // Force garbage collection if available
  if (global.gc) {
    global.gc();
  }
});

// Mock SystemReport's generateAutoCodingPrompt to prevent async operations after test teardown
jest.mock("./models/systemReport", () => {
  const originalModule = jest.requireActual("./models/systemReport");

  // Create a proper mock that prevents async operations
  const mockGenerateAutoCodingPrompt = jest.fn().mockImplementation(() => {
    // Return a resolved promise immediately to prevent async operations
    return Promise.resolve({
      success: false,
      error: "Mocked in test environment - no async operations",
    });
  });

  return {
    ...originalModule,
    SystemReport: {
      ...originalModule.SystemReport,
      generateAutoCodingPrompt: mockGenerateAutoCodingPrompt,
    },
  };
});

// Mock AutoCodingPromptGenerator to prevent it from running in tests
jest.mock("./utils/helpers/autoCodingPromptGenerator", () => ({
  __esModule: true,
  default: {
    generatePrompt: jest.fn().mockResolvedValue("mocked prompt"),
    clearCache: jest.fn(),
  },
}));

// Mock SlackNotifier to prevent async operations
jest.mock("./utils/notifications/slack", () => ({
  __esModule: true,
  default: {
    notifyNewReport: jest
      .fn()
      .mockResolvedValue({ success: true, skipped: true }),
    postAutoCodingPrompt: jest
      .fn()
      .mockResolvedValue({ success: true, skipped: true }),
    notifyNewMessage: jest
      .fn()
      .mockResolvedValue({ success: true, skipped: true }),
    notifyStatusUpdate: jest
      .fn()
      .mockResolvedValue({ success: true, skipped: true }),
    isEnabled: jest.fn().mockResolvedValue(false),
    isAutoCodingEnabled: jest.fn().mockResolvedValue(false),
  },
}));
