import * as fs from "fs";
import * as path from "path";
import { Workspace } from "../models/workspace";
import { CollectorA<PERSON> } from "../utils/collectorApi";
import { getUserDocumentPathName } from "../utils/files/multer";
import type { FilteredUser } from "../types/models";
import type { JobStatus } from "../types/jobs";

const BULK_UPLOAD_MAX_FILES = 5000;
const JOB_RETENTION_TIME = 60 * 60 * 1000; // 1 hour in milliseconds

interface FileUpload {
  originalname: string;
  path: string;
  size?: number;
  mimetype?: string;
}

interface ProcessedFileResult {
  success: boolean;
  originalName: string;
  documentPaths: string[];
}

interface FileError {
  filename: string;
  error: string;
}

interface BulkUploadJob {
  id: string;
  status: JobStatus;
  totalFiles: number;
  processedFiles: number;
  failedFiles: number;
  errors: FileError[];
  successfulFiles: string[]; // Track successful file paths for embedding
  startedAt: number | null;
  completedAt: number | null;
  cancelled: boolean;
}

class BulkUploadProcessor {
  static jobs = new Map<string, BulkUploadJob>();
  static cleanupInterval: NodeJS.Timeout | null = null;

  // Initialize cleanup interval
  static initCleanup(): void {
    if (!this.cleanupInterval) {
      this.cleanupInterval = setInterval(
        () => {
          this.cleanupOldJobs();
        },
        15 * 60 * 1000
      ); // Run cleanup every 15 minutes
    }
  }

  // Clean up old completed/failed jobs
  static cleanupOldJobs(): void {
    const now = Date.now();
    const jobsToDelete: string[] = [];

    for (const [jobId, job] of this.jobs.entries()) {
      // Only clean up completed, failed, or cancelled jobs
      if (["completed", "failed", "cancelled"].includes(job.status)) {
        const jobAge = now - (job.completedAt || job.startedAt || 0);
        if (jobAge > JOB_RETENTION_TIME) {
          jobsToDelete.push(jobId);
        }
      }
    }

    // Delete old jobs
    jobsToDelete.forEach((jobId) => {
      this.jobs.delete(jobId);
      console.log(`[BulkUploadProcessor] Cleaned up old job: ${jobId}`);
    });

    if (jobsToDelete.length > 0) {
      console.log(
        `[BulkUploadProcessor] Cleaned up ${jobsToDelete.length} old jobs. Active jobs: ${this.jobs.size}`
      );
    }
  }

  private jobId: string;
  private workspaceSlug: string;
  private user: FilteredUser;
  private module: string;
  private job: BulkUploadJob;

  constructor(
    jobId: string,
    workspaceSlug: string,
    user: FilteredUser,
    module: string
  ) {
    this.jobId = jobId;
    this.workspaceSlug = workspaceSlug;
    this.user = user;
    this.module = module;
    this.job = {
      id: jobId,
      status: "initializing",
      totalFiles: 0,
      processedFiles: 0,
      failedFiles: 0,
      errors: [],
      successfulFiles: [], // Track successful file paths for embedding
      startedAt: null,
      completedAt: null,
      cancelled: false,
    };
    BulkUploadProcessor.jobs.set(jobId, this.job);

    // Initialize cleanup on first job creation
    BulkUploadProcessor.initCleanup();
  }

  static getJob(jobId: string): BulkUploadJob | undefined {
    return BulkUploadProcessor.jobs.get(jobId);
  }

  static cancelJob(jobId: string): boolean {
    const job = BulkUploadProcessor.jobs.get(jobId);
    if (job && job.status === "processing") {
      job.cancelled = true;
      return true;
    }
    return false;
  }

  // Stop cleanup interval (call on server shutdown)
  static stopCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
      console.log("[BulkUploadProcessor] Cleanup interval stopped");
    }
  }

  async processFiles(files: FileUpload[]): Promise<BulkUploadJob> {
    try {
      this.job.status = "processing";
      this.job.startedAt = Date.now();
      this.job.totalFiles = files.length;

      const workspace = await Workspace.get({ slug: this.workspaceSlug });
      if (!workspace) {
        throw new Error("Workspace not found");
      }

      // Process files one at a time to avoid race conditions with collector service
      // Note: With 5000 file limit, processing may take considerable time
      for (const file of files) {
        if (this.job.cancelled) {
          break;
        }

        try {
          const result = await this.processFileWithRetry(file, workspace, 3);
          this.job.processedFiles++;

          // Track successful document paths for embedding
          if (result && result.documentPaths) {
            console.log(
              `[BulkUploadProcessor] Adding document paths for ${file.originalname}:`,
              result.documentPaths
            );
            this.job.successfulFiles.push(...result.documentPaths);
          }
        } catch (error) {
          this.job.failedFiles++;
          this.job.errors.push({
            filename: file.originalname,
            error: error instanceof Error ? error.message : "Unknown error",
          });
          console.error(
            `Failed to process ${file.originalname}: ${error instanceof Error ? error.message : String(error)}`
          );
        }
      }

      // Complete job
      this.job.status = this.job.cancelled ? "cancelled" : "completed";
      this.job.completedAt = Date.now();

      console.log(`Bulk upload job ${this.jobId} completed:`, {
        totalFiles: this.job.totalFiles,
        processedFiles: this.job.processedFiles,
        failedFiles: this.job.failedFiles,
        duration: this.job.completedAt - (this.job.startedAt || 0),
      });

      return this.job;
    } catch (error) {
      console.error(
        `Bulk upload processor error: ${error instanceof Error ? error.message : String(error)}`
      );
      this.job.status = "failed";
      this.job.completedAt = Date.now();
      this.job.errors.push({
        filename: "System",
        error: error instanceof Error ? error.message : String(error),
      });
      return this.job;
    }
  }

  private async processFileWithRetry(
    file: FileUpload,
    workspace: any,
    retriesLeft: number
  ): Promise<ProcessedFileResult> {
    try {
      if (this.job.cancelled) {
        throw new Error("Job cancelled");
      }

      return await this.processFile(file, workspace);
    } catch (error) {
      if (retriesLeft > 0 && !this.job.cancelled) {
        const attempt = 4 - retriesLeft; // Calculate which attempt this is (1, 2, 3)
        const delay = Math.pow(2, attempt) * 1000; // Exponential backoff: 2s, 4s, 8s
        console.log(
          `Retrying file ${file.originalname}, attempts left: ${retriesLeft - 1}, waiting ${delay}ms`
        );
        await new Promise((resolve) => setTimeout(resolve, delay));
        return this.processFileWithRetry(file, workspace, retriesLeft - 1);
      }
      throw error;
    }
  }

  private async processFile(
    file: FileUpload,
    _workspace: any
  ): Promise<ProcessedFileResult> {
    // Determine folder path based on module
    let folderName: string;
    const userForPath = {
      ...this.user,
      username: this.user.username || "anonymous",
      email: this.user.email || "",
    } as any; // Cast to avoid type issues during migration

    if (this.module === "document-drafting") {
      folderName = getUserDocumentPathName(
        userForPath,
        true,
        this.workspaceSlug
      );
    } else {
      const workspacePath = getUserDocumentPathName(
        userForPath,
        false,
        this.workspaceSlug
      );
      folderName = path.join(workspacePath, `custom-documents`);
    }

    // Verify file exists before processing
    try {
      await fs.promises.stat(file.path);
    } catch {
      throw new Error(`File not found: ${file.originalname}`);
    }

    // Process with collector
    const Collector = new CollectorApi();
    const result = await Collector.processDocument(
      file.originalname,
      folderName
    );

    if (result === false) {
      throw new Error("Document processing failed");
    }

    const { success, reason, documents } = result;

    if (!success) {
      throw new Error(reason || "Unknown processing error");
    }

    // Return the document paths for embedding
    // The collector returns paths without workspace slug, but we need them for proper file operations
    const documentPaths = documents
      ? documents.map((doc: any) => {
          // For document drafting module, the path already includes the full path
          if (this.module === "document-drafting") {
            console.log(
              `[processFile] Document drafting path: ${doc.location}`
            );
            return doc.location;
          }
          // For other modules, prepend the workspace slug to match expected format
          const fullPath = `${this.workspaceSlug}/${doc.location}`;
          console.log(
            `[processFile] Collector returned: ${doc.location}, Full path: ${fullPath}`
          );
          return fullPath;
        })
      : [];

    console.log(
      `[processFile] Final document paths for ${file.originalname}:`,
      documentPaths
    );

    return {
      success: true,
      originalName: file.originalname,
      documentPaths: documentPaths,
    };
  }
}

export {
  BulkUploadProcessor,
  BULK_UPLOAD_MAX_FILES,
  type BulkUploadJob,
  type FileUpload,
  type ProcessedFileResult,
  type FileError,
};
