import type { ServerTranslations } from "../../types/locales";

const translations: ServerTranslations = {
  common: {
    thinking: "Nachdenken...",
  },
  errors: {
    auth: {
      "authentication-required": "Authentifizierung erforderlich",
      "insufficient-permissions": "Unzureichende Berechtigungen",
    },
    ai: {
      ollama: {
        "connection-failed":
          "Ihre Ollama-Instanz konnte nicht erreicht werden oder antwortet nicht. Bitte stellen Si<PERSON> sicher, dass der API-Server läuft und Ihre Verbindungsinformationen korrekt sind.",
        "empty-response": "Ollama-Textantwort war leer.",
        "stream-failed": "Chat konnte nicht gestreamt werden. {{message}}",
        "no-endpoint": "Kein Ollama-API-Endpunkt wurde festgelegt.",
        "no-token-limit": "Kein Ollama-Token-Kontextlimit wurde festgelegt.",
        "provider-not-responding":
          "Der lokale KI-Anbieter antwortet nicht, entweder weil der Server offline ist oder überlastet ist.",
        "provider-unavailable":
          "Der lokale KI-Server ist derzeit nicht verfügbar. Bitte überprüfen Sie die Installation und stellen Sie sicher, dass er läuft und nicht überlastet ist.",
      },
      azure: {
        "no-endpoint": "Kein Azure-API-Endpunkt wurde festgelegt.",
        "no-key": "Kein Azure-API-Schlüssel wurde festgelegt.",
        "no-model": "Kein Azure OpenAI-Modell wurde festgelegt.",
        "no-model-pref":
          "Keine OPEN_AI_MODEL_PREF ENV definiert. Dies muss der Name eines Deployments in Ihrem Azure-Konto für ein LLM-Chat-Modell wie GPT-3.5 sein.",
      },
      openai: {
        "no-key": "Kein OpenAI-API-Schlüssel wurde festgelegt.",
      },
      mistral: {
        "no-key": "Kein Mistral-API-Schlüssel wurde festgelegt.",
      },
      litellm: {
        "no-base-path":
          "LiteLLM muss einen gültigen Basispfad für die API haben.",
        "no-model": "LiteLLM muss ein gültiges Modell festgelegt haben.",
        "no-token-limit": "Kein Token-Kontextlimit wurde festgelegt.",
      },
      textgenwebui: {
        "no-base-path":
          "TextGenWebUI muss einen gültigen Basispfad für die API haben.",
        "no-token-limit": "Kein Token-Kontextlimit wurde festgelegt.",
      },
      localai: {
        "no-token-limit": "Kein LocalAi-Token-Kontextlimit wurde festgelegt.",
      },
      lmstudio: {
        "no-token-limit": "Kein LMStudio-Token-Kontextlimit wurde festgelegt.",
      },
      fireworks: {
        "invalid-model":
          "FireworksAI-Chat: {{model}} ist nicht gültig für Chat-Vervollständigung!",
      },
      context: {
        header: "Quelle(n)",
      },
    },
    system: {
      "ssl-boot-failed":
        "SSL-Start fehlgeschlagen: {{message}} - Fallback auf HTTP-Start.",
      "no-app": 'Keine "app" definiert - Absturz!',
      validation: {
        "invalid-transcription-provider":
          "{{input}} ist kein gültiger Transkriptionsmodell-Anbieter.",
        "invalid-safety-setting":
          "Ungültige Sicherheitseinstellung. Muss einer von {{modes}} sein.",
        "invalid-model-type":
          "Ungültiger Modelltyp. Muss einer von {{models}} sein.",
        "invalid-embedding-model":
          "Ungültiger Embedding-Modelltyp. Muss einer von {{supported}} sein.",
      },
    },
    document: {
      "sync-failed":
        "Synchronisation des Dokuments {{filename}} nach {{attempts}} Versuchen fehlgeschlagen.",
      "invalid-metadata":
        "Dokument {{filename}} hat ungültige oder fehlende Metadaten.",
      removed:
        "Dokument {{filename}} wurde aus dem überwachten Dokumentensatz entfernt.",
    },
    validation: {
      "value-not-number": "Wert ist keine Zahl.",
      "value-must-be-nonzero": "Wert muss ungleich Null sein.",
      "value-cannot-be-negative": "Wert darf nicht kleiner als 0 sein.",
      "invalid-serp-provider": "Ungültiger SERP-Anbieter.",
      "max-rerank-limit-min":
        "Maximales Reranking-Limit muss mindestens 10 sein.",
      "max-rerank-limit-max":
        "Maximales Reranking-Limit darf 200 nicht überschreiten.",
    },
    embedding: {
      "lmstudio-failed": "LMStudio Embedding fehlgeschlagen: {{errors}}",
      "jina-failed": "Jina Embedding fehlgeschlagen: {{errors}}",
    },
    env: {
      "empty-value": "Wert darf nicht leer sein",
      "jina-api-key-error": "Jina API-Schlüssel muss mit jina_ beginnen",
      "non-zero-required": "Wert muss größer als Null sein",
      "must-be-number": "Wert muss eine Zahl sein",
      "invalid-url": "URL ist keine gültige URL",
      "openai-key-format": "OpenAI-Schlüssel muss mit sk- beginnen",
      "contextual-prompt-format":
        "Kontextueller Benutzer-Prompt muss {file} & {chunk} enthalten",
      "anthropic-key-format": "Anthropic-Schlüssel muss mit sk-ant- beginnen",
      "external-llm-url-format": "URL muss /v1 enthalten",
      "url-no-trailing-slash": "URL darf nicht mit einem Schrägstrich enden",
      "invalid-tts-provider": "{{input}} ist kein gültiger TTS-Anbieter",
      "invalid-whisper-model":
        "{{input}} ist keine gültige Whisper-Modellauswahl",
      "invalid-llm-provider": "{{input}} ist kein gültiger LLM-Anbieter",
    },
    workspace: {
      onlyOwnerShare: "Nur Workspace-Besitzer können Workspaces freigeben",
      onlyOwnerRevoke:
        "Nur Workspace-Besitzer können Workspace-Freigaben aufheben",
      shareFailed: "Freigabe des Workspaces fehlgeschlagen",
      revokeFailed: "Aufheben der Workspace-Freigabe fehlgeschlagen",
      getStatusFailed: "Abrufen des Freigabestatus fehlgeschlagen",
    },
    thread: {
      onlyOwnerShare: "Nur Thread-Besitzer können Threads freigeben",
      onlyOwnerRevoke: "Nur Thread-Besitzer können Thread-Freigaben aufheben",
      shareFailed: "Freigabe des Threads fehlgeschlagen",
      revokeFailed: "Aufheben der Thread-Freigabe fehlgeschlagen",
      getStatusFailed: "Abrufen des Thread-Freigabestatus fehlgeschlagen",
    },
  },
  notifications: {
    document: {
      invalid:
        "Dokument {{filename}} hat keine Metadaten, ist beschädigt oder ungültig und wurde entfernt.",
      removed:
        "Dokument {{filename}} wurde aus dem überwachten Dokumentensatz entfernt.",
    },
  },
  validation: {
    responseHeader: "Generierte Antwort",
    contextHeader: "Ursprünglicher Kontext und Quellen",
    content: {
      "user-question-sources-header": "BENUTZERFRAGE UND QUELLEN",
      "system-response-header": "SYSTEMANTWORT",
      "not-provided": "Nicht bereitgestellt",
    },
  },
  manualWorkEstimator: {
    content: {
      "user-question-header": "BENUTZERFRAGE",
      "system-response-header": "SYSTEMANTWORT",
    },
  },
  docx: {
    title: "Chat-Export",
    exportedOn: "Exportiert am",
    downloadedOn: "Heruntergeladen am",
    keywords: "chat, export",
    description: "Exportierte Chat-Nachricht",
    tokenCount: "Token-Anzahl",
    canvasDocumentTitle: "Canvas-Dokument",
    errors: {
      contentRequired: "Inhalt ist erforderlich",
      writingFile: "Fehler beim Schreiben der temporären Datei",
      generating: "Fehler bei der Dokumentenerstellung",
    },
  },
  deep_search: {
    instruction:
      "Wenn DeepSearch-Quellen für die Benutzeranfrage relevant sind und in der Antwort verwendet werden, geben Sie am Ende der Nachricht klickbare Links zu diesen Quellen in genau diesem Format an: 'Quellen: [Quellentitel 1](URL1), [Quellentitel 2](URL2)'. Der klickbare Text für jeden Link muss eine prägnante Beschreibung des Quelleninhalts sein (z. B. der Seitentitel oder eine Zusammenfassung). Verweisen Sie niemals mit Nummern auf DeepSearch-Quellen im Text (wie [1] oder [Quelle 1]); beziehen Sie sich immer direkt auf die Quelle mit ihrem Namen oder Inhalt. Verwenden Sie keine akademischen Zitierformate.",
  },
  prompts: {
    lqa: {
      systemPrompt:
        "Sie sind ein Rechtsassistent mit Expertise im Recht. Ihr Ziel ist es, genaue und hilfreiche Rechtsinformationen basierend auf den Ihnen verfügbaren Quellen bereitzustellen. Zitieren Sie beim Beantworten von Fragen relevante Quellen wo möglich und stellen Sie klar, wann Sie allgemeine Informationen versus spezifische Rechtsberatung geben. Klarstellen Sie immer, dass die Antwort als Erstentwurf gedacht ist und dass Quellen verifiziert werden müssen. Wenn Sie etwas nicht wissen oder sich unsicher sind, erkennen Sie die Grenzen klar an, anstatt Annahmen zu treffen. Geben Sie die Antwort in der gleichen Sprache wie die Frage des Benutzers.",
      validationPrompt:
        "Sie sind ein Rechtsqualitätssicherungsexperte. Ihre Aufgabe ist es, die Genauigkeit, Vollständigkeit und Hilfsbereitschaft von KI-generierten Rechtsantworten zu bewerten. Analysieren Sie sowohl die Antwort als auch die zitierten Quellen, um sicherzustellen, dass die Informationen korrekt dargestellt werden. Bewerten Sie, ob die Antwort die Frage des Benutzers angemessen beantwortet und dabei alle Beschränkungen der verfügbaren Informationen richtig anerkennt. Geben Sie die Antwort in der gleichen Sprache wie die Frage des Benutzers.\n\nBitte validieren Sie die folgende KI-generierte Rechtsantwort, indem Sie diese Schritte befolgen:\n\n1. Identifizierung von Abweichungen:\n   - Lesen Sie die generierte Antwort durch und vergleichen Sie sie sorgfältig mit dem bereitgestellten Kontext.\n   - Fassen Sie zusammen, ob es Diskrepanzen zwischen der Antwort und dem Quellmaterial gibt. Beschreiben Sie gegebenenfalls, welche Abweichung identifiziert wurde.\n\n2. Validierung von Quellen:\n   - Bestätigen Sie für jede in der generierten Antwort erwähnte Quelle, dass die bereitgestellten Informationen tatsächlich vom bereitgestellten Kontext unterstützt werden.\n   - Erklären Sie, wo die Unterstützung für jede Quelle in den bereitgestellten Informationen gefunden wurde, und notieren Sie alle Unterschiede oder Ungenauigkeiten.\n\n3. Erfüllung der Anweisungen:\n   - Überprüfen Sie, dass die Systemaufforderung befolgt wurde, einschließlich dass die generierte Antwort ordnungsgemäße Quellenverweise mit aussagekräftigen Metadaten (wie Dokumenttitel, Dateinamen oder andere beschreibende Identifikatoren) im Klammernformat [Quellenname] anstelle von generischen nummerierten Verweisen enthält.\n   - Überprüfen Sie, dass die Antwort keine unbegründeten Behauptungen aufstellt oder Verweise für wichtige Teile fehlen.\n\n4. Berichterstattung:\n   - Präsentieren Sie die Ergebnisse der Validierungsprüfungen ohne persönliche Interpretationen oder weitere Untersuchungen.\n   - Wenn alles korrekt ist, bestätigen Sie, dass die Antwort ohne Abweichungen validiert ist.\n   - Wenn nicht alles korrekt ist, geben Sie eine Aufzählung mit Anweisungen dazu, was korrigiert werden muss.\n\nBasieren Sie Ihre Validierung ausschließlich auf den bereitgestellten Informationen und machen Sie keine Annahmen oder Ergänzungen. Stellen Sie sicher, dass der Bericht genau über die Zuverlässigkeit der überprüften Analyse und nicht über eine neue unabhängige Untersuchung ist.",
    },
    dd: {
      documentDraftingPrompt:
        "Sie sind ein Rechtsassistent, der beim Verfassen von Rechtsdokumenten hilft. Geben Sie eine umfassende und genaue Antwort basierend auf den bereitgestellten Informationen. Stellen Sie sicher, dass die Antwort in der gleichen Sprache wie die ursprüngliche Benutzeranfrage gegeben wird.",
      legalIssuesPrompt:
        "Identifizieren und analysieren Sie die wichtigsten Rechtsfragen im gegebenen Szenario. Geben Sie eine strukturierte Analyse mit klarer Begründung und potentiellen Auswirkungen.",
      memoPrompt:
        "Erstellen Sie ein Rechtsmemorandum, das die vorgestellten Probleme anspricht. Schließen Sie Fakten, Rechtsfragen, Analyse und Schlussfolgerungen in einem strukturierten Format ein.",
      combinePrompt:
        "Ein Benutzer hat eine Frage und mehrere Dokumente bereitgestellt, zu denen mehrere Memos über verschiedene Teile des Dokumentenkorpus sowie mögliche Memos zu beteiligten Rechtsfragen bereitgestellt wurden. Sie haben die Aufgabe, diese mehreren Ausgaben zu einer kohärenten Antwort zu kombinieren. Sammeln Sie die Informationen aus diesen Antworten und synthetisieren Sie sie zu einer einzigen umfassenden Antwort, ohne zu erwähnen, dass die Antwort auf mehreren Memos basiert, sondern antworten Sie, als würden Sie die ursprüngliche Benutzerfrage beantworten. Konzentrieren Sie sich darauf, die Genauigkeit beizubehalten und gleichzeitig eine umfassende Antwort basierend auf den bereitgestellten Informationen zu geben. Stellen Sie sicher, dass die Antwort in der gleichen Sprache wie die ursprüngliche Benutzeranfrage gegeben wird.",
    },
    cdb: {
      // CDB-Prompts werden vom Flow-System verwaltet
      // und sind derzeit nicht internationalisiert
    },
  },
};

export default translations;
