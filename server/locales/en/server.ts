import type { ServerTranslations } from "../../types/locales";

const translations: ServerTranslations = {
  common: {
    thinking: "Thinking...",
  },
  errors: {
    auth: {
      "authentication-required": "Authentication required",
      "insufficient-permissions": "Insufficient permissions",
    },
    ai: {
      ollama: {
        "connection-failed":
          "Your Ollama instance could not be reached or is not responding. Please make sure it is running the API server and your connection information is correct.",
        "empty-response": "Ollama text response was empty.",
        "stream-failed": "Could not stream chat. {{message}}",
        "no-endpoint": "No Ollama API endpoint was set.",
        "no-token-limit": "No Ollama token context limit was set.",
        "provider-not-responding":
          "The Local AI engine is not responding, either due to server being offline or overloaded.",
        "provider-unavailable":
          "The Local AI server is currently unavailable. Please check installation and make sure it's running and not overloaded.",
      },
      azure: {
        "no-endpoint": "No Azure API endpoint was set.",
        "no-key": "No Azure API key was set.",
        "no-model": "No Azure OpenAI model was set.",
        "no-model-pref":
          "No OPEN_AI_MODEL_PREF ENV defined. This must be the name of a deployment on your Azure account for an LLM chat model like GPT-3.5.",
      },
      openai: {
        "no-key": "No OpenAI API key was set.",
      },
      mistral: {
        "no-key": "No Mistral API key was set.",
      },
      litellm: {
        "no-base-path":
          "LiteLLM must have a valid base path to use for the api.",
        "no-model": "LiteLLM must have a valid model set.",
        "no-token-limit": "No token context limit was set.",
      },
      textgenwebui: {
        "no-base-path":
          "TextGenWebUI must have a valid base path to use for the api.",
        "no-token-limit": "No token context limit was set.",
      },
      localai: {
        "no-token-limit": "No LocalAi token context limit was set.",
      },
      lmstudio: {
        "no-token-limit": "No LMStudio token context limit was set.",
      },
      fireworks: {
        "invalid-model":
          "FireworksAI chat: {{model}} is not valid for chat completion!",
      },
      context: {
        header: "Source(s)",
      },
    },
    system: {
      "ssl-boot-failed":
        "SSL Boot Failed: {{message}} - falling back to HTTP boot.",
      "no-app": 'No "app" defined - crashing!',
      validation: {
        "invalid-transcription-provider":
          "{{input}} is not a valid transcription model provider.",
        "invalid-safety-setting":
          "Invalid Safety setting. Must be one of {{modes}}.",
        "invalid-model-type": "Invalid Model type. Must be one of {{models}}.",
        "invalid-embedding-model":
          "Invalid Embedding model type. Must be one of {{supported}}.",
      },
    },
    document: {
      "sync-failed":
        "Failed to sync document {{filename}} after {{attempts}} attempts.",
      "invalid-metadata":
        "Document {{filename}} has no metadata, is broken, or invalid.",
      removed:
        "Document {{filename}} has been removed from the watched document set.",
    },
    validation: {
      "value-not-number": "Value is not a number.",
      "value-must-be-nonzero": "Value must be non-zero.",
      "value-cannot-be-negative": "Value cannot be less than 0.",
      "invalid-serp-provider": "Invalid SERP provider.",
      "max-rerank-limit-min": "Maximum rerank limit must be at least 10.",
      "max-rerank-limit-max": "Maximum rerank limit cannot exceed 200.",
    },
    embedding: {
      "lmstudio-failed": "LMStudio Failed to embed: {{errors}}",
      "jina-failed": "Jina Failed to embed: {{errors}}",
    },
    env: {
      "empty-value": "Value cannot be empty",
      "jina-api-key-error": "Jina API key must start with jina_",
      "non-zero-required": "Value must be greater than zero",
      "must-be-number": "Value must be a number",
      "invalid-url": "URL is not a valid URL",
      "openai-key-format": "OpenAI Key must start with sk-",
      "contextual-prompt-format":
        "Contextual User Prompt must contain {file} & {chunk}",
      "anthropic-key-format": "Anthropic Key must start with sk-ant-",
      "external-llm-url-format": "URL must include /v1",
      "url-no-trailing-slash": "URL cannot end with a slash",
      "invalid-tts-provider": "{{input}} is not a valid TTS provider",
      "invalid-whisper-model":
        "{{input}} is not a valid Whisper model selection",
      "invalid-llm-provider": "{{input}} is not a valid LLM provider",
    },
    workspace: {
      onlyOwnerShare: "Only workspace owners can share workspaces",
      onlyOwnerRevoke: "Only workspace owners can revoke workspace shares",
      shareFailed: "Failed to share workspace",
      revokeFailed: "Failed to revoke workspace share",
      getStatusFailed: "Failed to get share status",
    },
    thread: {
      onlyOwnerShare: "Only thread owners can share threads",
      onlyOwnerRevoke: "Only thread owners can revoke thread shares",
      shareFailed: "Failed to share thread",
      revokeFailed: "Failed to revoke thread share",
      getStatusFailed: "Failed to get thread share status",
    },
  },
  notifications: {
    document: {
      invalid:
        "Document {{filename}} has no metadata, is broken, or invalid and has been removed.",
      removed:
        "Document {{filename}} has been removed from the watched document set.",
    },
  },
  validation: {
    responseHeader: "Generated Response",
    contextHeader: "Original Context and Sources",
    content: {
      "user-question-sources-header": "USER QUESTION AND SOURCES",
      "system-response-header": "SYSTEM RESPONSE",
      "not-provided": "Not provided",
    },
  },
  manualWorkEstimator: {
    content: {
      "user-question-header": "USER QUESTION",
      "system-response-header": "SYSTEM RESPONSE",
    },
  },
  docx: {
    title: "Chat Export",
    exportedOn: "Exported on",
    downloadedOn: "Downloaded on",
    keywords: "chat, export",
    description: "Exported chat message",
    tokenCount: "Token count",
    canvasDocumentTitle: "Canvas Document",
    errors: {
      contentRequired: "Content is required",
      writingFile: "Error writing temporary file",
      generating: "Error generating document",
    },
  },
  deep_search: {
    instruction:
      "If any DeepSearch sources are relevant to the user query and used in the response, provide clickable links to those sources at the end of the message in this exact format: 'Sources: [Source Title 1](URL1), [Source Title 2](URL2)'. The clickable text for each link must be a concise description of the source content (e.g., the page title or a summary). Never reference DeepSearch sources using numbers in the text (like [1] or [Source 1]); always refer to the source by its name or content directly. Do not use academic citation formats.",
  },
  prompts: {
    lqa: {
      systemPrompt:
        "You are a legal assistant with expertise in law. Your goal is to provide accurate and helpful legal information based on the sources available to you. When answering questions, cite relevant sources where possible, and clearly state when you're providing general information versus specific legal advice. Always clarify that the response is intended as a first draft and that sources need verification. When you don't know or aren't sure about something, acknowledge the limitations clearly rather than making assumptions. Provide the response in the same language as the user's question.",
      validationPrompt:
        "You are a legal quality assurance expert. Your task is to evaluate the accuracy, completeness, and helpfulness of AI-generated legal responses. Analyze both the response and the sources cited to ensure the information is presented accurately. Assess whether the response adequately addresses the user's question while properly acknowledging any limitations in the available information. Provide the response in the same language as the user's question.\n\nPlease validate the following AI-generated legal response by following these steps:\n\n1. Identification of Deviations:\n   - Read through the generated response and compare it carefully with the provided context.\n   - Summarize if there are any discrepancies between the response and the source material. If so, describe what deviation has been identified.\n\n2. Validation of Sources:\n   - For each source mentioned in the generated response, confirm that the information provided is actually supported by the provided context.\n   - Explain where support for each source has been found in the provided information, and note any differences or inaccuracies.\n\n3. Fulfillment of Instructions:\n   - Verify that the system prompt has been followed, including that the generated response contains proper source references using meaningful metadata (such as document titles, filenames, or other descriptive identifiers) within bracket format [Source Name] rather than generic numbered references.\n   - Check that the response does not make any unsubstantiated claims or lack references for important parts.\n\n4. Reporting:\n   - Present the results of the validation checks without giving any personal interpretations or further investigations.\n   - If everything is correct, confirm that the response is validated without deviations.\n   - If everything is not correct, provide a bullet point list with instructions on what needs to be corrected.\n\nBase your validation solely on the information provided and do not make any assumptions or additions of your own. Ensure the report is precisely about the reliability of the reviewed analysis and not a new independent investigation.",
    },
    dd: {
      documentDraftingPrompt:
        "You are a legal assistant tasked with helping draft legal documents. Provide a comprehensive and accurate response based on the information provided. Ensure that the response is given in the same language as the original user query.",
      legalIssuesPrompt:
        "Identify and analyze the key legal issues in the given scenario. Provide a structured analysis with clear reasoning and potential implications.",
      memoPrompt:
        "Create a legal memorandum that addresses the presented issues. Include facts, legal questions, analysis, and conclusions in a structured format.",
      combinePrompt:
        "A user has provided a question and multiple documents, upon which several memos on different parts of the document corpus has been provided along with possible memos on legal matters involved. You are tasked with combining these multiple outputs into a coherent response. Gather the information from these responses and synthesize it into a single comprehensive answer, without mentioning that the response is based on multiple memos, instead answering as if answering the original user question. Focus on maintaining accuracy while providing a comprehensive answer based on the information provided. Ensure that the response is given in the same language as the original user query.",
    },
    cdb: {
      // CDB prompts are handled by the flow system
      // and are not currently internationalized
    },
  },
};

export default translations;
