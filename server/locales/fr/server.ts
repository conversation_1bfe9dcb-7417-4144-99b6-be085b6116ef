import type { ServerTranslations } from "../../types/locales";

const translations: ServerTranslations = {
  common: {
    thinking: "Réflexion...",
  },
  errors: {
    auth: {
      "authentication-required": "Authentification requise",
      "insufficient-permissions": "Permissions insuffisantes",
    },
    ai: {
      ollama: {
        "connection-failed":
          "Votre instance Ollama n'a pas pu être atteinte ou ne répond pas. Veuillez vous assurer que le serveur API est en cours d'exécution et que vos informations de connexion sont correctes.",
        "empty-response": "La réponse textuelle d'Ollama était vide.",
        "stream-failed": "Impossible de diffuser le chat. {{message}}",
        "no-endpoint": "Aucun point de terminaison Ollama API n'a été défini.",
        "no-token-limit":
          "Aucune limite de contexte de token Ollama n'a été définie.",
        "provider-not-responding":
          "Le fournisseur d'IA local ne répond pas, soit parce que le serveur est hors ligne, soit parce qu'il est surchargé.",
        "provider-unavailable":
          "Le serveur IA local est actuellement indisponible. Veuillez vérifier l'installation et vous assurer qu'il fonctionne et qu'il n'est pas surchargé.",
      },
      azure: {
        "no-endpoint": "Aucun point de terminaison Azure API n'a été défini.",
        "no-key": "Aucune clé API Azure n'a été définie.",
        "no-model": "Aucun modèle Azure OpenAI n'a été défini.",
        "no-model-pref":
          "Aucun ENV OPEN_AI_MODEL_PREF n'a été défini. Cela doit être le nom d'un déploiement sur votre compte Azure pour un modèle de chat LLM comme GPT-3.5.",
      },
      openai: {
        "no-key": "Aucune clé API OpenAI n'a été définie.",
      },
      mistral: {
        "no-key": "Aucune clé API Mistral n'a été définie.",
      },
      litellm: {
        "no-base-path":
          "LiteLLM doit avoir un chemin de base valide pour utiliser l'API.",
        "no-model": "LiteLLM doit avoir un modèle valide défini.",
        "no-token-limit": "Aucune limite de contexte de token n'a été définie.",
      },
      textgenwebui: {
        "no-base-path":
          "TextGenWebUI doit avoir un chemin de base valide pour utiliser l'API.",
        "no-token-limit": "Aucune limite de contexte de token n'a été définie.",
      },
      localai: {
        "no-token-limit":
          "Aucune limite de contexte de token LocalAi n'a été définie.",
      },
      lmstudio: {
        "no-token-limit":
          "Aucune limite de contexte de token LMStudio n'a été définie.",
      },
      fireworks: {
        "invalid-model":
          "Discussion FireworksAI : {{model}} n'est pas valide pour la complétion de chat !",
      },
      context: {
        header: "Source(s)",
      },
    },
    system: {
      "ssl-boot-failed":
        "Échec du démarrage SSL : {{message}} - retour au démarrage HTTP.",
      "no-app": 'Aucune "app" définie - arrêt !',
      validation: {
        "invalid-transcription-provider":
          "{{input}} n'est pas un fournisseur de modèle de transcription valide.",
        "invalid-safety-setting":
          "Paramètre de sécurité invalide. Doit être l'un de {{modes}}.",
        "invalid-model-type":
          "Type de modèle invalide. Doit être l'un de {{models}}.",
        "invalid-embedding-model":
          "Type de modèle d'embedding invalide. Doit être l'un de {{supported}}.",
      },
    },
    document: {
      "sync-failed":
        "Échec de la synchronisation du document {{filename}} après {{attempts}} tentatives.",
      "invalid-metadata":
        "Le document {{filename}} a des métadonnées invalides ou manquantes.",
      removed:
        "Le document {{filename}} a été retiré de l'ensemble des documents surveillés.",
    },
    validation: {
      "value-not-number": "La valeur n'est pas un nombre.",
      "value-must-be-nonzero": "La valeur doit être non nulle.",
      "value-cannot-be-negative": "La valeur ne peut pas être inférieure à 0.",
      "invalid-serp-provider": "Fournisseur SERP invalide.",
      "max-rerank-limit-min":
        "La limite maximale de reclassement doit être d'au moins 10.",
      "max-rerank-limit-max":
        "La limite maximale de reclassement ne peut pas dépasser 200.",
    },
    embedding: {
      "lmstudio-failed": "Échec de l'embedding LMStudio : {{errors}}",
      "jina-failed": "Échec de l'embedding Jina : {{errors}}",
    },
    env: {
      "empty-value": "La valeur ne peut pas être vide",
      "jina-api-key-error": "La clé API Jina doit commencer par jina_",
      "non-zero-required": "La valeur doit être supérieure à zéro",
      "must-be-number": "La valeur doit être un nombre",
      "invalid-url": "L'URL n'est pas une URL valide",
      "openai-key-format": "La clé OpenAI doit commencer par sk-",
      "contextual-prompt-format":
        "L'invite contextuelle de l'utilisateur doit contenir {file} & {chunk}",
      "anthropic-key-format": "La clé Anthropic doit commencer par sk-ant-",
      "external-llm-url-format": "L'URL doit inclure /v1",
      "url-no-trailing-slash":
        "L'URL ne peut pas se terminer par une barre oblique",
      "invalid-tts-provider": "{{input}} n'est pas un fournisseur TTS valide",
      "invalid-whisper-model":
        "{{input}} n'est pas une sélection de modèle Whisper valide",
      "invalid-llm-provider": "{{input}} n'est pas un fournisseur LLM valide",
    },
    workspace: {
      onlyOwnerShare:
        "Seuls les propriétaires d'espace de travail peuvent partager des espaces de travail",
      onlyOwnerRevoke:
        "Seuls les propriétaires d'espace de travail peuvent révoquer le partage d'espaces de travail",
      shareFailed: "Échec du partage de l'espace de travail",
      revokeFailed: "Échec de la révocation du partage de l'espace de travail",
      getStatusFailed: "Échec de la récupération du statut de partage",
    },
    thread: {
      onlyOwnerShare:
        "Seuls les propriétaires de fil peuvent partager des fils",
      onlyOwnerRevoke:
        "Seuls les propriétaires de fil peuvent révoquer le partage de fil",
      shareFailed: "Échec du partage du fil",
      revokeFailed: "Échec de la révocation du partage du fil",
      getStatusFailed: "Échec de la récupération du statut de partage du fil",
    },
  },
  notifications: {
    document: {
      invalid:
        "Le document {{filename}} n'a pas de métadonnées, est corrompu ou invalide et a été supprimé.",
      removed:
        "Le document {{filename}} a été retiré de l'ensemble des documents surveillés.",
    },
  },
  validation: {
    responseHeader: "Réponse Générée",
    contextHeader: "Contexte et Sources Originaux",
    content: {
      "user-question-sources-header": "QUESTION UTILISATEUR ET SOURCES",
      "system-response-header": "RÉPONSE SYSTÈME",
      "not-provided": "Non fourni",
    },
  },
  manualWorkEstimator: {
    content: {
      "user-question-header": "QUESTION UTILISATEUR",
      "system-response-header": "RÉPONSE SYSTÈME",
    },
  },
  docx: {
    title: "Export de conversation",
    exportedOn: "Exporté le",
    downloadedOn: "Téléchargé le",
    keywords: "conversation, export",
    description: "Message de conversation exporté",
    tokenCount: "Nombre de tokens",
    canvasDocumentTitle: "Document Canvas",
    errors: {
      contentRequired: "Le contenu est requis",
      writingFile: "Erreur lors de l'écriture du fichier temporaire",
      generating: "Erreur lors de la génération du document",
    },
  },
  deep_search: {
    instruction:
      "Si des sources DeepSearch sont pertinentes pour la requête de l'utilisateur et utilisées dans la réponse, fournissez des liens cliquables vers ces sources à la fin du message dans ce format exact : 'Sources : [Titre de la source 1](URL1), [Titre de la source 2](URL2)'. Le texte cliquable pour chaque lien doit être une description concise du contenu de la source (par exemple, le titre de la page ou un résumé). Ne référencez jamais les sources DeepSearch en utilisant des numéros dans le texte (comme [1] ou [Source 1]) ; référez-vous toujours à la source directement par son nom ou son contenu. N'utilisez pas de formats de citation académiques.",
  },
  prompts: {
    lqa: {
      systemPrompt:
        "Vous êtes un assistant juridique expert en droit. Votre objectif est de fournir des informations juridiques précises et utiles basées sur les sources à votre disposition. Lorsque vous répondez aux questions, citez les sources pertinentes lorsque cela est possible, et indiquez clairement quand vous fournissez des informations générales par rapport à des conseils juridiques spécifiques. Précisez toujours que la réponse est destinée comme un premier brouillon et que les sources nécessitent une vérification. Lorsque vous ne savez pas ou n'êtes pas sûr de quelque chose, reconnaissez clairement les limites plutôt que de faire des suppositions. Fournissez la réponse dans la même langue que la question de l'utilisateur.",
      validationPrompt:
        "Vous êtes un expert en assurance qualité juridique. Votre tâche est d'évaluer la précision, l'exhaustivité et l'utilité des réponses juridiques générées par l'IA. Analysez à la fois la réponse et les sources citées pour vous assurer que les informations sont présentées avec précision. Évaluez si la réponse répond de manière adéquate à la question de l'utilisateur tout en reconnaissant correctement les limites des informations disponibles. Fournissez la réponse dans la même langue que la question de l'utilisateur.\n\nVeuillez valider la réponse juridique suivante générée par l'IA en suivant ces étapes :\n\n1. Identification des écarts :\n   - Lisez la réponse générée et comparez-la soigneusement avec le contexte fourni.\n   - Résumez s'il y a des divergences entre la réponse et le matériel source. Si oui, décrivez quel écart a été identifié.\n\n2. Validation des sources :\n   - Pour chaque source mentionnée dans la réponse générée, confirmez que les informations fournies sont réellement supportées par le contexte fourni.\n   - Expliquez où le support pour chaque source a été trouvé dans les informations fournies, et notez toute différence ou inexactitude.\n\n3. Respect des instructions :\n   - Vérifiez que l'invite système a été suivie, y compris que la réponse générée contient des références de sources appropriées utilisant des métadonnées significatives (comme les titres de documents, noms de fichiers, ou autres identifiants descriptifs) au format entre crochets [Nom de la source] plutôt que des références numérotées génériques.\n   - Vérifiez que la réponse ne fait pas d'affirmations non substantiées ou ne manque pas de références pour des parties importantes.\n\n4. Rapport :\n   - Présentez les résultats des vérifications de validation sans donner d'interprétations personnelles ou d'investigations supplémentaires.\n   - Si tout est correct, confirmez que la réponse est validée sans écarts.\n   - Si tout n'est pas correct, fournissez une liste à puces avec des instructions sur ce qui doit être corrigé.\n\nBasez votre validation uniquement sur les informations fournies et ne faites aucune supposition ou ajout de votre part. Assurez-vous que le rapport concerne précisément la fiabilité de l'analyse examinée et non une nouvelle enquête indépendante.",
    },
    dd: {
      documentDraftingPrompt:
        "Vous êtes un assistant juridique chargé d'aider à la rédaction de documents juridiques. Fournissez une réponse complète et précise basée sur les informations fournies. Assurez-vous que la réponse est donnée dans la même langue que la requête utilisateur originale.",
      legalIssuesPrompt:
        "Identifiez et analysez les questions juridiques clés dans le scénario donné. Fournissez une analyse structurée avec un raisonnement clair et des implications potentielles.",
      memoPrompt:
        "Créez un mémorandum juridique qui traite des questions présentées. Incluez les faits, les questions juridiques, l'analyse et les conclusions dans un format structuré.",
      combinePrompt:
        "Un utilisateur a fourni une question et plusieurs documents, sur lesquels plusieurs mémos sur différentes parties du corpus documentaire ont été fournis ainsi que d'éventuels mémos sur les questions juridiques impliquées. Vous êtes chargé de combiner ces multiples sorties en une réponse cohérente. Rassemblez les informations de ces réponses et synthétisez-les en une seule réponse complète, sans mentionner que la réponse est basée sur plusieurs mémos, mais en répondant comme si vous répondiez à la question utilisateur originale. Concentrez-vous sur le maintien de la précision tout en fournissant une réponse complète basée sur les informations fournies. Assurez-vous que la réponse est donnée dans la même langue que la requête utilisateur originale.",
    },
    cdb: {
      // Les invites CDB sont gérées par le système de flux
      // et ne sont pas actuellement internationalisées
    },
  },
};

export default translations;
