import type { ServerTranslations } from "../../types/locales";

const translations: ServerTranslations = {
  common: {
    thinking: "Tenker...",
  },
  errors: {
    auth: {
      "authentication-required": "Autentisering påkrevd",
      "insufficient-permissions": "Utilstrekkelige tillatelser",
    },
    ai: {
      ollama: {
        "connection-failed":
          "Din Ollama-instans kunne ikke nås eller svarer ikke. Sørg for at API-serveren kjører og at tilkoblingsinformasjonen din er korrekt.",
        "empty-response": "Ollama tekstsvar var tomt.",
        "stream-failed": "Kunne ikke strømme chatten. {{message}}",
        "no-endpoint": "Ingen Ollama API-endepunkt er satt.",
        "no-token-limit": "Ingen Ollama-token-kontekstgrense er satt.",
        "provider-not-responding":
          "Den lokale AI-leverandøren svarer ikke, enten fordi serveren er offline eller overbelastet.",
        "provider-unavailable":
          "Den lokale AI-serveren er for øyeblikket utilgjengelig. Vennligst sjekk installasjonen og sørg for at den kjører og ikke er overbelastet.",
      },
      azure: {
        "no-endpoint": "Ingen Azure API-endepunkt er satt.",
        "no-key": "Ingen Azure API-nøkkel er satt.",
        "no-model": "Ingen Azure OpenAI-modell er satt.",
        "no-model-pref":
          "Ingen OPEN_AI_MODEL_PREF ENV definert. Dette må være navnet på en distribusjon på din Azure-konto for en LLM-chattemodell som GPT-3.5.",
      },
      openai: {
        "no-key": "Ingen OpenAI API-nøkkel er satt.",
      },
      mistral: {
        "no-key": "Ingen Mistral API-nøkkel er satt.",
      },
      litellm: {
        "no-base-path": "LiteLLM må ha en gyldig basesti for å bruke API-et.",
        "no-model": "LiteLLM må ha en gyldig modell satt.",
        "no-token-limit": "Ingen token-kontekstgrense er satt.",
      },
      textgenwebui: {
        "no-base-path":
          "TextGenWebUI må ha en gyldig basesti for å bruke API-et.",
        "no-token-limit": "Ingen token-kontekstgrense er satt.",
      },
      localai: {
        "no-token-limit": "Ingen LocalAi-token-kontekstgrense er satt.",
      },
      lmstudio: {
        "no-token-limit": "Ingen LMStudio-token-kontekstgrense er satt.",
      },
      fireworks: {
        "invalid-model":
          "FireworksAI chat: {{model}} er ikke gyldig for chattkomplettering!",
      },
      context: {
        header: "Kilde(r)",
      },
    },
    system: {
      "ssl-boot-failed":
        "SSL-oppstart mislyktes: {{message}} - går tilbake til HTTP-oppstart.",
      "no-app": 'Ingen "app" definert - krasjer!',
      validation: {
        "invalid-transcription-provider":
          "{{input}} er ikke en gyldig transkripsjonmodell-leverandør.",
        "invalid-safety-setting":
          "Ugyldig sikkerhetsinnstilling. Må være en av {{modes}}.",
        "invalid-model-type": "Ugyldig modelltype. Må være en av {{models}}.",
        "invalid-embedding-model":
          "Ugyldig innbyggingsmodelltype. Må være en av {{supported}}.",
      },
    },
    document: {
      "sync-failed":
        "Kunne ikke synkronisere dokument {{filename}} etter {{attempts}} forsøk.",
      "invalid-metadata":
        "Dokument {{filename}} har ugyldig eller manglende metadata.",
      removed:
        "Dokument {{filename}} har blitt fjernet fra settet med overvåkede dokumenter.",
    },
    validation: {
      "value-not-number": "Verdien er ikke et tall.",
      "value-must-be-nonzero": "Verdien må være forskjellig fra null.",
      "value-cannot-be-negative": "Verdien kan ikke være mindre enn 0.",
      "invalid-serp-provider": "Ugyldig SERP-leverandør.",
      "max-rerank-limit-min": "Maksimal rerangering grense må være minst 10.",
      "max-rerank-limit-max":
        "Maksimal rerangering grense kan ikke overstige 200.",
    },
    embedding: {
      "lmstudio-failed": "LMStudio-innbygging mislyktes: {{errors}}",
      "jina-failed": "Jina-innbygging mislyktes: {{errors}}",
    },
    env: {
      "jina-api-key-error": "Jina API-nøkkel må starte med jina_",
      "empty-value": "Verdien kan ikke være tom",
      "non-zero-required": "Verdien må være større enn null",
      "must-be-number": "Verdien må være et tall",
      "invalid-url": "URL-en er ikke en gyldig URL",
      "openai-key-format": "OpenAI-nøkkelen må starte med sk-",
      "contextual-prompt-format":
        "Kontekstuell brukerforespørsel må inneholde {file} & {chunk}",
      "anthropic-key-format": "Anthropic-nøkkelen må starte med sk-ant-",
      "external-llm-url-format": "URL-en må inneholde /v1",
      "url-no-trailing-slash": "URL-en kan ikke slutte med en skråstrek",
      "invalid-tts-provider": "{{input}} er ikke en gyldig TTS-leverandør",
      "invalid-whisper-model":
        "{{input}} er ikke et gyldig valg av Whisper-modell",
      "invalid-llm-provider": "{{input}} er ikke en gyldig LLM-leverandør",
    },
    workspace: {
      onlyOwnerShare: "Kun arbeidsområdeeiere kan dele arbeidsområder",
      onlyOwnerRevoke:
        "Kun arbeidsområdeeiere kan fjerne deling av arbeidsområder",
      shareFailed: "Kunne ikke dele arbeidsområdet",
      revokeFailed: "Kunne ikke fjerne deling av arbeidsområdet",
      getStatusFailed: "Kunne ikke hente delingsstatus for arbeidsområdet",
    },
    thread: {
      onlyOwnerShare: "Kun trådeiere kan dele tråder",
      onlyOwnerRevoke: "Kun trådeiere kan fjerne deling av tråder",
      shareFailed: "Kunne ikke dele tråden",
      revokeFailed: "Kunne ikke fjerne deling av tråden",
      getStatusFailed: "Kunne ikke hente delingsstatus for tråden",
    },
  },
  validation: {
    responseHeader: "Generert Svar",
    contextHeader: "Opprinnelig Kontekst og Kilder",
    content: {
      "user-question-sources-header": "BRUKERSPØRSMÅL OG KILDER",
      "system-response-header": "SYSTEMSVAR",
      "not-provided": "Ikke oppgitt",
    },
  },
  manualWorkEstimator: {
    content: {
      "user-question-header": "BRUKERSPØRSMÅL",
      "system-response-header": "SYSTEMSVAR",
    },
  },
  notifications: {
    document: {
      invalid:
        "Dokument {{filename}} har ingen metadata, er ødelagt eller ugyldig og har blitt fjernet.",
      removed:
        "Dokument {{filename}} har blitt fjernet fra settet med overvåkede dokumenter.",
    },
  },
  docx: {
    title: "Chat-eksport",
    exportedOn: "Eksportert",
    downloadedOn: "Nedlastet",
    keywords: "chat, eksport",
    description: "Eksportert chat-melding",
    tokenCount: "Antall tokens",
    canvasDocumentTitle: "Canvas-dokument",
    errors: {
      contentRequired: "Innhold er påkrevd",
      writingFile: "Feil ved skriving av midlertidig fil",
      generating: "Feil ved generering av dokument",
    },
  },
  deep_search: {
    instruction:
      "Hvis noen DeepSearch-kilder er relevante for brukerforespørselen og brukt i svaret, oppgi klikkbare lenker til disse kildene på slutten av meldingen i nøyaktig dette formatet: 'Kilder: [Kildetittel 1](URL1), [Kildetittel 2](URL2)'. Den klikkbare teksten for hver lenke må være en kort beskrivelse av kildens innhold (f.eks. sidetittel eller sammendrag). Referer aldri til DeepSearch-kilder med tall i teksten (som [1] eller [Kilde 1]); henvis alltid til kilden direkte med dens navn eller innhold. Ikke bruk akademiske siteringsformater.",
  },
  prompts: {
    lqa: {
      systemPrompt:
        "Du er en juridisk assistent med ekspertise innen jus. Målet ditt er å gi nøyaktig og nyttig juridisk informasjon basert på kildene som er tilgjengelige for deg. Når du svarer på spørsmål, sitér relevante kilder der det er mulig, og gjør det klart når du gir generell informasjon kontra spesifikke juridiske råd. Klargjør alltid at svaret er ment som et førsteutkast og at kilder må verifiseres. Når du ikke vet eller er usikker på noe, erkjenn begrensningene tydelig i stedet for å gjøre antagelser. Gi svaret på samme språk som brukerens spørsmål.",
      validationPrompt:
        "Du er en juridisk kvalitetssikringsekspert. Din oppgave er å evaluere nøyaktigheten, fullstendigheten og nytten av AI-genererte juridiske svar. Analyser både svaret og de siterte kildene for å sikre at informasjonen er presentert nøyaktig. Vurder om svaret på en tilstrekkelig måte adresserer brukerens spørsmål samtidig som det på riktig måte erkjenner eventuelle begrensninger i den tilgjengelige informasjonen. Gi svaret på samme språk som brukerens spørsmål.\n\nVennligst valider det følgende AI-genererte juridiske svaret ved å følge disse trinnene:\n\n1. Identifisering av avvik:\n   - Les gjennom det genererte svaret og sammenlign det nøye med den oppgitte konteksten.\n   - Sammenfatt om det er noen uoverensstemmelser mellom svaret og kildematerialet. Hvis så, beskriv hvilket avvik som er identifisert.\n\n2. Validering av kilder:\n   - For hver kilde nevnt i det genererte svaret, bekreft at informasjonen som er gitt faktisk støttes av den oppgitte konteksten.\n   - Forklar hvor støtte for hver kilde er funnet i den oppgitte informasjonen, og merk eventuelle forskjeller eller unøyaktigheter.\n\n3. Oppfyllelse av instruksjoner:\n   - Verifiser at systemprompt er fulgt, inkludert at det genererte svaret inneholder passende kildereferanser med meningsfylt metadata (som dokumenttitler, filnavn eller andre beskrivende identifikatorer) i parentesformat [Kildenavn] i stedet for generiske nummererte referanser.\n   - Sjekk at svaret ikke gjør noen udokumenterte påstander eller mangler referanser for viktige deler.\n\n4. Rapportering:\n   - Presenter resultatene av valideringskontrollene uten å gi personlige tolkninger eller ytterligere undersøkelser.\n   - Hvis alt er riktig, bekreft at svaret er validert uten avvik.\n   - Hvis alt ikke er riktig, gi en punktliste med instruksjoner om hva som må korrigeres.\n\nBaser valideringen din utelukkende på den oppgitte informasjonen og gjør ingen antakelser eller tillegg på egen hånd. Sørg for at rapporten er presist om påliteligheten til den gjennomgåtte analysen og ikke en ny uavhengig undersøkelse.",
    },
    dd: {
      documentDraftingPrompt:
        "Du er en juridisk assistent som hjelper til med å utarbeide juridiske dokumenter. Gi et omfattende og nøyaktig svar basert på den oppgitte informasjonen. Sørg for at svaret gis på samme språk som den opprinnelige brukerforespørselen.",
      legalIssuesPrompt:
        "Identifiser og analyser de viktigste juridiske spørsmålene i det gitte scenarioet. Gi en strukturert analyse med klar begrunnelse og potensielle implikasjoner.",
      memoPrompt:
        "Lag et juridisk memorandum som adresserer de presenterte spørsmålene. Inkluder fakta, juridiske spørsmål, analyse og konklusjoner i et strukturert format.",
      combinePrompt:
        "En bruker har oppgitt et spørsmål og flere dokumenter, der flere memoer om forskjellige deler av dokumentkorpuset er oppgitt sammen med mulige memoer om juridiske spørsmål som er involvert. Du har i oppgave å kombinere disse multiple utdataene til et sammenhengene svar. Samle informasjonen fra disse svarene og syntetiser den til et enkelt omfattende svar, uten å nevne at svaret er basert på flere memoer, men svare som om du svarer på det opprinnelige brukerspørsmålet. Fokuser på å opprettholde nøyaktigheten samtidig som du gir et omfattende svar basert på den oppgitte informasjonen. Sørg for at svaret gis på samme språk som den opprinnelige brukerforespørselen.",
    },
    cdb: {
      // CDB-prompts håndteres av flytsystemet
      // og er for øyeblikket ikke internasjonalisert
    },
  },
};

export default translations;
