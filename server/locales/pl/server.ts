import type { ServerTranslations } from "../../types/locales";

const translations: ServerTranslations = {
  common: {
    thinking: "Myślę...",
  },
  errors: {
    auth: {
      "authentication-required": "Wymagana autoryzacja",
      "insufficient-permissions": "Niewystarczające uprawnienia",
    },
    ai: {
      ollama: {
        "connection-failed":
          "Nie można połączyć się z instancją Ollama lub nie odpowiada. Upewnij się, że serwer API jest uruchomiony i informacje o połączeniu są poprawne.",
        "empty-response": "Odpowiedź tekstowa Ollama była pusta.",
        "stream-failed": "Nie można przesłać czatu. {{message}}",
        "no-endpoint": "Nie ustawiono punktu końcowego API Ollama.",
        "no-token-limit": "Nie ustawiono limitu kontekstu tokenów Ollama.",
        "provider-not-responding":
          "Lokalny dostawca AI nie odpowiada, albo z powodu wyłączenia serwera, albo jego przeciążenia.",
        "provider-unavailable":
          "Lokalny serwer AI jest obecnie niedostępny. Sprawdź instalację i upewnij się, że działa i nie jest przeciążony.",
      },
      azure: {
        "no-endpoint": "Nie ustawiono punktu końcowego API Azure.",
        "no-key": "Nie ustawiono klucza API Azure.",
        "no-model": "Nie ustawiono modelu Azure OpenAI.",
        "no-model-pref":
          "Nie zdefiniowano ENV OPEN_AI_MODEL_PREF. Musi to być nazwa wdrożenia na koncie Azure dla modelu czatu LLM, takiego jak GPT-3.5.",
      },
      openai: {
        "no-key": "Nie ustawiono klucza API OpenAI.",
      },
      mistral: {
        "no-key": "Nie ustawiono klucza API Mistral.",
      },
      litellm: {
        "no-base-path":
          "LiteLLM musi mieć prawidłową ścieżkę bazową do korzystania z API.",
        "no-model": "LiteLLM musi mieć ustawiony prawidłowy model.",
        "no-token-limit": "Nie ustawiono limitu kontekstu tokenów.",
      },
      textgenwebui: {
        "no-base-path":
          "TextGenWebUI musi mieć prawidłową ścieżkę bazową do korzystania z API.",
        "no-token-limit": "Nie ustawiono limitu kontekstu tokenów.",
      },
      localai: {
        "no-token-limit": "Nie ustawiono limitu kontekstu tokenów LocalAi.",
      },
      lmstudio: {
        "no-token-limit": "Nie ustawiono limitu kontekstu tokenów LMStudio.",
      },
      fireworks: {
        "invalid-model":
          "FireworksAI czat: {{model}} nie jest ważny dla uzupełnienia czatu!",
      },
      context: {
        header: "Źródło(a)",
      },
    },
    system: {
      "ssl-boot-failed":
        "Uruchomienie SSL nie powiodło się: {{message}} - przełączanie na uruchomienie HTTP.",
      "no-app": 'Nie zdefiniowano "app" - awaria!',
      validation: {
        "invalid-transcription-provider":
          "{{input}} nie jest prawidłowym dostawcą modelu transkrypcji.",
        "invalid-safety-setting":
          "Nieprawidłowe ustawienie bezpieczeństwa. Musi być jednym z {{modes}}.",
        "invalid-model-type":
          "Nieprawidłowy typ modelu. Musi być jednym z {{models}}.",
        "invalid-embedding-model":
          "Nieprawidłowy typ modelu osadzania. Musi być jednym z {{supported}}.",
      },
    },
    document: {
      "sync-failed":
        "Nie udało się zsynchronizować dokumentu {{filename}} po {{attempts}} próbach.",
      "invalid-metadata":
        "Dokument {{filename}} ma nieprawidłowe lub brakujące metadane.",
      removed:
        "Dokument {{filename}} został usunięty z zestawu monitorowanych dokumentów.",
    },
    validation: {
      "value-not-number": "Wartość nie jest liczbą.",
      "value-must-be-nonzero": "Wartość musi być różna od zera.",
      "value-cannot-be-negative": "Wartość nie może być mniejsza niż 0.",
      "invalid-serp-provider": "Nieprawidłowy dostawca SERP.",
      "max-rerank-limit-min":
        "Maksymalny limit ponownego rankingu musi wynosić co najmniej 10.",
      "max-rerank-limit-max":
        "Maksymalny limit ponownego rankingu nie może przekraczać 200.",
    },
    embedding: {
      "lmstudio-failed": "Osadzanie LMStudio nie powiodło się: {{errors}}",
      "jina-failed": "Osadzanie Jina nie powiodło się: {{errors}}",
    },
    env: {
      "jina-api-key-error": "Klucz API Jina musi zaczynać się od jina_",
      "empty-value": "Wartość nie może być pusta",
      "non-zero-required": "Wartość musi być większa od zera",
      "must-be-number": "Wartość musi być liczbą",
      "invalid-url": "URL nie jest prawidłowym adresem URL",
      "openai-key-format": "Klucz OpenAI musi zaczynać się od sk-",
      "contextual-prompt-format":
        "Kontekstowe zapytanie użytkownika musi zawierać {file} i {chunk}",
      "anthropic-key-format": "Klucz Anthropic musi zaczynać się od sk-ant-",
      "external-llm-url-format": "URL musi zawierać /v1",
      "url-no-trailing-slash": "URL nie może kończyć się ukośnikiem",
      "invalid-tts-provider": "{{input}} nie jest prawidłowym dostawcą TTS",
      "invalid-whisper-model":
        "{{input}} nie jest prawidłowym wyborem modelu Whisper",
      "invalid-llm-provider": "{{input}} nie jest prawidłowym dostawcą LLM",
    },
    workspace: {
      onlyOwnerShare:
        "Tylko właściciele obszarów roboczych mogą udostępniać obszary robocze",
      onlyOwnerRevoke:
        "Tylko właściciele obszarów roboczych mogą cofnąć udostępnianie obszarów roboczych",
      shareFailed: "Nie udało się udostępnić obszaru roboczego",
      revokeFailed: "Nie udało się cofnąć udostępniania obszaru roboczego",
      getStatusFailed:
        "Nie udało się pobrać statusu udostępniania obszaru roboczego",
    },
    thread: {
      onlyOwnerShare: "Tylko właściciele wątków mogą udostępniać wątki",
      onlyOwnerRevoke:
        "Tylko właściciele wątków mogą cofnąć udostępnianie wątków",
      shareFailed: "Nie udało się udostępnić wątku",
      revokeFailed: "Nie udało się cofnąć udostępniania wątku",
      getStatusFailed: "Nie udało się pobrać statusu udostępniania wątku",
    },
  },
  validation: {
    responseHeader: "Wygenerowana Odpowiedź",
    contextHeader: "Oryginalny Kontekst i Źródła",
    content: {
      "user-question-sources-header": "PYTANIE UŻYTKOWNIKA I ŹRÓDŁA",
      "system-response-header": "ODPOWIEDŹ SYSTEMU",
      "not-provided": "Nie podano",
    },
  },
  manualWorkEstimator: {
    content: {
      "user-question-header": "PYTANIE UŻYTKOWNIKA",
      "system-response-header": "ODPOWIEDŹ SYSTEMU",
    },
  },
  notifications: {
    document: {
      invalid:
        "Dokument {{filename}} nie ma metadanych, jest uszkodzony lub nieprawidłowy i został usunięty.",
      removed:
        "Dokument {{filename}} został usunięty z zestawu monitorowanych dokumentów.",
    },
  },
  docx: {
    title: "Eksport czatu",
    exportedOn: "Wyeksportowano",
    downloadedOn: "Pobrano",
    keywords: "czat, eksport",
    description: "Wyeksportowana wiadomość czatu",
    tokenCount: "Liczba tokenów",
    canvasDocumentTitle: "Dokument Canvas",
    errors: {
      contentRequired: "Treść jest wymagana",
      writingFile: "Błąd podczas zapisywania pliku tymczasowego",
      generating: "Błąd podczas generowania dokumentu",
    },
  },
  deep_search: {
    instruction:
      "Jeśli jakiekolwiek źródła DeepSearch są istotne dla zapytania użytkownika i zostały użyte w odpowiedzi, podaj klikalne linki do tych źródeł na końcu wiadomości w dokładnie tym formacie: 'Źródła: [Tytuł źródła 1](URL1), [Tytuł źródła 2](URL2)'. Klikalny tekst dla każdego linku musi być zwięzłym opisem zawartości źródła (np. tytuł strony lub podsumowanie). Nigdy nie odwołuj się do źródeł DeepSearch za pomocą numerów w tekście (jak [1] lub [Źródło 1]); zawsze odwołuj się do źródła bezpośrednio przez jego nazwę lub zawartość. Nie używaj akademickich formatów cytowania.",
  },
  prompts: {
    lqa: {
      systemPrompt:
        "Jesteś asystentem prawnym z ekspertyzą w dziedzinie prawa. Twoim celem jest dostarczenie dokładnych i pomocnych informacji prawnych na podstawie źródeł dostępnych dla Ciebie. Odpowiadając na pytania, cytuj odpowiednie źródła tam, gdzie to możliwe, i jasno określaj, kiedy podajesz informacje ogólne w porównaniu z konkretnymi poradami prawnymi. Zawsze wyjaśniaj, że odpowiedź jest przeznaczona jako pierwszy szkic i że źródła wymagają weryfikacji. Kiedy czegoś nie wiesz lub nie jesteś pewny, jasno uznaj ograniczenia zamiast robic założenia. Udziel odpowiedzi w tym samym języku co pytanie użytkownika.",
      validationPrompt:
        "Jesteś ekspertem ds. zapewnienia jakości prawnej. Twoim zadaniem jest ocena dokładności, pełności i przydatności odpowiedzi prawnych generowanych przez AI. Przeanalizuj zarówno odpowiedź, jak i cytowane źródła, aby upewnić się, że informacje są przedstawione dokładnie. Oceń, czy odpowiedź odpowiednio odnosi się do pytania użytkownika, jednocześnie właściwie uznając wszelkie ograniczenia w dostępnych informacjach. Udziel odpowiedzi w tym samym języku co pytanie użytkownika.\n\nProszę zwaliduj następującą odpowiedź prawną wygenerowana przez AI, postępując zgodnie z tymi krokami:\n\n1. Identyfikacja odchyleń:\n   - Przeczytaj wygenerowana odpowiedź i porównaj ją dokładnie z dostarczonym kontekstem.\n   - Podsumuj, czy są jakieś rozbieżności między odpowiedzią a materiałem źródłowym. Jeśli tak, opisz jakie odchylenie zostało zidentyfikowane.\n\n2. Walidacja źródeł:\n   - Dla każdego źródła wymienionego w wygenerowanej odpowiedzi, potwierdź, że podane informacje są rzeczywiście poparte przez dostarczony kontekst.\n   - Wyjaśnij, gdzie wsparcie dla każdego źródła zostało znalezione w dostarczonych informacjach, i zanotuj wszelkie różnice lub niedokładności.\n\n3. Spełnienie instrukcji:\n   - Zweryfikuj, że systemowy prompt został zastosowany, w tym że wygenerowana odpowiedź zawiera właściwe odniesienia do źródeł z użyciem znaczących metadanych (takich jak tytuły dokumentów, nazwy plików lub inne opisowe identyfikatory) w formacie nawiasowym [Nazwa źródła] zamiast ogólnych ponumerowanych odniesień.\n   - Sprawdź, czy odpowiedź nie zawiera żadnych nieuzasadnionych twierdzeń lub nie brakuje odniesień do ważnych części.\n\n4. Raportowanie:\n   - Przedstaw wyniki kontroli walidacji bez podawania osobistych interpretacji lub dalszych badań.\n   - Jeśli wszystko jest poprawne, potwierdź, że odpowiedź jest zwalidowana bez odchyleń.\n   - Jeśli nie wszystko jest poprawne, podaj listę punktów z instrukcjami dotyczącymi tego, co wymaga korekty.\n\nOpórz swoją walidację wyłącznie na dostarczonych informacjach i nie rób żadnych założeń ani własnych dodatków. Upewnij się, że raport dotyczy dokładnie wiarygodności przeglądanej analizy, a nie nowego niezależnego dochodzenia.",
    },
    dd: {
      documentDraftingPrompt:
        "Jesteś asystentem prawnym zajmującym się tworzeniem dokumentów prawnych. Podaj wyczerpującą i dokładną odpowiedź na podstawie dostarczonych informacji. Upewnij się, że odpowiedź jest udzielana w tym samym języku co oryginalne zapytanie użytkownika.",
      legalIssuesPrompt:
        "Zidentyfikuj i przeanalizuj kluczowe kwestie prawne w danym scenariuszu. Podaj strukturalną analizę z jasnym uzasadnieniem i potencjalnymi implikacjami.",
      memoPrompt:
        "Stwórz prawne memorandum, które odnosi się do przedstawionych kwestii. Uwzględnij fakty, pytania prawne, analizę i wnioski w strukturalnym formacie.",
      combinePrompt:
        "Użytkownik dostarczył pytanie i kilka dokumentów, na podstawie których zostało dostarczonych kilka not dotyczących różnych części korpusu dokumentów wraz z możliwymi notami dotyczącymi zagadnień prawnych. Masz zadanie połączyć te wielokrotne wyjścia w spójną odpowiedź. Zbierz informacje z tych odpowiedzi i syntezuj je w jedną kompleksową odpowiedź, nie wspominając, że odpowiedź jest oparta na wielu notach, ale odpowiadając, jakbyś odpowiadał na oryginalne pytanie użytkownika. Skup się na zachowaniu dokładności, jednocześnie zapewniając kompleksową odpowiedź opartą na dostarczonych informacjach. Upewnij się, że odpowiedź jest udzielana w tym samym języku co oryginalne zapytanie użytkownika.",
    },
    cdb: {
      // Prompty CDB są obsługiwane przez system przepływu
      // i obecnie nie są zinternacjonalizowane
    },
  },
};

export default translations;
