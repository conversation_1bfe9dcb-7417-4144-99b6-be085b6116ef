import type { ServerTranslations } from "../../types/locales";

const translations: ServerTranslations = {
  common: {
    thinking: "Ibitekerezo...",
  },
  errors: {
    auth: {
      "authentication-required": "Kugaragaza ubunyangamugayo bikenewe",
      "insufficient-permissions": "Ububasha butah<PERSON>je",
    },
    ai: {
      ollama: {
        "connection-failed":
          "Ntabwo twashoboye kugera kuri Ollama yawe cyangwa ntiyitaba. Nyamuneka menya neza ko seriveri ya API ikora kandi amakuru yawe yo guhuza ari ukuri.",
        "empty-response":
          "Igisubizo cy'umwandiko wa Ollama cyari kirimo ubusa.",
        "stream-failed": "Ntabwo byashobotse gutunganya chat. {{message}}",
        "no-endpoint": "Nta endpoint ya API ya Ollama yashyizweho.",
        "no-token-limit": "Nta urugero rw'ibimenyetso bya Ollama rwashyizweho.",
        "provider-not-responding":
          "Utanga AI waho nti<PERSON>, bishobora kuba ari ukubera ko seriveri idakora cyangwa ifite umuvuduko mwinshi.",
        "provider-unavailable":
          "Seriveri ya AI waho ntabwo ikoreshwa kuri ubu. Nyamuneka suzuma neza uko yashyizweho kandi urebe ko ikora neza kandi ntabwo ifite umuvuduko mwinshi.",
      },
      azure: {
        "no-endpoint": "Nta endpoint ya API ya Azure yashyizweho.",
        "no-key": "Nta urufunguzo rwa API ya Azure rwashyizweho.",
        "no-model": "Nta modeli ya Azure OpenAI yashyizweho.",
        "no-model-pref":
          "Nta OPEN_AI_MODEL_PREF ENV yasobanuwe. Iki kigomba kuba izina ry'ishyirwa kuri konti yawe ya Azure kuri modeli ya LLM chat nka GPT-3.5.",
      },
      openai: {
        "no-key": "Nta urufunguzo rwa API ya OpenAI rwashyizweho.",
      },
      mistral: {
        "no-key": "Nta urufunguzo rwa API ya Mistral rwashyizweho.",
      },
      litellm: {
        "no-base-path":
          "LiteLLM igomba kugira inzira y'ibanze yemewe yo gukoresha API.",
        "no-model": "LiteLLM igomba kugira modeli yemewe yashyizweho.",
        "no-token-limit": "Nta urugero rw'ibimenyetso rwashyizweho.",
      },
      textgenwebui: {
        "no-base-path":
          "TextGenWebUI igomba kugira inzira y'ibanze yemewe yo gukoresha API.",
        "no-token-limit": "Nta urugero rw'ibimenyetso rwashyizweho.",
      },
      localai: {
        "no-token-limit":
          "Nta urugero rw'ibimenyetso bya LocalAi rwashyizweho.",
      },
      lmstudio: {
        "no-token-limit":
          "Nta urugero rw'ibimenyetso bya LMStudio rwashyizweho.",
      },
      fireworks: {
        "invalid-model":
          "FireworksAI chat: {{model}} ntibyemewe kuri chat completion!",
      },
      context: {
        header: "Inkomoko",
      },
    },
    system: {
      "ssl-boot-failed":
        "Gutangira SSL byanze: {{message}} - gusubira ku gutangira HTTP.",
      "no-app": 'Nta "app" yasobanuwe - guhomba!',
      validation: {
        "invalid-transcription-provider":
          "{{input}} si utanga serivisi wemewe w'inyandiko.",
        "invalid-safety-setting":
          "Igenamiterere ry'umutekano ritemewe. Rigomba kuba rimwe muri {{modes}}.",
        "invalid-model-type":
          "Ubwoko bwa modeli butemewe. Bugomba kuba bumwe muri {{models}}.",
        "invalid-embedding-model":
          "Ubwoko bwa modeli y'embedding butemewe. Bugomba kuba bumwe muri {{supported}}.",
      },
    },
    document: {
      "sync-failed":
        "Byanze guhuza inyandiko {{filename}} nyuma y'imirongo {{attempts}}.",
      "invalid-metadata":
        "Inyandiko {{filename}} ifite metadata itemewe cyangwa ibuze.",
      removed:
        "Inyandiko {{filename}} yakuwe mu matsinda y'inyandiko zikurikiranwa.",
    },
    validation: {
      "value-not-number": "Agaciro si umubare.",
      "value-must-be-nonzero": "Agaciro kagomba kuba katari zeru.",
      "value-cannot-be-negative": "Agaciro ntikashobora kuba munsi ya 0.",
      "invalid-serp-provider": "Utanga serivisi wa SERP utemewe.",
      "max-rerank-limit-min":
        "Umubare ntarengwa wo kongera gushyirwa mu byiciro ugomba kuba nibura 10.",
      "max-rerank-limit-max":
        "Umubare ntarengwa wo kongera gushyirwa mu byiciro ntushobora kurenza 200.",
    },
    embedding: {
      "lmstudio-failed": "Embedding ya LMStudio yananiwe: {{errors}}",
      "jina-failed": "Embedding ya Jina yananiwe: {{errors}}",
    },
    env: {
      "jina-api-key-error":
        "Urufunguzo rwa API ya Jina rugomba gutangira na jina_",
      "empty-value": "Agaciro ntikashobora kuba ubusa",
      "non-zero-required": "Agaciro kagomba kuba karenze zeru",
      "must-be-number": "Agaciro kagomba kuba umubare",
      "invalid-url": "URL si URL yemewe",
      "openai-key-format": "Urufunguzo rwa OpenAI rugomba gutangira na sk-",
      "contextual-prompt-format":
        "Ikibazo cy'umukoresha kigomba kuba kirimo {file} na {chunk}",
      "anthropic-key-format":
        "Urufunguzo rwa Anthropic rugomba gutangira na sk-ant-",
      "external-llm-url-format": "URL igomba kuba irimo /v1",
      "url-no-trailing-slash":
        "URL ntishobora kurangira n'akamenyetso ka slash",
      "invalid-tts-provider": "{{input}} si utanga serivisi wemewe wa TTS",
      "invalid-whisper-model": "{{input}} siyo ihitamo ryiza rya model Whisper",
      "invalid-llm-provider": "{{input}} si utanga serivisi wemewe wa LLM",
    },
    workspace: {
      onlyOwnerShare: "Ni ba nyir'ibyumba gusa bashobora gusangiza ibyumba",
      onlyOwnerRevoke:
        "Ni ba nyir'ibyumba gusa bashobora guhagarika gusangiza ibyumba",
      shareFailed: "Byananiwe gusangiza ibyumba",
      revokeFailed: "Byananiwe guhagarika gusangiza ibyumba",
      getStatusFailed: "Byananiwe kubona uko gusangiza ibyumba bimeze",
    },
    thread: {
      onlyOwnerShare: "Ni ba nyir'imirongo gusa bashobora gusangiza imirongo",
      onlyOwnerRevoke:
        "Ni ba nyir'imirongo gusa bashobora guhagarika gusangiza imirongo",
      shareFailed: "Byananiwe gusangiza umurongo",
      revokeFailed: "Byananiwe guhagarika gusangiza umurongo",
      getStatusFailed: "Byananiwe kubona uko gusangiza imirongo bimeze",
    },
  },
  validation: {
    responseHeader: "Igisubizo Cyatanzwe",
    contextHeader: "Inyandiko n'Inkomoko by'Umwimerere",
    content: {
      "user-question-sources-header": "IKIBAZO CY'UKORESHA N'INKOMOKO",
      "system-response-header": "IGISUBIZO CY'UBURYO",
      "not-provided": "Ntabwo byatanzwe",
    },
  },
  manualWorkEstimator: {
    content: {
      "user-question-header": "IKIBAZO CY'UKORESHA",
      "system-response-header": "IGISUBIZO CY'UBURYO",
    },
  },
  notifications: {
    document: {
      invalid:
        "Inyandiko {{filename}} nta metadata ifite, yangiritse, cyangwa ntiyemewe kandi yakuweho.",
      removed:
        "Inyandiko {{filename}} yakuwe mu matsinda y'inyandiko zikurikiranwa.",
    },
  },
  docx: {
    title: "Kohereza Ikiganiro",
    exportedOn: "Byoherejwe",
    downloadedOn: "Byakuwe",
    keywords: "ikiganiro, kohereza",
    description: "Ubutumwa bwoherejwe",
    tokenCount: "Umubare wa tokens",
    canvasDocumentTitle: "Inyandiko ya Canvas",
    errors: {
      contentRequired: "Ibikubiyemo birakenewe",
      writingFile: "Ikosa mu kwandika dosiye y'agateganyo",
      generating: "Ikosa mu gukora inyandiko",
    },
  },
  deep_search: {
    instruction:
      "Niba hari inkomoko za DeepSearch zijyanye n'ikibazo cy'umukoresha kandi zikoreshwa mu gisubizo, tanga amahuriro ashobora gukandwaho kuri izo nkomoko ku mpera z'ubutumwa muri ubu buryo nyabwo: 'Inkomoko: [Umutwe w'Inkomoko 1](URL1), [Umutwe w'Inkomoko 2](URL2)'. Umwandiko ushobora gukandwaho kuri buri huriro ugomba kuba ibisobanuro bigufi by'ibirimo mu nkomoko (urugero, umutwe w'urupapuro cyangwa incamake). Ntukigere werekeza ku nkomoko za DeepSearch ukoresheje imibare mu mwandiko (nka [1] cyangwa [Inkomoko 1]); buri gihe vuga inkomoko ukoresha izina ryayo cyangwa ibikubiyemo mu buryo butaziguye. Ntukoreshe uburyo bwo gukoresha inkomoko bwa akademiki.",
  },
  prompts: {
    lqa: {
      systemPrompt:
        "Uri umufasha w'amategeko ufite ubumenyi bw'ibunyangamugayo. Igamije ryawe ni ugutanga amakuru y'amategeko akomeye kandi afasha ashingiye ku nkomoko zikuboneka. Mugihe usubiraho ibibazo, vuga inkomoko z'ingenzi aho bishoboka, kandi usobanure neza igihe utanga amakuru rusange ukurura n'inyigisho zihariye z'amategeko. Buri gihe sobanura neza ko igisubizo kigamije nk'iyandikwa rya mbere kandi ko inkomoko zikeneye kugenzurwa. Igihe utazi cyangwa utashidikanya ikintu runaka, emera ingorane mu buryo butaziguye aho gukora ibitekerezo. Tanga igisubizo mu rurimi rumwe n'ikibazo cy'umukoresha.",
      validationPrompt:
        "Uri umuhanga mu kureba ubwiza bw'amategeko. Akazi kawe ni ugusuzuma ukuri, ubwuzuye, n'akamaro k'ibisubizo by'amategeko byakozwe na AI. Suzuma igisubizo n'inkomoko zavuzwe kugira ngo ubone ko amakuru yerekanwa neza. Suzuma niba igisubizo gikemura neza ikibazo cy'umukoresha mugihe gikemera neza ingorane zose mu makuru aboneka. Tanga igisubizo mu rurimi rumwe n'ikibazo cy'umukoresha.\n\nNyamuneka genzura iki gisubizo cy'amategeko gikurikira cyakozwe na AI ukurikije izi ntambwe:\n\n1. Kwirengagiza amahinduka:\n   - Soma igisubizo cyakozwe hanyuma ugereranye n'urwego rwahawe.\n   - Vuga niba hari itandukaniro hagati y'igisubizo n'ibikoresho by'inkomoko. Niba ari byo, sobanura iryo hinduka ryamenyekanye.\n\n2. Kwemeza inkomoko:\n   - Kuri buri nkomoko yavuzwe mu gisubizo cyakozwe, emeza ko amakuru yatanzwe ashyigikiwe n'urwego rwahawe.\n   - Sobanura aho ubushobozi bwa buri nkomoko bwasangiye mu makuru yahawe, kandi umenye itandukaniro cyangwa amakosa yose.\n\n3. Kwuzuza amabwiriza:\n   - Genzura niba system prompt yakurikijwe, harimo n'igisubizo cyakozwe gifite amahuriro menshi y'inkomoko ukoresheje metadata ifite intego (nk'amazina y'inyandiko, amazina ya dosiye, cyangwa izindi ziranga zisobanura) muri format y'inyuguti [Izina ry'Inkomoko] aho kugira amahuriro rusange akabarwa.\n   - Suzuma niba igisubizo gikora ibitekerezo bidashyigikiwe cyangwa ntibishaka amahuriro ku bice by'ingenzi.\n\n4. Gutanga raporo:\n   - Erekana ibisubizo by'ikurikirana ryo gusuzuma udakoze ibisobanuro bwe cyangwa ubushakashatsi bw'inyongera.\n   - Niba ibintu byose ari byo, emeza ko igisubizo cyemejwe nta mahinduka.\n   - Niba ibintu byose atari byo, tanga urutonde rw'amabwiriza ku byo bigomba gukosozwa.\n\nShyira ubugenzurzi bwawe gusa ku makuru yahawe kandi ntukore ibitekerezo cyangwa inyongera zawe. Menya neza ko raporo ikubiye neza ku kwizera kw'isesengura ryasuwe kandi atari ubushakashatsi bushya bwigenga.",
    },
    dd: {
      documentDraftingPrompt:
        "Uri umufasha w'amategeko ufasha gukora inyandiko z'amategeko. Tanga igisubizo cyuzuye kandi cyukuri gishingiye ku makuru yahawe. Menya neza ko igisubizo gitangwa mu rurimi rumwe n'ikibazo cy'umukoresha cya mbere.",
      legalIssuesPrompt:
        "Menya hanyuma usuzume ibibazo by'amategeko by'ingenzi mu rwego rwahawe. Tanga isesengura ryubatswe rifite intego idasobanutse n'ingaruka zishoboka.",
      memoPrompt:
        "Kora memorandum y'amategeko ikemura ibibazo byagaragajwe. Shiramo amakuru, ibibazo by'amategeko, isesengura, n'imyanzuro mu buryo bwubatswe.",
      combinePrompt:
        "Umukoresha yatanze ikibazo n'inyandiko nyinshi, hakoreshejwe memo nyinshi ku bice bitandukanye by'inyandiko hamwe na memo zishoboka ku bibazo by'amategeko birebana. Ufite umurimo wo guhuza ibyo bisubizo byinshi mu gisubizo kimwe gifitanye isano. Kusanya amakuru ava mu bisubizo hanyuma ubyongeramo mu kibazo kimwe cyuzuye, utavuge ko igisubizo gishingiye ku memo nyinshi, ahubwo usubiraho nkaho usubiraho ikibazo cya mbere cy'umukoresha. Wibande ku kubika ukuri mugihe utanga igisubizo cyuzuye gishingiye ku makuru yahawe. Menya neza ko igisubizo gitangwa mu rurimi rumwe n'ikibazo cy'umukoresha cya mbere.",
    },
    cdb: {
      // Prompts za CDB zikora imirimo ya sisitemu
      // kandi mu gihe kino ntizafashwe mu ndimi zinyuranye
    },
  },
};

export default translations;
