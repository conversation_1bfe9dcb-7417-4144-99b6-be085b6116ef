import type { ServerTranslations } from "../../types/locales";

const translations: ServerTranslations = {
  common: {
    thinking: "Tänker...",
  },
  errors: {
    auth: {
      "authentication-required": "Autentisering krävs",
      "insufficient-permissions": "Otillräckliga behörigheter",
    },
    ai: {
      ollama: {
        "connection-failed":
          "Din Ollama-instans kunde inte nås eller svarar inte. Se till att API-servern körs och att din anslutningsinformation är korrekt.",
        "empty-response": "Ollamas textsvar var tomt.",
        "stream-failed": "Kunde inte strömma chatten. {{message}}",
        "no-endpoint": "Ingen Ollama API-endpoint har angetts.",
        "no-token-limit": "Ingen Ollama-token-kontextgräns har angetts.",
        "provider-not-responding":
          "Den lokala AI-motorn svarar inte, antingen på grund av att servern är offline eller överbelastad.",
        "provider-unavailable":
          "Den lokala AI-servern är för närvarande inte tillgänglig. Vänligen kontrollera installationen och se till att den är uppstartad och inte är överbelastad.",
      },
      azure: {
        "no-endpoint": "Ingen Azure API-endpoint har angetts.",
        "no-key": "Ingen Azure API-nyckel har angetts.",
        "no-model": "Ingen Azure OpenAI-modell har angetts.",
        "no-model-pref":
          "Ingen OPEN_AI_MODEL_PREF ENV definierad. Detta måste vara namnet på en distribution på ditt Azure-konto för en LLM-chattmodell som GPT-3.5.",
      },
      openai: {
        "no-key": "Ingen OpenAI API-nyckel har angetts.",
      },
      mistral: {
        "no-key": "Ingen Mistral API-nyckel har angetts.",
      },
      litellm: {
        "no-base-path":
          "LiteLLM måste ha en giltig bassökväg för att använda API:et.",
        "no-model": "LiteLLM måste ha en giltig modell inställd.",
        "no-token-limit": "Ingen token-kontextgräns har angetts.",
      },
      textgenwebui: {
        "no-base-path":
          "TextGenWebUI måste ha en giltig bassökväg för att använda API:et.",
        "no-token-limit": "Ingen token-kontextgräns har angetts.",
      },
      localai: {
        "no-token-limit": "Ingen LocalAi-token-kontextgräns har angetts.",
      },
      lmstudio: {
        "no-token-limit": "Ingen LMStudio-token-kontextgräns har angetts.",
      },
      fireworks: {
        "invalid-model":
          "FireworksAI chatt: {{model}} är inte giltig för chattfullbordan!",
      },
      context: {
        header: "Källa(or)",
      },
    },
    system: {
      "ssl-boot-failed":
        "SSL-start misslyckades: {{message}} - återgår till HTTP-start.",
      "no-app": 'Ingen "app" definierad - kraschar!',
      validation: {
        "invalid-transcription-provider":
          "{{input}} är inte en giltig transkriptionsmodelleverantör.",
        "invalid-safety-setting":
          "Ogiltig säkerhetsinställning. Måste vara en av {{modes}}.",
        "invalid-model-type": "Ogiltig modelltyp. Måste vara en av {{models}}.",
        "invalid-embedding-model":
          "Ogiltig inbäddningsmodelltyp. Måste vara en av {{supported}}.",
      },
    },
    document: {
      "sync-failed":
        "Kunde inte synkronisera dokument {{filename}} efter {{attempts}} försök.",
      "invalid-metadata":
        "Dokument {{filename}} har ogiltig eller saknad metadata.",
      removed:
        "Dokument {{filename}} har tagits bort från uppsättningen övervakade dokument.",
    },
    validation: {
      "value-not-number": "Värdet är inte ett nummer.",
      "value-must-be-nonzero": "Värdet måste vara skilt från noll.",
      "value-cannot-be-negative": "Värdet kan inte vara mindre än 0.",
      "invalid-serp-provider": "Ogiltig SERP-leverantör.",
      "max-rerank-limit-min":
        "Maximal omrangordningsgräns måste vara minst 10.",
      "max-rerank-limit-max":
        "Maximal omrangordningsgräns får inte överstiga 200.",
    },
    embedding: {
      "lmstudio-failed": "LMStudio-inbäddning misslyckades: {{errors}}",
      "jina-failed": "Jina-inbäddning misslyckades: {{errors}}",
    },
    env: {
      "jina-api-key-error": "Jina API-nyckeln måste börja med jina_",
      "empty-value": "Värdet kan inte vara tomt",
      "non-zero-required": "Värdet måste vara större än noll",
      "must-be-number": "Värdet måste vara ett nummer",
      "invalid-url": "URL:en är inte en giltig URL",
      "openai-key-format": "OpenAI-nyckeln måste börja med sk-",
      "contextual-prompt-format":
        "Kontextuell användarfråga måste innehålla {file} & {chunk}",
      "anthropic-key-format": "Anthropic-nyckeln måste börja med sk-ant-",
      "external-llm-url-format": "URL:en måste innehålla /v1",
      "url-no-trailing-slash": "URL:en kan inte sluta med ett snedstreck",
      "invalid-tts-provider": "{{input}} är inte en giltig TTS-leverantör",
      "invalid-whisper-model":
        "{{input}} är inte ett giltigt val av Whisper-modell",
      "invalid-llm-provider": "{{input}} är inte en giltig LLM-leverantör",
    },
    workspace: {
      onlyOwnerShare: "Endast arbetsområdets ägare kan dela arbetsområden",
      onlyOwnerRevoke:
        "Endast arbetsområdets ägare kan återkalla delning av arbetsområden",
      shareFailed: "Det gick inte att dela arbetsområdet",
      revokeFailed: "Det gick inte att återkalla delning av arbetsområdet",
      getStatusFailed:
        "Det gick inte att hämta delningsstatus för arbetsområdet",
    },
    thread: {
      onlyOwnerShare: "Endast ägare av tråden kan dela trådar",
      onlyOwnerRevoke: "Endast ägare av tråden kan återkalla delning av trådar",
      shareFailed: "Det gick inte att dela tråden",
      revokeFailed: "Det gick inte att återkalla delning av tråden",
      getStatusFailed: "Det gick inte att hämta delningsstatus för tråden",
    },
  },
  validation: {
    responseHeader: "Genererat Svar",
    contextHeader: "Ursprunglig kontext och källor",
    content: {
      "user-question-sources-header": "ANVÄNDARFRÅGA OCH KÄLLOR",
      "system-response-header": "SYSTEMSVAR",
      "not-provided": "Ej tillhandahållet",
    },
  },
  manualWorkEstimator: {
    content: {
      "user-question-header": "ANVÄNDARFRÅGA",
      "system-response-header": "SYSTEMSVAR",
    },
  },
  notifications: {
    document: {
      invalid:
        "Dokument {{filename}} har ingen metadata, är trasigt eller ogiltigt och har tagits bort.",
      removed:
        "Dokument {{filename}} har tagits bort från uppsättningen övervakade dokument.",
    },
  },
  docx: {
    title: "Chatt-export",
    exportedOn: "Exporterad",
    downloadedOn: "Nedladdad",
    keywords: "chatt, export",
    description: "Exporterat chattmeddelande",
    tokenCount: "Antal tokens",
    canvasDocumentTitle: "Canvas-dokument",
    errors: {
      contentRequired: "Innehåll krävs",
      writingFile: "Fel vid skrivning av temporär fil",
      generating: "Fel vid generering av dokument",
    },
  },
  deep_search: {
    instruction:
      "Om några DeepSearch-källor är relevanta för användarens fråga och används i svaret, ange klickbara länkar till dessa källor i slutet av meddelandet i exakt detta format: 'Källor: [Källtitel 1](URL1), [Källtitel 2](URL2)'. Den klickbara texten för varje länk måste vara en kortfattad beskrivning av källans innehåll (t.ex. sidtitel eller sammanfattning). Referera aldrig till DeepSearch-källor med nummer i texten (som [1] eller [Källa 1]); hänvisa alltid till källan med dess namn eller innehåll direkt. Använd inte akademiska citatformat.",
  },
  prompts: {
    lqa: {
      systemPrompt:
        "Du är en juridisk assistent med expertis inom juridik. Ditt mål är att ge korrekt och användbar juridisk information baserad på de källor som är tillgängliga för dig. När du svarar på frågor, citera relevanta källor där det är möjligt, och tydliggör när du ger allmän information kontra specifik juridisk rådgivning. Förtydliga alltid att svaret är avsett som ett första utkast och att källor behöver verifieras. När du inte vet eller är osäker på något, erkänn tydligt begränsningarna snarare än att göra antaganden. Ge svaret på samma språk som användarens fråga.",
      validationPrompt:
        "Du är en juridisk kvalitetssäkringsexpert. Din uppgift är att utvärdera noggrannheten, fullständigheten och användbarheten av AI-genererade juridiska svar. Analysera både svaret och de citerade källorna för att säkerställa att informationen presenteras korrekt. Bedöm om svaret på ett adekvat sätt adresserar användarens fråga samtidigt som det korrekt erkänner eventuella begränsningar i den tillgängliga informationen. Ge svaret på samma språk som användarens fråga.\n\nVänligen validera följande AI-genererade juridiska svar genom att följa dessa steg:\n\n1. Identifiering av avvikelser:\n   - Läs igenom det genererade svaret och jämför det noggrant med den tillhandahållna kontexten.\n   - Sammanfatta om det finns några diskrepanser mellan svaret och källmaterialet. Om så är fallet, beskriv vilken avvikelse som har identifierats.\n\n2. Validering av källor:\n   - För varje källa som nämns i det genererade svaret, bekräfta att informationen som tillhandahålls faktiskt stöds av den tillhandahållna kontexten.\n   - Förklara var stöd för varje källa har hittats i den tillhandahållna informationen, och notera eventuella skillnader eller felaktigheter.\n\n3. Uppfyllelse av instruktioner:\n   - Verifiera att systempromten har följts, inklusive att det genererade svaret innehåller korrekta källreferenser med meningsfulla metadata (såsom dokumenttitlar, filnamn eller andra beskrivande identifierare) inom parentesformat [Källnamn] snarare än generiska numrerade referenser.\n   - Kontrollera att svaret inte gör några osubstantierade påståenden eller saknar referenser för viktiga delar.\n\n4. Rapportering:\n   - Presentera resultaten av valideringskontrollerna utan att ge personliga tolkningar eller ytterligare undersökningar.\n   - Om allt är korrekt, bekräfta att svaret är validerat utan avvikelser.\n   - Om allt inte är korrekt, ge en punktlista med instruktioner om vad som behöver korrigeras.\n\nBasera din validering enbart på den tillhandahållna informationen och gör inga antaganden eller tillägg på egen hand. Säkerställ att rapporten är precis om tillförlitligheten av den granskade analysen och inte en ny oberoende undersökning.",
    },
    dd: {
      documentDraftingPrompt:
        "Du är en juridisk assistent som hjälper till med att skriva juridiska dokument. Ge ett omfattande och korrekt svar baserat på den tillhandahållna informationen. Säkerställ att svaret ges på samma språk som den ursprungliga användarfrågan.",
      legalIssuesPrompt:
        "Identifiera och analysera de viktigaste juridiska frågorna i det givna scenariot. Ge en strukturerad analys med tydlig reasoning och potentiella konsekvenser.",
      memoPrompt:
        "Skapa ett juridiskt memorandum som adresserar de presenterade frågorna. Inkludera fakta, juridiska frågor, analys och slutsatser i ett strukturerat format.",
      combinePrompt:
        "En användare har tillhandahållit en fråga och flera dokument, på vilka flera memorandums om olika delar av dokumentkorpusen har tillhandahållits tillsammans med möjliga memorandums om juridiska frågor som är involverade. Du har i uppdrag att kombinera dessa multipla utdata till ett sammanhängande svar. Samla informationen från dessa svar och syntetisera den till ett enda omfattande svar, utan att nämna att svaret är baserat på flera memorandums, utan svara som om du svarar på den ursprungliga användarfrågan. Fokusera på att bibehålla noggrannheten samtidigt som du ger ett omfattande svar baserat på den tillhandahållna informationen. Säkerställ att svaret ges på samma språk som den ursprungliga användarfrågan.",
    },
    cdb: {
      // CDB-prompts hanteras av flödessystemet
      // och är för närvarande inte internationaliserade
    },
  },
};

export default translations;
