import { EventLogs } from "../eventLogs";
import prisma from "../../utils/prisma";
import { useTimerCleanup } from "../../tests/helpers/timerCleanup";
import { event_logs as PrismaEventLog } from "@prisma/client";

// Mock dependencies
jest.mock("../../utils/prisma", () => ({
  __esModule: true,
  default: {
    event_logs: {
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      deleteMany: jest.fn(),
    },
  },
}));

// Mock the dynamic User import
jest.mock("../user", () => ({
  User: {
    get: jest.fn(),
  },
}));

// Mock console.error to avoid noise in tests
const originalConsoleError = console.error;
beforeAll(() => {
  console.error = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
});

describe("EventLogs Model", () => {
  // Setup automatic timer cleanup
  useTimerCleanup();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("logEvent", () => {
    const mockEventLog: PrismaEventLog = {
      id: 1,
      event: "user.login",
      metadata: '{"ip":"127.0.0.1"}',
      userId: 123,
      occurredAt: new Date("2024-01-01T00:00:00Z"),
    };

    test("should successfully log an event with all parameters", async () => {
      (prisma.event_logs.create as jest.Mock).mockResolvedValue(mockEventLog);

      const result = await EventLogs.logEvent(
        "user.login",
        { ip: "127.0.0.1" },
        123
      );

      expect(prisma.event_logs.create).toHaveBeenCalledWith({
        data: {
          event: "user.login",
          metadata: '{"ip":"127.0.0.1"}',
          userId: 123,
          occurredAt: expect.any(Date),
        },
      });
      expect(result.eventLog).toEqual(mockEventLog);
      expect(result.message).toBeNull();
    });

    test("should log event with empty metadata object", async () => {
      (prisma.event_logs.create as jest.Mock).mockResolvedValue({
        ...mockEventLog,
        metadata: "{}",
      });

      const result = await EventLogs.logEvent("user.logout");

      expect(prisma.event_logs.create).toHaveBeenCalledWith({
        data: {
          event: "user.logout",
          metadata: "{}",
          userId: null,
          occurredAt: expect.any(Date),
        },
      });
      expect(result.eventLog).toBeTruthy();
      expect(result.message).toBeNull();
    });

    test("should log event with null metadata", async () => {
      (prisma.event_logs.create as jest.Mock).mockResolvedValue({
        ...mockEventLog,
        metadata: null,
      });

      // Pass null explicitly as second parameter
      const result = await EventLogs.logEvent("user.logout", null as any);

      expect(prisma.event_logs.create).toHaveBeenCalledWith({
        data: {
          event: "user.logout",
          metadata: null,
          userId: null,
          occurredAt: expect.any(Date),
        },
      });
      expect(result.eventLog).toBeTruthy();
      expect(result.message).toBeNull();
    });

    test("should log event without userId", async () => {
      (prisma.event_logs.create as jest.Mock).mockResolvedValue({
        ...mockEventLog,
        userId: null,
      });

      const result = await EventLogs.logEvent("system.startup", {
        version: "1.0.0",
      });

      expect(prisma.event_logs.create).toHaveBeenCalledWith({
        data: {
          event: "system.startup",
          metadata: '{"version":"1.0.0"}',
          userId: null,
          occurredAt: expect.any(Date),
        },
      });
      expect(result.eventLog).toBeTruthy();
      expect(result.message).toBeNull();
    });

    test("should handle database errors gracefully", async () => {
      const error = new Error("Database connection failed");
      (prisma.event_logs.create as jest.Mock).mockRejectedValue(error);

      const result = await EventLogs.logEvent("user.action", {}, 456);

      expect(console.error).toHaveBeenCalledWith(
        "[Event Logging Failed] - user.action",
        "Database connection failed"
      );
      expect(result.eventLog).toBeNull();
      expect(result.message).toBe("Database connection failed");
    });

    test("should handle non-Error exceptions", async () => {
      (prisma.event_logs.create as jest.Mock).mockRejectedValue("String error");

      const result = await EventLogs.logEvent("test.event");

      expect(console.error).toHaveBeenCalledWith(
        "[Event Logging Failed] - test.event",
        "String error"
      );
      expect(result.eventLog).toBeNull();
      expect(result.message).toBe("String error");
    });
  });

  describe("getByEvent", () => {
    const mockLogs: PrismaEventLog[] = [
      {
        id: 1,
        event: "user.login",
        metadata: null,
        userId: 1,
        occurredAt: new Date("2024-01-01"),
      },
      {
        id: 2,
        event: "user.login",
        metadata: null,
        userId: 2,
        occurredAt: new Date("2024-01-02"),
      },
    ];

    test("should retrieve logs by event name with default ordering", async () => {
      (prisma.event_logs.findMany as jest.Mock).mockResolvedValue(mockLogs);

      const result = await EventLogs.getByEvent("user.login");

      expect(prisma.event_logs.findMany).toHaveBeenCalledWith({
        where: { event: "user.login" },
        orderBy: { occurredAt: "desc" },
      });
      expect(result).toEqual(mockLogs);
    });

    test("should apply limit when provided", async () => {
      (prisma.event_logs.findMany as jest.Mock).mockResolvedValue([
        mockLogs[0],
      ]);

      const result = await EventLogs.getByEvent("user.login", 1);

      expect(prisma.event_logs.findMany).toHaveBeenCalledWith({
        where: { event: "user.login" },
        take: 1,
        orderBy: { occurredAt: "desc" },
      });
      expect(result).toHaveLength(1);
    });

    test("should apply custom ordering when provided", async () => {
      (prisma.event_logs.findMany as jest.Mock).mockResolvedValue(mockLogs);

      const result = await EventLogs.getByEvent("user.login", null, {
        id: "asc",
      });

      expect(prisma.event_logs.findMany).toHaveBeenCalledWith({
        where: { event: "user.login" },
        orderBy: { id: "asc" },
      });
      expect(result).toEqual(mockLogs);
    });

    test("should apply offset when provided", async () => {
      (prisma.event_logs.findMany as jest.Mock).mockResolvedValue([
        mockLogs[1],
      ]);

      const result = await EventLogs.getByEvent("user.login", null, null, 1);

      expect(prisma.event_logs.findMany).toHaveBeenCalledWith({
        where: { event: "user.login" },
        skip: 1,
        orderBy: { occurredAt: "desc" },
      });
      expect(result).toHaveLength(1);
    });

    test("should apply all parameters when provided", async () => {
      (prisma.event_logs.findMany as jest.Mock).mockResolvedValue([
        mockLogs[1],
      ]);

      const result = await EventLogs.getByEvent(
        "user.login",
        5,
        { userId: "desc" },
        10
      );

      expect(prisma.event_logs.findMany).toHaveBeenCalledWith({
        where: { event: "user.login" },
        take: 5,
        skip: 10,
        orderBy: { userId: "desc" },
      });
      expect(result).toBeTruthy();
    });

    test("should handle database errors and return empty array", async () => {
      const error = new Error("Query failed");
      (prisma.event_logs.findMany as jest.Mock).mockRejectedValue(error);

      const result = await EventLogs.getByEvent("user.login");

      expect(console.error).toHaveBeenCalledWith("Query failed");
      expect(result).toEqual([]);
    });

    test("should handle non-Error exceptions", async () => {
      (prisma.event_logs.findMany as jest.Mock).mockRejectedValue(
        "String error"
      );

      const result = await EventLogs.getByEvent("user.login");

      expect(console.error).toHaveBeenCalledWith("String error");
      expect(result).toEqual([]);
    });
  });

  describe("getByUserId", () => {
    const mockUserLogs: PrismaEventLog[] = [
      {
        id: 3,
        event: "user.update",
        metadata: null,
        userId: 456,
        occurredAt: new Date("2024-01-03"),
      },
      {
        id: 4,
        event: "user.delete",
        metadata: null,
        userId: 456,
        occurredAt: new Date("2024-01-04"),
      },
    ];

    test("should retrieve logs by userId with default ordering", async () => {
      (prisma.event_logs.findMany as jest.Mock).mockResolvedValue(mockUserLogs);

      const result = await EventLogs.getByUserId(456);

      expect(prisma.event_logs.findMany).toHaveBeenCalledWith({
        where: { userId: 456 },
        orderBy: { occurredAt: "desc" },
      });
      expect(result).toEqual(mockUserLogs);
    });

    test("should apply limit when provided", async () => {
      (prisma.event_logs.findMany as jest.Mock).mockResolvedValue([
        mockUserLogs[0],
      ]);

      const result = await EventLogs.getByUserId(456, 1);

      expect(prisma.event_logs.findMany).toHaveBeenCalledWith({
        where: { userId: 456 },
        take: 1,
        orderBy: { occurredAt: "desc" },
      });
      expect(result).toHaveLength(1);
    });

    test("should apply custom ordering when provided", async () => {
      (prisma.event_logs.findMany as jest.Mock).mockResolvedValue(mockUserLogs);

      const result = await EventLogs.getByUserId(456, null, { event: "asc" });

      expect(prisma.event_logs.findMany).toHaveBeenCalledWith({
        where: { userId: 456 },
        orderBy: { event: "asc" },
      });
      expect(result).toEqual(mockUserLogs);
    });

    test("should handle database errors and return empty array", async () => {
      const error = new Error("Database error");
      (prisma.event_logs.findMany as jest.Mock).mockRejectedValue(error);

      const result = await EventLogs.getByUserId(789);

      expect(console.error).toHaveBeenCalledWith("Database error");
      expect(result).toEqual([]);
    });

    test("should handle non-Error exceptions", async () => {
      (prisma.event_logs.findMany as jest.Mock).mockRejectedValue(12345);

      const result = await EventLogs.getByUserId(789);

      expect(console.error).toHaveBeenCalledWith("12345");
      expect(result).toEqual([]);
    });
  });

  describe("where", () => {
    const mockFilteredLogs: PrismaEventLog[] = [
      {
        id: 5,
        event: "system.error",
        metadata: '{"level":"critical"}',
        userId: null,
        occurredAt: new Date("2024-01-05"),
      },
    ];

    test("should query with empty where clause", async () => {
      (prisma.event_logs.findMany as jest.Mock).mockResolvedValue(
        mockFilteredLogs
      );

      const result = await EventLogs.where();

      expect(prisma.event_logs.findMany).toHaveBeenCalledWith({
        where: {},
        orderBy: { occurredAt: "desc" },
      });
      expect(result).toEqual(mockFilteredLogs);
    });

    test("should query with complex where clause", async () => {
      (prisma.event_logs.findMany as jest.Mock).mockResolvedValue(
        mockFilteredLogs
      );

      const whereClause = {
        event: "system.error",
        userId: 999,
        customField: "value",
      };

      const result = await EventLogs.where(whereClause);

      expect(prisma.event_logs.findMany).toHaveBeenCalledWith({
        where: whereClause,
        orderBy: { occurredAt: "desc" },
      });
      expect(result).toEqual(mockFilteredLogs);
    });

    test("should apply all query parameters", async () => {
      (prisma.event_logs.findMany as jest.Mock).mockResolvedValue([]);

      const result = await EventLogs.where(
        { event: "test" },
        10,
        { id: "asc" },
        5
      );

      expect(prisma.event_logs.findMany).toHaveBeenCalledWith({
        where: { event: "test" },
        take: 10,
        skip: 5,
        orderBy: { id: "asc" },
      });
      expect(result).toEqual([]);
    });

    test("should handle database errors", async () => {
      const error = new Error("Where query failed");
      (prisma.event_logs.findMany as jest.Mock).mockRejectedValue(error);

      const result = await EventLogs.where({ event: "error" });

      expect(console.error).toHaveBeenCalledWith("Where query failed");
      expect(result).toEqual([]);
    });
  });

  describe("whereWithData", () => {
    const mockLog: PrismaEventLog = {
      id: 6,
      event: "user.action",
      metadata: null,
      userId: 123,
      occurredAt: new Date("2024-01-06"),
    };

    test("should enhance logs with user data", async () => {
      // Setup EventLogs.where to return mock data
      jest.spyOn(EventLogs, "where").mockResolvedValue([mockLog]);

      // Mock dynamic import and User.get
      const { User } = await import("../user");
      (User.get as jest.Mock).mockResolvedValue({
        id: 123,
        username: "testuser",
      });

      const result = await EventLogs.whereWithData({ event: "user.action" });

      expect(EventLogs.where).toHaveBeenCalledWith(
        { event: "user.action" },
        null,
        null,
        null
      );
      expect(User.get).toHaveBeenCalledWith({ id: 123 });
      expect(result).toEqual([
        {
          ...mockLog,
          user: { username: "testuser" },
        },
      ]);
    });

    test("should handle logs without userId", async () => {
      const logWithoutUser: PrismaEventLog = {
        ...mockLog,
        userId: null,
      };

      jest.spyOn(EventLogs, "where").mockResolvedValue([logWithoutUser]);

      const result = await EventLogs.whereWithData();

      expect(result).toEqual([
        {
          ...logWithoutUser,
          user: { username: "unknown user" },
        },
      ]);
    });

    test("should handle user not found", async () => {
      jest.spyOn(EventLogs, "where").mockResolvedValue([mockLog]);

      const { User } = await import("../user");
      (User.get as jest.Mock).mockResolvedValue(null);

      const result = await EventLogs.whereWithData({ userId: 123 });

      expect(result).toEqual([
        {
          ...mockLog,
          user: { username: "unknown user" },
        },
      ]);
    });

    test("should handle user without username", async () => {
      jest.spyOn(EventLogs, "where").mockResolvedValue([mockLog]);

      const { User } = await import("../user");
      (User.get as jest.Mock).mockResolvedValue({
        id: 123,
        username: null,
      });

      const result = await EventLogs.whereWithData();

      expect(result).toEqual([
        {
          ...mockLog,
          user: { username: "unknown user" },
        },
      ]);
    });

    test("should pass through all parameters to where method", async () => {
      jest.spyOn(EventLogs, "where").mockResolvedValue([]);

      await EventLogs.whereWithData({ event: "test" }, 20, 15, {
        occurredAt: "asc",
      });

      expect(EventLogs.where).toHaveBeenCalledWith(
        { event: "test" },
        20,
        { occurredAt: "asc" },
        15
      );
    });

    test("should handle errors gracefully", async () => {
      const error = new Error("Enhancement failed");
      jest.spyOn(EventLogs, "where").mockRejectedValue(error);

      const result = await EventLogs.whereWithData();

      expect(console.error).toHaveBeenCalledWith("Enhancement failed");
      expect(result).toEqual([]);
    });

    test("should handle non-Error exceptions", async () => {
      jest.spyOn(EventLogs, "where").mockRejectedValue({ code: "ERR_001" });

      const result = await EventLogs.whereWithData();

      expect(console.error).toHaveBeenCalledWith("[object Object]");
      expect(result).toEqual([]);
    });
  });

  describe("count", () => {
    test("should count all logs when no clause provided", async () => {
      (prisma.event_logs.count as jest.Mock).mockResolvedValue(100);

      const result = await EventLogs.count();

      expect(prisma.event_logs.count).toHaveBeenCalledWith({
        where: {},
      });
      expect(result).toBe(100);
    });

    test("should count logs matching where clause", async () => {
      (prisma.event_logs.count as jest.Mock).mockResolvedValue(25);

      const result = await EventLogs.count({ event: "user.login" });

      expect(prisma.event_logs.count).toHaveBeenCalledWith({
        where: { event: "user.login" },
      });
      expect(result).toBe(25);
    });

    test("should handle database errors and return 0", async () => {
      const error = new Error("Count failed");
      (prisma.event_logs.count as jest.Mock).mockRejectedValue(error);

      const result = await EventLogs.count();

      expect(console.error).toHaveBeenCalledWith("Count failed");
      expect(result).toBe(0);
    });

    test("should handle non-Error exceptions", async () => {
      (prisma.event_logs.count as jest.Mock).mockRejectedValue(null);

      const result = await EventLogs.count();

      expect(console.error).toHaveBeenCalledWith("null");
      expect(result).toBe(0);
    });
  });

  describe("delete", () => {
    test("should delete all logs when no clause provided", async () => {
      (prisma.event_logs.deleteMany as jest.Mock).mockResolvedValue({
        count: 50,
      });

      const result = await EventLogs.delete();

      expect(prisma.event_logs.deleteMany).toHaveBeenCalledWith({
        where: {},
      });
      expect(result).toBe(true);
    });

    test("should delete logs matching where clause", async () => {
      (prisma.event_logs.deleteMany as jest.Mock).mockResolvedValue({
        count: 10,
      });

      const result = await EventLogs.delete({ userId: 123 });

      expect(prisma.event_logs.deleteMany).toHaveBeenCalledWith({
        where: { userId: 123 },
      });
      expect(result).toBe(true);
    });

    test("should handle database errors and return false", async () => {
      const error = new Error("Delete failed");
      (prisma.event_logs.deleteMany as jest.Mock).mockRejectedValue(error);

      const result = await EventLogs.delete();

      expect(console.error).toHaveBeenCalledWith("Delete failed");
      expect(result).toBe(false);
    });

    test("should handle non-Error exceptions", async () => {
      (prisma.event_logs.deleteMany as jest.Mock).mockRejectedValue(undefined);

      const result = await EventLogs.delete();

      expect(console.error).toHaveBeenCalledWith("undefined");
      expect(result).toBe(false);
    });
  });

  describe("clear", () => {
    test("should clear all event logs successfully", async () => {
      (prisma.event_logs.deleteMany as jest.Mock).mockResolvedValue({
        count: 1000,
      });

      const result = await EventLogs.clear();

      expect(prisma.event_logs.deleteMany).toHaveBeenCalledWith({});
      expect(result).toBe(true);
    });

    test("should throw error when database operation fails", async () => {
      const error = new Error("Clear operation failed");
      (prisma.event_logs.deleteMany as jest.Mock).mockRejectedValue(error);

      await expect(EventLogs.clear()).rejects.toThrow("Clear operation failed");
      expect(console.error).toHaveBeenCalledWith("Clear operation failed");
    });

    test("should handle and throw non-Error exceptions", async () => {
      const nonErrorException = { message: "Custom error object" };
      (prisma.event_logs.deleteMany as jest.Mock).mockRejectedValue(
        nonErrorException
      );

      await expect(EventLogs.clear()).rejects.toEqual(nonErrorException);
      expect(console.error).toHaveBeenCalledWith("[object Object]");
    });
  });

  describe("Concurrent Operations", () => {
    test("should handle concurrent logging operations", async () => {
      let callCount = 0;
      (prisma.event_logs.create as jest.Mock).mockImplementation(async () => {
        // Simulate async delay
        await new Promise((resolve) => setTimeout(resolve, 10));
        callCount++;
        return {
          id: callCount,
          event: `event${callCount}`,
          metadata: null,
          userId: null,
          occurredAt: new Date(),
        };
      });

      const promises = Array.from({ length: 5 }, (_, i) =>
        EventLogs.logEvent(`concurrent.event.${i}`)
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(5);
      results.forEach((result, _index) => {
        expect(result.eventLog).toBeTruthy();
        expect(result.message).toBeNull();
      });
      expect(prisma.event_logs.create).toHaveBeenCalledTimes(5);
    });

    test("should handle concurrent read operations", async () => {
      // Restore any spied methods first
      if (jest.isMockFunction(EventLogs.where)) {
        (EventLogs.where as jest.Mock).mockRestore();
      }

      (prisma.event_logs.findMany as jest.Mock).mockImplementation(async () => {
        // Simulate async delay
        await new Promise((resolve) => setTimeout(resolve, 5));
        return [];
      });

      const promises = [
        EventLogs.getByEvent("event1"),
        EventLogs.getByUserId(1),
        EventLogs.where({ event: "event2" }),
        EventLogs.getByEvent("event3"),
      ];

      const results = await Promise.all(promises);

      expect(results).toHaveLength(4);
      results.forEach((result) => {
        expect(Array.isArray(result)).toBe(true);
      });
    });
  });

  describe("Edge Cases", () => {
    test("should handle extremely large metadata objects", async () => {
      const largeMetadata = {
        data: Array(1000).fill("x").join(""),
        nested: {
          deep: {
            value: Array(100)
              .fill(0)
              .map((_, i) => ({ id: i, value: `item${i}` })),
          },
        },
      };

      (prisma.event_logs.create as jest.Mock).mockResolvedValue({
        id: 1,
        event: "large.metadata",
        metadata: JSON.stringify(largeMetadata),
        userId: null,
        occurredAt: new Date(),
      });

      const result = await EventLogs.logEvent("large.metadata", largeMetadata);

      expect(result.eventLog).toBeTruthy();
      expect(result.message).toBeNull();
    });

    test("should handle special characters in event names", async () => {
      const specialEvent = "user.action:login/attempt@2024";
      (prisma.event_logs.create as jest.Mock).mockResolvedValue({
        id: 1,
        event: specialEvent,
        metadata: null,
        userId: null,
        occurredAt: new Date(),
      });

      const result = await EventLogs.logEvent(specialEvent);

      expect(prisma.event_logs.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          event: specialEvent,
        }),
      });
      expect(result.eventLog?.event).toBe(specialEvent);
    });

    test("should handle null/undefined in metadata properly", async () => {
      const metadataWithNulls = {
        field1: null,
        field2: undefined,
        field3: "value",
      };

      (prisma.event_logs.create as jest.Mock).mockResolvedValue({
        id: 1,
        event: "test",
        metadata: JSON.stringify(metadataWithNulls),
        userId: null,
        occurredAt: new Date(),
      });

      const result = await EventLogs.logEvent("test", metadataWithNulls);

      const callArgs = (prisma.event_logs.create as jest.Mock).mock.calls[0][0];
      const parsedMetadata = JSON.parse(callArgs.data.metadata);

      expect(parsedMetadata.field1).toBeNull();
      expect(parsedMetadata.field2).toBeUndefined();
      expect(parsedMetadata.field3).toBe("value");
      expect(result.eventLog).toBeTruthy();
    });

    test("should handle numeric zero as userId", async () => {
      (prisma.event_logs.create as jest.Mock).mockResolvedValue({
        id: 1,
        event: "test",
        metadata: "{}",
        userId: null, // zero is falsy, so it becomes null
        occurredAt: new Date(),
      });

      const result = await EventLogs.logEvent("test", {}, 0);

      expect(prisma.event_logs.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: null, // zero is falsy, so it becomes null
        }),
      });
      expect(result.eventLog?.userId).toBe(null);
    });
  });

  describe("Integration with TypeScript Types", () => {
    test("WhereClause interface should accept various types", async () => {
      // Restore any spied methods first
      if (jest.isMockFunction(EventLogs.where)) {
        (EventLogs.where as jest.Mock).mockRestore();
      }

      const validWhereClauses = [
        { event: "test" },
        { userId: 123 },
        { event: "test", userId: 123 },
        { customField: "value", anotherField: 456 },
        {},
      ];

      for (const clause of validWhereClauses) {
        (prisma.event_logs.findMany as jest.Mock).mockResolvedValue([]);
        const result = await EventLogs.where(clause);
        expect(Array.isArray(result)).toBe(true);
      }
    });

    test("OrderByClause interface should enforce asc/desc values", async () => {
      const validOrderByClauses: Array<{ [key: string]: "asc" | "desc" }> = [
        { occurredAt: "desc" as const },
        { id: "asc" as const },
        { event: "desc" as const, userId: "asc" as const },
      ];

      for (const orderBy of validOrderByClauses) {
        (prisma.event_logs.findMany as jest.Mock).mockResolvedValue([]);
        const result = await EventLogs.getByEvent("test", null, orderBy);
        expect(Array.isArray(result)).toBe(true);
      }
    });
  });
});
