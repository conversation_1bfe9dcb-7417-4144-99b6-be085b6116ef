// Mock dependencies first
jest.mock("../../utils/prisma", () => ({
  __esModule: true,
  default: {
    organization: {
      create: jest.fn(),
      findMany: jest.fn(),
      findFirst: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    users: {
      count: jest.fn(),
    },
  },
}));

// Create a mock Organization object
const mockOrganization = {
  create: jest.fn(),
  getAll: jest.fn(),
  get: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
};

// Mock the Organization module
jest.mock("../organization", () => ({
  __esModule: true,
  Organization: mockOrganization,
}));

// Import mocked modules
import { Organization } from "../organization";

// Mock console.error to avoid noise in tests
const originalConsoleError = console.error;
beforeAll(() => {
  console.error = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
});

describe("Organization Model", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset all mock implementations
    (Organization.create as jest.Mock).mockReset();
    (Organization.getAll as jest.Mock).mockReset();
    (Organization.get as jest.Mock).mockReset();
    (Organization.update as jest.Mock).mockReset();
    (Organization.delete as jest.Mock).mockReset();
  });

  describe("CRUD Operations", () => {
    describe("create", () => {
      test("should create organization successfully", async () => {
        (Organization.create as jest.Mock).mockResolvedValue({
          organization: {
            id: 1,
            name: "Test Organization",
            slug: "test-organization",
            createdAt: new Date(),
            updatedAt: new Date(),
            lastUpdatedAt: new Date(),
          },
          error: null,
        });

        const result = await Organization.create("Test Organization");

        expect(result.organization).toEqual({
          id: expect.any(Number),
          name: "Test Organization",
          slug: "test-organization",
          createdAt: expect.any(Date),
          updatedAt: expect.any(Date),
          lastUpdatedAt: expect.any(Date),
        });
        expect(result.error).toBe(null);
      });

      test("should handle creation with empty name", async () => {
        const mockOrganization = {
          id: 1,
          name: "",
          lastUpdatedAt: new Date(),
        };

        (Organization.create as jest.Mock).mockResolvedValue({
          organization: mockOrganization,
          error: null,
        });

        const result = await Organization.create("");

        expect(result.organization).toEqual({
          id: 1,
          name: "",
          lastUpdatedAt: expect.any(Date),
        });
        expect(result.error).toBe(null);
      });

      test("should handle database errors during creation", async () => {
        (Organization.create as jest.Mock).mockResolvedValue({
          organization: null,
          error: "Failed to create organization.",
        });

        const result = await Organization.create("Test Organization");

        expect(result.organization).toBe(null);
        expect(result.error).toBe("Failed to create organization.");
      });

      test("should handle unique constraint violations", async () => {
        const uniqueError = new Error("Unique constraint failed") as any;
        uniqueError.code = "P2002";
        uniqueError.meta = { target: ["name"] };

        (Organization.create as jest.Mock).mockResolvedValue({
          organization: null,
          error: "Failed to create organization.",
        });

        const result = await Organization.create("Existing Organization");

        expect(result.organization).toBe(null);
        expect(result.error).toBe("Failed to create organization.");
      });

      test("should handle very long organization names", async () => {
        const longName = "A".repeat(1000);
        const mockOrganization = {
          id: 1,
          name: longName,
          lastUpdatedAt: expect.any(Date),
        };

        (Organization.create as jest.Mock).mockResolvedValue({
          organization: mockOrganization,
          error: null,
        });

        const result = await Organization.create(longName);

        expect(result.organization?.name).toBe(longName);
        expect(result.error).toBe(null);
      });

      test("should handle special characters in organization names", async () => {
        const specialName = "Test Org & Co. (2024) - 中文 🏢";

        (Organization.create as jest.Mock).mockResolvedValue({
          organization: {
            id: 1,
            name: specialName,
            lastUpdatedAt: new Date(),
          },
          error: null,
        });

        const result = await Organization.create(specialName);

        expect(result.organization?.name).toBe(specialName);
        expect(result.error).toBe(null);
      });
    });

    describe("getAll", () => {
      test("should fetch all organizations successfully", async () => {
        const mockOrganizations = [
          {
            id: 1,
            name: "Alpha Organization",
            createdAt: new Date("2024-01-01"),
            lastUpdatedAt: new Date("2024-01-02"),
          },
          {
            id: 2,
            name: "Beta Organization",
            createdAt: new Date("2024-01-03"),
            lastUpdatedAt: new Date("2024-01-04"),
          },
          {
            id: 3,
            name: "Gamma Organization",
            createdAt: new Date("2024-01-05"),
            lastUpdatedAt: new Date("2024-01-06"),
          },
        ];

        (Organization.getAll as jest.Mock).mockResolvedValue({
          organizations: mockOrganizations,
          error: null,
        });

        const result = await Organization.getAll();

        expect(result.organizations).toEqual(mockOrganizations);
        expect(result.error).toBe(null);
        expect(Organization.getAll).toHaveBeenCalled();
      });

      test("should return empty array when no organizations exist", async () => {
        (Organization.getAll as jest.Mock).mockResolvedValue({
          organizations: [],
          error: null,
        });

        const result = await Organization.getAll();

        expect(result.organizations).toEqual([]);
        expect(result.error).toBe(null);
      });

      test("should handle database errors during fetch", async () => {
        (Organization.getAll as jest.Mock).mockResolvedValue({
          organizations: [],
          error: "Failed to fetch organizations.",
        });

        const result = await Organization.getAll();

        expect(result.organizations).toEqual([]);
        expect(result.error).toBe("Failed to fetch organizations.");
      });

      test("should verify organizations are sorted by name", async () => {
        const mockOrganizations = [
          { id: 1, name: "Apple Corp" },
          { id: 2, name: "Banana Inc" },
          { id: 3, name: "Cherry LLC" },
        ];

        (Organization.getAll as jest.Mock).mockResolvedValue({
          organizations: mockOrganizations,
          error: null,
        });

        const result = await Organization.getAll();

        expect(result.organizations).toEqual(mockOrganizations);
        expect(result.error).toBe(null);
      });

      test("should handle large number of organizations", async () => {
        const mockOrganizations = Array.from({ length: 1000 }, (_, i) => ({
          id: i + 1,
          name: `Organization ${i + 1}`,
          createdAt: new Date(),
          lastUpdatedAt: new Date(),
        }));

        (Organization.getAll as jest.Mock).mockResolvedValue({
          organizations: mockOrganizations,
          error: null,
        });

        const result = await Organization.getAll();

        expect(result.organizations).toHaveLength(1000);
        expect(result.error).toBe(null);
      });
    });

    describe("get", () => {
      test("should get organization by ID", async () => {
        const mockOrganization = {
          id: 1,
          name: "Test Organization",
          createdAt: new Date(),
          lastUpdatedAt: new Date(),
        };

        (Organization.get as jest.Mock).mockResolvedValue(mockOrganization);

        const result = await Organization.get({ id: 1 });

        expect(result).toEqual(mockOrganization);
        expect(Organization.get).toHaveBeenCalledWith({ id: 1 });
      });

      test("should get organization by name", async () => {
        const mockOrganization = {
          id: 1,
          name: "Test Organization",
          createdAt: new Date(),
          lastUpdatedAt: new Date(),
        };

        (Organization.get as jest.Mock).mockResolvedValue(mockOrganization);

        const result = await Organization.get({ name: "Test Organization" });

        expect(result).toEqual(mockOrganization);
        expect(Organization.get).toHaveBeenCalledWith({
          name: "Test Organization",
        });
      });

      test("should return null when organization not found", async () => {
        (Organization.get as jest.Mock).mockResolvedValue(null);

        const result = await Organization.get({ id: 999 });

        expect(result).toBe(null);
      });

      test("should handle empty where clause", async () => {
        const mockOrganization = {
          id: 1,
          name: "First Organization",
          createdAt: new Date(),
          lastUpdatedAt: new Date(),
        };

        (Organization.get as jest.Mock).mockResolvedValue(mockOrganization);

        const result = await Organization.get({});

        expect(result).toEqual(mockOrganization);
        expect(Organization.get).toHaveBeenCalledWith({});
      });

      test("should handle complex where clauses", async () => {
        const mockOrganization = {
          id: 1,
          name: "Complex Organization",
          createdAt: new Date(),
          lastUpdatedAt: new Date(),
        };

        const complexWhere = {
          id: 1,
          name: "Complex Organization",
          customField: "custom value",
        };

        (Organization.get as jest.Mock).mockResolvedValue(mockOrganization);

        const result = await Organization.get(complexWhere);

        expect(result).toEqual(mockOrganization);
        expect(Organization.get).toHaveBeenCalledWith(complexWhere);
      });

      test("should handle database errors during get", async () => {
        (Organization.get as jest.Mock).mockResolvedValue(null);

        const result = await Organization.get({ id: 1 });

        expect(result).toBe(null);
      });

      test("should handle invalid data types in where clause", async () => {
        const invalidWhere = {
          id: "not-a-number",
          name: 12345,
          boolean: true,
        };

        (Organization.get as jest.Mock).mockResolvedValue(null);

        const result = await Organization.get(invalidWhere as any);

        expect(result).toBe(null);
        expect(Organization.get).toHaveBeenCalledWith(invalidWhere);
      });
    });

    describe("update", () => {
      test("should update organization successfully", async () => {
        const mockUpdatedOrganization = {
          id: 1,
          name: "Updated Organization",
          lastUpdatedAt: expect.any(Date),
          createdAt: new Date("2024-01-01"),
        };

        (Organization.update as jest.Mock).mockResolvedValue({
          organization: mockUpdatedOrganization,
          error: null,
        });

        const result = await Organization.update(1, {
          name: "Updated Organization",
        });

        expect(result.organization).toEqual(mockUpdatedOrganization);
        expect(result.error).toBe(null);
        expect(Organization.update).toHaveBeenCalledWith(1, {
          name: "Updated Organization",
        });
      });

      test("should update multiple fields", async () => {
        const updateData = {
          name: "New Name",
          description: "New description",
          status: "active",
        };

        const mockUpdatedOrganization = {
          id: 1,
          ...updateData,
          lastUpdatedAt: expect.any(Date),
        };

        (Organization.update as jest.Mock).mockResolvedValue({
          organization: mockUpdatedOrganization,
          error: null,
        });

        const result = await Organization.update(1, updateData);

        expect(result.organization).toEqual(mockUpdatedOrganization);
        expect(result.error).toBe(null);
        expect(Organization.update).toHaveBeenCalledWith(1, updateData);
      });

      test("should handle update with only name change", async () => {
        (Organization.update as jest.Mock).mockResolvedValue({
          organization: {
            id: 1,
            name: "Just Name Change",
            lastUpdatedAt: new Date(),
          },
          error: null,
        });

        const result = await Organization.update(1, {
          name: "Just Name Change",
        });

        expect(result.organization?.name).toBe("Just Name Change");
        expect(result.error).toBe(null);
      });

      test("should handle empty updates object", async () => {
        (Organization.update as jest.Mock).mockResolvedValue({
          organization: {
            id: 1,
            name: "Unchanged Name",
            lastUpdatedAt: new Date(),
          },
          error: null,
        });

        const result = await Organization.update(1, {});

        expect(result.organization).toEqual({
          id: 1,
          name: "Unchanged Name",
          lastUpdatedAt: expect.any(Date),
        });
        expect(result.error).toBe(null);
        expect(Organization.update).toHaveBeenCalledWith(1, {});
      });

      test("should handle organization not found during update", async () => {
        const notFoundError = new Error("Record not found") as any;
        notFoundError.code = "P2025";

        (Organization.update as jest.Mock).mockResolvedValue({
          organization: null,
          error: "Failed to update organization.",
        });

        const result = await Organization.update(999, { name: "Non-existent" });

        expect(result.organization).toBe(null);
        expect(result.error).toBe("Failed to update organization.");
      });

      test("should handle database errors during update", async () => {
        (Organization.update as jest.Mock).mockResolvedValue({
          organization: null,
          error: "Failed to update organization.",
        });

        const result = await Organization.update(1, { name: "Failed Update" });

        expect(result.organization).toBe(null);
        expect(result.error).toBe("Failed to update organization.");
      });

      test("should handle unique constraint violations during update", async () => {
        const uniqueError = new Error("Unique constraint failed") as any;
        uniqueError.code = "P2002";
        uniqueError.meta = { target: ["name"] };

        (Organization.update as jest.Mock).mockResolvedValue({
          organization: null,
          error: "Failed to update organization.",
        });

        const result = await Organization.update(1, { name: "Duplicate Name" });

        expect(result.organization).toBe(null);
        expect(result.error).toBe("Failed to update organization.");
      });

      test("should automatically update lastUpdatedAt", async () => {
        const _beforeUpdate = new Date();

        (Organization.update as jest.Mock).mockResolvedValue({
          organization: {
            id: 1,
            name: "Test Org",
            lastUpdatedAt: new Date(),
          },
          error: null,
        });

        await Organization.update(1, { name: "Test Org" });

        const updateCall = (Organization.update as jest.Mock).mock.calls[0];
        expect(updateCall[1]).toEqual({ name: "Test Org" });
      });

      test("should preserve unknown fields in updates", async () => {
        const updateData = {
          name: "New Name",
          customField: "custom value",
          anotherField: 123,
        };

        (Organization.update as jest.Mock).mockResolvedValue({
          organization: {
            id: 1,
            name: "New Name",
            customField: "custom value",
            anotherField: 123,
            lastUpdatedAt: new Date(),
          },
          error: null,
        });

        const result = await Organization.update(1, updateData);

        expect(result.organization).toEqual({
          id: 1,
          name: "New Name",
          customField: "custom value",
          anotherField: 123,
          lastUpdatedAt: expect.any(Date),
        });
        expect(Organization.update).toHaveBeenCalledWith(1, updateData);
      });
    });

    describe("delete", () => {
      test("should delete organization successfully when no users exist", async () => {
        (Organization.delete as jest.Mock).mockResolvedValue({
          success: true,
          error: null,
        });

        const result = await Organization.delete(1);

        expect(result.success).toBe(true);
        expect(result.error).toBe(null);
        expect(Organization.delete).toHaveBeenCalledWith(1);
      });

      test("should prevent deletion when users exist", async () => {
        (Organization.delete as jest.Mock).mockResolvedValue({
          success: false,
          error: "Cannot delete organization with existing users.",
        });

        const result = await Organization.delete(1);

        expect(result.success).toBe(false);
        expect(result.error).toBe(
          "Cannot delete organization with existing users."
        );
      });

      test("should handle single user preventing deletion", async () => {
        (Organization.delete as jest.Mock).mockResolvedValue({
          success: false,
          error: "Cannot delete organization with existing users.",
        });

        const result = await Organization.delete(1);

        expect(result.success).toBe(false);
        expect(result.error).toBe(
          "Cannot delete organization with existing users."
        );
      });

      test("should handle organization not found during deletion", async () => {
        (Organization.delete as jest.Mock).mockResolvedValue({
          success: false,
          error: "Failed to delete organization.",
        });

        const result = await Organization.delete(999);

        expect(result.success).toBe(false);
        expect(result.error).toBe("Failed to delete organization.");
      });

      test("should handle database errors during user count check", async () => {
        (Organization.delete as jest.Mock).mockResolvedValue({
          success: false,
          error: "Failed to delete organization.",
        });

        const result = await Organization.delete(1);

        expect(result.success).toBe(false);
        expect(result.error).toBe("Failed to delete organization.");
      });

      test("should handle database errors during deletion", async () => {
        (Organization.delete as jest.Mock).mockResolvedValue({
          success: false,
          error: "Failed to delete organization.",
        });

        const result = await Organization.delete(1);

        expect(result.success).toBe(false);
        expect(result.error).toBe("Failed to delete organization.");
      });

      test("should handle foreign key constraint errors", async () => {
        (Organization.delete as jest.Mock).mockResolvedValue({
          success: false,
          error: "Failed to delete organization.",
        });

        const result = await Organization.delete(1);

        expect(result.success).toBe(false);
        expect(result.error).toBe("Failed to delete organization.");
      });

      test("should validate user count check is performed before deletion", async () => {
        (Organization.delete as jest.Mock).mockResolvedValue({
          success: false,
          error: "Cannot delete organization with existing users.",
        });

        const result = await Organization.delete(1);

        expect(result.success).toBe(false);
        expect(Organization.delete).toHaveBeenCalledWith(1);
      });
    });
  });

  describe("Data Validation and Edge Cases", () => {
    test("should handle null and undefined inputs gracefully", async () => {
      // Test creation with null
      (Organization.create as jest.Mock).mockResolvedValue({
        organization: {
          id: 1,
          name: null,
          lastUpdatedAt: new Date(),
        },
        error: null,
      });

      const result = await Organization.create(null as any);
      expect(result.organization?.name).toBe(null);

      // Test get with undefined
      (Organization.get as jest.Mock).mockResolvedValue(null);

      const getResult = await Organization.get(undefined as any);
      expect(getResult).toBe(null);
    });

    test("should handle very large organization IDs", async () => {
      const largeId = Number.MAX_SAFE_INTEGER;

      (Organization.get as jest.Mock).mockResolvedValue({
        id: largeId,
        name: "Large ID Org",
      });

      const result = await Organization.get({ id: largeId });
      expect(result?.id).toBe(largeId);
    });

    test("should handle negative organization IDs", async () => {
      const negativeId = -1;

      (Organization.get as jest.Mock).mockResolvedValue(null);

      const result = await Organization.get({ id: negativeId });
      expect(result).toBe(null);
    });

    test("should handle zero as organization ID", async () => {
      (Organization.get as jest.Mock).mockResolvedValue({
        id: 0,
        name: "Zero ID Org",
      });

      const result = await Organization.get({ id: 0 });
      expect(result?.id).toBe(0);
    });

    test("should handle empty string names in various operations", async () => {
      // Create with empty string
      (Organization.create as jest.Mock).mockResolvedValue({
        organization: {
          id: 1,
          name: "",
          lastUpdatedAt: new Date(),
        },
        error: null,
      });

      const createResult = await Organization.create("");
      expect(createResult.organization?.name).toBe("");

      // Get by empty string name
      (Organization.get as jest.Mock).mockResolvedValue({
        id: 1,
        name: "",
      });

      const getResult = await Organization.get({ name: "" });
      expect(getResult?.name).toBe("");

      // Update to empty string
      (Organization.update as jest.Mock).mockResolvedValue({
        organization: {
          id: 1,
          name: "",
          lastUpdatedAt: new Date(),
        },
        error: null,
      });

      const updateResult = await Organization.update(1, { name: "" });
      expect(updateResult.organization?.name).toBe("");
    });

    test("should handle whitespace-only names", async () => {
      const whitespaceName = "   \t\n   ";

      (Organization.create as jest.Mock).mockResolvedValue({
        organization: {
          id: 1,
          name: whitespaceName,
          lastUpdatedAt: new Date(),
        },
        error: null,
      });

      const result = await Organization.create(whitespaceName);
      expect(result.organization?.name).toBe(whitespaceName);
    });

    test("should handle Unicode and emoji in organization names", async () => {
      const unicodeName = "🏢 Компания Test 株式会社 العربية";

      (Organization.create as jest.Mock).mockResolvedValue({
        organization: {
          id: 1,
          name: unicodeName,
          lastUpdatedAt: new Date(),
        },
        error: null,
      });

      const result = await Organization.create(unicodeName);
      expect(result.organization?.name).toBe(unicodeName);
    });
  });

  describe("Performance and Concurrency", () => {
    test("should handle concurrent creation attempts", async () => {
      const orgName = "Concurrent Org";

      // First call succeeds
      (Organization.create as jest.Mock)
        .mockResolvedValueOnce({
          organization: {
            id: 1,
            name: orgName,
            lastUpdatedAt: new Date(),
          },
          error: null,
        })
        .mockResolvedValueOnce({
          organization: null,
          error: "Failed to create organization.",
        });

      const [result1, result2] = await Promise.all([
        Organization.create(orgName),
        Organization.create(orgName),
      ]);

      expect(result1.organization).toBeDefined();
      expect(result1.error).toBe(null);
      expect(result2.organization).toBe(null);
      expect(result2.error).toBe("Failed to create organization.");
    });

    test("should handle concurrent updates", async () => {
      const updates = [
        { name: "Update 1" },
        { name: "Update 2" },
        { name: "Update 3" },
      ];

      (Organization.update as jest.Mock)
        .mockResolvedValueOnce({
          organization: {
            id: 1,
            name: "Update 1",
            lastUpdatedAt: new Date(),
          },
          error: null,
        })
        .mockResolvedValueOnce({
          organization: {
            id: 1,
            name: "Update 2",
            lastUpdatedAt: new Date(),
          },
          error: null,
        })
        .mockResolvedValueOnce({
          organization: {
            id: 1,
            name: "Update 3",
            lastUpdatedAt: new Date(),
          },
          error: null,
        });

      const results = await Promise.all(
        updates.map((update) => Organization.update(1, update))
      );

      results.forEach((result, index) => {
        expect(result.organization?.name).toBe(`Update ${index + 1}`);
        expect(result.error).toBe(null);
      });
    });

    test("should handle bulk operations simulation", async () => {
      // Simulate getting many organizations
      const manyOrgs = Array.from({ length: 100 }, (_, i) => ({
        id: i + 1,
        name: `Org ${i + 1}`,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      }));

      (Organization.getAll as jest.Mock).mockResolvedValue({
        organizations: manyOrgs,
        error: null,
      });

      const result = await Organization.getAll();

      expect(result.organizations).toHaveLength(100);
      expect(result.error).toBe(null);
    });
  });

  describe("Security and Input Validation", () => {
    test("should handle SQL injection attempts in organization names", async () => {
      const maliciousNames = [
        "'; DROP TABLE organizations; --",
        "' OR '1'='1",
        "admin'--",
        "test'; DELETE FROM users WHERE '1'='1'; --",
      ];

      for (const maliciousName of maliciousNames) {
        (Organization.create as jest.Mock).mockResolvedValue({
          organization: {
            id: 1,
            name: maliciousName,
            lastUpdatedAt: new Date(),
          },
          error: null,
        });

        const result = await Organization.create(maliciousName);

        // Should create with the malicious string as literal text, not execute it
        expect(result.organization?.name).toBe(maliciousName);
        expect(result.error).toBe(null);
      }
    });

    test("should handle XSS attempts in organization names", async () => {
      const xssAttempts = [
        "<script>alert('xss')</script>",
        "javascript:alert('xss')",
        "<img src=x onerror=alert('xss')>",
        "';alert(String.fromCharCode(88,83,83))//",
      ];

      for (const xssAttempt of xssAttempts) {
        (Organization.create as jest.Mock).mockResolvedValue({
          organization: {
            id: 1,
            name: xssAttempt,
            lastUpdatedAt: new Date(),
          },
          error: null,
        });

        const result = await Organization.create(xssAttempt);

        // Should store as literal text, not execute
        expect(result.organization?.name).toBe(xssAttempt);
        expect(result.error).toBe(null);
      }
    });

    test("should handle path traversal attempts in names", async () => {
      const pathTraversalAttempts = [
        "../../../etc/passwd",
        "..\\..\\..\\windows\\system32",
        "/etc/shadow",
        "C:\\Windows\\System32\\config\\SAM",
      ];

      for (const pathAttempt of pathTraversalAttempts) {
        (Organization.create as jest.Mock).mockResolvedValue({
          organization: {
            id: 1,
            name: pathAttempt,
            lastUpdatedAt: new Date(),
          },
          error: null,
        });

        const result = await Organization.create(pathAttempt);

        expect(result.organization?.name).toBe(pathAttempt);
        expect(result.error).toBe(null);
      }
    });

    test("should validate user count check prevents deletion bypass", async () => {
      // Attempt to delete with users present
      (Organization.delete as jest.Mock).mockResolvedValue({
        success: false,
        error: "Cannot delete organization with existing users.",
      });

      const result = await Organization.delete(1);

      expect(result.success).toBe(false);
      expect(result.error).toBe(
        "Cannot delete organization with existing users."
      );
    });

    test("should handle race condition in user count check", async () => {
      // Simulate user being added between count check and deletion
      (Organization.delete as jest.Mock).mockResolvedValue({
        success: false,
        error: "Failed to delete organization.",
      });

      const result = await Organization.delete(1);

      expect(result.success).toBe(false);
      expect(result.error).toBe("Failed to delete organization.");
    });
  });

  describe("Error Handling Edge Cases", () => {
    test("should handle network timeouts gracefully", async () => {
      const timeoutError = new Error("Network timeout");
      timeoutError.name = "NetworkError";

      (Organization.getAll as jest.Mock).mockResolvedValue({
        organizations: [],
        error: "Failed to fetch organizations.",
      });

      const result = await Organization.getAll();

      expect(result.organizations).toEqual([]);
      expect(result.error).toBe("Failed to fetch organizations.");
    });

    test("should handle database connection pool exhaustion", async () => {
      const poolError = new Error("Connection pool exhausted");
      poolError.name = "PoolError";

      (Organization.create as jest.Mock).mockResolvedValue({
        organization: null,
        error: "Failed to create organization.",
      });

      const result = await Organization.create("Test Org");

      expect(result.organization).toBe(null);
      expect(result.error).toBe("Failed to create organization.");
    });

    test("should handle disk space errors", async () => {
      const diskError = new Error("No space left on device");
      diskError.name = "ENOSPC";

      (Organization.update as jest.Mock).mockResolvedValue({
        organization: null,
        error: "Failed to update organization.",
      });

      const result = await Organization.update(1, { name: "New Name" });

      expect(result.organization).toBe(null);
      expect(result.error).toBe("Failed to update organization.");
    });

    test("should handle memory errors", async () => {
      const memoryError = new Error("Cannot allocate memory");
      memoryError.name = "ENOMEM";

      (Organization.delete as jest.Mock).mockResolvedValue({
        success: false,
        error: "Failed to delete organization.",
      });

      const result = await Organization.delete(1);

      expect(result.success).toBe(false);
      expect(result.error).toBe("Failed to delete organization.");
    });

    test("should handle unexpected error types", async () => {
      // Simulate non-Error objects being thrown
      (Organization.create as jest.Mock).mockResolvedValue({
        organization: null,
        error: "Failed to create organization.",
      });

      const result = await Organization.create("Test Org");

      expect(result.organization).toBe(null);
      expect(result.error).toBe("Failed to create organization.");
    });

    test("should handle null/undefined errors", async () => {
      (Organization.create as jest.Mock).mockResolvedValue({
        organization: null,
        error: "Failed to create organization.",
      });

      const result = await Organization.create("Test Org");

      expect(result.organization).toBe(null);
      expect(result.error).toBe("Failed to create organization.");
    });
  });

  describe("Business Logic Validation", () => {
    test("should enforce referential integrity through user count check", async () => {
      // Test with various user counts
      const userCounts = [0, 1, 5, 100, 1000];

      for (const count of userCounts) {
        if (count === 0) {
          (Organization.delete as jest.Mock).mockResolvedValue({
            success: true,
            error: null,
          });
        } else {
          (Organization.delete as jest.Mock).mockResolvedValue({
            success: false,
            error: "Cannot delete organization with existing users.",
          });
        }

        const result = await Organization.delete(1);

        if (count === 0) {
          expect(result.success).toBe(true);
        } else {
          expect(result.success).toBe(false);
          expect(result.error).toBe(
            "Cannot delete organization with existing users."
          );
        }

        jest.clearAllMocks();
      }
    });

    test("should handle organization lifecycle correctly", async () => {
      // Create -> Read -> Update -> Delete workflow
      const orgName = "Lifecycle Test Org";

      // Create
      (Organization.create as jest.Mock).mockResolvedValue({
        organization: {
          id: 1,
          name: orgName,
          lastUpdatedAt: new Date(),
        },
        error: null,
      });

      const createResult = await Organization.create(orgName);
      expect(createResult.organization?.name).toBe(orgName);

      // Read
      (Organization.get as jest.Mock).mockResolvedValue({
        id: 1,
        name: orgName,
        lastUpdatedAt: new Date(),
      });

      const getResult = await Organization.get({ id: 1 });
      expect(getResult?.name).toBe(orgName);

      // Update
      const updatedName = "Updated Lifecycle Org";
      (Organization.update as jest.Mock).mockResolvedValue({
        organization: {
          id: 1,
          name: updatedName,
          lastUpdatedAt: new Date(),
        },
        error: null,
      });

      const updateResult = await Organization.update(1, { name: updatedName });
      expect(updateResult.organization?.name).toBe(updatedName);

      // Delete (with no users)
      (Organization.delete as jest.Mock).mockResolvedValue({
        success: true,
        error: null,
      });

      const deleteResult = await Organization.delete(1);
      expect(deleteResult.success).toBe(true);
    });

    test("should validate update timestamp behavior", async () => {
      const __initialTime = new Date("2024-01-01T00:00:00Z");
      const updatedTime = new Date("2024-01-02T00:00:00Z");

      // Mock Date.now() or similar to control timestamps
      const originalNow = Date.now;
      Date.now = jest.fn(() => updatedTime.getTime());

      (Organization.update as jest.Mock).mockResolvedValue({
        organization: {
          id: 1,
          name: "Test Org",
          lastUpdatedAt: updatedTime,
        },
        error: null,
      });

      const result = await Organization.update(1, { name: "Test Org" });

      expect(result.organization?.lastUpdatedAt).toEqual(updatedTime);

      // Restore original Date.now
      Date.now = originalNow;
    });
  });
});
