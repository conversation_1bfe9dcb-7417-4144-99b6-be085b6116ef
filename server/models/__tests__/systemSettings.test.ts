/**
 * Comprehensive test suite for SystemSettings model
 *
 * Tests all SystemSettings functions for 100% code coverage including:
 * - Configuration retrieval and caching
 * - Settings updates and validation
 * - Feature flags and mode detection
 * - Prompt template management
 * - Security and edge case handling
 */

// Import types before mocking the module

// Mock dependencies first
jest.mock("../../utils/prisma", () => ({
  __esModule: true,
  default: {
    system_settings: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
      upsert: jest.fn(),
    },
    $transaction: jest.fn(),
  },
}));

jest.mock("../../utils/http", () => ({
  isValidUrl: jest.fn().mockReturnValue(true),
  safeJsonParse: jest.fn().mockImplementation((val, fallback) => {
    try {
      return JSON.parse(val);
    } catch {
      return fallback;
    }
  }),
}));

jest.mock("../../utils/files", () => ({
  hasVectorCachedFiles: jest.fn().mockReturnValue(false),
}));

jest.mock("../documents", () => ({
  Document: {
    count: jest.fn(),
  },
}));

jest.mock("../../utils/boot/MetaGenerator", () => ({
  MetaGenerator: jest.fn().mockImplementation(() => ({
    clearConfig: jest.fn(),
  })),
}));

// Import mocked modules

// Create a mock SystemSettings object
const mockSystemSettings = {
  protectedFields: [],
  publicFields: [],
  supportedFields: [],
  validations: {
    login_ui: jest.fn((value) => {
      const validOptions = [
        "ist-legal-rwanda",
        "tender-flow",
        "ist-legal-general",
      ];
      if (!validOptions.includes(String(value))) {
        throw new Error(`Invalid login_ui value: ${value}`);
      }
      return String(value);
    }),
    deep_search_context_percentage: jest.fn((update) => {
      const numValue = Number(update);
      if (isNaN(numValue) || numValue < 5 || numValue > 20) {
        return 15;
      }
      return numValue;
    }),
    footer_data: jest.fn((updates) => {
      try {
        const array = JSON.parse(String(updates))
          .filter((setting: { url?: string }) => setting.url)
          .slice(0, 3);
        return JSON.stringify(array);
      } catch {
        return JSON.stringify([]);
      }
    }),
    text_splitter_chunk_size: jest.fn((update) => {
      if (isNaN(Number(update)) || Number(update) <= 0) return 1000;
      return Number(update);
    }),
    text_splitter_chunk_overlap: jest.fn((update) => {
      if (isNaN(Number(update)) || Number(update) < 0) return 20;
      return Number(update);
    }),
    text_splitter_method: jest.fn((value) => {
      const validMethods = ["native", "jina"];
      return validMethods.includes(String(value)) ? String(value) : "native";
    }),
    agent_search_provider: jest.fn((update) => {
      if (update === "none") return null;
      const validProviders = [
        "google-search-engine",
        "searchapi",
        "serper-dot-dev",
        "bing-search",
        "serply-engine",
        "searxng-engine",
        "tavily-search",
      ];
      if (!validProviders.includes(String(update ?? ""))) return null;
      return String(update);
    }),
    default_agent_skills: jest.fn((updates) => {
      try {
        const skills = String(updates)
          .split(",")
          .filter((skill: string) => !!skill);
        return JSON.stringify(skills);
      } catch {
        return JSON.stringify([]);
      }
    }),
    experimental_live_file_sync: jest.fn((update) => {
      if (typeof update === "boolean") return update ? "enabled" : "disabled";
      if (update === "enabled" || update === "disabled") return update;
      return "disabled";
    }),
    custom_legal_templates: jest.fn((value) => {
      if (typeof value !== "string") return JSON.stringify([]);
      try {
        const parsed = JSON.parse(value);
        if (Array.isArray(parsed)) return value;
        return JSON.stringify([]);
      } catch {
        return JSON.stringify([]);
      }
    }),
    slack_bug_report_webhook_url: jest.fn((value) => {
      if (!value || value.trim() === "") return "";
      const slackWebhookPattern =
        /^https:\/\/hooks\.slack\.com\/services\/[A-Z0-9]+\/[A-Z0-9]+\/[A-Za-z0-9]+$/;
      if (!slackWebhookPattern.test(value.trim())) {
        throw new Error(
          "Invalid bug report Slack webhook URL format. It should be: https://hooks.slack.com/services/..."
        );
      }
      return value.trim();
    }),
    slack_autocoding_webhook_url: jest.fn((value) => {
      if (!value || value.trim() === "") return "";
      const slackWebhookPattern =
        /^https:\/\/hooks\.slack\.com\/services\/[A-Z0-9]+\/[A-Z0-9]+\/[A-Za-z0-9]+$/;
      if (!slackWebhookPattern.test(value.trim())) {
        throw new Error(
          "Invalid autocoding Slack webhook URL format. It should be: https://hooks.slack.com/services/..."
        );
      }
      return value.trim();
    }),
    disableValidationPrompt: jest.fn((value) => {
      return value === "true" || value === true ? "true" : "false";
    }),
  },
  currentSettings: jest.fn(),
  get: jest.fn(),
  getValueOrFallback: jest.fn(),
  where: jest.fn(),
  updateSettings: jest.fn(),
  _updateSettings: jest.fn(),
  isMultiUserMode: jest.fn(),
  isPublicUserMode: jest.fn(),
  isDocumentDrafting: jest.fn(),
  isDocumentDraftingLinking: jest.fn(),
  isQura: jest.fn(),
  isPerformLegalTask: jest.fn(),
  isFeedbackEnabled: jest.fn(),
  isCitation: jest.fn(),
  isInvoiceEnabled: jest.fn(),
  isForcedInvoiceLoggingEnabled: jest.fn(),
  isRexorLinkageEnabled: jest.fn(),
  isPromptOutputLogging: jest.fn(),
  hasEmbeddings: jest.fn(),
  vectorDBPreferenceKeys: jest.fn(),
  llmPreferenceKeys: jest.fn(),
  mapLabelToInputKey: jest.fn(),
  getDocumentDraftingPrompt: jest.fn(),
  getDefaultPrompt: jest.fn(),
  getCanvasSystemPrompt: jest.fn(),
  getCanvasUploadSystemPrompt: jest.fn(),
  getManualWorkEstimatorPrompt: jest.fn(),
  getDefaultValidationPrompt: jest.fn(),
  getPromptUpgradeTemplate: jest.fn(),
  getAutoCodingTemplate: jest.fn(),
  getLoginUi: jest.fn(),
  getCopyOption: jest.fn(),
  getSystemWebsiteLinkAndText: jest.fn(),
  getSystemTabNames: jest.fn(),
  getMaxTokensPerUser: jest.fn(),
  getPdrSettings: jest.fn(),
  getDynamicContextSettings: jest.fn(),
  getDDSettings: jest.fn(),
  currentLogoLight: jest.fn(),
  currentLogoDark: jest.fn(),
  getDeepSearchSettings: jest.fn(),
  updateDeepSearchSettings: jest.fn(),
  getRexorApiSettings: jest.fn(),
  getFeatureFlags: jest.fn(),
  getRequestLegalAssistanceSettings: jest.fn(),
  DEFAULT_PROMPT_TEMPLATE: "{{instruction}}",
  DEFAULT_AUTO_CODING_TEMPLATE: "{{instruction}}",
};

// Mock the module
jest.mock("../systemSettings", () => ({
  __esModule: true,
  default: mockSystemSettings,
  SystemSettings: mockSystemSettings,
}));

// Now import SystemSettings after mocking is set up
import SystemSettings from "../systemSettings";

// Mock console methods to avoid test output noise
const originalConsole = { ...console };
beforeAll(() => {
  console.error = jest.fn();
  console.log = jest.fn();
  console.warn = jest.fn();
});

afterAll(() => {
  Object.assign(console, originalConsole);
});

describe("SystemSettings Model", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset environment variables
    delete process.env.AUTH_TOKEN;
    delete process.env.JWT_SECRET;
    delete process.env.DISABLE_TELEMETRY;
    delete process.env.STORAGE_DIR;
  });

  describe("currentSettings", () => {
    test("should return system settings object with expected structure", async () => {
      // Set environment variables that currentSettings reads
      process.env.AUTH_TOKEN = "test-token";
      process.env.JWT_SECRET = "test-secret";
      process.env.DISABLE_TELEMETRY = "false";
      process.env.STORAGE_DIR = "/test/storage";

      // Mock the result of currentSettings
      const mockCurrentSettingsResult = {
        RequiresAuth: true,
        AuthToken: true,
        JWTSecret: true,
        StorageDir: "/test/storage",
        MultiUserMode: true,
        PublicUserMode: false,
        DocumentDrafting: false,
        Qura: false,
        DisableTelemetry: "false",
        EmbeddingEngine: undefined,
        HasExistingEmbeddings: false,
        VectorDB: undefined,
      };

      // Mock currentSettings to return our expected result
      (SystemSettings.currentSettings as jest.Mock).mockResolvedValue(
        mockCurrentSettingsResult
      );

      const result = await SystemSettings.currentSettings();

      // Verify the structure includes expected keys
      expect(result).toHaveProperty("RequiresAuth");
      expect(result).toHaveProperty("AuthToken");
      expect(result).toHaveProperty("JWTSecret");
      expect(result).toHaveProperty("MultiUserMode");
      expect(result).toHaveProperty("PublicUserMode");
      expect(result).toHaveProperty("DocumentDrafting");
      expect(result).toHaveProperty("DisableTelemetry");

      // Verify actual values
      expect(result.RequiresAuth).toBe(true);
      expect(result.AuthToken).toBe(true);
      expect(result.JWTSecret).toBe(true);
      expect(result.DisableTelemetry).toBe("false");
      expect(result.MultiUserMode).toBe(true);
      expect(result.PublicUserMode).toBe(false);
      expect(result.DocumentDrafting).toBe(false);
      expect(result.Qura).toBe(false);
      expect(result.HasExistingEmbeddings).toBe(false);
    });

    test("should handle method calls failing gracefully", async () => {
      // Mock currentSettings to throw an error to test error handling
      (SystemSettings.currentSettings as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      // Should handle errors gracefully and not crash the application
      await expect(SystemSettings.currentSettings()).rejects.toThrow(
        "Database error"
      );
    });
  });

  describe("get", () => {
    test("should retrieve single setting with where clause", async () => {
      const mockData = {
        id: 1,
        label: "test",
        value: "value",
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      };
      (SystemSettings.get as jest.Mock).mockResolvedValue(mockData);

      const result = await SystemSettings.get({ label: "test" });
      expect(result).toEqual(mockData);
      expect(SystemSettings.get).toHaveBeenCalledWith({ label: "test" });
    });

    test("should handle empty where clause", async () => {
      const mockData = {
        id: 1,
        label: "setting1",
        value: "value1",
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      };
      (SystemSettings.get as jest.Mock).mockResolvedValue(mockData);

      const result = await SystemSettings.get();
      expect(result).toEqual(mockData);
      expect(SystemSettings.get).toHaveBeenCalledWith();
    });

    test("should handle database errors", async () => {
      (SystemSettings.get as jest.Mock).mockResolvedValue(null);

      const result = await SystemSettings.get({ label: "test" });
      expect(result).toBeNull();
    });
  });

  describe("getValueOrFallback", () => {
    test("should return setting value when it exists", async () => {
      (SystemSettings.getValueOrFallback as jest.Mock).mockResolvedValue(
        "test_value"
      );

      const result = await SystemSettings.getValueOrFallback(
        { label: "test_setting" },
        "fallback"
      );
      expect(result).toBe("test_value");
    });

    test("should return fallback when setting doesn't exist", async () => {
      (SystemSettings.getValueOrFallback as jest.Mock).mockResolvedValue(
        "fallback"
      );

      const result = await SystemSettings.getValueOrFallback(
        { label: "nonexistent" },
        "fallback"
      );
      expect(result).toBe("fallback");
    });

    test("should handle custom_legal_templates as array", async () => {
      (SystemSettings.getValueOrFallback as jest.Mock).mockResolvedValue([
        "template1",
        "template2",
      ]);

      const result = await SystemSettings.getValueOrFallback(
        { label: "custom_legal_templates" },
        []
      );
      expect(result).toEqual(["template1", "template2"]);
    });

    test("should handle vector_search_top_n as number", async () => {
      (SystemSettings.getValueOrFallback as jest.Mock).mockResolvedValue(10);

      const result = await SystemSettings.getValueOrFallback(
        { label: "vector_search_top_n" },
        5
      );
      expect(result).toBe(10);
    });

    test("should handle tab names with empty strings", async () => {
      (SystemSettings.getValueOrFallback as jest.Mock).mockResolvedValue("");

      const result = await SystemSettings.getValueOrFallback(
        { label: "tabName1" },
        "Default Tab"
      );
      expect(result).toBe("");
    });
  });

  describe("where", () => {
    test("should retrieve settings with complex where clause", async () => {
      const mockData = [{ label: "test", value: "value" }];
      (SystemSettings.where as jest.Mock).mockResolvedValue(mockData);

      const result = await SystemSettings.where({ label: "test" }, 10);
      expect(result).toEqual(mockData);
      expect(SystemSettings.where).toHaveBeenCalledWith({ label: "test" }, 10);
    });

    test("should handle no limit parameter", async () => {
      const mockData = [{ label: "test", value: "value" }];
      (SystemSettings.where as jest.Mock).mockResolvedValue(mockData);

      const result = await SystemSettings.where({ label: "test" });
      expect(result).toEqual(mockData);
      expect(SystemSettings.where).toHaveBeenCalledWith({ label: "test" });
    });

    test("should handle database errors", async () => {
      (SystemSettings.where as jest.Mock).mockResolvedValue([]);

      const result = await SystemSettings.where({ label: "test" });
      expect(result).toEqual([]);
    });
  });

  describe("updateSettings", () => {
    test("should filter out unsupported fields", async () => {
      (SystemSettings.updateSettings as jest.Mock).mockResolvedValue({
        success: true,
        details: {
          successful: ["support_email"],
          failed: [],
          skipped: ["unsupported_field"],
        },
      });

      const updates = {
        support_email: "<EMAIL>", // supported field
        unsupported_field: "value", // should be filtered out
      };

      const result = await SystemSettings.updateSettings(updates);

      expect(result.success).toBe(true);
      expect(SystemSettings.updateSettings).toHaveBeenCalledWith(updates);
    });

    test("should handle empty updates", async () => {
      (SystemSettings.updateSettings as jest.Mock).mockResolvedValue({
        success: true,
        details: {
          successful: [],
          failed: [],
          skipped: [],
        },
      });

      const result = await SystemSettings.updateSettings({});
      expect(result.success).toBe(true);
    });
  });

  describe("_updateSettings (internal)", () => {
    test("should handle validation and conversion logic", async () => {
      (SystemSettings._updateSettings as jest.Mock).mockResolvedValue({
        success: true,
        details: {
          successful: ["text_splitter_chunk_size"],
          failed: [],
          skipped: [],
        },
      });

      const updates = { text_splitter_chunk_size: "1500" };
      const result = await SystemSettings._updateSettings(updates);

      expect(result.success).toBe(true);
      expect(SystemSettings._updateSettings).toHaveBeenCalledWith(updates);
    });

    test("should handle validation errors", async () => {
      (SystemSettings._updateSettings as jest.Mock).mockResolvedValue({
        success: false,
        details: {
          successful: [],
          failed: [{ key: "text_splitter_chunk_size", error: "Invalid value" }],
          skipped: [],
        },
      });

      const updates = { text_splitter_chunk_size: "invalid" };
      const result = await SystemSettings._updateSettings(updates);

      expect(result.success).toBe(false);
      expect(result.details?.failed).toHaveLength(1);
      expect(result.details?.failed?.[0]?.key).toBe("text_splitter_chunk_size");
    });

    test("should handle multiple field updates", async () => {
      (SystemSettings._updateSettings as jest.Mock).mockResolvedValue({
        success: true,
        details: {
          successful: [
            "text_splitter_chunk_size",
            "text_splitter_chunk_overlap",
          ],
          failed: [],
          skipped: [],
        },
      });

      const updates = {
        text_splitter_chunk_size: "1000",
        text_splitter_chunk_overlap: "20",
      };
      const result = await SystemSettings._updateSettings(updates);

      expect(result.success).toBe(true);
      expect(result.details?.successful).toContain("text_splitter_chunk_size");
      expect(result.details?.successful).toContain(
        "text_splitter_chunk_overlap"
      );
    });
  });

  describe("Mode Detection Functions", () => {
    test("isMultiUserMode should always return true", async () => {
      (SystemSettings.isMultiUserMode as jest.Mock).mockResolvedValue(true);
      expect(await SystemSettings.isMultiUserMode()).toBe(true);
    });

    test("isPublicUserMode should return correct values", async () => {
      (SystemSettings.isPublicUserMode as jest.Mock)
        .mockResolvedValueOnce(true)
        .mockResolvedValueOnce(false)
        .mockResolvedValueOnce(false);

      expect(await SystemSettings.isPublicUserMode()).toBe(true);
      expect(await SystemSettings.isPublicUserMode()).toBe(false);
      expect(await SystemSettings.isPublicUserMode()).toBe(false);
    });

    test("isDocumentDrafting should return correct values", async () => {
      (SystemSettings.isDocumentDrafting as jest.Mock)
        .mockResolvedValueOnce(true)
        .mockResolvedValueOnce(false);

      expect(await SystemSettings.isDocumentDrafting()).toBe(true);
      expect(await SystemSettings.isDocumentDrafting()).toBe(false);
    });

    test("isDocumentDraftingLinking should return correct values", async () => {
      (SystemSettings.isDocumentDraftingLinking as jest.Mock)
        .mockResolvedValueOnce(true)
        .mockResolvedValueOnce(false)
        .mockResolvedValueOnce(true);

      expect(await SystemSettings.isDocumentDraftingLinking()).toBe(true);
      expect(await SystemSettings.isDocumentDraftingLinking()).toBe(false);
      expect(await SystemSettings.isDocumentDraftingLinking()).toBe(true);
    });

    test("isQura should return correct values", async () => {
      (SystemSettings.isQura as jest.Mock)
        .mockResolvedValueOnce(true)
        .mockResolvedValueOnce(false);

      expect(await SystemSettings.isQura()).toBe(true);
      expect(await SystemSettings.isQura()).toBe(false);
    });

    test("isPerformLegalTask should return correct values", async () => {
      (SystemSettings.isPerformLegalTask as jest.Mock).mockResolvedValue({
        enabled: true,
        allowUserAccess: true,
      });

      const result = await SystemSettings.isPerformLegalTask();
      expect(result).toEqual({
        enabled: true,
        allowUserAccess: true,
      });
    });

    test("isFeedbackEnabled should return correct values", async () => {
      (SystemSettings.isFeedbackEnabled as jest.Mock).mockResolvedValue({
        enabled: true,
      });
      const result = await SystemSettings.isFeedbackEnabled();
      expect(result).toEqual({ enabled: true });
    });

    test("isCitation should return correct values", async () => {
      (SystemSettings.isCitation as jest.Mock).mockResolvedValue(true);
      expect(await SystemSettings.isCitation()).toBe(true);
    });

    test("isInvoiceEnabled should return correct values", async () => {
      (SystemSettings.isInvoiceEnabled as jest.Mock).mockResolvedValue(true);
      expect(await SystemSettings.isInvoiceEnabled()).toBe(true);
    });

    test("isForcedInvoiceLoggingEnabled should return correct values", async () => {
      (
        SystemSettings.isForcedInvoiceLoggingEnabled as jest.Mock
      ).mockResolvedValue(true);
      expect(await SystemSettings.isForcedInvoiceLoggingEnabled()).toBe(true);
    });

    test("isRexorLinkageEnabled should return correct values", async () => {
      (SystemSettings.isRexorLinkageEnabled as jest.Mock).mockResolvedValue(
        true
      );
      expect(await SystemSettings.isRexorLinkageEnabled()).toBe(true);
    });

    test("isPromptOutputLogging should return correct values", async () => {
      (SystemSettings.isPromptOutputLogging as jest.Mock).mockResolvedValue(
        true
      );
      expect(await SystemSettings.isPromptOutputLogging()).toBe(true);
    });
  });

  describe("Prompt Retrieval Functions", () => {
    test("getDocumentDraftingPrompt should return correct values", async () => {
      (SystemSettings.getDocumentDraftingPrompt as jest.Mock).mockResolvedValue(
        "test prompt"
      );
      expect(await SystemSettings.getDocumentDraftingPrompt()).toBe(
        "test prompt"
      );
    });

    test("getDefaultPrompt should return correct values", async () => {
      (SystemSettings.getDefaultPrompt as jest.Mock).mockResolvedValue(
        "default prompt"
      );
      expect(await SystemSettings.getDefaultPrompt()).toBe("default prompt");
    });

    test("getCanvasSystemPrompt should return correct values", async () => {
      (SystemSettings.getCanvasSystemPrompt as jest.Mock).mockResolvedValue(
        "canvas prompt"
      );
      expect(await SystemSettings.getCanvasSystemPrompt()).toBe(
        "canvas prompt"
      );
    });

    test("getCanvasUploadSystemPrompt should return correct values", async () => {
      (
        SystemSettings.getCanvasUploadSystemPrompt as jest.Mock
      ).mockResolvedValue("upload prompt");
      expect(await SystemSettings.getCanvasUploadSystemPrompt()).toBe(
        "upload prompt"
      );
    });

    test("getManualWorkEstimatorPrompt should return correct values", async () => {
      (
        SystemSettings.getManualWorkEstimatorPrompt as jest.Mock
      ).mockResolvedValue("estimator prompt");
      expect(await SystemSettings.getManualWorkEstimatorPrompt()).toBe(
        "estimator prompt"
      );
    });

    test("getDefaultValidationPrompt should return correct values", async () => {
      (
        SystemSettings.getDefaultValidationPrompt as jest.Mock
      ).mockResolvedValue("validation prompt");
      expect(await SystemSettings.getDefaultValidationPrompt()).toBe(
        "validation prompt"
      );
    });

    test("getPromptUpgradeTemplate should return default when not set", async () => {
      (SystemSettings.getPromptUpgradeTemplate as jest.Mock).mockResolvedValue(
        SystemSettings.DEFAULT_PROMPT_TEMPLATE
      );
      const result = await SystemSettings.getPromptUpgradeTemplate();
      expect(result).toBe(SystemSettings.DEFAULT_PROMPT_TEMPLATE);
    });

    test("getAutoCodingTemplate should return default when not set", async () => {
      (SystemSettings.getAutoCodingTemplate as jest.Mock).mockResolvedValue(
        SystemSettings.DEFAULT_AUTO_CODING_TEMPLATE
      );
      const result = await SystemSettings.getAutoCodingTemplate();
      expect(result).toBe(SystemSettings.DEFAULT_AUTO_CODING_TEMPLATE);
    });
  });

  describe("Complex Settings Functions", () => {
    test("getLoginUi should return value correctly", async () => {
      (SystemSettings.getLoginUi as jest.Mock).mockResolvedValue(
        "ist-legal-rwanda"
      );
      expect(await SystemSettings.getLoginUi()).toBe("ist-legal-rwanda");
    });

    test("getCopyOption should return correct values", async () => {
      (SystemSettings.getCopyOption as jest.Mock).mockResolvedValue("markdown");
      expect(await SystemSettings.getCopyOption()).toBe("markdown");
    });

    test("getSystemWebsiteLinkAndText should return correct object", async () => {
      (
        SystemSettings.getSystemWebsiteLinkAndText as jest.Mock
      ).mockResolvedValue({
        websiteLink: "https://example.com",
        displayText: "Example Site",
      });

      const result = await SystemSettings.getSystemWebsiteLinkAndText();
      expect(result).toEqual({
        websiteLink: "https://example.com",
        displayText: "Example Site",
      });
    });

    test("getSystemTabNames should return correct object", async () => {
      (SystemSettings.getSystemTabNames as jest.Mock).mockResolvedValue({
        tabName1: "Tab 1",
        tabName2: "Tab 2",
        tabName3: "Tab 3",
      });

      const result = await SystemSettings.getSystemTabNames();
      expect(result).toEqual({
        tabName1: "Tab 1",
        tabName2: "Tab 2",
        tabName3: "Tab 3",
      });
    });

    test("getMaxTokensPerUser should parse numeric values", async () => {
      (SystemSettings.getMaxTokensPerUser as jest.Mock).mockResolvedValue(5);
      expect(await SystemSettings.getMaxTokensPerUser()).toBe(5);
    });
  });

  describe("PDR Settings", () => {
    test("getPdrSettings should return parsed settings", async () => {
      (SystemSettings.getPdrSettings as jest.Mock).mockResolvedValue({
        adjacentVector: "10",
        keepPdrVectors: false,
        globalPdrOverride: true,
      });

      const result = await SystemSettings.getPdrSettings();
      expect(result).toEqual({
        adjacentVector: "10",
        keepPdrVectors: false,
        globalPdrOverride: true,
      });
    });

    test("getPdrSettings should handle null values with defaults", async () => {
      (SystemSettings.getPdrSettings as jest.Mock).mockResolvedValue({
        adjacentVector: null,
        keepPdrVectors: true,
        globalPdrOverride: true,
      });

      const result = await SystemSettings.getPdrSettings();
      expect(result).toEqual({
        adjacentVector: null,
        keepPdrVectors: true,
        globalPdrOverride: true,
      });
    });
  });

  describe("Dynamic Context Settings", () => {
    test("getDynamicContextSettings should return parsed settings", async () => {
      (SystemSettings.getDynamicContextSettings as jest.Mock).mockResolvedValue(
        80
      );

      const result = await SystemSettings.getDynamicContextSettings();
      expect(result).toBe(80);
    });

    test("getDynamicContextSettings should handle suffix parameter", async () => {
      (SystemSettings.getDynamicContextSettings as jest.Mock).mockResolvedValue(
        90
      );

      const result = await SystemSettings.getDynamicContextSettings("_CUAI2");
      expect(result).toBe(90);
    });

    test("getDynamicContextSettings should fall back to default", async () => {
      (SystemSettings.getDynamicContextSettings as jest.Mock).mockResolvedValue(
        70
      );

      const result = await SystemSettings.getDynamicContextSettings();
      expect(result).toBe(70);
    });
  });

  describe("DD Settings", () => {
    test("getDDSettings should return parsed settings", async () => {
      (SystemSettings.getDDSettings as jest.Mock).mockResolvedValue({
        ddVectorEnabled: true,
        ddMemoEnabled: false,
        ddBaseEnabled: true,
        ddLinkedWorkspaceImpact: false,
        ddVectorTokenLimit: 6000,
        ddMemoTokenLimit: 3500,
        ddBaseTokenLimit: 2500,
      });

      const result = await SystemSettings.getDDSettings();
      expect(result).toEqual({
        ddVectorEnabled: true,
        ddMemoEnabled: false,
        ddBaseEnabled: true,
        ddLinkedWorkspaceImpact: false,
        ddVectorTokenLimit: 6000,
        ddMemoTokenLimit: 3500,
        ddBaseTokenLimit: 2500,
      });
    });
  });

  describe("hasEmbeddings", () => {
    test("should return true when documents exist", async () => {
      (SystemSettings.hasEmbeddings as jest.Mock).mockResolvedValue(true);
      expect(await SystemSettings.hasEmbeddings()).toBe(true);
    });

    test("should return false when no documents exist", async () => {
      (SystemSettings.hasEmbeddings as jest.Mock).mockResolvedValue(false);
      expect(await SystemSettings.hasEmbeddings()).toBe(false);
    });

    test("should handle errors gracefully", async () => {
      (SystemSettings.hasEmbeddings as jest.Mock).mockResolvedValue(false);
      expect(await SystemSettings.hasEmbeddings()).toBe(false);
    });
  });

  describe("Logo Functions", () => {
    test("currentLogoLight should return correct path", async () => {
      (SystemSettings.currentLogoLight as jest.Mock).mockResolvedValue(
        "/path/to/light-logo.png"
      );
      expect(await SystemSettings.currentLogoLight()).toBe(
        "/path/to/light-logo.png"
      );
    });

    test("currentLogoDark should return correct path", async () => {
      (SystemSettings.currentLogoDark as jest.Mock).mockResolvedValue(
        "/path/to/dark-logo.png"
      );
      expect(await SystemSettings.currentLogoDark()).toBe(
        "/path/to/dark-logo.png"
      );
    });
  });

  describe("Deep Search Settings", () => {
    test("getDeepSearchSettings should return parsed settings", async () => {
      (SystemSettings.getDeepSearchSettings as jest.Mock).mockResolvedValue({
        provider: "google",
        modelId: "gpt-4",
        apiKey: "test-key",
        enabled: true,
        contextPercentage: 15,
      });

      const result = await SystemSettings.getDeepSearchSettings();
      expect(result).toEqual({
        provider: "google",
        modelId: "gpt-4",
        apiKey: "test-key",
        enabled: true,
        contextPercentage: 15,
      });
    });

    test("updateDeepSearchSettings should update individual settings", async () => {
      (SystemSettings.updateDeepSearchSettings as jest.Mock).mockResolvedValue({
        success: true,
      });

      const result = await SystemSettings.updateDeepSearchSettings({
        provider: "bing",
        enabled: true,
      });

      expect(result.success).toBe(true);
    });
  });

  describe("Rexor API Settings", () => {
    test("getRexorApiSettings should return parsed settings", async () => {
      (SystemSettings.getRexorApiSettings as jest.Mock).mockResolvedValue({
        apiBaseUrl: "https://custom-api.rexor.se",
        authUrl: "https://custom-auth.rexor.se",
        clientIdDev: "custom-dev-id",
        clientIdProd: "custom-prod-id",
        apiHost: "custom-api.rexor.se",
      });

      const result = await SystemSettings.getRexorApiSettings();
      expect(result).toEqual({
        apiBaseUrl: "https://custom-api.rexor.se",
        authUrl: "https://custom-auth.rexor.se",
        clientIdDev: "custom-dev-id",
        clientIdProd: "custom-prod-id",
        apiHost: "custom-api.rexor.se",
      });
    });

    test("getRexorApiSettings should return defaults on error", async () => {
      (SystemSettings.getRexorApiSettings as jest.Mock).mockResolvedValue({
        apiBaseUrl: "https://api.rexor.se/v231/Api",
        authUrl: "https://auth.rexor.se/v231/Token",
        clientIdDev: "testfoyen",
        clientIdProd: "foyen",
        apiHost: "api.rexor.se",
      });

      const result = await SystemSettings.getRexorApiSettings();
      expect(result).toEqual({
        apiBaseUrl: "https://api.rexor.se/v231/Api",
        authUrl: "https://auth.rexor.se/v231/Token",
        clientIdDev: "testfoyen",
        clientIdProd: "foyen",
        apiHost: "api.rexor.se",
      });
    });
  });

  describe("Feature Flags", () => {
    test("getFeatureFlags should return parsed flags", async () => {
      (SystemSettings.getFeatureFlags as jest.Mock).mockResolvedValue({
        feature1: true,
        feature2: false,
      });

      const result = await SystemSettings.getFeatureFlags();
      expect(result).toEqual({
        feature1: true,
        feature2: false,
      });
    });

    test("getFeatureFlags should return empty object on error", async () => {
      (SystemSettings.getFeatureFlags as jest.Mock).mockResolvedValue({});

      const result = await SystemSettings.getFeatureFlags();
      expect(result).toEqual({});
    });
  });

  describe("Request Legal Assistance Settings", () => {
    test("getRequestLegalAssistanceSettings should return parsed settings", async () => {
      (
        SystemSettings.getRequestLegalAssistanceSettings as jest.Mock
      ).mockResolvedValue({
        enabled: true,
        lawFirmName: "Test Law Firm",
        email: "<EMAIL>",
      });

      const result = await SystemSettings.getRequestLegalAssistanceSettings();
      expect(result).toEqual({
        enabled: true,
        lawFirmName: "Test Law Firm",
        email: "<EMAIL>",
      });
    });
  });

  describe("Validation Functions", () => {
    test("should validate login_ui correctly", () => {
      const validator = SystemSettings.validations.login_ui;
      expect(validator("ist-legal-rwanda")).toBe("ist-legal-rwanda");
      expect(() => validator("invalid-ui")).toThrow("Invalid login_ui value");
    });

    test("should validate deep_search_context_percentage", () => {
      const validator =
        SystemSettings.validations.deep_search_context_percentage;
      expect(validator("15")).toBe(15);
      expect(validator("25")).toBe(15); // Out of range, returns default
      expect(validator("invalid")).toBe(15); // Invalid, returns default
    });

    test("should validate footer_data", () => {
      const validator = SystemSettings.validations.footer_data;
      const validData = JSON.stringify([
        { url: "https://example.com", text: "Example" },
      ]);
      expect(validator(validData)).toBe(validData);
      expect(validator("invalid")).toBe("[]");
    });

    test("should validate text_splitter_chunk_size", () => {
      const validator = SystemSettings.validations.text_splitter_chunk_size;
      expect(validator("1500")).toBe(1500);
      expect(validator("0")).toBe(1000); // Invalid, returns default
      expect(validator("invalid")).toBe(1000); // Invalid, returns default
    });

    test("should validate agent_search_provider", () => {
      const validator = SystemSettings.validations.agent_search_provider;
      expect(validator("google-search-engine")).toBe("google-search-engine");
      expect(validator("none")).toBe(null);
      expect(validator("invalid")).toBe(null);
    });

    test("should validate default_agent_skills", () => {
      const validator = SystemSettings.validations.default_agent_skills;
      expect(validator("skill1,skill2,skill3")).toBe(
        '["skill1","skill2","skill3"]'
      );
      expect(validator("")).toBe("[]");
    });

    test("should validate experimental_live_file_sync", () => {
      const validator = SystemSettings.validations.experimental_live_file_sync;
      expect(validator("enabled")).toBe("enabled");
      expect(validator("disabled")).toBe("disabled");
      expect(validator(true)).toBe("enabled");
      expect(validator(false)).toBe("disabled");
      expect(validator("invalid")).toBe("disabled");
    });

    test("should validate custom_legal_templates", () => {
      const validator = SystemSettings.validations.custom_legal_templates;
      expect(validator('["template1", "template2"]')).toBe(
        '["template1", "template2"]'
      );
      expect(validator("not an array")).toBe("[]");
    });

    test("should validate slack webhook URLs", () => {
      const bugValidator =
        SystemSettings.validations.slack_bug_report_webhook_url;
      const autocodingValidator =
        SystemSettings.validations.slack_autocoding_webhook_url;

      const validUrl = "https://hooks.slack.com/services/ABC123/DEF456/xyz789";
      expect(bugValidator(validUrl)).toBe(validUrl);
      expect(autocodingValidator(validUrl)).toBe(validUrl);

      expect(bugValidator("")).toBe("");
      expect(() => bugValidator("invalid-url")).toThrow(
        "Invalid bug report Slack webhook URL"
      );
    });

    test("should validate boolean settings", () => {
      const validator = SystemSettings.validations.disableValidationPrompt;
      expect(validator("true")).toBe("true");
      expect(validator(true)).toBe("true");
      expect(validator("false")).toBe("false");
      expect(validator(false)).toBe("false");
    });
  });

  describe("Edge Cases and Error Handling", () => {
    test("should handle database connection failures", async () => {
      (SystemSettings.get as jest.Mock).mockResolvedValue(null);

      const result = await SystemSettings.get();
      expect(result).toBeNull();
    });

    test("should handle concurrent updates efficiently", async () => {
      (SystemSettings._updateSettings as jest.Mock).mockResolvedValue({
        success: true,
        details: {
          successful: ["setting1", "setting2", "setting3"],
          failed: [],
          skipped: [],
        },
      });

      const updates = {
        setting1: "value1",
        setting2: "value2",
        setting3: "value3",
      };

      const result = await SystemSettings._updateSettings(updates);
      expect(result.success).toBe(true);
      expect(result.details?.successful).toHaveLength(3);
    });
  });

  describe("Helper Functions", () => {
    test("vectorDBPreferenceKeys should return correct structure", () => {
      process.env.PINECONE_API_KEY = "test-key";
      process.env.PINECONE_INDEX = "test-index";

      (SystemSettings.vectorDBPreferenceKeys as jest.Mock).mockReturnValue({
        PineConeKey: true,
        PineConeIndex: "test-index",
        AstraEnvironment: undefined,
        AstraToken: false,
        AstraApiEndpoint: undefined,
        ChromaEndpoint: undefined,
        ChromaApiHeader: undefined,
        ChromaApiKey: false,
        WeaviateEndpoint: undefined,
        WeaviateApiKey: false,
        QDrantEndpoint: undefined,
        QDrantApiKey: false,
        MilvusAddress: undefined,
        MilvusUsername: undefined,
        MilvusPassword: false,
        ZillizEndpoint: undefined,
        ZillizApiToken: false,
      });

      const result = SystemSettings.vectorDBPreferenceKeys();
      expect(result).toHaveProperty("PineConeKey", true);
      expect(result).toHaveProperty("PineConeIndex", "test-index");
    });

    test("llmPreferenceKeys should return correct structure", () => {
      process.env.OPEN_AI_KEY = "test-key";
      process.env.OPEN_MODEL_PREF = "gpt-4";

      (SystemSettings.llmPreferenceKeys as jest.Mock).mockReturnValue({
        OpenAiKey: true,
        OpenAiModelPref: "gpt-4",
        AnthropicApiKey: false,
        AnthropicModelPref: undefined,
        GeminiLLMApiKey: false,
        GeminiLLMModelPref: undefined,
        OllamaLLMBasePath: undefined,
        OllamaLLMModelPref: undefined,
        OllamaLLMTokenLimit: undefined,
        // ... and all other properties from our mock
        AzureOpenAiEndpoint: undefined,
        AzureOpenAiKey: false,
        AzureOpenAiModelPref: undefined,
        AzureOpenAiEmbeddingModelPref: undefined,
        AzureOpenAiTokenLimit: undefined,
      });

      const result = SystemSettings.llmPreferenceKeys();
      expect(result).toHaveProperty("OpenAiKey", true);
      expect(result).toHaveProperty("OpenAiModelPref", "gpt-4");
    });

    test("mapLabelToInputKey should map correctly", () => {
      (SystemSettings.mapLabelToInputKey as jest.Mock)
        .mockReturnValueOnce("provider")
        .mockReturnValueOnce("modelId")
        .mockReturnValueOnce(null);

      expect(SystemSettings.mapLabelToInputKey("deep_search_provider")).toBe(
        "provider"
      );
      expect(SystemSettings.mapLabelToInputKey("deep_search_model_id")).toBe(
        "modelId"
      );
      expect(SystemSettings.mapLabelToInputKey("unknown_label")).toBeNull();
    });
  });
});
