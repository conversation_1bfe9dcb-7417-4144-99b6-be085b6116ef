import { Workspace } from "../workspace";
import prisma from "../../utils/prisma";
import { WorkspaceUser } from "../workspaceUsers";
import { LinkedWorkspace } from "../linkedWorkspaces";
import { Document } from "../documents";
import { User } from "../user";
import { EventLogs } from "../eventLogs";
import SystemSettings from "../systemSettings";
import slugifyModule from "slugify";

// Mock dependencies
jest.mock("../../utils/prisma", () => ({
  __esModule: true,
  default: {
    workspaces: {
      create: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      updateMany: jest.fn(),
      deleteMany: jest.fn(),
    },
  },
}));

jest.mock("../workspaceUsers", () => ({
  WorkspaceUser: {
    create: jest.fn(),
    get: jest.fn(),
    where: jest.fn(),
    delete: jest.fn(),
  },
}));

jest.mock("../linkedWorkspaces", () => ({
  LinkedWorkspace: {
    where: jest.fn(),
    create: jest.fn(),
    delete: jest.fn(),
  },
}));

jest.mock("../documents", () => ({
  Document: {
    forWorkspace: jest.fn(),
    count: jest.fn(),
  },
}));

jest.mock("../user", () => ({
  User: {
    get: jest.fn(),
  },
}));

jest.mock("../eventLogs", () => ({
  EventLogs: {
    logEvent: jest.fn(),
  },
}));

jest.mock("../systemSettings", () => ({
  __esModule: true,
  default: {
    get: jest.fn(),
  },
}));

jest.mock("slugify", () => {
  const mockSlugify = jest.fn((text, _options) => {
    if (typeof text === "string") {
      return text
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .replace(/^-+|-+$/g, "");
    }
    return text;
  });
  // Add extend property to match slugify API
  Object.assign(mockSlugify, { extend: jest.fn() });
  return mockSlugify;
});

jest.mock("uuid", () => ({
  v4: jest.fn(() => "mock-uuid-1234"),
}));

// Mock console.error to avoid noise in tests
const originalConsoleError = console.error;
beforeAll(() => {
  console.error = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
});

// Helper function to create a complete mock user
const createMockUser = (overrides: Partial<any> = {}) => ({
  id: 1,
  role: "default",
  createdAt: new Date(),
  lastUpdatedAt: new Date(),
  organizationId: null,
  username: "user",
  password: "hashed_password",
  pfpFilename: null,
  suspended: 0,
  custom_ai_userselected: false,
  custom_ai_option: 1,
  custom_system_prompt: null,
  seen_recovery_codes: false,
  custom_ai_selected_engine: "default",
  economy_system_id: null,
  ...overrides,
});

// Helper function to create a complete mock workspace
const createMockWorkspace = (overrides: Partial<any> = {}) => ({
  id: 1,
  name: "Test Workspace",
  createdAt: new Date(),
  lastUpdatedAt: new Date(),
  pfpFilename: null,
  user_id: 1,
  type: null,
  slug: "test-workspace",
  sharedWithOrg: false,
  vectorTag: null,
  openAiTemp: null,
  openAiHistory: 20,
  agentProvider: "anthropic",
  agentModel: "claude-3-sonnet-20240229",
  agentGuidance: null,
  chatProvider: "anthropic",
  documents: [],
  threads: [],
  customEmbedChunkSize: null,
  customEmbedChunkOverlap: null,
  agentSkills: [],
  order: 0,
  embeddingProvider: null,
  embeddingModel: null,
  topN: 4,
  chatType: "query",
  vectorsCount: 0,
  canDelete: true,
  lastChatAt: null,
  vectorSearchMode: null,
  pdr: false,
  hasMessages: false,
  openAiPrompt: null,
  similarityThreshold: null,
  chatModel: null,
  chatMode: null,
  queryRefusalResponse: null,
  ...overrides,
});

describe("Workspace Model", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Slugify Method", () => {
    test("should extend slugify with custom mappings", () => {
      const mockSlugify = jest.fn().mockReturnValue("test-slug");
      (slugifyModule as unknown as jest.Mock).mockImplementation(mockSlugify);

      const result = Workspace.slugify("test input", { lower: true });

      expect(slugifyModule.extend).toHaveBeenCalledWith({
        "+": " plus ",
        "!": " bang ",
        "@": " at ",
        "*": " splat ",
        ".": " dot ",
        ":": "",
        "~": "",
        "(": "",
        ")": "",
        "'": "",
        '"': "",
        "|": "",
      });
      expect(mockSlugify).toHaveBeenCalledWith("test input", { lower: true });
      expect(result).toBe("test-slug");
    });
  });

  describe("CRUD Operations", () => {
    describe("new", () => {
      const mockSystemSetting = { value: "30" };

      beforeEach(() => {
        (SystemSettings.get as jest.Mock).mockResolvedValue(mockSystemSetting);
        (slugifyModule as unknown as jest.Mock).mockReturnValue(
          "test-workspace"
        );
        jest.spyOn(Workspace, "get").mockResolvedValue(null);
      });

      test("should create new workspace successfully", async () => {
        const mockWorkspace = {
          id: 1,
          name: "Test Workspace",
          slug: "test-workspace",
          user_id: 1,
          type: "general",
          openAiPrompt: "test prompt",
        };

        (prisma.workspaces.create as jest.Mock).mockResolvedValue(
          mockWorkspace
        );
        (WorkspaceUser.create as jest.Mock).mockResolvedValue(true);

        const result = await Workspace.new(
          "Test Workspace",
          1,
          "general",
          "test prompt"
        );

        expect(result.workspace).toMatchObject({
          id: 1,
          name: "Test Workspace",
          slug: "test-workspace",
          user_id: 1,
          type: "general",
          openAiPrompt: "test prompt",
        });
        expect(result.message).toBe(null);
        expect(prisma.workspaces.create).toHaveBeenCalledWith({
          data: {
            name: "Test Workspace",
            slug: "test-workspace",
            user_id: 1,
            type: "general",
            openAiPrompt: "test prompt",
            pdr: true,
            vectorSearchMode: "rerank",
            topN: 30,
          },
        });
        expect(WorkspaceUser.create).toHaveBeenCalledWith(1, 1);
      });

      test("should handle null name", async () => {
        const result = await Workspace.new(null);

        expect(result.workspace).toBeUndefined();
        expect(result.message).toBe("name cannot be null");
      });

      test("should handle duplicate slug", async () => {
        const existingWorkspace = createMockWorkspace({
          id: 2,
          slug: "test-workspace",
        });

        jest.spyOn(Workspace, "get").mockResolvedValueOnce(existingWorkspace);
        jest.spyOn(Workspace, "get").mockResolvedValueOnce(null);

        (slugifyModule as unknown as jest.Mock)
          .mockReturnValueOnce("test-workspace")
          .mockReturnValueOnce("test-workspace-12345678");

        const mockWorkspace = {
          id: 1,
          name: "Test Workspace",
          slug: "test-workspace-12345678",
        };

        (prisma.workspaces.create as jest.Mock).mockResolvedValue(
          mockWorkspace
        );

        const result = await Workspace.new("Test Workspace");

        expect(result.workspace?.slug).toContain("test-workspace-");
        expect(result.workspace?.slug).not.toBe("test-workspace");
      });

      test("should handle workspace creation without user", async () => {
        const mockWorkspace = {
          id: 1,
          name: "Test Workspace",
          slug: "test-workspace",
          user_id: 0,
        };

        (prisma.workspaces.create as jest.Mock).mockResolvedValue(
          mockWorkspace
        );

        const result = await Workspace.new("Test Workspace", null);

        expect(result.workspace).toMatchObject({
          id: 1,
          name: "Test Workspace",
          slug: "test-workspace",
          user_id: 0,
        });
        expect(WorkspaceUser.create).not.toHaveBeenCalled();
      });

      test("should handle database errors", async () => {
        (prisma.workspaces.create as jest.Mock).mockRejectedValue(
          new Error("Database error")
        );

        const result = await Workspace.new("Test Workspace");

        expect(result.workspace).toBeUndefined();
        expect(result.message).toBe("Database error");
      });

      test("should use fallback UUID when slug is empty", async () => {
        (slugifyModule as unknown as jest.Mock).mockReturnValue("");

        const mockWorkspace = {
          id: 1,
          name: "Test Workspace",
          slug: "mock-uuid-1234",
        };

        (prisma.workspaces.create as jest.Mock).mockResolvedValue(
          mockWorkspace
        );

        const result = await Workspace.new("Test Workspace");

        expect(result.workspace?.slug).toBe("mock-uuid-1234");
      });

      test("should use default topN when system setting is not available", async () => {
        (SystemSettings.get as jest.Mock).mockResolvedValue(null);

        const mockWorkspace = { id: 1, name: "Test", slug: "test" };
        (prisma.workspaces.create as jest.Mock).mockResolvedValue(
          mockWorkspace
        );

        await Workspace.new("Test");

        expect(prisma.workspaces.create).toHaveBeenCalledWith({
          data: expect.objectContaining({
            topN: 30, // Default value
          }),
        });
      });
    });

    describe("get", () => {
      beforeEach(() => {
        // Restore the get method for these tests
        if (jest.isMockFunction(Workspace.get)) {
          (Workspace.get as jest.Mock).mockRestore();
        }
      });

      test("should get workspace by ID", async () => {
        const mockWorkspace = createMockWorkspace({
          id: 1,
          name: "Test Workspace",
          slug: "test-workspace",
        });

        (prisma.workspaces.findFirst as jest.Mock).mockResolvedValue(
          mockWorkspace
        );

        const result = await Workspace.get({ id: 1 });

        expect(result).toEqual({ ...mockWorkspace, documents: [] });
        expect(prisma.workspaces.findFirst).toHaveBeenCalledWith({
          where: { id: 1 },
        });
      });

      test("should get workspace with documents", async () => {
        const mockWorkspace = createMockWorkspace({ name: "Test" });
        const mockDocuments = [{ id: 1, filename: "test.pdf" }];

        (prisma.workspaces.findFirst as jest.Mock).mockResolvedValue(
          mockWorkspace
        );
        (Document.forWorkspace as jest.Mock).mockResolvedValue(mockDocuments);

        const result = await Workspace.get({ id: 1 }, true);

        expect(result?.documents).toEqual(mockDocuments);
        expect(Document.forWorkspace).toHaveBeenCalledWith(1);
      });

      test("should convert string ID to number", async () => {
        const mockWorkspace = createMockWorkspace({ name: "Test" });
        (prisma.workspaces.findFirst as jest.Mock).mockResolvedValue(
          mockWorkspace
        );

        const result = await Workspace.get({ id: 1 });

        expect(result).toBeDefined();
        expect(prisma.workspaces.findFirst).toHaveBeenCalledWith({
          where: { id: 1 },
        });
      });

      test("should handle invalid ID", async () => {
        // Mock to return null for invalid ID
        (prisma.workspaces.findFirst as jest.Mock).mockResolvedValue(null);

        const result = await Workspace.get({ id: NaN });

        expect(result).toBe(null);
        expect(prisma.workspaces.findFirst).toHaveBeenCalledWith({
          where: { id: NaN },
        });
      });

      test("should handle database errors", async () => {
        (prisma.workspaces.findFirst as jest.Mock).mockRejectedValue(
          new Error("Database error")
        );

        const result = await Workspace.get({ id: 1 });

        expect(result).toBe(null);
      });

      test("should get workspace by slug", async () => {
        const mockWorkspace = createMockWorkspace({
          id: 1,
          slug: "test-workspace",
        });
        (prisma.workspaces.findFirst as jest.Mock).mockResolvedValue(
          mockWorkspace
        );

        const result = await Workspace.get({ slug: "test-workspace" });

        expect(result).toBeDefined();
        expect(prisma.workspaces.findFirst).toHaveBeenCalledWith({
          where: { slug: "test-workspace" },
        });
      });
    });

    describe("update", () => {
      test("should update workspace successfully", async () => {
        const mockWorkspace = createMockWorkspace({
          name: "Updated Workspace",
        });
        (prisma.workspaces.update as jest.Mock).mockResolvedValue(
          mockWorkspace
        );

        const result = await Workspace.update(1, { name: "Updated Workspace" });

        expect(result.workspace).toEqual(mockWorkspace);
        expect(result.message).toBe(null);
      });

      test("should handle no workspace ID", async () => {
        await expect(Workspace.update(null, { name: "Test" })).rejects.toThrow(
          "No workspace id provided for update"
        );
      });

      test("should filter out non-writable fields", async () => {
        const mockWorkspace = createMockWorkspace({ name: "Test" });
        (prisma.workspaces.update as jest.Mock).mockResolvedValue(
          mockWorkspace
        );

        await Workspace.update(1, {
          name: "Test",
          id: 999, // Should be filtered out
          invalidField: "invalid", // Should be filtered out
        } as any);

        expect(prisma.workspaces.update).toHaveBeenCalledWith({
          where: { id: 1 },
          data: { name: "Test" },
        });
      });

      test("should handle chatProvider default value", async () => {
        const mockWorkspace = { id: 1, chatProvider: null };
        (prisma.workspaces.update as jest.Mock).mockResolvedValue(
          mockWorkspace
        );

        await Workspace.update(1, { chatProvider: "default" });

        expect(prisma.workspaces.update).toHaveBeenCalledWith({
          where: { id: 1 },
          data: { chatProvider: null, chatModel: null },
        });
      });

      test("should handle embeddingProvider default and native values", async () => {
        const mockWorkspace = { id: 1 };
        (prisma.workspaces.update as jest.Mock).mockResolvedValue(
          mockWorkspace
        );

        await Workspace.update(1, { embeddingProvider: "default" });

        expect(prisma.workspaces.update).toHaveBeenCalledWith({
          where: { id: 1 },
          data: { embeddingProvider: null, embeddingModel: null },
        });

        jest.clearAllMocks();

        await Workspace.update(1, { embeddingProvider: "native" });

        expect(prisma.workspaces.update).toHaveBeenCalledWith({
          where: { id: 1 },
          data: { embeddingProvider: "native", embeddingModel: null },
        });
      });

      test("should return existing workspace when no valid updates", async () => {
        const existingWorkspace = createMockWorkspace({
          name: "Existing",
          slug: "existing",
        });
        jest.spyOn(Workspace, "get").mockResolvedValue(existingWorkspace);

        const result = await Workspace.update(1, {
          invalidField: "invalid",
        } as any);

        expect(result.workspace).toEqual(existingWorkspace);
        expect(result.message).toBe("No valid fields to update!");
      });
    });

    describe("updateMany", () => {
      test("should update multiple workspaces", async () => {
        (prisma.workspaces.updateMany as jest.Mock).mockResolvedValue({
          count: 3,
        });

        const result = await Workspace.updateMany(
          { user_id: 1 },
          { chatProvider: "openai" }
        );

        expect(result.count).toBe(3);
        expect(result.message).toBe(null);
      });

      test("should handle no valid fields", async () => {
        const result = await Workspace.updateMany({}, {
          invalidField: "invalid",
        } as any);

        expect(result.count).toBe(0);
        expect(result.message).toBe("No valid fields to update!");
      });

      test("should handle null openAiHistory", async () => {
        (prisma.workspaces.updateMany as jest.Mock).mockResolvedValue({
          count: 1,
        });

        await Workspace.updateMany({}, { openAiHistory: undefined });

        expect(prisma.workspaces.updateMany).toHaveBeenCalledWith({
          where: {},
          data: {}, // openAiHistory should be removed when undefined
        });
      });

      test("should handle database errors", async () => {
        (prisma.workspaces.updateMany as jest.Mock).mockRejectedValue(
          new Error("Database error")
        );

        const result = await Workspace.updateMany({}, { name: "Test" });

        expect(result.count).toBe(0);
        expect(result.message).toBe("Database error");
      });
    });

    describe("delete", () => {
      test("should delete workspaces", async () => {
        (prisma.workspaces.deleteMany as jest.Mock).mockResolvedValue({
          count: 2,
        });

        const result = await Workspace.delete({ user_id: 1 });

        expect(result).toBe(true);
        expect(prisma.workspaces.deleteMany).toHaveBeenCalledWith({
          where: { user_id: 1 },
        });
      });

      test("should handle database errors", async () => {
        (prisma.workspaces.deleteMany as jest.Mock).mockRejectedValue(
          new Error("Database error")
        );

        const result = await Workspace.delete({ id: 1 });

        expect(result).toBe(false);
      });
    });

    describe("where", () => {
      test("should find workspaces with limit and order", async () => {
        const mockWorkspaces = [
          createMockWorkspace({ name: "Workspace 1" }),
          createMockWorkspace({ id: 2, name: "Workspace 2" }),
        ];

        (prisma.workspaces.findMany as jest.Mock).mockResolvedValue(
          mockWorkspaces
        );

        const result = await Workspace.where({ user_id: 1 }, 10, {
          name: "asc",
        });

        expect(result).toEqual(mockWorkspaces);
        expect(prisma.workspaces.findMany).toHaveBeenCalledWith({
          where: { user_id: 1 },
          take: 10,
          orderBy: { name: "asc" },
        });
      });

      test("should handle no limit or orderBy", async () => {
        const mockWorkspaces = [createMockWorkspace({ name: "Test" })];
        (prisma.workspaces.findMany as jest.Mock).mockResolvedValue(
          mockWorkspaces
        );

        const result = await Workspace.where({});

        expect(result).toEqual(mockWorkspaces);
        expect(prisma.workspaces.findMany).toHaveBeenCalledWith({
          where: {},
        });
      });

      test("should handle database errors", async () => {
        (prisma.workspaces.findMany as jest.Mock).mockRejectedValue(
          new Error("Database error")
        );

        const result = await Workspace.where({});

        expect(result).toEqual([]);
      });
    });
  });

  describe("User Access Methods", () => {
    describe("userHasAccess", () => {
      test("should return true for admin users", async () => {
        (User.get as jest.Mock).mockResolvedValue(
          createMockUser({ role: "admin" })
        );

        const result = await Workspace.userHasAccess(1, 1);

        expect(result).toBe(true);
        expect(WorkspaceUser.get).not.toHaveBeenCalled();
      });

      test("should check workspace user relationship for non-admin", async () => {
        (User.get as jest.Mock).mockResolvedValue(createMockUser());
        (WorkspaceUser.get as jest.Mock).mockResolvedValue({
          id: 1,
          user_id: 1,
          workspace_id: 1,
        });

        const result = await Workspace.userHasAccess(1, 1);

        expect(result).toBe(true);
        expect(WorkspaceUser.get).toHaveBeenCalledWith({
          user_id: 1,
          workspace_id: 1,
        });
      });

      test("should return false when user not found", async () => {
        (User.get as jest.Mock).mockResolvedValue(null);

        const result = await Workspace.userHasAccess(999, 1);

        expect(result).toBe(false);
      });

      test("should return false when user has no access", async () => {
        (User.get as jest.Mock).mockResolvedValue(createMockUser());
        (WorkspaceUser.get as jest.Mock).mockResolvedValue(null);

        const result = await Workspace.userHasAccess(1, 1);

        expect(result).toBe(false);
      });
    });

    describe("whereWithUser", () => {
      const mockUser = createMockUser();

      test("should get all workspaces for admin", async () => {
        const adminUser = createMockUser({ role: "admin" });
        const mockWorkspaces = [createMockWorkspace({ name: "Test" })];

        (prisma.workspaces.findMany as jest.Mock).mockResolvedValue(
          mockWorkspaces
        );

        const result = await Workspace.whereWithUser(adminUser);

        expect(result).toEqual(mockWorkspaces);
        expect(prisma.workspaces.findMany).toHaveBeenCalledWith({
          where: {},
          orderBy: [{ order: "asc" }, { name: "asc" }],
        });
      });

      test("should get user workspaces for non-admin", async () => {
        const mockWorkspaces = [createMockWorkspace({ name: "Test" })];

        (prisma.workspaces.findMany as jest.Mock).mockResolvedValue(
          mockWorkspaces
        );

        const result = await Workspace.whereWithUser(mockUser, 1);

        expect(result).toEqual(mockWorkspaces);
        expect(prisma.workspaces.findMany).toHaveBeenCalledWith({
          where: {
            OR: [{ workspace_users: { some: { user_id: 1 } } }, { user_id: 1 }],
          },
          orderBy: [{ order: "asc" }, { name: "asc" }],
        });
      });

      test("should return empty array when no user ID and not admin", async () => {
        // Create a user with no ID to test the edge case
        const userWithNoId = createMockUser({ id: null });

        // Should not call database due to early return
        const result = await Workspace.whereWithUser(userWithNoId, null);

        expect(result).toEqual([]);
        expect(prisma.workspaces.findMany).not.toHaveBeenCalled();
      });

      test("should handle database errors", async () => {
        (prisma.workspaces.findMany as jest.Mock).mockRejectedValue(
          new Error("Database error")
        );

        const result = await Workspace.whereWithUser(mockUser);

        expect(result).toEqual([]);
      });
    });

    describe("getWithUser", () => {
      const mockUser = createMockUser();
      const mockWorkspace = createMockWorkspace({ name: "Test Workspace" });

      beforeEach(() => {
        jest.spyOn(Workspace, "get").mockResolvedValue(mockWorkspace);
        jest.spyOn(Workspace, "userHasAccess").mockResolvedValue(true);
      });

      afterEach(() => {
        jest.restoreAllMocks();
      });

      test("should get workspace for admin user", async () => {
        const adminUser = createMockUser({ role: "admin" });

        const result = await Workspace.getWithUser(adminUser, { id: 1 });

        expect(result).toEqual(mockWorkspace);
        expect(Workspace.userHasAccess).not.toHaveBeenCalled();
      });

      test("should get workspace for user with access", async () => {
        const result = await Workspace.getWithUser(mockUser, { id: 1 });

        expect(result).toEqual(mockWorkspace);
        expect(Workspace.userHasAccess).toHaveBeenCalledWith(1, 1);
      });

      test("should return null when user has no access", async () => {
        jest.spyOn(Workspace, "userHasAccess").mockResolvedValue(false);

        const result = await Workspace.getWithUser(mockUser, { id: 1 });

        expect(result).toBe(null);
      });

      test("should return null when workspace not found", async () => {
        jest.spyOn(Workspace, "get").mockResolvedValue(null);

        const result = await Workspace.getWithUser(mockUser, { id: 999 });

        expect(result).toBe(null);
      });
    });
  });

  describe("Relationship Management", () => {
    describe("linkedWorkspaces", () => {
      test("should get linked workspaces", async () => {
        const mockLinkedWorkspaces = [
          { id: 1, workspace_id: 1, linkedWorkspace_id: 2 },
          { id: 2, workspace_id: 1, linkedWorkspace_id: 3 },
        ];

        (LinkedWorkspace.where as jest.Mock).mockResolvedValue(
          mockLinkedWorkspaces
        );

        const result = await Workspace.linkedWorkspaces(1);

        expect(result).toEqual([
          { id: 1, primaryWorkspaceId: 1, linkedWorkspaceId: 2 },
          { id: 2, primaryWorkspaceId: 1, linkedWorkspaceId: 3 },
        ]);
        expect(LinkedWorkspace.where).toHaveBeenCalledWith({ workspace_id: 1 });
      });

      test("should handle invalid workspace ID", async () => {
        const result = await Workspace.linkedWorkspaces("invalid");

        expect(result).toEqual([]);
        expect(LinkedWorkspace.where).not.toHaveBeenCalled();
      });

      test("should handle database errors", async () => {
        (LinkedWorkspace.where as jest.Mock).mockRejectedValue(
          new Error("Database error")
        );

        const result = await Workspace.linkedWorkspaces(1);

        expect(result).toEqual([]);
      });
    });

    describe("workspaceUsers", () => {
      test("should get workspace users", async () => {
        const mockUsers = [
          { id: 1, user_id: 1, workspace_id: 1 },
          { id: 2, user_id: 2, workspace_id: 1 },
        ];

        (WorkspaceUser.where as jest.Mock).mockResolvedValue(mockUsers);

        const result = await Workspace.workspaceUsers(1);

        expect(result).toEqual(mockUsers);
        expect(WorkspaceUser.where).toHaveBeenCalledWith({ workspace_id: 1 });
      });

      test("should handle invalid workspace ID", async () => {
        const result = await Workspace.workspaceUsers("invalid");

        expect(result).toEqual([]);
      });
    });

    describe("updateUsers", () => {
      test("should update workspace users", async () => {
        (WorkspaceUser.delete as jest.Mock).mockResolvedValue(true);
        (WorkspaceUser.create as jest.Mock).mockResolvedValue(true);

        const result = await Workspace.updateUsers(1, [2, 3, 4]);

        expect(result.success).toBe(true);
        expect(WorkspaceUser.delete).toHaveBeenCalledWith({ workspace_id: 1 });
        expect(WorkspaceUser.create).toHaveBeenCalledTimes(3);
        expect(WorkspaceUser.create).toHaveBeenCalledWith(2, 1);
        expect(WorkspaceUser.create).toHaveBeenCalledWith(3, 1);
        expect(WorkspaceUser.create).toHaveBeenCalledWith(4, 1);
      });

      test("should handle invalid workspace ID", async () => {
        const result = await Workspace.updateUsers("invalid", [1, 2]);

        expect(result.success).toBe(false);
        expect(result.error).toBe("Invalid workspace ID");
      });

      test("should handle database errors", async () => {
        (WorkspaceUser.delete as jest.Mock).mockRejectedValue(
          new Error("Database error")
        );

        const result = await Workspace.updateUsers(1, [2, 3]);

        expect(result.success).toBe(false);
        expect(result.error).toBe("Database error");
      });
    });

    describe("updateLinkedWorkspaces", () => {
      test("should update linked workspaces", async () => {
        (LinkedWorkspace.delete as jest.Mock).mockResolvedValue(true);
        (LinkedWorkspace.create as jest.Mock).mockResolvedValue(true);

        const result = await Workspace.updateLinkedWorkspaces(1, [2, 3]);

        expect(result.success).toBe(true);
        expect(LinkedWorkspace.delete).toHaveBeenCalledWith({
          workspace_id: 1,
        });
        expect(LinkedWorkspace.create).toHaveBeenCalledTimes(2);
        expect(LinkedWorkspace.create).toHaveBeenCalledWith(1, 2);
        expect(LinkedWorkspace.create).toHaveBeenCalledWith(1, 3);
      });

      test("should handle invalid workspace ID", async () => {
        const result = await Workspace.updateLinkedWorkspaces(
          "invalid",
          [1, 2]
        );

        expect(result.success).toBe(false);
        expect(result.error).toBe("Invalid workspace ID");
      });
    });
  });

  describe("Utility Methods", () => {
    describe("vectorSearchMode", () => {
      test("should validate vector search modes", () => {
        expect(Workspace.vectorSearchMode("default")).toBe("default");
        expect(Workspace.vectorSearchMode("rerank")).toBe("rerank");
        expect(Workspace.vectorSearchMode("invalid")).toBe("default");
        expect(Workspace.vectorSearchMode(null)).toBe("default");
        expect(Workspace.vectorSearchMode(123)).toBe("default");
      });
    });

    describe("isUserOwner", () => {
      test("should return true for workspace owner", async () => {
        const mockWorkspace = createMockWorkspace({ user_id: 1 });
        const mockUser = createMockUser();

        jest.spyOn(Workspace, "get").mockResolvedValue(mockWorkspace);

        const result = await Workspace.isUserOwner(mockUser, 1);

        expect(result).toBe(true);
      });

      test("should return true for admin users", async () => {
        const mockWorkspace = createMockWorkspace({ user_id: 2 });
        const mockUser = createMockUser({ role: "admin" });

        jest.spyOn(Workspace, "get").mockResolvedValue(mockWorkspace);

        const result = await Workspace.isUserOwner(mockUser, 1);

        expect(result).toBe(true);
      });

      test("should return false for non-owner non-admin", async () => {
        const mockWorkspace = createMockWorkspace({ user_id: 2 });
        const mockUser = createMockUser();

        jest.spyOn(Workspace, "get").mockResolvedValue(mockWorkspace);

        const result = await Workspace.isUserOwner(mockUser, 1);

        expect(result).toBe(false);
      });

      test("should return false when workspace not found", async () => {
        const mockUser = createMockUser();

        jest.spyOn(Workspace, "get").mockResolvedValue(null);

        const result = await Workspace.isUserOwner(mockUser, 999);

        expect(result).toBe(false);
      });
    });

    describe("trackChange", () => {
      test("should track workspace changes", async () => {
        const prevData = { id: 1, name: "Old Name", slug: "old-slug" };
        const nextData = { name: "New Name", openAiTemp: 0.7 };
        const user = { id: 1, username: "testuser" };

        (EventLogs.logEvent as jest.Mock).mockResolvedValue({
          eventLog: {},
          message: null,
        });

        await Workspace.trackChange(prevData as any, nextData, user as any);

        expect(EventLogs.logEvent).toHaveBeenCalledWith("workspace_updated", {
          workspaceId: 1,
          workspaceName: "Old Name",
          changes: {
            name: "Old Name → New Name",
            openAiTemp: "undefined → 0.7",
          },
          userId: 1,
        });
      });

      test("should not log when no changes", async () => {
        const prevData = createMockWorkspace({ name: "Same Name" });
        const nextData = { name: "Same Name" };

        await Workspace.trackChange(prevData as any, nextData, null);

        expect(EventLogs.logEvent).not.toHaveBeenCalled();
      });

      test("should handle tracking errors gracefully", async () => {
        const prevData = createMockWorkspace({ name: "Test" });
        const nextData = { name: "New Test" };

        (EventLogs.logEvent as jest.Mock).mockRejectedValue(
          new Error("Logging error")
        );

        // Should not throw
        await expect(
          Workspace.trackChange(prevData as any, nextData, null)
        ).resolves.toBeUndefined();
      });
    });

    describe("updateOrders", () => {
      test("should update workspace orders", async () => {
        const updates = [
          { id: 1, order: 1 },
          { id: 2, order: 2 },
          { id: 3, order: 3 },
        ];

        (prisma.workspaces.update as jest.Mock).mockResolvedValue({});

        const result = await Workspace.updateOrders(updates);

        expect(result).toBe(true);
        expect(prisma.workspaces.update).toHaveBeenCalledTimes(3);
        expect(prisma.workspaces.update).toHaveBeenCalledWith({
          where: { id: 1 },
          data: { order: 1 },
        });
      });

      test("should handle database errors", async () => {
        const updates = [{ id: 1, order: 1 }];

        (prisma.workspaces.update as jest.Mock).mockRejectedValue(
          new Error("Database error")
        );

        const result = await Workspace.updateOrders(updates);

        expect(result).toBe(false);
      });
    });

    describe("getPopulatedWorkspaces", () => {
      test("should get workspaces with document counts", async () => {
        const mockUser = createMockUser();
        const mockWorkspaces = [
          createMockWorkspace({ name: "Workspace 1" }),
          createMockWorkspace({ id: 2, name: "Workspace 2" }),
        ];

        jest
          .spyOn(Workspace, "whereWithUser")
          .mockResolvedValue(mockWorkspaces);
        (Document.count as jest.Mock)
          .mockResolvedValueOnce(5) // First workspace has 5 documents
          .mockResolvedValueOnce(3); // Second workspace has 3 documents

        const result = await Workspace.getPopulatedWorkspaces(mockUser);

        expect(result).toEqual([
          { ...mockWorkspaces[0], documentCount: 5 },
          { ...mockWorkspaces[1], documentCount: 3 },
        ]);
        expect(Document.count).toHaveBeenCalledTimes(2);
        expect(Document.count).toHaveBeenCalledWith({ workspaceId: 1 });
        expect(Document.count).toHaveBeenCalledWith({ workspaceId: 2 });
      });

      test("should return empty array for null user", async () => {
        const result = await Workspace.getPopulatedWorkspaces(null);

        expect(result).toEqual([]);
        expect(Document.count).not.toHaveBeenCalled();
      });

      test("should handle database errors", async () => {
        const mockUser = createMockUser();

        jest
          .spyOn(Workspace, "whereWithUser")
          .mockRejectedValue(new Error("Database error"));

        const result = await Workspace.getPopulatedWorkspaces(mockUser);

        expect(result).toEqual([]);
      });
    });
  });

  describe("Edge Cases and Error Handling", () => {
    test("should handle concurrent slug generation", async () => {
      // First call returns existing workspace, second call returns null
      jest
        .spyOn(Workspace, "get")
        .mockResolvedValueOnce(
          createMockWorkspace({ id: 1, slug: "test-workspace" })
        )
        .mockResolvedValueOnce(null);

      (slugifyModule as unknown as jest.Mock)
        .mockReturnValueOnce("test-workspace")
        .mockReturnValueOnce("test-workspace-12345678");

      const mockWorkspace = createMockWorkspace({
        id: 2,
        slug: "test-workspace-12345678",
      });
      (prisma.workspaces.create as jest.Mock).mockResolvedValue(mockWorkspace);

      const result = await Workspace.new("Test Workspace");

      expect(result.workspace?.slug).toContain("test-workspace-");
    });

    test("should handle malformed update data", async () => {
      const result = await Workspace.update(1, {
        invalidField: "invalid",
        anotherInvalid: 123,
      } as any);

      expect(result.message).toBe("No valid fields to update!");
    });

    test("should handle empty workspace queries", async () => {
      (prisma.workspaces.findMany as jest.Mock).mockResolvedValue([]);

      const result = await Workspace.where({ name: "nonexistent" } as any);

      expect(result).toEqual([]);
    });

    test("should handle workspace access with invalid user data", async () => {
      (User.get as jest.Mock).mockResolvedValue(null);

      const result = await Workspace.userHasAccess(1, 1);

      expect(result).toBe(false);
    });
  });

  describe("Security Tests", () => {
    test("should validate workspace access properly", async () => {
      const nonAdminUser = createMockUser({ id: 2, role: "default" });

      (User.get as jest.Mock).mockResolvedValue(nonAdminUser);
      (WorkspaceUser.get as jest.Mock).mockResolvedValue(null);

      const result = await Workspace.userHasAccess(2, 1);

      expect(result).toBe(false);
      expect(WorkspaceUser.get).toHaveBeenCalledWith({
        user_id: 2,
        workspace_id: 1,
      });
    });

    test("should prevent unauthorized workspace access", async () => {
      const user = createMockUser();
      const workspace = createMockWorkspace({ name: "Restricted" });

      jest.spyOn(Workspace, "get").mockResolvedValue(workspace);
      // Mock the dependencies of userHasAccess instead
      (User.get as jest.Mock).mockResolvedValue(user);
      (WorkspaceUser.get as jest.Mock).mockResolvedValue(null); // No access

      const result = await Workspace.getWithUser(user, { id: 1 });

      expect(result).toBe(null);
    });

    test("should filter writable fields in updates", async () => {
      const maliciousUpdate = {
        name: "Safe Name",
        id: 999, // Should be filtered
        createdAt: new Date(), // Should be filtered
        user_id: 888, // This should be allowed as it's in writable fields
        maliciousField: "malicious", // Should be filtered
      };

      const mockWorkspace = { id: 1, name: "Safe Name", user_id: 888 };
      (prisma.workspaces.update as jest.Mock).mockResolvedValue(mockWorkspace);

      await Workspace.update(1, maliciousUpdate as any);

      // Check that only valid fields were passed to the update
      const updateCall = (prisma.workspaces.update as jest.Mock).mock
        .calls[0][0];
      expect(updateCall.data).toEqual({
        name: "Safe Name",
        user_id: 888,
      });
      expect(updateCall.data.id).toBeUndefined();
      expect(updateCall.data.createdAt).toBeUndefined();
      expect(updateCall.data.maliciousField).toBeUndefined();
    });
  });
});
