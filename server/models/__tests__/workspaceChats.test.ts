/**
 * Comprehensive test suite for WorkspaceChats model
 *
 * Tests all WorkspaceChats functions for 100% code coverage including:
 * - Chat creation and management
 * - Chat history and threading
 * - User and workspace associations
 * - Metrics and feedback handling
 * - Bulk operations and performance
 * - Security and edge case handling
 */

// Unmock WorkspaceChats to test the real implementation
jest.unmock("../workspaceChats");

import { WorkspaceChats } from "../workspaceChats";
import prisma from "../../utils/prisma";
import { useTimerCleanup } from "../../tests/helpers/timerCleanup";

// Mock dependencies
jest.mock("../../utils/prisma", () => ({
  workspace_chats: {
    create: jest.fn(),
    findMany: jest.fn(),
    findFirst: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    updateMany: jest.fn(),
    delete: jest.fn(),
    deleteMany: jest.fn(),
    count: jest.fn(),
    createMany: jest.fn(),
  },
}));

// Mock console methods to avoid test output noise
const originalConsole = { ...console };
beforeAll(() => {
  console.error = jest.fn();
  console.log = jest.fn();
  console.warn = jest.fn();
});

afterAll(() => {
  Object.assign(console, originalConsole);
});

describe("WorkspaceChats Model", () => {
  // Setup automatic timer cleanup
  useTimerCleanup();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("new (create chat)", () => {
    const mockChatData = {
      workspaceId: 1,
      prompt: "Test prompt",
      response: { content: "Test response" },
      user: { id: 1, username: "testuser" },
      threadId: 1,
      include: true,
      apiSessionId: "session-123",
      invoice_ref: "invoice-456",
      metrics: { tokens: 100, time: 500 },
    };

    test("should create new chat successfully", async () => {
      const mockCreatedChat = {
        id: 1,
        workspaceId: mockChatData.workspaceId,
        prompt: mockChatData.prompt,
        response: JSON.stringify(mockChatData.response),
        user_id: mockChatData.user?.id ?? null,
        thread_id: mockChatData.threadId,
        include: mockChatData.include,
        api_session_id: mockChatData.apiSessionId,
        invoice_ref: mockChatData.invoice_ref,
        metrics: JSON.stringify(mockChatData.metrics),
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      };

      (prisma.workspace_chats.create as jest.Mock).mockResolvedValue(
        mockCreatedChat
      );

      const result = await WorkspaceChats.new(mockChatData);

      expect(result.chat).toEqual(mockCreatedChat);
      expect(result.message).toBeNull();
      expect(prisma.workspace_chats.create).toHaveBeenCalledWith({
        data: {
          workspaceId: 1,
          prompt: "Test prompt",
          response: JSON.stringify(mockChatData.response),
          user_id: 1,
          thread_id: 1,
          include: true,
          api_session_id: "session-123",
          invoice_ref: "invoice-456",
          metrics: JSON.stringify(mockChatData.metrics),
        },
      });
    });

    test("should create chat with minimal data", async () => {
      const minimalData = {
        workspaceId: 1,
        prompt: "Test prompt",
      };

      const mockCreatedChat = {
        id: 1,
        workspaceId: 1,
        prompt: "Test prompt",
        response: "{}",
        user_id: null,
        thread_id: null,
        include: true,
        api_session_id: null,
        invoice_ref: null,
        metrics: "{}",
      };

      (prisma.workspace_chats.create as jest.Mock).mockResolvedValue(
        mockCreatedChat
      );

      const result = await WorkspaceChats.new(minimalData);

      expect(result.chat).toBeDefined();
      expect(result.message).toBeNull();
      expect(prisma.workspace_chats.create).toHaveBeenCalledWith({
        data: {
          workspaceId: 1,
          prompt: "Test prompt",
          response: "{}",
          user_id: null,
          thread_id: null,
          include: true,
          api_session_id: null,
          invoice_ref: null,
          metrics: "{}",
        },
      });
    });

    test("should handle metrics from response object", async () => {
      const dataWithResponseMetrics = {
        workspaceId: 1,
        prompt: "Test prompt",
        response: {
          content: "Response",
          metrics: { tokens: 50, time: 200 },
        },
      };

      const mockCreatedChat = { id: 1, ...dataWithResponseMetrics };
      (prisma.workspace_chats.create as jest.Mock).mockResolvedValue(
        mockCreatedChat
      );

      await WorkspaceChats.new(dataWithResponseMetrics);

      expect(prisma.workspace_chats.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          metrics: JSON.stringify({ tokens: 50, time: 200 }),
        }),
      });
    });

    test("should handle string metrics", async () => {
      const dataWithStringMetrics = {
        workspaceId: 1,
        prompt: "Test prompt",
        metrics: '{"tokens": 75}',
      };

      const mockCreatedChat = { id: 1, ...dataWithStringMetrics };
      (prisma.workspace_chats.create as jest.Mock).mockResolvedValue(
        mockCreatedChat
      );

      await WorkspaceChats.new(dataWithStringMetrics);

      expect(prisma.workspace_chats.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          metrics: '{"tokens": 75}',
        }),
      });
    });

    test("should handle database errors gracefully", async () => {
      (prisma.workspace_chats.create as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      const result = await WorkspaceChats.new(mockChatData);

      expect(result.chat).toBeNull();
      expect(result.message).toBe("Database error");
      expect(console.error).toHaveBeenCalled();
    });

    test("should handle null user correctly", async () => {
      const dataWithNullUser = {
        workspaceId: 1,
        prompt: "Test prompt",
        user: null,
      };

      const mockCreatedChat = { id: 1, ...dataWithNullUser };
      (prisma.workspace_chats.create as jest.Mock).mockResolvedValue(
        mockCreatedChat
      );

      await WorkspaceChats.new(dataWithNullUser);

      expect(prisma.workspace_chats.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          user_id: null,
        }),
      });
    });

    test("should handle user without ID", async () => {
      const dataWithUserNoId = {
        workspaceId: 1,
        prompt: "Test prompt",
        user: { username: "testuser" } as any,
      };

      const mockCreatedChat = { id: 1, ...dataWithUserNoId };
      (prisma.workspace_chats.create as jest.Mock).mockResolvedValue(
        mockCreatedChat
      );

      await WorkspaceChats.new(dataWithUserNoId);

      expect(prisma.workspace_chats.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          user_id: null,
        }),
      });
    });
  });

  describe("forWorkspaceByUser", () => {
    test("should retrieve chats for user in workspace", async () => {
      const mockChats = [
        { id: 1, prompt: "Chat 1", user_id: 1, workspaceId: 1 },
        { id: 2, prompt: "Chat 2", user_id: 1, workspaceId: 1 },
      ];

      (prisma.workspace_chats.findMany as jest.Mock).mockResolvedValue(
        mockChats
      );

      const result = await WorkspaceChats.forWorkspaceByUser(1, 1, 10, {
        id: "asc",
      });

      expect(result).toEqual(mockChats);
      expect(prisma.workspace_chats.findMany).toHaveBeenCalledWith({
        where: {
          workspaceId: 1,
          user_id: 1,
          thread_id: null,
          api_session_id: null,
          include: true,
        },
        take: 10,
        orderBy: { id: "asc" },
      });
    });

    test("should handle default parameters", async () => {
      const mockChats = [{ id: 1, prompt: "Chat 1" }];
      (prisma.workspace_chats.findMany as jest.Mock).mockResolvedValue(
        mockChats
      );

      await WorkspaceChats.forWorkspaceByUser(1, 1);

      expect(prisma.workspace_chats.findMany).toHaveBeenCalledWith({
        where: {
          workspaceId: 1,
          user_id: 1,
          thread_id: null,
          api_session_id: null,
          include: true,
        },
        orderBy: { id: "asc" },
      });
    });

    test("should handle database errors", async () => {
      (prisma.workspace_chats.findMany as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      const result = await WorkspaceChats.forWorkspaceByUser(1, 1);

      expect(result).toEqual([]);
      expect(console.error).toHaveBeenCalled();
    });
  });

  describe("forWorkspace", () => {
    test("should retrieve all chats for workspace", async () => {
      const mockChats = [
        { id: 1, prompt: "Chat 1", workspaceId: 1 },
        { id: 2, prompt: "Chat 2", workspaceId: 1 },
      ];

      (prisma.workspace_chats.findMany as jest.Mock).mockResolvedValue(
        mockChats
      );

      const result = await WorkspaceChats.forWorkspace(1, 10, {
        id: "desc",
      } as any);

      expect(result).toEqual(mockChats);
      expect(prisma.workspace_chats.findMany).toHaveBeenCalledWith({
        where: {
          workspaceId: 1,
          thread_id: null,
          api_session_id: null,
          include: true,
        },
        orderBy: { id: "desc" },
        take: 10,
      });
    });

    test("should handle default parameters", async () => {
      const mockChats = [{ id: 1, prompt: "Chat 1" }];
      (prisma.workspace_chats.findMany as jest.Mock).mockResolvedValue(
        mockChats
      );

      await WorkspaceChats.forWorkspace(1);

      expect(prisma.workspace_chats.findMany).toHaveBeenCalledWith({
        where: {
          workspaceId: 1,
          thread_id: null,
          api_session_id: null,
          include: true,
        },
        orderBy: { id: "asc" },
      });
    });

    test("should handle database errors", async () => {
      (prisma.workspace_chats.findMany as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      const result = await WorkspaceChats.forWorkspace(1);

      expect(result).toEqual([]);
      expect(console.error).toHaveBeenCalled();
    });
  });

  describe("markHistoryInvalid", () => {
    test("should mark workspace history as invalid", async () => {
      (prisma.workspace_chats.updateMany as jest.Mock).mockResolvedValue({
        count: 5,
      });

      const result = await WorkspaceChats.markHistoryInvalid(1, { id: 1 });

      expect(result).toBe(undefined);
      expect(prisma.workspace_chats.updateMany).toHaveBeenCalledWith({
        where: { workspaceId: 1, user_id: 1, thread_id: null },
        data: { include: false },
      });
    });

    test("should handle database errors", async () => {
      (prisma.workspace_chats.updateMany as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      const result = await WorkspaceChats.markHistoryInvalid(1, { id: 1 });

      expect(result).toBe(undefined);
      expect(console.error).toHaveBeenCalled();
    });

    test("should handle null user ID", async () => {
      (prisma.workspace_chats.updateMany as jest.Mock).mockResolvedValue({
        count: 3,
      });

      const result = await WorkspaceChats.markHistoryInvalid(1, null);

      expect(result).toBe(undefined);
      expect(prisma.workspace_chats.updateMany).toHaveBeenCalledWith({
        where: { workspaceId: 1, user_id: undefined, thread_id: null },
        data: { include: false },
      });
    });
  });

  describe("markThreadHistoryInvalid", () => {
    test("should mark thread history as invalid", async () => {
      (prisma.workspace_chats.updateMany as jest.Mock).mockResolvedValue({
        count: 3,
      });

      const result = await WorkspaceChats.markThreadHistoryInvalid(
        1,
        { id: 1 },
        1
      );

      expect(result).toBe(undefined);
      expect(prisma.workspace_chats.updateMany).toHaveBeenCalledWith({
        where: { workspaceId: 1, thread_id: 1, user_id: 1 },
        data: { include: false },
      });
    });

    test("should handle database errors", async () => {
      (prisma.workspace_chats.updateMany as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      const result = await WorkspaceChats.markThreadHistoryInvalid(
        1,
        { id: 1 },
        1
      );

      expect(result).toBe(undefined);
      expect(console.error).toHaveBeenCalled();
    });

    test("should handle null values", async () => {
      (prisma.workspace_chats.updateMany as jest.Mock).mockResolvedValue({
        count: 2,
      });

      const result = await WorkspaceChats.markThreadHistoryInvalid(
        1,
        null,
        null
      );

      expect(result).toBe(undefined);
      // Function returns early when threadId is null, so no prisma call is made
      expect(prisma.workspace_chats.updateMany).not.toHaveBeenCalled();
    });
  });

  describe("get", () => {
    test("should retrieve specific chat by ID", async () => {
      const mockChat = {
        id: 1,
        prompt: "Test prompt",
        response: "Test response",
        workspaceId: 1,
      };

      (prisma.workspace_chats.findFirst as jest.Mock).mockResolvedValue(
        mockChat
      );

      const result = await WorkspaceChats.get({ id: 1 });

      expect(result).toEqual(mockChat);
      expect(prisma.workspace_chats.findFirst).toHaveBeenCalledWith({
        where: { id: 1 },
      });
    });

    test("should return null for non-existent chat", async () => {
      (prisma.workspace_chats.findFirst as jest.Mock).mockResolvedValue(null);

      const result = await WorkspaceChats.get({ id: 999 });

      expect(result).toBeNull();
    });

    test("should handle database errors", async () => {
      (prisma.workspace_chats.findFirst as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      const result = await WorkspaceChats.get({ id: 1 });

      expect(result).toBeNull();
      expect(console.error).toHaveBeenCalled();
    });
  });

  describe("delete", () => {
    test("should delete chat by ID", async () => {
      const _mockDeletedChat = {
        id: 1,
        prompt: "Deleted chat",
        workspaceId: 1,
      };

      (prisma.workspace_chats.deleteMany as jest.Mock).mockResolvedValue({
        count: 1,
      });

      const result = await WorkspaceChats.delete({ id: 1 });

      expect(result).toBe(true);
      expect(prisma.workspace_chats.deleteMany).toHaveBeenCalledWith({
        where: { id: 1 },
      });
    });

    test("should handle deletion errors", async () => {
      (prisma.workspace_chats.deleteMany as jest.Mock).mockRejectedValue(
        new Error("Chat not found")
      );

      const result = await WorkspaceChats.delete({ id: 999 });

      expect(result).toBe(false);
      expect(console.error).toHaveBeenCalled();
    });

    test("should handle database connection errors", async () => {
      (prisma.workspace_chats.deleteMany as jest.Mock).mockRejectedValue(
        new Error("Database connection failed")
      );

      const result = await WorkspaceChats.delete({ id: 1 });

      expect(result).toBe(false);
    });
  });

  describe("deleteOlderThan", () => {
    test("should delete chats older than specified date", async () => {
      const cutoffDate = new Date("2023-01-01");
      (prisma.workspace_chats.deleteMany as jest.Mock).mockResolvedValue({
        count: 10,
      });

      const result = await WorkspaceChats.deleteOlderThan(cutoffDate);

      expect(result).toBe(10);
      expect(prisma.workspace_chats.deleteMany).toHaveBeenCalledWith({
        where: { createdAt: { lt: cutoffDate } },
      });
    });

    test("should handle no chats to delete", async () => {
      const cutoffDate = new Date("2020-01-01");
      (prisma.workspace_chats.deleteMany as jest.Mock).mockResolvedValue({
        count: 0,
      });

      const result = await WorkspaceChats.deleteOlderThan(cutoffDate);

      expect(result).toBe(0);
    });

    test("should handle database errors", async () => {
      const cutoffDate = new Date("2023-01-01");
      (prisma.workspace_chats.deleteMany as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await expect(WorkspaceChats.deleteOlderThan(cutoffDate)).rejects.toThrow(
        "Database error"
      );
      expect(console.error).toHaveBeenCalled();
    });
  });

  describe("where", () => {
    test("should retrieve chats with where clause", async () => {
      const mockChats = [
        { id: 1, prompt: "Chat 1", workspaceId: 1 },
        { id: 2, prompt: "Chat 2", workspaceId: 1 },
      ];

      (prisma.workspace_chats.findMany as jest.Mock).mockResolvedValue(
        mockChats
      );

      const result = await WorkspaceChats.where(
        { workspaceId: 1 },
        10,
        { id: "desc" },
        0
      );

      expect(result).toEqual(mockChats);
      expect(prisma.workspace_chats.findMany).toHaveBeenCalledWith({
        where: { workspaceId: 1 },
        take: 10,
        skip: 0,
        orderBy: { id: "desc" },
      });
    });

    test("should handle empty where clause", async () => {
      const mockChats = [{ id: 1, prompt: "All chats" }];
      (prisma.workspace_chats.findMany as jest.Mock).mockResolvedValue(
        mockChats
      );

      const result = await WorkspaceChats.where();

      expect(result).toEqual(mockChats);
      expect(prisma.workspace_chats.findMany).toHaveBeenCalledWith({
        where: {},
      });
    });

    test("should handle complex where clauses", async () => {
      const complexWhere = {
        workspaceId: 1,
        user_id: 1,
        include: true,
        thread_id: { not: null },
      };

      const mockChats = [{ id: 1, prompt: "Complex query" }];
      (prisma.workspace_chats.findMany as jest.Mock).mockResolvedValue(
        mockChats
      );

      const result = await WorkspaceChats.where(
        complexWhere,
        5,
        { id: "desc" },
        10
      );

      expect(result).toEqual(mockChats);
      expect(prisma.workspace_chats.findMany).toHaveBeenCalledWith({
        where: complexWhere,
        take: 5,
        skip: 10,
        orderBy: { id: "desc" },
      });
    });

    test("should handle database errors", async () => {
      (prisma.workspace_chats.findMany as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      const result = await WorkspaceChats.where({ workspaceId: 1 });

      expect(result).toEqual([]);
      expect(console.error).toHaveBeenCalled();
    });
  });

  describe("count", () => {
    test("should count chats with where clause", async () => {
      (prisma.workspace_chats.count as jest.Mock).mockResolvedValue(25);

      const result = await WorkspaceChats.count({ workspaceId: 1 });

      expect(result).toBe(25);
      expect(prisma.workspace_chats.count).toHaveBeenCalledWith({
        where: { workspaceId: 1 },
      });
    });

    test("should count all chats with empty clause", async () => {
      (prisma.workspace_chats.count as jest.Mock).mockResolvedValue(100);

      const result = await WorkspaceChats.count();

      expect(result).toBe(100);
      expect(prisma.workspace_chats.count).toHaveBeenCalledWith({
        where: {},
      });
    });

    test("should handle database errors", async () => {
      (prisma.workspace_chats.count as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      const result = await WorkspaceChats.count({ workspaceId: 1 });

      expect(result).toBe(0);
      expect(console.error).toHaveBeenCalled();
    });
  });

  describe("whereWithData", () => {
    test("should retrieve chats with related data", async () => {
      const mockChatsWithData = [
        {
          id: 1,
          prompt: "Chat 1",
          workspaceId: 1,
          workspace: { name: "Test Workspace", slug: "test-workspace" },
          user: { username: "testuser" },
        },
      ];

      (prisma.workspace_chats.findMany as jest.Mock).mockResolvedValue(
        mockChatsWithData
      );

      const result = await WorkspaceChats.whereWithData(
        { workspaceId: 1 },
        10,
        0
      );

      expect(result).toEqual(mockChatsWithData);
      expect(prisma.workspace_chats.findMany).toHaveBeenCalledWith({
        where: { workspaceId: 1 },
        take: 10,
        skip: 0,
      });
    });

    test("should handle default parameters", async () => {
      const mockChatsWithData = [{ id: 1, prompt: "Chat with data" }];
      (prisma.workspace_chats.findMany as jest.Mock).mockResolvedValue(
        mockChatsWithData
      );

      await WorkspaceChats.whereWithData();

      expect(prisma.workspace_chats.findMany).toHaveBeenCalledWith({
        where: {},
      });
    });

    test("should handle database errors", async () => {
      (prisma.workspace_chats.findMany as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      const result = await WorkspaceChats.whereWithData({ workspaceId: 1 });

      expect(result).toEqual([]);
      expect(console.error).toHaveBeenCalled();
    });
  });

  describe("updateFeedbackScore", () => {
    test("should update feedback score successfully", async () => {
      const mockUpdatedChat = {
        id: 1,
        feedbackScore: true,
        prompt: "Chat with feedback",
      };

      (prisma.workspace_chats.update as jest.Mock).mockResolvedValue(
        mockUpdatedChat
      );

      const result = await WorkspaceChats.updateFeedbackScore(1, 1);

      expect(result).toBe(undefined);
      expect(prisma.workspace_chats.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: { feedbackScore: true },
      });
    });

    test("should handle null feedback score", async () => {
      const mockUpdatedChat = {
        id: 1,
        feedbackScore: null,
        prompt: "Chat with null feedback",
      };

      (prisma.workspace_chats.update as jest.Mock).mockResolvedValue(
        mockUpdatedChat
      );

      const result = await WorkspaceChats.updateFeedbackScore(1, null);

      expect(result).toBe(undefined);
      expect(prisma.workspace_chats.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: { feedbackScore: null },
      });
    });

    test("should handle update errors", async () => {
      (prisma.workspace_chats.update as jest.Mock).mockRejectedValue(
        new Error("Chat not found")
      );

      const result = await WorkspaceChats.updateFeedbackScore(999, 1);

      expect(result).toBe(undefined);
      expect(console.error).toHaveBeenCalled();
    });
  });

  describe("update", () => {
    test("should update chat data successfully", async () => {
      const updateData = {
        prompt: "Updated prompt",
        response: '{"content": "Updated response"}',
        include: false,
      };

      const mockUpdatedChat = {
        id: 1,
        ...updateData,
        workspaceId: 1,
      };

      (prisma.workspace_chats.update as jest.Mock).mockResolvedValue(
        mockUpdatedChat
      );

      const result = await WorkspaceChats.update(1, updateData);

      expect(result).toEqual(mockUpdatedChat);
      expect(prisma.workspace_chats.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: updateData,
      });
    });

    test("should handle partial updates", async () => {
      const partialUpdate = { include: false };

      const mockUpdatedChat = {
        id: 1,
        include: false,
        prompt: "Original prompt",
      };

      (prisma.workspace_chats.update as jest.Mock).mockResolvedValue(
        mockUpdatedChat
      );

      const result = await WorkspaceChats.update(1, partialUpdate);

      expect(result).toEqual(mockUpdatedChat);
      expect(prisma.workspace_chats.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: partialUpdate,
      });
    });

    test("should handle update errors", async () => {
      (prisma.workspace_chats.update as jest.Mock).mockRejectedValue(
        new Error("Update failed")
      );

      const result = await WorkspaceChats.update(1, { prompt: "New prompt" });

      expect(result).toBeNull();
      expect(console.error).toHaveBeenCalled();
    });
  });

  describe("bulkCreate", () => {
    test("should create multiple chats successfully", async () => {
      const chatDataArray = [
        { workspaceId: 1, prompt: "Chat 1", response: {}, user_id: 1 },
        { workspaceId: 1, prompt: "Chat 2", response: {}, user_id: 1 },
        { workspaceId: 1, prompt: "Chat 3", response: {}, user_id: 2 },
      ] as any;

      (prisma.workspace_chats.create as jest.Mock).mockImplementation((data) =>
        Promise.resolve({ id: Math.random(), ...data.data })
      );

      const result = await WorkspaceChats.bulkCreate(chatDataArray);

      expect((result as any).chats).toBeDefined();
      expect((result as any).message).toBeNull();
      expect(prisma.workspace_chats.create).toHaveBeenCalledTimes(3);
    });

    test("should handle empty array", async () => {
      const result = await WorkspaceChats.bulkCreate([]);

      expect((result as any).chats).toBeDefined();
      expect((result as any).message).toBeNull();
      expect(prisma.workspace_chats.create).not.toHaveBeenCalled();
    });

    test("should handle bulk creation errors", async () => {
      const chatDataArray = [
        { workspaceId: 1, prompt: "Chat 1", response: {}, user_id: 1 },
      ];

      (prisma.workspace_chats.create as jest.Mock).mockRejectedValue(
        new Error("Bulk creation failed")
      );

      const result = await WorkspaceChats.bulkCreate(chatDataArray);

      expect((result as any).chats).toBeNull();
      expect((result as any).message).toBe("Bulk creation failed");
      expect(console.error).toHaveBeenCalled();
    });

    test("should handle large bulk operations", async () => {
      const largeDataArray = Array.from({ length: 1000 }, (_, i) => ({
        workspaceId: 1,
        prompt: `Chat ${i + 1}`,
        response: {},
        user_id: 1,
      }));

      (prisma.workspace_chats.create as jest.Mock).mockImplementation((data) =>
        Promise.resolve({ id: Math.random(), ...data.data })
      );

      const result = await WorkspaceChats.bulkCreate(largeDataArray);

      expect((result as any).chats).toBeDefined();
      expect((result as any).message).toBeNull();
    });
  });

  describe("forThread", () => {
    test("should retrieve chats for specific thread", async () => {
      const mockThreadChats = [
        { id: 1, prompt: "Thread chat 1", thread_id: 1, workspaceId: 1 },
        { id: 2, prompt: "Thread chat 2", thread_id: 1, workspaceId: 1 },
      ];

      (prisma.workspace_chats.findMany as jest.Mock).mockResolvedValue(
        mockThreadChats
      );

      const result = await WorkspaceChats.forThread(1, true);

      expect(result).toEqual(mockThreadChats);
      expect(prisma.workspace_chats.findMany).toHaveBeenCalledWith({
        where: { thread_id: 1 },
        orderBy: { id: "asc" },
      });
    });

    test("should handle default parameters", async () => {
      const mockThreadChats = [{ id: 1, prompt: "Thread chat" }];
      (prisma.workspace_chats.findMany as jest.Mock).mockResolvedValue(
        mockThreadChats
      );

      await WorkspaceChats.forThread(1, true);

      expect(prisma.workspace_chats.findMany).toHaveBeenCalledWith({
        where: { thread_id: 1 },
        orderBy: { id: "asc" },
      });
    });

    test("should handle database errors", async () => {
      (prisma.workspace_chats.findMany as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      const result = await WorkspaceChats.forThread(1, true);

      expect(result).toEqual([]);
      expect(console.error).toHaveBeenCalled();
    });
  });

  describe("Edge Cases and Error Handling", () => {
    test("should handle very large prompts", async () => {
      const largePrompt = "x".repeat(100000);
      const chatData = {
        workspaceId: 1,
        prompt: largePrompt,
      };

      const mockCreatedChat = { id: 1, prompt: largePrompt };
      (prisma.workspace_chats.create as jest.Mock).mockResolvedValue(
        mockCreatedChat
      );

      const result = await WorkspaceChats.new(chatData);

      expect(result.chat).toBeDefined();
      expect(result.message).toBeNull();
    });

    test("should handle special characters in prompts", async () => {
      const specialPrompt =
        "Special chars: àáâãäåæçèéêë ñòóôõö ùúûü ÿý 中文 العربية עברית 🚀🎉";
      const chatData = {
        workspaceId: 1,
        prompt: specialPrompt,
      };

      const mockCreatedChat = { id: 1, prompt: specialPrompt };
      (prisma.workspace_chats.create as jest.Mock).mockResolvedValue(
        mockCreatedChat
      );

      const result = await WorkspaceChats.new(chatData);

      expect(result.chat).toBeDefined();
      expect(result.message).toBeNull();
    });

    test("should handle invalid JSON in response", async () => {
      const chatData = {
        workspaceId: 1,
        prompt: "Test prompt",
        response: {},
      };

      // Should still handle it as a string
      const mockCreatedChat = { id: 1, response: "{}" };
      (prisma.workspace_chats.create as jest.Mock).mockResolvedValue(
        mockCreatedChat
      );

      const result = await WorkspaceChats.new(chatData);

      expect(result.chat).toBeDefined();
      expect(result.message).toBeNull();
    });

    test("should handle concurrent chat creation", async () => {
      const chatDataArray = Array.from({ length: 10 }, (_, i) => ({
        workspaceId: 1,
        prompt: `Concurrent chat ${i + 1}`,
      }));

      // Mock successful creation for all
      (prisma.workspace_chats.create as jest.Mock).mockImplementation((data) =>
        Promise.resolve({ id: Math.random(), ...data.data })
      );

      const promises = chatDataArray.map((data) => WorkspaceChats.new(data));
      const results = await Promise.all(promises);

      results.forEach((result) => {
        expect(result.chat).toBeDefined();
        expect(result.message).toBeNull();
      });
    });

    test("should handle null and undefined values gracefully", async () => {
      const chatDataWithNulls = {
        workspaceId: 1,
        prompt: "Test prompt",
        response: {},
        user: undefined,
        threadId: null,
        apiSessionId: undefined,
        invoice_ref: null,
        metrics: undefined,
      } as any;

      const mockCreatedChat = { id: 1, prompt: "Test prompt" };
      (prisma.workspace_chats.create as jest.Mock).mockResolvedValue(
        mockCreatedChat
      );

      const result = await WorkspaceChats.new(chatDataWithNulls);

      expect(result.chat).toBeDefined();
      expect(result.message).toBeNull();
    });

    test("should handle database connection timeouts", async () => {
      (prisma.workspace_chats.findMany as jest.Mock).mockImplementation(
        () =>
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error("Connection timeout")), 100)
          )
      );

      const result = await WorkspaceChats.forWorkspace(1);

      expect(result).toEqual([]);
      expect(console.error).toHaveBeenCalled();
    });

    test("should handle invalid workspace IDs", async () => {
      const chatData = {
        workspaceId: -1, // Invalid ID
        prompt: "Test prompt",
      };

      (prisma.workspace_chats.create as jest.Mock).mockRejectedValue(
        new Error("Invalid workspace ID")
      );

      const result = await WorkspaceChats.new(chatData);

      expect(result.chat).toBeNull();
      expect(result.message).toBeDefined();
    });

    test("should handle extremely large result sets", async () => {
      // Mock a very large result set
      const largeResultSet = Array.from({ length: 10000 }, (_, i) => ({
        id: i + 1,
        prompt: `Chat ${i + 1}`,
        workspaceId: 1,
      }));

      (prisma.workspace_chats.findMany as jest.Mock).mockResolvedValue(
        largeResultSet
      );

      const result = await WorkspaceChats.forWorkspace(1, 10000);

      expect(result).toHaveLength(10000);
      expect(result[0].prompt).toBe("Chat 1");
      expect(result[9999].prompt).toBe("Chat 10000");
    });
  });

  describe("Performance Tests", () => {
    test("should handle high-frequency chat creation efficiently", async () => {
      const chatDataArray = Array.from({ length: 100 }, (_, i) => ({
        workspaceId: 1,
        prompt: `Performance test chat ${i + 1}`,
        response: { content: `Response ${i + 1}` },
        user: { id: 1, username: "testuser" },
      }));

      (prisma.workspace_chats.create as jest.Mock).mockImplementation((data) =>
        Promise.resolve({ id: Math.random(), ...data.data })
      );

      const startTime = Date.now();
      const promises = chatDataArray.map((data) => WorkspaceChats.new(data));
      await Promise.all(promises);
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
    });

    test("should handle large pagination efficiently", async () => {
      const mockChats = Array.from({ length: 1000 }, (_, i) => ({
        id: i + 1,
        prompt: `Paginated chat ${i + 1}`,
        workspaceId: 1,
      }));

      (prisma.workspace_chats.findMany as jest.Mock).mockResolvedValue(
        mockChats.slice(0, 100) // First page
      );

      const startTime = Date.now();
      const result = await WorkspaceChats.forWorkspace(1, 100);
      const endTime = Date.now();

      expect(result).toHaveLength(100);
      expect(endTime - startTime).toBeLessThan(100); // Should complete quickly
    });
  });
});
