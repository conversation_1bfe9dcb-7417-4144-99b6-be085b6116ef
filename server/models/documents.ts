// Global type definitions for ESLint compatibility
/* global Record:readonly, Partial:readonly */

import { v4 as uuidv4 } from "uuid";
import { getVectorDbClass } from "../utils/helpers";
import {
  PrismaClient,
  workspace_documents as PrismaWorkspaceDocument,
  workspaces as PrismaWorkspace,
} from "@prisma/client";
import { Telemetry } from "./telemetry";
import { EventLogs } from "./eventLogs";
import { safeJsonParse } from "../utils/http";
import * as path from "path";
import * as fs from "fs";
import {
  fileData,
  documentsPath,
  isWithin,
  deletePDFFile,
} from "../utils/files";
import { bulkDocumentProcessor } from "../jobs/bulk-document-processor";
import { VectorDatabase } from "../types/vectorDb";

// Initialize Prisma client safely for test environments
let prisma: PrismaClient;
try {
  prisma = new PrismaClient();
} catch {
  // If Prisma client initialization fails (e.g., in CI without generated client),
  // create a minimal fallback for tests
  console.warn("Failed to initialize Prisma client, using fallback for tests");
  prisma = {} as PrismaClient;
}

function getFilenameFromPath(path: string | null | undefined): string {
  if (!path) return "";
  return path.split("/").pop() || "";
}

// Document types
export interface DocumentMetadata {
  title?: string;
  chunkSource?: string;
  [key: string]: string | boolean | null | undefined;
}

export interface ParsedDocumentResult {
  metadata: DocumentMetadata | null;
  type: string | null;
  source: string | null;
}

export interface DocumentCreateData {
  docId: string;
  filename: string;
  docpath: string;
  workspaceId: number;
  metadata: string;
}

export interface DocumentUpdateData {
  pinned?: boolean;
  pdr?: boolean;
  watched?: boolean;
  starred?: boolean;
  lastUpdatedAt?: Date;
}

export interface DocumentResult {
  document?: PrismaWorkspaceDocument | null;
  message: string | null;
}

export interface DocumentUpdateManyResult {
  count: number;
  message: string | null;
}

export interface DocumentEmbedResult {
  failedToEmbed: string[];
  errors: string[];
  embedded: string[];
  bulkJob?: boolean;
  jobId?: string;
  status?: string;
  progress?: Record<string, unknown>;
}

export interface BatchProcessResult {
  embedded: string[];
  failedToEmbed: string[];
  errors: string[];
}

export interface VectorizedDocument {
  docId: string;
  path: string;
  workspaceSlug: string;
}

export interface DocumentContentResult {
  title: string;
  content: string;
}

export interface UpdatePathResult {
  updatedCount: number;
  message: string | null;
}

export interface WhereClause {
  id?: number;
  docId?: string;
  filename?: string | { endsWith?: string; contains?: string };
  docpath?: string | { contains?: string };
  workspaceId?: number;
  pinned?: boolean;
  pdr?: boolean;
  watched?: boolean;
  starred?: boolean;
  metadata?: string | { contains?: string };
  OR?: WhereClause[];
  [key: string]:
    | string
    | number
    | boolean
    | Date
    | WhereClause[]
    | { endsWith?: string; contains?: string }
    | { contains?: string }
    | null
    | undefined;
}

export interface DocumentModelStatic {
  writable: string[];
  parseDocumentTypeAndSource: (
    document: PrismaWorkspaceDocument | null
  ) => ParsedDocumentResult;
  forWorkspace: (
    workspaceId?: number | null
  ) => Promise<PrismaWorkspaceDocument[]>;
  delete: (clause?: WhereClause) => Promise<boolean>;
  deleteStorage: (workspaceSlug: string) => Promise<boolean>;
  get: (clause?: WhereClause) => Promise<PrismaWorkspaceDocument | null>;
  getPDRs: (clause?: WhereClause) => Promise<number[]>;
  where: (
    clause?: WhereClause,
    limit?: number | null,
    orderBy?: Record<string, "asc" | "desc"> | null,
    include?: Record<string, boolean> | null,
    select?: Record<string, boolean> | null
  ) => Promise<PrismaWorkspaceDocument[]>;
  addDocuments: (
    workspace: PrismaWorkspace,
    additions?: string[],
    userId?: number | null,
    slug?: string
  ) => Promise<DocumentEmbedResult>;
  _addDocumentsSequential: (
    workspace: PrismaWorkspace,
    additions?: string[],
    userId?: number | null,
    slug?: string
  ) => Promise<DocumentEmbedResult>;
  _processBatch: (
    workspace: PrismaWorkspace,
    batch: string[],
    VectorDb: VectorDatabase
  ) => Promise<BatchProcessResult>;
  removeDocuments: (
    workspace: PrismaWorkspace,
    removals?: string[],
    userId?: number | null,
    slug?: string,
    isWorkspaceRemoval?: boolean
  ) => Promise<boolean>;
  count: (clause?: WhereClause, limit?: number | null) => Promise<number>;
  update: (
    id?: number | null,
    data?: DocumentUpdateData
  ) => Promise<DocumentResult>;
  _updateAll: (
    clause?: WhereClause,
    data?: DocumentUpdateData
  ) => Promise<boolean>;
  updateMany: (
    workspaceId: number,
    data?: DocumentUpdateData
  ) => Promise<DocumentUpdateManyResult>;
  content: (docId: string) => Promise<DocumentContentResult>;
  contents: (path: string) => Promise<DocumentContentResult>;
  contentByDocPath: (docPath: string) => Promise<DocumentContentResult>;
  _stripSource: (sourceString: string, type: string) => string;
  updateDocpathAndMetadata: (
    oldPath: string,
    newPath: string
  ) => Promise<UpdatePathResult>;
}

const Document: DocumentModelStatic = {
  writable: ["pinned", "pdr", "watched", "starred", "lastUpdatedAt"],

  /**
   * Parse document type and source from document metadata
   */
  parseDocumentTypeAndSource: function (
    document: PrismaWorkspaceDocument | null
  ): ParsedDocumentResult {
    const metadata = safeJsonParse(
      document?.metadata ?? null,
      null
    ) as DocumentMetadata | null;
    if (!metadata) return { metadata: null, type: null, source: null };

    // Parse the correct type of source and its original source path.
    const chunkSource = metadata?.chunkSource;
    if (!chunkSource) return { metadata, type: null, source: null };

    const idx = chunkSource.indexOf("://");
    const [type, source] = [
      chunkSource.slice(0, idx),
      chunkSource.slice(idx + 3),
    ];
    return { metadata, type, source: this._stripSource(source, type) };
  },

  forWorkspace: async function (
    workspaceId: number | null = null
  ): Promise<PrismaWorkspaceDocument[]> {
    if (!workspaceId) return [];

    try {
      return await prisma.workspace_documents.findMany({
        where: { workspaceId: workspaceId },
        orderBy: {
          filename: "asc",
        },
      });
    } catch (error: unknown) {
      console.error(
        `ERROR in forWorkspace: ${error instanceof Error ? error.message : String(error)}`
      );
      return [];
    }
  },

  delete: async function (clause: WhereClause = {}): Promise<boolean> {
    try {
      await prisma.workspace_documents.deleteMany({ where: clause });
      return true;
    } catch (error: unknown) {
      console.error(error instanceof Error ? error.message : String(error));
      return false;
    }
  },

  deleteStorage: async function (workspaceSlug: string): Promise<boolean> {
    if (!workspaceSlug) return false;
    try {
      const workspacePath = path?.join(documentsPath, workspaceSlug);
      if (
        !fs?.existsSync(workspacePath) ||
        !isWithin(documentsPath, workspacePath)
      ) {
        return false;
      }

      fs?.rmSync(workspacePath, { recursive: true, force: true });
      return true;
    } catch (error: unknown) {
      console.error(
        "Failed to delete workspace storage directory.",
        error instanceof Error ? error.message : String(error)
      );
      return false;
    }
  },

  get: async function (
    clause: WhereClause = {}
  ): Promise<PrismaWorkspaceDocument | null> {
    try {
      // Log the query for debugging
      console.log(`[DOCUMENT-GET] Query:`, JSON.stringify(clause, null, 2));

      // The Prisma schema for workspace_documents uses camelCase (workspaceId, pinned, pdr, watched)
      // No need to convert field names - use them as is
      const document = await prisma.workspace_documents.findFirst({
        where: clause,
      });

      // Log the result for debugging
      if (document) {
        console.log(`[DOCUMENT-GET] Found document:`, {
          id: document?.id,
          docId: document?.docId,
          filename: document?.filename,
          docpath: document?.docpath,
          starred: document?.starred,
        });
      } else {
        console.log(`[DOCUMENT-GET] No document found for query`);
      }

      return document || null;
    } catch (error: unknown) {
      console.error(
        `ERROR in Document?.get: ${error instanceof Error ? error.message : String(error)}`
      );
      return null;
    }
  },

  getPDRs: async function (clause: WhereClause = {}): Promise<number[]> {
    try {
      // The Prisma schema for workspace_documents uses camelCase (workspaceId, pinned, pdr, watched)
      // No need to convert field names - use them as is
      const workspaceIds = await prisma.workspace_documents.findMany({
        where: clause,
        select: {
          workspaceId: true, // Using camelCase field name as in Prisma schema
        },
        orderBy: {
          filename: "asc",
        },
      });
      return workspaceIds?.map((pdr) => pdr?.workspaceId) || [];
    } catch (error: unknown) {
      console.error(
        `ERROR in Document?.getPDRs: ${error instanceof Error ? error.message : String(error)}`
      );
      return [];
    }
  },

  where: async function (
    clause: WhereClause = {},
    limit: number | null = null,
    orderBy: Record<string, "asc" | "desc"> | null = { filename: "asc" },
    include: Record<string, boolean> | null = null,
    select: Record<string, boolean> | null = null
  ): Promise<PrismaWorkspaceDocument[]> {
    try {
      // Only log DOCUMENT-WHERE in test environment
      if (process.env.NODE_ENV === "test") {
        console.log(`[DOCUMENT-WHERE] Query:`, JSON.stringify(clause, null, 2));
      }

      // The Prisma schema for workspace_documents uses camelCase (workspaceId, pinned, pdr, watched)
      // No need to convert field names - use them as is
      const results = await prisma.workspace_documents.findMany({
        where: clause,
        ...(limit !== null ? { take: limit } : {}),
        ...(orderBy !== null ? { orderBy } : {}),
        ...(include !== null ? { include } : {}),
        ...(select !== null ? { select: { ...select } } : {}),
      });

      // Only log DOCUMENT-WHERE results in test environment
      if (process.env.NODE_ENV === "test") {
        console.log(
          `[DOCUMENT-WHERE] Found ${results?.length} documents:`,
          results?.map((doc) => ({
            id: doc?.id,
            docId: doc?.docId,
            filename: doc?.filename,
            docpath: doc?.docpath,
            starred: doc?.starred,
          }))
        );
      }

      return results;
    } catch (error: unknown) {
      console.error(
        `ERROR in Document?.where: ${error instanceof Error ? error.message : String(error)}`
      );
      return [];
    }
  },

  addDocuments: async function (
    workspace: PrismaWorkspace,
    additions: string[] = [],
    userId: number | null = null,
    slug: string = ""
  ): Promise<DocumentEmbedResult> {
    if (additions?.length === 0)
      return { failedToEmbed: [], embedded: [], errors: [] };

    // Use bulk processor for large operations (20+ files to match frontend expectations)
    const BULK_THRESHOLD =
      parseInt(process.env.BULK_PROCESSING_THRESHOLD || "") || 20;
    if (additions.length >= BULK_THRESHOLD) {
      console.log(
        `[ADD_DOCUMENTS] Using bulk processor for ${additions?.length} files`
      );

      try {
        const result = await bulkDocumentProcessor?.startBulkJob(
          workspace,
          additions,
          String(userId || 0),
          slug
        );
        return {
          bulkJob: true,
          jobId: result?.jobId,
          status: result?.status,
          progress: result?.progress as unknown as Record<string, unknown>,
          failedToEmbed: [],
          errors: [],
          embedded: [],
        };
      } catch (error: unknown) {
        console.error(
          `[ADD_DOCUMENTS] Bulk processor failed, falling back to sequential:`,
          error
        );
        // Fall back to sequential processing if bulk processor fails
      }
    }

    // Sequential processing for smaller operations or fallback
    console.log(
      `[ADD_DOCUMENTS] Using sequential processing for ${additions?.length} files`
    );
    return this._addDocumentsSequential(workspace, additions, userId, slug);
  },

  _addDocumentsSequential: async function (
    workspace: PrismaWorkspace,
    additions: string[] = [],
    userId: number | null = null,
    slug: string = ""
  ): Promise<DocumentEmbedResult> {
    const VectorDb = getVectorDbClass() as VectorDatabase;
    const embedded: string[] = [];
    const failedToEmbed: string[] = [];
    const errors: string[] = [];

    // Process in smaller batches to improve performance
    const BATCH_SIZE = 10;
    for (let i = 0; i < additions.length; i += BATCH_SIZE) {
      const batch = additions.slice(i, i + BATCH_SIZE);
      const batchResults = await this._processBatch(workspace, batch, VectorDb);

      if (batchResults?.embedded) embedded.push(...batchResults.embedded);
      if (batchResults?.failedToEmbed)
        failedToEmbed.push(...batchResults.failedToEmbed);
      batchResults?.errors.forEach((error: string) => errors.push(error));
    }

    let LLMSelection: string;
    let Embedder: string;
    let VectorDbSelection: string;
    if (slug === "document-drafting") {
      LLMSelection = process.env.LLM_PROVIDER_DD || "openai";
      Embedder = process.env.EMBEDDING_ENGINE_DD || "inherit";
      VectorDbSelection = process.env.VECTOR_DB_DD || "lancedb";
    } else {
      LLMSelection = process.env.LLM_PROVIDER || "openai";
      Embedder = process.env.EMBEDDING_ENGINE || "inherit";
      VectorDbSelection = process.env.VECTOR_DB || "lancedb";
    }

    await Telemetry?.sendTelemetry("documents_embedded_in_workspace", {
      LLMSelection,
      Embedder,
      VectorDbSelection,
      TTSSelection: process.env.TTS_PROVIDER || "native",
    });
    await EventLogs?.logEvent(
      "workspace_documents_added",
      {
        workspaceName: workspace?.name || "Unknown Workspace",
        numberOfDocumentsAdded: additions?.length,
      },
      userId
    );
    return { failedToEmbed, errors, embedded };
  },

  _processBatch: async function (
    workspace: PrismaWorkspace,
    batch: string[],
    VectorDb: VectorDatabase
  ): Promise<BatchProcessResult> {
    const { findActualFilePath } = await import(
      "../utils/files/findActualFilePath"
    );
    const embedded: string[] = [];
    const failedToEmbed: string[] = [];
    const errors: string[] = [];

    console.log(`[ADD_DOCUMENTS] Processing batch of ${batch?.length} files`);

    // Step 1: Check which documents already exist to avoid duplicate processing
    const filenames = batch
      .map((path) => getFilenameFromPath(path))
      .filter(Boolean);
    const existingDocs = await prisma.workspace_documents.findMany({
      where: {
        workspaceId: workspace?.id,
        filename: { in: filenames },
      },
      select: { filename: true, docpath: true },
    });

    // Create a set of existing document identifiers for quick lookup
    const existingDocSet = new Set(
      existingDocs.map((doc) => `${doc.filename}:${doc.docpath}`)
    );

    // Filter out files that already exist
    const filesToProcess = batch.filter((path) => {
      const filename = getFilenameFromPath(path);
      const docIdentifier = `${filename}:${path}`;
      const alreadyExists = existingDocSet.has(docIdentifier);

      if (alreadyExists) {
        console.log(`[ADD_DOCUMENTS] Skipping duplicate file: ${filename}`);
        // Note: Don't count existing files as newly embedded
      }

      return !alreadyExists;
    });

    if (filesToProcess.length === 0) {
      console.log(`[ADD_DOCUMENTS] All files in batch already exist, skipping`);
      return { embedded, failedToEmbed, errors };
    }

    // Step 2: Process new files - vectorize first (outside transaction)
    const documentsToCreate: DocumentCreateData[] = [];
    const successfulVectorizations: VectorizedDocument[] = []; // Track successful vectorizations for cleanup

    for (const path of filesToProcess) {
      try {
        // Try to find the actual file location (it may have been moved)
        const filename = path.split("/").pop() || path;
        const actualPath = await findActualFilePath(
          documentsPath,
          workspace.slug,
          filename
        );

        if (!actualPath) {
          console.error(
            `[ADD_DOCUMENTS] File not found in workspace: ${filename}`
          );
          failedToEmbed.push(filename);
          errors.push(`File not found in workspace: ${filename}`);
          continue;
        }

        // Use the actual path found within the workspace
        const fullPath = `${workspace.slug}/${actualPath}`;
        console.log(
          `[ADD_DOCUMENTS] Using actual path for embedding: ${fullPath}`
        );

        const data = await fileData(fullPath);
        if (!data) {
          failedToEmbed.push(filename || "");
          errors.push(`Failed to read file data: ${fullPath}`);
          continue;
        }

        const docId = uuidv4();
        const { pageContent: _pageContent, ...metadata } = data;
        const title = (metadata as { title?: string }).title;

        // Vectorize document OUTSIDE of SQL transaction
        const { vectorized, error } = await VectorDb.addDocumentToNamespace(
          workspace?.slug,
          { ...data, docId },
          fullPath
        );

        if (!vectorized) {
          console.error(
            "Failed to vectorize",
            title || getFilenameFromPath(path)
          );
          failedToEmbed.push(title || getFilenameFromPath(path));
          errors.push(error || `Vectorization failed for ${path}`);
          continue;
        }

        // Track successful vectorization for potential cleanup
        successfulVectorizations.push({
          docId,
          path: fullPath,
          workspaceSlug: workspace.slug,
        });

        // Prepare document for database insertion with the actual path
        const newDoc: DocumentCreateData = {
          docId,
          filename: filename || "",
          docpath: fullPath, // Use the actual found path
          workspaceId: workspace.id,
          metadata: JSON.stringify(metadata),
        };

        documentsToCreate.push(newDoc);
      } catch (error: unknown) {
        console.error(`[ADD_DOCUMENTS] Error processing file ${path}:`, error);
        failedToEmbed.push(getFilenameFromPath(path));
        errors.push(
          `Error processing ${path}: ${error instanceof Error ? error.message : String(error)}`
        );
      }
    }

    // Step 3: Insert documents into SQL database
    if (documentsToCreate.length > 0) {
      try {
        const result = await prisma.workspace_documents.createMany({
          data: documentsToCreate,
        });

        console.log(
          `[ADD_DOCUMENTS] Bulk insert attempted for ${documentsToCreate.length} documents, inserted: ${result.count}`
        );

        // Step 4: Verify which documents were actually inserted
        const insertedDocuments = await prisma.workspace_documents.findMany({
          where: {
            workspaceId: workspace?.id,
            docId: { in: documentsToCreate.map((doc) => doc.docId) },
          },
          select: { docId: true },
        });

        const insertedDocIds = new Set(
          insertedDocuments.map((doc) => doc.docId)
        );

        // Step 5: Count only actually inserted documents as embedded and clean up failed ones
        for (const doc of documentsToCreate) {
          const vectorization = successfulVectorizations.find(
            (v) => v.docId === doc.docId
          );

          if (insertedDocIds.has(doc.docId)) {
            // Document was successfully inserted, count as embedded
            if (vectorization) {
              embedded.push(vectorization.path);
            }
          } else {
            // Document was not inserted (likely duplicate), clean up vector entry
            if (vectorization) {
              try {
                await VectorDb.deleteDocumentFromNamespace(
                  workspace?.slug,
                  doc.docId
                );
                console.log(
                  `[ADD_DOCUMENTS] Cleaned up vector entry for duplicate document: ${doc.filename}`
                );
              } catch (cleanupError: unknown) {
                console.error(
                  `[ADD_DOCUMENTS] Failed to cleanup vector entry for ${doc.filename}:`,
                  cleanupError
                );
              }
            }
            // Note: Don't count duplicates as failures or successes in metrics
          }
        }

        console.log(
          `[ADD_DOCUMENTS] Verified ${insertedDocIds.size} documents actually inserted, ${embedded.length} newly embedded`
        );
      } catch (error: unknown) {
        console.error(
          `[ADD_DOCUMENTS] Bulk insert failed, trying individual inserts:`,
          error
        );

        // Step 6: Fall back to individual inserts with proper cleanup
        for (const doc of documentsToCreate) {
          const vectorization = successfulVectorizations.find(
            (v) => v.docId === doc.docId
          );

          try {
            await prisma.workspace_documents.create({ data: doc });
            // Individual insert succeeded, count as embedded
            if (vectorization) {
              embedded.push(vectorization.path);
            }
          } catch (individualError: unknown) {
            // Check if error is due to duplicate (unique constraint violation)
            if (
              individualError &&
              typeof individualError === "object" &&
              "code" in individualError &&
              individualError.code === "P2002"
            ) {
              // Document already exists, clean up vector entry
              if (vectorization) {
                try {
                  await VectorDb.deleteDocumentFromNamespace(
                    workspace?.slug,
                    doc.docId
                  );
                  console.log(
                    `[ADD_DOCUMENTS] Document ${doc.filename} already exists, cleaned up vector entry`
                  );
                } catch (cleanupError: unknown) {
                  console.error(
                    `[ADD_DOCUMENTS] Failed to cleanup vector entry for duplicate ${doc.filename}:`,
                    cleanupError
                  );
                }
              }
              // Note: Don't count existing documents as failures or successes
            } else {
              // Actual insert failure, clean up vector entry and count as failure
              if (vectorization) {
                try {
                  await VectorDb.deleteDocumentFromNamespace(
                    workspace?.slug,
                    doc.docId
                  );
                  console.log(
                    `[ADD_DOCUMENTS] Cleaned up vector entry for failed insert: ${doc.filename}`
                  );
                } catch (cleanupError: unknown) {
                  console.error(
                    `[ADD_DOCUMENTS] Failed to cleanup vector entry for ${doc.filename}:`,
                    cleanupError
                  );
                }
              }
              console.error(
                `[ADD_DOCUMENTS] Individual insert failed for ${doc.filename}:`,
                individualError
              );
              failedToEmbed.push(doc.filename);
              errors.push(
                individualError instanceof Error
                  ? individualError.message
                  : String(individualError)
              );
            }
          }
        }
      }
    }

    console.log(
      `[ADD_DOCUMENTS] Batch completed - Newly embedded: ${embedded.length}, Failed: ${failedToEmbed.length}, Skipped existing: ${batch.length - filesToProcess.length}`
    );

    return { embedded, failedToEmbed, errors };
  },

  removeDocuments: async function (
    workspace: PrismaWorkspace,
    removals: string[] = [],
    userId: number | null = null,
    slug: string = "",
    isWorkspaceRemoval: boolean = false
  ): Promise<boolean> {
    const VectorDb = getVectorDbClass() as VectorDatabase;
    if (removals?.length === 0) return true;

    console.log("Removing documents:", removals);

    for (const docPath of removals) {
      console.log("Processing document removal:", docPath);

      const document = await this.get({
        docpath: docPath,
        workspaceId: workspace?.id,
      });

      if (!document) {
        console.log("Document not found in database:", docPath);
        continue;
      }

      console.log("Found document in database:", document);

      // Delete the document from vector database
      await VectorDb.deleteDocumentFromNamespace(
        workspace?.slug,
        document.docId
      );

      if (!isWorkspaceRemoval) {
        try {
          // Get the full path of the JSON file
          const fullJsonPath = path?.resolve(documentsPath, docPath);
          console.log("Full JSON path:", fullJsonPath);

          // Delete the associated PDF file first
          deletePDFFile(fullJsonPath);
          // Then delete the JSON file
          fs.unlinkSync(fullJsonPath);
          console.log("Deleted JSON file:", fullJsonPath);

          // Delete database records
          await prisma.workspace_documents.delete({
            where: { id: document.id, workspaceId: workspace?.id },
          });
          await prisma.document_vectors.deleteMany({
            where: { docId: document.docId },
          });
          console.log("Deleted database records for document:", document.id);
        } catch (error: unknown) {
          console.error("Error removing document files:", error);
        }
      } else {
        console.log(
          "Workspace removal flag set. Removing workspace association for:",
          docPath
        );
        try {
          // Remove workspace association but keep the document record
          await prisma.workspace_documents.delete({
            where: { id: document.id, workspaceId: workspace?.id },
          });

          // Remove vector records
          await prisma.document_vectors.deleteMany({
            where: { docId: document.docId },
          });

          // Note: We don't need to update the document record here since we've already
          // deleted the workspace_documents association. The document will appear
          // in the available files list without any workspace-specific properties.

          console.log("Document returned to filerow state:", docPath);
        } catch (error: unknown) {
          console.error("Error returning document to filerow state:", error);
        }
      }
    }

    let LLMSelection: string;
    let Embedder: string;
    let VectorDbSelection: string;
    if (slug === "document-drafting") {
      LLMSelection = process.env.LLM_PROVIDER_DD || "openai";
      Embedder = process.env.EMBEDDING_ENGINE_DD || "inherit";
      VectorDbSelection = process.env.VECTOR_DB_DD || "lancedb";
    } else {
      LLMSelection = process.env.LLM_PROVIDER || "openai";
      Embedder = process.env.EMBEDDING_ENGINE || "inherit";
      VectorDbSelection = process.env.VECTOR_DB || "lancedb";
    }

    await Telemetry?.sendTelemetry("documents_removed_in_workspace", {
      LLMSelection,
      Embedder,
      VectorDbSelection,
      TTSSelection: process.env.TTS_PROVIDER || "native",
    });
    await EventLogs?.logEvent(
      "workspace_documents_removed",
      {
        workspaceName: workspace?.name || "Unknown Workspace",
        numberOfDocuments: removals?.length,
      },
      userId
    );
    return true;
  },

  count: async function (
    clause: WhereClause = {},
    limit: number | null = null
  ): Promise<number> {
    try {
      const count = await prisma.workspace_documents.count({
        where: clause,
        ...(limit !== null ? { take: limit } : {}),
      });
      return count;
    } catch (error: unknown) {
      console.error(
        "FAILED TO COUNT DOCUMENTS.",
        error instanceof Error ? error.message : String(error)
      );
      return 0;
    }
  },

  update: async function (
    id: number | null = null,
    data: DocumentUpdateData = {}
  ): Promise<DocumentResult> {
    if (!id) throw new Error("No workspace document id provided for update");

    const validKeys = Object.keys(data).filter((key) =>
      this.writable.includes(key)
    );
    if (validKeys.length === 0)
      return { document: null, message: "No valid fields to update!" };

    try {
      // The Prisma schema for workspace_documents uses camelCase (workspaceId, pinned, pdr, watched)
      // No need to convert field names - use them as is
      const document = await prisma.workspace_documents.update({
        where: { id },
        data,
      });
      return { document, message: null };
    } catch (error: unknown) {
      console.error(
        `ERROR in Document?.update: ${error instanceof Error ? error.message : String(error)}`
      );
      return {
        document: null,
        message: error instanceof Error ? error.message : String(error),
      };
    }
  },

  _updateAll: async function (
    clause: WhereClause = {},
    data: DocumentUpdateData = {}
  ): Promise<boolean> {
    try {
      // The Prisma schema for workspace_documents uses camelCase (workspaceId, pinned, pdr, watched)
      // No need to convert field names - use them as is
      await prisma.workspace_documents.updateMany({
        where: clause,
        data,
      });
      return true;
    } catch (error: unknown) {
      console.error(
        `ERROR in Document?._updateAll: ${error instanceof Error ? error.message : String(error)}`
      );
      return false;
    }
  },

  updateMany: async function (
    workspaceId: number,
    data: DocumentUpdateData = {}
  ): Promise<DocumentUpdateManyResult> {
    if (!workspaceId) throw new Error("No workspaceId provided for update");

    const validKeys = Object.keys(data).filter((key) =>
      this.writable.includes(key)
    );
    if (validKeys.length === 0) {
      return { count: 0, message: "No valid fields to update!" };
    }

    try {
      const result = await prisma.workspace_documents.updateMany({
        where: { workspaceId },
        data,
      });
      return { count: result.count, message: null };
    } catch (error: unknown) {
      console.error(error instanceof Error ? error.message : String(error));
      return {
        count: 0,
        message: error instanceof Error ? error.message : String(error),
      };
    }
  },

  content: async function (docId: string): Promise<DocumentContentResult> {
    if (!docId) throw new Error("No workspace docId provided!");
    const document = await this.get({ docId: String(docId) });
    if (!document) throw new Error(`Could not find a document by id ${docId}`);

    const data = await fileData(document.docpath);
    return {
      title: String(data.title || ""),
      content: String(data.pageContent || ""),
    };
  },

  contents: async function (path: string): Promise<DocumentContentResult> {
    if (!path) throw new Error("No document url provided!");
    const document = await this.get({
      filename: { endsWith: path + ".json" },
    });
    if (!document) throw new Error(`Could not find a document by path ${path}`);

    const data = await fileData(document.docpath);
    return {
      title: String(data.title || ""),
      content: String(data.pageContent || ""),
    };
  },

  contentByDocPath: async function (
    docPath: string
  ): Promise<DocumentContentResult> {
    const data = await fileData(docPath);
    return {
      title: String(data.title || ""),
      content: String(data.pageContent || ""),
    };
  },

  // Some data sources have encoded params in them we don't want to log - so strip those details.
  _stripSource: function (sourceString: string, type: string): string {
    if (["confluence", "github"].includes(type)) {
      const _src = new URL(sourceString);
      _src.search = ""; // remove all search params that are encoded for resync.
      return _src.toString();
    }

    return sourceString;
  },

  updateDocpathAndMetadata: async function (
    oldPath: string,
    newPath: string
  ): Promise<UpdatePathResult> {
    try {
      oldPath = oldPath?.replace(/\\/g, "/").split("/").slice(-2).join("/");
      newPath = newPath?.replace(/\\/g, "/").split("/").slice(-2).join("/");

      const documentsToUpdate = await prisma.workspace_documents.findMany({
        where: {
          OR: [
            { docpath: { contains: oldPath } },
            { metadata: { contains: oldPath } },
          ],
        },
      });

      const updates = documentsToUpdate?.map((doc) => {
        const updatedDocpath = doc?.docpath.replace(oldPath, newPath);
        const updatedMetadata =
          doc?.metadata?.replace(oldPath, newPath) || doc?.metadata;
        return prisma.workspace_documents.update({
          where: { id: doc?.id },
          data: {
            docpath: updatedDocpath,
            metadata: updatedMetadata,
          },
        });
      });
      await Promise?.all(updates);

      return { updatedCount: updates?.length, message: null };
    } catch (error: unknown) {
      console.error(error instanceof Error ? error.message : String(error));
      return {
        updatedCount: 0,
        message: error instanceof Error ? error.message : String(error),
      };
    }
  },
};

export { Document };
