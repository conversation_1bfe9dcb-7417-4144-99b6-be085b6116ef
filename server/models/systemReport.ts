import slackNotifier from "../utils/notifications/slack";
import prisma from "../utils/prisma";
import autoCodingPromptGenerator from "../utils/helpers/autoCodingPromptGenerator";
import type { Prisma } from "@prisma/client";

// Type enums for better type safety
enum ReportType {
  INCIDENT = "INCIDENT",
  FEATURE_REQUEST = "FEATURE_REQUEST",
}

enum SeverityLevel {
  CRITICAL = "CRITICAL",
  HIGH = "HIGH",
  MEDIUM = "MEDIUM",
  LOW = "LOW",
}

enum ServiceCategory {
  AUTHENTICATION = "AUTHENTICATION",
  DOCUMENT_MANAGEMENT = "DOCUMENT_MANAGEMENT",
  CHAT_SYSTEM = "CHAT_SYSTEM",
  SEARCH = "SEARCH",
  ADMIN = "ADMIN",
  UI_UX = "UI_UX",
  OTHER = "OTHER",
}

enum ReportStatus {
  REPORTED = "REPORTED",
  CLASSIFIED = "CLASSIFIED",
  FIRST_LINE = "FIRST_LINE",
  ESCALATED_L2 = "ESCALATED_L2",
  ESCALATED_L3 = "ESCALATED_L3",
  IN_PROGRESS = "IN_PROGRESS",
  RESOLVED = "RESOLVED",
  CLOSED = "CLOSED",
}

interface CreateReportData {
  title: string;
  description: string;
  type?: ReportType;
  severity?: SeverityLevel;
  affected_service?: ServiceCategory;
  userId: number;
}

interface ReportFilters {
  type?: ReportType;
  status?: ReportStatus;
  severity?: SeverityLevel;
}

interface SystemReportUser {
  id: number;
  username: string;
}

interface SystemReportMessage {
  id: number;
  reportId: number;
  content: string;
  userId: number;
  createdAt: Date;
  users: SystemReportUser;
}

interface SystemReportEntity {
  id: number;
  title: string;
  description: string;
  type: ReportType;
  severity: SeverityLevel | null;
  affected_service: ServiceCategory | null;
  status: ReportStatus;
  userId: number;
  resolver_user_id: number | null;
  resolution_comment: string | null;
  resolution_confirmed: boolean;
  resolution_confirmed_at: Date | null;
  resolution_confirmed_by: number | null;
  createdAt: Date;
  updatedAt: Date;
  users: SystemReportUser;
  resolver_user?: SystemReportUser | null;
  resolution_confirmer?: SystemReportUser | null;
  system_report_messages?: SystemReportMessage[];
  _count?: {
    system_report_messages: number;
  };
}

interface ReportStatistics {
  stats: Array<{
    type: ReportType;
    status: ReportStatus;
    severity: SeverityLevel | null;
    _count: number;
  }>;
  totalIncidents: number;
  totalFeatureRequests: number;
  openIncidents: number;
  criticalIncidents: number;
}

interface UpdateIncidentDetailsParams {
  severity?: SeverityLevel;
  affected_service?: ServiceCategory;
}

interface UpdateTitleDescriptionParams {
  title?: string;
  description?: string;
}

interface AutoCodingPromptResult {
  success: boolean;
  prompt?: string;
  slack?: {
    autoCoding: {
      success: boolean;
      channel?: string;
      timestamp?: string;
      error?: string;
    };
  };
  error?: string;
}

const SystemReport = {
  // Valid incident types
  TYPES: ReportType,

  // Valid severity levels (for incidents)
  SEVERITIES: SeverityLevel,

  // Valid service categories
  SERVICES: ServiceCategory,

  // Valid status transitions
  STATUSES: ReportStatus,

  // Status transition rules
  ALLOWED_TRANSITIONS: {
    [ReportStatus.REPORTED]: [ReportStatus.CLASSIFIED, ReportStatus.RESOLVED], // Can resolve immediately
    [ReportStatus.CLASSIFIED]: [ReportStatus.FIRST_LINE, ReportStatus.RESOLVED], // Can resolve immediately
    [ReportStatus.FIRST_LINE]: [
      ReportStatus.ESCALATED_L2,
      ReportStatus.IN_PROGRESS,
      ReportStatus.RESOLVED,
    ],
    [ReportStatus.ESCALATED_L2]: [
      ReportStatus.ESCALATED_L3,
      ReportStatus.IN_PROGRESS,
      ReportStatus.RESOLVED,
    ],
    [ReportStatus.ESCALATED_L3]: [
      ReportStatus.IN_PROGRESS,
      ReportStatus.RESOLVED,
    ],
    [ReportStatus.IN_PROGRESS]: [ReportStatus.RESOLVED],
    [ReportStatus.RESOLVED]: [ReportStatus.CLOSED, ReportStatus.FIRST_LINE], // Can reopen
    [ReportStatus.CLOSED]: [ReportStatus.FIRST_LINE, ReportStatus.IN_PROGRESS], // Can reopen from closed
  },

  create: async function (
    reportData: CreateReportData
  ): Promise<SystemReportEntity | null> {
    try {
      const { title, description, type, severity, affected_service, userId } =
        reportData;

      // Validate required fields
      if (!title || !description || !userId) {
        throw new Error("Title, description, and userId are required");
      }

      // Validate type
      if (type && !Object.values(ReportType).includes(type)) {
        throw new Error("Invalid type");
      }

      // Validate severity for incidents
      if (
        type === ReportType.INCIDENT &&
        severity &&
        !Object.values(SeverityLevel).includes(severity)
      ) {
        throw new Error("Invalid severity");
      }

      // Validate affected service
      if (
        affected_service &&
        !Object.values(ServiceCategory).includes(affected_service)
      ) {
        throw new Error("Invalid affected service");
      }

      const report = await prisma.system_reports.create({
        data: {
          title,
          description,
          type: type || ReportType.INCIDENT,
          severity: type === ReportType.INCIDENT ? severity || null : null,
          affected_service:
            type === ReportType.INCIDENT ? affected_service || null : null,
          userId,
          updatedAt: new Date(),
        },
        include: {
          users: {
            select: {
              id: true,
              username: true,
            },
          },
          resolver_user: {
            select: {
              id: true,
              username: true,
            },
          },
        },
      });

      const typedReport = {
        ...report,
        type: report.type as ReportType,
        severity: report.severity as SeverityLevel | null,
        affected_service: report.affected_service as ServiceCategory | null,
        status: report.status as ReportStatus,
      };

      // Send Slack notification for new report (non-blocking)
      // Use async/await with proper error handling to avoid logging after test teardown
      if (process.env.NODE_ENV !== "test") {
        setImmediate(async () => {
          try {
            const reportForSlack = {
              ...typedReport,
              type: String(typedReport.type),
              severity: typedReport.severity
                ? String(typedReport.severity)
                : undefined,
              affected_service: typedReport.affected_service
                ? String(typedReport.affected_service)
                : undefined,
              status: String(typedReport.status),
              users: {
                ...typedReport.users,
                username: typedReport.users?.username ?? "",
              },
              resolver_user: typedReport.resolver_user
                ? {
                    ...typedReport.resolver_user,
                    username: typedReport.resolver_user.username ?? "",
                  }
                : undefined,
            };
            await slackNotifier.notifyNewReport(reportForSlack);
          } catch {
            console.error(
              "[SystemReport] Failed to send Slack notification for new report:"
            );
          }
        });
      }

      // Generate and post auto-coding prompt (non-blocking)
      // Use async/await with proper error handling to avoid logging after test teardown
      if (process.env.NODE_ENV !== "test") {
        setImmediate(async () => {
          try {
            await this.generateAutoCodingPrompt(
              typedReport as SystemReportEntity
            );
          } catch {
            console.error(
              "[SystemReport] Failed to generate auto-coding prompt:"
            );
          }
        });
      }

      return typedReport as SystemReportEntity;
    } catch {
      console.error("Error creating system report");
      return null;
    }
  },

  getAll: async function (
    filters: ReportFilters = {}
  ): Promise<SystemReportEntity[]> {
    try {
      const { type, status, severity } = filters;
      const where: Prisma.system_reportsWhereInput = {};

      if (type) where.type = type;
      if (status) where.status = status;
      if (severity) where.severity = severity;

      const reports = await prisma.system_reports.findMany({
        where,
        include: {
          users: {
            select: {
              id: true,
              username: true,
            },
          },
          resolver_user: {
            select: {
              id: true,
              username: true,
            },
          },
          _count: {
            select: {
              system_report_messages: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });
      return reports as SystemReportEntity[];
    } catch {
      console.error("Error fetching system reports");
      return [];
    }
  },

  getAllForUser: async function (
    userId: number,
    filters: ReportFilters = {}
  ): Promise<SystemReportEntity[]> {
    try {
      const { type, status, severity } = filters;
      const where: Prisma.system_reportsWhereInput = {
        userId: userId, // Filter by user ID
      };

      if (type) where.type = type;
      if (status) where.status = status;
      if (severity) where.severity = severity;

      const reports = await prisma.system_reports.findMany({
        where,
        include: {
          users: {
            select: {
              id: true,
              username: true,
            },
          },
          resolver_user: {
            select: {
              id: true,
              username: true,
            },
          },
          _count: {
            select: {
              system_report_messages: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });
      return reports as SystemReportEntity[];
    } catch {
      console.error("Error fetching user system reports");
      return [];
    }
  },

  getAllByType: async function (
    type: ReportType
  ): Promise<SystemReportEntity[]> {
    return this.getAll({ type });
  },

  get: async function (id: number): Promise<SystemReportEntity | null> {
    try {
      const report = await prisma.system_reports.findUnique({
        where: { id },
        include: {
          users: {
            select: {
              id: true,
              username: true,
            },
          },
          resolver_user: {
            select: {
              id: true,
              username: true,
            },
          },
          resolution_confirmer: {
            select: {
              id: true,
              username: true,
            },
          },
          system_report_messages: {
            include: {
              users: {
                select: {
                  id: true,
                  username: true,
                },
              },
            },
            orderBy: {
              createdAt: "asc",
            },
          },
        },
      });
      return report as SystemReportEntity | null;
    } catch {
      console.error(`Error fetching system report with id ${id}`);
      return null;
    }
  },

  getUserReportById: async function (
    id: number,
    userId: number
  ): Promise<SystemReportEntity | null> {
    try {
      const report = await prisma.system_reports.findFirst({
        where: {
          id: id,
          userId: userId, // Only return if user owns the report
        },
        include: {
          users: {
            select: {
              id: true,
              username: true,
            },
          },
          resolver_user: {
            select: {
              id: true,
              username: true,
            },
          },
          resolution_confirmer: {
            select: {
              id: true,
              username: true,
            },
          },
          system_report_messages: {
            include: {
              users: {
                select: {
                  id: true,
                  username: true,
                },
              },
            },
            orderBy: {
              createdAt: "asc",
            },
          },
        },
      });
      return report as SystemReportEntity | null;
    } catch {
      console.error(
        `Error fetching user system report with id ${id} for user ${userId}`
      );
      return null;
    }
  },

  updateStatus: async function (
    id: number,
    status: ReportStatus,
    resolverUserId: number | null = null
  ): Promise<SystemReportEntity | null> {
    try {
      // Get current report to validate transition
      const currentReport = await this.get(id);
      if (!currentReport) {
        throw new Error("Report not found");
      }

      // Validate status transition
      if (!this.isValidStatusTransition(currentReport.status, status)) {
        throw new Error(
          `Invalid status transition from ${currentReport.status} to ${status}`
        );
      }

      const updateData: Prisma.system_reportsUpdateInput = {
        status,
        updatedAt: new Date(),
      };

      // Set resolver if moving to first line or later
      if (
        resolverUserId &&
        (status === ReportStatus.FIRST_LINE ||
          status === ReportStatus.ESCALATED_L2 ||
          status === ReportStatus.ESCALATED_L3 ||
          status === ReportStatus.IN_PROGRESS)
      ) {
        updateData.resolver_user = { connect: { id: resolverUserId } };
      }

      const report = await prisma.system_reports.update({
        where: { id },
        data: updateData,
        include: {
          users: {
            select: {
              id: true,
              username: true,
            },
          },
          resolver_user: {
            select: {
              id: true,
              username: true,
            },
          },
        },
      });
      return report as SystemReportEntity;
    } catch {
      console.error(`Error updating status for report with id ${id}`);
      return null;
    }
  },

  isValidStatusTransition: function (
    currentStatus: ReportStatus,
    newStatus: ReportStatus
  ): boolean {
    const allowedTransitions = this.ALLOWED_TRANSITIONS[currentStatus] || [];
    return allowedTransitions.includes(newStatus);
  },

  assignResolver: async function (
    id: number,
    resolverUserId: number
  ): Promise<SystemReportEntity | null> {
    try {
      const report = await prisma.system_reports.update({
        where: { id },
        data: {
          resolver_user: { connect: { id: resolverUserId } },
          updatedAt: new Date(),
        },
        include: {
          resolver_user: {
            select: {
              id: true,
              username: true,
            },
          },
        },
      });
      return report as SystemReportEntity;
    } catch {
      console.error(`Error assigning resolver for report with id ${id}`);
      return null;
    }
  },

  addResolutionComment: async function (
    id: number,
    resolutionComment: string
  ): Promise<SystemReportEntity | null> {
    try {
      const report = await prisma.system_reports.update({
        where: { id },
        data: {
          resolution_comment: resolutionComment,
          updatedAt: new Date(),
        },
      });
      return report as SystemReportEntity;
    } catch {
      console.error(`Error adding resolution comment for report with id ${id}`);
      return null;
    }
  },

  updateIncidentDetails: async function (
    id: number,
    { severity, affected_service }: UpdateIncidentDetailsParams
  ): Promise<SystemReportEntity | null> {
    try {
      const updateData: Prisma.system_reportsUpdateInput = {
        updatedAt: new Date(),
      };

      if (severity && Object.values(SeverityLevel).includes(severity)) {
        updateData.severity = severity;
      }

      if (
        affected_service &&
        Object.values(ServiceCategory).includes(affected_service)
      ) {
        updateData.affected_service = affected_service;
      }

      const report = await prisma.system_reports.update({
        where: { id },
        data: updateData,
        include: {
          users: {
            select: {
              id: true,
              username: true,
            },
          },
          resolver_user: {
            select: {
              id: true,
              username: true,
            },
          },
        },
      });
      return report as SystemReportEntity;
    } catch {
      console.error(`Error updating incident details for report with id ${id}`);
      return null;
    }
  },

  getStatistics: async function (): Promise<ReportStatistics | null> {
    try {
      const stats = await prisma.system_reports.groupBy({
        by: ["type", "status", "severity"],
        _count: true,
      });

      const totalIncidents = await prisma.system_reports.count({
        where: { type: ReportType.INCIDENT },
      });

      const totalFeatureRequests = await prisma.system_reports.count({
        where: { type: ReportType.FEATURE_REQUEST },
      });

      const openIncidents = await prisma.system_reports.count({
        where: {
          type: ReportType.INCIDENT,
          status: { not: ReportStatus.CLOSED },
        },
      });

      const criticalIncidents = await prisma.system_reports.count({
        where: {
          type: ReportType.INCIDENT,
          severity: SeverityLevel.CRITICAL,
          status: { not: ReportStatus.CLOSED },
        },
      });

      return {
        stats: stats.map((stat) => ({
          type: stat.type as ReportType,
          status: stat.status as ReportStatus,
          severity: stat.severity as SeverityLevel,
          _count: stat._count,
        })),
        totalIncidents,
        totalFeatureRequests,
        openIncidents,
        criticalIncidents,
      };
    } catch {
      console.error("Error fetching statistics");
      return null;
    }
  },

  addMessage: async function (
    reportId: number,
    content: string,
    userId: number
  ): Promise<SystemReportMessage | null> {
    try {
      const message = await prisma.system_report_messages.create({
        data: {
          reportId,
          content,
          userId,
        },
        include: {
          users: {
            select: {
              id: true,
              username: true,
            },
          },
        },
      });

      // Update the report's updatedAt timestamp
      await prisma.system_reports.update({
        where: { id: reportId },
        data: { updatedAt: new Date() },
      });

      // Get the full report for Slack notification
      const report = await this.get(reportId);
      if (report) {
        // Send Slack notification for new message (non-blocking)
        // Use async/await with proper error handling to avoid logging after test teardown
        if (process.env.NODE_ENV !== "test") {
          setImmediate(async () => {
            try {
              const reportForSlack = {
                ...report,
                severity: report.severity ?? undefined,
                affected_service: report.affected_service
                  ? String(report.affected_service)
                  : undefined,
                users: {
                  ...report.users,
                  username: report.users?.username ?? "",
                },
                resolver_user: report.resolver_user
                  ? {
                      ...report.resolver_user,
                      username: report.resolver_user.username ?? "",
                    }
                  : undefined,
              };
              await slackNotifier.notifyNewMessage(reportForSlack, message, {
                ...message.users,
                username: message.users?.username ?? "",
              });
            } catch {
              console.error(
                "[SystemReport] Failed to send Slack notification for new message:"
              );
            }
          });
        }
      }

      return message as SystemReportMessage;
    } catch {
      console.error(`Error adding message to report with id ${reportId}`);
      return null;
    }
  },

  updateTitleAndDescription: async function (
    id: number,
    { title, description }: UpdateTitleDescriptionParams
  ): Promise<SystemReportEntity | null> {
    try {
      const updateData: Prisma.system_reportsUpdateInput = {
        updatedAt: new Date(),
      };

      if (title && title.trim()) {
        updateData.title = title.trim();
      }

      if (description && description.trim()) {
        updateData.description = description.trim();
      }

      const report = await prisma.system_reports.update({
        where: { id },
        data: updateData,
        include: {
          users: {
            select: {
              id: true,
              username: true,
            },
          },
          resolver_user: {
            select: {
              id: true,
              username: true,
            },
          },
        },
      });
      return report as SystemReportEntity;
    } catch {
      console.error(
        `Error updating title and description for report with id ${id}`
      );
      return null;
    }
  },

  delete: async function (id: number): Promise<SystemReportEntity | null> {
    try {
      // First delete all related messages
      await prisma.system_report_messages.deleteMany({
        where: { reportId: id },
      });

      // Then delete the report itself
      const deletedReport = await prisma.system_reports.delete({
        where: { id },
      });

      return deletedReport as SystemReportEntity;
    } catch {
      console.error(`Error deleting system report with id ${id}`);
      return null;
    }
  },

  /**
   * Generate and post auto-coding prompt for a system report
   */
  generateAutoCodingPrompt: async function (
    report: SystemReportEntity
  ): Promise<AutoCodingPromptResult> {
    try {
      console.log(
        `[SystemReport] Generating auto-coding prompt for report #${report.id}`
      );

      // Generate the prompt - no filtering, always generate for any report
      const generatedPrompt = await autoCodingPromptGenerator.generatePrompt({
        ...report,
        affected_service: String(report.affected_service ?? ""),
        severity: String(report.severity ?? ""),
        // Do not add users or resolver_user if not in SystemReport type
      });

      if (!generatedPrompt) {
        throw new Error("Failed to generate auto-coding prompt");
      }

      // Send auto-coding prompt to autocoding webhook URL
      // Note: Original bug report notification is handled separately in the create() method
      const reportForSlack = {
        ...report,
        severity: String(report.severity ?? ""),
        affected_service: report.affected_service
          ? String(report.affected_service)
          : undefined,
        // Do not add users or resolver_user if not in SystemReport type
      };
      const autoCodingSlackResult = await slackNotifier.postAutoCodingPrompt(
        reportForSlack,
        generatedPrompt
      );

      if (autoCodingSlackResult.success) {
        console.log(
          `[SystemReport] Auto-coding prompt posted to autocoding webhook for report #${report.id}`
        );
      }

      return {
        success: true,
        prompt: generatedPrompt,
        slack: {
          autoCoding: autoCodingSlackResult,
        },
      };
    } catch (error) {
      console.error(
        `[SystemReport] Error generating auto-coding prompt for report #${report.id}:`,
        error instanceof Error ? error.message : String(error)
      );
      return {
        success: false,
        error:
          error instanceof Error
            ? error instanceof Error
              ? error.message
              : String(error)
            : "Unknown error",
      };
    }
  },

  confirmResolution: async function (
    reportId: number,
    confirmingUserId: number
  ): Promise<SystemReportEntity | null> {
    try {
      const updated = await prisma.system_reports.update({
        where: { id: reportId },
        data: {
          resolution_confirmed: true,
          resolution_confirmed_at: new Date(),
          resolution_confirmer: { connect: { id: confirmingUserId } },
          updatedAt: new Date(),
        },
        include: {
          users: {
            select: {
              id: true,
              username: true,
            },
          },
          resolver_user: {
            select: {
              id: true,
              username: true,
            },
          },
          resolution_confirmer: {
            select: {
              id: true,
              username: true,
            },
          },
        },
      });
      return updated as SystemReportEntity;
    } catch (error) {
      console.error(
        `Error confirming resolution for report ${reportId}:`,
        error instanceof Error ? error.message : String(error)
      );
      return null;
    }
  },

  rejectResolution: async function (
    reportId: number,
    _rejectingUserId: number
  ): Promise<SystemReportEntity | null> {
    try {
      const updated = await prisma.system_reports.update({
        where: { id: reportId },
        data: {
          status: ReportStatus.REPORTED,
          resolver_user: { disconnect: true },
          resolution_comment: null,
          resolution_confirmed: false,
          resolution_confirmed_at: null,
          resolution_confirmer: { disconnect: true },
          updatedAt: new Date(),
        },
        include: {
          users: {
            select: {
              id: true,
              username: true,
            },
          },
          resolver_user: {
            select: {
              id: true,
              username: true,
            },
          },
          resolution_confirmer: {
            select: {
              id: true,
              username: true,
            },
          },
        },
      });
      return updated as SystemReportEntity;
    } catch (error) {
      console.error(
        `Error rejecting resolution for report ${reportId}:`,
        error instanceof Error ? error.message : String(error)
      );
      return null;
    }
  },
};

export {
  SystemReport,
  type CreateReportData,
  type ReportFilters,
  type SystemReportEntity,
  type SystemReportMessage,
  type ReportStatistics,
  type UpdateIncidentDetailsParams,
  type UpdateTitleDescriptionParams,
  type AutoCodingPromptResult,
  ReportType,
  SeverityLevel,
  ServiceCategory,
  ReportStatus,
};
