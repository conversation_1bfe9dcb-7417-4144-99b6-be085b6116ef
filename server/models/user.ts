import prisma from "../utils/prisma";

// Import TypeScript utility types
// TypeScript utility types are built-in
import { EventLogs } from "./eventLogs";

// Import TypeScript utility types
// TypeScript utility types are built-in
import { UserToken } from "./userToken";

// Import TypeScript utility types
// TypeScript utility types are built-in
import { Organization } from "./organization";
import type { Organization as PrismaOrganization } from "@prisma/client";

// Import TypeScript utility types
// TypeScript utility types are built-in
import {
  FilteredUser,
  UserCreateParams,
  UserUpdateParams,
  PasswordCheckResult,
  ModelResponse,
  WhereClause,
} from "../types/models";
import { users as PrismaUser, Prisma, UserStyleProfile } from "@prisma/client";

// Import TypeScript utility types
// TypeScript utility types are built-in
import * as bcrypt from "bcryptjs";

// Import TypeScript utility types
// TypeScript utility types are built-in
import passwordComplexity from "joi-password-complexity";

// Type definition for joi-password-complexity validator
interface PasswordValidator {
  validate(password: string): {
    error?: {
      details: Array<{ message: string }>;
    };
    value?: string;
  };
}

// Import TypeScript utility types
// TypeScript utility types are built-in
import { Workspace } from "./workspace";

const User = {
  usernameRegex: new RegExp(/^[a-z0-9_.@-]+$/),
  writable: [
    // Used for generic updates so we can validate keys in request body
    "username",
    "password",
    "pfpFilename",
    "role",
    "suspended",
    "custom_ai_userselected",
    "custom_ai_option",
    "custom_ai_selected_engine",
    "custom_system_prompt",
    "economy_system_id",
    "organizationId",
  ],
  validations: {
    username: (newValue: unknown = ""): string => {
      try {
        const username = String(newValue);

        // Check length constraints first
        if (username.length < 2)
          throw new Error("Username must be at least 2 characters");
        if (username.length > 100)
          throw new Error("Username cannot be longer than 100 characters");

        // Check against regex pattern for security - this will reject SQL injection, XSS, path traversal, etc.
        const usernameRegex = /^[a-z0-9_.@-]+$/;
        if (!usernameRegex.test(username))
          throw new Error(
            "Username can only include lowercase letters, numbers, underscores (_), dots (.), @ symbols, and hyphens (-)."
          );

        return username;
      } catch (e) {
        throw new Error(e instanceof Error ? e.message : String(e));
      }
    },
    role: (role: unknown = "default"): string => {
      const VALID_ROLES = ["default", "admin", "manager", "superuser"];

      // Handle null, undefined, or empty string cases
      if (role === null || role === undefined || role === "") {
        return "default";
      }

      const roleString = String(role);
      if (!VALID_ROLES.includes(roleString)) {
        throw new Error(
          `Invalid role. Allowed roles are: ${VALID_ROLES.join(", ")}`
        );
      }
      return roleString;
    },
    custom_ai_userselected: (value: unknown): boolean => {
      return Boolean(value);
    },
    custom_ai_option: (value: unknown = 1): number => {
      const option = Number(value);
      if (isNaN(option) || option < 1 || option > 3) {
        return 1; // Default to option 1 if invalid
      }
      return option;
    },
    custom_ai_selected_engine: (value: unknown = "_CUAI"): string => {
      // Validate that the value is a string and has a reasonable length
      if (typeof value !== "string") {
        return "_CUAI"; // Default value if not a string
      }

      // Trim the value to prevent excessively long strings
      const trimmed = String(value).trim().substring(0, 50);

      // If empty after trimming, return default
      return trimmed || "_CUAI";
    },
    custom_system_prompt: (value: unknown): string | null => {
      if (value === null || value === undefined) return null;
      const str = String(value).trim();
      if (str.length === 0) return null;
      if (str.length > 10000) {
        throw new Error("Custom system prompt cannot exceed 10,000 characters");
      }
      return str;
    },
    organizationId: (value: unknown): number | null => {
      if (value === null || value === undefined) return null;
      const id = parseInt(String(value));
      if (isNaN(id) || id <= 0) {
        // Allow null or handle invalid ID case - here we allow null
        return null;
      }
      return id;
    },
  },

  // validations for the above writable fields.
  castColumnValue: function (
    key: string,
    value: unknown
  ): string | number | null {
    switch (key) {
      case "suspended":
      case "custom_ai_userselected":
        return Number(Boolean(value));
      case "custom_ai_option":
        return Number(value) || 1;
      case "organizationId": {
        const parsedId = parseInt(String(value));
        return isNaN(parsedId) ? null : parsedId;
      }
      default:
        return String(value);
    }
  },

  filterFields: function (
    user: PrismaUser & { organization?: PrismaOrganization | null }
  ): FilteredUser {
    const { password: _password, ...rest } = user;
    // When spreading 'rest', ensure organization is undefined if null
    const { organization, ...restWithoutOrg } = rest;
    const filtered: FilteredUser = {
      ...restWithoutOrg,
      organization: organization === null ? undefined : organization,
      custom_ai_userselected: Boolean(rest.custom_ai_userselected),
      username: rest.username === null ? undefined : rest.username,
    };

    // Only add seen_recovery_codes if it exists in the source object
    if ("seen_recovery_codes" in rest) {
      filtered.seen_recovery_codes =
        rest.seen_recovery_codes !== null
          ? Boolean(rest.seen_recovery_codes)
          : null;
    }

    return filtered as FilteredUser;
  },

  create: async function ({
    username,
    password,
    role = "default",
    economy_system_id,
    organizationId = null,
    newOrganizationName = null,
  }: UserCreateParams): Promise<ModelResponse<FilteredUser>> {
    const passwordCheck = this.checkPasswordComplexity(password);
    if (!passwordCheck.checkedOK) {
      return { user: undefined, error: passwordCheck.error };
    }

    try {
      // Username validation is handled by this.validations.username() below

      let finalOrganizationId = organizationId;

      // If a new organization name is provided, create it first
      if (newOrganizationName) {
        const existingOrg = await Organization.get({
          name: newOrganizationName,
        });
        if (existingOrg) {
          console.warn(
            `Organization "${newOrganizationName}" already exists. Using existing ID.`
          );
          finalOrganizationId = existingOrg.id;
        } else {
          const { organization: newOrg, error: orgError } =
            await Organization.create(newOrganizationName);
          if (orgError || !newOrg) {
            return {
              user: undefined,
              error: orgError || "Failed to create new organization.",
            };
          }
          finalOrganizationId = newOrg.id;
        }
      }

      const hashedPassword = bcrypt.hashSync(password, 10);
      const user = await prisma.users.create({
        data: {
          username: this.validations.username(username),
          password: hashedPassword,
          role: this.validations.role(role),
          economy_system_id: economy_system_id || null,
          organizationId: finalOrganizationId,
        },
        include: {
          organization: true,
        },
      });

      if (role === "default") {
        // TODO: Implement workspace creation for new users
        // await Workspace.addStandardUser(user.id);
      }

      return { user: this.filterFields(user), error: null };
    } catch (error: unknown) {
      console.error(
        "FAILED TO CREATE USER.",
        error instanceof Error ? error.message : String(error)
      );
      if (
        (error as { code?: string }).code === "P2002" &&
        (
          (error as { meta?: { target?: string[] } })?.meta?.target ?? []
        )?.includes("name")
      ) {
        return {
          user: undefined,
          error: `Organization with name "${newOrganizationName}" already exists.`,
        };
      }
      return {
        user: undefined,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },

  // Log the changes to a user object, but omit sensitive fields
  // that are not meant to be logged.
  loggedChanges: function (
    updates: Record<string, unknown>,
    prev: Record<string, unknown> = {}
  ): Record<string, string> {
    const changes: Record<string, string> = {};
    const sensitiveFields = ["password"];

    Object.keys(updates).forEach((key) => {
      if (!sensitiveFields.includes(key) && updates[key] !== prev[key]) {
        changes[key] = `${prev[key]} => ${updates[key]}`;
      }
    });

    return changes;
  },

  update: async function (
    userId: number | string,
    updates: UserUpdateParams = {}
  ): Promise<ModelResponse<FilteredUser>> {
    try {
      if (!userId) throw new Error("No user id provided for update");
      const currentUser = await prisma.users.findUnique({
        where: { id: parseInt(String(userId)) },
      });
      if (!currentUser) return { success: false, error: "User not found" };

      const finalUpdates: UserUpdateParams = { ...updates };
      const newOrganizationName = finalUpdates.newOrganizationName;
      delete finalUpdates.newOrganizationName;

      let finalOrganizationId = finalUpdates.organizationId;

      if (newOrganizationName) {
        const existingOrg = await Organization.get({
          name: newOrganizationName,
        });
        if (existingOrg) {
          console.warn(
            `Organization "${newOrganizationName}" already exists. Using existing ID.`
          );
          finalOrganizationId = existingOrg.id;
        } else {
          const { organization: newOrg, error: orgError } =
            await Organization.create(newOrganizationName);
          if (orgError || !newOrg) {
            return {
              success: false,
              error:
                orgError || "Failed to create new organization during update.",
            };
          }
          finalOrganizationId = newOrg.id;
        }
        finalUpdates.organizationId = finalOrganizationId;
      } else if (
        Object.prototype.hasOwnProperty.call(finalUpdates, "organizationId")
      ) {
        if (
          finalUpdates.organizationId === "none" ||
          finalUpdates.organizationId === null ||
          finalUpdates.organizationId === undefined
        ) {
          finalUpdates.organizationId = null;
        } else {
          const parsedId = parseInt(String(finalUpdates.organizationId));
          finalUpdates.organizationId = isNaN(parsedId) ? null : parsedId;
        }
      } else {
        // Always ensure organizationId is explicitly set to null when not provided
        finalUpdates.organizationId = null;
      }

      Object.entries(finalUpdates).forEach(([key, value]) => {
        if (this.writable.includes(key)) {
          if (key in this.validations) {
            const validationFn =
              this.validations[key as keyof typeof this.validations];
            if (validationFn) {
              finalUpdates[key] = validationFn(
                this.castColumnValue(key, value)
              );
            }
          } else {
            finalUpdates[key] = this.castColumnValue(key, value);
          }
          return;
        }
        delete finalUpdates[key];
      });

      if (Object.keys(finalUpdates).length === 0 && !newOrganizationName)
        return { success: false, error: "No valid updates applied." };

      if ("password" in finalUpdates) {
        const passwordCheck = this.checkPasswordComplexity(
          finalUpdates.password
        );
        if (!passwordCheck.checkedOK) {
          return { success: false, error: passwordCheck.error };
        }
        finalUpdates.password = bcrypt.hashSync(
          String(finalUpdates.password),
          10
        );
      }

      if (
        "username" in finalUpdates &&
        currentUser.username !== finalUpdates.username &&
        !this.usernameRegex.test(String(finalUpdates.username))
      )
        return {
          success: false,
          error:
            "Username can only include lowercase letters, numbers, underscores (_), dots (.), @ symbols, and hyphens (-).",
        };

      const user = await prisma.users.update({
        where: { id: parseInt(String(userId)) },
        data: finalUpdates as Prisma.usersUpdateInput,
        include: {
          organization: true,
        },
      });

      await EventLogs.logEvent(
        "user_updated",
        {
          username: user.username,
          changes: this.loggedChanges(finalUpdates, currentUser),
        },
        typeof userId === "string" ? parseInt(userId) : userId
      );
      return { success: true, error: null, user: this.filterFields(user) };
    } catch (error: unknown) {
      console.error(error instanceof Error ? error.message : String(error));
      if (
        (error as { code?: string }).code === "P2002" &&
        (
          (error as { meta?: { target?: string[] } })?.meta?.target ?? []
        )?.includes("name") &&
        updates.newOrganizationName
      ) {
        return {
          success: false,
          error: `Organization with name "${updates.newOrganizationName}" already exists.`,
        };
      }
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },

  // Explicit direct update of user object.
  // Only use this method when directly setting a key value
  // that takes no user input for the keys being modified.
  _update: async function (
    id: number | null = null,
    data: Partial<PrismaUser> = {}
  ): Promise<{ user: PrismaUser | null; message: string | null }> {
    if (!id) throw new Error("No user id provided for update");

    try {
      const user = await prisma.users.update({
        where: { id },
        data,
      });
      return { user, message: null };
    } catch (error: unknown) {
      console.error(error instanceof Error ? error.message : String(error));
      return {
        user: null,
        message: error instanceof Error ? error.message : String(error),
      };
    }
  },

  get: async function (clause: WhereClause = {}): Promise<FilteredUser | null> {
    try {
      // If clause is empty or doesn't have any valid properties, return null
      if (!clause || Object.keys(clause).length === 0) {
        return null;
      }

      // Simple ID validation - convert string IDs to numbers
      if (clause.id !== undefined) {
        if (clause.id === null || clause.id === undefined) {
          return null;
        }

        if (typeof clause.id === "string") {
          // Use Number() instead of parseInt() to properly handle malicious inputs
          // Number() will return NaN for strings that contain any non-numeric characters
          const parsedId = Number(clause.id);
          clause.id = parsedId;
        }
      }

      const user = await prisma.users.findFirst({
        where: clause as Prisma.usersWhereInput,
      });
      return user ? this.filterFields({ ...user }) : null;
    } catch (error) {
      console.error(
        "Error in User.get:",
        error instanceof Error ? error.message : String(error)
      );
      return null;
    }
  },

  // Returns user object with all fields
  _get: async function (
    clause: WhereClause = {}
  ): Promise<
    (PrismaUser & { organization?: PrismaOrganization | null }) | null
  > {
    try {
      // Simple ID validation - convert string IDs to numbers
      if (clause.id !== undefined && typeof clause.id === "string") {
        const parsedId = parseInt(clause.id);
        if (isNaN(parsedId)) {
          return null;
        }
        clause.id = parsedId;
      }

      const user = await prisma.users.findFirst({
        where: clause as Prisma.usersWhereInput,
        include: { organization: true },
      });
      return user ? { ...user } : null;
    } catch (error) {
      console.error(
        "Error in User._get:",
        error instanceof Error ? error.message : String(error)
      );
      return null;
    }
  },

  count: async function (clause: WhereClause = {}): Promise<number> {
    try {
      const count = await prisma.users.count({
        where: clause as Prisma.usersWhereInput,
      });
      return count;
    } catch (error: unknown) {
      console.error(error instanceof Error ? error.message : String(error));
      return 0;
    }
  },

  delete: async function (clause: WhereClause = {}): Promise<boolean> {
    try {
      const usersToDelete = await prisma.users.findMany({
        where: clause as Prisma.usersWhereInput,
      });

      if (usersToDelete.length === 0) {
        return true;
      }

      for (const user of usersToDelete) {
        const workspaces = await Workspace.where({ user_id: user.id });

        for (const workspace of workspaces) {
          await Workspace.delete({ slug: workspace.slug });
        }

        await UserToken.deleteAllUserTokens(user.id);
        await prisma.users.delete({ where: { id: user.id } });
      }

      return true;
    } catch (error) {
      console.error("Error deleting user and associated data:", error);
      return false;
    }
  },

  where: async function (
    clause: WhereClause = {},
    limit: number | null = null,
    offset: number | null = null
  ): Promise<{ users: FilteredUser[]; total: number }> {
    try {
      const { users, total } = await prisma.$transaction(async (tx) => {
        const users = await tx.users.findMany({
          where: clause as Prisma.usersWhereInput,
          ...(limit !== null ? { take: limit } : {}),
          ...(offset !== null ? { skip: offset } : {}),
          orderBy: { createdAt: "desc" },
          include: { organization: true },
        });
        const total = await tx.users.count({
          where: clause as Prisma.usersWhereInput,
        });
        return { users, total };
      });

      return { users: users.map((user) => this.filterFields(user)), total };
    } catch (error: unknown) {
      console.error(error instanceof Error ? error.message : String(error));
      return { users: [], total: 0 };
    }
  },

  checkPasswordComplexity: function (
    passwordInput: string = ""
  ): PasswordCheckResult {
    // Can be set via ENV variable on boot. No frontend config at this time.
    // Docs: https://www.npmjs.com/package/joi-password-complexity
    const complexityOptions = {
      min: Number(process.env.PASSWORDMINCHAR) || 8,
      max: Number(process.env.PASSWORDMAXCHAR) || 250,
      lowerCase: Number(process.env.PASSWORDLOWERCASE) || 0,
      upperCase: Number(process.env.PASSWORDUPPERCASE) || 0,
      numeric: Number(process.env.PASSWORDNUMERIC) || 0,
      symbol: Number(process.env.PASSWORDSYMBOL) || 0,
      // reqCount should be equal to how many conditions you are testing for (1-4)
      requirementCount: Number(process.env.PASSWORDREQUIREMENTS) || 0,
    };

    const validator = passwordComplexity(
      complexityOptions,
      "password"
    ) as unknown as PasswordValidator;
    const complexityCheck = validator.validate(passwordInput);
    if ("error" in complexityCheck) {
      let myError = "";
      let prepend = "";
      for (let i = 0; i < complexityCheck.error!.details.length; i++) {
        myError += prepend + complexityCheck.error!.details[i].message;
        prepend = ", ";
      }
      return { checkedOK: false, error: myError };
    }

    return { checkedOK: true, error: "No error." };
  },

  getWithOrg: async function (
    clause: WhereClause = {}
  ): Promise<FilteredUser | null> {
    try {
      // Simple ID validation - convert string IDs to numbers
      if (clause.id !== undefined && typeof clause.id === "string") {
        const parsedId = parseInt(clause.id);
        if (isNaN(parsedId)) {
          return null;
        }
        clause.id = parsedId;
      }

      const user = await prisma.users.findFirst({
        where: clause as Prisma.usersWhereInput,
        include: { organization: true },
      });
      return user ? this.filterFields({ ...user }) : null;
    } catch (error) {
      console.error(
        "Error in User.getWithOrg:",
        error instanceof Error ? error.message : String(error)
      );
      return null;
    }
  },

  // Get user with style preferences for chat system
  getWithStylePreferences: async function (
    userId: number | string
  ): Promise<FilteredUser | null> {
    try {
      if (!userId) {
        return null;
      }

      const user = await prisma.users.findFirst({
        where: { id: parseInt(String(userId)) },
        include: {
          userStyleProfile: {
            where: { is_active: true },
            take: 1,
          },
        },
      });

      if (!user) {
        return null;
      }

      // Add style alignment properties to user object
      const userWithStyle = this.filterFields({ ...user });
      userWithStyle.styleAlignmentEnabled = user?.userStyleProfile?.length > 0;
      userWithStyle.activeStyleProfile = user.userStyleProfile[0] || null;

      return userWithStyle;
    } catch (error) {
      console.error("Error fetching user with style preferences:", error);
      return null;
    }
  },

  // Check if user has style alignment enabled
  hasStyleAlignment: async function (
    userId: number | string
  ): Promise<boolean> {
    try {
      if (!userId) {
        return false;
      }

      const activeProfile = await prisma.userStyleProfile.findFirst({
        where: {
          user_id: parseInt(String(userId)),
          is_active: true,
        },
      });

      return !!activeProfile;
    } catch (error) {
      console.error("Error checking style alignment:", error);
      return false;
    }
  },

  // Get active style profile for a user
  getActiveStyleProfile: async function (
    userId: number | string
  ): Promise<UserStyleProfile | null> {
    try {
      if (!userId) {
        return null;
      }

      const profile = await prisma.userStyleProfile.findFirst({
        where: {
          user_id: parseInt(String(userId)),
          is_active: true,
        },
      });

      return profile;
    } catch (error) {
      console.error("Error fetching active style profile:", error);
      return null;
    }
  },
};

export { User };
