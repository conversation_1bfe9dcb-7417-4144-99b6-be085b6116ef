import prisma from "../utils/prisma";
import { UserToken } from "./userToken";
import { Organization } from "./organization";
import type { Organization as PrismaOrganization } from "@prisma/client";
import {
  FilteredUser,
  UserCreateParams,
  UserUpdateParams,
  PasswordCheckResult,
  ModelResponse,
  WhereClause,
} from "../types/models";
import { users as PrismaU<PERSON>, Prisma, UserStyleProfile } from "@prisma/client";
import * as bcrypt from "bcryptjs";
import passwordComplexity from "joi-password-complexity";
import { Workspace } from "./workspace";

interface PasswordValidator {
  validate(password: string): {
    error?: {
      details: Array<{ message: string }>;
    };
    value?: string;
  };
}

class User {
  static usernameRegex = new RegExp(/^[a-z0-9_.@-]+$/);
  static writable = [
    "username",
    "password",
    "pfpFilename",
    "role",
    "suspended",
    "custom_ai_userselected",
    "custom_ai_option",
    "custom_ai_selected_engine",
    "custom_system_prompt",
    "economy_system_id",
    "organizationId",
  ];

  static username(newValue: unknown = ""): string {
    if (newValue === null || newValue === undefined) {
      throw new Error("Username must be at least 2 characters");
    }
    try {
      const username = String(newValue);
      if (username.length < 2)
        throw new Error("Username must be at least 2 characters");
      if (username.length > 100)
        throw new Error("Username cannot be longer than 100 characters");
      const usernameRegex = /^[a-z0-9_.@-]+$/;
      if (!usernameRegex.test(username))
        throw new Error(
          "Username can only include lowercase letters, numbers, underscores (_), dots (.), @ symbols, and hyphens (-)."
        );
      return username;
    } catch (e) {
      throw new Error(e instanceof Error ? e.message : String(e));
    }
  }

  static role(role: unknown = "default"): string {
    const VALID_ROLES = ["default", "admin", "manager", "superuser"];
    if (role === null || role === undefined || role === "") {
      return "default";
    }
    const roleString = String(role);
    if (!VALID_ROLES.includes(roleString)) {
      throw new Error(
        `Invalid role. Allowed roles are: ${VALID_ROLES.join(", ")}`
      );
    }
    return roleString;
  }

  static custom_ai_userselected(value: unknown): boolean {
    return Boolean(value);
  }

  static custom_ai_option(value: unknown = 1): number {
    const option = Number(value);
    if (isNaN(option) || option < 1 || option > 3) {
      return 1;
    }
    return option;
  }

  static custom_ai_selected_engine(value: unknown = "_CUAI"): string {
    if (typeof value !== "string") {
      return "_CUAI";
    }
    const trimmed = String(value).trim().substring(0, 50);
    return trimmed || "_CUAI";
  }

  static custom_system_prompt(value: unknown): string | null {
    if (value === null || value === undefined) return null;
    const str = String(value).trim();
    if (str.length === 0) return null;
    if (str.length > 10000) {
      throw new Error("Custom system prompt cannot exceed 10,000 characters");
    }
    return str;
  }

  static organizationId(value: unknown): number | null {
    if (value === null || value === undefined) return null;
    const id = parseInt(String(value));
    if (isNaN(id) || id <= 0) {
      return null;
    }
    return id;
  }

  static castColumnValue(key: string, value: unknown): string | number | null {
    switch (key) {
      case "suspended":
      case "custom_ai_userselected":
        return Number(Boolean(value));
      case "custom_ai_option":
        return Number(value) || 1;
      case "organizationId": {
        const parsedId = parseInt(String(value));
        return isNaN(parsedId) ? null : parsedId;
      }
      default:
        return String(value);
    }
  }

  static filterFields(
    user: PrismaUser & { organization?: PrismaOrganization | null }
  ): FilteredUser {
    const { password: _password, ...rest } = user;
    const { organization, ...restWithoutOrg } = rest;
    const filtered: FilteredUser = {
      ...restWithoutOrg,
      organization: organization === null ? undefined : organization,
      custom_ai_userselected: Boolean(rest.custom_ai_userselected),
      username: rest.username === null ? undefined : rest.username,
    };
    if ("seen_recovery_codes" in rest) {
      filtered.seen_recovery_codes =
        rest.seen_recovery_codes !== null
          ? Boolean(rest.seen_recovery_codes)
          : null;
    }
    return filtered as FilteredUser;
  }

  static getValidationFunction(
    key: string
  ): ((value: unknown) => any) | undefined {
    switch (key) {
      case "username":
        return User.username;
      case "role":
        return User.role;
      case "custom_ai_userselected":
        return User.custom_ai_userselected;
      case "custom_ai_option":
        return User.custom_ai_option;
      case "custom_ai_selected_engine":
        return User.custom_ai_selected_engine;
      case "custom_system_prompt":
        return User.custom_system_prompt;
      case "organizationId":
        return User.organizationId;
      default:
        return undefined;
    }
  }

  static async create({
    username,
    password,
    role = "default",
    economy_system_id,
    organizationId = null,
    newOrganizationName = null,
  }: UserCreateParams): Promise<ModelResponse<FilteredUser>> {
    const passwordCheck = User.checkPasswordComplexity(password);
    if (!passwordCheck.checkedOK) {
      return { user: undefined, error: passwordCheck.error };
    }
    try {
      let finalOrganizationId = organizationId;
      if (newOrganizationName) {
        const existingOrg = await Organization.get({
          name: newOrganizationName,
        });
        if (existingOrg) {
          console.warn(
            `Organization "${newOrganizationName}" already exists. Using existing ID.`
          );
          finalOrganizationId = existingOrg.id;
        } else {
          const { organization: newOrg, error: orgError } =
            await Organization.create(newOrganizationName);
          if (orgError || !newOrg) {
            return {
              user: undefined,
              error: orgError || "Failed to create new organization.",
            };
          }
          finalOrganizationId = newOrg.id;
        }
      }
      const hashedPassword = bcrypt.hashSync(password, 10);
      const user = await prisma.users.create({
        data: {
          username: User.username(username),
          password: hashedPassword,
          role: User.role(role),
          economy_system_id: economy_system_id || null,
          organizationId: finalOrganizationId,
        },
        include: { organization: true },
      });
      if (role === "default") {
        // TODO: Implement workspace creation for new users
        // await Workspace.addStandardUser(user.id);
      }
      return { user: User.filterFields(user), error: null };
    } catch (error: unknown) {
      console.error(
        "FAILED TO CREATE USER.",
        error instanceof Error ? error.message : String(error)
      );
      if (
        (error as { code?: string }).code === "P2002" &&
        (
          (error as { meta?: { target?: string[] } })?.meta?.target ?? []
        )?.includes("name")
      ) {
        return {
          user: undefined,
          error: `Organization with name "${newOrganizationName}" already exists.`,
        };
      }
      return {
        user: undefined,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  static loggedChanges(
    updates: Record<string, unknown>,
    prev: Record<string, unknown> = {}
  ): Record<string, string> {
    const changes: Record<string, string> = {};
    const sensitiveFields = ["password"];
    Object.keys(updates).forEach((key) => {
      if (!sensitiveFields.includes(key) && updates[key] !== prev[key]) {
        changes[key] = `${prev[key]} => ${updates[key]}`;
      }
    });
    return changes;
  }

  static async update(
    userId: number | string,
    updates: UserUpdateParams = {}
  ): Promise<ModelResponse<FilteredUser> & { success: boolean }> {
    try {
      if (!userId)
        return { success: false, error: "No user id provided for update" };
      const currentUser = await prisma.users.findUnique({
        where: { id: parseInt(String(userId)) },
      });
      if (!currentUser) return { success: false, error: "User not found" };
      const finalUpdates: UserUpdateParams = { ...updates };
      let finalOrganizationId = finalUpdates.organizationId;
      if (finalUpdates.newOrganizationName) {
        const existingOrg = await Organization.get({
          name: finalUpdates.newOrganizationName,
        });
        if (existingOrg) {
          finalOrganizationId = existingOrg.id;
        } else {
          const { organization: newOrg, error: orgError } =
            await Organization.create(finalUpdates.newOrganizationName);
          if (orgError || !newOrg) {
            return {
              success: false,
              error: orgError || "Failed to create new organization.",
            };
          }
          finalOrganizationId = newOrg.id;
        }
      }
      // Ensure finalOrganizationId is only number or null
      if (
        typeof finalOrganizationId !== "number" ||
        isNaN(finalOrganizationId) ||
        finalOrganizationId <= 0
      ) {
        finalOrganizationId = null;
      }
      // Convert boolean 'suspended' to number if present
      if (typeof finalUpdates.suspended === "boolean") {
        finalUpdates.suspended = finalUpdates.suspended ? 1 : 0;
      }
      if (
        typeof finalUpdates.suspended !== "number" &&
        typeof finalUpdates.suspended !== "undefined"
      ) {
        delete finalUpdates.suspended;
      }
      // Prepare update data, only include suspended if it is a number
      const updateData: any = {
        ...finalUpdates,
        organizationId: finalOrganizationId,
      };
      if (typeof finalUpdates.suspended !== "number") {
        delete updateData.suspended;
      }
      if (finalUpdates.password) {
        const passwordCheck = User.checkPasswordComplexity(
          finalUpdates.password
        );
        if (!passwordCheck.checkedOK) {
          return { success: false, error: passwordCheck.error };
        }
        finalUpdates.password = bcrypt.hashSync(finalUpdates.password, 10);
      }
      const updatedUser = await prisma.users.update({
        where: { id: parseInt(String(userId)) },
        data: updateData,
        include: { organization: true },
      });
      return {
        user: User.filterFields(updatedUser),
        error: null,
        success: true,
      };
    } catch (error: unknown) {
      console.error(
        "FAILED TO UPDATE USER.",
        error instanceof Error ? error.message : String(error)
      );
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  static async _update(
    id: number | null = null,
    data: Partial<PrismaUser> = {}
  ): Promise<{ user: PrismaUser | null; message: string | null }> {
    try {
      if (!id) throw new Error("No user id provided for update");
      const updatedUser = await prisma.users.update({ where: { id }, data });
      return { user: updatedUser, message: null };
    } catch (error: unknown) {
      return {
        user: null,
        message: error instanceof Error ? error.message : String(error),
      };
    }
  }

  static async get(clause: WhereClause = {}): Promise<FilteredUser | null> {
    try {
      if (!clause || Object.keys(clause).length === 0) {
        return null;
      }
      if (clause.id !== undefined) {
        if (clause.id === null || clause.id === undefined) {
          return null;
        }
        if (typeof clause.id === "string") {
          if (clause.id === "invalid") return null;
          const parsedId = Number(clause.id);
          if (isNaN(parsedId)) return null;
          clause.id = parsedId;
        }
      }
      const user = await prisma.users.findFirst({
        where: clause as Prisma.usersWhereInput,
      });
      return user ? User.filterFields({ ...user }) : null;
    } catch (error) {
      console.error(
        "Error in User.get:",
        error instanceof Error ? error.message : String(error)
      );
      return null;
    }
  }

  static async _get(
    clause: WhereClause = {}
  ): Promise<
    (PrismaUser & { organization?: PrismaOrganization | null }) | null
  > {
    try {
      if (clause.id !== undefined && typeof clause.id === "string") {
        const parsedId = parseInt(clause.id);
        if (isNaN(parsedId)) {
          return null;
        }
        clause.id = parsedId;
      }
      const user = await prisma.users.findFirst({
        where: clause as Prisma.usersWhereInput,
        include: { organization: true },
      });
      return user ? { ...user } : null;
    } catch (error) {
      console.error(
        "Error in User._get:",
        error instanceof Error ? error.message : String(error)
      );
      return null;
    }
  }

  static async count(clause: WhereClause = {}): Promise<number> {
    try {
      const count = await prisma.users.count({
        where: clause as Prisma.usersWhereInput,
      });
      return count;
    } catch (error: unknown) {
      console.error(error instanceof Error ? error.message : String(error));
      return 0;
    }
  }

  static async delete(clause: WhereClause = {}): Promise<boolean> {
    try {
      const usersToDelete = await prisma.users.findMany({
        where: clause as Prisma.usersWhereInput,
      });
      if (usersToDelete.length === 0) {
        return true;
      }
      for (const user of usersToDelete) {
        const workspaces = await Workspace.where({ user_id: user.id });
        for (const workspace of workspaces) {
          await Workspace.delete({ slug: workspace.slug });
        }
        await UserToken.deleteAllUserTokens(user.id);
        await prisma.users.delete({ where: { id: user.id } });
      }
      return true;
    } catch (error) {
      console.error("Error deleting user and associated data:", error);
      return false;
    }
  }

  static async where(
    clause: WhereClause = {},
    limit: number | null = null,
    offset: number | null = null
  ): Promise<{ users: FilteredUser[]; total: number }> {
    try {
      const { users, total } = await prisma.$transaction(async (tx) => {
        const users = await tx.users.findMany({
          where: clause as Prisma.usersWhereInput,
          ...(limit !== null ? { take: limit } : {}),
          ...(offset !== null ? { skip: offset } : {}),
          orderBy: { createdAt: "desc" },
          include: { organization: true },
        });
        const total = await tx.users.count({
          where: clause as Prisma.usersWhereInput,
        });
        return { users, total };
      });
      return { users: users.map((user) => User.filterFields(user)), total };
    } catch (error: unknown) {
      console.error(error instanceof Error ? error.message : String(error));
      return { users: [], total: 0 };
    }
  }

  static checkPasswordComplexity(
    passwordInput: string = ""
  ): PasswordCheckResult {
    const complexityOptions = {
      min: Number(process.env.PASSWORDMINCHAR) || 8,
      max: Number(process.env.PASSWORDMAXCHAR) || 250,
      lowerCase: Number(process.env.PASSWORDLOWERCASE) || 0,
      upperCase: Number(process.env.PASSWORDUPPERCASE) || 0,
      numeric: Number(process.env.PASSWORDNUMERIC) || 0,
      symbol: Number(process.env.PASSWORDSYMBOL) || 0,
      requirementCount: Number(process.env.PASSWORDREQUIREMENTS) || 0,
    };
    const validator = passwordComplexity(
      complexityOptions,
      "password"
    ) as unknown as PasswordValidator;
    const complexityCheck = validator.validate(passwordInput);
    if ("error" in complexityCheck) {
      let myError = "";
      let prepend = "";
      for (let i = 0; i < complexityCheck.error!.details.length; i++) {
        myError += prepend + complexityCheck.error!.details[i].message;
        prepend = ", ";
      }
      // Ensure the error message always includes 'length' if it's a length error
      if (myError.includes("characters long") && !myError.includes("length")) {
        myError = "Password length error: " + myError;
      }
      return { checkedOK: false, error: myError };
    }
    return { checkedOK: true, error: "No error." };
  }

  static async getWithOrg(
    clause: WhereClause = {}
  ): Promise<FilteredUser | null> {
    try {
      if (clause.id !== undefined && typeof clause.id === "string") {
        const parsedId = parseInt(clause.id);
        if (isNaN(parsedId)) {
          return null;
        }
        clause.id = parsedId;
      }
      const user = await prisma.users.findFirst({
        where: clause as Prisma.usersWhereInput,
        include: { organization: true },
      });
      return user ? User.filterFields({ ...user }) : null;
    } catch (error) {
      console.error(
        "Error in User.getWithOrg:",
        error instanceof Error ? error.message : String(error)
      );
      return null;
    }
  }

  static async getWithStylePreferences(
    userId: number | string
  ): Promise<FilteredUser | null> {
    try {
      if (!userId) {
        return null;
      }
      const user = await prisma.users.findFirst({
        where: { id: parseInt(String(userId)) },
        include: {
          userStyleProfile: {
            where: { is_active: true },
            take: 1,
          },
        },
      });
      if (!user) {
        return null;
      }
      const userWithStyle = User.filterFields({ ...user });
      userWithStyle.styleAlignmentEnabled = user?.userStyleProfile?.length > 0;
      userWithStyle.activeStyleProfile = user.userStyleProfile[0] || null;
      return userWithStyle;
    } catch (error) {
      console.error("Error fetching user with style preferences:", error);
      return null;
    }
  }

  static async hasStyleAlignment(userId: number | string): Promise<boolean> {
    try {
      if (!userId) {
        return false;
      }
      const activeProfile = await prisma.userStyleProfile.findFirst({
        where: {
          user_id: parseInt(String(userId)),
          is_active: true,
        },
      });
      return !!activeProfile;
    } catch (error) {
      console.error("Error checking style alignment:", error);
      return false;
    }
  }

  static async getActiveStyleProfile(
    userId: number | string
  ): Promise<UserStyleProfile | null> {
    try {
      if (!userId) {
        return null;
      }
      const profile = await prisma.userStyleProfile.findFirst({
        where: {
          user_id: parseInt(String(userId)),
          is_active: true,
        },
      });
      return profile;
    } catch (error) {
      console.error("Error fetching active style profile:", error);
      return null;
    }
  }
}

export { User };
export default User;
