import { welcome_messages as PrismaWelcomeMessage } from "@prisma/client";
import prisma from "../utils/prisma";
// Welcome message types
export interface WelcomeMessageData {
  heading: string;
  text: string;
}
export interface WelcomeMessageResponse {
  id?: number;
  response?: string | null;
  heading: string | null;
  text: string | null;
}
export interface WelcomeMessageSaveResult {
  success: boolean;
  error?: string;
}
export interface WhereClause {
  id?: number;
  heading?: string;
  text?: string;
  [key: string]: unknown;
}
const WelcomeMessages = {
  get: async function (
    clause: WhereClause = {}
  ): Promise<PrismaWelcomeMessage | null> {
    try {
      const message = await prisma.welcome_messages.findFirst({
        where: clause,
      });
      return message || null;
    } catch (error) {
      console.error("Error in get:", error);
      return null;
    }
  },
  where: async function (
    clause: WhereClause = {},
    limit?: number
  ): Promise<PrismaWelcomeMessage[]> {
    try {
      const messages = await prisma.welcome_messages.findMany({
        where: clause,
        take: limit || undefined,
      });
      return messages;
    } catch (error) {
      console.error(error instanceof Error ? error.message : String(error));
      return [];
    }
  },
  save: async function (
    message: WelcomeMessageData
  ): Promise<WelcomeMessageSaveResult> {
    try {
      if (!message || typeof message !== "object") {
        throw new Error("Invalid message format");
      }
      await prisma.welcome_messages.deleteMany({});
      await prisma.welcome_messages.create({
        data: {
          heading: message.heading || "",
          text: message.text || "",
        },
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
  getMessages: async function (): Promise<WelcomeMessageResponse> {
    try {
      const message = await prisma.welcome_messages.findFirst({
        select: {
          id: true,
          response: true,
          heading: true,
          text: true,
        },
      });
      return message || { heading: null, text: null };
    } catch {
      return { heading: null, text: null };
    }
  },
};
export { WelcomeMessages };
