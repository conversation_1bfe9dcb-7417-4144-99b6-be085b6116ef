import prisma from "../utils/prisma";
import { Workspace } from "./workspace";
import { User } from "./user";
import { Prisma, workspace_chats } from "@prisma/client";

// Type definitions
interface ChatUser {
  id: number;
  username?: string;
}

interface WorkspaceChatData {
  workspaceId: number;
  prompt: string;
  response?: Record<string, unknown> & {
    metrics?: Record<string, unknown>;
  };
  user?: ChatUser | null;
  threadId?: number | null;
  include?: boolean;
  apiSessionId?: string | null;
  invoice_ref?: string | null;
  metrics?: Record<string, unknown> | string;
}

type WorkspaceChat = workspace_chats;

interface WorkspaceChatWithData {
  id: number;
  workspaceId: number;
  prompt: string;
  response: string;
  user_id: number | null;
  thread_id: number | null;
  api_session_id: string | null;
  include: boolean;
  invoice_ref: string | null;
  metrics: string | null;
  feedbackScore?: boolean | null;
  createdAt: Date;
  lastUpdatedAt?: Date;
  workspace?: {
    name: string;
    slug: string | null;
  };
  user?: {
    username: string;
  };
}

interface CreateChatResult {
  chat: WorkspaceChat | null;
  message: string | null;
}

interface BulkCreateResult {
  chats: WorkspaceChat[] | null;
  message: string | null;
}

type PrismaWhereClause = Prisma.workspace_chatsWhereInput;

interface WhereClause {
  [key: string]: unknown;
  user?: {
    username: string;
  };
}

const WorkspaceChats = {
  new: async function ({
    workspaceId,
    prompt,
    response = {},
    user = null,
    threadId = null,
    include = true,
    apiSessionId = null,
    invoice_ref = null,
    metrics = {},
  }: WorkspaceChatData): Promise<CreateChatResult> {
    try {
      if (!metrics && response.metrics) {
        metrics = response.metrics as Record<string, unknown>;
      }

      // Handle metrics being either an {} or a string
      const serializedMetrics = metrics
        ? typeof metrics === "string"
          ? metrics
          : JSON.stringify(metrics)
        : null;

      const chat = await prisma.workspace_chats.create({
        data: {
          workspaceId,
          prompt,
          response: JSON.stringify(response),
          user_id: user?.id ?? null,
          thread_id: threadId,
          api_session_id: apiSessionId,
          include,
          invoice_ref,
          metrics: serializedMetrics,
        },
      });

      return { chat, message: null };
    } catch (error: unknown) {
      console.error(error instanceof Error ? error.message : String(error));
      return {
        chat: null,
        message: error instanceof Error ? error.message : String(error),
      };
    }
  },

  forWorkspaceByUser: async function (
    workspaceId: number | null = null,
    userId: number | null = null,
    limit: number | null = null,
    orderBy: Prisma.workspace_chatsOrderByWithRelationInput | null = null
  ): Promise<WorkspaceChat[]> {
    if (!workspaceId || !userId) return [];
    try {
      const chats = await prisma.workspace_chats.findMany({
        where: {
          workspaceId,
          user_id: userId,
          thread_id: null, // this function is now only used for the default thread on workspaces and users
          api_session_id: null, // do not include api-session chats in the frontend for anyone.
          include: true,
        },
        ...(limit !== null ? { take: limit } : {}),
        ...(orderBy !== null ? { orderBy } : { orderBy: { id: "asc" } }),
      });
      return chats;
    } catch (error: unknown) {
      console.error(
        error instanceof Error ? error : new Error(String(error)).message
      );
      return [];
    }
  },

  forWorkspace: async function (
    workspaceId: number | null = null,
    limit: number | null = null,
    orderBy: Prisma.workspace_chatsOrderByWithRelationInput | null = null
  ): Promise<WorkspaceChat[]> {
    if (!workspaceId) return [];
    try {
      const chats = await prisma.workspace_chats.findMany({
        where: {
          workspaceId,
          thread_id: null, // this function is now only used for the default thread on workspaces
          api_session_id: null, // do not include api-session chats in the frontend for anyone.
          include: true,
        },
        ...(limit !== null ? { take: limit } : {}),
        ...(orderBy !== null ? { orderBy } : { orderBy: { id: "asc" } }),
      });
      return chats;
    } catch (error: unknown) {
      console.error(
        error instanceof Error ? error : new Error(String(error)).message
      );
      return [];
    }
  },

  markHistoryInvalid: async function (
    workspaceId: number | null = null,
    user: { id: number } | null = null
  ): Promise<void> {
    if (!workspaceId) return;
    try {
      await prisma.workspace_chats.updateMany({
        where: {
          workspaceId,
          user_id: user?.id,
          thread_id: null, // this function is now only used for the default thread on workspaces
        },
        data: {
          include: false,
        },
      });
      return;
    } catch (error) {
      console.error(
        error instanceof Error ? error : new Error(String(error)).message
      );
    }
  },

  markThreadHistoryInvalid: async function (
    workspaceId: number | null = null,
    user: { id: number } | null = null,
    threadId: number | null = null
  ): Promise<void> {
    if (!workspaceId || !threadId) return;
    try {
      await prisma.workspace_chats.updateMany({
        where: {
          workspaceId,
          thread_id: threadId,
          user_id: user?.id,
        },
        data: {
          include: false,
        },
      });
      return;
    } catch (error) {
      console.error(
        error instanceof Error ? error : new Error(String(error)).message
      );
    }
  },

  get: async function (
    where: Record<string, unknown> = {}
  ): Promise<WorkspaceChat | null> {
    try {
      const chat = await prisma.workspace_chats.findFirst({ where });
      return chat;
    } catch (error) {
      console.error(
        error instanceof Error ? error : new Error(String(error)).message
      );
      return null;
    }
  },

  delete: async function (
    clause: Record<string, unknown> = {},
    _isSystemWipe: boolean = false
  ): Promise<boolean> {
    try {
      // Only delete the chat records from the database
      // Do not touch any workspace files as they may be referenced by other chats
      await prisma.workspace_chats.deleteMany({
        where: clause,
      });
      return true;
    } catch (error) {
      console.error(
        error instanceof Error ? error : new Error(String(error)).message
      );
      return false;
    }
  },

  deleteOlderThan: async function (date: Date): Promise<number> {
    try {
      const deleteResult = await prisma.workspace_chats.deleteMany({
        where: {
          createdAt: {
            lt: date,
          },
        },
      });

      return deleteResult.count;
    } catch (error) {
      console.error(
        "Error deleting older chats:",
        error instanceof Error ? error : new Error(String(error)).message
      );
      throw error;
    }
  },

  where: async function (
    clause: WhereClause = {},
    limit: number | null = null,
    orderBy: Record<string, unknown> | null = null,
    offset: number | null = null
  ): Promise<WorkspaceChat[]> {
    try {
      if (clause?.user?.username) {
        const userFilter = clause.user;
        delete clause.user;

        clause = {
          ...clause,
          users: {
            username: userFilter.username,
          },
        };
      }

      const chats = await prisma.workspace_chats.findMany({
        where: clause as PrismaWhereClause,
        ...(limit !== null ? { take: limit } : {}),
        ...(offset !== null ? { skip: offset } : {}),
        ...(orderBy !== null ? { orderBy } : {}),
      });
      return chats;
    } catch (error: unknown) {
      console.error(
        error instanceof Error ? error : new Error(String(error)).message
      );
      return [];
    }
  },

  count: async function (clause: WhereClause = {}): Promise<number> {
    try {
      let whereClause = { ...clause };
      if (whereClause?.user?.username) {
        const userFilter = whereClause.user;
        delete whereClause.user;

        whereClause = {
          ...whereClause,
          users: {
            username: userFilter.username,
          },
        };
      }

      const count = await prisma.workspace_chats.count({
        where: whereClause as PrismaWhereClause,
      });
      return count;
    } catch (error) {
      console.error(
        error instanceof Error ? error : new Error(String(error)).message
      );
      return 0;
    }
  },

  whereWithData: async function (
    clause: WhereClause = {},
    limit: number | null = null,
    offset: number | null = null,
    orderBy: Record<string, unknown> | null = null
  ): Promise<WorkspaceChatWithData[]> {
    try {
      const results = await this.where(clause, limit, orderBy, offset);

      for (const res of results) {
        const workspace = await Workspace.get({ id: res.workspaceId });
        (res as WorkspaceChatWithData).workspace = workspace
          ? { name: workspace.name, slug: workspace.slug }
          : { name: "deleted workspace", slug: null };

        const user = res.user_id ? await User.get({ id: res.user_id }) : null;
        (res as WorkspaceChatWithData).user = user
          ? { username: user.username || "unknown user" }
          : { username: res.api_session_id !== null ? "API" : "unknown user" };

        (res as WorkspaceChatWithData).metrics = res.metrics
          ? JSON.parse(res.metrics)
          : null;
      }

      return results as WorkspaceChatWithData[];
    } catch (error: unknown) {
      console.error(
        error instanceof Error ? error : new Error(String(error)).message
      );
      return [];
    }
  },

  updateFeedbackScore: async function (
    chatId: number | null = null,
    feedbackScore: number | null = null
  ): Promise<void> {
    if (!chatId) return;
    try {
      await prisma.workspace_chats.update({
        where: {
          id: Number(chatId),
        },
        data: {
          feedbackScore:
            feedbackScore === null ? null : Number(feedbackScore) === 1,
        },
      });
      return;
    } catch (error) {
      console.error(
        error instanceof Error ? error : new Error(String(error)).message
      );
    }
  },

  // Explicit update of settings + key validations.
  // Only use this method when directly setting a key value
  // that takes no user input for the keys being modified.
  update: async function (
    id: number,
    data: Partial<WorkspaceChat> = {}
  ): Promise<WorkspaceChat | null> {
    try {
      // If metrics is an object, stringify it
      if (data.metrics && typeof data.metrics === "object") {
        data.metrics = JSON.stringify(data.metrics);
      }

      const result = await prisma.workspace_chats.update({
        where: { id },
        data,
      });
      return result;
    } catch (error) {
      console.error(
        `Error updating workspace chat: ${error instanceof Error ? error : new Error(String(error)).message}`
      );
      return null;
    }
  },

  bulkCreate: async function (
    chatsData: WorkspaceChatData[]
  ): Promise<BulkCreateResult> {
    // TODO: Replace with createMany when we update prisma to latest version
    // The version of prisma that we are currently using does not support createMany with SQLite
    try {
      const createdChats: WorkspaceChat[] = [];
      for (const chatData of chatsData) {
        // Convert WorkspaceChatData to the format expected by Prisma
        const prismaData = {
          workspaceId: chatData.workspaceId,
          prompt: chatData.prompt,
          response: chatData.response
            ? typeof chatData.response === "string"
              ? chatData.response
              : JSON.stringify(chatData.response)
            : "",
          user_id: chatData.user?.id ?? null,
          thread_id: chatData.threadId ?? null,
          include: chatData.include ?? true,
          api_session_id: chatData.apiSessionId ?? null,
          invoice_ref: chatData.invoice_ref ?? null,
          metrics: chatData.metrics
            ? typeof chatData.metrics === "string"
              ? chatData.metrics
              : JSON.stringify(chatData.metrics)
            : null,
        };
        const chat = await prisma.workspace_chats.create({
          data: prismaData,
        });
        createdChats.push(chat);
      }
      return { chats: createdChats, message: null };
    } catch (error) {
      console.error(
        error instanceof Error ? error : new Error(String(error)).message
      );
      return {
        chats: null,
        message: error instanceof Error ? error.message : String(error),
      };
    }
  },

  // Get all chats for a specific thread
  forThread: async function (
    threadId: number,
    includeInvisible: boolean = false
  ): Promise<WorkspaceChat[]> {
    try {
      const whereClause: Record<string, unknown> = {
        thread_id: threadId,
      };

      if (!includeInvisible) {
        whereClause.include = true;
      }

      const chats = await prisma.workspace_chats.findMany({
        where: whereClause,
        orderBy: {
          id: "asc",
        },
      });
      return chats;
    } catch (error) {
      console.error("Error fetching chats for thread:", error);
      return [];
    }
  },
};

export { WorkspaceChats };
export default WorkspaceChats;
