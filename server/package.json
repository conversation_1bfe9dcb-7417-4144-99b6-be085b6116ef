{"name": "ist-legal-server", "version": "1.1.3", "description": "Server endpoints to process or create content for chatting", "main": "dist/index.js", "author": "IST Legal", "license": "Copyright", "private": false, "engines": {"node": ">=18.12.1"}, "scripts": {"dev": "cross-env NODE_ENV=development nodemon index.ts", "build": "rm -rf dist && ./node_modules/.bin/tsc -p tsconfig.build.json && mkdir -p dist/swagger && cp swagger/*.css swagger/*.json dist/swagger/ && node scripts/validate-swagger-build.js", "start": "NODE_ENV=production node dist/index.js", "lint": "npx prettier --ignore-path ../.prettierignore --write ./endpoints ./models ./utils ./tests && npx eslint ./endpoints ./models ./utils ./tests --ext .js,.jsx,.ts,.tsx --fix && npm run typecheck", "typecheck": "tsc --noEmit -p tsconfig.check.json", "swagger": "node ./swagger/init.js", "test": "OPENAI_API_KEY=dummy_key jest --config jest.config.local.js --silent", "test:ci": "OPENAI_API_KEY=dummy_key jest --config jest.config.ci.js --silent", "test:watch": "OPENAI_API_KEY=dummy_key jest --config jest.config.local.js --watch"}, "prisma": {"seed": "node prisma/seed.js"}, "dependencies": {"@anthropic-ai/sdk": "^0.32.1", "@aws-sdk/client-ses": "^3.799.0", "@azure/openai": "1.0.0-beta.10", "@datastax/astra-db-ts": "^1.5.0", "@google/genai": "^0.9.0", "@ladjs/graceful": "^3.2.2", "@lancedb/lancedb": "^0.15.0", "@langchain/anthropic": "0.1.16", "@langchain/aws": "^0.0.9", "@langchain/community": "^0.3.22", "@langchain/core": "^0.3.18", "@langchain/openai": "0.0.28", "@langchain/textsplitters": "0.0.0", "@mintplex-labs/bree": "^9.2.5", "@mintplex-labs/express-ws": "^5.0.7", "@modelcontextprotocol/sdk": "^1.11.0", "@pinecone-database/pinecone": "^2.0.1", "@prisma/client": "6.11.1", "@qdrant/js-client-rest": "^1.9.0", "@xenova/transformers": "^2.14.0", "@zilliz/milvus2-sdk-node": "^2.3.5", "apache-arrow": "19.0.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "body-parser": "^1.20.2", "chalk": "^4", "check-disk-space": "^3.4.0", "chromadb": "^1.5.2", "cohere-ai": "^7.9.5", "cors": "^2.8.5", "croner": "^9.0.0", "cross-env": "^7.0.3", "diff": "^7.0.0", "docx": "^9.3.0", "docxtemplater": "^3.43.0", "dotenv": "^16.0.3", "elevenlabs": "^0.5.0", "express": "^4.18.2", "extract-json-from-string": "^1.0.1", "graphql": "^16.7.1", "joi": "^17.11.0", "joi-password-complexity": "^5.2.0", "js-tiktoken": "^1.0.7", "jsonrepair": "^3.7.0", "jsonwebtoken": "^9.0.0", "langchain": "^0.3.9", "mammoth": "^1.9.0", "markdown-it": "^14.1.0", "mime": "^3.0.0", "moment": "^2.29.4", "mssql": "^11.0.1", "multer": "^1.4.5-lts.1", "mysql2": "^3.9.8", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "node-llama-cpp": "^2.8.0", "officegen": "^0.6.5", "ollama": "^0.5.10", "openai": "4.95.1", "pg": "^8.11.5", "pinecone-client": "^1.1.0", "pizzip": "^3.0.6", "pluralize": "^8.0.0", "posthog-node": "^3.1.1", "prisma": "6.11.1", "prosemirror-markdown": "^1.13.2", "prosemirror-model": "^1.25.1", "prosemirror-schema-basic": "^1.2.4", "prosemirror-schema-list": "^1.5.1", "remark-docx": "^0.1.6", "remark-parse": "^11.0.0", "slugify": "^1.6.6", "swagger-autogen": "^2.23.5", "swagger-ui-express": "^5.0.0", "truncate": "^3.0.0", "unified": "^11.0.5", "url-pattern": "^1.0.3", "uuid": "^9.0.0", "uuid-apikey": "^1.5.3", "vectordb": "0.4.11", "weaviate-ts-client": "^1.4.0", "winston": "^3.13.0"}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.28.0", "@babel/plugin-transform-modules-commonjs": "^7.26.3", "@babel/preset-env": "^7.26.9", "@babel/preset-typescript": "^7.27.1", "@inquirer/prompts": "^4.3.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/mssql": "^9.1.7", "@types/multer": "^2.0.0", "@types/node": "^24.0.10", "@types/pg": "^8.15.4", "@types/supertest": "^6.0.3", "@types/ws": "^8.18.1", "@playwright/test": "^1.40.0", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "babel-jest": "^29.7.0", "eslint": "^9.25.1", "eslint-config-prettier": "^9.0.0", "eslint-plugin-ft-flow": "^3.0.0", "eslint-plugin-package-json": "^0.31.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.3", "eslint-plugin-unused-imports": "^4.1.4", "flow-bin": "^0.217.0", "flow-remove-types": "^2.217.1", "glob": "^11.0.3", "globals": "^13.21.0", "hermes-eslint": "^0.15.0", "jest": "^29.7.0", "jest-junit": "^16.0.0", "jsonc-eslint-parser": "^2.4.0", "node-html-markdown": "^1.3.0", "node-mocks-http": "^1.17.2", "nodemon": "^3.1.9", "prettier": "^3.0.3", "rimraf": "^6.0.1", "supertest": "^7.0.0", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "ws": "^8.18.3", "zod": "^3.25.76"}}