#!/usr/bin/env node
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

interface CustomLegalTemplate {
  category: string;
  documentType: string;
  templateContent: string;
  templateFormatting?: string | null;
  customInputs?: Record<string, any>;
}

async function main(): Promise<void> {
  console.log("Starting backfill of custom_legal_templates...");

  const setting = await prisma.system_settings.findUnique({
    where: { label: "custom_legal_templates" },
  });

  if (!setting || !setting.value) {
    console.log("No custom_legal_templates found. Skipping backfill.");
    return;
  }

  let templates: CustomLegalTemplate[];
  try {
    templates = JSON.parse(setting.value) as CustomLegalTemplate[];
  } catch (e) {
    console.error("Failed to parse custom_legal_templates JSON:", e);
    process.exit(1);
  }

  for (const t of templates) {
    await prisma.systemLegalTemplate.create({
      data: {
        category: t.category,
        documentType: t.documentType,
        templateContent: t.templateContent,
        templateFormatting: t.templateFormatting || null,
        customInputs: t.customInputs ? JSON.stringify(t.customInputs) : null,
      } as any,
    });
    console.log(`Inserted system template: ${t.category}/${t.documentType}`);
  }

  await prisma.system_settings.update({
    where: { label: "custom_legal_templates" },
    data: { label: "legacy_custom_legal_templates" },
  });

  console.log("Renamed system_settings label to legacy_custom_legal_templates");
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
