generator client {
    provider      = "prisma-client-js"
  binaryTargets = ["native", "darwin-arm64"]
}

datasource db {
  provider = "sqlite"
  url      = "file:../storage/istlegal.db"
}

model Category {
  id                Int      @id @default(autoincrement())
  name              String
  sub_category      String?
  description       String?
  legal_task_prompt String?
  legalTaskType     String   @default("noMainDoc")
  createdAt         DateTime @default(now())
  updatedAt         DateTime
}

model Feedback {
  id        Int      @id @default(autoincrement())
  fullName  String
  message   String
  filePath  String?
  createdAt DateTime @default(now())
}

model Organization {
  id                        Int                         @id @default(autoincrement())
  name                      String                      @unique
  createdAt                 DateTime                    @default(now())
  lastUpdatedAt             DateTime
  OrganizationLegalTemplate OrganizationLegalTemplate[]
  users                     users[]
}

model OrganizationLegalTemplate {
  id                 Int          @id @default(autoincrement())
  organizationId     Int
  category           String
  documentType       String
  templateContent    String
  templateFormatting String?
  customInputs       String?
  createdAt          DateTime     @default(now())
  updatedAt          DateTime
  Organization       Organization @relation(fields: [organizationId], references: [id])

  @@index([organizationId])
  @@index([category, documentType])
}

model SystemLegalTemplate {
  id                 Int      @id @default(autoincrement())
  category           String
  documentType       String
  templateContent    String
  templateFormatting String?
  customInputs       String?
  createdAt          DateTime @default(now())
  updatedAt          DateTime

  @@index([category, documentType])
}

model ThreadShare {
  id                Int               @id @default(autoincrement())
  threadId          Int
  userId            Int
  createdAt         DateTime          @default(now())
  users             users             @relation(fields: [userId], references: [id], onDelete: Cascade)
  workspace_threads workspace_threads @relation(fields: [threadId], references: [id], onDelete: Cascade)

  @@unique([threadId, userId])
  @@index([userId])
  @@index([threadId])
}

model UserLegalTemplate {
  id                 Int      @id @default(autoincrement())
  userId             Int
  category           String
  documentType       String
  templateContent    String
  templateFormatting String?
  customInputs       String?
  createdAt          DateTime @default(now())
  updatedAt          DateTime
  users              users    @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([category, documentType])
}

model UserStyleProfile {
  id           Int      @id @default(autoincrement())
  user_id      Int
  name         String
  description  String?
  instructions String
  is_active    Boolean  @default(false)
  createdAt    DateTime @default(now())
  updatedAt    DateTime
  users        users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([user_id, name])
  @@index([user_id])
}

model WorkspaceShare {
  id          Int        @id @default(autoincrement())
  workspaceId Int
  userId      Int
  createdAt   DateTime   @default(now())
  users       users      @relation(fields: [userId], references: [id], onDelete: Cascade)
  workspaces  workspaces @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  @@unique([workspaceId, userId])
  @@index([userId])
  @@index([workspaceId])
}

model api_keys {
  id            Int      @id @default(autoincrement())
  secret        String?  @unique
  createdBy     Int?
  createdAt     DateTime @default(now())
  lastUpdatedAt DateTime @default(now())
}

model browser_extension_api_keys {
  id            Int      @id @default(autoincrement())
  key           String   @unique
  user_id       Int?
  createdAt     DateTime @default(now())
  lastUpdatedAt DateTime
  users         users?   @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id])
}

model cache_data {
  id            Int       @id @default(autoincrement())
  name          String
  data          String
  belongsTo     String?
  byId          Int?
  expiresAt     DateTime?
  createdAt     DateTime  @default(now())
  lastUpdatedAt DateTime  @default(now())
}

model document_sync_executions {
  id                   Int                  @id @default(autoincrement())
  queueId              Int
  status               String               @default("unknown")
  result               String?
  createdAt            DateTime             @default(now())
  document_sync_queues document_sync_queues @relation(fields: [queueId], references: [id], onDelete: Cascade)
}

model document_sync_queues {
  id                       Int                        @id @default(autoincrement())
  staleAfterMs             Int                        @default(604800000)
  nextSyncAt               DateTime
  createdAt                DateTime                   @default(now())
  lastSyncedAt             DateTime                   @default(now())
  workspaceDocId           Int                        @unique
  document_sync_executions document_sync_executions[]
  workspace_documents      workspace_documents        @relation(fields: [workspaceDocId], references: [id], onDelete: Cascade)
}

model document_vectors {
  id            Int      @id @default(autoincrement())
  docId         String
  vectorId      String
  createdAt     DateTime @default(now())
  lastUpdatedAt DateTime @default(now())
}

model embed_chats {
  id                     Int           @id @default(autoincrement())
  prompt                 String
  response               String
  session_id             String
  include                Boolean       @default(true)
  connection_information String?
  embed_id               Int
  usersId                Int?
  createdAt              DateTime      @default(now())
  users                  users?        @relation(fields: [usersId], references: [id])
  embed_configs          embed_configs @relation(fields: [embed_id], references: [id], onDelete: Cascade)
}

model embed_configs {
  id                         Int           @id @default(autoincrement())
  uuid                       String        @unique
  enabled                    Boolean       @default(false)
  chat_mode                  String        @default("query")
  allowlist_domains          String?
  allow_model_override       Boolean       @default(false)
  allow_temperature_override Boolean       @default(false)
  allow_prompt_override      Boolean       @default(false)
  max_chats_per_day          Int?
  max_chats_per_session      Int?
  workspace_id               Int
  createdBy                  Int?
  usersId                    Int?
  createdAt                  DateTime      @default(now())
  embed_chats                embed_chats[]
  users                      users?        @relation(fields: [usersId], references: [id])
  workspaces                 workspaces    @relation(fields: [workspace_id], references: [id], onDelete: Cascade)
}

model event_logs {
  id         Int      @id @default(autoincrement())
  event      String
  metadata   String?
  userId     Int?
  occurredAt DateTime @default(now())

  @@index([event])
}

model invites {
  id            Int      @id @default(autoincrement())
  code          String   @unique
  status        String   @default("pending")
  claimedBy     Int?
  workspaceIds  String?
  usersUsage    String?
  countUsage    Int      @default(0)
  maxUsage      Int      @default(1)
  createdAt     DateTime @default(now())
  createdBy     Int
  lastUpdatedAt DateTime @default(now())
}

model linked_workspaces {
  id                                                          Int        @id @default(autoincrement())
  workspace_id                                                Int
  linkedWorkspace_id                                          Int
  createdAt                                                   DateTime   @default(now())
  lastUpdatedAt                                               DateTime   @default(now())
  workspaces_linked_workspaces_linkedWorkspace_idToworkspaces workspaces @relation("linked_workspaces_linkedWorkspace_idToworkspaces", fields: [linkedWorkspace_id], references: [id], onDelete: Cascade)
  workspaces_linked_workspaces_workspace_idToworkspaces       workspaces @relation("linked_workspaces_workspace_idToworkspaces", fields: [workspace_id], references: [id], onDelete: Cascade)
}

model news_messages {
  id           Int       @id @default(autoincrement())
  title        String
  content      String
  priority     String    @default("medium")
  target_roles String?
  is_active    Boolean   @default(true)
  expires_at   DateTime?
  created_by   Int
  createdAt    DateTime  @default(now())
  updatedAt    DateTime
  users        users     @relation(fields: [created_by], references: [id])

  @@index([expires_at])
  @@index([is_active, createdAt])
}

model password_reset_tokens {
  id        Int      @id @default(autoincrement())
  user_id   Int
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())
  users     users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id])
}

model prompt_examples {
  id            Int      @id @default(autoincrement())
  title         String
  area          String
  prompt        String
  icon          String?
  workspaceSlug String
  createdAt     DateTime @default(now())
  updatedAt     DateTime
}

model recovery_codes {
  id        Int      @id @default(autoincrement())
  user_id   Int
  code_hash String
  createdAt DateTime @default(now())
  users     users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id])
}

model slash_command_presets {
  id            Int      @id @default(autoincrement())
  command       String
  prompt        String
  description   String
  uid           Int      @default(0)
  userId        Int?
  createdAt     DateTime @default(now())
  lastUpdatedAt DateTime @default(now())
  users         users?   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([uid, command])
}

model system_report_messages {
  id             Int            @id @default(autoincrement())
  content        String
  userId         Int
  reportId       Int
  createdAt      DateTime       @default(now())
  system_reports system_reports @relation(fields: [reportId], references: [id])
  users          users          @relation(fields: [userId], references: [id])

  @@index([reportId])
  @@index([userId])
}

model system_reports {
  id                     Int                      @id @default(autoincrement())
  title                  String
  description            String
  status                 String                   @default("REPORTED")
  type                   String                   @default("INCIDENT")
  severity               String?
  affected_service       String?
  userId                 Int
  resolver_user_id       Int?
  resolution_comment     String?
  resolution_confirmed   Boolean                  @default(false)
  resolution_confirmed_at DateTime?
  resolution_confirmed_by Int?
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime                 @updatedAt
  users                  users                    @relation("SystemReportUser", fields: [userId], references: [id], onDelete: Cascade)
  resolver_user          users?                   @relation("SystemReportResolver", fields: [resolver_user_id], references: [id], onDelete: SetNull)
  resolution_confirmer   users?                   @relation("SystemReportConfirmer", fields: [resolution_confirmed_by], references: [id], onDelete: SetNull)
  system_report_messages system_report_messages[]

  @@index([userId])
  @@index([resolver_user_id])
  @@index([status])
  @@index([type])
}

model system_settings {
  id            Int      @id @default(autoincrement())
  label         String   @unique
  value         String?
  createdAt     DateTime @default(now())
  lastUpdatedAt DateTime @default(now())
}

model user_news_dismissals {
  id           Int      @id @default(autoincrement())
  user_id      Int
  news_id      String
  dismissed_at DateTime @default(now())
  users        users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([user_id, news_id])
  @@index([news_id])
  @@index([user_id])
}

model user_prompt_libraries {
  id          Int      @id @default(autoincrement())
  user_id     Int
  name        String
  prompt_text String
  description String?
  created_at  DateTime @default(now())
  updated_at  DateTime
  users       users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([user_id, name])
  @@index([user_id])
}

model user_tokens {
  id            Int      @id @default(autoincrement())
  user_id       Int
  token         String   @unique
  device_info   String?
  last_used     DateTime @default(now())
  createdAt     DateTime @default(now())
  lastUpdatedAt DateTime @default(now())
  users         users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id])
}

model users {
  id                          Int                           @id @default(autoincrement())
  username                    String?                       @unique
  password                    String
  pfpFilename                 String?
  role                        String                        @default("default")
  suspended                   Int                           @default(0)
  seen_recovery_codes         Boolean?                      @default(false)
  custom_ai_userselected      Boolean                       @default(false)
  custom_ai_selected_engine   String                        @default("_CUAI")
  economy_system_id           String?
  createdAt                   DateTime                      @default(now())
  lastUpdatedAt               DateTime                      @default(now())
  organizationId              Int?
  custom_system_prompt        String?
  ThreadShare                 ThreadShare[]
  UserLegalTemplate           UserLegalTemplate[]
  userStyleProfile            UserStyleProfile[]
  WorkspaceShare              WorkspaceShare[]
  browser_extension_api_keys  browser_extension_api_keys[]
  embed_chats                 embed_chats[]
  embed_configs               embed_configs[]
  news_messages               news_messages[]
  password_reset_tokens       password_reset_tokens[]
  recovery_codes              recovery_codes[]
  slash_command_presets       slash_command_presets[]
  system_report_messages      system_report_messages[]
  system_reports_created      system_reports[] @relation("SystemReportUser")
  system_reports_resolved     system_reports[] @relation("SystemReportResolver")
  system_reports_confirmed    system_reports[] @relation("SystemReportConfirmer")
  user_news_dismissals        user_news_dismissals[]
  user_prompt_libraries       user_prompt_libraries[]
  user_tokens                 user_tokens[]
  organization                Organization?                 @relation(fields: [organizationId], references: [id])
  workspace_agent_invocations workspace_agent_invocations[]
  workspace_chats             workspace_chats[]
  workspace_threads           workspace_threads[]
  workspace_users             workspace_users[]

  @@index([organizationId])
}

model welcome_messages {
  id        Int      @id @default(autoincrement())
  heading   String?
  text      String?
  response  String?
  createdAt DateTime @default(now())
}

model workspace_agent_invocations {
  id            Int        @id @default(autoincrement())
  uuid          String
  prompt        String
  closed        Boolean    @default(false)
  user_id       Int?
  thread_id     Int?
  workspace_id  Int
  createdAt     DateTime   @default(now())
  lastUpdatedAt DateTime   @default(now())
  workspaces    workspaces @relation(fields: [workspace_id], references: [id], onDelete: Cascade)
  users         users?     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([uuid])
  @@index([uuid], map: "workspace_agent_invocations_uuid_key")
}

model workspace_chats {
  id             Int      @id @default(autoincrement())
  workspaceId    Int
  prompt         String
  response       String
  include        Boolean  @default(true)
  user_id        Int?
  thread_id      Int?
  api_session_id String?
  createdAt      DateTime @default(now())
  lastUpdatedAt  DateTime @default(now())
  feedbackScore  Boolean?
  invoice_ref    String?
  metrics        String?
  users          users?   @relation(fields: [user_id], references: [id], onDelete: Cascade)
}

model workspace_documents {
  id                   Int                   @id @default(autoincrement())
  docId                String
  filename             String
  docpath              String
  workspaceId          Int
  metadata             String?
  pinned               Boolean?              @default(false)
  pdr                  Boolean?              @default(false)
  watched              Boolean?              @default(false)
  createdAt            DateTime              @default(now())
  lastUpdatedAt        DateTime              @default(now())
  starred              Boolean?              @default(false)
  document_sync_queues document_sync_queues?
  workspaces           workspaces            @relation(fields: [workspaceId], references: [id])
}

model workspace_suggested_messages {
  id            Int        @id @default(autoincrement())
  workspaceId   Int
  heading       String
  message       String
  createdAt     DateTime   @default(now())
  lastUpdatedAt DateTime   @default(now())
  workspaces    workspaces @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  @@index([workspaceId])
}

model workspace_threads {
  id            Int           @id @default(autoincrement())
  name          String
  slug          String        @unique
  workspace_id  Int
  user_id       Int?
  createdAt     DateTime      @default(now())
  lastUpdatedAt DateTime      @default(now())
  sharedWithOrg Boolean       @default(false)
  ThreadShare   ThreadShare[]
  users         users?        @relation(fields: [user_id], references: [id], onDelete: Cascade)
  workspaces    workspaces    @relation(fields: [workspace_id], references: [id], onDelete: Cascade)

  @@index([user_id])
  @@index([workspace_id])
}

model workspace_users {
  id            Int        @id @default(autoincrement())
  user_id       Int
  workspace_id  Int
  createdAt     DateTime   @default(now())
  lastUpdatedAt DateTime   @default(now())
  users         users      @relation(fields: [user_id], references: [id], onDelete: Cascade)
  workspaces    workspaces @relation(fields: [workspace_id], references: [id], onDelete: Cascade)
}

model workspaces {
  id                                                                 Int                            @id @default(autoincrement())
  name                                                               String
  slug                                                               String                         @unique
  vectorTag                                                          String?
  createdAt                                                          DateTime                       @default(now())
  openAiTemp                                                         Float?
  openAiHistory                                                      Int                            @default(20)
  lastUpdatedAt                                                      DateTime                       @default(now())
  openAiPrompt                                                       String?
  similarityThreshold                                                Float?                         @default(0.25)
  chatProvider                                                       String?
  chatModel                                                          String?
  embeddingProvider                                                  String?
  embeddingModel                                                     String?
  topN                                                               Int?                           @default(4)
  type                                                               String?                        @default("")
  chatMode                                                           String?                        @default("chat")
  chatType                                                           String?                        @default("private")
  pfpFilename                                                        String?
  agentProvider                                                      String?
  agentModel                                                         String?
  queryRefusalResponse                                               String?
  vectorSearchMode                                                   String?
  user_id                                                            Int                            @default(0)
  pdr                                                                Boolean?                       @default(false)
  hasMessages                                                        Boolean?                       @default(false)
  order                                                              Int                            @default(0)
  sharedWithOrg                                                      Boolean                        @default(false)
  WorkspaceShare                                                     WorkspaceShare[]
  embed_configs                                                      embed_configs[]
  linked_workspaces_linked_workspaces_linkedWorkspace_idToworkspaces linked_workspaces[]            @relation("linked_workspaces_linkedWorkspace_idToworkspaces")
  linked_workspaces_linked_workspaces_workspace_idToworkspaces       linked_workspaces[]            @relation("linked_workspaces_workspace_idToworkspaces")
  workspace_agent_invocations                                        workspace_agent_invocations[]
  workspace_documents                                                workspace_documents[]
  workspace_suggested_messages                                       workspace_suggested_messages[]
  workspace_threads                                                  workspace_threads[]
  workspace_users                                                    workspace_users[]

  @@index([user_id])
}
