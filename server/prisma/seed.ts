import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// Define types for the seeding process
interface SystemSetting {
  label: string;
  value: string;
}

async function main(): Promise<void> {
  const settings: SystemSetting[] = [
    { label: "multi_user_mode", value: "true" },
    { label: "public_user_mode", value: "false" },
    { label: "limit_user_messages", value: "false" },
    { label: "message_limit", value: "25" },
    { label: "logo_light", value: "ISTLogo.png" },
    { label: "logo_dark", value: "ISTLogo.png" },
    {
      label: "prompt_upgrade_template",
      value: `Your task is to refine a proposed prompt from a user who is a legal professional.
The prompt is intended for use in a legal task, but the user is not an expert in crafting optimal prompts for AI handling.
The prompt will also be used to search a vector database for relevant sources using semantic search.
Improve the prompt for clarity, detail, and specificity.
Ensure that the prompt is designed to generate results that are engaging, comprehensive, and specified according to professional standards in the legal domain.
Generate the response in the same language provided in original prompt.
Do not respond to the prompt but only provide a suggestion for an improved prompt.
Include no introductory text, just respond with the replacement prompt suggestion.
This is the prompt to be refined: <ORIGINALPROMPT> {{prompt}} </ORIGINALPROMPT>`,
    },
    // LLM Provider defaults - set to "system-standard" for proper fallback behavior
    { label: "LLMProvider_PU", value: "system-standard" },
    { label: "LLMProvider_VA", value: "system-standard" },
    { label: "LLMProvider_TM", value: "system-standard" },
  ];

  for (const setting of settings) {
    const existing = await prisma.system_settings.findUnique({
      where: { label: setting.label },
    });

    // Only create the setting if it doesn't already exist
    if (!existing) {
      await prisma.system_settings.create({
        data: setting,
      });
    }
  }
}

main()
  .catch((e: Error) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
