import express, { Request, Response } from "express";
import SystemSettings from "../../../models/systemSettings";
import {
  strictMultiUserRoleValid,
  ROLES,
} from "../../../utils/middleware/multiUserProtected";
import { EncryptionManager } from "../../../utils/EncryptionManager";

const router = express.Router();

interface DeepSearchSettings {
  provider: string;
  modelId: string;
  apiKey: string | null;
  enabled: boolean;
  contextPercentage: number;
  hasApiKey?: boolean;
}

// Create a helper function to encrypt API keys
const encryptApiKey = (apiKey: string): string => {
  const encryptionManager = new EncryptionManager();
  return encryptionManager.encrypt(apiKey) || apiKey;
};

// Create admin middleware
const checkAdmin = strictMultiUserRoleValid([ROLES.admin]);

router.get(
  "/",
  checkAdmin,
  async (_req: Request, res: Response): Promise<void> => {
    try {
      const settings = await SystemSettings.getDeepSearchSettings();

      // Don't send the actual API key to the frontend, just whether one exists
      if (settings) {
        const hasApiKey = !!settings.apiKey;
        const settingsToReturn: DeepSearchSettings = {
          ...settings,
          apiKey: "",
          hasApiKey,
        };
        res.status(200).json({ settings: settingsToReturn });
        return;
      }

      res.status(200).json({
        settings: {
          provider: "google",
          modelId: "gemini-2.0-flash",
          apiKey: "",
          hasApiKey: false,
          enabled: false,
          contextPercentage: 10,
        } satisfies DeepSearchSettings,
      });
    } catch (error) {
      console.error("Error fetching DeepSearch settings:", error);
      res.status(500).json({ error: "Failed to fetch DeepSearch settings" });
    }
  }
);

router.put(
  "/",
  checkAdmin,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { provider, modelId, apiKey, enabled, contextPercentage } =
        req.body;

      // Get current settings to check if we need to update the API key
      const currentSettings = await SystemSettings.getDeepSearchSettings();

      // Only encrypt and update API key if a new one is provided
      let encryptedApiKey = (currentSettings?.apiKey ?? false) || null;
      if (apiKey) {
        encryptedApiKey = encryptApiKey(apiKey);
      }

      const settings = {
        provider,
        modelId,
        apiKey: encryptedApiKey ?? undefined,
        enabled,
        contextPercentage,
      };

      await SystemSettings.updateDeepSearchSettings(settings);

      // Don't send the actual API key back to the frontend
      res.status(200).json({
        settings: {
          ...settings,
          apiKey: "",
          hasApiKey: !!encryptedApiKey,
        } satisfies DeepSearchSettings,
      });
    } catch (error) {
      console.error("Error updating DeepSearch settings:", error);
      res.status(500).json({ error: "Failed to update DeepSearch settings" });
    }
  }
);

export default router;
