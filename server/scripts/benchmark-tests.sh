#!/bin/bash

echo "🚀 Jest Performance Benchmark"
echo "============================"
echo ""

# Function to run tests and measure time
run_test() {
    local config=$1
    local name=$2
    
    echo "Running: $name"
    start=$(date +%s)
    
    if [ "$config" = "default" ]; then
        npm test -- --silent > /dev/null 2>&1
    else
        npm run $config -- --silent > /dev/null 2>&1
    fi
    
    end=$(date +%s)
    duration=$((end - start))
    
    echo "✅ Completed in ${duration}s"
    echo ""
    
    return $duration
}

# Run benchmarks
echo "1️⃣ Default Configuration (single worker)"
run_test "default" "Default Jest Config"
default_time=$?

echo "2️⃣ Optimized Configuration"  
run_test "test:fast" "Optimized Jest Config"
optimized_time=$?

echo "3️⃣ Parallel Configuration"
run_test "test:parallel" "Parallel Jest Config"
parallel_time=$?

# Summary
echo "📊 Summary"
echo "========="
echo "Default:    ${default_time}s"
echo "Optimized:  ${optimized_time}s" 
echo "Parallel:   ${parallel_time}s"
echo ""

# Calculate improvements
if [ $default_time -gt 0 ]; then
    optimized_improvement=$(( (default_time - optimized_time) * 100 / default_time ))
    parallel_improvement=$(( (default_time - parallel_time) * 100 / default_time ))
    
    echo "📈 Improvements:"
    echo "Optimized: ${optimized_improvement}% faster"
    echo "Parallel:  ${parallel_improvement}% faster"
fi