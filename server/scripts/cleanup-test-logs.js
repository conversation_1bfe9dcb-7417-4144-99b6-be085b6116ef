#!/usr/bin/env node
/**
 * Test Log Cleanup Script
 *
 * Removes test entries from upload logs to prevent test pollution.
 * Can be run manually or as part of test cleanup.
 */

const fs = require("fs");
const path = require("path");

class TestLogCleaner {
  constructor() {
    // Handle both dev and CI environments
    this.isCIEnvironment = process.env.CI === "true" || !!process.env.CI;
    this.isGitHubActions = process.env.GITHUB_ACTIONS === "true";

    // In CI, use path relative to project root; in dev, relative to script location
    this.basePath = this.isCIEnvironment
      ? path.join(process.cwd(), "server", "storage")
      : path.join(__dirname, "../storage");

    this.testPatterns = [
      "test",
      "Test",
      "TEST",
      "mock",
      "Mock",
      "cleanup",
      "Cleanup",
      "/path/to/",
      "/tmp/",
      "test-workspace",
    ];
  }

  /**
   * Clean upload logs from test entries
   */
  async cleanUploadLogs() {
    const logFiles = [
      path.join(this.basePath, "uploadlogs/document_uploads.log"),
      path.join(this.basePath, "uploadlogs/bulk_uploads.log"),
      path.join(this.basePath, "uploadlogs/error_uploads.log"),
    ];

    let totalCleaned = 0;

    for (const logFile of logFiles) {
      const cleaned = await this.cleanLogFile(logFile);
      totalCleaned += cleaned;
    }

    console.log(`✅ Cleaned ${totalCleaned} test entries from upload logs`);
    return totalCleaned;
  }

  /**
   * Clean a specific log file
   */
  async cleanLogFile(filePath) {
    if (!fs.existsSync(filePath)) {
      return 0;
    }

    try {
      const content = fs.readFileSync(filePath, "utf8");
      const lines = content.split("\n");
      const cleanedLines = [];
      let removedCount = 0;

      for (const line of lines) {
        if (!line.trim()) {
          cleanedLines.push(line);
          continue;
        }

        if (this.shouldRemoveLine(line)) {
          removedCount++;
        } else {
          cleanedLines.push(line);
        }
      }

      if (removedCount > 0) {
        // Backup original file
        const backupPath = `${filePath}.backup.${Date.now()}`;
        fs.copyFileSync(filePath, backupPath);

        // Write cleaned content
        fs.writeFileSync(filePath, cleanedLines.join("\n"));

        const envInfo = this.getEnvironmentInfo();
        console.log(
          `📝 ${envInfo} ${path.basename(filePath)}: Removed ${removedCount} test entries (backup: ${path.basename(backupPath)})`
        );
      } else {
        const envInfo = this.getEnvironmentInfo();
        console.log(
          `✨ ${envInfo} ${path.basename(filePath)}: No test entries found`
        );
      }

      return removedCount;
    } catch (error) {
      console.error(
        `❌ Failed to clean ${path.basename(filePath)}:`,
        error.message
      );
      return 0;
    }
  }

  /**
   * Check if a log line should be removed
   */
  shouldRemoveLine(line) {
    try {
      const entry = JSON.parse(line);

      // Check workspace name
      if (entry.workspaceName && this.matchesTestPattern(entry.workspaceName)) {
        return true;
      }

      // Check file name
      if (entry.fileName && this.matchesTestPattern(entry.fileName)) {
        return true;
      }

      // Check path
      if (entry.path && this.matchesTestPattern(entry.path)) {
        return true;
      }

      // Check for test markers
      if (entry.testRun || entry.isTest || entry.cleanup) {
        return true;
      }

      return false;
    } catch {
      // If not valid JSON, check for test patterns in the line itself
      return this.matchesTestPattern(line);
    }
  }

  /**
   * Check if text matches test patterns
   */
  matchesTestPattern(text) {
    return this.testPatterns.some((pattern) =>
      text.toLowerCase().includes(pattern.toLowerCase())
    );
  }

  /**
   * Get environment information for logging
   */
  getEnvironmentInfo() {
    if (this.isGitHubActions) return "[GitHub Actions]";
    if (this.isCIEnvironment) return "[CI]";
    return "[Dev]";
  }

  /**
   * Get statistics about log files
   */
  getStats() {
    const logFiles = [
      path.join(this.basePath, "uploadlogs/document_uploads.log"),
      path.join(this.basePath, "uploadlogs/bulk_uploads.log"),
      path.join(this.basePath, "uploadlogs/error_uploads.log"),
    ];

    const stats = {};

    for (const logFile of logFiles) {
      if (fs.existsSync(logFile)) {
        const content = fs.readFileSync(logFile, "utf8");
        const lines = content.split("\n").filter((line) => line.trim());

        let testEntries = 0;
        for (const line of lines) {
          if (this.shouldRemoveLine(line)) {
            testEntries++;
          }
        }

        stats[path.basename(logFile)] = {
          total: lines.length,
          testEntries,
          production: lines.length - testEntries,
        };
      } else {
        stats[path.basename(logFile)] = {
          total: 0,
          testEntries: 0,
          production: 0,
        };
      }
    }

    return stats;
  }

  /**
   * Display statistics
   */
  showStats() {
    const stats = this.getStats();

    console.log("\n📊 Upload Log Statistics:");
    console.log("═".repeat(50));

    for (const [filename, data] of Object.entries(stats)) {
      console.log(`${filename}:`);
      console.log(`  Total entries: ${data.total}`);
      console.log(`  Test entries: ${data.testEntries}`);
      console.log(`  Production entries: ${data.production}`);
      console.log("");
    }
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const cleaner = new TestLogCleaner();

  if (args.includes("--stats") || args.includes("-s")) {
    cleaner.showStats();
    return;
  }

  if (args.includes("--help") || args.includes("-h")) {
    console.log(`
Test Log Cleanup Script

Usage:
  node cleanup-test-logs.js [options]

Options:
  --stats, -s    Show statistics about log files
  --help, -h     Show this help message

Examples:
  node cleanup-test-logs.js         # Clean test entries from logs
  node cleanup-test-logs.js --stats # Show log statistics
`);
    return;
  }

  console.log("🧹 Starting test log cleanup...");
  console.log("");

  // Show stats before cleanup
  if (!args.includes("--quiet")) {
    cleaner.showStats();
  }

  // Perform cleanup
  const cleaned = await cleaner.cleanUploadLogs();

  if (cleaned === 0) {
    console.log("✨ No test entries found - logs are already clean!");
  } else {
    console.log("");
    console.log("🎉 Cleanup completed successfully!");

    // Show stats after cleanup
    if (!args.includes("--quiet")) {
      console.log("\nAfter cleanup:");
      cleaner.showStats();
    }
  }
}

// Run if called directly
if (require.main === module) {
  main().catch((error) => {
    console.error("❌ Cleanup failed:", error);
    process.exit(1);
  });
}

module.exports = { TestLogCleaner };
