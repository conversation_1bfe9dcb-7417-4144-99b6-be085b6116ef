#!/usr/bin/env node

const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");

console.log("🔍 Finding all TypeScript compilation errors...\n");

try {
  // Run TypeScript compiler in no-emit mode to get all errors
  execSync("npx tsc --noEmit", {
    encoding: "utf8",
    stdio: "pipe",
    cwd: path.join(__dirname, ".."),
  });

  console.log("✅ No TypeScript errors found!");
} catch (error) {
  const errorOutput = error.stdout + error.stderr;
  const lines = errorOutput.split("\n");

  // Parse errors by category
  const errors = {
    unusedVariables: [],
    missingImports: [],
    typeMismatches: [],
    duplicateIdentifiers: [],
    cannotFind: [],
    other: [],
  };

  let _currentError = null;

  lines.forEach((line) => {
    if (line.includes("error TS")) {
      _currentError = line;

      if (
        line.includes("TS6133") ||
        line.includes("is declared but its value is never read")
      ) {
        errors.unusedVariables.push(line);
      } else if (
        line.includes("TS2307") ||
        line.includes("Cannot find module")
      ) {
        errors.missingImports.push(line);
      } else if (
        line.includes("TS2322") ||
        (line.includes("Type") && line.includes("is not assignable"))
      ) {
        errors.typeMismatches.push(line);
      } else if (
        line.includes("TS2300") ||
        line.includes("Duplicate identifier")
      ) {
        errors.duplicateIdentifiers.push(line);
      } else if (line.includes("TS2304") || line.includes("Cannot find name")) {
        errors.cannotFind.push(line);
      } else {
        errors.other.push(line);
      }
    }
  });

  // Generate report
  console.log("📊 TypeScript Error Summary:");
  console.log("================================");
  console.log(`Unused Variables: ${errors.unusedVariables.length}`);
  console.log(`Missing Imports: ${errors.missingImports.length}`);
  console.log(`Type Mismatches: ${errors.typeMismatches.length}`);
  console.log(`Duplicate Identifiers: ${errors.duplicateIdentifiers.length}`);
  console.log(`Cannot Find Name: ${errors.cannotFind.length}`);
  console.log(`Other Errors: ${errors.other.length}`);
  console.log(
    `TOTAL: ${Object.values(errors).reduce((sum, arr) => sum + arr.length, 0)}`
  );
  console.log("================================\n");

  // Save detailed report
  const reportPath = path.join(__dirname, "ts-errors-report.json");
  fs.writeFileSync(reportPath, JSON.stringify(errors, null, 2));
  console.log(`📝 Detailed report saved to: ${reportPath}`);

  // Show sample errors from each category
  Object.entries(errors).forEach(([category, errorList]) => {
    if (errorList.length > 0) {
      console.log(`\n📌 Sample ${category} errors (first 3):`);
      errorList.slice(0, 3).forEach((err) => {
        console.log(`  - ${err.trim()}`);
      });
    }
  });
}
