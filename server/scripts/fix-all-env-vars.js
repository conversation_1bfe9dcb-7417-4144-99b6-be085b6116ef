#!/usr/bin/env node

const fs = require("fs");

const filesToFix = ["utils/agents/aibitat/providers/ai-provider.ts"];

console.log("🔄 Fixing all environment variable type issues...");

let fixedCount = 0;

filesToFix.forEach((filePath) => {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return;
    }

    let content = fs.readFileSync(filePath, "utf-8");
    let modified = false;

    // Fix environment variables that need to be string | undefined
    const envVarPatterns = [
      {
        from: /process\.env\.([A-Z_][A-Z0-9_]*) \?\? undefined/g,
        to: (match, envVar) => `process.env.${envVar} as string | undefined`,
      },
      {
        from: /process\.env\.([A-Z_][A-Z0-9_]*) \?\? "not-used"/g,
        to: (match, envVar) =>
          `process.env.${envVar} as string | undefined ?? "not-used"`,
      },
      {
        from: /process\.env\?\.([A-Z_][A-Z0-9_]*)\?\.replace\([^)]+\) \?\? undefined/g,
        to: (match, envVar) =>
          `(process.env.${envVar}?.replace(/\\/+$/, "") as string | undefined)`,
      },
    ];

    envVarPatterns.forEach(({ from, to }) => {
      if (from.test(content)) {
        content = content.replace(from, to);
        modified = true;
      }
    });

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`  ✅ Fixed environment variable types in: ${filePath}`);
      fixedCount++;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
});

console.log(`\n✅ Fixed environment variable types in ${fixedCount} files`);
