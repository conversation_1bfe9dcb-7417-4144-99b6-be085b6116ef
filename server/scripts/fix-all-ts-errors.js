#!/usr/bin/env node

const fs = require("fs");
const { execSync } = require("child_process");

console.log("🔧 Starting comprehensive TypeScript error fix...\n");

// Get all TypeScript errors
const errors = execSync("npx tsc --noEmit 2>&1 || true", { encoding: "utf-8" });

// Parse errors by type
const errorTypes = {
  unusedVariables: [],
  missingImports: [],
  returnTypes: [],
  nullSafety: [],
  typeMismatches: [],
  duplicateExports: [],
  anyTypes: [],
  other: [],
};

errors.split("\n").forEach((line) => {
  const match = line.match(/^(.+?)\((\d+),(\d+)\):\s*error\s+TS(\d+):\s*(.+)$/);
  if (!match) return;

  const [, file, lineNum, column, errorCode, message] = match;
  const error = {
    file,
    line: parseInt(lineNum),
    column: parseInt(column),
    code: errorCode,
    message,
  };

  if (errorCode === "6133") {
    errorTypes.unusedVariables.push(error);
  } else if (errorCode === "2307") {
    errorTypes.missingImports.push(error);
  } else if (errorCode === "2322" && message.includes("void")) {
    errorTypes.returnTypes.push(error);
  } else if (["18046", "18047", "18048", "2531", "2532"].includes(errorCode)) {
    errorTypes.nullSafety.push(error);
  } else if (["2322", "2345", "2339"].includes(errorCode)) {
    errorTypes.typeMismatches.push(error);
  } else if (errorCode === "2484") {
    errorTypes.duplicateExports.push(error);
  } else if (errorCode === "7006") {
    errorTypes.anyTypes.push(error);
  } else {
    errorTypes.other.push(error);
  }
});

console.log("📊 Error summary:");
Object.entries(errorTypes).forEach(([type, errors]) => {
  console.log(`  ${type}: ${errors.length}`);
});
console.log("");

// Fix unused variables
console.log("🧹 Fixing unused variables...");
const fileGroups = {};
errorTypes.unusedVariables.forEach((error) => {
  if (!fileGroups[error.file]) fileGroups[error.file] = [];
  fileGroups[error.file].push(error);
});

Object.entries(fileGroups).forEach(([filePath, errors]) => {
  if (!fs.existsSync(filePath)) return;

  let content = fs.readFileSync(filePath, "utf8");
  const lines = content.split("\n");
  let modified = false;

  // Sort errors by line number in reverse to avoid offset issues
  errors.sort((a, b) => b.line - a.line);

  errors.forEach((error) => {
    const lineIndex = error.line - 1;
    const line = lines[lineIndex];
    if (!line) return;

    // Extract variable name from error message
    const varMatch = error.message.match(/'([^']+)'/);
    if (!varMatch) return;

    const varName = varMatch[1];

    // Check if it's a destructured import
    if (
      line.includes("import") &&
      line.includes("{") &&
      line.includes(varName)
    ) {
      // Remove from import
      const newLine = line
        .replace(new RegExp(`\\b${varName}\\s*,?\\s*`, "g"), "")
        .replace(/,\s*}/, " }")
        .replace(/{\s*,/, "{ ")
        .replace(/,\s*,/, ", ")
        .replace(/{\s*}/, "{}");

      if (newLine !== line) {
        lines[lineIndex] = newLine;
        modified = true;
      }
    } else if (
      line.includes(`const ${varName}`) ||
      line.includes(`let ${varName}`) ||
      line.includes(`var ${varName}`) ||
      line.includes(`: ${varName}`)
    ) {
      // Prefix with underscore
      const newLine = line.replace(
        new RegExp(`\\b${varName}\\b`, "g"),
        `_${varName}`
      );
      if (newLine !== line) {
        lines[lineIndex] = newLine;
        modified = true;
      }
    }
  });

  if (modified) {
    fs.writeFileSync(filePath, lines.join("\n"));
    console.log(`  ✓ Fixed ${filePath}`);
  }
});

// Fix null safety issues
console.log("\n🛡️ Fixing null safety issues...");
const nullSafetyGroups = {};
errorTypes.nullSafety.forEach((error) => {
  if (!nullSafetyGroups[error.file]) nullSafetyGroups[error.file] = [];
  nullSafetyGroups[error.file].push(error);
});

Object.entries(nullSafetyGroups).forEach(([filePath, errors]) => {
  if (!fs.existsSync(filePath)) return;

  let content = fs.readFileSync(filePath, "utf8");
  let modified = false;

  // Fix common null safety patterns
  content = content.replace(/(\w+)\.(\w+)(?!\?)/g, (match, obj, prop) => {
    // Don't add ? if it's already there or if it's a known safe pattern
    if (
      match.includes("?.") ||
      obj === "console" ||
      obj === "process" ||
      obj === "Math" ||
      obj === "JSON" ||
      obj === "Array" ||
      obj === "Object"
    ) {
      return match;
    }

    // Check if this matches one of our error locations
    const hasError = errors.some((_e) => content.indexOf(match) > -1);
    if (hasError) {
      modified = true;
      return `${obj}?.${prop}`;
    }
    return match;
  });

  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`  ✓ Fixed ${filePath}`);
  }
});

// Fix duplicate exports
console.log("\n📦 Fixing duplicate exports...");
const duplicateExportGroups = {};
errorTypes.duplicateExports.forEach((error) => {
  if (!duplicateExportGroups[error.file])
    duplicateExportGroups[error.file] = [];
  duplicateExportGroups[error.file].push(error);
});

Object.entries(duplicateExportGroups).forEach(([filePath, _errors]) => {
  if (!fs.existsSync(filePath)) return;

  let content = fs.readFileSync(filePath, "utf8");
  let modified = false;

  // Find and rename duplicate exports
  const exportMatches =
    content.match(
      /export\s+(?:const|let|var|function|class|interface|type)\s+(\w+)/g
    ) || [];
  const exportNames = {};

  exportMatches.forEach((match) => {
    const nameMatch = match.match(
      /export\s+(?:const|let|var|function|class|interface|type)\s+(\w+)/
    );
    if (nameMatch) {
      const name = nameMatch[1];
      exportNames[name] = (exportNames[name] || 0) + 1;
    }
  });

  // Rename duplicates
  Object.entries(exportNames).forEach(([name, count]) => {
    if (count > 1) {
      let occurrenceCount = 0;
      content = content.replace(
        new RegExp(
          `(export\\s+(?:const|let|var|function|class|interface|type)\\s+)(${name})\\b`,
          "g"
        ),
        (match, prefix, exportName) => {
          occurrenceCount++;
          if (occurrenceCount > 1) {
            modified = true;
            return `${prefix}${exportName}_${occurrenceCount}`;
          }
          return match;
        }
      );
    }
  });

  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`  ✓ Fixed ${filePath}`);
  }
});

// Fix any type parameters
console.log("\n🎯 Fixing implicit any types...");
const anyTypeGroups = {};
errorTypes.anyTypes.forEach((error) => {
  if (!anyTypeGroups[error.file]) anyTypeGroups[error.file] = [];
  anyTypeGroups[error.file].push(error);
});

Object.entries(anyTypeGroups).forEach(([filePath, errors]) => {
  if (!fs.existsSync(filePath)) return;

  let content = fs.readFileSync(filePath, "utf8");
  let modified = false;

  // Fix function parameters without types
  content = content.replace(
    /(\w+)\s*\(\s*([a-zA-Z_$][\w$]*)\s*(?:,\s*([a-zA-Z_$][\w$]*)\s*)*\)\s*{/g,
    (match, funcName, param1, param2) => {
      // Skip if already has types
      if (match.includes(":")) return match;

      // Check if this is one of our error locations
      const hasError = errors.some((_e) => content.indexOf(match) > -1);
      if (!hasError) return match;

      modified = true;
      if (param2) {
        return `${funcName}(${param1}: any, ${param2}: any) {`;
      }
      return `${funcName}(${param1}: any) {`;
    }
  );

  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`  ✓ Fixed ${filePath}`);
  }
});

// Final count
const finalErrors = execSync(
  'npx tsc --noEmit 2>&1 | grep -c "error TS" || echo "0"',
  { encoding: "utf-8" }
);
console.log(`\n📊 Final TypeScript error count: ${finalErrors.trim()}`);

if (parseInt(finalErrors) > 0) {
  console.log("\n⚠️  Some errors remain. Running detailed analysis...");

  // Get remaining error types
  const remainingErrors = execSync(
    'npx tsc --noEmit 2>&1 | grep "error TS" | awk -F"error TS" "{print $2}" | cut -d":" -f1 | sort | uniq -c | sort -rn | head -10 || true',
    { encoding: "utf-8" }
  );
  console.log("\nTop remaining error types:");
  console.log(remainingErrors);
}
