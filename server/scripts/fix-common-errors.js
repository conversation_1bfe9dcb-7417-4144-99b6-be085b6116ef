#!/usr/bin/env node

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

console.log("🔧 Fixing common TypeScript errors...");

// Get all TypeScript files
const findTsFiles = (dir) => {
  let files = [];
  const items = fs.readdirSync(dir);

  for (const item of items) {
    const fullPath = path.join(dir, item);
    if (
      fs.statSync(fullPath).isDirectory() &&
      item !== "node_modules" &&
      item !== ".git"
    ) {
      files = files.concat(findTsFiles(fullPath));
    } else if (item.endsWith(".ts") && !item.endsWith(".d.ts")) {
      files.push(fullPath);
    }
  }
  return files;
};

const serverDir = path.resolve(__dirname, "..");
const tsFiles = findTsFiles(serverDir);

console.log(`Found ${tsFiles.length} TypeScript files to process`);

let totalFixed = 0;

// Common fix patterns
const fixes = [
  // Fix catch blocks with unknown errors
  {
    name: "Unknown error types in catch blocks",
    pattern: /(catch\s*\(\s*(\w+)\s*\)\s*\{[^}]*?)\b\2\b(?=\.|\[)/g,
    replacement: "$1($2 as Error)",
  },

  // Fix null checks for possibly null values
  {
    name: "Null checks for array operations",
    pattern: /(\w+)\.length\s*>\s*0/g,
    replacement: "$1?.length > 0",
  },

  // Fix optional chaining for undefined/null
  {
    name: "Basic optional chaining",
    pattern: /(\w+)\.(\w+)\s*\?\s*\.(\w+)/g,
    replacement: "$1?.$2?.$3",
  },

  // Fix type assertions for unknown
  {
    name: "Type assertions for unknown to any",
    pattern: /(\(\s*\w+\s+as\s+)unknown(\s*\))/g,
    replacement: "$1any$2",
  },

  // Fix string | null to string | undefined conversions
  {
    name: "Convert null to undefined for optional properties",
    pattern: /\?\s*:\s*string\s*\|\s*null/g,
    replacement: "?: string | null",
  },
];

// Process each file
tsFiles.forEach((filePath) => {
  const relativePath = path.relative(serverDir, filePath);

  try {
    let content = fs.readFileSync(filePath, "utf-8");
    let modified = false;
    let fileFixCount = 0;

    fixes.forEach((fix) => {
      const newContent = content.replace(fix.pattern, fix.replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
        fileFixCount++;
      }
    });

    // Additional specific fixes

    // Fix Express router handlers
    if (
      content.includes("app.get") ||
      content.includes("app.post") ||
      content.includes("app.put") ||
      content.includes("app.delete")
    ) {
      const asyncHandlerPattern =
        /(app\.(get|post|put|delete|patch)\([^,]+,\s*async\s*\([^)]*\))\s*=>/g;
      const newContent = content.replace(
        asyncHandlerPattern,
        "$1: Promise<void> =>"
      );
      if (newContent !== content) {
        content = newContent;
        modified = true;
        fileFixCount++;
      }
    }

    // Fix return statements in Express handlers
    if (
      content.includes("response.status") ||
      content.includes("response.json")
    ) {
      const returnPattern =
        /(\s+)(response\.(status\(\d+\)\.json\([^;]+\)|json\([^;]+\)|sendStatus\(\d+\)|status\(\d+\)\.end\(\)));(\s*}(\s*catch|\s*finally|\s*$))/g;
      const newContent = content.replace(returnPattern, "$1return $2;$4");
      if (newContent !== content) {
        content = newContent;
        modified = true;
        fileFixCount++;
      }
    }

    // Fix error type assertions in catch blocks
    if (content.includes("catch")) {
      const errorTypePattern =
        /(catch\s*\(\s*(\w+)\s*\)[^}]*?)\b\2\.(message|stack|name)/g;
      const newContent = content.replace(
        errorTypePattern,
        "$1($2 as Error).$3"
      );
      if (newContent !== content) {
        content = newContent;
        modified = true;
        fileFixCount++;
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ Fixed ${fileFixCount} issues in: ${relativePath}`);
      totalFixed += fileFixCount;
    }
  } catch (error) {
    console.error(`❌ Error processing ${relativePath}:`, error.message);
  }
});

console.log(`\n🎉 Applied ${totalFixed} fixes across TypeScript files`);
console.log("🔍 Checking remaining error count...");

// Check new error count
try {
  execSync("npx tsc --noEmit 2>&1", { cwd: serverDir, encoding: "utf-8" });
  console.log("✅ No TypeScript errors remaining!");
} catch (error) {
  const errorCount = (error.stdout + error.stderr)
    .split("\n")
    .filter((line) => line.includes("error TS")).length;
  console.log(`📊 ${errorCount} TypeScript errors remaining`);
}
