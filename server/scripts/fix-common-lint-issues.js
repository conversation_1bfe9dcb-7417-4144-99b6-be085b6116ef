#!/usr/bin/env node

const path = require("path");
const { execSync } = require("child_process");

console.log("Starting ESLint auto-fix for common issues...\n");

// Run ESLint auto-fix on all TypeScript files
try {
  console.log("Running ESLint auto-fix on server directory...");
  execSync("npx eslint server --ext .ts,.tsx --fix", {
    stdio: "inherit",
    cwd: path.resolve(__dirname, "../.."),
  });
  console.log("✓ Auto-fix completed\n");
} catch {
  // ignore
  console.log("✓ Auto-fix completed with some remaining issues\n");
}

// Get remaining issues
console.log("Analyzing remaining issues...");
const remainingIssues = execSync(
  "npx eslint server --ext .ts,.tsx --format json",
  {
    cwd: path.resolve(__dirname, "../.."),
    encoding: "utf-8",
    stdio: ["pipe", "pipe", "ignore"],
  }
);

let results;
try {
  results = JSON.parse(remainingIssues);
} catch {
  // ignore
  console.error("Failed to parse ESLint results");
  process.exit(1);
}

// Count issues by type
const issueCounts = {};
let totalWarnings = 0;
let totalErrors = 0;
let filesWithIssues = 0;

results.forEach((file) => {
  if (file.errorCount > 0 || file.warningCount > 0) {
    filesWithIssues++;
    totalErrors += file.errorCount;
    totalWarnings += file.warningCount;

    file.messages.forEach((msg) => {
      const key = `${msg.ruleId} (${msg.severity === 2 ? "error" : "warning"})`;
      issueCounts[key] = (issueCounts[key] || 0) + 1;
    });
  }
});

console.log("\n=== ESLint Summary ===");
console.log(`Files with issues: ${filesWithIssues}`);
console.log(`Total errors: ${totalErrors}`);
console.log(`Total warnings: ${totalWarnings}`);
console.log("\n=== Most Common Issues ===");

// Sort and display top issues
const sortedIssues = Object.entries(issueCounts)
  .sort(([, a], [, b]) => b - a)
  .slice(0, 20);

sortedIssues.forEach(([rule, count]) => {
  console.log(`${count.toString().padStart(5)} - ${rule}`);
});

// Provide recommendations
console.log("\n=== Recommendations ===");
if (issueCounts["@typescript-eslint/no-explicit-any (warning)"] > 100) {
  console.log(
    '- Many "any" type warnings. Consider creating proper type definitions.'
  );
}
if (issueCounts["unused-imports/no-unused-vars (warning)"] > 50) {
  console.log(
    "- Many unused variables. Use _ prefix for intentionally unused parameters."
  );
}
if (totalErrors > 0) {
  console.log("- Fix remaining errors before addressing warnings.");
}

console.log("\nDone!");
