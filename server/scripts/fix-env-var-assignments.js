#!/usr/bin/env node

const fs = require("fs");

const filesToFix = ["utils/agents/aibitat/providers/ai-provider.ts"];

console.log("🔄 Fixing environment variable assignments...");

let fixedCount = 0;

filesToFix.forEach((filePath) => {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return;
    }

    let content = fs.readFileSync(filePath, "utf-8");
    let modified = false;

    // Fix environment variable assignments from || to ??
    const envVarPattern = /process\.env\.[A-Z_][A-Z0-9_]* \|\| undefined/g;
    if (envVarPattern.test(content)) {
      content = content.replace(envVarPattern, (match) => {
        return match.replace(" || ", " ?? ");
      });
      modified = true;
    }

    // Fix number type assignments
    const numberPattern = /process\.env\.[A-Z_][A-Z0-9_]* \|\| \d+/g;
    if (numberPattern.test(content)) {
      content = content.replace(numberPattern, (match) => {
        return match.replace(" || ", " ?? ");
      });
      modified = true;
    }

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(
        `  ✅ Fixed environment variable assignments in: ${filePath}`
      );
      fixedCount++;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
});

console.log(
  `\n✅ Fixed environment variable assignments in ${fixedCount} files`
);
