#!/usr/bin/env node

const fs = require("fs");
const { execSync } = require("child_process");

console.log("🔧 Fixing Express route return types...\n");

// Get all TypeScript errors
const errors = execSync("npx tsc --noEmit 2>&1 || true", { encoding: "utf-8" });

// Parse errors for return type issues
const returnTypeErrors = errors
  .split("\n")
  .filter((line) => line.includes("is not assignable to type 'void'"))
  .map((line) => {
    const match = line.match(/^(.+?)\((\d+),(\d+)\):/);
    if (match) {
      return {
        file: match[1],
        line: parseInt(match[2]),
        column: parseInt(match[3]),
      };
    }
    return null;
  })
  .filter(Boolean);

// Group by file
const fileGroups = {};
returnTypeErrors.forEach((error) => {
  if (!fileGroups[error.file]) {
    fileGroups[error.file] = [];
  }
  fileGroups[error.file].push(error);
});

let fixedCount = 0;

// Fix each file
Object.entries(fileGroups).forEach(([filePath, _errors]) => {
  if (!fs.existsSync(filePath)) return;

  console.log(`📝 Processing: ${filePath}`);
  let content = fs.readFileSync(filePath, "utf8");
  let modified = false;

  // Fix async route handlers to return Promise<void>
  content = content.replace(
    /async\s*\(([^)]*Request[^)]*),\s*([^)]*Response[^)]*)\)(?:\s*:\s*Promise<[^>]+>)?\s*=>/g,
    (match, req, res) => {
      // Check if it already has a return type
      if (match.includes(": Promise<")) {
        return match;
      }
      modified = true;
      return `async (${req}, ${res}): Promise<void> =>`;
    }
  );

  // Fix function declarations
  content = content.replace(
    /async\s+function\s+\w+\s*\(([^)]*Request[^)]*),\s*([^)]*Response[^)]*)\)(?:\s*:\s*Promise<[^>]+>)?\s*{/g,
    (match, req, res) => {
      if (match.includes(": Promise<")) {
        return match;
      }
      modified = true;
      const funcName = match.match(/function\s+(\w+)/)?.[1] || "";
      return `async function ${funcName}(${req}, ${res}): Promise<void> {`;
    }
  );

  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`  ✓ Fixed return types`);
    fixedCount++;
  }
});

// Also fix common patterns in specific directories
const directories = ["endpoints", "routes"];
directories.forEach((dir) => {
  if (!fs.existsSync(dir)) return;

  const files = execSync(`find ${dir} -name "*.ts" -type f`, {
    encoding: "utf-8",
  })
    .split("\n")
    .filter(Boolean);

  files.forEach((filePath) => {
    if (!fs.existsSync(filePath)) return;

    let content = fs.readFileSync(filePath, "utf8");
    let modified = false;

    // Fix app.get, app.post, etc. with async handlers
    content = content.replace(
      /app\.(get|post|put|delete|patch)\s*\([^,]+,\s*(?:\[[^\]]+\],\s*)?async\s*\(([^)]*)\)(?:\s*:\s*Promise<[^>]+>)?\s*=>/g,
      (match, method, params) => {
        if (match.includes(": Promise<")) {
          return match;
        }
        modified = true;
        const middleware = match.match(/\[([^\]]+)\]/)?.[0] || "";
        const route = match.match(/\(([^,]+),/)?.[1] || "";
        return `app.${method}(${route}, ${middleware ? middleware + ", " : ""}async (${params}): Promise<void> =>`;
      }
    );

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`  ✓ Fixed ${filePath}`);
      fixedCount++;
    }
  });
});

console.log(`\n✅ Fixed return types in ${fixedCount} files`);
