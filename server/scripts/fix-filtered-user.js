#!/usr/bin/env node

const fs = require("fs");

const filesToFix = [
  "endpoints/userCustomSystemPrompt.ts",
  "endpoints/userPromptLibrary.ts",
];

console.log("🔄 Fixing FilteredUser to AuthenticatedUser assignments...");

let fixedCount = 0;

filesToFix.forEach((filePath) => {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return;
    }

    let content = fs.readFileSync(filePath, "utf-8");
    let modified = false;

    // Fix AuthenticatedUser type assignments with explicit type assertion
    const patterns = [
      {
        from: /const user: AuthenticatedUser \| null = await userFromSession\(\s*request,\s*response\s*\);/g,
        to: "const user = await userFromSession(request, response) as AuthenticatedUser | null;",
      },
      {
        from: /const user: AuthenticatedUser \| null = await userFromSession\(\s*request\s*\);/g,
        to: "const user = await userFromSession(request) as AuthenticatedUser | null;",
      },
    ];

    patterns.forEach(({ from, to }) => {
      if (from.test(content)) {
        content = content.replace(from, to);
        modified = true;
      }
    });

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`  ✅ Fixed FilteredUser assignments in: ${filePath}`);
      fixedCount++;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
});

console.log(`\n✅ Fixed FilteredUser assignments in ${fixedCount} files`);
