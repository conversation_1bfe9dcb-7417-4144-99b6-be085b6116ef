#!/usr/bin/env node

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

console.log("🔧 Fixing .js to .ts import path issues...\n");

// Get TypeScript errors
let _errors = [];
try {
  execSync("npx tsc --noEmit", { encoding: "utf8", stdio: "pipe" });
} catch (error) {
  const errorOutput = error.stdout + error.stderr;
  const _lines = errorOutput.split("\n");

  // Look for import errors where .js files don't exist but .ts files do
  // _errors = lines.filter(
  //   (line) =>
  //     line.includes("TS2307") ||
  //     line.includes("Cannot find module") ||
  //     line.includes(".js")
  // );
}

// Also scan all TS files for .js imports
const findTsFiles = (dir, fileList = []) => {
  const files = fs.readdirSync(dir);

  files.forEach((file) => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      // Skip node_modules and other directories
      if (
        !file.includes("node_modules") &&
        !file.includes(".git") &&
        !file.includes("dist")
      ) {
        findTsFiles(filePath, fileList);
      }
    } else if (file.endsWith(".ts") && !file.endsWith(".d.ts")) {
      fileList.push(filePath);
    }
  });

  return fileList;
};

const tsFiles = findTsFiles(process.cwd());
console.log(`Found ${tsFiles.length} TypeScript files to check\n`);

let totalFixed = 0;
let filesModified = 0;

// Process each TypeScript file
tsFiles.forEach((filePath) => {
  try {
    let content = fs.readFileSync(filePath, "utf8");
    const _originalContent = content;
    const lines = content.split("\n");
    let modified = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Check for imports/exports with .js extensions
      const importMatch = line.match(
        /^(import|export).*from\s+['"](\.\.?\/[^'"]+\.js)['"]/
      );
      if (importMatch) {
        const jsPath = importMatch[2];
        const basePath = jsPath.replace(/\.js$/, "");

        // Check if corresponding .ts file exists
        const absoluteDir = path.dirname(filePath);
        const tsFilePath = path.resolve(absoluteDir, basePath + ".ts");
        const indexTsPath = path.resolve(absoluteDir, basePath, "index.ts");

        if (fs.existsSync(tsFilePath) || fs.existsSync(indexTsPath)) {
          // Remove .js extension since TypeScript will resolve it
          lines[i] = line.replace(jsPath, basePath);
          modified = true;
          totalFixed++;
          console.log(
            `✅ Fixed .js import in ${path.relative(process.cwd(), filePath)}:${i + 1}`
          );
          console.log(`   ${jsPath} → ${basePath}`);
        }
      }

      // Also check for dynamic imports
      const dynamicImportMatch = line.match(
        /import\(['"](\.\.?\/[^'"]+\.js)['"]\)/
      );
      if (dynamicImportMatch) {
        const jsPath = dynamicImportMatch[1];
        const basePath = jsPath.replace(/\.js$/, "");

        // Check if corresponding .ts file exists
        const absoluteDir = path.dirname(filePath);
        const tsFilePath = path.resolve(absoluteDir, basePath + ".ts");
        const indexTsPath = path.resolve(absoluteDir, basePath, "index.ts");

        if (fs.existsSync(tsFilePath) || fs.existsSync(indexTsPath)) {
          lines[i] = line.replace(jsPath, basePath);
          modified = true;
          totalFixed++;
          console.log(
            `✅ Fixed dynamic import in ${path.relative(process.cwd(), filePath)}:${i + 1}`
          );
          console.log(`   ${jsPath} → ${basePath}`);
        }
      }

      // Check for require statements with .js
      const requireMatch = line.match(/require\(['"](\.\.?\/[^'"]+\.js)['"]\)/);
      if (requireMatch) {
        const jsPath = requireMatch[1];
        const basePath = jsPath.replace(/\.js$/, "");

        // Check if corresponding .ts file exists
        const absoluteDir = path.dirname(filePath);
        const tsFilePath = path.resolve(absoluteDir, basePath + ".ts");
        const indexTsPath = path.resolve(absoluteDir, basePath, "index.ts");

        if (fs.existsSync(tsFilePath) || fs.existsSync(indexTsPath)) {
          lines[i] = line.replace(jsPath, basePath);
          modified = true;
          totalFixed++;
          console.log(
            `✅ Fixed require in ${path.relative(process.cwd(), filePath)}:${i + 1}`
          );
          console.log(`   ${jsPath} → ${basePath}`);
        }
      }
    }

    if (modified) {
      content = lines.join("\n");
      fs.writeFileSync(filePath, content, "utf8");
      filesModified++;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
});

console.log(
  `\n🎉 Fixed ${totalFixed} .js import references in ${filesModified} files!`
);

// Re-run TypeScript to see remaining errors
console.log("\n📊 Checking remaining TypeScript errors...");
try {
  execSync("npx tsc --noEmit", { encoding: "utf8", stdio: "pipe" });
  console.log("✅ No TypeScript errors remaining!");
} catch (error) {
  const errorOutput = error.stdout + error.stderr;
  const remainingJsImports = (errorOutput.match(/\.js['"]/g) || []).length;
  const remainingModuleErrors = (errorOutput.match(/TS2307/g) || []).length;
  console.log(
    `⚠️  ${remainingJsImports} .js import references still remaining.`
  );
  console.log(
    `⚠️  ${remainingModuleErrors} missing module errors still remaining.`
  );
}
