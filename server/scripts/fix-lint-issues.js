#!/usr/bin/env node

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

// Get all TypeScript files
function getAllTsFiles(dir) {
  const files = [];
  const items = fs.readdirSync(dir);

  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);

    if (
      stat.isDirectory() &&
      !item.startsWith(".") &&
      item !== "node_modules"
    ) {
      files.push(...getAllTsFiles(fullPath));
    } else if (item.endsWith(".ts") || item.endsWith(".tsx")) {
      files.push(fullPath);
    }
  }

  return files;
}

// Fix common issues
function fixCommonIssues(content) {
  let fixed = content;

  // Fix unused variables by removing them (if they're just declarations)
  // This is a simple approach for variables that are just assigned but never used
  fixed = fixed.replace(
    /\n\s*const\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*[^;]+;\s*(?=\n)/g,
    (match, varName) => {
      // Keep the line if the variable is used later
      const afterMatch = fixed.substring(fixed.indexOf(match) + match.length);
      if (afterMatch.includes(varName)) {
        return match;
      }
      return "\n";
    }
  );

  // Fix let to const where appropriate
  fixed = fixed.replace(
    /\n(\s*)let\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*([^;]+);/g,
    (match, indent, varName, value) => {
      // Check if variable is reassigned later
      const afterMatch = fixed.substring(fixed.indexOf(match) + match.length);
      if (
        afterMatch.includes(`${varName} =`) ||
        afterMatch.includes(`${varName}++`) ||
        afterMatch.includes(`${varName}--`)
      ) {
        return match;
      }
      return `\n${indent}const ${varName} = ${value};`;
    }
  );

  // Fix empty object types {} to object
  fixed = fixed.replace(/:\s*\{\}/g, ": object");

  return fixed;
}

// Process files
const serverDir = path.join(__dirname, "..");
const tsFiles = getAllTsFiles(path.join(serverDir, "endpoints"))
  .concat(getAllTsFiles(path.join(serverDir, "models")))
  .concat(getAllTsFiles(path.join(serverDir, "utils")));

console.log(`Processing ${tsFiles.length} TypeScript files...`);

let fixedCount = 0;
for (const file of tsFiles) {
  try {
    const content = fs.readFileSync(file, "utf8");
    const fixed = fixCommonIssues(content);

    if (fixed !== content) {
      fs.writeFileSync(file, fixed);
      fixedCount++;
      console.log(`Fixed: ${file}`);
    }
  } catch (error) {
    console.error(`Error processing ${file}:`, error.message);
  }
}

console.log(`\nFixed ${fixedCount} files.`);
console.log("Running prettier to format the changes...");

try {
  execSync("npx prettier --write ./endpoints ./models ./utils", {
    cwd: serverDir,
  });
  console.log("Prettier formatting completed.");
} catch (error) {
  console.error("Prettier formatting failed:", error.message);
}
