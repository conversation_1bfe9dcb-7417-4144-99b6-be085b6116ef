#!/usr/bin/env node

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

console.log("🔧 Fixing missing import errors...\n");

// Get TypeScript errors
let errors;
try {
  execSync("npx tsc --noEmit", { encoding: "utf8", stdio: "pipe" });
} catch (error) {
  const errorOutput = error.stdout + error.stderr;
  const lines = errorOutput.split("\n");

  errors = lines.filter(
    (line) => line.includes("TS2307") || line.includes("Cannot find module")
  );
}

if (!errors || errors.length === 0) {
  console.log("✅ No missing import errors found!");
  process.exit(0);
}

console.log(`Found ${errors.length} missing import errors to fix.\n`);

// Common fixes for known patterns
const importFixes = {
  // Add .js extension fixes
  "../../../utils/middleware/auth":
    "../../../utils/middleware/multiUserProtected",
  "node:abort-controller": "abort-controller",
  "../../../types/agents": "../../../types/chat-agent",

  // Add more specific fixes as needed
};

// Parse errors and group by file
const errorsByFile = {};
errors.forEach((error) => {
  const match = error.match(/(.+?)\((\d+),(\d+)\).*Cannot find module '(.+?)'/);
  if (match) {
    const [, filePath, line, column, modulePath] = match;
    if (!errorsByFile[filePath]) {
      errorsByFile[filePath] = [];
    }
    errorsByFile[filePath].push({
      line: parseInt(line),
      column: parseInt(column),
      modulePath: modulePath,
    });
  }
});

// Fix each file
let totalFixed = 0;
Object.entries(errorsByFile).forEach(([filePath, fileErrors]) => {
  const fullPath = path.join(process.cwd(), filePath);

  if (!fs.existsSync(fullPath)) {
    console.log(`⚠️  Skipping ${filePath} - file not found`);
    return;
  }

  let content = fs.readFileSync(fullPath, "utf8");
  const lines = content.split("\n");

  // Sort errors by line number in reverse order
  fileErrors.sort((a, b) => b.line - a.line);

  fileErrors.forEach((error) => {
    const lineIndex = error.line - 1;
    if (lineIndex >= 0 && lineIndex < lines.length) {
      const line = lines[lineIndex];
      const modulePath = error.modulePath;

      // Check if we have a known fix
      if (importFixes[modulePath]) {
        lines[lineIndex] = line.replace(modulePath, importFixes[modulePath]);
        totalFixed++;
        console.log(
          `✅ Fixed import: ${modulePath} → ${importFixes[modulePath]}`
        );
      }
      // Try adding .js extension if it's a relative path
      else if (
        modulePath.startsWith(".") &&
        !modulePath.endsWith(".js") &&
        !modulePath.endsWith(".ts")
      ) {
        // Check if .ts file exists
        const baseDir = path.dirname(fullPath);
        const tsPath = path.resolve(baseDir, modulePath + ".ts");
        const jsPath = path.resolve(baseDir, modulePath + ".js");
        const indexTsPath = path.resolve(baseDir, modulePath, "index.ts");
        const indexJsPath = path.resolve(baseDir, modulePath, "index.js");

        if (fs.existsSync(tsPath)) {
          // Don't add extension for .ts files in imports
          console.log(
            `ℹ️  TypeScript file exists: ${modulePath}.ts - no fix needed`
          );
        } else if (fs.existsSync(jsPath)) {
          lines[lineIndex] = line.replace(modulePath, modulePath + ".js");
          totalFixed++;
          console.log(
            `✅ Added .js extension: ${modulePath} → ${modulePath}.js`
          );
        } else if (fs.existsSync(indexTsPath) || fs.existsSync(indexJsPath)) {
          // Directory with index file - no change needed
          console.log(
            `ℹ️  Directory with index file: ${modulePath} - no fix needed`
          );
        } else {
          console.log(
            `⚠️  Could not fix import: ${modulePath} in ${filePath}:${error.line}`
          );
          console.log(`   Checked paths: ${tsPath}, ${jsPath}, ${indexTsPath}`);
        }
      } else {
        console.log(
          `⚠️  Unknown import pattern: ${modulePath} in ${filePath}:${error.line}`
        );
      }
    }
  });

  // Write back the fixed content
  content = lines.join("\n");
  fs.writeFileSync(fullPath, content, "utf8");
  if (totalFixed > 0) {
    console.log(`✅ Fixed imports in ${filePath}`);
  }
});

console.log(`\n🎉 Fixed ${totalFixed} missing import errors!`);

// Re-run TypeScript to see remaining errors
console.log("\n📊 Checking remaining TypeScript errors...");
try {
  execSync("npx tsc --noEmit", { encoding: "utf8", stdio: "pipe" });
  console.log("✅ No TypeScript errors remaining!");
} catch (error) {
  const errorOutput = error.stdout + error.stderr;
  const remainingImports = (errorOutput.match(/TS2307/g) || []).length;
  console.log(`⚠️  ${remainingImports} missing import errors still remaining.`);
}
