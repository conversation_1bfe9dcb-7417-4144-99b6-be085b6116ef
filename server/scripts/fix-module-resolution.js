#!/usr/bin/env node

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

console.log("🔧 Fixing module resolution and missing type errors...");

// Get module resolution errors
let tscOutput;
try {
  tscOutput = execSync("npx tsc --noEmit 2>&1", {
    cwd: __dirname + "/..",
    encoding: "utf-8",
  });
} catch (error) {
  tscOutput = error.stdout + error.stderr;
}

const moduleErrors = tscOutput.split("\n").filter(
  (line) =>
    line.includes("error TS7016") || // Could not find declaration file
    line.includes("error TS2614") || // No exported member
    line.includes("error TS1192") || // No default export
    line.includes("error TS2305") || // Module has no exported member
    line.includes("error TS2307") // Cannot find module
);

console.log(`Found ${moduleErrors.length} module resolution errors`);

// Install missing type packages
const missingTypes = ["@types/ws", "@types/multer", "@types/mime"];

for (const pkg of missingTypes) {
  try {
    console.log(`📦 Installing ${pkg}...`);
    execSync(`npm install --save-dev ${pkg}`, {
      cwd: __dirname + "/..",
      stdio: "inherit",
    });
    console.log(`✅ Installed ${pkg}`);
  } catch (error) {
    console.log(`⚠️  Failed to install ${pkg}:`, error.message);
  }
}

// Common import fixes
const fixes = [
  {
    pattern:
      /import\s+(\w+)\s+from\s+["']([^"']+)["'];?\s*\/\/.*has no default export/g,
    replacement: (match, importName, modulePath) => {
      return `import { ${importName} } from "${modulePath}";`;
    },
  },
  {
    pattern:
      /import\s*{\s*SystemSettings\s*}\s+from\s+["']([^"']*systemSettings)["'];/g,
    replacement: (match, modulePath) => {
      return `import SystemSettings from "${modulePath}";`;
    },
  },
];

// Process files with import issues
moduleErrors.forEach((errorLine) => {
  const fileMatch = errorLine.match(/^(.+?\.ts)\(/);
  if (!fileMatch) return;

  const filePath = path.resolve(__dirname + "/..", fileMatch[1]);
  if (!fs.existsSync(filePath)) return;

  try {
    let content = fs.readFileSync(filePath, "utf-8");
    let modified = false;

    // Apply fixes
    fixes.forEach((fix) => {
      const newContent = content.replace(fix.pattern, fix.replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    });

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ Fixed imports in: ${fileMatch[1]}`);
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
});

console.log("\n🎯 Module resolution fixes completed!");
