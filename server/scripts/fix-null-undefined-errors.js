#!/usr/bin/env node

const { execSync } = require("child_process");

// Get all TypeScript null/undefined errors
const getErrors = () => {
  try {
    const output = execSync('npx tsc --noEmit 2>&1 | grep -E "TS1804[678]"', {
      encoding: "utf8",
    });
    return output.split("\n").filter((line) => line.trim());
  } catch (error) {
    return error.stdout?.split("\n").filter((line) => line.trim()) || [];
  }
};

// Parse error line
const parseError = (errorLine) => {
  const match = errorLine.match(
    /^(.+?)\((\d+),(\d+)\): error (TS\d+): '(.+?)' is possibly '(.+?)'\./
  );
  if (!match) return null;

  return {
    file: match[1],
    line: parseInt(match[2]),
    column: parseInt(match[3]),
    code: match[4],
    variable: match[5],
    type: match[6],
  };
};

// Group errors by file
const groupErrorsByFile = (errors) => {
  const grouped = {};
  errors.forEach((error) => {
    const parsed = parseError(error);
    if (parsed) {
      if (!grouped[parsed.file]) {
        grouped[parsed.file] = [];
      }
      grouped[parsed.file].push(parsed);
    }
  });
  return grouped;
};

// Main function
const main = () => {
  console.log("Fetching TypeScript null/undefined errors...");
  const errors = getErrors();
  console.log(`Found ${errors.length} null/undefined errors`);

  const groupedErrors = groupErrorsByFile(errors);
  const files = Object.keys(groupedErrors);

  console.log(`\nErrors found in ${files.length} files:`);
  files.forEach((file) => {
    console.log(`  ${file}: ${groupedErrors[file].length} errors`);
  });

  // Output summary for manual review
  console.log("\n\nTop 10 most common error patterns:");
  const patterns = {};

  errors.forEach((error) => {
    const parsed = parseError(error);
    if (parsed) {
      const pattern = `${parsed.variable} is possibly ${parsed.type}`;
      patterns[pattern] = (patterns[pattern] || 0) + 1;
    }
  });

  Object.entries(patterns)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .forEach(([pattern, count]) => {
      console.log(`  ${pattern}: ${count} occurrences`);
    });
};

main();
