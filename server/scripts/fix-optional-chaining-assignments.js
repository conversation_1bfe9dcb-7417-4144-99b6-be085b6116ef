#!/usr/bin/env node

const fs = require("fs");
const path = require("path");
const glob = require("glob");

console.log("🔍 Searching for optional chaining assignment syntax...\n");

// Pattern to match optional chaining assignments
const optionalChainingAssignmentPattern = /(\w+)\?\.([\w[\]]+)\s*=\s*/g;

// Find all TypeScript files
const tsFiles = glob.sync("**/*.ts", {
  ignore: ["**/node_modules/**", "**/dist/**", "**/build/**"],
  cwd: path.resolve(__dirname, ".."),
});

let totalFiles = 0;
let totalFixes = 0;

tsFiles.forEach((file) => {
  const filePath = path.resolve(__dirname, "..", file);
  const content = fs.readFileSync(filePath, "utf8");

  // Check if file contains optional chaining assignments
  const matches = [...content.matchAll(optionalChainingAssignmentPattern)];

  if (matches.length > 0) {
    totalFiles++;
    console.log(`\n📄 ${file}`);

    let updatedContent = content;
    let fixes = 0;

    // Replace each match
    matches.forEach((match) => {
      const fullMatch = match[0];
      const objectName = match[1];
      const propertyName = match[2];
      const lineNum = content.substring(0, match.index).split("\n").length;

      // Skip if it's inside a string or comment
      const lineStart = content.lastIndexOf("\n", match.index) + 1;
      const lineEnd = content.indexOf("\n", match.index);
      const line = content.substring(
        lineStart,
        lineEnd === -1 ? undefined : lineEnd
      );

      // Simple check for strings and comments (not perfect but handles most cases)
      if (line.includes("//") && line.indexOf("//") < line.indexOf(fullMatch)) {
        return; // Skip if it's in a comment
      }
      if (line.includes('"') || line.includes("'") || line.includes("`")) {
        // More complex check needed for strings, skip for now if uncertain
        const beforeMatch = line.substring(0, line.indexOf(fullMatch));
        const quotesCount = (beforeMatch.match(/["'`]/g) || []).length;
        if (quotesCount % 2 !== 0) {
          return; // Skip if inside a string
        }
      }

      console.log(`   Line ${lineNum}: ${objectName}?.${propertyName} = ...`);

      // For 'this' object, we can safely remove the optional chaining
      if (objectName === "this") {
        const replacement = `${objectName}.${propertyName} = `;
        updatedContent = updatedContent.replace(fullMatch, replacement);
        console.log(`   ✅ Fixed: ${objectName}.${propertyName} = ...`);
        fixes++;
      } else {
        // For other objects, we need to add a conditional check
        console.log(
          `   ⚠️  Needs manual review: ${objectName}?.${propertyName} = ...`
        );
        console.log(
          `      Consider using: if (${objectName}) { ${objectName}.${propertyName} = ... }`
        );
      }
    });

    if (fixes > 0) {
      fs.writeFileSync(filePath, updatedContent, "utf8");
      console.log(`   💾 Saved ${fixes} fix(es)`);
      totalFixes += fixes;
    }
  }
});

console.log("\n" + "=".repeat(60));
console.log(
  `✨ Completed! Fixed ${totalFixes} optional chaining assignments in ${totalFiles} files.`
);

if (totalFixes === 0) {
  console.log(
    "No automatic fixes were applied. Check the warnings above for manual fixes needed."
  );
}
