#!/usr/bin/env node

const fs = require("fs");
const { execSync } = require("child_process");

console.log("🔄 Fixing overload errors systematically...");

// Get all overload errors
let tscOutput;
try {
  tscOutput = execSync("npx tsc --noEmit 2>&1", {
    encoding: "utf-8",
  });
} catch (error) {
  tscOutput = error.stdout + error.stderr;
}

const overloadErrors = tscOutput
  .split("\n")
  .filter((line) => line.includes("No overload matches this call"))
  .map((line) => {
    const match = line.match(/^(.+?):/);
    return match ? match[1] : null;
  })
  .filter(Boolean);

console.log(`Found ${overloadErrors.length} overload errors`);

// Group by file
const fileGroups = {};
overloadErrors.forEach((error) => {
  if (!fileGroups[error]) {
    fileGroups[error] = true;
  }
});

let fixedCount = 0;

function fixOverloadInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, "utf-8");
    let modified = false;

    // Fix common overload patterns
    const patterns = [
      // Express route handlers without Promise<void>
      {
        pattern: /async \(([^)]+)\) => \{/g,
        replacement: (match, params) => {
          if (params.includes("Request") && params.includes("Response")) {
            return `async (${params}): Promise<void> => {`;
          }
          return match;
        },
      },
      // Router handlers without Promise<void>
      {
        pattern:
          /router\.(get|post|put|delete)\([^,]+,\s*([^,]+,\s*)?async \(([^)]+)\) => \{/g,
        replacement: (match, method, middleware, params) => {
          if (params.includes("req") && params.includes("res")) {
            const cleanParams = params
              .replace(/Request<[^>]*>/g, "Request")
              .replace(/Response<[^>]*>/g, "Response");
            return match
              .replace(params, cleanParams)
              .replace(") => {", "): Promise<void> => {");
          }
          return match;
        },
      },
    ];

    patterns.forEach(({ pattern, replacement }) => {
      if (typeof replacement === "function") {
        const newContent = content.replace(pattern, replacement);
        if (newContent !== content) {
          content = newContent;
          modified = true;
        }
      } else {
        const newContent = content.replace(pattern, replacement);
        if (newContent !== content) {
          content = newContent;
          modified = true;
        }
      }
    });

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`  ✅ Fixed overloads in: ${filePath}`);
      fixedCount++;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
}

Object.keys(fileGroups).forEach(fixOverloadInFile);

console.log(`\n✅ Fixed overloads in ${fixedCount} files`);
