#!/usr/bin/env node

const fs = require("fs");

const providerFiles = [
  "utils/agents/aibitat/providers/azure.ts",
  "utils/agents/aibitat/providers/bedrock.ts",
  "utils/agents/aibitat/providers/deepseek.ts",
  "utils/agents/aibitat/providers/fireworksai.ts",
  "utils/agents/aibitat/providers/groq.ts",
  "utils/agents/aibitat/providers/koboldcpp.ts",
  "utils/agents/aibitat/providers/lmstudio.ts",
  "utils/agents/aibitat/providers/localai.ts",
  "utils/agents/aibitat/providers/mistral.ts",
  "utils/agents/aibitat/providers/ollama.ts",
  "utils/agents/aibitat/providers/openrouter.ts",
  "utils/agents/aibitat/providers/perplexity.ts",
  "utils/agents/aibitat/providers/textgenwebui.ts",
  "utils/agents/aibitat/providers/togetherai.ts",
  "utils/agents/aibitat/providers/xai.ts",
];

console.log("🔄 Fixing provider type errors...");

let fixedCount = 0;

providerFiles.forEach((filePath) => {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return;
    }

    let content = fs.readFileSync(filePath, "utf-8");
    let modified = false;

    // Fix import statements to use agents types
    const oldImport = /from "\.\.\/\.\.\/\.\.\/\.\.\/types\/chat-agent"/g;
    if (oldImport.test(content)) {
      content = content.replace(oldImport, 'from "../../../../types/agents"');
      modified = true;
      console.log(`  ✅ Fixed imports in: ${filePath}`);
    }

    // Fix specific import patterns
    const patterns = [
      {
        // Fix ChatMessage import from chat-agent to agents
        from: /import type \{ ([^}]*ChatMessage[^}]*) \} from "\.\.\/\.\.\/\.\.\/\.\.\/types\/chat-agent"/g,
        to: 'import type { $1 } from "../../../../types/agents"',
      },
      {
        // Fix missing optional chaining for content assignments
        from: /(\s+)([a-zA-Z]+)\.content = ([^;]+);(\s+)/g,
        to: '$1$2.content = $3 || "";$4',
      },
      {
        // Fix null assignments to number types
        from: /(\s+)this\.maxTokens = ([^;]+);/g,
        to: "$1this.maxTokens = $2 || 1024;",
      },
      {
        // Fix functionCall method access issues by adding type assertion
        from: /(\s+)const result = \(await this\?\.functionCall\(/g,
        to: "$1const result = (await (this as any)?.functionCall(",
      },
      {
        // Fix deduplicator property access
        from: /(\s+)this\?\.deduplicator\./g,
        to: "$1(this as any)?.deduplicator.",
      },
      {
        // Fix cleanMsgs method access
        from: /(\s+)this\?\.cleanMsgs\(/g,
        to: "$1(this as any)?.cleanMsgs(",
      },
      {
        // Fix providerLog method access in conditionals
        from: /(\s+)\(this as any\)\.providerLog\(/g,
        to: "$1(this as any).providerLog(",
      },
      {
        // Fix string | null to string | undefined issues
        from: /(\s+)content: ([a-zA-Z]+)\.content/g,
        to: '$1content: $2.content || ""',
      },
    ];

    patterns.forEach(({ from, to }) => {
      if (from.test(content)) {
        content = content.replace(from, to);
        modified = true;
      }
    });

    // Additional specific fixes for content type issues
    const contentFixes = [
      {
        // Fix ChatCompletionMessage content assignment
        from: /completion = response\?\.choices\[0\]\.message;/g,
        to: 'completion = { content: response?.choices[0].message?.content || "" };',
      },
      {
        // Fix completion content access
        from: /result: completion\?\.content \|\| "",/g,
        to: 'result: completion?.content || "",',
      },
    ];

    contentFixes.forEach(({ from, to }) => {
      if (from.test(content)) {
        content = content.replace(from, to);
        modified = true;
      }
    });

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`  ✅ Fixed provider types in: ${filePath}`);
      fixedCount++;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
});

console.log(`\n✅ Fixed provider types in ${fixedCount} files`);
