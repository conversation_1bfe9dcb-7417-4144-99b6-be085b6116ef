#!/usr/bin/env node

const fs = require("fs");
const path = require("path");

// List of files with incorrect import paths based on the TypeScript errors
const fixMap = {
  "utils/agents/aibitat/plugins/index.ts": {
    old: 'import type { AgentPlugin } from "../../../types/chat-agent";',
    new: 'import type { AgentPlugin } from "../../../../types/chat-agent";',
  },
  "utils/agents/aibitat/plugins/memory.ts": {
    old: 'import type { AIbitat, AgentPlugin } from "../../../types/chat-agent";',
    new: 'import type { AIbitat, AgentPlugin } from "../../../../types/chat-agent";',
  },
  "utils/agents/aibitat/plugins/rechart.ts": {
    old: 'import type { AIbitat, AgentPlugin } from "../../../types/chat-agent";',
    new: 'import type { AIbitat, AgentPlugin } from "../../../../types/chat-agent";',
  },
  "utils/agents/aibitat/plugins/save-file-browser.ts": {
    old: 'import type { AIbitat, AgentPlugin } from "../../../types/chat-agent";',
    new: 'import type { AIbitat, AgentPlugin } from "../../../../types/chat-agent";',
  },
  "utils/agents/aibitat/plugins/summarize.ts": {
    old: 'import type { AIbitat, AgentPlugin } from "../../../types/chat-agent";',
    new: 'import type { AIbitat, AgentPlugin } from "../../../../types/chat-agent";',
  },
  "utils/agents/aibitat/plugins/web-browsing.ts": {
    old: 'import type { AIbitat, AgentPlugin } from "../../../types/chat-agent";',
    new: 'import type { AIbitat, AgentPlugin } from "../../../../types/chat-agent";',
  },
  "utils/agents/aibitat/plugins/web-scraping.ts": {
    old: 'import type { AIbitat, AgentPlugin } from "../../../types/chat-agent";',
    new: 'import type { AIbitat, AgentPlugin } from "../../../../types/chat-agent";',
  },
  "utils/agents/aibitat/plugins/websocket.ts": {
    old: 'import type { AIbitat, AgentPlugin } from "../../../types/chat-agent";',
    new: 'import type { AIbitat, AgentPlugin } from "../../../../types/chat-agent";',
  },
  "utils/chats/flows/configurations/mainDocFlowConfig.ts": {
    old: 'import { FlowConfiguration } from "../../../types/chat-agent";',
    new: 'import { FlowConfiguration } from "../../../../types/chat-flow";',
  },
  "utils/chats/flows/configurations/noMainDocFlowConfig.ts": {
    old: 'import { FlowConfiguration } from "../../../types/chat-agent";',
    new: 'import { FlowConfiguration } from "../../../../types/chat-flow";',
  },
  "utils/chats/flows/configurations/referenceFlowConfig.ts": {
    old: 'import { FlowConfiguration } from "../../../types/chat-agent";',
    new: 'import { FlowConfiguration } from "../../../../types/chat-flow";',
  },
  "utils/chats/flows/core/WorkspaceManager.ts": {
    old: 'import type { WorkspaceDocumentWithPagination } from "../../../types/models";',
    new: 'import type { WorkspaceDocumentWithPagination } from "../../../../types/document";',
  },
  "utils/chats/flows/demo/modularFlowDemo.ts": {
    old: 'import { FlowContext } from "../../../types/chat-agent";',
    new: 'import { FlowContext } from "../../../../types/chat-flow";',
  },
};

console.log("🔧 Fixing remaining import path issues...\n");

let fixedCount = 0;
let errorCount = 0;

Object.entries(fixMap).forEach(([relPath, fix]) => {
  const fullPath = path.join(process.cwd(), relPath);

  if (!fs.existsSync(fullPath)) {
    console.log(`⚠️  File not found: ${relPath}`);
    errorCount++;
    return;
  }

  try {
    let content = fs.readFileSync(fullPath, "utf8");

    if (content.includes(fix.old)) {
      content = content.replace(fix.old, fix.new);
      fs.writeFileSync(fullPath, content, "utf8");
      console.log(`✅ Fixed import in ${relPath}`);
      fixedCount++;
    } else {
      // Try to find similar patterns
      const lines = content.split("\n");
      let found = false;

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        if (
          line.includes("types/chat-agent") ||
          line.includes("types/agents") ||
          line.includes("types/models")
        ) {
          console.log(
            `ℹ️  Found import on line ${i + 1} in ${relPath}: ${line.trim()}`
          );
          found = true;
        }
      }

      if (!found) {
        console.log(`⚠️  Import pattern not found in ${relPath}`);
      }
      errorCount++;
    }
  } catch (error) {
    console.error(`❌ Error processing ${relPath}:`, error.message);
    errorCount++;
  }
});

console.log(
  `\n📊 Summary: ${fixedCount} files fixed, ${errorCount} files with issues`
);

// Additionally, let's fix the SystemSettings import issue
const sqlAgentPath =
  "utils/agents/aibitat/plugins/sql-agent/SQLConnectors/index.ts";
const fullSqlPath = path.join(process.cwd(), sqlAgentPath);

if (fs.existsSync(fullSqlPath)) {
  try {
    let content = fs.readFileSync(fullSqlPath, "utf8");
    const oldImport =
      'import { SystemSettings } from "../../../../../../models/systemSettings";';
    const newImport =
      'import SystemSettings from "../../../../../../models/systemSettings";';

    if (content.includes(oldImport)) {
      content = content.replace(oldImport, newImport);
      fs.writeFileSync(fullSqlPath, content, "utf8");
      console.log(`\n✅ Fixed SystemSettings import in ${sqlAgentPath}`);
    }
  } catch (error) {
    console.error(`❌ Error fixing SystemSettings import:`, error.message);
  }
}
