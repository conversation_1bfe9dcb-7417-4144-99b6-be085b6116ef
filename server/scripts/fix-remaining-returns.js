#!/usr/bin/env node

const fs = require("fs");

console.log("🔄 Fixing remaining return statements in openai endpoint...");

const filePath = "endpoints/api/openai/index.ts";

try {
  let content = fs.readFileSync(filePath, "utf-8");
  let modified = false;

  // Fix specific return statements that were missed
  const patterns = [
    // Line 87: return response?.status(200).json({ data } as OpenAIModelsResponse);
    /(\s+)return response\?.status\(200\)\.json\(\{ data \} as OpenAIModelsResponse\);/g,
    // Line 144: return response?.status(400).json({...});
    /(\s+)return response\?.status\(400\)\.json\(\{[\s\S]*?\} as StreamResponseChunk\);/g,
    // Line 292: return response?.status(200).json({...});
    /(\s+)return response\?.status\(200\)\.json\(\{[\s\S]*?\} as OpenAIEmbeddingsResponse\);/g,
    // Line 369: return response?.status(200).json({...});
    /(\s+)return response\?.status\(200\)\.json\(\{[\s\S]*?\} as OpenAIVectorStoresResponse\);/g,
  ];

  patterns.forEach((pattern) => {
    const newContent = content.replace(pattern, (match, indent) => {
      // Extract the response call without return
      const responseCall = match.replace(/(\s+)return /, "$1");
      return responseCall.replace(/;$/, ";\n" + indent + "return;");
    });

    if (newContent !== content) {
      content = newContent;
      modified = true;
    }
  });

  // Also handle simpler cases
  content = content.replace(
    /(\s+)return (response\?\.[^;]+;)/g,
    "$1$2\n$1return;"
  );

  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`  ✅ Fixed return statements in: ${filePath}`);
  } else {
    console.log(`  ℹ️  No return statements to fix in: ${filePath}`);
  }
} catch (error) {
  console.error(`❌ Error processing ${filePath}:`, error.message);
}

console.log("\n✅ Done fixing return statements");
