#!/usr/bin/env node

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

console.log("🔄 Fixing void return statement errors...");

// Get TS2322 errors for Response types
let tscOutput;
try {
  tscOutput = execSync("npx tsc --noEmit 2>&1", {
    cwd: __dirname + "/..",
    encoding: "utf-8",
  });
} catch (error) {
  tscOutput = error.stdout + error.stderr;
}

const returnErrors = tscOutput
  .split("\n")
  .filter(
    (line) =>
      line.includes("error TS2322") &&
      line.includes("Response") &&
      line.includes("void")
  )
  .map((line) => {
    const match = line.match(/^(.+?):/);
    if (match) {
      return match[1];
    }
    return null;
  })
  .filter(Boolean);

console.log(`Found ${returnErrors.length} void return statement errors`);

// Group by file and remove duplicates
const fileGroups = {};
returnErrors.forEach((error) => {
  if (!fileGroups[error]) {
    fileGroups[error] = true;
  }
});

let fixedCount = 0;

function fixReturnStatements(filePath) {
  const fullPath = path.resolve(__dirname + "/..", filePath);
  if (!fs.existsSync(fullPath)) {
    console.log(`⚠️  File not found: ${fullPath}`);
    return;
  }

  console.log(`📝 Processing: ${filePath}`);

  try {
    let content = fs.readFileSync(fullPath, "utf-8");
    let modified = false;

    // Remove return keyword from response calls in void functions
    const patterns = [
      /(\s+)return (response\.status\([^;]+\);)/g,
      /(\s+)return (response\.json\([^;]+\);)/g,
      /(\s+)return (response\.sendStatus\([^;]+\);)/g,
      /(\s+)return (response\.end\([^;]+\);)/g,
      /(\s+)return (response\.send\([^;]+\);)/g,
    ];

    patterns.forEach((pattern) => {
      const newContent = content.replace(pattern, "$1$2");
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    });

    // Handle multi-line return statements
    content = content.replace(
      /(\s+)return response\n(\s+)\.status/g,
      "$1response\n$2.status"
    );

    if (modified || content !== fs.readFileSync(fullPath, "utf-8")) {
      fs.writeFileSync(fullPath, content);
      console.log(`  ✅ Fixed return statements in: ${filePath}`);
      fixedCount++;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
}

Object.keys(fileGroups).forEach(fixReturnStatements);

console.log(`\n✅ Fixed return statements in ${fixedCount} files`);
