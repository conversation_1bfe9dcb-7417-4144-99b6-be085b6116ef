#!/usr/bin/env node

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

console.log("🔧 Analyzing type mismatch errors...\n");

// Get TypeScript errors
let errors = [];
try {
  execSync("npx tsc --noEmit", { encoding: "utf8", stdio: "pipe" });
} catch (error) {
  const errorOutput = error.stdout + error.stderr;
  errors = errorOutput.split("\n");
}

// Categorize type mismatch errors
const typeMismatches = {
  missingProperties: [],
  incompatibleTypes: [],
  cannotAssign: [],
  notAssignable: [],
  other: [],
};

errors.forEach((error) => {
  if (
    error.includes("TS2741") ||
    error.includes("is missing the following properties")
  ) {
    typeMismatches.missingProperties.push(error);
  } else if (
    error.includes("TS2322") ||
    error.includes("is not assignable to type")
  ) {
    typeMismatches.notAssignable.push(error);
  } else if (error.includes("TS2345") || error.includes("Argument of type")) {
    typeMismatches.incompatibleTypes.push(error);
  } else if (
    error.includes("TS2339") ||
    error.includes("does not exist on type")
  ) {
    typeMismatches.cannotAssign.push(error);
  } else if (error.includes("Type") && error.includes("cannot")) {
    typeMismatches.other.push(error);
  }
});

console.log("📊 Type Mismatch Error Summary:");
console.log(
  `   Missing Properties: ${typeMismatches.missingProperties.length}`
);
console.log(`   Not Assignable: ${typeMismatches.notAssignable.length}`);
console.log(
  `   Incompatible Types: ${typeMismatches.incompatibleTypes.length}`
);
console.log(
  `   Property Does Not Exist: ${typeMismatches.cannotAssign.length}`
);
console.log(`   Other Type Errors: ${typeMismatches.other.length}`);
console.log("");

// Common fixes for type mismatches
const _fixes = {
  // Add missing optional properties
  addOptionalProperties: (filePath, properties) => {
    try {
      let content = fs.readFileSync(filePath, "utf8");

      // Find interface or type definitions
      const interfaceMatch = content.match(/interface\s+(\w+)\s*{([^}]+)}/g);
      if (interfaceMatch) {
        interfaceMatch.forEach((match) => {
          properties.forEach((prop) => {
            if (!match.includes(prop)) {
              // Add optional property
              const updatedMatch = match.replace(/}$/, `  ${prop}?: any;\n}`);
              content = content.replace(match, updatedMatch);
            }
          });
        });
      }

      fs.writeFileSync(filePath, content, "utf8");
      return true;
    } catch {
      // ignore
      return false;
    }
  },

  // Cast to any for complex type mismatches
  castToAny: (filePath, lineNumber, variableName) => {
    try {
      let content = fs.readFileSync(filePath, "utf8");
      const lines = content.split("\n");
      const lineIndex = lineNumber - 1;

      if (lineIndex >= 0 && lineIndex < lines.length) {
        const line = lines[lineIndex];
        // Add 'as any' casting
        if (!line.includes("as any")) {
          lines[lineIndex] = line.replace(
            variableName,
            `${variableName} as any`
          );
          content = lines.join("\n");
          fs.writeFileSync(filePath, content, "utf8");
          return true;
        }
      }
      return false;
    } catch {
      // ignore
      return false;
    }
  },
};

// Process some common patterns
console.log("\n🔍 Analyzing common patterns...\n");

// Pattern 1: Missing 'sources' and 'textResponse' in ResponseChunkData
const responseChunkErrors = typeMismatches.missingProperties.filter(
  (error) =>
    error.includes("ResponseChunkData") &&
    (error.includes("sources") || error.includes("textResponse"))
);

if (responseChunkErrors.length > 0) {
  console.log(`Found ${responseChunkErrors.length} ResponseChunkData errors`);

  responseChunkErrors.forEach((error) => {
    const match = error.match(/(.+?):\((\d+),(\d+)\)/);
    if (match) {
      const [, filePath, line, col] = match;
      console.log(`   ${filePath}:${line}:${col}`);
    }
  });
}

// Pattern 2: Type mismatches with 'any' types
const anyTypeErrors = typeMismatches.notAssignable.filter(
  (error) => error.includes("any") || error.includes("unknown")
);

console.log(`\nFound ${anyTypeErrors.length} errors related to 'any' types`);

// Pattern 3: Missing properties in function arguments
const functionArgErrors = typeMismatches.incompatibleTypes.filter((error) =>
  error.includes("Argument of type")
);

console.log(`Found ${functionArgErrors.length} function argument type errors`);

// Generate detailed report for manual fixes
const reportPath = path.join(process.cwd(), "type-mismatch-report.txt");
let report = "Type Mismatch Error Report\n";
report += "========================\n\n";

Object.entries(typeMismatches).forEach(([category, errors]) => {
  if (errors.length > 0) {
    report += `${category.toUpperCase()} (${errors.length} errors)\n`;
    report += "-".repeat(50) + "\n";

    // Group by file
    const byFile = {};
    errors.forEach((error) => {
      const fileMatch = error.match(/^(.+?):\(/);
      if (fileMatch) {
        const file = fileMatch[1];
        if (!byFile[file]) byFile[file] = [];
        byFile[file].push(error);
      }
    });

    Object.entries(byFile).forEach(([file, fileErrors]) => {
      report += `\n${file}:\n`;
      fileErrors.slice(0, 5).forEach((err) => {
        report += `  ${err}\n`;
      });
      if (fileErrors.length > 5) {
        report += `  ... and ${fileErrors.length - 5} more errors\n`;
      }
    });

    report += "\n";
  }
});

fs.writeFileSync(reportPath, report, "utf8");
console.log(`\n📄 Detailed report written to: ${reportPath}`);

// Suggest next steps
console.log("\n💡 Next Steps:");
console.log("1. Review the type-mismatch-report.txt for detailed errors");
console.log("2. Common fixes:");
console.log("   - Add missing properties as optional (property?: type)");
console.log("   - Use type assertions (value as Type) for compatible types");
console.log("   - Update function signatures to match expected types");
console.log("   - Use union types for multiple possible values");
console.log('3. Run "npx tsc --noEmit" to verify fixes');
