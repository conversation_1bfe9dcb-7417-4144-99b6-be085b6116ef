#!/usr/bin/env node

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

console.log("🧹 Fixing unused variables and imports...");

// Get all TS6133 errors
let tscOutput;
try {
  tscOutput = execSync("npx tsc --noEmit 2>&1", {
    cwd: __dirname + "/..",
    encoding: "utf-8",
  });
} catch (error) {
  // TypeScript compilation failed, but we can still read the error output
  tscOutput = error.stdout + error.stderr;
}
const unusedErrors = tscOutput
  .split("\n")
  .filter((line) => line.includes("error TS6133"))
  .map((line) => {
    const match = line.match(/^(.+?)\((\d+),(\d+)\).*'([^']+)'.*$/);
    if (match) {
      return {
        file: match[1],
        line: parseInt(match[2]),
        col: parseInt(match[3]),
        variable: match[4],
        fullLine: line,
      };
    }
    return null;
  })
  .filter(Boolean);

console.log(`Found ${unusedErrors.length} unused variable/import errors`);

// Group by file
const fileGroups = {};
unusedErrors.forEach((error) => {
  if (!fileGroups[error.file]) {
    fileGroups[error.file] = [];
  }
  fileGroups[error.file].push(error);
});

let fixedCount = 0;

Object.keys(fileGroups).forEach((filePath) => {
  const fullPath = path.resolve(__dirname + "/..", filePath);
  if (!fs.existsSync(fullPath)) {
    console.log(`⚠️  File not found: ${fullPath}`);
    return;
  }

  console.log(`📝 Processing: ${filePath}`);

  try {
    let content = fs.readFileSync(fullPath, "utf-8");
    const lines = content.split("\n");
    const errors = fileGroups[filePath].sort((a, b) => b.line - a.line); // Sort descending to avoid line number shifts

    errors.forEach((error) => {
      const lineIndex = error.line - 1;
      const line = lines[lineIndex];

      if (!line) return;

      // Handle unused imports
      if (line.includes("import") && line.includes(error.variable)) {
        // Check if it's a single import
        if (line.match(new RegExp(`import\\s+${error.variable}\\s+from`))) {
          // Remove entire import line
          lines[lineIndex] = "";
          fixedCount++;
          console.log(`  ✓ Removed import: ${error.variable}`);
        } else if (line.includes("{") && line.includes("}")) {
          // Remove from destructured import
          const regex = new RegExp(`\\s*,?\\s*${error.variable}\\s*,?`);
          const newLine = line.replace(regex, (match) => {
            // Handle commas properly
            if (match.includes(",")) {
              return match.startsWith(",") ? "" : ",";
            }
            return "";
          });

          // Clean up any remaining double commas or trailing commas
          const cleanedLine = newLine
            .replace(/,\s*,/g, ",")
            .replace(/,\s*}/g, "}")
            .replace(/{\s*,/g, "{")
            .replace(/{\s*}/g, "{}");

          // If import becomes empty, remove the line
          if (cleanedLine.match(/import\s+{\s*}\s+from/)) {
            lines[lineIndex] = "";
          } else {
            lines[lineIndex] = cleanedLine;
          }
          fixedCount++;
          console.log(
            `  ✓ Removed from destructured import: ${error.variable}`
          );
        }
      }
      // Handle unused variables (prefix with underscore)
      else if (line.includes(error.variable)) {
        // Don't modify if already prefixed with underscore
        if (!error.variable.startsWith("_")) {
          const regex = new RegExp(`\\b${error.variable}\\b`, "g");
          lines[lineIndex] = line.replace(regex, `_${error.variable}`);
          fixedCount++;
          console.log(
            `  ✓ Prefixed variable with underscore: ${error.variable} -> _${error.variable}`
          );
        }
      }
    });

    // Write back the modified content
    const newContent = lines.join("\n");
    if (newContent !== content) {
      fs.writeFileSync(fullPath, newContent);
      console.log(`  💾 Updated: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
});

console.log(`\n✅ Fixed ${fixedCount} unused variable/import issues`);
