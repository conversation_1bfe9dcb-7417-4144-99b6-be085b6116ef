#!/usr/bin/env node

const fs = require("fs");
const path = require("path");

// Files to process
const vectorDbFiles = [
  "utils/vectorDbProviders/lance/index.ts",
  "utils/vectorDbProviders/qdrant/index.ts",
  "utils/vectorDbProviders/weaviate/index.ts",
  "utils/vectorDbProviders/chroma/index.ts",
  "utils/vectorDbProviders/pinecone/index.ts",
  "utils/vectorDbProviders/milvus/index.ts",
  "utils/vectorDbProviders/zilliz/index.ts",
  "utils/vectorDbProviders/astra/index.ts",
];

// Common unused imports pattern for vector DB providers
const _unusedImportsPattern =
  /import\s*{\s*([^}]+)\s*}\s*from\s*["']\.\.\/\.\.\/\.\.\/types\/vectorDb["'];?/;

function removeUnusedImports(filePath) {
  try {
    const fullPath = path.join(__dirname, "..", filePath);
    if (!fs.existsSync(fullPath)) {
      console.log(`File not found: ${filePath}`);
      return;
    }

    let content = fs.readFileSync(fullPath, "utf8");
    const lines = content.split("\n");

    // Find import lines
    let importStart = -1;
    let importEnd = -1;

    for (let i = 0; i < lines.length; i++) {
      if (
        lines[i].includes("import {") &&
        lines[i].includes('from "../../../types/vectorDb"')
      ) {
        importStart = i;
        // Find the end of the import
        for (let j = i; j < lines.length; j++) {
          if (lines[j].includes("} from")) {
            importEnd = j;
            break;
          }
        }
        break;
      }
    }

    if (importStart === -1) return;

    // Extract all imported types
    const importLines = lines.slice(importStart, importEnd + 1).join("\n");
    const match = importLines.match(/import\s*{\s*([^}]+)\s*}\s*from/);
    if (!match) return;

    const imports = match[1].split(",").map((i) => i.trim());

    // Check which imports are actually used in the file
    const usedImports = [];
    const contentWithoutImport = lines.slice(importEnd + 1).join("\n");

    for (const imp of imports) {
      // Check if the import is used (excluding the import line itself)
      const regex = new RegExp(`\\b${imp}\\b`, "g");
      if (regex.test(contentWithoutImport)) {
        usedImports.push(imp);
      } else {
        console.log(`Removing unused import '${imp}' from ${filePath}`);
      }
    }

    // If all imports are unused, remove the entire import statement
    if (usedImports.length === 0) {
      lines.splice(importStart, importEnd - importStart + 1);
    } else {
      // Rebuild the import statement with only used imports
      const newImportLine = `import { ${usedImports.join(", ")} } from "../../../types/vectorDb";`;
      lines.splice(importStart, importEnd - importStart + 1, newImportLine);
    }

    // Remove AdjacentVectorsParams interface if it exists and is unused
    const newContent = lines.join("\n");
    if (
      newContent.includes("interface AdjacentVectorsParams") &&
      !newContent.includes("AdjacentVectorsParams>") &&
      !newContent.includes("AdjacentVectorsParams,") &&
      !newContent.includes("AdjacentVectorsParams ")
    ) {
      // Find and remove the interface
      const interfacePattern = /interface AdjacentVectorsParams\s*{[^}]+}\s*/g;
      const cleanedContent = newContent.replace(interfacePattern, "");
      fs.writeFileSync(fullPath, cleanedContent);
      console.log(
        `Removed unused AdjacentVectorsParams interface from ${filePath}`
      );
    } else {
      fs.writeFileSync(fullPath, newContent);
    }

    console.log(`Updated ${filePath}`);
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

// Process all files
console.log("Fixing unused vector DB imports...\n");
vectorDbFiles.forEach(removeUnusedImports);
console.log("\nDone!");
