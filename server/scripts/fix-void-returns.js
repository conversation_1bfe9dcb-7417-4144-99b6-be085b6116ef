#!/usr/bin/env node

const fs = require("fs");

const filesToFix = [
  "endpoints/admin.ts",
  "endpoints/api/admin/index.ts",
  "endpoints/api/admin/system.ts",
  "endpoints/api/document/index.ts",
  "endpoints/api/docx-edit/index.ts",
  "endpoints/api/embed/index.ts",
  "endpoints/api/openai/index.ts",
  "endpoints/api/system/index.ts",
  "endpoints/api/userManagement/index.ts",
  "endpoints/api/workspaceThread/index.ts",
  "endpoints/browserExtension.ts",
  "endpoints/chat.ts",
  "endpoints/document.ts",
  "endpoints/docxLlmProcessor.ts",
  "endpoints/embed/index.ts",
  "endpoints/invite.ts",
  "endpoints/mcpServers.ts",
  "endpoints/news.ts",
  "endpoints/requestLegalAssistance.ts",
  "endpoints/upgradeDeepSearchPrompt.ts",
  "endpoints/userCustomAiSettings.ts",
  "endpoints/userCustomSystemPrompt.ts",
  "endpoints/userPromptLibrary.ts",
  "endpoints/utils.ts",
  "endpoints/workspaces.ts",
  "endpoints/workspaceThreads.ts",
  "swagger/utils.ts",
];

console.log("🔄 Fixing void return statement errors...");

let fixedCount = 0;

filesToFix.forEach((filePath) => {
  try {
    let content = fs.readFileSync(filePath, "utf-8");
    let modified = false;

    // Remove return keyword from response calls in void functions
    const patterns = [
      /(\s+)return (response\.status\([^)]+\)\.json\([^;]+\);)/g,
      /(\s+)return (response\.json\([^;]+\);)/g,
      /(\s+)return (response\.sendStatus\([^)]+\);)/g,
      /(\s+)return (response\.status\([^)]+\)\.end\(\);)/g,
      /(\s+)return (response\.end\(\);)/g,
      /(\s+)return (response\.send\([^;]+\);)/g,
    ];

    patterns.forEach((pattern) => {
      const newContent = content.replace(pattern, "$1$2");
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    });

    // Handle multi-line return statements like:
    // return response
    //   .status(200)
    //   .json(result);
    const multiLinePattern =
      /(\s+)return response\n(\s+)\.status\(([^)]+)\)\n(\s+)\.json\(([^;]+)\);/g;
    content = content.replace(
      multiLinePattern,
      "$1response\n$2.status($3)\n$4.json($5);"
    );

    // Handle other multi-line patterns
    content = content.replace(
      /(\s+)return response\n(\s+)\.status/g,
      "$1response\n$2.status"
    );

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`  ✅ Fixed return statements in: ${filePath}`);
      fixedCount++;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
});

console.log(`\n✅ Fixed return statements in ${fixedCount} files`);
