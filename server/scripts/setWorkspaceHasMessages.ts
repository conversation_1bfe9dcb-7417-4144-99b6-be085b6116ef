import prisma from "../utils/prisma";

async function setWorkspaceHasMessages(): Promise<void> {
  try {
    console.log("Starting migration to set hasMessages flag on workspaces...");

    const workspaces = await prisma.workspaces.findMany({
      select: { id: true },
    });

    console.log(`Found ${workspaces.length} workspaces to check.`);

    let updatedCount = 0;

    for (const workspace of workspaces) {
      const threads = await prisma.workspace_threads.findMany({
        where: {
          workspace_id: workspace.id,
        },
        select: { id: true },
      });

      if (threads?.length > 0) {
        const threadIds = threads.map((t) => t.id);
        const threadChatCount = await prisma.workspace_chats.count({
          where: {
            workspaceId: workspace.id,
            thread_id: { in: threadIds },
          },
        });

        if (threadChatCount > 0) {
          await prisma.workspaces.update({
            where: { id: workspace.id },
            data: { hasMessages: true },
          });
          updatedCount++;
        }
      }
    }

    console.log(`Updated ${updatedCount} workspaces with hasMessages = true`);
    console.log("Migration completed successfully!");
  } catch (error) {
    console.error("Error during migration:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Execute the migration
setWorkspaceHasMessages()
  .then(() => {
    console.log("Script execution completed.");
    process.exit(0);
  })
  .catch((error) => {
    console.error("Script execution failed:", error);
    process.exit(1);
  });
