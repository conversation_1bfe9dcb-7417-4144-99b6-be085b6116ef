// <PERSON>ript to test Jina embedder functionality
import * as path from "path";
import { config } from "dotenv";
import fetch from "node-fetch";
import { <PERSON>aEmbedder } from "../utils/EmbeddingEngines/jina";
import type { EmbeddingTestResult } from "../types/scripts";

// Load root .env then override with server/.env
config();
config({
  path: path.resolve(__dirname, "../.env"),
  override: true,
});

interface JinaRequestBody {
  input: string;
  model: string;
  task: string;
  late_chunking?: boolean;
  embedding_type: string;
  dimensions: number;
}

async function testJinaEmbedder(): Promise<void> {
  const startTime = Date.now();

  try {
    console.log("Initializing Jina Embedder...");
    console.log("Environment variables:");
    console.log(
      "- JINA_API_KEY:",
      process.env.JINA_API_KEY ? "Set (hidden)" : "Not set"
    );
    console.log("- EMBEDDING_MODEL_PREF:", process.env.EMBEDDING_MODEL_PREF);
    console.log("- JINA_DIMENSIONS:", process.env.JINA_DIMENSIONS);
    console.log("- JINA_TASK:", process.env.JINA_TASK);
    console.log("- JINA_LATE_CHUNKING:", process.env.JINA_LATE_CHUNKING);
    console.log("- JINA_EMBEDDING_TYPE:", process.env.JINA_EMBEDDING_TYPE);

    const embedder = new JinaEmbedder();

    console.log("\nTesting single text embedding...");
    const testText = "This is a test of the Jina embedding API.";

    // Create a request body without return_tokens and return_chunks
    const requestBody: JinaRequestBody = {
      input: testText,
      model: embedder.model,
      task: "text-matching", // Default task
      late_chunking: false,
      embedding_type: "float",
      dimensions: 1024,
    };

    console.log("Request body:", JSON.stringify(requestBody, null, 2));

    console.log("\nAttempting to embed text...");
    const result = await embedder.embedTextInput(testText);

    console.log("\nEmbedding successful!");
    console.log("Embedding dimensions:", result.length);
    console.log("First 5 values:", result.slice(0, 5));

    // Test direct API call for diagnostics
    console.log("\nTesting direct Jina API call...");
    const apiKey = process.env.JINA_API_KEY;
    const directRequestBody: JinaRequestBody = {
      input: testText,
      model: embedder.model,
      task: "text-matching", // Default task
      embedding_type: "float",
      dimensions: 1024,
    };

    console.log(
      "Direct request body:",
      JSON.stringify(directRequestBody, null, 2)
    );

    const response = await fetch("https://api.jina.ai/v1/embeddings", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify(directRequestBody),
    });

    console.log("Response status:", response.status);
    const respText = await response.text();

    if (response.status === 401) {
      console.error(
        "Authentication failed: Invalid JINA_API_KEY – please check your credentials in .env"
      );
      return;
    }

    console.log("Response body:", respText);

    try {
      const respJson = JSON.parse(respText);
      console.log("Parsed JSON response:", respJson);
    } catch {
      console.log("Response is not valid JSON");
    }

    const testResult: EmbeddingTestResult = {
      provider: "jina",
      model: embedder.model,
      embedding: result,
      dimensions: result.length,
      duration: Date.now() - startTime,
    };

    console.log("\nTest completed successfully:", testResult);
  } catch (_error) {
    const err = _error as Error;
    console.error("Error testing Jina embedder:");
    console.error("Error type:", typeof _error);
    console.error("Error message:", err.message);
    console.error("Full error:", _error);

    interface DetailedError extends Error {
      details?: Array<{ message?: string; [key: string]: unknown }>;
      cause?: unknown;
    }

    const detailedError = _error as DetailedError;

    if (detailedError.details) {
      console.error("Error details:");
      detailedError.details.forEach((detail, idx: number) => {
        console.error(`Detail ${idx + 1}:`, detail);
      });
    }

    if (detailedError.cause) {
      console.error("Error cause:", detailedError.cause);
    }

    const testResult: EmbeddingTestResult = {
      provider: "jina",
      model: "unknown",
      error: err.message,
      duration: Date.now() - startTime,
    };

    console.log("\nTest failed:", testResult);
  }
}

testJinaEmbedder();
