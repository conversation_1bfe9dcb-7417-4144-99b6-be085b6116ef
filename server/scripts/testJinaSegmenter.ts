// Script to test Jina Segmenter functionality
import * as path from "path";
import { config } from "dotenv";
import * as https from "https";
import type { SegmentationTestResult } from "../types/scripts";

config({ path: path.resolve(__dirname, "../.env") });

interface SegmentRequestBody {
  content: string;
  return_tokens: boolean;
  return_chunks: boolean;
  max_chunk_length: number;
}

interface SegmentResponse {
  results?: Array<{ text?: string; content?: string }>;
  chunks?: string[];
  text?: string;
  num_tokens?: number;
  tokenizer?: string;
}

async function testJinaSegmenter(): Promise<void> {
  const startTime = Date.now();

  try {
    console.log("Testing Jina Segmenter API...");

    // Get API key from environment
    const jinaApiKey = process.env.JINA_API_KEY;
    if (!jinaApiKey) {
      console.error("No Jina API key found in environment variables");
      return;
    }

    console.log("Environment variables:");
    console.log("- JINA_API_KEY:", jina<PERSON>pi<PERSON>ey ? "Set (hidden)" : "Not set");

    // Test text
    const testText = `
    This is a test document that will be used to verify <PERSON><PERSON>'s segmentation API.
    It contains multiple sentences to ensure proper segmentation.

    The Jina API should analyze this text and break it into meaningful chunks
    based on semantic understanding and the specified maximum token length.

    Each chunk should maintain context and coherence while staying within
    the token limit specified in the system settings.
    `;

    // Create request body for segmentation
    const segmentRequestBody: SegmentRequestBody = {
      content: testText,
      return_tokens: true,
      return_chunks: true,
      max_chunk_length: 1000,
    };

    console.log(
      "Segmenter Request body:",
      JSON.stringify(segmentRequestBody, null, 2)
    );

    // Make API call using https module
    const options: https.RequestOptions = {
      hostname: "api.jina.ai",
      path: "/v1/segment",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${jinaApiKey}`,
      },
    };

    const req = https.request(options, (res) => {
      let data = "";

      res.on("data", (chunk) => {
        data += chunk;
      });

      res.on("end", () => {
        console.log("Segmenter Response status:", res.statusCode);

        if (res.statusCode === 401) {
          console.error(
            "Authentication failed: Invalid JINA_API_KEY – please check your credentials in .env"
          );

          const failResult: SegmentationTestResult = {
            provider: "jina",
            error: "Authentication failed",
            duration: Date.now() - startTime,
          };
          console.log("\nTest failed:", failResult);
          return;
        }

        try {
          console.log("Segmenter Response body:", data);

          if (res.statusCode === 200) {
            const response: SegmentResponse = JSON.parse(data);
            console.log("Segmentation successful!");

            // Extract chunks from the response
            let chunks: string[] = [];
            if (response.results && Array.isArray(response.results)) {
              chunks = response.results
                .map((r) => r.text || r.content)
                .filter((chunk): chunk is string => Boolean(chunk));
            } else if (response.chunks && Array.isArray(response.chunks)) {
              chunks = response.chunks;
            } else if (typeof response.text === "string") {
              chunks = [response.text];
            }

            console.log("Number of chunks:", chunks.length);
            chunks.forEach((chunk, index) => {
              console.log(`\nChunk ${index + 1}:`);
              console.log(
                chunk.substring(0, 100) + (chunk.length > 100 ? "..." : "")
              );
            });

            if (response.num_tokens) {
              console.log("\nToken information:");
              console.log("- Total tokens:", response.num_tokens);
              console.log("- Tokenizer:", response.tokenizer || "unknown");
            }

            const successResult: SegmentationTestResult = {
              provider: "jina",
              segments: chunks,
              segmentCount: chunks.length,
              duration: Date.now() - startTime,
            };

            console.log("\nTest completed successfully:", successResult);
          } else if (res.statusCode === 402) {
            console.error(
              "Payment Required: Your Jina account requires payment or additional credits."
            );
            console.error(
              "Please visit https://jina.ai/embeddings/ to add a payment method or purchase credits."
            );

            const paymentResult: SegmentationTestResult = {
              provider: "jina",
              error: "Payment required",
              duration: Date.now() - startTime,
            };
            console.log("\nTest failed:", paymentResult);
          }
        } catch (error) {
          const err = error as Error;
          console.error("Error parsing segmenter response:", error);

          interface DetailedError extends Error {
            stack?: string;
            details?: Array<{ message?: string; [key: string]: unknown }>;
            cause?: unknown;
          }

          const detailedError = error as DetailedError;

          if (detailedError.stack) {
            console.error("Error stack:", detailedError.stack);
          }
          if (detailedError.details) {
            console.error("Error details:");
            detailedError.details.forEach((detail, idx: number) => {
              console.error(`Detail ${idx + 1}:`, detail);
            });
          }
          if (detailedError.cause) {
            console.error("Error cause:", detailedError.cause);
          }

          const errorResult: SegmentationTestResult = {
            provider: "jina",
            error: err.message,
            duration: Date.now() - startTime,
          };
          console.log("\nTest failed:", errorResult);
        }
      });
    });

    req.on("error", (error) => {
      console.error("Segmenter request error:", error);

      const requestErrorResult: SegmentationTestResult = {
        provider: "jina",
        error: `Request error: ${error.message}`,
        duration: Date.now() - startTime,
      };
      console.log("\nTest failed:", requestErrorResult);
    });

    req.write(JSON.stringify(segmentRequestBody));
    req.end();
  } catch (error) {
    const err = error as Error;
    console.error("Error testing Jina Segmenter API:");
    console.error("Error type:", typeof error);
    console.error("Error message:", err.message);

    interface DetailedError extends Error {
      stack?: string;
      details?: Array<{ message?: string; [key: string]: unknown }>;
      cause?: unknown;
    }

    const detailedError = error as DetailedError;

    if (detailedError.stack) {
      console.error("Error stack:", detailedError.stack);
    }
    if (detailedError.details) {
      console.error("Error details:");
      detailedError.details.forEach((detail, idx: number) => {
        console.error(`Detail ${idx + 1}:`, detail);
      });
    }
    if (detailedError.cause) {
      console.error("Error cause:", detailedError.cause);
    }

    const errorResult: SegmentationTestResult = {
      provider: "jina",
      error: err.message,
      duration: Date.now() - startTime,
    };
    console.log("\nTest failed:", errorResult);
  }
}

// Run the test
testJinaSegmenter();
