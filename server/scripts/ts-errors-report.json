{"unusedVariables": ["endpoints/upgradePrompt.ts(139,10): error TS6133: '_processResponse' is declared but its value is never read.", "endpoints/workspaces.ts(719,15): error TS6133: '_includeDocuments' is declared but its value is never read.", "endpoints/workspaces.ts(923,15): error TS6133: '_includeDocuments' is declared but its value is never read.", "endpoints/workspaces.ts(983,15): error TS6133: '_includeDocuments' is declared but its value is never read.", "endpoints/workspaces.ts(1069,15): error TS6133: '_includeDocuments' is declared but its value is never read.", "endpoints/workspaces.ts(1805,19): error TS6133: '_success' is declared but its value is never read.", "endpoints/workspaces.ts(1805,37): error TS6133: '_documents' is declared but its value is never read.", "endpoints/workspaces.ts(1870,15): error TS6133: '_includeDocuments' is declared but its value is never read.", "jobs/bulk-document-processor.ts(46,20): error TS6133: '_RETRY_ATTEMPTS' is declared but its value is never read.", "models/systemSettings.ts(2,1): error TS6133: 't' is declared but its value is never read.", "utils/AiProviders/openAi/index.ts(14,3): error TS6133: 'LLMProviderStatic' is declared but its value is never read.", "utils/chats/apiChatHandler.ts(16,1): error TS6133: 'ChatMessage' is declared but its value is never read.", "utils/chats/apiChatHandler.ts(123,3): error TS6133: 'useDeepSearch' is declared but its value is never read.", "utils/chats/apiChatHandler.ts(415,3): error TS6133: 'useDeepSearch' is declared but its value is never read.", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(11,39): error TS6133: 'config' is declared but its value is never read.", "utils/chats/flows/processors/DocumentProcessingStageProcessor.ts(90,11): error TS6133: '_contextManager' is declared but its value is never read.", "utils/chats/flows/processors/LegalMemoProcessor.ts(712,17): error TS6133: '_saveMemoArtifacts' is declared but its value is never read.", "utils/chats/flows/processors/LegalMemoProcessor.ts(809,11): error TS6133: '_calculateSuccessRate' is declared but its value is never read.", "utils/chats/helpers/contextWindowManager.ts(188,11): error TS6133: '_strategy' is declared but its value is never read.", "utils/chats/streamLQA.ts(446,7): error TS6133: '_sourcesSave' is declared but its value is never read.", "utils/chats/streamLQA.ts(1032,17): error TS6133: '_completeText' is declared but its value is never read.", "utils/DeepSearch/deepsearchproviders/google.ts(67,11): error TS6133: '_location' is declared but its value is never read.", "utils/DocumentDrafting/index.ts(107,11): error TS6133: '_startTime' is declared but its value is never read.", "utils/documentEditing/editingLogic.ts(145,10): error TS6133: 'heuristicTextCompare' is declared but its value is never read.", "utils/documentEditing/editingLogic.ts(183,10): error TS6133: 'cleanLLMGeneratedId' is declared but its value is never read.", "utils/documentEditing/lineLevel/validation.ts(83,11): error TS6133: '_researchText' is declared but its value is never read.", "utils/documentEditing/workflows/lineLevelWorkflow.ts(180,11): error TS6133: '_applicationTime' is declared but its value is never read.", "utils/documentEditing/workflows/lineLevelWorkflow.ts(213,11): error TS6133: '_originalWordCount' is declared but its value is never read.", "utils/EmbeddingEngines/jina/index.ts(34,11): error TS6133: '_task' is declared but its value is never read.", "utils/EmbeddingEngines/jina/index.ts(35,11): error TS6133: '_lateChunking' is declared but its value is never read.", "utils/EmbeddingEngines/jina/index.ts(36,11): error TS6133: '_embeddingType' is declared but its value is never read.", "utils/files/purgeDocument.ts(155,9): error TS6133: '_workspaces' is declared but its value is never read.", "utils/helpers/autoCodingPromptGenerator.ts(633,11): error TS6133: '_preferredLanguage' is declared but its value is never read.", "utils/helpers/chat/index.ts(137,3): error TS6133: '_llmContextWindow' is declared but its value is never read.", "utils/helpers/chat/index.ts(711,5): error TS6133: '_contextTexts' is declared but its value is never read.", "utils/helpers/chat/index.ts(712,5): error TS6133: '_chatHistory' is declared but its value is never read.", "utils/helpers/chat/reasoningResponses.ts(87,7): error TS6133: '_placeholderEmitted' is declared but its value is never read.", "utils/helpers/chat/reasoningResponses.ts(88,7): error TS6133: '_contentStarted' is declared but its value is never read.", "utils/helpers/tiktoken.ts(14,11): error TS6133: '_model' is declared but its value is never read.", "utils/helpers/updateENV.ts(547,16): error TS6133: '_validJinaApiKey' is declared but its value is never read.", "utils/helpers/updateENV.ts(780,10): error TS6133: '_validHuggingFaceEndpoint' is declared but its value is never read.", "utils/middleware/validatedRequest.ts(9,1): error TS6133: 'AuthenticatedUser' is declared but its value is never read.", "utils/middleware/validWorkspace.ts(9,10): error TS6133: 'AuthenticatedUser' is declared but its value is never read.", "utils/vectorDbProviders/lance/index.ts(9,7): error TS6133: 'getEmbeddingEngineSelection' is declared but its value is never read.", "utils/vectorDbProviders/lance/index.ts(10,7): error TS6133: 'TextSplitter' is declared but its value is never read.", "utils/vectorDbProviders/lance/index.ts(12,9): error TS6133: 'storeVectorResult' is declared but its value is never read.", "utils/vectorDbProviders/lance/index.ts(15,7): error TS6133: 'Workspace' is declared but its value is never read."], "missingImports": ["utils/agents/aibitat/providers/anthropic.ts(9,8): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.", "utils/agents/aibitat/providers/azure.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.", "utils/agents/aibitat/providers/bedrock.ts(11,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.", "utils/agents/aibitat/providers/deepseek.ts(6,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.", "utils/agents/aibitat/providers/genericOpenAi.ts(6,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.", "utils/agents/aibitat/providers/groq.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.", "utils/agents/aibitat/providers/koboldcpp.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.", "utils/agents/aibitat/providers/lmstudio.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.", "utils/agents/aibitat/providers/localai.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.", "utils/agents/aibitat/providers/mistral.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.", "utils/agents/aibitat/providers/ollama.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.", "utils/agents/aibitat/providers/openai.ts(4,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.", "utils/agents/aibitat/providers/openrouter.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.", "utils/agents/aibitat/providers/perplexity.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.", "utils/agents/aibitat/providers/textgenwebui.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.", "utils/agents/aibitat/providers/togetherai.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.", "utils/agents/aibitat/providers/xai.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.", "utils/agents/aibitat/utils/summarize.ts(5,40): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations."], "typeMismatches": ["endpoints/admin.ts(260,9): error TS2322: Type 'Response<AdminUserResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(308,9): error TS2322: Type 'Response<CreateUserResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(425,9): error TS2322: Type 'Response<DeleteUserResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(442,9): error TS2322: Type 'Response<AdminInvitesResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(481,9): error TS2322: Type 'Response<CreateInviteResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(536,9): error TS2322: Type 'Response<AdminWorkspacesResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(562,9): error TS2322: Type 'Response<LinkedWorkspacesResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(580,9): error TS2322: Type 'Response<WorkspaceUsersResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(606,9): error TS2322: Type 'Response<CreateWorkspaceResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(722,9): error TS2322: Type 'Response<DeleteWorkspaceResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(766,9): error TS2322: Type 'Response<DeleteWorkspaceResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(791,9): error TS2322: Type 'Response<LoginUIResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(841,9): error TS2322: Type 'Response<UpdateLoginUIResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(981,9): error TS2322: Type 'Response<SystemPreferencesResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(1088,9): error TS2322: Type 'Response<SystemPreferencesResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(1124,9): error TS2322: Type 'Response<UpdateSystemPreferencesResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(1161,9): error TS2322: Type 'Response<PDRSettingsResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(1225,9): error TS2322: Type 'Response<DDSettingsResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(1254,9): error TS2322: Type 'Response<UpdateSystemPreferencesResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(1292,9): error TS2322: Type 'Response<UpdateSystemPreferencesResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(1309,9): error TS2322: Type 'Response<ApiKeysResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(1344,9): error TS2322: Type 'Response<GenerateApiKeyResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(1371,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(1422,9): error TS2322: Type 'Response<OrganizationsResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/admin.ts(1444,9): error TS2322: Type 'Response<CreateOrganizationResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/admin/system.ts(158,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/admin/system.ts(225,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/document/index.ts(212,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/document/index.ts(260,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/document/index.ts(309,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/document/index.ts(462,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/document/index.ts(604,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/document/index.ts(665,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/document/index.ts(704,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/docx-edit/index.ts(125,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/docx-edit/index.ts(231,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/docx-edit/index.ts(321,11): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/docx-edit/index.ts(504,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/docx-edit/index.ts(597,11): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/docx-edit/index.ts(687,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/docx-edit/index.ts(782,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/openai/index.ts(87,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/openai/index.ts(288,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/openai/index.ts(365,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/system/index.ts(39,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/system/index.ts(81,60): error TS2322: Type '{ WhisperProvider: string; WhisperModelPref: string; TextToSpeechProvider: string; TTSOpenAIKey: boolean; TTSOpenAIVoiceModel: string | undefined; TTSElevenLabsKey: boolean; TTSElevenLabsVoiceModel: string | undefined; ... 107 more ...; VectorDB: string | undefined; }' is not assignable to type 'SystemSettings'.", "endpoints/api/system/index.ts(168,64): error TS2322: Type 'string | false' is not assignable to type 'string | null'.", "endpoints/api/system/index.ts(210,11): error TS2345: Argument of type 'string' is not assignable to parameter of type 'ExportFormatType | undefined'.", "endpoints/api/userManagement/index.ts(84,15): error TS2322: Type '{ id: number; username: string | null; role: string; }[]' is not assignable to type 'UserResponse[]'.", "endpoints/api/userManagement/index.ts(124,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/api/workspaceThread/index.ts(504,11): error TS2322: Type 'string' is not assignable to type '\"query\" | \"chat\" | undefined'.", "endpoints/api/workspaceThread/index.ts(505,11): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.", "endpoints/api/workspaceThread/index.ts(694,11): error TS2322: Type 'string' is not assignable to type '\"query\" | \"chat\" | undefined'.", "endpoints/api/workspaceThread/index.ts(695,11): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.", "endpoints/chat.ts(456,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/document.ts(998,9): error TS2322: Type 'Response<AttachmentProcessResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/document.ts(1089,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/docxLlmProcessor.ts(83,11): error TS2322: Type 'CompletionResponse | null' is not assignable to type 'CompletionResponse'.", "endpoints/docxLlmProcessor.ts(128,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/generateLegalTaskPrompt.ts(137,5): error TS2719: Type 'LLMProvider | null' is not assignable to type 'LLMProvider | null'. Two different types with this name exist, but they are unrelated.", "endpoints/invite.ts(106,9): error TS2322: Type 'Response<AcceptInviteResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/mcpServers.ts(70,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/mcpServers.ts(93,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/mcpServers.ts(120,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/mcpServers.ts(145,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/mcpServers.ts(208,11): error TS2322: Type '{ mcpServers: {}; } | null' is not assignable to type 'MCPServerData'.", "endpoints/mcpServers.ts(234,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/upgradeDeepSearchPrompt.ts(109,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/upgradePrompt.ts(118,11): error TS2322: Type 'CompletionResponse | null' is not assignable to type 'CompletionResponse'.", "endpoints/userCustomAiSettings.ts(71,15): error TS2322: Type 'FilteredUser | null' is not assignable to type 'AuthenticatedUser | null'.", "endpoints/userCustomAiSettings.ts(153,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/userCustomAiSettings.ts(183,15): error TS2322: Type 'FilteredUser | null' is not assignable to type 'AuthenticatedUser | null'.", "endpoints/userCustomAiSettings.ts(230,11): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/userCustomSystemPrompt.ts(55,15): error TS2322: Type 'FilteredUser | null' is not assignable to type 'AuthenticatedUser | null'.", "endpoints/userCustomSystemPrompt.ts(81,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/userCustomSystemPrompt.ts(111,15): error TS2322: Type 'FilteredUser | null' is not assignable to type 'AuthenticatedUser | null'.", "endpoints/userCustomSystemPrompt.ts(144,11): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/userCustomSystemPrompt.ts(180,15): error TS2322: Type 'FilteredUser | null' is not assignable to type 'AuthenticatedUser | null'.", "endpoints/userCustomSystemPrompt.ts(199,11): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/userPromptLibrary.ts(80,15): error TS2322: Type 'FilteredUser | null' is not assignable to type 'AuthenticatedUser | null'.", "endpoints/userPromptLibrary.ts(93,15): error TS2322: Type 'UserPromptLibraryData[]' is not assignable to type 'UserPromptData[]'.", "endpoints/userPromptLibrary.ts(97,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/userPromptLibrary.ts(120,15): error TS2322: Type 'FilteredUser | null' is not assignable to type 'AuthenticatedUser | null'.", "endpoints/userPromptLibrary.ts(134,15): error TS2322: Type 'UserPromptLibraryData | null' is not assignable to type 'UserPromptData | null'.", "endpoints/userPromptLibrary.ts(145,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/userPromptLibrary.ts(168,15): error TS2322: Type 'FilteredUser | null' is not assignable to type 'AuthenticatedUser | null'.", "endpoints/userPromptLibrary.ts(202,11): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/userPromptLibrary.ts(232,15): error TS2322: Type 'FilteredUser | null' is not assignable to type 'AuthenticatedUser | null'.", "endpoints/userPromptLibrary.ts(249,17): error TS2322: Type 'UserPromptLibraryData | null' is not assignable to type 'UserPromptData | null'.", "endpoints/userPromptLibrary.ts(264,11): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/userPromptLibrary.ts(294,15): error TS2322: Type 'FilteredUser | null' is not assignable to type 'AuthenticatedUser | null'.", "endpoints/userPromptLibrary.ts(319,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/utils.ts(63,7): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/utils.ts(73,7): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/workspaces.ts(508,11): error TS2322: Type 'Response<ShareStatusResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/workspaces.ts(516,9): error TS2322: Type 'Response<ShareStatusResponse, Record<string, any>>' is not assignable to type 'void'.", "endpoints/workspaces.ts(546,11): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/workspaces.ts(556,11): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/workspaces.ts(576,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/workspaces.ts(602,11): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/workspaces.ts(612,11): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/workspaces.ts(624,11): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/workspaces.ts(630,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "endpoints/workspaces.ts(1619,35): error TS2322: Type 'Buffer<ArrayBufferLike> | null' is not assignable to type 'Buffer<ArrayBufferLike>'.", "endpoints/workspaces.ts(1711,13): error TS2322: Type 'null' is not assignable to type 'string | undefined'.", "endpoints/workspaceThreads.ts(158,9): error TS2322: Type 'ValidatedResponse' is not assignable to type 'void'.", "endpoints/workspaceThreads.ts(342,9): error TS2322: Type 'ValidatedResponse' is not assignable to type 'void'.", "endpoints/workspaceThreads.ts(385,9): error TS2322: Type 'ValidatedResponse' is not assignable to type 'void'.", "endpoints/workspaceThreads.ts(490,9): error TS2322: Type 'ValidatedResponse' is not assignable to type 'void'.", "endpoints/workspaceThreads.ts(543,9): error TS2322: Type 'ValidatedResponse' is not assignable to type 'void'.", "endpoints/workspaceThreads.ts(602,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "jobs/sync-watched.documents.ts(39,67): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'ValidFileType'.", "jobs/sync-watched.documents.ts(173,11): error TS2322: Type '{ not: number; }' is not assignable to type 'number'.", "models/browserExtensionApiKey.ts(75,9): error TS2322: Type '{ key: string; user_id: number | null; }' is not assignable to type '(Without<browser_extension_api_keysCreateInput, browser_extension_api_keysUncheckedCreateInput> & browser_extension_api_keysUncheckedCreateInput) | (Without<...> & browser_extension_api_keysCreateInput)'.", "models/documentSyncQueue.ts(236,7): error TS2322: Type '{ id: number; createdAt: Date; staleAfterMs: number; nextSyncAt: Date; lastSyncedAt: Date; workspaceDocId: number; } | null' is not assignable to type 'DocumentSyncQueueData | null'.", "models/documentSyncQueue.ts(256,7): error TS2322: Type '{ id: number; createdAt: Date; staleAfterMs: number; nextSyncAt: Date; lastSyncedAt: Date; workspaceDocId: number; }[]' is not assignable to type 'DocumentSyncQueueData[]'.", "models/embedChats.ts(125,7): error TS2322: Type '{ include: boolean; id: number; createdAt: Date; prompt: string; response: string; session_id: string; connection_information: string | null; embed_id: number; usersId: number | null; }[]' is not assignable to type 'EmbedChatData[]'.", "models/embedChats.ts(165,7): error TS2322: Type '{ include: boolean; id: number; createdAt: Date; prompt: string; response: string; session_id: string; connection_information: string | null; embed_id: number; usersId: number | null; } | null' is not assignable to type 'EmbedChatData | null'.", "models/embedChats.ts(197,7): error TS2322: Type '{ include: boolean; id: number; createdAt: Date; prompt: string; response: string; session_id: string; connection_information: string | null; embed_id: number; usersId: number | null; }[]' is not assignable to type 'EmbedChatData[]'.", "models/embedConfig.ts(259,7): error TS2322: Type '{ id: number; createdAt: Date; usersId: number | null; uuid: string; enabled: boolean; chat_mode: string; allowlist_domains: string | null; allow_model_override: boolean; allow_temperature_override: boolean; ... 4 more ...; createdBy: number | null; } | null' is not assignable to type 'EmbedConfigData | null'.", "models/embedConfig.ts(277,7): error TS2322: Type '({ workspaces: { id: number; name: string; createdAt: Date; lastUpdatedAt: Date; pfpFilename: string | null; user_id: number; type: string | null; slug: string; sharedWithOrg: boolean; ... 18 more ...; order: number; }; } & { ...; }) | null' is not assignable to type 'EmbedConfigWithWorkspace | null'.", "models/embedConfig.ts(287,9): error TS2322: Type 'WhereClause' is not assignable to type 'embed_configsWhereUniqueInput'.", "models/embedConfig.ts(307,7): error TS2322: Type '{ id: number; createdAt: Date; usersId: number | null; uuid: string; enabled: boolean; chat_mode: string; allowlist_domains: string | null; allow_model_override: boolean; allow_temperature_override: boolean; ... 4 more ...; createdBy: number | null; }[]' is not assignable to type 'EmbedConfigData[]'.", "models/invite.ts(281,17): error TS2322: Type '(number | null)[]' is not assignable to type 'number[]'.", "models/newsMessage.ts(140,9): error TS2322: Type '{ title: string; content: string; priority: NewsPriority; target_roles: string | null; expires_at: Date | null; created_by: number; }' is not assignable to type '(Without<news_messagesCreateInput, news_messagesUncheckedCreateInput> & news_messagesUncheckedCreateInput) | (Without<...> & news_messagesCreateInput)'.", "models/organizationLegalTemplate.ts(47,9): error TS2322: Type '{ organizationId: number; category: string; documentType: string; templateContent: string; templateFormatting: string; customInputs: string | null; }' is not assignable to type '(Without<OrganizationLegalTemplateCreateInput, OrganizationLegalTemplateUncheckedCreateInput> & OrganizationLegalTemplateUncheckedCreateInput) | (Without<...> & OrganizationLegalTemplateCreateInput)'.", "models/passwordRecovery.ts(180,9): error TS2322: Type 'WhereClause' is not assignable to type 'password_reset_tokensWhereUniqueInput'.", "models/promptExamples.ts(64,7): error TS2322: Type '{ title: string; id: number; createdAt: Date; updatedAt: Date; prompt: string; area: string; icon: string | null; workspaceSlug: string; } | null' is not assignable to type 'PromptExampleData | null'.", "models/promptExamples.ts(88,7): error TS2322: Type '{ title: string; id: number; prompt: string; area: string; icon: string | null; workspaceSlug: string; }[]' is not assignable to type 'PromptExampleResponse[]'.", "models/promptExamples.ts(108,13): error TS2322: Type '{ title: string; area: string; prompt: string; icon: string; workspaceSlug: string; }' is not assignable to type '(Without<prompt_examplesCreateInput, prompt_examplesUncheckedCreateInput> & prompt_examplesUncheckedCreateInput) | (Without<...> & prompt_examplesCreateInput)'.", "models/promptExamples.ts(119,31): error TS2322: Type '{ title: string; id: number; createdAt: Date; updatedAt: Date; prompt: string; area: string; icon: string | null; workspaceSlug: string; }[]' is not assignable to type 'PromptExampleData[]'.", "models/promptExamples.ts(138,7): error TS2322: Type '{ title: string; id: number; prompt: string; area: string; icon: string | null; workspaceSlug: string; }[]' is not assignable to type 'PromptExampleResponse[]'.", "models/systemLegalTemplate.ts(45,9): error TS2322: Type '{ category: string; documentType: string; templateContent: string; templateFormatting: string; customInputs: string | null; }' is not assignable to type '(Without<SystemLegalTemplateCreateInput, SystemLegalTemplateUncheckedCreateInput> & SystemLegalTemplateUncheckedCreateInput) | (Without<...> & SystemLegalTemplateCreateInput)'.", "models/threadShare.ts(158,22): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.", "models/userLegalTemplate.ts(46,9): error TS2322: Type '{ userId: number; category: string; documentType: string; templateContent: string; templateFormatting: string; customInputs: string; }' is not assignable to type '(Without<UserLegalTemplateCreateInput, UserLegalTemplateUncheckedCreateInput> & UserLegalTemplateUncheckedCreateInput) | (Without<...> & UserLegalTemplateCreateInput)'.", "models/userPromptLibrary.ts(162,9): error TS2322: Type '{ name: string | null; prompt_text: string | null; description: string | null; user_id: number; }' is not assignable to type '(Without<user_prompt_librariesCreateInput, user_prompt_librariesUncheckedCreateInput> & user_prompt_librariesUncheckedCreateInput) | (Without<...> & user_prompt_librariesCreateInput)'.", "models/userStyleProfile.ts(53,9): error TS2322: Type '{ user_id: number; name: string; instructions: string; is_active: false; }' is not assignable to type '(Without<UserStyleProfileCreateInput, UserStyleProfileUncheckedCreateInput> & UserStyleProfileUncheckedCreateInput) | (Without<...> & UserStyleProfileCreateInput)'.", "models/workspaceAgentInvocation.ts(57,9): error TS2322: Type '{ uuid: string; }' is not assignable to type 'workspace_agent_invocationsWhereUniqueInput'.", "models/workspaceAgentInvocation.ts(123,9): error TS2322: Type 'WhereClause' is not assignable to type 'workspace_agent_invocationsWhereUniqueInput'.", "prisma/backfill-legal-templates.ts(43,7): error TS2322: Type '{ category: string; documentType: string; templateContent: string; templateFormatting: string | null; customInputs: string | null; }' is not assignable to type '(Without<SystemLegalTemplateCreateInput, SystemLegalTemplateUncheckedCreateInput> & SystemLegalTemplateUncheckedCreateInput) | (Without<...> & SystemLegalTemplateCreateInput)'.", "swagger/utils.ts(47,7): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.", "utils/agents/aibitat/index.ts(66,5): error TS2322: Type 'ChatMessage[]' is not assignable to type 'ChatMessageInternal[]'.", "utils/agents/aibitat/index.ts(75,5): error TS2322: Type 'string' is not assignable to type 'AgentProviderType | null'.", "utils/agents/aibitat/index.ts(827,9): error TS2322: Type 'OllamaProvider' is not assignable to type 'ProviderClass'.", "utils/agents/aibitat/index.ts(835,9): error TS2322: Type 'KoboldCPPProvider' is not assignable to type 'ProviderClass'.", "utils/agents/aibitat/index.ts(837,9): error TS2322: Type 'LocalAiProvider' is not assignable to type 'ProviderClass'.", "utils/agents/aibitat/index.ts(847,9): error TS2322: Type 'TextWebGenUiProvider' is not assignable to type 'ProviderClass'.", "utils/agents/aibitat/plugins/chat-history.ts(91,19): error TS2322: Type 'number | null' is not assignable to type 'number'.", "utils/agents/aibitat/plugins/chat-history.ts(92,11): error TS2322: Type 'string | null' is not assignable to type 'number | null | undefined'.", "utils/agents/aibitat/plugins/chat-history.ts(115,19): error TS2322: Type 'number | null' is not assignable to type 'number'.", "utils/agents/aibitat/plugins/chat-history.ts(116,11): error TS2322: Type 'string | null' is not assignable to type 'number | null | undefined'.", "utils/agents/aibitat/plugins/index.ts(39,3): error TS2322: Type 'SQLAgentConfig' is not assignable to type 'AgentPlugin'.", "utils/agents/aibitat/plugins/index.ts(40,3): error TS2322: Type 'CliPluginWrapper' is not assignable to type 'AgentPlugin'.", "utils/agents/aibitat/plugins/index.ts(41,3): error TS2322: Type 'FileHistoryPluginWrapper' is not assignable to type 'AgentPlugin'.", "utils/agents/aibitat/plugins/memory.ts(103,15): error TS2322: Type '\"\"' is not assignable to type '\"search\" | \"store\"'.", "utils/agents/aibitat/plugins/memory.ts(169,21): error TS2322: Type '{ vectorized: boolean; error: string | null; }' is not assignable to type 'VectorStoreResult'.", "utils/agents/aibitat/plugins/summarize.ts(152,21): error TS2322: Type 'never[] | null' is not assignable to type 'DocumentInfo[]'.", "utils/agents/aibitat/plugins/web-scraping.ts(94,19): error TS2322: Type 'false | LinkContentResponse' is not assignable to type 'ScrapeResponse'.", "utils/agents/aibitat/providers/ai-provider.ts(53,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.", "utils/agents/aibitat/providers/ai-provider.ts(58,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.", "utils/agents/aibitat/providers/ai-provider.ts(66,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.", "utils/agents/aibitat/providers/ai-provider.ts(74,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.", "utils/agents/aibitat/providers/ai-provider.ts(86,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.", "utils/agents/aibitat/providers/ai-provider.ts(94,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.", "utils/agents/aibitat/providers/ai-provider.ts(102,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.", "utils/agents/aibitat/providers/ai-provider.ts(110,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.", "utils/agents/aibitat/providers/ai-provider.ts(111,11): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.", "utils/agents/aibitat/providers/ai-provider.ts(119,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.", "utils/agents/aibitat/providers/ai-provider.ts(132,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.", "utils/agents/aibitat/providers/ai-provider.ts(140,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.", "utils/agents/aibitat/providers/ai-provider.ts(165,11): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.", "utils/agents/aibitat/providers/ai-provider.ts(173,11): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.", "utils/agents/aibitat/providers/ai-provider.ts(181,11): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.", "utils/agents/aibitat/providers/ai-provider.ts(189,11): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.", "utils/agents/aibitat/providers/ai-provider.ts(197,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.", "utils/agents/aibitat/providers/azure.ts(33,52): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.", "utils/agents/aibitat/providers/azure.ts(134,9): error TS2322: Type 'ChatResponseMessage | undefined' is not assignable to type '{ content?: string | undefined; } | undefined'.", "utils/agents/aibitat/providers/bedrock.ts(40,51): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.", "utils/agents/aibitat/providers/bedrock.ts(163,9): error TS2322: Type 'AIMessageChunk' is not assignable to type '{ content?: string | undefined; }'.", "utils/agents/aibitat/providers/deepseek.ts(34,49): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.", "utils/agents/aibitat/providers/deepseek.ts(63,5): error TS2322: Type 'number | null' is not assignable to type 'number'.", "utils/agents/aibitat/providers/deepseek.ts(143,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.", "utils/agents/aibitat/providers/fireworksai.ts(27,52): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.", "utils/agents/aibitat/providers/fireworksai.ts(116,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content: string; }'.", "utils/agents/aibitat/providers/genericOpenAi.ts(38,54): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.", "utils/agents/aibitat/providers/genericOpenAi.ts(68,5): error TS2322: Type 'number | null' is not assignable to type 'number'.", "utils/agents/aibitat/providers/genericOpenAi.ts(163,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.", "utils/agents/aibitat/providers/groq.ts(33,45): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.", "utils/agents/aibitat/providers/groq.ts(134,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.", "utils/agents/aibitat/providers/koboldcpp.ts(32,50): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.", "utils/agents/aibitat/providers/koboldcpp.ts(135,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.", "utils/agents/aibitat/providers/lmstudio.ts(32,49): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.", "utils/agents/aibitat/providers/lmstudio.ts(135,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.", "utils/agents/aibitat/providers/localai.ts(33,48): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.", "utils/agents/aibitat/providers/localai.ts(137,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.", "utils/agents/aibitat/providers/mistral.ts(27,48): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.", "utils/agents/aibitat/providers/mistral.ts(122,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.", "utils/agents/aibitat/providers/ollama.ts(30,47): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.", "utils/agents/aibitat/providers/openrouter.ts(32,51): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.", "utils/agents/aibitat/providers/openrouter.ts(139,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.", "utils/agents/aibitat/providers/perplexity.ts(32,51): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.", "utils/agents/aibitat/providers/perplexity.ts(135,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.", "utils/agents/aibitat/providers/textgenwebui.ts(32,53): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.", "utils/agents/aibitat/providers/textgenwebui.ts(135,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.", "utils/agents/aibitat/providers/togetherai.ts(32,51): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.", "utils/agents/aibitat/providers/togetherai.ts(135,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.", "utils/agents/aibitat/providers/xai.ts(32,44): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.", "utils/agents/aibitat/providers/xai.ts(133,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.", "utils/agents/ephemeral.ts(232,7): error TS2322: Type 'ChatHistoryEntry[]' is not assignable to type 'ChatMessage[]'.", "utils/agents/imported.ts(65,11): error TS2322: Type 'PluginConfig | null' is not assignable to type 'PluginConfig'.", "utils/agents/imported.ts(102,13): error TS2322: Type 'PluginConfig | null' is not assignable to type 'PluginConfig'.", "utils/agents/imported.ts(126,13): error TS2322: Type 'PluginConfig | null' is not assignable to type 'PluginConfig'.", "utils/agents/index.ts(480,5): error TS2322: Type '(string | AgentFunction)[]' is not assignable to type 'string[]'.", "utils/AiProviders/anthropic/index.ts(388,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.", "utils/AiProviders/bedrock/index.ts(280,9): error TS2322: Type '{}' is not assignable to type 'string'.", "utils/AiProviders/bedrock/index.ts(284,7): error TS2322: Type '{}' is not assignable to type 'string'.", "utils/AiProviders/fireworksAi/index.ts(41,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.", "utils/AiProviders/fireworksAi/index.ts(175,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.", "utils/AiProviders/gemini/index.ts(377,9): error TS2322: Type 'string | { text?: string | undefined; inlineData?: { data: string; mimeType: string; } | undefined; }[]' is not assignable to type 'string | (TextContent | ImageContent)[]'.", "utils/AiProviders/genericOpenAi/index.ts(56,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.", "utils/AiProviders/genericOpenAi/index.ts(64,5): error TS2322: Type 'number | null' is not assignable to type 'number'.", "utils/AiProviders/genericOpenAi/index.ts(260,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.", "utils/AiProviders/huggingface/index.ts(180,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.", "utils/AiProviders/koboldCPP/index.ts(45,7): error TS2322: Type 'null' is not assignable to type 'string | undefined'.", "utils/AiProviders/koboldCPP/index.ts(240,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.", "utils/AiProviders/liteLLM/index.ts(41,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.", "utils/AiProviders/liteLLM/index.ts(180,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.", "utils/AiProviders/lmStudio/index.ts(36,7): error TS2322: Type 'null' is not assignable to type 'string | undefined'.", "utils/AiProviders/lmStudio/index.ts(163,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.", "utils/AiProviders/localAi/index.ts(37,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.", "utils/AiProviders/localAi/index.ts(165,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.", "utils/AiProviders/mistral/index.ts(179,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.", "utils/AiProviders/native/index.ts(244,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.", "utils/AiProviders/ollama/index.ts(549,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.", "utils/AiProviders/openRouter/index.ts(36,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.", "utils/AiProviders/openRouter/index.ts(172,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.", "utils/AiProviders/perplexity/index.ts(42,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.", "utils/AiProviders/perplexity/index.ts(179,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.", "utils/AiProviders/textGenWebUI/index.ts(36,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.", "utils/AiProviders/textGenWebUI/index.ts(166,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.", "utils/AiProviders/togetherAi/index.ts(42,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.", "utils/AiProviders/togetherAi/index.ts(173,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.", "utils/chats/apiChatHandler.ts(128,49): error TS2322: Type 'string | null' is not assignable to type 'string'.", "utils/chats/apiChatHandler.ts(135,7): error TS2322: Type 'string | null' is not assignable to type 'string'.", "utils/chats/apiChatHandler.ts(223,5): error TS2322: Type 'User | null' is not assignable to type 'User | null | undefined'.", "utils/chats/apiChatHandler.ts(224,5): error TS2719: Type 'Workspace' is not assignable to type 'Workspace'. Two different types with this name exist, but they are unrelated.", "utils/chats/apiChatHandler.ts(225,5): error TS2322: Type 'Thread | null' is not assignable to type 'Thread | null | undefined'.", "utils/chats/apiChatHandler.ts(516,49): error TS2322: Type 'string | null' is not assignable to type 'string'.", "utils/chats/apiChatHandler.ts(523,7): error TS2322: Type 'string | null' is not assignable to type 'string'.", "utils/chats/apiChatHandler.ts(614,5): error TS2322: Type 'User | null' is not assignable to type 'User | null | undefined'.", "utils/chats/apiChatHandler.ts(615,5): error TS2719: Type 'Workspace' is not assignable to type 'Workspace'. Two different types with this name exist, but they are unrelated.", "utils/chats/apiChatHandler.ts(616,5): error TS2322: Type 'Thread | null' is not assignable to type 'Thread | null | undefined'.", "utils/chats/commands/reset.ts(16,7): error TS2322: Type '(workspace: Workspace, _message: string, msgUUID: string, user?: User | null, thread?: Thread | null) => Promise<CommandResponse>' is not assignable to type 'CommandHandler'.", "utils/chats/commands/reset.ts(39,5): error TS2322: Type '\"reset_chat\"' is not assignable to type '\"stop\" | \"continue\" | \"reset\" | undefined'.", "utils/chats/embed.ts(245,5): error TS2322: Type 'string' is not assignable to type 'number'.", "utils/chats/flows/configurations/referenceFlowConfig.ts(759,11): error TS2322: Type '{ type: string; severity: string; requirement: string; issue: string; location: string; recommendation: string; }[]' is not assignable to type 'ComplianceIssue[]'.", "utils/chats/flows/configurations/referenceFlowConfig.ts(1160,5): error TS2322: Type 'string' is not assignable to type '{ [key: string]: any; SYSTEM_PROMPT?: string | undefined; USER_PROMPT?: string | undefined; PROMPT_TEMPLATE?: string | undefined; }'.", "utils/chats/flows/configurations/referenceFlowConfig.ts(1161,5): error TS2322: Type 'string' is not assignable to type '{ [key: string]: any; SYSTEM_PROMPT?: string | undefined; USER_PROMPT?: string | undefined; PROMPT_TEMPLATE?: string | undefined; }'.", "utils/chats/flows/configurations/referenceFlowConfig.ts(1162,5): error TS2322: Type 'string' is not assignable to type '{ [key: string]: any; SYSTEM_PROMPT?: string | undefined; USER_PROMPT?: string | undefined; PROMPT_TEMPLATE?: string | undefined; }'.", "utils/chats/flows/configurations/referenceFlowConfig.ts(1163,5): error TS2322: Type 'string' is not assignable to type '{ [key: string]: any; SYSTEM_PROMPT?: string | undefined; USER_PROMPT?: string | undefined; PROMPT_TEMPLATE?: string | undefined; }'.", "utils/chats/flows/core/FlowOrchestrator.ts(82,5): error TS2322: Type 'import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/abort-controller/dist/abort-controller\").AbortSignal | undefined' is not assignable to type 'AbortSignal | undefined'.", "utils/chats/flows/core/FlowOrchestrator.ts(312,9): error TS2322: Type 'import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/flows/core/ProgressManager\").ProgressManager' is not assignable to type 'import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow\").ProgressManager'.", "utils/chats/flows/core/FlowOrchestrator.ts(313,9): error TS2322: Type 'import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/flows/core/WorkspaceManager\").WorkspaceManager' is not assignable to type 'import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow\").WorkspaceManager'.", "utils/chats/flows/core/LLMCoordinator.ts(232,5): error TS2322: Type 'CompletionResponse | null' is not assignable to type 'ChatCompletionResult'.", "utils/chats/flows/demo/modularFlowDemo.ts(64,11): error TS2322: Type 'import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow\").ExecutionSummary' is not assignable to type 'ExecutionSummary'.", "utils/chats/flows/processors/CombinationProcessorFactory.ts(40,13): error TS2322: Type '{ id: number; createdAt: Date; lastUpdatedAt: Date; label: string; value: string | null; } | \"regular\"' is not assignable to type 'ProcessorType'.", "utils/chats/flows/processors/DocumentProcessingStageProcessor.ts(260,7): error TS2322: Type 'ProcessedDocument[]' is not assignable to type 'import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow\").ProcessedDocument[]'.", "utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(326,13): error TS2322: Type 'ParseResult<any>' is not assignable to type 'ParseResponse'.", "utils/chats/flows/processors/LegalMemoProcessor.ts(190,9): error TS2322: Type 'SectionWithMemos[]' is not assignable to type 'Section[]'.", "utils/chats/openaiCompatible.ts(562,11): error TS2322: Type 'boolean' is not assignable to type 'string'.", "utils/chats/openaiCompatible.ts(584,9): error TS2322: Type 'boolean' is not assignable to type 'string'.", "utils/chats/streamCanvas.ts(222,9): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.", "utils/chats/streamCanvas.ts(223,9): error TS2322: Type 'import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/models\").Workspace' is not assignable to type 'Workspace'.", "utils/chats/streamCanvas.ts(224,9): error TS2322: Type 'WorkspaceThread | null' is not assignable to type 'Thread | null | undefined'.", "utils/chats/streamCanvas.ts(227,7): error TS2322: Type 'any[]' is not assignable to type 'string'.", "utils/chats/streamCanvas.ts(428,7): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.", "utils/chats/streamCDB.ts(232,9): error TS2322: Type 'boolean | Error' is not assignable to type 'string | boolean | null | undefined'.", "utils/chats/streamDD.ts(1431,13): error TS2322: Type 'string | null' is not assignable to type 'string | number | undefined'.", "utils/chats/streamDD.ts(1915,13): error TS2322: Type 'string | null' is not assignable to type 'string | number | undefined'.", "utils/chats/streamDD.ts(2004,9): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.", "utils/chats/streamDD.ts(2008,7): error TS2322: Type 'number | undefined' is not assignable to type 'string | null'.", "utils/chats/streamDD.ts(2081,9): error TS2322: Type 'string | null' is not assignable to type 'string | number | undefined'.", "utils/chats/streamLQA.ts(205,5): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.", "utils/chats/streamLQA.ts(206,5): error TS2322: Type 'import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/models\").Workspace' is not assignable to type 'Workspace'.", "utils/chats/streamLQA.ts(207,5): error TS2322: Type 'WorkspaceThread | null' is not assignable to type 'Thread | null | undefined'.", "utils/chats/streamLQA.ts(485,7): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.", "utils/chats/streamLQA.ts(492,5): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.", "utils/chats/streamLQA.ts(493,5): error TS2322: Type 'import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/models\").Workspace' is not assignable to type 'Workspace'.", "utils/chats/streamLQA.ts(494,5): error TS2322: Type 'WorkspaceThread | null' is not assignable to type 'Thread | null | undefined'.", "utils/chats/streamLQA.ts(697,13): error TS2322: Type '{ adjacentVector: string | null; keepPdrVectors: boolean; globalPdrOverride: boolean; }' is not assignable to type 'PdrSettings'.", "utils/chats/streamLQA.ts(843,5): error TS2322: Type '(string | { text: string; meta: string; })[]' is not assignable to type 'string[]'.", "utils/chats/streamLQA.ts(896,11): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.", "utils/chats/streamLQA.ts(1120,13): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.", "utils/chats/streamLQA.ts(1123,11): error TS2322: Type 'number | undefined' is not assignable to type 'string | null'.", "utils/chats/streamLQA.ts(1193,7): error TS2322: Type 'string | null' is not assignable to type 'string | number | undefined'.", "utils/collectorApi/index.ts(114,5): error TS2322: Type 'unknown' is not assignable to type 'false | ProcessDocumentResponse'.", "utils/collectorApi/index.ts(142,5): error TS2322: Type 'unknown' is not assignable to type 'false | ProcessLinkResponse'.", "utils/collectorApi/index.ts(169,5): error TS2322: Type 'unknown' is not assignable to type 'ProcessRawTextResponse'.", "utils/collectorApi/index.ts(199,5): error TS2322: Type 'unknown' is not assignable to type 'ExtensionResponse'.", "utils/collectorApi/index.ts(227,5): error TS2322: Type 'unknown' is not assignable to type 'false | LinkContentResponse'.", "utils/contextualization/index.ts(91,7): error TS2322: Type 'CompletionResponse | null' is not assignable to type 'string'.", "utils/DeepSearch/deepsearchproviders/bing.ts(86,13): error TS2322: Type 'unknown' is not assignable to type 'BingApiResponse'.", "utils/DeepSearch/deepsearchproviders/brave.ts(121,13): error TS2322: Type 'unknown' is not assignable to type 'BraveApiResponse'.", "utils/DeepSearch/deepsearchproviders/google.ts(199,9): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.", "utils/DeepSearch/deepsearchproviders/google.ts(229,13): error TS2322: Type 'Candidate | undefined' is not assignable to type 'GoogleCandidate | undefined'.", "utils/documentEditing/workflows/lineLevelWorkflow.ts(134,9): error TS2322: Type 'DocumentEditingSuggestion[]' is not assignable to type 'EditingSuggestion[]'.", "utils/documentEditing/workflows/lineLevelWorkflow.ts(220,7): error TS2322: Type 'DocumentEditingSuggestion[]' is not assignable to type 'EditingSuggestion[]'.", "utils/docx/editWithLLM.ts(80,11): error TS2322: Type 'unknown' is not assignable to type 'LLMProcessorResponse'.", "utils/EmbeddingEngines/genericOpenAi/index.ts(34,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.", "utils/EmbeddingEngines/jina/index.ts(193,19): error TS2322: Type 'unknown' is not assignable to type 'JinaApiResponse'.", "utils/EmbeddingEngines/liteLLM/index.ts(28,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.", "utils/EmbeddingEngines/localAi/index.ts(28,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.", "utils/EmbeddingEngines/native/index.ts(86,41): error TS2345: Argument of type 'any[]' is not assignable to parameter of type '[task: PipelineType, model?: string | undefined, (PretrainedOptions | undefined)?]'.", "utils/EmbeddingEngines/ollama/index.ts(79,15): error TS2322: Type 'unknown' is not assignable to type 'OllamaEmbeddingResponse'.", "utils/EmbeddingEngines/voyageAi/index.ts(54,5): error TS2322: Type 'number[] | number[][]' is not assignable to type 'number[]'.", "utils/helpers/autoCodingPromptGenerator.ts(934,9): error TS2322: Type 'undefined' is not assignable to type 'string'.", "utils/helpers/camelcase.ts(100,5): error TS2322: Type 'undefined' is not assignable to type 'string | false'.", "utils/helpers/chat/convertTo.ts(181,5): error TS2322: Type 'WorkspaceChatWithData[]' is not assignable to type 'ChatRecord[]'.", "utils/helpers/chat/convertTo.ts(185,5): error TS2322: Type 'EmbedChatWithWorkspace[]' is not assignable to type 'ChatRecord[]'.", "utils/helpers/chat/convertTo.ts(418,5): error TS2322: Type '{ sources: never[]; } | null' is not assignable to type 'ResponseData'.", "utils/helpers/chat/index.ts(932,3): error TS2322: Type 'null' is not assignable to type 'TokenManager'.", "utils/helpers/chat/index.ts(933,3): error TS2322: Type 'null' is not assignable to type 'string'.", "utils/helpers/versionComparison.ts(130,5): error TS2322: Type '{ [key: string]: any; versions?: VersionObject[]; version?: string; description?: string; timestamp?: string; }' is not assignable to type 'VersionObject'.", "utils/middleware/validBrowserExtensionApiKey.ts(36,7): error TS2322: Type 'number | null' is not assignable to type 'string | number | undefined'.", "utils/robustLlmUtils/connectors/robustConnector.ts(224,13): error TS2322: Type '{ rawResponse: any; metrics: any; success: boolean; data: any; error: string | null; attemptsMade?: number; }' is not assignable to type 'JsonResponseResult'.", "utils/robustLlmUtils/connectors/robustConnector.ts(277,7): error TS2322: Type 'null' is not assignable to type 'string | undefined'."], "duplicateIdentifiers": ["utils/chats/flows/configurations/mainDocFlowConfig.ts(9,10): error TS2300: Duplicate identifier 'FlowConfiguration'.", "utils/chats/flows/configurations/mainDocFlowConfig.ts(10,10): error TS2300: Duplicate identifier 'FlowConfiguration'.", "utils/chats/flows/configurations/noMainDocFlowConfig.ts(9,10): error TS2300: Duplicate identifier 'FlowConfiguration'.", "utils/chats/flows/configurations/noMainDocFlowConfig.ts(10,10): error TS2300: Duplicate identifier 'FlowConfiguration'."], "cannotFind": ["endpoints/workspaces.ts(1827,26): error TS2552: Cannot find name 'documents'. Did you mean 'document'?", "swagger/index.ts(9,48): error TS2304: Cannot find name 'El<PERSON>'.", "swagger/index.ts(11,29): error TS2584: Cannot find name 'document'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "swagger/index.ts(16,26): error TS2304: Cannot find name 'MutationObserver'.", "swagger/index.ts(17,23): error TS2584: Cannot find name 'document'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "swagger/index.ts(24,22): error TS2584: Cannot find name 'document'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "swagger/index.ts(33,7): error TS2304: Cannot find name 'window'.", "swagger/index.ts(34,35): error TS2304: Cannot find name 'window'.", "swagger/index.ts(36,35): error TS2304: Cannot find name 'window'.", "swagger/index.ts(36,67): error TS2304: Cannot find name 'window'.", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(72,17): error TS2552: Cannot find name 'ProseMirrorDocument'. Did you mean 'LocalProseMirrorDocument'?", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(88,10): error TS2552: Cannot find name 'ProseMirrorDocument'. Did you mean 'LocalProseMirrorDocument'?", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(195,36): error TS2552: Cannot find name 'ProseMirrorDocument'. Did you mean 'LocalProseMirrorDocument'?", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(226,30): error TS2552: Cannot find name 'ProseMirrorDocument'. Did you mean 'LocalProseMirrorDocument'?", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(340,28): error TS2552: Cannot find name 'ProseMirrorDocument'. Did you mean 'LocalProseMirrorDocument'?", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(486,12): error TS2552: Cannot find name 'ProseMirrorDocument'. Did you mean 'LocalProseMirrorDocument'?", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(498,14): error TS2304: Cannot find name 'ProseMirrorDocument'.", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(627,12): error TS2304: Cannot find name 'ProseMirrorDocument'.", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(640,14): error TS2304: Cannot find name 'ProseMirrorDocument'.", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(884,12): error TS2304: Cannot find name 'ProseMirrorDocument'.", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(1016,8): error TS2304: Cannot find name 'ProseMirrorDocument'.", "utils/chats/flows/processors/LegalMemoProcessor.ts(315,19): error TS2304: Cannot find name 'FlowDependencies'.", "utils/chats/flows/processors/LegalMemoProcessor.ts(414,19): error TS2304: Cannot find name 'FlowDependencies'.", "utils/helpers/chat/reasoningResponses.ts(207,11): error TS2304: Cannot find name 'contentStarted'.", "utils/helpers/index.ts(67,13): error TS2304: Cannot find name 'ChatMessage'.", "utils/helpers/index.ts(75,13): error TS2304: Cannot find name 'ChatMessage'.", "utils/helpers/index.ts(88,36): error TS2304: Cannot find name 'LLMProviderStatic'."], "other": ["endpoints/api/admin/index.ts(71,5): error TS2769: No overload matches this call.", "endpoints/api/admin/index.ts(71,5): error TS7030: Not all code paths return a value.", "endpoints/api/admin/index.ts(199,5): error TS2769: No overload matches this call.", "endpoints/api/admin/index.ts(199,5): error TS7030: Not all code paths return a value.", "endpoints/api/admin/index.ts(304,5): error TS2769: No overload matches this call.", "endpoints/api/admin/index.ts(304,5): error TS7030: Not all code paths return a value.", "endpoints/api/admin/index.ts(376,5): error TS2769: No overload matches this call.", "endpoints/api/admin/index.ts(376,5): error TS7030: Not all code paths return a value.", "endpoints/api/admin/index.ts(427,5): error TS2769: No overload matches this call.", "endpoints/api/admin/index.ts(427,5): error TS7030: Not all code paths return a value.", "endpoints/api/admin/index.ts(502,5): error TS2769: No overload matches this call.", "endpoints/api/admin/index.ts(502,5): error TS7030: Not all code paths return a value.", "endpoints/api/admin/index.ts(554,5): error TS2769: No overload matches this call.", "endpoints/api/admin/index.ts(554,5): error TS7030: Not all code paths return a value.", "endpoints/api/admin/index.ts(609,5): error TS2769: No overload matches this call.", "endpoints/api/admin/index.ts(609,5): error TS7030: Not all code paths return a value.", "endpoints/api/admin/index.ts(676,5): error TS2769: No overload matches this call.", "endpoints/api/admin/index.ts(676,5): error TS7030: Not all code paths return a value.", "endpoints/api/admin/index.ts(746,5): error TS2769: No overload matches this call.", "endpoints/api/admin/index.ts(746,5): error TS7030: Not all code paths return a value.", "endpoints/api/admin/index.ts(807,5): error TS2769: No overload matches this call.", "endpoints/api/admin/index.ts(807,5): error TS7030: Not all code paths return a value.", "endpoints/api/admin/index.ts(878,5): error TS2769: No overload matches this call.", "endpoints/api/admin/index.ts(878,5): error TS7030: Not all code paths return a value.", "endpoints/api/admin/system.ts(58,5): error TS2769: No overload matches this call.", "endpoints/api/admin/system.ts(58,5): error TS7030: Not all code paths return a value.", "endpoints/api/admin/system.ts(176,5): error TS2769: No overload matches this call.", "endpoints/api/admin/system.ts(176,5): error TS7030: Not all code paths return a value.", "endpoints/api/document/index.ts(557,59): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{}'.", "endpoints/api/docx-edit/index.ts(17,8): error TS1192: Module '\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/i18n\"' has no default export.", "endpoints/api/docx-edit/index.ts(42,11): error TS2430: Interface 'FileUploadRequest' incorrectly extends interface 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.", "endpoints/api/docx-edit/index.ts(79,33): error TS2345: Argument of type 'FileUploadRequest' is not assignable to parameter of type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.", "endpoints/api/docx-edit/index.ts(536,33): error TS2345: Argument of type 'FileUploadRequest' is not assignable to parameter of type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.", "endpoints/api/embed/index.ts(18,5): error TS2769: No overload matches this call.", "endpoints/api/embed/index.ts(18,5): error TS7030: Not all code paths return a value.", "endpoints/api/embed/index.ts(91,5): error TS2769: No overload matches this call.", "endpoints/api/embed/index.ts(91,5): error TS7030: Not all code paths return a value.", "endpoints/api/embed/index.ts(163,5): error TS2769: No overload matches this call.", "endpoints/api/embed/index.ts(163,5): error TS7030: Not all code paths return a value.", "endpoints/api/openai/index.ts(144,37): error TS2352: Conversion of type '{ id: string; type: \"abort\"; textResponse: null; sources: never[]; close: true; error: string; }' to type 'StreamResponseChunk' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "endpoints/api/openai/index.ts(279,15): error TS7034: Variable 'data' implicitly has type 'any[]' in some locations where its type cannot be determined.", "endpoints/api/openai/index.ts(280,9): error TS18047: 'embeddings' is possibly 'null'.", "endpoints/api/openai/index.ts(290,11): error TS7005: Variable 'data' implicitly has an 'any[]' type.", "endpoints/api/system/index.ts(51,5): error TS2769: No overload matches this call.", "endpoints/api/system/index.ts(51,5): error TS7030: Not all code paths return a value.", "endpoints/api/system/index.ts(93,5): error TS2769: No overload matches this call.", "endpoints/api/system/index.ts(93,5): error TS7030: Not all code paths return a value.", "endpoints/api/system/index.ts(130,5): error TS2769: No overload matches this call.", "endpoints/api/system/index.ts(130,5): error TS7030: Not all code paths return a value.", "endpoints/api/system/index.ts(180,5): error TS2769: No overload matches this call.", "endpoints/api/system/index.ts(242,5): error TS2769: No overload matches this call.", "endpoints/api/system/index.ts(242,5): error TS7030: Not all code paths return a value.", "endpoints/api/system/index.ts(310,5): error TS2769: No overload matches this call.", "endpoints/api/system/index.ts(310,5): error TS7030: Not all code paths return a value.", "endpoints/api/workspace/index.ts(1011,41): error TS2345: Argument of type '{ response: Response<any, Record<string, any>>; workspace: any; message: string; mode: \"chat\" | \"query\"; user: null; thread: null; attachments: any[]; useDeepSearch: boolean; }' is not assignable to parameter of type 'StreamChatParams'.", "endpoints/api/workspaceThread/index.ts(48,11): error TS2430: Interface 'ThreadStreamChatRequestBody' incorrectly extends interface 'ThreadChatRequestBody'.", "endpoints/api/workspaceThread/index.ts(70,5): error TS2769: No overload matches this call.", "endpoints/api/workspaceThread/index.ts(70,5): error TS7030: Not all code paths return a value.", "endpoints/api/workspaceThread/index.ts(308,5): error TS2769: No overload matches this call.", "endpoints/api/workspaceThread/index.ts(308,5): error TS7030: Not all code paths return a value.", "endpoints/api/workspaceThread/index.ts(383,42): error TS2352: Conversion of type '{ history: Promise<ChatMessage[]>; }' to type 'ThreadChatHistoryResponse' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "endpoints/api/workspaceThread/index.ts(384,50): error TS2345: Argument of type 'WorkspaceChat[]' is not assignable to parameter of type 'ChatRecord[]'.", "endpoints/api/workspaceThread/index.ts(396,5): error TS2769: No overload matches this call.", "endpoints/api/workspaceThread/index.ts(396,5): error TS7030: Not all code paths return a value.", "endpoints/api/workspaceThread/index.ts(486,59): error TS2345: Argument of type 'string' is not assignable to parameter of type 'ChatMode'.", "endpoints/api/workspaceThread/index.ts(668,59): error TS2345: Argument of type 'string' is not assignable to parameter of type 'ChatMode'.", "endpoints/api/workspaceThread/index.ts(732,38): error TS2352: Conversion of type '{ id: string; type: \"abort\"; textResponse: null; sources: never[]; close: true; error: string; }' to type 'StreamResponseChunk' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "endpoints/browserExtension.ts(17,11): error TS6196: 'LocalsUser' is declared but never used.", "endpoints/browserExtension.ts(82,39): error TS2769: No overload matches this call.", "endpoints/browserExtension.ts(115,5): error TS2769: No overload matches this call.", "endpoints/browserExtension.ts(115,5): error TS7030: Not all code paths return a value.", "endpoints/browserExtension.ts(126,15): error TS2554: Expected 1-2 arguments, but got 3.", "endpoints/browserExtension.ts(130,15): error TS2345: Argument of type 'string | false' is not assignable to parameter of type 'boolean | undefined'.", "endpoints/browserExtension.ts(162,5): error TS2769: No overload matches this call.", "endpoints/browserExtension.ts(162,5): error TS7030: Not all code paths return a value.", "endpoints/browserExtension.ts(217,40): error TS18047: 'apiKey' is possibly 'null'.", "endpoints/browserExtension.ts(231,5): error TS2769: No overload matches this call.", "endpoints/browserExtension.ts(231,5): error TS7030: Not all code paths return a value.", "endpoints/browserExtension.ts(245,72): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.", "endpoints/browserExtension.ts(246,39): error TS2769: No overload matches this call.", "endpoints/document.ts(762,5): error TS2769: No overload matches this call.", "endpoints/document.ts(762,5): error TS7030: Not all code paths return a value.", "endpoints/document.ts(813,25): error TS7006: Parameter 'docpath' implicitly has an 'any' type.", "endpoints/document.ts(1305,17): error TS2345: Argument of type '{ success: false; error: string; }' is not assignable to parameter of type 'ResolvePathResponse'.", "endpoints/document.ts(1387,32): error TS2345: Argument of type '{ success: false; error: string; }' is not assignable to parameter of type 'ResolvePathResponse'.", "endpoints/embed/index.ts(76,48): error TS2345: Argument of type 'EmbedConfig' is not assignable to parameter of type 'EmbedConfig'.", "endpoints/embed/index.ts(107,38): error TS2345: Argument of type '{ id: string; type: string; textResponse: null; close: true; error: string; }' is not assignable to parameter of type 'ResponseChunkData'.", "endpoints/embed/index.ts(122,5): error TS2769: No overload matches this call.", "endpoints/embed/index.ts(122,5): error TS7030: Not all code paths return a value.", "endpoints/embed/index.ts(145,5): error TS2769: No overload matches this call.", "endpoints/embed/index.ts(145,5): error TS7030: Not all code paths return a value.", "endpoints/embedManagement.ts(74,61): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.", "endpoints/experimental/imported-agent-plugins.ts(24,5): error TS2769: No overload matches this call.", "endpoints/experimental/imported-agent-plugins.ts(24,5): error TS7030: Not all code paths return a value.", "endpoints/experimental/imported-agent-plugins.ts(42,5): error TS2769: No overload matches this call.", "endpoints/experimental/imported-agent-plugins.ts(42,5): error TS7030: Not all code paths return a value.", "endpoints/experimental/liveSync.ts(31,5): error TS2769: No overload matches this call.", "endpoints/experimental/liveSync.ts(31,5): error TS7030: Not all code paths return a value.", "endpoints/experimental/liveSync.ts(107,5): error TS2769: No overload matches this call.", "endpoints/experimental/liveSync.ts(107,5): error TS7030: Not all code paths return a value.", "endpoints/extensions/index.ts(25,5): error TS2769: No overload matches this call.", "endpoints/extensions/index.ts(25,5): error TS7030: Not all code paths return a value.", "endpoints/extensions/index.ts(50,5): error TS2769: No overload matches this call.", "endpoints/extensions/index.ts(50,5): error TS7030: Not all code paths return a value.", "endpoints/extensions/index.ts(74,5): error TS2769: No overload matches this call.", "endpoints/extensions/index.ts(74,5): error TS7030: Not all code paths return a value.", "endpoints/extensions/index.ts(96,5): error TS2769: No overload matches this call.", "endpoints/extensions/index.ts(96,5): error TS7030: Not all code paths return a value.", "endpoints/extensions/index.ts(118,5): error TS2769: No overload matches this call.", "endpoints/extensions/index.ts(118,5): error TS7030: Not all code paths return a value.", "endpoints/generateLegalTaskPrompt.ts(229,5): error TS2769: No overload matches this call.", "endpoints/getCDBDocumentation.ts(31,5): error TS2769: No overload matches this call.", "endpoints/invite.ts(97,45): error TS2345: Argument of type 'FilteredUser' is not assignable to parameter of type '{ id: number; createdAt: Date; lastUpdatedAt: Date; organizationId: number | null; username: string | null; password: string; pfpFilename: string | null; role: string; suspended: number; ... 4 more ...; custom_system_prompt: string | null; }'.", "endpoints/news.ts(84,5): error TS2769: No overload matches this call.", "endpoints/news.ts(84,5): error TS7030: Not all code paths return a value.", "endpoints/news.ts(93,17): error TS2339: Property 'view' does not exist on type 'DismissResult'.", "endpoints/news.ts(112,5): error TS2769: No overload matches this call.", "endpoints/news.ts(112,5): error TS7030: Not all code paths return a value.", "endpoints/news.ts(141,5): error TS2769: No overload matches this call.", "endpoints/news.ts(141,5): error TS7030: Not all code paths return a value.", "endpoints/news.ts(260,5): error TS2769: No overload matches this call.", "endpoints/news.ts(260,5): error TS7030: Not all code paths return a value.", "endpoints/news.ts(303,5): error TS2769: No overload matches this call.", "endpoints/news.ts(303,5): error TS7030: Not all code paths return a value.", "endpoints/news.ts(330,5): error TS2769: No overload matches this call.", "endpoints/news.ts(330,5): error TS7030: Not all code paths return a value.", "endpoints/requestLegalAssistance.ts(65,5): error TS2769: No overload matches this call.", "endpoints/requestLegalAssistance.ts(65,5): error TS7030: Not all code paths return a value.", "endpoints/system.ts(886,63): error TS2345: Argument of type '{ role: string; content: string; }[]' is not assignable to parameter of type 'ChatMessage[]'.", "endpoints/system/systemLegalTemplates.ts(85,32): error TS2345: Argument of type 'FilteredUser | null' is not assignable to parameter of type 'AuthenticatedUser | null'.", "endpoints/system/systemLegalTemplates.ts(111,32): error TS2345: Argument of type 'FilteredUser | null' is not assignable to parameter of type 'AuthenticatedUser | null'.", "endpoints/system/systemLegalTemplates.ts(174,32): error TS2345: Argument of type 'FilteredUser | null' is not assignable to parameter of type 'AuthenticatedUser | null'.", "endpoints/system/systemLegalTemplates.ts(237,32): error TS2345: Argument of type 'FilteredUser | null' is not assignable to parameter of type 'AuthenticatedUser | null'.", "endpoints/system/systemLegalTemplates.ts(275,32): error TS2345: Argument of type 'FilteredUser | null' is not assignable to parameter of type 'AuthenticatedUser | null'.", "endpoints/userCustomAiSettings.ts(104,32): error TS2339: Property 'getAll' does not exist on type '{ protectedFields: string[]; publicFields: string[]; supportedFields: string[]; validations: { login_ui: (value: any) => string; deep_search_context_percentage: (update: any) => number; footer_data: (updates: any) => string; ... 55 more ...; flow_referencefiles_compliance_threshold: (value: string | number) => strin...'.", "endpoints/userPromptLibrary.ts(193,17): error TS2739: Type 'UserPromptLibraryData' is missing the following properties from type 'UserPromptData': createdAt, updatedAt", "endpoints/workspaces.ts(310,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(310,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(467,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(467,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(647,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(647,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(707,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(707,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(719,17): error TS2339: Property '_includeDocuments' does not exist on type '{ includeDocuments?: string | undefined; }'.", "endpoints/workspaces.ts(803,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(803,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(827,59): error TS2554: Expected 1-2 arguments, but got 3.", "endpoints/workspaces.ts(856,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(856,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(891,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(891,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(923,17): error TS2339: Property '_includeDocuments' does not exist on type '{ includeDocuments?: string | undefined; }'.", "endpoints/workspaces.ts(983,17): error TS2339: Property '_includeDocuments' does not exist on type '{ includeDocuments?: string | undefined; }'.", "endpoints/workspaces.ts(1029,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(1029,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(1044,62): error TS2345: Argument of type '{ type: string; }' is not assignable to parameter of type 'number'.", "endpoints/workspaces.ts(1063,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(1063,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(1069,17): error TS2339: Property '_includeDocuments' does not exist on type '{ includeDocuments?: string | undefined; }'.", "endpoints/workspaces.ts(1097,59): error TS2554: Expected 1-2 arguments, but got 3.", "endpoints/workspaces.ts(1098,45): error TS2345: Argument of type 'string | false' is not assignable to parameter of type 'boolean | undefined'.", "endpoints/workspaces.ts(1106,67): error TS18047: 'user' is possibly 'null'.", "endpoints/workspaces.ts(1110,64): error TS2345: Argument of type 'WorkspaceChat[]' is not assignable to parameter of type 'ChatRecord[]'.", "endpoints/workspaces.ts(1236,13): error TS2698: Spread types may only be created from object types.", "endpoints/workspaces.ts(1252,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(1252,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(1270,11): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.", "endpoints/workspaces.ts(1327,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(1327,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(1348,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(1348,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(1384,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(1462,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(1530,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(1545,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(1545,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(1640,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(1640,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(1689,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(1689,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(1744,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(1744,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(1761,59): error TS2554: Expected 1-2 arguments, but got 3.", "endpoints/workspaces.ts(1762,45): error TS2345: Argument of type 'string | false' is not assignable to parameter of type 'boolean | undefined'.", "endpoints/workspaces.ts(1768,39): error TS18047: 'user' is possibly 'null'.", "endpoints/workspaces.ts(1805,19): error TS2339: Property '_success' does not exist on type 'ProcessDocumentResponse'.", "endpoints/workspaces.ts(1805,37): error TS2339: Property '_documents' does not exist on type 'ProcessDocumentResponse'.", "endpoints/workspaces.ts(1859,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(1859,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(1870,17): error TS2339: Property '_includeDocuments' does not exist on type '{ includeDocuments?: string | undefined; }'.", "endpoints/workspaces.ts(1894,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(1894,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(1969,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(1969,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(1998,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(1998,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(2034,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(2034,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(2054,5): error TS2769: No overload matches this call.", "endpoints/workspaces.ts(2054,5): error TS7030: Not all code paths return a value.", "endpoints/workspaces.ts(2060,58): error TS2554: Expected 1 arguments, but got 2.", "endpoints/workspaces.ts(2061,29): error TS2554: Expected 1 arguments, but got 0.", "endpoints/workspaceThreads.ts(111,5): error TS2769: No overload matches this call.", "endpoints/workspaceThreads.ts(169,5): error TS2769: No overload matches this call.", "endpoints/workspaceThreads.ts(234,5): error TS2769: No overload matches this call.", "endpoints/workspaceThreads.ts(239,44): error TS2345: Argument of type 'DeleteThreadRequest' is not assignable to parameter of type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.", "endpoints/workspaceThreads.ts(278,5): error TS2769: No overload matches this call.", "endpoints/workspaceThreads.ts(315,5): error TS2769: No overload matches this call.", "endpoints/workspaceThreads.ts(340,63): error TS2345: Argument of type 'WorkspaceChat[]' is not assignable to parameter of type 'ChatRecord[]'.", "endpoints/workspaceThreads.ts(357,5): error TS2769: No overload matches this call.", "endpoints/workspaceThreads.ts(362,44): error TS2345: Argument of type 'UpdateThreadRequest' is not assignable to parameter of type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.", "endpoints/workspaceThreads.ts(363,54): error TS2345: Argument of type 'UpdateThreadRequest' is not assignable to parameter of type 'RequestWithBody'.", "endpoints/workspaceThreads.ts(400,5): error TS2769: No overload matches this call.", "endpoints/workspaceThreads.ts(435,5): error TS2769: No overload matches this call.", "endpoints/workspaceThreads.ts(440,44): error TS2345: Argument of type 'ShareThreadRequest' is not assignable to parameter of type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.", "endpoints/workspaceThreads.ts(441,38): error TS2345: Argument of type 'ShareThreadRequest' is not assignable to parameter of type 'RequestWithBody'.", "endpoints/workspaceThreads.ts(505,5): error TS2769: No overload matches this call.", "index.ts(13,18): error TS7016: Could not find a declaration file for module 'cors'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/cors/lib/index.js' implicitly has an 'any' type.", "index.ts(53,53): error TS2306: File '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/system/organizationLegalTemplates.ts' is not a module.", "index.ts(81,16): error TS2345: Argument of type 'string | 3001' is not assignable to parameter of type 'number | undefined'.", "index.ts(89,17): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.", "index.ts(91,20): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.", "index.ts(92,26): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.", "index.ts(93,15): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.", "index.ts(94,16): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'Express'.", "index.ts(95,25): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'Application'.", "index.ts(96,17): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.", "index.ts(97,26): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'Express'.", "index.ts(98,15): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'Application'.", "index.ts(99,19): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.", "index.ts(100,16): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.", "index.ts(110,19): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.", "index.ts(111,21): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.", "index.ts(125,19): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.", "index.ts(128,27): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.", "index.ts(129,15): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'Application'.", "index.ts(130,25): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'Express'.", "index.ts(136,36): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.", "index.ts(142,29): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'Application'.", "index.ts(148,33): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.", "index.ts(151,28): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.", "index.ts(154,15): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'Express'.", "index.ts(160,30): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.", "index.ts(193,33): error TS2769: No overload matches this call.", "index.ts(193,33): error TS7030: Not all code paths return a value.", "index.ts(207,31): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type 'BaseVectorDatabaseProvider'.", "index.ts(228,46): error TS2345: Argument of type 'string | 3001' is not assignable to parameter of type 'number | undefined'.", "jobs/sync-watched.documents.ts(35,34): error TS2551: Property 'workspace' does not exist on type '{ id: number; createdAt: Date; lastUpdatedAt: Date; workspaceId: number; pdr: boolean | null; docId: string; filename: string; docpath: string; metadata: string | null; pinned: boolean | null; watched: boolean | null; starred: boolean | null; } & { ...; }'. Did you mean 'workspaces'?", "jobs/sync-watched.documents.ts(121,57): error TS2345: Argument of type 'DocumentSyncQueueWithRelations' is not assignable to parameter of type 'DocumentSyncQueueData'.", "jobs/sync-watched.documents.ts(208,58): error TS2345: Argument of type 'DocumentSyncQueueWithRelations' is not assignable to parameter of type 'DocumentSyncQueueData'.", "models/browserExtensionApiKey.ts(4,10): error TS2724: '\"@prisma/client\"' has no exported member named 'User'. Did you mean 'users'?", "models/cacheData.ts(2,10): error TS2724: '\"@prisma/client\"' has no exported member named 'CacheData'. Did you mean 'cache_data'?", "models/documents.ts(171,26): error TS2339: Property 'chunkSource' does not exist on type 'never'.", "models/documents.ts(173,16): error TS2339: Property 'chunkSource' does not exist on type 'never'.", "models/documents.ts(174,16): error TS2339: Property 'chunkSource' does not exist on type 'never'.", "models/documents.ts(926,33): error TS18047: 'doc.metadata' is possibly 'null'.", "models/documentSyncQueue.ts(176,7): error TS2739: Type '{ id: number; createdAt: Date; staleAfterMs: number; nextSyncAt: Date; lastSyncedAt: Date; workspaceDocId: number; }' is missing the following properties from type 'DocumentSyncQueueData': repeatFailures, lastUpdatedAt", "models/documentSyncQueue.ts(250,66): error TS2345: Argument of type '{ include?: IncludeClause | undefined; orderBy?: OrderByClause | undefined; take?: number | undefined; where: WhereClause; }' is not assignable to parameter of type '{ select?: document_sync_queuesSelect<DefaultArgs> | null | undefined; include?: document_sync_queuesInclude<DefaultArgs> | null | undefined; ... 5 more ...; distinct?: Document_sync_queuesScalarFieldEnum | ... 1 more ... | undefined; }'.", "models/documentSyncQueue.ts(311,12): error TS2352: Conversion of type 'DocumentSyncQueueData[]' to type 'DocumentSyncQueueWithRelations[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "models/embedChats.ts(100,16): error TS2741: Property 'lastUpdatedAt' is missing in type '{ include: boolean; id: number; createdAt: Date; prompt: string; response: string; session_id: string; connection_information: string | null; embed_id: number; usersId: number | null; }' but required in type 'EmbedChatData'.", "models/embedChats.ts(214,11): error TS2561: Object literal may only specify known properties, but 'embed_config' does not exist in type 'embed_chatsInclude<DefaultArgs>'. Did you mean to write 'embed_configs'?", "models/embedChats.ts(228,14): error TS2352: Conversion of type '{ include: boolean; id: number; createdAt: Date; prompt: string; response: string; session_id: string; connection_information: string | null; embed_id: number; usersId: number | null; }[]' to type 'EmbedChatWithWorkspace[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "models/embedConfig.ts(216,16): error TS2741: Property 'lastUpdatedAt' is missing in type '{ id: number; createdAt: Date; usersId: number | null; uuid: string; enabled: boolean; chat_mode: string; allowlist_domains: string | null; allow_model_override: boolean; allow_temperature_override: boolean; ... 4 more ...; createdBy: number | null; }' but required in type 'EmbedConfigData'.", "models/embedConfig.ts(331,14): error TS2352: Conversion of type '({ workspaces: { id: number; name: string; createdAt: Date; lastUpdatedAt: Date; pfpFilename: string | null; user_id: number; type: string | null; slug: string; sharedWithOrg: boolean; ... 18 more ...; order: number; }; _count: { ...; }; } & { ...; })[]' to type 'EmbedConfigWithWorkspace[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "models/newsMessage.ts(3,3): error TS6196: 'users' is declared but never used.", "models/passwordRecovery.ts(4,1): error TS6192: All imports in import declaration are unused.", "models/systemReport.ts(226,39): error TS2345: Argument of type 'SystemReportEntity' is not assignable to parameter of type 'SystemReport'.", "models/systemReport.ts(695,31): error TS2345: Argument of type 'SystemReportEntity' is not assignable to parameter of type 'SystemReport'.", "models/systemReport.ts(795,56): error TS2345: Argument of type 'SystemReportEntity' is not assignable to parameter of type 'SystemReport'.", "models/systemReport.ts(804,9): error TS2345: Argument of type 'SystemReportEntity' is not assignable to parameter of type 'SystemReport'.", "models/systemSettings.ts(2456,11): error TS6196: 'ValidationFunctions' is declared but never used.", "models/vectors.ts(4,10): error TS2724: '\"@prisma/client\"' has no exported member named 'DocumentVector'. Did you mean 'document_vectors'?", "models/vectors.ts(22,13): error TS7034: Variable 'inserts' implicitly has type 'any[]' in some locations where its type cannot be determined.", "models/vectors.ts(33,33): error TS7005: Variable 'inserts' implicitly has an 'any[]' type.", "models/vectors.ts(52,7): error TS2345: Argument of type 'number' is not assignable to parameter of type 'string'.", "models/workspace.ts(10,10): error TS2724: '\"@prisma/client\"' has no exported member named 'Workspace'. Did you mean 'workspaces'?", "models/workspace.ts(352,54): error TS2339: Property 'forWorkspace' does not exist on type 'LinkedWorkspaceModelStatic'.", "models/workspace.ts(368,41): error TS2339: Property 'forWorkspace' does not exist on type 'WorkspaceUserModelStatic'.", "models/workspace.ts(575,46): error TS2551: Property 'countForWorkspace' does not exist on type 'DocumentModelStatic'. Did you mean 'forWorkspace'?", "models/workspaceChats.ts(37,11): error TS2430: Interface 'WorkspaceChatWithData' incorrectly extends interface 'WorkspaceChat'.", "models/workspaceChats.ts(271,9): error TS2559: Type 'WhereClause' has no properties in common with type 'workspace_chatsWhereInput'.", "models/workspaceChats.ts(299,9): error TS2559: Type '{ [key: string]: any; user?: { username: string; } | undefined; }' has no properties in common with type 'workspace_chatsWhereInput'.", "routes/admin/system/deep-search-settings.ts(3,10): error TS2305: Module '\"../../../utils/middleware/multiUserProtected\"' has no exported member 'checkAdmin'.", "routes/admin/system/deep-search-settings.ts(4,10): error TS2305: Module '\"../../../utils/helpers\"' has no exported member 'encryptApiKey'.", "routes/admin/system/deep-search-settings.ts(25,29): error TS2769: No overload matches this call.", "routes/admin/system/deep-search-settings.ts(61,3): error TS2769: No overload matches this call.", "routes/admin/system/deep-search-settings.ts(83,53): error TS2345: Argument of type '{ provider: string; modelId: string; apiKey: string | null; enabled: boolean; contextPercentage: number; }' is not assignable to parameter of type '{ provider?: string | undefined; modelId?: string | undefined; apiKey?: string | undefined; enabled?: boolean | undefined; contextPercentage?: number | undefined; }'.", "scripts/testJinaEmbedder.ts(49,22): error TS2339: Property 'task' does not exist on type 'JinaEmbedder'.", "scripts/testJinaEmbedder.ts(50,31): error TS2339: Property 'lateChunking' does not exist on type 'JinaEmbedder'.", "scripts/testJinaEmbedder.ts(51,32): error TS2339: Property 'embeddingType' does not exist on type 'JinaEmbedder'.", "scripts/testJinaEmbedder.ts(52,28): error TS2341: Property 'dimensions' is private and only accessible within class 'JinaEmbedder'.", "scripts/testJinaEmbedder.ts(70,22): error TS2339: Property 'task' does not exist on type 'JinaEmbedder'.", "scripts/testJinaEmbedder.ts(71,32): error TS2339: Property 'embeddingType' does not exist on type 'JinaEmbedder'.", "scripts/testJinaEmbedder.ts(72,28): error TS2341: Property 'dimensions' is private and only accessible within class 'JinaEmbedder'.", "swagger/index.ts(3,9): error TS2669: Augmentations for the global scope can only be directly nested in external modules or ambient module declarations.", "swagger/init.ts(118,3): error TS2345: Argument of type '({ data }: SwaggerResult) => void' is not assignable to parameter of type '(value: false | { success: boolean; data: any; }) => void | PromiseLike<void>'.", "swagger/utils.ts(3,23): error TS7016: Could not find a declaration file for module 'swagger-ui-express'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/swagger-ui-express/index.js' implicitly has an 'any' type.", "types/agents.ts(425,3): error TS2564: Property 'status' has no initializer and is not definitely assigned in the constructor.", "types/agents.ts(431,3): error TS2390: Constructor implementation is missing.", "types/chat-agent.ts(172,3): error TS2687: All declarations of 'stage' must have identical modifiers.", "types/chat-agent.ts(465,3): error TS2687: All declarations of 'stage' must have identical modifiers.", "types/chat-agent.ts(465,3): error TS2717: Subsequent property declarations must have the same type.  Property 'stage' must be of type 'string', but here has type 'string | undefined'.", "types/chat-agent.ts(475,3): error TS2717: Subsequent property declarations must have the same type.  Property 'metrics' must be of type 'Record<string, any>', but here has type '{ [key: string]: any; totalExecutionTime: number; }'.", "types/chat-flow.ts(32,21): error TS2693: 'StageProcessor' only refers to a type, but is being used as a value here.", "types/test-utils.ts(7,10): error TS2724: '\"@prisma/client\"' has no exported member named 'User'. Did you mean 'users'?", "types/test-utils.ts(7,16): error TS2724: '\"@prisma/client\"' has no exported member named 'Workspace'. Did you mean 'workspaces'?", "types/test-utils.ts(7,27): error TS2724: '\"@prisma/client\"' has no exported member named 'WorkspaceThread'. Did you mean 'workspace_threads'?", "types/test-utils.ts(7,44): error TS2305: Mo<PERSON>le '\"@prisma/client\"' has no exported member 'Document'.", "types/test-utils.ts(23,18): error TS2430: Interface 'MockRequest' incorrectly extends interface 'Partial<Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>>'.", "utils/agents/aibitat/example/beginner-chat.ts(33,13): error TS2345: Argument of type 'AibitatFunctionDefinition' is not assignable to parameter of type 'AgentFunction'.", "utils/agents/aibitat/example/websocket/websock-branding-collab.ts(15,34): error TS1343: The 'import.meta' meta-property is only allowed when the '--module' option is 'es2020', 'es2022', 'esnext', 'system', 'node16', 'node18', or 'nodenext'.", "utils/agents/aibitat/example/websocket/websock-branding-collab.ts(22,19): error TS1378: Top-level 'await' expressions are only allowed when the 'module' option is set to 'es2022', 'esnext', 'system', 'node16', 'node18', 'nodenext', or 'preserve', and the 'target' option is set to 'es2017' or higher.", "utils/agents/aibitat/example/websocket/websock-branding-collab.ts(22,32): error TS7016: Could not find a declaration file for module '@mintplex-labs/express-ws'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/@mintplex-labs/express-ws/dist/index.js' implicitly has an 'any' type.", "utils/agents/aibitat/example/websocket/websock-multi-turn-chat.ts(15,34): error TS1343: The 'import.meta' meta-property is only allowed when the '--module' option is 'es2020', 'es2022', 'esnext', 'system', 'node16', 'node18', or 'nodenext'.", "utils/agents/aibitat/example/websocket/websock-multi-turn-chat.ts(22,19): error TS1378: Top-level 'await' expressions are only allowed when the 'module' option is set to 'es2022', 'esnext', 'system', 'node16', 'node18', 'nodenext', or 'preserve', and the 'target' option is set to 'es2017' or higher.", "utils/agents/aibitat/example/websocket/websock-multi-turn-chat.ts(22,32): error TS7016: Could not find a declaration file for module '@mintplex-labs/express-ws'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/@mintplex-labs/express-ws/dist/index.js' implicitly has an 'any' type.", "utils/agents/aibitat/index.ts(247,22): error TS2345: Argument of type '{ content: string; state: \"interrupt\"; from: string; to: string; }' is not assignable to parameter of type 'ChatMessageInternal'.", "utils/agents/aibitat/index.ts(276,11): error TS2739: Type '{ state: \"success\"; from: string; to: string; content: string; }' is missing the following properties from type 'ChatMessageInternal': role, timestamp", "utils/agents/aibitat/index.ts(322,11): error TS2739: Type '{ content: string; state: \"error\"; from: string; to: string; }' is missing the following properties from type 'ChatMessageInternal': role, timestamp", "utils/agents/aibitat/index.ts(505,48): error TS2345: Argument of type '{ members: string[]; provider: string | AgentProviderConfig; logging?: boolean; maxRounds: number; role: string; model: string; apiKey?: string; baseURL?: string; ... 4 more ...; presencePenalty?: number; }' is not assignable to parameter of type 'AgentProviderConfig'.", "utils/agents/aibitat/index.ts(618,103): error TS2345: Argument of type 'string | AgentFunction' is not assignable to parameter of type 'string | undefined'.", "utils/agents/aibitat/index.ts(621,48): error TS2345: Argument of type '{ role: string; prompt?: string; functions?: (AgentFunction | string)[]; provider: string | AgentProviderConfig; interrupt?: InterruptConfig | InterruptMode; ... 9 more ...; presencePenalty?: number; }' is not assignable to parameter of type 'AgentProviderConfig'.", "utils/agents/aibitat/index.ts(842,12): error TS2678: Type '\"generic-openai\"' is not comparable to type 'AgentProviderType'.", "utils/agents/aibitat/plugins/chat-history.ts(45,17): error TS2339: Property 'onMessage' does not exist on type 'AIbitat'.", "utils/agents/aibitat/plugins/chat-history.ts(60,26): error TS2339: Property '_storeSpecial' does not exist on type '{ name: string; setup: (aibitat: AIbitat) => void; }'.", "utils/agents/aibitat/plugins/chat-history.ts(69,24): error TS2339: Property '_store' does not exist on type '{ name: string; setup: (aibitat: AIbitat) => void; }'.", "utils/agents/aibitat/plugins/cli.ts(119,9): error TS2739: Type '{}' is missing the following properties from type 'AibitatMessage': from, to", "utils/agents/aibitat/plugins/memory.ts(47,17): error TS2551: Property 'function' does not exist on type 'AIbitat'. Did you mean 'functions'?", "utils/agents/aibitat/plugins/memory.ts(110,62): error TS2339: Property 'search' does not exist on type 'MemoryContext'.", "utils/agents/aibitat/plugins/memory.ts(111,61): error TS2339: Property 'store' does not exist on type 'MemoryContext'.", "utils/agents/aibitat/plugins/memory.ts(186,19): error TS2345: Argument of type 'null' is not assignable to parameter of type 'string | undefined'.", "utils/agents/aibitat/plugins/rechart.ts(51,17): error TS2551: Property 'function' does not exist on type 'AIbitat'. Did you mean 'functions'?", "utils/agents/aibitat/plugins/save-file-browser.ts(34,17): error TS2551: Property 'function' does not exist on type 'AIbitat'. Did you mean 'functions'?", "utils/agents/aibitat/plugins/sql-agent/get-table-schema.ts(20,5): error TS2741: Property 'plugin' is missing in type '{ name: string; description: string; setup(aibitat: any): void; }' but required in type 'AgentPlugin'.", "utils/agents/aibitat/plugins/sql-agent/list-database.ts(21,5): error TS2741: Property 'plugin' is missing in type '{ name: string; description: string; setup(aibitat: any): void; }' but required in type 'AgentPlugin'.", "utils/agents/aibitat/plugins/sql-agent/list-table.ts(19,5): error TS2741: Property 'plugin' is missing in type '{ name: string; description: string; setup(aibitat: any): void; }' but required in type 'AgentPlugin'.", "utils/agents/aibitat/plugins/sql-agent/query.ts(112,5): error TS2741: Property 'plugin' is missing in type '{ name: string; description: string; setup(aibitat: any): void; }' but required in type 'AgentPlugin'.", "utils/agents/aibitat/plugins/sql-agent/SQLConnectors/index.ts(107,39): error TS2345: Argument of type 'string | null | undefined' is not assignable to parameter of type 'string | null'.", "utils/agents/aibitat/plugins/sql-agent/SQLConnectors/MSSQL.ts(1,19): error TS7016: Could not find a declaration file for module 'mssql'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/mssql/index.js' implicitly has an 'any' type.", "utils/agents/aibitat/plugins/sql-agent/SQLConnectors/Postgresql.ts(1,19): error TS7016: Could not find a declaration file for module 'pg'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/pg/lib/index.js' implicitly has an 'any' type.", "utils/agents/aibitat/plugins/summarize.ts(53,17): error TS2551: Property 'function' does not exist on type 'AIbitat'. Did you mean 'functions'?", "utils/agents/aibitat/plugins/summarize.ts(102,54): error TS2339: Property 'listDocuments' does not exist on type 'DocumentSummarizerContext'.", "utils/agents/aibitat/plugins/summarize.ts(104,33): error TS2339: Property 'summarizeDoc' does not exist on type 'DocumentSummarizerContext'.", "utils/agents/aibitat/plugins/summarize.ts(133,39): error TS2339: Property 'title' does not exist on type '{}'.", "utils/agents/aibitat/plugins/summarize.ts(134,42): error TS2339: Property 'description' does not exist on type '{}'.", "utils/agents/aibitat/plugins/summarize.ts(153,28): error TS2339: Property 'listDocuments' does not exist on type 'DocumentSummarizerContext'.", "utils/agents/aibitat/plugins/web-browsing.ts(102,17): error TS2551: Property 'function' does not exist on type 'AIbitat'. Did you mean 'functions'?", "utils/agents/aibitat/plugins/web-browsing.ts(137,44): error TS2339: Property 'search' does not exist on type 'WebBrowsingContext'.", "utils/agents/aibitat/plugins/web-browsing.ts(289,49): error TS7006: Parameter 'searchResult' implicitly has an 'any' type.", "utils/agents/aibitat/plugins/web-browsing.ts(353,41): error TS7006: Parameter 'searchResult' implicitly has an 'any' type.", "utils/agents/aibitat/plugins/web-browsing.ts(478,41): error TS7006: Parameter 'searchResult' implicitly has an 'any' type.", "utils/agents/aibitat/plugins/web-browsing.ts(549,41): error TS7006: Parameter 'searchResult' implicitly has an 'any' type.", "utils/agents/aibitat/plugins/web-browsing.ts(605,41): error TS7006: Parameter 'searchResult' implicitly has an 'any' type.", "utils/agents/aibitat/plugins/web-scraping.ts(38,17): error TS2551: Property 'function' does not exist on type 'AIbitat'. Did you mean 'functions'?", "utils/agents/aibitat/plugins/web-scraping.ts(72,42): error TS2339: Property 'scrape' does not exist on type 'WebScrapingContext'.", "utils/agents/aibitat/plugins/websocket.ts(124,23): error TS2339: Property 'from' does not exist on type 'ChatMessage'.", "utils/agents/aibitat/plugins/websocket.ts(126,23): error TS2339: Property 'from' does not exist on type 'ChatMessage'.", "utils/agents/aibitat/providers/anthropic.ts(301,6): error TS1064: The return type of an async function or method must be the global Promise<T> type. Did you mean to write 'Promise<AsyncGenerator<string, any, any>>'?", "utils/agents/aibitat/providers/anthropic.ts(301,6): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.", "utils/agents/aibitat/providers/anthropic.ts(306,7): error TS1163: A 'yield' expression is only allowed in a generator body.", "utils/agents/aibitat/providers/azure.ts(75,16): error TS2532: Object is possibly 'undefined'.", "utils/agents/aibitat/providers/azure.ts(97,36): error TS2339: Property 'functionCall' does not exist on type 'AzureOpenAiProvider'.", "utils/agents/aibitat/providers/azure.ts(107,16): error TS2339: Property 'deduplicator' does not exist on type 'AzureOpenAiProvider'.", "utils/agents/aibitat/providers/azure.ts(129,16): error TS2339: Property 'cleanMsgs' does not exist on type 'AzureOpenAiProvider'.", "utils/agents/aibitat/providers/azure.ts(140,12): error TS2339: Property 'deduplicator' does not exist on type 'AzureOpenAiProvider'.", "utils/agents/aibitat/providers/azure.ts(142,17): error TS18048: 'completion' is possibly 'undefined'.", "utils/agents/aibitat/providers/bedrock.ts(105,15): error TS2345: Argument of type 'BaseMessage[]' is not assignable to parameter of type 'BaseLanguageModelInput'.", "utils/agents/aibitat/providers/bedrock.ts(130,36): error TS2339: Property 'functionCall' does not exist on type 'AWSBedrockProvider'.", "utils/agents/aibitat/providers/bedrock.ts(140,16): error TS2339: Property 'deduplicator' does not exist on type 'AWSBedrockProvider'.", "utils/agents/aibitat/providers/bedrock.ts(161,11): error TS2345: Argument of type 'BaseMessage[]' is not assignable to parameter of type 'BaseLanguageModelInput'.", "utils/agents/aibitat/providers/bedrock.ts(161,51): error TS2339: Property 'cleanMsgs' does not exist on type 'AWSBedrockProvider'.", "utils/agents/aibitat/providers/bedrock.ts(169,12): error TS2339: Property 'deduplicator' does not exist on type 'AWSBedrockProvider'.", "utils/agents/aibitat/providers/bedrock.ts(171,18): error TS18048: 'completion' is possibly 'undefined'.", "utils/agents/aibitat/providers/deepseek.ts(109,36): error TS2339: Property 'functionCall' does not exist on type 'DeepSeekProvider'.", "utils/agents/aibitat/providers/deepseek.ts(119,16): error TS2339: Property 'deduplicator' does not exist on type 'DeepSeekProvider'.", "utils/agents/aibitat/providers/deepseek.ts(141,26): error TS2339: Property 'cleanMsgs' does not exist on type 'DeepSeekProvider'.", "utils/agents/aibitat/providers/deepseek.ts(149,12): error TS2339: Property 'deduplicator' does not exist on type 'DeepSeekProvider'.", "utils/agents/aibitat/providers/deepseek.ts(151,17): error TS18048: 'completion' is possibly 'undefined'.", "utils/agents/aibitat/providers/fireworksai.ts(40,10): error TS2551: Property '_client' does not exist on type 'FireworksAIProvider'. Did you mean 'client'?", "utils/agents/aibitat/providers/fireworksai.ts(46,17): error TS2551: Property '_client' does not exist on type 'FireworksAIProvider'. Did you mean 'client'?", "utils/agents/aibitat/providers/fireworksai.ts(58,9): error TS2769: No overload matches this call.", "utils/agents/aibitat/providers/fireworksai.ts(87,47): error TS2339: Property 'functionCall' does not exist on type 'FireworksAIProvider'.", "utils/agents/aibitat/providers/fireworksai.ts(94,16): error TS2339: Property 'providerLog' does not exist on type 'FireworksAIProvider'.", "utils/agents/aibitat/providers/fireworksai.ts(95,16): error TS2339: Property 'deduplicator' does not exist on type 'FireworksAIProvider'.", "utils/agents/aibitat/providers/fireworksai.ts(109,14): error TS2339: Property 'providerLog' does not exist on type 'FireworksAIProvider'.", "utils/agents/aibitat/providers/fireworksai.ts(114,26): error TS2339: Property 'cleanMsgs' does not exist on type 'FireworksAIProvider'.", "utils/agents/aibitat/providers/fireworksai.ts(122,12): error TS2339: Property 'deduplicator' does not exist on type 'FireworksAIProvider'.", "utils/agents/aibitat/providers/fireworksai.ts(125,17): error TS18048: 'completion' is possibly 'undefined'.", "utils/agents/aibitat/providers/genericOpenAi.ts(114,36): error TS2339: Property 'functionCall' does not exist on type 'GenericOpenAiProvider'.", "utils/agents/aibitat/providers/genericOpenAi.ts(124,16): error TS2339: Property 'deduplicator' does not exist on type 'GenericOpenAiProvider'.", "utils/agents/aibitat/providers/genericOpenAi.ts(159,32): error TS2339: Property 'cleanMsgs' does not exist on type 'GenericOpenAiProvider'.", "utils/agents/aibitat/providers/genericOpenAi.ts(170,12): error TS2339: Property 'deduplicator' does not exist on type 'GenericOpenAiProvider'.", "utils/agents/aibitat/providers/genericOpenAi.ts(172,17): error TS18048: 'completion' is possibly 'undefined'.", "utils/agents/aibitat/providers/groq.ts(100,36): error TS2339: Property 'functionCall' does not exist on type 'GroqProvider'.", "utils/agents/aibitat/providers/groq.ts(110,16): error TS2339: Property 'deduplicator' does not exist on type 'GroqProvider'.", "utils/agents/aibitat/providers/groq.ts(132,26): error TS2339: Property 'cleanMsgs' does not exist on type 'GroqProvider'.", "utils/agents/aibitat/providers/groq.ts(140,12): error TS2339: Property 'deduplicator' does not exist on type 'GroqProvider'.", "utils/agents/aibitat/providers/groq.ts(142,17): error TS18048: 'completion' is possibly 'undefined'.", "utils/agents/aibitat/providers/koboldcpp.ts(101,36): error TS2339: Property 'functionCall' does not exist on type 'KoboldCPPProvider'.", "utils/agents/aibitat/providers/koboldcpp.ts(111,16): error TS2339: Property 'deduplicator' does not exist on type 'KoboldCPPProvider'.", "utils/agents/aibitat/providers/koboldcpp.ts(133,26): error TS2339: Property 'cleanMsgs' does not exist on type 'KoboldCPPProvider'.", "utils/agents/aibitat/providers/koboldcpp.ts(141,12): error TS2339: Property 'deduplicator' does not exist on type 'KoboldCPPProvider'.", "utils/agents/aibitat/providers/koboldcpp.ts(143,17): error TS18048: 'completion' is possibly 'undefined'.", "utils/agents/aibitat/providers/lmstudio.ts(101,36): error TS2339: Property 'functionCall' does not exist on type 'LMStudioProvider'.", "utils/agents/aibitat/providers/lmstudio.ts(111,16): error TS2339: Property 'deduplicator' does not exist on type 'LMStudioProvider'.", "utils/agents/aibitat/providers/lmstudio.ts(133,26): error TS2339: Property 'cleanMsgs' does not exist on type 'LMStudioProvider'.", "utils/agents/aibitat/providers/lmstudio.ts(141,12): error TS2339: Property 'deduplicator' does not exist on type 'LMStudioProvider'.", "utils/agents/aibitat/providers/lmstudio.ts(143,17): error TS18048: 'completion' is possibly 'undefined'.", "utils/agents/aibitat/providers/localai.ts(103,36): error TS2339: Property 'functionCall' does not exist on type 'LocalAiProvider'.", "utils/agents/aibitat/providers/localai.ts(113,16): error TS2339: Property 'deduplicator' does not exist on type 'LocalAiProvider'.", "utils/agents/aibitat/providers/localai.ts(135,26): error TS2339: Property 'cleanMsgs' does not exist on type 'LocalAiProvider'.", "utils/agents/aibitat/providers/localai.ts(143,12): error TS2339: Property 'deduplicator' does not exist on type 'LocalAiProvider'.", "utils/agents/aibitat/providers/localai.ts(145,17): error TS18048: 'completion' is possibly 'undefined'.", "utils/agents/aibitat/providers/mistral.ts(88,35): error TS2339: Property 'functionCall' does not exist on type 'MistralProvider'.", "utils/agents/aibitat/providers/mistral.ts(98,16): error TS2339: Property 'deduplicator' does not exist on type 'MistralProvider'.", "utils/agents/aibitat/providers/mistral.ts(120,26): error TS2339: Property 'cleanMsgs' does not exist on type 'MistralProvider'.", "utils/agents/aibitat/providers/mistral.ts(128,12): error TS2339: Property 'deduplicator' does not exist on type 'MistralProvider'.", "utils/agents/aibitat/providers/mistral.ts(130,17): error TS18048: 'completion' is possibly 'undefined'.", "utils/agents/aibitat/providers/ollama.ts(78,35): error TS2339: Property 'functionCall' does not exist on type 'OllamaProvider'.", "utils/agents/aibitat/providers/ollama.ts(88,16): error TS2339: Property 'deduplicator' does not exist on type 'OllamaProvider'.", "utils/agents/aibitat/providers/ollama.ts(110,26): error TS2339: Property 'cleanMsgs' does not exist on type 'OllamaProvider'.", "utils/agents/aibitat/providers/ollama.ts(122,12): error TS2339: Property 'deduplicator' does not exist on type 'OllamaProvider'.", "utils/agents/aibitat/providers/openrouter.ts(105,36): error TS2339: Property 'functionCall' does not exist on type 'OpenRouterProvider'.", "utils/agents/aibitat/providers/openrouter.ts(115,16): error TS2339: Property 'deduplicator' does not exist on type 'OpenRouterProvider'.", "utils/agents/aibitat/providers/openrouter.ts(137,26): error TS2339: Property 'cleanMsgs' does not exist on type 'OpenRouterProvider'.", "utils/agents/aibitat/providers/openrouter.ts(145,12): error TS2339: Property 'deduplicator' does not exist on type 'OpenRouterProvider'.", "utils/agents/aibitat/providers/openrouter.ts(147,17): error TS18048: 'completion' is possibly 'undefined'.", "utils/agents/aibitat/providers/perplexity.ts(101,36): error TS2339: Property 'functionCall' does not exist on type 'PerplexityProvider'.", "utils/agents/aibitat/providers/perplexity.ts(111,16): error TS2339: Property 'deduplicator' does not exist on type 'PerplexityProvider'.", "utils/agents/aibitat/providers/perplexity.ts(133,26): error TS2339: Property 'cleanMsgs' does not exist on type 'PerplexityProvider'.", "utils/agents/aibitat/providers/perplexity.ts(141,12): error TS2339: Property 'deduplicator' does not exist on type 'PerplexityProvider'.", "utils/agents/aibitat/providers/perplexity.ts(143,17): error TS18048: 'completion' is possibly 'undefined'.", "utils/agents/aibitat/providers/textgenwebui.ts(101,36): error TS2339: Property 'functionCall' does not exist on type 'TextWebGenUiProvider'.", "utils/agents/aibitat/providers/textgenwebui.ts(111,16): error TS2339: Property 'deduplicator' does not exist on type 'TextWebGenUiProvider'.", "utils/agents/aibitat/providers/textgenwebui.ts(133,26): error TS2339: Property 'cleanMsgs' does not exist on type 'TextWebGenUiProvider'.", "utils/agents/aibitat/providers/textgenwebui.ts(141,12): error TS2339: Property 'deduplicator' does not exist on type 'TextWebGenUiProvider'.", "utils/agents/aibitat/providers/textgenwebui.ts(143,17): error TS18048: 'completion' is possibly 'undefined'.", "utils/agents/aibitat/providers/togetherai.ts(101,36): error TS2339: Property 'functionCall' does not exist on type 'TogetherAIProvider'.", "utils/agents/aibitat/providers/togetherai.ts(111,16): error TS2339: Property 'deduplicator' does not exist on type 'TogetherAIProvider'.", "utils/agents/aibitat/providers/togetherai.ts(133,26): error TS2339: Property 'cleanMsgs' does not exist on type 'TogetherAIProvider'.", "utils/agents/aibitat/providers/togetherai.ts(141,12): error TS2339: Property 'deduplicator' does not exist on type 'TogetherAIProvider'.", "utils/agents/aibitat/providers/togetherai.ts(143,17): error TS18048: 'completion' is possibly 'undefined'.", "utils/agents/aibitat/providers/xai.ts(99,36): error TS2339: Property 'functionCall' does not exist on type 'XAIProvider'.", "utils/agents/aibitat/providers/xai.ts(109,16): error TS2339: Property 'deduplicator' does not exist on type 'XAIProvider'.", "utils/agents/aibitat/providers/xai.ts(131,26): error TS2339: Property 'cleanMsgs' does not exist on type 'XAIProvider'.", "utils/agents/aibitat/providers/xai.ts(139,12): error TS2339: Property 'deduplicator' does not exist on type 'XAIProvider'.", "utils/agents/aibitat/providers/xai.ts(141,17): error TS18048: 'completion' is possibly 'undefined'.", "utils/agents/defaults.ts(56,3): error TS2531: Object is possibly 'null'.", "utils/agents/defaults.ts(56,17): error TS2345: Argument of type 'string | null | undefined' is not assignable to parameter of type 'string | null'.", "utils/agents/ephemeral.ts(80,26): error TS2531: Object is possibly 'null'.", "utils/agents/ephemeral.ts(150,57): error TS2339: Property 'find' does not exist on type '(args?: any) => { name: string; setup: (aibitat: AIbitat) => void; }'.", "utils/agents/ephemeral.ts(151,12): error TS7006: Parameter 'child' implicitly has an 'any' type.", "utils/agents/ephemeral.ts(164,9): error TS2531: Object is possibly 'null'.", "utils/agents/ephemeral.ts(183,26): error TS18048: 'plugin' is possibly 'undefined'.", "utils/agents/ephemeral.ts(184,9): error TS2531: Object is possibly 'null'.", "utils/agents/ephemeral.ts(184,26): error TS18048: 'plugin' is possibly 'undefined'.", "utils/agents/ephemeral.ts(186,23): error TS18048: 'plugin' is possibly 'undefined'.", "utils/agents/ephemeral.ts(200,9): error TS2532: Object is possibly 'undefined'.", "utils/agents/ephemeral.ts(203,7): error TS2531: Object is possibly 'null'.", "utils/agents/ephemeral.ts(236,25): error TS2531: Object is possibly 'null'.", "utils/agents/ephemeral.ts(351,45): error TS2345: Argument of type '{ id: string; type: string; thought: string; sources: never[]; attachments: never[]; close: false; error: null; }' is not assignable to parameter of type 'ResponseChunkData'.", "utils/agents/ephemeral.ts(361,43): error TS2345: Argument of type '{ id: string; type: string; textResponse: string; sources: never[]; attachments: never[]; close: true; error: null; }' is not assignable to parameter of type 'ResponseChunkData'.", "utils/agents/imported.ts(154,29): error TS2698: Spread types may only be created from object types.", "utils/agents/imported.ts(197,13): error TS7006: Parameter 'aibitat' implicitly has an 'any' type.", "utils/agents/index.ts(9,1): error TS6192: All imports in import declaration are unused.", "utils/agents/index.ts(16,3): error TS6196: 'InterruptMode' is declared but never used.", "utils/agents/index.ts(18,3): error TS6196: 'AgentConfig' is declared but never used.", "utils/agents/index.ts(19,3): error TS6196: 'AgentFunction' is declared but never used.", "utils/agents/index.ts(20,3): error TS6196: 'AgentProviderConfig' is declared but never used.", "utils/agents/index.ts(405,13): error TS2345: Argument of type 'AIbitat' is not assignable to parameter of type 'Aibitat'.", "utils/agents/index.ts(448,23): error TS18048: 'plugin' is possibly 'undefined'.", "utils/agents/index.ts(497,14): error TS2352: Conversion of type 'ChatHistoryEntry[]' to type 'ChatMessage[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "utils/AiProviders/anthropic/index.ts(203,48): error TS2339: Property 'text' does not exist on type 'ContentBlock'.", "utils/AiProviders/anthropic/index.ts(214,54): error TS2739: Type '{}' is missing the following properties from type 'UsageMetrics': prompt_tokens, completion_tokens, total_tokens", "utils/AiProviders/anthropic/index.ts(245,7): error TS2345: Argument of type 'MessageStream' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.", "utils/AiProviders/bedrock/index.ts(211,9): error TS2353: Object literal may only specify known properties, and 'image_url' does not exist in type '{ type: string; text: string; }'.", "utils/AiProviders/bedrock/index.ts(275,41): error TS2339: Property 'length' does not exist on type '{}'.", "utils/AiProviders/bedrock/index.ts(278,60): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/bedrock/index.ts(304,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/cohere/index.ts(202,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/deepseek/index.ts(131,22): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'object'.", "utils/AiProviders/deepseek/index.ts(132,7): error TS18046: 'result.output' is of type 'unknown'.", "utils/AiProviders/deepseek/index.ts(137,21): error TS18046: 'result.output' is of type 'unknown'.", "utils/AiProviders/deepseek/index.ts(139,24): error TS18046: 'result.output' is of type 'unknown'.", "utils/AiProviders/deepseek/index.ts(140,28): error TS18046: 'result.output' is of type 'unknown'.", "utils/AiProviders/deepseek/index.ts(141,23): error TS18046: 'result.output' is of type 'unknown'.", "utils/AiProviders/deepseek/index.ts(142,20): error TS18046: 'result.output' is of type 'unknown'.", "utils/AiProviders/deepseek/index.ts(168,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/fireworksAi/index.ts(134,20): error TS18048: 'result.output.usage.completion_tokens' is possibly 'undefined'.", "utils/AiProviders/fireworksAi/index.ts(156,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/gemini/index.ts(235,13): error TS18046: 'data' is of type 'unknown'.", "utils/AiProviders/gemini/index.ts(235,41): error TS18046: 'data' is of type 'unknown'.", "utils/AiProviders/gemini/index.ts(236,16): error TS18046: 'data' is of type 'unknown'.", "utils/AiProviders/gemini/index.ts(476,39): error TS2339: Property 'text' does not exist on type '{}'.", "utils/AiProviders/gemini/index.ts(480,60): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/gemini/index.ts(513,9): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/genericOpenAi/index.ts(240,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/huggingface/index.ts(162,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/koboldCPP/index.ts(161,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/liteLLM/index.ts(35,23): error TS2769: No overload matches this call.", "utils/AiProviders/liteLLM/index.ts(51,38): error TS2769: No overload matches this call.", "utils/AiProviders/liteLLM/index.ts(162,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/lmStudio/index.ts(145,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/localAi/index.ts(66,23): error TS2769: No overload matches this call.", "utils/AiProviders/localAi/index.ts(75,23): error TS2769: No overload matches this call.", "utils/AiProviders/localAi/index.ts(147,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/mistral/index.ts(161,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/native/index.ts(29,44): error TS2556: A spread argument must either have a tuple type or be passed to a rest parameter.", "utils/AiProviders/native/index.ts(154,26): error TS2339: Property 'content' does not exist on type '{}'.", "utils/AiProviders/native/index.ts(156,60): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/native/index.ts(158,21): error TS2339: Property 'content' does not exist on type '{}'.", "utils/AiProviders/native/index.ts(161,35): error TS2339: Property 'content' does not exist on type '{}'.", "utils/AiProviders/native/index.ts(181,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/native/index.ts(199,64): error TS2345: Argument of type 'string' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/native/index.ts(233,62): error TS2345: Argument of type 'string' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/ollama/index.ts(3,1): error TS6192: All imports in import declaration are unused.", "utils/AiProviders/ollama/index.ts(362,9): error TS2416: Property 'constructPrompt' in type 'OllamaAILLM' is not assignable to the same property in base type 'LLMProvider'.", "utils/AiProviders/ollama/index.ts(486,30): error TS2339: Property 'then' does not exist on type 'never'.", "utils/AiProviders/ollama/index.ts(493,15): error TS2345: Argument of type 'AbortableAsyncIterator<ChatResponse>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.", "utils/AiProviders/ollama/index.ts(505,11): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/ollama/index.ts(541,44): error TS2345: Argument of type 'MonitoredStream' is not assignable to parameter of type 'MeasuredStream'.", "utils/AiProviders/openRouter/index.ts(154,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/perplexity/index.ts(161,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/textGenWebUI/index.ts(148,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/togetherAi/index.ts(155,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/AiProviders/xai/index.ts(167,22): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'object'.", "utils/AiProviders/xai/index.ts(168,7): error TS18046: 'result.output' is of type 'unknown'.", "utils/AiProviders/xai/index.ts(173,21): error TS18046: 'result.output' is of type 'unknown'.", "utils/AiProviders/xai/index.ts(175,24): error TS18046: 'result.output' is of type 'unknown'.", "utils/AiProviders/xai/index.ts(176,28): error TS18046: 'result.output' is of type 'unknown'.", "utils/AiProviders/xai/index.ts(177,23): error TS18046: 'result.output' is of type 'unknown'.", "utils/AiProviders/xai/index.ts(178,20): error TS18046: 'result.output' is of type 'unknown'.", "utils/AiProviders/xai/index.ts(200,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.", "utils/BackgroundWorkers/index.ts(65,7): error TS2739: Type '{ name: string; interval: string; }' is missing the following properties from type 'Job': path, timeout", "utils/boot/index.ts(4,23): error TS7016: Could not find a declaration file for module '@mintplex-labs/express-ws'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/@mintplex-labs/express-ws/dist/index.js' implicitly has an 'any' type.", "utils/boot/index.ts(11,15): error TS6196: 'BootOptions' is declared but never used.", "utils/boot/index.ts(64,29): error TS2769: No overload matches this call.", "utils/boot/MetaGenerator.ts(135,51): error TS2769: No overload matches this call.", "utils/chats/agents.ts(1,23): error TS7016: Could not find a declaration file for module 'pluralize'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/pluralize/pluralize.js' implicitly has an 'any' type.", "utils/chats/agents.ts(48,36): error TS2345: Argument of type '{ id: string; type: string; textResponse: string; sources: never[]; close: true; error: null; }' is not assignable to parameter of type 'ResponseChunkData'.", "utils/chats/agents.ts(64,34): error TS2345: Argument of type '{ id: string; type: string; textResponse: null; sources: never[]; close: false; error: null; websocketUUID: any; }' is not assignable to parameter of type 'ResponseChunkData'.", "utils/chats/agents.ts(75,34): error TS2345: Argument of type '{ id: string; type: string; textResponse: string; sources: never[]; close: true; error: null; }' is not assignable to parameter of type 'ResponseChunkData'.", "utils/chats/apiChatHandler.ts(134,7): error TS2739: Type 'Workspace' is missing the following properties from type 'Workspace': name, vectorTag, createdAt, lastUpdatedAt", "utils/chats/apiChatHandler.ts(231,5): error TS2741: Property 'name' is missing in type 'Workspace' but required in type 'import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/DocumentManager/index\").Workspace'.", "utils/chats/apiChatHandler.ts(332,32): error TS2345: Argument of type 'Workspace' is not assignable to parameter of type 'Workspace'.", "utils/chats/apiChatHandler.ts(484,9): error TS2345: Argument of type 'null' is not assignable to parameter of type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.", "utils/chats/apiChatHandler.ts(522,7): error TS2739: Type 'Workspace' is missing the following properties from type 'Workspace': name, vectorTag, createdAt, lastUpdatedAt", "utils/chats/apiChatHandler.ts(626,5): error TS2741: Property 'name' is missing in type 'Workspace' but required in type 'import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/DocumentManager/index\").Workspace'.", "utils/chats/apiChatHandler.ts(727,32): error TS2345: Argument of type 'Workspace' is not assignable to parameter of type 'Workspace'.", "utils/chats/commands/reset.ts(26,9): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.", "utils/chats/commands/reset.ts(30,47): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.", "utils/chats/contextWindow.ts(250,5): error TS2352: Conversion of type 'DocumentData[]' to type 'OptimizedItem[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "utils/chats/contextWindow.ts(260,5): error TS2352: Conversion of type 'DocumentData[]' to type 'OptimizedItem[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "utils/chats/contextWindow.ts(301,9): error TS2352: Conversion of type 'DocumentData[]' to type 'OptimizedItem[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "utils/chats/embed.ts(87,24): error TS2352: Conversion of type 'LLMProvider' to type 'LLMProv<PERSON> & { promptWindowLimit(): number; streamingEnabled(): boolean; }' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "utils/chats/embed.ts(105,34): error TS2345: Argument of type '{ id: string; type: string; textResponse: string; sources: never[]; close: true; error: null; }' is not assignable to parameter of type 'ResponseChunkData'.", "utils/chats/embed.ts(132,5): error TS2741: Property 'name' is missing in type 'Workspace' but required in type 'import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/DocumentManager/index\").Workspace'.", "utils/chats/embed.ts(167,34): error TS2345: Argument of type '{ id: string; type: string; textResponse: null; sources: never[]; close: true; error: string; }' is not assignable to parameter of type 'ResponseChunkData'.", "utils/chats/embed.ts(188,34): error TS2345: Argument of type '{ id: string; type: string; textResponse: string; sources: never[]; close: true; error: null; }' is not assignable to parameter of type 'ResponseChunkData'.", "utils/chats/embed.ts(203,39): error TS2339: Property 'compressMessages' does not exist on type 'LLMProvider & { promptWindowLimit(): number; streamingEnabled(): boolean; }'.", "utils/chats/embed.ts(220,26): error TS2339: Property 'getChatCompletion' does not exist on type 'LLMProvider & { promptWindowLimit(): number; streamingEnabled(): boolean; }'.", "utils/chats/embed.ts(221,67): error TS2339: Property 'defaultTemp' does not exist on type 'LLMProvider & { promptWindowLimit(): number; streamingEnabled(): boolean; }'.", "utils/chats/embed.ts(234,39): error TS2339: Property 'streamGetChatCompletion' does not exist on type 'LLMProvider & { promptWindowLimit(): number; streamingEnabled(): boolean; }'.", "utils/chats/embed.ts(235,65): error TS2339: Property 'defaultTemp' does not exist on type 'LLMProvider & { promptWindowLimit(): number; streamingEnabled(): boolean; }'.", "utils/chats/embed.ts(237,39): error TS2339: Property 'handleStream' does not exist on type 'LLMProvider & { promptWindowLimit(): number; streamingEnabled(): boolean; }'.", "utils/chats/embed.ts(269,37): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.", "utils/chats/flowDispatcher.ts(67,25): error TS2769: No overload matches this call.", "utils/chats/flowDispatcher.ts(260,45): error TS2345: Argument of type 'FlowRunnerOptions' is not assignable to parameter of type 'FlowOptions'.", "utils/chats/flows/configurations/mainDocFlowConfig.ts(9,10): error TS2305: Module '\"../../../../types/chat-flow\"' has no exported member 'FlowConfiguration'.", "utils/chats/flows/configurations/mainDocFlowConfig.ts(55,9): error TS2416: Property 'process' in type 'MainDocumentExtractionProcessor' is not assignable to the same property in base type 'StageProcessor'.", "utils/chats/flows/configurations/mainDocFlowConfig.ts(109,9): error TS2416: Property 'process' in type 'SectionMappingProcessor' is not assignable to the same property in base type 'StageProcessor'.", "utils/chats/flows/configurations/noMainDocFlowConfig.ts(9,10): error TS2305: Module '\"../../../../types/chat-flow\"' has no exported member 'FlowConfiguration'.", "utils/chats/flows/configurations/noMainDocFlowConfig.ts(38,9): error TS2416: Property 'process' in type 'DocumentToSectionMappingProcessor' is not assignable to the same property in base type 'StageProcessor'.", "utils/chats/flows/configurations/referenceFlowConfig.ts(121,51): error TS7006: Parameter 'doc' implicitly has an 'any' type.", "utils/chats/flows/configurations/referenceFlowConfig.ts(131,17): error TS7034: Variable 'referenceFiles' implicitly has type 'any[]' in some locations where its type cannot be determined.", "utils/chats/flows/configurations/referenceFlowConfig.ts(132,17): error TS7034: Variable 'reviewFiles' implicitly has type 'any[]' in some locations where its type cannot be determined.", "utils/chats/flows/configurations/referenceFlowConfig.ts(136,40): error TS7006: Parameter 'doc' implicitly has an 'any' type.", "utils/chats/flows/configurations/referenceFlowConfig.ts(141,60): error TS7006: Parameter 'refName' implicitly has an 'any' type.", "utils/chats/flows/configurations/referenceFlowConfig.ts(173,40): error TS7006: Parameter 'doc' implicitly has an 'any' type.", "utils/chats/flows/configurations/referenceFlowConfig.ts(203,29): error TS7005: Variable 'referenceFiles' implicitly has an 'any[]' type.", "utils/chats/flows/configurations/referenceFlowConfig.ts(207,26): error TS7005: Variable 'reviewFiles' implicitly has an 'any[]' type.", "utils/chats/flows/configurations/referenceFlowConfig.ts(219,58): error TS7006: Parameter 'd' implicitly has an 'any' type.", "utils/chats/flows/configurations/referenceFlowConfig.ts(242,13): error TS7005: Variable 'referenceFiles' implicitly has an 'any[]' type.", "utils/chats/flows/configurations/referenceFlowConfig.ts(243,13): error TS7005: Variable 'reviewFiles' implicitly has an 'any[]' type.", "utils/chats/flows/configurations/referenceFlowConfig.ts(245,26): error TS7005: Variable 'referenceFiles' implicitly has an 'any[]' type.", "utils/chats/flows/configurations/referenceFlowConfig.ts(246,23): error TS7005: Variable 'reviewFiles' implicitly has an 'any[]' type.", "utils/chats/flows/configurations/referenceFlowConfig.ts(982,24): error TS2415: Class 'ReferenceIterativeSectionDraftingProcessor' incorrectly extends base class 'IterativeSectionDraftingProcessor'.", "utils/chats/flows/configurations/referenceFlowConfig.ts(987,37): error TS2341: Property 'prepareDraftingOptions' is private and only accessible within class 'IterativeSectionDraftingProcessor'.", "utils/chats/flows/configurations/referenceFlowConfig.ts(1014,33): error TS2749: '_idx' refers to a value, but is being used as a type here. Did you mean 'typeof _idx'?", "utils/chats/flows/configurations/referenceFlowConfig.ts(1019,64): error TS2749: '_idx' refers to a value, but is being used as a type here. Did you mean 'typeof _idx'?", "utils/chats/flows/core/FlowOrchestrator.ts(78,5): error TS2740: Type 'Response<any, Record<string, any>>' is missing the following properties from type 'Response': headers, ok, statusText, url, and 8 more.", "utils/chats/flows/core/FlowOrchestrator.ts(97,9): error TS2345: Argument of type 'Response' is not assignable to parameter of type 'Response<any, Record<string, any>>'.", "utils/chats/flows/core/FlowOrchestrator.ts(395,57): error TS2693: 'StageProcessor' only refers to a type, but is being used as a value here.", "utils/chats/flows/core/LLMCoordinator.ts(225,27): error TS2345: Argument of type 'CompletionResponse | null' is not assignable to parameter of type 'ChatCompletionResult'.", "utils/chats/flows/core/LLMCoordinator.ts(242,34): error TS2554: Expected 2 arguments, but got 1.", "utils/chats/flows/core/ProgressManager.ts(97,9): error TS2783: 'uuid' is specified more than once, so this usage will be overwritten.", "utils/chats/flows/core/ProgressManager.ts(108,9): error TS2783: 'uuid' is specified more than once, so this usage will be overwritten.", "utils/chats/flows/core/WorkspaceManager.ts(4,15): error TS2305: Module '\"../../../../types/auth\"' has no exported member 'User'.", "utils/chats/flows/demo/modularFlowDemo.ts(13,10): error TS2305: Module '\"../../../../types/chat-flow\"' has no exported member 'FlowConfiguration'.", "utils/chats/flows/demo/modularFlowDemo.ts(47,47): error TS2345: Argument of type 'FlowOptions' is not assignable to parameter of type 'import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow\").FlowOptions'.", "utils/chats/flows/demo/modularFlowDemo.ts(57,37): error TS7006: Parameter 's' implicitly has an 'any' type.", "utils/chats/flows/demo/modularFlowDemo.ts(131,9): error TS6196: 'CustomProcessor' is declared but never used.", "utils/chats/flows/demo/modularFlowDemo.ts(148,5): error TS2353: Object literal may only specify known properties, and 'id' does not exist in type 'CustomFlowConfig'.", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(27,10): error TS2305: Module '\"../../../../types/chat-flow\"' has no exported member 'FlowDependencies'.", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(63,11): error TS6196: 'LocalProseMirrorDocument' is declared but never used.", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(159,9): error TS2416: Property 'process' in type 'AgenticCombinationStageProcessor' is not assignable to the same property in base type 'StageProcessor'.", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(319,9): error TS2353: Object literal may only specify known properties, and 'agenticCombinationApplied' does not exist in type 'Partial<FlowContext>'.", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(350,44): error TS2554: Expected 0 arguments, but got 1.", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(531,11): error TS2345: Argument of type 'string' is not assignable to parameter of type 'LegalMemo[]'.", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(673,11): error TS2345: Argument of type 'string' is not assignable to parameter of type 'LegalMemo[]'.", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(989,3): error TS2416: Property 'validateInputs' in type 'AgenticCombinationStageProcessor' is not assignable to the same property in base type 'StageProcessor'.", "utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(1006,3): error TS2416: Property 'shouldSkip' in type 'AgenticCombinationStageProcessor' is not assignable to the same property in base type 'StageProcessor'.", "utils/chats/flows/processors/CombinationProcessorFactory.ts(98,46): error TS2345: Argument of type 'ProcessorContext' is not assignable to parameter of type 'FlowContext & FlowContext'.", "utils/chats/flows/processors/CombinationProcessorFactory.ts(111,52): error TS2345: Argument of type 'ProcessorContext' is not assignable to parameter of type 'FlowContext & FlowContext'.", "utils/chats/flows/processors/CombinationProcessorFactory.ts(118,48): error TS2345: Argument of type 'ProcessorContext' is not assignable to parameter of type 'FlowContext & FlowContext'.", "utils/chats/flows/processors/DocumentProcessingStageProcessor.ts(199,13): error TS2353: Object literal may only specify known properties, and 'fileName' does not exist in type 'ProcessedDocument'.", "utils/chats/flows/processors/IterativeSectionDraftingProcessor.ts(99,11): error TS6196: 'DraftingResult' is declared but never used.", "utils/chats/flows/processors/IterativeSectionDraftingProcessor.ts(120,7): error TS2415: Class 'IterativeSectionDraftingProcessor' incorrectly extends base class 'StageProcessor'.", "utils/chats/flows/processors/IterativeSectionDraftingProcessor.ts(197,9): error TS2345: Argument of type 'Section[]' is not assignable to parameter of type 'DocumentSection[]'.", "utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(6,23): error TS2305: Module '\"../../../../types/chat-agent\"' has no exported member 'FlowDependencies'.", "utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(84,9): error TS2416: Property 'process' in type 'LegalIssueIdentificationProcessor' is not assignable to the same property in base type 'StageProcessor'.", "utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(206,7): error TS2561: Object literal may only specify known properties, but 'allLegalIssues' does not exist in type 'Partial<FlowContext>'. Did you mean to write 'legalIssues'?", "utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(258,14): error TS2345: Argument of type '\"debug\"' is not assignable to parameter of type '\"info\" | \"warn\" | \"error\"'.", "utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(293,14): error TS2345: Argument of type '\"debug\"' is not assignable to parameter of type '\"info\" | \"warn\" | \"error\"'.", "utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(504,3): error TS2416: Property 'validateInputs' in type 'LegalIssueIdentificationProcessor' is not assignable to the same property in base type 'StageProcessor'.", "utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(521,3): error TS2416: Property 'shouldSkip' in type 'LegalIssueIdentificationProcessor' is not assignable to the same property in base type 'StageProcessor'.", "utils/chats/flows/processors/LegalMemoProcessor.ts(79,7): error TS2415: Class 'LegalMemoProcessor' incorrectly extends base class 'StageProcessor'.", "utils/chats/helpers/contextWindowManager.ts(4,1): error TS6192: All imports in import declaration are unused.", "utils/chats/helpers/contextWindowManager.ts(275,37): error TS2345: Argument of type 'TokenBudget' is not assignable to parameter of type 'BudgetAllocation'.", "utils/chats/helpers/documentProcessing.ts(266,10): error TS18047: 'result' is possibly 'null'.", "utils/chats/helpers/documentProcessing.ts(409,20): error TS18047: 'result' is possibly 'null'.", "utils/chats/helpers/documentProcessing.ts(528,21): error TS18047: 'result' is possibly 'null'.", "utils/chats/helpers/documentProcessing.ts(715,9): error TS18047: 'result' is possibly 'null'.", "utils/chats/helpers/documentProcessing.ts(716,28): error TS18047: 'result' is possibly 'null'.", "utils/chats/helpers/documentProcessing.ts(860,9): error TS18047: 'result' is possibly 'null'.", "utils/chats/helpers/documentProcessing.ts(1249,14): error TS18047: 'result' is possibly 'null'.", "utils/chats/index.ts(54,64): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'number | null | undefined'.", "utils/chats/index.ts(100,60): error TS2345: Argument of type 'WorkspaceChat[]' is not assignable to parameter of type 'ChatRecord[]'.", "utils/chats/LLMConnector.ts(143,29): error TS2554: Expected 2 arguments, but got 1.", "utils/chats/LLMConnector.ts(165,27): error TS2339: Property 'metrics' does not exist on type 'LLMProvider'.", "utils/chats/LLMConnector.ts(172,20): error TS2339: Property 'metrics' does not exist on type 'LLMProvider'.", "utils/chats/openaiCompatible.ts(160,5): error TS2741: Property 'name' is missing in type 'Workspace' but required in type 'import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/DocumentManager/index\").Workspace'.", "utils/chats/openaiCompatible.ts(253,46): error TS2345: Argument of type 'Workspace' is not assignable to parameter of type 'Workspace'.", "utils/chats/openaiCompatible.ts(310,15): error TS18047: 'chat' is possibly 'null'.", "utils/chats/openaiCompatible.ts(379,7): error TS2345: Argument of type 'OpenAICompatibleResponse' is not assignable to parameter of type 'ResponseChunkData'.", "utils/chats/openaiCompatible.ts(401,5): error TS2741: Property 'name' is missing in type 'Workspace' but required in type 'import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/DocumentManager/index\").Workspace'.", "utils/chats/openaiCompatible.ts(441,9): error TS2345: Argument of type 'OpenAICompatibleResponse' is not assignable to parameter of type 'ResponseChunkData'.", "utils/chats/openaiCompatible.ts(484,7): error TS2345: Argument of type 'OpenAICompatibleResponse' is not assignable to parameter of type 'ResponseChunkData'.", "utils/chats/openaiCompatible.ts(502,46): error TS2345: Argument of type 'Workspace' is not assignable to parameter of type 'Workspace'.", "utils/chats/openaiCompatible.ts(511,7): error TS2345: Argument of type 'OpenAICompatibleResponse' is not assignable to parameter of type 'ResponseChunkData'.", "utils/chats/openaiCompatible.ts(557,7): error TS2345: Argument of type 'OpenAICompatibleResponse' is not assignable to parameter of type 'ResponseChunkData'.", "utils/chats/openaiCompatible.ts(563,19): error TS18047: 'chat' is possibly 'null'.", "utils/chats/openaiCompatible.ts(579,5): error TS2345: Argument of type 'OpenAICompatibleResponse' is not assignable to parameter of type 'ResponseChunkData'.", "utils/chats/streamCanvas.ts(10,1): error TS6192: All imports in import declaration are unused.", "utils/chats/streamCanvas.ts(70,25): error TS7053: Element implicitly has an 'any' type because expression of type '`LLMProvider${string}`' can't be used to index type '{ WhisperProvider: string; WhisperModelPref: string; TextToSpeechProvider: string; TTSOpenAIKey: boolean; TTSOpenAIVoiceModel: string | undefined; TTSElevenLabsKey: boolean; TTSElevenLabsVoiceModel: string | undefined; ... 107 more ...; VectorDB: string | undefined; }'.", "utils/chats/streamCanvas.ts(105,19): error TS7006: Parameter 'part' implicitly has an 'any' type.", "utils/chats/streamCanvas.ts(115,19): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ WhisperProvider: string; WhisperModelPref: string; TextToSpeechProvider: string; TTSOpenAIKey: boolean; TTSOpenAIVoiceModel: string | undefined; TTSElevenLabsKey: boolean; TTSElevenLabsVoiceModel: string | undefined; ... 107 more ...; VectorDB: string | undefined; }'.", "utils/chats/streamCanvas.ts(263,32): error TS2774: This condition will always return true since this function is always defined. Did you mean to call it instead?", "utils/chats/streamCanvas.ts(264,19): error TS2339: Property 'countTokens' does not exist on type 'TokenManager'.", "utils/chats/streamCanvas.ts(266,27): error TS2774: This condition will always return true since this function is always defined. Did you mean to call it instead?", "utils/chats/streamCanvas.ts(267,19): error TS2339: Property 'countTokens' does not exist on type 'TokenManager'.", "utils/chats/streamCanvas.ts(269,32): error TS2774: This condition will always return true since this function is always defined. Did you mean to call it instead?", "utils/chats/streamCanvas.ts(311,19): error TS2339: Property 'countTokens' does not exist on type 'TokenManager'.", "utils/chats/streamCanvas.ts(313,37): error TS2339: Property 'countTokens' does not exist on type 'TokenManager'.", "utils/chats/streamCanvas.ts(322,42): error TS2339: Property 'truncateToTokenLength' does not exist on type 'TokenManager'.", "utils/chats/streamCanvas.ts(339,19): error TS2774: This condition will always return true since this function is always defined. Did you mean to call it instead?", "utils/chats/streamCanvas.ts(347,39): error TS2345: Argument of type 'ChatPromptArgs' is not assignable to parameter of type 'Workspace'.", "utils/chats/streamCanvas.ts(352,26): error TS2722: Cannot invoke an object which is possibly 'undefined'.", "utils/chats/streamCanvas.ts(352,26): error TS18048: 'LLMConnector.streamGetChatCompletion' is possibly 'undefined'.", "utils/chats/streamCanvas.ts(352,63): error TS2345: Argument of type 'string' is not assignable to parameter of type 'ChatMessage[]'.", "utils/chats/streamCanvas.ts(389,36): error TS2345: Argument of type '{ uuid: string; sources: CanvasSource[]; type: string; textResponse: any; close: boolean; error: boolean; }' is not assignable to parameter of type 'ResponseChunkData'.", "utils/chats/streamCDB.ts(7,1): error TS6192: All imports in import declaration are unused.", "utils/chats/streamCDB.ts(176,38): error TS2345: Argument of type 'CDBOptions' is not assignable to parameter of type 'FlowOptions'.", "utils/chats/streamDD.ts(2015,39): error TS18047: 'chat' is possibly 'null'.", "utils/chats/streamDD.ts(2019,31): error TS18047: 'chat' is possibly 'null'.", "utils/chats/streamDD.ts(2019,57): error TS18047: 'chat' is possibly 'null'.", "utils/chats/streamDD.ts(2054,35): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.", "utils/chats/streamLQA.ts(21,1): error TS6192: All imports in import declaration are unused.", "utils/chats/streamLQA.ts(168,53): error TS2345: Argument of type 'FilteredUser | null' is not assignable to parameter of type 'User | null | undefined'.", "utils/chats/streamLQA.ts(182,20): error TS2339: Property 'attachment_context_percentage' does not exist on type '{ WhisperProvider: string; WhisperModelPref: string; TextToSpeechProvider: string; TTSOpenAIKey: boolean; TTSOpenAIVoiceModel: string | undefined; TTSElevenLabsKey: boolean; TTSElevenLabsVoiceModel: string | undefined; ... 107 more ...; VectorDB: string | undefined; }'.", "utils/chats/streamLQA.ts(191,7): error TS2554: Expected 2 arguments, but got 6.", "utils/chats/streamLQA.ts(196,34): error TS2345: Argument of type 'CommandResponse' is not assignable to parameter of type 'ResponseChunkData'.", "utils/chats/streamLQA.ts(225,32): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ WhisperProvider: string; WhisperModelPref: string; TextToSpeechProvider: string; TTSOpenAIKey: boolean; TTSOpenAIVoiceModel: string | undefined; TTSElevenLabsKey: boolean; TTSElevenLabsVoiceModel: string | undefined; ... 107 more ...; VectorDB: string | undefined; }'.", "utils/chats/streamLQA.ts(266,28): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ WhisperProvider: string; WhisperModelPref: string; TextToSpeechProvider: string; TTSOpenAIKey: boolean; TTSOpenAIVoiceModel: string | undefined; TTSElevenLabsKey: boolean; TTSElevenLabsVoiceModel: string | undefined; ... 107 more ...; VectorDB: string | undefined; }'.", "utils/chats/streamLQA.ts(285,29): error TS7053: Element implicitly has an 'any' type because expression of type '`LLMProvider${string}`' can't be used to index type '{ WhisperProvider: string; WhisperModelPref: string; TextToSpeechProvider: string; TTSOpenAIKey: boolean; TTSOpenAIVoiceModel: string | undefined; TTSElevenLabsKey: boolean; TTSElevenLabsVoiceModel: string | undefined; ... 107 more ...; VectorDB: string | undefined; }'.", "utils/chats/streamLQA.ts(325,25): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ WhisperProvider: string; WhisperModelPref: string; TextToSpeechProvider: string; TTSOpenAIKey: boolean; TTSOpenAIVoiceModel: string | undefined; TTSElevenLabsKey: boolean; TTSElevenLabsVoiceModel: string | undefined; ... 107 more ...; VectorDB: string | undefined; }'.", "utils/chats/streamLQA.ts(517,35): error TS2345: Argument of type 'import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/models\").Workspace' is not assignable to parameter of type 'Workspace'.", "utils/chats/streamLQA.ts(719,34): error TS2339: Property 'getAdjacentVectors' does not exist on type 'BaseVectorDatabaseProvider'.", "utils/chats/streamLQA.ts(943,34): error TS2345: Argument of type 'import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/models\").Workspace' is not assignable to parameter of type 'Workspace'.", "utils/chats/streamLQA.ts(1131,41): error TS18047: 'chat' is possibly 'null'.", "utils/chats/streamLQA.ts(1133,37): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.", "utils/collectorApi/index.ts(3,1): error TS6192: All imports in import declaration are unused.", "utils/contextualization/index.ts(44,7): error TS2353: Object literal may only specify known properties, and 'model' does not exist in type 'LLMProviderClassParams'.", "utils/database/index.ts(81,13): error TS2339: Property 'SystemSettings' does not exist on type 'typeof import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/systemSettings\")'.", "utils/database/index.ts(93,16): error TS2339: Property 'migrateTable' does not exist on type '{ usernameRegex: RegExp; writable: readonly [\"username\", \"password\", \"pfpFilename\", \"role\", \"suspended\", \"custom_ai_userselected\", \"custom_ai_option\", \"custom_ai_selected_engine\", \"custom_system_prompt\", \"economy_system_id\", \"organizationId\"]; ... 16 more ...; getActiveStyleProfile: (userId: string | number) => Prom...'.", "utils/database/index.ts(94,21): error TS2339: Property 'migrateTable' does not exist on type '{ defaultPrompt: string; writable: readonly [\"name\", \"slug\", \"vectorTag\", \"openAiTemp\", \"openAiHistory\", \"lastUpdatedAt\", \"openAiPrompt\", \"similarityThreshold\", \"chatProvider\", ... 15 more ..., \"sharedWithOrg\"]; ... 22 more ...; getPopulatedWorkspaces: (user: any) => Promise<...>; }'.", "utils/database/index.ts(95,25): error TS2339: Property 'migrateTable' does not exist on type 'WorkspaceUserModelStatic'.", "utils/database/index.ts(96,20): error TS2339: Property 'migrateTable' does not exist on type 'DocumentModelStatic'.", "utils/database/index.ts(97,27): error TS2339: Property 'migrateTable' does not exist on type '{ bulkInsert: (vectorRecords?: VectorRecord[]) => Promise<BulkInsertResult>; deleteForWorkspace: (workspaceId: number, user: any, slugModule: any) => Promise<...>; where: (clause?: any, limit?: number | undefined) => Promise<...>; deleteIds: (ids?: number[]) => Promise<...>; }'.", "utils/database/index.ts(98,26): error TS2339: Property 'migrateTable' does not exist on type '{ new: ({ workspaceId, prompt, response, user, threadId, include, apiSessionId, invoice_ref, metrics, }: WorkspaceChatData) => Promise<CreateChatResult>; forWorkspaceByUser: (workspaceId?: number | null, userId?: number | null, limit?: number | null, orderBy?: any) => Promise<...>; ... 12 more ...; forThread: (threa...'.", "utils/database/index.ts(99,18): error TS2339: Property 'migrateTable' does not exist on type 'InviteModelStatic'.", "utils/database/index.ts(100,27): error TS2339: Property 'migrateTable' does not exist on type '{ get: (clause?: WhereClause) => Promise<{ text: string | null; heading: string | null; id: number; createdAt: Date; response: string | null; } | null>; where: (clause?: WhereClause, limit?: number | undefined) => Promise<...>; save: (message: WelcomeMessageData) => Promise<...>; getMessages: () => Promise<...>; }'.", "utils/database/index.ts(101,18): error TS2339: Property 'migrateTable' does not exist on type '{ tablename: string; writable: never[]; makeSecret: () => string; create: (createdByUserId?: number | null) => Promise<ApiKeyCreateResult>; get: (clause?: WhereClause) => Promise<...>; count: (clause?: WhereClause) => Promise<...>; delete: (clause?: WhereClause) => Promise<...>; where: (clause?: WhereClause, limit?:...'.", "utils/DeepSearch/deepsearchproviders/google.ts(67,11): error TS2564: Property '_location' has no initializer and is not definitely assigned in the constructor.", "utils/DeepSearch/deepsearchproviders/google.ts(80,10): error TS2551: Property 'location' does not exist on type 'GoogleDeepSearchProvider'. Did you mean '_location'?", "utils/DeepSearch/deepsearchproviders/google.ts(179,11): error TS2353: Object literal may only specify known properties, and 'generationConfig' does not exist in type 'GenerateContentParameters'.", "utils/DeepSearch/index.ts(152,23): error TS18048: 'results.searchResults.length' is possibly 'undefined'.", "utils/DeepSearch/index.ts(195,7): error TS2345: Argument of type 'string' is not assignable to parameter of type 'Record<string, any>'.", "utils/documentEditing/editingLogic.ts(2,1): error TS6192: All imports in import declaration are unused.", "utils/documentEditing/lineLevel/validation.ts(79,52): error TS2345: Argument of type 'ProseMirrorDocument' is not assignable to parameter of type 'ProseMirrorNode'.", "utils/documentEditing/lineLevel/validation.ts(110,45): error TS2339: Property 'streamChatCompletion' does not exist on type 'RobustLLMConnector'.", "utils/documentEditing/utils/documentCleanup.ts(9,3): error TS2724: '\"../../../types/document-processing\"' has no exported member named '_ProseMirrorElementNode'. Did you mean 'ProseMirrorElementNode'?", "utils/documentEditing/utils/loggerUtils.ts(147,35): error TS2339: Property 'originalContent' does not exist on type 'DocumentEditingSuggestion'.", "utils/documentEditing/utils/loggerUtils.ts(148,47): error TS2339: Property 'originalContent' does not exist on type 'DocumentEditingSuggestion'.", "utils/documentEditing/utils/loggerUtils.ts(151,33): error TS2339: Property 'globalPartId' does not exist on type 'DocumentEditingSuggestion'.", "utils/documentEditing/utils/loggerUtils.ts(155,66): error TS2339: Property 'globalPartId' does not exist on type 'DocumentEditingSuggestion'.", "utils/documentEditing/utils/loggerUtils.ts(186,28): error TS2367: This comparison appears to be unintentional because the types '\"other\" | \"style\" | \"correction\" | \"improvement\" | \"legal\" | \"IMPROVE_CONTENT\" | \"Type not available\"' and '\"DELETE\"' have no overlap.", "utils/documentEditing/workflows/lineLevelWorkflow.ts(64,7): error TS2345: Argument of type 'import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/document-processing\").ProseMirrorDocument' is not assignable to parameter of type 'import(\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/prosemirror\").ProseMirrorDocument'.", "utils/documentEditing/workflows/lineLevelWorkflow.ts(87,9): error TS2561: Object literal may only specify known properties, but 'error' does not exist in type 'EditingResult'. Did you mean to write 'errors'?", "utils/documentEditing/workflows/lineLevelWorkflow.ts(117,7): error TS2345: Argument of type 'DocumentEditingSuggestion[]' is not assignable to parameter of type 'LineLevelSuggestion[]'.", "utils/documentEditing/workflows/lineLevelWorkflow.ts(169,52): error TS2345: Argument of type 'ProseMirrorDocument' is not assignable to parameter of type 'ProseMirrorNode'.", "utils/documentEditing/workflows/lineLevelWorkflow.ts(173,7): error TS2345: Argument of type 'LineInfo[]' is not assignable to parameter of type 'IndexedLine[]'.", "utils/documentEditing/workflows/lineLevelWorkflow.ts(255,7): error TS2353: Object literal may only specify known properties, and 'data' does not exist in type 'EditingResult'.", "utils/docx/compareAndHighlight.ts(11,27): error TS7016: Could not find a declaration file for module 'diff'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/diff/lib/index.js' implicitly has an 'any' type.", "utils/docx/compareAndHighlight.ts(13,8): error TS1192: Module '\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/i18n\"' has no default export.", "utils/docx/editWithLLM.ts(2,8): error TS1192: Module '\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/i18n\"' has no default export.", "utils/docx/templateRenderer.ts(6,8): error TS1192: Module '\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/i18n\"' has no default export.", "utils/docx/textToDocx.ts(4,8): error TS1192: Module '\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/i18n\"' has no default export.", "utils/EmbeddingEngines/cohere/index.ts(57,19): error TS2345: Argument of type '(res: CohereEmbedResponse) => void' is not assignable to parameter of type '(value: EmbedResponse) => void | PromiseLike<void>'.", "utils/EmbeddingEngines/jina/index.ts(34,11): error TS2564: Property '_task' has no initializer and is not definitely assigned in the constructor.", "utils/EmbeddingEngines/jina/index.ts(35,11): error TS2564: Property '_lateChunking' has no initializer and is not definitely assigned in the constructor.", "utils/EmbeddingEngines/jina/index.ts(36,11): error TS2564: Property '_embeddingType' has no initializer and is not definitely assigned in the constructor.", "utils/EmbeddingEngines/jina/index.ts(44,5): error TS2554: Expected 1 arguments, but got 0.", "utils/EmbeddingEngines/jina/index.ts(63,10): error TS2551: Property 'task' does not exist on type 'JinaEmbedder'. Did you mean '_task'?", "utils/EmbeddingEngines/jina/index.ts(68,10): error TS2551: Property 'lateChunking' does not exist on type 'JinaEmbedder'. Did you mean '_lateChunking'?", "utils/EmbeddingEngines/jina/index.ts(69,10): error TS2551: Property 'embeddingType' does not exist on type 'JinaEmbedder'. Did you mean '_embeddingType'?", "utils/EmbeddingEngines/lmstudio/index.ts(83,17): error TS2345: Argument of type '(json: LMStudioResponse) => { data: number[]; error: null; }' is not assignable to parameter of type '(value: unknown) => { data: number[]; error: null; } | PromiseLike<{ data: number[]; error: null; }>'.", "utils/EmbeddingEngines/lmstudio/index.ts(115,11): error TS2769: No overload matches this call.", "utils/EmbeddingEngines/openAi/index.ts(11,11): error TS6196: 'EmbeddingResponse' is declared but never used.", "utils/EmbeddingEngines/voyageAi/index.ts(49,7): error TS2554: Expected 1 arguments, but got 2.", "utils/EmbeddingEngines/voyageAi/index.ts(59,71): error TS2554: Expected 1 arguments, but got 2.", "utils/EncryptionManager/index.ts(13,11): error TS2564: Property 'encryptionKey' has no initializer and is not definitely assigned in the constructor.", "utils/EncryptionManager/index.ts(14,11): error TS2564: Property 'encryptionSalt' has no initializer and is not definitely assigned in the constructor.", "utils/EncryptionManager/index.ts(26,39): error TS2565: Property 'encryptionKey' is used before being assigned.", "utils/EncryptionManager/index.ts(26,59): error TS2565: Property 'encryptionSalt' is used before being assigned.", "utils/files/docxSessionCleanup.ts(53,11): error TS18046: 'statErr' is of type 'unknown'.", "utils/files/index.ts(99,30): error TS2345: Argument of type 'FilteredUser' is not assignable to parameter of type 'AuthenticatedUser'.", "utils/files/logo.ts(3,18): error TS7016: Could not find a declaration file for module 'mime'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/mime/index.js' implicitly has an 'any' type.", "utils/files/logo.ts(101,32): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.", "utils/files/logo.ts(108,19): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string | undefined'.", "utils/files/pfp.ts(3,18): error TS7016: Could not find a declaration file for module 'mime'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/mime/index.js' implicitly has an 'any' type.", "utils/helpers/admin/index.ts(41,30): error TS2345: Argument of type 'UserRole' is not assignable to parameter of type 'UserRole.MANAGER | UserRole.DEFAULT'.", "utils/helpers/admin/index.ts(49,30): error TS2345: Argument of type 'UserRole' is not assignable to parameter of type 'UserRole.SUPERUSER | UserRole.DEFAULT'.", "utils/helpers/admin/index.ts(106,30): error TS2345: Argument of type 'UserRole' is not assignable to parameter of type 'UserRole.MANAGER | UserRole.DEFAULT'.", "utils/helpers/admin/index.ts(114,30): error TS2345: Argument of type 'UserRole' is not assignable to parameter of type 'UserRole.SUPERUSER | UserRole.DEFAULT'.", "utils/helpers/autoCodingPromptGenerator.ts(60,11): error TS2564: Property 'paths' has no initializer and is not definitely assigned in the constructor.", "utils/helpers/chat/convertTo.ts(474,3): error TS2484: Export declaration conflicts with exported declaration of 'ChatRecord'.", "utils/helpers/chat/convertTo.ts(475,3): error TS2484: Export declaration conflicts with exported declaration of 'ChatFilters'.", "utils/helpers/chat/convertTo.ts(476,3): error TS2484: Export declaration conflicts with exported declaration of 'ExportFormat'.", "utils/helpers/chat/convertTo.ts(477,3): error TS2484: Export declaration conflicts with exported declaration of 'ExportResult'.", "utils/helpers/chat/convertTo.ts(478,3): error TS2484: Export declaration conflicts with exported declaration of 'PreparedChatData'.", "utils/helpers/chat/convertTo.ts(479,3): error TS2484: Export declaration conflicts with exported declaration of 'AlpacaChatData'.", "utils/helpers/chat/convertTo.ts(480,3): error TS2484: Export declaration conflicts with exported declaration of 'WorkspaceChatsMap'.", "utils/helpers/chat/convertTo.ts(481,3): error TS2484: Export declaration conflicts with exported declaration of 'Source'.", "utils/helpers/chat/convertTo.ts(482,3): error TS2484: Export declaration conflicts with exported declaration of 'ResponseData'.", "utils/helpers/chat/convertTo.ts(483,3): error TS2484: Export declaration conflicts with exported declaration of 'ChatType'.", "utils/helpers/chat/convertTo.ts(484,3): error TS2484: Export declaration conflicts with exported declaration of 'ExportFormatType'.", "utils/helpers/chat/index.ts(9,3): error TS2724: '\"../documentDisplay\"' has no exported member named '_FULLY_LOADED_TRANSLATIONS'. Did you mean 'FULLY_LOADED_TRANSLATIONS'?", "utils/helpers/chat/index.ts(137,3): error TS2339: Property '_llmContextWindow' does not exist on type 'DeepSearchFetchParams'.", "utils/helpers/chat/index.ts(241,22): error TS2339: Property 'similaritySearch' does not exist on type 'BaseVectorDatabaseProvider'.", "utils/helpers/chat/index.ts(544,63): error TS2739: Type 'ChatHistoryEntry' is missing the following properties from type 'ChatRecord': id, prompt, createdAt", "utils/helpers/chat/index.ts(711,5): error TS2339: Property '_contextTexts' does not exist on type 'PromptArgs'.", "utils/helpers/chat/index.ts(712,5): error TS2339: Property '_chatHistory' does not exist on type 'PromptArgs'.", "utils/helpers/chat/index.ts(786,63): error TS2739: Type 'ChatHistoryEntry' is missing the following properties from type 'ChatRecord': id, prompt, createdAt", "utils/helpers/chat/index.ts(1365,34): error TS2339: Property 'id' does not exist on type 'never'.", "utils/helpers/chat/LLMPerformanceMonitor.ts(128,3): error TS2484: Export declaration conflicts with exported declaration of 'StreamMetrics'.", "utils/helpers/chat/LLMPerformanceMonitor.ts(129,3): error TS2484: Export declaration conflicts with exported declaration of 'UsageReport'.", "utils/helpers/chat/LLMPerformanceMonitor.ts(130,3): error TS2484: Export declaration conflicts with exported declaration of 'Message'.", "utils/helpers/chat/LLMPerformanceMonitor.ts(131,3): error TS2484: Export declaration conflicts with exported declaration of 'OpenAICompatibleStream'.", "utils/helpers/chat/LLMPerformanceMonitor.ts(132,3): error TS2484: Export declaration conflicts with exported declaration of 'MonitoredStream'.", "utils/helpers/chat/LLMPerformanceMonitor.ts(133,3): error TS2484: Export declaration conflicts with exported declaration of 'AsyncFunctionResult'.", "utils/helpers/chat/responses.ts(302,17): error TS2349: This expression is not callable.", "utils/helpers/chat/responses.ts(312,17): error TS2349: This expression is not callable.", "utils/helpers/chat/tokenCount.ts(118,15): error TS2484: Export declaration conflicts with exported declaration of 'TokenCountResult'.", "utils/helpers/chat/tokenCount.ts(118,33): error TS2484: Export declaration conflicts with exported declaration of 'DocumentData'.", "utils/helpers/chat/tokenCount.ts(118,47): error TS2484: Export declaration conflicts with exported declaration of 'LLMProvider'.", "utils/helpers/chat/tokenCount.ts(118,60): error TS2484: Export declaration conflicts with exported declaration of 'WorkspaceRecord'.", "utils/helpers/customModels.ts(376,39): error TS2339: Property 'models' does not exist on type '{}'.", "utils/helpers/customModels.ts(386,46): error TS2339: Property 'models' does not exist on type '{}'.", "utils/helpers/customModels.ts(388,25): error TS2339: Property 'models' does not exist on type '{}'.", "utils/helpers/customModels.ts(626,46): error TS2345: Argument of type 'string | null | undefined' is not assignable to parameter of type 'string'.", "utils/helpers/starredDocuments.ts(108,32): error TS2339: Property 'docId' does not exist on type 'never'.", "utils/helpers/starredDocuments.ts(108,63): error TS2345: Argument of type 'SourceDocument' is not assignable to parameter of type 'never'.", "utils/helpers/starredDocuments.ts(115,17): error TS2339: Property 'docpath' does not exist on type 'never'.", "utils/helpers/starredDocuments.ts(115,33): error TS2339: Property 'metadata' does not exist on type 'never'.", "utils/helpers/starredDocuments.ts(115,58): error TS2339: Property 'metadata' does not exist on type 'never'.", "utils/helpers/starredDocuments.ts(116,32): error TS2339: Property 'filename' does not exist on type 'never'.", "utils/helpers/starredDocuments.ts(116,49): error TS2339: Property 'metadata' does not exist on type 'never'.", "utils/helpers/starredDocuments.ts(118,24): error TS2339: Property 'docId' does not exist on type 'never'.", "utils/helpers/starredDocuments.ts(119,24): error TS2339: Property 'title' does not exist on type 'never'.", "utils/helpers/starredDocuments.ts(119,38): error TS2339: Property 'metadata' does not exist on type 'never'.", "utils/helpers/starredDocuments.ts(183,35): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.", "utils/helpers/tiktoken.ts(14,11): error TS2564: Property '_model' has no initializer and is not definitely assigned in the constructor.", "utils/helpers/tiktoken.ts(19,10): error TS2551: Property 'model' does not exist on type 'TokenManager'. Did you mean '_model'?", "utils/helpers/tiktoken.ts(21,32): error TS2345: Argument of type 'string' is not assignable to parameter of type 'TiktokenEncoding'.", "utils/helpers/tiktoken.ts(26,38): error TS2345: Argument of type 'string' is not assignable to parameter of type 'TiktokenModel'.", "utils/helpers/vectorizationCheck.ts(45,37): error TS2339: Property 'checkDocumentHasDocId' does not exist on type 'BaseVectorDatabaseProvider'.", "utils/helpers/vectorizationCheck.ts(124,47): error TS7006: Parameter 'field' implicitly has an 'any' type.", "utils/helpers/vectorizationCheck.ts(354,66): error TS18046: 'error' is of type 'unknown'.", "utils/MCP/hypervisor/index.ts(39,11): error TS2430: Interface 'ExtendedMCPClient' incorrectly extends interface 'Client<{ method: string; params?: { [x: string]: unknown; _meta?: { [x: string]: unknown; progressToken?: string | number | undefined; } | undefined; } | undefined; }, { method: string; params?: { [x: string]: unknown; _meta?: { ...; } | undefined; } | undefined; }, { ...; }>'.", "utils/MCP/hypervisor/index.ts(89,13): error TS2564: Property 'mcpServerJSONPath' has no initializer and is not definitely assigned in the constructor.", "utils/MCP/hypervisor/index.ts(238,25): error TS2339: Property 'close' does not exist on type 'never'.", "utils/MCP/hypervisor/index.ts(313,17): error TS2352: Conversion of type 'Client<{ method: string; params?: { [x: string]: unknown; _meta?: { [x: string]: unknown; progressToken?: string | number | undefined; } | undefined; } | undefined; }, { method: string; params?: { [x: string]: unknown; _meta?: { ...; } | undefined; } | undefined; }, { ...; }>' to type 'ExtendedMCPClient' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "utils/MCP/index.ts(98,7): error TS2417: Class static side 'typeof MCPCompatibilityLayer' incorrectly extends base class static side 'typeof MCPHypervisor'.", "utils/middleware/isSupportedRepoProviders.ts(20,26): error TS2339: Property 'text' does not exist on type 'Response<any, Record<string, any>>'.", "utils/middleware/reportOwnerOrAdmin.ts(2,8): error TS2613: Module '\"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/systemReport\"' has no default export. Did you mean to use 'import { SystemReport } from \"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/systemReport\"' instead?", "utils/middleware/validatedRequest.ts(56,43): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.", "utils/middleware/validatedRequest.ts(70,7): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.", "utils/middleware/validatedRequest.ts(70,29): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.", "utils/middleware/validBrowserExtensionApiKey.ts(35,28): error TS2352: Conversion of type 'FilteredUser | null' to type 'AuthenticatedUser' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "utils/middleware/validWorkspace.ts(36,51): error TS2554: Expected 1-2 arguments, but got 3.", "utils/middleware/validWorkspace.ts(82,51): error TS2554: Expected 1-2 arguments, but got 3.", "utils/middleware/validWorkspace.ts(114,7): error TS2345: Argument of type 'UserRole' is not assignable to parameter of type 'UserRole.ADMIN | UserRole.MANAGER | UserRole.SUPERUSER'.", "utils/middleware/validWorkspace.ts(139,5): error TS2345: Argument of type '{ slug: string | undefined; workspace_id: any; } | { slug: string | undefined; user_id: number | null; }' is not assignable to parameter of type 'WhereClause | undefined'.", "utils/modulePatches.ts(15,26): error TS2352: Conversion of type 'Module' to type 'ModulePrototype' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "utils/modulePatches.ts(24,1): error TS2739: Type '(id: string) => any' is missing the following properties from type 'NodeRequire': cache, extensions, main, resolve", "utils/modulePatches.ts(24,2): error TS2352: Conversion of type 'Module' to type 'ModulePrototype' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "utils/notifications/slack.ts(847,9): error TS2353: Object literal may only specify known properties, and 'timeout' does not exist in type 'RequestInit'.", "utils/PasswordRecovery/index.ts(119,39): error TS18047: 'passwordResetToken' is possibly 'null'.", "utils/proseMirror/modules/analysis.ts(339,18): error TS2339: Property 'includes' does not exist on type 'string | number'.", "utils/proseMirror/modules/analysis.ts(339,52): error TS2339: Property 'includes' does not exist on type 'string | number'.", "utils/proseMirror/modules/documentBuilder.ts(11,3): error TS6196: 'ProseMirrorNode' is declared but never used.", "utils/proseMirror/modules/documentBuilder.ts(149,41): error TS7006: Parameter 'child' implicitly has an 'any' type.", "utils/robustLlmUtils/connectors/robustConnector.ts(214,11): error TS2353: Object literal may only specify known properties, and 'maxRetries' does not exist in type 'ParseOptions'.", "utils/robustLlmUtils/providers/embedderInstantiator.ts(61,33): error TS2554: Expected 0 arguments, but got 2.", "utils/robustLlmUtils/providers/embedderInstantiator.ts(96,9): error TS2554: Expected 0 arguments, but got 3.", "utils/robustLlmUtils/providers/providerInstantiator.ts(148,7): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'."]}