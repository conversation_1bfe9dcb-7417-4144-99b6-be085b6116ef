#!/usr/bin/env node

/**
 * Validates that all required Swagger files are present after the build process.
 * This script should be run as part of the build process to ensure
 * all necessary files are available for production deployment.
 */

const fs = require("fs");
const path = require("path");

// Required files that must exist in dist/swagger after build
const REQUIRED_FILES = [
  "index.css",
  "dark-swagger.css",
  "index.js",
  "openapi.json",
];

// Check if we're in the server directory or root
const distPath = fs.existsSync("dist") ? "dist" : path.join("server", "dist");
const swaggerDistPath = path.join(distPath, "swagger");

console.log("Validating Swagger build output...");
console.log(`Checking directory: ${swaggerDistPath}`);

// Check if swagger dist directory exists
if (!fs.existsSync(swaggerDistPath)) {
  console.error(`❌ Swagger dist directory not found: ${swaggerDistPath}`);
  console.error("Make sure to run the build command first.");
  process.exit(1);
}

// Check each required file
let hasErrors = false;
for (const file of REQUIRED_FILES) {
  const filePath = path.join(swaggerDistPath, file);
  if (!fs.existsSync(filePath)) {
    console.error(`❌ Missing required file: ${file}`);
    hasErrors = true;
  } else {
    const stats = fs.statSync(filePath);
    if (stats.size === 0) {
      console.error(`❌ File is empty: ${file}`);
      hasErrors = true;
    } else {
      console.log(`✅ Found ${file} (${stats.size} bytes)`);
    }
  }
}

if (hasErrors) {
  console.error("\n❌ Swagger build validation failed!");
  console.error("Some required files are missing or empty.");
  process.exit(1);
} else {
  console.log("\n✅ Swagger build validation passed!");
  console.log("All required files are present and valid.");
  process.exit(0);
}
