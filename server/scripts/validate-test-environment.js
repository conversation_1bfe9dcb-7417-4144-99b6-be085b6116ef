#!/usr/bin/env node
/**
 * Test Environment Validation Script
 *
 * Validates that the test environment is set up correctly
 * for both development and CI/CD environments.
 */

const fs = require("fs");
const path = require("path");

class EnvironmentValidator {
  constructor() {
    this.isCI = process.env.CI === "true" || !!process.env.CI;
    this.isGitHubActions = process.env.GITHUB_ACTIONS === "true";
    this.isTest = process.env.NODE_ENV === "test";
    this.cwd = process.cwd();
  }

  /**
   * Get environment information
   */
  getEnvironmentInfo() {
    return {
      environment: this.getEnvironmentType(),
      nodeEnv: process.env.NODE_ENV || "unknown",
      ci: this.isCI,
      githubActions: this.isGitHubActions,
      currentDirectory: this.cwd,
      nodeVersion: process.version,
      platform: process.platform,
    };
  }

  getEnvironmentType() {
    if (this.isGitHubActions) return "GitHub Actions";
    if (this.isCI) return "CI";
    return "Development";
  }

  /**
   * Validate storage directory structure
   */
  validateStorageStructure() {
    const basePath = this.isCI
      ? path.join(this.cwd, "server", "storage")
      : path.join(__dirname, "../storage");

    const requiredPaths = [
      "uploadlogs",
      "documents",
      "vector-cache",
      "lancedb",
      "tmp",
    ];

    const results = {
      basePath,
      exists: fs.existsSync(basePath),
      directories: {},
    };

    if (results.exists) {
      for (const dir of requiredPaths) {
        const dirPath = path.join(basePath, dir);
        results.directories[dir] = {
          path: dirPath,
          exists: fs.existsSync(dirPath),
          writable: this.checkWritable(dirPath),
        };
      }
    }

    return results;
  }

  /**
   * Check if directory is writable
   */
  checkWritable(dirPath) {
    try {
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }
      const testFile = path.join(dirPath, `.write-test-${Date.now()}`);
      fs.writeFileSync(testFile, "test");
      fs.unlinkSync(testFile);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate cleanup script paths
   */
  validateCleanupPaths() {
    const cleanupScript = this.isCI
      ? path.join(this.cwd, "server", "scripts", "cleanup-test-logs.js")
      : path.join(__dirname, "cleanup-test-logs.js");

    const testCleanupHelper = this.isCI
      ? path.join(this.cwd, "server", "tests", "helpers", "testCleanup.ts")
      : path.join(__dirname, "../tests/helpers/testCleanup.ts");

    return {
      cleanupScript: {
        path: cleanupScript,
        exists: fs.existsSync(cleanupScript),
      },
      testCleanupHelper: {
        path: testCleanupHelper,
        exists: fs.existsSync(testCleanupHelper),
      },
    };
  }

  /**
   * Validate test configuration
   */
  validateTestConfig() {
    const jestConfig = this.isCI
      ? path.join(this.cwd, "server", "jest.config.js")
      : path.join(__dirname, "../jest.config.js");

    const setupFile = this.isCI
      ? path.join(this.cwd, "server", "tests", "setup.ts")
      : path.join(__dirname, "../tests/setup.ts");

    return {
      jestConfig: {
        path: jestConfig,
        exists: fs.existsSync(jestConfig),
      },
      setupFile: {
        path: setupFile,
        exists: fs.existsSync(setupFile),
      },
    };
  }

  /**
   * Run full validation
   */
  validate() {
    console.log("🔍 Test Environment Validation");
    console.log("═".repeat(50));

    const env = this.getEnvironmentInfo();
    console.log("\n📋 Environment Information:");
    Object.entries(env).forEach(([key, value]) => {
      console.log(`  ${key}: ${value}`);
    });

    console.log("\n📁 Storage Structure:");
    const storage = this.validateStorageStructure();
    console.log(`  Base path: ${storage.basePath}`);
    console.log(`  Exists: ${storage.exists ? "✅" : "❌"}`);

    if (storage.exists) {
      Object.entries(storage.directories).forEach(([name, info]) => {
        const status = info.exists ? (info.writable ? "✅" : "⚠️ ") : "❌";
        console.log(
          `  ${name}: ${status} ${info.exists ? "(exists)" : "(missing)"}${info.writable === false ? " (not writable)" : ""}`
        );
      });
    }

    console.log("\n🧹 Cleanup Scripts:");
    const cleanup = this.validateCleanupPaths();
    Object.entries(cleanup).forEach(([name, info]) => {
      const status = info.exists ? "✅" : "❌";
      console.log(`  ${name}: ${status} ${info.path}`);
    });

    console.log("\n⚙️  Test Configuration:");
    const config = this.validateTestConfig();
    Object.entries(config).forEach(([name, info]) => {
      const status = info.exists ? "✅" : "❌";
      console.log(`  ${name}: ${status} ${info.path}`);
    });

    // Overall validation
    const allValid = [
      storage.exists,
      Object.values(storage.directories).every((d) => d.exists && d.writable),
      Object.values(cleanup).every((c) => c.exists),
      Object.values(config).every((c) => c.exists),
    ].every(Boolean);

    console.log("\n🎯 Overall Status:");
    console.log(`  Environment: ${allValid ? "✅ Ready" : "❌ Issues Found"}`);

    if (!allValid) {
      console.log("\n🔧 Suggested Actions:");
      if (!storage.exists) {
        console.log(
          "  • Create storage directories: mkdir -p server/storage/{uploadlogs,documents,vector-cache,lancedb,tmp}"
        );
      }
      if (!Object.values(storage.directories).every((d) => d.exists)) {
        console.log("  • Create missing storage subdirectories");
      }
      if (!Object.values(cleanup).every((c) => c.exists)) {
        console.log("  • Verify cleanup scripts are in the correct location");
      }
    }

    return allValid;
  }

  /**
   * Create missing directories
   */
  fix() {
    console.log("🔧 Fixing environment issues...");

    const storage = this.validateStorageStructure();
    const basePath = storage.basePath;

    // Create base storage directory
    if (!storage.exists) {
      fs.mkdirSync(basePath, { recursive: true });
      console.log(`✅ Created storage directory: ${basePath}`);
    }

    // Create subdirectories
    const requiredPaths = [
      "uploadlogs",
      "documents",
      "vector-cache",
      "lancedb",
      "tmp",
    ];
    for (const dir of requiredPaths) {
      const dirPath = path.join(basePath, dir);
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`✅ Created directory: ${dirPath}`);
      }
    }

    console.log("🎉 Environment fix completed!");
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const validator = new EnvironmentValidator();

  if (args.includes("--fix")) {
    validator.fix();
    console.log("");
    validator.validate();
  } else if (args.includes("--help") || args.includes("-h")) {
    console.log(`
Test Environment Validator

Usage:
  node validate-test-environment.js [options]

Options:
  --fix      Attempt to fix environment issues
  --help, -h Show this help message

Examples:
  node validate-test-environment.js        # Validate environment
  node validate-test-environment.js --fix  # Fix issues and validate
`);
  } else {
    const isValid = validator.validate();
    process.exit(isValid ? 0 : 1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch((error) => {
    console.error("❌ Validation failed:", error);
    process.exit(1);
  });
}

module.exports = { EnvironmentValidator };
