// Swagger UI customization script

// This is a client-side script that will run in the browser
// Disable TypeScript checking for this file as it requires DOM APIs
// @ts-nocheck - This file uses browser-specific DOM APIs and is not type-safe in Node.js/TS context

export {};

declare global {
  interface Window {
    SWAGGER_DOCS_ENV?: string;
  }
}

function waitForElm(selector: string): Promise<Element> {
  return new Promise((resolve) => {
    if (typeof document === "undefined") {
      throw new Error("Document is not available");
    }

    const existingElement = document.querySelector(selector);
    if (existingElement) {
      return resolve(existingElement);
    }

    const observer = new MutationObserver(() => {
      const element = document.querySelector(selector);
      if (element) {
        resolve(element);
        observer.disconnect();
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  });
}

// Force change the Swagger logo in the header
if (typeof window !== "undefined" && typeof document !== "undefined") {
  waitForElm(".topbar-wrapper").then((elm: Element) => {
    if (window.SWAGGER_DOCS_ENV === "development") {
      elm.innerHTML = `<img href='${window.location.origin}' src='http://localhost:3000/public/anything-llm-light.png' width='200'/>`;
    } else {
      elm.innerHTML = `<img href='${window.location.origin}' src='${window.location.origin}/anything-llm-light.png' width='200'/>`;
    }
  });
}
