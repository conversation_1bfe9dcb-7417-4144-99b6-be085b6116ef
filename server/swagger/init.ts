import swaggerAutogen from "swagger-autogen";
import fs from "fs";
import path from "path";

const swaggerGenerator = swaggerAutogen({ openapi: "3.0.0" });

// Skip swagger generation in development mode to prevent nodemon restart loop
if (process.env.NODE_ENV === "development") {
  console.log("Swagger generation skipped in development mode");
  process.exit(0);
}

interface SwaggerDoc {
  info: {
    version: string;
    title: string;
    description: string;
  };
  host: string;
  schemes: string[];
  securityDefinitions: {
    BearerAuth: {
      type: string;
      scheme: string;
      bearerFormat: string;
    };
  };
  security: Array<{ BearerAuth: string[] }>;
  definitions: {
    [key: string]: {
      [property: string]: string | number | boolean | null | undefined;
    };
  };
}

interface SwaggerParameter {
  name: string;
  in?: string;
  description?: string;
  required?: boolean;
  type?: string;
  schema?: {
    type?: string;
    format?: string;
    items?: {
      type?: string;
    };
    properties?: Record<string, unknown>;
  };
  [key: string]: string | boolean | Record<string, unknown> | undefined;
}

const doc: SwaggerDoc = {
  info: {
    version: "1.0.0",
    title: "Instance Developer API",
    description:
      "API endpoints that enable programmatic reading, writing, and updating of the instance. UI supplied by Swagger.io.",
  },
  // Swagger-autogen does not allow us to use relative paths as these will resolve to
  // http:///api in the openapi.json file, so we need to monkey-patch this post-generation.
  host: "/api",
  schemes: ["http"],
  securityDefinitions: {
    BearerAuth: {
      type: "http",
      scheme: "bearer",
      bearerFormat: "JWT",
    },
  },
  security: [{ BearerAuth: [] }],
  definitions: {
    InvalidAPIKey: {
      message: "Invalid API Key",
    },
    Category: {
      id: 1,
      name: "string",
      sub_category: "string",
      description: "string",
      legal_task_prompt: "string",
      legalTaskType: "string",
    },
  },
};

const outputFile = path.resolve(__dirname, "./openapi.json");
const endpointsFiles = [
  "../endpoints/api/auth/index.ts",
  "../endpoints/api/admin/index.ts",
  "../endpoints/api/document/index.ts",
  "../endpoints/api/workspace/index.ts",
  "../endpoints/api/system/index.ts",
  "../endpoints/api/workspaceThread/index.ts",
  "../endpoints/api/userManagement/index.ts",
  "../endpoints/api/openai/index.ts",
  "../endpoints/api/embed/index.ts",
  "../endpoints/api/categories.ts",
];

const filterAuthorizationParameters = (
  parameters: SwaggerParameter[] = []
): SwaggerParameter[] => {
  return parameters.filter((arg) => arg.name !== "Authorization");
};

interface SwaggerGenerationData {
  paths: Record<string, unknown>;
  [key: string]: unknown;
}

swaggerGenerator(outputFile, endpointsFiles, doc).then(
  (value: false | { success: boolean; data: SwaggerGenerationData }) => {
    const data =
      value && typeof value === "object" && "data" in value
        ? value.data
        : value;
    if (!data || !data.paths) {
      console.error("Swagger generation failed or returned invalid data.");
      return;
    }
    // Remove Authorization parameters from arguments.
    for (const pathKey of Object.keys(data.paths)) {
      const pathData = data.paths[pathKey] as Record<string, any>;

      if (pathData.get) {
        pathData.get.parameters = filterAuthorizationParameters(
          pathData.get.parameters
        );
      }

      if (pathData.post) {
        pathData.post.parameters = filterAuthorizationParameters(
          pathData.post.parameters
        );
      }

      if (pathData.delete) {
        pathData.delete.parameters = filterAuthorizationParameters(
          pathData.delete.parameters
        );
      }

      if (pathData.put) {
        pathData.put.parameters = filterAuthorizationParameters(
          pathData.put.parameters
        );
      }
    }

    const openApiSpec = {
      ...data,
      servers: [
        {
          url: "/api",
        },
      ],
    };

    fs.writeFileSync(outputFile, JSON.stringify(openApiSpec, null, 2), {
      encoding: "utf-8",
      flag: "w",
    });

    console.log(`Swagger-autogen:  \x1b[32mPatched servers.url ✔\x1b[0m`);
  }
);
