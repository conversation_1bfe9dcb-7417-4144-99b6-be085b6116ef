import fs from "fs";
import path from "path";
import swaggerUi from "swagger-ui-express";
import type { Express, Request, Response } from "express";

interface SwaggerOptions {
  customCss: string;
  customSiteTitle: string;
  customfavIcon: string;
  customJsStr?: string;
}

function faviconUrl(): string {
  return process.env.NODE_ENV === "production"
    ? "/public/favicon.png"
    : "http://localhost:3000/public/favicon.png";
}

export function setupSwagger(app: Express): void {
  app.use("/api/docs", swaggerUi.serve);

  const options: SwaggerOptions = {
    customCss: [
      fs.readFileSync(path.resolve(__dirname, "index.css"), "utf8"),
      fs.readFileSync(path.resolve(__dirname, "dark-swagger.css"), "utf8"),
    ].join("\n\n\n"),
    customSiteTitle: "ISTLegal Developer API Documentation",
    customfavIcon: faviconUrl(),
  };

  if (process.env.NODE_ENV === "production") {
    const swaggerDocument = require("./openapi.json");
    app.get(
      "/api/docs",
      swaggerUi.setup(swaggerDocument, {
        ...options,
        customJsStr:
          'window.SWAGGER_DOCS_ENV = "production";\n\n' +
          fs.readFileSync(path.resolve(__dirname, "index.js"), "utf8"),
      })
    );
  } else {
    // we regenerate the html page only in development mode to ensure it is up-to-date when the code is hot-reloaded.
    app.get(
      "/api/docs",
      async (_: Request, response: Response): Promise<void> => {
        // #swagger.ignore = true
        const swaggerDocument = require("./openapi.json");
        response.send(
          swaggerUi.generateHTML(swaggerDocument, {
            ...options,
            customJsStr:
              'window.SWAGGER_DOCS_ENV = "development";\n\n' +
              fs.readFileSync(path.resolve(__dirname, "index.js"), "utf8"),
          })
        );
      }
    );
  }
}

export { faviconUrl };
