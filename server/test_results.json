FAIL utils/EmbeddingEngines/__tests__/embedding-behavior.test.ts
  ● Embedding Providers Behavior Tests › Model Information › should have reasonable limits for concurrent processing

    expect(received).toBe(expected) // Object.is equality

    Expected: 16
    Received: 500

      256 |
      257 |       // API-based embedders should have similar limits
    > 258 |       expect(openaiEmbedder.maxConcurrentChunks).toBe(
          |                                                  ^
      259 |         azureEmbedder.maxConcurrentChunks
      260 |       );
      261 |

      at Object.<anonymous> (utils/EmbeddingEngines/__tests__/embedding-behavior.test.ts:258:50)

  ● Embedding Providers Behavior Tests › Error Handling › should provide meaningful error messages

    expect(received).toContain(expected) // indexOf

    Expected substring: "Azure OpenAI API key"
    Received string:    "No Azure API key was set."

      317 |       } catch (error: unknown) {
      318 |         expect(error instanceof Error).toBe(true);
    > 319 |         expect((error as Error).message).toContain("Azure OpenAI API key");
          |                                          ^
      320 |       }
      321 |     });
      322 |   });

      at Object.<anonymous> (utils/EmbeddingEngines/__tests__/embedding-behavior.test.ts:319:42)

FAIL utils/helpers/__tests__/validation.test.ts
  ● CSS Color Validation Tests › isValidCssColor - Invalid colors › should reject malformed hex colors

    expect(received).toBe(expected) // Object.is equality

    Expected: false
    Received: true

      137 |
      138 |       invalidHexColors.forEach((color) => {
    > 139 |         expect(isValidCssColor(color)).toBe(false);
          |                                        ^
      140 |       });
      141 |     });
      142 |

      at utils/helpers/__tests__/validation.test.ts:139:40
          at Array.forEach (<anonymous>)
      at Object.<anonymous> (utils/helpers/__tests__/validation.test.ts:138:24)

  ● CSS Color Validation Tests › isValidCssColor - Invalid colors › should reject malformed RGB colors

    expect(received).toBe(expected) // Object.is equality

    Expected: false
    Received: true

      153 |
      154 |       invalidRgbColors.forEach((color) => {
    > 155 |         expect(isValidCssColor(color)).toBe(false);
          |                                        ^
      156 |       });
      157 |     });
      158 |

      at utils/helpers/__tests__/validation.test.ts:155:40
          at Array.forEach (<anonymous>)
      at Object.<anonymous> (utils/helpers/__tests__/validation.test.ts:154:24)

  ● CSS Color Validation Tests › isValidCssColor - Invalid colors › should reject malformed RGBA colors

    expect(received).toBe(expected) // Object.is equality

    Expected: false
    Received: true

      168 |
      169 |       invalidRgbaColors.forEach((color) => {
    > 170 |         expect(isValidCssColor(color)).toBe(false);
          |                                        ^
      171 |       });
      172 |     });
      173 |

      at utils/helpers/__tests__/validation.test.ts:170:40
          at Array.forEach (<anonymous>)
      at Object.<anonymous> (utils/helpers/__tests__/validation.test.ts:169:25)

  ● CSS Color Validation Tests › isValidCssColor - Invalid colors › should reject malformed HSL colors

    expect(received).toBe(expected) // Object.is equality

    Expected: false
    Received: true

      185 |
      186 |       invalidHslColors.forEach((color) => {
    > 187 |         expect(isValidCssColor(color)).toBe(false);
          |                                        ^
      188 |       });
      189 |     });
      190 |

      at utils/helpers/__tests__/validation.test.ts:187:40
          at Array.forEach (<anonymous>)
      at Object.<anonymous> (utils/helpers/__tests__/validation.test.ts:186:24)

PASS tests/streamCDB-agentic-combination.test.ts
PASS tests/bulk-upload-embed.test.ts
PASS tests/integration/referenceFlowModular.test.ts
PASS tests/streamLQA.test.ts
PASS utils/DeepSearch/__tests__/index.test.ts
FAIL tests/e2e/features/legal-document-generation.test.ts
  ● Test suite failed to run

    Playwright Test needs to be invoked via 'npx playwright test' and excluded from Jest test runs.
    Creating one directory for Playwright tests and one for Jest is the recommended way of doing it.
    See https://playwright.dev/docs/intro for more information about Playwright Test.

      17 | import { TEST_CONFIG, generateTestData } from "../setup/test-config";
      18 |
    > 19 | test.describe("Legal Document Generation", () => {
         |      ^
      20 |   let loginPage: LoginPage;
      21 |   let dashboardPage: DashboardPage;
      22 |   let workspacePage: WorkspacePage;

      at throwIfRunningInsideJest (../node_modules/@playwright/test/node_modules/playwright/lib/common/testType.js:272:11)
      at TestTypeImpl._describe (../node_modules/@playwright/test/node_modules/playwright/lib/common/testType.js:113:5)
      at Function.describe (../node_modules/@playwright/test/node_modules/playwright/lib/transform/transform.js:275:12)
      at Object.<anonymous> (tests/e2e/features/legal-document-generation.test.ts:19:6)

FAIL tests/e2e/journeys/collaboration-workflow.test.ts
  ● Test suite failed to run

    Playwright Test needs to be invoked via 'npx playwright test' and excluded from Jest test runs.
    Creating one directory for Playwright tests and one for Jest is the recommended way of doing it.
    See https://playwright.dev/docs/intro for more information about Playwright Test.

      18 | import path from "path";
      19 |
    > 20 | test.describe("Collaboration Workflow", () => {
         |      ^
      21 |   let primaryUserLogin: LoginPage;
      22 |   let secondaryUserLogin: LoginPage;
      23 |   let primaryDashboard: DashboardPage;

      at throwIfRunningInsideJest (../node_modules/@playwright/test/node_modules/playwright/lib/common/testType.js:272:11)
      at TestTypeImpl._describe (../node_modules/@playwright/test/node_modules/playwright/lib/common/testType.js:113:5)
      at Function.describe (../node_modules/@playwright/test/node_modules/playwright/lib/transform/transform.js:275:12)
      at Object.<anonymous> (tests/e2e/journeys/collaboration-workflow.test.ts:20:6)

FAIL utils/prisma/__tests__/connectionPoolAdvanced.test.ts
  ● Advanced Database Connection Pool Tests › Transaction handling › should handle nested transactions

    expect(received).rejects.toThrow(expected)

    Expected substring: "Nested transactions not supported"
    Received message:   "operations is not iterable"

          240 |           // Simulate processing operations
          241 |           const results = [];
        > 242 |           for (const operation of operations) {
              |                                   ^
          243 |             if (typeof operation === "function") {
          244 |               results.push(await operation(client));
          245 |             } else {

      at Object.<anonymous> (utils/prisma/__tests__/connectionPoolAdvanced.test.ts:242:35)
      at Object.<anonymous> (utils/prisma/__tests__/connectionPoolAdvanced.test.ts:257:16)
      at Object.toThrow (node_modules/@jest/expect/node_modules/expect/build/index.js:218:22)
      at Object.<anonymous> (utils/prisma/__tests__/connectionPoolAdvanced.test.ts:269:17)

PASS tests/integration/frontend-backend/performance-concurrent.test.ts
FAIL utils/__tests__/memoryPerformance.test.ts
  ● Memory Management and Performance Tests › Memory leak detection › should not leak memory with repeated operations

    expect(received).toBeLessThan(expected)

    Expected: < 52428800
    Received:   68452448

      265 |
      266 |       // Memory growth should be reasonable for 100 operations (less than 50MB)
    > 267 |       expect(Math.abs(memoryGrowth)).toBeLessThan(50 * 1024 * 1024);
          |                                      ^
      268 |
      269 |       // Just verify the test completed without major memory leaks
      270 |       // (Memory can fluctuate due to garbage collection timing)

      at Object.<anonymous> (utils/__tests__/memoryPerformance.test.ts:267:38)

PASS tests/streamCDB-agentic-flow-configurations.test.ts
FAIL tests/e2e/features/chat-ai-interactions.test.ts
  ● Test suite failed to run

    Playwright Test needs to be invoked via 'npx playwright test' and excluded from Jest test runs.
    Creating one directory for Playwright tests and one for Jest is the recommended way of doing it.
    See https://playwright.dev/docs/intro for more information about Playwright Test.

      18 | import path from "path";
      19 |
    > 20 | test.describe("Chat AI Interactions", () => {
         |      ^
      21 |   let loginPage: LoginPage;
      22 |   let dashboardPage: DashboardPage;
      23 |   let workspacePage: WorkspacePage;

      at throwIfRunningInsideJest (../node_modules/@playwright/test/node_modules/playwright/lib/common/testType.js:272:11)
      at TestTypeImpl._describe (../node_modules/@playwright/test/node_modules/playwright/lib/common/testType.js:113:5)
      at Function.describe (../node_modules/@playwright/test/node_modules/playwright/lib/transform/transform.js:275:12)
      at Object.<anonymous> (tests/e2e/features/chat-ai-interactions.test.ts:20:6)

PASS utils/boot/__tests__/index.test.ts
PASS tests/integration/documentEditing/documentEditingIntegration.test.ts
FAIL utils/robustLlmUtils/__tests__/networkFailures.test.ts (166.801 s)
  ● Network Failure Handling Tests › HTTP error handling › should handle 4xx client errors without retry in circuit breaker

    expect(received).rejects.toThrow(expected)

    Expected substring: "Circuit breaker: Client error detected"
    Received message:   "Bad Request"

          196 |
          197 |     it("should handle 4xx client errors without retry in circuit breaker", async () => {
        > 198 |       const clientError = new Error("Bad Request");
              |                           ^
          199 |       (clientError as any).status = 400;
          200 |
          201 |       let _failureCount = 0;

      at Object.<anonymous> (utils/robustLlmUtils/__tests__/networkFailures.test.ts:198:27)
      at Object.toThrow (node_modules/@jest/expect/node_modules/expect/build/index.js:218:22)
      at Object.<anonymous> (utils/robustLlmUtils/__tests__/networkFailures.test.ts:217:17)

  ● Network Failure Handling Tests › Circuit breaker pattern implementation › should open circuit after consecutive failures

    expect(jest.fn()).toHaveBeenCalledTimes(expected)

    Expected number of calls: 3
    Received number of calls: 5

      272 |
      273 |       expect(circuitOpen).toBe(true);
    > 274 |       expect(circuitBreakerCallback).toHaveBeenCalledTimes(3);
          |                                      ^
      275 |     });
      276 |
      277 |     it("should implement half-open state after circuit cooldown", async () => {

      at Object.<anonymous> (utils/robustLlmUtils/__tests__/networkFailures.test.ts:274:38)

  ● Network Failure Handling Tests › Edge cases and boundary conditions › should handle null/undefined network responses

    expect(received).toEqual(expected) // deep equality

    Expected: {"data": "valid response"}
    Received: null

      559 |       const result = await withRetry(nullResponseService, { retries: 3 });
      560 |
    > 561 |       expect(result).toEqual({ data: "valid response" });
          |                      ^
      562 |       expect(nullResponseService).toHaveBeenCalledTimes(3);
      563 |     });
      564 |

      at Object.<anonymous> (utils/robustLlmUtils/__tests__/networkFailures.test.ts:561:22)

  ● Network Failure Handling Tests › Edge cases and boundary conditions › should handle network errors with missing error details

    thrown: "Exceeded timeout of 60000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      590 |     });
      591 |
    > 592 |     it("should handle network errors with missing error details", async () => {
          |     ^
      593 |       const vagueErrorService = jest
      594 |         .fn()
      595 |         .mockRejectedValueOnce(new Error()) // Empty error message

      at utils/robustLlmUtils/__tests__/networkFailures.test.ts:592:5
      at utils/robustLlmUtils/__tests__/networkFailures.test.ts:551:3
      at Object.<anonymous> (utils/robustLlmUtils/__tests__/networkFailures.test.ts:46:1)

PASS tests/integration/frontend-backend/file-operations.test.ts
PASS tests/integration/noMainDocFlowModular.test.ts
PASS tests/integration/supportFunctions.integration.test.ts
FAIL utils/__tests__/cleanLogs.test.ts
  ● Log Cleanup Utilities Tests › Performance and memory testing › should handle memory pressure during large cleanups

    expect(jest.fn()).toHaveBeenCalledTimes(expected)

    Expected number of calls: 100
    Received number of calls: 200

      487 |       await cleanOldLogs();
      488 |
    > 489 |       expect(mockFsPromises.unlink).toHaveBeenCalledTimes(largePaths.length);
          |                                     ^
      490 |     });
      491 |
      492 |     it("should handle rapid successive cleanup calls", async () => {

      at Object.<anonymous> (utils/__tests__/cleanLogs.test.ts:489:37)

FAIL tests/e2e/journeys/document-processing-flow.test.ts
  ● Test suite failed to run

    Playwright Test needs to be invoked via 'npx playwright test' and excluded from Jest test runs.
    Creating one directory for Playwright tests and one for Jest is the recommended way of doing it.
    See https://playwright.dev/docs/intro for more information about Playwright Test.

      18 | import path from "path";
      19 |
    > 20 | test.describe("Document Processing Flow", () => {
         |      ^
      21 |   let loginPage: LoginPage;
      22 |   let dashboardPage: DashboardPage;
      23 |   let workspacePage: WorkspacePage;

      at throwIfRunningInsideJest (../node_modules/@playwright/test/node_modules/playwright/lib/common/testType.js:272:11)
      at TestTypeImpl._describe (../node_modules/@playwright/test/node_modules/playwright/lib/common/testType.js:113:5)
      at Function.describe (../node_modules/@playwright/test/node_modules/playwright/lib/transform/transform.js:275:12)
      at Object.<anonymous> (tests/e2e/journeys/document-processing-flow.test.ts:20:6)

PASS types/__tests__/type-safety.test.ts
PASS tests/integration/frontend-backend/authentication-session.test.ts
PASS tests/security/token-revocation.test.ts
FAIL tests/e2e/journeys/user-onboarding.test.ts
  ● Test suite failed to run

    Playwright Test needs to be invoked via 'npx playwright test' and excluded from Jest test runs.
    Creating one directory for Playwright tests and one for Jest is the recommended way of doing it.
    See https://playwright.dev/docs/intro for more information about Playwright Test.

      18 | import path from "path";
      19 |
    > 20 | test.describe("User Onboarding Journey", () => {
         |      ^
      21 |   let loginPage: LoginPage;
      22 |   let dashboardPage: DashboardPage;
      23 |   let workspacePage: WorkspacePage;

      at throwIfRunningInsideJest (../node_modules/@playwright/test/node_modules/playwright/lib/common/testType.js:272:11)
      at TestTypeImpl._describe (../node_modules/@playwright/test/node_modules/playwright/lib/common/testType.js:113:5)
      at Function.describe (../node_modules/@playwright/test/node_modules/playwright/lib/transform/transform.js:275:12)
      at Object.<anonymous> (tests/e2e/journeys/user-onboarding.test.ts:20:6)

PASS jobs/__tests__/bulk-document-processor.test.ts
PASS tests/api/system.cdbDocumentation.test.ts
PASS tests/streamCDB-integration.test.ts
