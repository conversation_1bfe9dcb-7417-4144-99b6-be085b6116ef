# Test Cleanup Policy

## Overview

The test cleanup system has been updated to be more restrictive and only clean test-specific directories. Tests that create files in production directories must handle their own cleanup.

## Key Principles

1. **Test Isolation**: Tests should use test-specific directories, not production directories
2. **Self-Cleanup**: Tests that create files must clean them up in `afterEach` or `afterAll`
3. **No Production Cleanup**: The automated cleanup system will NOT clean production directories

## Test-Specific Directories

The cleanup system will ONLY clean these directories:

- `.test-data/` - Primary test data directory
- `test-data/` - Alternative test data directory
- `/tmp/istlegal-test/` - Test-specific temp directory
- `/tmp/test-*/` - Any temp directory starting with "test-"

## Production Directories (NOT cleaned automatically)

Tests must handle their own cleanup in these directories:

- `storage/documents/` - Production document storage
- `storage/uploads/` - Production upload directory
- `storage/assets/` - System assets (logos, etc.)
- `storage/vector-cache/` - Production vector storage
- `storage/lancedb/` - Production database
- `storage/tmp/` - Production temp directory

## Using Test Directories

The TestCleanup helper provides methods to use test directories:

```typescript
import { testCleanup } from "../helpers/testCleanup";

describe("MyTest", () => {
  let testFile: string;

  beforeEach(() => {
    // Get a test-specific file path
    testFile = testCleanup.getTestFilePath("test-document.pdf", "documents");
  });

  afterEach(() => {
    // Clean up your own files
    if (fs.existsSync(testFile)) {
      fs.unlinkSync(testFile);
    }
  });

  it("should process document", () => {
    // Create file in test directory
    fs.writeFileSync(testFile, "test content");

    // Your test logic here
  });
});
```

## Best Practices

### ✅ DO

- Use `testCleanup.getTestDirectory()` for test files
- Clean up files in `afterEach()` or `afterAll()`
- Use unique names for test files (include timestamps or UUIDs)
- Check if files exist before attempting cleanup

### ❌ DON'T

- Create files in production directories without cleanup
- Rely on automatic cleanup for production files
- Delete entire directories (only delete your specific files)
- Leave test artifacts in production directories

## Example: Upload Test with Proper Cleanup

```typescript
describe("Upload API", () => {
  const uploadedFiles: string[] = [];

  afterEach(async () => {
    // Clean up all uploaded files
    for (const file of uploadedFiles) {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
      }
    }
    uploadedFiles.length = 0;
  });

  it("should upload file", async () => {
    const response = await request(app)
      .post("/api/upload")
      .attach("file", "test-file.pdf");

    // Track the uploaded file for cleanup
    uploadedFiles.push(response.body.filePath);

    expect(response.status).toBe(200);
  });
});
```

## Migration Guide

If your tests currently rely on automatic cleanup of production directories:

1. Identify all file creation in your tests
2. Add explicit cleanup in `afterEach` or `afterAll`
3. Consider using test-specific directories instead
4. Test your cleanup by running tests multiple times

## Notes

- The 10-minute rule has been removed - test cleanup is now directory-based
- Upload logs are still cleaned (test entries only)
- Test database cleanup remains mocked
- Use `npm test -- --detectOpenHandles` to find cleanup issues
