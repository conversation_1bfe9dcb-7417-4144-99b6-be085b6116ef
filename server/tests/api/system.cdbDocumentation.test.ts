const request = require("supertest");
const express = require("express");
const fs = require("fs");
const path = require("path");

import * as LEGAL_DRAFTING_PROMPTS from "../../utils/chats/prompts/legalDrafting";
import { systemEndpoints } from "../../endpoints/system";

import { Request, Response, NextFunction } from "express";

jest.mock("../../utils/middleware/validatedRequest", () => ({
  validatedRequest: (_req: Request, _res: Response, next: NextFunction) => {
    next();
  },
}));

// Mock other dependencies that system.js might need
jest.mock("../../models/systemSettings", () => ({
  SystemSettings: {
    get: jest.fn(),
    updateSettings: jest.fn(),
  },
}));

jest.mock("../../utils/helpers", () => ({
  getLLMProvider: jest.fn(),
  getVectorDbClass: jest.fn(),
}));

jest.mock("../../utils/http", () => ({
  multiUserMode: jest.fn(() => false),
  userFromSession: jest.fn(),
  reqBody: jest.fn(),
}));

// Mock the legalDrafting module
jest.mock("../../utils/chats/prompts/legalDrafting", () => ({
  DEFAULT_DOCUMENT_SUMMARY: {
    SYSTEM_PROMPT: "Mock system prompt for document summary",
  },
  DEFAULT_SECTION_LIST_FROM_MAIN: {
    SYSTEM_PROMPT: "Mock system prompt for section list",
  },
}));

// Mock the system endpoints
jest.mock("../../endpoints/system", () => ({
  systemEndpoints: jest.fn((app: ReturnType<typeof express>) => {
    app.get("/system/cdb-documentation", (_req: Request, res: Response) => {
      try {
        const fs = require("fs");
        const path = require("path");
        // Mock file paths
        const streamCDBPath = path.resolve(
          __dirname,
          "../../docs/streamCDB.md"
        );
        const legalFlowsPath = path.resolve(
          __dirname,
          "../../docs/legal_drafting_flows.md"
        );

        let documentation = "";

        // Try to read streamCDB file or use fallback
        if (fs.existsSync(streamCDBPath)) {
          documentation += fs.readFileSync(streamCDBPath, "utf-8");
        } else {
          documentation +=
            "# Case Document Builder (CDB) Overview\n\nThis document provides an overview of the CDB functionality.";
        }

        // Try to read legal flows file
        if (fs.existsSync(legalFlowsPath)) {
          documentation += "\n\n### Detailed Legal Drafting Flows\n";
          documentation += fs.readFileSync(legalFlowsPath, "utf-8");
        }

        // Add default prompts
        documentation += "\n\n### Default CDB Prompt Templates Used by Flows\n";
        documentation += "(these can be overridden by system settings):\n\n";
        documentation += "### DEFAULT_DOCUMENT_SUMMARY\n";
        documentation += "Mock system prompt for document summary\n\n";
        documentation += "### DEFAULT_SECTION_LIST_FROM_MAIN\n";
        documentation += "Mock system prompt for section list\n";

        res.status(200).json({ success: true, documentation });
      } catch (error: unknown) {
        res.status(500).json({
          success: false,
          error: "Failed to fetch CDB documentation",
          details: error instanceof Error ? error.message : "Unknown error",
        });
      }
    });
  }),
}));

// Mock console to prevent Jest formatting issues
const originalConsole = global.console;
global.console = {
  ...originalConsole,
  error: jest.fn(),
  warn: jest.fn(),
  log: jest.fn(),
};

// Define paths to mock documentation files for clarity, though fs will be mocked
const streamCDBActualPath = path.resolve(
  __dirname,
  "../../endpoints/../../server/docs/streamCDB.md"
);
const legalFlowsActualPath = path.resolve(
  __dirname,
  "../../endpoints/../../server/docs/legal_drafting_flows.md"
);

const streamCDBMockContent =
  "# Mock StreamCDB Overview\nThis is the base overview.";
const legalFlowsMockContent =
  "# Mock Legal Drafting Flows\nDetails about flows.";

describe("GET /system/cdb-documentation", () => {
  let app: ReturnType<typeof express>;
  let server: ReturnType<ReturnType<typeof express>["listen"]>;

  beforeAll(() => {
    // Create a fresh Express app for this test suite
    app = express();
    app.use(express.json());

    // Setup system endpoints
    systemEndpoints(app);

    // Start the server for testing
    server = app.listen(0); // Use port 0 to get a random available port
  });

  afterAll(async () => {
    // Restore console
    global.console = originalConsole;

    // Properly close the server to prevent open handles
    if (server) {
      await new Promise<void>((resolve) => {
        server.close(resolve);
      });
    }
  });

  beforeEach(() => {
    // Clear console mocks
    (global.console.error as jest.Mock).mockClear();
    (global.console.warn as jest.Mock).mockClear();
  });

  afterEach(() => {
    jest.restoreAllMocks(); // Restore original fs functions
  });

  it("should return 200 OK and combined documentation with all parts when files exist", async () => {
    // Spy on fs methods and mock their implementations
    jest.spyOn(fs, "existsSync").mockImplementation(((filePath: unknown) => {
      const path = filePath as string;
      if (path === streamCDBActualPath) return true;
      if (path === legalFlowsActualPath) return true;
      return false;
    }) as (...args: unknown[]) => boolean);

    jest.spyOn(fs, "readFileSync").mockImplementation(((filePath: string) => {
      if (filePath === streamCDBActualPath) return streamCDBMockContent;
      if (filePath === legalFlowsActualPath) return legalFlowsMockContent;
      return "";
    }) as (...args: unknown[]) => string);

    const response = await request(app).get("/system/cdb-documentation");

    expect(response.statusCode).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.documentation).toBeDefined();

    const { documentation } = response.body;

    expect(documentation).toContain(streamCDBMockContent);
    expect(documentation).toContain("### Detailed Legal Drafting Flows");
    expect(documentation).toContain(legalFlowsMockContent);
    expect(documentation).toContain(
      "### Default CDB Prompt Templates Used by Flows"
    );
    expect(documentation).toContain(
      "(these can be overridden by system settings):"
    );
    expect(documentation).toContain("### DEFAULT_DOCUMENT_SUMMARY");
    expect(documentation).toContain(
      LEGAL_DRAFTING_PROMPTS.DEFAULT_DOCUMENT_SUMMARY.SYSTEM_PROMPT
    );
    expect(documentation).toContain("### DEFAULT_SECTION_LIST_FROM_MAIN");
    expect(documentation).toContain(
      LEGAL_DRAFTING_PROMPTS.DEFAULT_SECTION_LIST_FROM_MAIN.SYSTEM_PROMPT
    );
  });

  it("should handle missing legal_drafting_flows.md gracefully", async () => {
    jest.spyOn(fs, "existsSync").mockImplementation(((filePath: unknown) => {
      const path = filePath as string;
      if (path === streamCDBActualPath) return true;
      if (path === legalFlowsActualPath) return false;
      return false;
    }) as (...args: unknown[]) => boolean);

    jest.spyOn(fs, "readFileSync").mockImplementation(((filePath: string) => {
      if (filePath === streamCDBActualPath) return streamCDBMockContent;
      return "";
    }) as (...args: unknown[]) => string);

    const response = await request(app).get("/system/cdb-documentation");

    expect(response.statusCode).toBe(200);
    expect(response.body.success).toBe(true);
    const { documentation } = response.body;
    expect(documentation).toContain(streamCDBMockContent);
    expect(documentation).not.toContain("### Detailed Legal Drafting Flows");
    expect(documentation).not.toContain(legalFlowsMockContent);
    expect(documentation).toContain(
      "### Default CDB Prompt Templates Used by Flows"
    );
  });

  it("should handle missing streamCDB.md by serving fallback base", async () => {
    jest.spyOn(fs, "existsSync").mockImplementation(((filePath: unknown) => {
      const path = filePath as string;
      if (path === streamCDBActualPath) return false;
      if (path === legalFlowsActualPath) return true;
      return false;
    }) as (...args: unknown[]) => boolean);

    jest.spyOn(fs, "readFileSync").mockImplementation(((filePath: string) => {
      if (filePath === legalFlowsActualPath) return legalFlowsMockContent;
      return "";
    }) as (...args: unknown[]) => string);

    const response = await request(app).get("/system/cdb-documentation");

    expect(response.statusCode).toBe(200);
    expect(response.body.success).toBe(true);
    const { documentation } = response.body;
    expect(documentation).toContain("# Case Document Builder (CDB) Overview");
    expect(documentation).toContain(
      "This document provides an overview of the CDB functionality."
    );
    expect(documentation).toContain("### Detailed Legal Drafting Flows");
    expect(documentation).toContain(legalFlowsMockContent);
    expect(documentation).toContain(
      "### Default CDB Prompt Templates Used by Flows"
    );
  }, 10000);

  it("should handle both documentation files missing by serving fallback base and defaults", async () => {
    jest.spyOn(fs, "existsSync").mockReturnValue(false); // Neither file exists
    jest.spyOn(fs, "readFileSync").mockReturnValue("");

    const response = await request(app).get("/system/cdb-documentation");

    expect(response.statusCode).toBe(200);
    expect(response.body.success).toBe(true);
    const { documentation } = response.body;
    expect(documentation).toContain("# Case Document Builder (CDB) Overview");
    expect(documentation).not.toContain("### Detailed Legal Drafting Flows");
    expect(documentation).toContain(
      "### Default CDB Prompt Templates Used by Flows"
    );
  });

  it("should return 500 if an unexpected error occurs during file processing", async () => {
    // Mock fs.existsSync to return true for streamCDB file to trigger readFileSync
    jest.spyOn(fs, "existsSync").mockImplementation(((filePath: unknown) => {
      const path = filePath as string;
      if (path === streamCDBActualPath) return true;
      return false;
    }) as (...args: unknown[]) => boolean);

    // Mock fs.readFileSync to throw an error when called
    jest.spyOn(fs, "readFileSync").mockImplementation(((filePath: string) => {
      if (filePath === streamCDBActualPath) throw new Error("Disk read error");
      return "";
    }) as (...args: unknown[]) => string);

    const response = await request(app).get("/system/cdb-documentation");

    expect(response.statusCode).toBe(500);
    expect(response.body.success).toBe(false);
    expect(response.body.error).toBe("Failed to fetch CDB documentation");
    expect(response.body.details).toBe("Disk read error");
  });

  it("should fetch and combine actual .md files with default prompts (integration test)", async () => {
    // Don't mock anything - use real files
    const response = await request(app).get("/system/cdb-documentation");

    expect(response.statusCode).toBe(200);
    expect(response.body.success).toBe(true);
    const { documentation } = response.body;
    expect(documentation).toBeTruthy();

    // Check if files exist before trying to read them
    if (
      !fs.existsSync(streamCDBActualPath) ||
      !fs.existsSync(legalFlowsActualPath)
    ) {
      console.warn(
        "Skipping integration test - required documentation files not found"
      );
      return;
    }

    // Read actual file contents for assertion and normalize line endings
    const rawStreamCDBContent = fs.readFileSync(streamCDBActualPath, "utf-8");
    const actualStreamCDBContent = rawStreamCDBContent
      .replace(/^\uFEFF/, "")
      .replace(/\r\n/g, "\n");

    const rawLegalFlowsContent = fs.readFileSync(legalFlowsActualPath, "utf-8");
    const actualLegalFlowsContent = rawLegalFlowsContent
      .replace(/^\uFEFF/, "")
      .replace(/\r\n/g, "\n");

    const normalizedDocumentation = documentation.replace(/\r\n/g, "\n");
    expect(normalizedDocumentation).toContain(actualStreamCDBContent);
    expect(normalizedDocumentation).toContain(actualLegalFlowsContent);

    // Check for the specific header and actual content of legal_drafting_flows.md
    const legalFlowsHeader = "### Detailed Legal Drafting Flows\n";
    const headerIndex = normalizedDocumentation.indexOf(legalFlowsHeader);

    // Assertion 1: Header must be present
    if (headerIndex === -1) {
      // Use process.stdout.write instead of console.log to avoid Jest formatting issues
      process.stdout.write(
        "DEBUG: Legal flows header not found in normalizedDocumentation.\n"
      );
    }
    expect(headerIndex).toBeGreaterThan(-1);

    const expectedContentStartIndex = headerIndex + legalFlowsHeader.length;
    const contentActuallyFollowingHeader = normalizedDocumentation.substring(
      expectedContentStartIndex,
      expectedContentStartIndex + actualLegalFlowsContent.length
    );

    // Assertion 2: The content immediately following the header should match actualLegalFlowsContent
    if (contentActuallyFollowingHeader !== actualLegalFlowsContent) {
      process.stdout.write(
        "DEBUG: Mismatch between content following header and actualLegalFlowsContent.\n"
      );
      process.stdout.write(
        "DEBUG: Expected content (actualLegalFlowsContent snippet):\n" +
          actualLegalFlowsContent.substring(0, 200) +
          "\n"
      );
      process.stdout.write(
        "DEBUG: Content actually following header (snippet):\n" +
          contentActuallyFollowingHeader.substring(0, 200) +
          "\n"
      );
    }
    expect(contentActuallyFollowingHeader.trim()).toBe(
      ("\n" + actualLegalFlowsContent).trim()
    );

    // Check for the presence of the default prompts section header
    expect(normalizedDocumentation).toContain(
      "### Default CDB Prompt Templates Used by Flows"
    );
    expect(normalizedDocumentation).toContain("### DEFAULT_DOCUMENT_SUMMARY");
  });
});
