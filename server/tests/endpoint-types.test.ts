// Mock bcryptjs
jest.mock("bcryptjs", () => ({
  compare: jest.fn(() => Promise.resolve(true)), // Async version
  compareSync: jest.fn(() => true), // Sync version
  hashSync: jest.fn(() => "$2a$10$mockHashedPasswordForTesting"),
}));

// Mock models before importing app
jest.mock("../models/systemSettings", () => ({
  __esModule: true,
  default: {
    where: jest.fn(() => Promise.resolve([])),
    get: jest.fn(),
    updateSettings: jest.fn(),
    isMultiUserMode: jest.fn(() => false),
    currentSettings: jest.fn(() => Promise.resolve({})),
  },
}));

jest.mock("../models/user", () => ({
  User: {
    count: jest.fn(() => Promise.resolve(0)),
    _get: jest.fn(),
    get: jest.fn(),
    where: jest.fn(() => Promise.resolve({ users: [] })),
  },
}));

jest.mock("../models/userToken", () => ({
  UserToken: {
    create: jest.fn(() => Promise.resolve()),
    findByUserId: jest.fn(() => Promise.resolve([])),
    deleteByTokenId: jest.fn(() => Promise.resolve()),
  },
}));

import request from "supertest";
import app from "../index";
import { makeJWT } from "../utils/http";
import { TokenResponse, JWTToken, UUIDToken } from "../types/auth";

import { FilteredUser } from "../types/models";
import { TestWorkspace } from "../types/test-utils";

// Type guard functions to validate response shapes
function isTokenResponse(obj: unknown): obj is TokenResponse {
  return (
    obj !== null &&
    typeof obj === "object" &&
    "token" in obj &&
    "user" in obj &&
    typeof (obj as any).token === "string" &&
    (obj as any).user &&
    typeof (obj as any).user.id === "number" &&
    typeof (obj as any).user.username === "string" &&
    typeof (obj as any).user.role === "string"
  );
}

function isFilteredUser(obj: unknown): obj is FilteredUser {
  return (
    obj !== null &&
    typeof obj === "object" &&
    "id" in obj &&
    "username" in obj &&
    "role" in obj &&
    typeof (obj as any).id === "number" &&
    typeof (obj as any).username === "string" &&
    typeof (obj as any).role === "string"
  );
}

describe("Frontend-Backend Endpoint Type Validation", () => {
  let authToken: JWTToken;
  let testUser: FilteredUser;
  let originalNodeEnv: string | undefined;
  let databaseAvailable = true;

  beforeAll(async () => {
    // Store original NODE_ENV
    originalNodeEnv = process.env.NODE_ENV;

    // Set up test environment
    process.env.NODE_ENV = "test";
    process.env.JWT_SECRET = "test-jwt-secret-for-testing-only";

    // Use predefined test user from global setup
    testUser = {
      id: 7,
      username: "test_endpoint_user",
      role: "admin",
      createdAt: new Date(),
      lastUpdatedAt: new Date(),
      organizationId: null,
      pfpFilename: null,
      suspended: 0,
      custom_ai_userselected: false,
      custom_ai_selected_engine: "_CUAI",
      custom_system_prompt: null,
      economy_system_id: null,
    } as FilteredUser;

    // Create JWT token that will be recognized by our auth mocks
    authToken = makeJWT({ id: testUser.id, jti: "test-endpoint-jti" }, "1h");
    databaseAvailable = true;

    // Set up User mock to return a user with hashed password
    const hashedPassword = "$2a$10$mockHashedPasswordForTesting"; // Pre-hashed password
    const { User } = require("../models/user");
    User._get.mockResolvedValue({
      id: testUser.id,
      username: testUser.username,
      password: hashedPassword,
      role: testUser.role,
    });
  });

  afterAll(async () => {
    // Restore original NODE_ENV
    if (originalNodeEnv !== undefined) {
      process.env.NODE_ENV = originalNodeEnv;
    } else {
      delete process.env.NODE_ENV;
    }
  });

  describe("Authentication Endpoints", () => {
    test("POST /api/request-token - should return properly typed TokenResponse", async () => {
      const response = await request(app).post("/api/request-token").send({
        username: "test_endpoint_user",
        password: "Test123!@#",
      });

      // Handle CI/CD environment where authentication may fail
      if (response.status === 401 && !databaseAvailable) {
        // Skip test in CI/CD environment where database is not available
        return;
      }

      expect(response.status).toBe(200);

      // Type validation
      expect(isTokenResponse(response.body)).toBe(true);

      // Additional checks
      const tokenResponse: TokenResponse = response.body;
      expect(tokenResponse.token).toBeDefined();
      expect(tokenResponse.token.split(".").length).toBe(3); // JWT format
      expect(tokenResponse.user.id).toBe(testUser.id);
      expect(tokenResponse.user.username).toBe("test_endpoint_user");
      expect(tokenResponse.user.role).toBe("admin");
    });

    test("GET /api/system/check-token - should validate JWT and return user", async () => {
      const response = await request(app)
        .get("/api/system/check-token")
        .set("Authorization", `Bearer ${authToken}`);

      // Handle CI/CD environment where authentication may fail
      if (response.status === 401 && !databaseAvailable) {
        // Skip test in CI/CD environment where database is not available
        return;
      }

      expect(response.status).toBe(200);

      expect(response.body).toHaveProperty("valid", true);
      expect(response.body).toHaveProperty("user");
      expect(isFilteredUser(response.body.user)).toBe(true);
    });

    test("GET /api/system/check-token - should reject invalid token", async () => {
      const response = await request(app)
        .get("/api/system/check-token")
        .set("Authorization", "Bearer invalid_token")
        .expect(401);

      expect(response.body).toHaveProperty("error");
      expect(typeof response.body.error).toBe("string");
    });
  });

  describe("System Endpoints", () => {
    test("GET /api/ping - should return pong", async () => {
      const response = await request(app).get("/api/ping").expect(200);

      expect(response.body).toEqual({ message: "pong" });
    });

    test("GET /api/setup-complete - should return setup status", async () => {
      const response = await request(app)
        .get("/api/setup-complete")
        .expect(200);

      expect(response.body).toHaveProperty("results");
      const { results } = response.body;
      expect(typeof results.setupComplete).toBe("boolean");
      expect(typeof results.MultiUserMode).toBe("boolean");
      expect(typeof results.language).toBe("string");
      expect(["string", "object"]).toContain(typeof results.palette);
    });

    test("GET /api/env-dump - should return environment configuration", async () => {
      const response = await request(app).get("/api/env-dump").expect(200);

      expect(response.body).toBeDefined();
      expect(typeof response.body).toBe("object");
    });
  });

  describe("Workspace Endpoints", () => {
    test("GET /api/workspaces - should require authentication", async () => {
      await request(app).get("/api/workspaces").expect(401);
    });

    test("GET /api/workspaces - should return workspace list with auth", async () => {
      const response = await request(app)
        .get("/api/workspaces")
        .set("Authorization", `Bearer ${authToken}`);

      // Handle CI/CD environment where authentication may fail
      if (response.status === 401 && !databaseAvailable) {
        // Skip test in CI/CD environment where database is not available
        return;
      }

      expect(response.status).toBe(200);

      expect(response.body).toHaveProperty("workspaces");
      expect(Array.isArray(response.body.workspaces)).toBe(true);
    });

    test("POST /api/workspace/new - should create workspace with proper types", async () => {
      const workspaceName = `test_workspace_${Date.now()}`;
      const response = await request(app)
        .post("/api/workspace/new")
        .set("Authorization", `Bearer ${authToken}`)
        .send({ name: workspaceName });

      // Handle CI/CD environment where authentication may fail
      if (response.status === 401 && !databaseAvailable) {
        // Skip test in CI/CD environment where database is not available
        return;
      }

      expect(response.status).toBe(200);

      expect(response.body).toHaveProperty("workspace");
      expect(response.body).toHaveProperty("message");

      const { workspace } = response.body;
      expect(typeof workspace.id).toBe("number");
      expect(workspace.name).toBe(workspaceName);
      expect(typeof workspace.slug).toBe("string");

      // Clean up - no action needed for mocked workspace
    });
  });

  describe("Document Endpoints", () => {
    test("GET /api/documents - should return document list", async () => {
      const response = await request(app)
        .get("/api/documents")
        .set("Authorization", `Bearer ${authToken}`);

      // Handle CI/CD environment where authentication may fail
      if (response.status === 401 && !databaseAvailable) {
        // Skip test in CI/CD environment where database is not available
        return;
      }

      expect(response.status).toBe(200);

      expect(response.body).toHaveProperty("documentsLoaded");
      expect(Array.isArray(response.body.documentsLoaded)).toBe(true);
    });

    test("GET /api/system/vector-count - should return vector count", async () => {
      const response = await request(app)
        .get("/api/system/vector-count")
        .set("Authorization", `Bearer ${authToken}`);

      // Handle CI/CD environment where authentication may fail
      if (response.status === 401 && !databaseAvailable) {
        // Skip test in CI/CD environment where database is not available
        return;
      }

      expect(response.status).toBe(200);

      expect(response.body).toHaveProperty("vectorCount");
      expect(typeof response.body.vectorCount).toBe("number");
    });
  });

  describe("Chat Endpoints", () => {
    let testWorkspace: TestWorkspace;

    beforeAll(async () => {
      // Create a test workspace for chat tests
      testWorkspace = {
        id: 1,
        name: `test_chat_workspace_${Date.now()}`,
        slug: `test-chat-${Date.now()}`,
        user_id: testUser.id,
        vectorTag: null,
        createdAt: new Date(),
        openAiTemp: null,
        openAiHistory: 20,
        lastUpdatedAt: new Date(),
        openAiPrompt: null,
        similarityThreshold: 0.25,
        chatProvider: null,
        chatModel: null,
        embeddingProvider: null,
        embeddingModel: null,
        topN: 4,
        type: "",
        chatMode: "chat",
        chatType: "private",
        pfpFilename: null,
        agentProvider: null,
        agentModel: null,
        queryRefusalResponse: null,
        vectorSearchMode: null,
        pdr: false,
        hasMessages: false,
        order: 0,
        sharedWithOrg: false,
      };
    });

    afterAll(async () => {
      // No cleanup needed for mock workspace
    });

    test("POST /api/workspace/chat - should validate chat request types", async () => {
      const response = await request(app)
        .post(`/api/workspace/${testWorkspace.slug}/chat`)
        .set("Authorization", `Bearer ${authToken}`)
        .send({
          message: "Test message",
          mode: "chat",
        });

      // Handle CI/CD environment where authentication may fail
      if (response.status === 401 && !databaseAvailable) {
        // Skip test in CI/CD environment where database is not available
        return;
      }

      expect(response.status).toBe(200);

      // Response should be SSE stream
      expect(response.headers["content-type"]).toContain("text/event-stream");
    });

    test("GET /api/workspace/chats - should return chat history", async () => {
      const response = await request(app)
        .get(`/api/workspace/${testWorkspace.slug}/chats`)
        .set("Authorization", `Bearer ${authToken}`);

      // Handle CI/CD environment where authentication may fail
      if (response.status === 401 && !databaseAvailable) {
        // Skip test in CI/CD environment where database is not available
        return;
      }

      expect(response.status).toBe(200);

      expect(response.body).toHaveProperty("history");
      expect(Array.isArray(response.body.history)).toBe(true);
    });
  });

  describe("User Management Endpoints", () => {
    test("GET /api/users - should return user list (admin only)", async () => {
      const response = await request(app)
        .get("/api/admin/users")
        .set("Authorization", `Bearer ${authToken}`);

      // Handle CI/CD environment where authentication may fail
      if (response.status === 401 && !databaseAvailable) {
        // Skip test in CI/CD environment where database is not available
        return;
      }

      expect(response.status).toBe(200);

      expect(response.body).toHaveProperty("users");
      expect(Array.isArray(response.body.users)).toBe(true);

      if (response.body.users.length > 0) {
        const user = response.body.users[0];
        expect(isFilteredUser(user)).toBe(true);
      }
    });

    test("GET /api/user - should return current user info", async () => {
      const response = await request(app)
        .get("/api/user")
        .set("Authorization", `Bearer ${authToken}`);

      // Handle CI/CD environment where authentication may fail
      if (response.status === 401 && !databaseAvailable) {
        // Skip test in CI/CD environment where database is not available
        return;
      }

      expect(response.status).toBe(200);

      expect(isFilteredUser(response.body)).toBe(true);
      expect(response.body.id).toBe(testUser.id);
    });

    test("POST /api/user/update - should update user with proper validation", async () => {
      const response = await request(app)
        .post("/api/user/update")
        .set("Authorization", `Bearer ${authToken}`)
        .send({
          username: "test_endpoint_user", // Keep same username
        });

      // Handle CI/CD environment where authentication may fail
      if (response.status === 401 && !databaseAvailable) {
        // Skip test in CI/CD environment where database is not available
        return;
      }

      expect(response.status).toBe(200);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body).toHaveProperty("message");
    });
  });

  describe("Type Safety Validation", () => {
    test("JWT token should decode to proper type", () => {
      const decoded = makeJWT({ id: testUser.id }, "1h");
      expect(typeof decoded).toBe("string");

      // Verify JWT structure
      const parts = decoded.split(".");
      expect(parts.length).toBe(3);
    });

    test("UUID vs JWT token type distinction", () => {
      const uuid: UUIDToken = "550e8400-e29b-41d4-a716-************";
      const jwt: JWTToken = authToken;

      // Type checks (these would fail at compile time if types were wrong)
      expect(uuid.length).toBe(36); // UUID format
      expect(jwt.split(".").length).toBe(3); // JWT format
    });
  });

  describe("Error Response Types", () => {
    test("All error responses should have consistent structure", async () => {
      const endpoints = [
        { method: "get", path: "/api/workspaces", expectedStatus: 401 },
        { method: "post", path: "/api/workspace/new", expectedStatus: 401 },
        { method: "get", path: "/api/documents", expectedStatus: 401 },
        { method: "get", path: "/api/users", expectedStatus: 401 },
      ];

      for (const endpoint of endpoints) {
        const response = await request(app)
          [endpoint.method as "get" | "post"](endpoint.path)
          .expect(endpoint.expectedStatus);

        expect(response.body).toHaveProperty("error");
        expect(typeof response.body.error).toBe("string");
      }
    });
  });

  describe("Admin Endpoints", () => {
    test("GET /api/admin/users - should validate admin response types", async () => {
      const response = await request(app)
        .get("/api/admin/users")
        .set("Authorization", `Bearer ${authToken}`);

      // Handle CI/CD environment where authentication may fail
      if (response.status === 401 && !databaseAvailable) {
        // Skip test in CI/CD environment where database is not available
        return;
      }

      expect(response.status).toBe(200);

      expect(response.body).toHaveProperty("users");
      expect(Array.isArray(response.body.users)).toBe(true);

      if (response.body.users.length > 0) {
        const user = response.body.users[0];
        expect(typeof user.id).toBe("number");
        expect(typeof user.username).toBe("string");
        expect(typeof user.role).toBe("string");
      }
    });

    test("GET /api/admin/system-settings - should return system settings", async () => {
      const response = await request(app)
        .get("/api/admin/system-settings")
        .set("Authorization", `Bearer ${authToken}`);

      // Handle CI/CD environment where authentication may fail
      if (response.status === 401 && !databaseAvailable) {
        // Skip test in CI/CD environment where database is not available
        return;
      }

      expect(response.status).toBe(200);

      expect(response.body).toHaveProperty("settings");
      expect(typeof response.body.settings).toBe("object");
    });
  });

  describe("Legal Templates Endpoints", () => {
    test("GET /api/custom-legal-templates - should return template list", async () => {
      const response = await request(app)
        .get("/api/custom-legal-templates")
        .set("Authorization", `Bearer ${authToken}`);

      // Handle CI/CD environment where authentication may fail
      if (response.status === 401 && !databaseAvailable) {
        // Skip test in CI/CD environment where database is not available
        return;
      }

      // Accept both 200 and 403 as valid responses
      expect([200, 403]).toContain(response.status);

      if (response.status === 200) {
        // Response handled silently
        expect(response.body).toHaveProperty("templates");
        expect(Array.isArray(response.body.templates)).toBe(true);
      }
    });
  });

  describe("MCP Servers Endpoints", () => {
    test("GET /api/mcp-servers/status - should return MCP status", async () => {
      const response = await request(app)
        .get("/api/mcp-servers/status")
        .set("Authorization", `Bearer ${authToken}`);

      // Handle CI/CD environment where authentication may fail
      if (response.status === 401 && !databaseAvailable) {
        // Skip test in CI/CD environment where database is not available
        return;
      }

      expect(response.status).toBe(200);

      expect(response.body).toHaveProperty("mcpServers");
      expect(typeof response.body.mcpServers).toBe("object");
    });
  });
});
