import { GeminiLLM } from "../utils/AiProviders/gemini";
import { ChatMessage, LLMStreamResponse } from "../types/ai-providers";
import { Readable } from "stream";

// Import the GeminiChat interface from the implementation
interface GeminiChat {
  sendMessage: (params: { message: string }) => Promise<{ text: string }>;
  sendMessageStream: (params: {
    message: string;
  }) => Promise<AsyncIterable<{ text: () => string }>>;
}

describe("GeminiLLM Integration Tests", () => {
  beforeAll(() => {
    // Ensure a dummy API key is present for instantiation
    process.env.GEMINI_API_KEY = "test-key";
  });

  describe("getChatCompletion (non-stream)", () => {
    let gemini: GeminiLLM;

    beforeEach(() => {
      gemini = new GeminiLLM();
      // Mock the chat client to return a fixed response
      (gemini as any).ai.chats.create = jest.fn(
        (): GeminiChat => ({
          sendMessage: jest.fn((_params: { message: string }) =>
            Promise.resolve({ text: "hello world" })
          ),
          sendMessageStream: jest.fn((_params: { message: string }) =>
            Promise.resolve({
              [Symbol.asyncIterator]: async function* () {
                yield { text: () => "hello world" };
              },
            })
          ),
        })
      );
    });

    test("returns textResponse and metrics", async () => {
      const messages: ChatMessage[] = [{ role: "user", content: "hi" }];
      const response = await gemini.getChatCompletion(messages);
      expect(response).not.toBeNull();
      expect(response!.textResponse).toBe("hello world");
      expect(response!.metrics).toHaveProperty("prompt_tokens");
      expect(response!.metrics).toHaveProperty("completion_tokens");
      expect(response!.metrics).toHaveProperty("total_tokens");
      expect(response!.metrics).toHaveProperty("duration");
    });
  });

  describe("streamGetChatCompletion (streaming)", () => {
    let gemini: GeminiLLM;
    let fakeStream: Readable;

    beforeEach(() => {
      gemini = new GeminiLLM();
      // Build a fake async iterable stream
      fakeStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { text: () => "streamed-" };
          yield { text: () => "data" };
        },
        start: Date.now(),
        endMeasurement: jest.fn(),
        metrics: {},
      } as any;
      (gemini as any).ai.chats.create = jest.fn(
        (): GeminiChat => ({
          sendMessage: jest.fn((_params: { message: string }) =>
            Promise.resolve({ text: "fallback" })
          ),
          sendMessageStream: jest.fn((_params: { message: string }) =>
            Promise.resolve(fakeStream as AsyncIterable<{ text: () => string }>)
          ),
        })
      );
    });

    test("yields chunks and stream metadata", async () => {
      const messages: ChatMessage[] = [{ role: "user", content: "ping" }];
      const response: LLMStreamResponse =
        await gemini.streamGetChatCompletion(messages);
      let combined = "";
      for await (const chunk of response.stream) {
        combined += (chunk as any).text();
      }
      expect(combined).toBe("streamed-data");
      // The monitored stream should have start time and endMeasurement
      expect(typeof (response.stream as any).start).toBe("number");
      expect(typeof response.endMeasurement).toBe("function");
    });
  });

  describe("streamGetChatCompletion (fallback)", () => {
    let gemini: GeminiLLM;

    beforeEach(() => {
      gemini = new GeminiLLM();
      // Force streaming to throw, so fallback path is hit
      (gemini as any).ai.chats.create = jest.fn(
        (): GeminiChat => ({
          sendMessageStream: jest.fn((_params: { message: string }) => {
            throw new Error("stream error");
          }),
          sendMessage: jest.fn((_params: { message: string }) =>
            Promise.resolve({ text: "fallback-response" })
          ),
        })
      );
    });

    test("fallback yields single chunk with full text", async () => {
      const messages: ChatMessage[] = [{ role: "user", content: "test" }];
      const response: LLMStreamResponse =
        await gemini.streamGetChatCompletion(messages);
      let output = "";
      for await (const chunk of response.stream) {
        output += (chunk as any).text();
      }
      expect(output).toBe("fallback-response");
    });
  });
});
