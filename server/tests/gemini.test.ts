// Integration tests for GeminiLLM
// Mock the GenAI SDK for both chat and embedder
jest.resetModules();
jest.mock("@google/genai", () => ({
  GoogleGenAI: class {
    chats = {
      create: () => {
        return {
          sendMessage: ({ message }: { message: string }) =>
            Promise.resolve({ text: message + "-reply" }),
          sendMessageStream: ({ message }: { message: string }) =>
            Promise.resolve({
              [Symbol.asyncIterator]: async function* () {
                // First chunk: first character, second: rest of string
                yield { text: () => message.slice(0, 1) };
                yield { text: () => message.slice(1) };
              },
            }),
        };
      },
    };
    constructor(_params: { apiKey: string }) {}
  },
}));

import { GeminiLLM } from "../utils/AiProviders/gemini/index";
import type { ChatMessage } from "../types/ai-providers";

describe("GeminiLLM integration", () => {
  beforeAll(() => {
    // Provide a dummy API key for tests
    process.env.GEMINI_API_KEY = "test-key";
  });

  let llm: GeminiLLM;

  beforeEach(() => {
    // Instantiate without embedder override or model suffix
    llm = new GeminiLLM();
  });

  test("getChatCompletion returns expected textResponse and metrics", async () => {
    const messages: ChatMessage[] = [{ role: "user", content: "test" }];
    const result = await llm.getChatCompletion(messages);
    expect(result).not.toBeNull();
    if (result) {
      expect(result.textResponse).toBe("test-reply");
      expect(result.metrics).toBeDefined();
      if (result.metrics) {
        expect(result.metrics).toHaveProperty("prompt_tokens");
        expect(result.metrics).toHaveProperty("completion_tokens");
        expect(result.metrics.total_tokens).toBe(
          result.metrics.prompt_tokens + result.metrics.completion_tokens
        );
        expect(typeof result.metrics.duration).toBe("number");
      }
    }
  });

  test("streamGetChatCompletion returns async iterable chunks and initial metrics", async () => {
    const messages: ChatMessage[] = [{ role: "user", content: "hello" }];
    const response = await llm.streamGetChatCompletion(messages);
    const chunks: string[] = [];
    for await (const chunk of response.stream) {
      chunks.push((chunk as unknown as { text: () => string }).text());
    }
    expect(chunks).toEqual(["h", "ello"]);
    // Ensure stream has metrics if available
    const streamWithMetrics = response.stream as unknown as {
      metrics?: { prompt_tokens?: number; duration?: number };
    };
    if (streamWithMetrics.metrics) {
      expect(streamWithMetrics.metrics).toHaveProperty("prompt_tokens");
      expect(streamWithMetrics.metrics.prompt_tokens).toBeDefined();
      expect(typeof streamWithMetrics.metrics.duration).toBe("number");
    }
  });

  test("streamGetChatCompletion falls back on sendMessageStream error", async () => {
    // Override chat implementation to throw on streaming
    (
      llm as unknown as { ai: { chats: { create: () => unknown } } }
    ).ai.chats.create = () => ({
      sendMessageStream: () => Promise.reject(new Error("stream fail")),
      sendMessage: ({ message }: { message: string }) =>
        Promise.resolve({ text: message + "-fallback" }),
    });
    const messages: ChatMessage[] = [{ role: "user", content: "bye" }];
    const response = await llm.streamGetChatCompletion(messages);
    const chunks: string[] = [];
    for await (const chunk of response.stream) {
      chunks.push((chunk as unknown as { text: () => string }).text());
    }
    expect(chunks).toEqual(["bye-fallback"]);
  });
});
