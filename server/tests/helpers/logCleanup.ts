/**
 * Log Cleanup Utilities
 *
 * Specialized utilities for cleaning up test entries from log files
 * to prevent test pollution in upload logs, error logs, etc.
 */

import * as fs from "fs";
import * as path from "path";

export interface LogCleanupOptions {
  testWorkspacePatterns?: string[];
  testFilePatterns?: string[];
  testPathPatterns?: string[];
  maxAge?: number; // Remove entries older than this many milliseconds
  dryRun?: boolean;
}

export class LogCleaner {
  private defaultOptions: LogCleanupOptions = {
    testWorkspacePatterns: [
      "test",
      "Test",
      "TEST",
      "mock",
      "Mock",
      "cleanup",
      "Cleanup",
    ],
    testFilePatterns: [
      "test",
      "Test",
      "mock",
      "Mock",
      "cleanup",
      "temp",
      "tmp",
    ],
    testPathPatterns: [
      "/path/to/",
      "/tmp/",
      "/test/",
      "test-",
      "mock-",
      "cleanup-",
    ],
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    dryRun: false,
  };

  constructor(
    private basePath: string = path.join(__dirname, "../../storage")
  ) {}

  /**
   * Clean upload logs from test entries
   */
  public async cleanUploadLogs(options: LogCleanupOptions = {}): Promise<void> {
    const opts = { ...this.defaultOptions, ...options };

    const logFiles = [
      path.join(this.basePath, "uploadlogs/document_uploads.log"),
      path.join(this.basePath, "uploadlogs/bulk_uploads.log"),
      path.join(this.basePath, "uploadlogs/error_uploads.log"),
      path.join(this.basePath, "uploadlogs/workspace_uploads.log"),
    ];

    for (const logFile of logFiles) {
      await this.cleanLogFile(logFile, opts);
    }
  }

  /**
   * Clean a specific log file
   */
  public async cleanLogFile(
    filePath: string,
    options: LogCleanupOptions = {}
  ): Promise<void> {
    const opts = { ...this.defaultOptions, ...options };

    if (!fs.existsSync(filePath)) {
      return;
    }

    try {
      const content = fs.readFileSync(filePath, "utf8");
      const lines = content.split("\n");
      const cleanedLines: string[] = [];
      let removedCount = 0;

      for (const line of lines) {
        if (!line.trim()) {
          cleanedLines.push(line);
          continue;
        }

        if (this.shouldRemoveLine(line, opts)) {
          removedCount++;
          if (opts.dryRun) {
            console.log(`[DRY RUN] Would remove: ${line.substring(0, 100)}...`);
          }
        } else {
          cleanedLines.push(line);
        }
      }

      if (!opts.dryRun && removedCount > 0) {
        fs.writeFileSync(filePath, cleanedLines.join("\n"));
        console.log(
          `[LogCleaner] Cleaned ${removedCount} test entries from ${path.basename(filePath)}`
        );
      } else if (opts.dryRun) {
        console.log(
          `[LogCleaner] [DRY RUN] Would remove ${removedCount} entries from ${path.basename(filePath)}`
        );
      }
    } catch (error) {
      console.warn(`[LogCleaner] Failed to clean log file ${filePath}:`, error);
    }
  }

  /**
   * Determine if a log line should be removed
   */
  private shouldRemoveLine(line: string, options: LogCleanupOptions): boolean {
    try {
      const entry = JSON.parse(line);

      // Check workspace name patterns
      if (
        entry.workspaceName &&
        this.matchesPatterns(
          entry.workspaceName,
          options.testWorkspacePatterns || []
        )
      ) {
        return true;
      }

      // Check file name patterns
      if (
        entry.fileName &&
        this.matchesPatterns(entry.fileName, options.testFilePatterns || [])
      ) {
        return true;
      }

      // Check path patterns
      if (
        entry.path &&
        this.matchesPatterns(entry.path, options.testPathPatterns || [])
      ) {
        return true;
      }

      // Check age
      if (options.maxAge && entry.timestamp) {
        const entryTime = new Date(entry.timestamp).getTime();
        const now = Date.now();
        if (now - entryTime > options.maxAge) {
          return true;
        }
      }

      // Check for test-specific identifiers
      if (entry.testRun || entry.isTest || entry.cleanup) {
        return true;
      }

      return false;
    } catch {
      // If not valid JSON, keep the line unless it contains obvious test patterns
      return this.matchesPatterns(line, [
        "test-workspace",
        "Test Workspace",
        "cleanup-test",
        "mock-file",
        "/path/to/test",
      ]);
    }
  }

  /**
   * Check if a string matches any of the given patterns
   */
  private matchesPatterns(text: string, patterns: string[]): boolean {
    return patterns.some((pattern) => {
      if (pattern.includes("*")) {
        // Convert wildcard pattern to regex
        const regexPattern = pattern.replace(/\*/g, ".*").replace(/\?/g, ".");
        return new RegExp(regexPattern, "i").test(text);
      } else {
        return text.toLowerCase().includes(pattern.toLowerCase());
      }
    });
  }

  /**
   * Archive old log entries instead of deleting them
   */
  public async archiveOldEntries(
    filePath: string,
    archiveDir: string,
    maxAge: number
  ): Promise<void> {
    if (!fs.existsSync(filePath)) {
      return;
    }

    try {
      const content = fs.readFileSync(filePath, "utf8");
      const lines = content.split("\n");
      const currentLines: string[] = [];
      const archivedLines: string[] = [];

      for (const line of lines) {
        if (!line.trim()) {
          currentLines.push(line);
          continue;
        }

        try {
          const entry = JSON.parse(line);
          const entryTime = new Date(
            entry.timestamp || entry.dateAdded
          ).getTime();
          const now = Date.now();

          if (now - entryTime > maxAge) {
            archivedLines.push(line);
          } else {
            currentLines.push(line);
          }
        } catch {
          currentLines.push(line); // Keep non-JSON lines
        }
      }

      if (archivedLines.length > 0) {
        // Ensure archive directory exists
        if (!fs.existsSync(archiveDir)) {
          fs.mkdirSync(archiveDir, { recursive: true });
        }

        const archiveFile = path.join(
          archiveDir,
          `${path.basename(filePath, path.extname(filePath))}_${new Date().toISOString().split("T")[0]}${path.extname(filePath)}`
        );

        fs.writeFileSync(archiveFile, archivedLines.join("\n"));
        fs.writeFileSync(filePath, currentLines.join("\n"));

        console.log(
          `[LogCleaner] Archived ${archivedLines.length} old entries to ${archiveFile}`
        );
      }
    } catch (error) {
      console.warn(
        `[LogCleaner] Failed to archive log file ${filePath}:`,
        error
      );
    }
  }

  /**
   * Get statistics about a log file
   */
  public getLogStats(filePath: string): {
    total: number;
    testEntries: number;
    validJson: number;
  } {
    if (!fs.existsSync(filePath)) {
      return { total: 0, testEntries: 0, validJson: 0 };
    }

    try {
      const content = fs.readFileSync(filePath, "utf8");
      const lines = content.split("\n").filter((line) => line.trim());

      let testEntries = 0;
      let validJson = 0;

      for (const line of lines) {
        try {
          const _entry = JSON.parse(line);
          validJson++;

          if (this.shouldRemoveLine(line, this.defaultOptions)) {
            testEntries++;
          }
        } catch {
          // Not valid JSON
        }
      }

      return {
        total: lines.length,
        testEntries,
        validJson,
      };
    } catch (error) {
      console.warn(`[LogCleaner] Failed to get stats for ${filePath}:`, error);
      return { total: 0, testEntries: 0, validJson: 0 };
    }
  }

  /**
   * Clean all logs in the storage directory
   */
  public async cleanAllLogs(options: LogCleanupOptions = {}): Promise<void> {
    const logsDir = path.join(this.basePath, "uploadlogs");

    if (!fs.existsSync(logsDir)) {
      return;
    }

    const logFiles = fs
      .readdirSync(logsDir)
      .filter((file) => file.endsWith(".log"))
      .map((file) => path.join(logsDir, file));

    console.log(`[LogCleaner] Found ${logFiles.length} log files to clean`);

    for (const logFile of logFiles) {
      await this.cleanLogFile(logFile, options);
    }
  }
}

// Export utility functions
export const cleanUploadLogs = (options?: LogCleanupOptions) => {
  const cleaner = new LogCleaner();
  return cleaner.cleanUploadLogs(options);
};

export const cleanLogFile = (filePath: string, options?: LogCleanupOptions) => {
  const cleaner = new LogCleaner();
  return cleaner.cleanLogFile(filePath, options);
};

export const getLogStats = (filePath: string) => {
  const cleaner = new LogCleaner();
  return cleaner.getLogStats(filePath);
};

export const cleanAllLogs = (options?: LogCleanupOptions) => {
  const cleaner = new LogCleaner();
  return cleaner.cleanAllLogs(options);
};
