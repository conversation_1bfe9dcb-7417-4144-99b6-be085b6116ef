/**
 * Mock isolation utility for Jest tests
 *
 * This helper ensures proper isolation between test files by resetting
 * all mocks and modules after each test file completes.
 */

export function setupMockIsolation(): void {
  beforeAll(() => {
    // Clear all mocks before starting test suite
    jest.clearAllMocks();

    // Reset all modules to ensure clean state
    jest.resetModules();
  });

  afterAll(() => {
    // Clear all mocks after test suite completes
    jest.clearAllMocks();

    // Reset all modules to prevent pollution between test files
    jest.resetModules();

    // Clear any cached require calls
    jest.clearAllTimers();

    // Reset Jest's internal state
    jest.restoreAllMocks();
  });
}

/**
 * Enhanced cleanup for tests that import complex modules
 */
export function setupDeepMockIsolation(): void {
  setupMockIsolation();

  afterAll(() => {
    // Additional cleanup for tests that might have deep module dependencies
    if (typeof global.gc === "function") {
      global.gc();
    }

    // Clear any remaining process listeners that might cause hangs
    process.removeAllListeners("uncaughtException");
    process.removeAllListeners("unhandledRejection");
    process.removeAllListeners("SIGTERM");
    process.removeAllListeners("SIGINT");
  });
}
