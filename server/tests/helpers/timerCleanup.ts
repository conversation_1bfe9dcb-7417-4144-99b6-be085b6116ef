/* global NodeJS */
/**
 * Timer cleanup utility for Jest tests
 *
 * This helper prevents hanging tests by tracking and cleaning up all timers
 * created during test execution.
 */

/// <reference types="node" />
/* eslint-env node */

// NodeJS.Timeout and NodeJS.Immediate are globally available in Node.js types
// No import needed; just use them directly as types

// Add NodeJS type declarations if missing (for test environments)
declare global {
  namespace NodeJS {
    interface Timeout {}
    interface Immediate {}
  }
}

interface TimerCleanupManager {
  setup: () => void;
  cleanup: () => void;
}

export function createTimerCleanupManager(): TimerCleanupManager {
  // @ts-ignore
  const timerIds: NodeJS.Timeout[] = [];
  // @ts-ignore
  const immediateIds: NodeJS.Immediate[] = [];
  // @ts-ignore
  const intervalIds: NodeJS.Timeout[] = [];

  // Store original timer functions
  const originalSetTimeout = global.setTimeout;
  const originalSetImmediate = global.setImmediate;
  const originalSetInterval = global.setInterval;

  const setup = () => {
    // Override setTimeout to track timer IDs
    global.setTimeout = ((callback: () => void, delay?: number) => {
      const timerId = originalSetTimeout(callback, delay || 0);
      timerIds.push(timerId);
      return timerId;
    }) as typeof setTimeout;

    // Override setImmediate to track timer IDs
    global.setImmediate = ((callback: () => void) => {
      const timerId = originalSetImmediate(callback);
      immediateIds.push(timerId);
      return timerId;
    }) as typeof setImmediate;

    // Override setInterval to track timer IDs
    global.setInterval = ((callback: () => void, delay?: number) => {
      const timerId = originalSetInterval(callback, delay || 0);
      intervalIds.push(timerId);
      return timerId;
    }) as typeof setInterval;
  };

  const cleanup = () => {
    // Clear all tracked timers
    timerIds.forEach((timerId) => clearTimeout(timerId));
    immediateIds.forEach((timerId) => clearImmediate(timerId));
    intervalIds.forEach((timerId) => clearInterval(timerId));

    // Clear arrays
    timerIds.length = 0;
    immediateIds.length = 0;
    intervalIds.length = 0;

    // Restore original timer functions
    global.setTimeout = originalSetTimeout;
    global.setImmediate = originalSetImmediate;
    global.setInterval = originalSetInterval;

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
  };

  return { setup, cleanup };
}

/**
 * Jest hooks for automatic timer cleanup
 * Use in describe blocks to automatically handle timer cleanup
 */
export function useTimerCleanup(): void {
  const manager = createTimerCleanupManager();

  beforeAll(() => {
    manager.setup();
  });

  afterAll(() => {
    manager.cleanup();
  });
}
