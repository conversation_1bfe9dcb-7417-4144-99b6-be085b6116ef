/**
 * Phase 4.1: Backend ↔ Collector Integration Tests
 *
 * Comprehensive integration tests covering the complete document processing
 * pipeline between backend and collector services, including error propagation,
 * status synchronization, file transfer, and performance boundaries.
 */

import fs from "fs";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import { CollectorApi } from "../../utils/collectorApi";
import { BulkUploadProcessor } from "../../jobs/bulk-upload-processor";
import { bulkDocumentProcessor } from "../../jobs/bulk-document-processor";
import { documentsPath } from "../../utils/files";
import type { FilteredUser } from "../../types/models";
import { useTimerCleanup } from "../helpers/timerCleanup";
import { setupDeepMockIsolation } from "../helpers/mockIsolation";
// Remove the import of ProcessDocumentResponse if it does not exist
// import { ProcessDocumentResponse } from "../../types/collector";

// Mock external dependencies
jest.mock("../../utils/collectorApi");
jest.mock("../../models/workspace", () => ({
  Workspace: {
    get: jest.fn().mockImplementation(async ({ slug }) => {
      if (slug === "test-workspace") {
        return {
          id: 1,
          slug: "test-workspace",
          name: "Test Workspace",
        };
      }
      // Handle workspace-{index} pattern for concurrent tests
      if (slug && slug.startsWith("workspace-")) {
        const index = parseInt(slug.split("-")[1]);
        return {
          id: index + 2, // Start from id 2 to avoid conflicts
          slug: slug,
          name: `Test Workspace ${index}`,
        };
      }
      return null;
    }),
  },
}));
jest.mock("../../utils/prisma", () => ({
  workspace_documents: {
    findMany: jest.fn(),
    createMany: jest.fn(),
  },
  $transaction: jest.fn(),
}));
jest.mock("../../utils/helpers", () => ({
  getVectorDbClass: jest.fn(),
}));
jest.mock("../../utils/files", () => ({
  ...jest.requireActual("../../utils/files"),
  fileData: jest.fn(),
}));

jest.mock("../../utils/files/multer", () => ({
  ...jest.requireActual("../../utils/files/multer"),
  getUserDocumentPathName: jest.fn(
    (user, isDocumentDrafting, workspaceSlug) => {
      if (isDocumentDrafting) {
        return `document-drafting/user-${user?.id}_${workspaceSlug}`;
      }
      return workspaceSlug || "default-workspace";
    }
  ),
}));

jest.mock("../../utils/files/findActualFilePath", () => ({
  findActualFilePath: jest
    .fn()
    .mockImplementation(async (documentsPath, workspaceSlug, filename) => {
      // Simulate finding the file in the correct workspace
      return filename; // Return the filename as if it was found
    }),
}));

jest.mock("../../models/telemetry", () => ({
  Telemetry: {
    sendTelemetry: jest.fn().mockResolvedValue(undefined),
  },
}));

jest.mock("../../models/eventLogs", () => ({
  EventLogs: {
    logEvent: jest.fn().mockResolvedValue(undefined),
  },
}));

// Test constants
const TEST_TIMEOUT = 30000;
const COLLECTOR_HOTDIR_PATH = path.join(__dirname, "../../../collector/hotdir");
const TEST_FILES_DIR = path.join(__dirname, "./test-files");

// Test data setup
// Fix 1: Add required fields to mockUser
const mockUser: FilteredUser = {
  id: 1,
  username: "testuser",
  email: "<EMAIL>",
  role: "user",
  createdAt: new Date(),
  lastUpdatedAt: new Date(),
  suspended: 0,
  organizationId: 1,
  pfpFilename: null,
  seen_recovery_codes: false,
  custom_ai_userselected: false,
  custom_ai_selected_engine: "",
  custom_ai_option: "",
  custom_ai_temperature: 0,
  custom_ai_max_tokens: 0,
  custom_ai_prompt: "",
  economy_system_id: null,
  custom_system_prompt: null,
};

const mockWorkspace = {
  id: 1,
  slug: "test-workspace",
  name: "Test Workspace",
};

describe("Backend ↔ Collector Integration Tests", () => {
  // Setup automatic timer cleanup and deep mock isolation
  useTimerCleanup();
  setupDeepMockIsolation();

  let mockCollectorApi: jest.Mocked<CollectorApi>;
  let testFiles: string[] = [];

  beforeAll(async () => {
    // Ensure test directories exist
    await fs.promises.mkdir(TEST_FILES_DIR, { recursive: true });
    await fs.promises.mkdir(COLLECTOR_HOTDIR_PATH, { recursive: true });

    // Create test files
    await createTestFiles();
  });

  beforeEach(() => {
    jest.clearAllMocks();

    // Reset CollectorApi mock
    mockCollectorApi = new CollectorApi() as jest.Mocked<CollectorApi>;
    (CollectorApi as jest.MockedClass<typeof CollectorApi>).mockImplementation(
      () => mockCollectorApi
    );

    // Setup default mock responses
    mockCollectorApi.online.mockResolvedValue(true);
    mockCollectorApi.processDocument.mockResolvedValue({
      success: true,
      documents: [
        {
          id: uuidv4(),
          filename: "test.pdf",
          content: "Test content",
          metadata: { location: "/documents/test-workspace/test.pdf.json" },
        },
      ],
    });

    // Setup mocks for bulk document processor
    const mockPrisma = require("../../utils/prisma");
    const mockHelpers = require("../../utils/helpers");
    const mockFiles = require("../../utils/files");

    // Mock prisma operations
    mockPrisma.workspace_documents.findMany.mockResolvedValue([]);
    mockPrisma.workspace_documents.createMany.mockResolvedValue({ count: 1 });
    mockPrisma.$transaction.mockImplementation(async (callback: any) => {
      if (typeof callback === "function") {
        return callback(mockPrisma);
      }
      return { count: 1 };
    });

    // Mock vector database operations
    const mockVectorDb = {
      addDocumentToNamespace: jest.fn().mockResolvedValue({
        vectorized: true,
        error: null,
      }),
      deleteDocumentFromNamespace: jest.fn().mockResolvedValue(true),
    };
    mockHelpers.getVectorDbClass.mockReturnValue(mockVectorDb);

    // Mock file operations
    mockFiles.fileData.mockResolvedValue({
      pageContent: "Test content",
      metadata: {
        title: "Test Document",
        source: "test.pdf",
      },
    });
  });

  afterAll(async () => {
    // Cleanup test files
    await cleanupTestFiles();

    // Force cleanup of any BulkUploadProcessor intervals
    try {
      const {
        BulkUploadProcessor,
      } = require("../../jobs/bulk-upload-processor");
      if (
        BulkUploadProcessor &&
        typeof BulkUploadProcessor.stopCleanup === "function"
      ) {
        BulkUploadProcessor.stopCleanup();
      }
    } catch (_e) {
      // Ignore cleanup errors
    }
  });

  describe("Document Processing Pipeline", () => {
    it(
      "should complete full document processing workflow",
      async () => {
        const _testFile = await createTestPdf("complete-workflow.pdf");

        // Mock successful collector response
        mockCollectorApi.processDocument.mockResolvedValue({
          success: true,
          documents: [
            {
              id: uuidv4(),
              filename: "complete-workflow.pdf",
              content: "PDF content extracted successfully",
              metadata: {
                location:
                  "/documents/test-workspace/complete-workflow.pdf.json",
                pages: 1,
                size: 1024,
              },
            },
          ],
        });

        // Process document through collector
        const result = await mockCollectorApi.processDocument(
          "complete-workflow.pdf",
          "test-workspace"
        );

        if (result && typeof result === "object" && "success" in result) {
          expect(result.success).toBe(true);
          expect((result as any).documents).toHaveLength(1);
        }
        expect((result as any).documents[0].content).toContain(
          "PDF content extracted"
        );
        expect(mockCollectorApi.processDocument).toHaveBeenCalledWith(
          "complete-workflow.pdf",
          "test-workspace"
        );
      },
      TEST_TIMEOUT
    );

    it(
      "should handle file transfer to collector hotdir",
      async () => {
        const sourceFile = await createTestPdf("transfer-test.pdf");
        const uniqueId = uuidv4();
        const targetFile = path.join(
          COLLECTOR_HOTDIR_PATH,
          `transfer-test-${uniqueId}.pdf`
        );

        // Simulate file transfer to collector hotdir
        await fs.promises.copyFile(sourceFile, targetFile);

        // Verify file exists in collector hotdir
        await expect(fs.promises.access(targetFile)).resolves.toBeUndefined();

        // Mock collector processing
        mockCollectorApi.processDocument.mockImplementation(
          async (filename) => {
            // Verify collector can access the file
            const collectorFile = path.join(
              COLLECTOR_HOTDIR_PATH,
              filename || ""
            );
            await fs.promises.access(collectorFile);

            return {
              success: true,
              documents: [
                {
                  id: uuidv4(),
                  filename: filename,
                  content: "File processed from hotdir",
                  metadata: {
                    location: `/documents/test-workspace/${filename}.json`,
                  },
                },
              ],
            };
          }
        );

        const result = await mockCollectorApi.processDocument(
          `transfer-test-${uniqueId}.pdf`,
          "test-workspace"
        );

        if (result && typeof result === "object" && "success" in result) {
          expect(result.success).toBe(true);
          expect((result as any).documents[0].content).toBe(
            "File processed from hotdir"
          );
        }

        // Cleanup
        await fs.promises.unlink(targetFile).catch(() => {});
      },
      TEST_TIMEOUT
    );

    it(
      "should handle OCR and document analysis integration",
      async () => {
        const _testImage = await createTestImage("ocr-test.png");

        // Mock OCR processing response
        mockCollectorApi.processDocument.mockResolvedValue({
          success: true,
          documents: [
            {
              id: uuidv4(),
              filename: "ocr-test.png",
              content: "Extracted text from image via OCR",
              metadata: {
                location: "/documents/test-workspace/ocr-test.png.json",
                ocr: {
                  language: "eng",
                  confidence: 0.95,
                  extractedText: "Extracted text from image via OCR",
                },
              },
            },
          ],
        });

        const result = await mockCollectorApi.processDocument(
          "ocr-test.png",
          "test-workspace"
        );

        if (result && typeof result === "object" && "success" in result) {
          expect(result.success).toBe(true);
          expect((result as any).documents[0].metadata.ocr).toBeDefined();
          expect((result as any).documents[0].metadata.ocr.confidence).toBe(
            0.95
          );
          expect((result as any).documents[0].content).toContain("OCR");
        }
      },
      TEST_TIMEOUT
    );

    it(
      "should handle format conversion processing",
      async () => {
        const _testDocx = await createTestDocx("format-conversion.docx");

        // Mock format conversion response
        mockCollectorApi.processDocument.mockResolvedValue({
          success: true,
          documents: [
            {
              id: uuidv4(),
              filename: "format-conversion.docx",
              content: "DOCX content converted to text",
              metadata: {
                location:
                  "/documents/test-workspace/format-conversion.docx.json",
                originalFormat: "docx",
                convertedFormat: "text",
                wordCount: 25,
              },
            },
          ],
        });

        const result = await mockCollectorApi.processDocument(
          "format-conversion.docx",
          "test-workspace"
        );

        if (result && typeof result === "object" && "success" in result) {
          expect(result.success).toBe(true);
          expect((result as any).documents[0].metadata.originalFormat).toBe(
            "docx"
          );
          expect((result as any).documents[0].metadata.convertedFormat).toBe(
            "text"
          );
          expect((result as any).documents[0].content).toContain(
            "converted to text"
          );
        }
      },
      TEST_TIMEOUT
    );
  });

  describe("Error Propagation and Handling", () => {
    it(
      "should propagate collector service unavailable errors",
      async () => {
        mockCollectorApi.online.mockResolvedValue(false);

        // Simulate service unavailable
        mockCollectorApi.processDocument.mockRejectedValue(
          new Error("Collector service is not available")
        );

        // Check if service is online first
        const isOnline = await mockCollectorApi.online();
        expect(isOnline).toBe(false);

        await expect(
          mockCollectorApi.processDocument("test.pdf", "test-workspace")
        ).rejects.toThrow("Collector service is not available");

        expect(mockCollectorApi.online).toHaveBeenCalled();
      },
      TEST_TIMEOUT
    );

    it(
      "should handle collector processing timeout errors",
      async () => {
        mockCollectorApi.processDocument.mockImplementation(() => {
          return new Promise((_, reject) => {
            setTimeout(() => reject(new Error("Processing timeout")), 100);
          });
        });

        await expect(
          mockCollectorApi.processDocument("large-file.pdf", "test-workspace")
        ).rejects.toThrow("Processing timeout");
      },
      TEST_TIMEOUT
    );

    it(
      "should handle corrupt file processing errors",
      async () => {
        mockCollectorApi.processDocument.mockResolvedValue({
          success: false,
          reason: "File appears to be corrupted or password protected",
          documents: [],
        });

        const result = await mockCollectorApi.processDocument(
          "corrupt.pdf",
          "test-workspace"
        );

        if (result && typeof result === "object" && "success" in result) {
          expect(result.success).toBe(false);
          expect(result.reason).toContain("corrupted or password protected");
          expect((result as any).documents).toHaveLength(0);
        }
      },
      TEST_TIMEOUT
    );

    it(
      "should handle network communication errors",
      async () => {
        mockCollectorApi.processDocument.mockRejectedValue(
          new Error("ECONNREFUSED: Connection refused")
        );

        await expect(
          mockCollectorApi.processDocument("test.pdf", "test-workspace")
        ).rejects.toThrow("ECONNREFUSED");
      },
      TEST_TIMEOUT
    );

    it(
      "should handle file not found errors in hotdir",
      async () => {
        mockCollectorApi.processDocument.mockResolvedValue({
          success: false,
          reason: "File not found in hotdir: nonexistent.pdf",
          documents: [],
        });

        const result = await mockCollectorApi.processDocument(
          "nonexistent.pdf",
          "test-workspace"
        );

        if (result && typeof result === "object" && "success" in result) {
          expect(result.success).toBe(false);
          expect(result.reason).toContain("File not found in hotdir");
        }
      },
      TEST_TIMEOUT
    );

    it(
      "should handle collector error responses with detailed error info",
      async () => {
        mockCollectorApi.processDocument.mockResolvedValue({
          success: false,
          reason:
            "Processing failed: Unsupported file format .xyz - UNSUPPORTED_FORMAT",
          documents: [],
          supportedFormats: ["pdf", "docx", "txt", "md"],
        } as any);

        const result = await mockCollectorApi.processDocument(
          "test.xyz",
          "test-workspace"
        );

        if (result && typeof result === "object" && "success" in result) {
          expect(result.success).toBe(false);
          expect(result.reason).toContain("Unsupported file format");
          expect(result.reason).toContain("UNSUPPORTED_FORMAT");
          expect((result as any).supportedFormats).toContain("pdf");
        }
      },
      TEST_TIMEOUT
    );
  });

  describe("Status Synchronization and Progress Tracking", () => {
    it(
      "should track bulk processing progress with collector status updates",
      async () => {
        const processor = new BulkUploadProcessor(
          uuidv4(),
          "test-workspace",
          mockUser,
          "document-drafting"
        );

        // Workspace is already mocked in beforeEach

        const testFiles = [
          { originalname: "doc1.pdf", path: "/tmp/doc1.pdf", size: 1024 },
          { originalname: "doc2.pdf", path: "/tmp/doc2.pdf", size: 2048 },
          { originalname: "doc3.pdf", path: "/tmp/doc3.pdf", size: 512 },
        ];

        // Create actual test files
        for (const file of testFiles) {
          await fs.promises.writeFile(file.path, "test content");
        }

        // Mock progressive collector responses
        let processCount = 0;
        mockCollectorApi.processDocument.mockImplementation(
          async (filename) => {
            processCount++;

            // Simulate processing delay
            await new Promise((resolve) => setTimeout(resolve, 10));

            return {
              success: true,
              documents: [
                {
                  id: uuidv4(),
                  filename: filename,
                  content: `Processed content for ${filename}`,
                  metadata: {
                    location: `/documents/test-workspace/${filename}.json`,
                    processedAt: new Date().toISOString(),
                    order: processCount,
                  },
                },
              ],
            };
          }
        );

        const job = await processor.processFiles(testFiles);

        if (job && typeof job === "object" && !Array.isArray(job)) {
          expect(job.status).toBe("completed");
          expect(job.totalFiles).toBe(3);
          expect(job.processedFiles).toBe(3);
          expect(job.failedFiles).toBe(0);
          expect(job.successfulFiles).toHaveLength(3);
          expect(mockCollectorApi.processDocument).toHaveBeenCalledTimes(3);
        }

        // Cleanup
        for (const file of testFiles) {
          await fs.promises.unlink(file.path).catch(() => {});
        }
      },
      TEST_TIMEOUT
    );

    it(
      "should handle real-time progress updates during processing",
      async () => {
        const _jobId = uuidv4();

        // Simulate long-running job with progress tracking
        const progressUpdates: number[] = [];

        mockCollectorApi.processDocument.mockImplementation(
          async (filename) => {
            // Simulate processing stages with progress updates
            for (let progress = 0; progress <= 100; progress += 25) {
              progressUpdates.push(progress);
              await new Promise((resolve) => setTimeout(resolve, 5));
            }

            return {
              success: true,
              documents: [
                {
                  id: uuidv4(),
                  filename: filename,
                  content: "Processed with progress tracking",
                  metadata: {
                    location: `/documents/test-workspace/${filename}.json`,
                  },
                },
              ],
            };
          }
        );

        await mockCollectorApi.processDocument(
          "progress-test.pdf",
          "test-workspace"
        );

        expect(progressUpdates).toEqual([0, 25, 50, 75, 100]);
      },
      TEST_TIMEOUT
    );

    it(
      "should synchronize status between backend and collector",
      async () => {
        const statusLog: string[] = [];

        // Mock status progression
        mockCollectorApi.online.mockImplementation(async () => {
          statusLog.push("collector-online-check");
          return true;
        });

        mockCollectorApi.processDocument.mockImplementation(
          async (filename) => {
            statusLog.push("collector-processing-start");
            await new Promise((resolve) => setTimeout(resolve, 10));
            statusLog.push("collector-processing-complete");

            return {
              success: true,
              documents: [
                {
                  id: uuidv4(),
                  filename: filename,
                  content: "Status synchronized processing",
                  metadata: {
                    location: `/documents/test-workspace/${filename}.json`,
                  },
                },
              ],
            };
          }
        );

        // Simulate backend processing workflow
        statusLog.push("backend-start");
        await mockCollectorApi.online();
        statusLog.push("backend-verified-collector");
        await mockCollectorApi.processDocument(
          "status-sync.pdf",
          "test-workspace"
        );
        statusLog.push("backend-complete");

        expect(statusLog).toEqual([
          "backend-start",
          "collector-online-check",
          "backend-verified-collector",
          "collector-processing-start",
          "collector-processing-complete",
          "backend-complete",
        ]);
      },
      TEST_TIMEOUT
    );
  });

  describe("File Transfer and Data Integrity", () => {
    it(
      "should ensure file integrity during transfer to collector",
      async () => {
        const originalContent =
          "This is test PDF content with specific checksum data";
        const testFile = path.join(TEST_FILES_DIR, "integrity-test.pdf");
        await fs.promises.writeFile(testFile, originalContent);

        const originalStats = await fs.promises.stat(testFile);
        const uniqueId = uuidv4();
        const collectorFile = path.join(
          COLLECTOR_HOTDIR_PATH,
          `integrity-test-${uniqueId}.pdf`
        );

        // Simulate file transfer
        await fs.promises.copyFile(testFile, collectorFile);

        // Verify integrity
        const transferredContent = await fs.promises.readFile(
          collectorFile,
          "utf8"
        );
        const transferredStats = await fs.promises.stat(collectorFile);

        expect(transferredContent).toBe(originalContent);
        expect(transferredStats.size).toBe(originalStats.size);

        // Cleanup
        await fs.promises.unlink(collectorFile).catch(() => {});
      },
      TEST_TIMEOUT
    );

    it(
      "should handle binary file transfer correctly",
      async () => {
        // Create a binary test file (simulated PDF header)
        const binaryData = Buffer.from([
          0x25, 0x50, 0x44, 0x46, 0x2d, 0x31, 0x2e, 0x34,
        ]); // %PDF-1.4
        const binaryFile = path.join(TEST_FILES_DIR, "binary-test.pdf");
        await fs.promises.writeFile(binaryFile, binaryData);

        const uniqueId = uuidv4();
        const collectorFile = path.join(
          COLLECTOR_HOTDIR_PATH,
          `binary-test-${uniqueId}.pdf`
        );

        // Transfer binary file
        await fs.promises.copyFile(binaryFile, collectorFile);

        // Verify binary integrity
        const originalBuffer = await fs.promises.readFile(binaryFile);
        const transferredBuffer = await fs.promises.readFile(collectorFile);

        expect(transferredBuffer.equals(originalBuffer)).toBe(true);
        expect(transferredBuffer[0]).toBe(0x25); // %
        expect(transferredBuffer[1]).toBe(0x50); // P

        // Cleanup
        await fs.promises.unlink(collectorFile).catch(() => {});
      },
      TEST_TIMEOUT
    );

    it(
      "should handle large file transfers efficiently",
      async () => {
        const largeContent = "x".repeat(1024 * 1024); // 1MB content
        const largeFile = path.join(TEST_FILES_DIR, "large-test.pdf");
        await fs.promises.writeFile(largeFile, largeContent);

        const startTime = Date.now();
        const uniqueId = uuidv4();
        const collectorFile = path.join(
          COLLECTOR_HOTDIR_PATH,
          `large-test-${uniqueId}.pdf`
        );

        await fs.promises.copyFile(largeFile, collectorFile);

        const transferTime = Date.now() - startTime;
        const transferredSize = (await fs.promises.stat(collectorFile)).size;

        expect(transferredSize).toBe(1024 * 1024);
        expect(transferTime).toBeLessThan(5000); // Should complete within 5 seconds

        // Cleanup
        await fs.promises.unlink(collectorFile).catch(() => {});
      },
      TEST_TIMEOUT
    );

    it(
      "should handle concurrent file transfers",
      async () => {
        const concurrentFiles = 5;
        const filePromises: Promise<void>[] = [];

        for (let i = 0; i < concurrentFiles; i++) {
          const filePromise = (async () => {
            const content = `Concurrent file content ${i}`;
            const sourceFile = path.join(TEST_FILES_DIR, `concurrent-${i}.pdf`);
            await fs.promises.writeFile(sourceFile, content);

            const uniqueId = uuidv4();
            const targetFile = path.join(
              COLLECTOR_HOTDIR_PATH,
              `concurrent-${i}-${uniqueId}.pdf`
            );

            await fs.promises.copyFile(sourceFile, targetFile);

            // Verify content
            const transferredContent = await fs.promises.readFile(
              targetFile,
              "utf8"
            );
            expect(transferredContent).toBe(content);

            // Cleanup
            await fs.promises.unlink(targetFile).catch(() => {});
          })();

          filePromises.push(filePromise);
        }

        await Promise.all(filePromises);
      },
      TEST_TIMEOUT
    );
  });

  describe("Background Job Coordination", () => {
    it(
      "should coordinate bulk document processing jobs",
      async () => {
        const jobCoordination: string[] = [];

        // Mock workspace queries
        const prisma = require("../../utils/prisma");
        prisma.workspace_documents.findMany.mockResolvedValue([]);
        prisma.workspace_documents.createMany.mockResolvedValue({ count: 3 });
        prisma.$transaction.mockImplementation(async (callback: any) => {
          if (typeof callback === "function") {
            return await callback(prisma);
          }
          return ["doc1", "doc2", "doc3"];
        });

        // Mock VectorDb
        const { getVectorDbClass } = require("../../utils/helpers");
        getVectorDbClass.mockReturnValue({
          addDocumentToNamespace: jest
            .fn()
            .mockResolvedValue({ vectorized: true }),
          deleteDocumentFromNamespace: jest.fn().mockResolvedValue(true),
        });

        // Mock file data
        const { fileData } = require("../../utils/files");
        (fileData as jest.Mock).mockResolvedValue({
          pageContent: "Test document content",
          metadata: { title: "Test Document" },
        });

        // Create test files
        const testFilePaths = ["doc1.pdf", "doc2.pdf", "doc3.pdf"];
        for (const filePath of testFilePaths) {
          const fullPath = path.join(documentsPath, "test-workspace", filePath);
          await fs.promises.mkdir(path.dirname(fullPath), { recursive: true });
          await fs.promises.writeFile(fullPath, "test content");
        }

        const job = await bulkDocumentProcessor.startBulkJob(
          mockWorkspace as any,
          testFilePaths,
          "1",
          ""
        );

        jobCoordination.push(`job-started-${job.jobId}`);

        // Wait for job completion with timeout
        let jobStatus = bulkDocumentProcessor.getJobStatus(job.jobId);
        const maxWaitTime = 10000; // 10 seconds
        const startTime = Date.now();

        while (
          jobStatus &&
          jobStatus.status === "processing" &&
          Date.now() - startTime < maxWaitTime
        ) {
          await new Promise((resolve) => setTimeout(resolve, 100));
          jobStatus = bulkDocumentProcessor.getJobStatus(job.jobId);
        }

        jobCoordination.push(`job-completed-${job.jobId}`);

        expect(jobCoordination).toHaveLength(2);
        // Check the final job status, not the initial job object
        if (
          jobStatus &&
          typeof jobStatus === "object" &&
          !Array.isArray(jobStatus)
        ) {
          console.log(`Final job status: ${jobStatus.status}`);
          expect(jobStatus.status).toBe("completed");
        }

        // Cleanup
        for (const filePath of testFilePaths) {
          const fullPath = path.join(documentsPath, "test-workspace", filePath);
          await fs.promises.unlink(fullPath).catch(() => {});
        }
      },
      TEST_TIMEOUT
    );

    it(
      "should handle job cancellation mid-processing",
      async () => {
        const processor = new BulkUploadProcessor(
          uuidv4(),
          "test-workspace",
          mockUser,
          "document-drafting"
        );

        // Workspace is already mocked in beforeEach

        // Create test files
        const testFiles = [
          { originalname: "cancel1.pdf", path: "/tmp/cancel1.pdf", size: 1024 },
          { originalname: "cancel2.pdf", path: "/tmp/cancel2.pdf", size: 1024 },
        ];

        for (const file of testFiles) {
          await fs.promises.writeFile(file.path, "test content");
        }

        // Mock slow collector processing
        mockCollectorApi.processDocument.mockImplementation(async () => {
          await new Promise((resolve) => setTimeout(resolve, 100));
          return {
            success: true,
            documents: [
              {
                id: uuidv4(),
                filename: "test.pdf",
                content: "content",
                metadata: {},
              },
            ],
          };
        });

        // Start processing
        const jobPromise = processor.processFiles(testFiles);

        // Cancel job after short delay
        setTimeout(() => {
          BulkUploadProcessor.cancelJob(processor["jobId"]);
        }, 50);

        const job = await jobPromise;

        if (job && typeof job === "object" && !Array.isArray(job)) {
          expect(job.status).toBe("cancelled");
          expect(job.processedFiles).toBeLessThan(testFiles.length);
        }

        // Cleanup
        for (const file of testFiles) {
          await fs.promises.unlink(file.path).catch(() => {});
        }
      },
      TEST_TIMEOUT
    );

    it(
      "should handle resource cleanup after processing",
      async () => {
        const cleanupLog: string[] = [];
        const testFile = path.join(TEST_FILES_DIR, "cleanup-test.pdf");
        await fs.promises.writeFile(testFile, "cleanup test content");

        const uniqueId = uuidv4();
        const collectorFile = path.join(
          COLLECTOR_HOTDIR_PATH,
          `cleanup-test-${uniqueId}.pdf`
        );

        // Simulate processing workflow with cleanup
        try {
          cleanupLog.push("transfer-file");
          await fs.promises.copyFile(testFile, collectorFile);

          cleanupLog.push("process-file");
          mockCollectorApi.processDocument.mockResolvedValue({
            success: true,
            documents: [
              {
                id: uuidv4(),
                filename: "cleanup-test.pdf",
                content: "processed",
                metadata: {},
              },
            ],
          });

          await mockCollectorApi.processDocument(
            `cleanup-test-${uniqueId}.pdf`,
            "test-workspace"
          );

          cleanupLog.push("cleanup-collector-file");
          await fs.promises.unlink(collectorFile);

          cleanupLog.push("cleanup-complete");
        } catch {
          cleanupLog.push("cleanup-error");
          // Ensure cleanup even on error
          await fs.promises.unlink(collectorFile).catch(() => {});
        }

        expect(cleanupLog).toContain("cleanup-complete");
        expect(fs.existsSync(collectorFile)).toBe(false);
      },
      TEST_TIMEOUT
    );
  });

  describe("Performance Under Load", () => {
    it(
      "should handle high-volume concurrent processing",
      async () => {
        const concurrentJobs = 5;
        const filesPerJob = 3;
        const allPromises: Promise<any>[] = [];

        for (let jobIndex = 0; jobIndex < concurrentJobs; jobIndex++) {
          const processor = new BulkUploadProcessor(
            uuidv4(),
            `workspace-${jobIndex}`,
            mockUser,
            "document-drafting"
          );

          // Mock workspace for each job
          const { Workspace } = require("../../models/workspace");
          Workspace.get.mockResolvedValue({
            ...mockWorkspace,
            slug: `workspace-${jobIndex}`,
          });

          let jobFiles: any[] = [];
          for (let fileIndex = 0; fileIndex < filesPerJob; fileIndex++) {
            const filename = `job${jobIndex}-file${fileIndex}.pdf`;
            const filepath = `/tmp/${filename}`;
            await fs.promises.writeFile(filepath, `content for ${filename}`);
            jobFiles.push({
              originalname: filename,
              path: filepath,
              size: 1024,
            });
          }

          // Mock fast processing for load test
          mockCollectorApi.processDocument.mockResolvedValue({
            success: true,
            documents: [
              {
                id: uuidv4(),
                filename: "test.pdf",
                content: "fast processing",
                metadata: { location: "/documents/test/test.pdf.json" },
              },
            ],
          });

          allPromises.push(
            processor.processFiles(jobFiles).then(async (job) => {
              // Cleanup files for this job
              for (const file of jobFiles) {
                await fs.promises.unlink(file.path).catch(() => {});
              }
              return job;
            })
          );
        }

        const startTime = Date.now();
        const results = await Promise.all(allPromises);
        const processingTime = Date.now() - startTime;

        // All jobs should complete successfully
        expect(results).toHaveLength(concurrentJobs);
        results.forEach((job) => {
          if (job && typeof job === "object" && !Array.isArray(job)) {
            expect(job.status).toBe("completed");
            expect(job.totalFiles).toBe(filesPerJob);
          }
        });

        // Performance expectation (should handle 15 files in reasonable time)
        expect(processingTime).toBeLessThan(10000); // 10 seconds
      },
      TEST_TIMEOUT
    );

    it(
      "should handle processing timeout boundaries",
      async () => {
        const timeoutResults: Array<{ success: boolean; duration: number }> =
          [];

        // Test various processing durations
        const testDurations = [100, 500, 1000, 2000]; // milliseconds

        for (const duration of testDurations) {
          mockCollectorApi.processDocument.mockImplementation(async () => {
            const startTime = Date.now();
            await new Promise((resolve) => setTimeout(resolve, duration));
            const actualDuration = Date.now() - startTime;

            return {
              success: true,
              documents: [
                {
                  id: uuidv4(),
                  filename: "timeout-test.pdf",
                  content: "timeout testing",
                  metadata: { processingDuration: actualDuration },
                },
              ],
            };
          });

          try {
            const startTime = Date.now();
            const result = await mockCollectorApi.processDocument(
              "timeout-test.pdf",
              "test-workspace"
            );
            const totalDuration = Date.now() - startTime;

            if (result !== false) {
              timeoutResults.push({
                success: result.success,
                duration: totalDuration,
              });
            } else {
              timeoutResults.push({
                success: false,
                duration: totalDuration,
              });
            }
          } catch {
            timeoutResults.push({
              success: false,
              duration: duration,
            });
          }
        }

        // All tests should succeed within reasonable time
        timeoutResults.forEach((result, index) => {
          expect(result.success).toBe(true);
          expect(result.duration).toBeGreaterThanOrEqual(testDurations[index]);
          expect(result.duration).toBeLessThan(testDurations[index] + 100); // Small tolerance
        });
      },
      TEST_TIMEOUT
    );

    it(
      "should handle memory pressure during large file processing",
      async () => {
        const memoryBefore = process.memoryUsage().heapUsed;

        // Simulate processing multiple large files
        const largeFilePromises = [];
        for (let i = 0; i < 5; i++) {
          const largeContent = "x".repeat(1024 * 100); // 100KB each
          const largeFile = path.join(TEST_FILES_DIR, `memory-test-${i}.pdf`);
          await fs.promises.writeFile(largeFile, largeContent);

          mockCollectorApi.processDocument.mockResolvedValue({
            success: true,
            documents: [
              {
                id: uuidv4(),
                filename: `memory-test-${i}.pdf`,
                content: largeContent, // Echo back large content
                metadata: { size: largeContent.length },
              },
            ],
          });

          largeFilePromises.push(
            mockCollectorApi.processDocument(
              `memory-test-${i}.pdf`,
              "test-workspace"
            )
          );
        }

        await Promise.all(largeFilePromises);

        const memoryAfter = process.memoryUsage().heapUsed;
        const memoryIncrease = memoryAfter - memoryBefore;

        // Memory increase should be reasonable (less than 50MB for test)
        expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
      },
      TEST_TIMEOUT
    );
  });

  // Helper functions
  async function createTestFiles(): Promise<void> {
    // Create basic test files for various scenarios
    const testFilenames = [
      "test-document.pdf",
      "test-image.png",
      "test-word.docx",
      "test-text.txt",
    ];

    for (const filename of testFilenames) {
      const filepath = path.join(TEST_FILES_DIR, filename);
      await fs.promises.writeFile(filepath, `Test content for ${filename}`);
      testFiles.push(filepath);
    }
  }

  async function createTestPdf(filename: string): Promise<string> {
    const filepath = path.join(TEST_FILES_DIR, filename);
    // Create a simple PDF-like file with header
    const pdfContent =
      "%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n>>\nendobj\nTest PDF content";
    await fs.promises.writeFile(filepath, pdfContent);
    testFiles.push(filepath);
    return filepath;
  }

  async function createTestImage(filename: string): Promise<string> {
    const filepath = path.join(TEST_FILES_DIR, filename);
    // Create a simple PNG-like file with header
    const pngHeader = Buffer.from([
      0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a,
    ]);
    const pngContent = Buffer.concat([pngHeader, Buffer.from("fake PNG data")]);
    await fs.promises.writeFile(filepath, pngContent);
    testFiles.push(filepath);
    return filepath;
  }

  async function createTestDocx(filename: string): Promise<string> {
    const filepath = path.join(TEST_FILES_DIR, filename);
    // Create a simple DOCX-like file with ZIP header
    const docxContent = "PK\x03\x04\x14\x00\x06\x00fake DOCX content";
    await fs.promises.writeFile(filepath, docxContent);
    testFiles.push(filepath);
    return filepath;
  }

  async function cleanupTestFiles(): Promise<void> {
    for (const filepath of testFiles) {
      await fs.promises.unlink(filepath).catch(() => {});
    }

    // Clean up test directories
    await fs.promises
      .rmdir(TEST_FILES_DIR, { recursive: true })
      .catch(() => {});

    // Clean up any remaining files in collector hotdir
    try {
      const hotdirFiles = await fs.promises.readdir(COLLECTOR_HOTDIR_PATH);
      for (const file of hotdirFiles) {
        if (file.includes("test") || file.includes("temp")) {
          await fs.promises
            .unlink(path.join(COLLECTOR_HOTDIR_PATH, file))
            .catch(() => {});
        }
      }
    } catch {
      // Hotdir might not exist, ignore
    }
  }
});
