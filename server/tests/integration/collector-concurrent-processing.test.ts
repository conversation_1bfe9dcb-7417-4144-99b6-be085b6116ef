/**
 * Phase 4.1: Collector Concurrent Processing Integration Tests
 *
 * Tests focusing on concurrent processing scenarios, resource management,
 * performance boundaries, and system resilience under load.
 */

import fs from "fs";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import { CollectorApi } from "../../utils/collectorApi";
import { BulkUploadProcessor } from "../../jobs/bulk-upload-processor";
import { bulkDocumentProcessor } from "../../jobs/bulk-document-processor";
import { documentsPath } from "../../utils/files";
import type { FilteredUser } from "../../types/models";

// Add this type for local use in the test file
interface ProcessDocumentResponse {
  success: boolean;
  reason?: string;
  documents: Array<{
    id?: string;
    filename?: string;
    docpath?: string;
    content?: string;
    metadata?: Record<string, unknown>;
    size?: number;
    mimetype?: string;
    [key: string]: unknown;
  }>;
}

// Mock external dependencies
jest.mock("../../utils/collectorApi");
jest.mock("../../models/workspace");
jest.mock("../../utils/prisma", () => ({
  workspace_documents: {
    findMany: jest.fn(),
    createMany: jest.fn(),
  },
  $transaction: jest.fn(),
}));
jest.mock("../../utils/helpers", () => ({
  getVectorDbClass: jest.fn(),
}));
jest.mock("../../utils/files", () => ({
  ...jest.requireActual("../../utils/files"),
  fileData: jest.fn(),
}));

const TEST_TIMEOUT = 45000; // Extended timeout for concurrent tests

describe("Collector Concurrent Processing Integration", () => {
  let mockCollectorApi: jest.Mocked<CollectorApi>;
  const testWorkspaces = ["workspace-1", "workspace-2", "workspace-3"];
  const mockUser: FilteredUser = {
    id: 1,
    username: "testuser",
    email: "<EMAIL>",
    role: "default",
    pfpFilename: "",
    suspended: 0,
    seen_recovery_codes: false,
    custom_ai_userselected: false,
    custom_ai_selected_engine: "",
    custom_ai_option: null,
    custom_system_prompt: "",
    economy_system_id: "",
    organizationId: null,
    createdAt: new Date(),
    lastUpdatedAt: new Date(),
    organization: undefined,
    styleAlignmentEnabled: false,
    activeStyleProfile: null,
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup CollectorApi mock
    mockCollectorApi = new CollectorApi() as jest.Mocked<CollectorApi>;
    (CollectorApi as jest.MockedClass<typeof CollectorApi>).mockImplementation(
      () => mockCollectorApi
    );

    // Setup default successful responses
    mockCollectorApi.online.mockResolvedValue(true);
    mockCollectorApi.processDocument.mockImplementation(
      async (filename, workspace) => {
        // Simulate realistic processing time
        await new Promise((resolve) =>
          setTimeout(resolve, Math.random() * 50 + 10)
        );

        return {
          success: true,
          documents: [
            {
              id: uuidv4(),
              filename: filename,
              content: `Processed content for ${filename}`,
              metadata: {
                location: `/documents/${workspace}/${filename}.json`,
                processedAt: new Date().toISOString(),
                workspace: workspace,
              },
            },
          ],
        };
      }
    );

    // Setup workspace mock
    const { Workspace } = require("../../models/workspace");
    Workspace.get.mockImplementation(async ({ slug }: { slug: string }) => ({
      id: testWorkspaces.indexOf(slug) + 1,
      slug: slug,
      name: `Test ${slug}`,
    }));

    // Setup VectorDb mock
    const { getVectorDbClass } = require("../../utils/helpers");
    getVectorDbClass.mockReturnValue({
      addDocumentToNamespace: jest.fn().mockResolvedValue({ vectorized: true }),
      deleteDocumentFromNamespace: jest.fn().mockResolvedValue(true),
    });

    // Setup file data mock
    const { fileData } = require("../../utils/files");
    fileData.mockResolvedValue({
      pageContent: "Mock document content",
      metadata: { title: "Mock Document" },
    });

    // Setup Prisma mocks
    const prisma = require("../../utils/prisma");
    prisma.workspace_documents.findMany.mockResolvedValue([]);
    prisma.workspace_documents.createMany.mockResolvedValue({ count: 1 });
    prisma.$transaction.mockImplementation(
      async (callback: (prisma: any) => any) => {
        if (typeof callback === "function") {
          return await callback(prisma);
        }
        return [];
      }
    );
  });

  describe("Concurrent File Processing", () => {
    it(
      "should handle multiple simultaneous document processing requests",
      async () => {
        const concurrentRequests = 10;
        const processStartTimes: number[] = [];
        const processEndTimes: number[] = [];

        // Track processing times
        mockCollectorApi.processDocument.mockImplementation(
          async (filename, workspace) => {
            const startTime = Date.now();
            processStartTimes.push(startTime);

            // Simulate varying processing times
            const processingTime = Math.random() * 100 + 50; // 50-150ms
            await new Promise((resolve) => setTimeout(resolve, processingTime));

            const endTime = Date.now();
            processEndTimes.push(endTime);

            return {
              success: true,
              documents: [
                {
                  id: uuidv4(),
                  filename: filename,
                  content: `Concurrent processing result for ${filename}`,
                  metadata: {
                    location: `/documents/${workspace}/${filename}.json`,
                    processingTime: endTime - startTime,
                  },
                },
              ],
            };
          }
        );

        // Create concurrent processing promises
        const processingPromises = Array.from(
          { length: concurrentRequests },
          (_, index) =>
            mockCollectorApi.processDocument(
              `concurrent-${index}.pdf`,
              `workspace-${index % 3}`
            )
        );

        const startTime = Date.now();
        const results = await Promise.all(processingPromises);
        const totalTime = Date.now() - startTime;

        // Verify all requests completed successfully
        expect(results).toHaveLength(concurrentRequests);
        results.forEach((result, index) => {
          if (
            typeof result === "object" &&
            result !== null &&
            "success" in result &&
            result.success
          ) {
            expect(result.success).toBe(true);
            expect(result.documents?.[0]?.filename ?? "unknown-filename").toBe(
              `concurrent-${index}.pdf`
            );
          }
        });

        // Performance assertions
        expect(mockCollectorApi.processDocument).toHaveBeenCalledTimes(
          concurrentRequests
        );
        expect(totalTime).toBeLessThan(1000); // Should complete faster than sequential processing

        // Verify concurrent execution (some requests might overlap in real environments)
        const maxStartTime = Math.max(...processStartTimes);
        const minEndTime = Math.min(...processEndTimes);
        // In test environments, requests might execute sequentially
        expect(minEndTime).toBeLessThanOrEqual(maxStartTime + 100); // Allow small overlap or sequential execution
      },
      TEST_TIMEOUT
    );

    it(
      "should handle concurrent processing across different workspaces",
      async () => {
        const workspaceResults: Record<string, any[]> = {};

        // Mock processing with workspace-specific behavior
        mockCollectorApi.processDocument.mockImplementation(
          async (filename?: string, folderName?: string) => {
            await new Promise((resolve) =>
              setTimeout(resolve, Math.random() * 30 + 10)
            );

            if (typeof folderName !== "string") {
              throw new Error("Workspace must be a string");
            }

            if (
              !Object.prototype.hasOwnProperty.call(
                workspaceResults,
                folderName
              )
            ) {
              workspaceResults[folderName] = [];
            }

            const result = {
              success: true,
              documents: [
                {
                  id: uuidv4(),
                  filename: filename ?? "unknown-filename",
                  content: `Content for ${filename} in ${folderName}`,
                  metadata: {
                    location: `/documents/${folderName}/${filename}.json`,
                    workspace: folderName,
                  },
                },
              ],
            };

            workspaceResults[folderName].push(result);
            return result;
          }
        );

        // Process files across multiple workspaces concurrently
        const processingPromises = testWorkspaces.flatMap((workspace) =>
          Array.from({ length: 3 }, (_, index) =>
            mockCollectorApi.processDocument(`file-${index}.pdf`, workspace)
          )
        );

        const results = await Promise.all(processingPromises);

        // Verify results are properly segregated by workspace
        expect(results).toHaveLength(9); // 3 workspaces × 3 files each
        expect(Object.keys(workspaceResults).sort()).toEqual(
          testWorkspaces.sort()
        );

        testWorkspaces.forEach((workspace) => {
          expect(workspaceResults[workspace]).toHaveLength(3);
          workspaceResults[workspace].forEach((result) => {
            expect(result.documents[0].metadata.workspace).toBe(workspace);
          });
        });
      },
      TEST_TIMEOUT
    );

    it(
      "should handle mixed success and failure scenarios concurrently",
      async () => {
        const mixedResults: Array<{ success: boolean; filename: string }> = [];

        mockCollectorApi.processDocument.mockImplementation(
          async (filename?: string, folderName?: string) => {
            await new Promise((resolve) =>
              setTimeout(resolve, Math.random() * 20 + 5)
            );

            // Simulate some failures based on filename pattern
            const shouldFail =
              (filename?.includes("fail") ?? false) || Math.random() < 0.2;

            if (shouldFail) {
              const errorResult = {
                success: false,
                reason: `Processing failed for ${filename ?? "unknown-filename"}`,
                documents: [],
              };
              mixedResults.push({
                success: false,
                filename: filename ?? "unknown-filename",
              });
              return errorResult;
            }

            const successResult = {
              success: true,
              documents: [
                {
                  id: uuidv4(),
                  filename: filename ?? "unknown-filename",
                  content: `Successfully processed ${filename ?? "unknown-filename"}`,
                  metadata: {
                    location: `/documents/${folderName}/${filename}.json`,
                  },
                },
              ],
            };
            mixedResults.push({
              success: true,
              filename: filename ?? "unknown-filename",
            });
            return successResult;
          }
        );

        // Create mix of files that should succeed and fail
        const filenames = [
          "success-1.pdf",
          "fail-1.pdf",
          "success-2.pdf",
          "normal-1.pdf",
          "fail-2.pdf",
          "normal-2.pdf",
          "success-3.pdf",
          "normal-3.pdf",
        ];

        const processingPromises = filenames.map((filename) =>
          mockCollectorApi.processDocument(filename, "test-workspace")
        );

        const results = await Promise.all(processingPromises);

        // Verify mixed results
        const successCount = results.filter(
          (r): r is typeof r & { success: true; documents: any[] } =>
            typeof r === "object" &&
            r !== null &&
            "success" in r &&
            r.success === true
        ).length;
        const failureCount = results.filter(
          (r): r is typeof r & { success: false; documents: any[] } =>
            typeof r === "object" &&
            r !== null &&
            "success" in r &&
            r.success === false
        ).length;

        expect(successCount + failureCount).toBe(filenames.length);
        expect(failureCount).toBeGreaterThan(0); // Should have some failures
        expect(successCount).toBeGreaterThan(0); // Should have some successes

        // Verify all files were processed
        expect(mixedResults).toHaveLength(filenames.length);
        filenames.forEach((filename) => {
          expect(mixedResults.some((r) => r.filename === filename)).toBe(true);
        });
      },
      TEST_TIMEOUT
    );
  });

  describe("Bulk Processing Concurrency", () => {
    it(
      "should handle multiple bulk upload jobs concurrently",
      async () => {
        const jobCount = 3;
        const filesPerJob = 4;
        const allJobPromises: Promise<any>[] = [];
        const jobResults: Array<{
          jobId: string;
          status: string;
          filesProcessed: number;
        }> = [];

        // Create temporary files for each job
        const cleanupFiles: string[] = [];

        for (let jobIndex = 0; jobIndex < jobCount; jobIndex++) {
          const jobId = uuidv4();
          const processor = new BulkUploadProcessor(
            jobId,
            `workspace-${jobIndex}`,
            mockUser,
            "document-drafting"
          );

          // Create test files for this job
          const jobFiles = [];
          for (let fileIndex = 0; fileIndex < filesPerJob; fileIndex++) {
            const filename = `job${jobIndex}-file${fileIndex}.pdf`;
            const filepath = `/tmp/${filename}`;
            await fs.promises.writeFile(filepath, `Content for ${filename}`);
            cleanupFiles.push(filepath);

            jobFiles.push({
              originalname: filename,
              path: filepath,
              size: 1024,
            });
          }

          // Start bulk processing
          const jobPromise = processor.processFiles(jobFiles).then((result) => {
            jobResults.push({
              jobId: jobId,
              status: result.status,
              filesProcessed: result.processedFiles,
            });
            return result;
          });

          allJobPromises.push(jobPromise);
        }

        const startTime = Date.now();
        const results = await Promise.all(allJobPromises);
        const totalTime = Date.now() - startTime;

        // Verify all jobs completed successfully
        expect(results).toHaveLength(jobCount);
        results.forEach((job, _index) => {
          expect(job.status).toBe("completed");
          expect(job.totalFiles).toBe(filesPerJob);
          expect(job.processedFiles).toBe(filesPerJob);
          expect(job.failedFiles).toBe(0);
        });

        // Verify collector was called for all files
        expect(mockCollectorApi.processDocument).toHaveBeenCalledTimes(
          jobCount * filesPerJob
        );

        // Performance expectation - concurrent should be faster than sequential
        expect(totalTime).toBeLessThan(filesPerJob * 1000); // Much faster than sequential

        // Cleanup
        for (const filepath of cleanupFiles) {
          await fs.promises.unlink(filepath).catch(() => {});
        }
      },
      TEST_TIMEOUT
    );

    it(
      "should handle bulk document processor with concurrent batches",
      async () => {
        const testFilePaths = Array.from(
          { length: 15 },
          (_, i) => `bulk-${i}.pdf`
        );

        // Create test files
        for (const filePath of testFilePaths) {
          const fullPath = path.join(documentsPath, "test-workspace", filePath);
          await fs.promises.mkdir(path.dirname(fullPath), { recursive: true });
          await fs.promises.writeFile(fullPath, "test content");
        }

        const job = await bulkDocumentProcessor.startBulkJob(
          { id: 1, slug: "test-workspace", name: "Test Workspace" } as any,
          testFilePaths,
          "1",
          ""
        );

        // Wait for job completion with timeout
        let jobStatus = bulkDocumentProcessor.getJobStatus(job.jobId);
        let waitTime = 0;
        const maxWaitTime = 30000; // 30 seconds

        while (
          jobStatus &&
          jobStatus.status === "processing" &&
          waitTime < maxWaitTime
        ) {
          await new Promise((resolve) => setTimeout(resolve, 100));
          waitTime += 100;
          jobStatus = bulkDocumentProcessor.getJobStatus(job.jobId);
        }

        expect(jobStatus?.status).toBe("completed");
        expect(jobStatus?.progress.total).toBe(testFilePaths.length);
        expect(jobStatus?.progress.processed).toBe(testFilePaths.length);

        // Cleanup
        for (const filePath of testFilePaths) {
          const fullPath = path.join(documentsPath, "test-workspace", filePath);
          await fs.promises.unlink(fullPath).catch(() => {});
        }
      },
      TEST_TIMEOUT
    );
  });

  describe("Resource Management and Limits", () => {
    it(
      "should handle processing under memory pressure",
      async () => {
        const memoryBefore = process.memoryUsage().heapUsed;
        const largeFileCount = 20;

        // Mock processing that simulates memory usage
        mockCollectorApi.processDocument.mockImplementation(
          async (filename, workspace) => {
            // Simulate memory allocation during processing
            const largeContent = "x".repeat(1024 * 100); // 100KB per file
            await new Promise((resolve) => setTimeout(resolve, 10));

            return {
              success: true,
              documents: [
                {
                  id: uuidv4(),
                  filename: filename,
                  content: largeContent,
                  metadata: {
                    location: `/documents/${workspace}/${filename}.json`,
                    size: largeContent.length,
                  },
                },
              ],
            };
          }
        );

        // Process large files concurrently
        const processingPromises = Array.from(
          { length: largeFileCount },
          (_, _index) =>
            mockCollectorApi.processDocument(
              `large-${_index}.pdf`,
              "test-workspace"
            )
        );

        const results = await Promise.all(processingPromises);
        const memoryAfter = process.memoryUsage().heapUsed;
        const memoryIncrease = memoryAfter - memoryBefore;

        // Verify processing succeeded
        expect(results).toHaveLength(largeFileCount);
        results.forEach((result) => {
          if (
            typeof result === "object" &&
            result !== null &&
            "success" in result &&
            result.success
          ) {
            expect(result.success).toBe(true);
            expect(result.documents?.[0]?.content?.length).toBe(1024 * 100);
          }
        });

        // Memory usage should be reasonable (less than 100MB increase)
        expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024);
      },
      TEST_TIMEOUT
    );

    it(
      "should handle file descriptor limits during concurrent processing",
      async () => {
        const highConcurrency = 50;
        const fileDescriptorUsage: number[] = [];

        mockCollectorApi.processDocument.mockImplementation(
          async (filename, workspace) => {
            // Simulate file descriptor usage
            fileDescriptorUsage.push(Date.now());
            await new Promise((resolve) =>
              setTimeout(resolve, Math.random() * 20 + 5)
            );

            return {
              success: true,
              documents: [
                {
                  id: uuidv4(),
                  filename: filename,
                  content: `Processed ${filename}`,
                  metadata: {
                    location: `/documents/${workspace}/${filename}.json`,
                  },
                },
              ],
            };
          }
        );

        // Create high concurrency scenario
        const processingPromises = Array.from(
          { length: highConcurrency },
          (_, _index) =>
            mockCollectorApi.processDocument(
              `fd-test-${_index}.pdf`,
              "test-workspace"
            )
        );

        const results = await Promise.all(processingPromises);

        // Verify all processes completed
        expect(results).toHaveLength(highConcurrency);
        expect(fileDescriptorUsage).toHaveLength(highConcurrency);

        // Verify no file descriptor leaks (all processes should complete)
        results.forEach((result) => {
          if (
            typeof result === "object" &&
            result !== null &&
            "success" in result &&
            result.success
          ) {
            expect(result.success).toBe(true);
          }
        });
      },
      TEST_TIMEOUT
    );

    it(
      "should handle network connection pool limits",
      async () => {
        const connectionRequests = 30;
        const connectionTimes: number[] = [];

        mockCollectorApi.processDocument.mockImplementation(
          async (filename, workspace) => {
            const connectionStart = Date.now();
            connectionTimes.push(connectionStart);

            // Simulate network processing time
            await new Promise((resolve) =>
              setTimeout(resolve, Math.random() * 30 + 10)
            );

            return {
              success: true,
              documents: [
                {
                  id: uuidv4(),
                  filename: filename,
                  content: `Network processed ${filename}`,
                  metadata: {
                    location: `/documents/${workspace}/${filename}.json`,
                    connectionTime: connectionStart,
                  },
                },
              ],
            };
          }
        );

        // Test connection pool under load
        const processingPromises = Array.from(
          { length: connectionRequests },
          (_, index) =>
            mockCollectorApi.processDocument(
              `connection-${index}.pdf`,
              "test-workspace"
            )
        );

        const startTime = Date.now();
        const results = await Promise.all(processingPromises);
        const totalTime = Date.now() - startTime;

        // Verify all connections succeeded
        expect(results).toHaveLength(connectionRequests);
        results.forEach((result) => {
          if (
            typeof result === "object" &&
            result !== null &&
            "success" in result &&
            result.success
          ) {
            expect(result.success).toBe(true);
          }
        });

        // Performance should be reasonable even with many connections
        expect(totalTime).toBeLessThan(2000); // Should complete within 2 seconds
        expect(connectionTimes).toHaveLength(connectionRequests);
      },
      TEST_TIMEOUT
    );
  });

  describe("Error Propagation in Concurrent Scenarios", () => {
    it(
      "should isolate failures in concurrent processing",
      async () => {
        const successFiles = [
          "success-1.pdf",
          "success-2.pdf",
          "success-3.pdf",
        ];
        const failFiles = ["fail-1.pdf", "fail-2.pdf"];
        const allFiles = [...successFiles, ...failFiles];

        mockCollectorApi.processDocument.mockImplementation(
          async (filename?: string, folderName?: string) => {
            await new Promise((resolve) =>
              setTimeout(resolve, Math.random() * 20 + 5)
            );

            if (filename?.includes("fail")) {
              throw new Error(
                `Processing failed for ${filename ?? "unknown-filename"}`
              );
            }

            return {
              success: true,
              documents: [
                {
                  id: uuidv4(),
                  filename: filename ?? "unknown-filename",
                  content: `Successfully processed ${filename ?? "unknown-filename"}`,
                  metadata: {
                    location: `/documents/${folderName}/${filename}.json`,
                  },
                },
              ],
            };
          }
        );

        // Process mixed success/failure files concurrently
        const processingPromises = allFiles.map((filename) =>
          mockCollectorApi
            .processDocument(filename, "test-workspace")
            .catch((error) => ({ error: error.message, filename }))
        );

        const results = await Promise.all(processingPromises);

        // Verify failures are isolated
        const successResults = results.filter(
          (r): r is typeof r & { success: true; documents: any[] } =>
            typeof r === "object" &&
            r !== null &&
            "success" in r &&
            r.success === true
        );
        const failureResults = results.filter(
          (r): r is { error: any; filename: string } =>
            typeof r === "object" && r !== null && "error" in r
        );

        expect(successResults).toHaveLength(successFiles.length);
        expect(failureResults).toHaveLength(failFiles.length);

        // Verify specific files succeeded/failed as expected
        successFiles.forEach((filename) => {
          const result = successResults.find(
            (r) => (r as any).documents?.[0]?.filename === filename
          );
          expect(result).toBeDefined();
        });

        failFiles.forEach((filename) => {
          const result = failureResults.find(
            (r) => (r as any).filename === filename
          );
          expect(result).toBeDefined();
          expect((result as any).error).toContain(
            `Processing failed for ${filename}`
          );
        });
      },
      TEST_TIMEOUT
    );

    it(
      "should handle collector service becoming unavailable during concurrent processing",
      async () => {
        let requestCount = 0;
        const unavailableAfter = 5;

        mockCollectorApi.processDocument.mockImplementation(
          async (filename?: string, folderName?: string) => {
            requestCount++;
            await new Promise((resolve) => setTimeout(resolve, 10));

            if (requestCount > unavailableAfter) {
              throw new Error("Collector service unavailable");
            }

            return {
              success: true,
              documents: [
                {
                  id: uuidv4(),
                  filename: filename ?? "unknown-filename",
                  content: `Processed before unavailable: ${filename ?? "unknown-filename"}`,
                  metadata: {
                    location: `/documents/${folderName}/${filename}.json`,
                  },
                },
              ],
            };
          }
        );

        // Start many concurrent requests
        const processingPromises = Array.from({ length: 10 }, (_, index) =>
          mockCollectorApi
            .processDocument(`availability-${index}.pdf`, "test-workspace")
            .catch((error) => ({ error: error.message, index }))
        );

        const results = await Promise.all(processingPromises);

        // Some should succeed, others should fail
        const successResults = results.filter(
          (r) => r !== false && !("error" in r)
        );
        const failureResults = results.filter(
          (r) => r !== false && "error" in r
        );

        expect(successResults.length).toBeLessThanOrEqual(unavailableAfter);
        expect(failureResults.length).toBeGreaterThan(0);

        // Verify failure message
        failureResults.forEach((result) => {
          expect((result as any).error).toContain(
            "Collector service unavailable"
          );
        });
      },
      TEST_TIMEOUT
    );

    it(
      "should handle partial network failures in concurrent processing",
      async () => {
        let networkFailureCount = 0;
        const maxFailures = 3;

        mockCollectorApi.processDocument.mockImplementation(
          async (filename?: string, folderName?: string) => {
            await new Promise((resolve) =>
              setTimeout(resolve, Math.random() * 20 + 5)
            );

            // Simulate intermittent network failures
            if (Math.random() < 0.3 && networkFailureCount < maxFailures) {
              networkFailureCount++;
              throw new Error(
                `Network timeout for ${filename ?? "unknown-filename"}`
              );
            }

            return {
              success: true,
              documents: [
                {
                  id: uuidv4(),
                  filename: filename ?? "unknown-filename",
                  content: `Network processed ${filename ?? "unknown-filename"}`,
                  metadata: {
                    location: `/documents/${folderName}/${filename}.json`,
                  },
                },
              ],
            };
          }
        );

        const processingPromises = Array.from({ length: 15 }, (_, index) =>
          mockCollectorApi
            .processDocument(`network-${index}.pdf`, "test-workspace")
            .catch((error) => ({ error: error.message, index }))
        );

        const results = await Promise.all(processingPromises);

        // Should have both successes and failures
        const filteredResults = [] as Array<
          ProcessDocumentResponse | { error: any; index: number }
        >;
        for (const r of results) {
          const isObject = typeof r === "object" && r !== null;
          if (
            isObject &&
            (Object.prototype.hasOwnProperty.call(r, "success") ||
              Object.prototype.hasOwnProperty.call(r, "error"))
          ) {
            filteredResults.push(
              r as ProcessDocumentResponse | { error: any; index: number }
            );
          }
        }
        const successResults = filteredResults.filter(
          (r): r is ProcessDocumentResponse =>
            "success" in r && r.success === true
        );
        const failureResults = filteredResults.filter(
          (r): r is { error: any; index: number } => "error" in r
        );

        expect(successResults.length).toBeGreaterThan(0);
        expect(failureResults.length).toBeLessThanOrEqual(maxFailures);
        expect(successResults.length + failureResults.length).toBe(15);

        // Verify failure messages
        failureResults.forEach((result) => {
          expect((result as any).error).toContain("Network timeout");
        });
      },
      TEST_TIMEOUT
    );
  });

  describe("Performance Monitoring and Metrics", () => {
    it(
      "should track processing performance metrics across concurrent requests",
      async () => {
        const performanceMetrics: Array<{
          filename: string;
          startTime: number;
          endTime: number;
          processingTime: number;
        }> = [];

        mockCollectorApi.processDocument.mockImplementation(
          async (filename?: string, folderName?: string) => {
            const startTime = Date.now();

            // Simulate varying processing times
            const processingDelay = Math.random() * 100 + 20; // 20-120ms
            await new Promise((resolve) =>
              setTimeout(resolve, processingDelay)
            );

            const endTime = Date.now();
            const processingTime = endTime - startTime;

            performanceMetrics.push({
              filename:
                typeof filename === "string" ? filename : "unknown-filename",
              startTime,
              endTime,
              processingTime,
            });

            return {
              success: true,
              documents: [
                {
                  id: uuidv4(),
                  filename:
                    typeof filename === "string"
                      ? filename
                      : "unknown-filename",
                  content: `Performance tested ${filename}`,
                  metadata: {
                    location: `/documents/${folderName}/${filename}.json`,
                    processingTime,
                  },
                },
              ],
            };
          }
        );

        const concurrentCount = 20;
        const processingPromises = Array.from(
          { length: concurrentCount },
          (_, index) =>
            mockCollectorApi.processDocument(
              `perf-${index}.pdf`,
              "test-workspace"
            )
        );

        const overallStartTime = Date.now();
        const results = await Promise.all(processingPromises);
        const overallEndTime = Date.now();
        const overallTime = overallEndTime - overallStartTime;

        // Verify all completed successfully
        expect(results).toHaveLength(concurrentCount);
        expect(performanceMetrics).toHaveLength(concurrentCount);

        // Calculate performance statistics
        const processingTimes = performanceMetrics.map((m) => m.processingTime);
        const averageTime =
          processingTimes.reduce((sum, time) => sum + time, 0) /
          processingTimes.length;
        const maxTime = Math.max(...processingTimes);
        const minTime = Math.min(...processingTimes);

        // Performance assertions
        expect(averageTime).toBeLessThan(150); // Average should be reasonable
        expect(maxTime).toBeLessThan(200); // Max time should be reasonable
        expect(minTime).toBeGreaterThan(15); // Min time should be realistic
        expect(overallTime).toBeLessThan(500); // Concurrent should be much faster than sequential

        // Verify concurrent execution (overlap in timing)
        const sortedByStart = performanceMetrics.sort(
          (a, b) => a.startTime - b.startTime
        );
        const firstStart = sortedByStart[0].startTime;
        const lastStart = sortedByStart[sortedByStart.length - 1].startTime;
        const timeSpread = lastStart - firstStart;

        // Time spread should be small for concurrent execution
        expect(timeSpread).toBeLessThan(100);
      },
      TEST_TIMEOUT
    );
  });
});
