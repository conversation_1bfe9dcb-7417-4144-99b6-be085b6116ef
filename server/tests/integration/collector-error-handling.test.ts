/**
 * Phase 4.1: Collector Error Handling and Retry Integration Tests
 *
 * Tests focusing on error handling, retry mechanisms, fault tolerance,
 * and system recovery in backend-collector integration scenarios.
 */

import fs from "fs";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import { CollectorApi } from "../../utils/collectorApi";
import { BulkUploadProcessor } from "../../jobs/bulk-upload-processor";

// Mock external dependencies
jest.mock("../../utils/collectorApi");
jest.mock("../../models/workspace");

// Mock fs for faster tests
jest.mock("fs", () => ({
  ...jest.requireActual("fs"),
  promises: {
    ...jest.requireActual("fs").promises,
    writeFile: jest.fn().mockResolvedValue(undefined),
    unlink: jest.fn().mockResolvedValue(undefined),
    stat: jest.fn().mockResolvedValue({ size: 1024, isFile: () => true }),
    mkdir: jest.fn().mockResolvedValue(undefined),
    rmdir: jest.fn().mockResolvedValue(undefined),
  },
}));

// Timeout is now set directly in describe block

describe("Collector Error Handling and Retry Integration", () => {
  jest.setTimeout(30000); // Set timeout for all tests in this suite
  let mockCollectorApi: jest.Mocked<CollectorApi>;
  const testWorkspace = {
    id: 1,
    slug: "error-test-workspace",
    name: "Error Test Workspace",
  };
  // 1. Update mockUser to include all required FilteredUser properties:
  const mockUser = {
    id: 1,
    username: "errortest",
    email: "<EMAIL>",
    role: "default" as const,
    pfpFilename: null,
    suspended: 0,
    seen_recovery_codes: false,
    custom_ai_userselected: false,
    custom_ai_selected_engine: "_CUAI",
    economy_system_id: null,
    createdAt: new Date(),
    lastUpdatedAt: new Date(),
    organizationId: null,
    custom_system_prompt: null,
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup CollectorApi mock
    mockCollectorApi = new CollectorApi() as jest.Mocked<CollectorApi>;
    (CollectorApi as jest.MockedClass<typeof CollectorApi>).mockImplementation(
      () => mockCollectorApi
    );

    // Setup workspace mock
    const { Workspace } = require("../../models/workspace");
    Workspace.get.mockResolvedValue(testWorkspace);
  });

  describe("Network Error Handling", () => {
    it("should handle connection refused errors", async () => {
      mockCollectorApi.online.mockRejectedValue(new Error("ECONNREFUSED"));

      const isOnline = await mockCollectorApi.online().catch(() => false);
      expect(isOnline).toBe(false);
    });

    it("should handle timeout errors during processing", async () => {
      mockCollectorApi.processDocument.mockImplementation(() => {
        return new Promise((_, reject) => {
          setTimeout(() => reject(new Error("ETIMEDOUT")), 10); // Reduced delay
        });
      });

      await expect(
        mockCollectorApi.processDocument("timeout-test.pdf", "test-workspace")
      ).rejects.toThrow("ETIMEDOUT");
    });

    it("should handle DNS resolution failures", async () => {
      mockCollectorApi.processDocument.mockRejectedValue(
        new Error("ENOTFOUND")
      );

      await expect(
        mockCollectorApi.processDocument("dns-test.pdf", "test-workspace")
      ).rejects.toThrow("ENOTFOUND");
    });

    it("should handle network interruptions during file transfer", async () => {
      let attemptCount = 0;
      mockCollectorApi.processDocument.mockImplementation(async () => {
        attemptCount++;
        if (attemptCount <= 2) {
          throw new Error("ECONNRESET: Connection reset by peer");
        }
        return {
          success: true,
          documents: [
            {
              id: uuidv4(),
              filename: "recovery-test.pdf",
              content: "Recovered after network interruption",
              metadata: { location: "/documents/test/recovery-test.pdf.json" },
            },
          ],
        };
      });

      // First two attempts should fail, third should succeed
      await expect(
        mockCollectorApi.processDocument("network-test.pdf", "test-workspace")
      ).rejects.toThrow("ECONNRESET");

      await expect(
        mockCollectorApi.processDocument("network-test.pdf", "test-workspace")
      ).rejects.toThrow("ECONNRESET");

      const result = await mockCollectorApi.processDocument(
        "network-test.pdf",
        "test-workspace"
      );
      expect(result && result.success).toBe(true);
      expect(attemptCount).toBe(3);
    });

    it("should handle partial data transmission errors", async () => {
      mockCollectorApi.processDocument.mockResolvedValue({
        success: false,
        reason: "Incomplete data received: Expected 1024 bytes, got 512 bytes",
        documents: [],
      });

      const result = await mockCollectorApi.processDocument(
        "partial-data.pdf",
        "test-workspace"
      );

      expect(result && result.success).toBe(false);
      expect(result && result.reason).toContain("Incomplete data received");
    });
  });

  describe("Service Availability Errors", () => {
    it("should handle collector service unavailable", async () => {
      mockCollectorApi.online.mockResolvedValue(false);
      mockCollectorApi.processDocument.mockRejectedValue(
        new Error("Collector service is not available")
      );

      const isOnline = await mockCollectorApi.online();
      expect(isOnline).toBe(false);

      await expect(
        mockCollectorApi.processDocument(
          "unavailable-test.pdf",
          "test-workspace"
        )
      ).rejects.toThrow("Collector service is not available");
    });

    it("should handle collector service returning 500 errors", async () => {
      mockCollectorApi.processDocument.mockRejectedValue(
        new Error("HTTP 500: Internal Server Error")
      );

      await expect(
        mockCollectorApi.processDocument("server-error.pdf", "test-workspace")
      ).rejects.toThrow("HTTP 500");
    });

    it("should handle collector service returning 502 Bad Gateway", async () => {
      mockCollectorApi.processDocument.mockRejectedValue(
        new Error("HTTP 502: Bad Gateway")
      );

      await expect(
        mockCollectorApi.processDocument("bad-gateway.pdf", "test-workspace")
      ).rejects.toThrow("HTTP 502");
    });

    it("should handle collector service overload (503 Service Unavailable)", async () => {
      mockCollectorApi.processDocument.mockRejectedValue(
        new Error("HTTP 503: Service Unavailable - Server overloaded")
      );

      await expect(
        mockCollectorApi.processDocument("overload-test.pdf", "test-workspace")
      ).rejects.toThrow("HTTP 503");
    });

    it("should handle collector maintenance mode", async () => {
      mockCollectorApi.processDocument.mockResolvedValue({
        success: false,
        reason: "Service temporarily unavailable for maintenance",
        documents: [],
      });

      const result = await mockCollectorApi.processDocument(
        "maintenance-test.pdf",
        "test-workspace"
      );

      expect(result && result.success).toBe(false);
      expect(result && result.reason).toContain("maintenance");
    });
  });

  describe("File Processing Errors", () => {
    it("should handle corrupt file errors", async () => {
      mockCollectorApi.processDocument.mockResolvedValue({
        success: false,
        reason: "File appears to be corrupted or truncated",
        documents: [],
      });

      const result = await mockCollectorApi.processDocument(
        "corrupt.pdf",
        "test-workspace"
      );

      expect(result && result.success).toBe(false);
      expect(result && result.reason).toContain("corrupted");
    });

    it("should handle password-protected file errors", async () => {
      mockCollectorApi.processDocument.mockResolvedValue({
        success: false,
        reason: "File is password protected and cannot be processed",
        documents: [],
      });

      const result = await mockCollectorApi.processDocument(
        "protected.pdf",
        "test-workspace"
      );

      expect(result && result.success).toBe(false);
      expect(result && result.reason).toContain("password protected");
    });

    it("should handle unsupported file format errors", async () => {
      mockCollectorApi.processDocument.mockResolvedValue({
        success: false,
        reason: "Unsupported file format: .xyz files are not supported",
        documents: [],
      });

      const result = await mockCollectorApi.processDocument(
        "unsupported.xyz",
        "test-workspace"
      );

      expect(result && result.success).toBe(false);
      expect(result && result.reason).toContain("Unsupported file format");
    });

    it("should handle file too large errors", async () => {
      mockCollectorApi.processDocument.mockResolvedValue({
        success: false,
        reason: "File size exceeds maximum limit of 100MB",
        documents: [],
      });

      const result = await mockCollectorApi.processDocument(
        "huge-file.pdf",
        "test-workspace"
      );

      expect(result && result.success).toBe(false);
      expect(result && result.reason).toContain("exceeds maximum limit");
    });

    it("should handle OCR processing failures", async () => {
      mockCollectorApi.processDocument.mockResolvedValue({
        success: false,
        reason: "OCR processing failed: Text extraction error",
        documents: [],
      });

      const result = await mockCollectorApi.processDocument(
        "ocr-fail.png",
        "test-workspace"
      );

      expect(result && result.success).toBe(false);
      expect(result && result.reason).toContain("OCR processing failed");
    });

    it("should handle conversion failures", async () => {
      mockCollectorApi.processDocument.mockResolvedValue({
        success: false,
        reason:
          "Document conversion failed: Unable to parse document structure",
        documents: [],
      });

      const result = await mockCollectorApi.processDocument(
        "conversion-fail.docx",
        "test-workspace"
      );

      expect(result && result.success).toBe(false);
      expect(result && result.reason).toContain("conversion failed");
    });
  });

  describe("Retry Mechanisms", () => {
    it("should implement exponential backoff retry strategy", async () => {
      const retryAttempts: number[] = [];
      let attemptCount = 0;

      mockCollectorApi.processDocument.mockImplementation(async () => {
        attemptCount++;
        retryAttempts.push(Date.now());

        if (attemptCount <= 3) {
          throw new Error("Temporary failure");
        }

        return {
          success: true,
          documents: [
            {
              id: uuidv4(),
              filename: "retry-test.pdf",
              content: "Success after retries",
              metadata: { location: "/documents/test/retry-test.pdf.json" },
            },
          ],
        };
      });

      // Simulate retry logic (this would be in the actual service)
      let result;
      let retries = 0;
      const maxRetries = 3;

      while (retries < maxRetries) {
        try {
          result = await mockCollectorApi.processDocument(
            "retry-test.pdf",
            "test-workspace"
          );
          break;
        } catch (_error) {
          retries++;
          if (retries < maxRetries) {
            // Exponential backoff: 2^retry * 10ms (reduced for test performance)
            const delay = Math.pow(2, retries) * 10;
            await new Promise((resolve) => setTimeout(resolve, delay));
          } else {
            result = { success: false, error: "Max retries exceeded" };
            break;
          }
        }
      }

      expect(result).toBeDefined();
      expect(attemptCount).toBe(3); // 3 failures, then break
      expect(retryAttempts).toHaveLength(3);
    });

    it("should respect maximum retry limits", async () => {
      let attemptCount = 0;

      mockCollectorApi.processDocument.mockImplementation(async () => {
        attemptCount++;
        throw new Error("Persistent failure");
      });

      // Simulate retry logic with max 3 attempts
      let lastError;
      let retries = 0;
      const maxRetries = 3;

      while (retries < maxRetries) {
        try {
          await mockCollectorApi.processDocument(
            "persistent-fail.pdf",
            "test-workspace"
          );
          break;
        } catch {
          lastError = new Error("Persistent failure");
          retries++;
          if (retries < maxRetries) {
            await new Promise((resolve) => setTimeout(resolve, 5)); // Reduced delay
          }
        }
      }

      expect(attemptCount).toBe(maxRetries);
      expect(lastError).toBeInstanceOf(Error);
      expect((lastError as Error).message).toBe("Persistent failure");
    });

    it("should handle jittered retry delays", async () => {
      const retryTimes: number[] = [];
      let attemptCount = 0;

      mockCollectorApi.processDocument.mockImplementation(async () => {
        attemptCount++;

        if (attemptCount <= 2) {
          throw new Error("Jitter test failure");
        }

        return {
          success: true,
          documents: [
            {
              id: uuidv4(),
              filename: "jitter-test.pdf",
              content: "Success with jitter",
              metadata: { location: "/documents/test/jitter-test.pdf.json" },
            },
          ],
        };
      });

      // Simulate jittered retry
      let result;
      let retries = 0;
      const maxRetries = 3;

      // First attempt (not a retry)
      try {
        result = await mockCollectorApi.processDocument(
          "jitter-test.pdf",
          "test-workspace"
        );
      } catch (_error) {
        // Now do retries with jitter
        while (retries < maxRetries) {
          retries++;
          retryTimes.push(Date.now());

          if (retries < maxRetries) {
            // Base delay + random jitter (reduced for test performance)
            const baseDelay = 10;
            const jitter = Math.random() * 5; // 0-5ms jitter
            await new Promise((resolve) =>
              setTimeout(resolve, baseDelay + jitter)
            );

            try {
              result = await mockCollectorApi.processDocument(
                "jitter-test.pdf",
                "test-workspace"
              );
              break;
            } catch (_retryError) {
              // Continue retrying
            }
          } else {
            result = { success: false, error: "Max retries exceeded" };
            break;
          }
        }
      }

      expect(result).toBeDefined();
      // The test should have at least attempted some retries
      expect(retryTimes.length).toBeGreaterThanOrEqual(0);

      // Verify delays have some variation due to jitter
      if (retryTimes.length >= 3) {
        const delay1 = retryTimes[1] - retryTimes[0];
        const delay2 = retryTimes[2] - retryTimes[1];

        // Delays should be roughly in the 10-15ms range but different (adjusted for test performance)
        expect(delay1).toBeGreaterThan(5);
        expect(delay1).toBeLessThan(30);
        expect(delay2).toBeGreaterThan(5);
        expect(delay2).toBeLessThan(30);
      } else if (retryTimes.length >= 2) {
        const delay1 = retryTimes[1] - retryTimes[0];

        // At least verify one delay is in the correct range (adjusted for test performance)
        expect(delay1).toBeGreaterThan(5);
        expect(delay1).toBeLessThan(30);
      } else {
        // If we don't have enough retry times, at least verify jitter was attempted
        expect(retryTimes.length).toBeGreaterThanOrEqual(1);
      }
    });

    it("should differentiate between retryable and non-retryable errors", async () => {
      const retryableErrors = [
        "ETIMEDOUT",
        "ECONNRESET",
        "ENOTFOUND",
        "HTTP 500",
        "HTTP 502",
        "HTTP 503",
      ];

      const nonRetryableErrors = [
        "File is password protected",
        "Unsupported file format",
        "File size exceeds maximum limit",
        "HTTP 400",
        "HTTP 401",
        "HTTP 403",
      ];

      for (const errorMessage of retryableErrors) {
        mockCollectorApi.processDocument.mockRejectedValue(
          new Error(errorMessage)
        );

        // Should be considered retryable
        try {
          await mockCollectorApi.processDocument("test.pdf", "test-workspace");
        } catch (_error) {
          const _isRetryable =
            errorMessage.includes("ETIMEDOUT") ||
            errorMessage.includes("ETIMEOUT") ||
            errorMessage.includes("ECONNRESET") ||
            errorMessage.includes("ENOTFOUND") ||
            errorMessage.includes("HTTP 500") ||
            errorMessage.includes("HTTP 502") ||
            errorMessage.includes("HTTP 503");

          expect(_isRetryable).toBe(true);
        }
      }

      for (const errorMessage of nonRetryableErrors) {
        mockCollectorApi.processDocument.mockResolvedValue({
          success: false,
          reason: errorMessage,
          documents: [],
        });

        const result = await mockCollectorApi.processDocument(
          "test.pdf",
          "test-workspace"
        );

        // Should not be retried (client errors or permanent failures)
        // These errors are not retryable:
        // - HTTP 4xx client errors
        // - Password protected files
        // - Unsupported file formats
        // - Files exceeding maximum size

        expect(result && result.success).toBe(false);
      }
    });
  });

  describe("Bulk Processing Error Handling", () => {
    // These tests involve file I/O and need more time
    jest.setTimeout(60000);
    it("should handle mixed success/failure in bulk processing", async () => {
      const processor = new BulkUploadProcessor(
        uuidv4(),
        "test-workspace",
        mockUser,
        "document-drafting"
      );

      // Create test files in a temp directory
      const tempDir = path.join(__dirname, "temp-test-files");
      await fs.promises.mkdir(tempDir, { recursive: true });

      const testFiles = [
        {
          originalname: "success1.pdf",
          path: path.join(tempDir, "success1.pdf"),
          size: 1024,
        },
        {
          originalname: "fail1.pdf",
          path: path.join(tempDir, "fail1.pdf"),
          size: 1024,
        },
        {
          originalname: "success2.pdf",
          path: path.join(tempDir, "success2.pdf"),
          size: 1024,
        },
        {
          originalname: "fail2.pdf",
          path: path.join(tempDir, "fail2.pdf"),
          size: 1024,
        },
      ];

      for (const file of testFiles) {
        await fs.promises.writeFile(file.path, "test content");
      }

      // Mock collector responses
      mockCollectorApi.processDocument.mockImplementation(async (filename) => {
        if (filename && filename.includes("fail")) {
          throw new Error(`Processing failed for ${filename}`);
        }

        return {
          success: true,
          documents: [
            {
              id: uuidv4(),
              filename: filename,
              content: `Successfully processed ${filename}`,
              metadata: {
                location: `/documents/test-workspace/${filename}.json`,
              },
            },
          ],
        };
      });

      const job = await processor.processFiles(testFiles);

      expect(job.status).toBe("completed");
      expect(job.totalFiles).toBe(4);
      expect(job.processedFiles).toBe(4); // All files are processed (attempted)
      expect(job.failedFiles).toBe(2); // Two files should fail
      expect(job.errors).toHaveLength(2);
      expect(job.errors[0].filename).toContain("fail");
      expect(job.errors[1].filename).toContain("fail");

      // Cleanup
      for (const file of testFiles) {
        await fs.promises.unlink(file.path).catch(() => {});
      }
      await fs.promises.rmdir(tempDir).catch(() => {});
    });

    it("should handle complete bulk processing failure", async () => {
      const processor = new BulkUploadProcessor(
        uuidv4(),
        "test-workspace",
        mockUser,
        "document-drafting"
      );

      // Create test files
      const testFiles = [
        { originalname: "fail1.pdf", path: "/tmp/fail1.pdf", size: 1024 },
        { originalname: "fail2.pdf", path: "/tmp/fail2.pdf", size: 1024 },
        { originalname: "fail3.pdf", path: "/tmp/fail3.pdf", size: 1024 },
      ];

      for (const file of testFiles) {
        await fs.promises.writeFile(file.path, "test content");
      }

      // Mock all failures
      mockCollectorApi.processDocument.mockRejectedValue(
        new Error("Collector service completely unavailable")
      );

      const job = await processor.processFiles(testFiles);

      expect(job.status).toBe("completed");
      expect(job.totalFiles).toBe(3);
      expect(job.processedFiles).toBe(3);
      expect(job.failedFiles).toBe(3);
      expect(job.errors).toHaveLength(3);
      expect(job.successfulFiles).toHaveLength(0);

      // Cleanup
      for (const file of testFiles) {
        await fs.promises.unlink(file.path).catch(() => {});
      }
    });

    it("should handle processor cancellation due to errors", async () => {
      const processor = new BulkUploadProcessor(
        uuidv4(),
        "test-workspace",
        mockUser,
        "document-drafting"
      );

      // Create test files
      const testFiles = [
        { originalname: "cancel1.pdf", path: "/tmp/cancel1.pdf", size: 1024 },
        { originalname: "cancel2.pdf", path: "/tmp/cancel2.pdf", size: 1024 },
        { originalname: "cancel3.pdf", path: "/tmp/cancel3.pdf", size: 1024 },
      ];

      for (const file of testFiles) {
        await fs.promises.writeFile(file.path, "test content");
      }

      // Mock slow processing to allow cancellation
      mockCollectorApi.processDocument.mockImplementation(async () => {
        await new Promise((resolve) => setTimeout(resolve, 50)); // Reduced delay for test performance
        return {
          success: true,
          documents: [
            {
              id: uuidv4(),
              filename: "test.pdf",
              content: "Processed",
              metadata: { location: "/documents/test/test.pdf.json" },
            },
          ],
        };
      });

      // Start processing and cancel after short delay
      const jobPromise = processor.processFiles(testFiles);

      setTimeout(() => {
        BulkUploadProcessor.cancelJob(processor["jobId"]);
      }, 25); // Reduced delay for test performance

      const job = await jobPromise;

      expect(job.status).toBe("cancelled");
      expect(job.processedFiles).toBeLessThan(testFiles.length);

      // Cleanup
      for (const file of testFiles) {
        await fs.promises.unlink(file.path).catch(() => {});
      }
    });
  });

  describe("Recovery and Resilience", () => {
    it("should implement circuit breaker pattern", async () => {
      let failureCount = 0;
      let circuitOpen = false;
      const failureThreshold = 3;

      mockCollectorApi.processDocument.mockImplementation(async () => {
        if (circuitOpen) {
          throw new Error("Circuit breaker is open - service unavailable");
        }

        failureCount++;
        if (failureCount <= failureThreshold) {
          throw new Error("Service failure");
        }

        // Reset on success
        failureCount = 0;
        return {
          success: true,
          documents: [
            {
              id: uuidv4(),
              filename: "circuit-test.pdf",
              content: "Success after circuit recovery",
              metadata: { location: "/documents/test/circuit-test.pdf.json" },
            },
          ],
        };
      });

      // Simulate circuit breaker logic
      for (let i = 0; i < failureThreshold; i++) {
        try {
          await mockCollectorApi.processDocument(
            "circuit-test.pdf",
            "test-workspace"
          );
        } catch {
          if (failureCount >= failureThreshold) {
            circuitOpen = true;
          }
        }
      }

      // Circuit should be open now
      expect(circuitOpen).toBe(true);

      await expect(
        mockCollectorApi.processDocument("circuit-test.pdf", "test-workspace")
      ).rejects.toThrow("Circuit breaker is open");
    });

    it("should implement graceful degradation", async () => {
      let primaryServiceAvailable = false;

      mockCollectorApi.processDocument.mockImplementation(async (filename) => {
        if (!primaryServiceAvailable) {
          // Fallback to simplified processing
          return {
            success: true,
            documents: [
              {
                id: uuidv4(),
                filename: filename,
                content: `Fallback processing: ${filename} (limited functionality)`,
                metadata: {
                  location: `/documents/fallback/${filename}.json`,
                  degraded: true,
                },
              },
            ],
          };
        }

        throw new Error("Primary service unavailable");
      });

      const result = await mockCollectorApi.processDocument(
        "degradation-test.pdf",
        "test-workspace"
      );

      expect(result && result.success).toBe(true);
      expect(
        result && result.documents && result.documents[0].content
      ).toContain("Fallback processing");
      expect(
        result &&
          result.documents &&
          result.documents[0].metadata &&
          (result.documents[0].metadata as any).degraded
      ).toBe(true);
    });

    it("should handle resource exhaustion scenarios", async () => {
      let processingQueue = 0;
      const maxConcurrent = 3;

      mockCollectorApi.processDocument.mockImplementation(async (filename) => {
        processingQueue++;

        if (processingQueue > maxConcurrent) {
          processingQueue--;
          throw new Error("Service overloaded - too many concurrent requests");
        }

        // Simulate processing time (reduced for test performance)
        await new Promise((resolve) => setTimeout(resolve, 10));
        processingQueue--;

        return {
          success: true,
          documents: [
            {
              id: uuidv4(),
              filename: filename,
              content: `Processed under load: ${filename}`,
              metadata: { location: `/documents/test/${filename}.json` },
            },
          ],
        };
      });

      // Test concurrent processing beyond limits
      const concurrentPromises = Array.from({ length: 5 }, (_, i) =>
        mockCollectorApi
          .processDocument(`load-test-${i}.pdf`, "test-workspace")
          .catch((error) => ({ error: error.message }))
      );

      const results = await Promise.all(concurrentPromises);

      const successCount = results.filter(
        (r) => typeof r === "object" && r !== null && !("error" in r)
      ).length;
      const overloadCount = results.filter(
        (r) =>
          typeof r === "object" &&
          r !== null &&
          "error" in r &&
          (r as any).error.includes("overloaded")
      ).length;

      expect(successCount).toBeLessThanOrEqual(maxConcurrent);
      expect(overloadCount).toBeGreaterThan(0);
    });

    it("should implement health checks and automatic recovery", async () => {
      let serviceHealthy = false;
      let healthCheckCount = 0;

      mockCollectorApi.online.mockImplementation(async () => {
        healthCheckCount++;

        // Service becomes healthy after 3 health checks
        if (healthCheckCount >= 3) {
          serviceHealthy = true;
        }

        return serviceHealthy;
      });

      mockCollectorApi.processDocument.mockImplementation(async () => {
        if (!serviceHealthy) {
          throw new Error("Service not healthy");
        }

        return {
          success: true,
          documents: [
            {
              id: uuidv4(),
              filename: "health-test.pdf",
              content: "Processed after health recovery",
              metadata: { location: "/documents/test/health-test.pdf.json" },
            },
          ],
        };
      });

      // Simulate health check loop (reduced delay for test performance)
      while (!serviceHealthy && healthCheckCount < 5) {
        await mockCollectorApi.online();
        await new Promise((resolve) => setTimeout(resolve, 5));
      }

      expect(serviceHealthy).toBe(true);
      expect(healthCheckCount).toBe(3);

      // Service should work now
      const result = await mockCollectorApi.processDocument(
        "health-test.pdf",
        "test-workspace"
      );
      expect(result && result.success).toBe(true);
    });
  });

  describe("Error Logging and Monitoring", () => {
    it("should log error details for debugging", async () => {
      const errorLogs: Array<{ level: string; message: string; details: any }> =
        [];

      // Mock logging function
      const mockLogger = {
        error: jest.fn((message, details) => {
          errorLogs.push({ level: "error", message, details });
        }),
        warn: jest.fn((message, details) => {
          errorLogs.push({ level: "warn", message, details });
        }),
      };

      mockCollectorApi.processDocument.mockRejectedValue(
        new Error("Detailed error for logging test")
      );

      try {
        await mockCollectorApi.processDocument(
          "logging-test.pdf",
          "test-workspace"
        );
      } catch {
        mockLogger.error("Collector processing failed", {
          filename: "logging-test.pdf",
          workspace: "test-workspace",
          error: new Error("Detailed error for logging test").message,
          timestamp: new Date().toISOString(),
        });
      }

      expect(mockLogger.error).toHaveBeenCalled();
      expect(errorLogs).toHaveLength(1);
      expect(errorLogs[0].level).toBe("error");
      expect(errorLogs[0].message).toContain("processing failed");
      expect(errorLogs[0].details.filename).toBe("logging-test.pdf");
    });

    it("should track error metrics for monitoring", async () => {
      const errorMetrics = {
        networkErrors: 0,
        serviceErrors: 0,
        processingErrors: 0,
        totalErrors: 0,
      };

      const errorTypes = [
        "ECONNREFUSED", // Network error
        "HTTP 500", // Service error
        "File corrupted", // Processing error
        "ETIMEDOUT", // Network error
        "HTTP 503", // Service error
      ];

      for (const errorType of errorTypes) {
        mockCollectorApi.processDocument.mockRejectedValue(
          new Error(errorType)
        );

        try {
          await mockCollectorApi.processDocument(
            "metrics-test.pdf",
            "test-workspace"
          );
        } catch {
          errorMetrics.totalErrors++;

          if (
            new Error(errorType).message.includes("ECONN") ||
            new Error(errorType).message.includes("ETIMEDOUT")
          ) {
            errorMetrics.networkErrors++;
          } else if (new Error(errorType).message.includes("HTTP 5")) {
            errorMetrics.serviceErrors++;
          } else {
            errorMetrics.processingErrors++;
          }
        }
      }

      expect(errorMetrics.totalErrors).toBe(5);
      expect(errorMetrics.networkErrors).toBe(2);
      expect(errorMetrics.serviceErrors).toBe(2);
      expect(errorMetrics.processingErrors).toBe(1);
    });

    it("should provide error context for troubleshooting", async () => {
      const errorContext = {
        timestamp: new Date().toISOString(),
        requestId: uuidv4(),
        filename: "context-test.pdf",
        workspace: "test-workspace",
        userAgent: "ISTLegal-Backend/1.0",
        serverVersion: "1.0.0",
      };

      mockCollectorApi.processDocument.mockRejectedValue(
        Object.assign(new Error("Context error test"), {
          context: errorContext,
        })
      );

      try {
        await mockCollectorApi.processDocument(
          "context-test.pdf",
          "test-workspace"
        );
      } catch (error) {
        expect((error as Error).message).toBe("Context error test");
        expect((error as any).context?.requestId).toBe(errorContext.requestId);
        expect((error as any).context?.filename).toBe("context-test.pdf");
      }
    });
  });
});
