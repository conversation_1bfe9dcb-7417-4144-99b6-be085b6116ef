/**
 * Phase 4.1: Collector Service Communication Integration Tests
 *
 * Tests focusing on communication patterns, status synchronization,
 * error handling, and resilience between backend and collector services.
 */

import { CollectorApi } from "../../utils/collectorApi";
import { CommunicationKey } from "../../utils/comKey";
import { EncryptionManager } from "../../utils/EncryptionManager";

// Mock fetch for testing HTTP communication
const mockFetch = jest.fn();
global.fetch = mockFetch;

// Mock dependencies
jest.mock("../../utils/comKey");
jest.mock("../../utils/EncryptionManager");

describe("Collector Service Communication Integration", () => {
  let collectorApi: CollectorApi;
  let mockComKey: jest.Mocked<CommunicationKey>;
  let mockEncryption: jest.Mocked<EncryptionManager>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup mocks
    mockComKey = {
      sign: jest.fn().mockReturnValue("mock-signature"),
      encrypt: jest.fn().mockReturnValue("mock-encrypted-payload"),
    } as any;

    mockEncryption = {
      xPayload: "mock-x-payload",
    } as any;

    (
      CommunicationKey as jest.MockedClass<typeof CommunicationKey>
    ).mockImplementation(() => mockComKey);
    (
      EncryptionManager as jest.MockedClass<typeof EncryptionManager>
    ).mockImplementation(() => mockEncryption);

    collectorApi = new CollectorApi();
  });

  describe("Service Health and Connectivity", () => {
    it("should detect when collector service is online", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
      });

      const isOnline = await collectorApi.online();

      expect(isOnline).toBe(true);
      expect(mockFetch).toHaveBeenCalledWith("http://0.0.0.0:8888");
    });

    it("should detect when collector service is offline", async () => {
      mockFetch.mockRejectedValueOnce(new Error("ECONNREFUSED"));

      const isOnline = await collectorApi.online();

      expect(isOnline).toBe(false);
    });

    it("should handle collector service returning error status", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
      });

      const isOnline = await collectorApi.online();

      expect(isOnline).toBe(false);
    });

    it("should handle network timeouts when checking service status", async () => {
      mockFetch.mockImplementation(() => {
        return new Promise((_, reject) => {
          setTimeout(() => reject(new Error("Network timeout")), 100);
        });
      });

      const isOnline = await collectorApi.online();

      expect(isOnline).toBe(false);
    });

    it("should fetch accepted file types from collector", async () => {
      const mockFileTypes = {
        documents: ["pdf", "docx", "txt"],
        images: ["png", "jpg", "gif"],
        audio: ["mp3", "wav"],
        maxSize: "100MB",
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue(mockFileTypes),
      });

      const fileTypes = await collectorApi.acceptedFileTypes();

      expect(fileTypes).toEqual(mockFileTypes);
      expect(mockFetch).toHaveBeenCalledWith("http://0.0.0.0:8888/accepts");
    });

    it("should handle collector file types endpoint failure", async () => {
      mockFetch.mockRejectedValueOnce(new Error("Service unavailable"));

      const fileTypes = await collectorApi.acceptedFileTypes();

      expect(fileTypes).toBeNull();
    });
  });

  describe("Document Processing Communication", () => {
    it("should send document processing request with proper authentication", async () => {
      const mockResponse = {
        success: true,
        documents: [
          {
            id: "doc-123",
            filename: "test.pdf",
            content: "Processed content",
          },
        ],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue(mockResponse),
      });

      const result = await collectorApi.processDocument(
        "test.pdf",
        "workspace"
      );

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith("http://0.0.0.0:8888/process", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Integrity": "mock-signature",
          "X-Payload-Signer": "mock-encrypted-payload",
        },
        body: expect.stringContaining("test.pdf"),
      });

      // Verify authentication headers were generated
      expect(mockComKey.sign).toHaveBeenCalled();
      expect(mockComKey.encrypt).toHaveBeenCalled();
    });

    it("should handle document processing with options", async () => {
      const mockResponse = {
        success: true,
        documents: [
          {
            id: "doc-456",
            filename: "ocr-test.png",
            content: "OCR extracted text",
          },
        ],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue(mockResponse),
      });

      // Set OCR environment variables
      process.env.WHISPER_PROVIDER = "openai";
      process.env.TARGET_OCR_LANG = "eng,deu";

      const result = await collectorApi.processDocument(
        "ocr-test.png",
        "workspace"
      );

      expect(result).toEqual(mockResponse);

      // Verify request body includes options
      const requestBody = JSON.parse((mockFetch.mock.calls[0][1] as any).body);
      expect(requestBody.options).toEqual(
        expect.objectContaining({
          whisperProvider: "openai",
          ocr: { langList: "eng,deu" },
        })
      );
    });

    it("should handle document processing failure responses", async () => {
      const mockErrorResponse = {
        success: false,
        reason: "File format not supported",
        documents: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue(mockErrorResponse),
      });

      const result = await collectorApi.processDocument(
        "unsupported.xyz",
        "workspace"
      );

      expect(result).toEqual(mockErrorResponse);
      expect(result !== false && result.success).toBe(false);
      expect(result !== false && result.reason).toContain("not supported");
    });

    it("should handle HTTP communication errors during processing", async () => {
      mockFetch.mockRejectedValueOnce(new Error("Connection reset"));

      const result = await collectorApi.processDocument(
        "test.pdf",
        "workspace"
      );

      expect(result).toEqual({
        success: false,
        reason: "Connection reset",
        documents: [],
      });
    });

    it("should handle collector returning invalid JSON", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockRejectedValue(new Error("Invalid JSON")),
      });

      const result = await collectorApi.processDocument(
        "test.pdf",
        "workspace"
      );

      expect(result).toEqual({
        success: false,
        reason: "Invalid JSON",
        documents: [],
      });
    });

    it("should handle collector HTTP error responses", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: "Internal Server Error",
      });

      const result = await collectorApi.processDocument(
        "test.pdf",
        "workspace"
      );

      expect(result).toEqual({
        success: false,
        reason: "Response could not be completed",
        documents: [],
      });
    });
  });

  describe("Link Processing Communication", () => {
    it("should process links with proper authentication", async () => {
      const mockResponse = {
        success: true,
        documents: [
          {
            id: "link-123",
            url: "https://example.com",
            content: "Scraped content",
          },
        ],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue(mockResponse),
      });

      const result = await collectorApi.processLink("https://example.com");

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        "http://0.0.0.0:8888/process-link",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-Integrity": "mock-signature",
            "X-Payload-Signer": "mock-encrypted-payload",
          },
          body: JSON.stringify({ link: "https://example.com" }),
        }
      );
    });

    it("should handle invalid URLs in link processing", async () => {
      const result = await collectorApi.processLink("");

      expect(result).toBe(false);
      expect(mockFetch).not.toHaveBeenCalled();
    });

    it("should handle link processing errors", async () => {
      mockFetch.mockRejectedValueOnce(new Error("Network error"));

      const result = await collectorApi.processLink("https://example.com");

      expect(result).toEqual({
        success: false,
        reason: "Network error",
        documents: [],
      });
    });
  });

  describe("Raw Text Processing Communication", () => {
    it("should process raw text with metadata", async () => {
      const mockResponse = {
        success: true,
        documents: [
          {
            id: "text-123",
            content: "Processed text",
            metadata: { source: "manual" },
          },
        ],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue(mockResponse),
      });

      const metadata = { source: "manual", type: "note" };
      const result = await collectorApi.processRawText("Sample text", metadata);

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        "http://0.0.0.0:8888/process-raw-text",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-Integrity": "mock-signature",
            "X-Payload-Signer": "mock-encrypted-payload",
          },
          body: JSON.stringify({
            textContent: "Sample text",
            metadata: metadata,
          }),
        }
      );
    });

    it("should handle empty text content", async () => {
      const mockResponse = {
        success: false,
        reason: "Empty text content",
        documents: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue(mockResponse),
      });

      const result = await collectorApi.processRawText("");

      expect(result).toEqual(mockResponse);
    });
  });

  describe("Extension Request Forwarding", () => {
    it("should forward extension requests to collector", async () => {
      const mockResponse = {
        success: true,
        data: { result: "extension processed" },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue(mockResponse),
      });

      const requestParams = {
        endpoint: "/custom-extension",
        method: "POST",
        body: JSON.stringify({ param: "value" }),
      };

      const result = await collectorApi.forwardExtensionRequest(requestParams);

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        "http://0.0.0.0:8888/custom-extension",
        {
          method: "POST",
          body: requestParams.body,
          headers: {
            "Content-Type": "application/json",
            "X-Integrity": "mock-signature",
            "X-Payload-Signer": "mock-encrypted-payload",
          },
        }
      );
    });

    it("should handle extension request failures", async () => {
      mockFetch.mockRejectedValueOnce(new Error("Extension not found"));

      const requestParams = {
        endpoint: "/nonexistent",
        method: "GET",
        body: "{}",
      };

      const result = await collectorApi.forwardExtensionRequest(requestParams);

      expect(result).toEqual({
        success: false,
        data: {},
        reason: "Extension not found",
      });
    });
  });

  describe("Link Content Retrieval", () => {
    it("should retrieve link content", async () => {
      const mockResponse = {
        success: true,
        content: "Retrieved web content",
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue(mockResponse),
      });

      const result = await collectorApi.getLinkContent("https://example.com");

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        "http://0.0.0.0:8888/util/get-link",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-Integrity": "mock-signature",
            "X-Payload-Signer": "mock-encrypted-payload",
          },
          body: JSON.stringify({ link: "https://example.com" }),
        }
      );
    });

    it("should handle empty link in content retrieval", async () => {
      const result = await collectorApi.getLinkContent("");

      expect(result).toBe(false);
      expect(mockFetch).not.toHaveBeenCalled();
    });

    it("should handle link content retrieval errors", async () => {
      mockFetch.mockRejectedValueOnce(new Error("Content retrieval failed"));

      const result = await collectorApi.getLinkContent("https://example.com");

      expect(result).toEqual({
        success: false,
        content: null,
      });
    });
  });

  describe("Authentication and Security", () => {
    it("should generate proper authentication headers for all requests", async () => {
      const _testData = JSON.stringify({ test: "data" });

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({ success: true }),
      });

      await collectorApi.processDocument("test.pdf", "workspace");

      // Verify communication key was used for signing
      expect(mockComKey.sign).toHaveBeenCalledWith(
        expect.stringContaining("test.pdf")
      );

      // Verify encryption manager was used for payload signing
      expect(mockComKey.encrypt).toHaveBeenCalledWith("mock-x-payload");
    });

    it("should handle authentication header generation errors", async () => {
      mockComKey.sign.mockImplementation(() => {
        throw new Error("Signing failed");
      });

      await expect(
        collectorApi.processDocument("test.pdf", "workspace")
      ).rejects.toThrow("Signing failed");
    });

    it("should include proper Content-Type headers", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({ success: true }),
      });

      await collectorApi.processDocument("test.pdf", "workspace");

      const headers = (mockFetch.mock.calls[0][1] as any).headers;
      expect(headers["Content-Type"]).toBe("application/json");
    });
  });

  describe("Error Resilience and Recovery", () => {
    it("should handle network interruptions gracefully", async () => {
      // Simulate network interruption
      mockFetch
        .mockRejectedValueOnce(new Error("ECONNRESET"))
        .mockRejectedValueOnce(new Error("ETIMEDOUT"))
        .mockResolvedValueOnce({
          ok: true,
          json: jest.fn().mockResolvedValue({ success: true }),
        });

      // First two calls should fail, but we can test each independently
      await expect(
        collectorApi.processDocument("test1.pdf", "workspace")
      ).resolves.toEqual({
        success: false,
        reason: "ECONNRESET",
        documents: [],
      });

      await expect(
        collectorApi.processDocument("test2.pdf", "workspace")
      ).resolves.toEqual({
        success: false,
        reason: "ETIMEDOUT",
        documents: [],
      });

      // Third call should succeed
      await expect(
        collectorApi.processDocument("test3.pdf", "workspace")
      ).resolves.toEqual({ success: true });
    });

    it("should handle malformed responses from collector", async () => {
      // Simulate malformed JSON response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockRejectedValue(new Error("Unexpected token")),
      });

      const result = await collectorApi.processDocument(
        "test.pdf",
        "workspace"
      );

      expect(result).toEqual({
        success: false,
        reason: "Unexpected token",
        documents: [],
      });
    });

    it("should handle collector returning unexpected response structure", async () => {
      // Collector returns response without expected fields
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({
          status: "ok", // Wrong field name
          files: [], // Wrong field name
        }),
      });

      const result = await collectorApi.processDocument(
        "test.pdf",
        "workspace"
      );

      // Should still return the response, even if structure is unexpected
      expect(result).toEqual({
        status: "ok",
        files: [],
      });
    });

    it("should handle collector service returning 502 Bad Gateway", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 502,
        statusText: "Bad Gateway",
      });

      const result = await collectorApi.processDocument(
        "test.pdf",
        "workspace"
      );

      expect(result).toEqual({
        success: false,
        reason: "Response could not be completed",
        documents: [],
      });
    });

    it("should handle very large response payloads", async () => {
      const largeContent = "x".repeat(10 * 1024 * 1024); // 10MB content
      const largeResponse = {
        success: true,
        documents: [
          {
            id: "large-doc",
            content: largeContent,
            metadata: { size: largeContent.length },
          },
        ],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue(largeResponse),
      });

      const result = await collectorApi.processDocument(
        "large-file.pdf",
        "workspace"
      );

      expect(result).toEqual(largeResponse);
      if (result !== false && result.documents?.[0]?.content) {
        expect(result.documents[0].content.length).toBe(10 * 1024 * 1024);
      }
    });
  });

  describe("Environment Configuration", () => {
    it("should use custom collector port from environment", async () => {
      const originalPort = process.env.COLLECTOR_PORT;
      process.env.COLLECTOR_PORT = "9999";

      // Create new instance to pick up environment change
      const customCollectorApi = new CollectorApi();

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
      });

      await customCollectorApi.online();

      expect(mockFetch).toHaveBeenCalledWith("http://0.0.0.0:9999");

      // Restore original environment
      if (originalPort) {
        process.env.COLLECTOR_PORT = originalPort;
      } else {
        delete process.env.COLLECTOR_PORT;
      }
    });

    it("should use default port when environment variable not set", async () => {
      const originalPort = process.env.COLLECTOR_PORT;
      delete process.env.COLLECTOR_PORT;

      const defaultCollectorApi = new CollectorApi();

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
      });

      await defaultCollectorApi.online();

      expect(mockFetch).toHaveBeenCalledWith("http://0.0.0.0:8888");

      // Restore original environment
      if (originalPort) {
        process.env.COLLECTOR_PORT = originalPort;
      }
    });

    it("should include environment-specific processing options", async () => {
      // Set various environment variables
      process.env.WHISPER_PROVIDER = "openai";
      process.env.WHISPER_MODEL_PREF = "whisper-1";
      process.env.OPEN_AI_KEY = "test-key";
      process.env.TARGET_OCR_LANG = "eng,spa,fra";

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({ success: true }),
      });

      await collectorApi.processDocument("test.pdf", "workspace");

      const requestBody = JSON.parse((mockFetch.mock.calls[0][1] as any).body);
      expect(requestBody.options).toEqual({
        whisperProvider: "openai",
        WhisperModelPref: "whisper-1",
        openAiKey: "test-key",
        ocr: {
          langList: "eng,spa,fra",
        },
      });
    });
  });
});
