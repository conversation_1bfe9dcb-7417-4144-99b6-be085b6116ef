/**
 * Frontend-Backend Error Propagation Integration Tests
 *
 * Tests how errors propagate between frontend and backend layers
 * - API error handling and responses
 * - Error message consistency
 * - Error logging and tracking
 * - Recovery mechanisms
 * - User-friendly error presentation
 */

import request from "supertest";
import app from "../../../index";
import prisma from "../../../utils/prisma";
import { User } from "../../../models/user";
import { Workspace } from "../../../models/workspace";
import { makeJWT } from "../../../utils/http";
import { JWTToken } from "../../../types/auth";

// Mock external dependencies to simulate various error conditions
jest.mock("../../../utils/helpers", () => {
  const originalModule = jest.requireActual("../../../utils/helpers");
  return {
    ...originalModule,
    getLLMProvider: jest.fn().mockReturnValue({
      getChatCompletion: jest.fn().mockImplementation(() => {
        // Simulate LLM provider errors based on test context
        if (global.simulateLLMError) {
          throw new Error("LLM service unavailable");
        }
        return Promise.resolve("Mock LLM response");
      }),
      embedTextInput: jest.fn().mockImplementation(() => {
        if (global.simulateEmbeddingError) {
          throw new Error("Embedding service failed");
        }
        return Promise.resolve([0.1, 0.2, 0.3]);
      }),
    }),
    getVectorDbClass: jest.fn().mockReturnValue({
      getOrCreateVectorDb: jest.fn().mockImplementation(() => {
        if (global.simulateVectorDbError) {
          throw new Error("Vector database connection failed");
        }
        return Promise.resolve({});
      }),
    }),
  };
});

jest.mock("../../../utils/collectorApi", () => ({
  CollectorApi: {
    processDocument: jest.fn().mockImplementation(() => {
      if (global.simulateCollectorError) {
        throw new Error("Document processing service unavailable");
      }
      return Promise.resolve({
        success: true,
        location: "/documents/processed/test.pdf",
      });
    }),
    acceptedFileTypes: jest.fn().mockResolvedValue(["pdf", "txt", "docx"]),
  },
}));

// Extend global interface for test flags
declare global {
  var simulateLLMError: boolean;
  var simulateEmbeddingError: boolean;
  var simulateVectorDbError: boolean;
  var simulateCollectorError: boolean;
  var simulateDatabaseError: boolean;
}

describe("Frontend-Backend Error Propagation Integration Tests", () => {
  let authToken: JWTToken;
  let testUserId: number;
  let testWorkspace: any;
  let databaseAvailable = true;
  let originalNodeEnv: string | undefined;

  beforeAll(async () => {
    originalNodeEnv = process.env.NODE_ENV;

    try {
      // Clean up previous test data
      await prisma.workspaces.deleteMany({
        where: { name: { contains: "Error_Propagation_Test" } },
      });
      await prisma.users.deleteMany({
        where: { username: "error_test_user" },
      });

      // Create test user
      const user = await User.create({
        username: "error_test_user",
        password: "Test123!@#",
        role: "admin",
      });

      if (!user.user) throw new Error("Failed to create test user");
      testUserId = user.user.id;
      authToken = makeJWT({ id: testUserId }, "1h");

      // Create test workspace
      const workspaceResult = await Workspace.new(
        "Error_Propagation_Test_Workspace",
        testUserId
      );
      if (!workspaceResult.workspace) {
        throw new Error("Failed to create test workspace");
      }
      testWorkspace = workspaceResult.workspace;
    } catch (error) {
      // If database is unavailable, mark it as such
      databaseAvailable = false;
      // Database unavailable for error-propagation tests
    }
  });

  afterAll(async () => {
    // Reset global error simulation flags
    global.simulateLLMError = false;
    global.simulateEmbeddingError = false;
    global.simulateVectorDbError = false;
    global.simulateCollectorError = false;
    global.simulateDatabaseError = false;

    // Restore original NODE_ENV
    if (originalNodeEnv !== undefined) {
      process.env.NODE_ENV = originalNodeEnv;
    } else {
      delete process.env.NODE_ENV;
    }

    if (databaseAvailable) {
      try {
        await prisma.workspaces.deleteMany({
          where: { name: { contains: "Error_Propagation_Test" } },
        });
        await prisma.users.deleteMany({
          where: { username: "error_test_user" },
        });
      } catch {
        // Silent cleanup
      }
    }
  });

  beforeEach(() => {
    // Reset error simulation flags before each test
    global.simulateLLMError = false;
    global.simulateEmbeddingError = false;
    global.simulateVectorDbError = false;
    global.simulateCollectorError = false;
    global.simulateDatabaseError = false;
  });

  describe("API Error Response Consistency", () => {
    test("should return consistent error format for authentication failures", async () => {
      const response = await request(app)
        .get("/api/workspaces")
        .set("Authorization", "Bearer invalid_token");

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty("error");
      expect(typeof response.body.error).toBe("string");
      expect(response.headers["content-type"]).toMatch(/application\/json/);
    });

    test("should return consistent error format for validation failures", async () => {
      const response = await request(app)
        .post("/api/workspace/new")
        .set("Authorization", `Bearer ${authToken}`)
        .send({
          /* missing required fields */
        });

      expect([400, 401, 422, 500]).toContain(response.status);
      if (response.body.error) {
        expect(typeof response.body.error).toBe("string");
      }
    });

    test("should return consistent error format for not found resources", async () => {
      const response = await request(app)
        .get("/api/workspace/nonexistent-workspace")
        .set("Authorization", `Bearer ${authToken}`);

      expect([401, 404]).toContain(response.status);
      if (response.body.error) {
        expect(typeof response.body.error).toBe("string");
      }
    });

    test("should return consistent error format for server errors", async () => {
      // Simulate a server error by hitting an endpoint that might fail
      const response = await request(app)
        .get("/api/non-existent-endpoint")
        .set("Authorization", `Bearer ${authToken}`);

      expect(response.status).toBe(404);
      // Some 404 responses are text/plain
      expect(response.headers["content-type"]).toMatch(
        /text\/plain|application\/json/
      );
    });
  });

  describe("Service Layer Error Propagation", () => {
    test("should propagate LLM service errors properly", async () => {
      if (!testWorkspace) {
        return; // Skip test if workspace creation failed
      }
      global.simulateLLMError = true;

      const response = await request(app)
        .post(`/api/workspace/${testWorkspace.slug}/stream-chat/standard`)
        .set("Authorization", `Bearer ${authToken}`)
        .send({
          message: "Test message that will trigger LLM error",
          mode: "chat",
        });

      // Should handle LLM errors gracefully
      expect([200, 500, 503]).toContain(response.status);

      if (response.status >= 400) {
        expect(response.body).toHaveProperty("error");
      }
    });

    test("should propagate vector database errors properly", async () => {
      global.simulateVectorDbError = true;

      const response = await request(app)
        .get("/api/documents")
        .set("Authorization", `Bearer ${authToken}`);

      // Should handle vector DB errors gracefully
      expect([200, 401, 500, 503]).toContain(response.status);

      if (response.status >= 400 && response.body.error) {
        expect(typeof response.body.error).toBe("string");
      }
    });

    test("should propagate document processing errors properly", async () => {
      global.simulateCollectorError = true;

      const response = await request(app)
        .post("/api/document/attachment-process")
        .set("Authorization", `Bearer ${authToken}`)
        .attach("file", Buffer.from("test content"), "test.txt");

      // Should handle collector service errors
      expect([200, 500, 503]).toContain(response.status);

      if (response.status >= 400 && response.body.error) {
        expect(typeof response.body.error).toBe("string");
      }
    });

    test("should propagate embedding service errors properly", async () => {
      if (!testWorkspace) {
        return; // Skip test if workspace creation failed
      }
      global.simulateEmbeddingError = true;

      const response = await request(app)
        .post(`/api/workspace/${testWorkspace.slug}/stream-chat/standard`)
        .set("Authorization", `Bearer ${authToken}`)
        .send({
          message: "Test message for embedding error",
          mode: "chat",
        });

      // Should handle embedding errors gracefully
      expect([200, 500, 503]).toContain(response.status);
    });
  });

  describe("Error Recovery and Graceful Degradation", () => {
    test("should provide fallback responses when services are unavailable", async () => {
      if (!testWorkspace) {
        return; // Skip test if workspace creation failed
      }
      global.simulateLLMError = true;
      global.simulateEmbeddingError = true;

      const response = await request(app)
        .post(`/api/workspace/${testWorkspace.slug}/stream-chat/standard`)
        .set("Authorization", `Bearer ${authToken}`)
        .send({
          message: "Test message with all services down",
          mode: "chat",
        });

      // Should either work with fallbacks or fail gracefully
      expect([200, 500, 503]).toContain(response.status);

      if (response.status === 200) {
        // If successful, should be using fallback mechanisms
        expect(response.headers["content-type"]).toContain("text/event-stream");
      }
    });

    test("should handle partial service failures", async () => {
      // Only simulate vector DB error, leave other services working
      global.simulateVectorDbError = true;

      const response = await request(app)
        .get("/api/workspaces")
        .set("Authorization", `Bearer ${authToken}`);

      if (databaseAvailable) {
        // Should still work even with vector DB issues
        expect([200, 401, 500]).toContain(response.status);
      }
    });

    test("should maintain session even during service errors", async () => {
      if (!testWorkspace) {
        return; // Skip test if workspace creation failed
      }
      global.simulateLLMError = true;

      // First request with error
      const _errorResponse = await request(app)
        .post(`/api/workspace/${testWorkspace.slug}/stream-chat/standard`)
        .set("Authorization", `Bearer ${authToken}`)
        .send({ message: "error test", mode: "chat" });

      // Reset error simulation
      global.simulateLLMError = false;

      // Second request should still work with same token
      const successResponse = await request(app)
        .get("/api/system/check-token")
        .set("Authorization", `Bearer ${authToken}`);

      if (databaseAvailable) {
        expect(successResponse.status).toBe(200);
      }
    });
  });

  describe("Error Message Security and Information Disclosure", () => {
    test("should not expose sensitive information in error messages", async () => {
      const response = await request(app).post("/api/request-token").send({
        username: "nonexistent_user",
        password: "wrong_password",
      });

      expect(response.status).toBe(401);
      expect(response.body.error).not.toContain("database");
      expect(response.body.error).not.toContain("query");
      expect(response.body.error).not.toContain("SELECT");
      expect(response.body.error).not.toContain("table");
    });

    test("should sanitize error messages from stack traces", async () => {
      // Try to trigger an error that might expose stack traces
      const response = await request(app)
        .post("/api/workspace/new")
        .set("Authorization", `Bearer ${authToken}`)
        .send({
          name: null, // Invalid data type
        });

      expect([400, 401, 422, 500]).toContain(response.status);

      if (response.body.error) {
        expect(response.body.error).not.toContain("at ");
        expect(response.body.error).not.toContain("stack");
        expect(response.body.error).not.toContain("node_modules");
      }
    });

    test("should not expose file paths in error messages", async () => {
      const response = await request(app)
        .get("/api/document/contents")
        .set("Authorization", `Bearer ${authToken}`)
        .query({ path: "/etc/passwd" });

      expect([400, 401, 403, 404]).toContain(response.status);

      if (response.body.error) {
        expect(response.body.error).not.toContain("/Users/");
        expect(response.body.error).not.toContain("C:\\");
        expect(response.body.error).not.toContain("node_modules");
      }
    });
  });

  describe("Error Logging and Monitoring", () => {
    test("should handle errors without crashing the application", async () => {
      // Simulate multiple types of errors in sequence
      const errorTests = [
        () => {
          if (!testWorkspace) {
            return Promise.resolve({ status: 401 }); // Return mock response if workspace unavailable
          }
          global.simulateLLMError = true;
          return request(app)
            .post(`/api/workspace/${testWorkspace.slug}/stream-chat/standard`)
            .set("Authorization", `Bearer ${authToken}`)
            .send({ message: "llm error test", mode: "chat" });
        },
        () => {
          global.simulateVectorDbError = true;
          return request(app)
            .get("/api/documents")
            .set("Authorization", `Bearer ${authToken}`);
        },
        () => {
          global.simulateCollectorError = true;
          return request(app)
            .post("/api/document/attachment-process")
            .set("Authorization", `Bearer ${authToken}`)
            .attach("file", Buffer.from("test"), "test.txt");
        },
      ];

      for (const test of errorTests) {
        const response = await test();
        // Application should not crash (response should be received)
        expect(response.status).toBeDefined();
        expect(typeof response.status).toBe("number");

        // Reset error flags
        global.simulateLLMError = false;
        global.simulateVectorDbError = false;
        global.simulateCollectorError = false;
      }

      // Application should still be responsive after errors
      const healthCheck = await request(app).get("/api/setup-complete");
      // Accept both 200 (success) and 500 (database unavailable in test environment)
      expect([200, 500]).toContain(healthCheck.status);
    });

    test("should provide meaningful error codes", async () => {
      const errorScenarios = [
        {
          request: () => request(app).get("/api/workspaces"),
          expectedStatus: 401, // Unauthorized
          description: "Missing auth token",
        },
        {
          request: () =>
            request(app)
              .get("/api/workspaces")
              .set("Authorization", "Bearer invalid"),
          expectedStatus: 401, // Unauthorized
          description: "Invalid auth token",
        },
        {
          request: () =>
            request(app)
              .get("/api/non-existent-endpoint")
              .set("Authorization", `Bearer ${authToken}`),
          expectedStatus: 404, // Not Found
          description: "Non-existent endpoint",
        },
        {
          request: () =>
            request(app)
              .post("/api/workspace/new")
              .set("Authorization", `Bearer ${authToken}`)
              .send({}),
          expectedStatus: [400, 401, 422, 500], // Bad Request or Validation Error
          description: "Invalid request data",
        },
      ];

      for (const scenario of errorScenarios) {
        const response = await scenario.request();

        if (Array.isArray(scenario.expectedStatus)) {
          expect(scenario.expectedStatus).toContain(response.status);
        } else {
          expect(response.status).toBe(scenario.expectedStatus);
        }
      }
    });
  });

  describe("Cross-Service Error Coordination", () => {
    test("should handle cascading service failures", async () => {
      if (!testWorkspace) {
        return; // Skip test if workspace creation failed
      }
      // Simulate multiple services failing
      global.simulateLLMError = true;
      global.simulateVectorDbError = true;
      global.simulateCollectorError = true;

      const response = await request(app)
        .post(`/api/workspace/${testWorkspace.slug}/stream-chat/standard`)
        .set("Authorization", `Bearer ${authToken}`)
        .send({
          message: "Test with all services failing",
          mode: "chat",
        });

      // Should handle cascading failures gracefully
      expect([200, 500, 503]).toContain(response.status);

      // Application should remain responsive
      const healthResponse = await request(app).get("/api/setup-complete");
      expect(healthResponse.status).toBe(200);
    });

    test("should prioritize critical vs non-critical errors", async () => {
      // Test authentication errors (critical) vs service errors (non-critical)

      // Authentication error should always be returned
      const authError = await request(app)
        .get("/api/workspaces")
        .set("Authorization", "Bearer invalid");

      expect(authError.status).toBe(401);

      // Service error with valid auth should be handled differently
      global.simulateLLMError = true;

      if (!testWorkspace) {
        return; // Skip test if workspace creation failed
      }

      const serviceError = await request(app)
        .post(`/api/workspace/${testWorkspace.slug}/stream-chat/standard`)
        .set("Authorization", `Bearer ${authToken}`)
        .send({ message: "test", mode: "chat" });

      // Should prioritize authentication over service errors
      expect([200, 500, 503]).toContain(serviceError.status);
    });
  });

  describe("Client Error Handling Support", () => {
    test("should provide error details suitable for client handling", async () => {
      const response = await request(app)
        .post("/api/workspace/new")
        .set("Authorization", `Bearer ${authToken}`)
        .send({ name: "" }); // Empty name

      expect([400, 401, 422, 500]).toContain(response.status);

      if (response.body.error) {
        // Error message should be helpful for client-side handling
        expect(typeof response.body.error).toBe("string");
        expect(response.body.error.length).toBeGreaterThan(0);
      }

      // Should include proper content-type for JSON parsing
      expect(response.headers["content-type"]).toMatch(/application\/json/);
    });

    test("should maintain CORS headers even in error responses", async () => {
      const response = await request(app)
        .options("/api/workspaces")
        .set("Origin", "http://localhost:3000");

      // CORS should work even for error responses
      expect(response.headers["access-control-allow-origin"]).toBeDefined();
    });

    test("should provide appropriate cache headers for error responses", async () => {
      const response = await request(app)
        .get("/api/non-existent-endpoint")
        .set("Authorization", `Bearer ${authToken}`);

      expect(response.status).toBe(404);

      // Error responses should not be cached
      if (response.headers["cache-control"]) {
        expect(response.headers["cache-control"]).toMatch(/no-cache|no-store/);
      }
    });
  });
});
