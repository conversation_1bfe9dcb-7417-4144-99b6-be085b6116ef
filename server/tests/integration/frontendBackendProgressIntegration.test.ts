/**
 * Frontend-Backend Progress Integration Tests
 *
 * These tests verify that the new modular flow system (flowDispatcher.js) is being used
 * instead of the legacy flow files (mainDoc.js, noMainDoc.js, referenceFiles.js).
 */

import { streamChatWithWorkspaceCDB } from "../../utils/chats/streamCDB";

// Mock the new modular flow system
jest.mock("../../utils/chats/flowDispatcher", () => ({
  runFlow: jest.fn().mockResolvedValue({ success: true }),
}));

// Mock dependencies
jest.mock("../../models/systemSettings", () => ({
  SystemSettings: {
    getValueOrFallback: jest.fn().mockResolvedValue("true"),
    currentSettings: jest.fn().mockResolvedValue({}),
  },
}));

jest.mock("../../utils/helpers", () => ({
  getLLMProvider: jest.fn().mockReturnValue({
    getChatCompletion: jest.fn().mockResolvedValue("Mock response"),
  }),
}));

import { runFlow } from "../../utils/chats/flowDispatcher";

// Cast mocked function to jest.Mock for type safety
const mockRunFlow = runFlow as jest.Mock;

describe("Frontend-Backend Progress Integration Tests", () => {
  let mockRequest: any, mockResponse: any;

  beforeEach(() => {
    jest.clearAllMocks();

    mockRequest = {
      headers: { authorization: "Bearer test-token" },
      method: "POST",
      url: "/chat",
      body: {},
      on: jest.fn(),
    };

    mockResponse = {
      writeHead: jest.fn(),
      write: jest.fn(),
      end: jest.fn(),
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
      on: jest.fn(),
    };
  });

  describe("Modular Flow System Verification", () => {
    test("should use flowDispatcher for main flow", async () => {
      const cdbOptions: any = [
        "Contract Review",
        "Instructions",
        "main-doc.json",
        "main",
        undefined,
      ];

      const mockWorkspace: any = { id: 1, slug: "test-workspace" };
      const mockUser: any = { id: 1, role: "admin" };
      const mockThread: any = { id: 1, slug: "test-thread" };

      await streamChatWithWorkspaceCDB(
        mockRequest,
        mockResponse,
        mockWorkspace,
        "Test message",
        "chat",
        mockUser,
        mockThread,
        [],
        "test-chat-id",
        false,
        false,
        "",
        undefined,
        "default",
        false,
        undefined,
        false,
        cdbOptions
      );

      // Verify the new modular system (flowDispatcher) is being used
      expect(mockRunFlow).toHaveBeenCalled();

      // Verify the call contains expected parameters
      const callArgs: any = mockRunFlow.mock.calls[0][0];
      expect(callArgs.chatId).toBe("test-chat-id");
      expect(callArgs.mainDocName).toBe("main-doc"); // .json extension is stripped in streamCDB
    });

    test("should use flowDispatcher for noMain flow", async () => {
      const cdbOptions: any = [
        "Policy Analysis",
        "Instructions",
        undefined,
        "noMain",
        undefined,
      ];

      const mockWorkspace: any = { id: 1, slug: "policy-workspace" };
      const mockUser: any = { id: 1, role: "admin" };
      const mockThread: any = { id: 1, slug: "policy-thread" };

      await streamChatWithWorkspaceCDB(
        mockRequest,
        mockResponse,
        mockWorkspace,
        "Policy analysis message",
        "chat",
        mockUser,
        mockThread,
        [],
        "policy-chat-id",
        false,
        false,
        "",
        undefined,
        "default",
        false,
        undefined,
        false,
        cdbOptions
      );

      // Verify the new modular system (flowDispatcher) is being used
      expect(mockRunFlow).toHaveBeenCalled();

      // Verify the call contains expected parameters
      const callArgs: any = mockRunFlow.mock.calls[0][0];
      expect(callArgs.chatId).toBe("policy-chat-id");
      expect(callArgs.mainDocName).toBeNull();
    });

    test("should use flowDispatcher for referenceFiles flow", async () => {
      const cdbOptions: any = [
        "Compliance Review",
        "Instructions",
        undefined,
        "referenceFiles",
        ["ref1.json"],
      ];

      const mockWorkspace: any = { id: 1, slug: "compliance-workspace" };
      const mockUser: any = { id: 1, role: "admin" };
      const mockThread: any = { id: 1, slug: "compliance-thread" };

      await streamChatWithWorkspaceCDB(
        mockRequest,
        mockResponse,
        mockWorkspace,
        "Compliance review message",
        "chat",
        mockUser,
        mockThread,
        [],
        "compliance-chat-id",
        false,
        false,
        "",
        undefined,
        "default",
        false,
        undefined,
        false,
        cdbOptions
      );

      // Verify the new modular system (flowDispatcher) is being used
      expect(mockRunFlow).toHaveBeenCalled();

      // Verify the call contains expected parameters
      const callArgs: any = mockRunFlow.mock.calls[0][0];
      expect(callArgs.chatId).toBe("compliance-chat-id");
    });

    test("should handle flow execution errors gracefully", async () => {
      // Mock an error in the flow execution
      jest
        .mocked(mockRunFlow)
        .mockRejectedValueOnce(new Error("Flow execution failed"));

      const cdbOptions: any = [
        "Error Task",
        "Instructions",
        "main-doc.json",
        "main",
        undefined,
      ];

      const mockWorkspace: any = { id: 1, slug: "error-workspace" };
      const mockUser: any = { id: 1, role: "admin" };
      const mockThread: any = { id: 1, slug: "error-thread" };

      try {
        await streamChatWithWorkspaceCDB(
          mockRequest,
          mockResponse,
          mockWorkspace,
          "Error test message",
          "chat",
          mockUser,
          mockThread,
          [],
          "error-chat-id",
          false,
          false,
          "",
          undefined,
          "default",
          false,
          undefined,
          false,
          cdbOptions
        );
      } catch (error) {
        // Expected to throw
        expect(
          error instanceof Error ? error.message : String(error)
        ).toContain("Flow execution failed");
      }

      // Verify flow dispatcher was called
      expect(mockRunFlow).toHaveBeenCalled();
    });
  });

  describe("Legacy System Verification", () => {
    test("should not import legacy flow files", () => {
      // Verify that legacy flow files are not being imported
      // This test ensures the modular system is being used instead

      // These should throw errors if the legacy files are imported
      expect(() => require("../../utils/chats/mainDoc")).toThrow();
      expect(() => require("../../utils/chats/noMainDoc")).toThrow();
      expect(() => require("../../utils/chats/referenceFiles")).toThrow();
    });

    test("should use flowDispatcher as single entry point", () => {
      // Verify that flowDispatcher is the entry point for all flows
      expect(mockRunFlow).toBeDefined();
      expect(typeof mockRunFlow).toBe("function");
    });
  });
});
