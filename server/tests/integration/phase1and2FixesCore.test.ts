import { describe, it, expect } from "@jest/globals";
import * as fs from "fs";
import * as path from "path";

describe("Phase 1 and Phase 2 Core Fixes", () => {
  describe("Phase 1: Critical Production Blockers", () => {
    it("should have fixed hardcoded OpenAI models", () => {
      // Check that the OpenAiOptions component doesn't have hardcoded invalid models
      const componentPath = path.join(
        __dirname,
        "../../../../frontend/src/components/LLMSelection/OpenAiOptions/index.tsx"
      );

      if (fs.existsSync(componentPath)) {
        const content = fs.readFileSync(componentPath, "utf8");

        // Should not contain invalid models
        expect(content).not.toContain('value="o3"');
        expect(content).not.toContain('value="o4-mini"');
        expect(content).not.toContain('value="gpt-4.1"');
      }
    });

    it("should have real LLM integration in document editing", () => {
      // Check that editingLogic.ts has real implementation
      const editingLogicPath = path.join(
        __dirname,
        "../../../utils/documentEditing/editingLogic.ts"
      );

      if (fs.existsSync(editingLogicPath)) {
        const content = fs.readFileSync(editingLogicPath, "utf8");

        // Should not have placeholder implementation
        expect(content).not.toContain("Response for: ${prompt}");
        // Should have real LLM integration
        expect(content).toContain("robustLlmUtils");
      }
    });

    it("should have fixed collector constants file paths", () => {
      // Check that constants.ts uses .ts extensions
      const constantsPath = path.join(
        __dirname,
        "../../../../collector/utils/constants.ts"
      );

      if (fs.existsSync(constantsPath)) {
        const content = fs.readFileSync(constantsPath, "utf8");

        // Should use .ts extensions, not .js
        expect(content).toContain('./convert/asTxt.ts"');
        expect(content).toContain('./convert/asPDF/index.ts"');
        expect(content).not.toContain('./convert/asTxt.js"');
        expect(content).not.toContain('./convert/asPDF/index.js"');
      }
    });

    it("should have fixed Docker entry point", () => {
      // Check docker-entrypoint.sh
      const dockerEntryPath = path.join(
        __dirname,
        "../../../../docker/docker-entrypoint.sh"
      );

      if (fs.existsSync(dockerEntryPath)) {
        const content = fs.readFileSync(dockerEntryPath, "utf8");

        // Should use compiled output
        expect(content).toContain("node /app/server/dist/index.js");
        expect(content).not.toContain("node /app/server/index.js");
      }
    });
  });

  describe("Phase 2: Functionality Completion", () => {
    it("should have fixed collector resync module imports", () => {
      // Check resync/index.ts
      const resyncPath = path.join(
        __dirname,
        "../../../../collector/extensions/resync/index.ts"
      );

      if (fs.existsSync(resyncPath)) {
        const content = fs.readFileSync(resyncPath, "utf8");

        // Should have correct imports
        expect(content).toContain('require("../youtube")');
        expect(content).toContain('require("../confluence")');
        expect(content).toContain('require("../repo-loaders/github")');
      }
    });

    it("should have internationalized chat prompts", () => {
      // Check that prompts exist in multiple languages
      const languages = ["de", "en", "fr", "no", "pl", "rw", "sv"];

      languages.forEach((lang) => {
        const localePath = path.join(
          __dirname,
          `../../../locales/${lang}/server.ts`
        );

        if (fs.existsSync(localePath)) {
          const content = fs.readFileSync(localePath, "utf8");

          // Should have prompt translations
          expect(content).toContain("prompts:");
          expect(content).toContain("lqa:");
          expect(content).toContain("dd:");
        }
      });
    });

    it("should have working DocumentBuilder component", () => {
      // Check that the API endpoint is fixed
      const systemModelPath = path.join(
        __dirname,
        "../../../../frontend/src/models/system.ts"
      );

      if (fs.existsSync(systemModelPath)) {
        const content = fs.readFileSync(systemModelPath, "utf8");

        // Should have correct API endpoint
        expect(content).toContain("/api/system/cdb-prompts");
      }
    });
  });

  describe("Test Coverage", () => {
    it("should have comprehensive test files", () => {
      const testFiles = [
        "../unit/docker/dockerEntryPoint.test.ts",
        "../unit/i18n/internationalizedChatPrompts.test.ts",
      ];

      testFiles.forEach((testFile) => {
        const testPath = path.join(__dirname, testFile);
        if (!fs.existsSync(testPath)) {
          // Missing test file
        }
        expect(fs.existsSync(testPath)).toBeTruthy();
      });
    });
  });
});
