/**
 * Multi-User Test Suite: Conflict Resolution
 *
 * Tests conflict resolution mechanisms including:
 * - Last-write-wins strategies
 * - Operational transformation
 * - Version control and merging
 * - User intent preservation
 * - Automated conflict resolution
 * - Manual conflict resolution workflows
 */

import { setupAuthMocks, TEST_USERS } from "../helpers/authTestHelpers";
import { Workspace } from "../../models/workspace";
import { Document } from "../../models/documents";
import { User } from "../../models/user";

jest.setTimeout(30000);

describe("Multi-User Conflict Resolution", () => {
  // Test users for conflict scenarios
  const conflictUsers = {
    admin: { ...TEST_USERS.admin, id: 501 },
    editor1: { ...TEST_USERS.user, id: 502, username: "editor1" },
    editor2: { ...TEST_USERS.user, id: 503, username: "editor2" },
    reviewer: { ...TEST_USERS.manager, id: 504, username: "reviewer" },
    observer: { ...TEST_USERS.user, id: 505, username: "observer" },
  };

  let testWorkspaceId: number;
  let mockWorkspace: any;
  let mockDocument: any;

  // Conflict resolution engine
  class ConflictResolver {
    private conflicts: Map<string, any[]> = new Map();
    private resolutionStrategies: Map<string, (conflicts: any[]) => any> =
      new Map();

    constructor() {
      this.setupDefaultStrategies();
    }

    private setupDefaultStrategies() {
      // Last-write-wins strategy
      this.resolutionStrategies.set("last-write-wins", (conflicts) => {
        return conflicts.sort((a, b) => b.timestamp - a.timestamp)[0];
      });

      // Priority-based resolution (admin > manager > user)
      this.resolutionStrategies.set("priority-based", (conflicts) => {
        const priorityOrder = { admin: 3, manager: 2, default: 1 };
        return conflicts.sort((a, b) => {
          const priorityA =
            priorityOrder[a.userRole as keyof typeof priorityOrder] || 0;
          const priorityB =
            priorityOrder[b.userRole as keyof typeof priorityOrder] || 0;

          // If roles are equal, fall back to timestamp (last-write-wins)
          if (priorityA === priorityB) {
            return b.timestamp - a.timestamp;
          }

          return priorityB - priorityA;
        })[0];
      });

      // First-come-first-served
      this.resolutionStrategies.set("first-come-first-served", (conflicts) => {
        return conflicts.sort((a, b) => a.timestamp - b.timestamp)[0];
      });

      // Merge strategy for compatible changes
      this.resolutionStrategies.set("merge", (conflicts) => {
        const baseValue = conflicts[0].originalValue || {};
        const merged = { ...baseValue };

        conflicts.forEach((conflict) => {
          Object.assign(merged, conflict.changes);
        });

        return { ...conflicts[0], resolvedValue: merged, strategy: "merged" };
      });
    }

    addConflict(resourceId: string, conflict: any) {
      if (!this.conflicts.has(resourceId)) {
        this.conflicts.set(resourceId, []);
      }
      this.conflicts.get(resourceId)!.push(conflict);
    }

    resolveConflicts(
      resourceId: string,
      strategy: string = "last-write-wins"
    ): any {
      const conflicts = this.conflicts.get(resourceId) || [];

      // Check strategy validity first, before checking conflict count
      const resolver = this.resolutionStrategies.get(strategy);
      if (!resolver) {
        throw new Error(`Unknown resolution strategy: ${strategy}`);
      }

      if (conflicts.length === 0) return null;
      if (conflicts.length === 1) return conflicts[0];

      const resolution = resolver(conflicts);

      // Clear conflicts after resolution
      this.conflicts.delete(resourceId);

      return {
        ...resolution,
        conflictCount: conflicts.length,
        resolvedAt: Date.now(),
      };
    }

    getConflicts(resourceId?: string): Map<string, any[]> | any[] {
      if (resourceId) {
        return this.conflicts.get(resourceId) || [];
      }
      return this.conflicts;
    }

    hasConflicts(resourceId: string): boolean {
      const conflicts = this.conflicts.get(resourceId) || [];
      return conflicts.length > 1;
    }
  }

  // Operational transformation utilities
  class OperationalTransform {
    static transformInsert(op1: any, op2: any): any {
      if (op1.type !== "insert" || op2.type !== "insert") return op2;

      if (op2.position <= op1.position) {
        return op2; // No transformation needed
      }

      return {
        ...op2,
        position: op2.position + (op1.text?.length || 0),
      };
    }

    static transformDelete(op1: any, op2: any): any {
      if (op1.type !== "delete" || op2.type !== "delete") return op2;

      if (op2.position <= op1.position) {
        return op2; // No transformation needed
      }

      return {
        ...op2,
        position: Math.max(op2.position - (op1.length || 0), op1.position),
      };
    }

    static transformOperations(baseOps: any[], newOps: any[]): any[] {
      let transformedOps = [...newOps];

      for (const baseOp of baseOps) {
        transformedOps = transformedOps.map((op) => {
          if (baseOp.type === "insert") {
            return this.transformInsert(baseOp, op);
          } else if (baseOp.type === "delete") {
            return this.transformDelete(baseOp, op);
          }
          return op;
        });
      }

      return transformedOps;
    }

    static applyOperations(text: string, operations: any[]): string {
      let result = text;

      // Sort operations by position (reverse for deletions)
      const sortedOps = operations.sort((a, b) => {
        if (a.type === "delete" && b.type === "delete") {
          return b.position - a.position; // Reverse for deletions
        }
        return a.position - b.position;
      });

      for (const op of sortedOps) {
        if (op.type === "insert") {
          result =
            result.slice(0, op.position) + op.text + result.slice(op.position);
        } else if (op.type === "delete") {
          result =
            result.slice(0, op.position) +
            result.slice(op.position + (op.length || 0));
        }
      }

      return result;
    }
  }

  const conflictResolver = new ConflictResolver();

  beforeEach(() => {
    jest.clearAllMocks();

    setupAuthMocks({
      enableAuth: true,
      validTokens: {
        "admin-token": conflictUsers.admin,
        "editor1-token": conflictUsers.editor1,
        "editor2-token": conflictUsers.editor2,
        "reviewer-token": conflictUsers.reviewer,
        "observer-token": conflictUsers.observer,
      },
    });

    testWorkspaceId = 98765;
    mockWorkspace = {
      id: testWorkspaceId,
      name: "Conflict Resolution Test",
      slug: "conflict-test",
      user_id: conflictUsers.admin.id,
      createdAt: new Date(),
      lastUpdatedAt: new Date(),
    };

    mockDocument = {
      id: 1,
      docId: "conflict-doc-123",
      filename: "conflict-test.pdf",
      docpath: "/test/path/conflict-test.pdf",
      workspaceId: testWorkspaceId,
      metadata: JSON.stringify({ title: "Conflict Test Document" }),
      pinned: false,
      watched: false,
      starred: false,
      content: "Original document content",
      version: 1,
      createdAt: new Date(),
    };

    // Ensure all model methods are Jest mocks before using mockResolvedValue
    jest.spyOn(Workspace, "get").mockResolvedValue(mockWorkspace);
    jest.spyOn(Document, "get").mockResolvedValue(mockDocument);
    jest.spyOn(Document, "update").mockResolvedValue(mockDocument);
    jest.spyOn(User, "get").mockImplementation(async (_clause) => {
      // Provide a default mock implementation or return value
      return null;
    });
  });

  describe("Last-Write-Wins Strategy", () => {
    it("should resolve simple property conflicts using last-write-wins", async () => {
      const conflicts = [
        {
          resourceId: `workspace_${testWorkspaceId}`,
          userId: conflictUsers.editor1.id,
          userRole: conflictUsers.editor1.role,
          changes: { name: "Updated by Editor 1" },
          timestamp: Date.now() - 1000,
          originalValue: mockWorkspace,
        },
        {
          resourceId: `workspace_${testWorkspaceId}`,
          userId: conflictUsers.editor2.id,
          userRole: conflictUsers.editor2.role,
          changes: { name: "Updated by Editor 2" },
          timestamp: Date.now() - 500,
          originalValue: mockWorkspace,
        },
        {
          resourceId: `workspace_${testWorkspaceId}`,
          userId: conflictUsers.admin.id,
          userRole: conflictUsers.admin.role,
          changes: { name: "Updated by Admin" },
          timestamp: Date.now(),
          originalValue: mockWorkspace,
        },
      ];

      conflicts.forEach((conflict) => {
        conflictResolver.addConflict(conflict.resourceId, conflict);
      });

      const resolution = conflictResolver.resolveConflicts(
        `workspace_${testWorkspaceId}`,
        "last-write-wins"
      );

      expect(resolution.userId).toBe(conflictUsers.admin.id);
      expect(resolution.changes.name).toBe("Updated by Admin");
      expect(resolution.conflictCount).toBe(3);
    });

    it("should handle document content conflicts", async () => {
      const conflicts = [
        {
          resourceId: `document_${mockDocument.id}`,
          userId: conflictUsers.editor1.id,
          userRole: "default",
          changes: { content: "Content updated by Editor 1" },
          timestamp: Date.now() - 1000,
          originalValue: mockDocument,
        },
        {
          resourceId: `document_${mockDocument.id}`,
          userId: conflictUsers.editor2.id,
          userRole: "default",
          changes: { content: "Content updated by Editor 2" },
          timestamp: Date.now(),
          originalValue: mockDocument,
        },
      ];

      conflicts.forEach((conflict) => {
        conflictResolver.addConflict(conflict.resourceId, conflict);
      });

      const resolution = conflictResolver.resolveConflicts(
        `document_${mockDocument.id}`,
        "last-write-wins"
      );

      // Last write wins should resolve to editor2
      expect(resolution.userId).toBe(conflictUsers.editor2.id);
      expect(resolution.changes.content).toBe("Content updated by Editor 2");
    });

    it("should preserve metadata during conflict resolution", async () => {
      const conflicts = [
        {
          resourceId: `document_${mockDocument.id}`,
          userId: conflictUsers.editor1.id,
          userRole: conflictUsers.editor1.role,
          changes: { pinned: true, metadata: { author: "Editor 1" } },
          timestamp: Date.now() - 1000,
          originalValue: mockDocument,
        },
        {
          resourceId: `document_${mockDocument.id}`,
          userId: conflictUsers.editor2.id,
          userRole: conflictUsers.editor2.role,
          changes: { starred: true, metadata: { category: "Important" } },
          timestamp: Date.now(),
          originalValue: mockDocument,
        },
      ];

      conflicts.forEach((conflict) => {
        conflictResolver.addConflict(conflict.resourceId, conflict);
      });

      const resolution = conflictResolver.resolveConflicts(
        `document_${mockDocument.id}`,
        "last-write-wins"
      );

      expect(resolution.userId).toBe(conflictUsers.editor2.id);
      expect(resolution.changes.starred).toBe(true);
      expect(resolution.timestamp).toBeGreaterThan(conflicts[0].timestamp);
    });
  });

  describe("Priority-Based Resolution", () => {
    it("should resolve conflicts based on user role priority", async () => {
      const conflicts = [
        {
          resourceId: `workspace_${testWorkspaceId}`,
          userId: conflictUsers.editor1.id,
          userRole: conflictUsers.editor1.role, // default
          changes: { name: "Updated by Regular User" },
          timestamp: Date.now(),
          originalValue: mockWorkspace,
        },
        {
          resourceId: `workspace_${testWorkspaceId}`,
          userId: conflictUsers.reviewer.id,
          userRole: conflictUsers.reviewer.role, // manager
          changes: { name: "Updated by Manager" },
          timestamp: Date.now() - 1000, // Earlier timestamp
          originalValue: mockWorkspace,
        },
        {
          resourceId: `workspace_${testWorkspaceId}`,
          userId: conflictUsers.admin.id,
          userRole: conflictUsers.admin.role, // admin
          changes: { name: "Updated by Admin" },
          timestamp: Date.now() - 2000, // Earliest timestamp
          originalValue: mockWorkspace,
        },
      ];

      conflicts.forEach((conflict) => {
        conflictResolver.addConflict(conflict.resourceId, conflict);
      });

      const resolution = conflictResolver.resolveConflicts(
        `workspace_${testWorkspaceId}`,
        "priority-based"
      );

      // Admin should win despite earliest timestamp
      expect(resolution.userId).toBe(conflictUsers.admin.id);
      expect(resolution.changes.name).toBe("Updated by Admin");
    });

    it("should fall back to timestamp when roles are equal", async () => {
      const conflicts = [
        {
          resourceId: `document_${mockDocument.id}`,
          userId: conflictUsers.editor1.id,
          userRole: conflictUsers.editor1.role, // default
          changes: { content: "Content by Editor 1" },
          timestamp: Date.now() - 1000,
          originalValue: mockDocument,
        },
        {
          resourceId: `document_${mockDocument.id}`,
          userId: conflictUsers.editor2.id,
          userRole: conflictUsers.editor2.role, // default
          changes: { content: "Content by Editor 2" },
          timestamp: Date.now(),
          originalValue: mockDocument,
        },
      ];

      conflicts.forEach((conflict) => {
        conflictResolver.addConflict(conflict.resourceId, conflict);
      });

      const resolution = conflictResolver.resolveConflicts(
        `document_${mockDocument.id}`,
        "priority-based"
      );

      // Should fall back to last-write-wins since roles are equal
      expect(resolution.userId).toBe(conflictUsers.editor2.id);
      expect(resolution.changes.content).toBe("Content by Editor 2");
    });

    it("should handle admin override scenarios", async () => {
      // Simulate scenario where admin needs to override user changes
      const userChange = {
        resourceId: `workspace_${testWorkspaceId}`,
        userId: conflictUsers.editor1.id,
        userRole: conflictUsers.editor1.role,
        changes: { settings: { allowPublicAccess: true } },
        timestamp: Date.now(),
        originalValue: mockWorkspace,
      };

      const adminOverride = {
        resourceId: `workspace_${testWorkspaceId}`,
        userId: conflictUsers.admin.id,
        userRole: conflictUsers.admin.role,
        changes: { settings: { allowPublicAccess: false } },
        timestamp: Date.now() + 1000,
        originalValue: mockWorkspace,
        isOverride: true,
      };

      conflictResolver.addConflict(userChange.resourceId, userChange);
      conflictResolver.addConflict(adminOverride.resourceId, adminOverride);

      const resolution = conflictResolver.resolveConflicts(
        `workspace_${testWorkspaceId}`,
        "priority-based"
      );

      expect(resolution.userId).toBe(conflictUsers.admin.id);
      expect(resolution.changes.settings.allowPublicAccess).toBe(false);
    });
  });

  describe("Operational Transformation", () => {
    it("should transform concurrent text insertions", async () => {
      const originalText = "Hello World";

      const operation1 = {
        type: "insert",
        position: 6, // After "Hello "
        text: "Beautiful ",
        userId: conflictUsers.editor1.id,
      };

      const operation2 = {
        type: "insert",
        position: 0, // Beginning of text
        text: "Hi, ",
        userId: conflictUsers.editor2.id,
      };

      // Transform operation1 based on operation2
      const transformedOp1 = OperationalTransform.transformInsert(
        operation2,
        operation1
      );

      // Apply operations in order
      let result = OperationalTransform.applyOperations(originalText, [
        operation2,
      ]);
      result = OperationalTransform.applyOperations(result, [transformedOp1]);

      expect(result).toBe("Hi, Hello Beautiful World");
    });

    it("should transform concurrent text deletions", async () => {
      const originalText = "Hello Beautiful World";

      const operation1 = {
        type: "delete",
        position: 6, // "Beautiful "
        length: 10,
        userId: conflictUsers.editor1.id,
      };

      const operation2 = {
        type: "delete",
        position: 0, // "Hello "
        length: 6,
        userId: conflictUsers.editor2.id,
      };

      // Transform operation1 based on operation2
      const transformedOp1 = OperationalTransform.transformDelete(
        operation2,
        operation1
      );

      // Apply operations
      let result = OperationalTransform.applyOperations(originalText, [
        operation2,
      ]);
      result = OperationalTransform.applyOperations(result, [transformedOp1]);

      expect(result).toBe("World");
    });

    it("should handle mixed insert/delete operations", async () => {
      const originalText = "The quick brown fox";

      const operations = [
        {
          type: "insert",
          position: 4,
          text: "very ",
          userId: conflictUsers.editor1.id,
        },
        {
          type: "delete",
          position: 10,
          length: 6,
          userId: conflictUsers.editor2.id,
        }, // "brown "
        {
          type: "insert",
          position: 19,
          text: " jumps",
          userId: conflictUsers.editor1.id,
        },
      ];

      const result = OperationalTransform.applyOperations(
        originalText,
        operations
      );

      // Should handle the transformations correctly
      expect(result).toContain("very");
      expect(result).not.toContain("brown");
      expect(result).toContain("jumps");
    });

    it("should preserve user intent in complex scenarios", async () => {
      const documentContent =
        "# Document Title\n\nContent paragraph 1\n\nContent paragraph 2";

      const userOperations = [
        {
          user: conflictUsers.editor1.id,
          operations: [
            { type: "insert", position: 17, text: "\n\n## Section Header" },
          ],
        },
        {
          user: conflictUsers.editor2.id,
          operations: [
            { type: "insert", position: 0, text: "# Legal Document\n\n" },
            { type: "delete", position: 0, length: 17 }, // Remove original title
          ],
        },
      ];

      // Apply user1 operations first
      let result = documentContent;
      for (const userOp of userOperations[0].operations) {
        result = OperationalTransform.applyOperations(result, [userOp]);
      }

      // Transform and apply user2 operations
      const transformedOps = OperationalTransform.transformOperations(
        userOperations[0].operations,
        userOperations[1].operations
      );

      for (const op of transformedOps) {
        result = OperationalTransform.applyOperations(result, [op]);
      }

      // After operational transformation, verify core content is preserved
      expect(result).toContain("Document Title");
      expect(result).toContain("Section Header");
      expect(result).toContain("Content paragraph");
    });
  });

  describe("Version Control and Merging", () => {
    it("should create document versions for conflict tracking", async () => {
      const documentVersions: Array<{
        version: number;
        content: string;
        author: number;
        timestamp: number;
      }> = [];

      // Simulate version tracking directly instead of through API calls
      const sequentialUpdates = [
        { content: "Version 1 content", userId: conflictUsers.editor1.id },
        { content: "Version 2 content", userId: conflictUsers.editor2.id },
        { content: "Version 3 content", userId: conflictUsers.admin.id },
      ];

      let currentVersion = 1;
      for (const update of sequentialUpdates) {
        const newVersion = {
          version: ++currentVersion,
          content: update.content,
          author: update.userId,
          timestamp: Date.now(),
        };
        documentVersions.push(newVersion);
      }

      expect(documentVersions).toHaveLength(3);
      expect(documentVersions[0].author).toBe(conflictUsers.editor1.id);
      expect(documentVersions[1].author).toBe(conflictUsers.editor2.id);
      expect(documentVersions[2].author).toBe(conflictUsers.admin.id);

      // Versions should be sequential
      for (let i = 1; i < documentVersions.length; i++) {
        expect(documentVersions[i].version).toBe(
          documentVersions[i - 1].version + 1
        );
      }
    });

    it("should merge compatible changes automatically", async () => {
      const baseDocument = {
        ...mockDocument,
        metadata: JSON.stringify({
          title: "Original Title",
          tags: ["legal", "draft"],
          priority: "medium",
        }),
      };

      const conflicts = [
        {
          resourceId: `document_${mockDocument.id}`,
          userId: conflictUsers.editor1.id,
          userRole: conflictUsers.editor1.role,
          changes: {
            metadata: JSON.stringify({
              title: "Updated Title",
              tags: ["legal", "draft"],
              priority: "medium",
            }),
          },
          timestamp: Date.now() - 500,
          originalValue: baseDocument,
        },
        {
          resourceId: `document_${mockDocument.id}`,
          userId: conflictUsers.editor2.id,
          userRole: conflictUsers.editor2.role,
          changes: {
            metadata: JSON.stringify({
              title: "Original Title",
              tags: ["legal", "draft", "reviewed"],
              priority: "high",
            }),
          },
          timestamp: Date.now(),
          originalValue: baseDocument,
        },
      ];

      conflicts.forEach((conflict) => {
        conflictResolver.addConflict(conflict.resourceId, conflict);
      });

      const resolution = conflictResolver.resolveConflicts(
        `document_${mockDocument.id}`,
        "merge"
      );

      expect(resolution.strategy).toBe("merged");

      // Merged metadata should contain the last changes (since merge strategy does simple Object.assign)
      const mergedMetadata = JSON.parse(resolution.resolvedValue.metadata);
      expect(mergedMetadata.title).toBe("Original Title"); // From editor2 (last change)
      expect(mergedMetadata.tags).toContain("reviewed"); // From editor2
      expect(mergedMetadata.priority).toBe("high"); // From editor2
    });

    it("should handle three-way merges", async () => {
      const _baseVersion = {
        content: "Line 1\nLine 2\nLine 3",
        version: 1,
        timestamp: Date.now() - 3000,
      };

      const branch1Changes = {
        content: "Line 1 Modified\nLine 2\nLine 3",
        version: 2,
        author: conflictUsers.editor1.id,
        timestamp: Date.now() - 2000,
      };

      const branch2Changes = {
        content: "Line 1\nLine 2\nLine 3 Modified\nLine 4 Added",
        version: 2, // Same version as branch1 (conflict)
        author: conflictUsers.editor2.id,
        timestamp: Date.now() - 1000,
      };

      // Simulate three-way merge
      const mergeResult = {
        content: "Line 1 Modified\nLine 2\nLine 3 Modified\nLine 4 Added",
        version: 3,
        mergedFrom: [branch1Changes.version, branch2Changes.version],
        authors: [branch1Changes.author, branch2Changes.author],
        timestamp: Date.now(),
      };

      // Both changes should be preserved in the merge
      expect(mergeResult.content).toContain("Line 1 Modified");
      expect(mergeResult.content).toContain("Line 3 Modified");
      expect(mergeResult.content).toContain("Line 4 Added");
      expect(mergeResult.mergedFrom).toHaveLength(2);
    });

    it("should detect unmergeable conflicts", async () => {
      const conflicts = [
        {
          resourceId: `document_${mockDocument.id}`,
          userId: conflictUsers.editor1.id,
          userRole: conflictUsers.editor1.role,
          changes: { content: "Completely different content A" },
          timestamp: Date.now() - 500,
          originalValue: mockDocument,
        },
        {
          resourceId: `document_${mockDocument.id}`,
          userId: conflictUsers.editor2.id,
          userRole: conflictUsers.editor2.role,
          changes: { content: "Completely different content B" },
          timestamp: Date.now(),
          originalValue: mockDocument,
        },
      ];

      conflicts.forEach((conflict) => {
        conflictResolver.addConflict(conflict.resourceId, conflict);
      });

      // Try to merge - should fail due to incompatible changes
      const attemptMerge = () => {
        const resolution = conflictResolver.resolveConflicts(
          `document_${mockDocument.id}`,
          "merge"
        );

        // Check if merge strategy produced a meaningful result
        if (resolution.strategy === "merged") {
          const mergedContent = resolution.resolvedValue.content;
          return (
            mergedContent.includes("content A") &&
            mergedContent.includes("content B")
          );
        }

        return false;
      };

      // For completely different content, merge might not be meaningful
      // Should fall back to another strategy
      const mergeSuccessful = attemptMerge();
      expect(typeof mergeSuccessful).toBe("boolean");
    });
  });

  describe("Automated Conflict Resolution", () => {
    it("should automatically resolve non-conflicting parallel changes", async () => {
      const _parallelChanges = [
        {
          resourceId: `workspace_${testWorkspaceId}`,
          userId: conflictUsers.editor1.id,
          userRole: conflictUsers.editor1.role,
          changes: { description: "Updated description" },
          timestamp: Date.now(),
          field: "description",
        },
        {
          resourceId: `workspace_${testWorkspaceId}`,
          userId: conflictUsers.editor2.id,
          userRole: conflictUsers.editor2.role,
          changes: { tags: ["updated", "collaborative"] },
          timestamp: Date.now() + 100,
          field: "tags",
        },
      ];

      // These changes don't conflict - different fields
      const autoResolution = {
        resourceId: `workspace_${testWorkspaceId}`,
        resolvedValue: {
          ...mockWorkspace,
          description: "Updated description",
          tags: ["updated", "collaborative"],
        },
        strategy: "auto-merge",
        conflictCount: 0, // No actual conflicts
      };

      expect(autoResolution.strategy).toBe("auto-merge");
      expect(autoResolution.resolvedValue.description).toBe(
        "Updated description"
      );
      expect(autoResolution.resolvedValue.tags).toContain("collaborative");
    });

    it("should auto-resolve based on change types", async () => {
      const additiveCchanges = [
        {
          resourceId: `document_${mockDocument.id}`,
          userId: conflictUsers.editor1.id,
          changeType: "add",
          changes: { tags: ["important"] },
          timestamp: Date.now(),
        },
        {
          resourceId: `document_${mockDocument.id}`,
          userId: conflictUsers.editor2.id,
          changeType: "add",
          changes: { tags: ["reviewed"] },
          timestamp: Date.now() + 100,
        },
      ];

      // Additive changes can be auto-merged
      const mergedTags = [
        ...(additiveCchanges[0].changes.tags || []),
        ...(additiveCchanges[1].changes.tags || []),
      ];

      expect(mergedTags).toContain("important");
      expect(mergedTags).toContain("reviewed");
      expect(mergedTags).toHaveLength(2);
    });

    it("should handle permissions-based auto-resolution", async () => {
      const permissionConflict = [
        {
          resourceId: `workspace_${testWorkspaceId}`,
          userId: conflictUsers.editor1.id,
          userRole: "default",
          changes: { permissions: { allowEdit: true } },
          timestamp: Date.now(),
          requiresApproval: true,
        },
        {
          resourceId: `workspace_${testWorkspaceId}`,
          userId: conflictUsers.admin.id,
          userRole: "admin",
          changes: { permissions: { allowEdit: false } },
          timestamp: Date.now() + 100,
          requiresApproval: false,
        },
      ];

      // Admin changes should auto-override user changes for permissions
      const autoResolution = permissionConflict.find(
        (change) => change.userRole === "admin"
      );

      expect(autoResolution?.changes.permissions.allowEdit).toBe(false);
      expect(autoResolution?.requiresApproval).toBe(false);
    });

    it("should queue conflicts requiring manual resolution", async () => {
      const manualResolutionQueue: any[] = [];

      const complexConflict = {
        resourceId: `document_${mockDocument.id}`,
        conflictType: "content_overlap",
        users: [conflictUsers.editor1.id, conflictUsers.editor2.id],
        changes: [
          {
            content: "Paragraph 1 version A\nParagraph 2",
            userId: conflictUsers.editor1.id,
          },
          {
            content: "Paragraph 1 version B\nParagraph 2",
            userId: conflictUsers.editor2.id,
          },
        ],
        requiresManualResolution: true,
        createdAt: Date.now(),
      };

      manualResolutionQueue.push(complexConflict);

      expect(manualResolutionQueue).toHaveLength(1);
      expect(complexConflict.requiresManualResolution).toBe(true);
      expect(complexConflict.users).toHaveLength(2);
    });
  });

  describe("Manual Conflict Resolution Workflows", () => {
    it("should provide conflict visualization for manual resolution", async () => {
      const conflictVisualization = {
        resourceId: `document_${mockDocument.id}`,
        baseVersion: {
          content: "Original line 1\nOriginal line 2\nOriginal line 3",
          version: 1,
          author: conflictUsers.admin.id,
        },
        conflicts: [
          {
            user: conflictUsers.editor1,
            changes: {
              content: "Modified line 1\nOriginal line 2\nOriginal line 3",
              version: 2,
            },
            timestamp: Date.now() - 1000,
          },
          {
            user: conflictUsers.editor2,
            changes: {
              content: "Original line 1\nModified line 2\nAdded line 4",
              version: 2,
            },
            timestamp: Date.now() - 500,
          },
        ],
        diffView: {
          line1: {
            base: "Original line 1",
            user1: "Modified line 1",
            user2: "Original line 1",
            conflict: true,
          },
          line2: {
            base: "Original line 2",
            user1: "Original line 2",
            user2: "Modified line 2",
            conflict: true,
          },
          line3: {
            base: "Original line 3",
            user1: "Original line 3",
            user2: "Original line 3",
            conflict: false,
          },
          line4: {
            base: null,
            user1: null,
            user2: "Added line 4",
            conflict: false,
          },
        },
      };

      expect(conflictVisualization.conflicts).toHaveLength(2);
      expect(conflictVisualization.diffView.line1.conflict).toBe(true);
      expect(conflictVisualization.diffView.line4.user2).toBe("Added line 4");
    });

    it("should allow reviewers to make resolution decisions", async () => {
      const resolutionDecisions = {
        resourceId: `document_${mockDocument.id}`,
        reviewer: conflictUsers.reviewer,
        decisions: [
          {
            conflictId: "line1_conflict",
            resolution: "accept_user1", // Use editor1's version
            rationale: "Better wording",
          },
          {
            conflictId: "line2_conflict",
            resolution: "accept_user2", // Use editor2's version
            rationale: "More accurate information",
          },
          {
            conflictId: "line4_addition",
            resolution: "accept", // Accept the addition
            rationale: "Valuable addition",
          },
        ],
        finalContent:
          "Modified line 1\nModified line 2\nOriginal line 3\nAdded line 4",
        resolvedAt: Date.now(),
        resolvedBy: conflictUsers.reviewer.id,
      };

      expect(resolutionDecisions.decisions).toHaveLength(3);
      expect(resolutionDecisions.decisions[0].resolution).toBe("accept_user1");
      expect(resolutionDecisions.finalContent).toContain("Modified line 1");
      expect(resolutionDecisions.finalContent).toContain("Modified line 2");
      expect(resolutionDecisions.finalContent).toContain("Added line 4");
    });

    it("should support collaborative resolution sessions", async () => {
      const collaborativeSession = {
        sessionId: `resolve_${Date.now()}`,
        resourceId: `document_${mockDocument.id}`,
        participants: [
          { user: conflictUsers.editor1, role: "contributor" },
          { user: conflictUsers.editor2, role: "contributor" },
          { user: conflictUsers.reviewer, role: "mediator" },
        ],
        chat: [
          {
            user: conflictUsers.reviewer.id,
            message: "Let's discuss the conflicts in line 1",
            timestamp: Date.now() - 300,
          },
          {
            user: conflictUsers.editor1.id,
            message: "I think my version is clearer",
            timestamp: Date.now() - 200,
          },
          {
            user: conflictUsers.editor2.id,
            message: "I agree, let's go with editor1's version",
            timestamp: Date.now() - 100,
          },
        ],
        resolutionProgress: {
          totalConflicts: 3,
          resolvedConflicts: 1,
          status: "in_progress",
        },
        startedAt: Date.now() - 600,
        expectedCompletion: Date.now() + 1800, // 30 minutes from now
      };

      expect(collaborativeSession.participants).toHaveLength(3);
      expect(collaborativeSession.chat).toHaveLength(3);
      expect(collaborativeSession.resolutionProgress.resolvedConflicts).toBe(1);
      expect(collaborativeSession.resolutionProgress.status).toBe(
        "in_progress"
      );
    });

    it("should provide resolution templates for common scenarios", async () => {
      const resolutionTemplates = {
        content_merge: {
          name: "Content Merge",
          description: "Merge non-conflicting content changes",
          strategy: "auto-merge",
          applicableWhen: ["different_sections", "additive_changes"],
        },
        expert_review: {
          name: "Expert Review",
          description: "Require subject matter expert to resolve",
          strategy: "manual-review",
          applicableWhen: ["technical_content", "legal_implications"],
        },
        democratic_vote: {
          name: "Democratic Vote",
          description: "Let team members vote on preferred version",
          strategy: "voting",
          applicableWhen: ["subjective_content", "style_preferences"],
        },
        admin_override: {
          name: "Admin Override",
          description: "Admin decision takes precedence",
          strategy: "priority-based",
          applicableWhen: ["policy_conflicts", "security_concerns"],
        },
      };

      expect(Object.keys(resolutionTemplates)).toHaveLength(4);
      expect(resolutionTemplates.content_merge.strategy).toBe("auto-merge");
      expect(resolutionTemplates.expert_review.strategy).toBe("manual-review");
      expect(resolutionTemplates.admin_override.strategy).toBe(
        "priority-based"
      );
    });

    it("should maintain audit trail for conflict resolutions", async () => {
      const resolutionAuditTrail = {
        resourceId: `document_${mockDocument.id}`,
        conflictId: `conflict_${Date.now()}`,
        timeline: [
          {
            event: "conflict_detected",
            timestamp: Date.now() - 1000,
            users: [conflictUsers.editor1.id, conflictUsers.editor2.id],
            details: "Conflicting changes to document content",
          },
          {
            event: "notification_sent",
            timestamp: Date.now() - 900,
            recipients: [conflictUsers.reviewer.id],
            details: "Conflict requires manual resolution",
          },
          {
            event: "resolution_started",
            timestamp: Date.now() - 600,
            user: conflictUsers.reviewer.id,
            details: "Reviewer began conflict resolution process",
          },
          {
            event: "stakeholder_consulted",
            timestamp: Date.now() - 300,
            participants: [conflictUsers.editor1.id, conflictUsers.editor2.id],
            details: "Collaborative discussion initiated",
          },
          {
            event: "resolution_completed",
            timestamp: Date.now(),
            resolver: conflictUsers.reviewer.id,
            strategy: "manual_merge",
            details: "Conflict resolved through manual merge",
          },
        ],
        finalOutcome: {
          strategy: "manual_merge",
          acceptedChanges: [
            "editor1_line1",
            "editor2_line2",
            "editor2_addition",
          ],
          rejectedChanges: ["editor2_line1", "editor1_line2"],
          customChanges: [],
        },
      };

      expect(resolutionAuditTrail.timeline).toHaveLength(5);
      expect(resolutionAuditTrail.timeline[0].event).toBe("conflict_detected");
      expect(resolutionAuditTrail.timeline[4].event).toBe(
        "resolution_completed"
      );
      expect(resolutionAuditTrail.finalOutcome.acceptedChanges).toHaveLength(3);
    });
  });

  describe("Edge Cases and Error Handling", () => {
    it("should handle circular conflict dependencies", async () => {
      // Document A depends on B, B depends on C, C depends on A
      const circularConflicts = [
        {
          resourceId: "doc_A",
          dependsOn: "doc_B",
          changes: { reference: "Updated reference to B" },
        },
        {
          resourceId: "doc_B",
          dependsOn: "doc_C",
          changes: { reference: "Updated reference to C" },
        },
        {
          resourceId: "doc_C",
          dependsOn: "doc_A",
          changes: { reference: "Updated reference to A" },
        },
      ];

      // Detection algorithm for circular dependencies
      const detectCircularDependency = (conflicts: any[]): boolean => {
        const visited = new Set();
        const recursionStack = new Set();

        const hasCycle = (resourceId: string): boolean => {
          if (recursionStack.has(resourceId)) return true;
          if (visited.has(resourceId)) return false;

          visited.add(resourceId);
          recursionStack.add(resourceId);

          const conflict = conflicts.find((c) => c.resourceId === resourceId);
          if (conflict?.dependsOn) {
            if (hasCycle(conflict.dependsOn)) return true;
          }

          recursionStack.delete(resourceId);
          return false;
        };

        return conflicts.some((conflict) => hasCycle(conflict.resourceId));
      };

      const hasCircularDependency = detectCircularDependency(circularConflicts);
      expect(hasCircularDependency).toBe(true);
    });

    it("should handle malformed conflict data", async () => {
      const malformedConflicts = [
        {
          resourceId: null, // Invalid
          userId: conflictUsers.editor1.id,
          changes: { name: "Test" },
        },
        {
          resourceId: `doc_${mockDocument.id}`,
          userId: undefined, // Invalid
          changes: { content: "Test content" },
        },
        {
          resourceId: `doc_${mockDocument.id}`,
          userId: conflictUsers.editor2.id,
          changes: null, // Invalid
        },
      ];

      const validConflicts = (malformedConflicts as any[]).filter(
        (conflict) =>
          conflict.resourceId &&
          conflict.userId &&
          conflict.changes &&
          typeof conflict.changes === "object"
      );

      expect(validConflicts).toHaveLength(0); // All are malformed
    });

    it("should handle resolution strategy failures", async () => {
      const testConflict = {
        resourceId: `doc_${mockDocument.id}`,
        userId: conflictUsers.editor1.id,
        userRole: conflictUsers.editor1.role,
        changes: { content: "Test content" },
        timestamp: Date.now(),
        originalValue: mockDocument,
      };

      conflictResolver.addConflict(testConflict.resourceId, testConflict);

      // Try unknown strategy
      expect(() => {
        conflictResolver.resolveConflicts(
          testConflict.resourceId,
          "unknown-strategy"
        );
      }).toThrow("Unknown resolution strategy: unknown-strategy");
    });

    it("should handle timeout scenarios in resolution", async () => {
      const longRunningResolution = new Promise((resolve, _reject) => {
        setTimeout(() => {
          resolve({
            resourceId: `doc_${mockDocument.id}`,
            strategy: "complex-merge",
            resolved: true,
            duration: 5000,
          });
        }, 5000); // 5 second resolution
      });

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error("Resolution timeout"));
        }, 2000); // 2 second timeout
      });

      try {
        await Promise.race([longRunningResolution, timeoutPromise]);
        throw new Error("Should have timed out");
      } catch (error) {
        expect((error as Error).message).toBe("Resolution timeout");
      }
    });
  });
});
