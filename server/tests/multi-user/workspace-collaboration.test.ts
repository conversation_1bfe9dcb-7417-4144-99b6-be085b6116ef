/**
 * Multi-User Test Suite: Workspace Collaboration
 *
 * Tests concurrent workspace operations between multiple users including:
 * - Multiple users accessing the same workspace
 * - Concurrent document operations
 * - Real-time chat sessions
 * - Permission boundary testing
 * - Resource contention handling
 */

// Mock SystemSettings before other imports
jest.mock("../../models/systemSettings", () => ({
  default: {
    get: jest.fn().mockResolvedValue({}),
    where: jest.fn().mockResolvedValue([]),
    getValueOrFallback: jest.fn(
      (key: string, defaultValue: any) => defaultValue
    ),
    supportedLanguages: [
      "en",
      "es",
      "fr",
      "de",
      "it",
      "pt",
      "ru",
      "zh",
      "ja",
      "ko",
    ],
    currentLanguage: jest.fn().mockResolvedValue("en"),
    getMultiUserMode: jest.fn().mockResolvedValue(true),
    getLLMProvider: jest.fn().mockResolvedValue("openai"),
    getEmbeddingProvider: jest.fn().mockResolvedValue("openai"),
    vectorDBPreferenceKeys: [],
    llmPreferenceKeys: [],
    embeddingPreferenceKeys: [],
  },
}));

// Mock the models before importing
jest.mock("../../models/workspace");
jest.mock("../../models/workspaceUsers");
jest.mock("../../models/workspaceChats");
jest.mock("../../models/documents");
jest.mock("../../models/user");

import { Workspace } from "../../models/workspace";
import { WorkspaceUser } from "../../models/workspaceUsers";
import { WorkspaceChats } from "../../models/workspaceChats";
import { Document } from "../../models/documents";
import { User } from "../../models/user";

jest.setTimeout(45000); // Extended timeout for multi-user scenarios in test environment

describe("Multi-User Workspace Collaboration", () => {
  // Test users for collaboration scenarios
  const collaborators = {
    admin: { id: 101, username: "admin", role: "admin" },
    manager: { id: 102, username: "manager", role: "manager" },
    user1: { id: 103, username: "collaborator1", role: "default" },
    user2: { id: 104, username: "collaborator2", role: "default" },
    user3: { id: 105, username: "collaborator3", role: "default" },
    outsider: { id: 106, username: "outsider", role: "default" },
  };

  let testWorkspaceId: number;
  let mockWorkspace: any;
  let mockDocument: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock workspace and document data
    testWorkspaceId = 12345;
    mockWorkspace = {
      id: testWorkspaceId,
      name: "Collaboration Test Workspace",
      slug: "collab-test",
      user_id: collaborators.admin.id,
      createdAt: new Date(),
      lastUpdatedAt: new Date(),
    };

    mockDocument = {
      id: 1,
      docId: "test-doc-123",
      filename: "collaboration-test.pdf",
      docpath: "/test/path/collaboration-test.pdf",
      workspaceId: testWorkspaceId,
      metadata: JSON.stringify({ title: "Collaboration Test Document" }),
      pinned: false,
      watched: false,
      starred: false,
      createdAt: new Date(),
    };

    // Mock model methods
    jest.mocked(Workspace).get = jest.fn().mockResolvedValue(mockWorkspace);
    jest.mocked(Workspace).userHasAccess = jest
      .fn()
      .mockImplementation(async (userId, workspaceId) => {
        if (userId === collaborators.outsider.id) return false;
        return workspaceId === testWorkspaceId;
      });
    jest.mocked(Workspace).delete = jest.fn().mockResolvedValue(true);

    jest.mocked(WorkspaceUser).where = jest.fn().mockResolvedValue([
      {
        id: 1,
        user_id: collaborators.user1.id,
        workspace_id: testWorkspaceId,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      },
      {
        id: 2,
        user_id: collaborators.user2.id,
        workspace_id: testWorkspaceId,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      },
    ]);

    jest.mocked(Document).forWorkspace = jest
      .fn()
      .mockResolvedValue([mockDocument]);
    jest.mocked(Document).get = jest.fn().mockResolvedValue(mockDocument);
    jest.mocked(Document).update = jest
      .fn()
      .mockResolvedValue({ document: mockDocument, message: null });
    jest.mocked(Document).delete = jest.fn().mockResolvedValue(true);

    jest.mocked(WorkspaceChats).forWorkspace = jest.fn().mockResolvedValue([]);
    jest.mocked(WorkspaceChats).new = jest.fn().mockResolvedValue({
      chat: {
        id: 123,
        workspaceId: testWorkspaceId,
        prompt: "test prompt",
        response: "test response",
        user_id: 1,
        thread_id: null,
        api_session_id: null,
        include: true,
        invoice_ref: null,
        metrics: null,
        feedbackScore: null,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      },
      message: null,
    });

    jest.mocked(User).get = jest.fn().mockResolvedValue(null);
  });

  describe("Concurrent Model Access", () => {
    it("should handle concurrent workspace access checks", async () => {
      const accessChecks = [
        Workspace.userHasAccess(collaborators.user1.id, testWorkspaceId),
        Workspace.userHasAccess(collaborators.user2.id, testWorkspaceId),
        Workspace.userHasAccess(collaborators.admin.id, testWorkspaceId),
      ];

      const results = await Promise.all(accessChecks);

      expect(results).toEqual([true, true, true]);
      expect(Workspace.userHasAccess).toHaveBeenCalledTimes(3);
    });

    it("should deny access to unauthorized users", async () => {
      const hasAccess = await Workspace.userHasAccess(
        collaborators.outsider.id,
        testWorkspaceId
      );
      expect(hasAccess).toBe(false);
    });

    it("should handle race conditions in workspace retrieval", async () => {
      // Simulate race condition with multiple rapid requests
      const getPromises = Array(5)
        .fill(null)
        .map(() => Workspace.get({ id: testWorkspaceId }));

      const results = await Promise.all(getPromises);

      // All should return the same workspace
      results.forEach((result) => {
        expect(result).toEqual(mockWorkspace);
      });
      expect(Workspace.get).toHaveBeenCalledTimes(5);
    });
  });

  describe("Concurrent Document Operations", () => {
    it("should handle concurrent document updates", async () => {
      const updatePromises = [
        Document.update(mockDocument.id, { pinned: true }),
        Document.update(mockDocument.id, { starred: true }),
        Document.update(mockDocument.id, { watched: true }),
      ];

      const results = await Promise.all(updatePromises);

      results.forEach((result) => {
        expect(result).toHaveProperty("document");
        expect(result.message).toBeNull();
      });
      expect(Document.update).toHaveBeenCalledTimes(3);
    });

    it("should maintain data consistency during concurrent access", async () => {
      // Mock optimistic locking scenario
      let callCount = 0;
      jest.mocked(Document).update = jest
        .fn()
        .mockImplementation(async (_id, data) => {
          callCount++;
          if (callCount === 1) {
            // Simulate delay for first request
            await new Promise((resolve) => setTimeout(resolve, 50));
          }
          return { document: { ...mockDocument, ...data }, message: null };
        });

      const [result1, result2] = await Promise.all([
        Document.update(mockDocument.id, { pinned: true }),
        Document.update(mockDocument.id, { pinned: false }),
      ]);

      expect(Document.update).toHaveBeenCalledTimes(2);
      expect(result1.document).toBeDefined();
      expect(result2.document).toBeDefined();
    });

    it("should handle document deletion conflicts", async () => {
      const deletePromises = [
        Document.delete({ id: mockDocument.id }),
        Document.delete({ id: mockDocument.id }),
      ];

      const results = await Promise.all(deletePromises);

      // Both should complete (even if one finds nothing to delete)
      expect(results).toEqual([true, true]);
      expect(Document.delete).toHaveBeenCalledTimes(2);
    });
  });

  describe("Concurrent Chat Sessions", () => {
    it("should handle multiple users creating chats simultaneously", async () => {
      const chatPromises = [
        WorkspaceChats.new({
          workspaceId: testWorkspaceId,
          prompt: "Hello from user 1",
          user: collaborators.user1,
        }),
        WorkspaceChats.new({
          workspaceId: testWorkspaceId,
          prompt: "Hello from user 2",
          user: collaborators.user2,
        }),
        WorkspaceChats.new({
          workspaceId: testWorkspaceId,
          prompt: "Hello from user 3",
          user: collaborators.user3,
        }),
      ];

      const results = await Promise.all(chatPromises);

      results.forEach((result) => {
        expect(result.chat).toBeDefined();
        expect(result.message).toBeNull();
      });
      expect(WorkspaceChats.new).toHaveBeenCalledTimes(3);
    });

    it("should maintain chat history consistency", async () => {
      const mockChats = [
        {
          id: 1,
          prompt: "Message 1",
          user_id: collaborators.user1.id,
          createdAt: new Date("2024-01-01T10:00:00Z"),
        },
        {
          id: 2,
          prompt: "Message 2",
          user_id: collaborators.user2.id,
          createdAt: new Date("2024-01-01T10:01:00Z"),
        },
        {
          id: 3,
          prompt: "Message 3",
          user_id: collaborators.user1.id,
          createdAt: new Date("2024-01-01T10:02:00Z"),
        },
      ];

      jest.mocked(WorkspaceChats).forWorkspace = jest
        .fn()
        .mockResolvedValue(mockChats as any);

      // Multiple users fetching chat history
      const historyPromises = [
        WorkspaceChats.forWorkspace(testWorkspaceId),
        WorkspaceChats.forWorkspace(testWorkspaceId),
        WorkspaceChats.forWorkspace(testWorkspaceId),
      ];

      const results = await Promise.all(historyPromises);

      // All users should see the same chat history
      results.forEach((result) => {
        expect(result).toEqual(mockChats);
      });
      expect(WorkspaceChats.forWorkspace).toHaveBeenCalledTimes(3);
    });
  });

  describe("Permission Enforcement", () => {
    it("should enforce workspace-level permissions consistently", async () => {
      // Test different user access levels
      const accessTests = [
        { user: collaborators.admin, expected: true },
        { user: collaborators.manager, expected: true },
        { user: collaborators.user1, expected: true },
        { user: collaborators.outsider, expected: false },
      ];

      const results = await Promise.all(
        accessTests.map((test) =>
          Workspace.userHasAccess(test.user.id, testWorkspaceId)
        )
      );

      results.forEach((result, index) => {
        expect(result).toBe(accessTests[index].expected);
      });
    });

    it("should handle role changes dynamically", async () => {
      // Simulate role change
      const updatedUser = { ...collaborators.user1, role: "manager" };
      jest.mocked(User).get = jest.fn().mockResolvedValue(updatedUser);

      const user = await User.get({ id: collaborators.user1.id });
      expect(user).toBeDefined();
      expect(user!.role).toBe("manager");
    });
  });

  describe("Resource Contention", () => {
    it("should handle concurrent document list fetching", async () => {
      const fetchPromises = Array(10)
        .fill(null)
        .map(() => Document.forWorkspace(testWorkspaceId));

      const results = await Promise.all(fetchPromises);

      results.forEach((result) => {
        expect(result).toEqual([mockDocument]);
      });
      expect(Document.forWorkspace).toHaveBeenCalledTimes(10);
    });

    it("should handle workspace deletion conflicts", async () => {
      // Simulate operations during workspace deletion
      const operations = [
        Workspace.delete({ id: testWorkspaceId }),
        WorkspaceChats.new({
          workspaceId: testWorkspaceId,
          prompt: "Test message",
          user: collaborators.user1,
        }),
        Document.forWorkspace(testWorkspaceId),
      ];

      const results = await Promise.all(operations);

      expect(results[0]).toBe(true); // Delete should succeed
      // Other operations may succeed or fail depending on timing
      expect(Workspace.delete).toHaveBeenCalledTimes(1);
    });
  });

  describe("Data Consistency", () => {
    it("should maintain workspace user list consistency", async () => {
      const userListPromises = [
        WorkspaceUser.where({ workspace_id: testWorkspaceId }),
        WorkspaceUser.where({ workspace_id: testWorkspaceId }),
        WorkspaceUser.where({ workspace_id: testWorkspaceId }),
      ];

      const results = await Promise.all(userListPromises);

      // All queries should return the same user list
      results.forEach((result) => {
        expect(result).toHaveLength(2);
        expect(result[0].user_id).toBe(collaborators.user1.id);
        expect(result[1].user_id).toBe(collaborators.user2.id);
      });
    });
  });

  describe("Performance Under Load", () => {
    it("should handle high concurrency without errors", async () => {
      const highConcurrencyOps = Array(20)
        .fill(null)
        .map((_, index) => {
          const operation = index % 3;
          switch (operation) {
            case 0:
              return Workspace.get({ id: testWorkspaceId });
            case 1:
              return Document.forWorkspace(testWorkspaceId);
            case 2:
              return WorkspaceChats.forWorkspace(testWorkspaceId);
            default:
              return Promise.resolve(null);
          }
        });

      const startTime = Date.now();
      const results = await Promise.all(highConcurrencyOps);
      const endTime = Date.now();

      // Should complete within reasonable time
      expect(endTime - startTime).toBeLessThan(15000);

      // All operations should complete successfully
      results.forEach((result) => {
        expect(result).toBeDefined();
      });
    });
  });

  describe("Error Recovery", () => {
    it("should recover from database connection issues", async () => {
      // Mock database error
      jest.mocked(Workspace).get = jest
        .fn()
        .mockRejectedValueOnce(new Error("Database connection lost"))
        .mockResolvedValue(mockWorkspace);

      const results = await Promise.allSettled([
        Workspace.get({ id: testWorkspaceId }),
        Workspace.get({ id: testWorkspaceId }),
      ]);

      expect(results[0].status).toBe("rejected");
      expect(results[1].status).toBe("fulfilled");
      if (results[1].status === "fulfilled") {
        expect(results[1].value).toEqual(mockWorkspace);
      }
    });

    it("should handle partial failures gracefully", async () => {
      // Mock partial failure scenario
      jest.mocked(Document).forWorkspace = jest
        .fn()
        .mockRejectedValueOnce(new Error("Document service unavailable"))
        .mockResolvedValue([mockDocument]);

      const results = await Promise.allSettled([
        Document.forWorkspace(testWorkspaceId),
        Document.forWorkspace(testWorkspaceId),
      ]);

      expect(results[0].status).toBe("rejected");
      expect(results[1].status).toBe("fulfilled");
    });
  });
});
