/* eslint-env jest */

import * as path from "path";
import type { AuthenticatedUser, DecodedJWT } from "../types/auth";
import type { UserCreateParams, UserUpdateParams } from "../types/models";
import type { Request, Response, NextFunction } from "express";
import { UserRole } from "../types/shared";

// Mock environment variables - MUST be set before any imports
process.env.NODE_ENV = "test";
process.env.JWT_SECRET = "test-jwt-secret-for-testing-only";
process.env.DATABASE_URL = "file:./test.db";
process.env.STORAGE_DIR = path.join(__dirname, "../storage");

// Mock API keys for embedding engines to prevent test failures
process.env.OPEN_AI_KEY = "test-openai-key";
process.env.OPENAI_API_KEY = "test-openai-key"; // Alternative name
process.env.EMBEDDING_BASE_PATH = "http://localhost:8080";
process.env.EMBEDDING_MODEL_PREF = "test-model";
process.env.VOYAGEAI_API_KEY = "test-voyage-key";
process.env.GEMINI_EMBEDDING_API_KEY = "test-gemini-key";
process.env.LITE_LLM_BASE_PATH = "http://localhost:8080";
process.env.COHERE_API_KEY = "test-cohere-key";
process.env.AZURE_OPENAI_ENDPOINT = "https://test.openai.azure.com";
process.env.AZURE_OPENAI_KEY = "test-azure-key";
process.env.GENERIC_OPEN_AI_BASE_PATH = "http://localhost:8080";
process.env.VECTOR_DB = "lancedb";
process.env.EMBEDDING_ENGINE = "native";
// Add additional required environment variables
process.env.GEMINI_API_KEY = "test-gemini-api-key";
process.env.GOOGLE_AI_API_KEY = "test-google-ai-key";

// Mock node-fetch globally for all tests
jest.mock("node-fetch", () => jest.fn());

// Mock systemMetrics to prevent fs import issues
jest.mock("../utils/systemMetrics", () => ({
  getSystemMetrics: jest.fn().mockResolvedValue({
    memory: {
      rss: 123456789,
      heapTotal: 234567890,
      heapUsed: 123456789,
      external: 45678901,
      arrayBuffers: 12345678,
    },
    uptime: 12345,
    platform: "test",
    nodeVersion: "v18.0.0",
    env: "test",
    pid: 12345,
    cpuUsage: {
      user: 123456,
      system: 78901,
    },
    timestamp: new Date().toISOString(),
  }),
  getStorageInfo: jest.fn().mockResolvedValue({
    path: "/test/storage",
    exists: true,
    isDirectory: true,
    size: 1000000,
    created: new Date(),
    modified: new Date(),
    accessible: true,
  }),
  getSystemMetadata: jest.fn().mockResolvedValue({
    version: "1.0.0",
    platform: "test",
    arch: "x64",
    nodeEnv: "test",
    serverPort: 3001,
    enableHttps: false,
    timestamp: new Date().toISOString(),
    startTime: new Date().toISOString(),
  }),
  getSystemDetails: jest.fn().mockResolvedValue({
    system: {
      platform: "test",
      arch: "x64",
      nodeVersion: "v18.0.0",
      pid: 12345,
      uptime: 12345,
      env: "test",
    },
    memory: {
      rss: 123456789,
      heapTotal: 234567890,
      heapUsed: 123456789,
      external: 45678901,
      arrayBuffers: 12345678,
    },
    cpu: {
      user: 123456,
      system: 78901,
    },
    server: {
      port: "3001",
      https: false,
      storageDir: "/test/storage",
    },
    features: {
      telemetryEnabled: true,
      multiUserMode: true,
      publicUserMode: false,
    },
    timestamp: new Date().toISOString(),
  }),
}));

// Mock Prisma client globally to prevent direct database access
jest.mock("../utils/prisma", () => ({
  __esModule: true,
  default: {
    users: {
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    workspaces: {
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      findMany: jest.fn(),
    },
    organization: {
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    userTokens: {
      findMany: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
    },
    system_settings: {
      findMany: jest.fn(),
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      upsert: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    $disconnect: jest.fn(),
    // Add getConnectionStats method for connection pool tests
    getConnectionStats: jest.fn(() => ({
      active: 1,
      idle: 2,
      total: 3,
      lastCheck: Date.now(),
    })),
  },
  // Export types for connection pool tests
  ConnectionStats: {},
  ExtendedPrismaClient: {},
}));

// Define test user interface
interface TestUser {
  id: number;
  username: string;
  email: string;
  role: UserRole;
  suspended: number;
  organizationId: number | null;
  createdAt: Date;
  lastUpdatedAt: Date;
}

// Define test organization interface
interface TestOrganization {
  id: number;
  name: string;
  slug: string;
  createdAt: Date;
  updatedAt: Date;
  lastUpdatedAt: Date;
}

// Define JWT with JTI interface for token tracking
interface DecodedJWTWithJTI extends DecodedJWT {
  jti?: string;
}

// Global fetch mock for browser APIs
global.fetch = jest.fn() as jest.MockedFunction<typeof fetch>;

// Mock bcrypt for authentication tests
jest.mock("bcryptjs", () => ({
  compare: jest.fn((password: string, _hash: string) => {
    // For test users, accept any password that's "Test123!@#"
    // For others, reject
    return Promise.resolve(password === "Test123!@#");
  }),
  hash: jest.fn((_password: string, rounds: number) => {
    return Promise.resolve(`$2a$${rounds}$mockHashedPassword123456`);
  }),
}));

// Mock uuid for generating test tokens with unique IDs
let uuidCounter = 0;
jest.mock("uuid", () => ({
  v4: jest.fn(() => `test-uuid-token-${++uuidCounter}`),
  v5: jest.fn(() => "mock-uuid-digest"),
}));

// Mock global objects and functions used in tests
// Silence regular logs but track errors and warnings for test failures
const _originalConsoleError = console.error;
const _originalConsoleWarn = console.warn;

jest.spyOn(console, "error").mockImplementation(() => {
  // Completely suppress all console.error output during tests for clean output
  return;
});

jest.spyOn(console, "warn").mockImplementation(() => {
  // Completely suppress all console.warn output during tests for clean output
  return;
});

jest.spyOn(console, "log").mockImplementation(() => {
  // Completely suppress all console.log output during tests for clean output
  return;
});

// ========================================================================
// GLOBAL AUTHENTICATION MOCKS - Applied to all tests
// ========================================================================

// Mock JWT decode function and userFromSession globally
jest.mock("../utils/http", () => ({
  ...jest.requireActual("../utils/http"),
  decodeJWT: jest.fn((token: string) => {
    // Handle predefined test tokens
    const tokenMap: Record<string, DecodedJWTWithJTI | null> = {
      "admin-token": { id: 1, jti: "admin-jti", exp: Date.now() / 1000 + 3600 },
      "manager-token": {
        id: 2,
        jti: "manager-jti",
        exp: Date.now() / 1000 + 3600,
      },
      "user-token": { id: 3, jti: "user-jti", exp: Date.now() / 1000 + 3600 },
      "suspended-token": {
        id: 4,
        jti: "suspended-jti",
        exp: Date.now() / 1000 + 3600,
      },
      "invalid-token": null,
    };

    if (tokenMap[token]) {
      return tokenMap[token];
    }

    // For real JWT tokens generated by makeJWT, try to decode them
    try {
      const jwt = require("jsonwebtoken");
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || "test-jwt-secret-for-testing-only"
      );
      return decoded;
    } catch {
      return null;
    }
  }),
  userFromSession: jest.fn(async (request: Request, response: Response) => {
    // Get user from validatedRequest middleware which sets res.locals.user
    const user = response?.locals?.user as AuthenticatedUser | undefined;
    if (user) {
      return user;
    }

    // Fallback to decoding from Authorization header
    const authHeader = request?.headers?.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return null;
    }

    const token = authHeader.substring(7);
    const decoded = require("../utils/http").decodeJWT(
      token
    ) as DecodedJWT | null;
    if (!decoded) {
      return null;
    }

    // Return a user object that matches our test users
    const testUser = Object.values(DEFAULT_TEST_USERS).find(
      (u: TestUser) => u.id === decoded.id
    );
    return testUser || null;
  }),
}));

// Create stateful mocks that track changes (shared between User and Organization)
// IMPORTANT: These variables must be declared before jest.mock() calls due to hoisting
const mockOrganizations = new Map<number, TestOrganization>([
  [
    1,
    {
      id: 1,
      name: "Test Organization",
      slug: "test-org",
      createdAt: new Date(),
      updatedAt: new Date(),
      lastUpdatedAt: new Date(),
    },
  ],
]);

// Track users by organization for deletion prevention
const orgUserCounts = new Map<number, number>([[1, 0]]);

let nextOrgId = 2;

// Global default auth users for consistency
const now = new Date();
const DEFAULT_TEST_USERS: Record<string, TestUser> = {
  admin: {
    id: 1,
    username: "admin-test",
    email: "<EMAIL>",
    role: UserRole.ADMIN,
    suspended: 0,
    organizationId: 1,
    createdAt: now,
    lastUpdatedAt: now,
  },
  manager: {
    id: 2,
    username: "manager-test",
    email: "<EMAIL>",
    role: UserRole.MANAGER,
    suspended: 0,
    organizationId: 1,
    createdAt: now,
    lastUpdatedAt: now,
  },
  user: {
    id: 3,
    username: "user-test",
    email: "<EMAIL>",
    role: UserRole.DEFAULT,
    suspended: 0,
    organizationId: 1,
    createdAt: now,
    lastUpdatedAt: now,
  },
  suspended: {
    id: 4,
    username: "suspended-test",
    email: "<EMAIL>",
    role: UserRole.DEFAULT,
    suspended: 1,
    organizationId: 1,
    createdAt: now,
    lastUpdatedAt: now,
  },
  // Add test users that match the test file expectations
  validation_test_user: {
    id: 5,
    username: "validation_test_user",
    email: "<EMAIL>",
    role: UserRole.ADMIN,
    suspended: 0,
    organizationId: 1,
    createdAt: now,
    lastUpdatedAt: now,
  },
  contract_test_user: {
    id: 6,
    username: "contract_test_user",
    email: "<EMAIL>",
    role: UserRole.ADMIN,
    suspended: 0,
    organizationId: 1,
    createdAt: now,
    lastUpdatedAt: now,
  },
  test_endpoint_user: {
    id: 7,
    username: "test_endpoint_user",
    email: "<EMAIL>",
    role: UserRole.ADMIN,
    suspended: 0,
    organizationId: 1,
    createdAt: now,
    lastUpdatedAt: now,
  },
};

// Mock User model globally
jest.mock("../models/user", () => ({
  User: {
    get: jest.fn(async ({ id }: { id: number }) => {
      const user = Object.values(DEFAULT_TEST_USERS).find(
        (u: TestUser) => u.id === id
      );
      return user || null;
    }),
    _get: jest.fn(
      async ({ username, id }: { username?: string; id?: number }) => {
        // Find user by username or id
        const user = Object.values(DEFAULT_TEST_USERS).find(
          (u: TestUser) =>
            (username && u.username === username) || (id && u.id === id)
        );

        if (user) {
          // Return user with hashed password that matches our bcrypt mock expectations
          return {
            ...user,
            password: "$2a$10$mockHashedPassword123456",
            createdAt: new Date(),
            lastUpdatedAt: new Date(),
          };
        }

        return null;
      }
    ),
    where: jest.fn().mockResolvedValue({
      users: [
        DEFAULT_TEST_USERS.admin,
        DEFAULT_TEST_USERS.manager,
        DEFAULT_TEST_USERS.user,
        DEFAULT_TEST_USERS.validation_test_user,
        DEFAULT_TEST_USERS.test_endpoint_user,
      ],
    }),
    create: jest.fn(async (userData: UserCreateParams) => {
      // Increment organization user count if organizationId is provided
      if (userData.organizationId) {
        const currentCount = orgUserCounts.get(userData.organizationId) || 0;
        orgUserCounts.set(userData.organizationId, currentCount + 1);
      }

      // For test users, return their predefined IDs
      if (userData.username === "validation_test_user") {
        return {
          user: { ...DEFAULT_TEST_USERS.validation_test_user, ...userData },
          error: null,
        };
      }

      if (userData.username === "contract_test_user") {
        return {
          user: { ...DEFAULT_TEST_USERS.contract_test_user, ...userData },
          error: null,
        };
      }

      if (userData.username === "test_endpoint_user") {
        return {
          user: { ...DEFAULT_TEST_USERS.test_endpoint_user, ...userData },
          error: null,
        };
      }

      return {
        user: {
          ...DEFAULT_TEST_USERS.user,
          ...userData,
          id: Math.floor(Math.random() * 10000) + 100, // Generate random ID for new users
        },
        error: null,
      };
    }),
    update: jest.fn(async (id: number, updates: UserUpdateParams) => {
      const user = Object.values(DEFAULT_TEST_USERS).find(
        (u: TestUser) => u.id === id
      );
      if (!user) {
        return { success: false, error: "User not found" };
      }
      return {
        success: true,
        user: { ...user, ...updates },
        error: null,
      };
    }),
    delete: jest.fn(async (_userId: number) => {
      // This would need more complex logic to track which org the user was in
      // For now, keep it simple
      return { success: true };
    }),
    count: jest.fn().mockResolvedValue(5), // Mock user count for setup-complete endpoint
    // Include validations for unit tests
    validations: {
      username: jest.fn((newValue: unknown = ""): string => {
        const username = String(newValue);
        if (username.length < 2)
          throw new Error("Username must be at least 2 characters");
        if (username.length > 100)
          throw new Error("Username cannot be longer than 100 characters");
        const usernameRegex = /^[a-z0-9_.@-]+$/;
        if (!usernameRegex.test(username))
          throw new Error(
            "Username can only include lowercase letters, numbers, underscores (_), dots (.), @ symbols, and hyphens (-)."
          );
        return username;
      }),
      role: jest.fn((role: unknown = "default"): string => {
        const VALID_ROLES = ["default", "admin", "manager", "superuser"];
        if (role === null || role === undefined || role === "") {
          return "default";
        }
        const roleString = String(role);
        if (!VALID_ROLES.includes(roleString)) {
          throw new Error(
            `Invalid role. Allowed roles are: ${VALID_ROLES.join(", ")}`
          );
        }
        return roleString;
      }),
      custom_system_prompt: jest.fn((value: unknown): string | null => {
        if (value === null || value === undefined) return null;
        const str = String(value).trim();
        if (str.length === 0) return null;
        if (str.length > 10000) {
          throw new Error(
            "Custom system prompt cannot exceed 10,000 characters"
          );
        }
        return str;
      }),
      organizationId: jest.fn((value: unknown): number | null => {
        if (value === null || value === undefined) return null;
        const id = parseInt(String(value));
        if (isNaN(id) || id <= 0) {
          return null;
        }
        return id;
      }),
    },
  },
}));

// Define organization update interface
interface OrganizationUpdateParams {
  name?: string;
  slug?: string;
  [key: string]: string | number | boolean | Date | null | undefined;
}

// Mock Organization model globally
jest.mock("../models/organization", () => ({
  Organization: {
    get: jest.fn(async ({ id }: { id: number }) => {
      return mockOrganizations.get(id) || null;
    }),
    getAll: jest.fn(async () => ({
      organizations: Array.from(mockOrganizations.values()),
      error: null,
    })),
    create: jest.fn(async (name: string) => {
      const newOrg: TestOrganization = {
        id: nextOrgId++,
        name,
        slug: name.toLowerCase().replace(/\s+/g, "-"),
        createdAt: new Date(),
        updatedAt: new Date(),
        lastUpdatedAt: new Date(),
      };
      mockOrganizations.set(newOrg.id, newOrg);
      orgUserCounts.set(newOrg.id, 0); // Initialize user count
      return {
        organization: newOrg,
        error: null,
      };
    }),
    update: jest.fn(async (id: number, updates: OrganizationUpdateParams) => {
      const org = mockOrganizations.get(id);
      if (!org) {
        return {
          organization: null,
          error: "Organization not found",
        };
      }
      const updatedOrg = {
        ...org,
        ...updates,
        updatedAt: new Date(),
        lastUpdatedAt: new Date(),
      };
      mockOrganizations.set(id, updatedOrg);
      return {
        organization: updatedOrg,
        error: null,
      };
    }),
    delete: jest.fn(async (id: number) => {
      const exists = mockOrganizations.has(id);
      if (!exists) {
        return {
          success: false,
          error: "Organization not found",
        };
      }

      // Check if organization has users
      const userCount = orgUserCounts.get(id) || 0;
      if (userCount > 0) {
        return {
          success: false,
          error: "Cannot delete organization with existing users.",
        };
      }

      mockOrganizations.delete(id);
      orgUserCounts.delete(id);
      return {
        success: true,
        error: null,
      };
    }),
  },
}));

// Mock UserToken model globally
jest.mock("../models/userToken", () => ({
  UserToken: {
    create: jest.fn(async (userId: number, token: string) => {
      return {
        id: Math.floor(Math.random() * 1000) + 1,
        user_id: userId,
        token,
        device_info: "Test Device",
        last_used: new Date(),
      };
    }),
    findByToken: jest.fn(async (jti: string) => {
      // Return valid token data for test JTIs
      if (jti.includes("jti")) {
        return { id: 1, userId: 1, lastUsed: new Date() };
      }
      return null;
    }),
    findByUserId: jest.fn(async (userId: number) => {
      return [{ id: 1, userId, lastUsed: new Date() }];
    }),
    updateLastUsed: jest.fn().mockResolvedValue(true),
  },
}));

// Mock EncryptionManager globally
jest.mock("../utils/EncryptionManager", () => ({
  EncryptionManager: jest.fn().mockImplementation(() => ({
    decrypt: jest.fn((_encrypted: string) => {
      // Mock decryption - in tests, assume encryption is bypassed
      return process.env.AUTH_TOKEN || "test-auth-token";
    }),
    encrypt: jest.fn((data: string) => `encrypted:${data}`),
  })),
}));

// Mock middleware functions globally
jest.mock("../utils/middleware/validatedRequest", () => ({
  validatedRequest: jest.fn(
    (req: Request, res: Response, next: NextFunction) => {
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return res.status(401).json({ error: "Unauthorized" });
      }

      const token = authHeader.substring(7);
      // Use our mocked decodeJWT function to validate the token
      const decoded = require("../utils/http").decodeJWT(
        token
      ) as DecodedJWT | null;
      if (!decoded) {
        return res.status(401).json({ error: "Invalid token" });
      }

      // Find the actual user to get the correct role
      const testUser = Object.values(DEFAULT_TEST_USERS).find(
        (u: TestUser) => u.id === decoded.id
      );

      if (!testUser) {
        return res.status(401).json({ error: "User not found" });
      }

      // Set full user object in request for other middleware
      (req as Request & { user: TestUser }).user = testUser;
      res.locals.user = testUser;
      res.locals.multiUserMode = true;
      next();
    }
  ),
}));

jest.mock("../utils/middleware/multiUserProtected", () => ({
  strictMultiUserRoleValid: jest.fn((allowedRoles: string[]) =>
    jest.fn((req: Request, res: Response, next: NextFunction) => {
      const user =
        res.locals?.user || (req as Request & { user?: TestUser }).user;
      res.locals.multiUserMode = true;

      // For admin endpoints, only allow admin users
      if (allowedRoles.includes("admin") && user?.role !== "admin") {
        return res.status(403).json({ error: "Forbidden" });
      }

      next();
    })
  ),
  flexUserRoleValid: jest.fn((allowedRoles: string[]) =>
    jest.fn((req: Request, res: Response, next: NextFunction) => {
      const user =
        res.locals?.user || (req as Request & { user?: TestUser }).user;
      res.locals.multiUserMode = true;

      // For admin endpoints, only allow admin users
      if (allowedRoles.includes("admin") && user?.role !== "admin") {
        return res.status(403).json({ error: "Forbidden" });
      }

      next();
    })
  ),
  legalTemplateScopeGuard: jest.fn((scope: string) =>
    jest.fn(async (req: Request, res: Response, next: NextFunction) => {
      // Get user from res.locals (set by validatedRequest) or call userFromSession directly
      let user =
        res.locals?.user || (req as Request & { user?: TestUser }).user;

      if (!user) {
        // Call the mocked userFromSession function
        user = (await require("../utils/http").userFromSession(
          req,
          res
        )) as AuthenticatedUser | null;
      }

      res.locals.multiUserMode = true;

      // Check authorization based on scope
      let authorized = false;
      if (scope === "system") {
        authorized = user?.role === "admin";
      } else if (scope === "org") {
        authorized =
          user?.role === "admin" ||
          user?.role === "manager" ||
          user?.role === "superuser";
      } else if (scope === "user") {
        authorized = true; // Users can manage their own templates
      }

      if (!authorized) {
        return res.status(403).json({ error: "Forbidden" });
      }

      next();
    })
  ),
  ROLES: {
    all: "<all>",
    admin: "admin",
    manager: "manager",
    superuser: "superuser",
    default: "default",
  },
}));

// Mock DocumentManager
jest.mock("../utils/DocumentManager", () => ({
  DocumentManager: jest.fn().mockImplementation(() => ({
    pinnedDocs: jest.fn().mockResolvedValue([]),
    pdrDocs: jest.fn().mockResolvedValue([]),
  })),
}));

// Mock VectorDb
interface MockVectorDb {
  hasNamespace: jest.Mock<Promise<boolean>>;
  namespaceCount: jest.Mock<Promise<number>>;
  performSimilaritySearch: jest.Mock;
  getAdjacentVectors: jest.Mock;
  totalVectors: jest.Mock<Promise<number>>;
}

// Remove unused GlobalWithVectorDb interface
// declare global block for type annotation only

declare global {
  // Add VectorDb to the NodeJS global object for tests
  var VectorDb: MockVectorDb;
}

// Assign the mock object to global.VectorDb
global.VectorDb = {
  hasNamespace: jest.fn().mockResolvedValue(true),
  namespaceCount: jest.fn().mockResolvedValue(100),
  performSimilaritySearch: jest.fn(),
  getAdjacentVectors: jest.fn(),
  totalVectors: jest.fn().mockResolvedValue(1500),
};

// Mock getVectorDbClass helper
jest.mock("../utils/helpers", () => ({
  ...jest.requireActual("../utils/helpers"),
  getVectorDbClass: jest.fn(() => ({
    totalVectors: jest.fn().mockResolvedValue(1500),
    hasNamespace: jest.fn().mockResolvedValue(true),
    namespaceCount: jest.fn().mockResolvedValue(100),
  })),
}));

// Mock TokenManager
jest.mock("../utils/helpers/tiktoken", () => ({
  TokenManager: jest.fn().mockImplementation(() => ({
    countFromString: jest.fn((str: string) => Math.ceil(str.length / 4)),
    statsFrom: jest.fn(() => ({ tokens: 100 })),
    tokensFromString: jest.fn((str: string) => {
      // Mock tokenization by splitting on words and creating a rough token array
      const words: string[] = str
        .split(/\s+/)
        .filter((word: string) => word.length > 0);
      const tokens: number[] = [];
      for (let i = 0; i < words.length; i++) {
        // Each word gets roughly 1-2 tokens
        tokens.push(i * 2);
        if (words[i].length > 6) tokens.push(i * 2 + 1);
      }
      return tokens;
    }),
    bytesFromTokens: jest.fn((tokens: number[]) => {
      // Mock detokenization - just create dummy text based on token count
      return "word ".repeat(Math.ceil(tokens.length / 2)).trim();
    }),
  })),
}));

// Mock AI Provider configs to prevent import issues
jest.mock("../utils/AiProviders/gemini/defaultModals", () => ({
  getGeminiModels: jest.fn(() => []),
  v1BetaModels: [],
  GeminiLLM: jest.fn(),
}));

jest.mock("../utils/helpers/customModels", () => ({
  chatModelProviders: jest.fn(() => ({})),
  embeddingModelProviders: jest.fn(() => ({})),
}));

// Define embedder interface
interface MockEmbedder {
  embedTextInput: jest.Mock<Promise<number[]>>;
  embedChunks: jest.Mock<Promise<number[][]>>;
  embed: jest.Mock<Promise<number[]>>;
}

// Mock individual embedding engines with proper constructor names and interfaces
jest.mock("../utils/EmbeddingEngines/native", () => ({
  NativeEmbedder: jest
    .fn()
    .mockImplementation(function NativeEmbedder(): MockEmbedder {
      // Create functions with proper parameter count
      const embedTextInput = function (_textInput: string): Promise<number[]> {
        return Promise.resolve([0.1, 0.2, 0.3]);
      };
      const embedChunks = function (
        _textChunks: string[]
      ): Promise<number[][]> {
        return Promise.resolve([[0.1, 0.2, 0.3]]);
      };
      const embed = function (): Promise<number[]> {
        return Promise.resolve([0.1, 0.2, 0.3]);
      };

      return {
        embedTextInput: jest.fn(embedTextInput),
        embedChunks: jest.fn(embedChunks),
        embed: jest.fn(embed),
      };
    }),
}));

jest.mock("../utils/EmbeddingEngines/openAi", () => ({
  OpenAiEmbedder: jest
    .fn()
    .mockImplementation(function OpenAiEmbedder(): MockEmbedder {
      const embedTextInput = function (_textInput: string): Promise<number[]> {
        return Promise.resolve([0.1, 0.2, 0.3]);
      };
      const embedChunks = function (
        _textChunks: string[]
      ): Promise<number[][]> {
        return Promise.resolve([[0.1, 0.2, 0.3]]);
      };
      const embed = function (): Promise<number[]> {
        return Promise.resolve([0.1, 0.2, 0.3]);
      };

      return {
        embedTextInput: jest.fn(embedTextInput),
        embedChunks: jest.fn(embedChunks),
        embed: jest.fn(embed),
      };
    }),
}));

jest.mock("../utils/EmbeddingEngines/azureOpenAi", () => ({
  AzureOpenAiEmbedder: jest
    .fn()
    .mockImplementation(function AzureOpenAiEmbedder(): MockEmbedder {
      const embedTextInput = function (_textInput: string): Promise<number[]> {
        return Promise.resolve([0.1, 0.2, 0.3]);
      };
      const embedChunks = function (
        _textChunks: string[]
      ): Promise<number[][]> {
        return Promise.resolve([[0.1, 0.2, 0.3]]);
      };
      const embed = function (): Promise<number[]> {
        return Promise.resolve([0.1, 0.2, 0.3]);
      };

      return {
        embedTextInput: jest.fn(embedTextInput),
        embedChunks: jest.fn(embedChunks),
        embed: jest.fn(embed),
      };
    }),
}));

// Helper function to create mock embedder
function createMockEmbedder(): MockEmbedder {
  const embedTextInput = function (_textInput: string): Promise<number[]> {
    return Promise.resolve([0.1, 0.2, 0.3]);
  };
  const embedChunks = function (_textChunks: string[]): Promise<number[][]> {
    return Promise.resolve([[0.1, 0.2, 0.3]]);
  };
  const embed = function (): Promise<number[]> {
    return Promise.resolve([0.1, 0.2, 0.3]);
  };

  return {
    embedTextInput: jest.fn(embedTextInput),
    embedChunks: jest.fn(embedChunks),
    embed: jest.fn(embed),
  };
}

jest.mock("../utils/EmbeddingEngines/localAi", () => ({
  LocalAiEmbedder: jest
    .fn()
    .mockImplementation(function LocalAiEmbedder(): MockEmbedder {
      return createMockEmbedder();
    }),
}));

jest.mock("../utils/EmbeddingEngines/ollama", () => ({
  OllamaEmbedder: jest
    .fn()
    .mockImplementation(function OllamaEmbedder(): MockEmbedder {
      return createMockEmbedder();
    }),
}));

jest.mock("../utils/EmbeddingEngines/lmstudio", () => ({
  LMStudioEmbedder: jest
    .fn()
    .mockImplementation(function LMStudioEmbedder(): MockEmbedder {
      return createMockEmbedder();
    }),
}));

jest.mock("../utils/EmbeddingEngines/cohere", () => ({
  CohereEmbedder: jest
    .fn()
    .mockImplementation(function CohereEmbedder(): MockEmbedder {
      return createMockEmbedder();
    }),
}));

jest.mock("../utils/EmbeddingEngines/voyageAi", () => ({
  VoyageAiEmbedder: jest
    .fn()
    .mockImplementation(function VoyageAiEmbedder(): MockEmbedder {
      return createMockEmbedder();
    }),
}));

jest.mock("../utils/EmbeddingEngines/genericOpenAi", () => ({
  GenericOpenAiEmbedder: jest
    .fn()
    .mockImplementation(function GenericOpenAiEmbedder(): MockEmbedder {
      return createMockEmbedder();
    }),
}));

jest.mock("../utils/EmbeddingEngines/gemini", () => ({
  GeminiEmbedder: jest
    .fn()
    .mockImplementation(function GeminiEmbedder(): MockEmbedder {
      return createMockEmbedder();
    }),
}));

jest.mock("../utils/EmbeddingEngines/jina", () => ({
  JinaEmbedder: jest
    .fn()
    .mockImplementation(function JinaEmbedder(): MockEmbedder {
      return createMockEmbedder();
    }),
}));

jest.mock("../utils/EmbeddingEngines/liteLLM", () => ({
  LiteLLMEmbedder: jest
    .fn()
    .mockImplementation(function LiteLLMEmbedder(): MockEmbedder {
      return createMockEmbedder();
    }),
}));

// Define system settings types
interface PdrSettings {
  pdrToken: number;
  responseToken: number;
  adjacentVector: number;
  keepPdrVectors: boolean;
}

interface SettingsUpdateResult {
  success: boolean;
  error?: string;
}

// Mock SystemSettings
jest.mock("../models/systemSettings", () => ({
  default: {
    getPdrSettings: jest.fn().mockResolvedValue({
      pdrToken: 100000,
      responseToken: 1000,
      adjacentVector: 2,
      keepPdrVectors: true,
    } as PdrSettings),
    isMultiUserMode: jest.fn().mockResolvedValue(true),
    isPublicUserMode: jest.fn().mockResolvedValue(false),
    get: jest.fn().mockResolvedValue(null),
    currentSettings: jest.fn().mockResolvedValue({}),
    getDynamicContextSettings: jest.fn().mockResolvedValue(70),
    getDeepSearchSettings: jest.fn().mockResolvedValue({}),
    isPromptOutputLogging: jest.fn().mockResolvedValue(false),
    getValueOrFallback: jest.fn().mockResolvedValue(null),
    updateSettings: jest
      .fn()
      .mockResolvedValue({ success: true } as SettingsUpdateResult),
    updateDeepSearchSettings: jest
      .fn()
      .mockResolvedValue({ success: true } as SettingsUpdateResult),
    getDDSettings: jest.fn().mockResolvedValue({}),
    getDocumentDraftingSettings: jest.fn().mockResolvedValue({}),
  },
  __esModule: true,
}));

// Mock WorkspaceChats
jest.mock("../models/workspaceChats", () => ({
  WorkspaceChats: {
    new: jest.fn().mockResolvedValue({}),
    where: jest.fn().mockResolvedValue([]),
  },
}));

// NOTE: Workspace model is not globally mocked here to allow unit tests to test the actual implementation
// Individual tests can mock specific functions as needed

// Set the current working directory to the server root for consistent path resolution
process.chdir(path.join(__dirname, ".."));

// Setup test cleanup to prevent test pollution
import { setupTestCleanup } from "./helpers/testCleanup";
setupTestCleanup();
