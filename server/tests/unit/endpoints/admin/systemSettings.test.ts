// File: server/tests/unit/endpoints/admin/systemSettings.test.js

// Use supertest for integration testing
import request from "supertest";
import express from "express";
import type { Request, Response, NextFunction } from "express";
import type { Response as SupertestResponse } from "supertest";
import type { AuthenticatedUser } from "../../../../types/shared";
const SystemSettings = require("../../../../models/systemSettings").default;
import { apiAdminSystemEndpoints } from "../../../../endpoints/api/admin/system";

// Explicitly mock SystemSettings for this test suite
jest.mock("../../../../models/systemSettings", () => ({
  __esModule: true,
  default: {
    // Mock the methods we expect to be called
    updateDeepSearchSettings: jest.fn(),
    getDeepSearchSettings: jest.fn(), // Mock GET as well if needed for other tests
    // Add other SystemSettings methods used by endpoints/middleware if necessary
    isMultiUserMode: jest.fn().mockResolvedValue(true), // Example mock
  },
}));

// Mock middleware used by the endpoint
// We need to mock the actual middleware used: flexUserRoleValid
jest.mock("../../../../utils/middleware/multiUserProtected", () => ({
  // Remove strictMultiUserRoleValid mock as it's not used by the API endpoint file
  flexUserRoleValid:
    () => (_req: Request, _res: Response, next: NextFunction) => {
      // Simulate an admin user that satisfies the required roles

      (_req as Request & { user?: AuthenticatedUser }).user = {
        id: 1,
        role: "admin" as any,
        username: "testadmin",
        email: "<EMAIL>",
        organizationId: 1,
        suspended: 0,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      };
      next();
    },
  ROLES: {
    all: "<all>", // Ensure all roles used by the real module are here
    admin: "admin",
    manager: "manager",
    superuser: "superuser",
    default: "default",
  },
}));

// Mock validatedRequest to simply call next()
// It seems this middleware might be interfering if not mocked.
jest.mock("../../../../utils/middleware/validatedRequest", () => ({
  validatedRequest: (_req: Request, _res: Response, next: NextFunction) => {
    next();
  },
}));

// Create a test Express app instance
const app = express();
app.use(express.json()); // Middleware to parse JSON bodies

// Mount the endpoints onto the test app
apiAdminSystemEndpoints(app);

describe("PUT /admin/system/deep-search-settings", () => {
  beforeEach(() => {
    // Reset the explicitly mocked methods

    (SystemSettings.updateDeepSearchSettings as jest.Mock).mockReset();
    // Reset others if needed
    // (SystemSettings.isMultiUserMode as any).mockReset();
  });

  it("should successfully update settings with valid data and return 200", async () => {
    const settingsPayload: Record<string, unknown> = {
      enabled: true,
      provider: "provider_x",
      config: { apiKey: "123" },
    };
    const mockUpdateResult = { success: true };
    (SystemSettings.updateDeepSearchSettings as jest.Mock).mockResolvedValue(
      mockUpdateResult
    );

    const response: SupertestResponse = await request(app)
      .put("/admin/system/deep-search-settings")
      .send(settingsPayload);

    expect(response.statusCode).toBe(200);
    expect(response.body).toEqual({ success: true }); // Endpoint returns simple success
    expect(SystemSettings.updateDeepSearchSettings).toHaveBeenCalledTimes(1);
    expect(SystemSettings.updateDeepSearchSettings).toHaveBeenCalledWith(
      settingsPayload
    );
  });

  it("should return 400 if validation fails (e.g., missing enabled flag)", async () => {
    const settingsPayload: Record<string, unknown> = { provider: "provider_x" }; // Missing 'enabled'

    // We expect the endpoint handler's validation to catch this *before* calling the model
    const response: SupertestResponse = await request(app)
      .put("/admin/system/deep-search-settings")
      .send(settingsPayload);

    expect(response.statusCode).toBe(400);
    expect(response.body.error).toBeDefined(); // Check for an error message
    // expect(response.body.details).toContain("Enabled must be a boolean"); // More specific check if needed
    expect(SystemSettings.updateDeepSearchSettings).not.toHaveBeenCalled();
  });

  it("should return 400 if contextPercentage is invalid", async () => {
    const settingsPayload: Record<string, unknown> = {
      enabled: true,
      contextPercentage: 99,
    };

    const response: SupertestResponse = await request(app)
      .put("/admin/system/deep-search-settings")
      .send(settingsPayload);

    expect(response.statusCode).toBe(400);
    expect(response.body.error).toContain("Invalid contextPercentage");
    expect(SystemSettings.updateDeepSearchSettings).not.toHaveBeenCalled();
  });

  it("should delete apiKey if masked value is sent", async () => {
    const settingsPayload: Record<string, unknown> = {
      enabled: true,
      apiKey: "••••••••••key4",
    };
    (SystemSettings.updateDeepSearchSettings as jest.Mock).mockResolvedValue({
      success: true,
    });

    await request(app)
      .put("/admin/system/deep-search-settings")
      .send(settingsPayload);

    expect(SystemSettings.updateDeepSearchSettings).toHaveBeenCalledWith({
      enabled: true, // apiKey should have been deleted before calling model
    });
  });

  it("should return 500 if model update fails", async () => {
    const settingsPayload: Record<string, unknown> = {
      enabled: true,
      provider: "provider_x",
      config: { apiKey: "123" },
    };
    const error = new Error("Database error");
    (SystemSettings.updateDeepSearchSettings as jest.Mock).mockRejectedValue(
      error
    );

    const response: SupertestResponse = await request(app)
      .put("/admin/system/deep-search-settings")
      .send(settingsPayload);

    expect(response.statusCode).toBe(500);
    expect(response.body.error).toContain(
      "Failed to update DeepSearch settings"
    );
    expect(response.body.details).toBe(error.message);
    expect(SystemSettings.updateDeepSearchSettings).toHaveBeenCalledTimes(1);
  });

  // Note: Testing the 403 Forbidden case now depends on how the mock
  // for flexUserRoleValid is implemented. If the mock always allows access,
  // testing forbidden cases requires adjusting the mock per test or a different setup.
});

// Add describe block for GET /admin/system/deep-search-settings if needed
