const request = require("supertest");
const express = require("express");
import { systemEndpoints } from "../../../endpoints/system";
import { getLLMProvider } from "../../../utils/helpers";
import SystemSettings from "../../../models/systemSettings";
import type { Request, Response, NextFunction } from "express";
import type { LLMProvider } from "../../../types/ai-providers";
import type { ExpressApp } from "../../../types/shared";

// Using proper types for better type safety in tests

// Mock dependencies
jest.mock("../../../utils/helpers", () => ({
  getLLMProvider: jest.fn(),
  getVectorDbClass: jest.fn(),
}));

jest.mock("../../../utils/middleware/validatedRequest", () => ({
  validatedRequest: (_req: Request, _res: Response, next: NextFunction) =>
    next(),
}));

jest.mock("../../../utils/http", () => ({
  reqBody: (req: Request) => req.body,
  userFromSession: jest.fn(),
  multiUserMode: jest.fn(() => false),
  queryParams: jest.fn(),
  baseHeaders: jest.fn(),
  makeJWT: jest.fn(),
}));

jest.mock("../../../models/systemSettings", () => ({
  __esModule: true,
  default: {
    get: jest.fn(),
    getValueOrFallback: jest.fn(),
    publicFields: [],
  },
}));

// Mock other models that might be imported
jest.mock("../../../models/user", () => ({
  User: {
    where: jest.fn(),
  },
}));

jest.mock("../../../models/userToken", () => ({
  UserToken: {},
}));

jest.mock("../../../models/telemetry", () => ({
  Telemetry: {},
}));

jest.mock("../../../models/promptExamples", () => ({
  PromptExamples: {},
}));

jest.mock("../../../models/apiKeys", () => ({
  ApiKey: {},
}));

jest.mock("../../../models/workspaceChats", () => ({
  WorkspaceChats: {},
}));

jest.mock("../../../models/eventLogs", () => ({
  EventLogs: {},
}));

jest.mock("../../../models/slashCommandsPresets", () => ({
  SlashCommandPresets: {},
}));

jest.mock("../../../models/browserExtensionApiKey", () => ({
  BrowserExtensionApiKey: {},
}));

jest.mock("../../../models/workspace", () => ({
  Workspace: {},
}));

jest.mock("../../../models/category", () => ({
  Category: {},
}));

jest.mock("../../../models/Feedback", () => ({
  Feedback: {},
}));

jest.mock("../../../models/documentSyncQueue", () => ({
  DocumentSyncQueue: {},
}));

// Mock utility modules
jest.mock("../../../utils/files", () => ({
  viewLocalFiles: jest.fn(),
  normalizePath: jest.fn(),
  isWithin: jest.fn(),
}));

jest.mock("../../../utils/files/purgeDocument", () => ({
  purgeDocument: jest.fn(),
  purgeFolder: jest.fn(),
  cleanOldDocxSessionFiles: jest.fn(),
}));

jest.mock("../../../utils/helpers/updateENV", () => ({
  updateENV: jest.fn(),
  dumpENV: jest.fn(),
  KEY_MAPPING: {},
}));

jest.mock("../../../utils/files/multer", () => ({
  handleAssetUpload: jest.fn(),
  handlePfpUpload: jest.fn(),
}));

jest.mock("../../../utils/helpers/customModels", () => ({
  getCustomModels: jest.fn(),
  getSupportedModels: jest.fn(),
}));

jest.mock("../../../utils/middleware/multiUserProtected", () => ({
  flexUserRoleValid: jest.fn(
    () => (_req: Request, _res: Response, next: NextFunction) => next()
  ),
  ROLES: {
    admin: "admin",
    manager: "manager",
    user: "user",
  },
  isMultiUserSetup: jest.fn(),
}));

jest.mock("../../../utils/files/pfp", () => ({
  fetchPfp: jest.fn(),
  determinePfpFilepath: jest.fn(),
}));

jest.mock("../../../utils/helpers/chat/convertTo", () => ({
  exportChatsAsType: jest.fn(),
}));

jest.mock("../../../utils/collectorApi", () => ({
  CollectorApi: {},
}));

jest.mock("../../../utils/PasswordRecovery", () => ({
  recoverAccount: jest.fn(),
  resetPassword: jest.fn(),
  generateRecoveryCodes: jest.fn(),
}));

jest.mock("../../../utils/EncryptionManager", () => ({
  EncryptionManager: {},
}));

jest.mock("../../../utils/prisma", () => ({}));

jest.mock("../../../utils/i18n", () => ({
  t: jest.fn((key: string) => key),
}));

jest.mock("../../../utils/chats/streamDD", () => ({
  DEFAULT_COMBINE_PROMPT: "default combine prompt",
  DEFAULT_DOCUMENT_DRAFTING_PROMPT: "default document drafting prompt",
  DEFAULT_LEGAL_ISSUES_PROMPT: "default legal issues prompt",
  DEFAULT_MEMO_PROMPT: "default memo prompt",
}));

jest.mock("../../../utils/chats/prompts/legalDrafting", () => ({
  exportedLegalPrompts: [],
}));

jest.mock("../../../utils/chats/helpers/promptManager", () => ({
  PROMPT_MAPPINGS: {},
}));

jest.mock("../../../utils/DocumentManager", () => ({
  DocumentManager: {},
}));

jest.mock("../../../utils/files/logo", () => ({
  getDefaultFilenameLight: jest.fn(),
  getDefaultFilenameDark: jest.fn(),
  determineLogoLightFilepath: jest.fn(),
  determineLogoDarkFilepath: jest.fn(),
  fetchLogo: jest.fn(),
  validFilenameLight: jest.fn(),
  validFilenameDark: jest.fn(),
  renameLogoFile: jest.fn(),
  removeCustomLogoLight: jest.fn(),
  removeCustomLogoDark: jest.fn(),
  LOGO_LIGHT: "logo-light.png",
  LOGO_DARK: "logo-dark.png",
}));

// Mock axios
jest.mock("axios", () => ({
  get: jest.fn(),
  post: jest.fn(),
}));

// Mock uuid
jest.mock("uuid", () => ({
  v4: jest.fn(() => "mock-uuid"),
}));

// Mock dotenv
jest.mock("dotenv", () => ({
  config: jest.fn(),
}));

jest.mock("../../../utils/helpers/tiktoken", () => ({
  TokenManager: jest.fn().mockImplementation(() => ({
    countFromString: jest.fn().mockReturnValue(100), // Mock token count
  })),
}));

describe("Style Profile Generation", () => {
  let app: ExpressApp;
  let mockLLMConnector: Record<string, jest.MockedFunction<any>>;

  beforeEach(() => {
    // Set up environment variables
    process.env.LLM_PROVIDER = "openai";
    process.env.LLM_MODEL = "gpt-3.5-turbo";

    app = express() as ExpressApp;
    app.use(express.json());

    // Mock LLM connector
    mockLLMConnector = {
      getChatCompletion: jest.fn(),
      promptWindowLimit: jest.fn().mockReturnValue(4096), // Mock context window
    };

    (
      getLLMProvider as jest.MockedFunction<typeof getLLMProvider>
    ).mockReturnValue(mockLLMConnector as LLMProvider);

    // Mock SystemSettings to return null (use default prompt)
    (
      SystemSettings.get as jest.MockedFunction<typeof SystemSettings.get>
    ).mockResolvedValue({
      id: 1,
      label: "manual_work_estimator_prompt",
      value: "Default prompt",
      createdAt: new Date(),
      lastUpdatedAt: new Date(),
    });

    // Initialize system endpoints
    systemEndpoints(app as ExpressApp);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
    // Clean up any remaining async operations
    jest.runOnlyPendingTimers();
  });

  describe("Success Cases", () => {
    test("generates style profile successfully", async () => {
      const documentContent: string =
        "This is a sample legal document with formal language and structured paragraphs.";
      const expectedInstructions: string =
        "Use formal legal language with clear structure and professional tone.";

      mockLLMConnector.getChatCompletion.mockResolvedValue({
        textResponse: expectedInstructions,
      });

      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent });

      expect(response.status).toBe(200);

      expect(response.body).toEqual({
        success: true,
        styleInstructions: expectedInstructions,
        tokenInfo: {
          documentTokens: 100,
          availableTokens: 1096,
          contextWindow: 4096,
          reservedTokens: 3000,
        },
      });

      expect(getLLMProvider).toHaveBeenCalledWith({
        provider: process.env.LLM_PROVIDER,
        model: process.env.LLM_MODEL,
      });

      expect(mockLLMConnector.getChatCompletion).toHaveBeenCalledWith(
        [
          expect.objectContaining({
            role: "user",
            content: expect.stringContaining(documentContent),
            id: "system-style-analysis",
          }),
        ],
        {
          temperature: 0.3,
          maxTokens: 2000,
        }
      );
    });

    test("includes proper style analysis prompt", async () => {
      const documentContent: string = "Sample document content";
      mockLLMConnector.getChatCompletion.mockResolvedValue({
        textResponse: "Style instructions",
      });

      await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent })
        .expect(200);

      const callArgs = mockLLMConnector.getChatCompletion.mock.calls[0];
      const prompt: string = (callArgs[0] as Record<string, unknown>[])[0]
        .content as string;

      expect(prompt).toContain("legal writing style analyzer");
      expect(prompt).toContain("**Tone and Formality Level**");
      expect(prompt).toContain("**Sentence Structure and Length Patterns**");
      expect(prompt).toContain(
        "**Vocabulary choices and terminology preferences**"
      );
      expect(prompt).toContain("**Paragraph organization and flow**");
      expect(prompt).toContain("**Any unique stylistic elements or patterns**");
      expect(prompt).toContain(
        "**Professional conventions specific to the document type**"
      );
      expect(prompt).toContain(documentContent);
    });

    test("trims whitespace from generated instructions", async () => {
      const documentContent: string = "Sample document";
      const instructionsWithWhitespace: string =
        "  \n  Generated instructions  \n  ";

      mockLLMConnector.getChatCompletion.mockResolvedValue({
        textResponse: instructionsWithWhitespace,
      });

      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent })
        .expect(200);

      expect(response.body.styleInstructions).toBe("Generated instructions");
    }, 90000); // Increase timeout to 90 seconds
  });

  describe("Validation Errors", () => {
    test("returns 400 for missing documentContent", async () => {
      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({})
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain("Document content is required");
    });

    test("returns 400 for empty documentContent", async () => {
      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent: "" })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain("Document content is required");
    });

    test("returns 400 for non-string documentContent", async () => {
      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent: 123 })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain(
        "Document content is required for style analysis"
      );
    });

    test("returns 413 for oversized documentContent", async () => {
      const largeContent: string = "a".repeat(500001); // Over 500KB
      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent: largeContent })
        .expect(413);

      // Express returns a plain text error for 413, not a JSON body
      expect(response.text?.toLowerCase()).toMatch(
        /payload too large|request entity too large|413/
      );
    });
  });

  describe("Context Window Handling", () => {
    test("handles context window exceeded scenario", async () => {
      const documentContent: string = "Large document content";

      // Mock a smaller context window
      mockLLMConnector.promptWindowLimit.mockReturnValue(1000);

      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent })
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain(
        "Failed to generate style instructions"
      );
    });

    test("includes correct token calculation in response", async () => {
      const documentContent: string = "Sample document";
      mockLLMConnector.getChatCompletion.mockResolvedValue({
        textResponse: "Generated instructions",
      });

      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent })
        .expect(200);

      const tokenInfo = response.body.tokenInfo;
      expect(tokenInfo.documentTokens).toBe(100); // From mock
      expect(tokenInfo.contextWindow).toBe(4096); // From mock
      expect(tokenInfo.reservedTokens).toBe(3000); // Fixed value
      expect(tokenInfo.availableTokens).toBe(1096); // contextWindow - reservedTokens
    });
  });

  describe("LLM Provider Errors", () => {
    test("handles LLM provider not found", async () => {
      const documentContent: string = "Sample document";

      // Mock getLLMProvider to return null
      (
        getLLMProvider as jest.MockedFunction<typeof getLLMProvider>
      ).mockReturnValue(null as any);

      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent })
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain(
        "No LLM provider configured for style analysis"
      );
    });

    test("handles LLM API errors", async () => {
      const documentContent: string = "Sample document";

      // Mock LLM to throw an error
      mockLLMConnector.getChatCompletion.mockRejectedValue(
        new Error("LLM API error")
      );

      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent })
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain(
        "Internal server error during style generation"
      );
    });

    test("handles empty response from LLM", async () => {
      const documentContent: string = "Sample document";

      // Mock LLM to return empty response
      mockLLMConnector.getChatCompletion.mockResolvedValue({
        textResponse: "",
      });

      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent })
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain(
        "Failed to generate style instructions"
      );
    });
  });

  describe("Default Prompt Handling", () => {
    test("uses default prompt for style analysis", async () => {
      const documentContent: string = "Sample document";

      mockLLMConnector.getChatCompletion.mockResolvedValue({
        textResponse: "Style instructions",
      });

      await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent })
        .expect(200);

      const callArgs = mockLLMConnector.getChatCompletion.mock.calls[0];
      const prompt: string = (callArgs[0] as Record<string, unknown>[])[0]
        .content as string;

      // Should use default prompt
      expect(prompt).toContain("legal writing style analyzer");
      expect(prompt).toContain(documentContent);
    });
  });
});
