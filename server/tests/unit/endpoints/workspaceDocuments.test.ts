import request from "supertest";
import express from "express";
import { Router } from "express";
import { workspaceDocumentEndpoints } from "../../../endpoints/workspaceDocuments";
import { Document } from "../../../models/documents";
import { Workspace } from "../../../models/workspace";
import {
  isDocumentProperlyVectorized,
  reVectorizeDocument,
} from "../../../utils/helpers/vectorizationCheck";

// Mock models
jest.mock("../../../models/documents", () => ({
  Document: {
    get: jest.fn(),
    where: jest.fn(),
    update: jest.fn(),
  },
}));

jest.mock("../../../models/workspace", () => ({
  Workspace: {
    get: jest.fn(),
  },
}));

// Mock helpers
jest.mock("../../../utils/helpers/vectorizationCheck", () => ({
  isDocumentProperlyVectorized: jest.fn(),
  reVectorizeDocument: jest.fn(),
}));

// Mock utilities
jest.mock("../../../utils/http", () => ({
  reqBody: jest.fn((req) => req.body),
}));

describe("workspaceDocumentEndpoints", () => {
  let app: express.Application;
  let router: Router;

  beforeEach(() => {
    jest.clearAllMocks();
    app = express();
    router = Router();
    app.use(express.json());
    app.use("/api", router);

    // Default mocks
    (Workspace.get as jest.Mock).mockResolvedValue({
      id: 1,
      name: "Test Workspace",
      slug: "test-workspace",
    });

    (Document.get as jest.Mock).mockResolvedValue({
      id: 1,
      docId: "doc123",
      docpath: "/test/document.pdf",
      workspaceId: 1,
      starred: false,
    });

    (Document.where as jest.Mock).mockResolvedValue([
      {
        id: 1,
        docId: "doc1",
        docpath: "/folder/doc1.pdf",
        workspaceId: 1,
      },
      {
        id: 2,
        docId: "doc2",
        docpath: "/folder/subfolder/doc2.pdf",
        workspaceId: 1,
      },
    ]);

    (Document.update as jest.Mock).mockResolvedValue(true);
    (isDocumentProperlyVectorized as jest.Mock).mockResolvedValue(true);
    (reVectorizeDocument as jest.Mock).mockResolvedValue(true);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("endpoint registration", () => {
    it("should register endpoints on the router", () => {
      const routerPostSpy = jest.spyOn(router, "post");
      workspaceDocumentEndpoints(router);

      expect(routerPostSpy).toHaveBeenCalledWith(
        "/workspace/:slug/re-vectorize",
        expect.any(Function)
      );
      expect(routerPostSpy).toHaveBeenCalledWith(
        "/workspace/:slug/update-star",
        expect.any(Function)
      );
      expect(routerPostSpy).toHaveBeenCalledTimes(2);
    });
  });

  describe("POST /api/workspace/:slug/re-vectorize", () => {
    beforeEach(() => {
      workspaceDocumentEndpoints(router);
    });

    it("should successfully re-vectorize a document", async () => {
      const response = await request(app)
        .post("/api/workspace/test-workspace/re-vectorize")
        .send({ docId: "doc123" })
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: "Document re-vectorized successfully",
        docId: "doc123",
        docPath: "/test/document.pdf",
      });

      expect(reVectorizeDocument).toHaveBeenCalledWith(
        "test-workspace",
        "doc123",
        expect.any(Object)
      );
    });

    it("should handle workspace not found", async () => {
      (Workspace.get as jest.Mock).mockResolvedValueOnce(null);

      const response = await request(app)
        .post("/api/workspace/test-workspace/re-vectorize")
        .send({ docId: "doc123" })
        .expect(404);

      expect(response.body).toEqual({
        success: false,
        message: "Workspace not found",
      });
    });

    it("should handle missing docId", async () => {
      const response = await request(app)
        .post("/api/workspace/test-workspace/re-vectorize")
        .send({})
        .expect(400);

      expect(response.body).toEqual({
        success: false,
        message: "Document ID is required",
      });
    });

    it("should handle document not found", async () => {
      (Document.get as jest.Mock).mockResolvedValueOnce(null);

      const response = await request(app)
        .post("/api/workspace/test-workspace/re-vectorize")
        .send({ docId: "nonexistent" })
        .expect(404);

      expect(response.body).toEqual({
        success: false,
        message: "Document not found",
      });
    });

    it("should handle re-vectorization failure", async () => {
      (reVectorizeDocument as jest.Mock).mockResolvedValueOnce(false);

      const response = await request(app)
        .post("/api/workspace/test-workspace/re-vectorize")
        .send({ docId: "doc123" })
        .expect(500);

      expect(response.body).toEqual({
        success: false,
        message: "Failed to re-vectorize document",
        docId: "doc123",
        docPath: "/test/document.pdf",
      });
    });

    it("should handle errors gracefully", async () => {
      (Document.get as jest.Mock).mockRejectedValueOnce(new Error("DB error"));

      const response = await request(app)
        .post("/api/workspace/test-workspace/re-vectorize")
        .send({ docId: "doc123" })
        .expect(500);

      expect(response.body).toEqual({
        success: false,
        message: "DB error",
      });
    });
  });

  describe("POST /api/workspace/:slug/update-star", () => {
    beforeEach(() => {
      workspaceDocumentEndpoints(router);
    });

    it("should update star status for a single document", async () => {
      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "/test/document.pdf",
          starStatus: true,
        })
        .expect(200);

      expect(response.body).toEqual({
        message: "Document star status updated successfully",
        updatedCount: 1,
        requiresReVectorization: false,
        requiresMetadataUpdate: false,
      });

      expect(Document.update).toHaveBeenCalledWith(1, { starred: true });
    });

    it("should handle document not properly vectorized", async () => {
      (isDocumentProperlyVectorized as jest.Mock).mockResolvedValueOnce(false);

      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "/test/document.pdf",
          starStatus: true,
        })
        .expect(200);

      expect(response.body).toEqual({
        message: "Document requires re-vectorization before it can be starred",
        requiresReVectorization: true,
        requiresMetadataUpdate: true,
        docId: "doc123",
        docPath: "/test/document.pdf",
      });

      expect(Document.update).not.toHaveBeenCalled();
    });

    it("should force update when forceUpdate is true", async () => {
      (isDocumentProperlyVectorized as jest.Mock).mockResolvedValueOnce(false);

      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "/test/document.pdf",
          starStatus: true,
          forceUpdate: true,
        })
        .expect(200);

      expect(response.body.message).toBe(
        "Document star status updated successfully"
      );
      expect(Document.update).toHaveBeenCalled();
    });

    it("should update star status for all documents in a folder", async () => {
      // Reset the isDocumentProperlyVectorized mock to always return true
      (isDocumentProperlyVectorized as jest.Mock).mockReset();
      (isDocumentProperlyVectorized as jest.Mock).mockResolvedValue(true);

      // Mock documents that will match the folder path
      (Document.where as jest.Mock).mockResolvedValueOnce([
        {
          id: 1,
          docId: "doc1",
          docpath: "/folder/doc1.pdf",
          workspaceId: 1,
        },
        {
          id: 2,
          docId: "doc2",
          docpath: "/folder/subfolder/doc2.pdf",
          workspaceId: 1,
        },
      ]);

      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "/folder",
          starStatus: true,
          isFolder: true,
        })
        .expect(200);

      expect(response.body).toEqual({
        message: "Updated 2 documents in folder",
        updatedCount: 2,
      });

      expect(Document.update).toHaveBeenCalledTimes(2);
    });

    it("should handle empty folder", async () => {
      (Document.where as jest.Mock).mockResolvedValueOnce([]);

      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "/empty-folder",
          starStatus: true,
          isFolder: true,
        })
        .expect(200);

      expect(response.body).toEqual({
        message: "No documents found in the specified folder",
        updatedCount: 0,
      });
    });

    it("should skip non-vectorized documents in folder update", async () => {
      // Mock isDocumentProperlyVectorized to return true for first doc, false for second
      (isDocumentProperlyVectorized as jest.Mock).mockReset();
      (isDocumentProperlyVectorized as jest.Mock)
        .mockResolvedValueOnce(true)
        .mockResolvedValueOnce(false);

      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "/folder",
          starStatus: true,
          isFolder: true,
        })
        .expect(200);

      expect(response.body).toEqual({
        message: "Updated 1 documents in folder",
        updatedCount: 1,
      });

      expect(Document.update).toHaveBeenCalledTimes(1);
    });

    it("should handle invalid document path", async () => {
      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "   ", // Whitespace only, will be trimmed to empty
          starStatus: true,
        })
        .expect(400);

      expect(response.body).toEqual({
        message: "Invalid document path",
        error: "Document path cannot be empty",
      });
    });

    it("should handle missing document path", async () => {
      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          starStatus: true,
        })
        .expect(400);

      expect(response.body).toEqual({
        message: "Invalid document path",
        error: "Document path is required",
      });
    });

    it("should handle workspace not found", async () => {
      (Workspace.get as jest.Mock).mockResolvedValueOnce(null);

      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "/test/document.pdf",
          starStatus: true,
        })
        .expect(404);

      expect(response.body).toEqual({
        message: "Workspace not found",
      });
    });

    it("should handle document not found", async () => {
      (Document.get as jest.Mock).mockResolvedValueOnce(null);

      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "/nonexistent.pdf",
          starStatus: true,
        })
        .expect(404);

      expect(response.body).toEqual({
        message: "Document not found",
      });
    });

    it("should handle errors gracefully", async () => {
      (Workspace.get as jest.Mock).mockRejectedValueOnce(new Error("DB error"));

      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "/test/document.pdf",
          starStatus: true,
        })
        .expect(500);

      expect(response.body).toEqual({
        message: "DB error",
      });
    });

    it("should handle folder path with nested documents", async () => {
      // Reset the isDocumentProperlyVectorized mock to always return true
      (isDocumentProperlyVectorized as jest.Mock).mockReset();
      (isDocumentProperlyVectorized as jest.Mock).mockResolvedValue(true);

      (Document.where as jest.Mock).mockResolvedValueOnce([
        {
          id: 1,
          docId: "doc1",
          docpath: "parent/folder/doc1.pdf",
          workspaceId: 1,
        },
        {
          id: 2,
          docId: "doc2",
          docpath: "folder/doc2.pdf",
          workspaceId: 1,
        },
        {
          id: 3,
          docId: "doc3",
          docpath: "other/path/doc3.pdf",
          workspaceId: 1,
        },
      ]);

      const response = await request(app)
        .post("/api/workspace/test-workspace/update-star")
        .send({
          docPath: "folder",
          starStatus: true,
          isFolder: true,
        })
        .expect(200);

      // Should update only doc2 which starts with "folder/"
      // doc1 is in "parent/folder/" which should match the nested check
      expect(response.body.updatedCount).toBe(2);
    });
  });
});
