import {
  addGenerateLegalTaskPromptEndpoint,
  generateLegalTaskPrompt,
} from "../../endpoints/generateLegalTaskPrompt";
import { getLLMProvider } from "../../utils/helpers";
import { Workspace } from "../../models/workspace"; // Mocked below
import { reqBody } from "../../utils/http";
import express from "express";
import request from "supertest";

// Mock dependencies
jest.mock("../../utils/helpers", () => ({
  getLLMProvider: jest.fn(),
}));

jest.mock("../../utils/helpers/supportFunctions", () => ({
  getPromptUpgradeLLM: jest.fn((llm) => Promise.resolve(llm)),
}));

jest.mock("../../models/workspace", () => ({
  Workspace: {
    get: jest.fn(),
  },
}));

jest.mock("../../utils/middleware/validatedRequest", () => ({
  validatedRequest: (_req: any, _res: any, next: any) => next(),
}));

jest.mock("../../utils/middleware/multiUserProtected", () => ({
  flexUserRoleValid: () => (_req: any, _res: any, next: any) => next(),
  ROLES: { admin: "admin", manager: "manager", superuser: "superuser" },
}));

jest.mock("../../utils/http", () => ({
  reqBody: jest.fn(),
}));

jest.mock("../../utils/helpers/supportFunctions", () => ({
  getPromptUpgradeLLM: jest.fn((fallbackProvider) => fallbackProvider),
  getSupportFunctionLLM: jest.fn((_, fallbackProvider) => fallbackProvider),
}));

describe("generateLegalTaskPrompt internal function", () => {
  let mockLLMProviderInstance: any;
  let originalEnv: any;
  let consoleWarnSpy: any;

  beforeEach(() => {
    originalEnv = { ...process.env };
    delete process.env.LLM_PROVIDER;
    delete process.env.LLM_PROVIDER_PU;
    delete process.env.LLM_PROVIDER_SUPPORT; // Clear support provider to match test expectations
    // Suppress console.warn for these specific tests
    consoleWarnSpy = jest.spyOn(console, "warn").mockImplementation(() => {});

    mockLLMProviderInstance = {
      model: "test-model",
      promptWindowLimit: jest.fn().mockReturnValue(4000),
      isValidChatCompletionModel: jest.fn().mockReturnValue(true),
      getChatCompletion: jest
        .fn()
        .mockResolvedValue({ textResponse: "Upgraded by test mock" }),
      constructor: { name: "MockLLM" },
    };
    // getLLMProvider will be mocked per test or globally with specific return values
    (getLLMProvider as jest.MockedFunction<typeof getLLMProvider>)
      .mockReset()
      .mockReturnValue(mockLLMProviderInstance);
  });

  afterEach(() => {
    jest.clearAllMocks();
    process.env = originalEnv;
    // Restore console.warn
    if (consoleWarnSpy)
      (
        consoleWarnSpy as jest.MockedFunction<typeof consoleWarnSpy>
      ).mockRestore();
  });

  test("should use LLM_PROVIDER_PU if set and no provider is passed", async () => {
    process.env.LLM_PROVIDER_PU = "test-llm-pu";
    process.env.LLM_PROVIDER = "test-llm-default"; // Should be ignored

    await generateLegalTaskPrompt("some task for PU");

    expect(getLLMProvider).toHaveBeenCalledWith({
      provider: "test-llm-pu",
      settings_suffix: "_PU",
    });
    expect(mockLLMProviderInstance.getChatCompletion).toHaveBeenCalledTimes(1);
  });

  test("should use LLM_PROVIDER if LLM_PROVIDER_PU is not set and no provider is passed", async () => {
    delete process.env.LLM_PROVIDER_PU; // Ensure it's not set
    process.env.LLM_PROVIDER = "test-llm-default";

    // consoleWarnSpy is already active and will catch the warning without failing the test
    await generateLegalTaskPrompt("some task for default");

    expect(getLLMProvider).toHaveBeenCalledWith({
      provider: "test-llm-default",
    });
    expect(mockLLMProviderInstance.getChatCompletion).toHaveBeenCalledTimes(1);
    // We can assert that it was called if needed, but it won't output to console
    expect(consoleWarnSpy).toHaveBeenCalledWith(
      "LLM_PROVIDER_PU not set. Falling back to LLM_PROVIDER for prompt upgrade."
    );
  });

  test("should use provided LLM instance if passed, ignoring env vars", async () => {
    process.env.LLM_PROVIDER_PU = "test-llm-pu-ignored";
    process.env.LLM_PROVIDER = "test-llm-default-ignored";
    const customMockProvider: any = {
      model: "custom-model",
      promptWindowLimit: jest.fn().mockReturnValue(5000),
      isValidChatCompletionModel: jest.fn().mockReturnValue(true),
      getChatCompletion: jest
        .fn()
        .mockResolvedValue({ textResponse: "Upgraded via custom" }),
      constructor: { name: "CustomMockLLM" },
    };
    (getLLMProvider as jest.MockedFunction<typeof getLLMProvider>).mockClear(); // Ensure it's not called due to fallback logic

    const result: any = await generateLegalTaskPrompt(
      "custom task",
      customMockProvider
    );

    expect(getLLMProvider).not.toHaveBeenCalled();
    expect(customMockProvider.isValidChatCompletionModel).toHaveBeenCalledWith(
      "custom-model"
    );
    expect(customMockProvider.getChatCompletion).toHaveBeenCalledTimes(1);
    expect(result).toBe("Upgraded via custom");
  });

  test("should throw error if no LLM provider can be resolved (no env vars, no passed provider)", async () => {
    delete process.env.LLM_PROVIDER_PU;
    delete process.env.LLM_PROVIDER;
    (
      getLLMProvider as jest.MockedFunction<typeof getLLMProvider>
    ).mockReturnValue(null as any); // Simulate getLLMProvider failing for empty names

    await expect(generateLegalTaskPrompt("failing task")).rejects.toThrow(
      "Could not initialize LLM provider from Configured prompt upgrade LLMs (Support: not set, PU: not set, Default: not set)"
    );
  });

  test("should throw error if chosen default LLM (PU or fallback) is invalid", async () => {
    process.env.LLM_PROVIDER_PU = "invalid-pu-provider";
    process.env.LLM_PROVIDER = undefined; // Ensure fallback is not used initially for this specific test
    (
      getLLMProvider as jest.MockedFunction<typeof getLLMProvider>
    ).mockImplementation(({ provider }: any) => {
      if (provider === "invalid-pu-provider") return undefined as any;
      return undefined as any; // Should not reach here if PU is correctly nulled
    });

    await expect(
      generateLegalTaskPrompt("task for invalid PU")
    ).rejects.toThrow(
      "Could not initialize LLM provider from Configured prompt upgrade LLMs (Support: not set, PU: invalid-pu-provider, Default: not set)"
    );
  });

  test("should throw error if provided LLM instance is faulty (e.g., invalid model)", async () => {
    const faultyProvider: any = {
      model: "faulty-model",
      isValidChatCompletionModel: jest.fn().mockReturnValue(false),
      constructor: { name: "FaultyLLM" },
    };
    await expect(
      generateLegalTaskPrompt("task for faulty provided", faultyProvider)
    ).rejects.toThrow(
      `faulty-model is not valid for chat completion with FaultyLLM!`
    );
  });
});

describe("addGenerateLegalTaskPromptEndpoint", () => {
  let mockSystemLLMLegacyDefaultInstance: any; // For LLM_PROVIDER
  let mockSystemLLMPUInstance: any; // For LLM_PROVIDER_PU
  let mockWorkspaceLLMProviderInstance: any;
  let originalEnv: typeof process.env;

  const setupAppWithEndpoint = () => {
    const app = express() as any;
    app.use(express.json()); // Important for reqBody to work with JSON
    addGenerateLegalTaskPromptEndpoint(app);
    return app;
  };

  beforeEach(() => {
    originalEnv = { ...process.env };
    // Unset by default, set by specific tests
    delete process.env.LLM_PROVIDER;
    delete process.env.LLM_PROVIDER_PU;
    delete process.env.LLM_PROVIDER_SUPPORT; // Clear support provider to match test expectations

    mockSystemLLMLegacyDefaultInstance = {
      model: "system-legacy-model",
      promptWindowLimit: jest.fn().mockReturnValue(4000),
      isValidChatCompletionModel: jest.fn().mockReturnValue(true),
      getChatCompletion: jest.fn().mockResolvedValue({
        textResponse: "Upgraded via legacy system default",
      }),
      constructor: { name: "SystemLegacyLLM" },
    };

    mockSystemLLMPUInstance = {
      model: "system-pu-model",
      promptWindowLimit: jest.fn().mockReturnValue(4100),
      isValidChatCompletionModel: jest.fn().mockReturnValue(true),
      getChatCompletion: jest
        .fn()
        .mockResolvedValue({ textResponse: "Upgraded via PU system default" }),
      constructor: { name: "SystemPULLM" },
    };

    mockWorkspaceLLMProviderInstance = {
      // Renamed for clarity if used alongside system mocks
      model: "workspace-model",
      promptWindowLimit: jest.fn().mockReturnValue(8000),
      isValidChatCompletionModel: jest.fn().mockReturnValue(true),
      getChatCompletion: jest
        .fn()
        .mockResolvedValue({ textResponse: "Upgraded via workspace" }),
      constructor: { name: "WorkspaceLLM" },
    };

    (getLLMProvider as jest.MockedFunction<typeof getLLMProvider>)
      .mockReset()
      .mockImplementation(({ provider } = {}) => {
        if (provider === "test-system-pu-llm") return mockSystemLLMPUInstance;
        if (provider === "test-system-legacy-llm")
          return mockSystemLLMLegacyDefaultInstance;
        if (provider === "fetched-workspace-provider")
          return mockWorkspaceLLMProviderInstance;
        return null;
      });

    (Workspace.get as any).mockClear();
    (reqBody as jest.MockedFunction<typeof reqBody>).mockClear();
  });

  afterEach(() => {
    jest.clearAllMocks();
    process.env = originalEnv;
  });

  test("endpoint should use LLM_PROVIDER_PU when specified, useWorkspaceLLM is false", async () => {
    const app = setupAppWithEndpoint();
    process.env.LLM_PROVIDER_PU = "test-system-pu-llm";
    process.env.LLM_PROVIDER = "test-system-legacy-llm"; // Should be ignored
    (reqBody as jest.MockedFunction<typeof reqBody>).mockReturnValue({
      promptTemplate: "test pu template",
      useWorkspaceLLM: false,
    });

    const response = await request(app)
      .post("/generate-legal-task-prompt")
      .send({});

    expect(getLLMProvider).toHaveBeenCalledWith({
      provider: "test-system-pu-llm",
      settings_suffix: "_PU",
    });
    expect(mockSystemLLMPUInstance.getChatCompletion).toHaveBeenCalledTimes(1);
    expect(
      mockSystemLLMLegacyDefaultInstance.getChatCompletion
    ).not.toHaveBeenCalled();
    expect(
      mockWorkspaceLLMProviderInstance.getChatCompletion
    ).not.toHaveBeenCalled();
    expect(response.status).toBe(200);
    expect(response.body.prompt).toBe("Upgraded via PU system default");
  });

  test("endpoint should use LLM_PROVIDER if LLM_PROVIDER_PU not set, useWorkspaceLLM is false", async () => {
    const app = setupAppWithEndpoint();
    delete process.env.LLM_PROVIDER_PU;
    process.env.LLM_PROVIDER = "test-system-legacy-llm";
    (reqBody as jest.MockedFunction<typeof reqBody>).mockReturnValue({
      promptTemplate: "test legacy default template",
      useWorkspaceLLM: false,
    });
    const consoleWarnSpy = jest
      .spyOn(console, "warn")
      .mockImplementation(() => undefined);

    const response = await request(app)
      .post("/generate-legal-task-prompt")
      .send({});

    expect(getLLMProvider).toHaveBeenCalledWith({
      provider: "test-system-legacy-llm",
    });
    expect(
      mockSystemLLMLegacyDefaultInstance.getChatCompletion
    ).toHaveBeenCalledTimes(1);
    expect(consoleWarnSpy).toHaveBeenCalledWith(
      "LLM_PROVIDER_PU not set. Falling back to LLM_PROVIDER for prompt upgrade."
    );
    expect(response.status).toBe(200);
    expect(response.body.prompt).toBe("Upgraded via legacy system default");
    consoleWarnSpy.mockRestore();
  });

  test("endpoint should use workspace LLM when useWorkspaceLLM is true and workspaceId is provided", async () => {
    const app = setupAppWithEndpoint();
    const workspaceId = "ws123";
    const mockWorkspace = {
      id: workspaceId,
      chatProvider: "fetched-workspace-provider",
      chatModel: "specific-ws-model",
      llmSettings: JSON.stringify({ setting1: "value1" }),
      chatSettings: JSON.stringify({ temperature: 0.77 }),
    };
    (Workspace.get as jest.Mock).mockResolvedValue(mockWorkspace);
    // getLLMProvider will be called with workspace details

    (reqBody as jest.MockedFunction<typeof reqBody>).mockReturnValue({
      promptTemplate: "test template for workspace",
      workspaceId,
      useWorkspaceLLM: true,
    });

    const response = await request(app)
      .post("/generate-legal-task-prompt")
      .send({});

    expect(Workspace.get).toHaveBeenCalledWith({ id: Number(workspaceId) });
    expect(getLLMProvider).toHaveBeenCalledWith({
      provider: "fetched-workspace-provider",
      model: "specific-ws-model",
    });
    expect(
      mockWorkspaceLLMProviderInstance.getChatCompletion
    ).toHaveBeenCalledTimes(1);
    expect(mockSystemLLMPUInstance.getChatCompletion).not.toHaveBeenCalled();
    expect(
      mockSystemLLMLegacyDefaultInstance.getChatCompletion
    ).not.toHaveBeenCalled();
    expect(response.status).toBe(200);
    expect(response.body.prompt).toBe("Upgraded via workspace");
  });

  test("endpoint should use PU LLM if useWorkspaceLLM is true but workspaceId is missing", async () => {
    const app: any = setupAppWithEndpoint();
    process.env.LLM_PROVIDER_PU = "test-system-pu-llm";
    (reqBody as jest.MockedFunction<typeof reqBody>).mockReturnValue({
      promptTemplate: "test template",
      useWorkspaceLLM: true,
      workspaceId: null,
    });

    const response = await request(app)
      .post("/generate-legal-task-prompt")
      .send({});

    expect(getLLMProvider).toHaveBeenCalledWith({
      provider: "test-system-pu-llm",
      settings_suffix: "_PU",
    });
    expect(mockSystemLLMPUInstance.getChatCompletion).toHaveBeenCalledTimes(1);
    expect(response.status).toBe(200);
    expect(response.body.prompt).toBe("Upgraded via PU system default");
  });

  test("endpoint should return 404 if workspace not found when useWorkspaceLLM is true", async () => {
    const app: any = setupAppWithEndpoint();
    (Workspace.get as any).mockResolvedValue(null);
    (reqBody as jest.MockedFunction<typeof reqBody>).mockReturnValue({
      promptTemplate: "test template",
      workspaceId: "ws-not-found",
      useWorkspaceLLM: true,
    });

    const response = await request(app)
      .post("/generate-legal-task-prompt")
      .send({});

    expect(response.status).toBe(404);
    expect(response.body.error).toBe("Workspace not found.");
  });

  test("endpoint should return 500 if specifically chosen workspace LLM is invalid", async () => {
    const app: any = setupAppWithEndpoint();
    const workspaceId: any = "ws-bad-llm";
    const mockWorkspace: any = {
      id: workspaceId,
      llmProvider: "bad-workspace-provider",
      llmModelName: "bad-model",
      llmSettings: null,
      chatSettings: null,
    };
    (Workspace.get as jest.Mock).mockResolvedValue(mockWorkspace);
    // Simulate that getLLMProvider for this workspace returns an invalid/unusable instance
    const badLLMInstance: any = {
      model: "bad-model",
      isValidChatCompletionModel: jest.fn().mockReturnValue(false),
      constructor: { name: "BadLLM" },
    };
    (
      getLLMProvider as jest.MockedFunction<typeof getLLMProvider>
    ).mockImplementationOnce(() => badLLMInstance);

    (reqBody as jest.MockedFunction<typeof reqBody>).mockReturnValue({
      promptTemplate: "test template",
      workspaceId,
      useWorkspaceLLM: true,
    });

    const response = await request(app)
      .post("/generate-legal-task-prompt")
      .send({});

    expect(response.status).toBe(500);
    // The error message comes from the internal generateLegalTaskPrompt function
    expect(response.body.error).toContain(
      `bad-model is not valid for chat completion with BadLLM!`
    );
  });
});
