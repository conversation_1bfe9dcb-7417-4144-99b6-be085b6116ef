import { User } from "../../../models/user";
import prisma from "../../../utils/prisma";

// Unmock the User model to test the actual implementation
jest.unmock("../../../models/user");

// Mock dependencies
jest.mock("../../../models/eventLogs", () => ({
  EventLogs: {
    logEvent: jest.fn(),
  },
}));

// Mock Prisma
jest.mock("../../../utils/prisma", () => ({
  __esModule: true,
  default: {
    users: {
      create: jest.fn(),
      update: jest.fn(),
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    userStyleProfile: {
      findFirst: jest.fn(),
    },
    $transaction: jest.fn(),
  },
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe("User Model Tests", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("custom_system_prompt validation", () => {
    test("should return null for null input", () => {
      const result = User.custom_system_prompt(null);
      expect(result).toBeNull();
    });

    test("should return null for undefined input", () => {
      const result = User.custom_system_prompt(undefined);
      expect(result).toBeNull();
    });

    test("should return null for empty string", () => {
      const result = User.custom_system_prompt("");
      expect(result).toBeNull();
    });

    test("should return null for whitespace-only string", () => {
      const result = User.custom_system_prompt("   ");
      expect(result).toBeNull();
    });

    test("should return trimmed string for valid input", () => {
      const input = "  This is a valid system prompt  ";
      const result = User.custom_system_prompt(input);
      expect(result).toBe("This is a valid system prompt");
    });

    test("should accept string at character limit", () => {
      const input = "a".repeat(10000);
      const result = User.custom_system_prompt(input);
      expect(result).toBe(input);
    });

    test("should throw error for string exceeding character limit", () => {
      const input = "a".repeat(10001);
      expect(() => {
        User.custom_system_prompt(input);
      }).toThrow("Custom system prompt cannot exceed 10,000 characters");
    });

    test("should handle non-string input by converting to string", () => {
      const result = User.custom_system_prompt(123 as any);
      expect(result).toBe("123");
    });

    test("should handle multiline strings", () => {
      const input = "Line 1\nLine 2\nLine 3";
      const result = User.custom_system_prompt(input);
      expect(result).toBe(input);
    });
  });

  describe("Database operations", () => {
    describe("get method", () => {
      test("should call findFirst with correct parameters", async () => {
        const mockUser = {
          id: 1,
          username: "testuser",
          password: "hashedpassword",
          role: "default",
          createdAt: new Date(),
          updatedAt: new Date(),
          custom_ai_userselected: false,
          custom_ai_option: 1,
          custom_ai_selected_engine: "_CUAI",
          custom_system_prompt: null,
          economy_system_id: null,
          organizationId: null,
          suspended: false,
          pfpFilename: null,
        };

        (mockPrisma.users.findFirst as jest.Mock).mockResolvedValue(mockUser);

        const result = await User.get({ id: 1 });

        expect(mockPrisma.users.findFirst).toHaveBeenCalledWith({
          where: { id: 1 },
        });
        expect(result).toEqual({
          id: 1,
          username: "testuser",
          role: "default",
          createdAt: mockUser.createdAt,
          updatedAt: mockUser.updatedAt,
          custom_ai_userselected: false,
          custom_ai_option: 1,
          custom_ai_selected_engine: "_CUAI",
          custom_system_prompt: null,
          economy_system_id: null,
          organizationId: null,
          suspended: false,
          pfpFilename: null,
        });
      });

      test("should return null for non-existent user", async () => {
        (mockPrisma.users.findFirst as jest.Mock).mockResolvedValue(null);

        const result = await User.get({ id: 999 });

        expect(result).toBeNull();
      });
    });

    describe("update method", () => {
      test("should call findUnique and update with correct parameters", async () => {
        const existingUser = {
          id: 1,
          username: "oldname",
          password: "hashedpassword",
          role: "default",
          createdAt: new Date(),
          updatedAt: new Date(),
          custom_ai_userselected: false,
          custom_ai_option: 1,
          custom_ai_selected_engine: "_CUAI",
          custom_system_prompt: null,
          economy_system_id: null,
          organizationId: null,
          suspended: false,
          pfpFilename: null,
        };

        const updatedUser = {
          ...existingUser,
          username: "newname",
          organization: null,
        };

        (mockPrisma.users.findUnique as jest.Mock).mockResolvedValue(
          existingUser
        );
        (mockPrisma.users.update as jest.Mock).mockResolvedValue(updatedUser);

        const result = await User.update(1, { username: "newname" });

        expect(mockPrisma.users.findUnique).toHaveBeenCalledWith({
          where: { id: 1 },
        });
        expect(mockPrisma.users.update).toHaveBeenCalledWith({
          where: { id: 1 },
          data: {
            username: "newname",
            organizationId: null,
          },
          include: { organization: true },
        });
        expect(result.success).toBe(true);
        expect(result.user?.username).toBe("newname");
      });

      test("should return error for non-existent user", async () => {
        (mockPrisma.users.findUnique as jest.Mock).mockResolvedValue(null);

        const result = await User.update(999, { username: "newname" });

        expect(result.success).toBe(false);
        expect(result.error).toBe("User not found");
      });
    });

    describe("count method", () => {
      test("should call count with correct parameters", async () => {
        (mockPrisma.users.count as jest.Mock).mockResolvedValue(5);

        const result = await User.count({ role: "admin" });

        expect(mockPrisma.users.count).toHaveBeenCalledWith({
          where: { role: "admin" },
        });
        expect(result).toBe(5);
      });
    });
  });
});
