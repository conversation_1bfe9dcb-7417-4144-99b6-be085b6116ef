/**
 * Unit tests for robustLlmUtils/config/llmConfig module
 */

import {
  getActiveLLMConfig,
  getConfigValue,
} from "../../../../utils/robustLlmUtils/config/llmConfig";
import { logRobust } from "../../../../utils/robustLlmUtils/utils/logging";

// Mock the logging module
jest.mock("../../../../utils/robustLlmUtils/utils/logging", () => ({
  logRobust: jest.fn(),
}));

describe("llmConfig", () => {
  const originalEnv: any = process.env;

  beforeEach(() => {
    jest.clearAllMocks();
    // Create a clean environment without the loaded .env values
    process.env = {};
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  describe("getConfigValue", () => {
    test("should return suffixed environment variable when available", () => {
      process.env.TEST_KEY_SUFFIX = "suffixed_value";
      process.env.TEST_KEY = "base_value";

      const result: any = getConfigValue("TEST_KEY", "_SUFFIX");
      expect(result).toBe("suffixed_value");
    });

    test("should fallback to base key when suffixed not available", () => {
      process.env.TEST_KEY = "base_value";

      const result: any = getConfigValue("TEST_KEY", "_SUFFIX");
      expect(result).toBe("base_value");
    });

    test("should use fallback key when provided", () => {
      process.env.FALLBACK_KEY = "fallback_value";

      const result: any = getConfigValue("TEST_KEY", "_SUFFIX", "FALLBACK_KEY");
      expect(result).toBe("fallback_value");
    });

    test("should return undefined when no values found", () => {
      const result: any = getConfigValue("NONEXISTENT_KEY", "_SUFFIX");
      expect(result).toBeUndefined();
    });

    test("should handle empty suffix", () => {
      process.env.TEST_KEY = "base_value";

      const result: any = getConfigValue("TEST_KEY", "");
      expect(result).toBe("base_value");
    });

    test("should handle null suffix", () => {
      process.env.TEST_KEY = "base_value";

      const result: any = getConfigValue("TEST_KEY", undefined);
      expect(result).toBe("base_value");
    });
  });

  describe("getActiveLLMConfig", () => {
    test("should return OpenAI configuration with defaults", () => {
      process.env.LLM_PROVIDER = "openai";
      process.env.OPEN_AI_KEY = "test-api-key";

      // Clear the Jest-set OPENAI_API_KEY to use our test value
      delete process.env.OPENAI_API_KEY;

      const config: any = getActiveLLMConfig();

      expect(config.provider).toBe("openai");
      expect(config.apiKey).toBe("test-api-key");
      expect(config.model).toBe("gpt-4o");
      expect(config.modelName).toBe("gpt-4o");
      expect(config.settings_suffix).toBe("");
    });

    test("should use suffixed environment variables when suffix provided", () => {
      process.env.LLM_PROVIDER_CDB = "openai";
      process.env.OPEN_AI_KEY_CDB = "cdb-api-key";
      process.env.OPEN_AI_MODEL_PREF_CDB = "gpt-4-turbo";

      const config: any = getActiveLLMConfig("_CDB");

      expect(config.provider).toBe("openai");
      expect(config.apiKey).toBe("cdb-api-key");
      expect(config.model).toBe("gpt-4-turbo");
      expect(config.modelName).toBe("gpt-4-turbo");
      expect(config.settings_suffix).toBe("_CDB");
    });

    test("should return Gemini configuration with defaults", () => {
      process.env.LLM_PROVIDER = "gemini";
      process.env.GEMINI_API_KEY = "gemini-key";

      const config: any = getActiveLLMConfig();

      expect(config.provider).toBe("gemini");
      expect(config.apiKey).toBe("gemini-key");
      expect(config.model).toBe("gemini-2.5-flash");
      expect(config.modelName).toBe("gemini-2.5-flash");
    });

    test("should return Anthropic configuration", () => {
      process.env.LLM_PROVIDER = "anthropic";
      process.env.ANTHROPIC_API_KEY = "anthropic-key";
      process.env.ANTHROPIC_MODEL_PREF = "claude-3-sonnet";

      const config: any = getActiveLLMConfig();

      expect(config.provider).toBe("anthropic");
      expect(config.apiKey).toBe("anthropic-key");
      expect(config.model).toBe("claude-3-sonnet");
      expect(config.modelName).toBe("claude-3-sonnet");
    });

    test("should return Azure configuration", () => {
      process.env.LLM_PROVIDER = "azure";
      process.env.AZURE_OPENAI_KEY = "azure-key";
      process.env.OPEN_AI_MODEL_PREF = "gpt-4-deployment";
      process.env.AZURE_OPENAI_ENDPOINT = "https://test.openai.azure.com";

      const config: any = getActiveLLMConfig();

      expect(config.provider).toBe("azure");
      expect(config.apiKey).toBe("azure-key");
      expect(config.model).toBe("gpt-4-deployment");
      expect(config.modelName).toBe("gpt-4-deployment");
      expect(config.basePath).toBe("https://test.openai.azure.com");
    });

    test("should handle azureopenai alias", () => {
      process.env.LLM_PROVIDER = "azureopenai";
      process.env.AZURE_OPENAI_KEY = "azure-key";

      const config: any = getActiveLLMConfig();

      expect(config.provider).toBe("azureopenai");
      expect(config.apiKey).toBe("azure-key");
    });

    test("should return LocalAI configuration", () => {
      process.env.LLM_PROVIDER = "localai";
      process.env.LOCAL_AI_API_KEY = "local-key";
      process.env.LOCAL_AI_MODEL_PREF = "local-model";
      process.env.LOCAL_AI_BASE_PATH = "http://localhost:8080";
      process.env.LOCAL_AI_MODEL_TOKEN_LIMIT = "4096";

      const config: any = getActiveLLMConfig();

      expect(config.provider).toBe("localai");
      expect(config.apiKey).toBe("local-key");
      expect(config.model).toBe("local-model");
      expect(config.basePath).toBe("http://localhost:8080");
      expect(config.tokenLimit).toBe("4096");
    });

    test("should return Ollama configuration", () => {
      process.env.LLM_PROVIDER = "ollama";
      process.env.OLLAMA_MODEL_PREF = "llama2";
      process.env.OLLAMA_BASE_PATH = "http://localhost:11434";

      const config: any = getActiveLLMConfig();

      expect(config.provider).toBe("ollama");
      expect(config.model).toBe("llama2");
      expect(config.basePath).toBe("http://localhost:11434");
      expect(config.apiKey).toBeUndefined(); // Ollama doesn't require API key
    });

    test("should default to gemini when no provider specified", () => {
      const config: any = getActiveLLMConfig();

      expect(config.provider).toBe("gemini");
    });

    test("should handle unknown provider gracefully", () => {
      process.env.LLM_PROVIDER = "unknown_provider";

      const config: any = getActiveLLMConfig();

      expect(config.provider).toBe("unknown_provider");
      expect(config.model).toBe("default");
      expect(config.modelName).toBe("default");
      expect(config.apiKey).toBeUndefined();
    });

    test("should convert provider to lowercase", () => {
      process.env.LLM_PROVIDER = "OPENAI";
      process.env.OPEN_AI_KEY = "test-key";

      const config: any = getActiveLLMConfig();

      expect(config.provider).toBe("openai");
    });

    test("should include Gemini safety settings", () => {
      process.env.LLM_PROVIDER = "gemini";
      process.env.GEMINI_API_KEY = "gemini-key";
      process.env.GEMINI_SAFETY_SETTING = "BLOCK_MEDIUM_AND_ABOVE";

      const config: any = getActiveLLMConfig();

      expect(config.safetySetting).toBe("BLOCK_MEDIUM_AND_ABOVE");
    });

    test("should use LLM_SAFETY_LEVEL as fallback for Gemini", () => {
      process.env.LLM_PROVIDER = "gemini";
      process.env.GEMINI_API_KEY = "gemini-key";
      process.env.LLM_SAFETY_LEVEL = "MEDIUM";

      const config: any = getActiveLLMConfig();

      expect(config.safetySetting).toBe("MEDIUM");
    });

    test("should warn when API key missing for providers that require it", () => {
      process.env.LLM_PROVIDER = "openai";
      // Clear all OpenAI API key environment variables
      delete process.env.OPENAI_API_KEY;
      delete process.env.OPEN_AI_KEY;

      getActiveLLMConfig();

      expect(logRobust).toHaveBeenCalledWith(
        "warn",
        "API key for LLM provider 'openai' is missing or not found. This might cause issues."
      );
    });

    test("should not warn for providers that don't require API keys", () => {
      process.env.LLM_PROVIDER = "ollama";
      process.env.OLLAMA_MODEL_PREF = "llama2";
      // No API key set

      getActiveLLMConfig();

      // Should not have warnings about missing API key
      const warnCalls: any = (logRobust as jest.Mock).mock.calls.filter(
        (call: any) => call[0] === "warn"
      );
      const apiKeyWarnings: any = warnCalls.filter(
        (call: any) =>
          call[1].includes("API key") && call[1].includes("missing")
      );
      expect(apiKeyWarnings).toHaveLength(0);
    });

    test("should handle multiple suffixed configurations", () => {
      process.env.LLM_PROVIDER_CDB = "openai";
      process.env.OPEN_AI_KEY_CDB = "cdb-key";
      process.env.LLM_PROVIDER_DD = "anthropic";
      process.env.ANTHROPIC_API_KEY_DD = "dd-key";

      const cdbConfig: any = getActiveLLMConfig("_CDB");
      const ddConfig: any = getActiveLLMConfig("_DD");

      expect(cdbConfig.provider).toBe("openai");
      expect(cdbConfig.apiKey).toBe("cdb-key");
      expect(ddConfig.provider).toBe("anthropic");
      expect(ddConfig.apiKey).toBe("dd-key");
    });
  });
});
