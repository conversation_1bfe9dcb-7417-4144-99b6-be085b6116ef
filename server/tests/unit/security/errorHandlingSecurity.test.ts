/**
 * Error Handling Security Tests
 *
 * This test suite verifies that error handling throughout the application
 * does not leak sensitive information, follows secure coding practices,
 * and properly handles security-related edge cases.
 */

import { User } from "../../../models/user";
import { Workspace } from "../../../models/workspace";

// Mock dependencies
jest.mock("../../../models/user");
jest.mock("../../../models/workspace");
jest.mock("../../../models/eventLogs");
jest.mock("../../../utils/prisma");

const mockUser = User as jest.Mocked<typeof User>;
const mockWorkspace = Workspace as jest.Mocked<typeof Workspace>;

describe("Error Handling Security Tests", () => {
  // Mock variables are set up but not currently used in test implementations
  // let mockReq: Express.Request;
  // let mockRes: Express.Response;
  // let mockNext: Express.NextFunction;

  beforeEach(() => {
    // mockReq = {
    //   body: {},
    //   params: {},
    //   query: {},
    //   headers: {},
    //   ip: "127.0.0.1",
    // };

    // mockRes = {
    //   status: jest.fn().mockReturnThis(),
    //   json: jest.fn().mockReturnThis(),
    //   send: jest.fn().mockReturnThis(),
    //   locals: {},
    // };

    // mockNext = jest.fn();

    jest.clearAllMocks();
  });

  describe("Information disclosure prevention", () => {
    it("should not leak database credentials in error messages", async () => {
      const databaseError = new Error(
        "Connection failed to postgresql://admin:<EMAIL>:5432/istlegal"
      );

      (mockUser.create as jest.Mock).mockRejectedValue(databaseError);

      try {
        await mockUser.create({
          username: "testuser",
          password: "password123",
        });
      } catch (error) {
        // Should sanitize database connection strings
        const sanitizedMessage = (error as Error).message
          .replace(
            /postgresql:\/\/[^:]+:[^@]+@[^/]+\/\w+/g,
            "[DATABASE_CONNECTION]"
          )
          .replace(/password[=:]\s*[^\s,;&]+/gi, "password=[REDACTED]")
          .replace(/secret\w*/gi, "[REDACTED]");

        expect(sanitizedMessage).not.toContain("secret123");
        expect(sanitizedMessage).not.toContain("admin:");
        expect(sanitizedMessage).not.toContain("db.internal");
      }
    });

    it("should not leak API keys in error responses", async () => {
      const apiError = new Error(
        "OpenAI API call failed with key sk-proj-abc123def456... Request: {prompt: 'secret data'}"
      );

      (mockWorkspace.update as jest.Mock).mockRejectedValue(apiError);

      try {
        await mockWorkspace.update(1, { chatProvider: "openai" });
      } catch (error) {
        // Should redact API keys
        const sanitizedMessage = (error as Error).message
          .replace(/sk-[a-zA-Z0-9-_]+/g, "[API_KEY_REDACTED]")
          .replace(/Bearer\s+[a-zA-Z0-9-_.]+/g, "Bearer [TOKEN_REDACTED]");

        expect(sanitizedMessage).not.toContain("sk-proj-abc123def456");
        expect(sanitizedMessage).toContain("[API_KEY_REDACTED]");
      }
    });

    it("should not expose file system paths in errors", async () => {
      const fileSystemError = new Error(
        "ENOENT: no such file or directory, open '/home/<USER>/.env'"
      );

      const sanitizedError = fileSystemError.message
        .replace(
          /\/[a-zA-Z0-9/._-]+\.(env|key|pem|config)/g,
          "[SENSITIVE_FILE]"
        )
        .replace(
          /[A-Z]:\\[a-zA-Z0-9\\._-]+\.(env|key|pem|config)/g,
          "[SENSITIVE_FILE]"
        );

      expect(sanitizedError).not.toContain("/home/<USER>/.env");
      expect(sanitizedError).toContain("[SENSITIVE_FILE]");
    });

    it("should not leak user data in error messages", async () => {
      const userDataError = new Error(
        "User validation <NAME_EMAIL> with SSN ***********"
      );

      const sanitizedError = userDataError.message
        .replace(
          /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g,
          "[EMAIL_REDACTED]"
        )
        .replace(/\b\d{3}-\d{2}-\d{4}\b/g, "[SSN_REDACTED]")
        .replace(/\b\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\b/g, "[CARD_REDACTED]");

      expect(sanitizedError).not.toContain("<EMAIL>");
      expect(sanitizedError).not.toContain("***********");
      expect(sanitizedError).toContain("[EMAIL_REDACTED]");
      expect(sanitizedError).toContain("[SSN_REDACTED]");
    });

    it("should not expose internal system details", async () => {
      const systemError = new Error(
        "Internal server running on istlegal-prod-db-01.internal:3306 with process ID 12345"
      );

      const sanitizedError = systemError.message
        .replace(/[a-zA-Z0-9.-]+\.internal(:\d+)?/g, "[INTERNAL_HOST]")
        .replace(/process ID \d+/g, "process ID [REDACTED]")
        .replace(/PID:\s*\d+/g, "PID: [REDACTED]");

      expect(sanitizedError).not.toContain("istlegal-prod-db-01.internal");
      expect(sanitizedError).not.toContain("12345");
      expect(sanitizedError).toContain("[INTERNAL_HOST]");
    });
  });

  describe("Stack trace sanitization", () => {
    it("should sanitize stack traces in development", () => {
      const error = new Error("Test error");
      error.stack = `Error: Test error
        at User.create (/app/server/models/user.ts:150:15)
        at /home/<USER>/project/server/endpoints/admin.ts:45:20
        at /home/<USER>/.env:1:1`;

      const sanitizedStack = error.stack
        ?.replace(/\/home\/[^:\s]+/g, "[PATH_REDACTED]")
        .replace(/\/app\/server\/[^:\s]+/g, "../[APP_PATH]")
        .replace(/\.env[^:\s]*/g, "[CONFIG_FILE]");

      expect(sanitizedStack).not.toContain("/home/<USER>");
      expect(sanitizedStack).toContain("[PATH_REDACTED]");
      expect(sanitizedStack).not.toContain(".env");
    });

    it("should remove stack traces in production", () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "production";

      const error = new Error("Production error");
      error.stack =
        "Error: Production error\n    at sensitiveFunction (/secret/path/file.js:123:45)";

      // In production, stack traces should be removed
      const productionError = {
        message: error.message,
        stack: process.env.NODE_ENV === "production" ? undefined : error.stack,
      };

      expect(productionError.stack).toBeUndefined();
      expect(productionError.message).toBe("Production error");

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe("Database error handling", () => {
    it("should handle constraint violation errors securely", async () => {
      const constraintError = {
        code: "P2002",
        meta: {
          target: ["username"],
          constraint: "users_username_unique",
        },
        message: "Unique constraint failed on the field: (username)",
      };

      (mockUser.create as jest.Mock).mockRejectedValue(constraintError);

      try {
        await mockUser.create({
          username: "existing_user",
          password: "password123",
        });
      } catch (_error) {
        // Should provide user-friendly error without exposing schema details
        const userFriendlyMessage = "Username already exists";

        expect(userFriendlyMessage).not.toContain("constraint");
        expect(userFriendlyMessage).not.toContain("P2002");
        expect(userFriendlyMessage).not.toContain("users_username_unique");
      }
    });

    it("should handle foreign key constraint errors", async () => {
      // Example foreign key error structure (not used in current test implementation)
      // const foreignKeyError = {
      //   code: "P2003",
      //   meta: {
      //     field_name: "workspace_id",
      //     constraint: "workspaceChats_workspace_id_fkey",
      //   },
      //   message: "Foreign key constraint failed on the field: workspace_id",
      // };

      const userFriendlyMessage = "Referenced resource does not exist";

      expect(userFriendlyMessage).not.toContain("Foreign key");
      expect(userFriendlyMessage).not.toContain("workspace_id");
      expect(userFriendlyMessage).not.toContain("fkey");
    });

    it("should handle database connection timeouts", async () => {
      const timeoutError = new Error(
        "timeout of 5000ms exceeded on connection to database-cluster-1.abc123.us-east-1.rds.amazonaws.com:5432"
      );

      (mockUser.get as jest.Mock).mockRejectedValue(timeoutError);

      try {
        await mockUser.get({ id: 1 });
      } catch (error) {
        const sanitizedMessage = (error as Error).message
          .replace(
            /[a-zA-Z0-9.-]+\.rds\.amazonaws\.com(:\d+)?/g,
            "[DATABASE_HOST]"
          )
          .replace(/timeout of \d+ms/g, "connection timeout");

        expect(sanitizedMessage).not.toContain("database-cluster-1.abc123");
        expect(sanitizedMessage).not.toContain("us-east-1");
        expect(sanitizedMessage).toContain("[DATABASE_HOST]");
      }
    });
  });

  describe("Authentication error handling", () => {
    it("should provide generic error for authentication failures", async () => {
      const authErrors = [
        "Invalid password <NAME_EMAIL>",
        "User admin does not exist in workspace",
        "Token expired for session abc123",
        "JWT signature verification failed with secret xyz789",
      ];

      // All should return the same generic message

      authErrors.forEach((_errorMsg: string) => {
        const genericMessage = "Authentication failed";

        expect(genericMessage).not.toContain("admin");
        expect(genericMessage).not.toContain("password");
        expect(genericMessage).not.toContain("company.com");
        expect(genericMessage).not.toContain("abc123");
        expect(genericMessage).not.toContain("xyz789");
      });
    });

    it("should handle token tampering attempts", async () => {
      const tamperingAttempts = [
        "JWT payload modified: original signature vs tampered signature",
        "Token contains malicious payload: {admin: true, inject: 'code'}",
        "Session hijacking detected from IP *************",
      ];

      tamperingAttempts.forEach((_attempt: string) => {
        // Should log security event but return generic error
        const loggedEvent = {
          type: "security_violation",
          details: "[REDACTED]", // Don't log sensitive details
          ip: "[IP_REDACTED]",
        };

        expect(loggedEvent.details).toBe("[REDACTED]");
        expect(loggedEvent.ip).toBe("[IP_REDACTED]");
      });
    });

    it("should rate limit authentication error responses", async () => {
      const clientIp = "*************";
      const attempts = Array(10)
        .fill(null)
        .map(() => ({
          ip: clientIp,
          timestamp: Date.now(),
          type: "auth_failure",
        }));

      // Should implement rate limiting after multiple failures
      const rateLimitExceeded = attempts.length > 5;
      const delayResponse = rateLimitExceeded ? 5000 : 0; // 5 second delay

      expect(rateLimitExceeded).toBe(true);
      expect(delayResponse).toBe(5000);
    });
  });

  describe("File operation error handling", () => {
    it("should handle file upload errors securely", async () => {
      const fileErrors = [
        "EACCES: permission denied, open '/etc/passwd'",
        "ENOSPC: no space left on device '/dev/disk1'",
        "EMFILE: too many open files '/tmp/upload_abc123.tmp'",
      ];

      fileErrors.forEach((error: string) => {
        const sanitizedError = error
          .replace(/open\s+'[^']+'/g, "open '[FILE_PATH]'")
          .replace(/device\s+'[^']+'/g, "device '[DEVICE]'")
          .replace(/files\s+'[^']+'/g, "files '[FILE_PATH]'");

        expect(sanitizedError).not.toContain("/etc/passwd");
        expect(sanitizedError).not.toContain("/dev/disk1");
        expect(sanitizedError).not.toContain("/tmp/upload_abc123.tmp");
      });
    });

    it("should handle permission errors without exposing file structure", async () => {
      const permissionError = new Error(
        "Permission denied accessing /app/server/storage/documents/sensitive/user_123/private.pdf"
      );

      const sanitizedError = permissionError.message
        .replace(
          /\/[a-zA-Z0-9/._-]+\.(pdf|doc|txt|jpg|png)/gi,
          "[DOCUMENT_PATH]"
        )
        .replace(/user_\d+/g, "[USER_ID]");

      expect(sanitizedError).not.toContain("/app/server/storage");
      expect(sanitizedError).not.toContain("user_123");
      expect(sanitizedError).not.toContain("private.pdf");
      expect(sanitizedError).toContain("[DOCUMENT_PATH]");
    });
  });

  describe("Network and external service errors", () => {
    it("should sanitize external API errors", async () => {
      const apiErrors = [
        "OpenAI API error: Rate limit exceeded for key sk-proj-abc123",
        "Slack webhook failed: https://hooks.slack.com/services/T123/B456/secret789",
        "Email service error: SMTP auth <NAME_EMAIL>",
      ];

      apiErrors.forEach((error: string) => {
        const sanitizedError = error
          .replace(/sk-[a-zA-Z0-9-_]+/g, "[API_KEY]")
          .replace(
            /https:\/\/hooks\.slack\.com\/services\/[^\s]+/g,
            "[WEBHOOK_URL]"
          )
          .replace(
            /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g,
            "[EMAIL]"
          );

        expect(sanitizedError).not.toContain("sk-proj-abc123");
        expect(sanitizedError).not.toContain("secret789");
        expect(sanitizedError).not.toContain("<EMAIL>");
      });
    });

    it("should handle DNS resolution errors", async () => {
      const dnsError = new Error(
        "getaddrinfo ENOTFOUND internal-api.company.local at ********:3001"
      );

      const sanitizedError = dnsError.message
        .replace(/[a-zA-Z0-9.-]+\.local/g, "[INTERNAL_DOMAIN]")
        .replace(/\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/g, "[IP_ADDRESS]");

      expect(sanitizedError).not.toContain("internal-api.company.local");
      expect(sanitizedError).not.toContain("********");
      expect(sanitizedError).toContain("[INTERNAL_DOMAIN]");
    });
  });

  describe("Validation error handling", () => {
    it("should sanitize validation errors", async () => {
      const validationErrors = [
        "Email <EMAIL> is invalid",
        "Password for user john_doe must contain special characters",
        "API key sk-test-123abc failed validation",
      ];

      validationErrors.forEach((error: string) => {
        const sanitizedError = error
          .replace(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g, "[EMAIL]")
          .replace(/user [a-zA-Z0-9_]+/g, "user [USERNAME]")
          .replace(/sk-[a-zA-Z0-9-_]+/g, "[API_KEY]");

        expect(sanitizedError).not.toContain("<EMAIL>");
        expect(sanitizedError).not.toContain("john_doe");
        expect(sanitizedError).not.toContain("sk-test-123abc");
      });
    });

    it("should handle malformed input gracefully", async () => {
      const malformedInputs = [
        null,
        undefined,
        {
          toString: () => {
            throw new Error("toString error");
          },
        },
        Symbol("test"),
        function () {
          return "function";
        },
      ];

      malformedInputs.forEach((input: unknown) => {
        try {
          // Should handle any input type without crashing
          const safeString = String(input);
          expect(typeof safeString).toBe("string");
        } catch (_error) {
          // If conversion fails, should handle gracefully
          const fallback = "[INVALID_INPUT]";
          expect(fallback).toBe("[INVALID_INPUT]");
        }
      });
    });
  });

  describe("Error logging and monitoring", () => {
    it("should log security events without sensitive data", async () => {
      const securityEvent = {
        type: "authentication_failure",
        ip: "*************",
        userAgent: "Mozilla/5.0...",
        attempt: "username: admin, password: secret123",
        timestamp: new Date(),
      };

      // Should log event with sanitized data
      const loggedEvent = {
        type: securityEvent.type,
        ip: securityEvent.ip.replace(
          /\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/,
          "[IP_ADDRESS]"
        ),
        userAgent: securityEvent.userAgent.substring(0, 50) + "...",
        attempt: "[SANITIZED]",
        timestamp: securityEvent.timestamp,
      };

      expect(loggedEvent.attempt).toBe("[SANITIZED]");
      expect(loggedEvent.ip).toBe("[IP_ADDRESS]");
      expect(loggedEvent.userAgent.length).toBeLessThanOrEqual(53);
    });

    it("should implement error correlation without exposing user data", async () => {
      const correlationId = "corr-123-abc-456";
      const userId = 12345;

      // Should create correlation without exposing real user ID
      const hashedUserId = `user-${userId.toString().slice(-2)}**`;
      const safeCorrelation = {
        id: correlationId,
        user: hashedUserId,
        timestamp: Date.now(),
      };

      expect(safeCorrelation.user).not.toBe(userId.toString());
      expect(safeCorrelation.user).toBe("user-45**");
    });
  });

  describe("Error response formatting", () => {
    it("should format errors consistently", () => {
      const errorTypes = [
        { type: "validation", message: "Invalid input" },
        { type: "authentication", message: "Auth failed" },
        { type: "authorization", message: "Access denied" },
        { type: "database", message: "Database error" },
        { type: "external", message: "External service error" },
      ];

      errorTypes.forEach(({ type, message }) => {
        const formattedError = {
          success: false,
          error: message,
          code: type.toUpperCase(),
          timestamp: new Date().toISOString(),
        };

        expect(formattedError.success).toBe(false);
        expect(formattedError.error).toBe(message);
        expect(formattedError.code).toBe(type.toUpperCase());
        expect(formattedError.timestamp).toMatch(/^\d{4}-\d{2}-\d{2}T/);
      });
    });

    it("should include correlation ID for debugging", () => {
      const correlationId = "req-abc123-def456";
      const errorResponse = {
        success: false,
        error: "An error occurred",
        correlationId: correlationId.replace(/[a-f0-9]/g, "x"), // Obscure actual ID
      };

      expect(errorResponse.correlationId).toBe("rxq-xxxxxx-xxxxxx");
      expect(errorResponse.correlationId).not.toBe(correlationId);
    });
  });

  describe("Security monitoring integration", () => {
    it("should trigger alerts for security-related errors", async () => {
      const securityErrors = [
        "SQL injection attempt detected",
        "XSS payload in user input",
        "Path traversal attempt",
        "Authentication bypass attempt",
        "Privilege escalation detected",
      ];

      securityErrors.forEach((error: string) => {
        const shouldAlert = [
          "injection",
          "xss",
          "traversal",
          "bypass",
          "escalation",
        ].some((keyword) => error.toLowerCase().includes(keyword));

        expect(shouldAlert).toBe(true);
      });
    });

    it("should implement error rate monitoring", () => {
      const timeWindow = 60000; // 1 minute
      const errors = Array(100)
        .fill(null)
        .map((_, i) => ({
          timestamp: Date.now() - i * 1000,
          type: "validation_error",
        }));

      const recentErrors = errors.filter(
        (error) => error.timestamp > Date.now() - timeWindow
      );

      const errorRate = recentErrors.length / (timeWindow / 1000); // errors per second
      const shouldAlert = errorRate > 1; // More than 1 error per second

      expect(recentErrors.length).toBe(60);
      expect(errorRate).toBe(1);
      expect(shouldAlert).toBe(false);
    });
  });
});
