/**
 * WebSocket Connection and Management Tests
 *
 * Tests WebSocket connection establishment, authentication, and basic management
 * for the agent WebSocket system.
 */

import WebSocket from "ws";
import { WorkspaceAgentInvocation } from "../../models/workspaceAgentInvocation";
import { <PERSON><PERSON><PERSON><PERSON> } from "../../utils/agents";
import { Telemetry } from "../../models/telemetry";

// Mock dependencies
jest.mock("../../models/workspaceAgentInvocation");
jest.mock("../../utils/agents");
jest.mock("../../models/telemetry");
jest.mock("../../models/user");
jest.mock("../../models/workspace");

// Extended WebSocket type for testing
interface TestWebSocket extends WebSocket {
  handleFeedback?: (message: string) => void;
  checkBailCommand?: (data: string) => void;
}

// Mock implementation of WebSocket server for testing
class MockWebSocketServer {
  private connections: Map<string, TestWebSocket> = new Map();
  private handlers: Map<string, Function> = new Map();

  on(event: string, handler: Function) {
    this.handlers.set(event, handler);
  }

  simulateConnection(uuid: string): TestWebSocket {
    const mockWs: TestWebSocket = {
      send: jest.fn(),
      close: jest.fn(),
      on: jest.fn(),
      handleFeedback: undefined,
      checkBailCommand: undefined,
    } as any;

    this.connections.set(uuid, mockWs);

    // Simulate endpoint behavior
    if (!uuid) {
      mockWs.close();
      return mockWs;
    }

    // Simulate AgentHandler initialization
    const AgentHandler = require("../../utils/agents").AgentHandler;
    const handler = new AgentHandler();

    // Simulate the async init process
    handler
      .init({ uuid })
      .then(() => {
        // If handler has no invocation, close
        if (!handler.invocation) {
          mockWs.close();
          return;
        }

        // Send telemetry
        const Telemetry = require("../../models/telemetry").Telemetry;
        Telemetry.sendTelemetry("agent_chat_started");

        // Create AIbitat
        handler.createAIbitat({ socket: mockWs }).catch((error: Error) => {
          console.error("AIbitat creation error:", error);
          mockWs.close();
        });

        // Start agent cluster
        handler.startAgentCluster().catch((error: Error) => {
          console.error("Agent cluster startup error:", error);
          mockWs.close();
        });
      })
      .catch(() => {
        mockWs.close();
      });

    // Set up event handlers like the real endpoint does
    mockWs.on("message", () => {});
    mockWs.on("close", () => {
      handler.closeAlert();
      const WorkspaceAgentInvocation =
        require("../../models/workspaceAgentInvocation").WorkspaceAgentInvocation;
      WorkspaceAgentInvocation.close(uuid);
    });

    // Add checkBailCommand method
    mockWs.checkBailCommand = jest.fn();

    return mockWs;
  }

  simulateMessage(uuid: string, message: string) {
    const ws = this.connections.get(uuid);
    if (ws && ws.handleFeedback) {
      ws.handleFeedback(message);
    }
  }

  getConnection(uuid: string): TestWebSocket | undefined {
    return this.connections.get(uuid);
  }
}

describe("WebSocket Connection and Management", () => {
  let mockWSServer: MockWebSocketServer;
  let mockAgentHandler: jest.Mocked<AgentHandler>;
  let _mockInvocation: any;

  beforeEach(() => {
    jest.clearAllMocks();

    mockWSServer = new MockWebSocketServer();

    // Mock AgentHandler
    mockAgentHandler = {
      init: jest.fn().mockResolvedValue({}),
      invocation: { id: "test-invocation" },
      closeAlert: jest.fn(),
      log: jest.fn(),
      createAIbitat: jest.fn().mockResolvedValue({}),
      startAgentCluster: jest.fn().mockResolvedValue({}),
      aibitat: {
        abort: jest.fn(),
      },
    } as any;

    (AgentHandler as jest.MockedClass<typeof AgentHandler>).mockImplementation(
      () => mockAgentHandler
    );

    // Mock WorkspaceAgentInvocation
    _mockInvocation = {
      id: "test-invocation",
      uuid: "test-uuid",
      workspaceId: "test-workspace",
    };

    (WorkspaceAgentInvocation.close as jest.Mock) = jest.fn();

    // Mock Telemetry
    (Telemetry.sendTelemetry as jest.Mock) = jest.fn().mockResolvedValue({});
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe("Connection Establishment", () => {
    it("should establish WebSocket connection with valid UUID", async () => {
      const uuid = "valid-test-uuid";
      const ws = mockWSServer.simulateConnection(uuid);

      // Wait for async initialization
      await new Promise((resolve) => setTimeout(resolve, 0));

      expect(mockAgentHandler.init).toHaveBeenCalledWith({ uuid });
      expect(ws.on).toHaveBeenCalledWith("message", expect.any(Function));
      expect(ws.on).toHaveBeenCalledWith("close", expect.any(Function));
    });

    it("should reject connection without UUID", async () => {
      const ws = mockWSServer.simulateConnection("");

      expect(ws.close).toHaveBeenCalled();
      expect(mockAgentHandler.init).not.toHaveBeenCalled();
    });

    it("should reject connection with invalid agent invocation", async () => {
      mockAgentHandler.invocation = null;
      const ws = mockWSServer.simulateConnection("invalid-uuid");

      // Wait for async initialization
      await new Promise((resolve) => setTimeout(resolve, 10));

      expect(ws.close).toHaveBeenCalled();
    });

    it("should send telemetry on successful connection", async () => {
      const uuid = "telemetry-test-uuid";
      mockWSServer.simulateConnection(uuid);

      // Wait for async initialization
      await new Promise((resolve) => setTimeout(resolve, 0));

      expect(Telemetry.sendTelemetry).toHaveBeenCalledWith(
        "agent_chat_started"
      );
    });
  });

  describe("Connection Management", () => {
    let ws: TestWebSocket;
    const testUuid = "connection-mgmt-uuid";

    beforeEach(() => {
      ws = mockWSServer.simulateConnection(testUuid);
    });

    it("should set up message relay handlers", () => {
      expect(ws.on).toHaveBeenCalledWith("message", expect.any(Function));
      expect(typeof ws.handleFeedback).toBe("undefined"); // Initially undefined
      expect(typeof ws.checkBailCommand).toBe("function");
    });

    it("should handle WebSocket close event", () => {
      // Simulate close event
      const closeHandler = (ws.on as jest.Mock).mock.calls.find(
        (call) => call[0] === "close"
      )?.[1];

      if (closeHandler) {
        closeHandler();
      }

      expect(mockAgentHandler.closeAlert).toHaveBeenCalled();
      expect(WorkspaceAgentInvocation.close).toHaveBeenCalledWith(testUuid);
    });

    it("should initialize AI agent cluster", async () => {
      // Wait for async initialization
      await new Promise((resolve) => setTimeout(resolve, 0));

      expect(mockAgentHandler.createAIbitat).toHaveBeenCalledWith({
        socket: ws,
      });
      expect(mockAgentHandler.startAgentCluster).toHaveBeenCalled();
    });
  });

  describe("Error Handling", () => {
    it("should handle AgentHandler initialization errors", async () => {
      const error = new Error("Agent initialization failed");
      mockAgentHandler.init.mockRejectedValue(error);

      const ws = mockWSServer.simulateConnection("error-test-uuid");

      // Wait for async initialization to fail
      await new Promise((resolve) => setTimeout(resolve, 10));

      // When init fails, connection should be closed
      expect(ws.close).toHaveBeenCalled();
    });

    it("should handle AIbitat creation errors", async () => {
      // Suppress console.error for this test
      const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();

      const error = new Error("AIbitat creation failed");
      mockAgentHandler.createAIbitat.mockRejectedValue(error);

      const ws = mockWSServer.simulateConnection("aibitat-error-uuid");

      // Wait for initialization and AIbitat creation
      await new Promise((resolve) => setTimeout(resolve, 10));

      // The mock now catches createAIbitat errors and closes the connection
      expect(mockAgentHandler.createAIbitat).toHaveBeenCalled();
      expect(ws.close).toHaveBeenCalled();

      // Restore console.error
      consoleErrorSpy.mockRestore();
    });

    it("should handle agent cluster startup errors", async () => {
      // Suppress console.error for this test
      const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();

      const error = new Error("Agent cluster startup failed");
      mockAgentHandler.startAgentCluster.mockRejectedValue(error);

      const ws = mockWSServer.simulateConnection("cluster-error-uuid");

      // Wait for initialization and cluster startup
      await new Promise((resolve) => setTimeout(resolve, 10));

      // The mock now catches startAgentCluster errors and closes the connection
      expect(mockAgentHandler.startAgentCluster).toHaveBeenCalled();
      expect(ws.close).toHaveBeenCalled();

      // Restore console.error
      consoleErrorSpy.mockRestore();
    });
  });

  describe("Connection Lifecycle", () => {
    it("should properly initialize full connection lifecycle", async () => {
      const uuid = "lifecycle-test-uuid";
      const ws = mockWSServer.simulateConnection(uuid);

      // Wait for async initialization
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Verify initialization sequence
      expect(mockAgentHandler.init).toHaveBeenCalledWith({ uuid });
      expect(ws.on).toHaveBeenCalledTimes(2); // message and close handlers
      expect(mockAgentHandler.createAIbitat).toHaveBeenCalledWith({
        socket: ws,
      });
      expect(mockAgentHandler.startAgentCluster).toHaveBeenCalled();
      expect(Telemetry.sendTelemetry).toHaveBeenCalledWith(
        "agent_chat_started"
      );
    });

    it("should handle graceful disconnection", () => {
      const uuid = "disconnect-test-uuid";
      const ws = mockWSServer.simulateConnection(uuid);

      // Simulate close event
      const closeHandler = (ws.on as jest.Mock).mock.calls.find(
        (call) => call[0] === "close"
      )?.[1];

      if (closeHandler) {
        closeHandler();
      }

      expect(mockAgentHandler.closeAlert).toHaveBeenCalled();
      expect(WorkspaceAgentInvocation.close).toHaveBeenCalledWith(uuid);
    });
  });

  describe("WebSocket Message Relay", () => {
    it("should relay messages to handleFeedback when available", () => {
      const uuid = "relay-test-uuid";
      const ws = mockWSServer.simulateConnection(uuid);

      const mockHandleFeedback = jest.fn();
      ws.handleFeedback = mockHandleFeedback;

      const testMessage = "test message";
      mockWSServer.simulateMessage(uuid, testMessage);

      expect(mockHandleFeedback).toHaveBeenCalledWith(testMessage);
    });

    it("should fallback to checkBailCommand when handleFeedback unavailable", () => {
      const uuid = "bailcmd-test-uuid";
      const ws = mockWSServer.simulateConnection(uuid);

      const mockCheckBailCommand = jest.fn();
      ws.checkBailCommand = mockCheckBailCommand;
      ws.handleFeedback = undefined;

      const testMessage = '{"feedback": "exit"}';
      if (ws.checkBailCommand) {
        ws.checkBailCommand(testMessage);
      }

      expect(mockCheckBailCommand).toHaveBeenCalledWith(testMessage);
    });
  });

  describe("Authentication and Authorization", () => {
    it("should validate workspace access for agent invocation", async () => {
      const uuid = "auth-test-uuid";
      mockAgentHandler.invocation = {
        id: 1,
        workspace_id: 1,
        user_id: 1,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
        prompt: "test prompt",
        uuid: "test-uuid",
        closed: false,
        thread_id: null,
        workspaces: {
          id: 1,
          name: "Test Workspace",
          createdAt: new Date(),
          lastUpdatedAt: new Date(),
          pfpFilename: null,
          user_id: 1,
          type: null,
          slug: "test-workspace",
          sharedWithOrg: false,
          vectorTag: null,
          openAiTemp: null,
          openAiHistory: 20,
          openAiPrompt: null,
          similarityThreshold: 0.5,
          chatMode: "query",
          queryRefusalResponse: null,
          chatModel: null,
          order: 0,
          chatProvider: null,
          embeddingProvider: null,
          embeddingModel: null,
          topN: null,
          agentProvider: null,
          agentModel: null,
          pdr: null,
          vectorSearchMode: null,
          chatType: null,
          hasMessages: false,
        },
      };

      const ws = mockWSServer.simulateConnection(uuid);

      expect(mockAgentHandler.init).toHaveBeenCalledWith({ uuid });
      expect(ws.close).not.toHaveBeenCalled();
    });

    it("should reject unauthorized invocation access", async () => {
      const uuid = "unauth-test-uuid";
      mockAgentHandler.invocation = null;

      const ws = mockWSServer.simulateConnection(uuid);

      // Wait for async initialization
      await new Promise((resolve) => setTimeout(resolve, 10));

      expect(ws.close).toHaveBeenCalled();
    });
  });
});
