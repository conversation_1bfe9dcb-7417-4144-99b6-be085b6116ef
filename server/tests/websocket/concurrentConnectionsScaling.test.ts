/**
 * Concurrent WebSocket Connections and Scaling Tests
 *
 * Tests the system's ability to handle multiple simultaneous WebSocket connections,
 * connection scaling patterns, resource management, and performance under load.
 */

import { EventEmitter } from "events";
import { websocket } from "../../utils/agents/aibitat/plugins/websocket";

// Connection pool management
interface ConnectionInfo {
  id: string;
  userId: string;
  workspaceId: string;
  connectedAt: number;
  lastActivity: number;
  messageCount: number;
  status:
    | "connecting"
    | "connected"
    | "active"
    | "idle"
    | "disconnecting"
    | "disconnected";
}

interface ConnectionPool {
  connections: Map<string, ConnectionInfo>;
  maxConnections: number;
  activeConnections: number;
  totalConnectionsCreated: number;
  totalConnectionsClosed: number;
  peakConcurrentConnections: number;
}

// Mock WebSocket for scaling tests
class ScalableWebSocket extends EventEmitter {
  public id: string;
  public readyState = 1; // OPEN
  public send = jest.fn();
  public close = jest.fn();
  public userId: string;
  public workspaceId: string;

  private messagesSent = 0;
  private messagesReceived = 0;
  private connectionStartTime: number;
  private lastActivity: number;

  constructor(id: string, userId: string, workspaceId: string) {
    super();
    this.id = id;
    this.userId = userId;
    this.workspaceId = workspaceId;
    this.connectionStartTime = Date.now();
    this.lastActivity = Date.now();

    this.send.mockImplementation((data) => {
      this.messagesSent++;
      this.lastActivity = Date.now();
      this.emit("messageSent", { data, timestamp: Date.now() });
    });
  }

  public simulateIncomingMessage(message: any): void {
    this.messagesReceived++;
    this.lastActivity = Date.now();
    this.emit("message", JSON.stringify(message));
  }

  public getConnectionStats() {
    return {
      id: this.id,
      userId: this.userId,
      workspaceId: this.workspaceId,
      messagesSent: this.messagesSent,
      messagesReceived: this.messagesReceived,
      connectionDuration: Date.now() - this.connectionStartTime,
      lastActivity: this.lastActivity,
      isActive: Date.now() - this.lastActivity < 30000, // Active within 30 seconds
    };
  }

  public simulateDisconnection(): void {
    this.readyState = 3; // CLOSED
    this.emit("close", 1000, "Normal closure");
  }
}

// Connection pool manager
class WebSocketConnectionPool {
  private pool: ConnectionPool;
  private connections: Map<string, ScalableWebSocket> = new Map();
  private performanceMetrics: {
    connectionTimes: number[];
    messageThroughput: number[];
    resourceUsage: Array<{
      memory: number;
      connections: number;
      timestamp: number;
    }>;
  } = {
    connectionTimes: [],
    messageThroughput: [],
    resourceUsage: [],
  };

  constructor(maxConnections = 1000) {
    this.pool = {
      connections: new Map(),
      maxConnections,
      activeConnections: 0,
      totalConnectionsCreated: 0,
      totalConnectionsClosed: 0,
      peakConcurrentConnections: 0,
    };
  }

  public createConnection(
    userId: string,
    workspaceId: string
  ): ScalableWebSocket {
    if (this.pool.activeConnections >= this.pool.maxConnections) {
      throw new Error("Connection pool exhausted");
    }

    const connectionId = `conn-${Date.now()}-${this.pool.totalConnectionsCreated}`;
    const startTime = performance.now();

    const connection = new ScalableWebSocket(connectionId, userId, workspaceId);

    this.connections.set(connectionId, connection);

    const connectionInfo: ConnectionInfo = {
      id: connectionId,
      userId,
      workspaceId,
      connectedAt: Date.now(),
      lastActivity: Date.now(),
      messageCount: 0,
      status: "connected",
    };

    this.pool.connections.set(connectionId, connectionInfo);
    this.pool.activeConnections++;
    this.pool.totalConnectionsCreated++;

    if (this.pool.activeConnections > this.pool.peakConcurrentConnections) {
      this.pool.peakConcurrentConnections = this.pool.activeConnections;
    }

    const connectionTime = performance.now() - startTime;
    this.performanceMetrics.connectionTimes.push(connectionTime);

    // Set up connection event handlers
    connection.on("messageSent", () => {
      const info = this.pool.connections.get(connectionId);
      if (info) {
        info.messageCount++;
        info.lastActivity = Date.now();
        info.status = "active";
      }
    });

    connection.on("close", () => {
      this.closeConnection(connectionId);
    });

    return connection;
  }

  public closeConnection(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    const connectionInfo = this.pool.connections.get(connectionId);

    if (connection && connectionInfo) {
      // Remove from collections first to prevent circular calls
      this.connections.delete(connectionId);
      this.pool.connections.delete(connectionId);
      this.pool.activeConnections--;
      this.pool.totalConnectionsClosed++;
      connectionInfo.status = "disconnected";

      // Then trigger disconnection
      connection.simulateDisconnection();
    }
  }

  public getPoolStats() {
    return {
      ...this.pool,
      connections: Array.from(this.pool.connections.values()),
      averageConnectionTime:
        this.performanceMetrics.connectionTimes.length > 0
          ? this.performanceMetrics.connectionTimes.reduce((a, b) => a + b, 0) /
            this.performanceMetrics.connectionTimes.length
          : 0,
    };
  }

  public getConnectionsByWorkspace(workspaceId: string): ConnectionInfo[] {
    return Array.from(this.pool.connections.values()).filter(
      (conn) => conn.workspaceId === workspaceId
    );
  }

  public getConnectionsByUser(userId: string): ConnectionInfo[] {
    return Array.from(this.pool.connections.values()).filter(
      (conn) => conn.userId === userId
    );
  }

  public simulateLoad(
    connectionsPerSecond: number,
    duration: number
  ): Promise<void> {
    return new Promise((resolve) => {
      const interval = 1000 / connectionsPerSecond;
      let connectionsCreated = 0;
      const targetConnections = Math.floor(
        (duration / 1000) * connectionsPerSecond
      );

      const createConnectionInterval = setInterval(() => {
        if (connectionsCreated >= targetConnections) {
          clearInterval(createConnectionInterval);
          resolve();
          return;
        }

        try {
          const userId = `user-${connectionsCreated % 10}`; // 10 different users
          const workspaceId = `workspace-${connectionsCreated % 3}`; // 3 workspaces
          this.createConnection(userId, workspaceId);
          connectionsCreated++;
        } catch {
          // Pool exhausted
          clearInterval(createConnectionInterval);
          resolve();
        }
      }, interval);
    });
  }

  public recordResourceUsage(): void {
    this.performanceMetrics.resourceUsage.push({
      memory: process.memoryUsage().heapUsed,
      connections: this.pool.activeConnections,
      timestamp: Date.now(),
    });
  }

  public getPerformanceMetrics() {
    return this.performanceMetrics;
  }
}

// Mock AIbitat for scaling tests
interface MockScalingAIbitat extends EventEmitter {
  onError: jest.Mock;
  onMessage: jest.Mock;
  onTerminate: jest.Mock;
  onInterrupt: jest.Mock;
  continue: jest.Mock;
  retry: jest.Mock;
  introspect?: jest.Mock;
  socket?: {
    send: jest.Mock;
  };
}

describe("Concurrent WebSocket Connections and Scaling Tests", () => {
  let connectionPool: WebSocketConnectionPool;
  let testConnections: ScalableWebSocket[] = [];

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();

    connectionPool = new WebSocketConnectionPool();
    testConnections = [];
  });

  afterEach(() => {
    jest.useRealTimers();

    // Cleanup connections
    testConnections.forEach((conn) => {
      if (conn.readyState === 1) {
        conn.simulateDisconnection();
      }
    });
    testConnections = [];
  });

  describe("Connection Pool Management", () => {
    it("should handle multiple simultaneous connections", () => {
      const connectionCount = 50;
      const connections: ScalableWebSocket[] = [];

      // Create multiple connections simultaneously
      for (let i = 0; i < connectionCount; i++) {
        const userId = `user-${i % 10}`;
        const workspaceId = `workspace-${i % 5}`;
        const connection = connectionPool.createConnection(userId, workspaceId);
        connections.push(connection);
        testConnections.push(connection);
      }

      const poolStats = connectionPool.getPoolStats();
      expect(poolStats.activeConnections).toBe(connectionCount);
      expect(poolStats.totalConnectionsCreated).toBe(connectionCount);
      expect(poolStats.peakConcurrentConnections).toBe(connectionCount);
    });

    it("should enforce connection limits", () => {
      const limitedPool = new WebSocketConnectionPool(10); // Limit to 10 connections
      const connections: ScalableWebSocket[] = [];

      // Try to create more connections than the limit
      for (let i = 0; i < 15; i++) {
        try {
          const connection = limitedPool.createConnection(
            `user-${i}`,
            "workspace-1"
          );
          connections.push(connection);
        } catch (error) {
          expect((error as Error).message).toContain(
            "Connection pool exhausted"
          );
        }
      }

      const poolStats = limitedPool.getPoolStats();
      expect(poolStats.activeConnections).toBeLessThanOrEqual(10);
      expect(poolStats.totalConnectionsCreated).toBeLessThanOrEqual(10);

      // Cleanup
      connections.forEach((conn) => conn.simulateDisconnection());
    });

    it("should handle connection recycling", () => {
      const initialConnections = 5;
      const connections: ScalableWebSocket[] = [];

      // Create initial connections
      for (let i = 0; i < initialConnections; i++) {
        const connection = connectionPool.createConnection(
          `user-${i}`,
          "workspace-1"
        );
        connections.push(connection);
        testConnections.push(connection);
      }

      // Close some connections
      connections[0].simulateDisconnection();
      connections[2].simulateDisconnection();

      // Create new connections
      const newConnection1 = connectionPool.createConnection(
        "user-new-1",
        "workspace-1"
      );
      const newConnection2 = connectionPool.createConnection(
        "user-new-2",
        "workspace-1"
      );
      testConnections.push(newConnection1, newConnection2);

      const poolStats = connectionPool.getPoolStats();
      expect(poolStats.activeConnections).toBe(initialConnections); // 3 remaining + 2 new
      expect(poolStats.totalConnectionsCreated).toBe(initialConnections + 2);
      expect(poolStats.totalConnectionsClosed).toBe(2);
    });

    it("should track connections by workspace", () => {
      const workspaceA = "workspace-a";
      const workspaceB = "workspace-b";

      // Create connections for different workspaces
      for (let i = 0; i < 5; i++) {
        const connection = connectionPool.createConnection(
          `user-${i}`,
          workspaceA
        );
        testConnections.push(connection);
      }

      for (let i = 0; i < 3; i++) {
        const connection = connectionPool.createConnection(
          `user-${i + 5}`,
          workspaceB
        );
        testConnections.push(connection);
      }

      const workspaceAConnections =
        connectionPool.getConnectionsByWorkspace(workspaceA);
      const workspaceBConnections =
        connectionPool.getConnectionsByWorkspace(workspaceB);

      expect(workspaceAConnections.length).toBe(5);
      expect(workspaceBConnections.length).toBe(3);
    });

    it("should handle user connections across multiple workspaces", () => {
      const userId = "multi-workspace-user";
      const workspaces = ["ws-1", "ws-2", "ws-3"];

      // Create connections for same user in different workspaces
      workspaces.forEach((workspace) => {
        const connection = connectionPool.createConnection(userId, workspace);
        testConnections.push(connection);
      });

      const userConnections = connectionPool.getConnectionsByUser(userId);
      expect(userConnections.length).toBe(workspaces.length);

      const userWorkspaces = userConnections.map((conn) => conn.workspaceId);
      expect(userWorkspaces.sort()).toEqual(workspaces.sort());
    });
  });

  describe("Load Testing and Performance", () => {
    it("should handle high connection creation rate", async () => {
      const connectionsPerSecond = 20;
      const testDuration = 2000; // 2 seconds

      const loadPromise = connectionPool.simulateLoad(
        connectionsPerSecond,
        testDuration
      );

      // Advance timers to simulate the load
      jest.advanceTimersByTime(testDuration + 100);

      await loadPromise;

      const poolStats = connectionPool.getPoolStats();
      const expectedConnections = Math.floor(
        (testDuration / 1000) * connectionsPerSecond
      );

      expect(poolStats.totalConnectionsCreated).toBeGreaterThan(0);
      expect(poolStats.totalConnectionsCreated).toBeLessThanOrEqual(
        expectedConnections + 5
      ); // Allow some variance

      // Mark connections for cleanup
      poolStats.connections.forEach((conn) => {
        const wsConn = testConnections.find((tc) => tc.id === conn.id);
        if (!wsConn) {
          // These are from the simulated load, mark for cleanup by closing pool connections
          connectionPool.closeConnection(conn.id);
        }
      });
    });

    it("should measure connection establishment performance", () => {
      const connectionCount = 100;

      // Mock performance.now() to simulate realistic connection times
      let mockTime = 0;
      let callCount = 0;
      jest.spyOn(performance, "now").mockImplementation(() => {
        // Every other call advances time (start/end pairs)
        if (callCount % 2 === 0) {
          mockTime += 0.1; // Base time advance
        } else {
          mockTime += Math.random() * 2; // 0-2ms connection time
        }
        callCount++;
        return mockTime;
      });

      for (let i = 0; i < connectionCount; i++) {
        const connection = connectionPool.createConnection(
          `perf-user-${i}`,
          "perf-workspace"
        );
        testConnections.push(connection);
      }

      const poolStats = connectionPool.getPoolStats();

      expect(poolStats.averageConnectionTime).toBeGreaterThan(0);
      expect(poolStats.averageConnectionTime).toBeLessThan(10); // Average connection time should be < 10ms

      // Check that all connections were tracked
      expect(poolStats.totalConnectionsCreated).toBe(connectionCount);
    });

    it("should handle memory usage under high connection load", () => {
      const memoryBefore = process.memoryUsage().heapUsed;
      const connectionCount = 200;

      // Create many connections
      for (let i = 0; i < connectionCount; i++) {
        const connection = connectionPool.createConnection(
          `mem-user-${i}`,
          `mem-workspace-${i % 10}`
        );
        testConnections.push(connection);

        // Record memory usage periodically
        if (i % 50 === 0) {
          connectionPool.recordResourceUsage();
        }
      }

      const memoryAfter = process.memoryUsage().heapUsed;
      const memoryIncrease = memoryAfter - memoryBefore;
      const memoryPerConnection = memoryIncrease / connectionCount;

      expect(memoryPerConnection).toBeLessThan(50000); // Less than 50KB per connection
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024); // Less than 10MB total increase

      const performanceMetrics = connectionPool.getPerformanceMetrics();
      expect(performanceMetrics.resourceUsage.length).toBeGreaterThan(0);
    });

    it("should handle burst connection patterns", () => {
      const burstSize = 25;
      const burstCount = 4;
      const burstInterval = 100; // ms between bursts

      let totalConnections = 0;

      for (let burst = 0; burst < burstCount; burst++) {
        // Create burst of connections
        for (let i = 0; i < burstSize; i++) {
          const connection = connectionPool.createConnection(
            `burst-user-${burst}-${i}`,
            `burst-workspace-${burst}`
          );
          testConnections.push(connection);
          totalConnections++;
        }

        // Simulate interval between bursts
        jest.advanceTimersByTime(burstInterval);
      }

      const poolStats = connectionPool.getPoolStats();
      expect(poolStats.totalConnectionsCreated).toBe(totalConnections);
      expect(poolStats.activeConnections).toBe(totalConnections);
      expect(poolStats.peakConcurrentConnections).toBe(totalConnections);
    });
  });

  describe("Message Broadcasting and Scaling", () => {
    it("should handle broadcasting to multiple connections", () => {
      const connectionCount = 20;
      const connections: ScalableWebSocket[] = [];
      const plugins: any[] = [];

      // Create connections with WebSocket plugins
      for (let i = 0; i < connectionCount; i++) {
        const connection = connectionPool.createConnection(
          `broadcast-user-${i}`,
          "broadcast-workspace"
        );
        const mockAibitat = new EventEmitter() as MockScalingAIbitat;

        mockAibitat.onError = jest
          .fn()
          .mockImplementation((handler) => mockAibitat.on("error", handler));
        mockAibitat.onMessage = jest
          .fn()
          .mockImplementation((handler) => mockAibitat.on("message", handler));
        mockAibitat.onTerminate = jest
          .fn()
          .mockImplementation((handler) =>
            mockAibitat.on("terminate", handler)
          );
        mockAibitat.onInterrupt = jest
          .fn()
          .mockImplementation((handler) =>
            mockAibitat.on("interrupt", handler)
          );
        mockAibitat.continue = jest.fn();
        mockAibitat.retry = jest.fn();

        const plugin = websocket.plugin({ socket: connection });
        plugin.setup(mockAibitat as any);

        connections.push(connection);
        plugins.push({ plugin, aibitat: mockAibitat });
        testConnections.push(connection);
      }

      // Broadcast message to all connections
      const broadcastMessage = {
        content: "Broadcast test message",
        role: "system" as const,
        type: "broadcast",
      };

      plugins.forEach(({ aibitat }) => {
        aibitat.emit("message", broadcastMessage);
      });

      // Verify all connections received the message
      connections.forEach((connection) => {
        expect(connection.send).toHaveBeenCalledWith(
          expect.stringContaining("Broadcast test message")
        );
      });

      const poolStats = connectionPool.getPoolStats();
      expect(poolStats.activeConnections).toBe(connectionCount);
    });

    it("should handle selective message delivery by workspace", () => {
      const workspaceA = "workspace-a";
      const workspaceB = "workspace-b";
      const connectionsA: ScalableWebSocket[] = [];
      const connectionsB: ScalableWebSocket[] = [];
      const pluginsA: any[] = [];
      const pluginsB: any[] = [];

      // Create connections for workspace A
      for (let i = 0; i < 5; i++) {
        const connection = connectionPool.createConnection(
          `user-a-${i}`,
          workspaceA
        );
        const mockAibitat = new EventEmitter() as MockScalingAIbitat;

        mockAibitat.onError = jest
          .fn()
          .mockImplementation((handler) => mockAibitat.on("error", handler));
        mockAibitat.onMessage = jest
          .fn()
          .mockImplementation((handler) => mockAibitat.on("message", handler));
        mockAibitat.onTerminate = jest
          .fn()
          .mockImplementation((handler) =>
            mockAibitat.on("terminate", handler)
          );
        mockAibitat.onInterrupt = jest
          .fn()
          .mockImplementation((handler) =>
            mockAibitat.on("interrupt", handler)
          );
        mockAibitat.continue = jest.fn();
        mockAibitat.retry = jest.fn();

        const plugin = websocket.plugin({ socket: connection });
        plugin.setup(mockAibitat as any);

        connectionsA.push(connection);
        pluginsA.push({ plugin, aibitat: mockAibitat });
        testConnections.push(connection);
      }

      // Create connections for workspace B
      for (let i = 0; i < 3; i++) {
        const connection = connectionPool.createConnection(
          `user-b-${i}`,
          workspaceB
        );
        const mockAibitat = new EventEmitter() as MockScalingAIbitat;

        mockAibitat.onError = jest
          .fn()
          .mockImplementation((handler) => mockAibitat.on("error", handler));
        mockAibitat.onMessage = jest
          .fn()
          .mockImplementation((handler) => mockAibitat.on("message", handler));
        mockAibitat.onTerminate = jest
          .fn()
          .mockImplementation((handler) =>
            mockAibitat.on("terminate", handler)
          );
        mockAibitat.onInterrupt = jest
          .fn()
          .mockImplementation((handler) =>
            mockAibitat.on("interrupt", handler)
          );
        mockAibitat.continue = jest.fn();
        mockAibitat.retry = jest.fn();

        const plugin = websocket.plugin({ socket: connection });
        plugin.setup(mockAibitat as any);

        connectionsB.push(connection);
        pluginsB.push({ plugin, aibitat: mockAibitat });
        testConnections.push(connection);
      }

      // Send workspace-specific messages
      const messageA = {
        content: "Message for workspace A",
        role: "assistant" as const,
      };
      const messageB = {
        content: "Message for workspace B",
        role: "assistant" as const,
      };

      pluginsA.forEach(({ aibitat }) => {
        aibitat.emit("message", messageA);
      });

      pluginsB.forEach(({ aibitat }) => {
        aibitat.emit("message", messageB);
      });

      // Verify workspace isolation
      connectionsA.forEach((connection) => {
        expect(connection.send).toHaveBeenCalledWith(
          expect.stringContaining("workspace A")
        );
        expect(connection.send).not.toHaveBeenCalledWith(
          expect.stringContaining("workspace B")
        );
      });

      connectionsB.forEach((connection) => {
        expect(connection.send).toHaveBeenCalledWith(
          expect.stringContaining("workspace B")
        );
        expect(connection.send).not.toHaveBeenCalledWith(
          expect.stringContaining("workspace A")
        );
      });
    });

    it("should handle high-frequency message broadcasting", () => {
      const connectionCount = 10;
      const messageCount = 100;
      const connections: ScalableWebSocket[] = [];
      const plugins: any[] = [];

      // Create connections
      for (let i = 0; i < connectionCount; i++) {
        const connection = connectionPool.createConnection(
          `freq-user-${i}`,
          "freq-workspace"
        );
        const mockAibitat = new EventEmitter() as MockScalingAIbitat;

        mockAibitat.onError = jest
          .fn()
          .mockImplementation((handler) => mockAibitat.on("error", handler));
        mockAibitat.onMessage = jest
          .fn()
          .mockImplementation((handler) => mockAibitat.on("message", handler));
        mockAibitat.onTerminate = jest
          .fn()
          .mockImplementation((handler) =>
            mockAibitat.on("terminate", handler)
          );
        mockAibitat.onInterrupt = jest
          .fn()
          .mockImplementation((handler) =>
            mockAibitat.on("interrupt", handler)
          );
        mockAibitat.continue = jest.fn();
        mockAibitat.retry = jest.fn();

        const plugin = websocket.plugin({ socket: connection });
        plugin.setup(mockAibitat as any);

        connections.push(connection);
        plugins.push({ plugin, aibitat: mockAibitat });
        testConnections.push(connection);
      }

      // Send high-frequency messages
      const startTime = Date.now();

      for (let i = 0; i < messageCount; i++) {
        const message = {
          content: `High frequency message ${i + 1}`,
          role: "assistant" as const,
          sequence: i + 1,
        };

        plugins.forEach(({ aibitat }) => {
          aibitat.emit("message", message);
        });
      }

      const endTime = Date.now();
      const totalTime = endTime - startTime;
      const messagesPerSecond =
        (messageCount * connectionCount) / (totalTime / 1000);

      // Verify performance
      expect(messagesPerSecond).toBeGreaterThan(100); // Should handle > 100 messages/second
      expect(totalTime).toBeLessThan(5000); // Should complete within 5 seconds

      // Verify all messages were sent
      connections.forEach((connection) => {
        expect(connection.send).toHaveBeenCalledTimes(messageCount);
      });
    });
  });

  describe("Resource Management and Cleanup", () => {
    it("should handle graceful connection cleanup", () => {
      const connectionCount = 10;
      const connections: ScalableWebSocket[] = [];

      // Create connections
      for (let i = 0; i < connectionCount; i++) {
        const connection = connectionPool.createConnection(
          `cleanup-user-${i}`,
          "cleanup-workspace"
        );
        connections.push(connection);
        testConnections.push(connection);
      }

      const initialStats = connectionPool.getPoolStats();
      expect(initialStats.activeConnections).toBe(connectionCount);

      // Close half the connections
      const connectionsToClose = connections.slice(
        0,
        Math.floor(connectionCount / 2)
      );
      connectionsToClose.forEach((connection) => {
        connection.simulateDisconnection();
      });

      const finalStats = connectionPool.getPoolStats();
      expect(finalStats.activeConnections).toBe(
        connectionCount - connectionsToClose.length
      );
      expect(finalStats.totalConnectionsClosed).toBe(connectionsToClose.length);
    });

    it("should handle connection idle timeout", () => {
      const connection = connectionPool.createConnection(
        "idle-user",
        "idle-workspace"
      );
      testConnections.push(connection);

      const connectionInfo = connectionPool
        .getPoolStats()
        .connections.find((c) => c.id === connection.id);
      expect(connectionInfo?.status).toBe("connected");

      // Simulate activity
      connection.send("test message");
      const updatedInfo = connectionPool
        .getPoolStats()
        .connections.find((c) => c.id === connection.id);
      expect(updatedInfo?.status).toBe("active");
      expect(updatedInfo?.messageCount).toBe(1);

      // Simulate idle period
      jest.advanceTimersByTime(60000); // 1 minute

      const stats = connection.getConnectionStats();
      expect(stats.isActive).toBe(false); // Should be considered inactive after 30 seconds
    });

    it("should prevent memory leaks from closed connections", () => {
      const memoryBefore = process.memoryUsage().heapUsed;
      const connectionCount = 50;
      const connections: ScalableWebSocket[] = [];

      // Create and immediately close connections
      for (let i = 0; i < connectionCount; i++) {
        const connection = connectionPool.createConnection(
          `leak-user-${i}`,
          "leak-workspace"
        );
        connections.push(connection);
        connection.simulateDisconnection();
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const memoryAfter = process.memoryUsage().heapUsed;
      const memoryIncrease = memoryAfter - memoryBefore;
      const memoryPerConnection = memoryIncrease / connectionCount;

      // Should not have significant memory increase from closed connections
      expect(memoryPerConnection).toBeLessThan(10000); // Less than 10KB per closed connection

      const poolStats = connectionPool.getPoolStats();
      expect(poolStats.activeConnections).toBe(0);
      expect(poolStats.totalConnectionsClosed).toBe(connectionCount);
    });

    it("should handle abrupt disconnections without resource leaks", () => {
      const connectionCount = 20;
      const connections: ScalableWebSocket[] = [];

      // Create connections
      for (let i = 0; i < connectionCount; i++) {
        const connection = connectionPool.createConnection(
          `abrupt-user-${i}`,
          "abrupt-workspace"
        );
        connections.push(connection);
        testConnections.push(connection);
      }

      // Simulate abrupt disconnections (network errors, etc.)
      connections.forEach((connection) => {
        connection.readyState = 3; // CLOSED
        connection.emit("close", 1006, "Connection lost"); // Abnormal closure
      });

      const poolStats = connectionPool.getPoolStats();
      expect(poolStats.activeConnections).toBe(0);
      expect(poolStats.totalConnectionsClosed).toBe(connectionCount);
    });
  });

  describe("Edge Cases and Error Scenarios", () => {
    it("should handle connection pool exhaustion gracefully", () => {
      const smallPool = new WebSocketConnectionPool(5); // Very small pool
      const connections: ScalableWebSocket[] = [];
      const errors: string[] = [];

      // Fill the pool
      for (let i = 0; i < 5; i++) {
        const connection = smallPool.createConnection(
          `pool-user-${i}`,
          "pool-workspace"
        );
        connections.push(connection);
      }

      // Try to exceed the pool
      for (let i = 5; i < 10; i++) {
        try {
          const connection = smallPool.createConnection(
            `overflow-user-${i}`,
            "pool-workspace"
          );
          connections.push(connection);
        } catch (error) {
          errors.push((error as Error).message);
        }
      }

      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0]).toContain("Connection pool exhausted");

      // Cleanup
      connections.forEach((conn) => {
        try {
          conn.simulateDisconnection();
        } catch {
          // intentionally empty: error ignored by test
        }
      });
    });

    it("should handle rapid connect/disconnect cycles", () => {
      const cycleCount = 20;
      let totalConnections = 0;
      let totalDisconnections = 0;

      for (let cycle = 0; cycle < cycleCount; cycle++) {
        // Connect
        const connection = connectionPool.createConnection(
          `cycle-user-${cycle}`,
          "cycle-workspace"
        );
        totalConnections++;

        // Immediately disconnect
        connection.simulateDisconnection();
        totalDisconnections++;
      }

      const poolStats = connectionPool.getPoolStats();
      expect(poolStats.totalConnectionsCreated).toBe(totalConnections);
      expect(poolStats.totalConnectionsClosed).toBe(totalDisconnections);
      expect(poolStats.activeConnections).toBe(0);
    });

    it("should handle concurrent connection creation and destruction", () => {
      const operations = 50;
      const connections: ScalableWebSocket[] = [];

      // Perform concurrent create/destroy operations
      for (let i = 0; i < operations; i++) {
        if (i % 3 === 0 && connections.length > 0) {
          // Destroy a connection
          const connection = connections.pop();
          if (connection) {
            connection.simulateDisconnection();
          }
        } else {
          // Create a connection
          const connection = connectionPool.createConnection(
            `concurrent-user-${i}`,
            "concurrent-workspace"
          );
          connections.push(connection);
          testConnections.push(connection);
        }
      }

      const poolStats = connectionPool.getPoolStats();
      expect(poolStats.totalConnectionsCreated).toBeGreaterThan(0);
      expect(poolStats.activeConnections).toBe(connections.length);
    });
  });
});
