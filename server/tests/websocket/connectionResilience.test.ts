/**
 * Connection Resilience Tests (reconnection, heartbeat, timeouts)
 *
 * Tests WebSocket connection resilience features including reconnection logic,
 * heartbeat mechanisms, timeout handling, and graceful degradation scenarios.
 */

import { EventEmitter } from "events";
import { websocket } from "../../utils/agents/aibitat/plugins/websocket";

// Mock WebSocket states
const WEBSOCKET_STATES = {
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3,
} as const;

// Enhanced mock WebSocket with connection state simulation
class MockResilientWebSocket extends EventEmitter {
  public readyState: number = WEBSOCKET_STATES.CONNECTING;
  public send = jest.fn();
  public close = jest.fn();
  public handleFeedback?: jest.Mock;
  public checkBailCommand?: jest.Mock;
  public awaitResponse?: jest.Mock;
  public askForFeedback?: jest.Mock;

  // Connection state tracking
  private connectionAttempts = 0;
  private maxReconnectAttempts = 3;
  private reconnectDelay = 1000;
  private heartbeatInterval?: ReturnType<typeof setTimeout>;
  private connectionTimeout?: ReturnType<typeof setTimeout>;

  constructor(
    private shouldFailConnection = false,
    private connectionDelay = 0
  ) {
    super();
    this.simulateConnection();
  }

  private simulateConnection(): void {
    this.connectionAttempts++;

    if (
      this.shouldFailConnection &&
      this.connectionAttempts <= this.maxReconnectAttempts
    ) {
      setTimeout(() => {
        this.readyState = WEBSOCKET_STATES.CLOSED;
        this.emit(
          "error",
          new Error(`Connection attempt ${this.connectionAttempts} failed`)
        );
        this.attemptReconnection();
      }, this.connectionDelay);
      return;
    }

    setTimeout(() => {
      if (
        this.shouldFailConnection &&
        this.connectionAttempts > this.maxReconnectAttempts
      ) {
        this.readyState = WEBSOCKET_STATES.CLOSED;
        this.emit("error", new Error("Max reconnection attempts reached"));
        return;
      }

      this.readyState = WEBSOCKET_STATES.OPEN;
      this.emit("open");
      this.startHeartbeat();
    }, this.connectionDelay);
  }

  private attemptReconnection(): void {
    if (this.connectionAttempts < this.maxReconnectAttempts) {
      setTimeout(() => {
        this.readyState = WEBSOCKET_STATES.CONNECTING;
        this.emit("reconnecting", this.connectionAttempts);
        this.simulateConnection();
      }, this.reconnectDelay);
    }
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.readyState === WEBSOCKET_STATES.OPEN) {
        this.emit("heartbeat");
      }
    }, 30000); // 30 second heartbeat
  }

  public simulateDisconnection(): void {
    this.readyState = WEBSOCKET_STATES.CLOSED;
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    this.emit("close", 1006, "Connection lost");
  }

  public simulateTimeout(): void {
    this.connectionTimeout = setTimeout(() => {
      this.readyState = WEBSOCKET_STATES.CLOSED;
      this.emit("error", new Error("Connection timeout"));
      this.emit("close", 1006, "Timeout");
    }, 300000); // 5 minute timeout
  }

  public simulateNetworkError(): void {
    this.readyState = WEBSOCKET_STATES.CLOSED;
    this.emit("error", new Error("Network error"));
    this.emit("close", 1006, "Network error");
  }

  public forceClose(): void {
    this.readyState = WEBSOCKET_STATES.CLOSED;
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
    }
    this.emit("close", 1000, "Normal closure");
  }
}

// Mock AIbitat for resilience testing
interface MockResilientAIbitat extends EventEmitter {
  // AIbitat core properties
  emitter: EventEmitter;
  provider: string | null;
  defaultProvider: any;
  defaultInterrupt: string;
  maxRounds: number;
  _chats: any[];
  handlerProps: Record<string, unknown>;
  model?: string;
  agents: Map<string, any>;
  channels: Map<string, any>;
  functions: Map<string, any>;
  // AIbitat methods
  chats: any[];
  use: jest.Mock;
  agent: jest.Mock;
  channel: jest.Mock;
  getAgentConfig: jest.Mock;
  getChannelConfig: jest.Mock;
  start: jest.Mock;
  sendMessage: jest.Mock;
  processFunctionCall: jest.Mock;
  function: jest.Mock;
  introspect?: jest.Mock;
  // Mocked methods
  onError: jest.Mock;
  onMessage: jest.Mock;
  onTerminate: jest.Mock;
  onInterrupt: jest.Mock;
  continue: jest.Mock;
  retry: jest.Mock;
  socket?: {
    send: jest.Mock;
  };
}

describe("Connection Resilience Tests", () => {
  let mockSocket: MockResilientWebSocket;
  let mockAibitat: MockResilientAIbitat;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();

    mockSocket = new MockResilientWebSocket();

    // Add default error handler to prevent unhandled errors
    mockSocket.on("error", () => {
      // Silently handle errors in tests
    });

    const emitter = new EventEmitter();
    mockAibitat = emitter as MockResilientAIbitat;
    // AIbitat core properties
    mockAibitat.emitter = emitter;
    mockAibitat.provider = "openai";
    mockAibitat.defaultProvider = { provider: "openai" };
    mockAibitat.defaultInterrupt = "NEVER";
    mockAibitat.maxRounds = 10;
    mockAibitat._chats = [];
    mockAibitat.handlerProps = {};
    mockAibitat.model = "gpt-4o";
    mockAibitat.agents = new Map();
    mockAibitat.channels = new Map();
    mockAibitat.functions = new Map();
    // AIbitat methods
    Object.defineProperty(mockAibitat, "chats", {
      get: () => mockAibitat._chats,
      configurable: true,
    });
    mockAibitat.use = jest.fn(() => mockAibitat);
    mockAibitat.agent = jest.fn(() => mockAibitat);
    mockAibitat.channel = jest.fn(() => mockAibitat);
    mockAibitat.getAgentConfig = jest.fn();
    mockAibitat.getChannelConfig = jest.fn();
    mockAibitat.start = jest.fn();
    mockAibitat.sendMessage = jest.fn();
    mockAibitat.processFunctionCall = jest.fn();
    mockAibitat.function = jest.fn(() => mockAibitat);
    mockAibitat.introspect = jest.fn();
    // Mocked methods
    mockAibitat.onError = jest.fn().mockImplementation((handler) => {
      mockAibitat.on("error", handler);
    });
    mockAibitat.onMessage = jest.fn().mockImplementation((handler) => {
      mockAibitat.on("message", handler);
    });
    mockAibitat.onTerminate = jest.fn().mockImplementation((handler) => {
      mockAibitat.on("terminate", handler);
    });
    mockAibitat.onInterrupt = jest.fn().mockImplementation((handler) => {
      mockAibitat.on("interrupt", handler);
    });
    mockAibitat.continue = jest.fn();
    mockAibitat.retry = jest.fn();
  });

  afterEach(() => {
    jest.useRealTimers();
    mockSocket?.forceClose();
  });

  describe("Connection Establishment Resilience", () => {
    it("should handle successful connection after initial failure", async () => {
      const failingSocket = new MockResilientWebSocket(true, 100);
      const plugin = websocket.plugin({ socket: failingSocket });
      plugin.setup(mockAibitat);

      const connectionEvents: string[] = [];

      failingSocket.on("error", () => {
        connectionEvents.push("error");
      });

      failingSocket.on("reconnecting", (attempt) => {
        connectionEvents.push(`reconnecting-${attempt}`);
      });

      failingSocket.on("open", () => {
        connectionEvents.push("open");
      });

      // Fast-forward through connection attempts
      jest.advanceTimersByTime(5000);

      expect(connectionEvents).toContain("error");
      expect(
        connectionEvents.some((event) => event.startsWith("reconnecting"))
      ).toBe(true);
    });

    it("should respect maximum reconnection attempts", async () => {
      const persistentlyFailingSocket = new MockResilientWebSocket(true, 50);
      let errorCount = 0;

      persistentlyFailingSocket.on("error", () => {
        errorCount++;
      });

      // Allow all reconnection attempts to complete
      jest.advanceTimersByTime(10000);

      expect(errorCount).toBeGreaterThan(1);
      expect(errorCount).toBeLessThanOrEqual(4); // Initial + 3 retries
    });

    it("should handle connection timeout gracefully", () => {
      mockSocket.simulateTimeout();
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat);

      let timeoutHandled = false;
      mockSocket.on("error", (error) => {
        if (error.message.includes("timeout")) {
          timeoutHandled = true;
        }
      });

      // Fast-forward to trigger timeout
      jest.advanceTimersByTime(300000);

      expect(timeoutHandled).toBe(true);
      expect(mockSocket.readyState).toBe(WEBSOCKET_STATES.CLOSED);
    });
  });

  describe("Heartbeat and Keep-Alive Mechanisms", () => {
    it("should send periodic heartbeat messages", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat);

      let heartbeatCount = 0;
      mockSocket.on("heartbeat", () => {
        heartbeatCount++;
      });

      // Simulate connection establishment
      jest.advanceTimersByTime(100);

      // Simulate 2 minutes of heartbeats (4 beats at 30s intervals)
      jest.advanceTimersByTime(120000);

      expect(heartbeatCount).toBeGreaterThan(0);
      expect(heartbeatCount).toBeLessThanOrEqual(4);
    });

    it("should detect missed heartbeats and reconnect", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat);

      // Establish connection
      jest.advanceTimersByTime(100);
      expect(mockSocket.readyState).toBe(WEBSOCKET_STATES.OPEN);

      // Simulate network interruption
      mockSocket.simulateDisconnection();

      expect(mockSocket.readyState).toBe(WEBSOCKET_STATES.CLOSED);
    });

    it("should resume heartbeat after reconnection", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat);

      let heartbeatCount = 0;
      mockSocket.on("heartbeat", () => {
        heartbeatCount++;
      });

      // Establish initial connection
      jest.advanceTimersByTime(100);

      // Let some heartbeats occur
      jest.advanceTimersByTime(60000); // 2 heartbeats
      const initialHeartbeats = heartbeatCount;

      // Simulate disconnection and reconnection
      mockSocket.simulateDisconnection();
      const _newSocket = new MockResilientWebSocket();

      // Continue heartbeats on new connection
      jest.advanceTimersByTime(60000);

      expect(initialHeartbeats).toBeGreaterThan(0);
    });
  });

  describe("Message Delivery and Retry Logic", () => {
    it("should queue messages during disconnection", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat);

      const messageQueue: string[] = [];
      const originalSend = mockSocket.send;

      mockSocket.send = jest.fn().mockImplementation((data) => {
        if (mockSocket.readyState === WEBSOCKET_STATES.OPEN) {
          originalSend.call(mockSocket, data);
        } else {
          messageQueue.push(data);
        }
      });

      // Establish connection
      jest.advanceTimersByTime(100);

      // Send message while connected
      mockAibitat.emit("message", {
        from: "assistant",
        content: "Connected message",
        role: "assistant",
      });

      // Disconnect and try to send message
      mockSocket.simulateDisconnection();
      mockAibitat.emit("message", {
        from: "assistant",
        content: "Disconnected message",
        role: "assistant",
      });

      expect(mockSocket.send).toHaveBeenCalledTimes(2);
      expect(messageQueue).toHaveLength(1);
      expect(messageQueue[0]).toContain("Disconnected message");
    });

    it("should retry failed message delivery", async () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat);

      let sendAttempts = 0;
      mockSocket.send = jest.fn().mockImplementation(() => {
        sendAttempts++;
        if (sendAttempts < 3) {
          throw new Error("Send failed");
        }
        return true; // Success on 3rd attempt
      });

      // Establish connection
      jest.advanceTimersByTime(100);

      // Try to send message (should retry on failure)
      const testMessage = {
        from: "assistant",
        content: "Retry test message",
        role: "assistant" as const,
      };

      // Simulate retry logic
      try {
        mockAibitat.emit("message", testMessage);
      } catch {
        // First attempt fails
        setTimeout(() => {
          try {
            mockAibitat.emit("message", testMessage);
          } catch {
            // Second attempt fails
            setTimeout(() => {
              mockAibitat.emit("message", testMessage); // Third attempt succeeds
            }, 1000);
          }
        }, 1000);
      }

      jest.advanceTimersByTime(3000);

      expect(sendAttempts).toBe(3);
    });

    it("should handle message delivery failure gracefully", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat);

      // Mock console.error to suppress error output in tests
      const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();

      mockSocket.send = jest.fn().mockImplementation(() => {
        throw new Error("Send failed permanently");
      });

      // Establish connection
      jest.advanceTimersByTime(100);

      // The WebSocket plugin doesn't catch send errors, so they will throw
      expect(() => {
        mockAibitat.emit("message", {
          from: "assistant",
          content: "This should fail gracefully",
          role: "assistant",
        });
      }).toThrow("Send failed permanently");

      expect(mockSocket.send).toHaveBeenCalled();

      // Restore console.error
      consoleErrorSpy.mockRestore();
    });
  });

  describe("Timeout Handling", () => {
    it("should handle feedback timeout gracefully", async () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat);

      const feedbackTimeout = 300000; // 5 minutes

      // Setup feedback timeout scenario
      mockSocket.awaitResponse = jest.fn().mockImplementation(() => {
        return new Promise((resolve) => {
          setTimeout(() => resolve("exit"), feedbackTimeout);
        });
      });

      mockSocket.askForFeedback = jest.fn().mockImplementation(() => {
        return mockSocket.awaitResponse!();
      });

      const interruptPromise = new Promise<string>((resolve) => {
        mockAibitat.emit("interrupt", { from: "user", to: "assistant" });
        resolve("timeout-test");
      });

      // Fast-forward to trigger timeout
      jest.advanceTimersByTime(feedbackTimeout);

      await interruptPromise;

      expect(mockSocket.awaitResponse).toHaveBeenCalled();
    });

    it("should cleanup resources on timeout", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat);

      // Simulate connection establishment
      jest.advanceTimersByTime(100);

      // Manually trigger timeout condition
      mockSocket.readyState = WEBSOCKET_STATES.CLOSED;
      mockSocket.emit("error", new Error("Connection timeout"));
      mockSocket.emit("close", 1006, "Timeout");

      expect(mockSocket.readyState).toBe(WEBSOCKET_STATES.CLOSED);
    });

    it("should implement progressive backoff for reconnection attempts", () => {
      // Test that reconnection attempts happen with the mock
      const backoffSocket = new MockResilientWebSocket(true, 100);
      let errorCount = 0;

      backoffSocket.on("error", () => {
        errorCount++;
      });

      // Start connection attempts - will fail initially
      jest.advanceTimersByTime(100);

      // First failure should have occurred
      expect(errorCount).toBe(1);

      // Allow time for one reconnection attempt
      jest.advanceTimersByTime(1100); // 1000ms reconnect delay + 100ms connection time

      // Should have attempted reconnection
      expect(errorCount).toBeGreaterThan(1);
    });
  });

  describe("Network Error Recovery", () => {
    it("should recover from network interruption", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat);

      const networkEvents: string[] = [];

      mockSocket.on("error", (error) => {
        networkEvents.push(`error: ${error.message}`);
      });

      mockSocket.on("close", () => {
        networkEvents.push("close");
      });

      // Establish connection
      jest.advanceTimersByTime(100);
      expect(mockSocket.readyState).toBe(WEBSOCKET_STATES.OPEN);

      // Simulate network error
      mockSocket.simulateNetworkError();

      expect(networkEvents).toContain("error: Network error");
      expect(networkEvents).toContain("close");
      expect(mockSocket.readyState).toBe(WEBSOCKET_STATES.CLOSED);
    });

    it("should handle intermittent connectivity issues", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat);

      let connectionState = "disconnected";

      mockSocket.on("open", () => {
        connectionState = "connected";
      });

      mockSocket.on("close", () => {
        connectionState = "disconnected";
      });

      // Simulate intermittent connectivity
      jest.advanceTimersByTime(100); // Connect
      expect(connectionState).toBe("connected");

      mockSocket.simulateDisconnection(); // Disconnect
      expect(connectionState).toBe("disconnected");

      // Simulate reconnection after some time
      const _newSocket = new MockResilientWebSocket(false, 100);
      jest.advanceTimersByTime(200);

      // Test should verify the system can handle these state changes
      expect(true).toBe(true); // System should remain stable
    });

    it("should detect and handle zombie connections", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat);

      // Establish connection
      jest.advanceTimersByTime(100);
      expect(mockSocket.readyState).toBe(WEBSOCKET_STATES.OPEN);

      // Simulate zombie connection (appears open but no longer functional)
      mockSocket.send = jest.fn().mockImplementation(() => {
        throw new Error("EPIPE: Broken pipe");
      });

      // Try to send a message - should detect broken connection
      let _brokenPipeDetected = false;
      try {
        mockAibitat.emit("message", {
          from: "assistant",
          content: "Zombie test message",
          role: "assistant",
        });
      } catch (error) {
        if ((error as Error).message.includes("EPIPE")) {
          _brokenPipeDetected = true;
        }
      }

      expect(mockSocket.send).toHaveBeenCalled();
      // System should detect the broken connection through send failures
    });
  });

  describe("Graceful Degradation", () => {
    it("should continue operation without WebSocket when possible", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat);

      // Establish then lose connection
      jest.advanceTimersByTime(100);
      mockSocket.simulateDisconnection();

      // Try to continue operations
      const operationResults: boolean[] = [];

      // Simulate operations that could work without WebSocket
      try {
        mockAibitat.emit("message", {
          from: "system",
          content: "System message during disconnection",
          role: "system",
        });
        operationResults.push(true);
      } catch {
        operationResults.push(false);
      }

      // System should attempt to continue operation
      expect(operationResults).toContain(true);
    });

    it("should queue critical operations during outage", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat);

      const operationQueue: any[] = [];

      // Simulate outage
      mockSocket.simulateDisconnection();

      // Queue critical operations
      const criticalMessage = {
        from: "assistant",
        content: "Critical system update",
        role: "assistant",
        priority: "high",
      };

      operationQueue.push(criticalMessage);

      expect(operationQueue).toHaveLength(1);
      expect(operationQueue[0].priority).toBe("high");
    });

    it("should provide fallback mechanisms for essential features", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat);

      // Disable WebSocket functionality
      mockSocket.simulateDisconnection();

      // Test fallback for introspection
      if (mockAibitat.introspect) {
        const fallbackIntrospection: string[] = [];

        // Override introspect to use fallback
        mockAibitat.introspect = jest.fn().mockImplementation((message) => {
          fallbackIntrospection.push(message);
        });

        mockAibitat.introspect("Fallback test message");

        expect(fallbackIntrospection).toContain("Fallback test message");
      }

      // Test fallback for socket.send
      if (mockAibitat.socket) {
        const fallbackMessages: any[] = [];

        mockAibitat.socket.send = jest
          .fn()
          .mockImplementation((type, content) => {
            fallbackMessages.push({ type, content });
          });

        mockAibitat.socket.send("fallback", "test");

        expect(fallbackMessages).toHaveLength(1);
        expect(fallbackMessages[0].type).toBe("fallback");
      }
    });
  });

  describe("Resource Management and Cleanup", () => {
    it("should properly cleanup resources on connection failure", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat);

      const cleanupTracker = {
        timeoutsCleared: 0,
        intervalsCleared: 0,
        listenersRemoved: 0,
      };

      // Mock cleanup functions
      const originalClearTimeout = global.clearTimeout;
      const originalClearInterval = global.clearInterval;

      global.clearTimeout = jest.fn().mockImplementation((id) => {
        cleanupTracker.timeoutsCleared++;
        return originalClearTimeout(id);
      });

      global.clearInterval = jest.fn().mockImplementation((id) => {
        cleanupTracker.intervalsCleared++;
        return originalClearInterval(id);
      });

      // Establish connection then force failure
      jest.advanceTimersByTime(100);
      mockSocket.simulateNetworkError();
      mockSocket.forceClose();

      // Cleanup should occur - at least the heartbeat interval should be cleared
      expect(
        cleanupTracker.timeoutsCleared + cleanupTracker.intervalsCleared
      ).toBeGreaterThanOrEqual(1);

      // Restore original functions
      global.clearTimeout = originalClearTimeout;
      global.clearInterval = originalClearInterval;
    });

    it("should prevent memory leaks from failed connections", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat);

      const initialListenerCount = mockSocket.listenerCount("error");

      // Simulate multiple connection failures and reconnections
      for (let i = 0; i < 5; i++) {
        // Create a new connection
        mockSocket.readyState = WEBSOCKET_STATES.OPEN;
        jest.advanceTimersByTime(100);

        // Then simulate error
        mockSocket.simulateNetworkError();
        jest.advanceTimersByTime(1000);
      }

      const finalListenerCount = mockSocket.listenerCount("error");

      // Listener count should not grow significantly
      expect(finalListenerCount).toBeLessThanOrEqual(initialListenerCount + 1);
    });

    it("should handle concurrent connection attempts safely", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat);

      const connectionResults: string[] = [];

      // Simulate concurrent connection attempts
      const _attempts = Array.from({ length: 5 }, (_, i) => {
        return new Promise<void>((resolve) => {
          setTimeout(() => {
            try {
              if (mockSocket.readyState === WEBSOCKET_STATES.OPEN) {
                connectionResults.push(`attempt-${i}-success`);
              } else {
                connectionResults.push(`attempt-${i}-failed`);
              }
            } catch {
              connectionResults.push(`attempt-${i}-error`);
            }
            resolve();
          }, i * 100);
        });
      });

      jest.advanceTimersByTime(1000);

      // Should handle concurrent attempts without crashing
      expect(connectionResults.length).toBeGreaterThan(0);
      expect(
        connectionResults.every((result) => result.includes("attempt-"))
      ).toBe(true);
    });
  });
});
