/**
 * WebSocket Error Handling and Graceful Degradation Tests
 *
 * Tests comprehensive error scenarios, recovery mechanisms, and graceful degradation
 * patterns for WebSocket connections and real-time features.
 */

import { EventEmitter } from "events";
import { websocket } from "../../utils/agents/aibitat/plugins/websocket";

// Error types and categories
enum ErrorType {
  NETWORK = "network",
  PROTOCOL = "protocol",
  APPLICATION = "application",
  RESOURCE = "resource",
  TIMEOUT = "timeout",
  AUTHENTICATION = "authentication",
}

interface ErrorScenario {
  type: ErrorType;
  code: number;
  message: string;
  recoverable: boolean;
  retryable: boolean;
  fallbackAvailable: boolean;
}

// Mock WebSocket with error simulation capabilities
class ErrorSimulatingWebSocket extends EventEmitter {
  public id: string;
  public readyState = 1; // OPEN
  public send = jest.fn();
  public close = jest.fn();
  public userId: string;
  public workspaceId: string;

  private errorQueue: ErrorScenario[] = [];
  private messageCount = 0;
  private lastError: Error | null = null;
  private isRecovering = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3;

  constructor(id: string, userId: string, workspaceId: string) {
    super();
    this.id = id;
    this.userId = userId;
    this.workspaceId = workspaceId;

    this.send.mockImplementation((data) => {
      this.messageCount++;

      // Check for queued errors
      if (this.errorQueue.length > 0) {
        const error = this.errorQueue.shift()!;
        this.simulateError(error);
        return;
      }

      // Simulate successful send
      this.emit("messageSent", { data, timestamp: Date.now() });
    });

    this.close.mockImplementation((code = 1000, reason = "Normal closure") => {
      this.readyState = 3; // CLOSED
      this.emit("close", code, reason);
    });
  }

  public queueError(error: ErrorScenario): void {
    this.errorQueue.push(error);
  }

  public simulateError(scenario: ErrorScenario): void {
    const error = new Error(scenario.message);
    (error as any).code = scenario.code;
    (error as any).type = scenario.type;
    (error as any).recoverable = scenario.recoverable;
    (error as any).retryable = scenario.retryable;
    (error as any).fallbackAvailable = scenario.fallbackAvailable;

    this.lastError = error;

    switch (scenario.type) {
      case ErrorType.NETWORK:
        this.simulateNetworkError(scenario);
        break;
      case ErrorType.PROTOCOL:
        this.simulateProtocolError(scenario);
        break;
      case ErrorType.APPLICATION:
        this.simulateApplicationError(scenario);
        break;
      case ErrorType.RESOURCE:
        this.simulateResourceError(scenario);
        break;
      case ErrorType.TIMEOUT:
        this.simulateTimeoutError(scenario);
        break;
      case ErrorType.AUTHENTICATION:
        this.simulateAuthError(scenario);
        break;
    }
  }

  private simulateNetworkError(scenario: ErrorScenario): void {
    this.readyState = 3; // CLOSED
    this.emit("error", this.lastError);
    this.emit("close", scenario.code, scenario.message);

    if (
      scenario.recoverable &&
      this.reconnectAttempts < this.maxReconnectAttempts
    ) {
      this.attemptReconnection();
    }
  }

  private simulateProtocolError(_scenario: ErrorScenario): void {
    this.emit("error", this.lastError);
    if (!_scenario.recoverable) {
      this.readyState = 3; // CLOSED
      this.emit("close", _scenario.code, _scenario.message);
    }
  }

  private simulateApplicationError(_scenario: ErrorScenario): void {
    this.emit("error", this.lastError);
    // Application errors usually don't close the connection
  }

  private simulateResourceError(_scenario: ErrorScenario): void {
    this.emit("error", this.lastError);
    if (_scenario.code === 1009) {
      // Message too big
      // Connection typically stays open for resource errors
    } else if (_scenario.code === 1011) {
      // Server error
      this.readyState = 3; // CLOSED
      this.emit("close", _scenario.code, _scenario.message);
    }
  }

  private simulateTimeoutError(_scenario: ErrorScenario): void {
    this.emit("error", this.lastError);
    if (_scenario.recoverable) {
      this.attemptReconnection();
    } else {
      this.readyState = 3; // CLOSED
      this.emit("close", _scenario.code, _scenario.message);
    }
  }

  private simulateAuthError(_scenario: ErrorScenario): void {
    this.readyState = 3; // CLOSED
    this.emit("error", this.lastError);
    this.emit("close", _scenario.code, _scenario.message);
    // Auth errors are typically not recoverable without re-authentication
  }

  private attemptReconnection(): void {
    if (this.isRecovering) return;

    this.isRecovering = true;
    this.reconnectAttempts++;

    setTimeout(() => {
      if (this.reconnectAttempts <= this.maxReconnectAttempts) {
        this.readyState = 1; // OPEN
        this.isRecovering = false;
        this.emit("reconnected");
      } else {
        this.emit("reconnectFailed");
      }
    }, 1000 * this.reconnectAttempts); // Exponential backoff
  }

  public getErrorStats() {
    return {
      lastError: this.lastError,
      reconnectAttempts: this.reconnectAttempts,
      isRecovering: this.isRecovering,
      messageCount: this.messageCount,
    };
  }

  public resetErrorState(): void {
    this.lastError = null;
    this.reconnectAttempts = 0;
    this.isRecovering = false;
    this.errorQueue = [];
  }
}

// Circuit breaker for WebSocket connections
class WebSocketCircuitBreaker {
  private failureCount = 0;
  private lastFailureTime = 0;
  private state: "CLOSED" | "OPEN" | "HALF_OPEN" = "CLOSED";

  constructor(
    private failureThreshold = 5,
    private recoveryTimeout = 30000, // 30 seconds
    private retryTimeout = 5000 // 5 seconds
  ) {}

  public canAttemptConnection(): boolean {
    if (this.state === "CLOSED") return true;

    if (this.state === "OPEN") {
      const timeSinceLastFailure = Date.now() - this.lastFailureTime;
      if (timeSinceLastFailure > this.recoveryTimeout) {
        this.state = "HALF_OPEN";
        return true;
      }
      return false;
    }

    if (this.state === "HALF_OPEN") {
      const timeSinceLastFailure = Date.now() - this.lastFailureTime;
      return timeSinceLastFailure > this.retryTimeout;
    }

    return false;
  }

  public recordSuccess(): void {
    this.failureCount = 0;
    this.state = "CLOSED";
  }

  public recordFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (
      this.state === "HALF_OPEN" ||
      this.failureCount >= this.failureThreshold
    ) {
      this.state = "OPEN";
    }
  }

  public getState() {
    return {
      state: this.state,
      failureCount: this.failureCount,
      lastFailureTime: this.lastFailureTime,
    };
  }

  public reset(): void {
    this.failureCount = 0;
    this.lastFailureTime = 0;
    this.state = "CLOSED";
  }
}

// Fallback message queue for when WebSocket is unavailable
class FallbackMessageQueue {
  private queue: Array<{ message: any; timestamp: number; retries: number }> =
    [];
  private maxQueueSize = 100;
  private maxRetries = 3;

  public enqueue(message: any): boolean {
    if (this.queue.length >= this.maxQueueSize) {
      // Remove oldest message to make room
      this.queue.shift();
    }

    this.queue.push({
      message,
      timestamp: Date.now(),
      retries: 0,
    });

    return true;
  }

  public dequeue(): any | null {
    const item = this.queue.shift();
    return item ? item.message : null;
  }

  public retry(message: any): boolean {
    const item = this.queue.find((q) => q.message === message);
    if (item && item.retries < this.maxRetries) {
      item.retries++;
      item.timestamp = Date.now();
      return true;
    }
    return false;
  }

  public flush(): any[] {
    const messages = this.queue.map((item) => item.message);
    this.queue = [];
    return messages;
  }

  public getStats() {
    return {
      queueSize: this.queue.length,
      oldestMessage: this.queue.length > 0 ? this.queue[0].timestamp : null,
      totalRetries: this.queue.reduce((sum, item) => sum + item.retries, 0),
    };
  }
}

// Mock AIbitat for error testing
interface MockErrorAIbitat extends EventEmitter {
  onError: jest.Mock;
  onMessage: jest.Mock;
  onTerminate: jest.Mock;
  onInterrupt: jest.Mock;
  continue: jest.Mock;
  retry: jest.Mock;
  introspect?: jest.Mock;
  socket?: {
    send: jest.Mock;
  };
}

describe("WebSocket Error Handling and Graceful Degradation Tests", () => {
  let testSocket: ErrorSimulatingWebSocket;
  let circuitBreaker: WebSocketCircuitBreaker;
  let fallbackQueue: FallbackMessageQueue;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    jest.setSystemTime(new Date("2024-01-01"));

    testSocket = new ErrorSimulatingWebSocket(
      "test-socket",
      "test-user",
      "test-workspace"
    );

    // Add default error handler to prevent unhandled errors
    testSocket.on("error", () => {
      // Silently handle errors in tests
    });

    circuitBreaker = new WebSocketCircuitBreaker();
    fallbackQueue = new FallbackMessageQueue();
  });

  afterEach(() => {
    jest.useRealTimers();
    testSocket.resetErrorState();
    circuitBreaker.reset();
  });

  describe("Network Error Handling", () => {
    it("should handle connection refused errors", () => {
      const errorScenario: ErrorScenario = {
        type: ErrorType.NETWORK,
        code: 1006,
        message: "ECONNREFUSED: Connection refused",
        recoverable: true,
        retryable: true,
        fallbackAvailable: true,
      };

      const errorHandler = jest.fn();
      const closeHandler = jest.fn();
      const reconnectHandler = jest.fn();

      testSocket.on("error", errorHandler);
      testSocket.on("close", closeHandler);
      testSocket.on("reconnected", reconnectHandler);

      testSocket.simulateError(errorScenario);

      expect(errorHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          message: "ECONNREFUSED: Connection refused",
          code: 1006,
          type: ErrorType.NETWORK,
        })
      );
      expect(closeHandler).toHaveBeenCalledWith(
        1006,
        "ECONNREFUSED: Connection refused"
      );
      expect(testSocket.readyState).toBe(3); // CLOSED

      // Fast-forward to trigger reconnection
      jest.advanceTimersByTime(1000);
      expect(reconnectHandler).toHaveBeenCalled();
    });

    it("should handle DNS resolution failures", () => {
      const errorScenario: ErrorScenario = {
        type: ErrorType.NETWORK,
        code: 1015,
        message: "ENOTFOUND: DNS lookup failed",
        recoverable: false,
        retryable: false,
        fallbackAvailable: true,
      };

      const errorHandler = jest.fn();
      testSocket.on("error", errorHandler);

      testSocket.simulateError(errorScenario);

      expect(errorHandler).toHaveBeenCalled();
      expect(testSocket.getErrorStats().reconnectAttempts).toBe(0); // Should not attempt reconnection
    });

    it("should handle network timeout with exponential backoff", () => {
      const errorScenario: ErrorScenario = {
        type: ErrorType.TIMEOUT,
        code: 1008,
        message: "Network timeout after 30 seconds",
        recoverable: true,
        retryable: true,
        fallbackAvailable: true,
      };

      const reconnectHandler = jest.fn();
      const errorHandler = jest.fn();

      // Remove default error handler and add custom one for this test
      testSocket.removeAllListeners("error");
      testSocket.on("error", errorHandler);
      testSocket.on("reconnected", reconnectHandler);

      // Simulate multiple timeout errors
      testSocket.simulateError(errorScenario);
      expect(testSocket.getErrorStats().reconnectAttempts).toBe(1);

      // Fast-forward first reconnection attempt (1 second)
      jest.advanceTimersByTime(1000);
      expect(reconnectHandler).toHaveBeenCalledTimes(1);

      // Simulate another error
      testSocket.simulateError(errorScenario);
      expect(testSocket.getErrorStats().reconnectAttempts).toBe(2);

      // Fast-forward second reconnection attempt (2 seconds)
      jest.advanceTimersByTime(2000);
      expect(reconnectHandler).toHaveBeenCalledTimes(2);
    });

    it("should handle intermittent connectivity issues", () => {
      const networkErrors = [
        {
          type: ErrorType.NETWORK,
          code: 1006,
          message: "Connection lost",
          recoverable: true,
          retryable: true,
          fallbackAvailable: true,
        },
        {
          type: ErrorType.NETWORK,
          code: 1001,
          message: "Going away",
          recoverable: true,
          retryable: true,
          fallbackAvailable: true,
        },
      ];

      const errorHandler = jest.fn();
      const reconnectHandler = jest.fn();

      testSocket.on("error", errorHandler);
      testSocket.on("reconnected", reconnectHandler);

      // Simulate intermittent errors
      networkErrors.forEach((error, index) => {
        testSocket.simulateError(error as ErrorScenario);
        jest.advanceTimersByTime((index + 1) * 1000);
      });

      expect(errorHandler).toHaveBeenCalledTimes(networkErrors.length);
      expect(reconnectHandler).toHaveBeenCalledTimes(networkErrors.length);
    });
  });

  describe("Protocol and Application Error Handling", () => {
    it("should handle WebSocket protocol violations", () => {
      const protocolError: ErrorScenario = {
        type: ErrorType.PROTOCOL,
        code: 1002,
        message: "Protocol error: Invalid frame format",
        recoverable: false,
        retryable: false,
        fallbackAvailable: false,
      };

      const errorHandler = jest.fn();
      const closeHandler = jest.fn();

      testSocket.on("error", errorHandler);
      testSocket.on("close", closeHandler);

      testSocket.simulateError(protocolError);

      expect(errorHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          message: "Protocol error: Invalid frame format",
          code: 1002,
        })
      );
      expect(closeHandler).toHaveBeenCalled();
      expect(testSocket.getErrorStats().reconnectAttempts).toBe(0);
    });

    it("should handle application-level errors gracefully", () => {
      const appError: ErrorScenario = {
        type: ErrorType.APPLICATION,
        code: 4000,
        message: "Invalid message format",
        recoverable: true,
        retryable: true,
        fallbackAvailable: true,
      };

      const errorHandler = jest.fn();
      testSocket.on("error", errorHandler);

      testSocket.simulateError(appError);

      expect(errorHandler).toHaveBeenCalled();
      expect(testSocket.readyState).toBe(1); // Should remain open for app errors
    });

    it("should handle message size limit errors", () => {
      const resourceError: ErrorScenario = {
        type: ErrorType.RESOURCE,
        code: 1009,
        message: "Message too big (> 1MB)",
        recoverable: true,
        retryable: false,
        fallbackAvailable: true,
      };

      const errorHandler = jest.fn();
      testSocket.on("error", errorHandler);

      testSocket.simulateError(resourceError);

      expect(errorHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          code: 1009,
          message: "Message too big (> 1MB)",
        })
      );
      expect(testSocket.readyState).toBe(1); // Connection should stay open
    });

    it("should handle server errors with proper fallback", () => {
      const serverError: ErrorScenario = {
        type: ErrorType.RESOURCE,
        code: 1011,
        message: "Internal server error",
        recoverable: true,
        retryable: true,
        fallbackAvailable: true,
      };

      const errorHandler = jest.fn();
      const closeHandler = jest.fn();

      testSocket.on("error", errorHandler);
      testSocket.on("close", closeHandler);

      testSocket.simulateError(serverError);

      expect(errorHandler).toHaveBeenCalled();
      expect(closeHandler).toHaveBeenCalledWith(1011, "Internal server error");
      expect(testSocket.readyState).toBe(3); // Should close on server errors
    });
  });

  describe("Authentication and Authorization Errors", () => {
    it("should handle authentication failures", () => {
      const authError: ErrorScenario = {
        type: ErrorType.AUTHENTICATION,
        code: 4001,
        message: "Invalid authentication token",
        recoverable: false,
        retryable: false,
        fallbackAvailable: false,
      };

      const errorHandler = jest.fn();
      const closeHandler = jest.fn();

      testSocket.on("error", errorHandler);
      testSocket.on("close", closeHandler);

      testSocket.simulateError(authError);

      expect(errorHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          code: 4001,
          message: "Invalid authentication token",
        })
      );
      expect(closeHandler).toHaveBeenCalled();
      expect(testSocket.getErrorStats().reconnectAttempts).toBe(0);
    });

    it("should handle token expiration errors", () => {
      const tokenError: ErrorScenario = {
        type: ErrorType.AUTHENTICATION,
        code: 4002,
        message: "Token expired",
        recoverable: false,
        retryable: false,
        fallbackAvailable: true,
      };

      const errorHandler = jest.fn();
      testSocket.on("error", errorHandler);

      testSocket.simulateError(tokenError);

      expect(errorHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          code: 4002,
          message: "Token expired",
        })
      );
      expect(testSocket.readyState).toBe(3);
    });

    it("should handle authorization errors for workspace access", () => {
      const authzError: ErrorScenario = {
        type: ErrorType.AUTHENTICATION,
        code: 4003,
        message: "Insufficient permissions for workspace",
        recoverable: false,
        retryable: false,
        fallbackAvailable: false,
      };

      const errorHandler = jest.fn();
      testSocket.on("error", errorHandler);

      testSocket.simulateError(authzError);

      expect(errorHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          code: 4003,
          message: "Insufficient permissions for workspace",
        })
      );
    });
  });

  describe("Circuit Breaker Pattern", () => {
    it("should implement circuit breaker for repeated failures", () => {
      expect(circuitBreaker.canAttemptConnection()).toBe(true);
      expect(circuitBreaker.getState().state).toBe("CLOSED");

      // Record multiple failures
      for (let i = 0; i < 5; i++) {
        circuitBreaker.recordFailure();
      }

      expect(circuitBreaker.getState().state).toBe("OPEN");
      expect(circuitBreaker.canAttemptConnection()).toBe(false);
    });

    it("should transition to half-open state after recovery timeout", () => {
      // Trigger circuit breaker
      for (let i = 0; i < 5; i++) {
        circuitBreaker.recordFailure();
      }

      expect(circuitBreaker.getState().state).toBe("OPEN");

      // Fast-forward past recovery timeout
      jest.advanceTimersByTime(31000); // 31 seconds

      expect(circuitBreaker.canAttemptConnection()).toBe(true);
      expect(circuitBreaker.getState().state).toBe("HALF_OPEN");
    });

    it("should reset on successful connection", () => {
      // Trigger circuit breaker
      for (let i = 0; i < 3; i++) {
        circuitBreaker.recordFailure();
      }

      expect(circuitBreaker.getState().failureCount).toBe(3);

      circuitBreaker.recordSuccess();

      expect(circuitBreaker.getState().state).toBe("CLOSED");
      expect(circuitBreaker.getState().failureCount).toBe(0);
    });

    it("should handle retry timeout in half-open state", () => {
      // Get to half-open state
      for (let i = 0; i < 5; i++) {
        circuitBreaker.recordFailure();
      }
      jest.advanceTimersByTime(31000);

      expect(circuitBreaker.canAttemptConnection()).toBe(true);
      expect(circuitBreaker.getState().state).toBe("HALF_OPEN");

      // Record another failure - this should return to OPEN state
      circuitBreaker.recordFailure();

      // Should not allow immediate retry
      expect(circuitBreaker.canAttemptConnection()).toBe(false);
      expect(circuitBreaker.getState().state).toBe("OPEN");

      // Fast-forward recovery timeout to get back to HALF_OPEN
      jest.advanceTimersByTime(31000); // 31 seconds

      expect(circuitBreaker.canAttemptConnection()).toBe(true);
    });
  });

  describe("Fallback Message Queue", () => {
    it("should queue messages when WebSocket is unavailable", () => {
      const messages = [
        { content: "Message 1", type: "chat" },
        { content: "Message 2", type: "status" },
        { content: "Message 3", type: "progress" },
      ];

      messages.forEach((message) => {
        fallbackQueue.enqueue(message);
      });

      expect(fallbackQueue.getStats().queueSize).toBe(3);
    });

    it("should respect maximum queue size", () => {
      // Fill queue beyond capacity
      for (let i = 0; i < 150; i++) {
        fallbackQueue.enqueue({ content: `Message ${i}`, type: "test" });
      }

      const stats = fallbackQueue.getStats();
      expect(stats.queueSize).toBe(100); // Should not exceed max size
    });

    it("should implement retry mechanism for failed messages", () => {
      const message = { content: "Retry test", type: "chat" };

      fallbackQueue.enqueue(message);
      expect(fallbackQueue.getStats().queueSize).toBe(1);

      // Simulate retries
      expect(fallbackQueue.retry(message)).toBe(true);
      expect(fallbackQueue.retry(message)).toBe(true);
      expect(fallbackQueue.retry(message)).toBe(true);
      expect(fallbackQueue.retry(message)).toBe(false); // Max retries exceeded

      expect(fallbackQueue.getStats().totalRetries).toBe(3);
    });

    it("should flush messages when connection is restored", () => {
      const messages = [
        { content: "Queued 1", type: "chat" },
        { content: "Queued 2", type: "status" },
      ];

      messages.forEach((message) => fallbackQueue.enqueue(message));

      const flushedMessages = fallbackQueue.flush();

      expect(flushedMessages).toEqual(messages);
      expect(fallbackQueue.getStats().queueSize).toBe(0);
    });
  });

  describe("Graceful Degradation Patterns", () => {
    it("should gracefully degrade to HTTP polling when WebSocket fails", () => {
      const degradationHandler = jest.fn();
      const pollingFallback = jest.fn();

      // Simulate WebSocket failure
      const networkError: ErrorScenario = {
        type: ErrorType.NETWORK,
        code: 1006,
        message: "WebSocket connection failed",
        recoverable: false,
        retryable: false,
        fallbackAvailable: true,
      };

      // Remove default error handler for this test
      testSocket.removeAllListeners("error");

      testSocket.on("error", (error) => {
        if ((error as any).fallbackAvailable) {
          degradationHandler();
          pollingFallback();
        }
      });

      testSocket.simulateError(networkError);

      expect(degradationHandler).toHaveBeenCalled();
      expect(pollingFallback).toHaveBeenCalled();
    });

    it("should provide cached responses when real-time data is unavailable", () => {
      const cache = new Map();
      const cacheKey = "workspace-data";
      const cachedData = { status: "cached", timestamp: Date.now() };

      // Simulate cached data
      cache.set(cacheKey, cachedData);

      const getCachedData = (key: string) => {
        return cache.get(key);
      };

      // When WebSocket fails, use cached data
      const networkError: ErrorScenario = {
        type: ErrorType.NETWORK,
        code: 1006,
        message: "Connection lost",
        recoverable: true,
        retryable: true,
        fallbackAvailable: true,
      };

      let fallbackData = null;

      // Remove default error handler for this test
      testSocket.removeAllListeners("error");

      testSocket.on("error", (error) => {
        if ((error as any).fallbackAvailable) {
          fallbackData = getCachedData(cacheKey);
        }
      });

      testSocket.simulateError(networkError);

      expect(fallbackData).toEqual(cachedData);
    });

    it("should implement progressive enhancement for real-time features", () => {
      const featureFlags = {
        realTimeChat: true,
        liveCollaboration: true,
        instantNotifications: true,
      };

      const degradeFeatures = (errorType: ErrorType) => {
        switch (errorType) {
          case ErrorType.NETWORK:
            featureFlags.instantNotifications = false;
            break;
          case ErrorType.RESOURCE:
            featureFlags.liveCollaboration = false;
            break;
          case ErrorType.TIMEOUT:
            featureFlags.realTimeChat = false;
            break;
        }
      };

      // Simulate different error types
      const errors = [ErrorType.NETWORK, ErrorType.RESOURCE, ErrorType.TIMEOUT];

      errors.forEach((errorType) => {
        const error: ErrorScenario = {
          type: errorType,
          code: 1000,
          message: `${errorType} error`,
          recoverable: true,
          retryable: true,
          fallbackAvailable: true,
        };

        testSocket.on("error", (err) => {
          degradeFeatures((err as any).type);
        });

        testSocket.simulateError(error);
      });

      expect(featureFlags.realTimeChat).toBe(false);
      expect(featureFlags.liveCollaboration).toBe(false);
      expect(featureFlags.instantNotifications).toBe(false);
    });

    it("should handle partial service availability", () => {
      const serviceStatus = {
        chat: "available",
        documents: "available",
        collaboration: "available",
        notifications: "available",
      };

      const updateServiceStatus = (service: string, status: string) => {
        serviceStatus[service as keyof typeof serviceStatus] = status;
      };

      // Simulate partial service degradation
      const partialError: ErrorScenario = {
        type: ErrorType.APPLICATION,
        code: 4100,
        message: "Collaboration service temporarily unavailable",
        recoverable: true,
        retryable: true,
        fallbackAvailable: true,
      };

      testSocket.on("error", (error) => {
        const errorMessage = (error as Error).message;
        if (errorMessage.includes("Collaboration")) {
          updateServiceStatus("collaboration", "degraded");
        }
      });

      testSocket.simulateError(partialError);

      expect(serviceStatus.chat).toBe("available");
      expect(serviceStatus.documents).toBe("available");
      expect(serviceStatus.collaboration).toBe("degraded");
      expect(serviceStatus.notifications).toBe("available");
    });
  });

  describe("Recovery and Self-Healing", () => {
    it("should implement automatic error recovery", () => {
      const recoveryAttempts: string[] = [];
      const maxRecoveryAttempts = 3;

      const attemptRecovery = (errorType: ErrorType, attempt: number) => {
        recoveryAttempts.push(`${errorType}-attempt-${attempt}`);
        return attempt <= maxRecoveryAttempts;
      };

      const recoverableError: ErrorScenario = {
        type: ErrorType.NETWORK,
        code: 1006,
        message: "Temporary network error",
        recoverable: true,
        retryable: true,
        fallbackAvailable: true,
      };

      let recoveryAttempt = 0;

      testSocket.on("error", (error) => {
        if ((error as any).recoverable) {
          recoveryAttempt++;
          attemptRecovery((error as any).type, recoveryAttempt);
        }
      });

      // Simulate multiple errors requiring recovery
      for (let i = 0; i < 5; i++) {
        testSocket.simulateError(recoverableError);
      }

      expect(recoveryAttempts.length).toBe(5);
      expect(recoveryAttempts).toContain("network-attempt-1");
      expect(recoveryAttempts).toContain("network-attempt-5");
    });

    it("should implement health checks and auto-recovery", () => {
      const healthCheckInterval = 5000; // 5 seconds
      const healthCheckResults: boolean[] = [];

      const performHealthCheck = (): boolean => {
        // Simulate health check
        const isHealthy =
          testSocket.readyState === 1 &&
          testSocket.getErrorStats().lastError === null;
        healthCheckResults.push(isHealthy);
        return isHealthy;
      };

      // Start health checking
      const healthCheckTimer = setInterval(
        performHealthCheck,
        healthCheckInterval
      );

      // Initial healthy state
      jest.advanceTimersByTime(healthCheckInterval);
      expect(healthCheckResults[0]).toBe(true);

      // Simulate error
      const healthError: ErrorScenario = {
        type: ErrorType.NETWORK,
        code: 1006,
        message: "Health check failure",
        recoverable: true,
        retryable: true,
        fallbackAvailable: true,
      };

      // Remove default error handler for this test
      testSocket.removeAllListeners("error");
      testSocket.on("error", () => {
        // Handle error silently for health check test
      });

      testSocket.simulateError(healthError);

      // Check unhealthy state
      jest.advanceTimersByTime(healthCheckInterval);
      expect(healthCheckResults[1]).toBe(false);

      // Simulate recovery
      testSocket.resetErrorState();
      testSocket.readyState = 1;

      // Check recovered state
      jest.advanceTimersByTime(healthCheckInterval);
      expect(healthCheckResults[2]).toBe(true);

      clearInterval(healthCheckTimer);
    });

    it("should handle cascading failure recovery", () => {
      const cascadingErrors = [
        {
          type: ErrorType.NETWORK,
          code: 1006,
          message: "Initial network failure",
          recoverable: true,
          retryable: true,
          fallbackAvailable: true,
        },
        {
          type: ErrorType.TIMEOUT,
          code: 1008,
          message: "Recovery timeout",
          recoverable: true,
          retryable: true,
          fallbackAvailable: true,
        },
        {
          type: ErrorType.RESOURCE,
          code: 1011,
          message: "Resource exhaustion during recovery",
          recoverable: true,
          retryable: false,
          fallbackAvailable: true,
        },
      ];

      const errorHistory: string[] = [];
      const recoveryStrategies: string[] = [];

      testSocket.on("error", (error) => {
        errorHistory.push((error as any).type);

        if ((error as any).recoverable) {
          if ((error as any).retryable) {
            recoveryStrategies.push("retry");
          } else {
            recoveryStrategies.push("fallback");
          }
        } else {
          recoveryStrategies.push("abandon");
        }
      });

      cascadingErrors.forEach((error) => {
        testSocket.simulateError(error as ErrorScenario);
      });

      expect(errorHistory).toEqual(["network", "timeout", "resource"]);
      expect(recoveryStrategies).toEqual(["retry", "retry", "fallback"]);
    });
  });

  describe("Integration with WebSocket Plugin", () => {
    it("should handle errors in websocket plugin gracefully", () => {
      const mockAibitat = new EventEmitter() as MockErrorAIbitat;
      mockAibitat.onError = jest
        .fn()
        .mockImplementation((handler) => mockAibitat.on("error", handler));
      mockAibitat.onMessage = jest
        .fn()
        .mockImplementation((handler) => mockAibitat.on("message", handler));
      mockAibitat.onTerminate = jest
        .fn()
        .mockImplementation((handler) => mockAibitat.on("terminate", handler));
      mockAibitat.onInterrupt = jest
        .fn()
        .mockImplementation((handler) => mockAibitat.on("interrupt", handler));
      mockAibitat.continue = jest.fn();
      mockAibitat.retry = jest.fn();

      const plugin = websocket.plugin({ socket: testSocket });
      plugin.setup(mockAibitat as any);

      // Simulate plugin-level error
      const pluginError = new Error("Plugin processing error");
      mockAibitat.emit("error", pluginError);

      expect(mockAibitat.onError).toHaveBeenCalled();
    });

    it("should maintain plugin state during error recovery", () => {
      const mockAibitat = new EventEmitter() as MockErrorAIbitat;
      mockAibitat.onError = jest
        .fn()
        .mockImplementation((handler) => mockAibitat.on("error", handler));
      mockAibitat.onMessage = jest
        .fn()
        .mockImplementation((handler) => mockAibitat.on("message", handler));
      mockAibitat.onTerminate = jest
        .fn()
        .mockImplementation((handler) => mockAibitat.on("terminate", handler));
      mockAibitat.onInterrupt = jest
        .fn()
        .mockImplementation((handler) => mockAibitat.on("interrupt", handler));
      mockAibitat.continue = jest.fn();
      mockAibitat.retry = jest.fn();

      const plugin = websocket.plugin({ socket: testSocket });
      plugin.setup(mockAibitat as any);

      // Send some messages before error
      const preErrorMessage = {
        content: "Pre-error message",
        role: "user" as const,
      };
      mockAibitat.emit("message", preErrorMessage);

      expect(testSocket.send).toHaveBeenCalledWith(
        expect.stringContaining("Pre-error message")
      );

      // Simulate error and recovery
      const recoveryError: ErrorScenario = {
        type: ErrorType.NETWORK,
        code: 1006,
        message: "Temporary disconnection",
        recoverable: true,
        retryable: true,
        fallbackAvailable: true,
      };

      // Remove default error handler for this test
      testSocket.removeAllListeners("error");
      testSocket.on("error", () => {
        // Handle error silently for plugin state test
      });

      testSocket.simulateError(recoveryError);
      jest.advanceTimersByTime(1000); // Trigger reconnection

      // Send message after recovery
      const postErrorMessage = {
        content: "Post-error message",
        role: "assistant" as const,
      };
      mockAibitat.emit("message", postErrorMessage);

      expect(testSocket.send).toHaveBeenCalledWith(
        expect.stringContaining("Post-error message")
      );
    });
  });

  describe("Performance Under Error Conditions", () => {
    it("should maintain performance during error handling", () => {
      // Remove default error handler for this test
      testSocket.removeAllListeners("error");
      testSocket.on("error", () => {
        // Handle error silently for performance test
      });

      const errorCount = 50;
      const startTime = Date.now();

      for (let i = 0; i < errorCount; i++) {
        const error: ErrorScenario = {
          type: ErrorType.APPLICATION,
          code: 4000 + i,
          message: `Performance test error ${i}`,
          recoverable: true,
          retryable: false,
          fallbackAvailable: true,
        };

        testSocket.simulateError(error);
      }

      const endTime = Date.now();
      const processingTime = endTime - startTime;
      const avgTimePerError = processingTime / errorCount;

      expect(avgTimePerError).toBeLessThan(10); // Less than 10ms per error
      expect(processingTime).toBeLessThan(500); // Total time under 500ms
    });

    it("should handle memory usage during prolonged error conditions", () => {
      // Remove default error handler for this test
      testSocket.removeAllListeners("error");
      testSocket.on("error", () => {
        // Handle error silently for memory test
      });

      const memoryBefore = process.memoryUsage().heapUsed;
      const errorCount = 100;

      for (let i = 0; i < errorCount; i++) {
        const error: ErrorScenario = {
          type: ErrorType.RESOURCE,
          code: 1009,
          message: `Memory test error ${i}`,
          recoverable: true,
          retryable: true,
          fallbackAvailable: true,
        };

        testSocket.simulateError(error);
        testSocket.resetErrorState(); // Clean up after each error
      }

      const memoryAfter = process.memoryUsage().heapUsed;
      const memoryIncrease = memoryAfter - memoryBefore;

      expect(memoryIncrease).toBeLessThan(5 * 1024 * 1024); // Less than 5MB increase
    });
  });
});
