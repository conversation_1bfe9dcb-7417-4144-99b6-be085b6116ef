/**
 * Message Ordering and Delivery Guarantee Tests
 *
 * Tests WebSocket message ordering, delivery guarantees, and sequence integrity
 * to ensure reliable real-time communication patterns.
 */

import { EventEmitter } from "events";
import { websocket } from "../../utils/agents/aibitat/plugins/websocket";

// Message tracking utilities
interface SequencedMessage {
  id: string;
  sequence: number;
  timestamp: number;
  content: string;
  type: string;
  from?: string;
  role?: "user" | "assistant" | "system";
}

interface MessageDeliveryTracker {
  sent: SequencedMessage[];
  received: SequencedMessage[];
  acknowledged: string[];
  failed: string[];
  duplicates: string[];
}

// Mock WebSocket with delivery tracking
class MessageTrackingWebSocket extends EventEmitter {
  public readyState = 1; // OPEN
  public sentMessages: SequencedMessage[] = [];
  public deliveryTracker: MessageDeliveryTracker = {
    sent: [],
    received: [],
    acknowledged: [],
    failed: [],
    duplicates: [],
  };

  private messageSequence = 0;
  private deliveryReliability = 1.0; // 100% reliable by default
  private maxDeliveryDelay = 0;
  private allowDuplicates = false;
  private forceOrdering = true;

  constructor(
    reliability = 1.0,
    maxDelay = 0,
    allowDups = false,
    maintainOrder = true
  ) {
    super();
    this.deliveryReliability = reliability;
    this.maxDeliveryDelay = maxDelay;
    this.allowDuplicates = allowDups;
    this.forceOrdering = maintainOrder;
  }

  send = jest.fn().mockImplementation((data: string) => {
    try {
      const parsedData = JSON.parse(data);
      const message: SequencedMessage = {
        id: `msg-${Date.now()}-${this.messageSequence++}`,
        sequence: this.messageSequence,
        timestamp: Date.now(),
        content: data,
        type: parsedData.type || "unknown",
        from: parsedData.from,
        role: parsedData.role,
      };

      this.sentMessages.push(message);
      this.deliveryTracker.sent.push(message);

      // Simulate delivery with potential issues
      this.simulateMessageDelivery(message);
    } catch {
      // Handle non-JSON messages
      const message: SequencedMessage = {
        id: `raw-${Date.now()}-${this.messageSequence++}`,
        sequence: this.messageSequence,
        timestamp: Date.now(),
        content: data,
        type: "raw",
      };

      this.sentMessages.push(message);
      this.deliveryTracker.sent.push(message);
      this.simulateMessageDelivery(message);
    }
  });

  private simulateMessageDelivery(message: SequencedMessage): void {
    // Determine if message should be delivered
    const shouldDeliver = Math.random() < this.deliveryReliability;

    if (!shouldDeliver) {
      this.deliveryTracker.failed.push(message.id);
      return;
    }

    // Calculate delivery delay
    const delay = Math.random() * this.maxDeliveryDelay;

    setTimeout(() => {
      // Check for duplicates
      if (this.allowDuplicates && Math.random() < 0.1) {
        this.deliveryTracker.duplicates.push(message.id);
        this.deliverMessage(message); // First delivery
        setTimeout(() => this.deliverMessage(message), 10); // Duplicate
      } else {
        this.deliverMessage(message);
      }
    }, delay);
  }

  private deliverMessage(message: SequencedMessage): void {
    this.deliveryTracker.received.push(message);
    this.emit("messageDelivered", message);

    // Simulate acknowledgment
    setTimeout(() => {
      this.deliveryTracker.acknowledged.push(message.id);
      this.emit("messageAcknowledged", message.id);
    }, 5);
  }

  close = jest.fn();

  // Test utilities
  public getDeliveryStats() {
    return {
      sent: this.deliveryTracker.sent.length,
      received: this.deliveryTracker.received.length,
      acknowledged: this.deliveryTracker.acknowledged.length,
      failed: this.deliveryTracker.failed.length,
      duplicates: this.deliveryTracker.duplicates.length,
      successRate:
        this.deliveryTracker.received.length / this.deliveryTracker.sent.length,
    };
  }

  public verifyMessageOrder(): boolean {
    const receivedSequences = this.deliveryTracker.received.map(
      (m) => m.sequence
    );
    for (let i = 1; i < receivedSequences.length; i++) {
      if (receivedSequences[i] < receivedSequences[i - 1]) {
        return false; // Out of order detected
      }
    }
    return true;
  }

  public findMissingMessages(): number[] {
    const sentSequences = this.deliveryTracker.sent.map((m) => m.sequence);
    const receivedSequences = this.deliveryTracker.received.map(
      (m) => m.sequence
    );
    return sentSequences.filter((seq) => !receivedSequences.includes(seq));
  }
}

// Mock AIbitat for message ordering tests
interface MockOrderingAIbitat extends EventEmitter {
  onError: jest.Mock;
  onMessage: jest.Mock;
  onTerminate: jest.Mock;
  onInterrupt: jest.Mock;
  continue: jest.Mock;
  retry: jest.Mock;
  introspect?: jest.Mock;
  socket?: {
    send: jest.Mock;
  };
}

describe("Message Ordering and Delivery Guarantee Tests", () => {
  let mockSocket: MessageTrackingWebSocket;
  let mockAibitat: MockOrderingAIbitat;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();

    mockSocket = new MessageTrackingWebSocket();

    mockAibitat = new EventEmitter() as MockOrderingAIbitat;
    mockAibitat.onError = jest.fn().mockImplementation((handler) => {
      mockAibitat.on("error", handler);
    });
    mockAibitat.onMessage = jest.fn().mockImplementation((handler) => {
      mockAibitat.on("message", handler);
    });
    mockAibitat.onTerminate = jest.fn().mockImplementation((handler) => {
      mockAibitat.on("terminate", handler);
    });
    mockAibitat.onInterrupt = jest.fn().mockImplementation((handler) => {
      mockAibitat.on("interrupt", handler);
    });
    mockAibitat.continue = jest.fn();
    mockAibitat.retry = jest.fn();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe("Message Sequence Integrity", () => {
    it("should maintain message order in perfect conditions", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat as any);

      const messages = [
        { content: "First message", role: "user" as const, sequence: 1 },
        { content: "Second message", role: "assistant" as const, sequence: 2 },
        { content: "Third message", role: "user" as const, sequence: 3 },
        { content: "Fourth message", role: "assistant" as const, sequence: 4 },
        { content: "Fifth message", role: "system" as const, sequence: 5 },
      ];

      // Send messages in sequence
      messages.forEach((message) => {
        mockAibitat.emit("message", message);
      });

      // Fast-forward to allow delivery
      jest.advanceTimersByTime(100);

      // Verify order is maintained
      expect(mockSocket.verifyMessageOrder()).toBe(true);
      expect(mockSocket.getDeliveryStats().sent).toBe(messages.length);
      expect(mockSocket.getDeliveryStats().received).toBe(messages.length);
    });

    it("should handle rapid message sequences without dropping", () => {
      const plugin = websocket.plugin({
        socket: mockSocket,
        muteUserReply: false,
      });
      plugin.setup(mockAibitat as any);

      const rapidMessageCount = 50;
      const rapidMessages = Array.from(
        { length: rapidMessageCount },
        (_, i) => ({
          content: `Rapid message ${i + 1}`,
          role: "assistant" as const,
          sequence: i + 1,
          timestamp: Date.now() + i,
        })
      );

      // Send all messages rapidly
      rapidMessages.forEach((message) => {
        mockAibitat.emit("message", message);
      });

      jest.advanceTimersByTime(100);

      const stats = mockSocket.getDeliveryStats();
      expect(stats.sent).toBe(rapidMessageCount);
      expect(stats.received).toBe(rapidMessageCount);
      expect(mockSocket.verifyMessageOrder()).toBe(true);
    });

    it("should detect and handle out-of-order messages", () => {
      // Create socket that may deliver out of order
      const unreliableSocket = new MessageTrackingWebSocket(
        1.0,
        50,
        false,
        false
      );
      const plugin = websocket.plugin({ socket: unreliableSocket });
      plugin.setup(mockAibitat as any);

      const messages = [
        { content: "Message 1", role: "assistant" as const },
        { content: "Message 2", role: "assistant" as const },
        { content: "Message 3", role: "assistant" as const },
        { content: "Message 4", role: "assistant" as const },
        { content: "Message 5", role: "assistant" as const },
      ];

      messages.forEach((message) => {
        mockAibitat.emit("message", message);
      });

      jest.advanceTimersByTime(100);

      const stats = unreliableSocket.getDeliveryStats();
      expect(stats.sent).toBe(messages.length);

      // May or may not maintain order depending on delivery delays
      const orderMaintained = unreliableSocket.verifyMessageOrder();
      expect(typeof orderMaintained).toBe("boolean");
    });

    it("should handle message sequence gaps", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat as any);

      // Create socket with some delivery failures
      const partialSocket = new MessageTrackingWebSocket(0.8); // 80% reliability
      const partialPlugin = websocket.plugin({ socket: partialSocket });
      partialPlugin.setup(mockAibitat as any);

      const messages = Array.from({ length: 10 }, (_, i) => ({
        content: `Message ${i + 1}`,
        role: "assistant" as const,
        id: `msg-${i + 1}`,
      }));

      messages.forEach((message) => {
        mockAibitat.emit("message", message);
      });

      jest.advanceTimersByTime(100);

      const stats = partialSocket.getDeliveryStats();
      const missingMessages = partialSocket.findMissingMessages();

      expect(stats.sent).toBe(messages.length);
      expect(stats.failed).toBeGreaterThan(0);
      expect(missingMessages.length).toBe(stats.failed);
    });
  });

  describe("Delivery Guarantees", () => {
    it("should ensure all messages are delivered in reliable conditions", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat as any);

      const messageCount = 20;
      const messages = Array.from({ length: messageCount }, (_, i) => ({
        content: `Guaranteed message ${i + 1}`,
        role: "assistant" as const,
        priority: "normal",
      }));

      messages.forEach((message) => {
        mockAibitat.emit("message", message);
      });

      jest.advanceTimersByTime(100);

      const stats = mockSocket.getDeliveryStats();
      expect(stats.sent).toBe(messageCount);
      expect(stats.received).toBe(messageCount);
      expect(stats.successRate).toBe(1.0); // 100% delivery
      expect(stats.failed).toBe(0);
    });

    it("should handle acknowledgment tracking", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat as any);

      const acknowledgedMessages: string[] = [];

      mockSocket.on("messageAcknowledged", (messageId: string) => {
        acknowledgedMessages.push(messageId);
      });

      const testMessages = [
        { content: "Ack test 1", role: "assistant" as const },
        { content: "Ack test 2", role: "assistant" as const },
        { content: "Ack test 3", role: "assistant" as const },
      ];

      testMessages.forEach((message) => {
        mockAibitat.emit("message", message);
      });

      jest.advanceTimersByTime(100);

      const stats = mockSocket.getDeliveryStats();
      expect(stats.acknowledged).toBe(testMessages.length);
      expect(acknowledgedMessages.length).toBe(testMessages.length);
    });

    it("should detect and handle duplicate messages", () => {
      const duplicateSocket = new MessageTrackingWebSocket(1.0, 0, true); // Allow duplicates
      const plugin = websocket.plugin({ socket: duplicateSocket });
      plugin.setup(mockAibitat as any);

      const messages = [
        { content: "Duplicate test 1", role: "assistant" as const },
        { content: "Duplicate test 2", role: "assistant" as const },
      ];

      messages.forEach((message) => {
        mockAibitat.emit("message", message);
      });

      jest.advanceTimersByTime(100);

      const stats = duplicateSocket.getDeliveryStats();
      expect(stats.sent).toBe(messages.length);

      // Should handle duplicates gracefully
      if (stats.duplicates > 0) {
        expect(stats.received).toBeGreaterThan(stats.sent);
      }
    });

    it("should implement retry logic for failed deliveries", () => {
      const unreliableSocket = new MessageTrackingWebSocket(0.5); // 50% reliability
      const plugin = websocket.plugin({ socket: unreliableSocket });
      plugin.setup(mockAibitat as any);

      const retryAttempts: string[] = [];

      // Mock retry mechanism
      const originalSend = unreliableSocket.send;
      unreliableSocket.send = jest.fn().mockImplementation((data) => {
        retryAttempts.push(data);
        return originalSend.call(unreliableSocket, data);
      });

      const criticalMessages = [
        {
          content: "Critical message 1",
          role: "system" as const,
          priority: "high",
        },
        {
          content: "Critical message 2",
          role: "system" as const,
          priority: "high",
        },
      ];

      criticalMessages.forEach((message) => {
        mockAibitat.emit("message", message);
      });

      jest.advanceTimersByTime(100);

      const stats = unreliableSocket.getDeliveryStats();
      expect(stats.sent).toBe(criticalMessages.length);
      expect(retryAttempts.length).toBe(criticalMessages.length);
    });
  });

  describe("Message Priority and Queuing", () => {
    it("should handle priority message ordering", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat as any);

      const priorityMessages = [
        {
          content: "Low priority",
          role: "assistant" as const,
          priority: "low",
        },
        { content: "High priority", role: "system" as const, priority: "high" },
        {
          content: "Normal priority",
          role: "assistant" as const,
          priority: "normal",
        },
        {
          content: "Critical priority",
          role: "system" as const,
          priority: "critical",
        },
      ];

      priorityMessages.forEach((message) => {
        mockAibitat.emit("message", message);
      });

      jest.advanceTimersByTime(100);

      const stats = mockSocket.getDeliveryStats();
      expect(stats.sent).toBe(priorityMessages.length);
      expect(stats.received).toBe(priorityMessages.length);

      // Verify all messages were delivered regardless of priority
      const deliveredMessages = mockSocket.deliveryTracker.received;
      expect(deliveredMessages.length).toBe(priorityMessages.length);
    });

    it("should handle message queuing during disconnection", () => {
      // Start with disconnected socket
      const disconnectedSocket = new MessageTrackingWebSocket();
      disconnectedSocket.readyState = 3; // CLOSED

      const plugin = websocket.plugin({ socket: disconnectedSocket });
      plugin.setup(mockAibitat as any);

      const queuedMessages: any[] = [];

      // Override send to queue messages when disconnected
      disconnectedSocket.send = jest.fn().mockImplementation((data) => {
        if (disconnectedSocket.readyState !== 1) {
          queuedMessages.push(data);
        } else {
          // Normal send when connected
          return true;
        }
      });

      // Send messages while disconnected
      const messages = [
        { content: "Queued message 1", role: "assistant" as const },
        { content: "Queued message 2", role: "assistant" as const },
      ];

      messages.forEach((message) => {
        mockAibitat.emit("message", message);
      });

      expect(queuedMessages.length).toBe(messages.length);

      // Simulate reconnection and queue flush
      disconnectedSocket.readyState = 1; // OPEN

      // Simulate queue flush - send queued messages
      queuedMessages.forEach((data) => {
        disconnectedSocket.send(data);
      });

      expect(disconnectedSocket.send).toHaveBeenCalledTimes(
        messages.length * 2
      ); // Original queue + retry
    });

    it("should maintain FIFO order for same-priority messages", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat as any);

      const fifoMessages = Array.from({ length: 10 }, (_, i) => ({
        content: `FIFO message ${i + 1}`,
        role: "assistant" as const,
        priority: "normal",
        timestamp: Date.now() + i,
      }));

      fifoMessages.forEach((message) => {
        mockAibitat.emit("message", message);
      });

      jest.advanceTimersByTime(100);

      const deliveredMessages = mockSocket.deliveryTracker.received;

      // Verify FIFO order maintained
      for (let i = 1; i < deliveredMessages.length; i++) {
        expect(deliveredMessages[i].timestamp).toBeGreaterThanOrEqual(
          deliveredMessages[i - 1].timestamp
        );
      }
    });
  });

  describe("Message Integrity and Validation", () => {
    it("should preserve message content integrity", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat as any);

      const originalMessages = [
        {
          content: "Complex message with special chars: äöü ñ 中文 🚀",
          role: "assistant" as const,
          metadata: { source: "test", timestamp: Date.now() },
        },
        {
          content: JSON.stringify({ nested: { data: "value", number: 42 } }),
          role: "system" as const,
          type: "json-data",
        },
      ];

      originalMessages.forEach((message) => {
        mockAibitat.emit("message", message);
      });

      jest.advanceTimersByTime(100);

      const deliveredMessages = mockSocket.deliveryTracker.received;
      expect(deliveredMessages.length).toBe(originalMessages.length);

      deliveredMessages.forEach((delivered, index) => {
        // The content is JSON-stringified, so we need to parse it to check the original content
        const parsedContent = JSON.parse(delivered.content);
        expect(parsedContent.content).toBe(originalMessages[index].content);
      });
    });

    it("should handle large message payloads", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat as any);

      const largeContent = "x".repeat(10000); // 10KB message
      const largeMessage = {
        content: largeContent,
        role: "assistant" as const,
        size: largeContent.length,
      };

      mockAibitat.emit("message", largeMessage);
      jest.advanceTimersByTime(100);

      const stats = mockSocket.getDeliveryStats();
      expect(stats.sent).toBe(1);
      expect(stats.received).toBe(1);

      const deliveredMessage = mockSocket.deliveryTracker.received[0];
      const parsedContent = JSON.parse(deliveredMessage.content);
      expect(parsedContent.content).toBe(largeContent);
    });

    it("should validate message format and structure", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat as any);

      const validMessages = [
        { content: "Valid message", role: "assistant" as const },
        { content: "Another valid message", role: "user" as const },
      ];

      const invalidMessages = [
        null,
        undefined,
        { /* missing content */ role: "assistant" as const },
        { content: "", /* empty content */ role: "assistant" as const },
      ];

      // Send valid messages
      validMessages.forEach((message) => {
        mockAibitat.emit("message", message);
      });

      // Attempt to send invalid messages - they should be filtered out
      invalidMessages.forEach((message) => {
        try {
          if (message && message.content && message.content.trim() !== "") {
            mockAibitat.emit("message", message);
          }
        } catch {
          // Expected for invalid messages
        }
      });

      jest.advanceTimersByTime(100);

      const stats = mockSocket.getDeliveryStats();

      // Only valid messages should be processed
      expect(stats.sent).toBe(validMessages.length);
      expect(stats.received).toBe(validMessages.length);
    });
  });

  describe("Concurrent Message Handling", () => {
    it("should handle concurrent senders without interference", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat as any);

      const senderA = Array.from({ length: 5 }, (_, i) => ({
        content: `Sender A message ${i + 1}`,
        role: "user" as const,
        sender: "A",
      }));

      const senderB = Array.from({ length: 5 }, (_, i) => ({
        content: `Sender B message ${i + 1}`,
        role: "assistant" as const,
        sender: "B",
      }));

      // Interleave messages from both senders
      const interleavedMessages = [];
      const maxLength = Math.max(senderA.length, senderB.length);

      for (let i = 0; i < maxLength; i++) {
        if (i < senderA.length) interleavedMessages.push(senderA[i]);
        if (i < senderB.length) interleavedMessages.push(senderB[i]);
      }

      interleavedMessages.forEach((message) => {
        mockAibitat.emit("message", message);
      });

      jest.advanceTimersByTime(100);

      const stats = mockSocket.getDeliveryStats();
      expect(stats.sent).toBe(interleavedMessages.length);
      expect(stats.received).toBe(interleavedMessages.length);

      // Verify no message loss or corruption
      const deliveredMessages = mockSocket.deliveryTracker.received;
      const senderAReceived = deliveredMessages.filter((m) =>
        m.content.includes("Sender A")
      );
      const senderBReceived = deliveredMessages.filter((m) =>
        m.content.includes("Sender B")
      );

      expect(senderAReceived.length).toBe(senderA.length);
      expect(senderBReceived.length).toBe(senderB.length);
    });

    it("should maintain thread safety in high-concurrency scenarios", () => {
      const plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat as any);

      const concurrentMessageCount = 100;
      const messages = Array.from(
        { length: concurrentMessageCount },
        (_, i) => ({
          content: `Concurrent message ${i + 1}`,
          role: "assistant" as const,
          threadId: Math.floor(i / 10), // 10 threads
          messageId: i + 1,
        })
      );

      // Send all messages "simultaneously"
      messages.forEach((message) => {
        mockAibitat.emit("message", message);
      });

      jest.advanceTimersByTime(100);

      const stats = mockSocket.getDeliveryStats();
      expect(stats.sent).toBe(concurrentMessageCount);
      expect(stats.received).toBe(concurrentMessageCount);

      // Verify no race conditions caused data corruption
      const deliveredMessages = mockSocket.deliveryTracker.received;
      const uniqueMessageIds = new Set(
        deliveredMessages.map((m) => {
          const match = m.content.match(/message (\d+)/);
          return match ? parseInt(match[1]) : -1;
        })
      );

      expect(uniqueMessageIds.size).toBe(concurrentMessageCount);
    });
  });
});
