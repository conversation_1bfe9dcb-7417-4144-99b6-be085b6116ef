/**
 * Progress Updates and Status Synchronization Tests
 *
 * Tests WebSocket-based progress reporting, status synchronization,
 * and the ProgressManager integration with real-time updates.
 */

import { Response } from "express";
import {
  ProgressManager,
  ProgressData,
  ProgressChunkData,
} from "../../utils/chats/flows/core/ProgressManager";
import { writeResponseChunk } from "../../utils/helpers/chat/responses";

// Mock dependencies
jest.mock("../../utils/helpers/chat/responses");

// Mock Response object for testing
class MockResponse implements Partial<Response> {
  public writes: string[] = [];
  public ended = false;
  public headersSent = false;

  write(chunk: string): boolean {
    this.writes.push(chunk);
    return true;
  }

  end(chunk?: any): any {
    if (chunk) this.writes.push(chunk);
    this.ended = true;
    return this;
  }

  status(_code: number): any {
    return this;
  }

  json(obj: any): any {
    this.writes.push(JSON.stringify(obj));
    return this;
  }

  send(data: any): any {
    this.writes.push(typeof data === "string" ? data : JSON.stringify(data));
    return this;
  }
}

// Mock WebSocket for progress updates
interface MockProgressWebSocket {
  send: jest.Mock;
  close: jest.Mock;
  readyState: number;
  progressUpdates: ProgressChunkData[];
}

describe("Progress Updates and Status Synchronization", () => {
  let mockResponse: MockResponse;
  let mockWriteResponseChunk: jest.MockedFunction<typeof writeResponseChunk>;
  let progressManager: ProgressManager;
  let mockWebSocket: MockProgressWebSocket;

  const testChatId = "test-chat-id";
  const testFlowType = "legalMemoFlow";

  beforeEach(() => {
    jest.clearAllMocks();

    mockResponse = new MockResponse();
    mockWriteResponseChunk = writeResponseChunk as jest.MockedFunction<
      typeof writeResponseChunk
    >;
    mockWriteResponseChunk.mockImplementation((response, data) => {
      // Simulate writing to response
      response.write(`data: ${JSON.stringify(data)}\n\n`);
    });

    progressManager = new ProgressManager(
      mockResponse as unknown as Response,
      testChatId,
      testFlowType
    );

    mockWebSocket = {
      send: jest.fn(),
      close: jest.fn(),
      readyState: 1, // WebSocket.OPEN
      progressUpdates: [],
    };

    // Capture progress updates sent via WebSocket
    mockWebSocket.send.mockImplementation((data) => {
      try {
        const parsed = JSON.parse(data);
        if (parsed.type === "cdbProgress" || parsed.type === "progress") {
          mockWebSocket.progressUpdates.push(parsed);
        }
      } catch {
        // Ignore non-JSON messages
      }
    });
  });

  describe("Basic Progress Reporting", () => {
    it("should send initial progress update", () => {
      progressManager.sendProgress({
        status: "starting",
        message: "Initializing legal memo analysis...",
      });

      expect(mockWriteResponseChunk).toHaveBeenCalledTimes(2); // cdbProgress + progress

      const _expectedData = expect.objectContaining({
        uuid: testChatId,
        flowType: testFlowType,
        status: "starting",
        message: "Initializing legal memo analysis...",
        progress: -1, // Auto-translated from 'starting'
      });

      expect(mockWriteResponseChunk).toHaveBeenNthCalledWith(
        1,
        mockResponse,
        expect.objectContaining({
          sources: [],
          type: "cdbProgress",
          textResponse: null,
          close: false,
          error: false,
          uuid: testChatId,
          flowType: testFlowType,
          status: "starting",
          message: "Initializing legal memo analysis...",
          progress: -1,
        })
      );

      expect(mockWriteResponseChunk).toHaveBeenNthCalledWith(
        2,
        mockResponse,
        expect.objectContaining({
          sources: [],
          type: "progress",
          textResponse: null,
          close: false,
          error: false,
          uuid: testChatId,
          flowType: testFlowType,
          status: "starting",
          message: "Initializing legal memo analysis...",
          progress: -1,
        })
      );
    });

    it("should auto-translate status to progress values", () => {
      const statusTestCases = [
        { status: "starting", expectedProgress: -1 },
        { status: "in_progress", expectedProgress: -1 },
        { status: "complete", expectedProgress: 100 },
        { status: "error", expectedProgress: -2 },
      ];

      statusTestCases.forEach(({ status, expectedProgress }) => {
        mockWriteResponseChunk.mockClear();

        progressManager.sendProgress({
          status: status as any,
          message: `Testing ${status} status`,
        });

        expect(mockWriteResponseChunk).toHaveBeenCalledWith(
          mockResponse,
          expect.objectContaining({
            status,
            progress: expectedProgress,
          })
        );
      });
    });

    it("should preserve explicit progress values over auto-translation", () => {
      progressManager.sendProgress({
        status: "in_progress",
        progress: 50, // Explicit progress should override auto-translation
        message: "Half way through analysis",
      });

      expect(mockWriteResponseChunk).toHaveBeenCalledWith(
        mockResponse,
        expect.objectContaining({
          status: "in_progress",
          progress: 50, // Should keep explicit value, not auto-translate to -1
        })
      );
    });
  });

  describe("Step-based Progress Management", () => {
    beforeEach(() => {
      progressManager.setTotalSteps(5);
    });

    it("should handle step-based progress tracking", () => {
      progressManager.startStep(1, "Starting document analysis");

      expect(mockWriteResponseChunk).toHaveBeenCalledWith(
        mockResponse,
        expect.objectContaining({
          step: 1,
          totalSteps: 5,
          status: "starting",
          message: "Starting document analysis",
        })
      );
    });

    it("should update current step progress", () => {
      progressManager.startStep(2, "Analyzing content");
      progressManager.updateStep("Processing legal sections");

      expect(mockWriteResponseChunk).toHaveBeenLastCalledWith(
        mockResponse,
        expect.objectContaining({
          step: 2,
          status: "in_progress",
          message: "Processing legal sections",
        })
      );
    });

    it("should complete steps with data", () => {
      progressManager.startStep(3, "Generating recommendations");
      progressManager.completeStep("Recommendations generated", {
        recommendationCount: 5,
        categories: ["contract", "liability"],
      });

      expect(mockWriteResponseChunk).toHaveBeenLastCalledWith(
        mockResponse,
        expect.objectContaining({
          step: 3,
          status: "complete",
          message: "Recommendations generated",
          data: {
            recommendationCount: 5,
            categories: ["contract", "liability"],
          },
        })
      );
    });

    it("should handle step errors", () => {
      progressManager.startStep(4, "Finalizing document");
      progressManager.sendError(new Error("Processing failed"), 4);

      expect(mockWriteResponseChunk).toHaveBeenLastCalledWith(
        mockResponse,
        expect.objectContaining({
          step: 4,
          status: "error",
          message: "Processing failed",
          progress: -2,
        })
      );
    });
  });

  describe("Sub-step Progress Tracking", () => {
    it("should handle sub-step progress updates", () => {
      progressManager.setTotalSteps(3);
      progressManager.startStep(2, "Processing documents");

      progressManager.sendSubStepProgress(
        3, // current sub-step
        10, // total sub-steps
        "Analyzing document 3 of 10",
        "Document Analysis",
        30 // 30% progress
      );

      expect(mockWriteResponseChunk).toHaveBeenLastCalledWith(
        mockResponse,
        expect.objectContaining({
          step: 2,
          subStep: 3,
          total: 10,
          message: "Analyzing document 3 of 10",
          label: "Document Analysis",
          progress: 30,
        })
      );
    });

    it("should handle sub-steps without labels", () => {
      progressManager.startStep(1, "Initial processing");

      progressManager.sendSubStepProgress(1, 5, "Processing item 1 of 5");

      expect(mockWriteResponseChunk).toHaveBeenLastCalledWith(
        mockResponse,
        expect.objectContaining({
          subStep: 1,
          total: 5,
          message: "Processing item 1 of 5",
          progress: -1, // Default loading state
        })
      );
    });
  });

  describe("Final Status Reporting", () => {
    it("should send final completion with data", () => {
      progressManager.setTotalSteps(3);
      progressManager.sendFinalCompletion("Legal memo completed successfully", {
        documentId: "memo-123",
        pageCount: 15,
        wordCount: 3500,
      });

      expect(mockWriteResponseChunk).toHaveBeenLastCalledWith(
        mockResponse,
        expect.objectContaining({
          step: 3, // Should use totalSteps
          status: "complete",
          message: "Legal memo completed successfully",
          progress: 100,
          data: {
            documentId: "memo-123",
            pageCount: 15,
            wordCount: 3500,
          },
        })
      );
    });

    it("should send final error status", () => {
      progressManager.sendFinalError("Failed to process legal document");

      expect(mockWriteResponseChunk).toHaveBeenLastCalledWith(
        mockResponse,
        expect.objectContaining({
          status: "error",
          message: "Failed to process legal document",
          progress: -2,
        })
      );
    });

    it("should send abort notification", () => {
      progressManager.sendAbort();

      expect(mockWriteResponseChunk).toHaveBeenLastCalledWith(
        mockResponse,
        expect.objectContaining({
          status: "aborted",
          message: "Document processing was cancelled by user.",
          progress: -3,
        })
      );
    });
  });

  describe("Error Handling and Resilience", () => {
    it("should handle writeResponseChunk failures gracefully", () => {
      mockWriteResponseChunk.mockImplementation(() => {
        throw new Error("Response stream closed");
      });

      // Should not throw even if writing fails
      expect(() => {
        progressManager.sendProgress({
          message: "This should not crash the system",
        });
      }).not.toThrow();
    });

    it("should handle Error objects vs string messages", () => {
      const error = new Error("Test error message");
      progressManager.sendError(error);

      expect(mockWriteResponseChunk).toHaveBeenCalledWith(
        mockResponse,
        expect.objectContaining({
          message: "Test error message",
        })
      );

      // Test string error
      progressManager.sendError("String error message");

      expect(mockWriteResponseChunk).toHaveBeenCalledWith(
        mockResponse,
        expect.objectContaining({
          message: "String error message",
        })
      );
    });

    it("should continue processing after individual progress failures", () => {
      // First sendProgress call fails on first writeResponseChunk (cdbProgress)
      mockWriteResponseChunk.mockImplementationOnce(() => {
        throw new Error("First call failed");
      });

      // Second sendProgress call succeeds (both cdbProgress and progress)
      mockWriteResponseChunk.mockImplementationOnce((response, data) => {
        response.write(`data: ${JSON.stringify(data)}\n\n`);
      });

      mockWriteResponseChunk.mockImplementationOnce((response, data) => {
        response.write(`data: ${JSON.stringify(data)}\n\n`);
      });

      // First sendProgress fails after first writeResponseChunk due to try-catch
      progressManager.sendProgress({ message: "First message" });

      // Second sendProgress should succeed with both calls
      progressManager.sendProgress({ message: "Second message" });

      // First sendProgress: 1 call (failed after first writeResponseChunk)
      // Second sendProgress: 2 calls (both succeed)
      expect(mockWriteResponseChunk).toHaveBeenCalledTimes(3);
    });
  });

  describe("WebSocket Integration for Progress Updates", () => {
    it("should simulate WebSocket progress broadcasting to multiple clients", () => {
      const clients = [
        { id: "client-1", ws: { ...mockWebSocket, send: jest.fn() } },
        { id: "client-2", ws: { ...mockWebSocket, send: jest.fn() } },
        { id: "client-3", ws: { ...mockWebSocket, send: jest.fn() } },
      ];

      const progressData: ProgressData = {
        step: 2,
        status: "in_progress",
        message: "Analyzing legal clauses",
        progress: 45,
      };

      // Simulate broadcasting to multiple WebSocket clients
      clients.forEach((client) => {
        const message = JSON.stringify({
          type: "progress",
          uuid: testChatId,
          flowType: testFlowType,
          ...progressData,
        });

        client.ws.send(message);
      });

      // Verify all clients received the progress update
      clients.forEach((client) => {
        expect(client.ws.send).toHaveBeenCalledWith(
          expect.stringContaining('"type":"progress"')
        );
        expect(client.ws.send).toHaveBeenCalledWith(
          expect.stringContaining('"step":2')
        );
        expect(client.ws.send).toHaveBeenCalledWith(
          expect.stringContaining('"progress":45')
        );
      });
    });

    it("should handle disconnected WebSocket clients gracefully", () => {
      const activeClient = {
        ws: {
          send: jest.fn(),
          close: jest.fn(),
          readyState: 1,
          progressUpdates: [],
        },
      }; // OPEN
      const disconnectedClient = {
        ws: {
          send: jest.fn(),
          close: jest.fn(),
          readyState: 3,
          progressUpdates: [],
        },
      }; // CLOSED

      const progressMessage = JSON.stringify({
        type: "progress",
        message: "Test progress update",
      });

      // Only send to active clients
      if (activeClient.ws.readyState === 1) {
        activeClient.ws.send(progressMessage);
      }

      if (disconnectedClient.ws.readyState === 1) {
        disconnectedClient.ws.send(progressMessage);
      }

      expect(activeClient.ws.send).toHaveBeenCalledWith(progressMessage);
      expect(disconnectedClient.ws.send).not.toHaveBeenCalled();
    });
  });

  describe("Progress Data Validation", () => {
    it("should handle missing optional fields gracefully", () => {
      progressManager.sendProgress({
        message: "Minimal progress update",
      });

      expect(mockWriteResponseChunk).toHaveBeenCalledWith(
        mockResponse,
        expect.objectContaining({
          uuid: testChatId,
          flowType: testFlowType,
          message: "Minimal progress update",
        })
      );
    });

    it("should handle complex progress data structures", () => {
      const complexData = {
        step: 3,
        status: "in_progress" as const,
        message: "Processing complex legal analysis",
        progress: 75,
        data: {
          sections: {
            analyzed: 15,
            total: 20,
            details: {
              contracts: 8,
              amendments: 4,
              appendices: 3,
            },
          },
          metadata: {
            estimatedTimeRemaining: "2 minutes",
            processingSpeed: "1.2 sections/min",
          },
        },
        labelArgs: {
          sectionName: "Contract Analysis",
          documentType: "Legal Agreement",
        },
      };

      progressManager.sendProgress(complexData);

      expect(mockWriteResponseChunk).toHaveBeenCalledWith(
        mockResponse,
        expect.objectContaining({
          ...complexData,
          uuid: testChatId,
          flowType: testFlowType,
        })
      );
    });
  });

  describe("Real-time Synchronization Patterns", () => {
    it("should maintain progress state consistency across multiple updates", () => {
      const progressSequence = [
        {
          step: 1,
          status: "starting",
          message: "Step 1 starting",
          progress: -1,
        },
        {
          step: 1,
          status: "in_progress",
          message: "Step 1 processing",
          progress: 25,
        },
        {
          step: 1,
          status: "in_progress",
          message: "Step 1 almost done",
          progress: 90,
        },
        {
          step: 1,
          status: "complete",
          message: "Step 1 completed",
          progress: 100,
        },
        {
          step: 2,
          status: "starting",
          message: "Step 2 starting",
          progress: -1,
        },
      ];

      progressManager.setTotalSteps(3);

      progressSequence.forEach((update) => {
        progressManager.sendProgress(update as ProgressData);
      });

      expect(mockWriteResponseChunk).toHaveBeenCalledTimes(
        progressSequence.length * 2
      ); // Each update sends 2 messages

      // Verify the sequence maintained step information
      const calls = mockWriteResponseChunk.mock.calls;
      progressSequence.forEach((update, index) => {
        const cdbProgressCall = calls[index * 2];
        const progressCall = calls[index * 2 + 1];

        expect(cdbProgressCall[1]).toEqual(
          expect.objectContaining({
            step: update.step,
            status: update.status,
            progress: update.progress,
            totalSteps: 3,
          })
        );

        expect(progressCall[1]).toEqual(
          expect.objectContaining({
            step: update.step,
            status: update.status,
            progress: update.progress,
            totalSteps: 3,
          })
        );
      });
    });

    it("should handle rapid progress updates without data loss", () => {
      const rapidUpdates = Array.from({ length: 50 }, (_, i) => ({
        step: 1,
        status: "in_progress" as const,
        message: `Rapid update ${i + 1}`,
        progress: Math.floor((i + 1) * 2), // 2%, 4%, 6%, ..., 100%
      }));

      // Send all updates rapidly
      rapidUpdates.forEach((update) => {
        progressManager.sendProgress(update);
      });

      expect(mockWriteResponseChunk).toHaveBeenCalledTimes(
        rapidUpdates.length * 2
      );

      // Verify all updates were processed in order
      rapidUpdates.forEach((update, index) => {
        const callIndex = index * 2; // Each update generates 2 calls
        expect(mockWriteResponseChunk).toHaveBeenNthCalledWith(
          callIndex + 1,
          mockResponse,
          expect.objectContaining({
            message: update.message,
            progress: update.progress,
          })
        );
      });
    });
  });
});
