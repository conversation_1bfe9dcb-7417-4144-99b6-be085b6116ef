/**
 * Real-time Message Streaming and Chat Functionality Tests
 *
 * Tests WebSocket-based real-time messaging, chat streaming, response chunking,
 * and the WebSocket plugin integration with AI agents.
 */

import { EventEmitter } from "events";
import {
  websocket,
  WEBSOCKET_BAIL_COMMANDS,
} from "../../utils/agents/aibitat/plugins/websocket";
import { Telemetry } from "../../models/telemetry";
import { RetryError } from "../../utils/agents/aibitat/error";

// Mock dependencies
jest.mock("../../models/telemetry");
jest.mock("chalk", () => ({
  red: jest.fn((text: string) => text),
  yellow: jest.fn((text: string) => text),
}));

// Mock types for testing
interface MockWebSocket {
  send: jest.Mock;
  close: jest.Mock;
  askForFeedback?: jest.Mock;
  awaitResponse?: jest.Mock;
  handleFeedback?: jest.Mock;
}

interface MockAIbitat extends EventEmitter {
  introspect: jest.Mock;
  socket?: {
    send: jest.Mock;
  };
  onError: jest.Mock;
  onMessage: jest.Mock;
  onTerminate: jest.Mock;
  onInterrupt: jest.Mock;
  continue: jest.Mock;
  retry: jest.Mock;
}

interface MockChatMessage {
  from?: string;
  content: string;
  role: "user" | "assistant" | "system";
}

interface MockInterruptNode {
  from: string;
  to: string;
}

describe("Real-time Message Streaming and Chat Functionality", () => {
  let mockSocket: MockWebSocket;
  let mockAibitat: MockAIbitat;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();

    mockSocket = {
      send: jest.fn(),
      close: jest.fn(),
    };

    mockAibitat = new EventEmitter() as MockAIbitat;
    mockAibitat.introspect = jest.fn();
    mockAibitat.onError = jest.fn().mockImplementation((handler) => {
      mockAibitat.on("error", handler);
    });
    mockAibitat.onMessage = jest.fn().mockImplementation((handler) => {
      mockAibitat.on("message", handler);
    });
    mockAibitat.onTerminate = jest.fn().mockImplementation((handler) => {
      mockAibitat.on("terminate", handler);
    });
    mockAibitat.onInterrupt = jest.fn().mockImplementation((handler) => {
      mockAibitat.on("interrupt", handler);
    });
    mockAibitat.continue = jest.fn();
    mockAibitat.retry = jest.fn();

    (Telemetry.sendTelemetry as jest.Mock).mockResolvedValue({});
  });

  afterEach(() => {
    jest.useRealTimers();
    jest.resetAllMocks();
    // Clear any remaining event listeners
    if (mockAibitat) {
      mockAibitat.removeAllListeners();
    }
  });

  describe("WebSocket Plugin Initialization", () => {
    it("should initialize websocket plugin with default configuration", () => {
      const plugin = websocket.plugin({ socket: mockSocket });

      expect(plugin.name).toBe("websocket");
      expect(typeof plugin.setup).toBe("function");
    });

    it("should initialize with custom configuration", () => {
      const config = {
        socket: mockSocket,
        muteUserReply: false,
        introspection: true,
      };

      const plugin = websocket.plugin(config);
      plugin.setup(mockAibitat as any);

      expect(mockAibitat.onMessage).toHaveBeenCalled();
      expect(typeof mockAibitat.introspect).toBe("function");
    });

    it("should validate required socket parameter", () => {
      expect(() => {
        websocket.plugin({});
      }).not.toThrow(); // Plugin should handle missing socket gracefully
    });
  });

  describe("Real-time Message Handling", () => {
    let plugin: any;

    beforeEach(() => {
      plugin = websocket.plugin({
        socket: mockSocket,
        muteUserReply: false,
        introspection: true,
      });
      plugin.setup(mockAibitat as any);
    });

    it("should send assistant messages to WebSocket", () => {
      const assistantMessage: MockChatMessage = {
        from: "assistant",
        content: "Hello, I can help you with legal documents.",
        role: "assistant",
      };

      mockAibitat.emit("message", assistantMessage);

      expect(mockSocket.send).toHaveBeenCalledWith(
        JSON.stringify(assistantMessage)
      );
      expect(Telemetry.sendTelemetry).toHaveBeenCalledWith("agent_chat_sent");
    });

    it("should send system messages to WebSocket", () => {
      const systemMessage: MockChatMessage = {
        from: "system",
        content: "Processing your request...",
        role: "system",
      };

      mockAibitat.emit("message", systemMessage);

      expect(mockSocket.send).toHaveBeenCalledWith(
        JSON.stringify(systemMessage)
      );
      expect(Telemetry.sendTelemetry).toHaveBeenCalledWith("agent_chat_sent");
    });

    it("should mute user messages when muteUserReply is true", () => {
      // Create a fresh mock socket for this test
      const muteSocket = {
        send: jest.fn(),
        close: jest.fn(),
      };

      const mutePlugin = websocket.plugin({
        socket: muteSocket,
        muteUserReply: true,
      });
      mutePlugin.setup(mockAibitat as any);

      const userMessage: MockChatMessage = {
        from: "USER",
        content: "User input message",
        role: "user",
      };

      mockAibitat.emit("message", userMessage);

      expect(muteSocket.send).not.toHaveBeenCalled();
    });

    it("should send user messages when muteUserReply is false", () => {
      const userMessage: MockChatMessage = {
        from: "USER",
        content: "User input message",
        role: "user",
      };

      mockAibitat.emit("message", userMessage);

      expect(mockSocket.send).toHaveBeenCalledWith(JSON.stringify(userMessage));
    });
  });

  describe("Status and Progress Streaming", () => {
    let plugin: any;

    beforeEach(() => {
      plugin = websocket.plugin({
        socket: mockSocket,
        introspection: true,
      });
      plugin.setup(mockAibitat as any);
    });

    it("should send introspection messages for status updates", () => {
      const statusMessage = "Processing legal document analysis...";

      if (mockAibitat.introspect) {
        mockAibitat.introspect(statusMessage);
      }

      expect(mockSocket.send).toHaveBeenCalledWith(
        JSON.stringify({
          type: "statusResponse",
          content: statusMessage,
        })
      );
    });

    it("should not send introspection when disabled", () => {
      const noIntrospectionPlugin = websocket.plugin({
        socket: mockSocket,
        introspection: false,
      });
      noIntrospectionPlugin.setup(mockAibitat as any);

      const statusMessage = "This should not be sent";

      if (mockAibitat.introspect) {
        mockAibitat.introspect(statusMessage);
      }

      expect(mockSocket.send).not.toHaveBeenCalled();
    });

    it("should provide socket.send method for custom messages", () => {
      expect(mockAibitat.socket).toBeDefined();
      expect(typeof mockAibitat.socket?.send).toBe("function");

      mockAibitat.socket?.send("customType", { data: "test" });

      expect(mockSocket.send).toHaveBeenCalledWith(
        JSON.stringify({
          type: "customType",
          content: { data: "test" },
        })
      );
    });

    it("should handle unhandled message types", () => {
      mockAibitat.socket?.send(); // No parameters

      expect(mockSocket.send).toHaveBeenCalledWith(
        JSON.stringify({
          type: "__unhandled",
          content: "",
        })
      );
    });
  });

  describe("Interactive Feedback and Interrupts", () => {
    let plugin: any;

    beforeEach(() => {
      plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat as any);
    });

    it("should handle interrupt requests for feedback", async () => {
      const interruptNode: MockInterruptNode = {
        from: "user",
        to: "assistant",
      };

      // Set up the askForFeedback mock
      mockSocket.askForFeedback = jest.fn().mockResolvedValue("continue");

      const interruptPromise = new Promise<void>((resolve) => {
        mockAibitat.emit("interrupt", interruptNode);
        resolve();
      });

      await interruptPromise;

      expect(mockSocket.askForFeedback).toHaveBeenCalledWith(
        mockSocket,
        interruptNode
      );
    });

    it("should handle feedback timeout", async () => {
      const interruptNode: MockInterruptNode = {
        from: "user",
        to: "assistant",
      };

      // Setup awaitResponse to simulate timeout
      mockSocket.awaitResponse = jest.fn().mockImplementation(() => {
        return new Promise((resolve) => {
          setTimeout(() => resolve("exit"), 300000); // 5 minute timeout
        });
      });

      mockSocket.askForFeedback = jest.fn().mockImplementation(() => {
        return mockSocket.awaitResponse!();
      });

      mockAibitat.emit("interrupt", interruptNode);

      // Fast-forward time to trigger timeout
      jest.advanceTimersByTime(300000);

      expect(mockSocket.awaitResponse).toHaveBeenCalled();
    });

    it("should handle bail commands in feedback", async () => {
      const interruptNode: MockInterruptNode = {
        from: "user",
        to: "assistant",
      };

      // Test each bail command
      for (const bailCommand of WEBSOCKET_BAIL_COMMANDS) {
        mockSocket.askForFeedback = jest.fn().mockResolvedValue(bailCommand);

        const interruptPromise = new Promise<void>((resolve) => {
          mockAibitat.emit("interrupt", interruptNode);
          resolve();
        });

        await interruptPromise;

        expect(mockSocket.close).toHaveBeenCalled();

        // Reset mock for next iteration
        mockSocket.close.mockClear();
      }
    });

    it("should continue conversation with valid feedback", async () => {
      const interruptNode: MockInterruptNode = {
        from: "user",
        to: "assistant",
      };

      const feedback = "Please proceed with the analysis";
      mockSocket.askForFeedback = jest.fn().mockResolvedValue(feedback);

      const interruptPromise = new Promise<void>((resolve) => {
        mockAibitat.emit("interrupt", interruptNode);
        resolve();
      });

      await interruptPromise;

      expect(mockAibitat.continue).toHaveBeenCalledWith(feedback);
      expect(mockSocket.close).not.toHaveBeenCalled();
    });
  });

  describe("Connection Termination", () => {
    let plugin: any;

    beforeEach(() => {
      plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat as any);
    });

    it("should close socket on conversation termination", () => {
      mockAibitat.emit("terminate");

      expect(mockSocket.close).toHaveBeenCalled();
    });

    it("should handle multiple termination events gracefully", () => {
      mockAibitat.emit("terminate");
      mockAibitat.emit("terminate");
      mockAibitat.emit("terminate");

      expect(mockSocket.close).toHaveBeenCalledTimes(3);
    });
  });

  describe("Error Handling and Retry Logic", () => {
    let plugin: any;

    beforeEach(() => {
      plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat as any);
    });

    it("should handle general errors with introspection", async () => {
      // Create a plugin with introspection enabled
      const introspectionPlugin = websocket.plugin({
        socket: mockSocket,
        introspection: true,
      });
      introspectionPlugin.setup(mockAibitat as any);

      // Spy on the introspect method that was just created by the plugin
      const introspectSpy = jest.spyOn(mockAibitat, "introspect");

      const error = new Error("Test error message");

      await mockAibitat.emit("error", error);

      expect(introspectSpy).toHaveBeenCalledWith(
        `Error encountered while running: ${error.message}`
      );
    });

    it("should handle RetryError with automatic retry", async () => {
      const retryError = new RetryError("Temporary failure");

      await mockAibitat.emit("error", retryError);

      // Fast-forward time to trigger retry
      jest.advanceTimersByTime(60000);

      expect(mockAibitat.retry).toHaveBeenCalled();
    });

    it("should not retry for non-RetryError", async () => {
      const generalError = new Error("General error");

      await mockAibitat.emit("error", generalError);

      jest.advanceTimersByTime(60000);

      expect(mockAibitat.retry).not.toHaveBeenCalled();
    });

    it("should handle errors without message property", async () => {
      // Create a plugin with introspection enabled
      const introspectionPlugin = websocket.plugin({
        socket: mockSocket,
        introspection: true,
      });
      introspectionPlugin.setup(mockAibitat as any);

      // Spy on the introspect method that was just created by the plugin
      const introspectSpy = jest.spyOn(mockAibitat, "introspect");

      const errorWithoutMessage = {} as Error;

      await mockAibitat.emit("error", errorWithoutMessage);

      // Should not throw and should handle gracefully
      // The plugin checks for error.message, so introspect won't be called
      expect(introspectSpy).not.toHaveBeenCalled();
    });
  });

  describe("Message Formatting and JSON Handling", () => {
    let plugin: any;

    beforeEach(() => {
      plugin = websocket.plugin({ socket: mockSocket });
      plugin.setup(mockAibitat as any);
    });

    it("should properly serialize complex message objects", () => {
      const complexMessage = {
        from: "assistant",
        content: "Here is your legal analysis",
        role: "assistant",
        metadata: {
          sources: ["doc1.pdf", "doc2.pdf"],
          confidence: 0.95,
          timestamp: Date.now(),
        },
      };

      mockAibitat.emit("message", complexMessage);

      expect(mockSocket.send).toHaveBeenCalledWith(
        JSON.stringify(complexMessage)
      );
    });

    it("should handle messages with special characters", () => {
      const messageWithSpecialChars = {
        from: "assistant",
        content: 'Legal text with quotes: "Article 123" and symbols: §, ©, ®',
        role: "assistant",
      };

      mockAibitat.emit("message", messageWithSpecialChars);

      expect(mockSocket.send).toHaveBeenCalledWith(
        JSON.stringify(messageWithSpecialChars)
      );
    });

    it("should handle malformed JSON in feedback gracefully", () => {
      if (mockSocket.handleFeedback) {
        const malformedJson = '{"invalid": json}';

        expect(() => {
          mockSocket.handleFeedback!(malformedJson);
        }).not.toThrow();
      }
    });
  });

  describe("Concurrent Message Handling", () => {
    let plugin: any;

    beforeEach(() => {
      plugin = websocket.plugin({
        socket: mockSocket,
        muteUserReply: false,
      });
      plugin.setup(mockAibitat as any);
    });

    it("should handle rapid message sequences", () => {
      const messages = [
        { from: "user", content: "First message", role: "user" as const },
        {
          from: "assistant",
          content: "First response",
          role: "assistant" as const,
        },
        { from: "user", content: "Second message", role: "user" as const },
        {
          from: "assistant",
          content: "Second response",
          role: "assistant" as const,
        },
      ];

      // Emit all messages rapidly
      messages.forEach((message) => {
        mockAibitat.emit("message", message);
      });

      expect(mockSocket.send).toHaveBeenCalledTimes(4);
      messages.forEach((message, index) => {
        expect(mockSocket.send).toHaveBeenNthCalledWith(
          index + 1,
          JSON.stringify(message)
        );
      });
    });

    it("should maintain message order under high load", () => {
      const messageCount = 100;
      const messages = Array.from({ length: messageCount }, (_, i) => ({
        from: "assistant",
        content: `Message ${i}`,
        role: "assistant" as const,
        sequence: i,
      }));

      // Emit all messages
      messages.forEach((message) => {
        mockAibitat.emit("message", message);
      });

      expect(mockSocket.send).toHaveBeenCalledTimes(messageCount);

      // Verify order is maintained
      messages.forEach((message, index) => {
        expect(mockSocket.send).toHaveBeenNthCalledWith(
          index + 1,
          JSON.stringify(message)
        );
      });
    });
  });

  describe("Bail Command Detection", () => {
    it("should recognize all defined bail commands", () => {
      const expectedBailCommands = [
        "exit",
        "/exit",
        "stop",
        "/stop",
        "halt",
        "/halt",
        "/reset",
      ];

      expect(WEBSOCKET_BAIL_COMMANDS).toEqual(expectedBailCommands);
    });

    it("should handle case-sensitive bail command detection", () => {
      // Bail commands should be exact matches (case-sensitive)
      const testCases = [
        { command: "exit", shouldBail: true },
        { command: "EXIT", shouldBail: false },
        { command: "Exit", shouldBail: false },
        { command: "/stop", shouldBail: true },
        { command: "/STOP", shouldBail: false },
      ];

      testCases.forEach(({ command, shouldBail }) => {
        const isInBailCommands = WEBSOCKET_BAIL_COMMANDS.includes(command);
        expect(isInBailCommands).toBe(shouldBail);
      });
    });
  });
});
