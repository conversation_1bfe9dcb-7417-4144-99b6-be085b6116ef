{"ts-node": {"files": true}, "compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "allowJs": false, "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "noEmit": true, "declaration": false, "downlevelIteration": true, "isolatedModules": true, "types": ["node", "jest"], "typeRoots": ["./types", "./node_modules/@types"]}, "include": ["**/*.ts"], "exclude": ["node_modules/**/*", "dist/**/*", "src/**/*", "frontend/**/*"]}