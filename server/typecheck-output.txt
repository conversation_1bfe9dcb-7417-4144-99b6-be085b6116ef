
> ist-legal-server@1.1.3 typecheck
> tsc --noEmit -p tsconfig.check.json

endpoints/api/workspace/index.ts(17,10): error TS2305: Module '"../../../utils/chats/apiChatHandler"' has no exported member 'ApiChatHandler'.
endpoints/api/workspaceThread/index.ts(17,10): error TS2305: Module '"../../../utils/chats/apiChatHandler"' has no exported member 'ApiChatHandler'.
endpoints/api/workspaceThread/index.ts(763,26): error TS2304: Cannot find name 'Source'.
types/llm-bridge.ts(52,18): error TS2320: Interface 'LLMBridge' cannot simultaneously extend types 'LLMProvider' and 'LLMConnector'.
  Named property 'handleStream' of types 'LLMProvider' and 'LLMConnector' are not identical.
types/llm-bridge.ts(214,9): error TS2353: Object literal may only specify known properties, and 'stop' does not exist in type 'Partial<LLMChatCompletionOptions>'.
types/llm-bridge.ts(220,9): error TS2322: Type '((usage: Partial<UsageMetrics>) => void) | undefined' is not assignable to type '((usage: Partial<UsageMetrics>) => UsageMetrics) | undefined'.
  Type '(usage: Partial<UsageMetrics>) => void' is not assignable to type '(usage: Partial<UsageMetrics>) => UsageMetrics'.
    Type 'void' is not assignable to type 'UsageMetrics'.
types/llm-bridge.ts(290,5): error TS2322: Type '() => string' is not assignable to type '() => LLMProvider'.
  Type 'string' is not assignable to type 'LLMProvider'.
types/workspace-bridge.ts(161,10): error TS2367: This comparison appears to be unintentional because the types 'boolean | null' and 'number' have no overlap.
types/workspace-bridge.ts(164,20): error TS2367: This comparison appears to be unintentional because the types 'boolean' and 'number' have no overlap.
utils/agents/aibitat/example/websocket/websock-multi-turn-chat.ts(62,7): error TS2339: Property 'ws' does not exist on type 'Express'.
utils/agents/aibitat/index.ts(903,54): error TS2304: Cannot find name 'ProviderClass'.
utils/agents/aibitat/index.ts(905,33): error TS2304: Cannot find name 'ProviderClass'.
utils/agents/aibitat/index.ts(922,15): error TS2304: Cannot find name 'ProviderClass'.
utils/agents/aibitat/index.ts(930,55): error TS2304: Cannot find name 'ProviderClass'.
utils/agents/aibitat/index.ts(934,15): error TS2304: Cannot find name 'ProviderClass'.
utils/agents/aibitat/index.ts(944,58): error TS2304: Cannot find name 'ProviderClass'.
utils/agents/aibitat/index.ts(952,19): error TS2304: Cannot find name 'ProviderClass'.
utils/agents/aibitat/plugins/chat-history.ts(118,19): error TS2322: Type 'number | null' is not assignable to type 'number'.
  Type 'null' is not assignable to type 'number'.
utils/agents/aibitat/plugins/chat-history.ts(119,11): error TS2322: Type 'string | null' is not assignable to type 'number | null | undefined'.
  Type 'string' is not assignable to type 'number'.
utils/agents/aibitat/plugins/chat-history.ts(145,19): error TS2322: Type 'number | null' is not assignable to type 'number'.
  Type 'null' is not assignable to type 'number'.
utils/agents/aibitat/plugins/chat-history.ts(146,11): error TS2322: Type 'string | null' is not assignable to type 'number | null | undefined'.
  Type 'string' is not assignable to type 'number'.
utils/agents/aibitat/plugins/memory.ts(87,44): error TS2345: Argument of type 'MemoryFunctionConfig' is not assignable to parameter of type 'AgentFunction'.
  The types of 'parameters.type' are incompatible between these types.
    Type 'string' is not assignable to type '"object"'.
utils/agents/aibitat/plugins/rechart.ts(102,13): error TS2322: Type '(this: RechartContext, { type, dataset, title }: RechartArgs) => Promise<string>' is not assignable to type '((args: unknown, context?: AgentContext | undefined) => unknown) | ((this: unknown, args: unknown) => unknown)'.
  Type '(this: RechartContext, { type, dataset, title }: RechartArgs) => Promise<string>' is not assignable to type '(args: unknown, context?: AgentContext | undefined) => unknown'.
    Types of parameters '__1' and 'args' are incompatible.
      Type 'unknown' is not assignable to type 'RechartArgs'.
utils/agents/aibitat/plugins/save-file-browser.ts(90,13): error TS2322: Type '(this: SaveFileContext, { file_content, filename }: SaveFileArgs) => Promise<string>' is not assignable to type '((args: unknown, context?: AgentContext | undefined) => unknown) | ((this: unknown, args: unknown) => unknown)'.
  Type '(this: SaveFileContext, { file_content, filename }: SaveFileArgs) => Promise<string>' is not assignable to type '(args: unknown, context?: AgentContext | undefined) => unknown'.
    Types of parameters '__1' and 'args' are incompatible.
      Type 'unknown' is not assignable to type 'SaveFileArgs'.
utils/agents/aibitat/plugins/sql-agent/get-table-schema.ts(23,24): error TS2683: 'this' implicitly has type 'any' because it does not have a type annotation.
utils/agents/aibitat/plugins/sql-agent/get-table-schema.ts(40,34): error TS2554: Expected 2 arguments, but got 1.
utils/agents/aibitat/plugins/sql-agent/get-table-schema.ts(41,41): error TS2551: Property 'getTableSchema' does not exist on type 'DatabaseConnector'. Did you mean 'getTableSchemaSql'?
utils/agents/aibitat/plugins/sql-agent/get-table-schema.ts(52,24): error TS2339: Property 'close' does not exist on type 'DatabaseConnector'.
utils/agents/aibitat/plugins/sql-agent/get-table-schema.ts(53,28): error TS2339: Property 'close' does not exist on type 'DatabaseConnector'.
utils/agents/aibitat/plugins/sql-agent/get-table-schema.ts(133,11): error TS2322: Type '({ database_id, table_name, }: GetTableSchemaParams) => Promise<string>' is not assignable to type '((args: unknown, context?: AgentContext | undefined) => unknown) | ((this: unknown, args: unknown) => unknown)'.
  Type '({ database_id, table_name, }: GetTableSchemaParams) => Promise<string>' is not assignable to type '(args: unknown, context?: AgentContext | undefined) => unknown'.
    Types of parameters '__0' and 'args' are incompatible.
      Type 'unknown' is not assignable to type 'GetTableSchemaParams'.
utils/agents/aibitat/plugins/sql-agent/index.ts(3,10): error TS2724: '"./list-table"' has no exported member named 'SqlAgentListTables'. Did you mean 'SqlAgentListTable'?
utils/agents/aibitat/plugins/sql-agent/list-database.ts(21,24): error TS2683: 'this' implicitly has type 'any' because it does not have a type annotation.
utils/agents/aibitat/plugins/sql-agent/list-table.ts(21,24): error TS2683: 'this' implicitly has type 'any' because it does not have a type annotation.
utils/agents/aibitat/plugins/sql-agent/list-table.ts(38,34): error TS2554: Expected 2 arguments, but got 1.
utils/agents/aibitat/plugins/sql-agent/list-table.ts(39,41): error TS2551: Property 'getTablesList' does not exist on type 'DatabaseConnector'. Did you mean 'getTablesSql'?
utils/agents/aibitat/plugins/sql-agent/list-table.ts(50,24): error TS2339: Property 'close' does not exist on type 'DatabaseConnector'.
utils/agents/aibitat/plugins/sql-agent/list-table.ts(51,28): error TS2339: Property 'close' does not exist on type 'DatabaseConnector'.
utils/agents/aibitat/plugins/sql-agent/list-table.ts(119,11): error TS2322: Type '({ database_id, }: ListTablesParams) => Promise<string>' is not assignable to type '((args: unknown, context?: AgentContext | undefined) => unknown) | ((this: unknown, args: unknown) => unknown)'.
  Type '({ database_id, }: ListTablesParams) => Promise<string>' is not assignable to type '(args: unknown, context?: AgentContext | undefined) => unknown'.
    Types of parameters '__0' and 'args' are incompatible.
      Type 'unknown' is not assignable to type 'ListTablesParams'.
utils/agents/aibitat/plugins/sql-agent/query.ts(114,24): error TS2683: 'this' implicitly has type 'any' because it does not have a type annotation.
utils/agents/aibitat/plugins/sql-agent/query.ts(159,30): error TS2554: Expected 2 arguments, but got 1.
utils/agents/aibitat/plugins/sql-agent/query.ts(202,20): error TS2339: Property 'close' does not exist on type 'DatabaseConnector'.
utils/agents/aibitat/plugins/sql-agent/query.ts(203,24): error TS2339: Property 'close' does not exist on type 'DatabaseConnector'.
utils/agents/aibitat/plugins/sql-agent/query.ts(278,11): error TS2322: Type '({ database_id, sql_query, }: QueryParams) => Promise<string>' is not assignable to type '((args: unknown, context?: AgentContext | undefined) => unknown) | ((this: unknown, args: unknown) => unknown)'.
  Type '({ database_id, sql_query, }: QueryParams) => Promise<string>' is not assignable to type '(args: unknown, context?: AgentContext | undefined) => unknown'.
    Types of parameters '__0' and 'args' are incompatible.
      Type 'unknown' is not assignable to type 'QueryParams'.
utils/agents/aibitat/plugins/summarize.ts(87,28): error TS2345: Argument of type 'FunctionConfig' is not assignable to parameter of type 'AgentFunction'.
  The types of 'parameters.type' are incompatible between these types.
    Type 'string' is not assignable to type '"object"'.
utils/agents/aibitat/plugins/web-browsing.ts(109,28): error TS2345: Argument of type 'WebBrowsingContext & { search: (query: string) => Promise<string>; _googleSearchEngine: (query: string) => Promise<string>; _searchApi: (query: string) => Promise<...>; ... 4 more ...; _tavilySearch: (query: string) => Promise<...>; }' is not assignable to parameter of type 'AgentFunction'.
  Type 'WebBrowsingContext & { search: (query: string) => Promise<string>; _googleSearchEngine: (query: string) => Promise<string>; _searchApi: (query: string) => Promise<...>; ... 4 more ...; _tavilySearch: (query: string) => Promise<...>; }' is missing the following properties from type 'AgentFunction': name, description, parameters, handler
utils/agents/aibitat/plugins/web-browsing.ts(109,28): error TS2352: Conversion of type '{ super: AIbitat; name: string; description: string; examples: { prompt: string; call: string; }[]; parameters: { $schema: string; type: string; properties: { query: { type: string; description: string; }; }; additionalProperties: boolean; }; ... 8 more ...; _tavilySearch: (this: WebBrowsingContext, query: string) =...' to type 'WebBrowsingContext & { search: (query: string) => Promise<string>; _googleSearchEngine: (query: string) => Promise<string>; _searchApi: (query: string) => Promise<...>; ... 4 more ...; _tavilySearch: (query: string) => Promise<...>; }' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type '{ super: AIbitat; name: string; description: string; examples: { prompt: string; call: string; }[]; parameters: { $schema: string; type: string; properties: { query: { type: string; description: string; }; }; additionalProperties: boolean; }; ... 8 more ...; _tavilySearch: (this: WebBrowsingContext, query: string) =...' is not comparable to type 'WebBrowsingContext'.
    Types of property 'super' are incompatible.
      Type 'AIbitat' is not comparable to type 'AIbitat & { introspect: (message: string) => void; handlerProps: { log: (message: string) => void; }; }'.
        Type 'AIbitat' is not comparable to type '{ introspect: (message: string) => void; handlerProps: { log: (message: string) => void; }; }'.
          Types of property 'handlerProps' are incompatible.
            Property 'log' is missing in type 'Record<string, unknown>' but required in type '{ log: (message: string) => void; }'.
utils/agents/aibitat/plugins/web-browsing.ts(304,27): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string | SearchResult'.
  Type 'undefined' is not assignable to type 'string | SearchResult'.
utils/agents/aibitat/plugins/web-browsing.ts(306,27): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string | SearchResult'.
  Type 'undefined' is not assignable to type 'string | SearchResult'.
utils/agents/aibitat/plugins/web-browsing.ts(378,27): error TS18046: 'response' is of type 'unknown'.
utils/agents/aibitat/plugins/web-browsing.ts(379,25): error TS2339: Property 'organic' does not exist on type '{}'.
utils/agents/aibitat/plugins/web-browsing.ts(510,25): error TS2339: Property 'results' does not exist on type '{}'.
utils/agents/aibitat/plugins/web-browsing.ts(587,25): error TS2339: Property 'results' does not exist on type '{}'.
utils/agents/aibitat/plugins/web-browsing.ts(650,25): error TS2339: Property 'results' does not exist on type '{}'.
utils/agents/aibitat/plugins/web-scraping.ts(64,28): error TS2345: Argument of type 'WebScrapingFunctionConfig' is not assignable to parameter of type 'AgentFunction'.
  The types of 'parameters.type' are incompatible between these types.
    Type 'string' is not assignable to type '"object"'.
utils/agents/aibitat/plugins/websocket.ts(81,3): error TS2322: Type '({ socket, muteUserReply, introspection, }: WebSocketPluginConfig) => { name: string; setup(aibitat: AIbitat): void; }' is not assignable to type '((args?: unknown) => { name: string; setup: (aibitat: AIbitat) => void; }) & ((config: WebSocketPluginConfig) => { name: string; setup: (aibitat: AIbitat) => void; })'.
  Type '({ socket, muteUserReply, introspection, }: WebSocketPluginConfig) => { name: string; setup(aibitat: AIbitat): void; }' is not assignable to type '(args?: unknown) => { name: string; setup: (aibitat: AIbitat) => void; }'.
    Types of parameters '__0' and 'args' are incompatible.
      Type 'unknown' is not assignable to type 'WebSocketPluginConfig'.
utils/agents/aibitat/providers/ai-provider.ts(68,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(73,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(81,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(89,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(101,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(109,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(117,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(125,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(126,11): error TS2322: Type 'number | null | undefined' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(131,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(144,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(152,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(179,11): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(187,11): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(195,11): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(203,11): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(211,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/anthropic.ts(208,9): error TS2416: Property 'complete' in type 'AnthropicProvider' is not assignable to the same property in base type 'Provider'.
  Type '(messages: ChatMessage[], functions?: AgentFunction[] | null) => Promise<CompletionResult>' is not assignable to type '(messages: unknown[], functions?: unknown[] | undefined, config?: unknown) => Promise<unknown>'.
    Types of parameters 'functions' and 'functions' are incompatible.
      Type 'unknown[] | undefined' is not assignable to type 'AgentFunction[] | null | undefined'.
        Type 'unknown[]' is not assignable to type 'AgentFunction[]'.
          Type 'unknown' is not assignable to type 'AgentFunction'.
utils/agents/aibitat/providers/anthropic.ts(214,31): error TS2571: Object is of type 'unknown'.
utils/agents/aibitat/providers/azure.ts(78,5): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | null'.
  Type 'undefined' is not assignable to type 'string | null'.
utils/agents/aibitat/providers/azure.ts(112,11): error TS2345: Argument of type '({ messages, }: { messages: ChatMessage[]; }) => Promise<string | null>' is not assignable to parameter of type '(params: { messages: ChatMessage[]; }) => Promise<string>'.
  Type 'Promise<string | null>' is not assignable to type 'Promise<string>'.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
utils/agents/aibitat/providers/bedrock.ts(117,8): error TS2339: Property 'invoke' does not exist on type 'ChatBedrockConverse'.
utils/agents/aibitat/providers/bedrock.ts(118,14): error TS7006: Parameter 'res' implicitly has an 'any' type.
utils/agents/aibitat/providers/bedrock.ts(119,15): error TS7006: Parameter 'e' implicitly has an 'any' type.
utils/agents/aibitat/providers/bedrock.ts(145,11): error TS2345: Argument of type '({ messages, }: { messages: ChatMessage[]; }) => Promise<string | null>' is not assignable to parameter of type '(params: { messages: ChatMessage[]; }) => Promise<string>'.
  Type 'Promise<string | null>' is not assignable to type 'Promise<string>'.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
utils/agents/aibitat/providers/bedrock.ts(176,44): error TS2339: Property 'invoke' does not exist on type 'ChatBedrockConverse'.
utils/agents/aibitat/providers/deepseek.ts(44,30): error TS1210: Code contained in a class is evaluated in JavaScript's strict mode which does not allow this use of 'arguments'. For more information, see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode.
utils/agents/aibitat/providers/deepseek.ts(126,11): error TS2345: Argument of type '({ messages, }: { messages: ChatMessage[]; }) => Promise<string | null>' is not assignable to parameter of type '(params: { messages: ChatMessage[]; }) => Promise<string>'.
  Type 'Promise<string | null>' is not assignable to type 'Promise<string>'.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
utils/agents/aibitat/providers/fireworksai.ts(38,30): error TS1210: Code contained in a class is evaluated in JavaScript's strict mode which does not allow this use of 'arguments'. For more information, see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode.
utils/agents/aibitat/providers/genericOpenAi.ts(129,11): error TS2345: Argument of type '({ messages, }: { messages: ChatMessage[]; }) => Promise<string | null>' is not assignable to parameter of type '(params: { messages: ChatMessage[]; }) => Promise<string>'.
  Type 'Promise<string | null>' is not assignable to type 'Promise<string>'.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
utils/agents/aibitat/providers/groq.ts(42,30): error TS1210: Code contained in a class is evaluated in JavaScript's strict mode which does not allow this use of 'arguments'. For more information, see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode.
utils/agents/aibitat/providers/groq.ts(117,11): error TS2345: Argument of type '({ messages, }: { messages: ChatMessage[]; }) => Promise<string | null>' is not assignable to parameter of type '(params: { messages: ChatMessage[]; }) => Promise<string>'.
  Type 'Promise<string | null>' is not assignable to type 'Promise<string>'.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
utils/agents/aibitat/providers/helpers/untooled.ts(193,9): error TS2352: Conversion of type 'this' to type 'UnTooled & { providerLog: (message: string) => void; }' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'UnTooled' is not comparable to type 'UnTooled & { providerLog: (message: string) => void; }'.
    Property 'providerLog' is missing in type 'UnTooled' but required in type '{ providerLog: (message: string) => void; }'.
      Type 'this' is not comparable to type '{ providerLog: (message: string) => void; }'.
        Property 'providerLog' is missing in type 'UnTooled' but required in type '{ providerLog: (message: string) => void; }'.
utils/agents/aibitat/providers/helpers/untooled.ts(200,9): error TS2352: Conversion of type 'this' to type 'UnTooled & { providerLog: (message: string) => void; }' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'UnTooled' is not comparable to type 'UnTooled & { providerLog: (message: string) => void; }'.
    Property 'providerLog' is missing in type 'UnTooled' but required in type '{ providerLog: (message: string) => void; }'.
      Type 'this' is not comparable to type '{ providerLog: (message: string) => void; }'.
        Property 'providerLog' is missing in type 'UnTooled' but required in type '{ providerLog: (message: string) => void; }'.
utils/agents/aibitat/providers/koboldcpp.ts(41,30): error TS1210: Code contained in a class is evaluated in JavaScript's strict mode which does not allow this use of 'arguments'. For more information, see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode.
utils/agents/aibitat/providers/koboldcpp.ts(118,11): error TS2345: Argument of type '({ messages, }: { messages: ChatMessage[]; }) => Promise<string | null>' is not assignable to parameter of type '(params: { messages: ChatMessage[]; }) => Promise<string>'.
  Type 'Promise<string | null>' is not assignable to type 'Promise<string>'.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
utils/agents/aibitat/providers/lmstudio.ts(41,30): error TS1210: Code contained in a class is evaluated in JavaScript's strict mode which does not allow this use of 'arguments'. For more information, see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode.
utils/agents/aibitat/providers/lmstudio.ts(118,11): error TS2345: Argument of type '({ messages, }: { messages: ChatMessage[]; }) => Promise<string | null>' is not assignable to parameter of type '(params: { messages: ChatMessage[]; }) => Promise<string>'.
  Type 'Promise<string | null>' is not assignable to type 'Promise<string>'.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
utils/agents/aibitat/providers/localai.ts(42,30): error TS1210: Code contained in a class is evaluated in JavaScript's strict mode which does not allow this use of 'arguments'. For more information, see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode.
utils/agents/aibitat/providers/localai.ts(120,11): error TS2345: Argument of type '({ messages, }: { messages: ChatMessage[]; }) => Promise<string | null>' is not assignable to parameter of type '(params: { messages: ChatMessage[]; }) => Promise<string>'.
  Type 'Promise<string | null>' is not assignable to type 'Promise<string>'.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
utils/agents/aibitat/providers/mistral.ts(36,30): error TS1210: Code contained in a class is evaluated in JavaScript's strict mode which does not allow this use of 'arguments'. For more information, see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode.
utils/agents/aibitat/providers/mistral.ts(111,11): error TS2345: Argument of type '({ messages, }: { messages: ChatMessage[]; }) => Promise<string | null>' is not assignable to parameter of type '(params: { messages: ChatMessage[]; }) => Promise<string>'.
  Type 'Promise<string | null>' is not assignable to type 'Promise<string>'.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
utils/agents/aibitat/providers/ollama.ts(98,31): error TS2352: Conversion of type 'this' to type 'ProviderWithLogger' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'OllamaProvider' is missing the following properties from type 'ProviderWithLogger': providerLog, deduplicator, functionCall, cleanMsgs
utils/agents/aibitat/providers/ollama.ts(105,12): error TS2352: Conversion of type 'this' to type 'ProviderWithLogger' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'OllamaProvider' is missing the following properties from type 'ProviderWithLogger': providerLog, deduplicator, functionCall, cleanMsgs
utils/agents/aibitat/providers/ollama.ts(108,12): error TS2352: Conversion of type 'this' to type 'ProviderWithLogger' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'OllamaProvider' is missing the following properties from type 'ProviderWithLogger': providerLog, deduplicator, functionCall, cleanMsgs
utils/agents/aibitat/providers/ollama.ts(125,10): error TS2352: Conversion of type 'this' to type 'ProviderWithLogger' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'OllamaProvider' is missing the following properties from type 'ProviderWithLogger': providerLog, deduplicator, functionCall, cleanMsgs
utils/agents/aibitat/providers/ollama.ts(130,22): error TS2352: Conversion of type 'this' to type 'ProviderWithLogger' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'OllamaProvider' is missing the following properties from type 'ProviderWithLogger': providerLog, deduplicator, functionCall, cleanMsgs
utils/agents/aibitat/providers/ollama.ts(142,8): error TS2352: Conversion of type 'this' to type 'ProviderWithLogger' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'OllamaProvider' is missing the following properties from type 'ProviderWithLogger': providerLog, deduplicator, functionCall, cleanMsgs
utils/agents/aibitat/providers/openai.ts(100,9): error TS2416: Property 'complete' in type 'OpenAIProvider' is not assignable to the same property in base type 'Provider'.
  Type '(messages: ChatMessage[], functions?: AgentFunction[] | null) => Promise<CompletionResult>' is not assignable to type '(messages: unknown[], functions?: unknown[] | undefined, config?: unknown) => Promise<unknown>'.
    Types of parameters 'functions' and 'functions' are incompatible.
      Type 'unknown[] | undefined' is not assignable to type 'AgentFunction[] | null | undefined'.
        Type 'unknown[]' is not assignable to type 'AgentFunction[]'.
          Type 'unknown' is not assignable to type 'AgentFunction'.
utils/agents/aibitat/providers/openai.ts(105,30): error TS2571: Object is of type 'unknown'.
utils/agents/aibitat/providers/openai.ts(110,22): error TS2352: Conversion of type 'AgentFunction[]' to type 'ChatCompletionTool[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'AgentFunction' is missing the following properties from type 'ChatCompletionTool': function, type
utils/agents/aibitat/providers/openrouter.ts(41,30): error TS1210: Code contained in a class is evaluated in JavaScript's strict mode which does not allow this use of 'arguments'. For more information, see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode.
utils/agents/aibitat/providers/openrouter.ts(122,11): error TS2345: Argument of type '({ messages, }: { messages: ChatMessage[]; }) => Promise<string | null>' is not assignable to parameter of type '(params: { messages: ChatMessage[]; }) => Promise<string>'.
  Type 'Promise<string | null>' is not assignable to type 'Promise<string>'.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
utils/agents/aibitat/providers/perplexity.ts(41,30): error TS1210: Code contained in a class is evaluated in JavaScript's strict mode which does not allow this use of 'arguments'. For more information, see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode.
utils/agents/aibitat/providers/perplexity.ts(118,11): error TS2345: Argument of type '({ messages, }: { messages: ChatMessage[]; }) => Promise<string | null>' is not assignable to parameter of type '(params: { messages: ChatMessage[]; }) => Promise<string>'.
  Type 'Promise<string | null>' is not assignable to type 'Promise<string>'.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
utils/agents/aibitat/providers/textgenwebui.ts(41,30): error TS1210: Code contained in a class is evaluated in JavaScript's strict mode which does not allow this use of 'arguments'. For more information, see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode.
utils/agents/aibitat/providers/textgenwebui.ts(118,11): error TS2345: Argument of type '({ messages, }: { messages: ChatMessage[]; }) => Promise<string | null>' is not assignable to parameter of type '(params: { messages: ChatMessage[]; }) => Promise<string>'.
  Type 'Promise<string | null>' is not assignable to type 'Promise<string>'.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
utils/agents/aibitat/providers/togetherai.ts(41,30): error TS1210: Code contained in a class is evaluated in JavaScript's strict mode which does not allow this use of 'arguments'. For more information, see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode.
utils/agents/aibitat/providers/togetherai.ts(118,11): error TS2345: Argument of type '({ messages, }: { messages: ChatMessage[]; }) => Promise<string | null>' is not assignable to parameter of type '(params: { messages: ChatMessage[]; }) => Promise<string>'.
  Type 'Promise<string | null>' is not assignable to type 'Promise<string>'.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
utils/agents/aibitat/providers/xai.ts(41,30): error TS1210: Code contained in a class is evaluated in JavaScript's strict mode which does not allow this use of 'arguments'. For more information, see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode.
utils/agents/aibitat/providers/xai.ts(116,11): error TS2345: Argument of type '({ messages, }: { messages: ChatMessage[]; }) => Promise<string | null>' is not assignable to parameter of type '(params: { messages: ChatMessage[]; }) => Promise<string>'.
  Type 'Promise<string | null>' is not assignable to type 'Promise<string>'.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
utils/agents/aibitat/utils/summarize.ts(53,40): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'BaseLanguageModelInterface<any, BaseLanguageModelCallOptions>'.
utils/agents/ephemeral.ts(96,13): error TS2353: Object literal may only specify known properties, and 'from' does not exist in type '{ role: string; content: string; }'.
utils/agents/ephemeral.ts(106,56): error TS2339: Property 'text' does not exist on type '{}'.
utils/agents/ephemeral.ts(176,24): error TS2339: Property 'startupConfig' does not exist on type '{ name: string; plugin: () => AgentPlugin; }'.
utils/agents/ephemeral.ts(180,28): error TS2345: Argument of type 'AgentPlugin' is not assignable to parameter of type '{ setup: (aibitat: unknown) => void; } | { plugin: (options?: unknown) => { setup: (aibitat: unknown) => void; }; }'.
  Type 'AgentPlugin' is not assignable to type '{ plugin: (options?: unknown) => { setup: (aibitat: unknown) => void; }; }'.
    The types of 'plugin(...).setup' are incompatible between these types.
      Type '(aibitat: AIbitat) => void' is not assignable to type '(aibitat: unknown) => void'.
        Types of parameters 'aibitat' and 'aibitat' are incompatible.
          Type 'unknown' is not assignable to type 'AIbitat'.
utils/agents/ephemeral.ts(180,47): error TS2554: Expected 0 arguments, but got 1.
utils/agents/ephemeral.ts(202,28): error TS2345: Argument of type '{ runtimeArgs: Record<string, unknown>; name: string; config: PluginConfig; setup: (aibitat: { function: (params: AgentFunction) => void; handlerProps?: { log?: ((...args: unknown[]) => void) | undefined; } | undefined; introspect?: ((...args: unknown[]) => void) | undefined; }) => void; }' is not assignable to parameter of type '{ setup: (aibitat: unknown) => void; } | { plugin: (options?: unknown) => { setup: (aibitat: unknown) => void; }; }'.
  Type '{ runtimeArgs: Record<string, unknown>; name: string; config: PluginConfig; setup: (aibitat: { function: (params: AgentFunction) => void; handlerProps?: { log?: ((...args: unknown[]) => void) | undefined; } | undefined; introspect?: ((...args: unknown[]) => void) | undefined; }) => void; }' is not assignable to type '{ setup: (aibitat: unknown) => void; }'.
    Types of property 'setup' are incompatible.
      Type '(aibitat: { function: (params: AgentFunction) => void; handlerProps?: { log?: ((...args: unknown[]) => void) | undefined; } | undefined; introspect?: ((...args: unknown[]) => void) | undefined; }) => void' is not assignable to type '(aibitat: unknown) => void'.
        Types of parameters 'aibitat' and 'aibitat' are incompatible.
          Type 'unknown' is not assignable to type '{ function: (params: AgentFunction) => void; handlerProps?: { log?: ((...args: unknown[]) => void) | undefined; } | undefined; introspect?: ((...args: unknown[]) => void) | undefined; }'.
utils/agents/ephemeral.ts(219,9): error TS2345: Argument of type 'Record<string, unknown> | Record<string, { required: boolean; default?: unknown; }> | undefined' is not assignable to parameter of type 'Record<string, { required?: boolean | undefined; default?: unknown; }> | undefined'.
  Type 'Record<string, unknown>' is not assignable to type 'Record<string, { required?: boolean | undefined; default?: unknown; }>'.
    'string' index signatures are incompatible.
      Type 'unknown' is not assignable to type '{ required?: boolean | undefined; default?: unknown; }'.
utils/agents/ephemeral.ts(223,41): error TS2349: This expression is not callable.
  Not all constituents of type '((args?: unknown) => { name: string; setup: (aibitat: AIbitat) => void; }) | { name: string; plugin: () => AgentPlugin; }[] | ((options?: unknown) => { [key: string]: unknown; name: string; setup: (aibitat: unknown) => void; })' are callable.
    Type '{ name: string; plugin: () => AgentPlugin; }[]' has no call signatures.
utils/agents/ephemeral.ts(257,7): error TS2322: Type '{ role: string; content: string; from?: string | undefined; to?: string | undefined; state?: string | undefined; timestamp?: Date | undefined; }[]' is not assignable to type 'ChatMessage[]'.
  Type '{ role: string; content: string; from?: string | undefined; to?: string | undefined; state?: string | undefined; timestamp?: Date | undefined; }' is not assignable to type 'ChatMessage'.
    Types of property 'from' are incompatible.
      Type 'string | undefined' is not assignable to type 'string'.
        Type 'undefined' is not assignable to type 'string'.
utils/agents/ephemeral.ts(269,7): error TS2345: Argument of type 'HttpSocketPlugin' is not assignable to parameter of type '{ setup: (aibitat: unknown) => void; } | { plugin: (options?: unknown) => { setup: (aibitat: unknown) => void; }; }'.
  Type 'HttpSocketPlugin' is not assignable to type '{ setup: (aibitat: unknown) => void; }'.
    Types of property 'setup' are incompatible.
      Type '(aibitat: AibitatInstance) => void' is not assignable to type '(aibitat: unknown) => void'.
        Types of parameters 'aibitat' and 'aibitat' are incompatible.
          Type 'unknown' is not assignable to type 'AibitatInstance'.
utils/agents/ephemeral.ts(270,9): error TS2322: Type 'unknown' is not assignable to type 'SocketHandler'.
utils/agents/imported.ts(117,28): error TS2339: Property 'active' does not exist on type 'never'.
utils/agents/imported.ts(117,45): error TS2339: Property 'hubId' does not exist on type 'never'.
utils/agents/imported.ts(118,34): error TS2339: Property 'hubId' does not exist on type 'never'.
utils/agents/imported.ts(169,7): error TS2698: Spread types may only be created from object types.
utils/agents/imported.ts(232,26): error TS2352: Conversion of type '{ super: { function: (params: _AgentFunction) => void; handlerProps?: { log?: (...args: unknown[]) => void; }; introspect?: (...args: unknown[]) => void; }; name: string; config: PluginConfig; ... 5 more ...; parameters: { ...; }; }' to type 'AgentFunction & { super: { function: (params: AgentFunction) => void; handlerProps?: { log?: ((...args: unknown[]) => void) | undefined; } | undefined; introspect?: ((...args: unknown[]) => void) | undefined; }; ... 4 more ...; examples: unknown[]; }' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'handler' is missing in type '{ super: { function: (params: _AgentFunction) => void; handlerProps?: { log?: (...args: unknown[]) => void; }; introspect?: (...args: unknown[]) => void; }; name: string; config: PluginConfig; ... 5 more ...; parameters: { ...; }; }' but required in type 'AgentFunction'.
utils/agents/index.ts(62,26): error TS2531: Object is possibly 'null'.
utils/agents/index.ts(63,22): error TS2531: Object is possibly 'null'.
utils/agents/index.ts(64,24): error TS2531: Object is possibly 'null'.
utils/agents/index.ts(384,11): error TS2345: Argument of type 'Record<string, unknown> | undefined' is not assignable to parameter of type 'Record<string, { required?: boolean | undefined; default?: unknown; }> | undefined'.
  Type 'Record<string, unknown>' is not assignable to type 'Record<string, { required?: boolean | undefined; default?: unknown; }>'.
    'string' index signatures are incompatible.
      Type 'unknown' is not assignable to type '{ required?: boolean | undefined; default?: unknown; }'.
utils/agents/index.ts(389,55): error TS2339: Property 'setup' does not exist on type '{}'.
utils/agents/index.ts(390,30): error TS2345: Argument of type '{}' is not assignable to parameter of type '{ setup: (aibitat: unknown) => void; } | { plugin: (options?: unknown) => { setup: (aibitat: unknown) => void; }; }'.
utils/agents/index.ts(410,13): error TS2345: Argument of type 'AIbitat' is not assignable to parameter of type 'Aibitat'.
  Property 'introspect' is missing in type 'AIbitat' but required in type 'Aibitat'.
utils/agents/index.ts(434,32): error TS2345: Argument of type '{ name: string; setup: (_aibitat: Aibitat) => void; }' is not assignable to parameter of type '{ setup: (aibitat: unknown) => void; } | { plugin: (options?: unknown) => { setup: (aibitat: unknown) => void; }; }'.
  Type '{ name: string; setup: (_aibitat: Aibitat) => void; }' is not assignable to type '{ setup: (aibitat: unknown) => void; }'.
    Types of property 'setup' are incompatible.
      Type '(_aibitat: Aibitat) => void' is not assignable to type '(aibitat: unknown) => void'.
        Types of parameters '_aibitat' and 'aibitat' are incompatible.
          Type 'unknown' is not assignable to type 'Aibitat'.
utils/agents/index.ts(460,30): error TS2345: Argument of type '{ runtimeArgs: Record<string, unknown>; name: string; config: PluginConfig; setup: (aibitat: { function: (params: AgentFunction) => void; handlerProps?: { log?: ((...args: unknown[]) => void) | undefined; } | undefined; introspect?: ((...args: unknown[]) => void) | undefined; }) => void; }' is not assignable to parameter of type '{ setup: (aibitat: unknown) => void; } | { plugin: (options?: unknown) => { setup: (aibitat: unknown) => void; }; }'.
  Type '{ runtimeArgs: Record<string, unknown>; name: string; config: PluginConfig; setup: (aibitat: { function: (params: AgentFunction) => void; handlerProps?: { log?: ((...args: unknown[]) => void) | undefined; } | undefined; introspect?: ((...args: unknown[]) => void) | undefined; }) => void; }' is not assignable to type '{ setup: (aibitat: unknown) => void; }'.
    Types of property 'setup' are incompatible.
      Type '(aibitat: { function: (params: AgentFunction) => void; handlerProps?: { log?: ((...args: unknown[]) => void) | undefined; } | undefined; introspect?: ((...args: unknown[]) => void) | undefined; }) => void' is not assignable to type '(aibitat: unknown) => void'.
        Types of parameters 'aibitat' and 'aibitat' are incompatible.
          Type 'unknown' is not assignable to type '{ function: (params: AgentFunction) => void; handlerProps?: { log?: ((...args: unknown[]) => void) | undefined; } | undefined; introspect?: ((...args: unknown[]) => void) | undefined; }'.
utils/agents/index.ts(479,9): error TS2345: Argument of type 'Record<string, unknown> | Record<string, { required: boolean; default?: unknown; }> | undefined' is not assignable to parameter of type 'Record<string, { required?: boolean | undefined; default?: unknown; }> | undefined'.
  Type 'Record<string, unknown>' is not assignable to type 'Record<string, { required?: boolean | undefined; default?: unknown; }>'.
    'string' index signatures are incompatible.
      Type 'unknown' is not assignable to type '{ required?: boolean | undefined; default?: unknown; }'.
utils/AiProviders/anthropic/index.ts(226,24): error TS2694: Namespace '"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/@anthropic-ai/sdk/index".Anthropic' has no exported member 'Stream'.
utils/AiProviders/anthropic/index.ts(264,23): error TS2694: Namespace '"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/@anthropic-ai/sdk/index".Anthropic' has no exported member 'Stream'.
utils/AiProviders/anthropic/index.ts(301,11): error TS2322: Type 'string | undefined' is not assignable to type 'string'.
  Type 'undefined' is not assignable to type 'string'.
utils/AiProviders/anthropic/index.ts(344,17): error TS2339: Property 'stop_reason' does not exist on type 'RawMessageStreamEvent'.
  Property 'stop_reason' does not exist on type 'RawMessageStartEvent'.
utils/AiProviders/anthropic/index.ts(344,37): error TS2339: Property 'stop_reason' does not exist on type 'RawMessageStreamEvent'.
  Property 'stop_reason' does not exist on type 'RawMessageStartEvent'.
utils/AiProviders/anthropic/index.ts(378,5): error TS2322: Type '(input: ChatMessage | ChatMessage[]) => { tokens: number; strings: number; }' is not assignable to type '(input: TokenInput) => number'.
  Types of parameters 'input' and 'input' are incompatible.
    Type 'TokenInput' is not assignable to type 'ChatMessage | ChatMessage[]'.
      Type 'string' is not assignable to type 'ChatMessage | ChatMessage[]'.
utils/AiProviders/anthropic/index.ts(418,57): error TS2345: Argument of type 'string | (TextContent | ImageContent)[]' is not assignable to parameter of type 'string | undefined'.
  Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/anthropic/index.ts(424,53): error TS2345: Argument of type 'string | (TextContent | ImageContent)[]' is not assignable to parameter of type 'string | undefined'.
  Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/anthropic/index.ts(437,5): error TS2578: Unused '@ts-expect-error' directive.
utils/AiProviders/azureOpenAi/index.ts(179,7): error TS2322: Type 'string | null' is not assignable to type 'string'.
  Type 'null' is not assignable to type 'string'.
utils/AiProviders/azureOpenAi/index.ts(179,21): error TS2532: Object is possibly 'undefined'.
utils/AiProviders/azureOpenAi/index.ts(190,9): error TS2416: Property 'streamGetChatCompletion' in type 'AzureOpenAiLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(messages: ChatMessage[] | undefined, { temperature }: CompletionOptions) => Promise<AsyncIterable<ChatCompletions>>' is not assignable to type '(messages: ChatMessage[], options: CompletionOptions) => Promise<LLMStreamResponse>'.
    Type 'Promise<AsyncIterable<ChatCompletions>>' is not assignable to type 'Promise<LLMStreamResponse>'.
      Property 'stream' is missing in type 'AsyncIterable<ChatCompletions>' but required in type 'LLMStreamResponse'.
utils/AiProviders/azureOpenAi/index.ts(207,7): error TS2345: Argument of type 'EventStream<ChatCompletions>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Type 'EventStream<ChatCompletions>' is missing the following properties from type 'Promise<OpenAICompatibleStream>': then, catch, finally, [Symbol.toStringTag]
utils/AiProviders/azureOpenAi/index.ts(213,5): error TS2322: Type 'MonitoredStream' is not assignable to type 'AsyncIterable<ChatCompletions>'.
  The types returned by '[Symbol.asyncIterator]().next(...)' are incompatible between these types.
    Type 'Promise<IteratorResult<unknown, any>>' is not assignable to type 'Promise<IteratorResult<ChatCompletions, any>>'.
      Type 'IteratorResult<unknown, any>' is not assignable to type 'IteratorResult<ChatCompletions, any>'.
        Type 'IteratorYieldResult<unknown>' is not assignable to type 'IteratorResult<ChatCompletions, any>'.
          Type 'IteratorYieldResult<unknown>' is not assignable to type 'IteratorYieldResult<ChatCompletions>'.
            Type 'unknown' is not assignable to type 'ChatCompletions'.
utils/AiProviders/azureOpenAi/index.ts(240,17): error TS2339: Property 'endMeasurement' does not exist on type 'AsyncIterable<ChatCompletions>'.
utils/AiProviders/azureOpenAi/index.ts(271,15): error TS2339: Property 'endMeasurement' does not exist on type 'AsyncIterable<ChatCompletions>'.
utils/AiProviders/bedrock/index.ts(341,7): error TS2345: Argument of type 'AsyncIterable<string>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Type 'AsyncIterable<string>' is missing the following properties from type 'Promise<OpenAICompatibleStream>': then, catch, finally, [Symbol.toStringTag]
utils/AiProviders/bedrock/index.ts(362,3): error TS2416: Property 'handleStream' in type 'AWSBedrockLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(response: ReadableStream, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<string>' is not assignable to type '(response: Response<any, Record<string, any>>, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<...>'.
    Types of parameters 'response' and 'response' are incompatible.
      Type 'Response<any, Record<string, any>>' is missing the following properties from type 'ReadableStream': readable, read, setEncoding, pause, and 6 more.
utils/AiProviders/bedrock/index.ts(381,17): error TS2339: Property 'endMeasurement' does not exist on type 'AsyncIterable<unknown>'.
utils/AiProviders/bedrock/index.ts(418,17): error TS2339: Property 'endMeasurement' does not exist on type 'AsyncIterable<unknown>'.
utils/AiProviders/bedrock/index.ts(432,17): error TS2339: Property 'endMeasurement' does not exist on type 'AsyncIterable<unknown>'.
utils/AiProviders/cohere/index.ts(210,7): error TS2345: Argument of type 'Promise<Stream<StreamedChatResponse>>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Type 'Stream<StreamedChatResponse>' is not assignable to type 'OpenAICompatibleStream'.
    Index signature for type 'string' is missing in type 'Stream<StreamedChatResponse>'.
utils/AiProviders/cohere/index.ts(235,9): error TS2416: Property 'handleStream' in type 'CohereLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(response: ReadableStream, stream: AsyncIterable<unknown> & { endMeasurement: (usage: { prompt_tokens: number; completion_tokens: number; }) => void; }, responseProps: StreamResponseProps) => Promise<...>' is not assignable to type '(response: Response<any, Record<string, any>>, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<...>'.
    Types of parameters 'response' and 'response' are incompatible.
      Type 'Response<any, Record<string, any>>' is missing the following properties from type 'ReadableStream': readable, read, setEncoding, pause, and 6 more.
utils/AiProviders/deepseek/index.ts(173,7): error TS2345: Argument of type 'APIPromise<Stream<ChatCompletionChunk>>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Types of property 'then' are incompatible.
    Type '<TResult1 = Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }, TResult2 = never>(onfulfilled?: ((value: Stream<ChatCompletionChunk> & { ...; }) => TResult1 | PromiseLike<...>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Pro...' is not assignable to type '<TResult1 = OpenAICompatibleStream, TResult2 = never>(onfulfilled?: ((value: OpenAICompatibleStream) => TResult1 | PromiseLike<TResult1>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Promise<...>'.
      Types of parameters 'onfulfilled' and 'onfulfilled' are incompatible.
        Types of parameters 'value' and 'value' are incompatible.
          Type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }' is not assignable to type 'OpenAICompatibleStream'.
            Index signature for type 'string' is missing in type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }'.
utils/AiProviders/deepseek/index.ts(188,3): error TS2416: Property 'handleStream' in type 'DeepSeekLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(response: ReadableStream, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<string>' is not assignable to type '(response: Response<any, Record<string, any>>, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<...>'.
    Types of parameters 'response' and 'response' are incompatible.
      Type 'Response<any, Record<string, any>>' is missing the following properties from type 'ReadableStream': readable, read, setEncoding, pause, and 6 more.
utils/AiProviders/deepseek/index.ts(195,7): error TS2345: Argument of type 'AsyncIterable<unknown>' is not assignable to parameter of type 'MonitoredStream'.
  Property 'endMeasurement' is missing in type 'AsyncIterable<unknown>' but required in type 'MonitoredStream'.
utils/AiProviders/deepseek/index.ts(205,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.
  Type 'null' is not assignable to type 'number[][]'.
utils/AiProviders/fireworksAi/index.ts(166,7): error TS2345: Argument of type 'APIPromise<Stream<ChatCompletionChunk>>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Types of property 'then' are incompatible.
    Type '<TResult1 = Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }, TResult2 = never>(onfulfilled?: ((value: Stream<ChatCompletionChunk> & { ...; }) => TResult1 | PromiseLike<...>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Pro...' is not assignable to type '<TResult1 = OpenAICompatibleStream, TResult2 = never>(onfulfilled?: ((value: OpenAICompatibleStream) => TResult1 | PromiseLike<TResult1>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Promise<...>'.
      Types of parameters 'onfulfilled' and 'onfulfilled' are incompatible.
        Types of parameters 'value' and 'value' are incompatible.
          Type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }' is not assignable to type 'OpenAICompatibleStream'.
            Index signature for type 'string' is missing in type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }'.
utils/AiProviders/fireworksAi/index.ts(184,3): error TS2416: Property 'handleStream' in type 'FireworksAiLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(response: ReadableStream, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<string>' is not assignable to type '(response: Response<any, Record<string, any>>, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<...>'.
    Types of parameters 'response' and 'response' are incompatible.
      Type 'Response<any, Record<string, any>>' is missing the following properties from type 'ReadableStream': readable, read, setEncoding, pause, and 6 more.
utils/AiProviders/fireworksAi/index.ts(191,7): error TS2345: Argument of type 'AsyncIterable<unknown>' is not assignable to parameter of type 'MonitoredStream'.
  Property 'endMeasurement' is missing in type 'AsyncIterable<unknown>' but required in type 'MonitoredStream'.
utils/AiProviders/fireworksAi/index.ts(201,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.
  Type 'null' is not assignable to type 'number[][]'.
utils/AiProviders/gemini/index.ts(509,61): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/gemini/index.ts(511,9): error TS2322: Type 'string | { text: string; }' is not assignable to type 'string'.
  Type '{ text: string; }' is not assignable to type 'string'.
utils/AiProviders/gemini/index.ts(514,7): error TS2322: Type 'string | { text: string; }' is not assignable to type 'string'.
  Type '{ text: string; }' is not assignable to type 'string'.
utils/AiProviders/gemini/index.ts(541,9): error TS2345: Argument of type 'AsyncIterable<{ text: () => string; }>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Type 'AsyncIterable<{ text: () => string; }>' is missing the following properties from type 'Promise<OpenAICompatibleStream>': then, catch, finally, [Symbol.toStringTag]
utils/AiProviders/gemini/index.ts(567,9): error TS2322: Type '() => void' is not assignable to type '(usage: Partial<UsageMetrics>) => UsageMetrics'.
  Type 'void' is not assignable to type 'UsageMetrics'.
utils/AiProviders/gemini/index.ts(588,3): error TS2416: Property 'handleStream' in type 'GeminiLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(response: ReadableStream, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<string>' is not assignable to type '(response: Response<any, Record<string, any>>, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<...>'.
    Types of parameters 'response' and 'response' are incompatible.
      Type 'Response<any, Record<string, any>>' is missing the following properties from type 'ReadableStream': readable, read, setEncoding, pause, and 6 more.
utils/AiProviders/genericOpenAi/index.ts(244,7): error TS2345: Argument of type 'APIPromise<Stream<ChatCompletionChunk>>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Types of property 'then' are incompatible.
    Type '<TResult1 = Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }, TResult2 = never>(onfulfilled?: ((value: Stream<ChatCompletionChunk> & { ...; }) => TResult1 | PromiseLike<...>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Pro...' is not assignable to type '<TResult1 = OpenAICompatibleStream, TResult2 = never>(onfulfilled?: ((value: OpenAICompatibleStream) => TResult1 | PromiseLike<TResult1>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Promise<...>'.
      Types of parameters 'onfulfilled' and 'onfulfilled' are incompatible.
        Types of parameters 'value' and 'value' are incompatible.
          Type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }' is not assignable to type 'OpenAICompatibleStream'.
            Index signature for type 'string' is missing in type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }'.
utils/AiProviders/genericOpenAi/index.ts(261,3): error TS2416: Property 'handleStream' in type 'GenericOpenAiLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(response: ReadableStream, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<string>' is not assignable to type '(response: Response<any, Record<string, any>>, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<...>'.
    Types of parameters 'response' and 'response' are incompatible.
      Type 'Response<any, Record<string, any>>' is missing the following properties from type 'ReadableStream': readable, read, setEncoding, pause, and 6 more.
utils/AiProviders/genericOpenAi/index.ts(268,7): error TS2345: Argument of type 'AsyncIterable<unknown>' is not assignable to parameter of type 'MonitoredStream'.
  Property 'endMeasurement' is missing in type 'AsyncIterable<unknown>' but required in type 'MonitoredStream'.
utils/AiProviders/groq/index.ts(250,7): error TS2322: Type 'string | null' is not assignable to type 'string'.
  Type 'null' is not assignable to type 'string'.
utils/AiProviders/groq/index.ts(257,27): error TS2551: Property 'completion_time' does not exist on type 'CompletionUsage'. Did you mean 'completion_tokens'?
utils/AiProviders/groq/index.ts(258,34): error TS2339: Property 'total_time' does not exist on type 'CompletionUsage'.
utils/AiProviders/groq/index.ts(263,9): error TS2416: Property 'streamGetChatCompletion' in type 'GroqLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(messages: ChatMessage[] | undefined, { temperature }: CompletionOptions) => Promise<Stream<ChatCompletionChunk>>' is not assignable to type '(messages: ChatMessage[], options: CompletionOptions) => Promise<LLMStreamResponse>'.
    Type 'Promise<Stream<ChatCompletionChunk>>' is not assignable to type 'Promise<LLMStreamResponse>'.
      Property 'stream' is missing in type 'Stream<ChatCompletionChunk>' but required in type 'LLMStreamResponse'.
utils/AiProviders/groq/index.ts(282,7): error TS2345: Argument of type 'APIPromise<Stream<ChatCompletionChunk>>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Types of property 'then' are incompatible.
    Type '<TResult1 = Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }, TResult2 = never>(onfulfilled?: ((value: Stream<ChatCompletionChunk> & { ...; }) => TResult1 | PromiseLike<...>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Pro...' is not assignable to type '<TResult1 = OpenAICompatibleStream, TResult2 = never>(onfulfilled?: ((value: OpenAICompatibleStream) => TResult1 | PromiseLike<TResult1>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Promise<...>'.
      Types of parameters 'onfulfilled' and 'onfulfilled' are incompatible.
        Types of parameters 'value' and 'value' are incompatible.
          Type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }' is not assignable to type 'OpenAICompatibleStream'.
            Index signature for type 'string' is missing in type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }'.
utils/AiProviders/groq/index.ts(292,5): error TS2739: Type 'MonitoredStream' is missing the following properties from type 'Stream<ChatCompletionChunk>': iterator, controller, tee, toReadableStream
utils/AiProviders/groq/index.ts(300,52): error TS2345: Argument of type 'Stream<ChatCompletionChunk>' is not assignable to parameter of type 'MonitoredStream'.
  Property 'endMeasurement' is missing in type 'Stream<ChatCompletionChunk>' but required in type 'MonitoredStream'.
utils/AiProviders/huggingface/index.ts(122,33): error TS2503: Cannot find namespace 'OpenAI'.
utils/AiProviders/huggingface/index.ts(149,9): error TS2416: Property 'streamGetChatCompletion' in type 'HuggingFaceLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(messages: ChatMessage[] | null, { temperature }: CompletionOptions) => Promise<AsyncIterable<unknown>>' is not assignable to type '(messages: ChatMessage[], options: CompletionOptions) => Promise<LLMStreamResponse>'.
    Type 'Promise<AsyncIterable<unknown>>' is not assignable to type 'Promise<LLMStreamResponse>'.
      Property 'stream' is missing in type 'AsyncIterable<unknown>' but required in type 'LLMStreamResponse'.
utils/AiProviders/huggingface/index.ts(156,7): error TS2345: Argument of type 'APIPromise<ChatCompletion> & APIPromise<Stream<ChatCompletionChunk>> & APIPromise<ChatCompletion | Stream<...>>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Types of property 'then' are incompatible.
    Type '(<TResult1 = ChatCompletion & { _request_id?: string | null | undefined; }, TResult2 = never>(onfulfilled?: ((value: ChatCompletion & { _request_id?: string | null | undefined; }) => TResult1 | PromiseLike<...>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefi...' is not assignable to type '<TResult1 = OpenAICompatibleStream, TResult2 = never>(onfulfilled?: ((value: OpenAICompatibleStream) => TResult1 | PromiseLike<TResult1>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Promise<...>'.
      Types of parameters 'onfulfilled' and 'onfulfilled' are incompatible.
        Types of parameters 'value' and 'value' are incompatible.
          Property '[Symbol.asyncIterator]' is missing in type 'ChatCompletion & { _request_id?: string | null | undefined; }' but required in type 'OpenAICompatibleStream'.
utils/AiProviders/huggingface/index.ts(159,9): error TS2769: No overload matches this call.
  Overload 3 of 3, '(body: ChatCompletionCreateParamsBase, options?: RequestOptions<unknown> | undefined): APIPromise<ChatCompletion | Stream<...>>', gave the following error.
    Type '{ role: string; content: string; }[]' is not assignable to type 'ChatCompletionMessageParam[]'.
      Type '{ role: string; content: string; }' is not assignable to type 'ChatCompletionMessageParam'.
        Property 'name' is missing in type '{ role: string; content: string; }' but required in type 'ChatCompletionFunctionMessageParam'.
utils/AiProviders/huggingface/index.ts(167,3): error TS2416: Property 'handleStream' in type 'HuggingFaceLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(response: ReadableStream, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<string>' is not assignable to type '(response: Response<any, Record<string, any>>, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<...>'.
    Types of parameters 'response' and 'response' are incompatible.
      Type 'Response<any, Record<string, any>>' is missing the following properties from type 'ReadableStream': readable, read, setEncoding, pause, and 6 more.
utils/AiProviders/huggingface/index.ts(172,42): error TS2345: Argument of type 'ReadableStream' is not assignable to parameter of type 'Response<any, Record<string, any>>'.
  Type 'ReadableStream' is missing the following properties from type 'Response<any, Record<string, any>>': status, sendStatus, links, send, and 72 more.
utils/AiProviders/koboldCPP/index.ts(127,9): error TS2769: No overload matches this call.
  Overload 1 of 3, '(body: ChatCompletionCreateParamsNonStreaming, options?: RequestOptions<unknown> | undefined): APIPromise<ChatCompletion>', gave the following error.
    Type 'OpenAICompatibleMessage[]' is not assignable to type 'ChatCompletionMessageParam[]'.
      Type 'OpenAICompatibleMessage' is not assignable to type 'ChatCompletionMessageParam'.
        Type 'OpenAICompatibleMessage' is not assignable to type 'ChatCompletionSystemMessageParam | ChatCompletionUserMessageParam | ChatCompletionAssistantMessageParam | ChatCompletionFunctionMessageParam'.
          Type 'OpenAICompatibleMessage' is not assignable to type 'ChatCompletionAssistantMessageParam'.
            Types of property 'role' are incompatible.
              Type '"function" | "system" | "user" | "assistant"' is not assignable to type '"assistant"'.
                Type '"function"' is not assignable to type '"assistant"'.
  Overload 2 of 3, '(body: ChatCompletionCreateParamsStreaming, options?: RequestOptions<unknown> | undefined): APIPromise<Stream<ChatCompletionChunk>>', gave the following error.
    Type 'OpenAICompatibleMessage[]' is not assignable to type 'ChatCompletionMessageParam[]'.
      Type 'OpenAICompatibleMessage' is not assignable to type 'ChatCompletionMessageParam'.
        Type 'OpenAICompatibleMessage' is not assignable to type 'ChatCompletionSystemMessageParam | ChatCompletionUserMessageParam | ChatCompletionAssistantMessageParam | ChatCompletionFunctionMessageParam'.
          Type 'OpenAICompatibleMessage' is not assignable to type 'ChatCompletionAssistantMessageParam'.
            Types of property 'role' are incompatible.
              Type '"function" | "system" | "user" | "assistant"' is not assignable to type '"assistant"'.
                Type '"function"' is not assignable to type '"assistant"'.
  Overload 3 of 3, '(body: ChatCompletionCreateParamsBase, options?: RequestOptions<unknown> | undefined): APIPromise<ChatCompletion | Stream<...>>', gave the following error.
    Type 'OpenAICompatibleMessage[]' is not assignable to type 'ChatCompletionMessageParam[]'.
      Type 'OpenAICompatibleMessage' is not assignable to type 'ChatCompletionMessageParam'.
        Type 'OpenAICompatibleMessage' is not assignable to type 'ChatCompletionSystemMessageParam | ChatCompletionUserMessageParam | ChatCompletionAssistantMessageParam | ChatCompletionFunctionMessageParam'.
          Type 'OpenAICompatibleMessage' is not assignable to type 'ChatCompletionAssistantMessageParam'.
            Types of property 'role' are incompatible.
              Type '"function" | "system" | "user" | "assistant"' is not assignable to type '"assistant"'.
                Type '"function"' is not assignable to type '"assistant"'.
utils/AiProviders/koboldCPP/index.ts(155,7): error TS2345: Argument of type 'APIPromise<ChatCompletion> & APIPromise<Stream<ChatCompletionChunk>> & APIPromise<ChatCompletion | Stream<...>>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Types of property 'then' are incompatible.
    Type '(<TResult1 = ChatCompletion & { _request_id?: string | null | undefined; }, TResult2 = never>(onfulfilled?: ((value: ChatCompletion & { _request_id?: string | null | undefined; }) => TResult1 | PromiseLike<...>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefi...' is not assignable to type '<TResult1 = OpenAICompatibleStream, TResult2 = never>(onfulfilled?: ((value: OpenAICompatibleStream) => TResult1 | PromiseLike<TResult1>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Promise<...>'.
      Types of parameters 'onfulfilled' and 'onfulfilled' are incompatible.
        Types of parameters 'value' and 'value' are incompatible.
          Property '[Symbol.asyncIterator]' is missing in type 'ChatCompletion & { _request_id?: string | null | undefined; }' but required in type 'OpenAICompatibleStream'.
utils/AiProviders/koboldCPP/index.ts(158,9): error TS2769: No overload matches this call.
  Overload 3 of 3, '(body: ChatCompletionCreateParamsBase, options?: RequestOptions<unknown> | undefined): APIPromise<ChatCompletion | Stream<...>>', gave the following error.
    Type 'OpenAICompatibleMessage[]' is not assignable to type 'ChatCompletionMessageParam[]'.
      Type 'OpenAICompatibleMessage' is not assignable to type 'ChatCompletionMessageParam'.
        Type 'OpenAICompatibleMessage' is not assignable to type 'ChatCompletionSystemMessageParam | ChatCompletionUserMessageParam | ChatCompletionAssistantMessageParam | ChatCompletionFunctionMessageParam'.
          Type 'OpenAICompatibleMessage' is not assignable to type 'ChatCompletionAssistantMessageParam'.
            Types of property 'role' are incompatible.
              Type '"function" | "system" | "user" | "assistant"' is not assignable to type '"assistant"'.
                Type '"function"' is not assignable to type '"assistant"'.
utils/AiProviders/koboldCPP/index.ts(163,5): error TS2741: Property 'stream' is missing in type 'MonitoredStream' but required in type 'LLMStreamResponse'.
utils/AiProviders/koboldCPP/index.ts(166,3): error TS2416: Property 'handleStream' in type 'KoboldCPPLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(response: ReadableStream, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<string>' is not assignable to type '(response: Response<any, Record<string, any>>, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<...>'.
    Types of parameters 'response' and 'response' are incompatible.
      Type 'Response<any, Record<string, any>>' is missing the following properties from type 'ReadableStream': readable, read, setEncoding, pause, and 6 more.
utils/AiProviders/koboldCPP/index.ts(178,14): error TS2339: Property 'on' does not exist on type 'AsyncIterable<unknown>'.
utils/AiProviders/koboldCPP/index.ts(187,32): error TS2345: Argument of type 'ReadableStream' is not assignable to parameter of type 'Response<any, Record<string, any>>'.
  Type 'ReadableStream' is missing the following properties from type 'Response<any, Record<string, any>>': status, sendStatus, links, send, and 72 more.
utils/AiProviders/koboldCPP/index.ts(203,36): error TS2345: Argument of type 'ReadableStream' is not assignable to parameter of type 'Response<any, Record<string, any>>'.
  Type 'ReadableStream' is missing the following properties from type 'Response<any, Record<string, any>>': status, sendStatus, links, send, and 72 more.
utils/AiProviders/koboldCPP/index.ts(219,14): error TS2339: Property 'on' does not exist on type 'AsyncIterable<unknown>'.
utils/AiProviders/koboldCPP/index.ts(221,28): error TS2345: Argument of type 'ReadableStream' is not assignable to parameter of type 'Response<any, Record<string, any>>'.
  Type 'ReadableStream' is missing the following properties from type 'Response<any, Record<string, any>>': status, sendStatus, links, send, and 72 more.
utils/AiProviders/liteLLM/index.ts(157,7): error TS2345: Argument of type 'APIPromise<Stream<ChatCompletionChunk>>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Types of property 'then' are incompatible.
    Type '<TResult1 = Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }, TResult2 = never>(onfulfilled?: ((value: Stream<ChatCompletionChunk> & { ...; }) => TResult1 | PromiseLike<...>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Pro...' is not assignable to type '<TResult1 = OpenAICompatibleStream, TResult2 = never>(onfulfilled?: ((value: OpenAICompatibleStream) => TResult1 | PromiseLike<TResult1>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Promise<...>'.
      Types of parameters 'onfulfilled' and 'onfulfilled' are incompatible.
        Types of parameters 'value' and 'value' are incompatible.
          Type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }' is not assignable to type 'OpenAICompatibleStream'.
            Index signature for type 'string' is missing in type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }'.
utils/AiProviders/liteLLM/index.ts(166,5): error TS2741: Property 'stream' is missing in type 'MonitoredStream' but required in type 'LLMStreamResponse'.
utils/AiProviders/liteLLM/index.ts(169,3): error TS2416: Property 'handleStream' in type 'LiteLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(response: ReadableStream, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<string>' is not assignable to type '(response: Response<any, Record<string, any>>, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<...>'.
    Types of parameters 'response' and 'response' are incompatible.
      Type 'Response<any, Record<string, any>>' is missing the following properties from type 'ReadableStream': readable, read, setEncoding, pause, and 6 more.
utils/AiProviders/liteLLM/index.ts(174,42): error TS2345: Argument of type 'ReadableStream' is not assignable to parameter of type 'Response<any, Record<string, any>>'.
  Type 'ReadableStream' is missing the following properties from type 'Response<any, Record<string, any>>': status, sendStatus, links, send, and 72 more.
utils/AiProviders/lmStudio/index.ts(141,7): error TS2345: Argument of type 'APIPromise<Stream<ChatCompletionChunk>>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Types of property 'then' are incompatible.
    Type '<TResult1 = Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }, TResult2 = never>(onfulfilled?: ((value: Stream<ChatCompletionChunk> & { ...; }) => TResult1 | PromiseLike<...>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Pro...' is not assignable to type '<TResult1 = OpenAICompatibleStream, TResult2 = never>(onfulfilled?: ((value: OpenAICompatibleStream) => TResult1 | PromiseLike<TResult1>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Promise<...>'.
      Types of parameters 'onfulfilled' and 'onfulfilled' are incompatible.
        Types of parameters 'value' and 'value' are incompatible.
          Type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }' is not assignable to type 'OpenAICompatibleStream'.
            Index signature for type 'string' is missing in type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }'.
utils/AiProviders/lmStudio/index.ts(149,5): error TS2741: Property 'stream' is missing in type 'MonitoredStream' but required in type 'LLMStreamResponse'.
utils/AiProviders/lmStudio/index.ts(152,3): error TS2416: Property 'handleStream' in type 'LMStudioLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(response: ReadableStream, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<string>' is not assignable to type '(response: Response<any, Record<string, any>>, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<...>'.
    Types of parameters 'response' and 'response' are incompatible.
      Type 'Response<any, Record<string, any>>' is missing the following properties from type 'ReadableStream': readable, read, setEncoding, pause, and 6 more.
utils/AiProviders/lmStudio/index.ts(157,42): error TS2345: Argument of type 'ReadableStream' is not assignable to parameter of type 'Response<any, Record<string, any>>'.
  Type 'ReadableStream' is missing the following properties from type 'Response<any, Record<string, any>>': status, sendStatus, links, send, and 72 more.
utils/AiProviders/lmStudio/index.ts(169,9): error TS2416: Property 'compressMessages' in type 'LMStudioLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(promptArgs?: PromptArgs, rawHistory?: ChatMessage[]) => Promise<LLMStreamResponse>' is not assignable to type '(promptArgs: PromptArgs, rawHistory: ChatMessage[]) => Promise<ChatMessage[]>'.
    Type 'Promise<LLMStreamResponse>' is not assignable to type 'Promise<ChatMessage[]>'.
      Type 'LLMStreamResponse' is missing the following properties from type 'ChatMessage[]': length, pop, push, concat, and 29 more.
utils/AiProviders/localAi/index.ts(143,7): error TS2345: Argument of type 'APIPromise<Stream<ChatCompletionChunk>>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Types of property 'then' are incompatible.
    Type '<TResult1 = Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }, TResult2 = never>(onfulfilled?: ((value: Stream<ChatCompletionChunk> & { ...; }) => TResult1 | PromiseLike<...>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Pro...' is not assignable to type '<TResult1 = OpenAICompatibleStream, TResult2 = never>(onfulfilled?: ((value: OpenAICompatibleStream) => TResult1 | PromiseLike<TResult1>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Promise<...>'.
      Types of parameters 'onfulfilled' and 'onfulfilled' are incompatible.
        Types of parameters 'value' and 'value' are incompatible.
          Type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }' is not assignable to type 'OpenAICompatibleStream'.
            Index signature for type 'string' is missing in type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }'.
utils/AiProviders/localAi/index.ts(151,5): error TS2741: Property 'stream' is missing in type 'MonitoredStream' but required in type 'LLMStreamResponse'.
utils/AiProviders/localAi/index.ts(154,3): error TS2416: Property 'handleStream' in type 'LocalAiLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(response: ReadableStream, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<string>' is not assignable to type '(response: Response<any, Record<string, any>>, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<...>'.
    Types of parameters 'response' and 'response' are incompatible.
      Type 'Response<any, Record<string, any>>' is missing the following properties from type 'ReadableStream': readable, read, setEncoding, pause, and 6 more.
utils/AiProviders/localAi/index.ts(159,42): error TS2345: Argument of type 'ReadableStream' is not assignable to parameter of type 'Response<any, Record<string, any>>'.
  Type 'ReadableStream' is missing the following properties from type 'Response<any, Record<string, any>>': status, sendStatus, links, send, and 72 more.
utils/AiProviders/localAi/index.ts(171,9): error TS2416: Property 'compressMessages' in type 'LocalAiLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(promptArgs?: PromptArgs, rawHistory?: ChatMessage[]) => Promise<LLMStreamResponse>' is not assignable to type '(promptArgs: PromptArgs, rawHistory: ChatMessage[]) => Promise<ChatMessage[]>'.
    Type 'Promise<LLMStreamResponse>' is not assignable to type 'Promise<ChatMessage[]>'.
      Type 'LLMStreamResponse' is missing the following properties from type 'ChatMessage[]': length, pop, push, concat, and 29 more.
utils/AiProviders/mistral/index.ts(157,7): error TS2345: Argument of type 'APIPromise<Stream<ChatCompletionChunk>>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Types of property 'then' are incompatible.
    Type '<TResult1 = Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }, TResult2 = never>(onfulfilled?: ((value: Stream<ChatCompletionChunk> & { ...; }) => TResult1 | PromiseLike<...>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Pro...' is not assignable to type '<TResult1 = OpenAICompatibleStream, TResult2 = never>(onfulfilled?: ((value: OpenAICompatibleStream) => TResult1 | PromiseLike<TResult1>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Promise<...>'.
      Types of parameters 'onfulfilled' and 'onfulfilled' are incompatible.
        Types of parameters 'value' and 'value' are incompatible.
          Type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }' is not assignable to type 'OpenAICompatibleStream'.
            Index signature for type 'string' is missing in type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }'.
utils/AiProviders/mistral/index.ts(165,5): error TS2741: Property 'stream' is missing in type 'MonitoredStream' but required in type 'LLMStreamResponse'.
utils/AiProviders/mistral/index.ts(168,3): error TS2416: Property 'handleStream' in type 'MistralLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(response: ReadableStream, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<string>' is not assignable to type '(response: Response<any, Record<string, any>>, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<...>'.
    Types of parameters 'response' and 'response' are incompatible.
      Type 'Response<any, Record<string, any>>' is missing the following properties from type 'ReadableStream': readable, read, setEncoding, pause, and 6 more.
utils/AiProviders/mistral/index.ts(173,42): error TS2345: Argument of type 'ReadableStream' is not assignable to parameter of type 'Response<any, Record<string, any>>'.
  Type 'ReadableStream' is missing the following properties from type 'Response<any, Record<string, any>>': status, sendStatus, links, send, and 72 more.
utils/AiProviders/native/index.ts(41,12): error TS2352: Conversion of type 'typeof ChatLlamaCpp' to type 'new (config: LlamaCppConfig) => LlamaCppModel' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Types of construct signatures are incompatible.
    Type 'new (inputs: LlamaCppInputs) => ChatLlamaCpp' is not assignable to type 'new (config: LlamaCppConfig) => LlamaCppModel'.
      Construct signature return types 'ChatLlamaCpp' and 'LlamaCppModel' are incompatible.
        The types of 'call' are incompatible between these types.
          Type '(messages: BaseMessageLike[], options?: string[] | LlamaCppCallOptions | undefined, callbacks?: Callbacks | undefined) => Promise<...>' is not comparable to type '(messages: ChatMessage[]) => Promise<{ content: string; }>'.
            Types of parameters 'messages' and 'messages' are incompatible.
              Type 'ChatMessage[]' is not comparable to type 'BaseMessageLike[]'.
                Type 'ChatMessage' is not comparable to type 'BaseMessageLike'.
                  Type 'ChatMessage' is not comparable to type '{ type: "user" | "assistant" | "placeholder" | MessageType; } & BaseMessageFields & Record<string, unknown>'.
                    Property 'type' is missing in type 'ChatMessage' but required in type '{ type: "user" | "assistant" | "placeholder" | MessageType; }'.
utils/AiProviders/native/index.ts(109,5): error TS2322: Type 'undefined' is not assignable to type 'LlamaCppModel'.
utils/AiProviders/native/index.ts(176,7): error TS2345: Argument of type 'string' is not assignable to parameter of type 'Message[]'.
utils/AiProviders/native/index.ts(178,64): error TS2345: Argument of type 'string' is not assignable to parameter of type 'Message[]'.
utils/AiProviders/native/index.ts(191,9): error TS2416: Property 'streamGetChatCompletion' in type 'NativeLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(messages: ChatMessage[] | null, { temperature }: CompletionOptions) => Promise<MonitoredStream>' is not assignable to type '(messages: ChatMessage[], options: CompletionOptions) => Promise<LLMStreamResponse>'.
    Type 'Promise<MonitoredStream>' is not assignable to type 'Promise<LLMStreamResponse>'.
      Property 'stream' is missing in type 'MonitoredStream' but required in type 'LLMStreamResponse'.
utils/AiProviders/native/index.ts(199,7): error TS2345: Argument of type 'AsyncIterable<{ content: string; }>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Type 'AsyncIterable<{ content: string; }>' is missing the following properties from type 'Promise<OpenAICompatibleStream>': then, catch, finally, [Symbol.toStringTag]
utils/AiProviders/native/index.ts(218,64): error TS2345: Argument of type 'string' is not assignable to parameter of type 'Message[]'.
utils/AiProviders/native/index.ts(230,39): error TS2345: Argument of type '{} | null' is not assignable to parameter of type 'object'.
  Type 'null' is not assignable to type 'object'.
utils/AiProviders/native/index.ts(254,62): error TS2345: Argument of type 'string' is not assignable to parameter of type 'Message[]'.
utils/AiProviders/ollama/index.ts(325,56): error TS2339: Property 'then' does not exist on type 'object'.
utils/AiProviders/ollama/index.ts(335,10): error TS2339: Property 'code' does not exist on type '{}'.
utils/AiProviders/ollama/index.ts(336,11): error TS2339: Property 'code' does not exist on type '{}'.
utils/AiProviders/ollama/index.ts(337,12): error TS2339: Property 'message' does not exist on type '{}'.
utils/AiProviders/ollama/index.ts(338,12): error TS2339: Property 'message' does not exist on type '{}'.
utils/AiProviders/ollama/index.ts(348,46): error TS2339: Property 'message' does not exist on type 'object'.
utils/AiProviders/ollama/index.ts(349,13): error TS2339: Property 'message' does not exist on type 'object'.
utils/AiProviders/ollama/index.ts(447,9): error TS2416: Property 'streamGetChatCompletion' in type 'OllamaAILLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(messages: ChatMessage[] | null, { temperature }: CompletionOptions) => Promise<MonitoredStream>' is not assignable to type '(messages: ChatMessage[], options: CompletionOptions) => Promise<LLMStreamResponse>'.
    Type 'Promise<MonitoredStream>' is not assignable to type 'Promise<LLMStreamResponse>'.
      Property 'stream' is missing in type 'MonitoredStream' but required in type 'LLMStreamResponse'.
utils/AiProviders/ollama/index.ts(495,15): error TS2352: Conversion of type 'AbortableAsyncIterator<ChatResponse>' to type 'Promise<{ [Symbol.asyncIterator](): AsyncIterator<unknown, any, any>; }>' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'AbortableAsyncIterator<ChatResponse>' is missing the following properties from type 'Promise<{ [Symbol.asyncIterator](): AsyncIterator<unknown, any, any>; }>': then, catch, finally, [Symbol.toStringTag]
utils/AiProviders/ollama/index.ts(508,11): error TS2345: Argument of type 'MonitoredStream' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Type 'MonitoredStream' is missing the following properties from type 'Promise<OpenAICompatibleStream>': then, catch, finally, [Symbol.toStringTag]
utils/AiProviders/ollama/index.ts(518,16): error TS2339: Property 'code' does not exist on type '{}'.
utils/AiProviders/ollama/index.ts(519,17): error TS2339: Property 'code' does not exist on type '{}'.
utils/AiProviders/ollama/index.ts(520,18): error TS2339: Property 'message' does not exist on type '{}'.
utils/AiProviders/ollama/index.ts(521,18): error TS2339: Property 'message' does not exist on type '{}'.
utils/AiProviders/ollama/index.ts(547,68): error TS2694: Namespace '"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/helpers/chat/LLMPerformanceMonitor"' has no exported member 'MeasuredStream'.
utils/AiProviders/openAi/index.ts(263,7): error TS2345: Argument of type 'APIPromise<Stream<ChatCompletionChunk>>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Types of property 'then' are incompatible.
    Type '<TResult1 = Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }, TResult2 = never>(onfulfilled?: ((value: Stream<ChatCompletionChunk> & { ...; }) => TResult1 | PromiseLike<...>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Pro...' is not assignable to type '<TResult1 = OpenAICompatibleStream, TResult2 = never>(onfulfilled?: ((value: OpenAICompatibleStream) => TResult1 | PromiseLike<TResult1>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Promise<...>'.
      Types of parameters 'onfulfilled' and 'onfulfilled' are incompatible.
        Types of parameters 'value' and 'value' are incompatible.
          Type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }' is not assignable to type 'OpenAICompatibleStream'.
            Index signature for type 'string' is missing in type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }'.
utils/AiProviders/openAi/index.ts(277,5): error TS2741: Property 'stream' is missing in type 'MonitoredStream' but required in type 'LLMStreamResponse'.
utils/AiProviders/openAi/index.ts(280,3): error TS2416: Property 'handleStream' in type 'OpenAiLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(response: ReadableStream, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<string>' is not assignable to type '(response: Response<any, Record<string, any>>, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<...>'.
    Types of parameters 'response' and 'response' are incompatible.
      Type 'Response<any, Record<string, any>>' is missing the following properties from type 'ReadableStream': readable, read, setEncoding, pause, and 6 more.
utils/AiProviders/openAi/index.ts(285,42): error TS2345: Argument of type 'ReadableStream' is not assignable to parameter of type 'Response<any, Record<string, any>>'.
  Type 'ReadableStream' is missing the following properties from type 'Response<any, Record<string, any>>': status, sendStatus, links, send, and 72 more.
utils/AiProviders/openRouter/index.ts(151,7): error TS2345: Argument of type 'APIPromise<Stream<ChatCompletionChunk>>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Types of property 'then' are incompatible.
    Type '<TResult1 = Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }, TResult2 = never>(onfulfilled?: ((value: Stream<ChatCompletionChunk> & { ...; }) => TResult1 | PromiseLike<...>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Pro...' is not assignable to type '<TResult1 = OpenAICompatibleStream, TResult2 = never>(onfulfilled?: ((value: OpenAICompatibleStream) => TResult1 | PromiseLike<TResult1>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Promise<...>'.
      Types of parameters 'onfulfilled' and 'onfulfilled' are incompatible.
        Types of parameters 'value' and 'value' are incompatible.
          Type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }' is not assignable to type 'OpenAICompatibleStream'.
            Index signature for type 'string' is missing in type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }'.
utils/AiProviders/openRouter/index.ts(159,5): error TS2741: Property 'stream' is missing in type 'MonitoredStream' but required in type 'LLMStreamResponse'.
utils/AiProviders/openRouter/index.ts(162,3): error TS2416: Property 'handleStream' in type 'OpenRouterLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(response: ReadableStream, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<string>' is not assignable to type '(response: Response<any, Record<string, any>>, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<...>'.
    Types of parameters 'response' and 'response' are incompatible.
      Type 'Response<any, Record<string, any>>' is missing the following properties from type 'ReadableStream': readable, read, setEncoding, pause, and 6 more.
utils/AiProviders/openRouter/index.ts(167,42): error TS2345: Argument of type 'ReadableStream' is not assignable to parameter of type 'Response<any, Record<string, any>>'.
  Type 'ReadableStream' is missing the following properties from type 'Response<any, Record<string, any>>': status, sendStatus, links, send, and 72 more.
utils/AiProviders/openRouter/index.ts(179,9): error TS2416: Property 'compressMessages' in type 'OpenRouterLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(promptArgs?: PromptArgs, rawHistory?: ChatMessage[]) => Promise<LLMStreamResponse>' is not assignable to type '(promptArgs: PromptArgs, rawHistory: ChatMessage[]) => Promise<ChatMessage[]>'.
    Type 'Promise<LLMStreamResponse>' is not assignable to type 'Promise<ChatMessage[]>'.
      Type 'LLMStreamResponse' is missing the following properties from type 'ChatMessage[]': length, pop, push, concat, and 29 more.
utils/AiProviders/perplexity/index.ts(158,7): error TS2345: Argument of type 'APIPromise<Stream<ChatCompletionChunk>>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Types of property 'then' are incompatible.
    Type '<TResult1 = Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }, TResult2 = never>(onfulfilled?: ((value: Stream<ChatCompletionChunk> & { ...; }) => TResult1 | PromiseLike<...>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Pro...' is not assignable to type '<TResult1 = OpenAICompatibleStream, TResult2 = never>(onfulfilled?: ((value: OpenAICompatibleStream) => TResult1 | PromiseLike<TResult1>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Promise<...>'.
      Types of parameters 'onfulfilled' and 'onfulfilled' are incompatible.
        Types of parameters 'value' and 'value' are incompatible.
          Type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }' is not assignable to type 'OpenAICompatibleStream'.
            Index signature for type 'string' is missing in type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }'.
utils/AiProviders/perplexity/index.ts(166,5): error TS2741: Property 'stream' is missing in type 'MonitoredStream' but required in type 'LLMStreamResponse'.
utils/AiProviders/perplexity/index.ts(169,3): error TS2416: Property 'handleStream' in type 'PerplexityLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(response: ReadableStream, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<string>' is not assignable to type '(response: Response<any, Record<string, any>>, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<...>'.
    Types of parameters 'response' and 'response' are incompatible.
      Type 'Response<any, Record<string, any>>' is missing the following properties from type 'ReadableStream': readable, read, setEncoding, pause, and 6 more.
utils/AiProviders/perplexity/index.ts(174,42): error TS2345: Argument of type 'ReadableStream' is not assignable to parameter of type 'Response<any, Record<string, any>>'.
  Type 'ReadableStream' is missing the following properties from type 'Response<any, Record<string, any>>': status, sendStatus, links, send, and 72 more.
utils/AiProviders/textGenWebUI/index.ts(144,7): error TS2345: Argument of type 'APIPromise<Stream<ChatCompletionChunk>>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Types of property 'then' are incompatible.
    Type '<TResult1 = Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }, TResult2 = never>(onfulfilled?: ((value: Stream<ChatCompletionChunk> & { ...; }) => TResult1 | PromiseLike<...>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Pro...' is not assignable to type '<TResult1 = OpenAICompatibleStream, TResult2 = never>(onfulfilled?: ((value: OpenAICompatibleStream) => TResult1 | PromiseLike<TResult1>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Promise<...>'.
      Types of parameters 'onfulfilled' and 'onfulfilled' are incompatible.
        Types of parameters 'value' and 'value' are incompatible.
          Type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }' is not assignable to type 'OpenAICompatibleStream'.
            Index signature for type 'string' is missing in type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }'.
utils/AiProviders/textGenWebUI/index.ts(152,5): error TS2741: Property 'stream' is missing in type 'MonitoredStream' but required in type 'LLMStreamResponse'.
utils/AiProviders/textGenWebUI/index.ts(155,3): error TS2416: Property 'handleStream' in type 'TextGenWebUILLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(response: ReadableStream, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<string>' is not assignable to type '(response: Response<any, Record<string, any>>, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<...>'.
    Types of parameters 'response' and 'response' are incompatible.
      Type 'Response<any, Record<string, any>>' is missing the following properties from type 'ReadableStream': readable, read, setEncoding, pause, and 6 more.
utils/AiProviders/textGenWebUI/index.ts(160,42): error TS2345: Argument of type 'ReadableStream' is not assignable to parameter of type 'Response<any, Record<string, any>>'.
  Type 'ReadableStream' is missing the following properties from type 'Response<any, Record<string, any>>': status, sendStatus, links, send, and 72 more.
utils/AiProviders/togetherAi/index.ts(151,7): error TS2345: Argument of type 'APIPromise<Stream<ChatCompletionChunk>>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Types of property 'then' are incompatible.
    Type '<TResult1 = Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }, TResult2 = never>(onfulfilled?: ((value: Stream<ChatCompletionChunk> & { ...; }) => TResult1 | PromiseLike<...>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Pro...' is not assignable to type '<TResult1 = OpenAICompatibleStream, TResult2 = never>(onfulfilled?: ((value: OpenAICompatibleStream) => TResult1 | PromiseLike<TResult1>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Promise<...>'.
      Types of parameters 'onfulfilled' and 'onfulfilled' are incompatible.
        Types of parameters 'value' and 'value' are incompatible.
          Type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }' is not assignable to type 'OpenAICompatibleStream'.
            Index signature for type 'string' is missing in type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }'.
utils/AiProviders/togetherAi/index.ts(159,5): error TS2741: Property 'stream' is missing in type 'MonitoredStream' but required in type 'LLMStreamResponse'.
utils/AiProviders/togetherAi/index.ts(162,3): error TS2416: Property 'handleStream' in type 'TogetherAiLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(response: ReadableStream, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<string>' is not assignable to type '(response: Response<any, Record<string, any>>, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<...>'.
    Types of parameters 'response' and 'response' are incompatible.
      Type 'Response<any, Record<string, any>>' is missing the following properties from type 'ReadableStream': readable, read, setEncoding, pause, and 6 more.
utils/AiProviders/togetherAi/index.ts(167,42): error TS2345: Argument of type 'ReadableStream' is not assignable to parameter of type 'Response<any, Record<string, any>>'.
  Type 'ReadableStream' is missing the following properties from type 'Response<any, Record<string, any>>': status, sendStatus, links, send, and 72 more.
utils/AiProviders/xai/index.ts(188,9): error TS2416: Property 'streamGetChatCompletion' in type 'XAiLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(messages: ChatMessage[] | null, { temperature }: CompletionOptions) => Promise<Stream<ChatCompletionChunk> | null>' is not assignable to type '(messages: ChatMessage[], options: CompletionOptions) => Promise<LLMStreamResponse>'.
    Type 'Promise<Stream<ChatCompletionChunk> | null>' is not assignable to type 'Promise<LLMStreamResponse>'.
      Type 'Stream<ChatCompletionChunk> | null' is not assignable to type 'LLMStreamResponse'.
        Type 'null' is not assignable to type 'LLMStreamResponse'.
utils/AiProviders/xai/index.ts(198,7): error TS2345: Argument of type 'APIPromise<Stream<ChatCompletionChunk>>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Types of property 'then' are incompatible.
    Type '<TResult1 = Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }, TResult2 = never>(onfulfilled?: ((value: Stream<ChatCompletionChunk> & { ...; }) => TResult1 | PromiseLike<...>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Pro...' is not assignable to type '<TResult1 = OpenAICompatibleStream, TResult2 = never>(onfulfilled?: ((value: OpenAICompatibleStream) => TResult1 | PromiseLike<TResult1>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Promise<...>'.
      Types of parameters 'onfulfilled' and 'onfulfilled' are incompatible.
        Types of parameters 'value' and 'value' are incompatible.
          Type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }' is not assignable to type 'OpenAICompatibleStream'.
            Index signature for type 'string' is missing in type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }'.
utils/AiProviders/xai/index.ts(207,5): error TS2739: Type 'MonitoredStream' is missing the following properties from type 'Stream<ChatCompletionChunk>': iterator, controller, tee, toReadableStream
utils/AiProviders/xai/index.ts(210,3): error TS2416: Property 'handleStream' in type 'XAiLLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '(response: Stream<ChatCompletionChunk>, stream: ReadableStream, responseProps: StreamResponseProps) => Promise<...>' is not assignable to type '(response: Response<any, Record<string, any>>, stream: AsyncIterable<unknown>, responseProps: StreamResponseProps) => Promise<...>'.
    Types of parameters 'response' and 'response' are incompatible.
      Type 'Response<any, Record<string, any>>' is missing the following properties from type 'Stream<ChatCompletionChunk>': iterator, controller, tee, toReadableStream, [Symbol.asyncIterator]
utils/AiProviders/xai/index.ts(215,42): error TS2345: Argument of type 'Stream<ChatCompletionChunk>' is not assignable to parameter of type 'Response<any, Record<string, any>>'.
  Type 'Stream<ChatCompletionChunk>' is missing the following properties from type 'Response<any, Record<string, any>>': status, sendStatus, links, send, and 88 more.
utils/chats/apiChatHandler.ts(72,5): error TS2322: Type 'string | number | boolean | Date | undefined' is not assignable to type 'string | undefined'.
  Type 'number' is not assignable to type 'string'.
utils/chats/apiChatHandler.ts(336,11): error TS2322: Type 'LLMProvider | LLMConnector' is not assignable to type 'LLMConnector'.
  Type 'LLMProvider' is missing the following properties from type 'LLMConnector': _provider, _options, getProvider, provider, and 3 more.
utils/chats/apiChatHandler.ts(337,32): error TS2345: Argument of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/LLMConnector").LLMConnector' is not assignable to parameter of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/llm").LLMConnector'.
  Property 'getModelName' is missing in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/LLMConnector").LLMConnector' but required in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/llm").LLMConnector'.
utils/chats/apiChatHandler.ts(583,9): error TS2345: Argument of type '{ id: number; } | null' is not assignable to parameter of type 'WorkspaceThread | null | undefined'.
  Type '{ id: number; }' is missing the following properties from type 'WorkspaceThread': name, slug, workspaceId, userId, and 2 more.
utils/chats/apiChatHandler.ts(743,11): error TS2322: Type 'LLMProvider | LLMConnector' is not assignable to type 'LLMConnector'.
  Type 'LLMProvider' is missing the following properties from type 'LLMConnector': _provider, _options, getProvider, provider, and 3 more.
utils/chats/apiChatHandler.ts(744,32): error TS2345: Argument of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/LLMConnector").LLMConnector' is not assignable to parameter of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/llm").LLMConnector'.
  Property 'getModelName' is missing in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/LLMConnector").LLMConnector' but required in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/llm").LLMConnector'.
utils/chats/apiChatHandler.ts(992,1): error TS2304: Cannot find name 'e'.
utils/chats/embed.ts(89,5): error TS2322: Type 'Workspace | undefined' is not assignable to type 'Workspace | null | undefined'.
  Type 'Workspace' is missing the following properties from type 'Workspace': name, vectorTag, createdAt, lastUpdatedAt
utils/chats/embed.ts(128,5): error TS2322: Type '{ name: any; id: string; slug: string; chatProvider?: string | undefined; chatModel?: string | undefined; openAiPrompt?: string | undefined; openAiTemp?: number | undefined; similarityThreshold?: number | undefined; topN?: number | undefined; vectorSearchMode?: string | undefined; queryRefusalResponse?: string | und...' is not assignable to type 'Workspace | null | undefined'.
  Type '{ name: any; id: string; slug: string; chatProvider?: string; chatModel?: string; openAiPrompt?: string; openAiTemp?: number; similarityThreshold?: number; topN?: number; vectorSearchMode?: string; queryRefusalResponse?: string; }' is not assignable to type 'Workspace'.
    Types of property 'id' are incompatible.
      Type 'string' is not assignable to type 'number'.
utils/chats/embed.ts(131,33): error TS2339: Property 'name' does not exist on type 'Workspace'.
utils/chats/embed.ts(219,32): error TS2345: Argument of type 'Workspace' is not assignable to parameter of type 'Workspace'.
  Types of property 'id' are incompatible.
    Type 'string' is not assignable to type 'number'.
utils/chats/embed.ts(333,5): error TS2322: Type 'EmbedChatData[]' is not assignable to type 'ChatMessage[]'.
  Type 'EmbedChatData' is missing the following properties from type 'ChatMessage': role, content
utils/chats/flowDispatcher.ts(294,5): error TS2322: Type 'AbortSignal | null' is not assignable to type 'AbortSignal | undefined'.
  Type 'null' is not assignable to type 'AbortSignal | undefined'.
utils/chats/flowDispatcher.ts(307,5): error TS2322: Type 'Record<string, string>' is not assignable to type 'FlowPrompts'.
  'string' index signatures are incompatible.
    Type 'string' is not assignable to type '{ [key: string]: unknown; SYSTEM_PROMPT?: string | undefined; USER_PROMPT?: string | undefined; PROMPT_TEMPLATE?: string | undefined; }'.
utils/chats/flowDispatcher.ts(351,7): error TS2322: Type 'Record<string, string>' is not assignable to type 'FlowPrompts'.
  'string' index signatures are incompatible.
    Type 'string' is not assignable to type '{ [key: string]: unknown; SYSTEM_PROMPT?: string | undefined; USER_PROMPT?: string | undefined; PROMPT_TEMPLATE?: string | undefined; }'.
utils/chats/flows/configurations/mainDocFlowConfig.ts(186,65): error TS2339: Property 'length' does not exist on type '{}'.
utils/chats/flows/configurations/noMainDocFlowConfig.ts(71,19): error TS2352: Conversion of type 'string' to type '{ "Doc Name": string; }' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
utils/chats/flows/configurations/noMainDocFlowConfig.ts(118,21): error TS2352: Conversion of type 'string' to type '{ "Doc Name": string; }' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
utils/chats/flows/configurations/noMainDocFlowConfig.ts(138,38): error TS2304: Cannot find name 'Section'.
utils/chats/flows/configurations/referenceFlowConfig.ts(338,61): error TS2554: Expected 2 arguments, but got 1.
utils/chats/flows/configurations/referenceFlowConfig.ts(351,17): error TS18047: 'result' is possibly 'null'.
utils/chats/flows/configurations/referenceFlowConfig.ts(637,61): error TS2554: Expected 2 arguments, but got 1.
utils/chats/flows/configurations/referenceFlowConfig.ts(650,17): error TS18047: 'result' is possibly 'null'.
utils/chats/flows/configurations/referenceFlowConfig.ts(740,13): error TS2322: Type '{ documentId: string; displayName: string; rawAnalysis: string; complianceStatus: string; issuesFound: number; issues: { type: string; severity: string; requirement: string; issue: string; location: string; recommendation: string; }[]; complianceScore: number; }[]' is not assignable to type '{ documentId: string; displayName: string; rawAnalysis: string; complianceStatus: string; issuesFound: number; issues: ComplianceIssue[]; complianceScore: number; }[]'.
  Type '{ documentId: string; displayName: string; rawAnalysis: string; complianceStatus: string; issuesFound: number; issues: { type: string; severity: string; requirement: string; issue: string; location: string; recommendation: string; }[]; complianceScore: number; }' is not assignable to type '{ documentId: string; displayName: string; rawAnalysis: string; complianceStatus: string; issuesFound: number; issues: ComplianceIssue[]; complianceScore: number; }'.
    Types of property 'issues' are incompatible.
      Type '{ type: string; severity: string; requirement: string; issue: string; location: string; recommendation: string; }[]' is not assignable to type 'ComplianceIssue[]'.
        Type '{ type: string; severity: string; requirement: string; issue: string; location: string; recommendation: string; }' is not assignable to type 'ComplianceIssue'.
          Types of property 'type' are incompatible.
            Type 'string' is not assignable to type '"error" | "violation" | "gap"'.
utils/chats/flows/configurations/referenceFlowConfig.ts(741,13): error TS2322: Type '{ type: string; severity: string; requirement: string; issue: string; location: string; recommendation: string; }[]' is not assignable to type 'ComplianceIssue[]'.
  Type '{ type: string; severity: string; requirement: string; issue: string; location: string; recommendation: string; }' is not assignable to type 'ComplianceIssue'.
    Types of property 'type' are incompatible.
      Type 'string' is not assignable to type '"error" | "violation" | "gap"'.
utils/chats/flows/configurations/referenceFlowConfig.ts(937,61): error TS2554: Expected 2 arguments, but got 1.
utils/chats/flows/configurations/referenceFlowConfig.ts(950,17): error TS18047: 'result' is possibly 'null'.
utils/chats/flows/configurations/referenceFlowConfig.ts(1021,13): error TS2322: Type '{ "Doc Name": string; DisplayName: string; Description: string; DocumentType: string; }[]' is not assignable to type 'string[] & { "Doc Name": string; DisplayName: string; Description: string; DocumentType: string; }[]'.
  Type '{ "Doc Name": string; DisplayName: string; Description: string; DocumentType: string; }[]' is not assignable to type 'string[]'.
    Type '{ "Doc Name": string; DisplayName: string; Description: string; DocumentType: string; }' is not assignable to type 'string'.
utils/chats/flows/configurations/referenceFlowConfig.ts(1098,5): error TS2322: Type '{ SYSTEM_PROMPT: string; }' is not assignable to type 'string'.
utils/chats/flows/configurations/referenceFlowConfig.ts(1101,5): error TS2322: Type '{ SYSTEM_PROMPT: string; }' is not assignable to type 'string'.
utils/chats/flows/configurations/referenceFlowConfig.ts(1102,5): error TS2322: Type '{ SYSTEM_PROMPT: string; }' is not assignable to type 'string'.
utils/chats/flows/configurations/referenceFlowConfig.ts(1103,5): error TS2322: Type '{ SYSTEM_PROMPT: string; }' is not assignable to type 'string'.
utils/chats/flows/core/FlowOrchestrator.ts(85,5): error TS2322: Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/abort-controller/dist/abort-controller").AbortSignal | undefined' is not assignable to type 'AbortSignal | undefined'.
  Type 'AbortSignal' is missing the following properties from type 'AbortSignal': reason, throwIfAborted
utils/chats/flows/core/FlowOrchestrator.ts(107,52): error TS2345: Argument of type 'UserData' is not assignable to parameter of type 'FilteredUser'.
  Type 'UserData' is missing the following properties from type 'FilteredUser': createdAt, lastUpdatedAt, organizationId, pfpFilename, and 6 more.
utils/chats/flows/core/FlowOrchestrator.ts(111,48): error TS2345: Argument of type 'WorkspaceData' is not assignable to parameter of type 'Workspace'.
  Type 'WorkspaceData' is missing the following properties from type 'Workspace': vectorTag, createdAt, lastUpdatedAt
utils/chats/flows/core/FlowOrchestrator.ts(118,9): error TS2345: Argument of type 'Response<any, Record<string, any>>' is not assignable to parameter of type 'Response'.
  Type 'Response<any, Record<string, any>>' is missing the following properties from type 'Response': headers, ok, statusText, url, and 8 more.
utils/chats/flows/core/FlowOrchestrator.ts(260,13): error TS2345: Argument of type '(response: Response<any, Record<string, any>>, data: ResponseChunkData) => void' is not assignable to parameter of type '(response: Response, chunk: unknown) => void'.
  Types of parameters 'response' and 'response' are incompatible.
    Type 'Response' is missing the following properties from type 'Response<any, Record<string, any>>': sendStatus, links, send, jsonp, and 85 more.
utils/chats/flows/core/FlowOrchestrator.ts(328,9): error TS2322: Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/flows/core/ProgressManager").ProgressManager' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").ProgressManager'.
  Property 'response' is private in type 'ProgressManager' but not in type 'ProgressManager'.
utils/chats/flows/core/FlowOrchestrator.ts(329,9): error TS2322: Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/flows/core/WorkspaceManager").WorkspaceManager' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").WorkspaceManager'.
  Property 'user' is private in type 'WorkspaceManager' but not in type 'WorkspaceManager'.
utils/chats/flows/core/FlowOrchestrator.ts(330,9): error TS2322: Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/flows/core/LLMCoordinator").LLMCoordinator' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").LLMCoordinator'.
  Types of property 'connector' are incompatible.
    Type 'LLMConnector | null' is not assignable to type 'LLMProvider | null'.
      Type 'LLMConnector' is missing the following properties from type 'LLMProvider': limits, customPromptWindowLimit
utils/chats/flows/core/FlowOrchestrator.ts(331,9): error TS2322: Type 'ContextWindowManager | null' is not assignable to type 'ContextWindowManager'.
  Type 'null' is not assignable to type 'ContextWindowManager'.
utils/chats/flows/core/FlowOrchestrator.ts(332,9): error TS2322: Type 'TokenTracker | null' is not assignable to type 'TokenTracker'.
  Type 'null' is not assignable to type 'TokenTracker'.
utils/chats/flows/core/LLMCoordinator.ts(234,27): error TS2345: Argument of type 'CompletionResponse | null' is not assignable to parameter of type 'ChatCompletionResult'.
  Type 'null' is not assignable to type 'ChatCompletionResult'.
utils/chats/flows/core/LLMCoordinator.ts(241,5): error TS2322: Type 'CompletionResponse | null' is not assignable to type 'ChatCompletionResult'.
  Type 'null' is not assignable to type 'ChatCompletionResult'.
utils/chats/flows/core/LLMCoordinator.ts(288,34): error TS2352: Conversion of type 'LLMConnector' to type 'LLMProvider & { metrics?: Record<string, unknown> | undefined; }' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'LLMConnector' is missing the following properties from type 'LLMProvider': limits, customPromptWindowLimit
utils/chats/flows/core/LLMCoordinator.ts(303,34): error TS2352: Conversion of type 'LLMConnector | null' to type 'LLMProvider & { metrics?: Record<string, unknown> | undefined; }' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'LLMConnector' is not comparable to type 'LLMProvider & { metrics?: Record<string, unknown> | undefined; }'.
    Type 'LLMConnector' is missing the following properties from type 'LLMProvider': limits, customPromptWindowLimit
utils/chats/flows/core/LLMCoordinator.ts(322,34): error TS2352: Conversion of type 'LLMConnector | null' to type 'LLMProvider & { metrics?: Record<string, unknown> | undefined; provider?: string | undefined; }' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'LLMConnector' is not comparable to type 'LLMProvider & { metrics?: Record<string, unknown> | undefined; provider?: string | undefined; }'.
    Type 'LLMConnector' is missing the following properties from type 'LLMProvider': limits, customPromptWindowLimit
utils/chats/flows/core/LLMCoordinator.ts(349,34): error TS2352: Conversion of type 'LLMConnector | null' to type 'LLMProvider & { metrics?: Record<string, unknown> | undefined; }' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'LLMConnector' is not comparable to type 'LLMProvider & { metrics?: Record<string, unknown> | undefined; }'.
    Type 'LLMConnector' is missing the following properties from type 'LLMProvider': limits, customPromptWindowLimit
utils/chats/flows/core/LLMCoordinator.ts(372,35): error TS2352: Conversion of type 'LLMConnector | null' to type 'LLMProvider & { provider?: string | undefined; }' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'LLMConnector' is not comparable to type 'LLMProvider & { provider?: string | undefined; }'.
    Type 'LLMConnector' is missing the following properties from type 'LLMProvider': limits, customPromptWindowLimit
utils/chats/flows/core/StageProcessor.ts(102,24): error TS2352: Conversion of type '{ updateStep: () => void; sendSubStepProgress: () => void; }' to type 'ProgressManager' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type '{ updateStep: () => void; sendSubStepProgress: () => void; }' is missing the following properties from type 'ProgressManager': response, chatId, flowType, currentStep, and 9 more.
utils/chats/flows/core/StageProcessor.ts(130,18): error TS2352: Conversion of type '{ slug: string; id: number; type: string; }' to type 'WorkspaceData' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'name' is missing in type '{ slug: string; id: number; type: string; }' but required in type 'WorkspaceData'.
utils/chats/flows/core/StageProcessor.ts(139,9): error TS2741: Property 'name' is missing in type '{ slug: string; id: number; }' but required in type 'WorkspaceData'.
utils/chats/flows/core/WorkspaceManager.ts(84,7): error TS2345: Argument of type '{ id: string | number | boolean | Date | null | undefined; organization?: Organization; styleAlignmentEnabled?: boolean; activeStyleProfile?: UserStyleProfile | null; ... 12 more ...; custom_system_prompt: string | null; }' is not assignable to parameter of type 'AuthenticatedUser'.
  Types of property 'id' are incompatible.
    Type 'string | number | boolean | Date | null | undefined' is not assignable to type 'number'.
      Type 'undefined' is not assignable to type 'number'.
utils/chats/flows/demo/modularFlowDemo.ts(38,7): error TS2352: Conversion of type 'FlowConfiguration' to type 'FlowConfig' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Types of property 'stages' are incompatible.
    Type 'FlowStageConfig[]' is not comparable to type 'StageConfig[]'.
      Type 'FlowStageConfig' is not comparable to type 'StageConfig'.
        Index signature for type 'string' is missing in type 'FlowStageConfig'.
utils/chats/flows/demo/modularFlowDemo.ts(150,9): error TS2322: Type 'typeof StageProcessor' is not assignable to type 'new (...args: unknown[]) => StageProcessor'.
  Cannot assign an abstract constructor type to a non-abstract constructor type.
utils/chats/flows/demo/modularFlowDemo.ts(150,20): error TS2352: Conversion of type 'typeof SetupStageProcessor' to type 'typeof StageProcessor' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Types of construct signatures are incompatible.
    Type 'new (options?: Record<string, unknown>) => SetupStageProcessor' is not assignable to type 'abstract new (options?: Record<string, unknown>) => StageProcessor'.
      Construct signature return types 'SetupStageProcessor' and 'StageProcessor' are incompatible.
        The types returned by 'process(...)' are incompatible between these types.
          Type 'Promise<SetupResult>' is not comparable to type 'Promise<Partial<FlowContext>>'.
            Type 'SetupResult' is not comparable to type 'Partial<FlowContext>'.
              Index signature for type 'string' is missing in type 'SetupResult'.
utils/chats/flows/demo/modularFlowDemo.ts(155,9): error TS2322: Type 'typeof StageProcessor' is not assignable to type 'new (...args: unknown[]) => StageProcessor'.
  Cannot assign an abstract constructor type to a non-abstract constructor type.
utils/chats/flows/demo/modularFlowDemo.ts(160,9): error TS2322: Type 'typeof StageProcessor' is not assignable to type 'new (...args: unknown[]) => StageProcessor'.
  Cannot assign an abstract constructor type to a non-abstract constructor type.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(21,3): error TS2440: Import declaration conflicts with local declaration of 'LegalMemo'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(314,58): error TS2345: Argument of type 'CombinationMetrics' is not assignable to parameter of type 'Record<string, unknown>'.
  Index signature for type 'string' is missing in type 'CombinationMetrics'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(528,15): error TS2322: Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/documentEditing/editingLogic").EditingResult' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/flows/processors/AgenticCombinationStageProcessor").EditingResult'.
  Types of property 'stats' are incompatible.
    Type '{ totalSuggestions: number; appliedSuggestions: number; processingTime: number; } | undefined' is not assignable to type 'EditingStatistics | undefined'.
      Type '{ totalSuggestions: number; appliedSuggestions: number; processingTime: number; }' is missing the following properties from type 'EditingStatistics': approvedSuggestions, disapprovedSuggestions, appliedEdits, wordCount, lineCount
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(530,11): error TS2345: Argument of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/flows/processors/AgenticCombinationStageProcessor").LegalMemo[]' is not assignable to parameter of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/documentEditing/editingLogic").LegalMemo[]'.
  Property 'id' is missing in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/flows/processors/AgenticCombinationStageProcessor").LegalMemo' but required in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/documentEditing/editingLogic").LegalMemo'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(670,15): error TS2322: Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/documentEditing/editingLogic").EditingResult' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/flows/processors/AgenticCombinationStageProcessor").EditingResult'.
  Types of property 'stats' are incompatible.
    Type '{ totalSuggestions: number; appliedSuggestions: number; processingTime: number; } | undefined' is not assignable to type 'EditingStatistics | undefined'.
      Type '{ totalSuggestions: number; appliedSuggestions: number; processingTime: number; }' is missing the following properties from type 'EditingStatistics': approvedSuggestions, disapprovedSuggestions, appliedEdits, wordCount, lineCount
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(672,11): error TS2345: Argument of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/flows/processors/AgenticCombinationStageProcessor").LegalMemo[]' is not assignable to parameter of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/documentEditing/editingLogic").LegalMemo[]'.
  Property 'id' is missing in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/flows/processors/AgenticCombinationStageProcessor").LegalMemo' but required in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/documentEditing/editingLogic").LegalMemo'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(783,61): error TS2345: Argument of type '{ role: string; content: string; }[]' is not assignable to parameter of type 'ChatMessage[]'.
  Type '{ role: string; content: string; }' is not assignable to type 'ChatMessage'.
    Types of property 'role' are incompatible.
      Type 'string' is not assignable to type '"system" | "user" | "assistant"'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(821,36): error TS2339: Property 'length' does not exist on type '{}'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(823,33): error TS2339: Property 'forEach' does not exist on type '{}'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(824,55): error TS2345: Argument of type '{}' is not assignable to parameter of type 'PathLike'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(827,19): error TS2769: No overload matches this call.
  Overload 1 of 3, '(path: PathOrFileDescriptor, options?: { encoding?: null | undefined; flag?: string | undefined; } | null | undefined): NonSharedBuffer', gave the following error.
    Argument of type '{}' is not assignable to parameter of type 'PathOrFileDescriptor'.
  Overload 2 of 3, '(path: PathOrFileDescriptor, options: BufferEncoding | { encoding: BufferEncoding; flag?: string | undefined; }): string', gave the following error.
    Argument of type '{}' is not assignable to parameter of type 'PathOrFileDescriptor'.
  Overload 3 of 3, '(path: PathOrFileDescriptor, options?: BufferEncoding | (ObjectEncodingOptions & { flag?: string | undefined; }) | null | undefined): string | NonSharedBuffer', gave the following error.
    Argument of type '{}' is not assignable to parameter of type 'PathOrFileDescriptor'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(936,43): error TS2339: Property 'finalWordCount' does not exist on type '{}'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(939,51): error TS2339: Property 'wordChangeFromEditing' does not exist on type '{}'.
utils/chats/flows/processors/CombinationStageProcessor.ts(42,11): error TS2345: Argument of type 'CombinationOptions' is not assignable to parameter of type 'Record<string, unknown>'.
  Index signature for type 'string' is missing in type 'CombinationOptions'.
utils/chats/flows/processors/CombinationStageProcessor.ts(140,58): error TS2345: Argument of type 'CombinationMetrics' is not assignable to parameter of type 'Record<string, unknown>'.
  Index signature for type 'string' is missing in type 'CombinationMetrics'.
utils/chats/flows/processors/CombinationStageProcessor.ts(196,34): error TS2339: Property 'setupCompletedAt' does not exist on type '{}'.
utils/chats/flows/processors/CombinationStageProcessor.ts(198,59): error TS2339: Property 'setupCompletedAt' does not exist on type '{}'.
utils/chats/flows/processors/CombinationStageProcessor.ts(206,49): error TS2339: Property 'processedDocuments' does not exist on type '{}'.
utils/chats/flows/processors/CombinationStageProcessor.ts(206,82): error TS2339: Property 'totalDocuments' does not exist on type '{}'.
utils/chats/flows/processors/CombinationStageProcessor.ts(214,52): error TS2339: Property 'sectionsGenerated' does not exist on type '{}'.
utils/chats/flows/processors/CombinationStageProcessor.ts(214,94): error TS2339: Property 'sectionSource' does not exist on type '{}'.
utils/chats/flows/processors/DocumentProcessingStageProcessor.ts(145,11): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'LLMConnector'.
utils/chats/flows/processors/DocumentProcessingStageProcessor.ts(184,13): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'LLMConnector'.
utils/chats/flows/processors/IterativeSectionDraftingProcessor.ts(119,10): error TS2416: Property 'options' in type 'IterativeSectionDraftingProcessor' is not assignable to the same property in base type 'StageProcessor'.
  Type 'IterativeDraftingOptions' is not assignable to type 'Record<string, unknown>'.
    Index signature for type 'string' is missing in type 'IterativeDraftingOptions'.
utils/chats/flows/processors/IterativeSectionDraftingProcessor.ts(122,11): error TS2345: Argument of type 'IterativeDraftingOptions' is not assignable to parameter of type 'Record<string, unknown>'.
  Index signature for type 'string' is missing in type 'IterativeDraftingOptions'.
utils/chats/flows/processors/IterativeSectionDraftingProcessor.ts(192,57): error TS2345: Argument of type 'FlowContext' is not assignable to parameter of type 'DraftingContext'.
  Types of property 'processedDocuments' are incompatible.
    Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").ProcessedDocument[]' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/helpers/documentProcessing").ProcessedDocument[]'.
      Property 'name' is missing in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").ProcessedDocument' but required in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/helpers/documentProcessing").ProcessedDocument'.
utils/chats/flows/processors/IterativeSectionDraftingProcessor.ts(220,9): error TS2345: Argument of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").ContextWindowManager' is not assignable to parameter of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/helpers/contextWindowManager").ContextWindowManager'.
  Type 'ContextWindowManager' is missing the following properties from type 'ContextWindowManager': LLMConnector, tokenManager, options, metrics, and 11 more.
utils/chats/flows/processors/IterativeSectionDraftingProcessor.ts(258,62): error TS2345: Argument of type 'DraftingMetrics' is not assignable to parameter of type 'Record<string, unknown>'.
  Index signature for type 'string' is missing in type 'DraftingMetrics'.
utils/chats/flows/processors/IterativeSectionDraftingProcessor.ts(324,7): error TS2322: Type '{}' is not assignable to type 'string'.
utils/chats/flows/processors/IterativeSectionDraftingProcessor.ts(402,32): error TS18046: 'tokenTracker.startStage' is of type 'unknown'.
utils/chats/flows/processors/IterativeSectionDraftingProcessor.ts(503,29): error TS2352: Conversion of type 'FlowContext' to type 'DraftingContext' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Types of property 'processedDocuments' are incompatible.
    Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").ProcessedDocument[]' is not comparable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/helpers/documentProcessing").ProcessedDocument[]'.
      Property 'name' is missing in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").ProcessedDocument' but required in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/helpers/documentProcessing").ProcessedDocument'.
utils/chats/flows/processors/IterativeSectionDraftingProcessor.ts(533,29): error TS2352: Conversion of type 'FlowContext' to type 'DraftingContext' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Types of property 'processedDocuments' are incompatible.
    Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").ProcessedDocument[]' is not comparable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/helpers/documentProcessing").ProcessedDocument[]'.
      Property 'name' is missing in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").ProcessedDocument' but required in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/helpers/documentProcessing").ProcessedDocument'.
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(144,9): error TS2345: Argument of type '{}' is not assignable to parameter of type 'string'.
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(150,11): error TS2345: Argument of type 'Section' is not assignable to parameter of type 'SectionWithIssues'.
  Types of property 'identifiedLegalIssues' are incompatible.
    Type 'LegalIssue[] | undefined' is not assignable to type '(string | LegalIssue)[]'.
      Type 'undefined' is not assignable to type '(string | LegalIssue)[]'.
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(168,11): error TS2345: Argument of type '{}' is not assignable to parameter of type 'string'.
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(190,11): error TS2345: Argument of type '{}' is not assignable to parameter of type 'string'.
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(211,7): error TS2322: Type 'SectionWithIssues[]' is not assignable to type 'Section[]'.
  Property 'sectionNumber' is missing in type 'SectionWithIssues' but required in type 'Section'.
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(212,7): error TS2322: Type 'string[]' is not assignable to type 'LegalIssue[]'.
  Type 'string' is not assignable to type 'LegalIssue'.
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(286,40): error TS2339: Property 'lastCompletionTokens' does not exist on type '{}'.
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(287,49): error TS2339: Property 'lastCompletionTokens' does not exist on type '{}'.
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(335,9): error TS2740: Type 'LLMProvider' is missing the following properties from type 'LLMConnector': _provider, _options, getProvider, provider, and 3 more.
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(347,39): error TS2339: Property 'length' does not exist on type '{}'.
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(352,29): error TS2339: Property 'map' does not exist on type '{}'.
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(355,32): error TS2339: Property 'Issue' does not exist on type 'object'.
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(357,27): error TS2339: Property 'WORKSPACE_SLUG_FOR_LEGALDATA' does not exist on type 'object'.
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(406,22): error TS2749: 'Workspace' refers to a value, but is being used as a type here. Did you mean 'typeof Workspace'?
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(407,19): error TS2749: 'Workspace' refers to a value, but is being used as a type here. Did you mean 'typeof Workspace'?
utils/chats/flows/processors/LegalMemoProcessor.ts(137,11): error TS18046: 'tokenTracker.startStage' is of type 'unknown'.
utils/chats/flows/processors/LegalMemoProcessor.ts(142,60): error TS2345: Argument of type 'WorkspaceData' is not assignable to parameter of type 'Workspace'.
  Type 'WorkspaceData' is missing the following properties from type 'Workspace': vectorTag, createdAt, lastUpdatedAt
utils/chats/flows/processors/LegalMemoProcessor.ts(185,9): error TS2322: Type 'GeneratedMemo[]' is not assignable to type 'Memo[]'.
  Type 'GeneratedMemo' is not assignable to type 'Memo'.
    Index signature for type 'string' is missing in type 'GeneratedMemo'.
utils/chats/flows/processors/LegalMemoProcessor.ts(503,15): error TS2322: Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/models").Workspace' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/helpers/legalMemo").Workspace'.
  Types of property 'type' are incompatible.
    Type 'string | null | undefined' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/chats/flows/processors/LegalMemoProcessor.ts(506,15): error TS2322: Type 'BaseLLMProvider' is not assignable to type 'LLMConnector'.
  Types of property 'getChatCompletion' are incompatible.
    Type '(messages: ChatMessage[], options: CompletionOptions) => Promise<CompletionResponse | null>' is not assignable to type '(messages: ChatMessage[], options?: ChatCompletionOptions | undefined) => Promise<{ content: string; usage?: { prompt_tokens: number; completion_tokens: number; total_tokens: number; } | undefined; }>'.
      Types of parameters 'options' and 'options' are incompatible.
        Type 'ChatCompletionOptions | undefined' is not assignable to type 'CompletionOptions'.
          Type 'undefined' is not assignable to type 'CompletionOptions'.
utils/chats/flows/processors/LegalMemoProcessor.ts(756,5): error TS2322: Type 'string | boolean | undefined' is not assignable to type 'boolean'.
  Type 'undefined' is not assignable to type 'boolean'.
utils/chats/flows/processors/SetupStageProcessor.ts(89,9): error TS2416: Property 'process' in type 'SetupStageProcessor' is not assignable to the same property in base type 'StageProcessor'.
  Type '(context: SetupContext, dependencies: SetupDependencies) => Promise<SetupResult>' is not assignable to type '(context: FlowContext, dependencies: StageDependencies) => Promise<Partial<FlowContext>>'.
    Type 'Promise<SetupResult>' is not assignable to type 'Promise<Partial<FlowContext>>'.
      Type 'SetupResult' is not assignable to type 'Partial<FlowContext>'.
        Index signature for type 'string' is missing in type 'SetupResult'.
utils/chats/flows/processors/SetupStageProcessor.ts(208,62): error TS2345: Argument of type 'SetupMetrics' is not assignable to parameter of type 'Record<string, unknown>'.
  Index signature for type 'string' is missing in type 'SetupMetrics'.
utils/chats/flows/processors/SetupStageProcessor.ts(218,11): error TS2353: Object literal may only specify known properties, and 'setup' does not exist in type 'SetupMetrics'.
utils/chats/helpers/documentProcessing.ts(190,20): error TS2345: Argument of type 'CompletionResponse | null' is not assignable to parameter of type 'CompletionResponse | LLMResponse | ChatCompletionResult'.
  Type 'null' is not assignable to type 'CompletionResponse | LLMResponse | ChatCompletionResult'.
utils/chats/helpers/documentProcessing.ts(235,20): error TS2345: Argument of type 'CompletionResponse | null' is not assignable to parameter of type 'CompletionResponse | LLMResponse | ChatCompletionResult'.
  Type 'null' is not assignable to type 'CompletionResponse | LLMResponse | ChatCompletionResult'.
utils/chats/helpers/documentProcessing.ts(281,20): error TS2345: Argument of type 'CompletionResponse | null' is not assignable to parameter of type 'CompletionResponse | LLMResponse | ChatCompletionResult'.
  Type 'null' is not assignable to type 'CompletionResponse | LLMResponse | ChatCompletionResult'.
utils/chats/helpers/documentProcessing.ts(426,24): error TS2345: Argument of type 'CompletionResponse | null' is not assignable to parameter of type 'CompletionResponse | LLMResponse | ChatCompletionResult'.
  Type 'null' is not assignable to type 'CompletionResponse | LLMResponse | ChatCompletionResult'.
utils/chats/helpers/documentProcessing.ts(544,20): error TS2345: Argument of type 'CompletionResponse | null' is not assignable to parameter of type 'CompletionResponse | LLMResponse | ChatCompletionResult'.
  Type 'null' is not assignable to type 'CompletionResponse | LLMResponse | ChatCompletionResult'.
utils/chats/helpers/documentProcessing.ts(716,22): error TS2345: Argument of type 'CompletionResponse | null' is not assignable to parameter of type 'CompletionResponse | LLMResponse | ChatCompletionResult'.
  Type 'null' is not assignable to type 'CompletionResponse | LLMResponse | ChatCompletionResult'.
utils/chats/helpers/documentProcessing.ts(860,22): error TS2345: Argument of type 'CompletionResponse | null' is not assignable to parameter of type 'CompletionResponse | LLMResponse | ChatCompletionResult'.
  Type 'null' is not assignable to type 'CompletionResponse | LLMResponse | ChatCompletionResult'.
utils/chats/helpers/documentProcessing.ts(978,53): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
utils/chats/helpers/documentProcessing.ts(979,53): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
utils/chats/helpers/documentProcessing.ts(981,17): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
utils/chats/helpers/documentProcessing.ts(983,56): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
utils/chats/helpers/documentProcessing.ts(984,56): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
utils/chats/helpers/documentProcessing.ts(986,17): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
utils/chats/helpers/documentProcessing.ts(1270,20): error TS2345: Argument of type 'CompletionResponse | null' is not assignable to parameter of type 'CompletionResponse | LLMResponse | ChatCompletionResult'.
  Type 'null' is not assignable to type 'CompletionResponse | LLMResponse | ChatCompletionResult'.
utils/chats/helpers/llmResponseParser.ts(391,20): error TS18046: 'transformError' is of type 'unknown'.
utils/chats/helpers/llmResponseParser.ts(408,57): error TS18046: 'regexError' is of type 'unknown'.
utils/chats/helpers/llmResponseParser.ts(412,41): error TS18046: 'regexError' is of type 'unknown'.
utils/chats/helpers/llmResponseParser.ts(571,14): error TS18046: 'error' is of type 'unknown'.
utils/chats/index.ts(101,60): error TS2345: Argument of type 'WorkspaceChat[]' is not assignable to parameter of type 'ChatRecord[]'.
  Type 'WorkspaceChat' is not assignable to type 'ChatRecord'.
    Types of property 'feedbackScore' are incompatible.
      Type 'boolean | null | undefined' is not assignable to type 'number | null | undefined'.
        Type 'boolean' is not assignable to type 'number'.
utils/chats/LLMConnector.ts(129,7): error TS2345: Argument of type 'ReadableStream' is not assignable to parameter of type 'Response<any, Record<string, any>>'.
  Type 'ReadableStream' is missing the following properties from type 'Response<any, Record<string, any>>': status, sendStatus, links, send, and 72 more.
utils/chats/LLMConnector.ts(157,58): error TS2345: Argument of type 'unknown[]' is not assignable to parameter of type 'ChatMessage[]'.
  Type 'unknown' is not assignable to type 'ChatMessage'.
utils/chats/LLMConnector.ts(181,13): error TS2352: Conversion of type 'LLMProvider' to type 'Record<string, unknown>' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Index signature for type 'string' is missing in type 'LLMProvider'.
utils/chats/LLMConnector.ts(191,6): error TS2352: Conversion of type 'LLMProvider' to type 'Record<string, unknown>' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Index signature for type 'string' is missing in type 'LLMProvider'.
utils/chats/openaiCompatible.ts(179,23): error TS2345: Argument of type '{ token_count_estimate: number; text: string; }' is not assignable to parameter of type 'SourceResult'.
  Type '{ token_count_estimate: number; text: string; }' is missing the following properties from type 'SourceResult': id, content
utils/chats/openaiCompatible.ts(192,11): error TS2322: Type 'number | null | undefined' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
utils/chats/openaiCompatible.ts(193,11): error TS2322: Type 'number | null | undefined' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
utils/chats/openaiCompatible.ts(265,46): error TS2345: Argument of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/models").Workspace' is not assignable to parameter of type 'Workspace'.
  Types of property 'openAiPrompt' are incompatible.
    Type 'string | null | undefined' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/chats/openaiCompatible.ts(391,7): error TS2345: Argument of type 'OpenAICompatibleResponse' is not assignable to parameter of type 'ResponseChunkData'.
  Index signature for type 'string' is missing in type 'OpenAICompatibleResponse'.
utils/chats/openaiCompatible.ts(422,23): error TS2345: Argument of type '{ token_count_estimate: number; text: string; }' is not assignable to parameter of type 'SourceResult'.
  Type '{ token_count_estimate: number; text: string; }' is missing the following properties from type 'SourceResult': id, content
utils/chats/openaiCompatible.ts(435,11): error TS2322: Type 'number | null | undefined' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
utils/chats/openaiCompatible.ts(436,11): error TS2322: Type 'number | null | undefined' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
utils/chats/openaiCompatible.ts(453,9): error TS2345: Argument of type 'OpenAICompatibleResponse' is not assignable to parameter of type 'ResponseChunkData'.
  Index signature for type 'string' is missing in type 'OpenAICompatibleResponse'.
utils/chats/openaiCompatible.ts(498,7): error TS2345: Argument of type 'OpenAICompatibleResponse' is not assignable to parameter of type 'ResponseChunkData'.
  Index signature for type 'string' is missing in type 'OpenAICompatibleResponse'.
utils/chats/openaiCompatible.ts(516,46): error TS2345: Argument of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/models").Workspace' is not assignable to parameter of type 'Workspace'.
  Types of property 'openAiPrompt' are incompatible.
    Type 'string | null | undefined' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/chats/openaiCompatible.ts(525,7): error TS2345: Argument of type 'OpenAICompatibleResponse' is not assignable to parameter of type 'ResponseChunkData'.
  Index signature for type 'string' is missing in type 'OpenAICompatibleResponse'.
utils/chats/openaiCompatible.ts(549,5): error TS2345: Argument of type 'PassThrough' is not assignable to parameter of type 'Response<any, Record<string, any>>'.
  Type 'Transform' is missing the following properties from type 'Response<any, Record<string, any>>': status, sendStatus, links, send, and 50 more.
utils/chats/openaiCompatible.ts(571,7): error TS2345: Argument of type 'OpenAICompatibleResponse' is not assignable to parameter of type 'ResponseChunkData'.
  Index signature for type 'string' is missing in type 'OpenAICompatibleResponse'.
utils/chats/openaiCompatible.ts(593,5): error TS2345: Argument of type 'OpenAICompatibleResponse' is not assignable to parameter of type 'ResponseChunkData'.
  Index signature for type 'string' is missing in type 'OpenAICompatibleResponse'.
utils/chats/stream.ts(77,9): error TS2345: Argument of type 'unknown[]' is not assignable to parameter of type 'string[]'.
  Type 'unknown' is not assignable to type 'string'.
utils/chats/stream.ts(107,7): error TS2345: Argument of type 'Attachment[]' is not assignable to parameter of type 'Attachment[]'.
  Type 'Attachment' is not assignable to type 'Attachment'. Two different types with this name exist, but they are unrelated.
    'string' index signatures are incompatible.
      Type 'unknown' is not assignable to type 'string | undefined'.
utils/chats/streamCanvas.ts(76,42): error TS2339: Property 'toLowerCase' does not exist on type '{}'.
utils/chats/streamCanvas.ts(103,14): error TS2339: Property 'split' does not exist on type '{}'.
utils/chats/streamCanvas.ts(115,7): error TS2322: Type '{}' is not assignable to type 'string'.
utils/chats/streamCanvas.ts(209,32): error TS2345: Argument of type '(sourceDocument: SourceDocument) => string' is not assignable to parameter of type '(value: CanvasSource, index: number, array: CanvasSource[]) => string'.
  Types of parameters 'sourceDocument' and 'value' are incompatible.
    Type 'CanvasSource' is not assignable to type 'SourceDocument'.
      Index signature for type 'string' is missing in type 'CanvasSource'.
utils/chats/streamCanvas.ts(227,9): error TS2322: Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/models").Workspace' is not assignable to type 'Workspace'.
  Types of property 'openAiPrompt' are incompatible.
    Type 'string | null | undefined' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/chats/streamCanvas.ts(232,37): error TS18046: 'h' is of type 'unknown'.
utils/chats/streamCanvas.ts(232,48): error TS18046: 'h' is of type 'unknown'.
utils/chats/streamCanvas.ts(366,9): error TS2322: Type 'unknown[]' is not assignable to type 'Attachment[]'.
  Type 'unknown' is not assignable to type 'Attachment'.
utils/chats/streamCanvas.ts(409,31): error TS2504: Type 'LLMStreamResponse' must have a '[Symbol.asyncIterator]()' method that returns an async iterator.
utils/chats/streamCanvas.ts(460,7): error TS2322: Type 'string' is not assignable to type 'Record<string, unknown>'.
utils/chats/streamCDB.ts(171,38): error TS2345: Argument of type 'CDBOptions' is not assignable to parameter of type 'FlowOptions'.
  Types of property 'user' are incompatible.
    Type 'FilteredUser | null | undefined' is not assignable to type 'UserData | undefined'.
      Type 'null' is not assignable to type 'UserData | undefined'.
utils/chats/streamDD.ts(489,11): error TS2322: Type 'WorkspaceWithDocuments' is not assignable to type 'Workspace'.
  Types of property 'type' are incompatible.
    Type 'string | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/chats/streamDD.ts(680,5): error TS2322: Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/models").Workspace' is not assignable to type 'Workspace'.
  Types of property 'openAiPrompt' are incompatible.
    Type 'string | null | undefined' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/chats/streamDD.ts(1123,35): error TS2722: Cannot invoke an object which is possibly 'undefined'.
utils/chats/streamDD.ts(1123,35): error TS18048: 'LLMConnector.streamGetChatCompletion' is possibly 'undefined'.
utils/chats/streamDD.ts(1129,13): error TS2322: Type 'string | undefined' is not assignable to type 'string'.
  Type 'undefined' is not assignable to type 'string'.
utils/chats/streamDD.ts(1489,38): error TS2722: Cannot invoke an object which is possibly 'undefined'.
utils/chats/streamDD.ts(1489,38): error TS18048: 'LLMConnector.streamGetChatCompletion' is possibly 'undefined'.
utils/chats/streamDD.ts(1494,32): error TS2722: Cannot invoke an object which is possibly 'undefined'.
utils/chats/streamDD.ts(1494,32): error TS18048: 'LLMConnector.handleStream' is possibly 'undefined'.
utils/chats/streamDD.ts(1568,29): error TS2722: Cannot invoke an object which is possibly 'undefined'.
utils/chats/streamDD.ts(1568,29): error TS18048: 'LLMConnector.streamGetChatCompletion' is possibly 'undefined'.
utils/chats/streamDD.ts(1575,28): error TS2722: Cannot invoke an object which is possibly 'undefined'.
utils/chats/streamDD.ts(1635,35): error TS2722: Cannot invoke an object which is possibly 'undefined'.
utils/chats/streamDD.ts(1635,35): error TS18048: 'LLMConnector.streamGetChatCompletion' is possibly 'undefined'.
utils/chats/streamDD.ts(1641,13): error TS2322: Type 'string | undefined' is not assignable to type 'string'.
  Type 'undefined' is not assignable to type 'string'.
utils/chats/streamDD.ts(2001,38): error TS2722: Cannot invoke an object which is possibly 'undefined'.
utils/chats/streamDD.ts(2001,38): error TS18048: 'LLMConnector.streamGetChatCompletion' is possibly 'undefined'.
utils/chats/streamDD.ts(2006,32): error TS2722: Cannot invoke an object which is possibly 'undefined'.
utils/chats/streamDD.ts(2006,32): error TS18048: 'LLMConnector.handleStream' is possibly 'undefined'.
utils/chats/streamLQA.ts(258,36): error TS2783: 'uuid' is specified more than once, so this usage will be overwritten.
utils/chats/streamLQA.ts(502,7): error TS2322: Type 'number | undefined' is not assignable to type 'number'.
  Type 'undefined' is not assignable to type 'number'.
utils/chats/streamLQA.ts(504,7): error TS2322: Type 'number | undefined' is not assignable to type 'number'.
  Type 'undefined' is not assignable to type 'number'.
utils/chats/streamLQA.ts(594,5): error TS2345: Argument of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/models").Workspace' is not assignable to parameter of type 'Workspace'.
  Types of property 'openAiPrompt' are incompatible.
    Type 'string | null | undefined' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/chats/streamLQA.ts(602,10): error TS2345: Argument of type '(history: ChatHistoryEntry) => string' is not assignable to parameter of type '(value: unknown, index: number, array: unknown[]) => string'.
  Types of parameters 'history' and 'value' are incompatible.
    Type 'unknown' is not assignable to type 'ChatHistoryEntry'.
utils/chats/streamLQA.ts(670,7): error TS18048: 'llmContextWindow' is possibly 'undefined'.
utils/chats/streamLQA.ts(793,38): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
utils/chats/streamLQA.ts(794,33): error TS18048: 'currentTokens' is possibly 'undefined'.
utils/chats/streamLQA.ts(795,37): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
utils/chats/streamLQA.ts(796,32): error TS18048: 'currentTokens' is possibly 'undefined'.
utils/chats/streamLQA.ts(968,12): error TS18048: 'source.url' is possibly 'undefined'.
utils/chats/streamLQA.ts(969,44): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
utils/chats/streamLQA.ts(1047,11): error TS2345: Argument of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/models").Workspace' is not assignable to parameter of type 'Workspace'.
  Types of property 'openAiPrompt' are incompatible.
    Type 'string | null | undefined' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/chats/streamLQA.ts(1177,68): error TS2345: Argument of type 'StreamOptions' is not assignable to parameter of type 'StreamResponseProps'.
  Types of property 'sources' are incompatible.
    Type 'VectorSearchSource[]' is not assignable to type 'StreamSource[]'.
      Type 'VectorSearchSource' is not assignable to type 'StreamSource'.
        Types of property 'url' are incompatible.
          Type 'string | undefined' is not assignable to type 'string'.
            Type 'undefined' is not assignable to type 'string'.
utils/vectorDbProviders/astra/index.ts(116,8): error TS2554: Expected 2-3 arguments, but got 1.
utils/vectorDbProviders/astra/index.ts(128,59): error TS2345: Argument of type '{}' is not assignable to parameter of type 'number'.
utils/vectorDbProviders/astra/index.ts(154,55): error TS2345: Argument of type '{}' is not assignable to parameter of type 'number'.
utils/vectorDbProviders/astra/index.ts(542,41): error TS2304: Cannot find name 'FoundDoc'.
utils/vectorDbProviders/astra/index.ts(542,50): error TS2304: Cannot find name 'SomeDoc'.
utils/vectorDbProviders/chroma/index.ts(269,55): error TS2345: Argument of type 'Metadata | null' is not assignable to parameter of type 'SourceDocument'.
  Type 'null' is not assignable to type 'SourceDocument'.
utils/vectorDbProviders/milvus/index.ts(128,13): error TS2339: Property 'collection_names' does not exist on type 'ShowCollectionsResponse'.
utils/vectorDbProviders/milvus/index.ts(159,5): error TS2322: Type 'StatisticsResponse | null' is not assignable to type 'Record<string, unknown> | null'.
  Type 'StatisticsResponse' is not assignable to type 'Record<string, unknown>'.
    Index signature for type 'string' is missing in type 'StatisticsResponse'.
utils/vectorDbProviders/milvus/index.ts(179,5): error TS2322: Type 'boolean | Boolean' is not assignable to type 'boolean'.
  Type 'Boolean' is not assignable to type 'boolean'.
    'boolean' is a primitive, but 'Boolean' is a wrapper object. Prefer using 'boolean' when possible.
utils/vectorDbProviders/milvus/index.ts(267,29): error TS2571: Object is of type 'unknown'.
utils/vectorDbProviders/milvus/index.ts(483,5): error TS2322: Type 'null' is not assignable to type '{ embedTextInput: (input: string) => Promise<number[]>; }'.
