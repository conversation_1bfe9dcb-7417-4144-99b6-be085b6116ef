/**
 * TypeScript type definitions for the Chat Flow system
 * Covers FlowOrchestrator, StageProcessors, and related infrastructure
 */

// External type imports
import type { Response } from "express";
// Use Node's native AbortSignal instead of abort-controller
// AbortSignal is now globally available in Node.js 14.17+ and TypeScript recognizes it

// Internal type imports
import type { BaseLLMProvider } from "../utils/helpers";
import type { Workspace, FilteredUser } from "./models";

// Type aliases for backward compatibility
// Use the proper model types instead of simplified interfaces
export type WorkspaceData = Workspace;
export type UserData = FilteredUser;

// Import the actual ContextWindowManager class type for use in this file
import type { ContextWindowManager } from "../utils/chats/helpers/contextWindowManager";
import type { TokenTracker } from "../utils/chats/helpers/tokenTracker";

// Re-export for external consumers
export type { ContextWindowManager } from "../utils/chats/helpers/contextWindowManager";
export type { TokenTracker } from "../utils/chats/helpers/tokenTracker";

// ==================== Core Flow Types ====================

export interface FlowOptions {
  chatId: string;
  response: Response;
  workspace: WorkspaceData;
  user: UserData;
  message: string;
  abortSignal?: AbortSignal;
  cdbOptions?: string[];
  [key: string]: unknown; // Allow additional options
}

export interface FlowConfig {
  flowType: string;
  stages: StageConfig[];
  prompts: FlowPrompts;
  [key: string]: unknown; // Allow additional config
}

export interface StageConfig {
  name: string;
  processor: new (...args: unknown[]) => StageProcessor;
  options?: Record<string, unknown>;
  [key: string]: unknown; // Allow additional config
}

export interface FlowPrompts {
  [key: string]: {
    SYSTEM_PROMPT?: string;
    USER_PROMPT?: string;
    PROMPT_TEMPLATE?: string;
    [key: string]: unknown;
  };
}

export interface DocumentDescription {
  "Doc Name": string;
  DisplayName: string;
  Description: string;
  DocumentType?: string;
}

export interface FlowContext {
  // Flow execution data
  documents: ProcessedDocument[];
  processedDocuments: ProcessedDocument[];
  docDescriptions: DocumentDescription[];
  sectionList: Section[];
  legalIssues: LegalIssue[];
  memos: Memo[];
  sectionOutputs: SectionOutput[];
  finalContent: string;
  metrics: FlowMetrics;
  errors: FlowError[];

  // Optional context data
  initialization?: InitializationData;
  [key: string]: unknown; // Allow additional context
}

export interface FlowMetrics {
  totalExecutionTime?: number;
  llmMetrics?: Record<string, unknown>;
  tokenUsageReport?: TokenUsageReport;

  // Specific stage metrics
  setup?: SetupStageMetrics;
  documentProcessing?: DocumentProcessingMetrics;
  sectionPlanning?: SectionPlanningMetrics;
  combination?: CombinationStageMetrics;
  agenticCombination?: CombinationStageMetrics;

  [stageName: string]: unknown; // Allow additional stage-specific metrics
}

export interface SetupStageMetrics {
  setupCompletedAt?: string;
  [key: string]: unknown;
}

export interface DocumentProcessingMetrics {
  processedDocuments?: number;
  totalDocuments?: number;
  [key: string]: unknown;
}

export interface SectionPlanningMetrics {
  sectionsGenerated?: number;
  sectionSource?: string;
  [key: string]: unknown;
}

export interface CombinationStageMetrics {
  finalWordCount?: number;
  wordChangeFromEditing?: number;
  [key: string]: unknown;
}

export interface TokenUsageReport {
  summary: Record<string, unknown>;
  utilizationRate: number;
  totalIterations: number;
  iterativeProcessingUsed: boolean;
  providerInfo: Record<string, unknown>;
  contextManager: Record<string, unknown>;
}

export interface FlowError {
  stage: string;
  error: string;
  timestamp: string;
  [key: string]: unknown;
}

export interface InitializationData {
  workspaceInfo: WorkspaceInfo;
  llmProviderInfo: LLMProviderInfo;
  availableContextWindow: number;
}

export interface WorkspaceInfo {
  slug: string;
  path: string;
  totalDocuments: number;
  validDocuments: number;
  errorDocuments: number;
  user: {
    id?: string | number;
    email?: string;
  };
}

export interface LLMProviderInfo {
  provider: string;
  model: string;
  temperature: number;
  contextWindowLimit: number | string;
  availableContextWindow: number;
}

// ==================== Stage Processor Types ====================

export interface StageProcessorOptions {
  prompts?: FlowPrompts;
  flowType?: string;
  [key: string]: unknown;
}

export interface StageDependencies {
  progressManager: ProgressManager;
  workspaceManager: WorkspaceManager;
  llmCoordinator: LLMCoordinator;
  contextWindowManager: ContextWindowManager;
  tokenTracker: TokenTracker;
  abortChecker: () => void;
  legalTask: string;
  customInstructions: string;
  chatId: string;
  workspace: WorkspaceData;
  user: UserData;
  options: FlowOptions;
  flowType: string;
}

// Alias for backward compatibility with test files
export type ProcessorDependencies = StageDependencies;

export interface StageProcessor {
  options: StageProcessorOptions;
  name: string;

  process(
    context: FlowContext,
    dependencies: StageDependencies
  ): Promise<Partial<FlowContext> | void>;

  execute(): Promise<unknown>;
  validateInputs?(context: FlowContext): boolean;
  shouldSkip?(context: FlowContext): boolean;
  getStageName(): string;
  checkAbort(abortSignal: AbortSignal | undefined, chatId: string): void;
  log(
    level: "info" | "warn" | "error",
    message: string,
    data?: Record<string, unknown>
  ): void;
}

// ==================== Progress Manager Types ====================

export interface ProgressData {
  step?: number;
  status?: "starting" | "in_progress" | "complete" | "error" | "aborted";
  message?: string;
  progress?: number; // -1 for loading, 0-100 for progress, -2 for error, -3 for aborted
  total?: number;
  subStep?: number;
  label?: string;
  data?: unknown;
  totalSteps?: number;
}

export interface ProgressManager {
  // Remove private fields from interface - only define methods
  setTotalSteps(total: number): void;
  sendProgress(data?: ProgressData): void;
  startStep(
    stepNumber: number,
    message: string,
    additionalData?: Record<string, unknown>
  ): void;
  updateStep(message: string, additionalData?: Record<string, unknown>): void;
  completeStep(message: string, data?: Record<string, unknown>): void;
  sendError(error: Error | string, step?: number | null): void;
  sendSubStepProgress(
    subStep: number,
    total: number,
    message: string,
    label?: string | null,
    progress?: number
  ): void;
  sendFinalCompletion(message: string, data?: Record<string, unknown>): void;
  sendFinalError(error: Error | string): void;
  sendAbort(): void;
}

// ==================== Workspace Manager Types ====================

export interface WorkspaceManagerOptions {
  extensions?: string[];
  filter?: (fileName: string) => boolean;
}

export interface DocumentFile {
  id: string;
  fileName: string;
  displayName: string;
  content: string;
  metadata: Record<string, unknown>;
  raw: unknown;
}

export interface DocumentLoadResult {
  documents: DocumentFile[];
  errors: Array<{ fileName: string; error: string }>;
  totalFiles: number;
  processedFiles: number;
  skippedFiles: number;
}

export interface WorkspaceManager {
  // Remove private fields from interface - only define methods
  initialize(): Promise<void>;
  validateWorkspace(): Promise<void>;
  ensureDocumentBuilderDirectory(): Promise<void>;
  getDocumentFiles(options?: WorkspaceManagerOptions): string[];
  readDocumentFile(fileName: string): DocumentFile;
  readDocumentFiles(
    fileNames: string[],
    options?: { skipErrors?: boolean }
  ): {
    documents: DocumentFile[];
    errors: Array<{ fileName: string; error: string }>;
  };
  getAllDocuments(options?: {
    skipErrors?: boolean;
    skipEmpty?: boolean;
  }): DocumentLoadResult;
  getWorkspacePath(): string;
  getDocumentBuilderPath(): string;
  documentExists(fileName: string): boolean;
  getWorkspaceInfo(): WorkspaceInfo;
}

// ==================== LLM Coordinator Types ====================

export interface LLMCoordinatorOptions {
  enableTokenTracking?: boolean;
  enableContextManagement?: boolean;
  maxIterations?: number;
  reservedOutputTokens?: number;
  logTokenUsage?: boolean;
}

export interface TokenBudgetParams {
  systemPrompt?: string;
  userPromptTemplate?: string;
  reservedTokens?: number;
}

export interface TokenBudget {
  totalTokens: number;
  reservedTokens: number;
  availableTokens: number;
  // Legacy compatibility properties
  availableForContent?: number; // Alias for availableTokens
}

export interface ChatCompletionResult {
  textResponse: string;
  metrics?: {
    prompt_tokens?: number;
    completion_tokens?: number;
    total_tokens?: number;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

export interface LLMCoordinator {
  // Remove private fields from interface - only define methods
  initialize(): Promise<void>;
  getConnector(): BaseLLMProvider;
  getContextManager(): ContextWindowManager | null;
  getTokenTracker(): TokenTracker | null;
  getTemperature(): number;
  getAvailableContextWindow(): number;
  calculateTokenBudget(params: TokenBudgetParams): TokenBudget;
  getChatCompletion(
    messages: unknown[],
    options?: Record<string, unknown>,
    stage?: string | null,
    operation?: string | null
  ): Promise<ChatCompletionResult>;
  compressMessages(messageParams: unknown): Promise<unknown[]>;
  getMetrics(): Record<string, unknown>;
  generateTokenReport(): Record<string, unknown>;
  resetMetrics(): void;
  getProviderInfo(): LLMProviderInfo;
}

// ==================== Document Processing Types ====================

export interface ProcessedDocument {
  id: string;
  fileName: string;
  displayName: string;
  content: string;
  metadata?: Record<string, unknown>;
  // Add compatibility fields for documentProcessing
  name?: string; // Alternative to fileName for compatibility
  DisplayName?: string; // Alternative capitalization
  "Doc Name"?: string; // Legacy field name
  Content?: string; // Alternative capitalization
  pageContent?: string; // Alternative content field
  tokens?: number; // Token count
  [key: string]: unknown;
}

export interface Section {
  sectionNumber: number;
  title: string;
  relevantDocumentNames: string[];
  identifiedLegalIssues?: LegalIssue[];
  [key: string]: unknown;
}

export interface LegalIssue {
  Issue: string;
  WORKSPACE_SLUG_FOR_LEGALDATA: string;
  [key: string]: unknown;
}

export interface Memo {
  id?: string;
  title?: string;
  content: string;
  memoFilePath?: string;
  [key: string]: unknown;
}

export interface SectionOutput {
  sectionNumber: number;
  title: string;
  content: string;
  relevantMemos?: Memo[];
  [key: string]: unknown;
}

// ==================== Flow Orchestrator Types ====================

export interface FlowOrchestratorStage {
  name: string;
  processor: StageProcessor;
  config: StageConfig;
}

export interface ExecutionSummary {
  flowType: string;
  chatId: string;
  stagesExecuted: number;
  documentsProcessed: number;
  sectionsGenerated: number;
  sectionsDrafted: number;
  errors: FlowError[];
  metrics: FlowMetrics;
  finalContentLength: number;
}

export interface FlowOrchestrator {
  options: FlowOptions;
  config: FlowConfig;
  stages: FlowOrchestratorStage[];
  context: FlowContext;

  // Infrastructure
  progressManager: ProgressManager | null;
  workspaceManager: WorkspaceManager | null;
  llmCoordinator: LLMCoordinator | null;
  abortChecker: (() => void) | null;

  // Key options
  chatId: string;
  response: Response;
  workspace: WorkspaceData;
  user: UserData;
  legalTask: string;
  abortSignal?: AbortSignal;
  customInstructions: string;
  flowType: string;

  initialize(): Promise<void>;
  execute(): Promise<string>;
  addStage(
    stageName: string,
    stageProcessor: StageProcessor,
    stageConfig?: Record<string, unknown>
  ): void;
  getContext(): FlowContext;
  updateContext(updates: Partial<FlowContext>): void;
  getInfrastructure(): {
    progressManager: ProgressManager | null;
    workspaceManager: WorkspaceManager | null;
    llmCoordinator: LLMCoordinator | null;
    abortChecker: (() => void) | null;
  };
  getExecutionSummary(): ExecutionSummary;
}

// ==================== Error Types ====================

export interface AbortError extends Error {
  isAbortSignal: boolean;
  isAbort?: boolean;
}

// ==================== Helper Function Types ====================

export type AbortChecker = () => void;

export interface AbortHandlerParams {
  error: AbortError;
  purgeDocumentBuilder: (options: { uuid: string }) => number;
  writeResponseChunk: (response: Response, data: unknown) => void;
  response: Response;
}
