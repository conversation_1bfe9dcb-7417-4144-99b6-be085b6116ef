// Utility type definitions

// Type-only imports
import type { Response } from "express";
import type { Application } from "express";
import type { Server } from "net";

// Boot utilities types
export interface BootOptions {
  app: Application;
  port?: number;
}

export interface BootResult {
  app: Application;
  server: Server | null;
}

// MetaGenerator types
export interface MetaTagDefinition {
  tag: "link" | "meta" | "title";
  props?: Record<string, string> | null;
  content?: string | null;
}

export interface MetaGeneratorInterface {
  clearConfig(): void;
  generate(response: Response, code?: number): Promise<void>;
}

// TextSplitter types
export interface JinaOptions {
  returnTokens?: boolean;
  returnChunks?: boolean;
  maxChunkLength?: number;
}

export interface TextSplitterConfig {
  method?: "basic" | "jina";
  chunkSize?: number;
  chunkOverlap?: number;
  chunkHeaderMeta?: Record<string, unknown> | null;
  splitByFilename?: string;
  jinaOptions?: JinaOptions;
}

export interface JinaSplitResult {
  chunks: string[];
  metadata: {
    num_tokens?: number;
    tokenizer?: string;
    usage?: number;
    num_chunks: number;
    chunk_positions?: unknown[];
    tokens?: unknown[];
  };
}

export interface SplitterInterface {
  _splitText(documentText: string): Promise<string[] | JinaSplitResult>;
}

// EncryptionManager types
export interface EncryptionManagerOptions {
  key?: string | null;
  salt?: string | null;
}

export interface EncryptionManagerInterface {
  xPayload: string;
  encrypt(plainTextString: string): string | null;
  decrypt(encryptedString: string): string | null;
}

// Communication Key types
export interface CommunicationKeyInterface {
  key: string;
  encrypt(data: unknown): string;
  decrypt<T = unknown>(encodedData: string): T;
}

// Background Worker types
export interface BackgroundServiceInterface {
  boot(): Promise<void>;
  stop(): Promise<void>;
}

export interface WorkerMetadata {
  name: string;
  [key: string]: unknown;
}

export interface WorkerMessage {
  message: string;
  name: string;
  [key: string]: unknown;
}

export interface GracefulInstance {
  listen(): void;
  stopBree(bree: BreeInstance, exitCode: number): void;
}

// Bree scheduler instance type
export interface BreeInstance {
  start(): Promise<void>;
  stop(): Promise<void>;
  run(name: string): Promise<void>;
  workers: Map<string, unknown>;
  [key: string]: unknown;
}

// Telemetry types
export interface TelemetryInterface {
  flush(): void;
}

// Logger types
export interface LoggerInterface {
  log(text: string, ...args: unknown[]): void;
  error(message: string, meta?: Record<string, unknown>): void;
  warn(text: string, ...args: unknown[]): void;
  info(message: string, meta?: Record<string, unknown>): void;
}

// Helper function types
export type DumpENVFunction = () => void;

// Database helper types
export interface DatabaseConfig {
  connectionString?: string;
  poolSize?: number;
  timeout?: number;
}

// Notification types
export interface NotificationOptions {
  channel?: string;
  webhook?: string;
  message: string;
}

// HTTP utility types
export interface HttpRequestOptions {
  method?: string;
  headers?: Record<string, string>;
  body?: string | Record<string, unknown> | FormData | URLSearchParams;
  timeout?: number;
}

// Password recovery types
export interface PasswordRecoveryOptions {
  email: string;
  token?: string;
  expiresIn?: number;
}

// Context types
export interface ContextualizationOptions {
  workspaceId: number;
  query: string;
  topN?: number;
  threshold?: number;
}

// DeepSearch types
export interface DeepSearchResult {
  text?: string;
  searchResults?: DeepSearchItem[];
  searchEntryPointRenderedContent?: string;
  rawResponse?: DeepSearchRawResponse;
  error?: string;
}

export interface DeepSearchRawResponse {
  status?: number;
  headers?: Record<string, string>;
  data?: unknown;
  [key: string]: unknown;
}

export interface DeepSearchItem {
  title: string;
  snippet: string;
  link: string;
}

export interface DeepSearchProvider {
  search(query: string): Promise<DeepSearchResult>;
}

export interface DeepSearchConfig {
  apiKey?: string;
  modelId?: string;
  language?: string;
  location?: {
    type: string;
    country: string;
    city: string;
    region: string;
  };
}

export interface DeepSearchSettings {
  enabled: boolean;
  provider: string;
  apiKey: string | null;
  modelId: string;
  contextPercentage: number;
}

// CollectorAPI types
export interface CollectorAPIConfig {
  baseUrl?: string;
  apiKey?: string;
}

export interface DocumentProcessingResult {
  success: boolean;
  documents?: ProcessedDocument[];
  error?: string;
}

export interface ProcessedDocument {
  id: string;
  content: string;
  metadata?: Record<string, unknown>;
  [key: string]: unknown;
}

// Prisma utility types
export interface PrismaPoolConfig {
  min?: number;
  max?: number;
  idleTimeoutMillis?: number;
  createTimeoutMillis?: number;
  acquireTimeoutMillis?: number;
}

// ComKey types
export interface ComKeyConfig {
  algorithm?: string;
  iterations?: number;
  keylen?: number;
  digest?: string;
}

// Embedding Reranker types
export interface DocumentToRerank {
  text: string;
  [key: string]: unknown;
}

export interface RerankOptions {
  topK?: number;
}

export interface RerankedDocument extends DocumentToRerank {
  rerank_corpus_id: number;
  rerank_score: number;
}

export interface EmbeddingReranker {
  rerank(
    query: string,
    documents: DocumentToRerank[],
    options?: RerankOptions
  ): Promise<RerankedDocument[]>;
  preload?(): Promise<void>;
}

// Transformers types
export interface TransformersProgressCallback {
  status: string;
  progress?: number;
}

export interface TransformersConfig {
  progress_callback?: (progress: TransformersProgressCallback) => void;
  cache_dir?: string;
}

export interface TransformersModel {
  (
    inputs: TransformerInput
  ): Promise<{ logits: { sigmoid(): { tolist(): number[][] } } }>;
}

export interface TransformerInput {
  input_ids: number[][];
  attention_mask: number[][];
  [key: string]: unknown;
}

export interface TransformersTokenizer {
  (
    queries: string[],
    options: { text_pair: string[]; padding: boolean; truncation: boolean }
  ): TransformerTokenizerOutput;
}

export interface TransformerTokenizerOutput {
  input_ids: number[][];
  attention_mask: number[][];
  [key: string]: unknown;
}

export interface TransformersModules {
  AutoModelForSequenceClassification: {
    from_pretrained(
      modelName: string,
      config: TransformersConfig
    ): Promise<TransformersModel>;
  };
  AutoTokenizer: {
    from_pretrained(
      modelName: string,
      config: TransformersConfig
    ): Promise<TransformersTokenizer>;
  };
  env: {
    remoteHost: string;
    remotePathTemplate: string;
  };
}

// Re-export document processing types
export * from "./document-processing";
