# TypeScript Dependencies to Add to package.json

## Required devDependencies:
```json
"typescript": "^5.3.3",
"@types/node": "^20.11.0",
"@types/express": "^4.17.21",
"@types/cors": "^2.8.17",
"@types/jsonwebtoken": "^9.0.5",
"@types/bcryptjs": "^2.4.6",
"@types/multer": "^1.4.11",
"@types/uuid": "^9.0.7",
"@types/jest": "^29.5.11",
"ts-jest": "^29.1.1",
"ts-node": "^10.9.2",
"ts-node-dev": "^2.0.0",
"@types/supertest": "^6.0.2",
"@types/node-cron": "^3.0.11",
"@types/swagger-ui-express": "^4.1.6",
"@types/joi": "^17.2.3"
```

## Installation Command (to be run manually):
```bash
npm install --save-dev typescript @types/node @types/express @types/cors @types/jsonwebtoken @types/bcryptjs @types/multer @types/uuid @types/jest ts-jest ts-node ts-node-dev @types/supertest @types/node-cron @types/swagger-ui-express @types/joi
```

## Update package.json scripts:
```json
"scripts": {
  "dev": "cross-env NODE_ENV=development ts-node-dev --respawn --transpile-only index.ts",
  "build": "tsc",
  "start": "NODE_ENV=production node dist/index.js",
  "typecheck": "tsc --noEmit",
  "test": "OPENAI_API_KEY=dummy_key jest",
  "lint": "npx prettier --ignore-path ../.prettierignore --check ./endpoints ./models ./utils index.ts && npx eslint ./endpoints ./models ./utils index.ts package.json --quiet"
}
```

## Jest Configuration Update:
Add to jest.config.js:
```javascript
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>'],
  testMatch: ['**/__tests__/**/*.ts', '**/?(*.)+(spec|test).ts'],
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
  moduleFileExtensions: ['ts', 'js', 'json', 'node'],
};
```