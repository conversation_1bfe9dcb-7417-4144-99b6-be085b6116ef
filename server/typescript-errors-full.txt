endpoints/admin.ts(6,10): error TS2614: Module '"../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../models/systemSettings"' instead?
endpoints/admin.ts(236,11): error TS6196: 'UserRequest' is declared but never used.
endpoints/admin.ts(242,17): error TS2323: Cannot redeclare exported variable 'adminEndpoints'.
endpoints/admin.ts(254,37): error TS2740: Type '{ users: FilteredUser[]; total: number; }' is missing the following properties from type 'any[]': length, pop, push, concat, and 29 more.
endpoints/admin.ts(270,26): error TS2352: Conversion of type 'FilteredUser | null' to type 'AuthenticatedUser' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'email' is missing in type 'FilteredUser' but required in type 'AuthenticatedUser'.
endpoints/admin.ts(296,52): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
endpoints/admin.ts(316,26): error TS2352: Conversion of type 'FilteredUser | null' to type 'AuthenticatedUser' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'email' is missing in type 'FilteredUser' but required in type 'AuthenticatedUser'.
endpoints/admin.ts(324,52): error TS2345: Argument of type 'FilteredUser | null' is not assignable to parameter of type 'UserWithRole'.
  Type 'null' is not assignable to type 'UserWithRole'.
endpoints/admin.ts(347,37): error TS2322: Type 'boolean | undefined' is not assignable to type 'boolean'.
  Type 'undefined' is not assignable to type 'boolean'.
endpoints/admin.ts(347,46): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
endpoints/admin.ts(363,26): error TS2352: Conversion of type 'FilteredUser | null' to type 'AuthenticatedUser' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'email' is missing in type 'FilteredUser' but required in type 'AuthenticatedUser'.
endpoints/admin.ts(370,52): error TS2345: Argument of type 'FilteredUser | null' is not assignable to parameter of type 'UserWithRole'.
  Type 'null' is not assignable to type 'UserWithRole'.
endpoints/admin.ts(372,55): error TS2322: Type 'string | undefined' is not assignable to type 'string | null'.
  Type 'undefined' is not assignable to type 'string | null'.
endpoints/admin.ts(380,23): error TS18047: 'user' is possibly 'null'.
endpoints/admin.ts(418,22): error TS2352: Conversion of type 'FilteredUser | null' to type 'AuthenticatedUser' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'email' is missing in type 'FilteredUser' but required in type 'AuthenticatedUser'.
endpoints/admin.ts(432,25): error TS18047: 'invite' is possibly 'null'.
endpoints/admin.ts(434,23): error TS18047: 'invite' is possibly 'null'.
endpoints/admin.ts(438,45): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
endpoints/admin.ts(461,46): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
endpoints/admin.ts(485,22): error TS2352: Conversion of type 'FilteredUser | null' to type 'AuthenticatedUser' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'email' is missing in type 'FilteredUser' but required in type 'AuthenticatedUser'.
endpoints/admin.ts(489,42): error TS2339: Property 'whereWithUsersLinkedWorkspaces' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/admin.ts(515,50): error TS2339: Property 'linkedWorkspaces' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/admin.ts(533,39): error TS2339: Property 'workspaceUsers' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/admin.ts(550,22): error TS2352: Conversion of type 'FilteredUser | null' to type 'AuthenticatedUser' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'email' is missing in type 'FilteredUser' but required in type 'AuthenticatedUser'.
endpoints/admin.ts(559,48): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
endpoints/admin.ts(581,52): error TS2339: Property 'updateUsers' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/admin.ts(615,52): error TS2339: Property 'updateLinkedWorkspaces' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/admin.ts(636,22): error TS2352: Conversion of type 'FilteredUser | null' to type 'AuthenticatedUser' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'email' is missing in type 'FilteredUser' but required in type 'AuthenticatedUser'.
endpoints/admin.ts(647,69): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/admin.ts(649,28): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/admin.ts(651,21): error TS2339: Property 'type' does not exist on type 'WorkspaceWithDocuments'.
endpoints/admin.ts(653,63): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/admin.ts(654,55): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/admin.ts(655,23): error TS2339: Property 'type' does not exist on type 'WorkspaceWithDocuments'.
endpoints/admin.ts(656,76): error TS2339: Property 'user_id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/admin.ts(656,97): error TS2339: Property 'slug' does not exist on type 'WorkspaceWithDocuments'.
endpoints/admin.ts(659,50): error TS2339: Property 'slug' does not exist on type 'WorkspaceWithDocuments'.
endpoints/admin.ts(662,17): error TS7053: Element implicitly has an 'any' type because expression of type '"delete-namespace"' can't be used to index type 'BaseVectorDatabaseProvider'.
  Property 'delete-namespace' does not exist on type 'BaseVectorDatabaseProvider'.
endpoints/admin.ts(662,69): error TS2339: Property 'slug' does not exist on type 'WorkspaceWithDocuments'.
endpoints/admin.ts(684,22): error TS2352: Conversion of type 'FilteredUser | null' to type 'AuthenticatedUser' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'email' is missing in type 'FilteredUser' but required in type 'AuthenticatedUser'.
endpoints/admin.ts(696,28): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/admin.ts(698,21): error TS2339: Property 'type' does not exist on type 'WorkspaceWithDocuments'.
endpoints/admin.ts(700,63): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/admin.ts(702,17): error TS7053: Element implicitly has an 'any' type because expression of type '"delete-namespace"' can't be used to index type 'BaseVectorDatabaseProvider'.
  Property 'delete-namespace' does not exist on type 'BaseVectorDatabaseProvider'.
endpoints/admin.ts(702,69): error TS2339: Property 'slug' does not exist on type 'WorkspaceWithDocuments'.
endpoints/admin.ts(754,32): error TS2352: Conversion of type 'UpdateLoginUIRequestBody' to type 'string' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
endpoints/admin.ts(759,11): error TS2322: Type 'Response<UpdateLoginUIResponse, Record<string, any>>' is not assignable to type 'void'.
endpoints/admin.ts(773,11): error TS2322: Type 'Response<UpdateLoginUIResponse, Record<string, any>>' is not assignable to type 'void'.
endpoints/admin.ts(1245,9): error TS2322: Type 'Response<ApiKeysResponse, Record<string, any>>' is not assignable to type 'void'.
endpoints/admin.ts(1267,22): error TS2352: Conversion of type 'FilteredUser | null' to type 'AuthenticatedUser' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'email' is missing in type 'FilteredUser' but required in type 'AuthenticatedUser'.
endpoints/admin.ts(1279,9): error TS2322: Type 'Response<GenerateApiKeyResponse, Record<string, any>>' is not assignable to type 'void'.
endpoints/admin.ts(1281,11): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
endpoints/admin.ts(1306,9): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/admin.ts(1331,59): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
endpoints/admin.ts(1352,11): error TS2322: Type 'Response<OrganizationsResponse, Record<string, any>>' is not assignable to type 'void'.
endpoints/admin.ts(1376,51): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
endpoints/admin.ts(1385,10): error TS2323: Cannot redeclare exported variable 'adminEndpoints'.
endpoints/admin.ts(1385,10): error TS2484: Export declaration conflicts with exported declaration of 'adminEndpoints'.
endpoints/agentWebsocket.ts(2,23): error TS7016: Could not find a declaration file for module 'ws'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/ws/index.js' implicitly has an 'any' type.
  Try `npm i --save-dev @types/ws` if it exists or add a new declaration (.d.ts) file containing `declare module 'ws';`
endpoints/agentWebsocket.ts(11,11): error TS6196: 'WebSocketWithBind' is declared but never used.
endpoints/agentWebsocket.ts(37,3): error TS2722: Cannot invoke an object which is possibly 'undefined'.
endpoints/agentWebsocket.ts(66,13): error TS18047: 'agentHandler.aibitat' is possibly 'null'.
endpoints/api/admin/index.ts(4,10): error TS2614: Module '"../../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../../models/systemSettings"' instead?
endpoints/api/admin/index.ts(101,53): error TS2740: Type '{ users: FilteredUser[]; total: number; }' is missing the following properties from type 'AdminUser[]': length, pop, push, concat, and 29 more.
endpoints/api/admin/index.ts(165,11): error TS2322: Type 'FilteredUser | undefined' is not assignable to type 'AdminUser | null'.
  Type 'undefined' is not assignable to type 'AdminUser | null'.
endpoints/api/admin/index.ts(236,65): error TS2345: Argument of type 'FilteredUser | null' is not assignable to parameter of type 'UserWithRole'.
  Type 'null' is not assignable to type 'UserWithRole'.
endpoints/api/admin/index.ts(248,52): error TS2322: Type 'boolean | undefined' is not assignable to type 'boolean'.
  Type 'undefined' is not assignable to type 'boolean'.
endpoints/api/admin/index.ts(303,15): error TS18047: 'user' is possibly 'null'.
endpoints/api/admin/index.ts(306,21): error TS18047: 'user' is possibly 'null'.
endpoints/api/admin/index.ts(426,55): error TS2322: Type '{ id: number; createdAt: Date; lastUpdatedAt: Date; createdBy: number; status: string; code: string; claimedBy: number | null; workspaceIds: string | null; usersUsage: string | null; countUsage: number; maxUsage: number; } | null' is not assignable to type 'AdminInvite | null'.
  Type '{ id: number; createdAt: Date; lastUpdatedAt: Date; createdBy: number; status: string; code: string; claimedBy: number | null; workspaceIds: string | null; usersUsage: string | null; countUsage: number; maxUsage: number; }' is not assignable to type 'AdminInvite'.
    Types of property 'claimedBy' are incompatible.
      Type 'number | null' is not assignable to type 'string | null | undefined'.
        Type 'number' is not assignable to type 'string'.
endpoints/api/admin/index.ts(532,39): error TS2339: Property 'workspaceUsers' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/api/admin/index.ts(596,52): error TS2339: Property 'updateUsers' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/api/admin/index.ts(666,52): error TS2339: Property 'updateLinkedWorkspaces' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/api/auth/index.ts(36,35): error TS2353: Object literal may only specify known properties, and 'authenticated' does not exist in type 'ApiResponse<{ authenticated: boolean; }>'.
endpoints/api/categories.ts(14,27): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'Application<Record<string, any>>'.
      Type '(request: Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>, response: Response<any, Record<string, any>>) => Promise<...>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.
endpoints/api/categories.ts(14,27): error TS7030: Not all code paths return a value.
endpoints/api/categories.ts(51,9): error TS2322: Type 'CategoryData | null' is not assignable to type 'CategoryData | undefined'.
  Type 'null' is not assignable to type 'CategoryData | undefined'.
endpoints/api/categories.ts(70,9): error TS2322: Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/category").CategoryData[]' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/api").CategoryData[]'.
  Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/category").CategoryData' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/api").CategoryData'.
    Types of property 'sub_category' are incompatible.
      Type 'string | null' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.
endpoints/api/categories.ts(84,30): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'Application<Record<string, any>>'.
      Type '(request: Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>, response: Response<any, Record<string, any>>) => Promise<...>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.
endpoints/api/categories.ts(84,30): error TS7030: Not all code paths return a value.
endpoints/api/categories.ts(108,9): error TS2322: Type 'CategoryData | null' is not assignable to type 'CategoryData | undefined'.
  Type 'null' is not assignable to type 'CategoryData | undefined'.
endpoints/api/categories.ts(121,33): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'Application<Record<string, any>>'.
      Type '(req: Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>, res: Response<any, Record<string, any>>) => Promise<...>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.
endpoints/api/categories.ts(121,33): error TS7030: Not all code paths return a value.
endpoints/api/categories.ts(129,11): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
endpoints/api/document/index.ts(53,11): error TS6196: 'DocumentListResponse' is declared but never used.
endpoints/api/document/index.ts(105,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: DocumentUploadRequest, response: Response) => Promise<void>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: DocumentUploadRequest, response: Response) => Promise<void>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Types of parameters 'request' and 'req' are incompatible.
          Property 'file' is missing in type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>' but required in type 'DocumentUploadRequest'.
endpoints/api/document/index.ts(199,17): error TS2339: Property 'success' does not exist on type 'false | ProcessDocumentResponse'.
endpoints/api/document/index.ts(199,26): error TS2339: Property 'reason' does not exist on type 'false | ProcessDocumentResponse'.
endpoints/api/document/index.ts(199,34): error TS2339: Property 'documents' does not exist on type 'false | ProcessDocumentResponse'.
endpoints/api/document/index.ts(213,19): error TS2341: Property 'log' is private and only accessible within class 'CollectorApi'.
endpoints/api/document/index.ts(436,34): error TS2558: Expected 0 type arguments, but got 1.
endpoints/api/document/index.ts(450,17): error TS2339: Property 'success' does not exist on type 'false | ProcessLinkResponse'.
endpoints/api/document/index.ts(450,26): error TS2339: Property 'reason' does not exist on type 'false | ProcessLinkResponse'.
endpoints/api/document/index.ts(450,34): error TS2339: Property 'documents' does not exist on type 'false | ProcessLinkResponse'.
endpoints/api/document/index.ts(464,19): error TS2341: Property 'log' is private and only accessible within class 'CollectorApi'.
endpoints/api/document/index.ts(549,19): error TS2558: Expected 0 type arguments, but got 1.
endpoints/api/document/index.ts(608,19): error TS2341: Property 'log' is private and only accessible within class 'CollectorApi'.
endpoints/api/document/index.ts(773,34): error TS2558: Expected 0 type arguments, but got 1.
endpoints/api/document/index.ts(846,35): error TS2558: Expected 0 type arguments, but got 1.
endpoints/api/document/index.ts(847,39): error TS7031: Binding element 'from' implicitly has an 'any' type.
endpoints/api/document/index.ts(848,61): error TS2353: Object literal may only specify known properties, and 'in' does not exist in type '{ contains?: string | undefined; }'.
endpoints/api/document/index.ts(851,14): error TS7031: Binding element 'from' implicitly has an 'any' type.
endpoints/api/document/index.ts(853,51): error TS7031: Binding element 'from' implicitly has an 'any' type.
endpoints/api/document/index.ts(853,57): error TS7031: Binding element 'to' implicitly has an 'any' type.
endpoints/api/docx-edit/index.ts(1,8): error TS6133: 'express' is declared but its value is never read.
endpoints/api/docx-edit/index.ts(17,8): error TS1192: Module '"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/i18n"' has no default export.
endpoints/api/docx-edit/index.ts(19,10): error TS2614: Module '"../../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../../models/systemSettings"' instead?
endpoints/api/docx-edit/index.ts(22,3): error TS6133: 'DocxUploadRequest' is declared but its value is never read.
endpoints/api/docx-edit/index.ts(79,15): error TS6133: '_user' is declared but its value is never read.
endpoints/api/docx-edit/index.ts(537,15): error TS6133: '_user' is declared but its value is never read.
endpoints/api/embed/index.ts(91,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: Request, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/api/embed/index.ts(91,5): error TS7030: Not all code paths return a value.
endpoints/api/embed/index.ts(163,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: Request, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/api/embed/index.ts(163,5): error TS7030: Not all code paths return a value.
endpoints/api/openai/index.ts(144,37): error TS2352: Conversion of type '{ id: string; type: "abort"; textResponse: null; sources: never[]; close: true; error: string; }' to type 'StreamResponseChunk' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'uuid' is missing in type '{ id: string; type: "abort"; textResponse: null; sources: never[]; close: true; error: string; }' but required in type 'StreamResponseChunk'.
endpoints/api/openai/index.ts(162,13): error TS2739: Type 'WorkspaceWithDocuments' is missing the following properties from type 'Workspace': id, slug
endpoints/api/openai/index.ts(174,25): error TS2339: Property 'chatProvider' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/openai/index.ts(180,39): error TS2339: Property 'name' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/openai/index.ts(181,35): error TS2339: Property 'chatModel' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/openai/index.ts(208,11): error TS2739: Type 'WorkspaceWithDocuments' is missing the following properties from type 'Workspace': id, slug
endpoints/api/openai/index.ts(222,37): error TS2339: Property 'name' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/openai/index.ts(223,33): error TS2339: Property 'chatModel' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/openai/index.ts(279,15): error TS7034: Variable 'data' implicitly has type 'any[]' in some locations where its type cannot be determined.
endpoints/api/openai/index.ts(290,11): error TS7005: Variable 'data' implicitly has an 'any[]' type.
endpoints/api/system/index.ts(3,10): error TS2614: Module '"../../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../../models/systemSettings"' instead?
endpoints/api/system/index.ts(7,3): error TS2724: '"../../../utils/helpers/chat/convertTo"' has no exported member named 'prepareWorkspaceChatsForExport'. Did you mean 'prepareChatsForExport'?
endpoints/api/system/index.ts(7,3): error TS6133: 'prepareWorkspaceChatsForExport' is declared but its value is never read.
endpoints/api/system/index.ts(30,34): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_req: Request, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'Application<Record<string, any>>'.
      Type '(_req: Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>, response: Response<any, Record<string, any>>) => Promise<...>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.
endpoints/api/system/index.ts(30,34): error TS7030: Not all code paths return a value.
endpoints/api/system/index.ts(171,64): error TS2322: Type 'string | false' is not assignable to type 'string | null'.
  Type 'boolean' is not assignable to type 'string'.
endpoints/api/system/index.ts(183,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request, response: Response) => Promise<Response<any, Record<string, any>>>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: Request, response: Response) => Promise<Response<any, Record<string, any>>>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/api/system/index.ts(213,11): error TS2345: Argument of type 'string' is not assignable to parameter of type 'ExportFormatType | undefined'.
endpoints/api/system/index.ts(313,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: Request, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/api/system/index.ts(313,5): error TS7030: Not all code paths return a value.
endpoints/api/system/index.ts(315,22): error TS2339: Property 'file' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
endpoints/api/system/index.ts(323,68): error TS2339: Property 'file' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
endpoints/api/userManagement/index.ts(84,53): error TS2339: Property 'map' does not exist on type '{ users: FilteredUser[]; total: number; }'.
endpoints/api/userManagement/index.ts(84,58): error TS7006: Parameter 'user' implicitly has an 'any' type.
endpoints/api/workspace/index.ts(33,1): error TS6133: 'ChatRecord' is declared but its value is never read.
endpoints/api/workspace/index.ts(345,46): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspace/index.ts(350,21): error TS2339: Property 'type' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspace/index.ts(356,37): error TS2339: Property 'name' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspace/index.ts(443,25): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspace/index.ts(507,69): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspace/index.ts(509,56): error TS2345: Argument of type 'WorkspaceChat[]' is not assignable to parameter of type 'ChatRecord[]'.
  Type 'WorkspaceChat' is not assignable to type 'ChatRecord'.
    Types of property 'feedbackScore' are incompatible.
      Type 'boolean | null | undefined' is not assignable to type 'number | null | undefined'.
        Type 'boolean' is not assignable to type 'number'.
endpoints/api/workspace/index.ts(587,36): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspace/index.ts(602,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: Request, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/api/workspace/index.ts(657,34): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspace/index.ts(677,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: Request, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/api/workspace/index.ts(734,34): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspace/index.ts(754,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: Request, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/api/workspace/index.ts(824,59): error TS2345: Argument of type 'string' is not assignable to parameter of type 'ChatMode'.
endpoints/api/workspace/index.ts(839,11): error TS2739: Type 'WorkspaceWithDocuments' is missing the following properties from type 'Workspace': id, slug
endpoints/api/workspace/index.ts(841,11): error TS2322: Type 'string' is not assignable to type '"query" | "chat" | undefined'.
endpoints/api/workspace/index.ts(850,13): error TS6133: 'LLMSelection' is declared but its value is never read.
endpoints/api/workspace/index.ts(865,23): error TS2339: Property 'chatProvider' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspace/index.ts(871,37): error TS2339: Property 'name' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspace/index.ts(872,33): error TS2339: Property 'chatModel' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspace/index.ts(984,59): error TS2345: Argument of type 'string' is not assignable to parameter of type 'ChatMode'.
endpoints/api/workspace/index.ts(1006,11): error TS2739: Type 'WorkspaceWithDocuments' is missing the following properties from type 'Workspace': id, slug
endpoints/api/workspace/index.ts(1008,11): error TS2322: Type 'string' is not assignable to type '"query" | "chat" | undefined'.
endpoints/api/workspace/index.ts(1017,13): error TS6133: 'LLMSelection' is declared but its value is never read.
endpoints/api/workspace/index.ts(1032,23): error TS2339: Property 'chatProvider' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspace/index.ts(1038,37): error TS2339: Property 'name' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspace/index.ts(1039,33): error TS2339: Property 'chatModel' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspaceThread/index.ts(48,11): error TS2430: Interface 'ThreadStreamChatRequestBody' incorrectly extends interface 'ThreadChatRequestBody'.
  Types of property 'llmSelected' are incompatible.
    Type 'number | null | undefined' is not assignable to type 'number | undefined'.
      Type 'null' is not assignable to type 'number | undefined'.
endpoints/api/workspaceThread/index.ts(157,37): error TS2339: Property 'name' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspaceThread/index.ts(228,25): error TS18047: 'workspace' is possibly 'null'.
endpoints/api/workspaceThread/index.ts(228,35): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspaceThread/index.ts(289,35): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspaceThread/index.ts(352,25): error TS18047: 'workspace' is possibly 'null'.
endpoints/api/workspaceThread/index.ts(352,35): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspaceThread/index.ts(362,36): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspaceThread/index.ts(371,35): error TS2352: Conversion of type '{ history: Promise<ChatMessage[]>; }' to type 'ThreadChatHistoryResponse' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Types of property 'history' are incompatible.
    Type 'Promise<ChatMessage[]>' is missing the following properties from type 'any[]': length, pop, push, concat, and 29 more.
endpoints/api/workspaceThread/index.ts(372,50): error TS2345: Argument of type 'WorkspaceChat[]' is not assignable to parameter of type 'ChatRecord[]'.
  Type 'WorkspaceChat' is not assignable to type 'ChatRecord'.
    Types of property 'feedbackScore' are incompatible.
      Type 'boolean | null | undefined' is not assignable to type 'number | null | undefined'.
        Type 'boolean' is not assignable to type 'number'.
endpoints/api/workspaceThread/index.ts(446,25): error TS18047: 'workspace' is possibly 'null'.
endpoints/api/workspaceThread/index.ts(446,35): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspaceThread/index.ts(461,59): error TS2345: Argument of type 'string' is not assignable to parameter of type 'ChatMode'.
endpoints/api/workspaceThread/index.ts(477,11): error TS2739: Type 'WorkspaceWithDocuments' is missing the following properties from type 'Workspace': id, slug
endpoints/api/workspaceThread/index.ts(479,11): error TS2322: Type 'string' is not assignable to type '"query" | "chat" | undefined'.
endpoints/api/workspaceThread/index.ts(480,11): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.
  Type 'FilteredUser' is not assignable to type 'User'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string'.
        Type 'null' is not assignable to type 'string'.
endpoints/api/workspaceThread/index.ts(504,37): error TS2339: Property 'name' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspaceThread/index.ts(505,33): error TS2339: Property 'chatModel' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspaceThread/index.ts(615,25): error TS18047: 'workspace' is possibly 'null'.
endpoints/api/workspaceThread/index.ts(615,35): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspaceThread/index.ts(630,59): error TS2345: Argument of type 'string' is not assignable to parameter of type 'ChatMode'.
endpoints/api/workspaceThread/index.ts(654,11): error TS2739: Type 'WorkspaceWithDocuments' is missing the following properties from type 'Workspace': id, slug
endpoints/api/workspaceThread/index.ts(656,11): error TS2322: Type 'string' is not assignable to type '"query" | "chat" | undefined'.
endpoints/api/workspaceThread/index.ts(657,11): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.
  Type 'FilteredUser' is not assignable to type 'User'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string'.
        Type 'null' is not assignable to type 'string'.
endpoints/api/workspaceThread/index.ts(686,37): error TS2339: Property 'name' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspaceThread/index.ts(687,33): error TS2339: Property 'chatModel' does not exist on type 'WorkspaceWithDocuments'.
endpoints/api/workspaceThread/index.ts(694,38): error TS2345: Argument of type 'StreamResponseChunk' is not assignable to parameter of type 'ResponseChunkData'.
  Types of property 'sources' are incompatible.
    Type 'any[] | undefined' is not assignable to type 'Source[]'.
      Type 'undefined' is not assignable to type 'Source[]'.
endpoints/api/workspaceThread/index.ts(694,38): error TS2352: Conversion of type '{ id: string; type: "abort"; textResponse: null; sources: never[]; close: true; error: string; }' to type 'StreamResponseChunk' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'uuid' is missing in type '{ id: string; type: "abort"; textResponse: null; sources: never[]; close: true; error: string; }' but required in type 'StreamResponseChunk'.
endpoints/autoCodePrompt.ts(15,11): error TS6196: 'AutoCodePromptRequest' is declared but never used.
endpoints/autoCodePrompt.ts(102,7): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/autoCodePrompt.ts(117,7): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/autoCodePrompt.ts(125,7): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/autoCodePrompt.ts(172,15): error TS2345: Argument of type 'number[]' is not assignable to parameter of type 'string[]'.
  Type 'number' is not assignable to type 'string'.
endpoints/autoCodePrompt.ts(232,7): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/autoCodePrompt.ts(251,7): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/autoCodePrompt.ts(302,21): error TS2345: Argument of type 'number[]' is not assignable to parameter of type 'string[]'.
  Type 'number' is not assignable to type 'string'.
endpoints/autoCodePrompt.ts(368,7): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/autoCodePrompt.ts(411,7): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/autoCodePrompt.ts(417,7): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/autoCodePrompt.ts(423,7): error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/browserExtension.ts(49,11): error TS6196: 'CheckConnectionResponse' is declared but never used.
endpoints/browserExtension.ts(56,11): error TS6196: 'ApiKeysResponse' is declared but never used.
endpoints/browserExtension.ts(62,11): error TS6196: 'CreateApiKeyResponse' is declared but never used.
endpoints/browserExtension.ts(67,11): error TS6196: 'SuccessResponse' is declared but never used.
endpoints/browserExtension.ts(72,11): error TS6196: 'WorkspacesResponse' is declared but never used.
endpoints/browserExtension.ts(77,7): error TS6133: '_getUser' is declared but its value is never read.
endpoints/browserExtension.ts(91,29): error TS2339: Property 'whereWithUser' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/browserExtension.ts(113,39): error TS2769: No overload matches this call.
  Overload 1 of 2, '(message?: string | undefined, options?: ErrorOptions | undefined): Error', gave the following error.
    Argument of type 'string | null' is not assignable to parameter of type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
  Overload 2 of 2, '(message?: string | undefined): Error', gave the following error.
    Argument of type 'string | null' is not assignable to parameter of type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
endpoints/browserExtension.ts(132,29): error TS2339: Property 'whereWithUser' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/browserExtension.ts(146,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/browserExtension.ts(146,5): error TS7030: Not all code paths return a value.
endpoints/browserExtension.ts(154,29): error TS2339: Property 'getWithUser' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/browserExtension.ts(161,15): error TS2345: Argument of type 'string | false' is not assignable to parameter of type 'boolean | undefined'.
  Type 'string' is not assignable to type 'boolean | undefined'.
endpoints/browserExtension.ts(193,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/browserExtension.ts(193,5): error TS7030: Not all code paths return a value.
endpoints/browserExtension.ts(248,40): error TS18047: 'apiKey' is possibly 'null'.
endpoints/browserExtension.ts(262,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/browserExtension.ts(262,5): error TS7030: Not all code paths return a value.
endpoints/browserExtension.ts(266,35): error TS18047: 'user' is possibly 'null'.
endpoints/browserExtension.ts(269,22): error TS18047: 'user' is possibly 'null'.
endpoints/browserExtension.ts(273,72): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.
endpoints/browserExtension.ts(274,39): error TS2769: No overload matches this call.
  Overload 1 of 2, '(message?: string | undefined, options?: ErrorOptions | undefined): Error', gave the following error.
    Argument of type 'string | null' is not assignable to parameter of type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
  Overload 2 of 2, '(message?: string | undefined): Error', gave the following error.
    Argument of type 'string | null' is not assignable to parameter of type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
endpoints/document.ts(42,18): error TS2694: Namespace 'global.Express' has no exported member 'Multer'.
endpoints/document.ts(810,39): error TS7031: Binding element 'from' implicitly has an 'any' type.
endpoints/document.ts(811,61): error TS2353: Object literal may only specify known properties, and 'in' does not exist in type '{ contains?: string | undefined; }'.
endpoints/document.ts(815,14): error TS7031: Binding element 'from' implicitly has an 'any' type.
endpoints/document.ts(818,51): error TS7031: Binding element 'from' implicitly has an 'any' type.
endpoints/document.ts(818,57): error TS7031: Binding element 'to' implicitly has an 'any' type.
endpoints/document.ts(880,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: FileUploadRequest, response: Response<AttachmentProcessResponse>) => Promise<Response<AttachmentProcessResponse, Record<string, any>>>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: FileUploadRequest, response: Response<AttachmentProcessResponse>) => Promise<Response<AttachmentProcessResponse, Record<string, any>>>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<AttachmentProcessResponse, Record<string, any>>>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<AttachmentProcessResponse, Record<string, any>>>' is not assignable to type 'Promise<void>'.
            Type 'Response<AttachmentProcessResponse, Record<string, any>>' is not assignable to type 'void'.
endpoints/document.ts(921,11): error TS2554: Expected 0-2 arguments, but got 3.
endpoints/document.ts(937,21): error TS2339: Property 'success' does not exist on type 'false | ProcessDocumentResponse'.
  Property 'success' does not exist on type 'false'.
endpoints/document.ts(940,27): error TS2339: Property 'reason' does not exist on type 'false | ProcessDocumentResponse'.
  Property 'reason' does not exist on type 'false'.
endpoints/document.ts(945,33): error TS2339: Property 'documents' does not exist on type 'false | ProcessDocumentResponse'.
  Property 'documents' does not exist on type 'false'.
endpoints/document.ts(1021,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request<{}, {}, AttachmentCleanupBody>, response: Response) => Promise<Response<any, Record<string, any>>>' is not assignable to parameter of type 'Application<Record<string, any>>'.
      Type '(request: Request<{}, {}, AttachmentCleanupBody, ParsedQs, Record<string, any>>, response: Response<any, Record<string, any>>) => Promise<...>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.
endpoints/document.ts(1102,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request<{ slug: string; }, {}, RenameFileBody>, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<{ slug: string; }, any, RenameFileBody, ParsedQs, Record<string, any>>'.
      Type '(request: Request<{ slug: string; }, {}, RenameFileBody>, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<{ slug: string; }, any, RenameFileBody, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/document.ts(1102,5): error TS7030: Not all code paths return a value.
endpoints/document.ts(1142,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: Request<{}, DocumentContentsResponse, {}, { path?: string; }>, res: Response<DocumentContentsResponse>) => Promise<void | Response<DocumentContentsResponse, Record<...>>>' is not assignable to parameter of type 'Application<Record<string, any>>'.
      Type '(req: Request<{}, DocumentContentsResponse, {}, { path?: string | undefined; }, Record<string, any>>, res: Response<DocumentContentsResponse, Record<...>>) => Promise<...>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.
endpoints/document.ts(1201,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: Request<{}, {}, {}, { path?: string; }>, res: Response) => Promise<void | Response<any, Record<string, any>>>' is not assignable to parameter of type 'Application<Record<string, any>>'.
      Type '(req: Request<{}, {}, {}, { path?: string | undefined; }, Record<string, any>>, res: Response<any, Record<string, any>>) => Promise<void | Response<...>>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.
endpoints/document.ts(1235,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: Request<{ filename: string; }>, res: Response) => Promise<void | Response<any, Record<string, any>>>' is not assignable to parameter of type 'Application<Record<string, any>>'.
      Type '(req: Request<{ filename: string; }, any, any, ParsedQs, Record<string, any>>, res: Response<any, Record<string, any>>) => Promise<...>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.
endpoints/document.ts(1258,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: Request<{ filename: string; }>, res: Response) => Promise<void | Response<any, Record<string, any>>>' is not assignable to parameter of type 'Application<Record<string, any>>'.
      Type '(req: Request<{ filename: string; }, any, any, ParsedQs, Record<string, any>>, res: Response<any, Record<string, any>>) => Promise<...>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.
endpoints/document.ts(1281,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: Request<{}, ResolvePathResponse, {}, { path?: string; }>, res: Response<ResolvePathResponse>) => Promise<Response<ResolvePathResponse, Record<string, any>>>' is not assignable to parameter of type 'Application<Record<string, any>>'.
      Type '(req: Request<{}, ResolvePathResponse, {}, { path?: string | undefined; }, Record<string, any>>, res: Response<ResolvePathResponse, Record<...>>) => Promise<...>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.
endpoints/document.ts(1291,17): error TS2345: Argument of type '{ success: false; error: string; }' is not assignable to parameter of type 'ResolvePathResponse'.
  Property 'checkedPaths' is missing in type '{ success: false; error: string; }' but required in type 'ResolvePathResponse'.
endpoints/document.ts(1372,39): error TS2345: Argument of type '{ success: false; error: string; }' is not assignable to parameter of type 'ResolvePathResponse'.
  Property 'checkedPaths' is missing in type '{ success: false; error: string; }' but required in type 'ResolvePathResponse'.
endpoints/embed/index.ts(76,48): error TS2345: Argument of type 'EmbedConfig' is not assignable to parameter of type 'EmbedConfig'.
  Type 'EmbedConfig' is missing the following properties from type 'EmbedConfig': chat_mode, allow_model_override, allow_prompt_override, allow_temperature_override
endpoints/embed/index.ts(108,11): error TS2353: Object literal may only specify known properties, and 'id' does not exist in type 'ResponseChunkData'.
endpoints/embedManagement.ts(52,22): error TS18047: 'embed' is possibly 'null'.
endpoints/embedManagement.ts(72,61): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.
endpoints/generateLegalTaskPrompt.ts(137,5): error TS2719: Type 'LLMProvider | null' is not assignable to type 'LLMProvider | null'. Two different types with this name exist, but they are unrelated.
  Type 'LLMProvider' is missing the following properties from type 'LLMProvider': model, isValidChatCompletionModel, promptWindowLimit, getChatCompletion
endpoints/generateLegalTaskPrompt.ts(229,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request<{}, GenerateLegalTaskPromptResponse, GenerateLegalTaskPromptRequestBody>, response: Response<GenerateLegalTaskPromptResponse>) => Promise<...>' is not assignable to parameter of type 'RequestHandlerParams<{}, GenerateLegalTaskPromptResponse, GenerateLegalTaskPromptRequestBody, ParsedQs, Record<...>>'.
      Type '(request: Request<{}, GenerateLegalTaskPromptResponse, GenerateLegalTaskPromptRequestBody>, response: Response<GenerateLegalTaskPromptResponse>) => Promise<...>' is not assignable to type 'RequestHandler<{}, GenerateLegalTaskPromptResponse, GenerateLegalTaskPromptRequestBody, ParsedQs, Record<...>>'.
        Type 'Promise<Response<GenerateLegalTaskPromptResponse, Record<string, any>>>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<GenerateLegalTaskPromptResponse, Record<string, any>>>' is not assignable to type 'Promise<void>'.
            Type 'Response<GenerateLegalTaskPromptResponse, Record<string, any>>' is not assignable to type 'void'.
endpoints/generateLegalTaskPrompt.ts(261,33): error TS2339: Property 'llmProvider' does not exist on type 'WorkspaceWithDocuments'.
endpoints/generateLegalTaskPrompt.ts(262,34): error TS2339: Property 'llmModelName' does not exist on type 'WorkspaceWithDocuments'.
endpoints/generateLegalTaskPrompt.ts(265,29): error TS2339: Property 'llmSettings' does not exist on type 'WorkspaceWithDocuments'.
endpoints/generateLegalTaskPrompt.ts(266,40): error TS2339: Property 'llmSettings' does not exist on type 'WorkspaceWithDocuments'.
endpoints/generateLegalTaskPrompt.ts(268,29): error TS2339: Property 'chatSettings' does not exist on type 'WorkspaceWithDocuments'.
endpoints/generateLegalTaskPrompt.ts(269,40): error TS2339: Property 'chatSettings' does not exist on type 'WorkspaceWithDocuments'.
endpoints/getCDBDocumentation.ts(31,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>>>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>>>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/invite.ts(97,45): error TS2345: Argument of type 'FilteredUser' is not assignable to parameter of type '{ id: number; createdAt: Date; lastUpdatedAt: Date; organizationId: number | null; username: string | null; password: string; pfpFilename: string | null; role: string; suspended: number; ... 4 more ...; custom_system_prompt: string | null; }'.
  Property 'password' is missing in type 'FilteredUser' but required in type '{ id: number; createdAt: Date; lastUpdatedAt: Date; organizationId: number | null; username: string | null; password: string; pfpFilename: string | null; role: string; suspended: number; ... 4 more ...; custom_system_prompt: string | null; }'.
endpoints/mcpServers.ts(208,11): error TS2322: Type '{ mcpServers: {}; } | null' is not assignable to type 'MCPServerData'.
  Type 'null' is not assignable to type 'MCPServerData'.
endpoints/news.ts(84,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/news.ts(84,5): error TS7030: Not all code paths return a value.
endpoints/news.ts(93,17): error TS2339: Property 'view' does not exist on type 'DismissResult'.
endpoints/news.ts(112,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/news.ts(112,5): error TS7030: Not all code paths return a value.
endpoints/news.ts(141,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/news.ts(141,5): error TS7030: Not all code paths return a value.
endpoints/news.ts(260,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/news.ts(260,5): error TS7030: Not all code paths return a value.
endpoints/news.ts(281,21): error TS18047: 'newsMessage' is possibly 'null'.
endpoints/news.ts(282,24): error TS18047: 'newsMessage' is possibly 'null'.
endpoints/news.ts(283,29): error TS18047: 'newsMessage' is possibly 'null'.
endpoints/news.ts(285,22): error TS18047: 'newsMessage' is possibly 'null'.
endpoints/news.ts(286,22): error TS18047: 'newsMessage' is possibly 'null'.
endpoints/news.ts(301,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/news.ts(301,5): error TS7030: Not all code paths return a value.
endpoints/news.ts(328,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/news.ts(328,5): error TS7030: Not all code paths return a value.
endpoints/requestLegalAssistance.ts(65,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request<{}, LegalAssistanceResponse, LegalAssistanceRequestBody>, response: Response<LegalAssistanceResponse>) => Promise<Response<LegalAssistanceResponse, Record<...>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<{}, LegalAssistanceResponse, LegalAssistanceRequestBody, ParsedQs, Record<string, any>>'.
      Type '(request: Request<{}, LegalAssistanceResponse, LegalAssistanceRequestBody>, response: Response<LegalAssistanceResponse>) => Promise<Response<LegalAssistanceResponse, Record<...>> | undefined>' is not assignable to type 'RequestHandler<{}, LegalAssistanceResponse, LegalAssistanceRequestBody, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<LegalAssistanceResponse, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<LegalAssistanceResponse, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<LegalAssistanceResponse, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<LegalAssistanceResponse, Record<string, any>>' is not assignable to type 'void'.
endpoints/requestLegalAssistance.ts(65,5): error TS7030: Not all code paths return a value.
endpoints/system.ts(6,21): error TS7016: Could not find a declaration file for module 'multer'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/multer/index.js' implicitly has an 'any' type.
  Try `npm i --save-dev @types/multer` if it exists or add a new declaration (.d.ts) file containing `declare module 'multer';`
endpoints/system.ts(20,10): error TS2305: Module '"../models/passwordRecovery"' has no exported member '_PasswordRecovery'.
endpoints/system.ts(21,10): error TS2724: '"../models/telemetry"' has no exported member named '_Telemetry'. Did you mean 'Telemetry'?
endpoints/system.ts(22,10): error TS2724: '"../models/apiKeys"' has no exported member named '_ApiKey'. Did you mean 'ApiKey'?
endpoints/system.ts(23,10): error TS2724: '"../models/workspaceChats"' has no exported member named '_WorkspaceChats'. Did you mean 'WorkspaceChats'?
endpoints/system.ts(30,28): error TS2724: '"../utils/helpers"' has no exported member named '_getLLMProvider'. Did you mean 'getLLMProvider'?
endpoints/system.ts(31,10): error TS6133: 'isMultiUserSetup' is declared but its value is never read.
endpoints/system.ts(34,10): error TS2724: '"../utils/EncryptionManager"' has no exported member named '_EncryptionManager'. Did you mean 'EncryptionManager'?
endpoints/system.ts(35,10): error TS2724: '"../utils/collectorApi"' has no exported member named '_CollectorApi'. Did you mean 'CollectorApi'?
endpoints/system.ts(36,10): error TS2724: '"../utils/DocumentManager"' has no exported member named '_DocumentManager'. Did you mean 'DocumentManager'?
endpoints/system.ts(39,21): error TS2305: Module '"../utils/files/logo"' has no exported member '_determineLogo'.
endpoints/system.ts(41,1): error TS6192: All imports in import declaration are unused.
endpoints/system.ts(49,7): error TS6133: '_VALID_CHAT_MODE' is declared but its value is never read.
endpoints/system.ts(50,7): error TS6133: '_DEFAULT_MANUAL_WORK_ESTIMATOR_PROMPT' is declared but its value is never read.
endpoints/system.ts(53,10): error TS6133: '_getDefaultDirectory' is declared but its value is never read.
endpoints/system.ts(58,16): error TS6133: '_getSystemPrompts' is declared but its value is never read.
endpoints/system.ts(111,28): error TS2339: Property 'migrateDefaults' does not exist on type '{ protectedFields: string[]; publicFields: string[]; supportedFields: string[]; validations: { login_ui: (value: any) => string; deep_search_context_percentage: (update: any) => number; footer_data: (updates: any) => string; ... 55 more ...; flow_referencefiles_compliance_threshold: (value: any) => string; }; ... 50...'.
endpoints/system.ts(145,30): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'Application<Record<string, any>>'.
      Type '(req: Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>, res: Response<any, Record<string, any>>) => Promise<...>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.
endpoints/system.ts(145,30): error TS7030: Not all code paths return a value.
endpoints/system.ts(162,37): error TS2554: Expected 2-3 arguments, but got 1.
endpoints/system.ts(170,39): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'Application<Record<string, any>>'.
      Type '(req: Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>, res: Response<any, Record<string, any>>) => Promise<...>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.
endpoints/system.ts(170,39): error TS7030: Not all code paths return a value.
endpoints/system.ts(189,38): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'Application<Record<string, any>>'.
      Type '(req: Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>, res: Response<any, Record<string, any>>) => Promise<...>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.
endpoints/system.ts(189,38): error TS7030: Not all code paths return a value.
endpoints/system.ts(209,52): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/system.ts(209,52): error TS7030: Not all code paths return a value.
endpoints/system.ts(226,52): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/system.ts(226,52): error TS7030: Not all code paths return a value.
endpoints/system.ts(233,27): error TS2552: Cannot find name 'req'. Did you mean '_req'?
endpoints/system.ts(246,60): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/system.ts(246,60): error TS7030: Not all code paths return a value.
endpoints/system.ts(254,28): error TS2554: Expected 2 arguments, but got 1.
endpoints/system.ts(254,69): error TS2339: Property 'delete' does not exist on type 'Promise<{ id: number; createdAt: Date; lastUpdatedAt: Date; label: string; value: string | null; }[]>'.
endpoints/system.ts(263,57): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/system.ts(263,57): error TS7030: Not all code paths return a value.
endpoints/system.ts(270,48): error TS2552: Cannot find name 'req'. Did you mean '_req'?
endpoints/system.ts(280,76): error TS2339: Property 'password' does not exist on type 'FilteredUser'.
endpoints/system.ts(286,25): error TS2345: Argument of type '{ id: any; }' is not assignable to parameter of type 'string | number'.
endpoints/system.ts(295,59): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/system.ts(295,59): error TS7030: Not all code paths return a value.
endpoints/system.ts(321,55): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/system.ts(321,55): error TS7030: Not all code paths return a value.
endpoints/system.ts(337,54): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/system.ts(337,54): error TS7030: Not all code paths return a value.
endpoints/system.ts(357,22): error TS2345: Argument of type 'LogoResult' is not assignable to parameter of type 'string'.
endpoints/system.ts(371,22): error TS2345: Argument of type 'LogoResult' is not assignable to parameter of type 'string'.
endpoints/system.ts(384,22): error TS2552: Cannot find name 'req'. Did you mean '_req'?
endpoints/system.ts(387,22): error TS2345: Argument of type 'PfpResult' is not assignable to parameter of type 'string'.
endpoints/system.ts(397,43): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/system.ts(397,43): error TS7030: Not all code paths return a value.
endpoints/system.ts(421,54): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/system.ts(421,54): error TS7030: Not all code paths return a value.
endpoints/system.ts(428,33): error TS2552: Cannot find name 'req'. Did you mean '_req'?
endpoints/system.ts(441,50): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/system.ts(441,50): error TS7030: Not all code paths return a value.
endpoints/system.ts(456,51): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/system.ts(456,51): error TS7030: Not all code paths return a value.
endpoints/system.ts(463,27): error TS2552: Cannot find name 'req'. Did you mean '_req'?
endpoints/system.ts(473,51): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/system.ts(473,51): error TS7030: Not all code paths return a value.
endpoints/system.ts(480,36): error TS2339: Property 'whereWithLimit' does not exist on type '{ logEvent: (event: string, metadata?: Record<string, any>, userId?: number | null) => Promise<EventLogResult>; getByEvent: (event: string, limit?: number | null, orderBy?: OrderByClause | null) => Promise<...>; ... 4 more ...; delete: (clause?: WhereClause) => Promise<...>; }'.
endpoints/system.ts(482,18): error TS2552: Cannot find name 'req'. Did you mean '_req'?
endpoints/system.ts(493,59): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/system.ts(493,59): error TS7030: Not all code paths return a value.
endpoints/system.ts(500,30): error TS2552: Cannot find name 'req'. Did you mean '_req'?
endpoints/system.ts(505,33): error TS2554: Expected 0-1 arguments, but got 2.
endpoints/system.ts(513,57): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/system.ts(513,57): error TS7030: Not all code paths return a value.
endpoints/system.ts(520,24): error TS2552: Cannot find name 'req'. Did you mean '_req'?
endpoints/system.ts(533,77): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/system.ts(540,43): error TS2552: Cannot find name 'req'. Did you mean '_req'?
endpoints/system.ts(551,42): error TS2552: Cannot find name 'req'. Did you mean '_req'?
endpoints/system.ts(561,67): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(_req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/system/customLegalTemplates.ts(2,10): error TS2614: Module '"../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../models/systemSettings"' instead?
endpoints/system/systemLegalTemplates.ts(85,32): error TS2345: Argument of type 'FilteredUser | null' is not assignable to parameter of type 'AuthenticatedUser | null'.
  Type 'FilteredUser' is not assignable to type 'AuthenticatedUser'.
    Types of property 'organizationId' are incompatible.
      Type 'number | null' is not assignable to type 'number | undefined'.
        Type 'null' is not assignable to type 'number | undefined'.
endpoints/system/systemLegalTemplates.ts(111,32): error TS2345: Argument of type 'FilteredUser | null' is not assignable to parameter of type 'AuthenticatedUser | null'.
  Type 'FilteredUser' is not assignable to type 'AuthenticatedUser'.
    Types of property 'organizationId' are incompatible.
      Type 'number | null' is not assignable to type 'number | undefined'.
        Type 'null' is not assignable to type 'number | undefined'.
endpoints/system/systemLegalTemplates.ts(146,15): error TS18047: 'template' is possibly 'null'.
endpoints/system/systemLegalTemplates.ts(172,32): error TS2345: Argument of type 'FilteredUser | null' is not assignable to parameter of type 'AuthenticatedUser | null'.
  Type 'FilteredUser' is not assignable to type 'AuthenticatedUser'.
    Types of property 'organizationId' are incompatible.
      Type 'number | null' is not assignable to type 'number | undefined'.
        Type 'null' is not assignable to type 'number | undefined'.
endpoints/system/systemLegalTemplates.ts(235,32): error TS2345: Argument of type 'FilteredUser | null' is not assignable to parameter of type 'AuthenticatedUser | null'.
  Type 'FilteredUser' is not assignable to type 'AuthenticatedUser'.
    Types of property 'organizationId' are incompatible.
      Type 'number | null' is not assignable to type 'number | undefined'.
        Type 'null' is not assignable to type 'number | undefined'.
endpoints/system/systemLegalTemplates.ts(273,32): error TS2345: Argument of type 'FilteredUser | null' is not assignable to parameter of type 'AuthenticatedUser | null'.
  Type 'FilteredUser' is not assignable to type 'AuthenticatedUser'.
    Types of property 'organizationId' are incompatible.
      Type 'number | null' is not assignable to type 'number | undefined'.
        Type 'null' is not assignable to type 'number | undefined'.
endpoints/upgradePrompt.ts(139,10): error TS6133: 'processResponse' is declared but its value is never read.
endpoints/userCustomAiSettings.ts(9,10): error TS2614: Module '"../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../models/systemSettings"' instead?
endpoints/userCustomAiSettings.ts(71,15): error TS2322: Type 'FilteredUser | null' is not assignable to type 'AuthenticatedUser | null'.
  Type 'FilteredUser' is not assignable to type 'AuthenticatedUser'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string'.
        Type 'null' is not assignable to type 'string'.
endpoints/userCustomAiSettings.ts(183,15): error TS2322: Type 'FilteredUser | null' is not assignable to type 'AuthenticatedUser | null'.
  Type 'FilteredUser' is not assignable to type 'AuthenticatedUser'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string'.
        Type 'null' is not assignable to type 'string'.
endpoints/userCustomSystemPrompt.ts(9,10): error TS2614: Module '"../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../models/systemSettings"' instead?
endpoints/userCustomSystemPrompt.ts(55,15): error TS2322: Type 'FilteredUser | null' is not assignable to type 'AuthenticatedUser | null'.
  Type 'FilteredUser' is not assignable to type 'AuthenticatedUser'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string'.
        Type 'null' is not assignable to type 'string'.
endpoints/userCustomSystemPrompt.ts(111,15): error TS2322: Type 'FilteredUser | null' is not assignable to type 'AuthenticatedUser | null'.
  Type 'FilteredUser' is not assignable to type 'AuthenticatedUser'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string'.
        Type 'null' is not assignable to type 'string'.
endpoints/userCustomSystemPrompt.ts(180,15): error TS2322: Type 'FilteredUser | null' is not assignable to type 'AuthenticatedUser | null'.
  Type 'FilteredUser' is not assignable to type 'AuthenticatedUser'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string'.
        Type 'null' is not assignable to type 'string'.
endpoints/userPromptLibrary.ts(80,15): error TS2322: Type 'FilteredUser | null' is not assignable to type 'AuthenticatedUser | null'.
  Type 'FilteredUser' is not assignable to type 'AuthenticatedUser'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string'.
        Type 'null' is not assignable to type 'string'.
endpoints/userPromptLibrary.ts(93,15): error TS2322: Type 'UserPromptLibraryData[]' is not assignable to type 'UserPromptData[]'.
  Type 'UserPromptLibraryData' is missing the following properties from type 'UserPromptData': createdAt, updatedAt
endpoints/userPromptLibrary.ts(120,15): error TS2322: Type 'FilteredUser | null' is not assignable to type 'AuthenticatedUser | null'.
  Type 'FilteredUser' is not assignable to type 'AuthenticatedUser'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string'.
        Type 'null' is not assignable to type 'string'.
endpoints/userPromptLibrary.ts(134,15): error TS2322: Type 'UserPromptLibraryData | null' is not assignable to type 'UserPromptData | null'.
  Type 'UserPromptLibraryData' is missing the following properties from type 'UserPromptData': createdAt, updatedAt
endpoints/userPromptLibrary.ts(168,15): error TS2322: Type 'FilteredUser | null' is not assignable to type 'AuthenticatedUser | null'.
  Type 'FilteredUser' is not assignable to type 'AuthenticatedUser'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string'.
        Type 'null' is not assignable to type 'string'.
endpoints/userPromptLibrary.ts(193,17): error TS2739: Type 'UserPromptLibraryData' is missing the following properties from type 'UserPromptData': createdAt, updatedAt
endpoints/userPromptLibrary.ts(232,15): error TS2322: Type 'FilteredUser | null' is not assignable to type 'AuthenticatedUser | null'.
  Type 'FilteredUser' is not assignable to type 'AuthenticatedUser'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string'.
        Type 'null' is not assignable to type 'string'.
endpoints/userPromptLibrary.ts(249,17): error TS2322: Type 'UserPromptLibraryData | null' is not assignable to type 'UserPromptData | null'.
  Type 'UserPromptLibraryData' is missing the following properties from type 'UserPromptData': createdAt, updatedAt
endpoints/userPromptLibrary.ts(294,15): error TS2322: Type 'FilteredUser | null' is not assignable to type 'AuthenticatedUser | null'.
  Type 'FilteredUser' is not assignable to type 'AuthenticatedUser'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string'.
        Type 'null' is not assignable to type 'string'.
endpoints/workspaces.ts(33,10): error TS2614: Module '"../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../models/systemSettings"' instead?
endpoints/workspaces.ts(42,18): error TS2694: Namespace 'global.Express' has no exported member 'Multer'.
endpoints/workspaces.ts(169,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request<{ slug?: string; slugModule: string; }, {}, CreateWorkspaceBody>, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<{ slug?: string | undefined; slugModule: string; }, any, CreateWorkspaceBody, ParsedQs, Record<string, any>>'.
      Type '(request: Request<{ slug?: string; slugModule: string; }, {}, CreateWorkspaceBody>, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<{ slug?: string | undefined; slugModule: string; }, any, CreateWorkspaceBody, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/workspaces.ts(169,5): error TS7030: Not all code paths return a value.
endpoints/workspaces.ts(189,31): error TS2339: Property 'getAllForUser' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/workspaces.ts(189,45): error TS18047: 'user' is possibly 'null'.
endpoints/workspaces.ts(190,31): error TS2339: Property 'getAll' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/workspaces.ts(248,11): error TS2345: Argument of type 'number | undefined' is not assignable to parameter of type 'string | null | undefined'.
  Type 'number' is not assignable to type 'string'.
endpoints/workspaces.ts(262,48): error TS2345: Argument of type 'FilteredUser | null' is not assignable to parameter of type 'AuthenticatedUser | null'.
  Property 'email' is missing in type 'FilteredUser' but required in type 'AuthenticatedUser'.
endpoints/workspaces.ts(270,48): error TS2345: Argument of type 'FilteredUser | null' is not assignable to parameter of type 'AuthenticatedUser | null'.
  Property 'email' is missing in type 'FilteredUser' but required in type 'AuthenticatedUser'.
endpoints/workspaces.ts(313,29): error TS2339: Property 'getWithUser' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/workspaces.ts(314,43): error TS2345: Argument of type 'string | false' is not assignable to parameter of type 'boolean | undefined'.
  Type 'string' is not assignable to type 'boolean | undefined'.
endpoints/workspaces.ts(320,25): error TS2339: Property 'trackChange' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/workspaces.ts(360,46): error TS18047: 'workspace' is possibly 'null'.
endpoints/workspaces.ts(360,56): error TS2339: Property 'user_id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/workspaces.ts(361,48): error TS2345: Argument of type '{ id: any; organization?: Organization; styleAlignmentEnabled?: boolean; activeStyleProfile?: UserStyleProfile | null; ... 11 more ...; custom_system_prompt?: string | ... 1 more ... | undefined; }' is not assignable to parameter of type 'AuthenticatedUser'.
  Property 'email' is missing in type '{ id: any; organization?: Organization; styleAlignmentEnabled?: boolean; activeStyleProfile?: UserStyleProfile | null; ... 11 more ...; custom_system_prompt?: string | ... 1 more ... | undefined; }' but required in type 'AuthenticatedUser'.
endpoints/workspaces.ts(363,46): error TS18047: 'workspace' is possibly 'null'.
endpoints/workspaces.ts(363,56): error TS2339: Property 'user_id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/workspaces.ts(364,51): error TS2345: Argument of type '{ id: any; organization?: Organization; styleAlignmentEnabled?: boolean; activeStyleProfile?: UserStyleProfile | null; ... 11 more ...; custom_system_prompt?: string | ... 1 more ... | undefined; }' is not assignable to parameter of type 'AuthenticatedUser'.
  Property 'email' is missing in type '{ id: any; organization?: Organization; styleAlignmentEnabled?: boolean; activeStyleProfile?: UserStyleProfile | null; ... 11 more ...; custom_system_prompt?: string | ... 1 more ... | undefined; }' but required in type 'AuthenticatedUser'.
endpoints/workspaces.ts(384,17): error TS2339: Property 'filename' does not exist on type 'false | ProcessDocumentResponse'.
endpoints/workspaces.ts(384,38): error TS2339: Property 'success' does not exist on type 'false | ProcessDocumentResponse'.
endpoints/workspaces.ts(384,47): error TS2339: Property 'reason' does not exist on type 'false | ProcessDocumentResponse'.
endpoints/workspaces.ts(384,55): error TS2339: Property 'documents' does not exist on type 'false | ProcessDocumentResponse'.
endpoints/workspaces.ts(399,19): error TS2341: Property 'log' is private and only accessible within class 'CollectorApi'.
endpoints/workspaces.ts(447,52): error TS2345: Argument of type 'FilteredUser | null' is not assignable to parameter of type 'AuthenticatedUser | null'.
  Property 'email' is missing in type 'FilteredUser' but required in type 'AuthenticatedUser'.
endpoints/workspaces.ts(469,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_request: Request<{ slug: string; }>, response: Response<ShareStatusResponse>) => Promise<Response<ShareStatusResponse, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<{ slug: string; }, any, any, ParsedQs, Record<string, any>>'.
      Type '(_request: Request<{ slug: string; }>, response: Response<ShareStatusResponse>) => Promise<Response<ShareStatusResponse, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<{ slug: string; }, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<ShareStatusResponse, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<ShareStatusResponse, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<ShareStatusResponse, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<ShareStatusResponse, Record<string, any>>' is not assignable to type 'void'.
endpoints/workspaces.ts(469,5): error TS7030: Not all code paths return a value.
endpoints/workspaces.ts(508,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request<{ slug: string; }, {}, ShareWorkspaceBody>, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<{ slug: string; }, any, ShareWorkspaceBody, ParsedQs, Record<string, any>>'.
      Type '(request: Request<{ slug: string; }, {}, ShareWorkspaceBody>, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<{ slug: string; }, any, ShareWorkspaceBody, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/workspaces.ts(508,5): error TS7030: Not all code paths return a value.
endpoints/workspaces.ts(518,41): error TS2339: Property 'isUserOwner' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/workspaces.ts(518,67): error TS18047: 'user' is possibly 'null'.
endpoints/workspaces.ts(521,46): error TS18047: 'user' is possibly 'null'.
endpoints/workspaces.ts(521,46): error TS2345: Argument of type 'string' is not assignable to parameter of type 'UserRole.ADMIN | UserRole.MANAGER | UserRole.SUPERUSER'.
endpoints/workspaces.ts(557,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request<{ slug: string; }, {}, RevokeShareBody>, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<{ slug: string; }, any, RevokeShareBody, ParsedQs, Record<string, any>>'.
      Type '(request: Request<{ slug: string; }, {}, RevokeShareBody>, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<{ slug: string; }, any, RevokeShareBody, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/workspaces.ts(557,5): error TS7030: Not all code paths return a value.
endpoints/workspaces.ts(567,41): error TS2339: Property 'isUserOwner' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/workspaces.ts(567,67): error TS18047: 'user' is possibly 'null'.
endpoints/workspaces.ts(570,46): error TS18047: 'user' is possibly 'null'.
endpoints/workspaces.ts(570,46): error TS2345: Argument of type 'string' is not assignable to parameter of type 'UserRole.ADMIN | UserRole.MANAGER | UserRole.SUPERUSER'.
endpoints/workspaces.ts(626,17): error TS2339: Property 'success' does not exist on type 'false | ProcessLinkResponse'.
endpoints/workspaces.ts(626,26): error TS2339: Property 'reason' does not exist on type 'false | ProcessLinkResponse'.
endpoints/workspaces.ts(632,19): error TS2341: Property 'log' is private and only accessible within class 'CollectorApi'.
endpoints/workspaces.ts(660,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request<{ slug?: string; }, {}, UpdateEmbeddingsBody, { includeDocuments?: string; }>, response: Response<WorkspaceUpdateResponse | BulkJobResponse>) => Promise<...>' is not assignable to parameter of type 'RequestHandlerParams<{ slug?: string | undefined; }, {}, UpdateEmbeddingsBody, { includeDocuments?: string | undefined; }, Record<string, any>>'.
      Type '(request: Request<{ slug?: string; }, {}, UpdateEmbeddingsBody, { includeDocuments?: string; }>, response: Response<WorkspaceUpdateResponse | BulkJobResponse>) => Promise<...>' is not assignable to type 'RequestHandler<{ slug?: string | undefined; }, {}, UpdateEmbeddingsBody, { includeDocuments?: string | undefined; }, Record<string, any>>'.
        Type 'Promise<Response<BulkJobResponse | WorkspaceUpdateResponse, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<BulkJobResponse | WorkspaceUpdateResponse, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<BulkJobResponse | WorkspaceUpdateResponse, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<BulkJobResponse | WorkspaceUpdateResponse, Record<string, any>>' is not assignable to type 'void'.
endpoints/workspaces.ts(660,5): error TS7030: Not all code paths return a value.
endpoints/workspaces.ts(675,29): error TS2339: Property 'getWithUser' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/workspaces.ts(676,43): error TS2345: Argument of type 'string | false' is not assignable to parameter of type 'boolean | undefined'.
  Type 'string' is not assignable to type 'boolean | undefined'.
endpoints/workspaces.ts(756,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request<{ slug: string; jobId: string; }>, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<{ slug: string; jobId: string; }, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: Request<{ slug: string; jobId: string; }>, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<{ slug: string; jobId: string; }, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/workspaces.ts(756,5): error TS7030: Not all code paths return a value.
endpoints/workspaces.ts(780,31): error TS2339: Property 'getWithUser' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/workspaces.ts(809,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request<{ slug: string; jobId: string; }>, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<{ slug: string; jobId: string; }, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: Request<{ slug: string; jobId: string; }>, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<{ slug: string; jobId: string; }, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/workspaces.ts(809,5): error TS7030: Not all code paths return a value.
endpoints/workspaces.ts(880,29): error TS2339: Property 'getWithUser' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/workspaces.ts(881,43): error TS2345: Argument of type 'string | false' is not assignable to parameter of type 'boolean | undefined'.
  Type 'string' is not assignable to type 'boolean | undefined'.
endpoints/workspaces.ts(912,17): error TS7053: Element implicitly has an 'any' type because expression of type '"delete-namespace"' can't be used to index type 'BaseVectorDatabaseProvider'.
  Property 'delete-namespace' does not exist on type 'BaseVectorDatabaseProvider'.
endpoints/workspaces.ts(937,29): error TS2339: Property 'getWithUser' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/workspaces.ts(938,43): error TS2345: Argument of type 'string | false' is not assignable to parameter of type 'boolean | undefined'.
  Type 'string' is not assignable to type 'boolean | undefined'.
endpoints/workspaces.ts(961,17): error TS7053: Element implicitly has an 'any' type because expression of type '"delete-namespace"' can't be used to index type 'BaseVectorDatabaseProvider'.
  Property 'delete-namespace' does not exist on type 'BaseVectorDatabaseProvider'.
endpoints/workspaces.ts(991,42): error TS2339: Property 'whereWithUser' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/workspaces.ts(1019,29): error TS2339: Property 'getWithUser' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/workspaces.ts(1020,43): error TS2345: Argument of type 'string | false' is not assignable to parameter of type 'boolean | undefined'.
  Type 'string' is not assignable to type 'boolean | undefined'.
endpoints/workspaces.ts(1043,29): error TS2339: Property 'getWithUser' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/workspaces.ts(1044,43): error TS2345: Argument of type 'string | false' is not assignable to parameter of type 'boolean | undefined'.
  Type 'string' is not assignable to type 'boolean | undefined'.
endpoints/workspaces.ts(1052,67): error TS18047: 'user' is possibly 'null'.
endpoints/workspaces.ts(1056,64): error TS2345: Argument of type 'WorkspaceChat[]' is not assignable to parameter of type 'ChatRecord[]'.
  Type 'WorkspaceChat' is not assignable to type 'ChatRecord'.
    Types of property 'feedbackScore' are incompatible.
      Type 'boolean | null | undefined' is not assignable to type 'number | null | undefined'.
        Type 'boolean' is not assignable to type 'number'.
endpoints/workspaces.ts(1182,13): error TS2698: Spread types may only be created from object types.
endpoints/workspaces.ts(1216,11): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.
endpoints/workspaces.ts(1294,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request<{ slug: string; }, {}, SuggestedMessagesBody>, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<{ slug: string; }, any, SuggestedMessagesBody, ParsedQs, Record<string, any>>'.
      Type '(request: Request<{ slug: string; }, {}, SuggestedMessagesBody>, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<{ slug: string; }, any, SuggestedMessagesBody, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/workspaces.ts(1294,5): error TS7030: Not all code paths return a value.
endpoints/workspaces.ts(1330,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request<{ slug: string; }, {}, UpdatePinBody>, response: Response) => Promise<Response<any, Record<string, any>>>' is not assignable to parameter of type 'RequestHandlerParams<{ slug: string; }, any, UpdatePinBody, ParsedQs, Record<string, any>>'.
      Type '(request: Request<{ slug: string; }, {}, UpdatePinBody>, response: Response) => Promise<Response<any, Record<string, any>>>' is not assignable to type 'RequestHandler<{ slug: string; }, any, UpdatePinBody, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/workspaces.ts(1408,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request<{ slug: string; }, {}, UpdatePdrBody>, response: Response) => Promise<Response<any, Record<string, any>>>' is not assignable to parameter of type 'RequestHandlerParams<{ slug: string; }, any, UpdatePdrBody, ParsedQs, Record<string, any>>'.
      Type '(request: Request<{ slug: string; }, {}, UpdatePdrBody>, response: Response) => Promise<Response<any, Record<string, any>>>' is not assignable to type 'RequestHandler<{ slug: string; }, any, UpdatePdrBody, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/workspaces.ts(1476,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: Request, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/workspaces.ts(1476,5): error TS7030: Not all code paths return a value.
endpoints/workspaces.ts(1479,48): error TS2339: Property 'updateOrders' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/workspaces.ts(1491,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request<{ slug: string; chatId: string; }, any, any, ParsedQs, Record<string, any>>, response: Response<any, Record<string, any>>) => Promise<...>' is not assignable to parameter of type 'RequestHandlerParams<{ slug: string; chatId: string; }, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: Request<{ slug: string; chatId: string; }, any, any, ParsedQs, Record<string, any>>, response: Response<any, Record<string, any>>) => Promise<...>' is not assignable to type 'RequestHandler<{ slug: string; chatId: string; }, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/workspaces.ts(1491,5): error TS7030: Not all code paths return a value.
endpoints/workspaces.ts(1513,36): error TS18047: 'wsChat' is possibly 'null'.
endpoints/workspaces.ts(1513,60): error TS2339: Property 'text' does not exist on type 'never'.
endpoints/workspaces.ts(1517,42): error TS2339: Property 'ttsBuffer' does not exist on type 'TTSProvider'.
endpoints/workspaces.ts(1562,35): error TS2322: Type 'Buffer<ArrayBufferLike> | null' is not assignable to type 'Buffer<ArrayBufferLike>'.
  Type 'null' is not assignable to type 'Buffer<ArrayBufferLike>'.
endpoints/workspaces.ts(1583,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: FileUploadRequest & Request<{ slug: string; }, any, any, ParsedQs, Record<string, any>>, response: Response<any, Record<string, any>>) => Promise<...>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary & { slug: string; }, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: FileUploadRequest & Request<{ slug: string; }, any, any, ParsedQs, Record<string, any>>, response: Response<any, Record<string, any>>) => Promise<...>' is not assignable to type 'RequestHandler<ParamsDictionary & { slug: string; }, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/workspaces.ts(1583,5): error TS7030: Not all code paths return a value.
endpoints/workspaces.ts(1598,32): error TS18047: 'workspaceRecord' is possibly 'null'.
endpoints/workspaces.ts(1598,48): error TS2339: Property 'pfpFilename' does not exist on type 'WorkspaceWithDocuments'.
endpoints/workspaces.ts(1603,27): error TS18047: 'workspaceRecord' is possibly 'null'.
endpoints/workspaces.ts(1603,43): error TS2339: Property 'pfpFilename' does not exist on type 'WorkspaceWithDocuments'.
endpoints/workspaces.ts(1611,11): error TS18047: 'workspaceRecord' is possibly 'null'.
endpoints/workspaces.ts(1611,27): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/workspaces.ts(1632,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request<{ slug: string; }, any, any, ParsedQs, Record<string, any>>, response: Response<any, Record<string, any>>) => Promise<...>' is not assignable to parameter of type 'RequestHandlerParams<{ slug: string; }, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: Request<{ slug: string; }, any, any, ParsedQs, Record<string, any>>, response: Response<any, Record<string, any>>) => Promise<...>' is not assignable to type 'RequestHandler<{ slug: string; }, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/workspaces.ts(1632,5): error TS7030: Not all code paths return a value.
endpoints/workspaces.ts(1638,32): error TS18047: 'workspaceRecord' is possibly 'null'.
endpoints/workspaces.ts(1638,48): error TS2339: Property 'pfpFilename' does not exist on type 'WorkspaceWithDocuments'.
endpoints/workspaces.ts(1652,11): error TS18047: 'workspaceRecord' is possibly 'null'.
endpoints/workspaces.ts(1652,27): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/workspaces.ts(1654,13): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
endpoints/workspaces.ts(1687,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: FileUploadRequest & Request<{ slug?: string | undefined; slugModule: string; }, {}, {}, { includeDocuments?: string | undefined; }, Record<string, any>>, response: Response<...>) => Promise<...>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary & { slug?: string | undefined; slugModule: string; }, any, any, ParsedQs & { includeDocuments?: string | undefined; }, Record<string, any>>'.
      Type '(request: FileUploadRequest & Request<{ slug?: string | undefined; slugModule: string; }, {}, {}, { includeDocuments?: string | undefined; }, Record<string, any>>, response: Response<...>) => Promise<...>' is not assignable to type 'RequestHandler<ParamsDictionary & { slug?: string | undefined; slugModule: string; }, any, any, ParsedQs & { includeDocuments?: string | undefined; }, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/workspaces.ts(1687,5): error TS7030: Not all code paths return a value.
endpoints/workspaces.ts(1703,29): error TS2339: Property 'getWithUser' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/workspaces.ts(1704,43): error TS2345: Argument of type 'string | false' is not assignable to parameter of type 'boolean | undefined'.
  Type 'string' is not assignable to type 'boolean | undefined'.
endpoints/workspaces.ts(1710,39): error TS18047: 'user' is possibly 'null'.
endpoints/workspaces.ts(1735,46): error TS18047: 'workspace' is possibly 'null'.
endpoints/workspaces.ts(1735,56): error TS2339: Property 'user_id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/workspaces.ts(1736,48): error TS2345: Argument of type '{ id: any; organization?: Organization; styleAlignmentEnabled?: boolean; activeStyleProfile?: UserStyleProfile | null; ... 11 more ...; custom_system_prompt?: string | ... 1 more ... | undefined; }' is not assignable to parameter of type 'AuthenticatedUser'.
  Property 'email' is missing in type '{ id: any; organization?: Organization; styleAlignmentEnabled?: boolean; activeStyleProfile?: UserStyleProfile | null; ... 11 more ...; custom_system_prompt?: string | ... 1 more ... | undefined; }' but required in type 'AuthenticatedUser'.
endpoints/workspaces.ts(1738,46): error TS18047: 'workspace' is possibly 'null'.
endpoints/workspaces.ts(1738,56): error TS2339: Property 'user_id' does not exist on type 'WorkspaceWithDocuments'.
endpoints/workspaces.ts(1739,51): error TS2345: Argument of type '{ id: any; organization?: Organization; styleAlignmentEnabled?: boolean; activeStyleProfile?: UserStyleProfile | null; ... 11 more ...; custom_system_prompt?: string | ... 1 more ... | undefined; }' is not assignable to parameter of type 'AuthenticatedUser'.
  Property 'email' is missing in type '{ id: any; organization?: Organization; styleAlignmentEnabled?: boolean; activeStyleProfile?: UserStyleProfile | null; ... 11 more ...; custom_system_prompt?: string | ... 1 more ... | undefined; }' but required in type 'AuthenticatedUser'.
endpoints/workspaces.ts(1742,17): error TS2339: Property 'success' does not exist on type 'false | ProcessDocumentResponse'.
endpoints/workspaces.ts(1742,26): error TS2339: Property 'reason' does not exist on type 'false | ProcessDocumentResponse'.
endpoints/workspaces.ts(1742,34): error TS2339: Property 'documents' does not exist on type 'false | ProcessDocumentResponse'.
endpoints/workspaces.ts(1751,19): error TS2341: Property 'log' is private and only accessible within class 'CollectorApi'.
endpoints/workspaces.ts(1795,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request<{ slug?: string | undefined; }, {}, RemoveAndUnembedBody, { includeDocuments?: string | undefined; }, Record<string, any>>, response: Response<...>) => Promise<...>' is not assignable to parameter of type 'RequestHandlerParams<{ slug?: string | undefined; }, any, RemoveAndUnembedBody, { includeDocuments?: string | undefined; }, Record<string, any>>'.
      Type '(request: Request<{ slug?: string | undefined; }, {}, RemoveAndUnembedBody, { includeDocuments?: string | undefined; }, Record<string, any>>, response: Response<...>) => Promise<...>' is not assignable to type 'RequestHandler<{ slug?: string | undefined; }, any, RemoveAndUnembedBody, { includeDocuments?: string | undefined; }, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/workspaces.ts(1795,5): error TS7030: Not all code paths return a value.
endpoints/workspaces.ts(1810,29): error TS2339: Property 'getWithUser' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/workspaces.ts(1811,43): error TS2345: Argument of type 'string | false' is not assignable to parameter of type 'boolean | undefined'.
  Type 'string' is not assignable to type 'boolean | undefined'.
endpoints/workspaces.ts(1830,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request<{ slug: string; }, {}, ThreadForkBody>, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<{ slug: string; }, any, ThreadForkBody, ParsedQs, Record<string, any>>'.
      Type '(request: Request<{ slug: string; }, {}, ThreadForkBody>, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<{ slug: string; }, any, ThreadForkBody, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/workspaces.ts(1830,5): error TS7030: Not all code paths return a value.
endpoints/workspaces.ts(1869,29): error TS2339: Property 'text' does not exist on type '{}'.
endpoints/workspaces.ts(1869,66): error TS2339: Property 'text' does not exist on type '{}'.
endpoints/workspaces.ts(1905,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request<{ id: string; }>, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<{ id: string; }, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: Request<{ id: string; }>, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<{ id: string; }, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/workspaces.ts(1905,5): error TS7030: Not all code paths return a value.
endpoints/workspaces.ts(1934,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request<{ slug: string; }, {}, SuggestedMessagesBody>, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to parameter of type 'RequestHandlerParams<{ slug: string; }, any, SuggestedMessagesBody, ParsedQs, Record<string, any>>'.
      Type '(request: Request<{ slug: string; }, {}, SuggestedMessagesBody>, response: Response) => Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'RequestHandler<{ slug: string; }, any, SuggestedMessagesBody, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>> | undefined>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>> | undefined' is not assignable to type 'void'.
              Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
endpoints/workspaces.ts(1934,5): error TS7030: Not all code paths return a value.
endpoints/workspaces.ts(1996,29): error TS2339: Property 'getPopulatedWorkspaces' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/workspaces.ts(1997,29): error TS2339: Property 'getPopulatedWorkspaces' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
endpoints/workspaceThreads.ts(6,3): error TS6133: 'safeJsonParse' is declared but its value is never read.
endpoints/workspaceThreads.ts(21,10): error TS2724: '"../models/user"' has no exported member named 'users'. Did you mean 'User'?
endpoints/workspaceThreads.ts(42,11): error TS2430: Interface 'DeleteThreadRequest' incorrectly extends interface 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
  Types of property 'params' are incompatible.
    Type 'WorkspaceThreadParams' is not assignable to type 'ParamsDictionary'.
      Index signature for type 'string' is missing in type 'WorkspaceThreadParams'.
endpoints/workspaceThreads.ts(46,11): error TS2430: Interface 'UpdateThreadRequest' incorrectly extends interface 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
  Types of property 'params' are incompatible.
    Type 'WorkspaceThreadParams' is not assignable to type 'ParamsDictionary'.
      Index signature for type 'string' is missing in type 'WorkspaceThreadParams'.
endpoints/workspaceThreads.ts(54,11): error TS2430: Interface 'ShareThreadRequest' incorrectly extends interface 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
  Types of property 'params' are incompatible.
    Type 'WorkspaceThreadParams' is not assignable to type 'ParamsDictionary'.
      Index signature for type 'string' is missing in type 'WorkspaceThreadParams'.
endpoints/workspaceThreads.ts(110,11): error TS6196: 'ValidatedRequest' is declared but never used.
endpoints/workspaceThreads.ts(130,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: CreateThreadRequest, response: ValidatedResponse) => Promise<void>' is not assignable to parameter of type 'RequestHandlerParams<Pick<WorkspaceThreadParams, "slug">, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: CreateThreadRequest, response: ValidatedResponse) => Promise<void>' is not assignable to type 'RequestHandler<Pick<WorkspaceThreadParams, "slug">, any, any, ParsedQs, Record<string, any>>'.
        Types of parameters 'response' and 'res' are incompatible.
          Type 'Response<any, Record<string, any>, number>' is not assignable to type 'ValidatedResponse'.
            Types of property 'locals' are incompatible.
              Property 'workspace' is missing in type 'Record<string, any> & Locals' but required in type '{ workspace: WorkspaceData; thread?: ThreadData | undefined; }'.
endpoints/workspaceThreads.ts(166,11): error TS2345: Argument of type 'number | undefined' is not assignable to parameter of type 'string | null | undefined'.
  Type 'number' is not assignable to type 'string'.
endpoints/workspaceThreads.ts(188,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: ThreadsListRequest, response: ValidatedResponse) => Promise<void>' is not assignable to parameter of type 'RequestHandlerParams<Pick<WorkspaceThreadParams, "slug">, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: ThreadsListRequest, response: ValidatedResponse) => Promise<void>' is not assignable to type 'RequestHandler<Pick<WorkspaceThreadParams, "slug">, any, any, ParsedQs, Record<string, any>>'.
        Types of parameters 'response' and 'res' are incompatible.
          Type 'Response<any, Record<string, any>, number>' is not assignable to type 'ValidatedResponse'.
            Types of property 'locals' are incompatible.
              Property 'workspace' is missing in type 'Record<string, any> & Locals' but required in type '{ workspace: WorkspaceData; thread?: ThreadData | undefined; }'.
endpoints/workspaceThreads.ts(203,15): error TS6133: '_userInfo' is declared but its value is never read.
endpoints/workspaceThreads.ts(255,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: DeleteThreadRequest, response: ValidatedResponse) => Promise<void>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: DeleteThreadRequest, response: ValidatedResponse) => Promise<void>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Types of parameters 'request' and 'req' are incompatible.
          Type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>' is not assignable to type 'DeleteThreadRequest'.
            Types of property 'params' are incompatible.
              Property 'slug' is missing in type 'ParamsDictionary' but required in type 'WorkspaceThreadParams'.
endpoints/workspaceThreads.ts(260,44): error TS2345: Argument of type 'DeleteThreadRequest' is not assignable to parameter of type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
  Types of property 'params' are incompatible.
    Type 'WorkspaceThreadParams' is not assignable to type 'ParamsDictionary'.
      Index signature for type 'string' is missing in type 'WorkspaceThreadParams'.
endpoints/workspaceThreads.ts(299,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request, response: ValidatedResponse) => Promise<void>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: Request, response: ValidatedResponse) => Promise<void>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Types of parameters 'response' and 'res' are incompatible.
          Type 'Response<any, Record<string, any>, number>' is not assignable to type 'ValidatedResponse'.
            Types of property 'locals' are incompatible.
              Property 'workspace' is missing in type 'Record<string, any> & Locals' but required in type '{ workspace: WorkspaceData; thread?: ThreadData | undefined; }'.
endpoints/workspaceThreads.ts(336,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request, response: ValidatedResponse) => Promise<void>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: Request, response: ValidatedResponse) => Promise<void>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Types of parameters 'response' and 'res' are incompatible.
          Type 'Response<any, Record<string, any>, number>' is not assignable to type 'ValidatedResponse'.
            Types of property 'locals' are incompatible.
              Property 'workspace' is missing in type 'Record<string, any> & Locals' but required in type '{ workspace: WorkspaceData; thread?: ThreadData | undefined; }'.
endpoints/workspaceThreads.ts(360,56): error TS2339: Property 'forThread' does not exist on type '{ new: ({ workspaceId, prompt, response, user, threadId, include, apiSessionId, invoice_ref, metrics, }: WorkspaceChatData) => Promise<CreateChatResult>; forWorkspaceByUser: (workspaceId?: number | null, userId?: number | null, limit?: number | null, orderBy?: any) => Promise<...>; ... 11 more ...; bulkCreate: (chat...'.
endpoints/workspaceThreads.ts(363,35): error TS2352: Conversion of type '{ history: Promise<ChatMessage[]>; }' to type 'ChatHistoryResponse' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Types of property 'history' are incompatible.
    Type 'Promise<ChatMessage[]>' is missing the following properties from type 'ChatData[]': length, pop, push, concat, and 29 more.
endpoints/workspaceThreads.ts(378,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: UpdateThreadRequest, response: ValidatedResponse) => Promise<void>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, { name?: string | undefined; slug?: string | undefined; }, ParsedQs, Record<string, any>>'.
      Type '(request: UpdateThreadRequest, response: ValidatedResponse) => Promise<void>' is not assignable to type 'RequestHandler<ParamsDictionary, any, { name?: string | undefined; slug?: string | undefined; }, ParsedQs, Record<string, any>>'.
        Types of parameters 'request' and 'req' are incompatible.
          Type 'Request<ParamsDictionary, any, { name?: string | undefined; slug?: string | undefined; }, ParsedQs, Record<string, any>>' is not assignable to type 'UpdateThreadRequest'.
            Types of property 'params' are incompatible.
              Property 'slug' is missing in type 'ParamsDictionary' but required in type 'WorkspaceThreadParams'.
endpoints/workspaceThreads.ts(383,44): error TS2345: Argument of type 'UpdateThreadRequest' is not assignable to parameter of type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
  Types of property 'params' are incompatible.
    Type 'WorkspaceThreadParams' is not assignable to type 'ParamsDictionary'.
      Index signature for type 'string' is missing in type 'WorkspaceThreadParams'.
endpoints/workspaceThreads.ts(384,54): error TS2345: Argument of type 'UpdateThreadRequest' is not assignable to parameter of type 'RequestWithBody'.
  Types of property 'params' are incompatible.
    Type 'WorkspaceThreadParams' is not assignable to type 'ParamsDictionary'.
      Index signature for type 'string' is missing in type 'WorkspaceThreadParams'.
endpoints/workspaceThreads.ts(406,35): error TS2352: Conversion of type '{ thread: WorkspaceThreadResult; }' to type 'ThreadResponse' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Types of property 'thread' are incompatible.
    Type 'WorkspaceThreadResult' is missing the following properties from type 'ThreadData': id, name, slug, workspace_id, and 2 more.
endpoints/workspaceThreads.ts(421,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request, response: ValidatedResponse) => Promise<void>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: Request, response: ValidatedResponse) => Promise<void>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Types of parameters 'response' and 'res' are incompatible.
          Type 'Response<any, Record<string, any>, number>' is not assignable to type 'ValidatedResponse'.
            Types of property 'locals' are incompatible.
              Property 'workspace' is missing in type 'Record<string, any> & Locals' but required in type '{ workspace: WorkspaceData; thread?: ThreadData | undefined; }'.
endpoints/workspaceThreads.ts(456,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: ShareThreadRequest, response: ValidatedResponse) => Promise<void>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, { username: string; }, ParsedQs, Record<string, any>>'.
      Type '(request: ShareThreadRequest, response: ValidatedResponse) => Promise<void>' is not assignable to type 'RequestHandler<ParamsDictionary, any, { username: string; }, ParsedQs, Record<string, any>>'.
        Types of parameters 'request' and 'req' are incompatible.
          Type 'Request<ParamsDictionary, any, { username: string; }, ParsedQs, Record<string, any>>' is not assignable to type 'ShareThreadRequest'.
            Types of property 'params' are incompatible.
              Property 'slug' is missing in type 'ParamsDictionary' but required in type 'WorkspaceThreadParams'.
endpoints/workspaceThreads.ts(461,44): error TS2345: Argument of type 'ShareThreadRequest' is not assignable to parameter of type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
  Types of property 'params' are incompatible.
    Type 'WorkspaceThreadParams' is not assignable to type 'ParamsDictionary'.
      Index signature for type 'string' is missing in type 'WorkspaceThreadParams'.
endpoints/workspaceThreads.ts(462,38): error TS2345: Argument of type 'ShareThreadRequest' is not assignable to parameter of type 'RequestWithBody'.
  Types of property 'params' are incompatible.
    Type 'WorkspaceThreadParams' is not assignable to type 'ParamsDictionary'.
      Index signature for type 'string' is missing in type 'WorkspaceThreadParams'.
endpoints/workspaceThreads.ts(526,5): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(request: Request, response: ValidatedResponse) => Promise<void>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(request: Request, response: ValidatedResponse) => Promise<void>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Types of parameters 'response' and 'res' are incompatible.
          Type 'Response<any, Record<string, any>, number>' is not assignable to type 'ValidatedResponse'.
            Types of property 'locals' are incompatible.
              Property 'workspace' is missing in type 'Record<string, any> & Locals' but required in type '{ workspace: WorkspaceData; thread?: ThreadData | undefined; }'.
endpoints/workspaceThreads.ts(552,52): error TS2339: Property 'fork' does not exist on type 'WorkspaceThreadModelStatic'.
endpoints/workspaceThreads.ts(599,35): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
index.ts(13,18): error TS7016: Could not find a declaration file for module 'cors'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/cors/lib/index.js' implicitly has an 'any' type.
  Try `npm i --save-dev @types/cors` if it exists or add a new declaration (.d.ts) file containing `declare module 'cors';`
index.ts(53,53): error TS2306: File '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/endpoints/system/organizationLegalTemplates.ts' is not a module.
index.ts(81,16): error TS2345: Argument of type 'string | 3001' is not assignable to parameter of type 'number | undefined'.
  Type 'string' is not assignable to type 'number'.
index.ts(89,17): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.
  Type 'IRouter' is missing the following properties from type 'ExpressApp': request, response, init, defaultConfiguration, and 32 more.
index.ts(91,20): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.
  Type 'IRouter' is missing the following properties from type 'ExpressApp': request, response, init, defaultConfiguration, and 32 more.
index.ts(92,26): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.
  Type 'IRouter' is missing the following properties from type 'ExpressApp': request, response, init, defaultConfiguration, and 32 more.
index.ts(93,15): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.
  Type 'IRouter' is missing the following properties from type 'ExpressApp': request, response, init, defaultConfiguration, and 32 more.
index.ts(94,16): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'Express'.
  Type 'IRouter' is missing the following properties from type 'Express': request, response, init, defaultConfiguration, and 32 more.
index.ts(95,25): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'Application'.
  Type 'IRouter' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 30 more.
index.ts(96,17): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.
  Type 'IRouter' is missing the following properties from type 'ExpressApp': request, response, init, defaultConfiguration, and 32 more.
index.ts(97,26): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'Express'.
  Type 'IRouter' is missing the following properties from type 'Express': request, response, init, defaultConfiguration, and 32 more.
index.ts(98,15): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'Application'.
  Type 'IRouter' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 30 more.
index.ts(99,19): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.
  Type 'IRouter' is missing the following properties from type 'ExpressApp': request, response, init, defaultConfiguration, and 32 more.
index.ts(100,16): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.
  Type 'IRouter' is missing the following properties from type 'ExpressApp': request, response, init, defaultConfiguration, and 32 more.
index.ts(110,19): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.
  Type 'IRouter' is missing the following properties from type 'ExpressApp': request, response, init, defaultConfiguration, and 32 more.
index.ts(111,21): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.
  Type 'IRouter' is missing the following properties from type 'ExpressApp': request, response, init, defaultConfiguration, and 32 more.
index.ts(125,19): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.
  Type 'IRouter' is missing the following properties from type 'ExpressApp': request, response, init, defaultConfiguration, and 32 more.
index.ts(128,27): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.
  Type 'IRouter' is missing the following properties from type 'ExpressApp': request, response, init, defaultConfiguration, and 32 more.
index.ts(129,15): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'Application'.
  Type 'IRouter' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 30 more.
index.ts(130,25): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'Express'.
  Type 'IRouter' is missing the following properties from type 'Express': request, response, init, defaultConfiguration, and 32 more.
index.ts(136,36): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.
  Type 'IRouter' is missing the following properties from type 'ExpressApp': request, response, init, defaultConfiguration, and 32 more.
index.ts(142,29): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'Application'.
  Type 'IRouter' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 30 more.
index.ts(148,33): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.
  Type 'IRouter' is missing the following properties from type 'ExpressApp': request, response, init, defaultConfiguration, and 32 more.
index.ts(151,28): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.
  Type 'IRouter' is missing the following properties from type 'ExpressApp': request, response, init, defaultConfiguration, and 32 more.
index.ts(154,15): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'Express'.
  Type 'IRouter' is missing the following properties from type 'Express': request, response, init, defaultConfiguration, and 32 more.
index.ts(160,30): error TS2345: Argument of type 'Router' is not assignable to parameter of type 'ExpressApp'.
  Type 'IRouter' is missing the following properties from type 'ExpressApp': request, response, init, defaultConfiguration, and 32 more.
index.ts(207,31): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type 'BaseVectorDatabaseProvider'.
  No index signature with a parameter of type 'string' was found on type 'BaseVectorDatabaseProvider'.
index.ts(228,46): error TS2345: Argument of type 'string | 3001' is not assignable to parameter of type 'number | undefined'.
  Type 'string' is not assignable to type 'number'.
jobs/bulk-document-processor.ts(46,20): error TS6133: '_RETRY_ATTEMPTS' is declared but its value is never read.
jobs/sync-watched.documents.ts(35,34): error TS2551: Property 'workspace' does not exist on type '{ id: number; createdAt: Date; lastUpdatedAt: Date; workspaceId: number; pdr: boolean | null; docId: string; filename: string; docpath: string; metadata: string | null; pinned: boolean | null; watched: boolean | null; starred: boolean | null; } & { ...; }'. Did you mean 'workspaces'?
jobs/sync-watched.documents.ts(39,67): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'ValidFileType'.
  Type 'null' is not assignable to type 'ValidFileType'.
jobs/sync-watched.documents.ts(121,57): error TS2345: Argument of type 'DocumentSyncQueueWithRelations' is not assignable to parameter of type 'DocumentSyncQueueData'.
  Type 'DocumentSyncQueueWithRelations' is missing the following properties from type 'DocumentSyncQueueData': repeatFailures, lastUpdatedAt
jobs/sync-watched.documents.ts(173,11): error TS2322: Type '{ not: number; }' is not assignable to type 'number'.
jobs/sync-watched.documents.ts(208,58): error TS2345: Argument of type 'DocumentSyncQueueWithRelations' is not assignable to parameter of type 'DocumentSyncQueueData'.
  Type 'DocumentSyncQueueWithRelations' is missing the following properties from type 'DocumentSyncQueueData': repeatFailures, lastUpdatedAt
models/browserExtensionApiKey.ts(2,10): error TS2614: Module '"./systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "./systemSettings"' instead?
models/browserExtensionApiKey.ts(4,10): error TS2724: '"@prisma/client"' has no exported member named 'User'. Did you mean 'users'?
models/browserExtensionApiKey.ts(75,9): error TS2322: Type '{ key: string; user_id: number | null; }' is not assignable to type '(Without<browser_extension_api_keysCreateInput, browser_extension_api_keysUncheckedCreateInput> & browser_extension_api_keysUncheckedCreateInput) | (Without<...> & browser_extension_api_keysCreateInput)'.
  Type '{ key: string; user_id: number | null; }' is not assignable to type 'Without<browser_extension_api_keysCreateInput, browser_extension_api_keysUncheckedCreateInput> & browser_extension_api_keysUncheckedCreateInput'.
    Property 'lastUpdatedAt' is missing in type '{ key: string; user_id: number | null; }' but required in type 'browser_extension_api_keysUncheckedCreateInput'.
models/cacheData.ts(2,10): error TS2724: '"@prisma/client"' has no exported member named 'CacheData'. Did you mean 'cache_data'?
models/documents.ts(171,26): error TS2339: Property 'chunkSource' does not exist on type 'never'.
models/documents.ts(173,16): error TS2339: Property 'chunkSource' does not exist on type 'never'.
models/documents.ts(174,16): error TS2339: Property 'chunkSource' does not exist on type 'never'.
models/documents.ts(331,11): error TS6133: '_VectorDb' is declared but its value is never read.
models/documents.ts(927,33): error TS18047: 'doc.metadata' is possibly 'null'.
models/documentSyncQueue.ts(3,10): error TS2614: Module '"./systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "./systemSettings"' instead?
models/documentSyncQueue.ts(176,7): error TS2739: Type '{ id: number; createdAt: Date; staleAfterMs: number; nextSyncAt: Date; lastSyncedAt: Date; workspaceDocId: number; }' is missing the following properties from type 'DocumentSyncQueueData': repeatFailures, lastUpdatedAt
models/documentSyncQueue.ts(236,7): error TS2322: Type '{ id: number; createdAt: Date; staleAfterMs: number; nextSyncAt: Date; lastSyncedAt: Date; workspaceDocId: number; } | null' is not assignable to type 'DocumentSyncQueueData | null'.
  Type '{ id: number; createdAt: Date; staleAfterMs: number; nextSyncAt: Date; lastSyncedAt: Date; workspaceDocId: number; }' is missing the following properties from type 'DocumentSyncQueueData': repeatFailures, lastUpdatedAt
models/documentSyncQueue.ts(250,66): error TS2345: Argument of type '{ include?: IncludeClause | undefined; orderBy?: OrderByClause | undefined; take?: number | undefined; where: WhereClause; }' is not assignable to parameter of type '{ select?: document_sync_queuesSelect<DefaultArgs> | null | undefined; include?: document_sync_queuesInclude<DefaultArgs> | null | undefined; ... 5 more ...; distinct?: Document_sync_queuesScalarFieldEnum | ... 1 more ... | undefined; }'.
  Types of property 'include' are incompatible.
    Type 'IncludeClause | undefined' is not assignable to type 'document_sync_queuesInclude<DefaultArgs> | null | undefined'.
      Type 'IncludeClause' has no properties in common with type 'document_sync_queuesInclude<DefaultArgs>'.
models/documentSyncQueue.ts(256,7): error TS2322: Type '{ id: number; createdAt: Date; staleAfterMs: number; nextSyncAt: Date; lastSyncedAt: Date; workspaceDocId: number; }[]' is not assignable to type 'DocumentSyncQueueData[]'.
  Type '{ id: number; createdAt: Date; staleAfterMs: number; nextSyncAt: Date; lastSyncedAt: Date; workspaceDocId: number; }' is missing the following properties from type 'DocumentSyncQueueData': repeatFailures, lastUpdatedAt
models/documentSyncQueue.ts(311,12): error TS2352: Conversion of type 'DocumentSyncQueueData[]' to type 'DocumentSyncQueueWithRelations[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'DocumentSyncQueueData' is missing the following properties from type 'DocumentSyncQueueWithRelations': workspaceDoc, lastSyncedAt
models/embedChats.ts(100,16): error TS2741: Property 'lastUpdatedAt' is missing in type '{ include: boolean; id: number; createdAt: Date; prompt: string; response: string; session_id: string; connection_information: string | null; embed_id: number; usersId: number | null; }' but required in type 'EmbedChatData'.
models/embedChats.ts(125,7): error TS2322: Type '{ include: boolean; id: number; createdAt: Date; prompt: string; response: string; session_id: string; connection_information: string | null; embed_id: number; usersId: number | null; }[]' is not assignable to type 'EmbedChatData[]'.
  Property 'lastUpdatedAt' is missing in type '{ include: boolean; id: number; createdAt: Date; prompt: string; response: string; session_id: string; connection_information: string | null; embed_id: number; usersId: number | null; }' but required in type 'EmbedChatData'.
models/embedChats.ts(165,7): error TS2322: Type '{ include: boolean; id: number; createdAt: Date; prompt: string; response: string; session_id: string; connection_information: string | null; embed_id: number; usersId: number | null; } | null' is not assignable to type 'EmbedChatData | null'.
  Property 'lastUpdatedAt' is missing in type '{ include: boolean; id: number; createdAt: Date; prompt: string; response: string; session_id: string; connection_information: string | null; embed_id: number; usersId: number | null; }' but required in type 'EmbedChatData'.
models/embedChats.ts(197,7): error TS2322: Type '{ include: boolean; id: number; createdAt: Date; prompt: string; response: string; session_id: string; connection_information: string | null; embed_id: number; usersId: number | null; }[]' is not assignable to type 'EmbedChatData[]'.
  Property 'lastUpdatedAt' is missing in type '{ include: boolean; id: number; createdAt: Date; prompt: string; response: string; session_id: string; connection_information: string | null; embed_id: number; usersId: number | null; }' but required in type 'EmbedChatData'.
models/embedChats.ts(214,11): error TS2561: Object literal may only specify known properties, but 'embed_config' does not exist in type 'embed_chatsInclude<DefaultArgs>'. Did you mean to write 'embed_configs'?
models/embedChats.ts(228,14): error TS2352: Conversion of type '{ include: boolean; id: number; createdAt: Date; prompt: string; response: string; session_id: string; connection_information: string | null; embed_id: number; usersId: number | null; }[]' to type 'EmbedChatWithWorkspace[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type '{ include: boolean; id: number; createdAt: Date; prompt: string; response: string; session_id: string; connection_information: string | null; embed_id: number; usersId: number | null; }' is missing the following properties from type 'EmbedChatWithWorkspace': embed_config, lastUpdatedAt
models/embedConfig.ts(216,16): error TS2741: Property 'lastUpdatedAt' is missing in type '{ id: number; createdAt: Date; usersId: number | null; uuid: string; enabled: boolean; chat_mode: string; allowlist_domains: string | null; allow_model_override: boolean; allow_temperature_override: boolean; ... 4 more ...; createdBy: number | null; }' but required in type 'EmbedConfigData'.
models/embedConfig.ts(259,7): error TS2322: Type '{ id: number; createdAt: Date; usersId: number | null; uuid: string; enabled: boolean; chat_mode: string; allowlist_domains: string | null; allow_model_override: boolean; allow_temperature_override: boolean; ... 4 more ...; createdBy: number | null; } | null' is not assignable to type 'EmbedConfigData | null'.
  Property 'lastUpdatedAt' is missing in type '{ id: number; createdAt: Date; usersId: number | null; uuid: string; enabled: boolean; chat_mode: string; allowlist_domains: string | null; allow_model_override: boolean; allow_temperature_override: boolean; ... 4 more ...; createdBy: number | null; }' but required in type 'EmbedConfigData'.
models/embedConfig.ts(277,7): error TS2322: Type '({ workspaces: { id: number; name: string; createdAt: Date; lastUpdatedAt: Date; pfpFilename: string | null; user_id: number; type: string | null; slug: string; sharedWithOrg: boolean; ... 18 more ...; order: number; }; } & { ...; }) | null' is not assignable to type 'EmbedConfigWithWorkspace | null'.
  Type '{ workspaces: { id: number; name: string; createdAt: Date; lastUpdatedAt: Date; pfpFilename: string | null; user_id: number; type: string | null; slug: string; sharedWithOrg: boolean; ... 18 more ...; order: number; }; } & { ...; }' is not assignable to type 'EmbedConfigWithWorkspace | null'.
    Property 'lastUpdatedAt' is missing in type '{ workspaces: { id: number; name: string; createdAt: Date; lastUpdatedAt: Date; pfpFilename: string | null; user_id: number; type: string | null; slug: string; sharedWithOrg: boolean; ... 18 more ...; order: number; }; } & { ...; }' but required in type 'EmbedConfigWithWorkspace'.
models/embedConfig.ts(287,9): error TS2322: Type 'WhereClause' is not assignable to type 'embed_configsWhereUniqueInput'.
  Type 'WhereClause' is not assignable to type '{ id: string | number; uuid: string | number; } & { id?: number | undefined; uuid?: string | undefined; AND?: embed_configsWhereInput | embed_configsWhereInput[] | undefined; ... 16 more ...; workspaces?: (Without<...> & workspacesWhereInput) | ... 1 more ... | undefined; }'.
    Type 'WhereClause' is not assignable to type '{ id: string | number; uuid: string | number; }'.
      Types of property 'id' are incompatible.
        Type 'number | undefined' is not assignable to type 'string | number'.
          Type 'undefined' is not assignable to type 'string | number'.
models/embedConfig.ts(307,7): error TS2322: Type '{ id: number; createdAt: Date; usersId: number | null; uuid: string; enabled: boolean; chat_mode: string; allowlist_domains: string | null; allow_model_override: boolean; allow_temperature_override: boolean; ... 4 more ...; createdBy: number | null; }[]' is not assignable to type 'EmbedConfigData[]'.
  Property 'lastUpdatedAt' is missing in type '{ id: number; createdAt: Date; usersId: number | null; uuid: string; enabled: boolean; chat_mode: string; allowlist_domains: string | null; allow_model_override: boolean; allow_temperature_override: boolean; ... 4 more ...; createdBy: number | null; }' but required in type 'EmbedConfigData'.
models/embedConfig.ts(331,14): error TS2352: Conversion of type '({ workspaces: { id: number; name: string; createdAt: Date; lastUpdatedAt: Date; pfpFilename: string | null; user_id: number; type: string | null; slug: string; sharedWithOrg: boolean; ... 18 more ...; order: number; }; _count: { ...; }; } & { ...; })[]' to type 'EmbedConfigWithWorkspace[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'lastUpdatedAt' is missing in type '{ workspaces: { id: number; name: string; createdAt: Date; lastUpdatedAt: Date; pfpFilename: string | null; user_id: number; type: string | null; slug: string; sharedWithOrg: boolean; ... 18 more ...; order: number; }; _count: { ...; }; } & { ...; }' but required in type 'EmbedConfigWithWorkspace'.
models/invite.ts(281,17): error TS2322: Type '(number | null)[]' is not assignable to type 'number[]'.
  Type 'number | null' is not assignable to type 'number'.
    Type 'null' is not assignable to type 'number'.
models/newsMessage.ts(3,3): error TS6196: 'users' is declared but never used.
models/newsMessage.ts(140,9): error TS2322: Type '{ title: string; content: string; priority: NewsPriority; target_roles: string | null; expires_at: Date | null; created_by: number; }' is not assignable to type '(Without<news_messagesCreateInput, news_messagesUncheckedCreateInput> & news_messagesUncheckedCreateInput) | (Without<...> & news_messagesCreateInput)'.
  Type '{ title: string; content: string; priority: NewsPriority; target_roles: string | null; expires_at: Date | null; created_by: number; }' is not assignable to type 'Without<news_messagesCreateInput, news_messagesUncheckedCreateInput> & news_messagesUncheckedCreateInput'.
    Property 'updatedAt' is missing in type '{ title: string; content: string; priority: NewsPriority; target_roles: string | null; expires_at: Date | null; created_by: number; }' but required in type 'news_messagesUncheckedCreateInput'.
models/organizationLegalTemplate.ts(47,9): error TS2322: Type '{ organizationId: number; category: string; documentType: string; templateContent: string; templateFormatting: string; customInputs: string | null; }' is not assignable to type '(Without<OrganizationLegalTemplateCreateInput, OrganizationLegalTemplateUncheckedCreateInput> & OrganizationLegalTemplateUncheckedCreateInput) | (Without<...> & OrganizationLegalTemplateCreateInput)'.
  Type '{ organizationId: number; category: string; documentType: string; templateContent: string; templateFormatting: string; customInputs: string | null; }' is not assignable to type 'Without<OrganizationLegalTemplateCreateInput, OrganizationLegalTemplateUncheckedCreateInput> & OrganizationLegalTemplateUncheckedCreateInput'.
    Property 'updatedAt' is missing in type '{ organizationId: number; category: string; documentType: string; templateContent: string; templateFormatting: string; customInputs: string | null; }' but required in type 'OrganizationLegalTemplateUncheckedCreateInput'.
models/passwordRecovery.ts(4,1): error TS6192: All imports in import declaration are unused.
models/passwordRecovery.ts(180,9): error TS2322: Type 'WhereClause' is not assignable to type 'password_reset_tokensWhereUniqueInput'.
  Type 'WhereClause' is not assignable to type '{ id: string | number; token: string | number; } & { id?: number | undefined; token?: string | undefined; AND?: password_reset_tokensWhereInput | password_reset_tokensWhereInput[] | undefined; ... 5 more ...; users?: (Without<...> & usersWhereInput) | ... 1 more ... | undefined; }'.
    Property 'token' is missing in type 'WhereClause' but required in type '{ id: string | number; token: string | number; }'.
models/promptExamples.ts(64,7): error TS2322: Type '{ title: string; id: number; createdAt: Date; updatedAt: Date; prompt: string; area: string; icon: string | null; workspaceSlug: string; } | null' is not assignable to type 'PromptExampleData | null'.
  Property 'lastUpdatedAt' is missing in type '{ title: string; id: number; createdAt: Date; updatedAt: Date; prompt: string; area: string; icon: string | null; workspaceSlug: string; }' but required in type 'PromptExampleData'.
models/promptExamples.ts(88,7): error TS2322: Type '{ title: string; id: number; prompt: string; area: string; icon: string | null; workspaceSlug: string; }[]' is not assignable to type 'PromptExampleResponse[]'.
  Type '{ title: string; id: number; prompt: string; area: string; icon: string | null; workspaceSlug: string; }' is not assignable to type 'PromptExampleResponse'.
    Types of property 'icon' are incompatible.
      Type 'string | null' is not assignable to type 'string'.
        Type 'null' is not assignable to type 'string'.
models/promptExamples.ts(108,13): error TS2322: Type '{ title: string; area: string; prompt: string; icon: string; workspaceSlug: string; }' is not assignable to type '(Without<prompt_examplesCreateInput, prompt_examplesUncheckedCreateInput> & prompt_examplesUncheckedCreateInput) | (Without<...> & prompt_examplesCreateInput)'.
  Type '{ title: string; area: string; prompt: string; icon: string; workspaceSlug: string; }' is not assignable to type 'Without<prompt_examplesUncheckedCreateInput, prompt_examplesCreateInput> & prompt_examplesCreateInput'.
    Property 'updatedAt' is missing in type '{ title: string; area: string; prompt: string; icon: string; workspaceSlug: string; }' but required in type 'prompt_examplesCreateInput'.
models/promptExamples.ts(119,31): error TS2322: Type '{ title: string; id: number; createdAt: Date; updatedAt: Date; prompt: string; area: string; icon: string | null; workspaceSlug: string; }[]' is not assignable to type 'PromptExampleData[]'.
  Property 'lastUpdatedAt' is missing in type '{ title: string; id: number; createdAt: Date; updatedAt: Date; prompt: string; area: string; icon: string | null; workspaceSlug: string; }' but required in type 'PromptExampleData'.
models/promptExamples.ts(138,7): error TS2322: Type '{ title: string; id: number; prompt: string; area: string; icon: string | null; workspaceSlug: string; }[]' is not assignable to type 'PromptExampleResponse[]'.
  Type '{ title: string; id: number; prompt: string; area: string; icon: string | null; workspaceSlug: string; }' is not assignable to type 'PromptExampleResponse'.
    Types of property 'icon' are incompatible.
      Type 'string | null' is not assignable to type 'string'.
        Type 'null' is not assignable to type 'string'.
models/systemLegalTemplate.ts(45,9): error TS2322: Type '{ category: string; documentType: string; templateContent: string; templateFormatting: string; customInputs: string | null; }' is not assignable to type '(Without<SystemLegalTemplateCreateInput, SystemLegalTemplateUncheckedCreateInput> & SystemLegalTemplateUncheckedCreateInput) | (Without<...> & SystemLegalTemplateCreateInput)'.
  Type '{ category: string; documentType: string; templateContent: string; templateFormatting: string; customInputs: string | null; }' is not assignable to type 'Without<SystemLegalTemplateUncheckedCreateInput, SystemLegalTemplateCreateInput> & SystemLegalTemplateCreateInput'.
    Property 'updatedAt' is missing in type '{ category: string; documentType: string; templateContent: string; templateFormatting: string; customInputs: string | null; }' but required in type 'SystemLegalTemplateCreateInput'.
models/systemReport.ts(226,39): error TS2345: Argument of type 'SystemReportEntity' is not assignable to parameter of type 'SystemReport'.
  Types of property 'severity' are incompatible.
    Type 'SeverityLevel | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
models/systemReport.ts(695,31): error TS2345: Argument of type 'SystemReportEntity' is not assignable to parameter of type 'SystemReport'.
  Types of property 'severity' are incompatible.
    Type 'SeverityLevel | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
models/systemReport.ts(795,56): error TS2345: Argument of type 'SystemReportEntity' is not assignable to parameter of type 'SystemReport'.
  Types of property 'affected_service' are incompatible.
    Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/systemReport").ServiceCategory | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
models/systemReport.ts(804,9): error TS2345: Argument of type 'SystemReportEntity' is not assignable to parameter of type 'SystemReport'.
  Types of property 'severity' are incompatible.
    Type 'SeverityLevel | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
models/systemSettings.ts(304,65): error TS18046: 'e' is of type 'unknown'.
models/systemSettings.ts(309,29): error TS7006: Parameter 'update' implicitly has an 'any' type.
models/systemSettings.ts(323,27): error TS2769: No overload matches this call.
  Overload 1 of 2, '(message?: string | undefined, options?: ErrorOptions | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
  Overload 2 of 2, '(message?: string | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
models/systemSettings.ts(328,11): error TS18046: 'e' is of type 'unknown'.
models/systemSettings.ts(333,28): error TS7006: Parameter 'updates' implicitly has an 'any' type.
models/systemSettings.ts(335,51): error TS7006: Parameter 'skill' implicitly has an 'any' type.
models/systemSettings.ts(342,35): error TS7006: Parameter 'updates' implicitly has an 'any' type.
models/systemSettings.ts(344,9): error TS2345: Argument of type 'string | null | undefined' is not assignable to parameter of type 'string | null'.
  Type 'undefined' is not assignable to type 'string | null'.
models/systemSettings.ts(349,11): error TS2345: Argument of type 'never[] | null' is not assignable to parameter of type 'never[] | undefined'.
  Type 'null' is not assignable to type 'never[] | undefined'.
models/systemSettings.ts(358,35): error TS7006: Parameter 'update' implicitly has an 'any' type.
models/systemSettings.ts(364,23): error TS7006: Parameter 'newTitle' implicitly has an 'any' type.
models/systemSettings.ts(374,25): error TS7006: Parameter 'faviconUrl' implicitly has an 'any' type.
models/systemSettings.ts(385,29): error TS7006: Parameter 'update' implicitly has an 'any' type.
models/systemSettings.ts(388,19): error TS7006: Parameter 'url' implicitly has an 'any' type.
models/systemSettings.ts(393,58): error TS18046: 'e' is of type 'unknown'.
models/systemSettings.ts(397,19): error TS7006: Parameter 'text' implicitly has an 'any' type.
models/systemSettings.ts(407,16): error TS7006: Parameter 'text' implicitly has an 'any' type.
models/systemSettings.ts(417,16): error TS7006: Parameter 'text' implicitly has an 'any' type.
models/systemSettings.ts(427,16): error TS7006: Parameter 'text' implicitly has an 'any' type.
models/systemSettings.ts(437,36): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(447,40): error TS7006: Parameter 'update' implicitly has an 'any' type.
models/systemSettings.ts(451,68): error TS18046: 'e' is of type 'unknown'.
models/systemSettings.ts(455,40): error TS7006: Parameter 'update' implicitly has an 'any' type.
models/systemSettings.ts(459,68): error TS18046: 'e' is of type 'unknown'.
models/systemSettings.ts(463,36): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(480,66): error TS18046: 'e' is of type 'unknown'.
models/systemSettings.ts(484,27): error TS7006: Parameter 'update' implicitly has an 'any' type.
models/systemSettings.ts(488,29): error TS7006: Parameter 'update' implicitly has an 'any' type.
models/systemSettings.ts(498,68): error TS18046: 'e' is of type 'unknown'.
models/systemSettings.ts(502,30): error TS7006: Parameter 'update' implicitly has an 'any' type.
models/systemSettings.ts(506,30): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(528,41): error TS7006: Parameter 'update' implicitly has an 'any' type.
models/systemSettings.ts(538,48): error TS7006: Parameter 'update' implicitly has an 'any' type.
models/systemSettings.ts(549,40): error TS7006: Parameter 'update' implicitly has an 'any' type.
models/systemSettings.ts(552,46): error TS7006: Parameter 'text' implicitly has an 'any' type.
models/systemSettings.ts(558,38): error TS7006: Parameter 'email' implicitly has an 'any' type.
models/systemSettings.ts(567,31): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(570,23): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(573,28): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(584,36): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(589,36): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(592,33): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(595,33): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(598,36): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(601,36): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(614,36): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(627,27): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(639,31): error TS7006: Parameter 'update' implicitly has an 'any' type.
models/systemSettings.ts(650,70): error TS18046: 'e' is of type 'unknown'.
models/systemSettings.ts(654,42): error TS7006: Parameter 'update' implicitly has an 'any' type.
models/systemSettings.ts(667,11): error TS18046: 'e' is of type 'unknown'.
models/systemSettings.ts(672,36): error TS7006: Parameter 'update' implicitly has an 'any' type.
models/systemSettings.ts(685,11): error TS18046: 'e' is of type 'unknown'.
models/systemSettings.ts(690,21): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(694,43): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(701,45): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(708,53): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(715,29): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(722,31): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(729,39): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(736,46): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(739,48): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(742,53): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(745,30): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(752,32): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(759,48): error TS7006: Parameter 'value' implicitly has an 'any' type.
models/systemSettings.ts(907,9): error TS2345: Argument of type '""' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(911,9): error TS2345: Argument of type '""' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(915,9): error TS2345: Argument of type '""' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(919,9): error TS2345: Argument of type '""' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(923,9): error TS2345: Argument of type '""' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(927,9): error TS2345: Argument of type '""' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(931,9): error TS2345: Argument of type '75' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(935,9): error TS2345: Argument of type '75' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(939,9): error TS2345: Argument of type '75' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(943,9): error TS2345: Argument of type '75' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(947,9): error TS2345: Argument of type '75' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(951,9): error TS2345: Argument of type '75' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(955,9): error TS2345: Argument of type '75' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(965,9): error TS2345: Argument of type '"false"' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(969,9): error TS2345: Argument of type '""' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(973,9): error TS2345: Argument of type '""' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(976,9): error TS2345: Argument of type 'string | null | undefined' is not assignable to parameter of type 'string | null'.
  Type 'undefined' is not assignable to type 'string | null'.
models/systemSettings.ts(983,9): error TS2345: Argument of type '"false"' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(987,9): error TS2345: Argument of type '"false"' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(991,9): error TS2345: Argument of type '"false"' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(998,9): error TS2345: Argument of type '""' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(1002,9): error TS2345: Argument of type '""' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(1006,9): error TS2345: Argument of type '"false"' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(1010,9): error TS2345: Argument of type '""' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(1020,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1030,20): error TS2339: Property 'label' does not exist on type '{}'.
models/systemSettings.ts(1034,27): error TS2339: Property 'label' does not exist on type '{}'.
models/systemSettings.ts(1041,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1046,39): error TS7006: Parameter 'limit' implicitly has an 'any' type.
models/systemSettings.ts(1054,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1069,14): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{}'.
  No index signature with a parameter of type 'string' was found on type '{}'.
models/systemSettings.ts(1091,30): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{}'.
  No index signature with a parameter of type 'string' was found on type '{}'.
models/systemSettings.ts(1095,32): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ login_ui: (value: any) => string; deep_search_context_percentage: (update: any) => number; footer_data: (updates: any) => string; text_splitter_chunk_size: (update: any) => number; ... 54 more ...; flow_referencefiles_compliance_threshold: (value: any) => string; }'.
  No index signature with a parameter of type 'string' was found on type '{ login_ui: (value: any) => string; deep_search_context_percentage: (update: any) => number; footer_data: (updates: any) => string; text_splitter_chunk_size: (update: any) => number; ... 54 more ...; flow_referencefiles_compliance_threshold: (value: any) => string; }'.
models/systemSettings.ts(1098,49): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{}'.
  No index signature with a parameter of type 'string' was found on type '{}'.
models/systemSettings.ts(1101,36): error TS2345: Argument of type 'string' is not assignable to parameter of type 'never'.
models/systemSettings.ts(1104,13): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{}'.
  No index signature with a parameter of type 'string' was found on type '{}'.
models/systemSettings.ts(1108,15): error TS18046: 'validationError' is of type 'unknown'.
models/systemSettings.ts(1110,33): error TS2345: Argument of type '{ key: string; error: any; }' is not assignable to parameter of type 'never'.
models/systemSettings.ts(1110,47): error TS18046: 'validationError' is of type 'unknown'.
models/systemSettings.ts(1127,35): error TS2345: Argument of type 'string' is not assignable to parameter of type 'never'.
models/systemSettings.ts(1131,13): error TS18046: 'upsertError' is of type 'unknown'.
models/systemSettings.ts(1133,31): error TS2345: Argument of type '{ key: string; error: any; }' is not assignable to parameter of type 'never'.
models/systemSettings.ts(1133,45): error TS18046: 'upsertError' is of type 'unknown'.
models/systemSettings.ts(1164,57): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1165,39): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1178,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1188,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1200,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1211,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1221,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1233,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1245,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1257,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1267,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1277,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1287,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1297,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1307,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1327,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1384,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1404,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1502,65): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1530,26): error TS7006: Parameter 'setting' implicitly has an 'any' type.
models/systemSettings.ts(1537,28): error TS7006: Parameter 'setting' implicitly has an 'any' type.
models/systemSettings.ts(1537,37): error TS7006: Parameter 'defaultValue' implicitly has an 'any' type.
models/systemSettings.ts(1552,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1570,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1585,56): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1597,58): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1607,53): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1628,9): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1652,57): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1675,60): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1689,57): error TS2339: Property 'provider' does not exist on type '{}'.
models/systemSettings.ts(1690,57): error TS2339: Property 'modelId' does not exist on type '{}'.
models/systemSettings.ts(1691,56): error TS2339: Property 'apiKey' does not exist on type '{}'.
models/systemSettings.ts(1694,33): error TS2339: Property 'enabled' does not exist on type '{}'.
models/systemSettings.ts(1698,33): error TS2339: Property 'contextPercentage' does not exist on type '{}'.
models/systemSettings.ts(1715,32): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ login_ui: (value: any) => string; deep_search_context_percentage: (update: any) => number; footer_data: (updates: any) => string; text_splitter_chunk_size: (update: any) => number; ... 54 more ...; flow_referencefiles_compliance_threshold: (value: any) => string; }'.
  No index signature with a parameter of type 'string' was found on type '{ login_ui: (value: any) => string; deep_search_context_percentage: (update: any) => number; footer_data: (updates: any) => string; text_splitter_chunk_size: (update: any) => number; ... 54 more ...; flow_referencefiles_compliance_threshold: (value: any) => string; }'.
models/systemSettings.ts(1716,15): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ login_ui: (value: any) => string; deep_search_context_percentage: (update: any) => number; footer_data: (updates: any) => string; text_splitter_chunk_size: (update: any) => number; ... 54 more ...; flow_referencefiles_compliance_threshold: (value: any) => string; }'.
  No index signature with a parameter of type 'string' was found on type '{ login_ui: (value: any) => string; deep_search_context_percentage: (update: any) => number; footer_data: (updates: any) => string; text_splitter_chunk_size: (update: any) => number; ... 54 more ...; flow_referencefiles_compliance_threshold: (value: any) => string; }'.
models/systemSettings.ts(1737,39): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1742,33): error TS7006: Parameter 'label' implicitly has an 'any' type.
models/systemSettings.ts(1750,12): error TS7053: Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ deep_search_provider: string; deep_search_model_id: string; deep_search_api_key: string; deep_search_enabled: string; deep_search_context_percentage: string; }'.
models/systemSettings.ts(1758,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1768,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1777,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1787,62): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1829,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1839,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1850,21): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(1908,7): error TS7006: Parameter 'baseKey' implicitly has an 'any' type.
models/systemSettings.ts(1909,7): error TS7006: Parameter 'baseEnvKey' implicitly has an 'any' type.
models/systemSettings.ts(1911,24): error TS7006: Parameter 'val' implicitly has an 'any' type.
models/systemSettings.ts(1920,11): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{}'.
  No index signature with a parameter of type 'string' was found on type '{}'.
models/systemSettings.ts(1922,11): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{}'.
  No index signature with a parameter of type 'string' was found on type '{}'.
models/systemSettings.ts(1940,9): error TS2345: Argument of type '"chatgpt-4o-latest"' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(1962,9): error TS2345: Argument of type '4096' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(1976,9): error TS2345: Argument of type '"claude-2"' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(1989,9): error TS2345: Argument of type '"gemini-pro"' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(1994,9): error TS2345: Argument of type '"BLOCK_MEDIUM_AND_ABOVE"' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(2029,9): error TS2345: Argument of type '300' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(2035,9): error TS2345: Argument of type '"base"' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(2310,14): error TS2531: Object is possibly 'null'.
models/systemSettings.ts(2311,38): error TS2700: Rest types may only be created from object types.
models/systemSettings.ts(2327,7): error TS2345: Argument of type 'true' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(2329,12): error TS2367: This comparison appears to be unintentional because the types 'string | number | null' and 'boolean' have no overlap.
models/systemSettings.ts(2335,7): error TS2345: Argument of type '1' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(2345,11): error TS2345: Argument of type 'false' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(2353,11): error TS2345: Argument of type '""' is not assignable to parameter of type 'null | undefined'.
models/systemSettings.ts(2371,9): error TS18046: 'error' is of type 'unknown'.
models/systemSettings.ts(2384,64): error TS2339: Property 'database_id' does not exist on type 'never'.
models/systemSettings.ts(2388,28): error TS2339: Property 'action' does not exist on type 'never'.
models/systemSettings.ts(2389,25): error TS2339: Property 'database_id' does not exist on type 'never'.
models/systemSettings.ts(2391,39): error TS2339: Property 'database_id' does not exist on type 'never'.
models/systemSettings.ts(2397,28): error TS2339: Property 'action' does not exist on type 'never'.
models/systemSettings.ts(2399,19): error TS2339: Property 'connectionString' does not exist on type 'never'.
models/systemSettings.ts(2402,41): error TS2339: Property 'database_id' does not exist on type 'never'.
models/systemSettings.ts(2403,16): error TS2339: Property 'database_id' does not exist on type 'never'.
models/systemSettings.ts(2404,21): error TS2339: Property 'database_id' does not exist on type 'never'.
models/systemSettings.ts(2407,16): error TS2339: Property 'database_id' does not exist on type 'never'.
models/systemSettings.ts(2407,45): error TS2339: Property 'database_id' does not exist on type 'never'.
models/systemSettings.ts(2410,31): error TS2345: Argument of type '{ engine: any; database_id: any; connectionString: any; }' is not assignable to parameter of type 'never'.
models/systemSettings.ts(2411,24): error TS2339: Property 'engine' does not exist on type 'never'.
models/systemSettings.ts(2412,29): error TS2339: Property 'database_id' does not exist on type 'never'.
models/systemSettings.ts(2413,34): error TS2339: Property 'connectionString' does not exist on type 'never'.
models/telemetry.ts(2,10): error TS2614: Module '"./systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "./systemSettings"' instead?
models/threadShare.ts(158,22): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
models/userLegalTemplate.ts(46,9): error TS2322: Type '{ userId: number; category: string; documentType: string; templateContent: string; templateFormatting: string; customInputs: string; }' is not assignable to type '(Without<UserLegalTemplateCreateInput, UserLegalTemplateUncheckedCreateInput> & UserLegalTemplateUncheckedCreateInput) | (Without<...> & UserLegalTemplateCreateInput)'.
  Type '{ userId: number; category: string; documentType: string; templateContent: string; templateFormatting: string; customInputs: string; }' is not assignable to type 'Without<UserLegalTemplateCreateInput, UserLegalTemplateUncheckedCreateInput> & UserLegalTemplateUncheckedCreateInput'.
    Property 'updatedAt' is missing in type '{ userId: number; category: string; documentType: string; templateContent: string; templateFormatting: string; customInputs: string; }' but required in type 'UserLegalTemplateUncheckedCreateInput'.
models/userPromptLibrary.ts(162,9): error TS2322: Type '{ name: string | null; prompt_text: string | null; description: string | null; user_id: number; }' is not assignable to type '(Without<user_prompt_librariesCreateInput, user_prompt_librariesUncheckedCreateInput> & user_prompt_librariesUncheckedCreateInput) | (Without<...> & user_prompt_librariesCreateInput)'.
  Type '{ name: string | null; prompt_text: string | null; description: string | null; user_id: number; }' is not assignable to type 'Without<user_prompt_librariesCreateInput, user_prompt_librariesUncheckedCreateInput> & user_prompt_librariesUncheckedCreateInput'.
    Property 'updated_at' is missing in type '{ name: string | null; prompt_text: string | null; description: string | null; user_id: number; }' but required in type 'user_prompt_librariesUncheckedCreateInput'.
models/userStyleProfile.ts(1,10): error TS2614: Module '"../utils/prisma"' has no exported member 'prisma'. Did you mean to use 'import prisma from "../utils/prisma"' instead?
models/userStyleProfile.ts(237,40): error TS7006: Parameter 'tx' implicitly has an 'any' type.
models/vectors.ts(4,10): error TS2724: '"@prisma/client"' has no exported member named 'DocumentVector'. Did you mean 'document_vectors'?
models/vectors.ts(22,13): error TS7034: Variable 'inserts' implicitly has type 'any[]' in some locations where its type cannot be determined.
models/vectors.ts(33,33): error TS7005: Variable 'inserts' implicitly has an 'any[]' type.
models/vectors.ts(52,7): error TS2345: Argument of type 'number' is not assignable to parameter of type 'string'.
models/workspace.ts(10,3): error TS6133: 'WorkspaceCreateParams' is declared but its value is never read.
models/workspace.ts(14,10): error TS2724: '"@prisma/client"' has no exported member named 'Workspace'. Did you mean 'workspaces'?
models/workspace.ts(221,69): error TS2339: Property 'id' does not exist on type 'WorkspaceWithDocuments'.
models/workspaceAgentInvocation.ts(57,9): error TS2322: Type '{ uuid: string; }' is not assignable to type 'workspace_agent_invocationsWhereUniqueInput'.
  Type '{ uuid: string; }' is not assignable to type '{ id: number; } & { id?: number | undefined; AND?: workspace_agent_invocationsWhereInput | workspace_agent_invocationsWhereInput[] | undefined; ... 11 more ...; users?: (Without<...> & usersWhereInput) | ... 2 more ... | undefined; }'.
    Property 'id' is missing in type '{ uuid: string; }' but required in type '{ id: number; }'.
models/workspaceAgentInvocation.ts(123,9): error TS2322: Type 'WhereClause' is not assignable to type 'workspace_agent_invocationsWhereUniqueInput'.
  Type 'WhereClause' is not assignable to type '{ id: number; } & { id?: number | undefined; AND?: workspace_agent_invocationsWhereInput | workspace_agent_invocationsWhereInput[] | undefined; ... 11 more ...; users?: (Without<...> & usersWhereInput) | ... 2 more ... | undefined; }'.
    Type 'WhereClause' is not assignable to type '{ id: number; }'.
      Types of property 'id' are incompatible.
        Type 'number | undefined' is not assignable to type 'number'.
          Type 'undefined' is not assignable to type 'number'.
models/workspaceChats.ts(37,11): error TS2430: Interface 'WorkspaceChatWithData' incorrectly extends interface 'WorkspaceChat'.
  Property 'metrics' is optional in type 'WorkspaceChatWithData' but required in type 'WorkspaceChat'.
models/workspaceChats.ts(271,9): error TS2559: Type 'WhereClause' has no properties in common with type 'workspace_chatsWhereInput'.
models/workspaceChats.ts(299,9): error TS2559: Type '{ [key: string]: any; user?: { username: string; } | undefined; }' has no properties in common with type 'workspace_chatsWhereInput'.
models/workspacesSuggestedMessages.ts(4,17): error TS6133: 'PrismaWorkspace' is declared but its value is never read.
prisma/backfill-legal-templates.ts(43,7): error TS2322: Type '{ category: string; documentType: string; templateContent: string; templateFormatting: string | null; customInputs: string | null; }' is not assignable to type '(Without<SystemLegalTemplateCreateInput, SystemLegalTemplateUncheckedCreateInput> & SystemLegalTemplateUncheckedCreateInput) | (Without<...> & SystemLegalTemplateCreateInput)'.
  Type '{ category: string; documentType: string; templateContent: string; templateFormatting: string | null; customInputs: string | null; }' is not assignable to type 'Without<SystemLegalTemplateUncheckedCreateInput, SystemLegalTemplateCreateInput> & SystemLegalTemplateCreateInput'.
    Property 'updatedAt' is missing in type '{ category: string; documentType: string; templateContent: string; templateFormatting: string | null; customInputs: string | null; }' but required in type 'SystemLegalTemplateCreateInput'.
routes/admin/system/deep-search-settings.ts(2,10): error TS2614: Module '"../../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../../models/systemSettings"' instead?
routes/admin/system/deep-search-settings.ts(3,10): error TS2305: Module '"../../../utils/middleware/multiUserProtected"' has no exported member 'checkAdmin'.
routes/admin/system/deep-search-settings.ts(4,10): error TS2305: Module '"../../../utils/helpers"' has no exported member 'encryptApiKey'.
routes/admin/system/deep-search-settings.ts(25,29): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_req: Request, res: Response) => Promise<express.Response<any, Record<string, any>>>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(_req: Request, res: Response) => Promise<express.Response<any, Record<string, any>>>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
routes/admin/system/deep-search-settings.ts(61,3): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: Request<{}, {}, DeepSearchSettingsRequest>, res: Response) => Promise<express.Response<any, Record<string, any>>>' is not assignable to parameter of type 'RequestHandlerParams<{}, any, DeepSearchSettingsRequest, ParsedQs, Record<string, any>>'.
      Type '(req: Request<{}, {}, DeepSearchSettingsRequest>, res: Response) => Promise<express.Response<any, Record<string, any>>>' is not assignable to type 'RequestHandler<{}, any, DeepSearchSettingsRequest, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 'Promise<void>'.
            Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
scripts/testJinaEmbedder.ts(49,22): error TS2339: Property 'task' does not exist on type 'JinaEmbedder'.
scripts/testJinaEmbedder.ts(50,31): error TS2339: Property 'lateChunking' does not exist on type 'JinaEmbedder'.
scripts/testJinaEmbedder.ts(51,32): error TS2339: Property 'embeddingType' does not exist on type 'JinaEmbedder'.
scripts/testJinaEmbedder.ts(52,28): error TS2341: Property 'dimensions' is private and only accessible within class 'JinaEmbedder'.
scripts/testJinaEmbedder.ts(70,22): error TS2339: Property 'task' does not exist on type 'JinaEmbedder'.
scripts/testJinaEmbedder.ts(71,32): error TS2339: Property 'embeddingType' does not exist on type 'JinaEmbedder'.
scripts/testJinaEmbedder.ts(72,28): error TS2341: Property 'dimensions' is private and only accessible within class 'JinaEmbedder'.
swagger/index.ts(3,9): error TS2669: Augmentations for the global scope can only be directly nested in external modules or ambient module declarations.
swagger/index.ts(9,48): error TS2304: Cannot find name 'Element'.
swagger/index.ts(11,29): error TS2584: Cannot find name 'document'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.
swagger/index.ts(16,26): error TS2304: Cannot find name 'MutationObserver'.
swagger/index.ts(17,23): error TS2584: Cannot find name 'document'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.
swagger/index.ts(24,22): error TS2584: Cannot find name 'document'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.
swagger/index.ts(33,7): error TS2304: Cannot find name 'window'.
swagger/index.ts(34,35): error TS2304: Cannot find name 'window'.
swagger/index.ts(36,35): error TS2304: Cannot find name 'window'.
swagger/index.ts(36,67): error TS2304: Cannot find name 'window'.
swagger/init.ts(118,3): error TS2345: Argument of type '({ data }: SwaggerResult) => void' is not assignable to parameter of type '(value: false | { success: boolean; data: any; }) => void | PromiseLike<void>'.
  Types of parameters '__0' and 'value' are incompatible.
    Type 'false | { success: boolean; data: any; }' is not assignable to type 'SwaggerResult'.
      Type 'boolean' is not assignable to type 'SwaggerResult'.
swagger/utils.ts(3,23): error TS7016: Could not find a declaration file for module 'swagger-ui-express'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/swagger-ui-express/index.js' implicitly has an 'any' type.
  Try `npm i --save-dev @types/swagger-ui-express` if it exists or add a new declaration (.d.ts) file containing `declare module 'swagger-ui-express';`
swagger/utils.ts(44,26): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(_: Request, response: Response) => Promise<Response<any, Record<string, any>>>' is not assignable to parameter of type 'Application<Record<string, any>>'.
      Type '(_: Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>, response: Response<any, Record<string, any>>) => Promise<...>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.
types/agents.ts(416,3): error TS2564: Property 'status' has no initializer and is not definitely assigned in the constructor.
types/agents.ts(422,3): error TS2390: Constructor implementation is missing.
types/chat-agent.ts(172,3): error TS2687: All declarations of 'stage' must have identical modifiers.
types/chat-agent.ts(465,3): error TS2687: All declarations of 'stage' must have identical modifiers.
types/chat-agent.ts(465,3): error TS2717: Subsequent property declarations must have the same type.  Property 'stage' must be of type 'string', but here has type 'string | undefined'.
types/chat-agent.ts(475,3): error TS2717: Subsequent property declarations must have the same type.  Property 'metrics' must be of type 'Record<string, any>', but here has type '{ [key: string]: any; totalExecutionTime: number; }'.
types/chat-flow.ts(32,21): error TS2693: 'StageProcessor' only refers to a type, but is being used as a value here.
types/shared.ts(2,1): error TS6133: 'WebSocketServer' is declared but its value is never read.
types/shared.ts(2,43): error TS7016: Could not find a declaration file for module 'ws'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/ws/index.js' implicitly has an 'any' type.
  Try `npm i --save-dev @types/ws` if it exists or add a new declaration (.d.ts) file containing `declare module 'ws';`
types/test-utils.ts(7,10): error TS2724: '"@prisma/client"' has no exported member named 'User'. Did you mean 'users'?
types/test-utils.ts(7,16): error TS2724: '"@prisma/client"' has no exported member named 'Workspace'. Did you mean 'workspaces'?
types/test-utils.ts(7,27): error TS2724: '"@prisma/client"' has no exported member named 'WorkspaceThread'. Did you mean 'workspace_threads'?
types/test-utils.ts(7,44): error TS2305: Module '"@prisma/client"' has no exported member 'Document'.
types/test-utils.ts(23,18): error TS2430: Interface 'MockRequest' incorrectly extends interface 'Partial<Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>>'.
  Types of property 'user' are incompatible.
    Type '{ id: string; role: string; } | undefined' is not assignable to type 'AuthenticatedUser | undefined'.
      Type '{ id: string; role: string; }' is missing the following properties from type 'AuthenticatedUser': username, email, organizationId, suspended, and 2 more.
utils/agents/aibitat/example/beginner-chat.ts(14,6): error TS6196: 'AgentType' is declared but never used.
utils/agents/aibitat/example/beginner-chat.ts(35,13): error TS2345: Argument of type 'AibitatFunctionDefinition' is not assignable to parameter of type 'AgentFunction'.
  The types of 'parameters.properties' are incompatible between these types.
    Type 'Record<string, unknown>' is not assignable to type 'Record<string, FunctionProperty>'.
      'string' index signatures are incompatible.
        Type 'unknown' is not assignable to type 'FunctionProperty'.
utils/agents/aibitat/example/beginner-chat.ts(57,5): error TS2322: Type 'string' is not assignable to type 'InterruptConfig'.
utils/agents/aibitat/example/beginner-chat.ts(61,17): error TS2322: Type 'string' is not assignable to type 'AgentFunction'.
utils/agents/aibitat/example/blog-post-coding.ts(3,3): error TS2614: Module '"../plugins/index"' has no exported member 'cli'. Did you mean to use 'import cli from "../plugins/index"' instead?
utils/agents/aibitat/example/blog-post-coding.ts(5,3): error TS2614: Module '"../plugins/index"' has no exported member 'fileHistory'. Did you mean to use 'import fileHistory from "../plugins/index"' instead?
utils/agents/aibitat/example/blog-post-coding.ts(25,24): error TS2345: Argument of type 'AgentConfig' is not assignable to parameter of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/agents").AgentConfig'.
  Types of property 'functions' are incompatible.
    Type 'string[] | undefined' is not assignable to type 'AgentFunction[] | undefined'.
      Type 'string[]' is not assignable to type 'AgentFunction[]'.
        Type 'string' is not assignable to type 'AgentFunction'.
utils/agents/aibitat/example/blog-post-coding.ts(31,24): error TS2345: Argument of type 'AgentConfig' is not assignable to parameter of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/agents").AgentConfig'.
  Types of property 'functions' are incompatible.
    Type 'string[] | undefined' is not assignable to type 'AgentFunction[] | undefined'.
      Type 'string[]' is not assignable to type 'AgentFunction[]'.
        Type 'string' is not assignable to type 'AgentFunction'.
utils/agents/aibitat/example/blog-post-coding.ts(38,16): error TS2345: Argument of type 'AgentConfig' is not assignable to parameter of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/agents").AgentConfig'.
  Types of property 'functions' are incompatible.
    Type 'string[] | undefined' is not assignable to type 'AgentFunction[] | undefined'.
      Type 'string[]' is not assignable to type 'AgentFunction[]'.
        Type 'string' is not assignable to type 'AgentFunction'.
utils/agents/aibitat/example/websocket/websock-branding-collab.ts(11,23): error TS7016: Could not find a declaration file for module 'ws'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/ws/index.js' implicitly has an 'any' type.
  Try `npm i --save-dev @types/ws` if it exists or add a new declaration (.d.ts) file containing `declare module 'ws';`
utils/agents/aibitat/example/websocket/websock-branding-collab.ts(15,34): error TS1343: The 'import.meta' meta-property is only allowed when the '--module' option is 'es2020', 'es2022', 'esnext', 'system', 'node16', 'node18', or 'nodenext'.
utils/agents/aibitat/example/websocket/websock-branding-collab.ts(22,19): error TS1378: Top-level 'await' expressions are only allowed when the 'module' option is set to 'es2022', 'esnext', 'system', 'node16', 'node18', 'nodenext', or 'preserve', and the 'target' option is set to 'es2017' or higher.
utils/agents/aibitat/example/websocket/websock-branding-collab.ts(22,32): error TS7016: Could not find a declaration file for module '@mintplex-labs/express-ws'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/@mintplex-labs/express-ws/dist/index.js' implicitly has an 'any' type.
  Try `npm i --save-dev @types/mintplex-labs__express-ws` if it exists or add a new declaration (.d.ts) file containing `declare module '@mintplex-labs/express-ws';`
utils/agents/aibitat/example/websocket/websock-branding-collab.ts(55,10): error TS2339: Property 'on' does not exist on type 'ExtendedWebSocket'.
utils/agents/aibitat/example/websocket/websock-branding-collab.ts(59,10): error TS2339: Property 'on' does not exist on type 'ExtendedWebSocket'.
utils/agents/aibitat/example/websocket/websock-branding-collab.ts(66,12): error TS2339: Property 'send' does not exist on type 'ExtendedWebSocket'.
utils/agents/aibitat/example/websocket/websock-branding-collab.ts(98,32): error TS2345: Argument of type 'AgentConfig' is not assignable to parameter of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/agents").AgentConfig'.
  Types of property 'functions' are incompatible.
    Type 'string[] | undefined' is not assignable to type 'AgentFunction[] | undefined'.
      Type 'string[]' is not assignable to type 'AgentFunction[]'.
        Type 'string' is not assignable to type 'AgentFunction'.
utils/agents/aibitat/example/websocket/websock-branding-collab.ts(103,32): error TS2345: Argument of type 'AgentConfig' is not assignable to parameter of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/agents").AgentConfig'.
  Types of property 'functions' are incompatible.
    Type 'string[] | undefined' is not assignable to type 'AgentFunction[] | undefined'.
      Type 'string[]' is not assignable to type 'AgentFunction[]'.
        Type 'string' is not assignable to type 'AgentFunction'.
utils/agents/aibitat/example/websocket/websock-branding-collab.ts(109,18): error TS2345: Argument of type 'AgentConfig' is not assignable to parameter of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/agents").AgentConfig'.
  Types of property 'functions' are incompatible.
    Type 'string[] | undefined' is not assignable to type 'AgentFunction[] | undefined'.
      Type 'string[]' is not assignable to type 'AgentFunction[]'.
        Type 'string' is not assignable to type 'AgentFunction'.
utils/agents/aibitat/example/websocket/websock-multi-turn-chat.ts(11,23): error TS7016: Could not find a declaration file for module 'ws'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/ws/index.js' implicitly has an 'any' type.
  Try `npm i --save-dev @types/ws` if it exists or add a new declaration (.d.ts) file containing `declare module 'ws';`
utils/agents/aibitat/example/websocket/websock-multi-turn-chat.ts(15,34): error TS1343: The 'import.meta' meta-property is only allowed when the '--module' option is 'es2020', 'es2022', 'esnext', 'system', 'node16', 'node18', or 'nodenext'.
utils/agents/aibitat/example/websocket/websock-multi-turn-chat.ts(22,19): error TS1378: Top-level 'await' expressions are only allowed when the 'module' option is set to 'es2022', 'esnext', 'system', 'node16', 'node18', 'nodenext', or 'preserve', and the 'target' option is set to 'es2017' or higher.
utils/agents/aibitat/example/websocket/websock-multi-turn-chat.ts(22,32): error TS7016: Could not find a declaration file for module '@mintplex-labs/express-ws'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/@mintplex-labs/express-ws/dist/index.js' implicitly has an 'any' type.
  Try `npm i --save-dev @types/mintplex-labs__express-ws` if it exists or add a new declaration (.d.ts) file containing `declare module '@mintplex-labs/express-ws';`
utils/agents/aibitat/example/websocket/websock-multi-turn-chat.ts(62,10): error TS2339: Property 'on' does not exist on type 'ExtendedWebSocket'.
utils/agents/aibitat/example/websocket/websock-multi-turn-chat.ts(66,10): error TS2339: Property 'on' does not exist on type 'ExtendedWebSocket'.
utils/agents/aibitat/example/websocket/websock-multi-turn-chat.ts(73,12): error TS2339: Property 'send' does not exist on type 'ExtendedWebSocket'.
utils/agents/aibitat/example/websocket/websock-multi-turn-chat.ts(110,25): error TS2345: Argument of type 'AgentConfig' is not assignable to parameter of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/agents").AgentConfig'.
  Types of property 'functions' are incompatible.
    Type 'string[] | undefined' is not assignable to type 'AgentFunction[] | undefined'.
      Type 'string[]' is not assignable to type 'AgentFunction[]'.
        Type 'string' is not assignable to type 'AgentFunction'.
utils/agents/aibitat/example/websocket/websock-multi-turn-chat.ts(114,22): error TS2345: Argument of type 'AgentConfig' is not assignable to parameter of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/agents").AgentConfig'.
  Types of property 'functions' are incompatible.
    Type 'string[] | undefined' is not assignable to type 'AgentFunction[] | undefined'.
      Type 'string[]' is not assignable to type 'AgentFunction[]'.
        Type 'string' is not assignable to type 'AgentFunction'.
utils/agents/aibitat/index.ts(12,3): error TS6196: 'FunctionCall' is declared but never used.
utils/agents/aibitat/index.ts(16,3): error TS6196: 'AgentContext' is declared but never used.
utils/agents/aibitat/index.ts(67,5): error TS2322: Type 'ChatMessage[]' is not assignable to type 'ChatMessageInternal[]'.
  Property 'state' is missing in type 'ChatMessage' but required in type 'ChatMessageInternal'.
utils/agents/aibitat/index.ts(76,5): error TS2322: Type 'string' is not assignable to type 'AgentProviderType | null'.
utils/agents/aibitat/index.ts(248,22): error TS2345: Argument of type '{ content: string; state: "interrupt"; from: string; to: string; }' is not assignable to parameter of type 'ChatMessageInternal'.
  Type '{ content: string; state: "interrupt"; from: string; to: string; }' is missing the following properties from type 'ChatMessageInternal': role, timestamp
utils/agents/aibitat/index.ts(277,11): error TS2739: Type '{ state: "success"; from: string; to: string; content: string; }' is missing the following properties from type 'ChatMessageInternal': role, timestamp
utils/agents/aibitat/index.ts(323,11): error TS2739: Type '{ content: string; state: "error"; from: string; to: string; }' is missing the following properties from type 'ChatMessageInternal': role, timestamp
utils/agents/aibitat/index.ts(462,50): error TS2367: This comparison appears to be unintentional because the types 'InterruptConfig | undefined' and 'string' have no overlap.
utils/agents/aibitat/index.ts(506,48): error TS2345: Argument of type '{ members: string[]; provider: string | AgentProviderConfig; logging?: boolean; maxRounds: number; role: string; model: string; apiKey?: string; baseURL?: string; ... 4 more ...; presencePenalty?: number; }' is not assignable to parameter of type 'AgentProviderConfig'.
  Types of property 'provider' are incompatible.
    Type 'string | AgentProviderConfig' is not assignable to type 'string'.
      Type 'AgentProviderConfig' is not assignable to type 'string'.
utils/agents/aibitat/index.ts(539,13): error TS2339: Property 'result' does not exist on type 'String'.
utils/agents/aibitat/index.ts(539,48): error TS2345: Argument of type '{ role: MessageRole; content: string; }[]' is not assignable to parameter of type 'ChatMessage[]'.
  Type '{ role: MessageRole; content: string; }' is missing the following properties from type 'ChatMessage': from, to, timestamp
utils/agents/aibitat/index.ts(616,66): error TS2345: Argument of type 'AgentFunction' is not assignable to parameter of type 'string'.
utils/agents/aibitat/index.ts(619,48): error TS2345: Argument of type '{ role: string; prompt?: string; functions?: AgentFunction[]; provider: string | AgentProviderConfig; interrupt?: InterruptConfig; ... 9 more ...; presencePenalty?: number; }' is not assignable to parameter of type 'AgentProviderConfig'.
  Types of property 'provider' are incompatible.
    Type 'string | AgentProviderConfig' is not assignable to type 'string'.
      Type 'AgentProviderConfig' is not assignable to type 'string'.
utils/agents/aibitat/index.ts(643,48): error TS2345: Argument of type '{ role: MessageRole; content: string; name?: string | undefined; }[]' is not assignable to parameter of type 'ChatMessage[]'.
  Type '{ role: MessageRole; content: string; name?: string | undefined; }' is missing the following properties from type 'ChatMessage': from, to, timestamp
utils/agents/aibitat/index.ts(645,20): error TS2339: Property 'functionCall' does not exist on type 'string'.
utils/agents/aibitat/index.ts(646,52): error TS2339: Property 'functionCall' does not exist on type 'string'.
utils/agents/aibitat/index.ts(681,31): error TS2554: Expected 2 arguments, but got 1.
utils/agents/aibitat/index.ts(698,24): error TS2339: Property 'result' does not exist on type 'string'.
utils/agents/aibitat/index.ts(808,9): error TS2739: Type 'OpenAIProvider' is missing the following properties from type 'ProviderClass': provider, stream
utils/agents/aibitat/index.ts(810,9): error TS2741: Property 'provider' is missing in type 'AnthropicProvider' but required in type 'ProviderClass'.
utils/agents/aibitat/index.ts(812,9): error TS2739: Type 'LMStudioProvider' is missing the following properties from type 'ProviderClass': provider, providerLog, stream
utils/agents/aibitat/index.ts(814,9): error TS2739: Type 'OllamaProvider' is missing the following properties from type 'ProviderClass': provider, providerLog, stream
utils/agents/aibitat/index.ts(816,9): error TS2739: Type 'GroqProvider' is missing the following properties from type 'ProviderClass': provider, providerLog, stream
utils/agents/aibitat/index.ts(818,9): error TS2739: Type 'TogetherAIProvider' is missing the following properties from type 'ProviderClass': provider, providerLog, stream
utils/agents/aibitat/index.ts(820,9): error TS2739: Type 'AzureOpenAiProvider' is missing the following properties from type 'ProviderClass': provider, providerLog, stream
utils/agents/aibitat/index.ts(822,9): error TS2739: Type 'KoboldCPPProvider' is missing the following properties from type 'ProviderClass': provider, providerLog, stream
utils/agents/aibitat/index.ts(824,9): error TS2739: Type 'LocalAiProvider' is missing the following properties from type 'ProviderClass': provider, providerLog, stream
utils/agents/aibitat/index.ts(826,9): error TS2739: Type 'OpenRouterProvider' is missing the following properties from type 'ProviderClass': provider, providerLog, stream
utils/agents/aibitat/index.ts(828,9): error TS2739: Type 'MistralProvider' is missing the following properties from type 'ProviderClass': provider, providerLog, stream
utils/agents/aibitat/index.ts(830,9): error TS2739: Type 'GenericOpenAiProvider' is missing the following properties from type 'ProviderClass': provider, providerLog, stream
utils/agents/aibitat/index.ts(832,9): error TS2739: Type 'PerplexityProvider' is missing the following properties from type 'ProviderClass': provider, providerLog, stream
utils/agents/aibitat/index.ts(834,9): error TS2739: Type 'TextWebGenUiProvider' is missing the following properties from type 'ProviderClass': provider, providerLog, stream
utils/agents/aibitat/index.ts(836,9): error TS2739: Type 'AWSBedrockProvider' is missing the following properties from type 'ProviderClass': provider, providerLog, stream
utils/agents/aibitat/index.ts(838,9): error TS2739: Type 'FireworksAIProvider' is missing the following properties from type 'ProviderClass': provider, providerLog, stream
utils/agents/aibitat/index.ts(840,9): error TS2739: Type 'DeepSeekProvider' is missing the following properties from type 'ProviderClass': provider, providerLog, stream
utils/agents/aibitat/index.ts(842,9): error TS2739: Type 'XAIProvider' is missing the following properties from type 'ProviderClass': provider, providerLog, stream
utils/agents/aibitat/plugins/chat-history.ts(2,15): error TS2305: Module '"../../../../types/chat-agent"' has no exported member 'AIbitat'.
utils/agents/aibitat/plugins/chat-history.ts(2,24): error TS2305: Module '"../../../../types/chat-agent"' has no exported member 'AgentPlugin'.
utils/agents/aibitat/plugins/chat-history.ts(91,19): error TS2322: Type 'number | null' is not assignable to type 'number'.
  Type 'null' is not assignable to type 'number'.
utils/agents/aibitat/plugins/chat-history.ts(92,11): error TS2322: Type 'string | null' is not assignable to type 'number | null | undefined'.
  Type 'string' is not assignable to type 'number'.
utils/agents/aibitat/plugins/chat-history.ts(115,19): error TS2322: Type 'number | null' is not assignable to type 'number'.
  Type 'null' is not assignable to type 'number'.
utils/agents/aibitat/plugins/chat-history.ts(116,11): error TS2322: Type 'string | null' is not assignable to type 'number | null | undefined'.
  Type 'string' is not assignable to type 'number'.
utils/agents/aibitat/plugins/cli.ts(119,9): error TS2739: Type '{}' is missing the following properties from type 'AibitatMessage': from, to
utils/agents/aibitat/plugins/index.ts(10,15): error TS2305: Module '"../../../../types/chat-agent"' has no exported member 'AgentPlugin'.
utils/agents/aibitat/plugins/memory.ts(4,15): error TS2305: Module '"../../../../types/chat-agent"' has no exported member 'AIbitat'.
utils/agents/aibitat/plugins/memory.ts(4,24): error TS2305: Module '"../../../../types/chat-agent"' has no exported member 'AgentPlugin'.
utils/agents/aibitat/plugins/memory.ts(103,15): error TS2322: Type '""' is not assignable to type '"search" | "store"'.
utils/agents/aibitat/plugins/memory.ts(110,62): error TS2339: Property 'search' does not exist on type 'MemoryContext'.
utils/agents/aibitat/plugins/memory.ts(111,61): error TS2339: Property 'store' does not exist on type 'MemoryContext'.
utils/agents/aibitat/plugins/memory.ts(169,21): error TS2322: Type '{ vectorized: boolean; error: string | null; }' is not assignable to type 'VectorStoreResult'.
  Types of property 'error' are incompatible.
    Type 'string | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/plugins/memory.ts(186,19): error TS2345: Argument of type 'null' is not assignable to parameter of type 'string | undefined'.
utils/agents/aibitat/plugins/rechart.ts(3,15): error TS2305: Module '"../../../../types/chat-agent"' has no exported member 'AIbitat'.
utils/agents/aibitat/plugins/rechart.ts(3,24): error TS2305: Module '"../../../../types/chat-agent"' has no exported member 'AgentPlugin'.
utils/agents/aibitat/plugins/save-file-browser.ts(2,15): error TS2305: Module '"../../../../types/chat-agent"' has no exported member 'AIbitat'.
utils/agents/aibitat/plugins/save-file-browser.ts(2,24): error TS2305: Module '"../../../../types/chat-agent"' has no exported member 'AgentPlugin'.
utils/agents/aibitat/plugins/sql-agent/get-table-schema.ts(4,3): error TS6196: 'TableSchema' is declared but never used.
utils/agents/aibitat/plugins/sql-agent/SQLConnectors/index.ts(107,39): error TS2345: Argument of type 'string | null | undefined' is not assignable to parameter of type 'string | null'.
  Type 'undefined' is not assignable to type 'string | null'.
utils/agents/aibitat/plugins/sql-agent/SQLConnectors/MSSQL.ts(1,19): error TS7016: Could not find a declaration file for module 'mssql'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/mssql/index.js' implicitly has an 'any' type.
  Try `npm i --save-dev @types/mssql` if it exists or add a new declaration (.d.ts) file containing `declare module 'mssql';`
utils/agents/aibitat/plugins/sql-agent/SQLConnectors/MSSQL.ts(47,14): error TS2323: Cannot redeclare exported variable 'MSSQLConnector'.
utils/agents/aibitat/plugins/sql-agent/SQLConnectors/MSSQL.ts(231,10): error TS2323: Cannot redeclare exported variable 'MSSQLConnector'.
utils/agents/aibitat/plugins/sql-agent/SQLConnectors/MSSQL.ts(231,10): error TS2484: Export declaration conflicts with exported declaration of 'MSSQLConnector'.
utils/agents/aibitat/plugins/sql-agent/SQLConnectors/Postgresql.ts(1,19): error TS7016: Could not find a declaration file for module 'pg'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/pg/lib/index.js' implicitly has an 'any' type.
  Try `npm i --save-dev @types/pg` if it exists or add a new declaration (.d.ts) file containing `declare module 'pg';`
utils/agents/aibitat/plugins/summarize.ts(4,10): error TS2614: Module '"../providers/ai-provider"' has no exported member 'Provider'. Did you mean to use 'import Provider from "../providers/ai-provider"' instead?
utils/agents/aibitat/plugins/summarize.ts(5,15): error TS2305: Module '"../../../../types/chat-agent"' has no exported member 'AIbitat'.
utils/agents/aibitat/plugins/summarize.ts(5,24): error TS2305: Module '"../../../../types/chat-agent"' has no exported member 'AgentPlugin'.
utils/agents/aibitat/plugins/summarize.ts(102,54): error TS2339: Property 'listDocuments' does not exist on type 'DocumentSummarizerContext'.
utils/agents/aibitat/plugins/summarize.ts(104,33): error TS2339: Property 'summarizeDoc' does not exist on type 'DocumentSummarizerContext'.
utils/agents/aibitat/plugins/summarize.ts(133,39): error TS2339: Property 'title' does not exist on type '{}'.
utils/agents/aibitat/plugins/summarize.ts(134,42): error TS2339: Property 'description' does not exist on type '{}'.
utils/agents/aibitat/plugins/summarize.ts(152,21): error TS2322: Type 'never[] | null' is not assignable to type 'DocumentInfo[]'.
  Type 'null' is not assignable to type 'DocumentInfo[]'.
utils/agents/aibitat/plugins/summarize.ts(153,28): error TS2339: Property 'listDocuments' does not exist on type 'DocumentSummarizerContext'.
utils/agents/aibitat/plugins/web-browsing.ts(1,10): error TS2614: Module '"../../../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../../../models/systemSettings"' instead?
utils/agents/aibitat/plugins/web-browsing.ts(2,15): error TS2305: Module '"../../../../types/chat-agent"' has no exported member 'AIbitat'.
utils/agents/aibitat/plugins/web-browsing.ts(2,24): error TS2305: Module '"../../../../types/chat-agent"' has no exported member 'AgentPlugin'.
utils/agents/aibitat/plugins/web-browsing.ts(137,44): error TS2339: Property 'search' does not exist on type 'WebBrowsingContext'.
utils/agents/aibitat/plugins/web-browsing.ts(289,48): error TS7006: Parameter 'searchResult' implicitly has an 'any' type.
utils/agents/aibitat/plugins/web-browsing.ts(353,40): error TS7006: Parameter 'searchResult' implicitly has an 'any' type.
utils/agents/aibitat/plugins/web-browsing.ts(478,40): error TS7006: Parameter 'searchResult' implicitly has an 'any' type.
utils/agents/aibitat/plugins/web-browsing.ts(549,40): error TS7006: Parameter 'searchResult' implicitly has an 'any' type.
utils/agents/aibitat/plugins/web-browsing.ts(605,40): error TS7006: Parameter 'searchResult' implicitly has an 'any' type.
utils/agents/aibitat/plugins/web-scraping.ts(2,10): error TS2614: Module '"../providers/ai-provider"' has no exported member 'Provider'. Did you mean to use 'import Provider from "../providers/ai-provider"' instead?
utils/agents/aibitat/plugins/web-scraping.ts(4,15): error TS2305: Module '"../../../../types/chat-agent"' has no exported member 'AIbitat'.
utils/agents/aibitat/plugins/web-scraping.ts(4,24): error TS2305: Module '"../../../../types/chat-agent"' has no exported member 'AgentPlugin'.
utils/agents/aibitat/plugins/web-scraping.ts(72,42): error TS2339: Property 'scrape' does not exist on type 'WebScrapingContext'.
utils/agents/aibitat/plugins/web-scraping.ts(94,19): error TS2322: Type 'false | LinkContentResponse' is not assignable to type 'ScrapeResponse'.
  Type 'boolean' is not assignable to type 'ScrapeResponse'.
utils/agents/aibitat/plugins/websocket.ts(4,15): error TS2305: Module '"../../../../types/chat-agent"' has no exported member 'AIbitat'.
utils/agents/aibitat/plugins/websocket.ts(4,24): error TS2305: Module '"../../../../types/chat-agent"' has no exported member 'AgentPlugin'.
utils/agents/aibitat/plugins/websocket.ts(124,23): error TS2339: Property 'from' does not exist on type 'ChatMessage'.
utils/agents/aibitat/plugins/websocket.ts(126,23): error TS2339: Property 'from' does not exist on type 'ChatMessage'.
utils/agents/aibitat/providers/ai-provider.ts(52,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(57,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(65,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(73,11): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(85,11): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(93,11): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(101,11): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(109,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(110,11): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(118,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(128,11): error TS2322: Type 'string | null | undefined' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(136,11): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(147,31): error TS2345: Argument of type '{ baseURL?: string | null; apiKey?: string | null; temperature?: number | null; model?: string | null; maxTokens?: number | null; topP?: number | null; frequencyPenalty?: number | null; presencePenalty?: number | null; baseUrl: string | undefined; }' is not assignable to parameter of type 'OllamaInput & BaseLanguageModelParams & { disableStreaming?: boolean | undefined; }'.
  Type '{ baseURL?: string | null; apiKey?: string | null; temperature?: number | null; model?: string | null; maxTokens?: number | null; topP?: number | null; frequencyPenalty?: number | null; presencePenalty?: number | null; baseUrl: string | undefined; }' is not assignable to type 'OllamaInput'.
    Types of property 'frequencyPenalty' are incompatible.
      Type 'number | null | undefined' is not assignable to type 'number | undefined'.
        Type 'null' is not assignable to type 'number | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(156,11): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(164,11): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(172,11): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(180,11): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(188,11): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/ai-provider.ts(205,34): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
utils/agents/aibitat/providers/anthropic.ts(9,8): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.
utils/agents/aibitat/providers/anthropic.ts(300,5): error TS6133: 'config' is declared but its value is never read.
utils/agents/aibitat/providers/anthropic.ts(301,6): error TS1064: The return type of an async function or method must be the global Promise<T> type. Did you mean to write 'Promise<AsyncGenerator<string, any, any>>'?
utils/agents/aibitat/providers/anthropic.ts(301,6): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.
utils/agents/aibitat/providers/anthropic.ts(306,7): error TS1163: A 'yield' expression is only allowed in a generator body.
utils/agents/aibitat/providers/azure.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.
utils/agents/aibitat/providers/azure.ts(33,52): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.
  Cannot assign an abstract constructor type to a non-abstract constructor type.
utils/agents/aibitat/providers/azure.ts(75,16): error TS2532: Object is possibly 'undefined'.
utils/agents/aibitat/providers/azure.ts(97,36): error TS2339: Property 'functionCall' does not exist on type 'AzureOpenAiProvider'.
utils/agents/aibitat/providers/azure.ts(107,16): error TS2339: Property 'deduplicator' does not exist on type 'AzureOpenAiProvider'.
utils/agents/aibitat/providers/azure.ts(129,16): error TS2339: Property 'cleanMsgs' does not exist on type 'AzureOpenAiProvider'.
utils/agents/aibitat/providers/azure.ts(134,9): error TS2322: Type 'ChatResponseMessage | undefined' is not assignable to type '{ content?: string | undefined; } | undefined'.
  Type 'ChatResponseMessage' is not assignable to type '{ content?: string | undefined; }'.
    Types of property 'content' are incompatible.
      Type 'string | null' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/azure.ts(140,12): error TS2339: Property 'deduplicator' does not exist on type 'AzureOpenAiProvider'.
utils/agents/aibitat/providers/azure.ts(142,17): error TS18048: 'completion' is possibly 'undefined'.
utils/agents/aibitat/providers/bedrock.ts(11,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.
utils/agents/aibitat/providers/bedrock.ts(40,51): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.
  Cannot assign an abstract constructor type to a non-abstract constructor type.
utils/agents/aibitat/providers/bedrock.ts(105,15): error TS2345: Argument of type 'BaseMessage[]' is not assignable to parameter of type 'BaseLanguageModelInput'.
  Type 'BaseMessage[]' is not assignable to type 'BaseMessageLike[]'.
    Type 'BaseMessage' is not assignable to type 'BaseMessageLike'.
      Type 'BaseMessage' is not assignable to type 'MessageFieldWithRole'.
        Property 'role' is missing in type 'BaseMessage' but required in type '{ role: StringWithAutocomplete<"user" | "assistant" | MessageType>; content: MessageContent; name?: string | undefined; }'.
utils/agents/aibitat/providers/bedrock.ts(130,36): error TS2339: Property 'functionCall' does not exist on type 'AWSBedrockProvider'.
utils/agents/aibitat/providers/bedrock.ts(140,16): error TS2339: Property 'deduplicator' does not exist on type 'AWSBedrockProvider'.
utils/agents/aibitat/providers/bedrock.ts(161,11): error TS2345: Argument of type 'BaseMessage[]' is not assignable to parameter of type 'BaseLanguageModelInput'.
  Type 'BaseMessage[]' is not assignable to type 'BaseMessageLike[]'.
    Type 'BaseMessage' is not assignable to type 'BaseMessageLike'.
      Type 'BaseMessage' is not assignable to type 'MessageFieldWithRole'.
        Property 'role' is missing in type 'BaseMessage' but required in type '{ role: StringWithAutocomplete<"user" | "assistant" | MessageType>; content: MessageContent; name?: string | undefined; }'.
utils/agents/aibitat/providers/bedrock.ts(161,51): error TS2339: Property 'cleanMsgs' does not exist on type 'AWSBedrockProvider'.
utils/agents/aibitat/providers/bedrock.ts(163,9): error TS2322: Type 'AIMessageChunk' is not assignable to type '{ content?: string | undefined; }'.
  Types of property 'content' are incompatible.
    Type 'MessageContent' is not assignable to type 'string | undefined'.
      Type 'MessageContentComplex[]' is not assignable to type 'string'.
utils/agents/aibitat/providers/bedrock.ts(169,12): error TS2339: Property 'deduplicator' does not exist on type 'AWSBedrockProvider'.
utils/agents/aibitat/providers/bedrock.ts(171,18): error TS18048: 'completion' is possibly 'undefined'.
utils/agents/aibitat/providers/deepseek.ts(6,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.
utils/agents/aibitat/providers/deepseek.ts(34,49): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.
  Cannot assign an abstract constructor type to a non-abstract constructor type.
utils/agents/aibitat/providers/deepseek.ts(63,5): error TS2322: Type 'number | null' is not assignable to type 'number'.
  Type 'null' is not assignable to type 'number'.
utils/agents/aibitat/providers/deepseek.ts(109,36): error TS2339: Property 'functionCall' does not exist on type 'DeepSeekProvider'.
utils/agents/aibitat/providers/deepseek.ts(119,16): error TS2339: Property 'deduplicator' does not exist on type 'DeepSeekProvider'.
utils/agents/aibitat/providers/deepseek.ts(141,26): error TS2339: Property 'cleanMsgs' does not exist on type 'DeepSeekProvider'.
utils/agents/aibitat/providers/deepseek.ts(143,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.
  Types of property 'content' are incompatible.
    Type 'string | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/deepseek.ts(149,12): error TS2339: Property 'deduplicator' does not exist on type 'DeepSeekProvider'.
utils/agents/aibitat/providers/deepseek.ts(151,17): error TS18048: 'completion' is possibly 'undefined'.
utils/agents/aibitat/providers/fireworksai.ts(27,52): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.
  Cannot assign an abstract constructor type to a non-abstract constructor type.
utils/agents/aibitat/providers/fireworksai.ts(40,10): error TS2551: Property '_client' does not exist on type 'FireworksAIProvider'. Did you mean 'client'?
utils/agents/aibitat/providers/fireworksai.ts(46,17): error TS2551: Property '_client' does not exist on type 'FireworksAIProvider'. Did you mean 'client'?
utils/agents/aibitat/providers/fireworksai.ts(58,9): error TS2769: No overload matches this call.
  Overload 1 of 3, '(body: ChatCompletionCreateParamsNonStreaming, options?: RequestOptions<unknown> | undefined): APIPromise<ChatCompletion>', gave the following error.
    Type 'ChatMessage[]' is not assignable to type 'ChatCompletionMessageParam[]'.
      Type 'ChatMessage' is not assignable to type 'ChatCompletionMessageParam'.
        Type 'ChatMessage' is not assignable to type 'ChatCompletionSystemMessageParam | ChatCompletionUserMessageParam | ChatCompletionAssistantMessageParam | ChatCompletionFunctionMessageParam'.
          Type 'ChatMessage' is not assignable to type 'ChatCompletionFunctionMessageParam'.
            Types of property 'name' are incompatible.
              Type 'string | undefined' is not assignable to type 'string'.
                Type 'undefined' is not assignable to type 'string'.
  Overload 2 of 3, '(body: ChatCompletionCreateParamsStreaming, options?: RequestOptions<unknown> | undefined): APIPromise<Stream<ChatCompletionChunk>>', gave the following error.
    Type 'ChatMessage[]' is not assignable to type 'ChatCompletionMessageParam[]'.
      Type 'ChatMessage' is not assignable to type 'ChatCompletionMessageParam'.
        Type 'ChatMessage' is not assignable to type 'ChatCompletionSystemMessageParam | ChatCompletionUserMessageParam | ChatCompletionAssistantMessageParam | ChatCompletionFunctionMessageParam'.
          Type 'ChatMessage' is not assignable to type 'ChatCompletionFunctionMessageParam'.
            Types of property 'name' are incompatible.
              Type 'string | undefined' is not assignable to type 'string'.
                Type 'undefined' is not assignable to type 'string'.
  Overload 3 of 3, '(body: ChatCompletionCreateParamsBase, options?: RequestOptions<unknown> | undefined): APIPromise<ChatCompletion | Stream<...>>', gave the following error.
    Type 'ChatMessage[]' is not assignable to type 'ChatCompletionMessageParam[]'.
      Type 'ChatMessage' is not assignable to type 'ChatCompletionMessageParam'.
        Type 'ChatMessage' is not assignable to type 'ChatCompletionSystemMessageParam | ChatCompletionUserMessageParam | ChatCompletionAssistantMessageParam | ChatCompletionFunctionMessageParam'.
          Type 'ChatMessage' is not assignable to type 'ChatCompletionFunctionMessageParam'.
            Types of property 'name' are incompatible.
              Type 'string | undefined' is not assignable to type 'string'.
                Type 'undefined' is not assignable to type 'string'.
utils/agents/aibitat/providers/fireworksai.ts(87,47): error TS2339: Property 'functionCall' does not exist on type 'FireworksAIProvider'.
utils/agents/aibitat/providers/fireworksai.ts(94,16): error TS2339: Property 'providerLog' does not exist on type 'FireworksAIProvider'.
utils/agents/aibitat/providers/fireworksai.ts(95,16): error TS2339: Property 'deduplicator' does not exist on type 'FireworksAIProvider'.
utils/agents/aibitat/providers/fireworksai.ts(109,14): error TS2339: Property 'providerLog' does not exist on type 'FireworksAIProvider'.
utils/agents/aibitat/providers/fireworksai.ts(114,26): error TS2339: Property 'cleanMsgs' does not exist on type 'FireworksAIProvider'.
utils/agents/aibitat/providers/fireworksai.ts(116,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content: string; }'.
  Types of property 'content' are incompatible.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
utils/agents/aibitat/providers/fireworksai.ts(122,12): error TS2339: Property 'deduplicator' does not exist on type 'FireworksAIProvider'.
utils/agents/aibitat/providers/fireworksai.ts(125,17): error TS18048: 'completion' is possibly 'undefined'.
utils/agents/aibitat/providers/genericOpenAi.ts(6,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.
utils/agents/aibitat/providers/genericOpenAi.ts(38,54): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.
  Cannot assign an abstract constructor type to a non-abstract constructor type.
utils/agents/aibitat/providers/genericOpenAi.ts(68,5): error TS2322: Type 'number | null' is not assignable to type 'number'.
  Type 'null' is not assignable to type 'number'.
utils/agents/aibitat/providers/genericOpenAi.ts(114,36): error TS2339: Property 'functionCall' does not exist on type 'GenericOpenAiProvider'.
utils/agents/aibitat/providers/genericOpenAi.ts(124,16): error TS2339: Property 'deduplicator' does not exist on type 'GenericOpenAiProvider'.
utils/agents/aibitat/providers/genericOpenAi.ts(159,32): error TS2339: Property 'cleanMsgs' does not exist on type 'GenericOpenAiProvider'.
utils/agents/aibitat/providers/genericOpenAi.ts(163,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.
  Types of property 'content' are incompatible.
    Type 'string | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/genericOpenAi.ts(170,12): error TS2339: Property 'deduplicator' does not exist on type 'GenericOpenAiProvider'.
utils/agents/aibitat/providers/genericOpenAi.ts(172,17): error TS18048: 'completion' is possibly 'undefined'.
utils/agents/aibitat/providers/groq.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.
utils/agents/aibitat/providers/groq.ts(33,45): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.
  Cannot assign an abstract constructor type to a non-abstract constructor type.
utils/agents/aibitat/providers/groq.ts(100,36): error TS2339: Property 'functionCall' does not exist on type 'GroqProvider'.
utils/agents/aibitat/providers/groq.ts(110,16): error TS2339: Property 'deduplicator' does not exist on type 'GroqProvider'.
utils/agents/aibitat/providers/groq.ts(132,26): error TS2339: Property 'cleanMsgs' does not exist on type 'GroqProvider'.
utils/agents/aibitat/providers/groq.ts(134,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.
  Types of property 'content' are incompatible.
    Type 'string | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/groq.ts(140,12): error TS2339: Property 'deduplicator' does not exist on type 'GroqProvider'.
utils/agents/aibitat/providers/groq.ts(142,17): error TS18048: 'completion' is possibly 'undefined'.
utils/agents/aibitat/providers/helpers/untooled.ts(3,15): error TS2305: Module '"../../../../../types/chat-agent"' has no exported member 'AgentFunction'.
utils/agents/aibitat/providers/helpers/untooled.ts(3,30): error TS2459: Module '"../../../../../types/chat-agent"' declares 'ChatMessage' locally, but it is not exported.
utils/agents/aibitat/providers/koboldcpp.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.
utils/agents/aibitat/providers/koboldcpp.ts(32,50): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.
  Cannot assign an abstract constructor type to a non-abstract constructor type.
utils/agents/aibitat/providers/koboldcpp.ts(101,36): error TS2339: Property 'functionCall' does not exist on type 'KoboldCPPProvider'.
utils/agents/aibitat/providers/koboldcpp.ts(111,16): error TS2339: Property 'deduplicator' does not exist on type 'KoboldCPPProvider'.
utils/agents/aibitat/providers/koboldcpp.ts(133,26): error TS2339: Property 'cleanMsgs' does not exist on type 'KoboldCPPProvider'.
utils/agents/aibitat/providers/koboldcpp.ts(135,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.
  Types of property 'content' are incompatible.
    Type 'string | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/koboldcpp.ts(141,12): error TS2339: Property 'deduplicator' does not exist on type 'KoboldCPPProvider'.
utils/agents/aibitat/providers/koboldcpp.ts(143,17): error TS18048: 'completion' is possibly 'undefined'.
utils/agents/aibitat/providers/lmstudio.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.
utils/agents/aibitat/providers/lmstudio.ts(32,49): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.
  Cannot assign an abstract constructor type to a non-abstract constructor type.
utils/agents/aibitat/providers/lmstudio.ts(101,36): error TS2339: Property 'functionCall' does not exist on type 'LMStudioProvider'.
utils/agents/aibitat/providers/lmstudio.ts(111,16): error TS2339: Property 'deduplicator' does not exist on type 'LMStudioProvider'.
utils/agents/aibitat/providers/lmstudio.ts(133,26): error TS2339: Property 'cleanMsgs' does not exist on type 'LMStudioProvider'.
utils/agents/aibitat/providers/lmstudio.ts(135,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.
  Types of property 'content' are incompatible.
    Type 'string | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/lmstudio.ts(141,12): error TS2339: Property 'deduplicator' does not exist on type 'LMStudioProvider'.
utils/agents/aibitat/providers/lmstudio.ts(143,17): error TS18048: 'completion' is possibly 'undefined'.
utils/agents/aibitat/providers/localai.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.
utils/agents/aibitat/providers/localai.ts(33,48): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.
  Cannot assign an abstract constructor type to a non-abstract constructor type.
utils/agents/aibitat/providers/localai.ts(103,36): error TS2339: Property 'functionCall' does not exist on type 'LocalAiProvider'.
utils/agents/aibitat/providers/localai.ts(113,16): error TS2339: Property 'deduplicator' does not exist on type 'LocalAiProvider'.
utils/agents/aibitat/providers/localai.ts(135,26): error TS2339: Property 'cleanMsgs' does not exist on type 'LocalAiProvider'.
utils/agents/aibitat/providers/localai.ts(137,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.
  Types of property 'content' are incompatible.
    Type 'string | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/localai.ts(143,12): error TS2339: Property 'deduplicator' does not exist on type 'LocalAiProvider'.
utils/agents/aibitat/providers/localai.ts(145,17): error TS18048: 'completion' is possibly 'undefined'.
utils/agents/aibitat/providers/mistral.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.
utils/agents/aibitat/providers/mistral.ts(27,48): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.
  Cannot assign an abstract constructor type to a non-abstract constructor type.
utils/agents/aibitat/providers/mistral.ts(88,35): error TS2339: Property 'functionCall' does not exist on type 'MistralProvider'.
utils/agents/aibitat/providers/mistral.ts(98,16): error TS2339: Property 'deduplicator' does not exist on type 'MistralProvider'.
utils/agents/aibitat/providers/mistral.ts(120,26): error TS2339: Property 'cleanMsgs' does not exist on type 'MistralProvider'.
utils/agents/aibitat/providers/mistral.ts(122,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.
  Types of property 'content' are incompatible.
    Type 'string | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/mistral.ts(128,12): error TS2339: Property 'deduplicator' does not exist on type 'MistralProvider'.
utils/agents/aibitat/providers/mistral.ts(130,17): error TS18048: 'completion' is possibly 'undefined'.
utils/agents/aibitat/providers/ollama.ts(8,3): error TS6196: 'LangChainModelConfig' is declared but never used.
utils/agents/aibitat/providers/ollama.ts(9,8): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.
utils/agents/aibitat/providers/ollama.ts(34,47): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.
  Cannot assign an abstract constructor type to a non-abstract constructor type.
utils/agents/aibitat/providers/ollama.ts(82,35): error TS2339: Property 'functionCall' does not exist on type 'OllamaProvider'.
utils/agents/aibitat/providers/ollama.ts(92,16): error TS2339: Property 'deduplicator' does not exist on type 'OllamaProvider'.
utils/agents/aibitat/providers/ollama.ts(114,26): error TS2339: Property 'cleanMsgs' does not exist on type 'OllamaProvider'.
utils/agents/aibitat/providers/ollama.ts(126,12): error TS2339: Property 'deduplicator' does not exist on type 'OllamaProvider'.
utils/agents/aibitat/providers/openai.ts(4,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.
utils/agents/aibitat/providers/openrouter.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.
utils/agents/aibitat/providers/openrouter.ts(32,51): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.
  Cannot assign an abstract constructor type to a non-abstract constructor type.
utils/agents/aibitat/providers/openrouter.ts(105,36): error TS2339: Property 'functionCall' does not exist on type 'OpenRouterProvider'.
utils/agents/aibitat/providers/openrouter.ts(115,16): error TS2339: Property 'deduplicator' does not exist on type 'OpenRouterProvider'.
utils/agents/aibitat/providers/openrouter.ts(137,26): error TS2339: Property 'cleanMsgs' does not exist on type 'OpenRouterProvider'.
utils/agents/aibitat/providers/openrouter.ts(139,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.
  Types of property 'content' are incompatible.
    Type 'string | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/openrouter.ts(145,12): error TS2339: Property 'deduplicator' does not exist on type 'OpenRouterProvider'.
utils/agents/aibitat/providers/openrouter.ts(147,17): error TS18048: 'completion' is possibly 'undefined'.
utils/agents/aibitat/providers/perplexity.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.
utils/agents/aibitat/providers/perplexity.ts(32,51): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.
  Cannot assign an abstract constructor type to a non-abstract constructor type.
utils/agents/aibitat/providers/perplexity.ts(101,36): error TS2339: Property 'functionCall' does not exist on type 'PerplexityProvider'.
utils/agents/aibitat/providers/perplexity.ts(111,16): error TS2339: Property 'deduplicator' does not exist on type 'PerplexityProvider'.
utils/agents/aibitat/providers/perplexity.ts(133,26): error TS2339: Property 'cleanMsgs' does not exist on type 'PerplexityProvider'.
utils/agents/aibitat/providers/perplexity.ts(135,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.
  Types of property 'content' are incompatible.
    Type 'string | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/perplexity.ts(141,12): error TS2339: Property 'deduplicator' does not exist on type 'PerplexityProvider'.
utils/agents/aibitat/providers/perplexity.ts(143,17): error TS18048: 'completion' is possibly 'undefined'.
utils/agents/aibitat/providers/textgenwebui.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.
utils/agents/aibitat/providers/textgenwebui.ts(32,53): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.
  Cannot assign an abstract constructor type to a non-abstract constructor type.
utils/agents/aibitat/providers/textgenwebui.ts(101,36): error TS2339: Property 'functionCall' does not exist on type 'TextWebGenUiProvider'.
utils/agents/aibitat/providers/textgenwebui.ts(111,16): error TS2339: Property 'deduplicator' does not exist on type 'TextWebGenUiProvider'.
utils/agents/aibitat/providers/textgenwebui.ts(133,26): error TS2339: Property 'cleanMsgs' does not exist on type 'TextWebGenUiProvider'.
utils/agents/aibitat/providers/textgenwebui.ts(135,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.
  Types of property 'content' are incompatible.
    Type 'string | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/textgenwebui.ts(141,12): error TS2339: Property 'deduplicator' does not exist on type 'TextWebGenUiProvider'.
utils/agents/aibitat/providers/textgenwebui.ts(143,17): error TS18048: 'completion' is possibly 'undefined'.
utils/agents/aibitat/providers/togetherai.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.
utils/agents/aibitat/providers/togetherai.ts(32,51): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.
  Cannot assign an abstract constructor type to a non-abstract constructor type.
utils/agents/aibitat/providers/togetherai.ts(101,36): error TS2339: Property 'functionCall' does not exist on type 'TogetherAIProvider'.
utils/agents/aibitat/providers/togetherai.ts(111,16): error TS2339: Property 'deduplicator' does not exist on type 'TogetherAIProvider'.
utils/agents/aibitat/providers/togetherai.ts(133,26): error TS2339: Property 'cleanMsgs' does not exist on type 'TogetherAIProvider'.
utils/agents/aibitat/providers/togetherai.ts(135,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.
  Types of property 'content' are incompatible.
    Type 'string | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/togetherai.ts(141,12): error TS2339: Property 'deduplicator' does not exist on type 'TogetherAIProvider'.
utils/agents/aibitat/providers/togetherai.ts(143,17): error TS18048: 'completion' is possibly 'undefined'.
utils/agents/aibitat/providers/xai.ts(5,49): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.
utils/agents/aibitat/providers/xai.ts(32,44): error TS2322: Type 'typeof Provider' is not assignable to type 'Constructor<{}>'.
  Cannot assign an abstract constructor type to a non-abstract constructor type.
utils/agents/aibitat/providers/xai.ts(99,36): error TS2339: Property 'functionCall' does not exist on type 'XAIProvider'.
utils/agents/aibitat/providers/xai.ts(109,16): error TS2339: Property 'deduplicator' does not exist on type 'XAIProvider'.
utils/agents/aibitat/providers/xai.ts(131,26): error TS2339: Property 'cleanMsgs' does not exist on type 'XAIProvider'.
utils/agents/aibitat/providers/xai.ts(133,9): error TS2322: Type 'ChatCompletionMessage' is not assignable to type '{ content?: string | undefined; }'.
  Types of property 'content' are incompatible.
    Type 'string | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/agents/aibitat/providers/xai.ts(139,12): error TS2339: Property 'deduplicator' does not exist on type 'XAIProvider'.
utils/agents/aibitat/providers/xai.ts(141,17): error TS18048: 'completion' is possibly 'undefined'.
utils/agents/aibitat/utils/summarize.ts(5,40): error TS2307: Cannot find module '../../../types/chat-agent' or its corresponding type declarations.
utils/agents/defaults.ts(2,10): error TS2614: Module '"../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../models/systemSettings"' instead?
utils/agents/defaults.ts(18,7): error TS2322: Type 'string' is not assignable to type 'InterruptConfig'.
utils/agents/defaults.ts(56,3): error TS2531: Object is possibly 'null'.
utils/agents/ephemeral.ts(19,3): error TS6196: 'FilteredUser' is declared but never used.
utils/agents/ephemeral.ts(20,3): error TS6196: 'WorkspaceThread' is declared but never used.
utils/agents/ephemeral.ts(24,18): error TS6196: 'AgentChatMessage' is declared but never used.
utils/agents/ephemeral.ts(87,26): error TS2531: Object is possibly 'null'.
utils/agents/ephemeral.ts(158,12): error TS7006: Parameter 'child' implicitly has an 'any' type.
utils/agents/ephemeral.ts(171,9): error TS2531: Object is possibly 'null'.
utils/agents/ephemeral.ts(190,26): error TS18048: 'plugin' is possibly 'undefined'.
utils/agents/ephemeral.ts(191,9): error TS2531: Object is possibly 'null'.
utils/agents/ephemeral.ts(191,26): error TS18048: 'plugin' is possibly 'undefined'.
utils/agents/ephemeral.ts(193,23): error TS18048: 'plugin' is possibly 'undefined'.
utils/agents/ephemeral.ts(210,7): error TS2531: Object is possibly 'null'.
utils/agents/ephemeral.ts(239,7): error TS2322: Type 'ChatHistoryEntry[]' is not assignable to type 'ChatMessage[]'.
  Type 'ChatHistoryEntry' is missing the following properties from type 'ChatMessage': role, timestamp
utils/agents/ephemeral.ts(243,25): error TS2531: Object is possibly 'null'.
utils/agents/ephemeral.ts(365,11): error TS2322: Type 'null' is not assignable to type 'string | boolean'.
utils/agents/ephemeral.ts(375,9): error TS2322: Type 'null' is not assignable to type 'string | boolean'.
utils/agents/imported.ts(65,11): error TS2322: Type 'PluginConfig | null' is not assignable to type 'PluginConfig'.
  Type 'null' is not assignable to type 'PluginConfig'.
utils/agents/imported.ts(102,13): error TS2322: Type 'PluginConfig | null' is not assignable to type 'PluginConfig'.
  Type 'null' is not assignable to type 'PluginConfig'.
utils/agents/imported.ts(126,13): error TS2322: Type 'PluginConfig | null' is not assignable to type 'PluginConfig'.
  Type 'null' is not assignable to type 'PluginConfig'.
utils/agents/imported.ts(154,29): error TS2698: Spread types may only be created from object types.
utils/agents/imported.ts(197,13): error TS7006: Parameter 'aibitat' implicitly has an 'any' type.
utils/agents/index.ts(9,1): error TS6192: All imports in import declaration are unused.
utils/agents/index.ts(15,3): error TS2724: '"../../types/agents"' has no exported member named 'AgentInvocation'. Did you mean 'AgentFunction'?
utils/agents/index.ts(16,3): error TS6196: 'AgentConfig' is declared but never used.
utils/agents/index.ts(17,3): error TS6196: 'AgentFunction' is declared but never used.
utils/agents/index.ts(18,18): error TS6196: 'AgentChatMessage' is declared but never used.
utils/agents/index.ts(153,12): error TS2678: Type '"gemini"' is not comparable to type 'AgentProviderType | null'.
utils/agents/index.ts(165,12): error TS2678: Type '"generic-openai"' is not comparable to type 'AgentProviderType | null'.
utils/agents/index.ts(228,9): error TS2322: Type 'null' is not assignable to type 'string'.
utils/agents/index.ts(229,12): error TS2678: Type '"gemini"' is not comparable to type 'AgentProviderType | null'.
utils/agents/index.ts(232,9): error TS2322: Type 'null' is not assignable to type 'string'.
utils/agents/index.ts(237,12): error TS2678: Type '"generic-openai"' is not comparable to type 'AgentProviderType | null'.
utils/agents/index.ts(238,9): error TS2322: Type 'null' is not assignable to type 'string'.
utils/agents/index.ts(242,9): error TS2322: Type 'null' is not assignable to type 'string'.
utils/agents/index.ts(244,9): error TS2322: Type 'null' is not assignable to type 'string'.
utils/agents/index.ts(246,9): error TS2322: Type 'null' is not assignable to type 'string'.
utils/agents/index.ts(371,12): error TS7006: Parameter 'child' implicitly has an 'any' type.
utils/agents/index.ts(385,9): error TS2531: Object is possibly 'null'.
utils/agents/index.ts(403,13): error TS2345: Argument of type 'AIbitat | null' is not assignable to parameter of type 'Aibitat | null | undefined'.
  Property 'introspect' is missing in type 'AIbitat' but required in type 'Aibitat'.
utils/agents/index.ts(414,9): error TS2531: Object is possibly 'null'.
utils/agents/index.ts(414,9): error TS2532: Object is possibly 'undefined'.
utils/agents/index.ts(414,55): error TS2531: Object is possibly 'null'.
utils/agents/index.ts(414,55): error TS2532: Object is possibly 'undefined'.
utils/agents/index.ts(414,55): error TS2532: Object is possibly 'undefined'.
utils/agents/index.ts(418,11): error TS2531: Object is possibly 'null'.
utils/agents/index.ts(418,11): error TS2532: Object is possibly 'undefined'.
utils/agents/index.ts(418,11): error TS2532: Object is possibly 'undefined'.
utils/agents/index.ts(418,60): error TS2345: Argument of type 'string' is not assignable to parameter of type 'AgentFunction'.
utils/agents/index.ts(421,11): error TS2531: Object is possibly 'null'.
utils/agents/index.ts(441,26): error TS18048: 'plugin' is possibly 'undefined'.
utils/agents/index.ts(442,9): error TS2531: Object is possibly 'null'.
utils/agents/index.ts(442,26): error TS18048: 'plugin' is possibly 'undefined'.
utils/agents/index.ts(444,23): error TS18048: 'plugin' is possibly 'undefined'.
utils/agents/index.ts(462,7): error TS2531: Object is possibly 'null'.
utils/agents/index.ts(476,5): error TS2322: Type 'AgentFunction[]' is not assignable to type 'string[]'.
  Type 'AgentFunction' is not assignable to type 'string'.
utils/agents/index.ts(492,7): error TS2322: Type 'ChatHistoryEntry[]' is not assignable to type 'ChatMessage[]'.
  Type 'ChatHistoryEntry' is missing the following properties from type 'ChatMessage': role, timestamp
utils/AiProviders/anthropic/index.ts(203,48): error TS2339: Property 'text' does not exist on type 'ContentBlock'.
  Property 'text' does not exist on type 'ToolUseBlock'.
utils/AiProviders/anthropic/index.ts(214,54): error TS2739: Type '{}' is missing the following properties from type 'UsageMetrics': prompt_tokens, completion_tokens, total_tokens
utils/AiProviders/anthropic/index.ts(245,7): error TS2345: Argument of type 'MessageStream' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Type 'MessageStream' is missing the following properties from type 'Promise<OpenAICompatibleStream>': then, catch, finally, [Symbol.toStringTag]
utils/AiProviders/anthropic/index.ts(388,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.
  Type 'null' is not assignable to type 'number[][]'.
utils/AiProviders/azureOpenAi/index.ts(12,3): error TS6133: 'LLMProviderStatic' is declared but its value is never read.
utils/AiProviders/azureOpenAi/index.ts(39,23): error TS2769: No overload matches this call.
  Overload 1 of 2, '(message?: string | undefined, options?: ErrorOptions | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
  Overload 2 of 2, '(message?: string | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
utils/AiProviders/azureOpenAi/index.ts(41,23): error TS2769: No overload matches this call.
  Overload 1 of 2, '(message?: string | undefined, options?: ErrorOptions | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
  Overload 2 of 2, '(message?: string | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
utils/AiProviders/azureOpenAi/index.ts(117,9): error TS2353: Object literal may only specify known properties, and 'imageUrl' does not exist in type '{ type: string; text: string; }'.
utils/AiProviders/azureOpenAi/index.ts(139,7): error TS2322: Type 'ChatMessage | { role: "user"; content: string | { type: string; text?: string | undefined; imageUrl?: { url: string; } | undefined; }[]; }' is not assignable to type 'ChatMessage'.
  Type '{ role: "user"; content: string | { type: string; text?: string; imageUrl?: { url: string; }; }[]; }' is not assignable to type 'ChatMessage'.
    Types of property 'content' are incompatible.
      Type 'string | { type: string; text?: string | undefined; imageUrl?: { url: string; } | undefined; }[]' is not assignable to type 'string | (TextContent | ImageContent)[]'.
        Type '{ type: string; text?: string | undefined; imageUrl?: { url: string; } | undefined; }[]' is not assignable to type 'string | (TextContent | ImageContent)[]'.
          Type '{ type: string; text?: string | undefined; imageUrl?: { url: string; } | undefined; }[]' is not assignable to type '(TextContent | ImageContent)[]'.
            Type '{ type: string; text?: string | undefined; imageUrl?: { url: string; } | undefined; }' is not assignable to type 'TextContent | ImageContent'.
              Type '{ type: string; text?: string | undefined; imageUrl?: { url: string; } | undefined; }' is not assignable to type 'TextContent'.
                Types of property 'type' are incompatible.
                  Type 'string' is not assignable to type '"text"'.
utils/AiProviders/azureOpenAi/index.ts(142,9): error TS2322: Type 'string | { type: string; text?: string | undefined; imageUrl?: { url: string; } | undefined; }[]' is not assignable to type 'string | (TextContent | ImageContent)[]'.
  Type '{ type: string; text?: string | undefined; imageUrl?: { url: string; } | undefined; }[]' is not assignable to type 'string | (TextContent | ImageContent)[]'.
    Type '{ type: string; text?: string | undefined; imageUrl?: { url: string; } | undefined; }[]' is not assignable to type '(TextContent | ImageContent)[]'.
      Type '{ type: string; text?: string | undefined; imageUrl?: { url: string; } | undefined; }' is not assignable to type 'TextContent | ImageContent'.
        Type '{ type: string; text?: string | undefined; imageUrl?: { url: string; } | undefined; }' is not assignable to type 'TextContent'.
          Types of property 'type' are incompatible.
            Type 'string' is not assignable to type '"text"'.
utils/AiProviders/azureOpenAi/index.ts(154,38): error TS2769: No overload matches this call.
  Overload 1 of 2, '(message?: string | undefined, options?: ErrorOptions | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
  Overload 2 of 2, '(message?: string | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
utils/AiProviders/azureOpenAi/index.ts(163,7): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/azureOpenAi/index.ts(167,21): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/azureOpenAi/index.ts(169,24): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/azureOpenAi/index.ts(170,28): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/azureOpenAi/index.ts(171,23): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/azureOpenAi/index.ts(172,20): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/azureOpenAi/index.ts(182,38): error TS2769: No overload matches this call.
  Overload 1 of 2, '(message?: string | undefined, options?: ErrorOptions | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
  Overload 2 of 2, '(message?: string | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
utils/AiProviders/azureOpenAi/index.ts(189,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/bedrock/index.ts(208,9): error TS2353: Object literal may only specify known properties, and 'image_url' does not exist in type '{ type: string; text: string; }'.
utils/AiProviders/bedrock/index.ts(272,41): error TS2339: Property 'length' does not exist on type '{}'.
utils/AiProviders/bedrock/index.ts(275,60): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/bedrock/index.ts(277,9): error TS2322: Type '{}' is not assignable to type 'string'.
utils/AiProviders/bedrock/index.ts(281,7): error TS2322: Type '{}' is not assignable to type 'string'.
utils/AiProviders/bedrock/index.ts(301,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/cohere/index.ts(161,22): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'object'.
utils/AiProviders/cohere/index.ts(162,7): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/cohere/index.ts(166,26): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/cohere/index.ts(167,30): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/cohere/index.ts(169,21): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/cohere/index.ts(198,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/deepseek/index.ts(128,22): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'object'.
utils/AiProviders/deepseek/index.ts(129,7): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/deepseek/index.ts(134,21): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/deepseek/index.ts(136,24): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/deepseek/index.ts(137,28): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/deepseek/index.ts(138,23): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/deepseek/index.ts(139,20): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/deepseek/index.ts(165,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/fireworksAi/index.ts(41,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/AiProviders/fireworksAi/index.ts(134,20): error TS18048: 'result.output.usage.completion_tokens' is possibly 'undefined'.
utils/AiProviders/fireworksAi/index.ts(156,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/fireworksAi/index.ts(175,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.
  Type 'null' is not assignable to type 'number[][]'.
utils/AiProviders/gemini/index.ts(235,13): error TS18046: 'data' is of type 'unknown'.
utils/AiProviders/gemini/index.ts(235,41): error TS18046: 'data' is of type 'unknown'.
utils/AiProviders/gemini/index.ts(236,16): error TS18046: 'data' is of type 'unknown'.
utils/AiProviders/gemini/index.ts(377,9): error TS2322: Type 'string | { text?: string | undefined; inlineData?: { data: string; mimeType: string; } | undefined; }[]' is not assignable to type 'string | (TextContent | ImageContent)[]'.
  Type '{ text?: string | undefined; inlineData?: { data: string; mimeType: string; } | undefined; }[]' is not assignable to type 'string | (TextContent | ImageContent)[]'.
    Type '{ text?: string | undefined; inlineData?: { data: string; mimeType: string; } | undefined; }[]' is not assignable to type '(TextContent | ImageContent)[]'.
      Type '{ text?: string | undefined; inlineData?: { data: string; mimeType: string; } | undefined; }' is not assignable to type 'TextContent | ImageContent'.
        Property 'type' is missing in type '{ text?: string | undefined; inlineData?: { data: string; mimeType: string; } | undefined; }' but required in type 'TextContent'.
utils/AiProviders/gemini/index.ts(476,39): error TS2339: Property 'text' does not exist on type '{}'.
utils/AiProviders/gemini/index.ts(480,60): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/gemini/index.ts(513,9): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/genericOpenAi/index.ts(56,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/AiProviders/genericOpenAi/index.ts(64,5): error TS2322: Type 'number | null' is not assignable to type 'number'.
  Type 'null' is not assignable to type 'number'.
utils/AiProviders/genericOpenAi/index.ts(240,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/genericOpenAi/index.ts(260,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.
  Type 'null' is not assignable to type 'number[][]'.
utils/AiProviders/groq/index.ts(107,9): error TS2353: Object literal may only specify known properties, and 'image_url' does not exist in type '{ type: string; text: string; }'.
utils/AiProviders/groq/index.ts(176,9): error TS2322: Type 'string | { type: string; text?: string | undefined; image_url?: { url: string; } | undefined; }[]' is not assignable to type 'string | (TextContent | ImageContent)[]'.
  Type '{ type: string; text?: string | undefined; image_url?: { url: string; } | undefined; }[]' is not assignable to type 'string | (TextContent | ImageContent)[]'.
    Type '{ type: string; text?: string | undefined; image_url?: { url: string; } | undefined; }[]' is not assignable to type '(TextContent | ImageContent)[]'.
      Type '{ type: string; text?: string | undefined; image_url?: { url: string; } | undefined; }' is not assignable to type 'TextContent | ImageContent'.
        Type '{ type: string; text?: string | undefined; image_url?: { url: string; } | undefined; }' is not assignable to type 'ImageContent'.
          Types of property 'type' are incompatible.
            Type 'string' is not assignable to type '"image" | "image_url"'.
utils/AiProviders/groq/index.ts(226,22): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'object'.
utils/AiProviders/groq/index.ts(227,7): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/groq/index.ts(232,21): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/groq/index.ts(234,24): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/groq/index.ts(235,28): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/groq/index.ts(236,23): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/groq/index.ts(238,11): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/groq/index.ts(239,11): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/groq/index.ts(240,19): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/groq/index.ts(261,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/huggingface/index.ts(162,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/huggingface/index.ts(180,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.
  Type 'null' is not assignable to type 'number[][]'.
utils/AiProviders/koboldCPP/index.ts(45,7): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
utils/AiProviders/koboldCPP/index.ts(161,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/koboldCPP/index.ts(240,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.
  Type 'null' is not assignable to type 'number[][]'.
utils/AiProviders/liteLLM/index.ts(35,23): error TS2769: No overload matches this call.
  Overload 1 of 2, '(message?: string | undefined, options?: ErrorOptions | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
  Overload 2 of 2, '(message?: string | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
utils/AiProviders/liteLLM/index.ts(41,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/AiProviders/liteLLM/index.ts(51,38): error TS2769: No overload matches this call.
  Overload 1 of 2, '(message?: string | undefined, options?: ErrorOptions | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
  Overload 2 of 2, '(message?: string | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
utils/AiProviders/liteLLM/index.ts(162,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/liteLLM/index.ts(180,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.
  Type 'null' is not assignable to type 'number[][]'.
utils/AiProviders/lmStudio/index.ts(36,7): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
utils/AiProviders/lmStudio/index.ts(145,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/lmStudio/index.ts(163,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.
  Type 'null' is not assignable to type 'number[][]'.
utils/AiProviders/localAi/index.ts(37,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/AiProviders/localAi/index.ts(66,23): error TS2769: No overload matches this call.
  Overload 1 of 2, '(message?: string | undefined, options?: ErrorOptions | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
  Overload 2 of 2, '(message?: string | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
utils/AiProviders/localAi/index.ts(75,23): error TS2769: No overload matches this call.
  Overload 1 of 2, '(message?: string | undefined, options?: ErrorOptions | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
  Overload 2 of 2, '(message?: string | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
utils/AiProviders/localAi/index.ts(147,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/localAi/index.ts(165,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.
  Type 'null' is not assignable to type 'number[][]'.
utils/AiProviders/mistral/index.ts(161,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/mistral/index.ts(179,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.
  Type 'null' is not assignable to type 'number[][]'.
utils/AiProviders/native/index.ts(29,44): error TS2556: A spread argument must either have a tuple type or be passed to a rest parameter.
utils/AiProviders/native/index.ts(154,25): error TS2339: Property 'content' does not exist on type '{}'.
utils/AiProviders/native/index.ts(156,60): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/native/index.ts(158,21): error TS2339: Property 'content' does not exist on type '{}'.
utils/AiProviders/native/index.ts(161,35): error TS2339: Property 'content' does not exist on type '{}'.
utils/AiProviders/native/index.ts(181,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/native/index.ts(199,64): error TS2345: Argument of type 'string' is not assignable to parameter of type 'Message[]'.
utils/AiProviders/native/index.ts(233,62): error TS2345: Argument of type 'string' is not assignable to parameter of type 'Message[]'.
utils/AiProviders/native/index.ts(244,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.
  Type 'null' is not assignable to type 'number[][]'.
utils/AiProviders/ollama/index.ts(1,1): error TS6133: 'uuidv4' is declared but its value is never read.
utils/AiProviders/ollama/index.ts(4,1): error TS6192: All imports in import declaration are unused.
utils/AiProviders/ollama/index.ts(363,9): error TS2416: Property 'constructPrompt' in type 'OllamaAILLM' is not assignable to the same property in base type 'LLMProvider'.
  Type '({ systemPrompt, contextTexts, chatHistory, userPrompt, attachments, }: PromptArgs) => Promise<OllamaMessage[]>' is not assignable to type '(args: PromptArgs) => Promise<ChatMessage[]>'.
    Type 'Promise<OllamaMessage[]>' is not assignable to type 'Promise<ChatMessage[]>'.
      Type 'OllamaMessage[]' is not assignable to type 'ChatMessage[]'.
        Type 'OllamaMessage' is not assignable to type 'ChatMessage'.
          Types of property 'role' are incompatible.
            Type 'string' is not assignable to type '"user" | "system" | "assistant"'.
utils/AiProviders/ollama/index.ts(487,30): error TS2339: Property 'then' does not exist on type 'never'.
utils/AiProviders/ollama/index.ts(494,15): error TS2345: Argument of type 'AbortableAsyncIterator<ChatResponse>' is not assignable to parameter of type 'Promise<OpenAICompatibleStream>'.
  Type 'AbortableAsyncIterator<ChatResponse>' is missing the following properties from type 'Promise<OpenAICompatibleStream>': then, catch, finally, [Symbol.toStringTag]
utils/AiProviders/ollama/index.ts(506,11): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/ollama/index.ts(542,44): error TS2345: Argument of type 'MonitoredStream' is not assignable to parameter of type 'MeasuredStream'.
  Types of property 'endMeasurement' are incompatible.
    Type '(reportedUsage?: UsageReport | undefined) => StreamMetrics' is not assignable to type '(usage: UsageMetrics) => void'.
      Types of parameters 'reportedUsage' and 'usage' are incompatible.
        Type 'UsageMetrics' is not assignable to type 'UsageReport'.
          Index signature for type 'string' is missing in type 'UsageMetrics'.
utils/AiProviders/ollama/index.ts(550,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.
  Type 'null' is not assignable to type 'number[][]'.
utils/AiProviders/openAi/index.ts(8,10): error TS2724: '"../../EmbeddingEngines/openAi"' has no exported member named 'OpenAIEmbedder'. Did you mean 'OpenAiEmbedder'?
utils/AiProviders/openAi/index.ts(11,3): error TS6133: 'LLMProviderStatic' is declared but its value is never read.
utils/AiProviders/openAi/index.ts(30,11): error TS2564: Property '_useDeepSearch' has no initializer and is not definitely assigned in the constructor.
utils/AiProviders/openAi/index.ts(30,11): error TS6133: '_useDeepSearch' is declared but its value is never read.
utils/AiProviders/openAi/index.ts(50,10): error TS2551: Property 'useDeepSearch' does not exist on type 'OpenAiLLM'. Did you mean '_useDeepSearch'?
utils/AiProviders/openAi/index.ts(213,11): error TS2769: No overload matches this call.
  Overload 1 of 3, '(body: ChatCompletionCreateParamsNonStreaming, options?: RequestOptions<unknown> | undefined): APIPromise<ChatCompletion>', gave the following error.
    Type 'ChatMessage[]' is not assignable to type 'ChatCompletionMessageParam[]'.
      Type 'ChatMessage' is not assignable to type 'ChatCompletionMessageParam'.
        Type 'ChatMessage' is not assignable to type 'ChatCompletionSystemMessageParam | ChatCompletionUserMessageParam | ChatCompletionAssistantMessageParam'.
          Type 'ChatMessage' is not assignable to type 'ChatCompletionAssistantMessageParam'.
            Types of property 'role' are incompatible.
              Type '"user" | "system" | "assistant"' is not assignable to type '"assistant"'.
                Type '"user"' is not assignable to type '"assistant"'.
  Overload 2 of 3, '(body: ChatCompletionCreateParamsStreaming, options?: RequestOptions<unknown> | undefined): APIPromise<Stream<ChatCompletionChunk>>', gave the following error.
    Type 'ChatMessage[]' is not assignable to type 'ChatCompletionMessageParam[]'.
      Type 'ChatMessage' is not assignable to type 'ChatCompletionMessageParam'.
        Type 'ChatMessage' is not assignable to type 'ChatCompletionSystemMessageParam | ChatCompletionUserMessageParam | ChatCompletionAssistantMessageParam'.
          Type 'ChatMessage' is not assignable to type 'ChatCompletionAssistantMessageParam'.
            Types of property 'role' are incompatible.
              Type '"user" | "system" | "assistant"' is not assignable to type '"assistant"'.
                Type '"user"' is not assignable to type '"assistant"'.
  Overload 3 of 3, '(body: ChatCompletionCreateParamsBase, options?: RequestOptions<unknown> | undefined): APIPromise<ChatCompletion | Stream<...>>', gave the following error.
    Type 'ChatMessage[]' is not assignable to type 'ChatCompletionMessageParam[]'.
      Type 'ChatMessage' is not assignable to type 'ChatCompletionMessageParam'.
        Type 'ChatMessage' is not assignable to type 'ChatCompletionSystemMessageParam | ChatCompletionUserMessageParam | ChatCompletionAssistantMessageParam'.
          Type 'ChatMessage' is not assignable to type 'ChatCompletionAssistantMessageParam'.
            Types of property 'role' are incompatible.
              Type '"user" | "system" | "assistant"' is not assignable to type '"assistant"'.
                Type '"user"' is not assignable to type '"assistant"'.
utils/AiProviders/openAi/index.ts(257,9): error TS2769: No overload matches this call.
  Overload 3 of 3, '(body: ChatCompletionCreateParamsBase, options?: RequestOptions<unknown> | undefined): APIPromise<ChatCompletion | Stream<...>>', gave the following error.
    Type 'ChatMessage[]' is not assignable to type 'ChatCompletionMessageParam[]'.
      Type 'ChatMessage' is not assignable to type 'ChatCompletionMessageParam'.
        Type 'ChatMessage' is not assignable to type 'ChatCompletionSystemMessageParam | ChatCompletionUserMessageParam | ChatCompletionAssistantMessageParam'.
          Type 'ChatMessage' is not assignable to type 'ChatCompletionAssistantMessageParam'.
            Types of property 'role' are incompatible.
              Type '"user" | "system" | "assistant"' is not assignable to type '"assistant"'.
                Type '"user"' is not assignable to type '"assistant"'.
utils/AiProviders/openAi/index.ts(263,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/openAi/index.ts(285,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.
  Type 'null' is not assignable to type 'number[][]'.
utils/AiProviders/openRouter/index.ts(36,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/AiProviders/openRouter/index.ts(154,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/openRouter/index.ts(172,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.
  Type 'null' is not assignable to type 'number[][]'.
utils/AiProviders/perplexity/index.ts(42,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/AiProviders/perplexity/index.ts(161,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/perplexity/index.ts(179,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.
  Type 'null' is not assignable to type 'number[][]'.
utils/AiProviders/textGenWebUI/index.ts(36,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/AiProviders/textGenWebUI/index.ts(148,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/textGenWebUI/index.ts(166,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.
  Type 'null' is not assignable to type 'number[][]'.
utils/AiProviders/togetherAi/index.ts(42,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/AiProviders/togetherAi/index.ts(155,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/AiProviders/togetherAi/index.ts(173,5): error TS2322: Type 'number[][] | null' is not assignable to type 'number[][]'.
  Type 'null' is not assignable to type 'number[][]'.
utils/AiProviders/xai/index.ts(164,22): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'object'.
utils/AiProviders/xai/index.ts(165,7): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/xai/index.ts(170,21): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/xai/index.ts(172,24): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/xai/index.ts(173,28): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/xai/index.ts(174,23): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/xai/index.ts(175,20): error TS18046: 'result.output' is of type 'unknown'.
utils/AiProviders/xai/index.ts(197,7): error TS2345: Argument of type 'ChatMessage[]' is not assignable to parameter of type 'Message[]'.
  Type 'ChatMessage' is not assignable to type 'Message'.
    Types of property 'content' are incompatible.
      Type 'string | (TextContent | ImageContent)[]' is not assignable to type 'string'.
        Type '(TextContent | ImageContent)[]' is not assignable to type 'string'.
utils/BackgroundWorkers/index.ts(65,7): error TS2739: Type '{ name: string; interval: string; }' is missing the following properties from type 'Job': path, timeout
utils/boot/index.ts(4,23): error TS7016: Could not find a declaration file for module '@mintplex-labs/express-ws'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/@mintplex-labs/express-ws/dist/index.js' implicitly has an 'any' type.
  Try `npm i --save-dev @types/mintplex-labs__express-ws` if it exists or add a new declaration (.d.ts) file containing `declare module '@mintplex-labs/express-ws';`
utils/boot/index.ts(11,15): error TS6196: 'BootOptions' is declared but never used.
utils/boot/index.ts(64,29): error TS2769: No overload matches this call.
  Overload 1 of 2, '(message?: string | undefined, options?: ErrorOptions | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
  Overload 2 of 2, '(message?: string | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
utils/boot/MetaGenerator.ts(2,10): error TS2614: Module '"../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../models/systemSettings"' instead?
utils/boot/MetaGenerator.ts(135,51): error TS2769: No overload matches this call.
  Overload 1 of 2, '(o: { [s: string]: string; } | ArrayLike<string>): [string, string][]', gave the following error.
    Argument of type 'Record<string, string> | undefined' is not assignable to parameter of type '{ [s: string]: string; } | ArrayLike<string>'.
      Type 'undefined' is not assignable to type '{ [s: string]: string; } | ArrayLike<string>'.
  Overload 2 of 2, '(o: {}): [string, any][]', gave the following error.
    Argument of type 'Record<string, string> | undefined' is not assignable to parameter of type '{}'.
      Type 'undefined' is not assignable to type '{}'.
utils/chats/agents.ts(1,23): error TS7016: Could not find a declaration file for module 'pluralize'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/pluralize/pluralize.js' implicitly has an 'any' type.
  Try `npm i --save-dev @types/pluralize` if it exists or add a new declaration (.d.ts) file containing `declare module 'pluralize';`
utils/chats/agents.ts(59,9): error TS2322: Type 'null' is not assignable to type 'string | boolean'.
utils/chats/agents.ts(70,7): error TS2322: Type 'null' is not assignable to type 'string | boolean'.
utils/chats/agents.ts(86,7): error TS2322: Type 'null' is not assignable to type 'string | boolean'.
utils/chats/apiChatHandler.ts(152,49): error TS2322: Type 'string | null' is not assignable to type 'string'.
  Type 'null' is not assignable to type 'string'.
utils/chats/apiChatHandler.ts(158,7): error TS2739: Type 'Workspace' is missing the following properties from type 'Workspace': name, vectorTag, createdAt, lastUpdatedAt
utils/chats/apiChatHandler.ts(159,7): error TS2322: Type 'string | null' is not assignable to type 'string'.
  Type 'null' is not assignable to type 'string'.
utils/chats/apiChatHandler.ts(201,24): error TS2352: Conversion of type 'BaseLLMProvider' to type 'LLMConnector' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'defaultTemp' is missing in type 'BaseLLMProvider' but required in type 'LLMConnector'.
utils/chats/apiChatHandler.ts(248,5): error TS2322: Type 'User | null' is not assignable to type 'User | null | undefined'.
  Type 'User' is not assignable to type 'User'. Two different types with this name exist, but they are unrelated.
    Types of property 'id' are incompatible.
      Type 'number' is not assignable to type 'string'.
utils/chats/apiChatHandler.ts(249,5): error TS2719: Type 'Workspace' is not assignable to type 'Workspace'. Two different types with this name exist, but they are unrelated.
  Types of property 'id' are incompatible.
    Type 'number' is not assignable to type 'string'.
utils/chats/apiChatHandler.ts(250,5): error TS2322: Type 'Thread | null' is not assignable to type 'Thread | null | undefined'.
  Type 'Thread' is not assignable to type 'Thread'. Two different types with this name exist, but they are unrelated.
    Types of property 'id' are incompatible.
      Type 'number' is not assignable to type 'string'.
utils/chats/apiChatHandler.ts(256,5): error TS2741: Property 'name' is missing in type 'Workspace' but required in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/DocumentManager/index").Workspace'.
utils/chats/apiChatHandler.ts(357,32): error TS2345: Argument of type 'Workspace' is not assignable to parameter of type 'Workspace'.
  Types of property 'id' are incompatible.
    Type 'number' is not assignable to type 'string'.
utils/chats/apiChatHandler.ts(403,13): error TS18047: 'chat' is possibly 'null'.
utils/chats/apiChatHandler.ts(493,9): error TS2345: Argument of type 'null' is not assignable to parameter of type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
utils/chats/apiChatHandler.ts(525,49): error TS2322: Type 'string | null' is not assignable to type 'string'.
  Type 'null' is not assignable to type 'string'.
utils/chats/apiChatHandler.ts(531,7): error TS2739: Type 'Workspace' is missing the following properties from type 'Workspace': name, vectorTag, createdAt, lastUpdatedAt
utils/chats/apiChatHandler.ts(532,7): error TS2322: Type 'string | null' is not assignable to type 'string'.
  Type 'null' is not assignable to type 'string'.
utils/chats/apiChatHandler.ts(564,11): error TS2353: Object literal may only specify known properties, and 'thoughts' does not exist in type 'ResponseChunkData'.
utils/chats/apiChatHandler.ts(571,24): error TS2352: Conversion of type 'BaseLLMProvider' to type 'LLMConnector' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'defaultTemp' is missing in type 'BaseLLMProvider' but required in type 'LLMConnector'.
utils/chats/apiChatHandler.ts(595,7): error TS2322: Type 'null' is not assignable to type 'string | boolean'.
utils/chats/apiChatHandler.ts(624,5): error TS2322: Type 'User | null' is not assignable to type 'User | null | undefined'.
  Type 'User' is not assignable to type 'User'. Two different types with this name exist, but they are unrelated.
    Types of property 'id' are incompatible.
      Type 'number' is not assignable to type 'string'.
utils/chats/apiChatHandler.ts(625,5): error TS2719: Type 'Workspace' is not assignable to type 'Workspace'. Two different types with this name exist, but they are unrelated.
  Types of property 'id' are incompatible.
    Type 'number' is not assignable to type 'string'.
utils/chats/apiChatHandler.ts(626,5): error TS2322: Type 'Thread | null' is not assignable to type 'Thread | null | undefined'.
  Type 'Thread' is not assignable to type 'Thread'. Two different types with this name exist, but they are unrelated.
    Types of property 'id' are incompatible.
      Type 'number' is not assignable to type 'string'.
utils/chats/apiChatHandler.ts(636,5): error TS2741: Property 'name' is missing in type 'Workspace' but required in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/DocumentManager/index").Workspace'.
utils/chats/apiChatHandler.ts(679,7): error TS2353: Object literal may only specify known properties, and 'metrics' does not exist in type 'ResponseChunkData'.
utils/chats/apiChatHandler.ts(713,7): error TS2322: Type 'null' is not assignable to type 'string | boolean'.
utils/chats/apiChatHandler.ts(737,32): error TS2345: Argument of type 'Workspace' is not assignable to parameter of type 'Workspace'.
  Types of property 'id' are incompatible.
    Type 'number' is not assignable to type 'string'.
utils/chats/apiChatHandler.ts(765,7): error TS2353: Object literal may only specify known properties, and 'metrics' does not exist in type 'ResponseChunkData'.
utils/chats/apiChatHandler.ts(790,7): error TS2353: Object literal may only specify known properties, and 'chatId' does not exist in type 'ResponseChunkData'.
utils/chats/apiChatHandler.ts(790,15): error TS18047: 'chat' is possibly 'null'.
utils/chats/apiChatHandler.ts(795,32): error TS2345: Argument of type '{ uuid: string; type: string; close: true; error: false; }' is not assignable to parameter of type 'ResponseChunkData'.
  Type '{ uuid: string; type: string; close: true; error: false; }' is missing the following properties from type 'ResponseChunkData': sources, textResponse
utils/chats/commands/reset.ts(16,7): error TS2322: Type '(workspace: Workspace, _message: string, msgUUID: string, user?: User | null, thread?: Thread | null) => Promise<CommandResponse>' is not assignable to type 'CommandHandler'.
  Target signature provides too few arguments. Expected 3 or more, but got 2.
utils/chats/commands/reset.ts(26,9): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.
utils/chats/commands/reset.ts(30,47): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.
utils/chats/commands/reset.ts(39,5): error TS2322: Type '"reset_chat"' is not assignable to type '"stop" | "continue" | "reset" | undefined'.
utils/chats/contextWindow.ts(16,10): error TS2614: Module '"../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../models/systemSettings"' instead?
utils/chats/contextWindow.ts(102,43): error TS2339: Property 'countFromString' does not exist on type 'typeof TokenManager'.
utils/chats/contextWindow.ts(103,41): error TS2339: Property 'countFromString' does not exist on type 'typeof TokenManager'.
utils/chats/contextWindow.ts(133,20): error TS2339: Property 'countFromString' does not exist on type 'typeof TokenManager'.
utils/chats/contextWindow.ts(134,20): error TS2339: Property 'countFromString' does not exist on type 'typeof TokenManager'.
utils/chats/contextWindow.ts(166,35): error TS2339: Property 'countFromString' does not exist on type 'typeof TokenManager'.
utils/chats/contextWindow.ts(182,35): error TS2339: Property 'countFromString' does not exist on type 'typeof TokenManager'.
utils/chats/contextWindow.ts(244,5): error TS2345: Argument of type 'DocumentData[]' is not assignable to parameter of type 'OptimizedItem[]'.
  Type 'DocumentData' is missing the following properties from type 'OptimizedItem': id, tokens
utils/chats/contextWindow.ts(254,5): error TS2345: Argument of type 'DocumentData[]' is not assignable to parameter of type 'OptimizedItem[]'.
  Type 'DocumentData' is missing the following properties from type 'OptimizedItem': id, tokens
utils/chats/contextWindow.ts(295,9): error TS2345: Argument of type 'DocumentData[]' is not assignable to parameter of type 'OptimizedItem[]'.
  Type 'DocumentData' is missing the following properties from type 'OptimizedItem': id, tokens
utils/chats/contextWindow.ts(622,45): error TS2339: Property 'countFromString' does not exist on type 'typeof TokenManager'.
utils/chats/embed.ts(87,24): error TS2352: Conversion of type 'BaseLLMProvider' to type 'LLMProvider & { promptWindowLimit(): number; streamingEnabled(): boolean; }' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'BaseLLMProvider' is missing the following properties from type 'LLMProvider': name, type, models, authenticate, and 2 more.
utils/chats/embed.ts(112,7): error TS2322: Type 'null' is not assignable to type 'string | boolean'.
utils/chats/embed.ts(132,5): error TS2741: Property 'name' is missing in type 'Workspace' but required in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/DocumentManager/index").Workspace'.
utils/chats/embed.ts(168,7): error TS2353: Object literal may only specify known properties, and 'id' does not exist in type 'ResponseChunkData'.
utils/chats/embed.ts(196,7): error TS2322: Type 'null' is not assignable to type 'string | boolean'.
utils/chats/embed.ts(203,39): error TS2339: Property 'compressMessages' does not exist on type 'LLMProvider & { promptWindowLimit(): number; streamingEnabled(): boolean; }'.
utils/chats/embed.ts(220,26): error TS2339: Property 'getChatCompletion' does not exist on type 'LLMProvider & { promptWindowLimit(): number; streamingEnabled(): boolean; }'.
utils/chats/embed.ts(221,66): error TS2339: Property 'defaultTemp' does not exist on type 'LLMProvider & { promptWindowLimit(): number; streamingEnabled(): boolean; }'.
utils/chats/embed.ts(234,39): error TS2339: Property 'streamGetChatCompletion' does not exist on type 'LLMProvider & { promptWindowLimit(): number; streamingEnabled(): boolean; }'.
utils/chats/embed.ts(235,64): error TS2339: Property 'defaultTemp' does not exist on type 'LLMProvider & { promptWindowLimit(): number; streamingEnabled(): boolean; }'.
utils/chats/embed.ts(237,39): error TS2339: Property 'handleStream' does not exist on type 'LLMProvider & { promptWindowLimit(): number; streamingEnabled(): boolean; }'.
utils/chats/embed.ts(245,5): error TS2322: Type 'string' is not assignable to type 'number'.
utils/chats/embed.ts(269,37): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.
utils/chats/flowDispatcher.ts(260,45): error TS2345: Argument of type 'FlowRunnerOptions' is not assignable to parameter of type 'FlowOptions'.
  Types of property 'chatId' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.
utils/chats/flows/configurations/mainDocFlowConfig.ts(9,10): error TS2305: Module '"../../../../types/chat-flow"' has no exported member 'FlowConfiguration'.
utils/chats/flows/configurations/mainDocFlowConfig.ts(54,9): error TS2416: Property 'process' in type 'MainDocumentExtractionProcessor' is not assignable to the same property in base type 'StageProcessor'.
  Type '(_context: FlowContext, dependencies: FlowDependencies) => Promise<Partial<FlowContext>>' is not assignable to type '(context: FlowContext, dependencies: StageDependencies) => Promise<Partial<FlowContext>>'.
    Types of parameters '_context' and 'context' are incompatible.
      Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").FlowContext' is not assignable to type 'FlowContext'.
        Types of property 'docDescriptions' are incompatible.
          Type 'string[]' is not assignable to type '{ [key: string]: any; "Doc Name": string; }[]'.
            Type 'string' is not assignable to type '{ [key: string]: any; "Doc Name": string; }'.
utils/chats/flows/configurations/mainDocFlowConfig.ts(108,9): error TS2416: Property 'process' in type 'SectionMappingProcessor' is not assignable to the same property in base type 'StageProcessor'.
  Type '(context: FlowContext, dependencies: FlowDependencies) => Promise<Partial<FlowContext>>' is not assignable to type '(context: FlowContext, dependencies: StageDependencies) => Promise<Partial<FlowContext>>'.
    Types of parameters 'context' and 'context' are incompatible.
      Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").FlowContext' is not assignable to type 'FlowContext'.
        Types of property 'docDescriptions' are incompatible.
          Type 'string[]' is not assignable to type '{ [key: string]: any; "Doc Name": string; }[]'.
            Type 'string' is not assignable to type '{ [key: string]: any; "Doc Name": string; }'.
utils/chats/flows/configurations/noMainDocFlowConfig.ts(9,10): error TS2305: Module '"../../../../types/chat-flow"' has no exported member 'FlowConfiguration'.
utils/chats/flows/configurations/noMainDocFlowConfig.ts(37,9): error TS2416: Property 'process' in type 'DocumentToSectionMappingProcessor' is not assignable to the same property in base type 'StageProcessor'.
  Type '(context: FlowContext, dependencies: FlowDependencies) => Promise<Partial<FlowContext>>' is not assignable to type '(context: FlowContext, dependencies: StageDependencies) => Promise<Partial<FlowContext>>'.
    Types of parameters 'context' and 'context' are incompatible.
      Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").FlowContext' is not assignable to type 'FlowContext'.
        Types of property 'docDescriptions' are incompatible.
          Type 'string[]' is not assignable to type '{ [key: string]: any; "Doc Name": string; }[]'.
            Type 'string' is not assignable to type '{ [key: string]: any; "Doc Name": string; }'.
utils/chats/flows/configurations/referenceFlowConfig.ts(6,10): error TS2305: Module '"../../../../types/chat-flow"' has no exported member 'FlowConfiguration'.
utils/chats/flows/configurations/referenceFlowConfig.ts(87,15): error TS2416: Property 'process' in type 'DocumentCategorizationProcessor' is not assignable to the same property in base type 'StageProcessor'.
  Type '(context: ProcessingContext, dependencies: any) => Promise<any>' is not assignable to type '(context: FlowContext, dependencies: StageDependencies) => Promise<Partial<FlowContext>>'.
    Types of parameters 'context' and 'context' are incompatible.
      Type 'FlowContext' is missing the following properties from type 'ProcessingContext': referenceFiles, reviewFiles, allPrompts, referenceAnalysis, and 6 more.
utils/chats/flows/configurations/referenceFlowConfig.ts(134,17): error TS7034: Variable 'referenceFiles' implicitly has type 'any[]' in some locations where its type cannot be determined.
utils/chats/flows/configurations/referenceFlowConfig.ts(135,17): error TS7034: Variable 'reviewFiles' implicitly has type 'any[]' in some locations where its type cannot be determined.
utils/chats/flows/configurations/referenceFlowConfig.ts(144,60): error TS7006: Parameter 'refName' implicitly has an 'any' type.
utils/chats/flows/configurations/referenceFlowConfig.ts(206,29): error TS7005: Variable 'referenceFiles' implicitly has an 'any[]' type.
utils/chats/flows/configurations/referenceFlowConfig.ts(210,26): error TS7005: Variable 'reviewFiles' implicitly has an 'any[]' type.
utils/chats/flows/configurations/referenceFlowConfig.ts(245,13): error TS7005: Variable 'referenceFiles' implicitly has an 'any[]' type.
utils/chats/flows/configurations/referenceFlowConfig.ts(246,13): error TS7005: Variable 'reviewFiles' implicitly has an 'any[]' type.
utils/chats/flows/configurations/referenceFlowConfig.ts(248,26): error TS7005: Variable 'referenceFiles' implicitly has an 'any[]' type.
utils/chats/flows/configurations/referenceFlowConfig.ts(249,23): error TS7005: Variable 'reviewFiles' implicitly has an 'any[]' type.
utils/chats/flows/configurations/referenceFlowConfig.ts(260,15): error TS2416: Property 'process' in type 'ReferenceAnalysisProcessor' is not assignable to the same property in base type 'StageProcessor'.
  Type '(context: ProcessingContext, dependencies: any) => Promise<any>' is not assignable to type '(context: FlowContext, dependencies: StageDependencies) => Promise<Partial<FlowContext>>'.
    Types of parameters 'context' and 'context' are incompatible.
      Type 'FlowContext' is missing the following properties from type 'ProcessingContext': referenceFiles, reviewFiles, allPrompts, referenceAnalysis, and 6 more.
utils/chats/flows/configurations/referenceFlowConfig.ts(369,24): error TS18046: 'error' is of type 'unknown'.
utils/chats/flows/configurations/referenceFlowConfig.ts(514,15): error TS2416: Property 'process' in type 'ReviewFileAnalysisProcessor' is not assignable to the same property in base type 'StageProcessor'.
  Type '(context: ProcessingContext, dependencies: any) => Promise<any>' is not assignable to type '(context: FlowContext, dependencies: StageDependencies) => Promise<Partial<FlowContext>>'.
    Types of parameters 'context' and 'context' are incompatible.
      Type 'FlowContext' is missing the following properties from type 'ProcessingContext': referenceFiles, reviewFiles, allPrompts, referenceAnalysis, and 6 more.
utils/chats/flows/configurations/referenceFlowConfig.ts(634,24): error TS18046: 'error' is of type 'unknown'.
utils/chats/flows/configurations/referenceFlowConfig.ts(769,11): error TS2322: Type '{ type: string; severity: string; requirement: string; issue: string; location: string; recommendation: string; }[]' is not assignable to type 'ComplianceIssue[]'.
  Type '{ type: string; severity: string; requirement: string; issue: string; location: string; recommendation: string; }' is not assignable to type 'ComplianceIssue'.
    Types of property 'type' are incompatible.
      Type 'string' is not assignable to type '"error" | "violation" | "gap"'.
utils/chats/flows/configurations/referenceFlowConfig.ts(800,15): error TS2416: Property 'process' in type 'DocumentDescriptionProcessor' is not assignable to the same property in base type 'StageProcessor'.
  Type '(context: ProcessingContext, dependencies: any) => Promise<any>' is not assignable to type '(context: FlowContext, dependencies: StageDependencies) => Promise<Partial<FlowContext>>'.
    Types of parameters 'context' and 'context' are incompatible.
      Type 'FlowContext' is missing the following properties from type 'ProcessingContext': referenceFiles, reviewFiles, allPrompts, referenceAnalysis, and 6 more.
utils/chats/flows/configurations/referenceFlowConfig.ts(901,24): error TS18046: 'error' is of type 'unknown'.
utils/chats/flows/configurations/referenceFlowConfig.ts(963,15): error TS2416: Property 'process' in type 'ComplianceSectionPlanningProcessor' is not assignable to the same property in base type 'SectionPlanningStageProcessor'.
  Type '(context: ProcessingContext, dependencies: any) => Promise<any>' is not assignable to type '(context: FlowContext, dependencies: StageDependencies) => Promise<Partial<FlowContext>>'.
    Types of parameters 'context' and 'context' are incompatible.
      Type 'FlowContext' is missing the following properties from type 'ProcessingContext': referenceFiles, reviewFiles, allPrompts, referenceAnalysis, and 6 more.
utils/chats/flows/configurations/referenceFlowConfig.ts(968,46): error TS2345: Argument of type 'ProcessingContext' is not assignable to parameter of type 'FlowContext'.
  Type 'ProcessingContext' is missing the following properties from type 'FlowContext': legalIssues, memos, sectionOutputs, finalContent, and 2 more.
utils/chats/flows/configurations/referenceFlowConfig.ts(999,24): error TS2415: Class 'ReferenceIterativeSectionDraftingProcessor' incorrectly extends base class 'IterativeSectionDraftingProcessor'.
  Property 'prepareDraftingOptions' is private in type 'IterativeSectionDraftingProcessor' but not in type 'ReferenceIterativeSectionDraftingProcessor'.
utils/chats/flows/configurations/referenceFlowConfig.ts(1004,37): error TS2341: Property 'prepareDraftingOptions' is private and only accessible within class 'IterativeSectionDraftingProcessor'.
utils/chats/flows/configurations/referenceFlowConfig.ts(1030,73): error TS2749: '_idx' refers to a value, but is being used as a type here. Did you mean 'typeof _idx'?
utils/chats/flows/configurations/referenceFlowConfig.ts(1034,64): error TS2749: '_idx' refers to a value, but is being used as a type here. Did you mean 'typeof _idx'?
utils/chats/flows/core/FlowOrchestrator.ts(78,5): error TS2740: Type 'Response<any, Record<string, any>>' is missing the following properties from type 'Response': headers, ok, statusText, url, and 8 more.
utils/chats/flows/core/FlowOrchestrator.ts(82,5): error TS2322: Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/abort-controller/dist/abort-controller").AbortSignal | undefined' is not assignable to type 'AbortSignal | undefined'.
  Type 'AbortSignal' is missing the following properties from type 'AbortSignal': reason, throwIfAborted
utils/chats/flows/core/FlowOrchestrator.ts(97,9): error TS2345: Argument of type 'Response' is not assignable to parameter of type 'Response<any, Record<string, any>>'.
  Type 'Response' is missing the following properties from type 'Response<any, Record<string, any>>': sendStatus, links, send, jsonp, and 84 more.
utils/chats/flows/core/FlowOrchestrator.ts(312,9): error TS2322: Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/flows/core/ProgressManager").ProgressManager' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").ProgressManager'.
  Property 'response' is private in type 'ProgressManager' but not in type 'ProgressManager'.
utils/chats/flows/core/FlowOrchestrator.ts(313,9): error TS2322: Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/flows/core/WorkspaceManager").WorkspaceManager' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").WorkspaceManager'.
  Property 'user' is private in type 'WorkspaceManager' but not in type 'WorkspaceManager'.
utils/chats/flows/core/FlowOrchestrator.ts(395,57): error TS2693: 'StageProcessor' only refers to a type, but is being used as a value here.
utils/chats/flows/core/ProgressManager.ts(97,9): error TS2783: 'uuid' is specified more than once, so this usage will be overwritten.
utils/chats/flows/core/ProgressManager.ts(108,9): error TS2783: 'uuid' is specified more than once, so this usage will be overwritten.
utils/chats/flows/core/WorkspaceManager.ts(4,15): error TS2305: Module '"../../../../types/auth"' has no exported member 'User'.
utils/chats/flows/demo/modularFlowDemo.ts(13,10): error TS2305: Module '"../../../../types/chat-flow"' has no exported member 'FlowConfiguration'.
utils/chats/flows/demo/modularFlowDemo.ts(47,47): error TS2345: Argument of type 'FlowOptions' is not assignable to parameter of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").FlowOptions'.
  Type 'FlowOptions' is missing the following properties from type 'FlowOptions': chatId, response, workspace, user, message
utils/chats/flows/demo/modularFlowDemo.ts(57,37): error TS7006: Parameter 's' implicitly has an 'any' type.
utils/chats/flows/demo/modularFlowDemo.ts(64,11): error TS2322: Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").ExecutionSummary' is not assignable to type 'ExecutionSummary'.
  The types of 'metrics.totalExecutionTime' are incompatible between these types.
    Type 'number | undefined' is not assignable to type 'number'.
      Type 'undefined' is not assignable to type 'number'.
utils/chats/flows/demo/modularFlowDemo.ts(131,9): error TS6196: 'CustomProcessor' is declared but never used.
utils/chats/flows/demo/modularFlowDemo.ts(148,5): error TS2353: Object literal may only specify known properties, and 'id' does not exist in type 'CustomFlowConfig'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(4,10): error TS2614: Module '"../../../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../../../models/systemSettings"' instead?
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(7,3): error TS2305: Module '"../../../robustLlmUtils"' has no exported member 'initializeRobustLLMConnector'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(8,3): error TS2305: Module '"../../../robustLlmUtils"' has no exported member 'getActiveLLMConfig'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(13,3): error TS6133: 'pruneEmptyListItems' is declared but its value is never read.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(20,23): error TS2305: Module '"../../../../types/chat-agent"' has no exported member 'FlowDependencies'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(150,9): error TS2416: Property 'process' in type 'AgenticCombinationStageProcessor' is not assignable to the same property in base type 'StageProcessor'.
  Type '(context: FlowContext, dependencies: FlowDependencies) => Promise<Partial<FlowContext>>' is not assignable to type '(context: FlowContext, dependencies: StageDependencies) => Promise<Partial<FlowContext>>'.
    Types of parameters 'context' and 'context' are incompatible.
      Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").FlowContext' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-agent").FlowContext'.
        Types of property 'errors' are incompatible.
          Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").FlowError[]' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-agent").FlowError[]'.
            Type 'FlowError' is missing the following properties from type 'FlowError': code, name, message
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(229,56): error TS2345: Argument of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/flows/processors/AgenticCombinationStageProcessor").ProseMirrorDocument' is not assignable to parameter of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/prosemirror").ProseMirrorDocument'.
  Property 'type' is missing in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/flows/processors/AgenticCombinationStageProcessor").ProseMirrorDocument' but required in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/prosemirror").ProseMirrorDocument'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(310,9): error TS2353: Object literal may only specify known properties, and 'agenticCombinationApplied' does not exist in type 'Partial<FlowContext>'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(468,53): error TS2345: Argument of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/flows/processors/AgenticCombinationStageProcessor").ProseMirrorDocument' is not assignable to parameter of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/prosemirror").ProseMirrorDocument'.
  Property 'type' is missing in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/flows/processors/AgenticCombinationStageProcessor").ProseMirrorDocument' but required in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/prosemirror").ProseMirrorDocument'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(522,11): error TS2345: Argument of type 'string' is not assignable to parameter of type 'LegalMemo[]'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(664,11): error TS2345: Argument of type 'string' is not assignable to parameter of type 'LegalMemo[]'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(881,53): error TS2345: Argument of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/flows/processors/AgenticCombinationStageProcessor").ProseMirrorDocument' is not assignable to parameter of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/prosemirror").ProseMirrorDocument'.
  Property 'type' is missing in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/chats/flows/processors/AgenticCombinationStageProcessor").ProseMirrorDocument' but required in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/prosemirror").ProseMirrorDocument'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(961,9): error TS2353: Object literal may only specify known properties, and 'text' does not exist in type 'ResponseChunkData'.
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(980,3): error TS2416: Property 'validateInputs' in type 'AgenticCombinationStageProcessor' is not assignable to the same property in base type 'StageProcessor'.
  Type '(context: FlowContext) => boolean' is not assignable to type '(_context: FlowContext) => boolean'.
    Types of parameters 'context' and '_context' are incompatible.
      Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").FlowContext' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-agent").FlowContext'.
        Types of property 'errors' are incompatible.
          Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").FlowError[]' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-agent").FlowError[]'.
            Type 'FlowError' is missing the following properties from type 'FlowError': code, name, message
utils/chats/flows/processors/AgenticCombinationStageProcessor.ts(997,3): error TS2416: Property 'shouldSkip' in type 'AgenticCombinationStageProcessor' is not assignable to the same property in base type 'StageProcessor'.
  Type '(context: FlowContext) => boolean' is not assignable to type '(_context: FlowContext) => boolean'.
    Types of parameters 'context' and '_context' are incompatible.
      Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").FlowContext' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-agent").FlowContext'.
        Types of property 'errors' are incompatible.
          Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").FlowError[]' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-agent").FlowError[]'.
            Type 'FlowError' is missing the following properties from type 'FlowError': code, name, message
utils/chats/flows/processors/CombinationProcessorFactory.ts(40,13): error TS2322: Type '{ id: number; createdAt: Date; lastUpdatedAt: Date; label: string; value: string | null; } | "regular"' is not assignable to type 'ProcessorType'.
  Type '{ id: number; createdAt: Date; lastUpdatedAt: Date; label: string; value: string | null; }' is not assignable to type 'ProcessorType'.
utils/chats/flows/processors/CombinationProcessorFactory.ts(98,46): error TS2345: Argument of type 'ProcessorContext' is not assignable to parameter of type 'FlowContext & FlowContext'.
  Type 'ProcessorContext' is missing the following properties from type 'FlowContext': documents, processedDocuments, docDescriptions, sectionList, and 5 more.
utils/chats/flows/processors/CombinationProcessorFactory.ts(111,52): error TS2345: Argument of type 'ProcessorContext' is not assignable to parameter of type 'FlowContext & FlowContext'.
  Type 'ProcessorContext' is missing the following properties from type 'FlowContext': documents, processedDocuments, docDescriptions, sectionList, and 5 more.
utils/chats/flows/processors/CombinationProcessorFactory.ts(118,48): error TS2345: Argument of type 'ProcessorContext' is not assignable to parameter of type 'FlowContext & FlowContext'.
  Type 'ProcessorContext' is missing the following properties from type 'FlowContext': documents, processedDocuments, docDescriptions, sectionList, and 5 more.
utils/chats/flows/processors/DocumentProcessingStageProcessor.ts(90,11): error TS6133: 'contextManager' is declared but its value is never read.
utils/chats/flows/processors/DocumentProcessingStageProcessor.ts(199,13): error TS2353: Object literal may only specify known properties, and 'fileName' does not exist in type 'ProcessedDocument'.
utils/chats/flows/processors/DocumentProcessingStageProcessor.ts(260,7): error TS2322: Type 'ProcessedDocument[]' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").ProcessedDocument[]'.
  Property 'fileName' is missing in type 'ProcessedDocument' but required in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").ProcessedDocument'.
utils/chats/flows/processors/IterativeSectionDraftingProcessor.ts(99,11): error TS6196: 'DraftingResult' is declared but never used.
utils/chats/flows/processors/IterativeSectionDraftingProcessor.ts(120,7): error TS2415: Class 'IterativeSectionDraftingProcessor' incorrectly extends base class 'StageProcessor'.
  Property 'options' is protected in type 'IterativeSectionDraftingProcessor' but public in type 'StageProcessor'.
utils/chats/flows/processors/IterativeSectionDraftingProcessor.ts(197,9): error TS2345: Argument of type 'Section[]' is not assignable to parameter of type 'DocumentSection[]'.
  Type 'Section' is missing the following properties from type 'DocumentSection': index_number, description, relevant_documents, legal_issues_to_address
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(6,23): error TS2305: Module '"../../../../types/chat-agent"' has no exported member 'FlowDependencies'.
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(84,9): error TS2416: Property 'process' in type 'LegalIssueIdentificationProcessor' is not assignable to the same property in base type 'StageProcessor'.
  Type '(context: FlowContext, dependencies: FlowDependencies) => Promise<Partial<FlowContext>>' is not assignable to type '(context: FlowContext, dependencies: StageDependencies) => Promise<Partial<FlowContext>>'.
    Types of parameters 'context' and 'context' are incompatible.
      Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").FlowContext' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-agent").FlowContext'.
        Types of property 'errors' are incompatible.
          Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").FlowError[]' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-agent").FlowError[]'.
            Type 'FlowError' is missing the following properties from type 'FlowError': code, name, message
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(206,7): error TS2561: Object literal may only specify known properties, but 'allLegalIssues' does not exist in type 'Partial<FlowContext>'. Did you mean to write 'legalIssues'?
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(258,14): error TS2345: Argument of type '"debug"' is not assignable to parameter of type '"info" | "warn" | "error"'.
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(293,14): error TS2345: Argument of type '"debug"' is not assignable to parameter of type '"info" | "warn" | "error"'.
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(326,13): error TS2322: Type 'ParseResult<any>' is not assignable to type 'ParseResponse'.
  Types of property 'error' are incompatible.
    Type 'string | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(504,3): error TS2416: Property 'validateInputs' in type 'LegalIssueIdentificationProcessor' is not assignable to the same property in base type 'StageProcessor'.
  Type '(context: FlowContext) => boolean' is not assignable to type '(_context: FlowContext) => boolean'.
    Types of parameters 'context' and '_context' are incompatible.
      Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").FlowContext' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-agent").FlowContext'.
        Types of property 'errors' are incompatible.
          Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").FlowError[]' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-agent").FlowError[]'.
            Type 'FlowError' is missing the following properties from type 'FlowError': code, name, message
utils/chats/flows/processors/LegalIssueIdentificationProcessor.ts(521,3): error TS2416: Property 'shouldSkip' in type 'LegalIssueIdentificationProcessor' is not assignable to the same property in base type 'StageProcessor'.
  Type '(_context: import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-agent").FlowContext) => boolean' is not assignable to type '(_context: import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").FlowContext) => boolean'.
    Types of parameters '_context' and '_context' are incompatible.
      Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").FlowContext' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-agent").FlowContext'.
        Types of property 'errors' are incompatible.
          Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-flow").FlowError[]' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/chat-agent").FlowError[]'.
            Type 'FlowError' is missing the following properties from type 'FlowError': code, name, message
utils/chats/flows/processors/LegalMemoProcessor.ts(79,7): error TS2415: Class 'LegalMemoProcessor' incorrectly extends base class 'StageProcessor'.
  Property 'options' is private in type 'LegalMemoProcessor' but not in type 'StageProcessor'.
utils/chats/flows/processors/LegalMemoProcessor.ts(147,31): error TS6133: 'memoIndex' is declared but its value is never read.
utils/chats/flows/processors/LegalMemoProcessor.ts(188,9): error TS2322: Type 'SectionWithMemos[]' is not assignable to type 'Section[]'.
  Type 'SectionWithMemos' is missing the following properties from type 'Section': sectionNumber, title, relevantDocumentNames
utils/chats/flows/processors/LegalMemoProcessor.ts(309,19): error TS2304: Cannot find name 'FlowDependencies'.
utils/chats/flows/processors/LegalMemoProcessor.ts(404,19): error TS2304: Cannot find name 'FlowDependencies'.
utils/chats/flows/processors/LegalMemoProcessor.ts(702,17): error TS6133: 'saveMemoArtifacts' is declared but its value is never read.
utils/chats/flows/processors/LegalMemoProcessor.ts(799,11): error TS6133: 'calculateSuccessRate' is declared but its value is never read.
utils/chats/helpers/contextWindowManager.ts(4,1): error TS6192: All imports in import declaration are unused.
utils/chats/helpers/contextWindowManager.ts(188,11): error TS6133: '_strategy' is declared but its value is never read.
utils/chats/helpers/contextWindowManager.ts(275,37): error TS2345: Argument of type 'TokenBudget' is not assignable to parameter of type 'BudgetAllocation'.
  Index signature for type 'string' is missing in type 'TokenBudget'.
utils/chats/helpers/documentProcessing.ts(160,66): error TS2345: Argument of type '{ systemPrompt: string; userPrompt: string; }' is not assignable to parameter of type 'CompressMessagesParams'.
  Property 'messages' is missing in type '{ systemPrompt: string; userPrompt: string; }' but required in type 'CompressMessagesParams'.
utils/chats/helpers/documentProcessing.ts(175,3): error TS2322: Type 'string | undefined' is not assignable to type 'string'.
  Type 'undefined' is not assignable to type 'string'.
utils/chats/helpers/documentProcessing.ts(205,66): error TS2345: Argument of type '{ systemPrompt: string; userPrompt: string; }' is not assignable to parameter of type 'CompressMessagesParams'.
  Property 'messages' is missing in type '{ systemPrompt: string; userPrompt: string; }' but required in type 'CompressMessagesParams'.
utils/chats/helpers/documentProcessing.ts(218,10): error TS18048: 'relevanceResult.textResponse' is possibly 'undefined'.
utils/chats/helpers/documentProcessing.ts(251,58): error TS2345: Argument of type '{ systemPrompt: string; userPrompt: string; }' is not assignable to parameter of type 'CompressMessagesParams'.
  Property 'messages' is missing in type '{ systemPrompt: string; userPrompt: string; }' but required in type 'CompressMessagesParams'.
utils/chats/helpers/documentProcessing.ts(261,10): error TS18048: 'result.textResponse' is possibly 'undefined'.
utils/chats/helpers/documentProcessing.ts(394,62): error TS2345: Argument of type '{ systemPrompt: string; userPrompt: string; }' is not assignable to parameter of type 'CompressMessagesParams'.
  Property 'messages' is missing in type '{ systemPrompt: string; userPrompt: string; }' but required in type 'CompressMessagesParams'.
utils/chats/helpers/documentProcessing.ts(404,20): error TS18048: 'result.textResponse' is possibly 'undefined'.
utils/chats/helpers/documentProcessing.ts(512,58): error TS2345: Argument of type '{ systemPrompt: string; userPrompt: string; }' is not assignable to parameter of type 'CompressMessagesParams'.
  Property 'messages' is missing in type '{ systemPrompt: string; userPrompt: string; }' but required in type 'CompressMessagesParams'.
utils/chats/helpers/documentProcessing.ts(523,21): error TS18048: 'result.textResponse' is possibly 'undefined'.
utils/chats/helpers/documentProcessing.ts(626,30): error TS2345: Argument of type 'TokenBudget' is not assignable to parameter of type 'BudgetAllocation'.
  Index signature for type 'string' is missing in type 'TokenBudget'.
utils/chats/helpers/documentProcessing.ts(681,68): error TS2345: Argument of type '{ systemPrompt: string; userPrompt: string; }' is not assignable to parameter of type 'CompressMessagesParams'.
  Property 'messages' is missing in type '{ systemPrompt: string; userPrompt: string; }' but required in type 'CompressMessagesParams'.
utils/chats/helpers/documentProcessing.ts(770,30): error TS2345: Argument of type 'TokenBudget' is not assignable to parameter of type 'BudgetAllocation'.
  Index signature for type 'string' is missing in type 'TokenBudget'.
utils/chats/helpers/documentProcessing.ts(823,68): error TS2345: Argument of type '{ systemPrompt: string; userPrompt: string; }' is not assignable to parameter of type 'CompressMessagesParams'.
  Property 'messages' is missing in type '{ systemPrompt: string; userPrompt: string; }' but required in type 'CompressMessagesParams'.
utils/chats/helpers/documentProcessing.ts(851,9): error TS18048: 'result.textResponse' is possibly 'undefined'.
utils/chats/helpers/documentProcessing.ts(895,7): error TS2561: Object literal may only specify known properties, but 'SYSTEM_PROMPT' does not exist in type 'SectionDraftingPrompts'. Did you mean to write 'systemPrompt'?
utils/chats/helpers/documentProcessing.ts(995,48): error TS2341: Property 'LLMConnector' is private and only accessible within class 'ContextWindowManager'.
utils/chats/helpers/documentProcessing.ts(997,52): error TS2551: Property 'SYSTEM_PROMPT' does not exist on type 'SectionDraftingPrompts'. Did you mean 'systemPrompt'?
utils/chats/helpers/documentProcessing.ts(998,50): error TS2551: Property 'USER_PROMPT' does not exist on type 'SectionDraftingPrompts'. Did you mean 'userPrompt'?
utils/chats/helpers/documentProcessing.ts(1218,66): error TS2345: Argument of type '{ systemPrompt: string; userPrompt: string; }' is not assignable to parameter of type 'CompressMessagesParams'.
  Property 'messages' is missing in type '{ systemPrompt: string; userPrompt: string; }' but required in type 'CompressMessagesParams'.
utils/chats/helpers/documentProcessing.ts(1239,14): error TS18048: 'result.textResponse' is possibly 'undefined'.
utils/chats/helpers/documentProcessing.ts(1244,5): error TS2739: Type 'ChatCompletionResult' is missing the following properties from type 'LLMResponse': content, role
utils/chats/helpers/promptManager.ts(1,10): error TS2614: Module '"../../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../../models/systemSettings"' instead?
utils/chats/index.ts(54,64): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'number | null | undefined'.
  Type 'string' is not assignable to type 'number'.
utils/chats/index.ts(100,60): error TS2345: Argument of type 'WorkspaceChat[]' is not assignable to parameter of type 'ChatRecord[]'.
  Type 'WorkspaceChat' is not assignable to type 'ChatRecord'.
    Types of property 'feedbackScore' are incompatible.
      Type 'boolean | null | undefined' is not assignable to type 'number | null | undefined'.
        Type 'boolean' is not assignable to type 'number'.
utils/chats/LLMConnector.ts(10,3): error TS6133: 'LLMResponse' is declared but its value is never read.
utils/chats/LLMConnector.ts(11,3): error TS2459: Module '"../../types/chat-agent"' declares 'ChatMessage' locally, but it is not exported.
utils/chats/LLMConnector.ts(40,11): error TS2564: Property '_options' has no initializer and is not definitely assigned in the constructor.
utils/chats/LLMConnector.ts(40,11): error TS6133: '_options' is declared but its value is never read.
utils/chats/LLMConnector.ts(43,5): error TS2740: Type 'BaseLLMProvider' is missing the following properties from type 'LLMProvider': name, type, models, authenticate, and 2 more.
utils/chats/LLMConnector.ts(44,10): error TS2551: Property 'options' does not exist on type 'LLMConnector'. Did you mean '_options'?
utils/chats/LLMConnector.ts(54,40): error TS2339: Property 'getChatCompletion' does not exist on type 'LLMProvider'.
utils/chats/LLMConnector.ts(65,32): error TS2339: Property 'getChatCompletion' does not exist on type 'LLMProvider'.
utils/chats/LLMConnector.ts(74,32): error TS2339: Property 'compressMessages' does not exist on type 'LLMProvider'.
utils/chats/LLMConnector.ts(81,26): error TS2339: Property 'defaultTemp' does not exist on type 'LLMProvider'.
utils/chats/LLMConnector.ts(88,26): error TS2551: Property 'model' does not exist on type 'LLMProvider'. Did you mean 'models'?
utils/chats/LLMConnector.ts(95,26): error TS2339: Property 'metrics' does not exist on type 'LLMProvider'.
utils/chats/LLMConnector.ts(102,19): error TS2339: Property 'metrics' does not exist on type 'LLMProvider'.
utils/chats/LLMConnector.ts(109,33): error TS2339: Property 'promptWindowLimit' does not exist on type 'LLMProvider'.
utils/chats/LLMConnector.ts(110,23): error TS2339: Property 'promptWindowLimit' does not exist on type 'LLMProvider'.
utils/chats/openaiCompatible.ts(135,24): error TS2352: Conversion of type 'BaseLLMProvider' to type 'LLMConnector' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'defaultTemp' is missing in type 'BaseLLMProvider' but required in type 'LLMConnector'.
utils/chats/openaiCompatible.ts(182,5): error TS2741: Property 'name' is missing in type 'Workspace' but required in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/DocumentManager/index").Workspace'.
utils/chats/openaiCompatible.ts(275,46): error TS2345: Argument of type 'Workspace' is not assignable to parameter of type 'Workspace'.
  Types of property 'id' are incompatible.
    Type 'number' is not assignable to type 'string'.
utils/chats/openaiCompatible.ts(316,15): error TS18047: 'chat' is possibly 'null'.
utils/chats/openaiCompatible.ts(334,24): error TS2352: Conversion of type 'BaseLLMProvider' to type 'LLMConnector' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'defaultTemp' is missing in type 'BaseLLMProvider' but required in type 'LLMConnector'.
utils/chats/openaiCompatible.ts(385,7): error TS2345: Argument of type 'OpenAICompatibleResponse' is not assignable to parameter of type 'ResponseChunkData'.
  Type 'OpenAICompatibleResponse' is missing the following properties from type 'ResponseChunkData': sources, type, textResponse, close, error
utils/chats/openaiCompatible.ts(407,5): error TS2741: Property 'name' is missing in type 'Workspace' but required in type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/DocumentManager/index").Workspace'.
utils/chats/openaiCompatible.ts(447,9): error TS2345: Argument of type 'OpenAICompatibleResponse' is not assignable to parameter of type 'ResponseChunkData'.
  Type 'OpenAICompatibleResponse' is missing the following properties from type 'ResponseChunkData': sources, type, textResponse, close, error
utils/chats/openaiCompatible.ts(490,7): error TS2345: Argument of type 'OpenAICompatibleResponse' is not assignable to parameter of type 'ResponseChunkData'.
  Type 'OpenAICompatibleResponse' is missing the following properties from type 'ResponseChunkData': sources, type, textResponse, close, error
utils/chats/openaiCompatible.ts(508,46): error TS2345: Argument of type 'Workspace' is not assignable to parameter of type 'Workspace'.
  Types of property 'id' are incompatible.
    Type 'number' is not assignable to type 'string'.
utils/chats/openaiCompatible.ts(517,7): error TS2345: Argument of type 'OpenAICompatibleResponse' is not assignable to parameter of type 'ResponseChunkData'.
  Type 'OpenAICompatibleResponse' is missing the following properties from type 'ResponseChunkData': sources, type, textResponse, close, error
utils/chats/openaiCompatible.ts(563,7): error TS2345: Argument of type 'OpenAICompatibleResponse' is not assignable to parameter of type 'ResponseChunkData'.
  Type 'OpenAICompatibleResponse' is missing the following properties from type 'ResponseChunkData': sources, type, textResponse, close, error
utils/chats/openaiCompatible.ts(568,11): error TS2322: Type 'boolean' is not assignable to type 'string'.
utils/chats/openaiCompatible.ts(569,19): error TS18047: 'chat' is possibly 'null'.
utils/chats/openaiCompatible.ts(585,5): error TS2345: Argument of type 'OpenAICompatibleResponse' is not assignable to parameter of type 'ResponseChunkData'.
  Type 'OpenAICompatibleResponse' is missing the following properties from type 'ResponseChunkData': sources, type, textResponse, close, error
utils/chats/openaiCompatible.ts(590,9): error TS2322: Type 'boolean' is not assignable to type 'string'.
utils/chats/streamCanvas.ts(8,10): error TS2614: Module '"../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../models/systemSettings"' instead?
utils/chats/streamCanvas.ts(10,1): error TS6192: All imports in import declaration are unused.
utils/chats/streamCanvas.ts(101,19): error TS7006: Parameter 'part' implicitly has an 'any' type.
utils/chats/streamCanvas.ts(218,9): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.
  Type 'FilteredUser' is not assignable to type 'User'.
    Types of property 'id' are incompatible.
      Type 'number' is not assignable to type 'string'.
utils/chats/streamCanvas.ts(219,9): error TS2322: Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/models").Workspace' is not assignable to type 'Workspace'.
  Types of property 'id' are incompatible.
    Type 'number' is not assignable to type 'string'.
utils/chats/streamCanvas.ts(220,9): error TS2322: Type 'WorkspaceThread | null' is not assignable to type 'Thread | null | undefined'.
  Type 'WorkspaceThread' is not assignable to type 'Thread'.
    Types of property 'id' are incompatible.
      Type 'number' is not assignable to type 'string'.
utils/chats/streamCanvas.ts(223,7): error TS2322: Type 'any[]' is not assignable to type 'string'.
utils/chats/streamCanvas.ts(259,32): error TS2774: This condition will always return true since this function is always defined. Did you mean to call it instead?
utils/chats/streamCanvas.ts(260,19): error TS2339: Property 'countTokens' does not exist on type 'TokenManager'.
utils/chats/streamCanvas.ts(262,27): error TS2774: This condition will always return true since this function is always defined. Did you mean to call it instead?
utils/chats/streamCanvas.ts(263,19): error TS2339: Property 'countTokens' does not exist on type 'TokenManager'.
utils/chats/streamCanvas.ts(265,32): error TS2774: This condition will always return true since this function is always defined. Did you mean to call it instead?
utils/chats/streamCanvas.ts(307,19): error TS2339: Property 'countTokens' does not exist on type 'TokenManager'.
utils/chats/streamCanvas.ts(309,37): error TS2339: Property 'countTokens' does not exist on type 'TokenManager'.
utils/chats/streamCanvas.ts(318,42): error TS2339: Property 'truncateToTokenLength' does not exist on type 'TokenManager'.
utils/chats/streamCanvas.ts(335,19): error TS2774: This condition will always return true since this function is always defined. Did you mean to call it instead?
utils/chats/streamCanvas.ts(343,39): error TS2345: Argument of type 'ChatPromptArgs' is not assignable to parameter of type 'Workspace'.
  Property 'id' is missing in type 'ChatPromptArgs' but required in type 'Workspace'.
utils/chats/streamCanvas.ts(348,63): error TS2345: Argument of type 'string' is not assignable to parameter of type 'ChatMessage[]'.
utils/chats/streamCanvas.ts(349,58): error TS2339: Property 'defaultTemp' does not exist on type 'BaseLLMProvider'.
utils/chats/streamCanvas.ts(385,36): error TS2345: Argument of type '{ uuid: string; sources: CanvasSource[]; type: string; textResponse: any; close: boolean; error: boolean; }' is not assignable to parameter of type 'ResponseChunkData'.
  Types of property 'sources' are incompatible.
    Type 'CanvasSource[]' is not assignable to type 'Source[]'.
      Property 'url' is missing in type 'CanvasSource' but required in type 'Source'.
utils/chats/streamCanvas.ts(424,7): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.
  Type 'FilteredUser' is not assignable to type 'User'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.
utils/chats/streamCDB.ts(7,1): error TS6192: All imports in import declaration are unused.
utils/chats/streamCDB.ts(175,19): error TS2345: Argument of type 'CDBOptions' is not assignable to parameter of type 'FlowOptions'.
  Types of property 'cdbOptions' are incompatible.
    Type 'string[] | undefined' is not assignable to type '[(string | undefined)?, (string | undefined)?, (string | undefined)?, (FlowType | undefined)?, (any[] | undefined)?] | undefined'.
      Type 'string[]' is not assignable to type '[(string | undefined)?, (string | undefined)?, (string | undefined)?, (FlowType | undefined)?, (any[] | undefined)?]'.
        Target allows only 5 element(s) but source may have more.
utils/chats/streamCDB.ts(225,9): error TS2353: Object literal may only specify known properties, and 'chatId' does not exist in type 'ResponseChunkData'.
utils/chats/streamCDB.ts(233,9): error TS2322: Type 'boolean | Error' is not assignable to type 'string | boolean'.
  Type 'Error' is not assignable to type 'string | boolean'.
utils/chats/streamDD.ts(10,3): error TS6133: 'chatPrompt' is declared but its value is never read.
utils/chats/streamDD.ts(15,10): error TS2614: Module '"../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../models/systemSettings"' instead?
utils/chats/streamDD.ts(26,1): error TS6192: All imports in import declaration are unused.
utils/chats/streamDD.ts(145,36): error TS2345: Argument of type '{ uuid: string; type: string; }' is not assignable to parameter of type 'ResponseChunkData'.
  Type '{ uuid: string; type: string; }' is missing the following properties from type 'ResponseChunkData': sources, textResponse, close, error
utils/chats/streamDD.ts(161,34): error TS2345: Argument of type '{ uuid: string; type: string; }' is not assignable to parameter of type 'ResponseChunkData'.
  Type '{ uuid: string; type: string; }' is missing the following properties from type 'ResponseChunkData': sources, textResponse, close, error
utils/chats/streamDD.ts(167,53): error TS2345: Argument of type 'FilteredUser | null' is not assignable to parameter of type 'User | null | undefined'.
  Type 'FilteredUser' is not assignable to type 'User'.
    Types of property 'id' are incompatible.
      Type 'number' is not assignable to type 'string'.
utils/chats/streamDD.ts(174,7): error TS2554: Expected 2 arguments, but got 6.
utils/chats/streamDD.ts(179,34): error TS2345: Argument of type 'CommandResponse' is not assignable to parameter of type 'ResponseChunkData'.
  Type 'CommandResponse' is missing the following properties from type 'ResponseChunkData': uuid, sources, type, textResponse, and 2 more.
utils/chats/streamDD.ts(188,5): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.
  Type 'FilteredUser' is not assignable to type 'User'.
    Types of property 'id' are incompatible.
      Type 'number' is not assignable to type 'string'.
utils/chats/streamDD.ts(189,5): error TS2322: Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/models").Workspace' is not assignable to type 'Workspace'.
  Types of property 'id' are incompatible.
    Type 'number' is not assignable to type 'string'.
utils/chats/streamDD.ts(190,5): error TS2322: Type 'WorkspaceThread | null' is not assignable to type 'Thread | null | undefined'.
  Type 'WorkspaceThread' is not assignable to type 'Thread'.
    Types of property 'id' are incompatible.
      Type 'number' is not assignable to type 'string'.
utils/chats/streamDD.ts(317,61): error TS2339: Property 'defaultTemp' does not exist on type 'BaseLLMProvider'.
utils/chats/streamDD.ts(362,62): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/chats/streamDD.ts(410,12): error TS18047: 'linked' is possibly 'null'.
utils/chats/streamDD.ts(410,19): error TS2339: Property 'slug' does not exist on type 'WorkspaceWithDocuments'.
utils/chats/streamDD.ts(416,89): error TS18047: 'linked' is possibly 'null'.
utils/chats/streamDD.ts(416,96): error TS2339: Property 'slug' does not exist on type 'WorkspaceWithDocuments'.
utils/chats/streamDD.ts(424,11): error TS2322: Type 'WorkspaceWithDocuments | null' is not assignable to type 'Workspace'.
  Type 'null' is not assignable to type 'Workspace'.
utils/chats/streamDD.ts(427,11): error TS2322: Type 'BaseLLMProvider' is not assignable to type 'LLMConnector'.
  The types returned by 'compressMessages(...)' are incompatible between these types.
    Type 'ChatMessage[]' is missing the following properties from type 'Promise<any[]>': then, catch, finally, [Symbol.toStringTag]
utils/chats/streamDD.ts(452,41): error TS18047: 'linked' is possibly 'null'.
utils/chats/streamDD.ts(452,48): error TS2339: Property 'slug' does not exist on type 'WorkspaceWithDocuments'.
utils/chats/streamDD.ts(457,76): error TS18047: 'linked' is possibly 'null'.
utils/chats/streamDD.ts(457,83): error TS2339: Property 'slug' does not exist on type 'WorkspaceWithDocuments'.
utils/chats/streamDD.ts(466,39): error TS18047: 'linked' is possibly 'null'.
utils/chats/streamDD.ts(466,46): error TS2339: Property 'slug' does not exist on type 'WorkspaceWithDocuments'.
utils/chats/streamDD.ts(473,64): error TS18047: 'linked' is possibly 'null'.
utils/chats/streamDD.ts(473,71): error TS2339: Property 'slug' does not exist on type 'WorkspaceWithDocuments'.
utils/chats/streamDD.ts(525,12): error TS18047: 'linked' is possibly 'null'.
utils/chats/streamDD.ts(525,19): error TS2339: Property 'slug' does not exist on type 'WorkspaceWithDocuments'.
utils/chats/streamDD.ts(529,22): error TS18047: 'linked' is possibly 'null'.
utils/chats/streamDD.ts(529,29): error TS2339: Property 'slug' does not exist on type 'WorkspaceWithDocuments'.
utils/chats/streamDD.ts(532,40): error TS2339: Property 'similarityThreshold' does not exist on type 'WorkspaceWithDocuments'.
utils/chats/streamDD.ts(533,25): error TS2339: Property 'topN' does not exist on type 'WorkspaceWithDocuments'.
utils/chats/streamDD.ts(546,139): error TS18047: 'linked' is possibly 'null'.
utils/chats/streamDD.ts(546,146): error TS2339: Property 'slug' does not exist on type 'WorkspaceWithDocuments'.
utils/chats/streamDD.ts(554,85): error TS18047: 'linked' is possibly 'null'.
utils/chats/streamDD.ts(554,92): error TS2339: Property 'slug' does not exist on type 'WorkspaceWithDocuments'.
utils/chats/streamDD.ts(560,88): error TS18047: 'linked' is possibly 'null'.
utils/chats/streamDD.ts(560,95): error TS2339: Property 'slug' does not exist on type 'WorkspaceWithDocuments'.
utils/chats/streamDD.ts(574,9): error TS6133: '_tokenMetadata' is declared but its value is never read.
utils/chats/streamDD.ts(610,7): error TS2322: Type 'null' is not assignable to type 'string | boolean'.
utils/chats/streamDD.ts(623,7): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.
  Type 'FilteredUser' is not assignable to type 'User'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.
utils/chats/streamDD.ts(631,5): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.
  Type 'FilteredUser' is not assignable to type 'User'.
    Types of property 'id' are incompatible.
      Type 'number' is not assignable to type 'string'.
utils/chats/streamDD.ts(632,5): error TS2322: Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/models").Workspace' is not assignable to type 'Workspace'.
  Types of property 'id' are incompatible.
    Type 'number' is not assignable to type 'string'.
utils/chats/streamDD.ts(633,5): error TS2322: Type 'WorkspaceThread | null' is not assignable to type 'Thread | null | undefined'.
  Type 'WorkspaceThread' is not assignable to type 'Thread'.
    Types of property 'id' are incompatible.
      Type 'number' is not assignable to type 'string'.
utils/chats/streamDD.ts(671,7): error TS2322: Type 'null' is not assignable to type 'string | boolean'.
utils/chats/streamDD.ts(685,9): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.
  Type 'FilteredUser' is not assignable to type 'User'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.
utils/chats/streamDD.ts(735,66): error TS2339: Property 'defaultTemp' does not exist on type 'BaseLLMProvider'.
utils/chats/streamDD.ts(760,59): error TS2345: Argument of type 'string[]' is not assignable to parameter of type 'string'.
utils/chats/streamDD.ts(785,34): error TS2345: Argument of type 'string[]' is not assignable to parameter of type 'string'.
utils/chats/streamDD.ts(804,46): error TS2345: Argument of type 'FilteredUser | null' is not assignable to parameter of type 'AuthenticatedUser | null'.
  Property 'email' is missing in type 'FilteredUser' but required in type 'AuthenticatedUser'.
utils/chats/streamDD.ts(811,34): error TS2345: Argument of type '{ uuid: string; type: string; error: string; close: true; }' is not assignable to parameter of type 'ResponseChunkData'.
  Type '{ uuid: string; type: string; error: string; close: true; }' is missing the following properties from type 'ResponseChunkData': sources, textResponse
utils/chats/streamDD.ts(966,9): error TS2353: Object literal may only specify known properties, and 'systemPrompt' does not exist in type 'ChatMessage[]'.
utils/chats/streamDD.ts(1032,11): error TS2353: Object literal may only specify known properties, and 'systemPrompt' does not exist in type 'ChatMessage[]'.
utils/chats/streamDD.ts(1056,13): error TS2322: Type 'void' is not assignable to type 'string'.
utils/chats/streamDD.ts(1056,70): error TS2554: Expected 1 arguments, but got 3.
utils/chats/streamDD.ts(1070,15): error TS2353: Object literal may only specify known properties, and 'metrics' does not exist in type 'ResponseChunkData'.
utils/chats/streamDD.ts(1263,17): error TS2353: Object literal may only specify known properties, and 'systemPrompt' does not exist in type 'ChatMessage[]'.
utils/chats/streamDD.ts(1324,13): error TS2353: Object literal may only specify known properties, and 'systemPrompt' does not exist in type 'ChatMessage[]'.
utils/chats/streamDD.ts(1354,13): error TS2353: Object literal may only specify known properties, and 'systemPrompt' does not exist in type 'ChatMessage[]'.
utils/chats/streamDD.ts(1388,11): error TS2322: Type 'void' is not assignable to type 'string'.
utils/chats/streamDD.ts(1390,13): error TS2554: Expected 1 arguments, but got 3.
utils/chats/streamDD.ts(1407,13): error TS2353: Object literal may only specify known properties, and 'metrics' does not exist in type 'ResponseChunkData'.
utils/chats/streamDD.ts(1453,9): error TS2554: Expected 1 arguments, but got 2.
utils/chats/streamDD.ts(1466,7): error TS2322: Type 'void' is not assignable to type 'string'.
utils/chats/streamDD.ts(1466,64): error TS2554: Expected 1 arguments, but got 3.
utils/chats/streamDD.ts(1501,11): error TS2353: Object literal may only specify known properties, and 'systemPrompt' does not exist in type 'ChatMessage[]'.
utils/chats/streamDD.ts(1525,13): error TS2322: Type 'void' is not assignable to type 'string'.
utils/chats/streamDD.ts(1525,70): error TS2554: Expected 1 arguments, but got 3.
utils/chats/streamDD.ts(1539,15): error TS2353: Object literal may only specify known properties, and 'metrics' does not exist in type 'ResponseChunkData'.
utils/chats/streamDD.ts(1732,17): error TS2353: Object literal may only specify known properties, and 'systemPrompt' does not exist in type 'ChatMessage[]'.
utils/chats/streamDD.ts(1793,13): error TS2353: Object literal may only specify known properties, and 'systemPrompt' does not exist in type 'ChatMessage[]'.
utils/chats/streamDD.ts(1823,13): error TS2353: Object literal may only specify known properties, and 'systemPrompt' does not exist in type 'ChatMessage[]'.
utils/chats/streamDD.ts(1857,11): error TS2322: Type 'void' is not assignable to type 'string'.
utils/chats/streamDD.ts(1859,13): error TS2554: Expected 1 arguments, but got 3.
utils/chats/streamDD.ts(1876,13): error TS2353: Object literal may only specify known properties, and 'metrics' does not exist in type 'ResponseChunkData'.
utils/chats/streamDD.ts(1914,7): error TS2454: Variable 'completeText' is used before being assigned.
utils/chats/streamDD.ts(1917,61): error TS2454: Variable 'completeText' is used before being assigned.
utils/chats/streamDD.ts(1958,17): error TS2454: Variable 'completeText' is used before being assigned.
utils/chats/streamDD.ts(1966,9): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.
  Type 'FilteredUser' is not assignable to type 'User'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.
utils/chats/streamDD.ts(1970,7): error TS2322: Type 'number | undefined' is not assignable to type 'string | null'.
  Type 'undefined' is not assignable to type 'string | null'.
utils/chats/streamDD.ts(1973,61): error TS2454: Variable 'completeText' is used before being assigned.
utils/chats/streamDD.ts(1977,39): error TS18047: 'chat' is possibly 'null'.
utils/chats/streamDD.ts(1978,27): error TS2454: Variable 'completeText' is used before being assigned.
utils/chats/streamDD.ts(1981,31): error TS18047: 'chat' is possibly 'null'.
utils/chats/streamDD.ts(1981,57): error TS18047: 'chat' is possibly 'null'.
utils/chats/streamDD.ts(2016,35): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.
utils/chats/streamDD.ts(2043,9): error TS2353: Object literal may only specify known properties, and 'chatId' does not exist in type 'ResponseChunkData'.
utils/chats/streamDD.ts(2052,34): error TS2345: Argument of type '{ uuid: string; type: string; close: true; error: false; }' is not assignable to parameter of type 'ResponseChunkData'.
  Type '{ uuid: string; type: string; close: true; error: false; }' is missing the following properties from type 'ResponseChunkData': sources, textResponse
utils/chats/streamLQA.ts(15,10): error TS2614: Module '"../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../models/systemSettings"' instead?
utils/chats/streamLQA.ts(21,1): error TS6192: All imports in import declaration are unused.
utils/chats/streamLQA.ts(168,53): error TS2345: Argument of type 'FilteredUser | null' is not assignable to parameter of type 'User | null | undefined'.
  Type 'FilteredUser' is not assignable to type 'User'.
    Types of property 'id' are incompatible.
      Type 'number' is not assignable to type 'string'.
utils/chats/streamLQA.ts(191,7): error TS2554: Expected 2 arguments, but got 6.
utils/chats/streamLQA.ts(196,34): error TS2345: Argument of type 'CommandResponse' is not assignable to parameter of type 'ResponseChunkData'.
  Type 'CommandResponse' is missing the following properties from type 'ResponseChunkData': uuid, sources, type, textResponse, and 2 more.
utils/chats/streamLQA.ts(205,5): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.
  Type 'FilteredUser' is not assignable to type 'User'.
    Types of property 'id' are incompatible.
      Type 'number' is not assignable to type 'string'.
utils/chats/streamLQA.ts(206,5): error TS2322: Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/models").Workspace' is not assignable to type 'Workspace'.
  Types of property 'id' are incompatible.
    Type 'number' is not assignable to type 'string'.
utils/chats/streamLQA.ts(207,5): error TS2322: Type 'WorkspaceThread | null' is not assignable to type 'Thread | null | undefined'.
  Type 'WorkspaceThread' is not assignable to type 'Thread'.
    Types of property 'id' are incompatible.
      Type 'number' is not assignable to type 'string'.
utils/chats/streamLQA.ts(446,7): error TS6133: '_sourcesSave' is declared but its value is never read.
utils/chats/streamLQA.ts(472,7): error TS2322: Type 'null' is not assignable to type 'string | boolean'.
utils/chats/streamLQA.ts(485,7): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.
  Type 'FilteredUser' is not assignable to type 'User'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.
utils/chats/streamLQA.ts(492,5): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.
  Type 'FilteredUser' is not assignable to type 'User'.
    Types of property 'id' are incompatible.
      Type 'number' is not assignable to type 'string'.
utils/chats/streamLQA.ts(493,5): error TS2322: Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/models").Workspace' is not assignable to type 'Workspace'.
  Types of property 'id' are incompatible.
    Type 'number' is not assignable to type 'string'.
utils/chats/streamLQA.ts(494,5): error TS2322: Type 'WorkspaceThread | null' is not assignable to type 'Thread | null | undefined'.
  Type 'WorkspaceThread' is not assignable to type 'Thread'.
    Types of property 'id' are incompatible.
      Type 'number' is not assignable to type 'string'.
utils/chats/streamLQA.ts(517,35): error TS2345: Argument of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/models").Workspace' is not assignable to parameter of type 'Workspace'.
  Types of property 'id' are incompatible.
    Type 'number' is not assignable to type 'string'.
utils/chats/streamLQA.ts(719,34): error TS2339: Property 'getAdjacentVectors' does not exist on type 'BaseVectorDatabaseProvider'.
utils/chats/streamLQA.ts(786,60): error TS6133: 'text' is declared but its value is never read.
utils/chats/streamLQA.ts(843,5): error TS2322: Type '(string | { text: string; meta: string; })[]' is not assignable to type 'string[]'.
  Type 'string | { text: string; meta: string; }' is not assignable to type 'string'.
    Type '{ text: string; meta: string; }' is not assignable to type 'string'.
utils/chats/streamLQA.ts(854,5): error TS2304: Cannot find name 'sourcesSave'.
utils/chats/streamLQA.ts(882,9): error TS2322: Type 'null' is not assignable to type 'string | boolean'.
utils/chats/streamLQA.ts(896,11): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.
  Type 'FilteredUser' is not assignable to type 'User'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.
utils/chats/streamLQA.ts(943,34): error TS2345: Argument of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/models").Workspace' is not assignable to parameter of type 'Workspace'.
  Types of property 'id' are incompatible.
    Type 'number' is not assignable to type 'string'.
utils/chats/streamLQA.ts(980,11): error TS2353: Object literal may only specify known properties, and 'metrics' does not exist in type 'ResponseChunkData'.
utils/chats/streamLQA.ts(1034,17): error TS6133: '_completeText' is declared but its value is never read.
utils/chats/streamLQA.ts(1122,13): error TS2322: Type 'FilteredUser | null' is not assignable to type 'User | null | undefined'.
  Type 'FilteredUser' is not assignable to type 'User'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.
utils/chats/streamLQA.ts(1125,11): error TS2322: Type 'number | undefined' is not assignable to type 'string | null'.
  Type 'undefined' is not assignable to type 'string | null'.
utils/chats/streamLQA.ts(1133,41): error TS18047: 'chat' is possibly 'null'.
utils/chats/streamLQA.ts(1135,37): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.
utils/chats/streamLQA.ts(1172,9): error TS2353: Object literal may only specify known properties, and 'fileAttachments' does not exist in type 'ResponseChunkData'.
utils/chats/streamLQA.ts(1195,7): error TS2353: Object literal may only specify known properties, and 'chatId' does not exist in type 'ResponseChunkData'.
utils/collectorApi/index.ts(3,1): error TS6192: All imports in import declaration are unused.
utils/collectorApi/index.ts(114,5): error TS2322: Type 'unknown' is not assignable to type 'false | ProcessDocumentResponse'.
utils/collectorApi/index.ts(142,5): error TS2322: Type 'unknown' is not assignable to type 'false | ProcessLinkResponse'.
utils/collectorApi/index.ts(169,5): error TS2322: Type 'unknown' is not assignable to type 'ProcessRawTextResponse'.
utils/collectorApi/index.ts(199,5): error TS2322: Type 'unknown' is not assignable to type 'ExtensionResponse'.
utils/collectorApi/index.ts(227,5): error TS2322: Type 'unknown' is not assignable to type 'false | LinkContentResponse'.
utils/contextualization/index.ts(44,7): error TS2353: Object literal may only specify known properties, and 'model' does not exist in type 'LLMProviderClassParams'.
utils/contextualization/index.ts(47,20): error TS2351: This expression is not constructable.
  Type 'BaseLLMProviderClass' has no construct signatures.
utils/contextualization/index.ts(47,20): error TS18047: 'LLMClass' is possibly 'null'.
utils/contextualization/index.ts(91,7): error TS2322: Type 'CompletionResponse | null' is not assignable to type 'string'.
  Type 'null' is not assignable to type 'string'.
utils/database/index.ts(81,13): error TS2339: Property 'SystemSettings' does not exist on type 'typeof import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/systemSettings")'.
utils/database/index.ts(93,16): error TS2339: Property 'migrateTable' does not exist on type '{ usernameRegex: RegExp; writable: readonly ["username", "password", "pfpFilename", "role", "suspended", "custom_ai_userselected", "custom_ai_option", "custom_ai_selected_engine", "custom_system_prompt", "economy_system_id", "organizationId"]; ... 16 more ...; getActiveStyleProfile: (userId: string | number) => Prom...'.
utils/database/index.ts(94,21): error TS2339: Property 'migrateTable' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
utils/database/index.ts(95,25): error TS2339: Property 'migrateTable' does not exist on type 'WorkspaceUserModelStatic'.
utils/database/index.ts(96,20): error TS2339: Property 'migrateTable' does not exist on type 'DocumentModelStatic'.
utils/database/index.ts(97,27): error TS2339: Property 'migrateTable' does not exist on type '{ bulkInsert: (vectorRecords?: VectorRecord[]) => Promise<BulkInsertResult>; deleteForWorkspace: (workspaceId: number, user: any, slugModule: any) => Promise<...>; where: (clause?: any, limit?: number | undefined) => Promise<...>; deleteIds: (ids?: number[]) => Promise<...>; }'.
utils/database/index.ts(98,26): error TS2339: Property 'migrateTable' does not exist on type '{ new: ({ workspaceId, prompt, response, user, threadId, include, apiSessionId, invoice_ref, metrics, }: WorkspaceChatData) => Promise<CreateChatResult>; forWorkspaceByUser: (workspaceId?: number | null, userId?: number | null, limit?: number | null, orderBy?: any) => Promise<...>; ... 11 more ...; bulkCreate: (chat...'.
utils/database/index.ts(99,18): error TS2339: Property 'migrateTable' does not exist on type 'InviteModelStatic'.
utils/database/index.ts(100,27): error TS2339: Property 'migrateTable' does not exist on type '{ get: (clause?: WhereClause) => Promise<{ text: string | null; heading: string | null; id: number; createdAt: Date; response: string | null; } | null>; where: (clause?: WhereClause, limit?: number | undefined) => Promise<...>; save: (message: WelcomeMessageData) => Promise<...>; getMessages: () => Promise<...>; }'.
utils/database/index.ts(101,18): error TS2339: Property 'migrateTable' does not exist on type '{ tablename: string; writable: never[]; makeSecret: () => string; create: (createdByUserId?: number | null) => Promise<ApiKeyCreateResult>; get: (clause?: WhereClause) => Promise<...>; count: (clause?: WhereClause) => Promise<...>; delete: (clause?: WhereClause) => Promise<...>; where: (clause?: WhereClause, limit?:...'.
utils/DeepSearch/deepsearchproviders/bing.ts(86,13): error TS2322: Type 'unknown' is not assignable to type 'BingApiResponse'.
utils/DeepSearch/deepsearchproviders/brave.ts(121,13): error TS2322: Type 'unknown' is not assignable to type 'BraveApiResponse'.
utils/DeepSearch/deepsearchproviders/google.ts(67,11): error TS2564: Property '_location' has no initializer and is not definitely assigned in the constructor.
utils/DeepSearch/deepsearchproviders/google.ts(67,11): error TS6133: '_location' is declared but its value is never read.
utils/DeepSearch/deepsearchproviders/google.ts(80,10): error TS2551: Property 'location' does not exist on type 'GoogleDeepSearchProvider'. Did you mean '_location'?
utils/DeepSearch/deepsearchproviders/google.ts(179,11): error TS2353: Object literal may only specify known properties, and 'generationConfig' does not exist in type 'GenerateContentParameters'.
utils/DeepSearch/deepsearchproviders/google.ts(199,9): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/DeepSearch/deepsearchproviders/google.ts(229,13): error TS2322: Type 'Candidate | undefined' is not assignable to type 'GoogleCandidate | undefined'.
  Type 'Candidate' is not assignable to type 'GoogleCandidate'.
    Types of property 'content' are incompatible.
      Type 'Content | undefined' is not assignable to type '{ parts?: { text: string; }[] | undefined; } | undefined'.
        Type 'Content' is not assignable to type '{ parts?: { text: string; }[] | undefined; }'.
          Types of property 'parts' are incompatible.
            Type 'Part[] | undefined' is not assignable to type '{ text: string; }[] | undefined'.
              Type 'Part[]' is not assignable to type '{ text: string; }[]'.
                Type 'Part' is not assignable to type '{ text: string; }'.
                  Types of property 'text' are incompatible.
                    Type 'string | undefined' is not assignable to type 'string'.
                      Type 'undefined' is not assignable to type 'string'.
utils/DeepSearch/index.ts(6,10): error TS2614: Module '"../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../models/systemSettings"' instead?
utils/DeepSearch/index.ts(152,23): error TS18048: 'results.searchResults.length' is possibly 'undefined'.
utils/DeepSearch/index.ts(195,7): error TS2345: Argument of type 'string' is not assignable to parameter of type 'Record<string, any>'.
utils/DocumentDrafting/index.ts(107,11): error TS6133: '_startTime' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(2,10): error TS2305: Module '"../robustLlmUtils"' has no exported member 'initializeRobustLLMConnector'.
utils/documentEditing/editingLogic.ts(2,40): error TS2305: Module '"../robustLlmUtils"' has no exported member 'withRetry'.
utils/documentEditing/editingLogic.ts(5,3): error TS6133: 'proseMirrorToGranularParts' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(6,3): error TS6133: 'renderPMIndexedLinesForLLM' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(7,3): error TS6133: 'renderPMGranularPartsForLLM' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(8,3): error TS6133: 'cleanInvalidMarkdownFromSuggestions' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(9,3): error TS6133: 'convertProseMirrorToMarkdown' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(10,3): error TS6133: 'pruneEmptyListItems' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(12,1): error TS6192: All imports in import declaration are unused.
utils/documentEditing/editingLogic.ts(164,10): error TS6133: 'heuristicTextCompare' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(202,10): error TS6133: 'cleanLLMGeneratedId' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(239,11): error TS6133: '_logEntry' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(356,3): error TS6133: 'chatIdForFileOps' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(357,3): error TS6133: 'basePathForFileOps' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(373,11): error TS6133: '_llmConnector' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(380,52): error TS2345: Argument of type 'ProseMirrorDoc' is not assignable to parameter of type 'ProseMirrorNode'.
  Type 'ProseMirrorDoc' is not assignable to type 'ProseMirrorElementNode'.
    Types of property 'type' are incompatible.
      Type 'string' is not assignable to type '"doc" | "heading" | "paragraph" | "listItem" | "image" | "bulletList" | "orderedList" | "blockquote" | "codeBlock" | "hardBreak" | "horizontalRule" | "table" | "tableRow" | "tableCell" | "tableHeader"'.
utils/documentEditing/editingLogic.ts(388,7): error TS2345: Argument of type 'LineInfo[]' is not assignable to parameter of type 'IndexedLine[]'.
  Type 'LineInfo' is missing the following properties from type 'IndexedLine': lineNumber, originalLineText
utils/documentEditing/editingLogic.ts(404,7): error TS2345: Argument of type 'LineInfo[]' is not assignable to parameter of type 'IndexedLine[]'.
  Type 'LineInfo' is missing the following properties from type 'IndexedLine': lineNumber, originalLineText
utils/documentEditing/editingLogic.ts(470,3): error TS6133: 'sendProgress' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(484,3): error TS6133: 'sendProgress' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(494,3): error TS6133: 'sendProgress' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(518,3): error TS6133: 'sendProgress' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(531,3): error TS6133: 'sendProgress' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(540,3): error TS6133: 'sendProgress' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(562,3): error TS6133: 'sendProgress' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(563,3): error TS6133: 'chatIdForFileOps' is declared but its value is never read.
utils/documentEditing/editingLogic.ts(564,3): error TS6133: 'basePathForFileOps' is declared but its value is never read.
utils/documentEditing/lineLevel/application.ts(15,3): error TS6133: 'proseMirrorToIndexedLines' is declared but its value is never read.
utils/documentEditing/lineLevel/application.ts(16,3): error TS6133: 'cleanInvalidMarkdownFromSuggestions' is declared but its value is never read.
utils/documentEditing/lineLevel/application.ts(17,3): error TS6133: 'convertProseMirrorToMarkdown' is declared but its value is never read.
utils/documentEditing/lineLevel/application.ts(27,1): error TS6133: 'fsSync' is declared but its value is never read.
utils/documentEditing/lineLevel/application.ts(187,5): error TS2322: Type 'boolean' is not assignable to type 'ProseMirrorDocument'.
utils/documentEditing/lineLevel/validation.ts(34,1): error TS6133: 'fsSync' is declared but its value is never read.
utils/documentEditing/lineLevel/validation.ts(74,55): error TS2345: Argument of type 'LLMConfig' is not assignable to parameter of type 'Partial<LLMConfig>'.
  Types of property 'provider' are incompatible.
    Type 'string' is not assignable to type 'LLMProvider | undefined'.
utils/documentEditing/lineLevel/validation.ts(80,52): error TS2345: Argument of type 'ProseMirrorDocument' is not assignable to parameter of type 'ProseMirrorNode'.
  Type 'ProseMirrorDocument' is not assignable to type 'ProseMirrorElementNode'.
    Types of property 'content' are incompatible.
      Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/document-processing").ProseMirrorNode[]' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/prosemirror").ProseMirrorNode[]'.
        Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/document-processing").ProseMirrorNode' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/prosemirror").ProseMirrorNode'.
          Type 'ProseMirrorNode' is not assignable to type 'ProseMirrorElementNode'.
            Types of property 'type' are incompatible.
              Type 'string' is not assignable to type '"doc" | "heading" | "paragraph" | "listItem" | "image" | "bulletList" | "orderedList" | "blockquote" | "codeBlock" | "hardBreak" | "horizontalRule" | "table" | "tableRow" | "tableCell" | "tableHeader"'.
utils/documentEditing/lineLevel/validation.ts(100,61): error TS2339: Property 'replace' does not exist on type '(language: string) => string'.
utils/documentEditing/lineLevel/validation.ts(105,57): error TS2339: Property 'replace' does not exist on type '(params: { prompt: string; documentContent: string; suggestions: string; }) => string'.
utils/documentEditing/lineLevel/validation.ts(118,45): error TS2339: Property 'streamChatCompletion' does not exist on type 'RobustLLMConnector'.
utils/documentEditing/utils/documentCleanup.ts(9,3): error TS6133: 'ProseMirrorElementNode' is declared but its value is never read.
utils/documentEditing/utils/loggerUtils.ts(147,35): error TS2339: Property 'originalContent' does not exist on type 'DocumentEditingSuggestion'.
utils/documentEditing/utils/loggerUtils.ts(148,47): error TS2339: Property 'originalContent' does not exist on type 'DocumentEditingSuggestion'.
utils/documentEditing/utils/loggerUtils.ts(151,33): error TS2339: Property 'globalPartId' does not exist on type 'DocumentEditingSuggestion'.
utils/documentEditing/utils/loggerUtils.ts(155,66): error TS2339: Property 'globalPartId' does not exist on type 'DocumentEditingSuggestion'.
utils/documentEditing/utils/loggerUtils.ts(186,28): error TS2367: This comparison appears to be unintentional because the types '"other" | "style" | "correction" | "improvement" | "legal" | "IMPROVE_CONTENT" | "Type not available"' and '"DELETE"' have no overlap.
utils/documentEditing/workflows/lineLevelWorkflow.ts(64,7): error TS2345: Argument of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/document-processing").ProseMirrorDocument' is not assignable to parameter of type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/prosemirror").ProseMirrorDocument'.
  Types of property 'content' are incompatible.
    Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/document-processing").ProseMirrorNode[]' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/prosemirror").ProseMirrorNode[]'.
      Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/document-processing").ProseMirrorNode' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/prosemirror").ProseMirrorNode'.
        Type 'ProseMirrorNode' is not assignable to type 'ProseMirrorElementNode'.
          Types of property 'type' are incompatible.
            Type 'string' is not assignable to type '"doc" | "heading" | "paragraph" | "listItem" | "image" | "bulletList" | "orderedList" | "blockquote" | "codeBlock" | "hardBreak" | "horizontalRule" | "table" | "tableRow" | "tableCell" | "tableHeader"'.
utils/documentEditing/workflows/lineLevelWorkflow.ts(76,37): error TS2339: Property 'length' does not exist on type 'LineLevelSuggestionResult'.
utils/documentEditing/workflows/lineLevelWorkflow.ts(97,31): error TS2339: Property 'length' does not exist on type 'LineLevelSuggestionResult'.
utils/documentEditing/workflows/lineLevelWorkflow.ts(108,7): error TS2345: Argument of type 'LineLevelSuggestionResult' is not assignable to parameter of type 'LineLevelSuggestion[]'.
  Type 'LineLevelSuggestionResult' is missing the following properties from type 'LineLevelSuggestion[]': length, pop, push, concat, and 29 more.
utils/documentEditing/workflows/lineLevelWorkflow.ts(145,52): error TS2345: Argument of type 'ProseMirrorDocument' is not assignable to parameter of type 'ProseMirrorNode'.
  Type 'ProseMirrorDocument' is not assignable to type 'ProseMirrorElementNode'.
    Types of property 'content' are incompatible.
      Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/document-processing").ProseMirrorNode[]' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/prosemirror").ProseMirrorNode[]'.
        Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/document-processing").ProseMirrorNode' is not assignable to type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/types/prosemirror").ProseMirrorNode'.
          Type 'ProseMirrorNode' is not assignable to type 'ProseMirrorElementNode'.
            Types of property 'type' are incompatible.
              Type 'string' is not assignable to type '"doc" | "heading" | "paragraph" | "listItem" | "image" | "bulletList" | "orderedList" | "blockquote" | "codeBlock" | "hardBreak" | "horizontalRule" | "table" | "tableRow" | "tableCell" | "tableHeader"'.
utils/documentEditing/workflows/lineLevelWorkflow.ts(149,7): error TS2345: Argument of type 'LineInfo[]' is not assignable to parameter of type 'IndexedLine[]'.
  Type 'LineInfo' is missing the following properties from type 'IndexedLine': id, isHeading, isEmpty, nodeType, and 3 more.
utils/documentEditing/workflows/lineLevelWorkflow.ts(156,11): error TS6133: '_applicationTime' is declared but its value is never read.
utils/documentEditing/workflows/lineLevelWorkflow.ts(167,5): error TS2322: Type 'boolean' is not assignable to type 'ProseMirrorDocument'.
utils/documentEditing/workflows/lineLevelWorkflow.ts(173,37): error TS2339: Property 'length' does not exist on type 'LineLevelSuggestionResult'.
utils/documentEditing/workflows/lineLevelWorkflow.ts(188,11): error TS6133: '_originalWordCount' is declared but its value is never read.
utils/documentEditing/workflows/lineLevelWorkflow.ts(194,7): error TS2740: Type 'LineLevelSuggestionResult' is missing the following properties from type 'EditingSuggestion[]': length, pop, push, concat, and 29 more.
utils/documentEditing/workflows/lineLevelWorkflow.ts(198,39): error TS2339: Property 'length' does not exist on type 'LineLevelSuggestionResult'.
utils/docx/compareAndHighlight.ts(11,27): error TS7016: Could not find a declaration file for module 'diff'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/diff/lib/index.js' implicitly has an 'any' type.
  Try `npm i --save-dev @types/diff` if it exists or add a new declaration (.d.ts) file containing `declare module 'diff';`
utils/docx/compareAndHighlight.ts(13,8): error TS1192: Module '"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/i18n"' has no default export.
utils/docx/editWithLLM.ts(2,8): error TS1192: Module '"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/i18n"' has no default export.
utils/docx/editWithLLM.ts(80,11): error TS2322: Type 'unknown' is not assignable to type 'LLMProcessorResponse'.
utils/docx/editWithLLM.ts(94,35): error TS18046: 'error' is of type 'unknown'.
utils/docx/templateRenderer.ts(6,8): error TS1192: Module '"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/i18n"' has no default export.
utils/docx/textToDocx.ts(4,8): error TS1192: Module '"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/utils/i18n"' has no default export.
utils/EmbeddingEngines/cohere/index.ts(57,19): error TS2345: Argument of type '(res: CohereEmbedResponse) => void' is not assignable to parameter of type '(value: EmbedResponse) => void | PromiseLike<void>'.
  Types of parameters 'res' and 'value' are incompatible.
    Type 'EmbedResponse' is not assignable to type 'CohereEmbedResponse'.
      Type 'EmbeddingsByType' is not assignable to type 'CohereEmbedResponse'.
        Types of property 'embeddings' are incompatible.
          Type 'EmbedByTypeResponseEmbeddings' is missing the following properties from type 'number[][]': length, pop, push, concat, and 29 more.
utils/EmbeddingEngines/genericOpenAi/index.ts(34,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/EmbeddingEngines/jina/index.ts(34,11): error TS2564: Property '_task' has no initializer and is not definitely assigned in the constructor.
utils/EmbeddingEngines/jina/index.ts(34,11): error TS6133: '_task' is declared but its value is never read.
utils/EmbeddingEngines/jina/index.ts(35,11): error TS2564: Property '_lateChunking' has no initializer and is not definitely assigned in the constructor.
utils/EmbeddingEngines/jina/index.ts(35,11): error TS6133: '_lateChunking' is declared but its value is never read.
utils/EmbeddingEngines/jina/index.ts(36,11): error TS2564: Property '_embeddingType' has no initializer and is not definitely assigned in the constructor.
utils/EmbeddingEngines/jina/index.ts(36,11): error TS6133: '_embeddingType' is declared but its value is never read.
utils/EmbeddingEngines/jina/index.ts(44,5): error TS2554: Expected 1 arguments, but got 0.
utils/EmbeddingEngines/jina/index.ts(63,10): error TS2551: Property 'task' does not exist on type 'JinaEmbedder'. Did you mean '_task'?
utils/EmbeddingEngines/jina/index.ts(68,10): error TS2551: Property 'lateChunking' does not exist on type 'JinaEmbedder'. Did you mean '_lateChunking'?
utils/EmbeddingEngines/jina/index.ts(69,10): error TS2551: Property 'embeddingType' does not exist on type 'JinaEmbedder'. Did you mean '_embeddingType'?
utils/EmbeddingEngines/jina/index.ts(193,19): error TS2322: Type 'unknown' is not assignable to type 'JinaApiResponse'.
utils/EmbeddingEngines/liteLLM/index.ts(28,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/EmbeddingEngines/lmstudio/index.ts(83,17): error TS2345: Argument of type '(json: LMStudioResponse) => { data: number[]; error: null; }' is not assignable to parameter of type '(value: unknown) => { data: number[]; error: null; } | PromiseLike<{ data: number[]; error: null; }>'.
  Types of parameters 'json' and 'value' are incompatible.
    Type 'unknown' is not assignable to type 'LMStudioResponse'.
utils/EmbeddingEngines/lmstudio/index.ts(115,11): error TS2769: No overload matches this call.
  Overload 1 of 2, '(message?: string | undefined, options?: ErrorOptions | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
  Overload 2 of 2, '(message?: string | undefined): Error', gave the following error.
    Argument of type 'Promise<string>' is not assignable to parameter of type 'string'.
utils/EmbeddingEngines/localAi/index.ts(28,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/EmbeddingEngines/native/index.ts(86,41): error TS2345: Argument of type 'any[]' is not assignable to parameter of type '[task: PipelineType, model?: string | undefined, (PretrainedOptions | undefined)?]'.
  Target requires 1 element(s) but source may have fewer.
utils/EmbeddingEngines/ollama/index.ts(79,15): error TS2322: Type 'unknown' is not assignable to type 'OllamaEmbeddingResponse'.
utils/EmbeddingEngines/openAi/index.ts(11,11): error TS6196: 'EmbeddingResponse' is declared but never used.
utils/EmbeddingEngines/voyageAi/index.ts(49,7): error TS2554: Expected 1 arguments, but got 2.
utils/EmbeddingEngines/voyageAi/index.ts(54,5): error TS2322: Type 'number[] | number[][]' is not assignable to type 'number[]'.
  Type 'number[][]' is not assignable to type 'number[]'.
    Type 'number[]' is not assignable to type 'number'.
utils/EmbeddingEngines/voyageAi/index.ts(59,71): error TS2554: Expected 1 arguments, but got 2.
utils/EncryptionManager/index.ts(13,11): error TS2564: Property 'encryptionKey' has no initializer and is not definitely assigned in the constructor.
utils/EncryptionManager/index.ts(14,11): error TS2564: Property 'encryptionSalt' has no initializer and is not definitely assigned in the constructor.
utils/EncryptionManager/index.ts(26,39): error TS2565: Property 'encryptionKey' is used before being assigned.
utils/EncryptionManager/index.ts(26,59): error TS2565: Property 'encryptionSalt' is used before being assigned.
utils/files/docxSessionCleanup.ts(53,11): error TS18046: 'statErr' is of type 'unknown'.
utils/files/docxSessionCleanup.ts(61,7): error TS18046: 'readDirErr' is of type 'unknown'.
utils/files/docxSessionCleanup.ts(63,13): error TS18046: 'readDirErr' is of type 'unknown'.
utils/files/index.ts(99,30): error TS2345: Argument of type 'FilteredUser' is not assignable to parameter of type 'AuthenticatedUser'.
  Property 'email' is missing in type 'FilteredUser' but required in type 'AuthenticatedUser'.
utils/files/index.ts(121,25): error TS2339: Property 'user_id' does not exist on type 'WorkspaceWithDocuments'.
utils/files/index.ts(125,17): error TS2339: Property 'slug' does not exist on type 'WorkspaceWithDocuments'.
utils/files/index.ts(137,17): error TS2339: Property 'slug' does not exist on type 'WorkspaceWithDocuments'.
utils/files/logo.ts(3,10): error TS2305: Module '"mime"' has no exported member 'getType'.
utils/files/logo.ts(5,10): error TS2614: Module '"../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../models/systemSettings"' instead?
utils/files/logo.ts(101,32): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
utils/files/logo.ts(108,19): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
utils/files/multer.ts(1,20): error TS7016: Could not find a declaration file for module 'multer'. '/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/multer/index.js' implicitly has an 'any' type.
  Try `npm i --save-dev @types/multer` if it exists or add a new declaration (.d.ts) file containing `declare module 'multer';`
utils/files/multer.ts(12,45): error TS2694: Namespace 'global.Express' has no exported member 'Multer'.
utils/files/multer.ts(43,17): error TS2694: Namespace 'global.Express' has no exported member 'Multer'.
utils/files/multer.ts(69,19): error TS2694: Namespace 'global.Express' has no exported member 'Multer'.
utils/files/multer.ts(91,17): error TS2694: Namespace 'global.Express' has no exported member 'Multer'.
utils/files/multer.ts(117,17): error TS2694: Namespace 'global.Express' has no exported member 'Multer'.
utils/files/pfp.ts(3,10): error TS2305: Module '"mime"' has no exported member 'getType'.
utils/files/pfp.ts(57,34): error TS2339: Property 'pfpFilename' does not exist on type 'WorkspaceWithDocuments'.
utils/files/purgeDocument.ts(155,9): error TS6133: '_workspaces' is declared but its value is never read.
utils/helpers/admin/index.ts(41,30): error TS2345: Argument of type 'UserRole' is not assignable to parameter of type 'UserRole.MANAGER | UserRole.DEFAULT'.
utils/helpers/admin/index.ts(49,30): error TS2345: Argument of type 'UserRole' is not assignable to parameter of type 'UserRole.SUPERUSER | UserRole.DEFAULT'.
utils/helpers/admin/index.ts(106,30): error TS2345: Argument of type 'UserRole' is not assignable to parameter of type 'UserRole.MANAGER | UserRole.DEFAULT'.
utils/helpers/admin/index.ts(114,30): error TS2345: Argument of type 'UserRole' is not assignable to parameter of type 'UserRole.SUPERUSER | UserRole.DEFAULT'.
utils/helpers/autoCodingPromptGenerator.ts(60,11): error TS2564: Property 'paths' has no initializer and is not definitely assigned in the constructor.
utils/helpers/autoCodingPromptGenerator.ts(633,11): error TS6133: '_preferredLanguage' is declared but its value is never read.
utils/helpers/autoCodingPromptGenerator.ts(934,9): error TS2322: Type 'undefined' is not assignable to type 'string'.
utils/helpers/camelcase.ts(100,5): error TS2322: Type 'undefined' is not assignable to type 'string | false'.
utils/helpers/chat/convertTo.ts(181,5): error TS2322: Type 'WorkspaceChatWithData[]' is not assignable to type 'ChatRecord[]'.
  Type 'WorkspaceChatWithData' is not assignable to type 'ChatRecord'.
    Types of property 'feedbackScore' are incompatible.
      Type 'boolean | null | undefined' is not assignable to type 'number | null | undefined'.
        Type 'boolean' is not assignable to type 'number'.
utils/helpers/chat/convertTo.ts(185,5): error TS2322: Type 'EmbedChatWithWorkspace[]' is not assignable to type 'ChatRecord[]'.
  Type 'EmbedChatWithWorkspace' is not assignable to type 'ChatRecord'.
    Types of property 'embed_config' are incompatible.
      Property 'workspace' is missing in type '{ workspaces: { name: string; }; }' but required in type '{ [key: string]: any; workspace: { [key: string]: any; name: string; }; }'.
utils/helpers/chat/convertTo.ts(419,5): error TS2322: Type '{ sources: never[]; } | null' is not assignable to type 'ResponseData'.
  Type 'null' is not assignable to type 'ResponseData'.
utils/helpers/chat/convertTo.ts(475,3): error TS2484: Export declaration conflicts with exported declaration of 'ChatRecord'.
utils/helpers/chat/convertTo.ts(476,3): error TS2484: Export declaration conflicts with exported declaration of 'ChatFilters'.
utils/helpers/chat/convertTo.ts(477,3): error TS2484: Export declaration conflicts with exported declaration of 'ExportFormat'.
utils/helpers/chat/convertTo.ts(478,3): error TS2484: Export declaration conflicts with exported declaration of 'ExportResult'.
utils/helpers/chat/convertTo.ts(479,3): error TS2484: Export declaration conflicts with exported declaration of 'PreparedChatData'.
utils/helpers/chat/convertTo.ts(480,3): error TS2484: Export declaration conflicts with exported declaration of 'AlpacaChatData'.
utils/helpers/chat/convertTo.ts(481,3): error TS2484: Export declaration conflicts with exported declaration of 'WorkspaceChatsMap'.
utils/helpers/chat/convertTo.ts(482,3): error TS2484: Export declaration conflicts with exported declaration of 'Source'.
utils/helpers/chat/convertTo.ts(483,3): error TS2484: Export declaration conflicts with exported declaration of 'ResponseData'.
utils/helpers/chat/convertTo.ts(484,3): error TS2484: Export declaration conflicts with exported declaration of 'ChatType'.
utils/helpers/chat/convertTo.ts(485,3): error TS2484: Export declaration conflicts with exported declaration of 'ExportFormatType'.
utils/helpers/chat/index.ts(7,10): error TS2614: Module '"../../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../../models/systemSettings"' instead?
utils/helpers/chat/index.ts(9,3): error TS6133: 'FULLY_LOADED_TRANSLATIONS' is declared but its value is never read.
utils/helpers/chat/index.ts(133,23): error TS2323: Cannot redeclare exported variable 'fetchDeepSearchResults'.
utils/helpers/chat/index.ts(137,3): error TS6133: 'llmContextWindow' is declared but its value is never read.
utils/helpers/chat/index.ts(217,23): error TS2323: Cannot redeclare exported variable 'fetchAdditionalSources'.
utils/helpers/chat/index.ts(241,22): error TS2339: Property 'similaritySearch' does not exist on type 'BaseVectorDatabaseProvider'.
utils/helpers/chat/index.ts(293,23): error TS2323: Cannot redeclare exported variable 'fetchAttachedFilesContext'.
utils/helpers/chat/index.ts(409,23): error TS2323: Cannot redeclare exported variable 'messageArrayCompressor'.
utils/helpers/chat/index.ts(544,63): error TS2739: Type 'ChatHistoryEntry' is missing the following properties from type 'ChatRecord': id, prompt, createdAt
utils/helpers/chat/index.ts(703,23): error TS2323: Cannot redeclare exported variable 'messageStringCompressor'.
utils/helpers/chat/index.ts(711,5): error TS6133: 'contextTexts' is declared but its value is never read.
utils/helpers/chat/index.ts(712,5): error TS6133: 'chatHistory' is declared but its value is never read.
utils/helpers/chat/index.ts(783,63): error TS2739: Type 'ChatHistoryEntry' is missing the following properties from type 'ChatRecord': id, prompt, createdAt
utils/helpers/chat/index.ts(924,17): error TS2323: Cannot redeclare exported variable 'cannonball'.
utils/helpers/chat/index.ts(927,3): error TS2322: Type 'null' is not assignable to type 'TokenManager'.
utils/helpers/chat/index.ts(928,3): error TS2322: Type 'null' is not assignable to type 'string'.
utils/helpers/chat/index.ts(1276,17): error TS2323: Cannot redeclare exported variable 'extractTitle'.
utils/helpers/chat/index.ts(1301,17): error TS2323: Cannot redeclare exported variable 'fillSourceWindow'.
utils/helpers/chat/index.ts(1360,34): error TS2339: Property 'id' does not exist on type 'never'.
utils/helpers/chat/index.ts(1375,3): error TS2323: Cannot redeclare exported variable 'messageArrayCompressor'.
utils/helpers/chat/index.ts(1375,3): error TS2484: Export declaration conflicts with exported declaration of 'messageArrayCompressor'.
utils/helpers/chat/index.ts(1376,3): error TS2323: Cannot redeclare exported variable 'messageStringCompressor'.
utils/helpers/chat/index.ts(1376,3): error TS2484: Export declaration conflicts with exported declaration of 'messageStringCompressor'.
utils/helpers/chat/index.ts(1377,3): error TS2323: Cannot redeclare exported variable 'fillSourceWindow'.
utils/helpers/chat/index.ts(1377,3): error TS2484: Export declaration conflicts with exported declaration of 'fillSourceWindow'.
utils/helpers/chat/index.ts(1378,3): error TS2323: Cannot redeclare exported variable 'fetchAdditionalSources'.
utils/helpers/chat/index.ts(1378,3): error TS2484: Export declaration conflicts with exported declaration of 'fetchAdditionalSources'.
utils/helpers/chat/index.ts(1379,3): error TS2323: Cannot redeclare exported variable 'fetchAttachedFilesContext'.
utils/helpers/chat/index.ts(1379,3): error TS2484: Export declaration conflicts with exported declaration of 'fetchAttachedFilesContext'.
utils/helpers/chat/index.ts(1380,3): error TS2323: Cannot redeclare exported variable 'fetchDeepSearchResults'.
utils/helpers/chat/index.ts(1380,3): error TS2484: Export declaration conflicts with exported declaration of 'fetchDeepSearchResults'.
utils/helpers/chat/index.ts(1381,3): error TS2323: Cannot redeclare exported variable 'cannonball'.
utils/helpers/chat/index.ts(1381,3): error TS2484: Export declaration conflicts with exported declaration of 'cannonball'.
utils/helpers/chat/index.ts(1383,3): error TS2323: Cannot redeclare exported variable 'extractTitle'.
utils/helpers/chat/index.ts(1383,3): error TS2484: Export declaration conflicts with exported declaration of 'extractTitle'.
utils/helpers/chat/LLMPerformanceMonitor.ts(128,3): error TS2484: Export declaration conflicts with exported declaration of 'StreamMetrics'.
utils/helpers/chat/LLMPerformanceMonitor.ts(129,3): error TS2484: Export declaration conflicts with exported declaration of 'UsageReport'.
utils/helpers/chat/LLMPerformanceMonitor.ts(130,3): error TS2484: Export declaration conflicts with exported declaration of 'Message'.
utils/helpers/chat/LLMPerformanceMonitor.ts(131,3): error TS2484: Export declaration conflicts with exported declaration of 'OpenAICompatibleStream'.
utils/helpers/chat/LLMPerformanceMonitor.ts(132,3): error TS2484: Export declaration conflicts with exported declaration of 'MonitoredStream'.
utils/helpers/chat/LLMPerformanceMonitor.ts(133,3): error TS2484: Export declaration conflicts with exported declaration of 'AsyncFunctionResult'.
utils/helpers/chat/reasoningResponses.ts(87,7): error TS6133: '_placeholderEmitted' is declared but its value is never read.
utils/helpers/chat/reasoningResponses.ts(88,7): error TS6133: '_contentStarted' is declared but its value is never read.
utils/helpers/chat/reasoningResponses.ts(207,11): error TS2304: Cannot find name 'contentStarted'.
utils/helpers/chat/responses.ts(86,17): error TS2323: Cannot redeclare exported variable 'clientAbortedHandler'.
utils/helpers/chat/responses.ts(103,17): error TS2323: Cannot redeclare exported variable 'handleDefaultStreamResponseV2'.
utils/helpers/chat/responses.ts(135,37): error TS2504: Type 'MonitoredStream' must have a '[Symbol.asyncIterator]()' method that returns an async iterator.
utils/helpers/chat/responses.ts(229,23): error TS2323: Cannot redeclare exported variable 'convertToChatHistory'.
utils/helpers/chat/responses.ts(306,17): error TS2323: Cannot redeclare exported variable 'convertToPromptHistory'.
utils/helpers/chat/responses.ts(352,17): error TS2323: Cannot redeclare exported variable 'writeResponseChunk'.
utils/helpers/chat/responses.ts(361,3): error TS2323: Cannot redeclare exported variable 'handleDefaultStreamResponseV2'.
utils/helpers/chat/responses.ts(361,3): error TS2484: Export declaration conflicts with exported declaration of 'handleDefaultStreamResponseV2'.
utils/helpers/chat/responses.ts(362,3): error TS2323: Cannot redeclare exported variable 'convertToChatHistory'.
utils/helpers/chat/responses.ts(362,3): error TS2484: Export declaration conflicts with exported declaration of 'convertToChatHistory'.
utils/helpers/chat/responses.ts(363,3): error TS2323: Cannot redeclare exported variable 'convertToPromptHistory'.
utils/helpers/chat/responses.ts(363,3): error TS2484: Export declaration conflicts with exported declaration of 'convertToPromptHistory'.
utils/helpers/chat/responses.ts(364,3): error TS2323: Cannot redeclare exported variable 'writeResponseChunk'.
utils/helpers/chat/responses.ts(364,3): error TS2484: Export declaration conflicts with exported declaration of 'writeResponseChunk'.
utils/helpers/chat/responses.ts(365,3): error TS2323: Cannot redeclare exported variable 'clientAbortedHandler'.
utils/helpers/chat/responses.ts(365,3): error TS2484: Export declaration conflicts with exported declaration of 'clientAbortedHandler'.
utils/helpers/chat/tokenCount.ts(59,13): error TS2322: Type 'WorkspaceWithDocuments | null' is not assignable to type 'WorkspaceRecord | null'.
  Type 'WorkspaceWithDocuments' is missing the following properties from type 'WorkspaceRecord': id, slug
utils/helpers/chat/tokenCount.ts(118,15): error TS2484: Export declaration conflicts with exported declaration of 'TokenCountResult'.
utils/helpers/chat/tokenCount.ts(118,33): error TS2484: Export declaration conflicts with exported declaration of 'DocumentData'.
utils/helpers/chat/tokenCount.ts(118,47): error TS2484: Export declaration conflicts with exported declaration of 'LLMProvider'.
utils/helpers/chat/tokenCount.ts(118,60): error TS2484: Export declaration conflicts with exported declaration of 'WorkspaceRecord'.
utils/helpers/customModels.ts(1,10): error TS2305: Module '"../AiProviders/openRouter"' has no exported member 'fetchOpenRouterModels'.
utils/helpers/customModels.ts(220,12): error TS2322: Type 'never[] | Model[]' is not assignable to type 'CustomModel[]'.
  Type 'Model[]' is not assignable to type 'CustomModel[]'.
    Property 'name' is missing in type 'Model' but required in type 'CustomModel'.
utils/helpers/customModels.ts(252,12): error TS2322: Type 'Model[]' is not assignable to type 'CustomModel[]'.
  Property 'name' is missing in type 'Model' but required in type 'CustomModel'.
utils/helpers/customModels.ts(276,12): error TS2322: Type 'never[] | Model[]' is not assignable to type 'CustomModel[]'.
  Type 'Model[]' is not assignable to type 'CustomModel[]'.
    Property 'name' is missing in type 'Model' but required in type 'CustomModel'.
utils/helpers/customModels.ts(296,14): error TS2322: Type 'never[] | Model[]' is not assignable to type 'CustomModel[]'.
  Type 'Model[]' is not assignable to type 'CustomModel[]'.
    Property 'name' is missing in type 'Model' but required in type 'CustomModel'.
utils/helpers/customModels.ts(320,14): error TS2322: Type 'never[] | Model[]' is not assignable to type 'CustomModel[]'.
  Type 'Model[]' is not assignable to type 'CustomModel[]'.
    Property 'name' is missing in type 'Model' but required in type 'CustomModel'.
utils/helpers/customModels.ts(375,39): error TS2339: Property 'models' does not exist on type '{}'.
utils/helpers/customModels.ts(385,46): error TS2339: Property 'models' does not exist on type '{}'.
utils/helpers/customModels.ts(387,25): error TS2339: Property 'models' does not exist on type '{}'.
utils/helpers/customModels.ts(429,3): error TS6133: 'apiKey' is declared but its value is never read.
utils/helpers/customModels.ts(469,11): error TS18046: 'model' is of type 'unknown'.
utils/helpers/customModels.ts(470,21): error TS18046: 'model' is of type 'unknown'.
utils/helpers/customModels.ts(471,13): error TS18046: 'model' is of type 'unknown'.
utils/helpers/customModels.ts(499,12): error TS2322: Type 'never[] | Model[]' is not assignable to type 'CustomModel[]'.
  Type 'Model[]' is not assignable to type 'CustomModel[]'.
    Property 'name' is missing in type 'Model' but required in type 'CustomModel'.
utils/helpers/customModels.ts(613,12): error TS2322: Type 'Model[] | { created: number; id: string; object: "model"; owned_by: string; }[]' is not assignable to type 'CustomModel[]'.
  Type 'Model[]' is not assignable to type 'CustomModel[]'.
    Property 'name' is missing in type 'Model' but required in type 'CustomModel'.
utils/helpers/customModels.ts(624,46): error TS2345: Argument of type 'string | null | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
utils/helpers/starredDocuments.ts(108,32): error TS2339: Property 'docId' does not exist on type 'never'.
utils/helpers/starredDocuments.ts(108,63): error TS2345: Argument of type 'SourceDocument' is not assignable to parameter of type 'never'.
utils/helpers/starredDocuments.ts(115,17): error TS2339: Property 'docpath' does not exist on type 'never'.
utils/helpers/starredDocuments.ts(115,32): error TS2339: Property 'metadata' does not exist on type 'never'.
utils/helpers/starredDocuments.ts(115,56): error TS2339: Property 'metadata' does not exist on type 'never'.
utils/helpers/starredDocuments.ts(116,32): error TS2339: Property 'filename' does not exist on type 'never'.
utils/helpers/starredDocuments.ts(116,48): error TS2339: Property 'metadata' does not exist on type 'never'.
utils/helpers/starredDocuments.ts(118,24): error TS2339: Property 'docId' does not exist on type 'never'.
utils/helpers/starredDocuments.ts(119,24): error TS2339: Property 'title' does not exist on type 'never'.
utils/helpers/starredDocuments.ts(119,37): error TS2339: Property 'metadata' does not exist on type 'never'.
utils/helpers/starredDocuments.ts(183,35): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
utils/helpers/supportFunctions.ts(2,10): error TS2614: Module '"../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../models/systemSettings"' instead?
utils/helpers/tiktoken.ts(14,11): error TS2564: Property '_model' has no initializer and is not definitely assigned in the constructor.
utils/helpers/tiktoken.ts(14,11): error TS6133: '_model' is declared but its value is never read.
utils/helpers/tiktoken.ts(19,10): error TS2551: Property 'model' does not exist on type 'TokenManager'. Did you mean '_model'?
utils/helpers/tiktoken.ts(21,32): error TS2345: Argument of type 'string' is not assignable to parameter of type 'TiktokenEncoding'.
utils/helpers/tiktoken.ts(26,38): error TS2345: Argument of type 'string' is not assignable to parameter of type 'TiktokenModel'.
utils/helpers/updateENV.ts(87,15): error TS2300: Duplicate identifier 'isNotEmpty'.
utils/helpers/updateENV.ts(88,15): error TS2300: Duplicate identifier 'supportedLLM'.
utils/helpers/updateENV.ts(89,15): error TS2300: Duplicate identifier 'validOpenAIKey'.
utils/helpers/updateENV.ts(90,15): error TS2300: Duplicate identifier 'validOpenAiTokenLimit'.
utils/helpers/updateENV.ts(91,15): error TS2300: Duplicate identifier 'validAnthropicApiKey'.
utils/helpers/updateENV.ts(92,15): error TS2300: Duplicate identifier 'validAnthropicModel'.
utils/helpers/updateENV.ts(93,15): error TS2300: Duplicate identifier 'validGeminiSafetySetting'.
utils/helpers/updateENV.ts(94,15): error TS2300: Duplicate identifier 'validLLMExternalBasePath'.
utils/helpers/updateENV.ts(95,15): error TS2300: Duplicate identifier 'validDockerizedUrl'.
utils/helpers/updateENV.ts(96,15): error TS2300: Duplicate identifier 'nonZero'.
utils/helpers/updateENV.ts(97,15): error TS2300: Duplicate identifier 'validOllamaLLMBasePath'.
utils/helpers/updateENV.ts(98,15): error TS2300: Duplicate identifier 'isInteger'.
utils/helpers/updateENV.ts(99,15): error TS2300: Duplicate identifier 'isDownloadedModel'.
utils/helpers/updateENV.ts(100,15): error TS2300: Duplicate identifier 'supportedEmbeddingModel'.
utils/helpers/updateENV.ts(101,15): error TS2300: Duplicate identifier 'supportedVectorDB'.
utils/helpers/updateENV.ts(102,15): error TS2300: Duplicate identifier 'requiresForceMode'.
utils/helpers/updateENV.ts(103,15): error TS2300: Duplicate identifier 'noRestrictedChars'.
utils/helpers/updateENV.ts(104,15): error TS2300: Duplicate identifier 'supportedTranscriptionProvider'.
utils/helpers/updateENV.ts(105,15): error TS2300: Duplicate identifier 'validLocalWhisper'.
utils/helpers/updateENV.ts(106,15): error TS2300: Duplicate identifier 'supportedTTSProvider'.
utils/helpers/updateENV.ts(107,15): error TS2300: Duplicate identifier 'validSlackWebhookUrl'.
utils/helpers/updateENV.ts(108,15): error TS2300: Duplicate identifier 'validContextualUserPrompt'.
utils/helpers/updateENV.ts(109,15): error TS2300: Duplicate identifier 'isValidURL'.
utils/helpers/updateENV.ts(110,15): error TS2300: Duplicate identifier 'validChromaURL'.
utils/helpers/updateENV.ts(210,6): error TS2322: Type '(input?: string) => string | number' is not assignable to type 'ValidationFunction'.
  Type 'string | number' is not assignable to type 'string | Promise<string | null> | null'.
    Type 'number' is not assignable to type 'string | Promise<string | null> | null'.
utils/helpers/updateENV.ts(219,5): error TS2322: Type '(input?: string) => boolean' is not assignable to type 'ValidationFunction'.
  Type 'boolean' is not assignable to type 'string | Promise<string | null> | null'.
utils/helpers/updateENV.ts(511,10): error TS2300: Duplicate identifier 'isNotEmpty'.
utils/helpers/updateENV.ts(512,41): error TS2322: Type 'Promise<string>' is not assignable to type 'string'.
utils/helpers/updateENV.ts(515,10): error TS2300: Duplicate identifier 'nonZero'.
utils/helpers/updateENV.ts(516,29): error TS2322: Type 'Promise<string>' is not assignable to type 'string'.
utils/helpers/updateENV.ts(517,31): error TS2322: Type 'Promise<string>' is not assignable to type 'string'.
utils/helpers/updateENV.ts(520,10): error TS2300: Duplicate identifier 'isInteger'.
utils/helpers/updateENV.ts(521,29): error TS2322: Type 'Promise<string>' is not assignable to type 'string | number'.
utils/helpers/updateENV.ts(525,10): error TS2300: Duplicate identifier 'isValidURL'.
utils/helpers/updateENV.ts(530,5): error TS2322: Type 'Promise<string>' is not assignable to type 'string'.
utils/helpers/updateENV.ts(534,10): error TS2300: Duplicate identifier 'validOpenAIKey'.
utils/helpers/updateENV.ts(535,43): error TS2322: Type 'Promise<string>' is not assignable to type 'string'.
utils/helpers/updateENV.ts(538,10): error TS2300: Duplicate identifier 'validSlackWebhookUrl'.
utils/helpers/updateENV.ts(546,10): error TS2300: Duplicate identifier 'validContextualUserPrompt'.
utils/helpers/updateENV.ts(548,5): error TS2322: Type 'Promise<string>' is not assignable to type 'string'.
utils/helpers/updateENV.ts(553,10): error TS2300: Duplicate identifier 'validAnthropicApiKey'.
utils/helpers/updateENV.ts(556,7): error TS2322: Type 'Promise<string>' is not assignable to type 'string'.
utils/helpers/updateENV.ts(559,10): error TS6133: 'validJinaApiKey' is declared but its value is never read.
utils/helpers/updateENV.ts(560,45): error TS2322: Type 'Promise<string>' is not assignable to type 'string'.
utils/helpers/updateENV.ts(563,10): error TS2300: Duplicate identifier 'validLLMExternalBasePath'.
utils/helpers/updateENV.ts(566,32): error TS2322: Type 'Promise<string>' is not assignable to type 'string'.
utils/helpers/updateENV.ts(568,7): error TS2322: Type 'Promise<string>' is not assignable to type 'string'.
utils/helpers/updateENV.ts(571,5): error TS2322: Type 'Promise<string>' is not assignable to type 'string'.
utils/helpers/updateENV.ts(575,10): error TS2300: Duplicate identifier 'validOllamaLLMBasePath'.
utils/helpers/updateENV.ts(579,7): error TS2322: Type 'Promise<string>' is not assignable to type 'string'.
utils/helpers/updateENV.ts(582,5): error TS2322: Type 'Promise<string>' is not assignable to type 'string'.
utils/helpers/updateENV.ts(586,10): error TS2300: Duplicate identifier 'supportedTTSProvider'.
utils/helpers/updateENV.ts(595,7): error TS2322: Type 'Promise<string>' is not assignable to type 'string'.
utils/helpers/updateENV.ts(598,10): error TS2300: Duplicate identifier 'validLocalWhisper'.
utils/helpers/updateENV.ts(606,7): error TS2322: Type 'Promise<string>' is not assignable to type 'string'.
utils/helpers/updateENV.ts(609,10): error TS2300: Duplicate identifier 'supportedLLM'.
utils/helpers/updateENV.ts(638,7): error TS2322: Type 'Promise<string>' is not assignable to type 'string'.
utils/helpers/updateENV.ts(641,10): error TS2300: Duplicate identifier 'supportedTranscriptionProvider'.
utils/helpers/updateENV.ts(646,10): error TS2300: Duplicate identifier 'validGeminiSafetySetting'.
utils/helpers/updateENV.ts(658,10): error TS2300: Duplicate identifier 'validAnthropicModel'.
utils/helpers/updateENV.ts(674,10): error TS2300: Duplicate identifier 'supportedEmbeddingModel'.
utils/helpers/updateENV.ts(694,10): error TS2300: Duplicate identifier 'supportedVectorDB'.
utils/helpers/updateENV.ts(710,10): error TS2300: Duplicate identifier 'validChromaURL'.
utils/helpers/updateENV.ts(716,10): error TS2300: Duplicate identifier 'validOpenAiTokenLimit'.
utils/helpers/updateENV.ts(725,10): error TS2300: Duplicate identifier 'requiresForceMode'.
utils/helpers/updateENV.ts(734,10): error TS2300: Duplicate identifier 'isDownloadedModel'.
utils/helpers/updateENV.ts(750,16): error TS2300: Duplicate identifier 'validDockerizedUrl'.
utils/helpers/updateENV.ts(782,10): error TS6133: 'validHuggingFaceEndpoint' is declared but its value is never read.
utils/helpers/updateENV.ts(788,10): error TS2300: Duplicate identifier 'noRestrictedChars'.
utils/helpers/updateENV.ts(1000,7): error TS18046: 'error' is of type 'unknown'.
utils/helpers/updateENV.ts(1034,7): error TS18046: 'error' is of type 'unknown'.
utils/helpers/updateENV.ts(1096,7): error TS18046: 'error' is of type 'unknown'.
utils/helpers/vectorizationCheck.ts(45,37): error TS2339: Property 'checkDocumentHasDocId' does not exist on type 'BaseVectorDatabaseProvider'.
utils/helpers/vectorizationCheck.ts(58,9): error TS18046: 'error' is of type 'unknown'.
utils/helpers/vectorizationCheck.ts(124,47): error TS7006: Parameter 'field' implicitly has an 'any' type.
utils/helpers/vectorizationCheck.ts(154,11): error TS18046: 'schemaError' is of type 'unknown'.
utils/helpers/vectorizationCheck.ts(155,11): error TS18046: 'schemaError' is of type 'unknown'.
utils/helpers/vectorizationCheck.ts(354,66): error TS18046: 'error' is of type 'unknown'.
utils/helpers/versionComparison.ts(130,5): error TS2322: Type '{ [key: string]: any; versions?: VersionObject[]; version?: string; description?: string; timestamp?: string; }' is not assignable to type 'VersionObject'.
  Types of property 'version' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.
utils/MCP/hypervisor/index.ts(39,11): error TS2430: Interface 'ExtendedMCPClient' incorrectly extends interface 'Client<{ method: string; params?: { [x: string]: unknown; _meta?: { [x: string]: unknown; progressToken?: string | number | undefined; } | undefined; } | undefined; }, { method: string; params?: { [x: string]: unknown; _meta?: { ...; } | undefined; } | undefined; }, { ...; }>'.
  Types of property 'transport' are incompatible.
    Type '{ _process: ChildProcess; close(): void; onclose?: (() => void) | undefined; onerror?: ((error: any) => void) | undefined; onmessage?: ((message: any) => void) | undefined; }' is missing the following properties from type 'Transport': start, send
utils/MCP/hypervisor/index.ts(89,13): error TS2564: Property 'mcpServerJSONPath' has no initializer and is not definitely assigned in the constructor.
utils/MCP/hypervisor/index.ts(238,25): error TS2339: Property 'close' does not exist on type 'never'.
utils/MCP/hypervisor/index.ts(313,17): error TS2352: Conversion of type 'Client<{ method: string; params?: { [x: string]: unknown; _meta?: { [x: string]: unknown; progressToken?: string | number | undefined; } | undefined; } | undefined; }, { method: string; params?: { [x: string]: unknown; _meta?: { ...; } | undefined; } | undefined; }, { ...; }>' to type 'ExtendedMCPClient' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'aibitatToolIds' is missing in type 'Client<{ method: string; params?: { [x: string]: unknown; _meta?: { [x: string]: unknown; progressToken?: string | number | undefined; } | undefined; } | undefined; }, { method: string; params?: { [x: string]: unknown; _meta?: { ...; } | undefined; } | undefined; }, { ...; }>' but required in type 'ExtendedMCPClient'.
utils/MCP/index.ts(2,15): error TS2614: Module '"./hypervisor"' has no exported member 'MCPServerDefinition'. Did you mean to use 'import MCPServerDefinition from "./hypervisor"' instead?
utils/MCP/index.ts(98,7): error TS2417: Class static side 'typeof MCPCompatibilityLayer' incorrectly extends base class static side 'typeof MCPHypervisor'.
  Types have separate declarations of a private property '_instance'.
utils/middleware/featureFlagEnabled.ts(2,10): error TS2614: Module '"../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../models/systemSettings"' instead?
utils/middleware/isSupportedRepoProviders.ts(20,26): error TS2339: Property 'text' does not exist on type 'Response<any, Record<string, any>>'.
utils/middleware/multiUserProtected.ts(2,10): error TS2614: Module '"../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../models/systemSettings"' instead?
utils/middleware/reportOwnerOrAdmin.ts(2,8): error TS2613: Module '"/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/systemReport"' has no default export. Did you mean to use 'import { SystemReport } from "/Users/<USER>/GitHub/I_produktion/ISTLegal/server/models/systemReport"' instead?
utils/middleware/validApiKey.ts(3,10): error TS2614: Module '"../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../models/systemSettings"' instead?
utils/middleware/validatedRequest.ts(3,10): error TS2614: Module '"../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../models/systemSettings"' instead?
utils/middleware/validatedRequest.ts(56,43): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
utils/middleware/validatedRequest.ts(70,7): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
utils/middleware/validatedRequest.ts(70,29): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
utils/middleware/validBrowserExtensionApiKey.ts(3,10): error TS2614: Module '"../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../models/systemSettings"' instead?
utils/middleware/validBrowserExtensionApiKey.ts(35,28): error TS2352: Conversion of type 'FilteredUser | null' to type 'AuthenticatedUser' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'email' is missing in type 'FilteredUser' but required in type 'AuthenticatedUser'.
utils/middleware/validBrowserExtensionApiKey.ts(36,7): error TS2322: Type 'number | null' is not assignable to type 'string | number | undefined'.
  Type 'null' is not assignable to type 'string | number | undefined'.
utils/middleware/validWorkspace.ts(11,11): error TS2430: Interface 'WorkspaceRequest' incorrectly extends interface 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
  Types of property 'query' are incompatible.
    Type '{ includeDocuments?: string | boolean | undefined; }' is not assignable to type 'ParsedQs'.
      Property 'includeDocuments' is incompatible with index signature.
        Type 'string | boolean' is not assignable to type 'string | ParsedQs | (string | ParsedQs)[] | undefined'.
          Type 'false' is not assignable to type 'string | ParsedQs | (string | ParsedQs)[] | undefined'.
utils/middleware/validWorkspace.ts(30,5): error TS2345: Argument of type 'WorkspaceRequest' is not assignable to parameter of type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
  Types of property 'query' are incompatible.
    Type '{ includeDocuments?: string | boolean | undefined; }' is not assignable to type 'ParsedQs'.
      Property 'includeDocuments' is incompatible with index signature.
        Type 'string | boolean' is not assignable to type 'string | ParsedQs | (string | ParsedQs)[] | undefined'.
          Type 'false' is not assignable to type 'string | ParsedQs | (string | ParsedQs)[] | undefined'.
utils/middleware/validWorkspace.ts(36,23): error TS2339: Property 'getWithUser' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
utils/middleware/validWorkspace.ts(37,37): error TS2345: Argument of type 'string | boolean' is not assignable to parameter of type 'boolean | undefined'.
  Type 'string' is not assignable to type 'boolean | undefined'.
utils/middleware/validWorkspace.ts(76,5): error TS2345: Argument of type 'WorkspaceRequest' is not assignable to parameter of type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
  Types of property 'query' are incompatible.
    Type '{ includeDocuments?: string | boolean | undefined; }' is not assignable to type 'ParsedQs'.
      Property 'includeDocuments' is incompatible with index signature.
        Type 'string | boolean' is not assignable to type 'string | ParsedQs | (string | ParsedQs)[] | undefined'.
          Type 'false' is not assignable to type 'string | ParsedQs | (string | ParsedQs)[] | undefined'.
utils/middleware/validWorkspace.ts(82,23): error TS2339: Property 'getWithUser' does not exist on type '{ defaultPrompt: string; writable: readonly ["name", "slug", "vectorTag", "openAiTemp", "openAiHistory", "lastUpdatedAt", "openAiPrompt", "similarityThreshold", "chatProvider", ... 15 more ..., "sharedWithOrg"]; ... 9 more ...; userHasAccess: (userId: number, workspaceId: number) => Promise<...>; }'.
utils/middleware/validWorkspace.ts(83,37): error TS2345: Argument of type 'string | boolean' is not assignable to parameter of type 'boolean | undefined'.
  Type 'string' is not assignable to type 'boolean | undefined'.
utils/middleware/validWorkspace.ts(114,7): error TS2345: Argument of type 'UserRole' is not assignable to parameter of type 'UserRole.ADMIN | UserRole.MANAGER | UserRole.SUPERUSER'.
utils/middleware/validWorkspace.ts(139,5): error TS2345: Argument of type '{ slug: string | undefined; workspace_id: any; } | { slug: string | undefined; user_id: number | null; }' is not assignable to parameter of type 'WhereClause | undefined'.
  Type '{ slug: string | undefined; user_id: number | null; }' is not assignable to type 'WhereClause'.
    Types of property 'user_id' are incompatible.
      Type 'number | null' is not assignable to type 'number | undefined'.
        Type 'null' is not assignable to type 'number | undefined'.
utils/modulePatches.ts(15,26): error TS2352: Conversion of type 'Module' to type 'ModulePrototype' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Types of property 'require' are incompatible.
    Type '(id: string) => any' is missing the following properties from type 'NodeRequire': cache, extensions, main, resolve
utils/modulePatches.ts(24,1): error TS2739: Type '(id: string) => any' is missing the following properties from type 'NodeRequire': cache, extensions, main, resolve
utils/modulePatches.ts(24,2): error TS2352: Conversion of type 'Module' to type 'ModulePrototype' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Types of property 'require' are incompatible.
    Type '(id: string) => any' is missing the following properties from type 'NodeRequire': cache, extensions, main, resolve
utils/notifications/slack.ts(847,9): error TS2353: Object literal may only specify known properties, and 'timeout' does not exist in type 'RequestInit'.
utils/PasswordRecovery/index.ts(119,39): error TS18047: 'passwordResetToken' is possibly 'null'.
utils/proseMirror/modules/analysis.ts(339,18): error TS2339: Property 'includes' does not exist on type 'string | number'.
  Property 'includes' does not exist on type 'number'.
utils/proseMirror/modules/analysis.ts(339,52): error TS2339: Property 'includes' does not exist on type 'string | number'.
  Property 'includes' does not exist on type 'number'.
utils/proseMirror/modules/documentBuilder.ts(11,3): error TS6196: 'ProseMirrorNode' is declared but never used.
utils/proseMirror/modules/documentBuilder.ts(149,41): error TS7006: Parameter 'child' implicitly has an 'any' type.
utils/robustLlmUtils/connectors/robustConnector.ts(214,11): error TS2353: Object literal may only specify known properties, and 'maxRetries' does not exist in type 'ParseOptions'.
utils/robustLlmUtils/connectors/robustConnector.ts(224,13): error TS2322: Type '{ rawResponse: any; metrics: any; success: boolean; data: any; error: string | null; attemptsMade?: number; }' is not assignable to type 'JsonResponseResult'.
  Types of property 'error' are incompatible.
    Type 'string | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
utils/robustLlmUtils/connectors/robustConnector.ts(277,7): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
utils/robustLlmUtils/providers/embedderInstantiator.ts(61,33): error TS2554: Expected 0 arguments, but got 2.
utils/robustLlmUtils/providers/embedderInstantiator.ts(96,9): error TS2554: Expected 0 arguments, but got 3.
utils/robustLlmUtils/providers/providerInstantiator.ts(148,7): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
utils/TextSplitter/index.ts(241,11): error TS6133: 'log' is declared but its value is never read.
utils/TextToSpeech/elevenLabs/index.ts(38,7): error TS2322: Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/elevenlabs/api/types/Voice").Voice[]' is not assignable to type 'Voice[]'.
  Type 'import("/Users/<USER>/GitHub/I_produktion/ISTLegal/server/node_modules/elevenlabs/api/types/Voice").Voice' is not assignable to type 'Voice'.
    Types of property 'name' are incompatible.
      Type 'string | undefined' is not assignable to type 'string'.
        Type 'undefined' is not assignable to type 'string'.
utils/vectorDbProviders/astra/index.ts(1,10): error TS2305: Module '"@datastax/astra-db-ts"' has no exported member 'AstraDB'.
utils/vectorDbProviders/astra/index.ts(6,3): error TS6133: 'VectorDbOperations' is declared but its value is never read.
utils/vectorDbProviders/astra/index.ts(7,3): error TS6133: 'VectorRecord' is declared but its value is never read.
utils/vectorDbProviders/astra/index.ts(8,3): error TS6133: 'VectorSearchResult' is declared but its value is never read.
utils/vectorDbProviders/astra/index.ts(9,3): error TS6133: 'VectorQueryOptions' is declared but its value is never read.
utils/vectorDbProviders/astra/index.ts(10,3): error TS6133: 'VectorInsertOptions' is declared but its value is never read.
utils/vectorDbProviders/astra/index.ts(11,3): error TS6133: 'CollectionInfo' is declared but its value is never read.
utils/vectorDbProviders/astra/index.ts(13,3): error TS6133: 'VectorMetric' is declared but its value is never read.
utils/vectorDbProviders/astra/index.ts(26,11): error TS6196: 'AstraDbConfig' is declared but never used.
utils/vectorDbProviders/astra/index.ts(535,31): error TS2304: Cannot find name 'RequestRedirect'.
utils/vectorDbProviders/chroma/index.ts(3,10): error TS2614: Module '"../../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../../models/systemSettings"' instead?
utils/vectorDbProviders/chroma/index.ts(15,3): error TS6133: 'VectorRecord' is declared but its value is never read.
utils/vectorDbProviders/chroma/index.ts(16,3): error TS6133: 'VectorSearchResult' is declared but its value is never read.
utils/vectorDbProviders/chroma/index.ts(17,3): error TS6133: 'VectorQueryOptions' is declared but its value is never read.
utils/vectorDbProviders/chroma/index.ts(18,3): error TS6133: 'VectorInsertOptions' is declared but its value is never read.
utils/vectorDbProviders/chroma/index.ts(19,3): error TS6133: 'CollectionInfo' is declared but its value is never read.
utils/vectorDbProviders/chroma/index.ts(88,11): error TS6196: 'AdjacentVectorsParams' is declared but never used.
utils/vectorDbProviders/chroma/index.ts(336,25): error TS2339: Property 'exists' does not exist on type 'boolean | VectorCacheResult'.
  Property 'exists' does not exist on type 'false'.
utils/vectorDbProviders/chroma/index.ts(342,19): error TS2339: Property 'chunks' does not exist on type 'boolean | VectorCacheResult'.
utils/vectorDbProviders/chroma/index.ts(405,55): error TS2345: Argument of type 'string | string[]' is not assignable to parameter of type 'string'.
  Type 'string[]' is not assignable to type 'string'.
utils/vectorDbProviders/chroma/index.ts(559,5): error TS2322: Type 'null' is not assignable to type 'string'.
utils/vectorDbProviders/lance/index.ts(8,3): error TS6133: 'VectorDbOperations' is declared but its value is never read.
utils/vectorDbProviders/lance/index.ts(9,3): error TS6133: 'VectorRecord' is declared but its value is never read.
utils/vectorDbProviders/lance/index.ts(10,3): error TS6133: 'VectorSearchResult' is declared but its value is never read.
utils/vectorDbProviders/lance/index.ts(11,3): error TS6133: 'VectorQueryOptions' is declared but its value is never read.
utils/vectorDbProviders/lance/index.ts(12,3): error TS6133: 'VectorInsertOptions' is declared but its value is never read.
utils/vectorDbProviders/lance/index.ts(13,3): error TS6133: 'CollectionInfo' is declared but its value is never read.
utils/vectorDbProviders/lance/index.ts(15,3): error TS6133: 'VectorMetric' is declared but its value is never read.
utils/vectorDbProviders/lance/index.ts(19,7): error TS6198: All destructured elements are unused.
utils/vectorDbProviders/lance/index.ts(91,11): error TS6196: 'AdjacentVectorsParams' is declared but never used.
utils/vectorDbProviders/lance/index.ts(493,38): error TS6133: 'metadata' is declared but its value is never read.
utils/vectorDbProviders/lance/index.ts(564,5): error TS2322: Type 'null' is not assignable to type 'string'.
utils/vectorDbProviders/milvus/index.ts(12,3): error TS6133: 'VectorRecord' is declared but its value is never read.
utils/vectorDbProviders/milvus/index.ts(13,3): error TS6133: 'VectorSearchResult' is declared but its value is never read.
utils/vectorDbProviders/milvus/index.ts(14,3): error TS6133: 'VectorQueryOptions' is declared but its value is never read.
utils/vectorDbProviders/milvus/index.ts(15,3): error TS6133: 'VectorInsertOptions' is declared but its value is never read.
utils/vectorDbProviders/milvus/index.ts(16,3): error TS6133: 'CollectionInfo' is declared but its value is never read.
utils/vectorDbProviders/pinecone/index.ts(3,10): error TS2614: Module '"../../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../../models/systemSettings"' instead?
utils/vectorDbProviders/pinecone/index.ts(14,3): error TS6133: 'VectorRecord' is declared but its value is never read.
utils/vectorDbProviders/pinecone/index.ts(15,3): error TS6133: 'VectorSearchResult' is declared but its value is never read.
utils/vectorDbProviders/pinecone/index.ts(16,3): error TS6133: 'VectorQueryOptions' is declared but its value is never read.
utils/vectorDbProviders/pinecone/index.ts(17,3): error TS6133: 'VectorInsertOptions' is declared but its value is never read.
utils/vectorDbProviders/pinecone/index.ts(18,3): error TS6133: 'CollectionInfo' is declared but its value is never read.
utils/vectorDbProviders/pinecone/index.ts(88,11): error TS6196: 'AdjacentVectorsParams' is declared but never used.
utils/vectorDbProviders/pinecone/index.ts(256,25): error TS2339: Property 'exists' does not exist on type 'boolean | VectorCacheResult'.
  Property 'exists' does not exist on type 'false'.
utils/vectorDbProviders/pinecone/index.ts(259,19): error TS2339: Property 'chunks' does not exist on type 'boolean | VectorCacheResult'.
utils/vectorDbProviders/pinecone/index.ts(306,55): error TS2345: Argument of type 'string | string[]' is not assignable to parameter of type 'string'.
  Type 'string[]' is not assignable to type 'string'.
utils/vectorDbProviders/pinecone/index.ts(477,5): error TS2322: Type 'null' is not assignable to type 'string'.
utils/vectorDbProviders/qdrant/index.ts(3,10): error TS2614: Module '"../../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../../models/systemSettings"' instead?
utils/vectorDbProviders/qdrant/index.ts(14,3): error TS6133: 'VectorRecord' is declared but its value is never read.
utils/vectorDbProviders/qdrant/index.ts(15,3): error TS6133: 'VectorSearchResult' is declared but its value is never read.
utils/vectorDbProviders/qdrant/index.ts(16,3): error TS6133: 'VectorQueryOptions' is declared but its value is never read.
utils/vectorDbProviders/qdrant/index.ts(17,3): error TS6133: 'VectorInsertOptions' is declared but its value is never read.
utils/vectorDbProviders/qdrant/index.ts(18,3): error TS6133: 'CollectionInfo' is declared but its value is never read.
utils/vectorDbProviders/qdrant/index.ts(89,11): error TS6196: 'AdjacentVectorsParams' is declared but never used.
utils/vectorDbProviders/qdrant/index.ts(128,51): error TS2554: Expected 1-2 arguments, but got 0.
utils/vectorDbProviders/qdrant/index.ts(187,55): error TS2345: Argument of type 'Record<string, any> | undefined' is not assignable to parameter of type 'SourceDocument'.
  Type 'undefined' is not assignable to type 'SourceDocument'.
utils/vectorDbProviders/qdrant/index.ts(299,25): error TS2339: Property 'exists' does not exist on type 'boolean | VectorCacheResult'.
  Property 'exists' does not exist on type 'false'.
utils/vectorDbProviders/qdrant/index.ts(301,19): error TS2339: Property 'chunks' does not exist on type 'boolean | VectorCacheResult'.
utils/vectorDbProviders/qdrant/index.ts(387,55): error TS2345: Argument of type 'string | string[]' is not assignable to parameter of type 'string'.
  Type 'string[]' is not assignable to type 'string'.
utils/vectorDbProviders/qdrant/index.ts(499,11): error TS6133: '_tokenManager' is declared but its value is never read.
utils/vectorDbProviders/qdrant/index.ts(517,5): error TS2322: Type 'null' is not assignable to type 'string'.
utils/vectorDbProviders/weaviate/index.ts(3,10): error TS2614: Module '"../../../models/systemSettings"' has no exported member 'SystemSettings'. Did you mean to use 'import SystemSettings from "../../../models/systemSettings"' instead?
utils/vectorDbProviders/weaviate/index.ts(15,3): error TS6133: 'VectorRecord' is declared but its value is never read.
utils/vectorDbProviders/weaviate/index.ts(16,3): error TS6133: 'VectorSearchResult' is declared but its value is never read.
utils/vectorDbProviders/weaviate/index.ts(17,3): error TS6133: 'VectorQueryOptions' is declared but its value is never read.
utils/vectorDbProviders/weaviate/index.ts(18,3): error TS6133: 'VectorInsertOptions' is declared but its value is never read.
utils/vectorDbProviders/weaviate/index.ts(19,3): error TS6133: 'CollectionInfo' is declared but its value is never read.
utils/vectorDbProviders/weaviate/index.ts(104,11): error TS6196: 'AdjacentVectorsParams' is declared but never used.
utils/vectorDbProviders/weaviate/index.ts(370,25): error TS2339: Property 'exists' does not exist on type 'boolean | VectorCacheResult'.
  Property 'exists' does not exist on type 'false'.
utils/vectorDbProviders/weaviate/index.ts(387,19): error TS2339: Property 'chunks' does not exist on type 'boolean | VectorCacheResult'.
utils/vectorDbProviders/weaviate/index.ts(450,55): error TS2345: Argument of type 'string | string[]' is not assignable to parameter of type 'string'.
  Type 'string[]' is not assignable to type 'string'.
utils/vectorDbProviders/weaviate/index.ts(554,11): error TS6133: '_tokenManager' is declared but its value is never read.
utils/vectorDbProviders/weaviate/index.ts(572,5): error TS2322: Type 'null' is not assignable to type 'string'.
utils/vectorDbProviders/zilliz/index.ts(12,3): error TS6133: 'VectorRecord' is declared but its value is never read.
utils/vectorDbProviders/zilliz/index.ts(13,3): error TS6133: 'VectorSearchResult' is declared but its value is never read.
utils/vectorDbProviders/zilliz/index.ts(14,3): error TS6133: 'VectorQueryOptions' is declared but its value is never read.
utils/vectorDbProviders/zilliz/index.ts(15,3): error TS6133: 'VectorInsertOptions' is declared but its value is never read.
utils/vectorDbProviders/zilliz/index.ts(16,3): error TS6133: 'CollectionInfo' is declared but its value is never read.
