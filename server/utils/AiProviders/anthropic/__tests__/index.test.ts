import { AnthropicLLM } from "../index";
import { NativeEmbedder } from "../../../EmbeddingEngines/native";
import { LLMPerformanceMonitor } from "../../../helpers/chat/LLMPerformanceMonitor";
import {
  writeResponseChunk,
  clientAbortedHandler,
} from "../../../helpers/chat/responses";
import { formatContextTexts } from "../../../helpers";
import Anthropic from "@anthropic-ai/sdk";
import type { Response as ExpressResponse } from "express";
import type {
  ChatMessage,
  PromptArgs,
  EmbeddingEngine,
  Attachment,
  StreamResponseProps,
} from "../../../../types/ai-providers";

// Mock dependencies
jest.mock("@anthropic-ai/sdk");
jest.mock("../../../EmbeddingEngines/native");
jest.mock("../../../helpers/chat/LLMPerformanceMonitor");
jest.mock("../../../helpers/chat/responses");
jest.mock("../../../helpers", () => ({
  formatContextTexts: jest.fn().mockImplementation(async (texts: string[]) => {
    return texts.join("\n\n---CONTEXT---\n\n");
  }),
}));
jest.mock("../../../helpers/chat", () => ({
  messageArrayCompressor: jest
    .fn()
    .mockImplementation(async (provider, messages, _rawHistory) => {
      return messages;
    }),
}));
jest.mock("../../streamConverters", () => ({
  createMonitoredStream: jest.fn().mockImplementation(async (stream) => {
    return {
      ...stream,
      start: Date.now(),
      duration: 0,
      metrics: {},
      endMeasurement: jest.fn().mockReturnValue({
        prompt_tokens: 100,
        completion_tokens: 50,
        total_tokens: 150,
        outputTps: 50,
        duration: 1000,
      }),
    };
  }),
  createStreamResponse: jest.fn().mockImplementation((stream) => ({
    stream,
    endMeasurement: stream.endMeasurement,
  })),
}));

// Mock Anthropic client
const mockAnthropicClient = {
  messages: {
    create: jest.fn(),
    stream: jest.fn(),
  },
};

// Import Anthropic after mocking
const MockAnthropic = Anthropic as jest.MockedClass<typeof Anthropic>;

describe("AnthropicLLM", () => {
  const mockNativeEmbedder = NativeEmbedder as jest.MockedClass<
    typeof NativeEmbedder
  >;
  const mockLLMPerformanceMonitor = LLMPerformanceMonitor as jest.MockedClass<
    typeof LLMPerformanceMonitor
  >;
  const mockWriteResponseChunk = writeResponseChunk as jest.MockedFunction<
    typeof writeResponseChunk
  >;
  const mockClientAbortedHandler = clientAbortedHandler as jest.MockedFunction<
    typeof clientAbortedHandler
  >;
  const _mockFormatContextTexts = formatContextTexts as jest.MockedFunction<
    typeof formatContextTexts
  >;

  let originalEnv: typeof process.env;
  let consoleLogSpy: jest.SpyInstance;
  let consoleErrorSpy: jest.SpyInstance;

  beforeEach(() => {
    // Store original environment
    originalEnv = { ...process.env };

    // Set up required environment variables
    process.env.ANTHROPIC_API_KEY = "test-anthropic-api-key";
    process.env.ANTHROPIC_MODEL_PREF = "claude-3-opus-latest";

    // Clear all mocks
    jest.clearAllMocks();

    // Spy on console methods
    consoleLogSpy = jest.spyOn(console, "log").mockImplementation();
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();

    // Mock Anthropic constructor
    MockAnthropic.mockImplementation(() => mockAnthropicClient as any);

    // Mock NativeEmbedder
    mockNativeEmbedder.mockClear();
    const mockEmbedderInstance = {
      embedTextInput: jest.fn().mockResolvedValue([0.1, 0.2, 0.3]),
      embedChunks: jest.fn().mockResolvedValue([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]),
    };
    mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);

    // Mock LLM Performance Monitor
    mockLLMPerformanceMonitor.measureAsyncFunction = jest
      .fn()
      .mockImplementation(async (promise) => {
        const output = await promise;
        return { output, duration: 1000 };
      });
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
    consoleLogSpy.mockRestore();
    consoleErrorSpy.mockRestore();
  });

  describe("constructor and initialization", () => {
    it("should initialize with default settings", () => {
      const anthropic = new AnthropicLLM();

      expect(anthropic.model).toBe("claude-3-opus-latest");
      expect(anthropic.limits).toEqual({
        history: 200000 * 0.15,
        system: 200000 * 0.15,
        user: 200000 * 0.7,
      });
      expect(anthropic.defaultTemp).toBe(0.7);
      expect(anthropic.embedder).toBeDefined();
    });

    it("should throw error when API key is missing", () => {
      delete process.env.ANTHROPIC_API_KEY;

      expect(() => new AnthropicLLM()).toThrow("No Anthropic API key was set.");
    });

    it("should use custom embedder when provided", () => {
      const customEmbedder = {} as EmbeddingEngine;
      const anthropic = new AnthropicLLM(customEmbedder);

      expect(anthropic.embedder).toBe(customEmbedder);
    });

    it("should use model preference when provided", () => {
      const anthropic = new AnthropicLLM(null, "claude-3-sonnet-20240229");

      expect(anthropic.model).toBe("claude-3-sonnet-20240229");
    });

    it("should use settings suffix when provided", () => {
      process.env.ANTHROPIC_API_KEY_CUSTOM = "custom-api-key";
      process.env.ANTHROPIC_MODEL_PREF_CUSTOM = "claude-3-haiku-20240307";

      const anthropic = new AnthropicLLM(null, null, "_CUSTOM");

      expect(anthropic.model).toBe("claude-3-haiku-20240307");
      expect(MockAnthropic).toHaveBeenCalledWith({
        apiKey: "custom-api-key",
      });
    });

    it("should use default model when no preference is set", () => {
      delete process.env.ANTHROPIC_MODEL_PREF;

      const anthropic = new AnthropicLLM();

      expect(anthropic.model).toBe("claude-2.0");
    });

    it("should calculate limits based on context window", () => {
      const anthropic = new AnthropicLLM(null, "claude-3-opus-latest");
      const contextWindow = 200000;

      expect(anthropic.limits).toEqual({
        history: contextWindow * 0.15,
        system: contextWindow * 0.15,
        user: contextWindow * 0.7,
      });
    });
  });

  describe("streamingEnabled", () => {
    it("should return true since streamGetChatCompletion is defined", () => {
      const anthropic = new AnthropicLLM();
      expect(anthropic.streamingEnabled()).toBe(true);
    });
  });

  describe("promptWindowLimit methods", () => {
    it("should return static prompt window limit for known models", () => {
      expect(AnthropicLLM.promptWindowLimit("claude-3-opus-latest")).toBe(
        200000
      );
      expect(AnthropicLLM.promptWindowLimit("claude-3-sonnet-20240229")).toBe(
        200000
      );
      expect(AnthropicLLM.promptWindowLimit("claude-2.1")).toBe(200000);
    });

    it("should return default for unknown model", () => {
      expect(AnthropicLLM.promptWindowLimit("unknown-model")).toBe(200000);
    });

    it("should return instance prompt window limit", () => {
      const anthropic = new AnthropicLLM();
      expect(anthropic.promptWindowLimit()).toBe(200000);
    });

    it("should return custom prompt window limit", () => {
      const anthropic = new AnthropicLLM();
      expect(anthropic.customPromptWindowLimit()).toBe(200000);
    });

    it("should handle empty model name", () => {
      expect(AnthropicLLM.promptWindowLimit("")).toBe(200000);
    });

    it("should handle different Claude models correctly", () => {
      const opus = new AnthropicLLM(null, "claude-3-opus-latest");
      const sonnet = new AnthropicLLM(null, "claude-3-sonnet-20240229");
      const haiku = new AnthropicLLM(null, "claude-3-haiku-20240307");

      expect(opus.promptWindowLimit()).toBe(200000);
      expect(sonnet.promptWindowLimit()).toBe(200000);
      expect(haiku.promptWindowLimit()).toBe(200000);
    });
  });

  describe("isValidChatCompletionModel", () => {
    it("should return true for Claude models", () => {
      const anthropic = new AnthropicLLM();

      expect(anthropic.isValidChatCompletionModel("claude-3-opus-latest")).toBe(
        true
      );
      expect(
        anthropic.isValidChatCompletionModel("claude-3-sonnet-20240229")
      ).toBe(true);
      expect(
        anthropic.isValidChatCompletionModel("claude-3-haiku-20240307")
      ).toBe(true);
      expect(
        anthropic.isValidChatCompletionModel("claude-3-5-sonnet-20240620")
      ).toBe(true);
      expect(anthropic.isValidChatCompletionModel("claude-opus-4-0")).toBe(
        true
      );
      expect(anthropic.isValidChatCompletionModel("claude-sonnet-4-0")).toBe(
        true
      );
    });

    it("should return false for invalid models", () => {
      const anthropic = new AnthropicLLM();

      expect(anthropic.isValidChatCompletionModel("invalid-model")).toBe(false);
      expect(anthropic.isValidChatCompletionModel("gpt-4")).toBe(false);
    });

    it("should handle empty model name", () => {
      const anthropic = new AnthropicLLM();
      expect(anthropic.isValidChatCompletionModel("")).toBe(false);
    });

    it("should handle undefined model name", () => {
      const anthropic = new AnthropicLLM();
      expect(anthropic.isValidChatCompletionModel()).toBe(false);
    });
  });

  describe("generateContent", () => {
    let anthropic: AnthropicLLM;

    beforeEach(() => {
      anthropic = new AnthropicLLM();
    });

    it("should return string when no attachments", () => {
      const result = anthropic["generateContent"]({
        userPrompt: "Hello Claude",
        attachments: [],
      });

      expect(result).toBe("Hello Claude");
    });

    it("should handle image attachments", () => {
      const attachments: Attachment[] = [
        {
          name: "image.png",
          mime: "image/png",
          contentString: "data:image/png;base64,iVBORw0KGgo...",
        },
      ];

      const result = anthropic["generateContent"]({
        userPrompt: "Analyze this image",
        attachments,
      });

      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray).toHaveLength(2);
      expect(resultArray[0]).toEqual({
        type: "text",
        text: "Analyze this image",
      });
      expect(resultArray[1]).toEqual({
        type: "image",
        source: {
          type: "base64",
          media_type: "image/png",
          data: "iVBORw0KGgo...",
        },
      });
    });

    it("should handle text attachments", () => {
      const attachments: Attachment[] = [
        {
          name: "document.txt",
          contentString: "File content here",
          type: "text/plain",
        },
      ];

      const result = anthropic["generateContent"]({
        userPrompt: "Analyze this document",
        attachments,
      });

      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray[1]).toEqual({
        type: "text",
        text: "\n\nAttached file: document.txt\nFile content here",
      });
    });

    it("should handle multiple attachments", () => {
      const attachments: Attachment[] = [
        {
          name: "image.jpg",
          mime: "image/jpeg",
          contentString: "data:image/jpeg;base64,test",
        },
        {
          name: "text.txt",
          mime: "text/plain",
          contentString: "Text content",
        },
      ];

      const result = anthropic["generateContent"]({
        userPrompt: "Analyze these files",
        attachments,
      });

      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray).toHaveLength(3); // text + image + text file
    });

    it("should handle attachments without mime type", () => {
      const attachments: Attachment[] = [
        {
          name: "file.bin",
          contentString: "Binary content",
        },
      ];

      const result = anthropic["generateContent"]({
        userPrompt: "Process this",
        attachments,
      });

      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray[1].type).toBe("text");
    });

    it("should handle various image formats", () => {
      const attachments: Attachment[] = [
        {
          name: "test.jpg",
          mime: "image/jpeg",
          contentString: "data:image/jpeg;base64,jpeg_data",
        },
        {
          name: "test.webp",
          mime: "image/webp",
          contentString: "data:image/webp;base64,webp_data",
        },
        {
          name: "test.gif",
          mime: "image/gif",
          contentString: "data:image/gif;base64,gif_data",
        },
      ];

      const result = anthropic["generateContent"]({
        userPrompt: "Analyze images",
        attachments,
      });

      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray).toHaveLength(4); // 1 text + 3 images
    });
  });

  describe("constructPrompt", () => {
    let anthropic: AnthropicLLM;

    beforeEach(() => {
      anthropic = new AnthropicLLM();
    });

    it("should construct prompt with all components", async () => {
      const promptArgs: PromptArgs = {
        systemPrompt: "You are a helpful legal assistant.",
        contextTexts: ["Legal context 1", "Legal context 2"],
        chatHistory: [
          { role: "user", content: "What is contract law?" },
          { role: "assistant", content: "Contract law governs agreements..." },
        ],
        userPrompt: "Tell me more about consideration",
      };

      const result = await anthropic.constructPrompt(promptArgs);

      expect(result).toEqual([
        {
          role: "system",
          content:
            "You are a helpful legal assistant.Legal context 1\n\n---CONTEXT---\n\nLegal context 2",
        },
        { role: "user", content: "What is contract law?" },
        { role: "assistant", content: "Contract law governs agreements..." },
        { role: "user", content: "Tell me more about consideration" },
      ]);
    });

    it("should handle attachments in prompt", async () => {
      const attachment: Attachment = {
        name: "contract.pdf",
        mime: "application/pdf",
        contentString: "Contract content",
      };

      const promptArgs: PromptArgs = {
        systemPrompt: "Analyze contracts",
        userPrompt: "Review this contract",
        attachments: [attachment],
      };

      const result = await anthropic.constructPrompt(promptArgs);

      const lastMessage = result[result.length - 1];
      expect(lastMessage.role).toBe("user");
      expect(Array.isArray(lastMessage.content)).toBe(true);
    });

    it("should handle empty context texts", async () => {
      const promptArgs: PromptArgs = {
        systemPrompt: "System prompt",
        contextTexts: [],
        chatHistory: [],
        userPrompt: "User prompt",
      };

      const result = await anthropic.constructPrompt(promptArgs);

      expect(result).toEqual([
        { role: "system", content: "System prompt" },
        { role: "user", content: "User prompt" },
      ]);
    });

    it("should handle missing optional parameters", async () => {
      const promptArgs: PromptArgs = {
        userPrompt: "Simple prompt",
      };

      const result = await anthropic.constructPrompt(promptArgs);

      expect(result).toEqual([
        { role: "system", content: "" },
        { role: "user", content: "Simple prompt" },
      ]);
    });
  });

  describe("getChatCompletion", () => {
    let anthropic: AnthropicLLM;

    beforeEach(() => {
      anthropic = new AnthropicLLM();
      mockAnthropicClient.messages.create.mockClear();
    });

    it("should return completion response successfully", async () => {
      const mockResponse = {
        content: [
          {
            type: "text",
            text: "Legal response from Claude",
          },
        ],
        usage: {
          input_tokens: 50,
          output_tokens: 100,
        },
      };

      mockAnthropicClient.messages.create.mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: "system", content: "You are a legal assistant" },
        { role: "user", content: "Explain negligence in tort law" },
      ];

      const result = await anthropic.getChatCompletion(messages, {
        temperature: 0.5,
      });

      expect(result).toEqual({
        textResponse: "Legal response from Claude",
        metrics: {
          prompt_tokens: 50,
          completion_tokens: 100,
          total_tokens: 150,
          outputTps: 100 / 1000, // 100 tokens / 1000 ms
          duration: 1000,
        },
      });

      expect(mockAnthropicClient.messages.create).toHaveBeenCalledWith({
        model: "claude-3-opus-latest",
        max_tokens: 4096,
        system: "You are a legal assistant",
        messages: [{ role: "user", content: "Explain negligence in tort law" }],
        temperature: 0.5,
      });
    });

    it("should filter out empty messages", async () => {
      const mockResponse = {
        content: [{ type: "text", text: "Response" }],
        usage: { input_tokens: 10, output_tokens: 20 },
      };

      mockAnthropicClient.messages.create.mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: "system", content: "System" },
        { role: "user", content: "" }, // Empty content
        { role: "assistant", content: null as any }, // Null content
        { role: "user", content: "Valid message" },
      ];

      await anthropic.getChatCompletion(messages, {});

      expect(mockAnthropicClient.messages.create).toHaveBeenCalledWith({
        model: "claude-3-opus-latest",
        max_tokens: 4096,
        system: "System",
        messages: [{ role: "user", content: "Valid message" }],
        temperature: 0.7,
      });
    });

    it("should handle API errors gracefully", async () => {
      mockAnthropicClient.messages.create.mockImplementation(() => {
        throw new Error("Anthropic API error");
      });
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementation(async () => {
          throw new Error("Anthropic API error");
        });

      const messages: ChatMessage[] = [
        { role: "user", content: "Test message" },
      ];

      const result = await anthropic.getChatCompletion(messages, {});

      expect(result).toEqual({
        textResponse: "Anthropic API error",
        metrics: {},
      });
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          message: "Anthropic API error",
        })
      );
    });

    it("should throw error for invalid model", async () => {
      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];
      anthropic.isValidChatCompletionModel = jest.fn().mockReturnValue(false);

      await expect(anthropic.getChatCompletion(messages, {})).rejects.toThrow(
        "Anthropic chat: claude-3-opus-latest is not valid for chat completion!"
      );
    });

    it("should throw error for null messages", async () => {
      await expect(anthropic.getChatCompletion(null, {})).rejects.toThrow(
        "Anthropic chat: claude-3-opus-latest is not valid for chat completion!"
      );
    });

    it("should handle missing usage data", async () => {
      mockAnthropicClient.messages.create.mockResolvedValue({
        content: [{ type: "text", text: "Response" }],
      });

      const messages: ChatMessage[] = [
        { role: "system", content: "System" },
        { role: "user", content: "Test" },
      ];

      const result = await anthropic.getChatCompletion(messages, {});

      expect(result?.metrics).toEqual({
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
        outputTps: 0,
        duration: 1000,
      });
    });

    it("should use default temperature", async () => {
      mockAnthropicClient.messages.create.mockResolvedValue({
        content: [{ type: "text", text: "Response" }],
      });

      const messages: ChatMessage[] = [
        { role: "system", content: "System" },
        { role: "user", content: "Test" },
      ];

      await anthropic.getChatCompletion(messages, {});

      expect(mockAnthropicClient.messages.create).toHaveBeenCalledWith({
        model: "claude-3-opus-latest",
        max_tokens: 4096,
        system: "System",
        messages: [{ role: "user", content: "Test" }],
        temperature: 0.7,
      });
    });

    it("should handle multimodal content in messages", async () => {
      const mockResponse = {
        content: [{ type: "text", text: "Image analysis complete" }],
        usage: { input_tokens: 1000, output_tokens: 50 },
      };

      mockAnthropicClient.messages.create.mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: "system", content: "Analyze images" },
        {
          role: "user",
          content: [
            { type: "text", text: "What's in this image?" },
            {
              type: "image",
              source: {
                type: "base64",
                media_type: "image/png",
                data: "base64data",
              },
            },
          ],
        },
      ];

      const result = await anthropic.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("Image analysis complete");
      expect(result?.metrics?.prompt_tokens).toBe(1000);
    });
  });

  describe("streamGetChatCompletion", () => {
    let anthropic: AnthropicLLM;

    beforeEach(() => {
      anthropic = new AnthropicLLM();
      mockAnthropicClient.messages.stream.mockClear();
    });

    it("should stream completion successfully", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield {
            type: "message_start",
            message: { usage: { input_tokens: 50 } },
          };
          yield {
            type: "content_block_delta",
            delta: { type: "text_delta", text: "Legal " },
          };
          yield {
            type: "content_block_delta",
            delta: { type: "text_delta", text: "response" },
          };
          yield {
            type: "message_delta",
            usage: { output_tokens: 20 },
          };
          yield { type: "message_stop" };
        },
      };

      mockAnthropicClient.messages.stream.mockReturnValue(mockStream);

      const messages: ChatMessage[] = [
        { role: "system", content: "Legal assistant" },
        { role: "user", content: "Explain contract formation" },
      ];

      const result = await anthropic.streamGetChatCompletion(messages, {
        temperature: 0.7,
      });

      expect(result.stream).toBeDefined();
      expect(result.endMeasurement).toBeDefined();

      expect(mockAnthropicClient.messages.stream).toHaveBeenCalledWith({
        model: "claude-3-opus-latest",
        max_tokens: 4096,
        system: "Legal assistant",
        messages: [{ role: "user", content: "Explain contract formation" }],
        temperature: 0.7,
      });
    });

    it("should throw error for invalid model", async () => {
      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];
      anthropic.isValidChatCompletionModel = jest.fn().mockReturnValue(false);

      await expect(
        anthropic.streamGetChatCompletion(messages, {})
      ).rejects.toThrow(
        "Anthropic chat: claude-3-opus-latest is not valid for chat completion!"
      );
    });

    it("should throw error for empty messages", async () => {
      await expect(anthropic.streamGetChatCompletion([], {})).rejects.toThrow(
        "Cannot process stream chat completion with empty messages."
      );
    });

    it("should filter out empty messages in stream", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { type: "message_stop" };
        },
      };

      mockAnthropicClient.messages.stream.mockReturnValue(mockStream);

      const messages: ChatMessage[] = [
        { role: "system", content: "System" },
        { role: "user", content: "" }, // Empty
        { role: "user", content: "Valid message" },
      ];

      await anthropic.streamGetChatCompletion(messages, {});

      expect(mockAnthropicClient.messages.stream).toHaveBeenCalledWith({
        model: "claude-3-opus-latest",
        max_tokens: 4096,
        system: "System",
        messages: [{ role: "user", content: "Valid message" }],
        temperature: 0.7,
      });
    });
  });

  describe("handleStream", () => {
    let anthropic: AnthropicLLM;
    let mockResponse: Partial<ExpressResponse>;

    beforeEach(() => {
      anthropic = new AnthropicLLM();
      mockResponse = {
        write: jest.fn(),
        end: jest.fn(),
        on: jest.fn(),
        removeListener: jest.fn(),
      };
    });

    it("should handle stream events successfully", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield "chunk1";
          yield "chunk2";
        },
        on: jest.fn((event, callback) => {
          if (event === "streamEvent") {
            // Simulate stream events
            setTimeout(() => {
              callback({
                type: "message_start",
                message: { usage: { input_tokens: 100 } },
              });
              callback({
                type: "content_block_delta",
                delta: { type: "text_delta", text: "Hello " },
              });
              callback({
                type: "content_block_delta",
                delta: { type: "text_delta", text: "Claude" },
              });
              callback({
                type: "message_delta",
                usage: { output_tokens: 50 },
              });
              callback({ type: "message_stop" });
            }, 0);
          }
        }),
        endMeasurement: jest.fn(),
      };

      const responseProps: StreamResponseProps = {
        uuid: "test-uuid",
        sources: [{ url: "http://example.com", title: "Example" }],
      };

      const fullText = await anthropic.handleStream(
        mockResponse as ExpressResponse,
        mockStream as any,
        responseProps
      );

      expect(fullText).toBe("Hello Claude");
      expect(mockWriteResponseChunk).toHaveBeenCalledWith(mockResponse, {
        uuid: "test-uuid",
        sources: [{ url: "http://example.com", title: "Example" }],
        type: "textResponseChunk",
        textResponse: "Hello ",
        close: false,
        error: false,
      });
    });

    it("should handle stream errors", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: jest.fn(),
        on: jest.fn((event, callback) => {
          if (event === "error") {
            callback({
              error: {
                error: {
                  type: "rate_limit_error",
                  message: "Rate limit exceeded",
                },
              },
            });
          }
        }),
        endMeasurement: jest.fn(),
      };

      const responseProps: StreamResponseProps = {
        uuid: "test-uuid",
        sources: [],
      };

      await anthropic.handleStream(
        mockResponse as ExpressResponse,
        mockStream as any,
        responseProps
      );

      expect(mockWriteResponseChunk).toHaveBeenCalledWith(mockResponse, {
        uuid: "test-uuid",
        sources: [],
        type: "abort",
        textResponse: null,
        close: true,
        error: "Anthropic Error:rate_limit_error Rate limit exceeded",
      });
    });

    it("should handle client abort", async () => {
      let streamAborted = false;
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          // Check if stream was aborted before yielding
          if (!streamAborted) {
            yield {
              type: "content_block_delta",
              delta: { type: "text_delta", text: "test" },
            };
          }
          yield { type: "message_stop" };
        },
        on: jest.fn((event, callback) => {
          if (event === "streamEvent") {
            // Simulate immediate abort
            setTimeout(() => {
              streamAborted = true;
              callback({ type: "message_stop" });
            }, 0);
          }
        }),
        endMeasurement: jest.fn(),
      };

      (mockResponse as any).on = jest.fn((event: string, handler: Function) => {
        if (event === "close") {
          // Simulate client abort immediately
          setTimeout(() => handler(), 0);
        }
      });

      const responseProps: StreamResponseProps = {
        uuid: "test-uuid",
        sources: [],
      };

      await anthropic.handleStream(
        mockResponse as ExpressResponse,
        mockStream as any,
        responseProps
      );

      expect(mockClientAbortedHandler).toHaveBeenCalled();
    }, 10000);

    it("should handle stop reasons", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: jest.fn(),
        on: jest.fn((event, callback) => {
          if (event === "streamEvent") {
            callback({
              type: "content_block_delta",
              delta: {
                type: "text_delta",
                text: "Response",
                stop_reason: "end_turn",
              },
            });
          }
        }),
        endMeasurement: jest.fn(),
      };

      const responseProps: StreamResponseProps = { uuid: "test-uuid" };

      await anthropic.handleStream(
        mockResponse as ExpressResponse,
        mockStream as any,
        responseProps
      );

      expect(mockWriteResponseChunk).toHaveBeenCalledWith(mockResponse, {
        uuid: "test-uuid",
        sources: [],
        type: "textResponseChunk",
        textResponse: "",
        close: true,
        error: false,
      });
    });
  });

  describe("embedding methods", () => {
    let anthropic: AnthropicLLM;

    beforeEach(() => {
      anthropic = new AnthropicLLM();
    });

    it("should embed text input successfully", async () => {
      const result = await anthropic.embedTextInput("Legal text");

      expect(result).toEqual([0.1, 0.2, 0.3]);
      expect(anthropic.embedder.embedTextInput).toHaveBeenCalledWith(
        "Legal text"
      );
    });

    it("should embed chunks successfully", async () => {
      const chunks = ["Chunk 1", "Chunk 2"];
      const result = await anthropic.embedChunks(chunks);

      expect(result).toEqual([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]);
      expect(anthropic.embedder.embedChunks).toHaveBeenCalledWith(chunks);
    });

    it("should handle empty chunks array", async () => {
      const result = await anthropic.embedChunks([]);

      expect(result).toEqual([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]);
    });

    it("should handle null result from embedder", async () => {
      const mockEmbedderInstance = {
        embedTextInput: jest.fn(),
        embedChunks: jest.fn().mockResolvedValue(null),
      };
      mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);

      const anthropic = new AnthropicLLM();
      const result = await anthropic.embedChunks(["Test"]);

      expect(result).toEqual([]);
    });
  });

  describe("compressMessages", () => {
    let anthropic: AnthropicLLM;

    beforeEach(() => {
      anthropic = new AnthropicLLM();
    });

    it("should compress messages successfully", async () => {
      const { messageArrayCompressor } = require("../../../helpers/chat");
      const compressedMessages = [{ role: "system", content: "Compressed" }];
      messageArrayCompressor.mockResolvedValue(compressedMessages);

      const promptArgs: PromptArgs = {
        systemPrompt: "Legal system",
        userPrompt: "Legal query",
      };
      const rawHistory: ChatMessage[] = [{ role: "user", content: "History" }];

      const result = await anthropic.compressMessages(promptArgs, rawHistory);

      expect(result).toEqual(compressedMessages);
      expect(messageArrayCompressor).toHaveBeenCalledWith(
        anthropic,
        expect.arrayContaining([
          { role: "system", content: "Legal system" },
          { role: "user", content: "Legal query" },
        ]),
        rawHistory,
        [],
        [],
        undefined
      );
    });

    it("should handle maxAllowedTokens parameter", async () => {
      const { messageArrayCompressor } = require("../../../helpers/chat");
      messageArrayCompressor.mockResolvedValue([]);

      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        userPrompt: "User",
        maxAllowedTokens: 100000,
      };

      await anthropic.compressMessages(promptArgs, []);

      expect(messageArrayCompressor).toHaveBeenCalledWith(
        anthropic,
        expect.any(Array),
        [],
        [],
        [],
        100000
      );
    });
  });

  describe("countAnthropicTokens", () => {
    let anthropic: AnthropicLLM;
    let mockTokenManager: any;

    beforeEach(() => {
      anthropic = new AnthropicLLM();
      mockTokenManager = {
        countFromString: jest.fn().mockReturnValue(100),
      };
    });

    it("should count tokens for string input", () => {
      const result = anthropic.countAnthropicTokens(
        mockTokenManager,
        "Test string"
      );

      expect(result).toEqual({ tokens: 100, strings: 1 });
      expect(mockTokenManager.countFromString).toHaveBeenCalledWith(
        "Test string"
      );
    });

    it("should count tokens for message array", () => {
      const messages: ChatMessage[] = [
        { role: "system", content: "System message" },
        { role: "user", content: "User message" },
        { role: "assistant", content: "Assistant message" },
      ];

      mockTokenManager.countFromString.mockImplementation((str: string) => {
        if (str === "System message") return 50;
        if (str === "User message") return 30;
        if (str === "Assistant message") return 40;
        return 0;
      });

      const result = anthropic.countAnthropicTokens(mockTokenManager, messages);

      // 50 (system) + 30 (user) + 40 (assistant) + 12 (overhead: 3 messages * 4)
      expect(result).toEqual({ tokens: 132, strings: 3 });
    });

    it("should handle non-string content in messages", () => {
      const messages: ChatMessage[] = [
        { role: "system", content: "System" },
        {
          role: "user",
          content: [
            { type: "text", text: "Text content" },
            {
              type: "image",
              source: { type: "base64", media_type: "image/png", data: "data" },
            },
          ],
        },
      ];

      const result = anthropic.countAnthropicTokens(mockTokenManager, messages);

      expect(result.strings).toBe(2);
    });

    it("should throw error for invalid input type", () => {
      expect(() => {
        anthropic.countAnthropicTokens(mockTokenManager, 123 as any);
      }).toThrow("Not a supported tokenized format.");
    });
  });

  describe("security testing", () => {
    let anthropic: AnthropicLLM;

    beforeEach(() => {
      anthropic = new AnthropicLLM();
    });

    it("should protect API key in initialization", () => {
      const apiKey = "sk-ant-api03-secret-key";
      process.env.ANTHROPIC_API_KEY = apiKey;

      new AnthropicLLM();

      expect(MockAnthropic).toHaveBeenCalledWith({
        apiKey: apiKey,
      });
    });

    it("should handle malicious input in prompts", async () => {
      const maliciousPrompt = "<script>alert('xss')</script>";
      const messages: ChatMessage[] = [
        { role: "system", content: "System" },
        { role: "user", content: maliciousPrompt },
      ];

      mockAnthropicClient.messages.create.mockResolvedValue({
        content: [{ type: "text", text: "Safe response" }],
      });

      const result = await anthropic.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("Safe response");
      expect(mockAnthropicClient.messages.create).toHaveBeenCalledWith({
        model: "claude-3-opus-latest",
        max_tokens: 4096,
        system: "System",
        messages: [{ role: "user", content: maliciousPrompt }],
        temperature: 0.7,
      });
    });

    it("should sanitize error messages", async () => {
      mockAnthropicClient.messages.create.mockImplementation(() => {
        throw new Error("Invalid API key: sk-ant-api03-sensitive");
      });
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementation(async () => {
          throw new Error("Invalid API key: sk-ant-api03-sensitive");
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await anthropic.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe(
        "Invalid API key: sk-ant-api03-sensitive"
      );
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          message: "Invalid API key: sk-ant-api03-sensitive",
        })
      );
    });

    it("should handle SQL injection attempts in attachments", () => {
      const sqlInjectionAttachment: Attachment = {
        name: "'; DROP TABLE users; --",
        contentString: "SELECT * FROM users WHERE id = '1' OR '1'='1'",
        type: "text/plain",
      };

      const result = anthropic["generateContent"]({
        userPrompt: "Process this query",
        attachments: [sqlInjectionAttachment],
      });

      // Should treat as regular text, not execute
      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray[1].text).toContain("SELECT * FROM users");
    });

    it("should validate base64 image data", () => {
      const invalidBase64: Attachment = {
        name: "image.png",
        mime: "image/png",
        contentString: "data:image/png;base64,<script>alert('xss')</script>",
      };

      const result = anthropic["generateContent"]({
        userPrompt: "Analyze image",
        attachments: [invalidBase64],
      });

      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray[1].source.data).toBe("<script>alert('xss')</script>");
    });
  });

  describe("performance testing", () => {
    let anthropic: AnthropicLLM;

    beforeEach(() => {
      anthropic = new AnthropicLLM();
    });

    it("should handle large context windows", async () => {
      const largeContext = Array(100)
        .fill("Large legal document section")
        .join(" ");
      const messages: ChatMessage[] = Array(50)
        .fill(null)
        .map((_, i) => ({
          role: i % 2 === 0 ? "user" : "assistant",
          content: largeContext,
        }));

      mockAnthropicClient.messages.create.mockResolvedValue({
        content: [{ type: "text", text: "Handled large context" }],
      });

      const result = await anthropic.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("Handled large context");
    });

    it("should handle concurrent requests", async () => {
      mockAnthropicClient.messages.create.mockResolvedValue({
        content: [{ type: "text", text: "Concurrent response" }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const promises = Array(10)
        .fill(null)
        .map(() => anthropic.getChatCompletion(messages, {}));

      const results = await Promise.all(promises);

      expect(results).toHaveLength(10);
      results.forEach((result) => {
        expect(result?.textResponse).toBe("Concurrent response");
      });
    });

    it("should measure performance accurately", async () => {
      const mockResponse = {
        content: [{ type: "text", text: "Timed response" }],
        usage: {
          input_tokens: 200,
          output_tokens: 150,
        },
      };

      mockAnthropicClient.messages.create.mockResolvedValue(mockResponse);
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementation(async (promise) => {
          const output = await promise;
          return { output, duration: 2500 };
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await anthropic.getChatCompletion(messages, {});

      expect(result?.metrics).toEqual({
        prompt_tokens: 200,
        completion_tokens: 150,
        total_tokens: 350,
        outputTps: 150 / 2500, // 150 tokens / 2500 ms
        duration: 2500,
      });
    });

    it("should handle memory-intensive operations", () => {
      const largeAttachments: Attachment[] = Array(50)
        .fill(null)
        .map((_, i) => ({
          name: `large-file-${i}.txt`,
          contentString: "x".repeat(100000), // 100KB each
          type: "text/plain",
        }));

      const result = anthropic["generateContent"]({
        userPrompt: "Process large files",
        attachments: largeAttachments,
      });

      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray).toHaveLength(51); // 1 prompt + 50 attachments
    });
  });

  describe("edge cases", () => {
    let anthropic: AnthropicLLM;

    beforeEach(() => {
      anthropic = new AnthropicLLM();
    });

    it("should handle rate limiting", async () => {
      mockAnthropicClient.messages.create.mockImplementation(() => {
        const error = new Error("Rate limit exceeded");
        (error as any).status = 429;
        (error as any).headers = {
          "retry-after": "60",
        };
        throw error;
      });
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementation(async () => {
          const error = new Error("Rate limit exceeded");
          (error as any).status = 429;
          (error as any).headers = {
            "retry-after": "60",
          };
          throw error;
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await anthropic.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("Rate limit exceeded");
    });

    it("should handle network timeouts", async () => {
      mockAnthropicClient.messages.create.mockImplementation(() => {
        const error = new Error("Request timeout");
        (error as any).code = "ETIMEDOUT";
        throw error;
      });
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementation(async () => {
          const error = new Error("Request timeout");
          (error as any).code = "ETIMEDOUT";
          throw error;
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await anthropic.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("Request timeout");
    });

    it("should handle invalid API responses", async () => {
      mockAnthropicClient.messages.create.mockResolvedValue({
        invalid: "response",
        content: undefined,
      });

      const messages: ChatMessage[] = [
        { role: "system", content: "System" },
        { role: "user", content: "Test" },
      ];

      const result = await anthropic.getChatCompletion(messages, {});

      expect(result?.textResponse).toContain("Cannot read properties");
    });

    it("should handle null message content", async () => {
      mockAnthropicClient.messages.create.mockResolvedValue({
        content: [{ type: "text", text: null }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await anthropic.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("");
    });

    it("should handle environment variable edge cases", () => {
      process.env.ANTHROPIC_API_KEY = "";

      expect(() => new AnthropicLLM()).toThrow("No Anthropic API key was set.");

      delete process.env.ANTHROPIC_API_KEY;
      expect(() => new AnthropicLLM()).toThrow("No Anthropic API key was set.");
    });

    it("should handle missing MODEL_MAP entries gracefully", () => {
      const unknownModel = new AnthropicLLM(null, "claude-unknown-model");
      expect(unknownModel.promptWindowLimit()).toBe(200000); // Should use default
    });

    it("should handle invalid settings suffix", () => {
      process.env.ANTHROPIC_API_KEY_INVALID = undefined as any;

      // Should throw because no valid API key found
      expect(() => new AnthropicLLM(null, null, "_INVALID")).toThrow(
        "No Anthropic API key was set."
      );
    });

    it("should handle mixed content types in responses", async () => {
      mockAnthropicClient.messages.create.mockResolvedValue({
        content: [
          { type: "text", text: "First part" },
          { type: "text", text: " Second part" },
        ],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await anthropic.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("First part");
    });

    it("should handle stream with no events", async () => {
      const emptyStream = {
        [Symbol.asyncIterator]: async function* () {
          // Empty stream
        },
        on: jest.fn(),
      };

      mockAnthropicClient.messages.stream.mockReturnValue(emptyStream);

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await anthropic.streamGetChatCompletion(messages, {});

      expect(result.stream).toBeDefined();
    });

    it("should handle different temperature values", async () => {
      mockAnthropicClient.messages.create.mockResolvedValue({
        content: [{ type: "text", text: "Response" }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      // Test with temperature 0
      await anthropic.getChatCompletion(messages, { temperature: 0 });
      expect(mockAnthropicClient.messages.create).toHaveBeenCalledWith(
        expect.objectContaining({ temperature: 0 })
      );

      // Test with temperature 1
      await anthropic.getChatCompletion(messages, { temperature: 1 });
      expect(mockAnthropicClient.messages.create).toHaveBeenCalledWith(
        expect.objectContaining({ temperature: 1 })
      );

      // Test with undefined temperature (should use default)
      await anthropic.getChatCompletion(messages, {});
      expect(mockAnthropicClient.messages.create).toHaveBeenCalledWith(
        expect.objectContaining({ temperature: 0.7 })
      );
    });

    it("should handle unicode and special characters", async () => {
      const unicodeMessages: ChatMessage[] = [
        {
          role: "user",
          content: "Test with emojis 🚀💻🔒 and special chars: ñáéíóú",
        },
        {
          role: "assistant",
          content: "Unicode response: 你好世界 مرحبا بالعالم",
        },
      ];

      mockAnthropicClient.messages.create.mockResolvedValue({
        content: [{ type: "text", text: "Handled unicode ✓" }],
      });

      const result = await anthropic.getChatCompletion(unicodeMessages, {});

      expect(result?.textResponse).toBe("Handled unicode ✓");
    });

    it("should handle extremely long inputs", async () => {
      const veryLongPrompt = "x".repeat(1000000); // 1MB of text
      const messages: ChatMessage[] = [
        { role: "user", content: veryLongPrompt },
      ];

      mockAnthropicClient.messages.create.mockResolvedValue({
        content: [{ type: "text", text: "Handled long input" }],
      });

      const result = await anthropic.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("Handled long input");
    });

    it("should handle Anthropic-specific error formats", () => {
      const mockStream = {
        [Symbol.asyncIterator]: jest.fn(),
        on: jest.fn((event, callback) => {
          if (event === "error") {
            // Test various error formats
            callback(new Error("Simple error"));
            callback({ message: "Object error" });
            callback({
              error: { error: { type: "api_error", message: "API Error" } },
            });
          }
        }),
        endMeasurement: jest.fn(),
      };

      const responseProps: StreamResponseProps = { uuid: "test-uuid" };

      anthropic.handleStream(
        { on: jest.fn(), removeListener: jest.fn() } as any,
        mockStream as any,
        responseProps
      );

      expect(mockWriteResponseChunk).toHaveBeenCalledWith(
        expect.any(Object),
        expect.objectContaining({
          type: "abort",
          error: expect.any(String),
        })
      );
    });

    it("should handle attachment edge cases", () => {
      const edgeCaseAttachments: Attachment[] = [
        {
          name: "",
          contentString: "Empty name",
        },
        {
          name: "no-extension",
          contentString: "No file extension",
        },
        {
          name: "huge.txt",
          contentString: "x".repeat(10 * 1024 * 1024), // 10MB
          size: 10 * 1024 * 1024,
        },
      ];

      const result = anthropic["generateContent"]({
        userPrompt: "Process edge cases",
        attachments: edgeCaseAttachments,
      });

      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray).toHaveLength(4); // 1 prompt + 3 attachments
    });

    it("should handle prompt with only attachments and no text", () => {
      const attachments: Attachment[] = [
        {
          name: "image.png",
          mime: "image/png",
          contentString: "data:image/png;base64,test",
        },
      ];

      const result = anthropic["generateContent"]({
        userPrompt: "",
        attachments,
      });

      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray[0].text).toBe("");
    });

    it("should handle circular references in error objects", async () => {
      mockAnthropicClient.messages.create.mockImplementation(() => {
        const error = new Error("Circular error");
        (error as any).inner = error; // Create circular reference
        throw error;
      });
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementation(async () => {
          const error = new Error("Circular error");
          (error as any).inner = error; // Create circular reference
          throw error;
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await anthropic.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("Circular error");
    });

    it("should handle streaming with backpressure", async () => {
      let yieldCount = 0;
      const backpressureStream = {
        [Symbol.asyncIterator]: async function* () {
          while (yieldCount < 1000) {
            yieldCount++;
            yield {
              type: "content_block_delta",
              delta: { type: "text_delta", text: `chunk${yieldCount}` },
            };
            // Simulate backpressure with small delay
            if (yieldCount % 100 === 0) {
              await new Promise((resolve) => setTimeout(resolve, 1));
            }
          }
          yield { type: "message_stop" };
        },
        on: jest.fn(),
      };

      mockAnthropicClient.messages.stream.mockReturnValue(backpressureStream);

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await anthropic.streamGetChatCompletion(messages, {});

      expect(result).toBeDefined();
      expect(result.stream).toBeDefined();
    });

    it("should handle content array edge cases", async () => {
      const messages: ChatMessage[] = [
        { role: "system", content: "System" },
        {
          role: "user",
          content: [], // Empty array
        },
        {
          role: "assistant",
          content: [
            { type: "text", text: "" }, // Empty text
          ],
        },
        {
          role: "user",
          content: "Valid message",
        },
      ];

      mockAnthropicClient.messages.create.mockResolvedValue({
        content: [{ type: "text", text: "Response" }],
      });

      await anthropic.getChatCompletion(messages, {});

      // Should filter out empty content messages, but keep non-empty arrays even with empty text
      expect(mockAnthropicClient.messages.create).toHaveBeenCalledWith({
        model: "claude-3-opus-latest",
        max_tokens: 4096,
        system: "System",
        messages: [
          {
            role: "assistant",
            content: [{ type: "text", text: "" }],
          },
          { role: "user", content: "Valid message" },
        ],
        temperature: 0.7,
      });
    });
  });

  describe("Anthropic-specific features", () => {
    let anthropic: AnthropicLLM;

    beforeEach(() => {
      anthropic = new AnthropicLLM();
    });

    it("should handle Claude-specific model features", () => {
      // Test different Claude models
      const models = [
        "claude-3-opus-latest",
        "claude-3-sonnet-20240229",
        "claude-3-haiku-20240307",
        "claude-3-5-sonnet-20240620",
        "claude-3-5-haiku-latest",
        "claude-opus-4-0",
      ];

      models.forEach((model) => {
        const instance = new AnthropicLLM(null, model);
        expect(instance.isValidChatCompletionModel(model)).toBe(true);
        expect(instance.promptWindowLimit()).toBeGreaterThan(0);
      });
    });

    it("should handle Anthropic's specific content format", async () => {
      const mockResponse = {
        content: [
          { type: "text", text: "Part 1" },
          { type: "text", text: " Part 2" },
          { type: "text", text: " Part 3" },
        ],
        usage: { input_tokens: 100, output_tokens: 50 },
      };

      mockAnthropicClient.messages.create.mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: "user", content: "Test multi-part response" },
      ];

      const result = await anthropic.getChatCompletion(messages, {});

      // Should only return the first text block
      expect(result?.textResponse).toBe("Part 1");
    });

    it("should handle system message correctly", async () => {
      const messages: ChatMessage[] = [
        { role: "system", content: "You are Claude, a helpful AI assistant." },
        { role: "user", content: "Hello" },
        { role: "assistant", content: "Hi there!" },
        { role: "user", content: "How are you?" },
      ];

      mockAnthropicClient.messages.create.mockResolvedValue({
        content: [{ type: "text", text: "I'm doing well!" }],
      });

      await anthropic.getChatCompletion(messages, {});

      // System message should be passed separately
      expect(mockAnthropicClient.messages.create).toHaveBeenCalledWith({
        model: "claude-3-opus-latest",
        max_tokens: 4096,
        system: "You are Claude, a helpful AI assistant.",
        messages: [
          { role: "user", content: "Hello" },
          { role: "assistant", content: "Hi there!" },
          { role: "user", content: "How are you?" },
        ],
        temperature: 0.7,
      });
    });

    it("should handle Anthropic's specific stream event types", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: jest.fn(),
        on: jest.fn((event, callback) => {
          if (event === "streamEvent") {
            // Test all Anthropic event types
            callback({
              type: "message_start",
              message: { usage: { input_tokens: 100 } },
            });
            callback({
              type: "content_block_start",
              content_block: { type: "text", text: "" },
            });
            callback({ type: "ping" });
            callback({
              type: "content_block_delta",
              delta: { type: "text_delta", text: "Hello" },
            });
            callback({ type: "content_block_stop" });
            callback({ type: "message_delta", usage: { output_tokens: 10 } });
            callback({ type: "message_stop" });
          }
        }),
        endMeasurement: jest.fn(),
      };

      const responseProps: StreamResponseProps = { uuid: "test-uuid" };

      const result = await anthropic.handleStream(
        { on: jest.fn(), removeListener: jest.fn() } as any,
        mockStream as any,
        responseProps
      );

      expect(result).toBe("Hello");
    });
  });

  describe("Token counting with custom implementation", () => {
    let anthropic: AnthropicLLM;

    beforeEach(() => {
      anthropic = new AnthropicLLM();
    });

    it("should use custom token counting in compressMessages", async () => {
      const { messageArrayCompressor } = require("../../../helpers/chat");

      // Mock to capture the tokenManager passed to messageArrayCompressor
      let capturedTokenManager: any;
      messageArrayCompressor.mockImplementation(
        async (
          provider: any,
          messages: any,
          rawHistory: any,
          attachments: any,
          _: any,
          maxTokens: any,
          tokenManager: any
        ) => {
          capturedTokenManager = tokenManager;
          return messages;
        }
      );

      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        userPrompt: "User",
      };

      await anthropic.compressMessages(promptArgs, []);

      // Verify statsFrom was overridden
      expect(capturedTokenManager).toBeUndefined(); // Because we're not passing it in the current implementation
    });

    it("should handle token counting for complex message structures", () => {
      const mockTokenManager = {
        countFromString: jest
          .fn()
          .mockReturnValueOnce(100) // system
          .mockReturnValueOnce(50) // user
          .mockReturnValueOnce(75), // assistant
        tokensFromString: jest.fn(),
        bytesFromTokens: jest.fn(),
        statsFrom: jest.fn(),
        truncateToTokenLength: jest.fn(),
      } as any;

      const messages: ChatMessage[] = [
        { role: "system", content: "Complex system prompt with legal context" },
        {
          role: "user",
          content: [
            { type: "text", text: "User text" },
            {
              type: "image",
              source: { type: "base64", media_type: "image/png", data: "data" },
            },
          ],
        },
        { role: "assistant", content: "Assistant response" },
      ];

      const result = anthropic.countAnthropicTokens(mockTokenManager, messages);

      // System: 100, User: 50, Assistant: 75, Overhead: 12 (3 messages * 4)
      expect(result).toEqual({ tokens: 237, strings: 3 });
    });
  });

  describe("Additional API error handling", () => {
    let anthropic: AnthropicLLM;

    beforeEach(() => {
      anthropic = new AnthropicLLM();
    });

    it("should handle Anthropic-specific API errors", async () => {
      mockAnthropicClient.messages.create.mockImplementation(() => {
        const error = new Error(
          "Messages must alternate between user and assistant roles"
        );
        (error as any).status = 400;
        (error as any).error = {
          type: "invalid_request_error",
          message: "Messages must alternate between user and assistant roles",
        };
        throw error;
      });
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementation(async () => {
          const error = new Error(
            "Messages must alternate between user and assistant roles"
          );
          (error as any).status = 400;
          (error as any).error = {
            type: "invalid_request_error",
            message: "Messages must alternate between user and assistant roles",
          };
          throw error;
        });

      const messages: ChatMessage[] = [
        { role: "user", content: "First" },
        { role: "user", content: "Second" }, // Invalid: two user messages in a row
      ];

      const result = await anthropic.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe(
        "Messages must alternate between user and assistant roles"
      );
    });

    it("should handle content policy violations", async () => {
      mockAnthropicClient.messages.create.mockImplementation(() => {
        const error = new Error("Content filtered due to policy violation");
        (error as any).status = 400;
        (error as any).error = {
          type: "invalid_request_error",
          message: "Content filtered due to policy violation",
        };
        throw error;
      });
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementation(async () => {
          const error = new Error("Content filtered due to policy violation");
          (error as any).status = 400;
          (error as any).error = {
            type: "invalid_request_error",
            message: "Content filtered due to policy violation",
          };
          throw error;
        });

      const messages: ChatMessage[] = [
        { role: "user", content: "Inappropriate content" },
      ];

      const result = await anthropic.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe(
        "Content filtered due to policy violation"
      );
    });

    it("should handle server errors", async () => {
      mockAnthropicClient.messages.create.mockImplementation(() => {
        const error = new Error("An internal error occurred");
        (error as any).status = 500;
        (error as any).error = {
          type: "internal_server_error",
          message: "An internal error occurred",
        };
        throw error;
      });
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementation(async () => {
          const error = new Error("An internal error occurred");
          (error as any).status = 500;
          (error as any).error = {
            type: "internal_server_error",
            message: "An internal error occurred",
          };
          throw error;
        });

      const messages: ChatMessage[] = [
        { role: "user", content: "Test message" },
      ];

      const result = await anthropic.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("An internal error occurred");
    });
  });
});
