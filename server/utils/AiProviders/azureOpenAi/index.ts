import { v4 as uuidv4 } from "uuid";
import { NativeEmbedder } from "../../EmbeddingEngines/native";
import { LLMPerformanceMonitor } from "../../helpers/chat/LLMPerformanceMonitor";
import {
  writeResponseChunk,
  clientAbortedHandler,
} from "../../helpers/chat/responses";
import { formatContextTexts } from "../../helpers";
import {
  LLMProvider,
  ChatMessage,
  PromptArgs,
  CompletionOptions,
  CompletionResponse,
  StreamResponseProps,
  EmbeddingEngine,
  TokenLimits,
  Attachment,
  TextContent,
  ImageContent,
  LLMStreamResponse,
} from "../../../types/ai-providers";
import {
  createStreamResponse,
  createMonitoredStream,
} from "../streamConverters";
import { MonitoredStream } from "../../../types/ai-provider-streams";
import type { OpenAIClient, ChatCompletions } from "@azure/openai";
import type { Response as ExpressResponse } from "express";

export class AzureOpenAiLLM implements LLMProvider {
  model: string;
  limits: TokenLimits;
  embedder: EmbeddingEngine;
  defaultTemp: number;
  private openai: OpenAIClient;
  private settings_suffix: string | null;

  constructor(
    embedder: EmbeddingEngine | null = null,
    _modelPreference: string | null = null,
    settings_suffix: string | null = null
  ) {
    this.settings_suffix = settings_suffix;
    const { OpenAIClient, AzureKeyCredential } = require("@azure/openai");
    const endpointKey = `AZURE_OPENAI_ENDPOINT${this.settings_suffix || ""}`;
    const apiKeyKey = `AZURE_OPENAI_KEY${this.settings_suffix || ""}`;

    if (!process.env[endpointKey])
      throw new Error("Azure OpenAI endpoint not configured");
    if (!process.env[apiKeyKey])
      throw new Error("Azure OpenAI API key not configured");

    this.openai = new OpenAIClient(
      process.env[endpointKey]!,
      new AzureKeyCredential(process.env[apiKeyKey]!)
    );
    this.model =
      process.env[`OPEN_AI_MODEL_PREF${this.settings_suffix || ""}`] || "";
    this.limits = {
      history: this.promptWindowLimit() * 0.15,
      system: this.promptWindowLimit() * 0.15,
      user: this.promptWindowLimit() * 0.7,
    };

    this.embedder = embedder ?? new NativeEmbedder();
    this.defaultTemp = 0.7;
  }

  async #appendContext(contextTexts: string[] = []): Promise<string> {
    if (!contextTexts || !contextTexts.length) return "";
    return await formatContextTexts(contextTexts);
  }

  streamingEnabled(): boolean {
    return "streamGetChatCompletion" in this;
  }

  static promptWindowLimit(_modelName?: string): number {
    return process.env[`AZURE_OPENAI_TOKEN_LIMIT`]
      ? Number(process.env[`AZURE_OPENAI_TOKEN_LIMIT`])
      : 4096;
  }

  // Sure the user selected a proper value for the token limit
  // could be any of these https://learn.microsoft.com/en-us/azure/ai-services/openai/concepts/models#gpt-4-models
  // and if undefined - assume it is the lowest end.
  promptWindowLimit(): number {
    return process.env[`AZURE_OPENAI_TOKEN_LIMIT${this.settings_suffix || ""}`]
      ? Number(
          process.env[`AZURE_OPENAI_TOKEN_LIMIT${this.settings_suffix || ""}`]
        )
      : 4096;
  }

  customPromptWindowLimit(): number {
    return this.promptWindowLimit();
  }

  isValidChatCompletionModel(_modelName: string = ""): boolean {
    // The Azure user names their "models" as deployments and they can be any name
    // so we rely on the user to put in the correct deployment as only they would
    // know it.
    return true;
  }

  /**
   * Generates appropriate content array for a message + attachments.
   */
  #generateContent({
    userPrompt,
    attachments = [],
  }: {
    userPrompt: string;
    attachments?: Attachment[];
  }): string | Array<TextContent | ImageContent> {
    if (!attachments.length) {
      return userPrompt;
    }
    const content: Array<TextContent | ImageContent> = [
      { type: "text", text: userPrompt },
    ];
    for (const attachment of attachments) {
      content.push({
        type: "image_url",
        image_url: {
          url: attachment.contentString,
        },
      });
    }
    return content;
  }

  async constructPrompt({
    systemPrompt = "",
    contextTexts = [],
    chatHistory = [],
    userPrompt = "",
    attachments = [], // This is the specific attachment for only this prompt
  }: PromptArgs): Promise<ChatMessage[]> {
    const formattedContext = await this.#appendContext(contextTexts);
    const prompt: ChatMessage = {
      role: "system",
      content: `${systemPrompt}${formattedContext}`,
    };
    const userMessage: ChatMessage = {
      role: "user",
      content: this.#generateContent({
        userPrompt: userPrompt || "",
        attachments,
      }),
    };

    return [prompt, ...chatHistory, userMessage];
  }

  async getChatCompletion(
    messages: ChatMessage[] = [],
    { temperature = 0.7 }: CompletionOptions
  ): Promise<CompletionResponse | null> {
    if (!this.model) throw new Error("Azure OpenAI model not configured");

    // Convert our ChatMessage format to Azure's expected format
    const azureMessages = messages.map((msg) => ({
      role: msg.role,
      content:
        typeof msg.content === "string"
          ? msg.content
          : JSON.stringify(msg.content),
    }));

    const result = await LLMPerformanceMonitor.measureAsyncFunction(
      this.openai.getChatCompletions(this.model, azureMessages, {
        temperature,
      })
    );

    const output = result.output as ChatCompletions;
    if (
      !Object.prototype.hasOwnProperty.call(output, "choices") ||
      output.choices.length === 0
    )
      return null;
    return {
      textResponse: output.choices[0].message?.content || "",
      metrics: {
        prompt_tokens: output?.usage?.promptTokens || 0,
        completion_tokens: output?.usage?.completionTokens || 0,
        total_tokens: output?.usage?.totalTokens || 0,
        outputTps: (output?.usage?.completionTokens || 0) / result.duration,
        duration: result.duration,
      },
    };
  }

  async streamGetChatCompletion(
    messages: ChatMessage[],
    { temperature = 0.7 }: CompletionOptions
  ): Promise<LLMStreamResponse> {
    if (!this.model)
      throw new Error("Azure OpenAI model preference not configured");

    // Convert our ChatMessage format to Azure's expected format
    const azureMessages = messages.map((msg) => ({
      role: msg.role,
      content:
        typeof msg.content === "string"
          ? msg.content
          : JSON.stringify(msg.content),
    }));

    const stream = await this.openai.streamChatCompletions(
      this.model,
      azureMessages,
      {
        temperature,
        n: 1,
      }
    );

    const monitoredStream = await createMonitoredStream(
      stream,
      messages.map((msg) => ({
        content: typeof msg.content === "string" ? msg.content : "",
      }))
    );

    return createStreamResponse(monitoredStream);
  }

  /**
   * Handles the stream response from the AzureOpenAI API.
   * Azure does not return the usage metrics in the stream response, but 1msg = 1token
   * so we can estimate the completion tokens by counting the number of messages.
   */
  handleStream(
    response: ExpressResponse,
    stream: AsyncIterable<unknown>,
    responseProps: StreamResponseProps
  ): Promise<string> {
    const { uuid = uuidv4(), sources = [] } = responseProps;

    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve) => {
      let fullText = "";
      const usage = {
        completion_tokens: 0,
      };

      // Establish listener to early-abort a streaming response
      // in case things go sideways or the user does not like the response.
      // We preserve the generated text but continue as if chat was completed
      // to preserve previously generated content.
      const handleAbort = () => {
        if ("endMeasurement" in stream) {
          (stream as MonitoredStream).endMeasurement(usage);
        }
        clientAbortedHandler(resolve, fullText);
      };
      response.on("close", handleAbort);

      for await (const event of stream) {
        const chatCompletion = event as ChatCompletions;
        for (const choice of chatCompletion.choices) {
          const delta = choice?.delta?.content;
          if (!delta) continue;
          fullText += delta;
          usage.completion_tokens++;
          writeResponseChunk(response, {
            uuid,
            sources: [],
            type: "textResponseChunk",
            textResponse: delta,
            close: false,
            error: false,
          });
        }
      }

      writeResponseChunk(response, {
        uuid,
        sources,
        type: "textResponseChunk",
        textResponse: "",
        close: true,
        error: false,
      });
      response.removeListener("close", handleAbort);
      if ("endMeasurement" in stream) {
        (stream as MonitoredStream).endMeasurement(usage);
      }
      resolve(fullText);
    });
  }

  // Simple wrapper for dynamic embedder & normalize interface for all LLM implementations
  async embedTextInput(textInput: string): Promise<number[]> {
    return await this.embedder.embedTextInput(textInput);
  }

  async embedChunks(textChunks: string[] = []): Promise<number[][]> {
    return (await this.embedder.embedChunks(textChunks)) || [];
  }

  async compressMessages(
    promptArgs: PromptArgs = {},
    rawHistory: ChatMessage[] = []
  ): Promise<ChatMessage[]> {
    const { messageArrayCompressor } = require("../../helpers/chat");
    const messageArray = await this.constructPrompt(promptArgs);
    return await messageArrayCompressor(this, messageArray, rawHistory);
  }
}

export default AzureOpenAiLLM;
