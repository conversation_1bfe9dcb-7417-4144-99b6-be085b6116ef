import { GeminiLLM } from "../index";
import { NativeEmbedder } from "../../../EmbeddingEngines/native";
import { LLMPerformanceMonitor } from "../../../helpers/chat/LLMPerformanceMonitor";
import {
  writeResponseChunk,
  clientAbortedHandler,
} from "../../../helpers/chat/responses";
import { formatContextTexts } from "../../../helpers";
import type { Response as ExpressResponse } from "express";
import type {
  ChatMessage,
  PromptArgs,
  EmbeddingEngine,
  Attachment,
  StreamResponseProps,
} from "../../../../types/ai-providers";

// Mock dependencies
jest.mock("@google/genai");
jest.mock("../../../EmbeddingEngines/native");
jest.mock("../../../helpers/chat/LLMPerformanceMonitor");
jest.mock("../../../helpers/chat/responses");
jest.mock("../../../helpers", () => ({
  formatContextTexts: jest.fn().mockImplementation(async (texts: string[]) => {
    return texts.join("\n\n---CONTEXT---\n\n");
  }),
}));
jest.mock("../../../helpers/chat", () => ({
  messageArrayCompressor: jest
    .fn()
    .mockImplementation(async (provider, messages, _rawHistory) => {
      return messages;
    }),
}));
jest.mock("../../streamConverters", () => ({
  createMonitoredStream: jest.fn().mockImplementation(async (stream) => {
    return {
      ...stream,
      start: Date.now(),
      duration: 0,
      metrics: {},
      endMeasurement: jest.fn().mockReturnValue({
        prompt_tokens: 100,
        completion_tokens: 50,
        total_tokens: 150,
        outputTps: 50,
        duration: 1000,
      }),
    };
  }),
  createStreamResponse: jest.fn().mockImplementation((stream) => ({
    stream,
    endMeasurement: stream.endMeasurement,
  })),
  ensureAsyncIterable: jest.fn().mockImplementation((stream) => stream),
}));

// Mock the GoogleGenAI import correctly
jest.doMock("@google/genai", () => ({
  GoogleGenAI: jest.fn().mockImplementation(() => ({
    getGenerativeModel: jest.fn().mockReturnValue({
      generateContent: jest.fn(),
      generateContentStream: jest.fn(),
      startChat: jest.fn(),
      countTokens: jest.fn(),
    }),
    chats: {
      create: jest.fn().mockReturnValue({
        sendMessage: jest.fn(),
        sendMessageStream: jest.fn(),
      }),
    },
  })),
  HarmCategory: {
    HARM_CATEGORY_HATE_SPEECH: "HARM_CATEGORY_HATE_SPEECH",
    HARM_CATEGORY_DANGEROUS_CONTENT: "HARM_CATEGORY_DANGEROUS_CONTENT",
    HARM_CATEGORY_HARASSMENT: "HARM_CATEGORY_HARASSMENT",
    HARM_CATEGORY_SEXUALLY_EXPLICIT: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
  },
  HarmBlockThreshold: {
    BLOCK_NONE: "BLOCK_NONE",
    BLOCK_ONLY_HIGH: "BLOCK_ONLY_HIGH",
    BLOCK_MEDIUM_AND_ABOVE: "BLOCK_MEDIUM_AND_ABOVE",
    BLOCK_LOW_AND_ABOVE: "BLOCK_LOW_AND_ABOVE",
  },
}));

describe("GeminiLLM", () => {
  const mockNativeEmbedder = NativeEmbedder as jest.MockedClass<
    typeof NativeEmbedder
  >;
  const mockLLMPerformanceMonitor = LLMPerformanceMonitor as jest.MockedClass<
    typeof LLMPerformanceMonitor
  >;
  const mockWriteResponseChunk = writeResponseChunk as jest.MockedFunction<
    typeof writeResponseChunk
  >;
  const _mockClientAbortedHandler = clientAbortedHandler as jest.MockedFunction<
    typeof clientAbortedHandler
  >;
  const _mockFormatContextTexts = formatContextTexts as jest.MockedFunction<
    typeof formatContextTexts
  >;

  let originalEnv: typeof process.env;
  let consoleLogSpy: jest.SpyInstance;
  let consoleErrorSpy: jest.SpyInstance;

  beforeEach(() => {
    // Store original environment
    originalEnv = { ...process.env };

    // Set up required environment variables
    process.env.GEMINI_API_KEY = "test-gemini-api-key";
    process.env.GEMINI_LLM_MODEL_PREF = "gemini-2.5-flash";

    // Clear all mocks
    jest.clearAllMocks();

    // Spy on console methods
    consoleLogSpy = jest.spyOn(console, "log").mockImplementation();
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();

    // Mock NativeEmbedder
    mockNativeEmbedder.mockClear();
    const mockEmbedderInstance = {
      embedTextInput: jest.fn().mockResolvedValue([0.1, 0.2, 0.3]),
      embedChunks: jest.fn().mockResolvedValue([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]),
    };
    mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);

    // Mock LLM Performance Monitor
    mockLLMPerformanceMonitor.measureAsyncFunction = jest
      .fn()
      .mockImplementation(async (promise) => {
        const output = await promise;
        return { output, duration: 1000 };
      });
    mockLLMPerformanceMonitor.countTokens = jest
      .fn()
      .mockImplementation((messages) => {
        // Simple token counting mock
        if (Array.isArray(messages)) {
          return messages.length * 10; // Arbitrary token count
        }
        return 10;
      });
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
    consoleLogSpy.mockRestore();
    consoleErrorSpy.mockRestore();
  });

  describe("constructor and initialization", () => {
    it("should initialize with default settings", () => {
      const gemini = new GeminiLLM();

      expect(gemini.model).toBe("gemini-2.5-flash");
      expect(gemini.limits).toEqual({
        history: 1048576 * 0.15,
        system: 1048576 * 0.15,
        user: 1048576 * 0.7,
      });
      expect(gemini.defaultTemp).toBe(0.7);
      expect(gemini.embedder).toBeDefined();
    });

    it("should throw error when API key is missing", () => {
      delete process.env.GEMINI_API_KEY;
      delete process.env.GEMINI_API_KEY_DD;

      expect(() => new GeminiLLM()).toThrow("No Gemini API key was set.");
    });

    it("should use custom embedder when provided", () => {
      const customEmbedder = {} as EmbeddingEngine;
      const gemini = new GeminiLLM(customEmbedder);

      expect(gemini.embedder).toBe(customEmbedder);
    });

    it("should use model preference when provided", () => {
      const gemini = new GeminiLLM(null, "gemini-1.5-flash");

      expect(gemini.model).toBe("gemini-1.5-flash");
    });

    it("should use settings suffix when provided", () => {
      process.env.GEMINI_API_KEY_CUSTOM = "custom-api-key";
      process.env.GEMINI_LLM_MODEL_PREF_CUSTOM = "gemini-2.0-flash";

      const gemini = new GeminiLLM(null, null, "_CUSTOM");

      expect(gemini.model).toBe("gemini-2.0-flash");
    });

    it("should throw error when both API keys are missing", () => {
      delete process.env.GEMINI_API_KEY;
      delete process.env.GEMINI_API_KEY_DD;

      expect(() => new GeminiLLM()).toThrow("No Gemini API key was set.");
    });
  });

  describe("streamingEnabled", () => {
    it("should return true since streamGetChatCompletion is defined", () => {
      const gemini = new GeminiLLM();
      expect(gemini.streamingEnabled()).toBe(true);
    });
  });

  describe("promptWindowLimit methods", () => {
    it("should return static prompt window limit for known models", () => {
      expect(GeminiLLM.promptWindowLimit("gemini-2.5-flash")).toBe(1048576);
      expect(GeminiLLM.promptWindowLimit("gemini-pro")).toBe(1048576);
      expect(GeminiLLM.promptWindowLimit("gemini-2.0-flash")).toBe(1048576);
    });

    it("should return default for unknown model", () => {
      expect(GeminiLLM.promptWindowLimit("unknown-model")).toBe(1048576);
    });

    it("should return instance prompt window limit", () => {
      const gemini = new GeminiLLM();
      expect(gemini.promptWindowLimit()).toBe(1048576);
    });
  });

  describe("isValidChatCompletionModel", () => {
    it("should return true for all models", async () => {
      const gemini = new GeminiLLM();

      expect(await gemini.isValidChatCompletionModel("gemini-2.5-flash")).toBe(
        true
      );
      expect(await gemini.isValidChatCompletionModel("gemini-pro")).toBe(true);
      expect(await gemini.isValidChatCompletionModel("any-model")).toBe(true);
    });
  });

  describe("getChatCompletion", () => {
    let _gemini: GeminiLLM;

    beforeEach(() => {
      _gemini = new GeminiLLM();
    });

    afterEach(() => {
      // Only clear mock calls, don't reset implementations
      jest.clearAllMocks();
    });

    it("should return completion response successfully", async () => {
      const { GoogleGenAI } = require("@google/genai");
      const mockChat = {
        sendMessage: jest.fn().mockResolvedValue({
          text: "Response from Gemini",
        }),
      };
      const mockModel = {
        startChat: jest.fn().mockReturnValue(mockChat),
        generateContent: jest.fn(),
        generateContentStream: jest.fn(),
        countTokens: jest.fn(),
      };
      const mockAI = {
        getGenerativeModel: jest.fn().mockReturnValue(mockModel),
        chats: {
          create: jest.fn().mockReturnValue(mockChat),
        },
      };
      GoogleGenAI.mockImplementation(() => mockAI);

      const messages: ChatMessage[] = [
        { role: "system", content: "You are a helpful assistant" },
        { role: "user", content: "Tell me about Gemini" },
      ];

      const gemini = new GeminiLLM();
      const result = await gemini.getChatCompletion(messages, {
        temperature: 0.5,
      });

      expect(result).toEqual({
        textResponse: "Response from Gemini",
        metrics: {
          prompt_tokens: 20, // 2 messages * 10 tokens per message (from mock)
          completion_tokens: 10, // 1 assistant message * 10 tokens
          total_tokens: 30,
          outputTps: 30 / 1000,
          duration: 1000,
        },
      });
    });

    it("should handle API errors gracefully", async () => {
      const { GoogleGenAI } = require("@google/genai");
      const mockChat = {
        sendMessage: jest
          .fn()
          .mockImplementation(() =>
            Promise.reject(new Error("Gemini API error"))
          ),
      };
      const mockAI = {
        getGenerativeModel: jest.fn(),
        chats: {
          create: jest.fn().mockReturnValue(mockChat),
        },
      };
      GoogleGenAI.mockImplementation(() => mockAI);

      // Temporarily override the performance monitor mock for this test only
      const originalMeasureAsyncFunction =
        mockLLMPerformanceMonitor.measureAsyncFunction;
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementation(async () => {
          throw new Error("Gemini API error");
        });

      const messages: ChatMessage[] = [
        { role: "user", content: "Test message" },
      ];

      const gemini = new GeminiLLM();
      await expect(gemini.getChatCompletion(messages, {})).rejects.toThrow(
        "Gemini API error"
      );

      // Restore the original mock after the test
      mockLLMPerformanceMonitor.measureAsyncFunction =
        originalMeasureAsyncFunction;
    });

    it("should not throw error for any model since validation is disabled", async () => {
      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];
      const { GoogleGenAI } = require("@google/genai");
      const mockChat = {
        sendMessage: jest.fn().mockResolvedValue({ text: "Response" }),
      };
      const mockAI = {
        getGenerativeModel: jest.fn(),
        chats: {
          create: jest.fn().mockReturnValue(mockChat),
        },
      };
      GoogleGenAI.mockImplementation(() => mockAI);

      const gemini = new GeminiLLM();
      const result = await gemini.getChatCompletion(messages, {});

      expect(result).toBeDefined();
    });

    it("should handle missing usage data", async () => {
      const { GoogleGenAI } = require("@google/genai");
      const mockChat = {
        sendMessage: jest.fn().mockResolvedValue({
          text: "Response",
        }),
      };
      const mockAI = {
        getGenerativeModel: jest.fn(),
        chats: {
          create: jest.fn().mockReturnValue(mockChat),
        },
      };
      GoogleGenAI.mockImplementation(() => mockAI);

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const gemini = new GeminiLLM();
      const result = await gemini.getChatCompletion(messages, {});

      expect(result?.metrics).toEqual({
        prompt_tokens: 10, // From countTokens mock
        completion_tokens: 10, // From countTokens mock
        total_tokens: 20,
        outputTps: 20 / 1000,
        duration: 1000,
      });
    });
  });

  describe("streamGetChatCompletion", () => {
    let gemini: GeminiLLM;

    beforeEach(() => {
      const { GoogleGenAI } = require("@google/genai");
      const mockStreamChat = {
        sendMessageStream: jest.fn().mockResolvedValue({
          [Symbol.asyncIterator]: async function* () {
            yield {
              text: () => "Chunk 1",
              usageMetadata: { promptTokenCount: 50 },
            };
            yield {
              text: () => " Chunk 2",
              usageMetadata: { candidatesTokenCount: 20 },
            };
          },
        }),
      };
      const mockAI = {
        getGenerativeModel: jest.fn(),
        chats: {
          create: jest.fn().mockReturnValue(mockStreamChat),
        },
      };
      GoogleGenAI.mockImplementation(() => mockAI);
      gemini = new GeminiLLM();
    });

    it("should stream completion successfully", async () => {
      const messages: ChatMessage[] = [
        { role: "system", content: "System prompt" },
        { role: "user", content: "Stream test" },
      ];

      const result = await gemini.streamGetChatCompletion(messages, {
        temperature: 0.7,
      });

      expect(result.stream).toBeDefined();
      expect(result.endMeasurement).toBeDefined();
    });

    it("should handle empty user messages array", async () => {
      const messages: ChatMessage[] = [{ role: "system", content: "System" }];

      // The implementation filters for user messages and may not throw
      const result = await gemini.streamGetChatCompletion(messages, {});
      expect(result).toBeDefined();
    });
  });

  describe("handleStream", () => {
    let gemini: GeminiLLM;
    let mockResponse: Partial<ExpressResponse>;

    beforeEach(() => {
      gemini = new GeminiLLM();
      mockResponse = {
        write: jest.fn(),
        end: jest.fn(),
        on: jest.fn(),
        removeListener: jest.fn(),
      };
    });

    it("should handle stream events successfully", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { text: () => "Hello " };
          yield { text: () => "from Gemini" };
        },
        endMeasurement: jest.fn(),
      };

      const responseProps: StreamResponseProps = {
        uuid: "test-uuid",
        sources: [],
      };

      const fullText = await gemini.handleStream(
        mockResponse as ExpressResponse,
        mockStream as any,
        responseProps
      );

      expect(fullText).toBe("Hello from Gemini");
      expect(mockWriteResponseChunk).toHaveBeenCalledWith(mockResponse, {
        uuid: "test-uuid",
        sources: [],
        type: "textResponseChunk",
        textResponse: "Hello ",
        close: false,
        error: false,
      });
    });

    it("should handle stream errors", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield ""; // Dummy yield to satisfy generator requirement
          throw new Error("Stream error");
        },
        endMeasurement: jest.fn(),
      };

      const responseProps: StreamResponseProps = {
        uuid: "test-uuid",
        sources: [],
      };

      await gemini.handleStream(
        mockResponse as ExpressResponse,
        mockStream as any,
        responseProps
      );

      expect(mockWriteResponseChunk).toHaveBeenCalledWith(mockResponse, {
        uuid: "test-uuid",
        sources: [],
        type: "abort",
        textResponse: null,
        close: true,
        error: "Failed to process Gemini response stream. Please try again.",
      });
    });
  });

  describe("embedding methods", () => {
    let gemini: GeminiLLM;

    beforeEach(() => {
      gemini = new GeminiLLM();
    });

    it("should embed text input successfully", async () => {
      const result = await gemini.embedTextInput("Test text");

      expect(result).toEqual([0.1, 0.2, 0.3]);
      expect(gemini.embedder.embedTextInput).toHaveBeenCalledWith("Test text");
    });

    it("should embed chunks successfully", async () => {
      const chunks = ["Chunk 1", "Chunk 2"];
      const result = await gemini.embedChunks(chunks);

      expect(result).toEqual([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]);
      expect(gemini.embedder.embedChunks).toHaveBeenCalledWith(chunks);
    });
  });

  describe("compressMessages", () => {
    let gemini: GeminiLLM;

    beforeEach(() => {
      gemini = new GeminiLLM();
    });

    it("should compress messages successfully", async () => {
      const { messageArrayCompressor } = require("../../../helpers/chat");
      const compressedMessages = [{ role: "system", content: "Compressed" }];
      messageArrayCompressor.mockResolvedValue(compressedMessages);

      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        userPrompt: "User",
      };
      const rawHistory: ChatMessage[] = [{ role: "user", content: "History" }];

      const result = await gemini.compressMessages(promptArgs, rawHistory);

      expect(result).toEqual(compressedMessages);
    });
  });

  describe("constructPrompt", () => {
    let gemini: GeminiLLM;

    beforeEach(() => {
      gemini = new GeminiLLM();
    });

    it("should construct prompt with all components", async () => {
      const promptArgs: PromptArgs = {
        systemPrompt: "You are a helpful assistant.",
        contextTexts: ["Context 1", "Context 2"],
        chatHistory: [
          { role: "user", content: "What is Gemini?" },
          { role: "assistant", content: "Gemini is Google's AI model." },
        ],
        userPrompt: "Tell me more",
      };

      const result = await gemini.constructPrompt(promptArgs);

      expect(result).toEqual([
        {
          role: "system",
          content:
            "You are a helpful assistant.Context 1\n\n---CONTEXT---\n\nContext 2",
        },
        { role: "user", content: "What is Gemini?" },
        { role: "assistant", content: "Gemini is Google's AI model." },
        { role: "user", content: "Tell me more" },
      ]);
    });

    it("should handle attachments in prompt", async () => {
      const attachment: Attachment = {
        name: "image.png",
        mime: "image/png",
        contentString: "data:image/png;base64,iVBORw0KGgo...",
      };

      const promptArgs: PromptArgs = {
        systemPrompt: "Analyze images",
        userPrompt: "What's in this image?",
        attachments: [attachment],
      };

      const result = await gemini.constructPrompt(promptArgs);

      const lastMessage = result[result.length - 1];
      expect(lastMessage.role).toBe("user");
      expect(Array.isArray(lastMessage.content)).toBe(true);
    });
  });

  describe("security testing", () => {
    let _gemini: GeminiLLM;

    beforeEach(() => {
      _gemini = new GeminiLLM();
    });

    it("should handle malicious input in prompts", async () => {
      const maliciousPrompt = "<script>alert('xss')</script>";
      const messages: ChatMessage[] = [
        { role: "user", content: maliciousPrompt },
      ];

      const { GoogleGenAI } = require("@google/genai");
      // Use a fresh mock setup for this test
      const safeMockChat = {
        sendMessage: jest.fn().mockResolvedValue({
          text: "Safe response",
        }),
      };
      const safeMockAI = {
        getGenerativeModel: jest.fn(),
        chats: {
          create: jest.fn().mockReturnValue(safeMockChat),
        },
      };
      GoogleGenAI.mockImplementation(() => safeMockAI);

      // Create a new instance with the updated mock
      const geminiSafe = new GeminiLLM();
      const result = await geminiSafe.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("Safe response");
    });

    it("should protect API key in initialization", () => {
      const apiKey = "test-gemini-api-key-secret";
      process.env.GEMINI_API_KEY = apiKey;

      new GeminiLLM();

      // The API key should be used but not exposed
      const { GoogleGenAI } = require("@google/genai");
      expect(GoogleGenAI).toHaveBeenCalledWith({
        apiKey: apiKey,
      });
    });
  });

  describe("performance testing", () => {
    let gemini: GeminiLLM;

    beforeEach(() => {
      gemini = new GeminiLLM();
    });

    it("should handle large context windows", async () => {
      const largeContext = Array(100).fill("Large document section").join(" ");
      const messages: ChatMessage[] = Array(50)
        .fill(null)
        .map((_, i) => ({
          role: i % 2 === 0 ? "user" : "assistant",
          content: largeContext,
        }));

      const { GoogleGenAI } = require("@google/genai");
      const mockChat = {
        sendMessage: jest.fn().mockResolvedValue({
          text: "Handled large context",
        }),
      };
      const mockAI = {
        getGenerativeModel: jest.fn(),
        chats: {
          create: jest.fn().mockReturnValue(mockChat),
        },
      };
      GoogleGenAI.mockImplementation(() => mockAI);

      const result = await gemini.getChatCompletion(messages, {});

      expect(result?.textResponse).toBeTruthy();
    });

    it("should handle concurrent requests", async () => {
      const { GoogleGenAI } = require("@google/genai");
      const mockChat = {
        sendMessage: jest.fn().mockResolvedValue({
          text: "Concurrent response",
        }),
      };
      const mockAI = {
        getGenerativeModel: jest.fn(),
        chats: {
          create: jest.fn().mockReturnValue(mockChat),
        },
      };
      GoogleGenAI.mockImplementation(() => mockAI);

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const promises = Array(10)
        .fill(null)
        .map(() => gemini.getChatCompletion(messages, {}));

      const results = await Promise.all(promises);

      expect(results).toHaveLength(10);
      results.forEach((result) => {
        expect(result?.textResponse).toBeTruthy();
      });
    });
  });

  describe("edge cases", () => {
    let gemini: GeminiLLM;

    beforeEach(() => {
      gemini = new GeminiLLM();
    });

    afterEach(() => {
      // Only clear mock calls, don't reset implementations
      jest.clearAllMocks();
    });

    it("should handle rate limiting", async () => {
      const { GoogleGenAI } = require("@google/genai");
      const mockChat = {
        sendMessage: jest
          .fn()
          .mockRejectedValue(new Error("Resource exhausted")),
      };
      const mockAI = {
        getGenerativeModel: jest.fn(),
        chats: {
          create: jest.fn().mockReturnValue(mockChat),
        },
      };
      GoogleGenAI.mockImplementation(() => mockAI);
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementation(async () => {
          const error = new Error("Resource exhausted");
          (error as any).status = 429;
          throw error;
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(gemini.getChatCompletion(messages, {})).rejects.toThrow(
        "Resource exhausted"
      );
    });

    it("should handle environment variable edge cases", () => {
      process.env.GEMINI_API_KEY = "";

      expect(() => new GeminiLLM()).toThrow("No Gemini API key was set.");

      delete process.env.GEMINI_API_KEY;
      delete process.env.GEMINI_API_KEY_DD;
      expect(() => new GeminiLLM()).toThrow("No Gemini API key was set.");
    });

    it("should handle different temperature values", async () => {
      const { GoogleGenAI } = require("@google/genai");
      const mockChat = {
        sendMessage: jest.fn().mockResolvedValue({
          text: "Response",
        }),
      };
      const mockAI = {
        getGenerativeModel: jest.fn(),
        chats: {
          create: jest.fn().mockReturnValue(mockChat),
        },
      };
      GoogleGenAI.mockClear();
      GoogleGenAI.mockImplementation(() => mockAI);

      // Create a new instance for this test
      const geminiTemp = new GeminiLLM();
      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      // The implementation doesn't pass temperature to the API
      await geminiTemp.getChatCompletion(messages, { temperature: 0 });
      expect(mockChat.sendMessage).toHaveBeenCalled();

      // Test with temperature 1
      await geminiTemp.getChatCompletion(messages, { temperature: 1 });
      expect(mockChat.sendMessage).toHaveBeenCalled();
    });

    it("should handle unicode and special characters", async () => {
      const unicodeMessages: ChatMessage[] = [
        {
          role: "user",
          content: "Test with emojis 🚀💻🔒 and special chars: ñáéíóú",
        },
        {
          role: "assistant",
          content: "Unicode response: 你好世界 مرحبا بالعالم",
        },
      ];

      const { GoogleGenAI } = require("@google/genai");
      const mockChat = {
        sendMessage: jest.fn().mockResolvedValue({
          text: "Handled unicode ✓",
        }),
      };
      const mockAI = {
        getGenerativeModel: jest.fn(),
        chats: {
          create: jest.fn().mockReturnValue(mockChat),
        },
      };
      GoogleGenAI.mockImplementation(() => mockAI);

      const result = await gemini.getChatCompletion(unicodeMessages, {});

      expect(result?.textResponse).toBeTruthy();
    });
  });
});
