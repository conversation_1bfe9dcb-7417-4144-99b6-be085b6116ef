import { NativeEmbedder } from "../../EmbeddingEngines/native";
import { LLMPerformanceMonitor } from "../../helpers/chat/LLMPerformanceMonitor";
import {
  writeResponseChunk,
  clientAbortedHandler,
} from "../../helpers/chat/responses";
import { MODEL_MAP } from "../modelMap";
import { defaultGeminiModels } from "./defaultModals";
import { formatContextTexts } from "../../helpers";
import { v4 as uuidv4 } from "uuid";
import {
  createMonitoredStream,
  createStreamResponse,
} from "../streamConverters";
import {
  LLMProvider,
  ChatMessage,
  PromptArgs,
  CompletionOptions,
  CompletionResponse,
  StreamResponseProps,
  EmbeddingEngine,
  TokenLimits,
  Attachment,
  TextContent,
  ImageContent,
  LLMStreamResponse,
  UsageMetrics,
} from "../../../types/ai-providers";

import { MonitoredStream } from "../../../types/ai-provider-streams";
import type { Response as ExpressResponse } from "express";

// Google GenAI types
interface GoogleGenAI {
  chats: {
    create: (config: {
      model: string;
      history: GeminiMessage[];
      safetySettings: GeminiSafetySettings[];
    }) => GeminiChat;
  };
}

interface GeminiChat {
  sendMessage: (params: { message: string }) => Promise<{ text: string }>;
  sendMessageStream: (params: {
    message: string;
  }) => Promise<AsyncIterable<{ text: () => string }>>;
}

interface GeminiModel {
  id: string;
  name: string;
  contextWindow: number;
  experimental: boolean;
}

interface GeminiMessage {
  role: "user" | "model";
  parts: Array<{ text: string }>;
}

interface GeminiSafetySettings {
  category: string;
  threshold: string;
}
export class GeminiLLM implements LLMProvider {
  model: string;
  limits: TokenLimits;
  embedder: EmbeddingEngine;
  defaultTemp: number;
  private ai: GoogleGenAI;
  private safetyThreshold: string;

  constructor(
    embedder: EmbeddingEngine | null = null,
    modelPreference: string | null = null,
    settings_suffix: string | null = null
  ) {
    // Check for either the suffixed or non-suffixed API key
    const apiKeyEnvVar = `GEMINI_API_KEY${settings_suffix || ""}`;
    if (!process.env[apiKeyEnvVar] && !process.env.GEMINI_API_KEY)
      throw new Error("No Gemini API key was set.");

    if (process.env.NODE_ENV === "development") {
      try {
        const stackTrace = new Error().stack;
        if (typeof stackTrace === "string") {
          const stackLines = stackTrace?.split("\n");
          let caller = "Unknown";

          if ((stackLines?.length ?? 0) > 2) {
            const callerLine = stackLines[2].trim();
            const match =
              callerLine?.match(/at (.+?) \(/) || callerLine?.match(/at (.*)/);
            if (match && match[1]) {
              caller = match[1];
            }
          }

          console.log(
            `\x1b[32m[GeminiLLM INIT]\x1b[0m ============================================`
          );
          console.log(
            `\x1b[32m[GeminiLLM INIT]\x1b[0m Initialization triggered by: ${caller}`
          );
          console.log(
            `\x1b[32m[GeminiLLM INIT]\x1b[0m Settings suffix: "${
              settings_suffix || "none"
            }"`
          );
          console.log(
            `\x1b[32m[GeminiLLM INIT]\x1b[0m Model preference: "${
              modelPreference || "none"
            }"`
          );
          console.log(
            `\x1b[32m[GeminiLLM INIT]\x1b[0m Embedder type: ${
              embedder
                ? embedder?.constructor.name
                : "none (will use NativeEmbedder)"
            }"`
          );
          console.log(
            `\x1b[32m[GeminiLLM INIT]\x1b[0m API Key suffix: "${
              settings_suffix || ""
            }"`
          );
          console.log(`\x1b[32m[GeminiLLM INIT]\x1b[0m Caller stack:`);
          const relevantStack = stackLines?.slice(1, 6);
          relevantStack?.forEach((line, index) => {
            console.log(
              `\x1b[32m[GeminiLLM INIT]\x1b[0m   ${index + 1}. ${line?.trim()}`
            );
          });
          console.log(
            `\x1b[32m[GeminiLLM INIT]\x1b[0m ============================================`
          );
        }
      } catch {
        // Fails silently to avoid impacting production.
      }
    }

    // Initialize GenAI client
    const { GoogleGenAI } = require("@google/genai");
    const apiKey =
      process.env[`GEMINI_API_KEY${settings_suffix || ""}`] ||
      (settings_suffix ? process.env.GEMINI_API_KEY : null);

    if (!apiKey) {
      throw new Error(
        `No Gemini API key found for suffix: ${settings_suffix || "none"}`
      );
    }

    const ai = new GoogleGenAI({
      apiKey: apiKey,
    });
    this.ai = ai;
    this.model =
      modelPreference ||
      process.env[`GEMINI_LLM_MODEL_PREF${settings_suffix || ""}`] ||
      "gemini-2.5-flash";
    this.limits = {
      history: this.promptWindowLimit() * 0.15,
      system: this.promptWindowLimit() * 0.15,
      user: this.promptWindowLimit() * 0.7,
    };

    this.embedder = embedder ?? new NativeEmbedder();
    this.defaultTemp = MODEL_MAP?.gemini.defaults?.temperature; // not used for Gemini
    this.safetyThreshold = this.#fetchSafetyThreshold();
    this.#log(`Initialized with model: ${this.model}`);
  }

  #log(text: string, ...args: unknown[]): void {
    console.log(`\x1b[32m[GeminiLLM]\x1b[0m ${text}`, ...args);
  }

  async #appendContext(contextTexts: string[] = []): Promise<string> {
    if (!contextTexts || !contextTexts?.length) return "";
    return await formatContextTexts(contextTexts);
  }

  // BLOCK_NONE can be a special candidate for some fields
  // https://(cloud?.google.com ?? 0) / vertex-ai/generative-ai/docs/multimodal/configure-safety-attributes#how_to_remove_automated_response_blocking_for_select_safety_attributes
  // so if you are wondering why BLOCK_NONE still failed, the link above will explain why.
  #fetchSafetyThreshold(): string {
    const threshold =
      process.env.GEMINI_SAFETY_SETTING ?? "BLOCK_MEDIUM_AND_ABOVE";
    const safetyThresholds = [
      "BLOCK_NONE",
      "BLOCK_ONLY_HIGH",
      "BLOCK_MEDIUM_AND_ABOVE",
      "BLOCK_LOW_AND_ABOVE",
    ];
    return safetyThresholds?.includes(threshold)
      ? threshold
      : "BLOCK_MEDIUM_AND_ABOVE";
  }

  #safetySettings(): GeminiSafetySettings[] {
    return [
      {
        category: "HARM_CATEGORY_HATE_SPEECH",
        threshold: this.safetyThreshold,
      },
      {
        category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
        threshold: this.safetyThreshold,
      },
      {
        category: "HARM_CATEGORY_HARASSMENT",
        threshold: this?.safetyThreshold,
      },
      {
        category: "HARM_CATEGORY_DANGEROUS_CONTENT",
        threshold: this.safetyThreshold,
      },
    ];
  }

  streamingEnabled(): boolean {
    return "streamGetChatCompletion" in this;
  }

  static promptWindowLimit(modelName?: string): number {
    if (modelName && MODEL_MAP?.gemini?.models?.[modelName]) {
      return MODEL_MAP?.gemini.models[modelName].context;
    }
    return MODEL_MAP?.gemini.defaults?.contextWindow;
  }

  promptWindowLimit(): number {
    if (MODEL_MAP?.gemini?.models?.[this.model]) {
      return MODEL_MAP?.gemini.models[this.model].context;
    }
    return MODEL_MAP?.gemini.defaults?.contextWindow;
  }

  /**
   * Used when custom AI configurations are active.
   * Returns the standard prompt window limit for the current model.
   * This allows us to provide consistent token limits across different configurations.
   */
  customPromptWindowLimit(): number {
    return this.promptWindowLimit();
  }

  /**
   * Fetches Gemini models from the Google Generative AI API
   */
  static async fetchModels(
    apiKey: string,
    limit: number = 1_000,
    pageToken: string | null = null
  ): Promise<GeminiModel[]> {
    const url = new URL(
      "https://(generativelanguage?.googleapis.com ?? 0) / v1beta/models"
    );
    url?.searchParams.set("pageSize", limit?.toString());
    url?.searchParams.set("key", apiKey);
    if (pageToken) url?.searchParams.set("pageToken", pageToken);
    return fetch(url?.toString(), {
      method: "GET",
      headers: { "Content-Type": "application/json" },
    })
      .then((res) => res?.json())
      .then((data: unknown) => {
        const typedData = data as {
          error?: { message: string };
          models?: Array<{
            name: string;
            displayName?: string;
            inputTokenLimit: number;
            supportedGenerationMethods: string[];
          }>;
        };
        if (typedData?.error) throw new Error(typedData?.error.message);
        return typedData?.models ?? [];
      })
      .then((models) => {
        // First get models from API response
        const apiModels = models
          .filter((model) =>
            model?.supportedGenerationMethods.some(
              (method: string) =>
                method?.includes("generate") || method?.includes("vision")
            )
          )
          .map((model): GeminiModel => {
            const modelId = model?.name.split("/").pop();
            const displayName = model?.displayName || modelId;

            // Improved name formatting logic
            let formattedName = displayName;
            if (displayName?.startsWith("gemini-exp-")) {
              formattedName = `Gemini Experimental ${displayName?.split("gemini-exp-")[1]}`;
            } else if (modelId?.includes("exp")) {
              // Handle cases where exp is in the middle of the name
              const datePart = modelId?.match(/\d{4}/)?.[0];
              if (datePart) {
                formattedName = `Gemini Experimental ${datePart}`;
              }
            }

            return {
              id: modelId || "unknown",
              name: formattedName || "Unknown Model",
              contextWindow: model?.inputTokenLimit || 0,
              experimental: Boolean(
                modelId?.includes("exp") || displayName?.includes("exp")
              ),
            };
          });

        // If API call succeeded but returned no models, use defaults
        if ((apiModels?.length ?? 0) === 0) {
          return defaultGeminiModels;
        }

        // Create a Set of model IDs to track what we've seen
        const seenModelIds = new Set();
        const uniqueModels: GeminiModel[] = [];

        // Process API models first
        for (const model of apiModels) {
          if (!seenModelIds?.has(model?.id)) {
            seenModelIds?.add(model?.id);
            uniqueModels?.push(model);
          }
        }

        return uniqueModels;
      })
      .catch((e) => {
        console.error(`Gemini:getGeminiModels`, e?.message);
        return defaultGeminiModels;
      });
  }

  /**
   * Checks if a model is valid for chat completion (unused)
   * @deprecated
   */
  async isValidChatCompletionModel(_modelName: string = ""): Promise<boolean> {
    // Validation disabled for Gemini as it's not needed
    return true;
  }

  /**
   * Generates appropriate content array for a message + attachments.
   */
  #generateContent({
    userPrompt,
    attachments = [],
  }: {
    userPrompt: string;
    attachments?: Attachment[];
  }):
    | string
    | Array<{
        text?: string;
        inlineData?: { data: string; mimeType: string };
      }> {
    if (!attachments?.length) {
      return userPrompt;
    }

    // For Gemini, we need to combine all text content into a single string
    let combinedText = userPrompt;
    const imageAttachments: Array<{
      inlineData: { data: string; mimeType: string };
    }> = [];

    // Process attachments
    attachments?.forEach((attachment, index) => {
      if (attachment?.mime?.startsWith("image/")) {
        // Collect image attachments separately
        imageAttachments?.push({
          inlineData: {
            data: attachment?.contentString.split("base64,")[1],
            mimeType: attachment?.mime,
          },
        });
      } else {
        // Add text attachments to combined text
        const fileListEntry =
          index === 0
            ? `\n\nAttached file${(attachments?.length ?? 0) > 1 ? "s" : ""}: ${attachment?.name}\n`
            : `${attachment?.name}\n`;
        combinedText += fileListEntry + attachment?.contentString;
      }
    });

    // If we have image attachments, return array with both text and images
    if ((imageAttachments?.length ?? 0) > 0) {
      return [{ text: combinedText }, ...imageAttachments];
    }

    // Otherwise just return the combined text
    return combinedText;
  }

  async constructPrompt({
    systemPrompt = "",
    contextTexts = [],
    chatHistory = [],
    userPrompt = "",
    attachments = [],
  }: PromptArgs): Promise<ChatMessage[]> {
    const formattedContext = await this.#appendContext(contextTexts);
    const systemMessage: ChatMessage = {
      role: "system",
      content: `${systemPrompt}${formattedContext}`,
    };

    // If we have attachments, create a single user message with the content
    if (attachments && (attachments?.length ?? 0) > 0) {
      const userMessage: ChatMessage = {
        role: "user",
        content: this.#generateContent({
          userPrompt: userPrompt || "",
          attachments,
        }) as string | Array<TextContent | ImageContent>,
      };
      const result: ChatMessage[] = [
        systemMessage,
        ...chatHistory,
        userMessage,
      ];
      return result;
    }

    // Otherwise, use the simple format
    const result: ChatMessage[] = [
      systemMessage,
      ...chatHistory,
      { role: "user" as const, content: userPrompt || "" },
    ];
    return result;
  }

  // This will take an OpenAi format message array and only pluck valid roles from it.
  formatMessages(messages: ChatMessage[] = []): GeminiMessage[] {
    // Gemini roles are either user || model.
    // and all "content" is relabeled to "parts"
    const allMessages = messages
      .map((message): GeminiMessage | null => {
        // Handle content that could be string or array
        let textContent: string;
        if (typeof message?.content === "string") {
          textContent = message?.content;
        } else if (
          Array.isArray(message?.content) &&
          (message?.content?.length ?? 0) > 0
        ) {
          // Extract text from the first part if it's an array
          const firstPart = message?.content[0] ?? undefined;
          if (typeof firstPart === "object" && "text" in firstPart) {
            textContent = firstPart?.text;
          } else {
            textContent = String(firstPart);
          }
        } else {
          textContent = "";
        }

        if (message?.role === "system")
          return { role: "user", parts: [{ text: textContent }] };
        if (message?.role === "user")
          return { role: "user", parts: [{ text: textContent }] };
        if (message?.role === "assistant")
          return {
            role: "model",
            parts: [{ text: textContent }],
          };
        return null;
      })
      .filter((msg): msg is GeminiMessage => !!msg);

    // Specifically, Google cannot have the last sent message be from a user with no assistant reply
    // otherwise it will crash. So if the last item is from the user, it was not completed so pop it off
    // the history.
    if (
      (allMessages?.length ?? 0) > 0 &&
      allMessages[(allMessages?.length ?? 0) - 1].role === "user"
    )
      allMessages?.pop();

    // Validate that after every user message, there is a model message
    // sometimes when using gemini we try to compress messages in order to retain as
    // much context as possible but this may mess up the order of the messages that the gemini model expects
    // we do this check to work around the edge case where 2 user prompts may be next to each other, in the message array
    for (let i = 0; i < allMessages?.length; i++) {
      if (
        allMessages[i].role === "user" &&
        i < (allMessages?.length ?? 0) - 1 &&
        allMessages[i + 1].role !== "model"
      ) {
        allMessages?.splice(i + 1, 0, {
          role: "model",
          parts: [{ text: "Okay." }],
        });
      }
    }

    return allMessages;
  }

  async getChatCompletion(
    messages: ChatMessage[] = [],
    _opts: CompletionOptions = {}
  ): Promise<CompletionResponse | null> {
    // Get the LAST user message, not the first one
    const userMessages = messages?.filter((chat) => chat?.role === "user");
    const prompt = userMessages?.[userMessages.length - 1]?.content as string;
    const chat = this.ai.chats?.create({
      model: this.model,
      history: this.formatMessages(messages),
      safetySettings: this.#safetySettings(),
    });

    const { output: responseObj, duration } =
      (await LLMPerformanceMonitor?.measureAsyncFunction(
        chat?.sendMessage({ message: prompt })
      )) || { output: null, duration: 0 };
    const responseText =
      typeof responseObj === "string"
        ? responseObj
        : (responseObj as { text?: string })?.text ||
          JSON.stringify(responseObj);

    if (!responseText) throw new Error("Gemini: No response could be parsed.");

    const promptTokens = LLMPerformanceMonitor?.countTokens(
      messages.map((msg) => ({
        content:
          typeof msg.content === "string"
            ? msg.content
            : JSON.stringify(msg.content),
        role: msg.role,
      }))
    );
    const completionTokens = LLMPerformanceMonitor?.countTokens([
      { content: responseText, role: "assistant" },
    ]);
    return {
      textResponse: responseText,
      metrics: {
        prompt_tokens: promptTokens,
        completion_tokens: completionTokens,
        total_tokens: promptTokens + completionTokens,
        outputTps: (promptTokens + completionTokens) / duration,
        duration,
      },
    };
  }

  async streamGetChatCompletion(
    messages: ChatMessage[] = [],
    _opts: CompletionOptions = {}
  ): Promise<LLMStreamResponse> {
    // Get the LAST user message, not the first one
    const userMessages = messages?.filter((chat) => chat?.role === "user");
    const prompt = userMessages?.[userMessages.length - 1]?.content as string;
    const chat = this.ai.chats?.create({
      model: this.model,
      history: this.formatMessages(messages),
      safetySettings: this.#safetySettings(),
    });

    if (!chat) {
      throw new Error("Failed to create Gemini chat instance");
    }

    try {
      // Start streaming and monitor performance (pass the raw stream directly)
      const rawStream = await chat.sendMessageStream({ message: prompt });

      if (!rawStream) {
        throw new Error("Failed to create stream from Gemini");
      }

      // Ensure the stream is properly converted to AsyncIterable
      const { ensureAsyncIterable } = require("../streamConverters");
      const asyncIterableStream = ensureAsyncIterable(rawStream);

      const responseStream = await createMonitoredStream(
        asyncIterableStream,
        messages.map((msg) => ({
          content:
            typeof msg.content === "string"
              ? msg.content
              : JSON.stringify(msg.content),
          role: msg.role,
        }))
      );

      if (!responseStream)
        throw new Error("Could not stream response stream from Gemini.");
      return createStreamResponse(responseStream);
    } catch (error: unknown) {
      this.#log(
        `Stream error: ${error instanceof Error ? error.message : String(error)}`
      );
      // Fall back to non-streaming response if streaming fails
      const responseObj = await chat?.sendMessage({ message: prompt });
      const text = responseObj?.text ?? "";

      // Create a mock stream with the full response
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { text: () => text };
        },
        start: Date.now(),
        duration: 0,
        metrics: {
          prompt_tokens: 0,
          completion_tokens: 0,
          total_tokens: 0,
          outputTps: 0,
          duration: 0,
        },
        endMeasurement: (usage: Partial<UsageMetrics> = {}): UsageMetrics => ({
          prompt_tokens: usage.prompt_tokens || 0,
          completion_tokens: usage.completion_tokens || 0,
          total_tokens: usage.total_tokens || 0,
          outputTps: usage.outputTps || 0,
          duration: usage.duration || 0,
        }),
      } as MonitoredStream;

      return createStreamResponse(mockStream);
    }
  }

  async compressMessages(
    promptArgs: PromptArgs = {},
    rawHistory: ChatMessage[] = []
  ): Promise<ChatMessage[]> {
    const { messageArrayCompressor } = require("../../helpers/chat");
    const messageArray = await this.constructPrompt(promptArgs);
    return await messageArrayCompressor(
      this,
      messageArray,
      rawHistory,
      [],
      [],
      promptArgs?.maxAllowedTokens
    );
  }

  handleStream(
    response: ExpressResponse,
    stream: AsyncIterable<unknown>,
    responseProps: StreamResponseProps
  ): Promise<string> {
    const { uuid = uuidv4(), sources = [] } = responseProps;
    // Usage is not available for Gemini streams
    // so we need to calculate the completion tokens manually
    // because 1 chunk != 1 token in gemini responses and it buffers
    // many tokens before sending them to the client as a "chunk"

    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve) => {
      let fullText = "";

      // Establish listener to early-abort a streaming response
      // in case things go sideways or the user does not like the response.
      // We preserve the generated text but continue as if chat was completed
      // to preserve previously generated content.
      const handleAbort = () => {
        (stream as MonitoredStream)?.endMeasurement({
          completion_tokens: LLMPerformanceMonitor?.countTokens([
            { content: fullText },
          ] as { content: string }[]),
        });
        clientAbortedHandler(resolve, fullText);
      };
      response?.on("close", handleAbort);

      try {
        // Ensure the stream is async iterable
        const { ensureAsyncIterable } = require("../streamConverters");
        const iterableStream = ensureAsyncIterable(stream);

        for await (const chunk of iterableStream) {
          let chunkText;
          // Extract text from chunk: support .text() method or .text property
          try {
            const chunkObj = chunk as {
              text?: (() => string) | string;
              textContent?: string;
            };
            if (typeof chunkObj?.text === "function") {
              chunkText = chunkObj?.text();
            } else if (typeof chunkObj?.text === "string") {
              chunkText = chunkObj?.text;
            } else if (typeof chunkObj?.textContent === "string") {
              chunkText = chunkObj?.textContent;
            } else {
              // Unknown chunk format, default to empty string
              chunkText = "";
            }
          } catch (e: unknown) {
            // If text() throws due to safety protocols, abort streaming
            chunkText = e instanceof Error ? e.message : String(e);
            writeResponseChunk(response, {
              uuid,
              sources: [],
              type: "abort",
              textResponse: null,
              close: true,
              error: e instanceof Error ? e.message : String(e),
            });
            (stream as MonitoredStream)?.endMeasurement({
              completion_tokens: 0,
            });
            resolve(e instanceof Error ? e.message : String(e));
            return;
          }

          fullText += chunkText;
          writeResponseChunk(response, {
            uuid,
            sources: [],
            type: "textResponseChunk",
            textResponse: chunkText,
            close: false,
            error: false,
          });
        }

        writeResponseChunk(response, {
          uuid,
          sources,
          type: "textResponseChunk",
          textResponse: "",
          close: true,
          error: false,
        });
        response?.removeListener("close", handleAbort);
        (stream as MonitoredStream)?.endMeasurement({
          completion_tokens: LLMPerformanceMonitor?.countTokens([
            { content: fullText },
          ] as { content: string }[]),
        });
        resolve(fullText);
      } catch (error: unknown) {
        this.#log(
          `Stream handling error: ${error instanceof Error ? error.message : String(error)}`
        );
        this.#log(`Full error details:`, error);

        // If we have some text already, send it as the final response
        if (fullText) {
          writeResponseChunk(response, {
            uuid,
            sources,
            type: "textResponseChunk",
            textResponse: "",
            close: true,
            error: false,
          });
          response?.removeListener("close", handleAbort);
          (stream as MonitoredStream)?.endMeasurement({
            completion_tokens: LLMPerformanceMonitor?.countTokens([
              { content: fullText },
            ]),
          });
          resolve(fullText);
        } else {
          // If we have no text, send an error message
          writeResponseChunk(response, {
            uuid,
            sources: [],
            type: "abort",
            textResponse: null,
            close: true,
            error:
              "Failed to process Gemini response stream. Please try again.",
          });
          (stream as MonitoredStream)?.endMeasurement({ completion_tokens: 0 });
          resolve(
            "Failed to process Gemini response stream. Please try again."
          );
        }
      }
    });
  }

  // Simple wrapper for dynamic embedder & normalize interface for all LLM implementations
  async embedTextInput(textInput: string): Promise<number[]> {
    return await this.embedder.embedTextInput(textInput);
  }

  async embedChunks(textChunks: string[]): Promise<number[][]> {
    return (await this.embedder.embedChunks(textChunks)) || [];
  }
}

export default GeminiLLM;
