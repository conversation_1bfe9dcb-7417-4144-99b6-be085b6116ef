import { GenericOpenAiLLM } from "../index";
import { OpenAI as OpenAIApi } from "openai";
import { NativeEmbedder } from "../../../EmbeddingEngines/native";
import { LLMPerformanceMonitor } from "../../../helpers/chat/LLMPerformanceMonitor";
import { handleDefaultStreamResponseV2 } from "../../../helpers/chat/responses";
import { convertOpenAIStream } from "../../streamConverters";
import type { Response as ExpressResponse } from "express";
import type {
  ChatMessage,
  PromptArgs,
  EmbeddingEngine,
  Attachment,
} from "../../../../types/ai-providers";

// Mock dependencies
jest.mock("openai");
jest.mock("../../../EmbeddingEngines/native");
jest.mock("../../../helpers/chat/LLMPerformanceMonitor");
jest.mock("../../../helpers/chat/responses");
jest.mock("../../streamConverters");
jest.mock("../../../helpers", () => ({
  formatContextTexts: jest.fn().mockImplementation(async (texts: string[]) => {
    return texts.join("\n\n---CONTEXT---\n\n");
  }),
}));
jest.mock("../../../helpers/chat", () => ({
  messageArrayCompressor: jest
    .fn()
    .mockImplementation(async (provider, messages, _rawHistory) => {
      return messages;
    }),
}));
jest.mock("../../../http", () => ({
  toValidNumber: jest
    .fn()
    .mockImplementation((value: string | undefined, defaultValue: number) => {
      if (!value) return defaultValue;
      const num = Number(value);
      return isNaN(num) ? defaultValue : num;
    }),
}));

describe("GenericOpenAiLLM", () => {
  const mockOpenAI = OpenAIApi as jest.MockedClass<typeof OpenAIApi>;
  const mockNativeEmbedder = NativeEmbedder as jest.MockedClass<
    typeof NativeEmbedder
  >;
  const mockLLMPerformanceMonitor = LLMPerformanceMonitor as jest.MockedClass<
    typeof LLMPerformanceMonitor
  >;
  const mockConvertOpenAIStream = convertOpenAIStream as jest.MockedFunction<
    typeof convertOpenAIStream
  >;
  const mockHandleDefaultStreamResponseV2 =
    handleDefaultStreamResponseV2 as jest.MockedFunction<
      typeof handleDefaultStreamResponseV2
    >;

  let originalEnv: typeof process.env;
  let consoleLogSpy: jest.SpyInstance;

  beforeEach(() => {
    // Store original environment
    originalEnv = { ...process.env };

    // Set up required environment variables
    process.env.GENERIC_OPEN_AI_BASE_PATH = "http://localhost:8080/v1";
    process.env.GENERIC_OPEN_AI_API_KEY = "test-api-key-generic";
    process.env.GENERIC_OPEN_AI_MODEL_PREF = "gpt-4";
    process.env.GENERIC_OPEN_AI_MODEL_TOKEN_LIMIT = "8192";
    process.env.GENERIC_OPEN_AI_MAX_TOKENS = "2048";

    // Clear all mocks
    jest.clearAllMocks();

    // Spy on console.log
    consoleLogSpy = jest.spyOn(console, "log").mockImplementation();

    // Mock OpenAI instance
    mockOpenAI.mockClear();
    mockOpenAI.prototype.chat = {
      completions: {
        create: jest.fn().mockResolvedValue({
          choices: [{ message: { content: "default response" } }],
          usage: { prompt_tokens: 1, completion_tokens: 1, total_tokens: 2 },
        }),
      },
    } as any;

    // Mock NativeEmbedder
    mockNativeEmbedder.mockClear();
    const mockEmbedderInstance = {
      embedTextInput: jest.fn().mockResolvedValue([0.1, 0.2, 0.3]),
      embedChunks: jest.fn().mockResolvedValue([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]),
    };
    mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
    consoleLogSpy.mockRestore();
  });

  describe("constructor", () => {
    it("should initialize with default settings", () => {
      const _genericOpenAi = new GenericOpenAiLLM();

      expect(mockOpenAI).toHaveBeenCalledWith({
        baseURL: "http://localhost:8080/v1",
        apiKey: "test-api-key-generic",
      });
      expect(_genericOpenAi.model).toBe("gpt-4");
      expect(_genericOpenAi["maxTokens"]).toBe(2048);
      expect(_genericOpenAi.limits).toEqual({
        history: 8192 * 0.15,
        system: 8192 * 0.15,
        user: 8192 * 0.7,
      });
      expect(_genericOpenAi.defaultTemp).toBe(0.7);
      expect(consoleLogSpy).toHaveBeenCalledWith(
        "\x1b[36m[GenericOpenAiLLM]\x1b[0m Inference API: http://localhost:8080/v1 Model: gpt-4"
      );
    });

    it("should use custom embedder when provided", () => {
      const customEmbedder = {} as EmbeddingEngine;
      const _genericOpenAi = new GenericOpenAiLLM(customEmbedder);

      expect(_genericOpenAi.embedder).toBe(customEmbedder);
    });

    it("should use model preference when provided", () => {
      const _genericOpenAi = new GenericOpenAiLLM(null, "claude-3");

      expect(_genericOpenAi.model).toBe("claude-3");
    });

    it("should use settings suffix when provided", () => {
      process.env.GENERIC_OPEN_AI_BASE_PATH_CUSTOM =
        "http://custom-server:8080/v1";
      process.env.GENERIC_OPEN_AI_API_KEY_CUSTOM = "custom-api-key";
      process.env.GENERIC_OPEN_AI_MODEL_PREF_CUSTOM = "custom-model";
      process.env.GENERIC_OPEN_AI_MODEL_TOKEN_LIMIT_CUSTOM = "16384";
      process.env.GENERIC_OPEN_AI_MAX_TOKENS_CUSTOM = "4096";

      const _genericOpenAi = new GenericOpenAiLLM(null, null, "_CUSTOM");

      expect(mockOpenAI).toHaveBeenCalledWith({
        baseURL: "http://custom-server:8080/v1",
        apiKey: "custom-api-key",
      });
      expect(_genericOpenAi.model).toBe("custom-model");
      expect(_genericOpenAi["maxTokens"]).toBe(4096);
      expect(_genericOpenAi.limits).toEqual({
        history: 16384 * 0.15,
        system: 16384 * 0.15,
        user: 16384 * 0.7,
      });
    });

    it("should throw error when base path is missing", () => {
      delete process.env.GENERIC_OPEN_AI_BASE_PATH;

      expect(() => new GenericOpenAiLLM()).toThrow(
        "GenericOpenAI must have a valid base path to use for the api."
      );
    });

    it("should throw error when model is missing", () => {
      delete process.env.GENERIC_OPEN_AI_MODEL_PREF;

      expect(() => new GenericOpenAiLLM()).toThrow(
        "GenericOpenAI must have a valid model set."
      );
    });

    it("should handle missing API key", () => {
      delete process.env.GENERIC_OPEN_AI_API_KEY;

      const _genericOpenAi = new GenericOpenAiLLM();

      expect(mockOpenAI).toHaveBeenCalledWith({
        baseURL: "http://localhost:8080/v1",
        apiKey: undefined,
      });
    });

    it("should use default max tokens when not specified", () => {
      delete process.env.GENERIC_OPEN_AI_MAX_TOKENS;

      const _genericOpenAi = new GenericOpenAiLLM();

      expect(_genericOpenAi["maxTokens"]).toBe(1024);
    });

    it("should handle invalid max tokens", () => {
      process.env.GENERIC_OPEN_AI_MAX_TOKENS = "invalid";

      const _genericOpenAi = new GenericOpenAiLLM();

      expect(_genericOpenAi["maxTokens"]).toBe(1024);
    });
  });

  describe("static properties", () => {
    it("should have correct modelsWithCustomMaxTokens", () => {
      expect(GenericOpenAiLLM.modelsWithCustomMaxTokens).toEqual([
        "o1-mini",
        "o1-preview",
      ]);
    });

    it("should have correct modelsWithoutStreaming", () => {
      expect(GenericOpenAiLLM.modelsWithoutStreaming).toEqual([
        "o1-mini",
        "o1-preview",
      ]);
    });

    it("should have correct modelsWithTempOne", () => {
      expect(GenericOpenAiLLM.modelsWithTempOne).toEqual([
        "o1-mini",
        "o1-preview",
      ]);
    });
  });

  describe("log", () => {
    it("should log with correct formatting", () => {
      const _genericOpenAi = new GenericOpenAiLLM();
      _genericOpenAi.log("Test message", "arg1", 123);

      expect(consoleLogSpy).toHaveBeenCalledWith(
        "\x1b[36m[GenericOpenAiLLM]\x1b[0m Test message",
        "arg1",
        123
      );
    });
  });

  describe("streamingEnabled", () => {
    it("should return true since streamGetChatCompletion is defined", () => {
      const _genericOpenAi = new GenericOpenAiLLM();
      expect(_genericOpenAi.streamingEnabled()).toBe(true);
    });
  });

  describe("promptWindowLimit methods", () => {
    it("should return static prompt window limit", () => {
      expect(GenericOpenAiLLM.promptWindowLimit()).toBe(8192);
    });

    it("should throw error for invalid static limit", () => {
      process.env.GENERIC_OPEN_AI_MODEL_TOKEN_LIMIT = "invalid";
      expect(() => GenericOpenAiLLM.promptWindowLimit()).toThrow(
        "No token context limit was set."
      );
    });

    it("should return instance prompt window limit", () => {
      const _genericOpenAi = new GenericOpenAiLLM();
      expect(_genericOpenAi.promptWindowLimit()).toBe(8192);
    });

    it("should return instance prompt window limit with suffix", () => {
      process.env.GENERIC_OPEN_AI_BASE_PATH_TEST = "http://test:8080/v1";
      process.env.GENERIC_OPEN_AI_MODEL_PREF_TEST = "test-model";
      process.env.GENERIC_OPEN_AI_MODEL_TOKEN_LIMIT_TEST = "16384";
      const _genericOpenAi = new GenericOpenAiLLM(null, null, "_TEST");
      expect(_genericOpenAi.promptWindowLimit()).toBe(16384);
    });

    it("customPromptWindowLimit should return same as promptWindowLimit", () => {
      const _genericOpenAi = new GenericOpenAiLLM();
      expect(_genericOpenAi.customPromptWindowLimit()).toBe(
        _genericOpenAi.promptWindowLimit()
      );
    });
  });

  describe("isValidChatCompletionModel", () => {
    it("should always return true", async () => {
      const _genericOpenAi = new GenericOpenAiLLM();
      expect(await _genericOpenAi.isValidChatCompletionModel()).toBe(true);
      expect(await _genericOpenAi.isValidChatCompletionModel("any-model")).toBe(
        true
      );
    });
  });

  describe("generateContent", () => {
    it("should return string when no attachments", () => {
      const _genericOpenAi = new GenericOpenAiLLM();
      const result = _genericOpenAi["generateContent"]({
        userPrompt: "Hello world",
        attachments: [],
      });

      expect(result).toBe("Hello world");
    });

    it("should return content array with attachments", () => {
      const _genericOpenAi = new GenericOpenAiLLM();
      const attachments: Attachment[] = [
        {
          name: "image1.png",
          contentString: "data:image/png;base64,iVBORw0KGgo...",
          type: "image/png",
          size: 1024,
        },
        {
          name: "image2.jpg",
          contentString: "data:image/jpeg;base64,/9j/4AAQSkZJRg...",
          type: "image/jpeg",
          size: 2048,
        },
      ];

      const result = _genericOpenAi["generateContent"]({
        userPrompt: "Analyze these images",
        attachments,
      });

      expect(result).toEqual([
        { type: "text", text: "Analyze these images" },
        {
          type: "image_url",
          image_url: {
            url: "data:image/png;base64,iVBORw0KGgo...",
            detail: "high",
          },
        },
        {
          type: "image_url",
          image_url: {
            url: "data:image/jpeg;base64,/9j/4AAQSkZJRg...",
            detail: "high",
          },
        },
      ]);
    });
  });

  describe("constructPrompt", () => {
    it("should construct prompt with all components", async () => {
      const _genericOpenAi = new GenericOpenAiLLM();
      const promptArgs: PromptArgs = {
        systemPrompt: "You are a helpful assistant.",
        contextTexts: ["Context 1", "Context 2"],
        chatHistory: [
          { role: "user", content: "Hello" },
          { role: "assistant", content: "Hi there!" },
        ],
        userPrompt: "How are you?",
      };

      const result = await _genericOpenAi.constructPrompt(promptArgs);

      expect(result).toEqual([
        {
          role: "system",
          content:
            "You are a helpful assistant.Context 1\n\n---CONTEXT---\n\nContext 2",
        },
        { role: "user", content: "Hello" },
        { role: "assistant", content: "Hi there!" },
        { role: "user", content: "How are you?" },
      ]);
    });

    it("should handle attachments in prompt", async () => {
      const _genericOpenAi = new GenericOpenAiLLM();
      const attachment: Attachment = {
        name: "test.png",
        contentString: "data:image/png;base64,test",
        type: "image/png",
        size: 100,
      };

      const promptArgs: PromptArgs = {
        systemPrompt: "Analyze images",
        userPrompt: "What's in this image?",
        attachments: [attachment],
      };

      const result = await _genericOpenAi.constructPrompt(promptArgs);

      expect(result[result.length - 1]).toEqual({
        role: "user",
        content: [
          { type: "text", text: "What's in this image?" },
          {
            type: "image_url",
            image_url: {
              url: "data:image/png;base64,test",
              detail: "high",
            },
          },
        ],
      });
    });

    it("should handle empty context texts", async () => {
      const _genericOpenAi = new GenericOpenAiLLM();
      const promptArgs: PromptArgs = {
        systemPrompt: "System prompt",
        contextTexts: [],
        chatHistory: [],
        userPrompt: "User prompt",
      };

      const result = await _genericOpenAi.constructPrompt(promptArgs);

      expect(result).toEqual([
        { role: "system", content: "System prompt" },
        { role: "user", content: "User prompt" },
      ]);
    });

    it("should handle missing optional parameters", async () => {
      const _genericOpenAi = new GenericOpenAiLLM();
      const promptArgs: PromptArgs = {
        userPrompt: "User prompt",
      };

      const result = await _genericOpenAi.constructPrompt(promptArgs);

      expect(result).toEqual([
        { role: "system", content: "" },
        { role: "user", content: "User prompt" },
      ]);
    });
  });

  describe("getChatCompletion", () => {
    it("should return completion response successfully", async () => {
      const mockResponse = {
        choices: [
          {
            message: { content: "Test response" },
          },
        ],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 5,
          total_tokens: 15,
        },
      };

      const _genericOpenAi = new GenericOpenAiLLM();

      // Mock the create function to return a promise
      const createMock = _genericOpenAi["openai"].chat.completions
        .create as jest.Mock;
      createMock.mockResolvedValue(mockResponse);

      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockImplementation(async (promise) => {
        const output = await promise;
        return { output, duration: 1000 };
      });

      const messages: ChatMessage[] = [
        { role: "user", content: "Test message" },
      ];

      const result = await _genericOpenAi.getChatCompletion(messages, {
        temperature: 0.5,
      });

      expect(result).toEqual({
        textResponse: "Test response",
        metrics: {
          prompt_tokens: 10,
          completion_tokens: 5,
          total_tokens: 15,
          outputTps: 5 / 1000,
          duration: 1000,
        },
      });

      expect(
        _genericOpenAi["openai"].chat.completions.create
      ).toHaveBeenCalledWith({
        model: "gpt-4",
        messages: messages,
        temperature: 0.5,
        max_tokens: 2048,
      });
    });

    it("should use default temperature", async () => {
      const mockResponse = {
        choices: [{ message: { content: "Test" } }],
        usage: { prompt_tokens: 1, completion_tokens: 1, total_tokens: 2 },
      };

      const _genericOpenAi = new GenericOpenAiLLM();

      // Mock the create function to return a promise
      const createMock = _genericOpenAi["openai"].chat.completions
        .create as jest.Mock;
      createMock.mockResolvedValue(mockResponse);

      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockImplementation(async (promise) => {
        const output = await promise;
        return { output, duration: 500 };
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await _genericOpenAi.getChatCompletion(messages, {});

      expect(
        _genericOpenAi["openai"].chat.completions.create
      ).toHaveBeenCalledWith(expect.objectContaining({ temperature: 0.7 }));
    });

    it("should return null for null messages", async () => {
      const _genericOpenAi = new GenericOpenAiLLM();
      const result = await _genericOpenAi.getChatCompletion(null, {});
      expect(result).toBeNull();
    });

    it("should return null when no choices in response", async () => {
      const _genericOpenAi = new GenericOpenAiLLM();

      // Mock the create function to return a promise
      const createMock = _genericOpenAi["openai"].chat.completions
        .create as jest.Mock;
      createMock.mockResolvedValue({ choices: [] });

      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockImplementation(async (promise) => {
        const output = await promise;
        return { output, duration: 100 };
      });

      const result = await _genericOpenAi.getChatCompletion(
        [{ role: "user", content: "Test" }],
        {}
      );

      expect(result).toBeNull();
    });

    it("should handle missing usage data", async () => {
      const mockResponse = {
        choices: [{ message: { content: "Test response" } }],
      };

      const _genericOpenAi = new GenericOpenAiLLM();

      // Mock the create function to return a promise
      const createMock = _genericOpenAi["openai"].chat.completions
        .create as jest.Mock;
      createMock.mockResolvedValue(mockResponse);

      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockImplementation(async (promise) => {
        const output = await promise;
        return { output, duration: 1000 };
      });

      const result = await _genericOpenAi.getChatCompletion(
        [{ role: "user", content: "Test" }],
        {}
      );

      expect(result).toEqual({
        textResponse: "Test response",
        metrics: {
          prompt_tokens: 0,
          completion_tokens: 0,
          total_tokens: 0,
          outputTps: 0,
          duration: 1000,
        },
      });
    });

    it("should handle API errors with catch block", async () => {
      const mockCreate = jest
        .fn()
        .mockRejectedValue(new Error("API request failed"));
      const _genericOpenAi = new GenericOpenAiLLM();
      _genericOpenAi["openai"].chat.completions.create = mockCreate;

      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockImplementation(async (promise) => {
        const output = await promise;
        return { output, duration: 100 };
      });

      await expect(
        _genericOpenAi.getChatCompletion(
          [{ role: "user", content: "Test" }],
          {}
        )
      ).rejects.toThrow("API request failed");
    });

    it("should handle empty message content", async () => {
      const mockResponse = {
        choices: [{ message: { content: null } }],
        usage: { prompt_tokens: 1, completion_tokens: 0, total_tokens: 1 },
      };

      const _genericOpenAi = new GenericOpenAiLLM();

      // Mock the create function to return a promise
      const createMock = _genericOpenAi["openai"].chat.completions
        .create as jest.Mock;
      createMock.mockResolvedValue(mockResponse);

      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockImplementation(async (promise) => {
        const output = await promise;
        return { output, duration: 100 };
      });

      const result = await _genericOpenAi.getChatCompletion(
        [{ role: "user", content: "Test" }],
        {}
      );

      expect(result?.textResponse).toBe("");
    });

    it("should handle invalid response format", async () => {
      const _genericOpenAi = new GenericOpenAiLLM();

      // Mock the create function to return a promise
      const createMock = _genericOpenAi["openai"].chat.completions
        .create as jest.Mock;
      createMock.mockResolvedValue("invalid response");

      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockImplementation(async (promise) => {
        const output = await promise;
        return { output, duration: 100 };
      });

      const result = await _genericOpenAi.getChatCompletion(
        [{ role: "user", content: "Test" }],
        {}
      );

      expect(result).toBeNull();
    });
  });

  describe("streamGetChatCompletion", () => {
    it("should stream completion successfully", async () => {
      const _mockStream = { stream: "mock" };
      const mockConvertedStream = { converted: "stream" };

      mockConvertOpenAIStream.mockResolvedValue(mockConvertedStream as any);

      const _genericOpenAi = new GenericOpenAiLLM();
      const messages: ChatMessage[] = [
        { role: "user", content: "Test message" },
      ];

      const result = await _genericOpenAi.streamGetChatCompletion(messages, {
        temperature: 0.8,
      });

      expect(result).toBe(mockConvertedStream);
      expect(
        _genericOpenAi["openai"].chat.completions.create
      ).toHaveBeenCalledWith({
        model: "gpt-4",
        stream: true,
        messages: messages,
        temperature: 0.8,
        max_tokens: 2048,
      });
      expect(mockConvertOpenAIStream).toHaveBeenCalledWith(
        (_genericOpenAi["openai"].chat.completions.create as jest.Mock).mock
          .results[0].value,
        [{ content: "Test message" }]
      );
    });

    it("should use default temperature", async () => {
      mockConvertOpenAIStream.mockResolvedValue({ stream: "mock" } as any);

      const _genericOpenAi = new GenericOpenAiLLM();
      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await _genericOpenAi.streamGetChatCompletion(messages, {});

      expect(
        _genericOpenAi["openai"].chat.completions.create
      ).toHaveBeenCalledWith(expect.objectContaining({ temperature: 0.7 }));
    });

    it("should throw error for null messages", async () => {
      const _genericOpenAi = new GenericOpenAiLLM();

      await expect(
        _genericOpenAi.streamGetChatCompletion(null, {})
      ).rejects.toThrow("No messages provided");
    });

    it("should handle complex message content", async () => {
      mockConvertOpenAIStream.mockResolvedValue({ stream: "mock" } as any);

      const _genericOpenAi = new GenericOpenAiLLM();
      const complexContent = [
        { type: "text", text: "Look at this image" },
        { type: "image_url", image_url: { url: "data:image/png;base64,test" } },
      ];
      const messages: ChatMessage[] = [
        { role: "user", content: complexContent as any },
      ];

      await _genericOpenAi.streamGetChatCompletion(messages, {});

      const createCall = _genericOpenAi["openai"].chat.completions
        .create as jest.Mock;
      expect(createCall).toHaveBeenCalled();

      expect(mockConvertOpenAIStream).toHaveBeenCalledWith(
        createCall.mock.results[0].value,
        [{ content: JSON.stringify(complexContent) }]
      );
    });

    it("should handle stream conversion errors", async () => {
      const error = new Error("Stream conversion failed");
      mockConvertOpenAIStream.mockRejectedValue(error);

      const _genericOpenAi = new GenericOpenAiLLM();

      await expect(
        _genericOpenAi.streamGetChatCompletion(
          [{ role: "user", content: "Test" }],
          {}
        )
      ).rejects.toThrow("Stream conversion failed");
    });
  });

  describe("handleStream", () => {
    it("should handle stream with default handler", async () => {
      const mockResponse = {} as ExpressResponse;
      const mockStream = {} as AsyncIterable<unknown>;
      const mockProps = { prop: "value" };

      mockHandleDefaultStreamResponseV2.mockResolvedValue("stream-result");

      const _genericOpenAi = new GenericOpenAiLLM();
      const result = await _genericOpenAi.handleStream(
        mockResponse,
        mockStream,
        mockProps as any
      );

      expect(result).toBe("stream-result");
      expect(mockHandleDefaultStreamResponseV2).toHaveBeenCalledWith(
        mockResponse,
        mockStream,
        mockProps
      );
    });
  });

  describe("embedTextInput", () => {
    it("should embed text input successfully", async () => {
      const _genericOpenAi = new GenericOpenAiLLM();
      const result = await _genericOpenAi.embedTextInput("Test text");

      expect(result).toEqual([0.1, 0.2, 0.3]);
      expect(_genericOpenAi.embedder.embedTextInput).toHaveBeenCalledWith(
        "Test text"
      );
    });

    it("should handle embedding errors", async () => {
      const error = new Error("Embedding failed");
      const mockEmbedderInstance = {
        embedTextInput: jest.fn().mockRejectedValue(error),
        embedChunks: jest.fn(),
      };
      mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);

      const _genericOpenAi = new GenericOpenAiLLM();

      await expect(_genericOpenAi.embedTextInput("Test text")).rejects.toThrow(
        "Embedding failed"
      );
    });
  });

  describe("embedChunks", () => {
    it("should embed chunks successfully", async () => {
      const _genericOpenAi = new GenericOpenAiLLM();
      const chunks = ["Chunk 1", "Chunk 2"];
      const result = await _genericOpenAi.embedChunks(chunks);

      expect(result).toEqual([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]);
      expect(_genericOpenAi.embedder.embedChunks).toHaveBeenCalledWith(chunks);
    });

    it("should handle empty chunks", async () => {
      const mockEmbedderInstance = {
        embedTextInput: jest.fn(),
        embedChunks: jest.fn().mockResolvedValue(null),
      };
      mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);

      const _genericOpenAi = new GenericOpenAiLLM();
      const result = await _genericOpenAi.embedChunks([]);

      expect(result).toEqual([]);
    });

    it("should handle null result from embedder", async () => {
      const mockEmbedderInstance = {
        embedTextInput: jest.fn(),
        embedChunks: jest.fn().mockResolvedValue(null),
      };
      mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);

      const _genericOpenAi = new GenericOpenAiLLM();
      const result = await _genericOpenAi.embedChunks(["Test"]);

      expect(result).toEqual([]);
    });

    it("should handle embedding errors", async () => {
      const error = new Error("Bulk embedding failed");
      const mockEmbedderInstance = {
        embedTextInput: jest.fn(),
        embedChunks: jest.fn().mockRejectedValue(error),
      };
      mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);

      const _genericOpenAi = new GenericOpenAiLLM();

      await expect(_genericOpenAi.embedChunks(["Test"])).rejects.toThrow(
        "Bulk embedding failed"
      );
    });
  });

  describe("compressMessages", () => {
    it("should compress messages successfully", async () => {
      const { messageArrayCompressor } = require("../../../helpers/chat");
      const compressedMessages = [{ role: "system", content: "Compressed" }];
      messageArrayCompressor.mockResolvedValue(compressedMessages);

      const _genericOpenAi = new GenericOpenAiLLM();
      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        userPrompt: "User",
      };
      const rawHistory: ChatMessage[] = [{ role: "user", content: "History" }];

      const result = await _genericOpenAi.compressMessages(
        promptArgs,
        rawHistory
      );

      expect(result).toEqual(compressedMessages);
      expect(messageArrayCompressor).toHaveBeenCalledWith(
        _genericOpenAi,
        expect.arrayContaining([
          { role: "system", content: "System" },
          { role: "user", content: "User" },
        ]),
        rawHistory
      );
    });

    it("should handle empty parameters", async () => {
      const { messageArrayCompressor } = require("../../../helpers/chat");
      messageArrayCompressor.mockResolvedValue([]);

      const _genericOpenAi = new GenericOpenAiLLM();
      const result = await _genericOpenAi.compressMessages();

      expect(result).toEqual([]);
      expect(messageArrayCompressor).toHaveBeenCalledWith(
        _genericOpenAi,
        expect.arrayContaining([
          { role: "system", content: "" },
          { role: "user", content: "" },
        ]),
        []
      );
    });

    it("should handle compression errors", async () => {
      const { messageArrayCompressor } = require("../../../helpers/chat");
      const error = new Error("Compression failed");
      messageArrayCompressor.mockRejectedValue(error);

      const _genericOpenAi = new GenericOpenAiLLM();

      await expect(_genericOpenAi.compressMessages({}, [])).rejects.toThrow(
        "Compression failed"
      );
    });
  });

  describe("Edge cases and error scenarios", () => {
    it("should handle network errors during API calls", async () => {
      const networkError = new Error("Network error: ECONNREFUSED");
      const mockCreate = jest.fn().mockRejectedValue(networkError);
      const _genericOpenAi = new GenericOpenAiLLM();
      _genericOpenAi["openai"].chat.completions.create = mockCreate;

      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockImplementation(async (promise) => {
        const output = await promise;
        return { output, duration: 100 };
      });

      await expect(
        _genericOpenAi.getChatCompletion(
          [{ role: "user", content: "Test" }],
          {}
        )
      ).rejects.toThrow("Network error: ECONNREFUSED");
    });

    it("should handle timeout errors", async () => {
      const timeoutError = new Error("Request timeout");
      const mockCreate = jest.fn().mockRejectedValue(timeoutError);
      const _genericOpenAi = new GenericOpenAiLLM();
      _genericOpenAi["openai"].chat.completions.create = mockCreate;

      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockImplementation(async (promise) => {
        const output = await promise;
        return { output, duration: 100 };
      });

      await expect(
        _genericOpenAi.getChatCompletion(
          [{ role: "user", content: "Test" }],
          {}
        )
      ).rejects.toThrow("Request timeout");
    });

    it("should handle rate limiting", async () => {
      const rateLimitError = new Error("Rate limit exceeded");
      rateLimitError.name = "RateLimitError";
      const mockCreate = jest.fn().mockRejectedValue(rateLimitError);
      const _genericOpenAi = new GenericOpenAiLLM();
      _genericOpenAi["openai"].chat.completions.create = mockCreate;

      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockImplementation(async (promise) => {
        const output = await promise;
        return { output, duration: 100 };
      });

      await expect(
        _genericOpenAi.getChatCompletion(
          [{ role: "user", content: "Test" }],
          {}
        )
      ).rejects.toThrow("Rate limit exceeded");
    });

    it("should handle very large token limits", () => {
      process.env.GENERIC_OPEN_AI_MODEL_TOKEN_LIMIT = "128000";
      process.env.GENERIC_OPEN_AI_MAX_TOKENS = "64000";

      const _genericOpenAi = new GenericOpenAiLLM();

      expect(_genericOpenAi.promptWindowLimit()).toBe(128000);
      expect(_genericOpenAi["maxTokens"]).toBe(64000);
      expect(_genericOpenAi.limits).toEqual({
        history: 128000 * 0.15,
        system: 128000 * 0.15,
        user: 128000 * 0.7,
      });
    });

    it("should handle zero token limit", () => {
      process.env.GENERIC_OPEN_AI_MODEL_TOKEN_LIMIT = "0";

      const _genericOpenAi = new GenericOpenAiLLM();

      // Zero is a valid number, so it should return 0, not throw
      expect(_genericOpenAi.promptWindowLimit()).toBe(0);
    });

    it("should handle negative token limit", () => {
      process.env.GENERIC_OPEN_AI_MODEL_TOKEN_LIMIT = "-1";

      const _genericOpenAi = new GenericOpenAiLLM();

      // Negative numbers are valid numbers, so it should return -1, not throw
      expect(_genericOpenAi.promptWindowLimit()).toBe(-1);
    });

    it("should handle extremely long messages", async () => {
      const longMessage = "x".repeat(10000);
      const mockResponse = {
        choices: [{ message: { content: "Response to long message" } }],
        usage: {
          prompt_tokens: 10000,
          completion_tokens: 10,
          total_tokens: 10010,
        },
      };

      const _genericOpenAi = new GenericOpenAiLLM();

      // Mock the create function to return a promise
      const createMock = _genericOpenAi["openai"].chat.completions
        .create as jest.Mock;
      createMock.mockResolvedValue(mockResponse);

      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockImplementation(async (promise) => {
        const output = await promise;
        return { output, duration: 5000 };
      });

      const result = await _genericOpenAi.getChatCompletion(
        [{ role: "user", content: longMessage }],
        {}
      );

      expect(result?.textResponse).toBe("Response to long message");
      expect(result?.metrics?.prompt_tokens).toBe(10000);
    });

    it("should handle malformed environment variables gracefully", () => {
      process.env.GENERIC_OPEN_AI_BASE_PATH = "";

      expect(() => new GenericOpenAiLLM()).toThrow(
        "GenericOpenAI must have a valid base path to use for the api."
      );
    });

    it("should handle whitespace-only model names", () => {
      process.env.GENERIC_OPEN_AI_MODEL_PREF = "   ";

      // Whitespace-only string is still truthy, so it won't throw in constructor
      const _genericOpenAi = new GenericOpenAiLLM();
      expect(_genericOpenAi.model).toBe("   ");
    });

    it("should properly handle API key with special characters", () => {
      process.env.GENERIC_OPEN_AI_API_KEY =
        "sk-proj-!@#$%^&*()_+-=[]{}|;:,.<>?";

      const _genericOpenAi = new GenericOpenAiLLM();

      expect(mockOpenAI).toHaveBeenCalledWith({
        baseURL: "http://localhost:8080/v1",
        apiKey: "sk-proj-!@#$%^&*()_+-=[]{}|;:,.<>?",
      });
    });

    it("should handle concurrent requests", async () => {
      const mockResponses = [
        {
          choices: [{ message: { content: "Response 1" } }],
          usage: { total_tokens: 10 },
        },
        {
          choices: [{ message: { content: "Response 2" } }],
          usage: { total_tokens: 20 },
        },
        {
          choices: [{ message: { content: "Response 3" } }],
          usage: { total_tokens: 30 },
        },
      ];

      const _genericOpenAi = new GenericOpenAiLLM();

      // Mock the create function to return different responses
      let callCount = 0;
      const createMock = _genericOpenAi["openai"].chat.completions
        .create as jest.Mock;
      createMock.mockImplementation(() => {
        const response = mockResponses[callCount];
        callCount++;
        return Promise.resolve(response);
      });

      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockImplementation(async (promise) => {
        const output = await promise;
        return { output, duration: 100 };
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const [result1, result2, result3] = await Promise.all([
        _genericOpenAi.getChatCompletion(messages, {}),
        _genericOpenAi.getChatCompletion(messages, {}),
        _genericOpenAi.getChatCompletion(messages, {}),
      ]);

      expect(result1?.textResponse).toBe("Response 1");
      expect(result2?.textResponse).toBe("Response 2");
      expect(result3?.textResponse).toBe("Response 3");
    });

    it("should handle multiple attachments of various types", async () => {
      const _genericOpenAi = new GenericOpenAiLLM();
      const attachments: Attachment[] = [
        {
          name: "image1.png",
          contentString: "data:image/png;base64,test1",
          type: "image/png",
          size: 1000,
        },
        {
          name: "image2.jpg",
          contentString: "data:image/jpeg;base64,test2",
          type: "image/jpeg",
          size: 2000,
        },
        {
          name: "image3.gif",
          contentString: "data:image/gif;base64,test3",
          type: "image/gif",
          size: 3000,
        },
      ];

      const promptArgs: PromptArgs = {
        userPrompt: "Analyze all images",
        attachments,
      };

      const result = await _genericOpenAi.constructPrompt(promptArgs);
      const lastMessage = result[result.length - 1];

      expect(lastMessage.content).toHaveLength(4); // 1 text + 3 images
      expect(lastMessage.content).toEqual(
        expect.arrayContaining([
          { type: "text", text: "Analyze all images" },
          expect.objectContaining({ type: "image_url" }),
        ])
      );
    });
  });
});
