import { LiteLLM } from "../index";
import { OpenAI as OpenAIApi } from "openai";
import { NativeEmbedder } from "../../../EmbeddingEngines/native";
import { LLMPerformanceMonitor } from "../../../helpers/chat/LLMPerformanceMonitor";
import { handleDefaultStreamResponseV2 } from "../../../helpers/chat/responses";
import { convertOpenAIStream } from "../../streamConverters";
import type { Response as ExpressResponse } from "express";
import type {
  ChatMessage,
  PromptArgs,
  EmbeddingEngine,
} from "../../../../types/ai-providers";

// Mock dependencies
jest.mock("openai");
jest.mock("../../../EmbeddingEngines/native");
jest.mock("../../../helpers/chat/LLMPerformanceMonitor");
jest.mock("../../../helpers/chat/responses");
jest.mock("../../streamConverters");
jest.mock("../../../helpers", () => ({
  formatContextTexts: jest.fn().mockImplementation(async (texts: string[]) => {
    return texts.join("\n\n---CONTEXT---\n\n");
  }),
}));
jest.mock("../../../helpers/chat", () => ({
  messageArrayCompressor: jest
    .fn()
    .mockImplementation(async (provider, messages, _rawHistory) => {
      return messages;
    }),
}));

describe("LiteLLM", () => {
  const mockOpenAI = OpenAIApi as jest.MockedClass<typeof OpenAIApi>;
  const mockNativeEmbedder = NativeEmbedder as jest.MockedClass<
    typeof NativeEmbedder
  >;
  const mockLLMPerformanceMonitor = LLMPerformanceMonitor as jest.MockedClass<
    typeof LLMPerformanceMonitor
  >;
  const mockConvertOpenAIStream = convertOpenAIStream as jest.MockedFunction<
    typeof convertOpenAIStream
  >;
  const mockHandleDefaultStreamResponseV2 =
    handleDefaultStreamResponseV2 as jest.MockedFunction<
      typeof handleDefaultStreamResponseV2
    >;

  let originalEnv: typeof process.env;
  let consoleLogSpy: jest.SpyInstance;

  beforeEach(() => {
    // Store original environment
    originalEnv = { ...process.env };

    // Set up required environment variables
    process.env.LITE_LLM_BASE_PATH = "http://localhost:8000";
    process.env.LITE_LLM_API_KEY = "test-api-key";
    process.env.LITE_LLM_MODEL_PREF = "gpt-3.5-turbo";
    process.env.LITE_LLM_MODEL_TOKEN_LIMIT = "4096";

    // Clear all mocks
    jest.clearAllMocks();

    // Spy on console.log
    consoleLogSpy = jest.spyOn(console, "log").mockImplementation();

    // Mock OpenAI instance
    mockOpenAI.mockClear();
    mockOpenAI.prototype.chat = {
      completions: {
        create: jest.fn(),
      },
    } as any;

    // Mock NativeEmbedder
    mockNativeEmbedder.mockClear();
    const mockEmbedderInstance = {
      embedTextInput: jest.fn().mockResolvedValue([0.1, 0.2, 0.3]),
      embedChunks: jest.fn().mockResolvedValue([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]),
    };
    mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
    consoleLogSpy.mockRestore();
  });

  describe("constructor", () => {
    it("should initialize with default settings", () => {
      const litellm = new LiteLLM();

      expect(mockOpenAI).toHaveBeenCalledWith({
        baseURL: "http://localhost:8000",
        apiKey: "test-api-key",
      });
      expect(litellm.model).toBe("gpt-3.5-turbo");
      expect(litellm.limits).toEqual({
        history: 4096 * 0.15,
        system: 4096 * 0.15,
        user: 4096 * 0.7,
      });
      expect(litellm.defaultTemp).toBe(0.7);
      expect(consoleLogSpy).toHaveBeenCalledWith(
        "\x1b[36m[LiteLLM]\x1b[0m Inference API: http://localhost:8000 Model: gpt-3.5-turbo"
      );
    });

    it("should use custom embedder when provided", () => {
      const customEmbedder = {} as EmbeddingEngine;
      const litellm = new LiteLLM(customEmbedder);

      expect(litellm.embedder).toBe(customEmbedder);
    });

    it("should use model preference when provided", () => {
      const litellm = new LiteLLM(null, "gpt-4");

      expect(litellm.model).toBe("gpt-4");
    });

    it("should use settings suffix when provided", () => {
      process.env.LITE_LLM_BASE_PATH_TEST = "http://test-server:8000";
      process.env.LITE_LLM_API_KEY_TEST = "test-suffix-key";
      process.env.LITE_LLM_MODEL_PREF_TEST = "gpt-4-turbo";
      process.env.LITE_LLM_MODEL_TOKEN_LIMIT_TEST = "8192";

      const litellm = new LiteLLM(null, null, "_TEST");

      expect(mockOpenAI).toHaveBeenCalledWith({
        baseURL: "http://test-server:8000",
        apiKey: "test-suffix-key",
      });
      expect(litellm.model).toBe("gpt-4-turbo");
      expect(litellm.limits).toEqual({
        history: 8192 * 0.15,
        system: 8192 * 0.15,
        user: 8192 * 0.7,
      });
    });

    it("should throw error when base path is missing", () => {
      delete process.env.LITE_LLM_BASE_PATH;

      expect(() => new LiteLLM()).toThrow("LiteLLM base path is required");
    });

    it("should throw error when model is missing", () => {
      delete process.env.LITE_LLM_MODEL_PREF;

      expect(() => new LiteLLM()).toThrow("LiteLLM model is required");
    });

    it("should handle missing API key", () => {
      delete process.env.LITE_LLM_API_KEY;

      const _litellm = new LiteLLM();

      expect(mockOpenAI).toHaveBeenCalledWith({
        baseURL: "http://localhost:8000",
        apiKey: undefined,
      });
    });

    it("should use default max tokens when not specified", () => {
      delete process.env.LITE_LLM_MODEL_TOKEN_LIMIT;

      const litellm = new LiteLLM();

      // Default is 1024 when not specified
      expect(litellm["maxTokens"]).toBe(1024);
    });
  });

  describe("log", () => {
    it("should log with correct formatting", () => {
      const litellm = new LiteLLM();
      litellm.log("Test message", "arg1", 123);

      expect(consoleLogSpy).toHaveBeenCalledWith(
        "\x1b[36m[LiteLLM]\x1b[0m Test message",
        "arg1",
        123
      );
    });
  });

  describe("streamingEnabled", () => {
    it("should return true since streamGetChatCompletion is defined", () => {
      const litellm = new LiteLLM();
      expect(litellm.streamingEnabled()).toBe(true);
    });
  });

  describe("promptWindowLimit methods", () => {
    it("should return static prompt window limit", () => {
      expect(LiteLLM.promptWindowLimit()).toBe(4096);
    });

    it("should throw error for invalid static limit", () => {
      process.env.LITE_LLM_MODEL_TOKEN_LIMIT = "invalid";
      expect(() => LiteLLM.promptWindowLimit()).toThrow(
        "No token context limit was set."
      );
    });

    it("should return instance prompt window limit", () => {
      const litellm = new LiteLLM();
      expect(litellm.promptWindowLimit()).toBe(4096);
    });

    it("should return instance prompt window limit with suffix", () => {
      // Need to also set the base path and model for the suffix
      process.env.LITE_LLM_BASE_PATH_CUSTOM = "http://custom-server:8000";
      process.env.LITE_LLM_MODEL_PREF_CUSTOM = "custom-model";
      process.env.LITE_LLM_MODEL_TOKEN_LIMIT_CUSTOM = "8192";
      const litellm = new LiteLLM(null, null, "_CUSTOM");
      expect(litellm.promptWindowLimit()).toBe(8192);
    });

    it("should throw error for invalid instance limit", () => {
      const litellm = new LiteLLM();
      // Override the env var after instance creation to test the method
      const originalLimit = process.env.LITE_LLM_MODEL_TOKEN_LIMIT;
      process.env.LITE_LLM_MODEL_TOKEN_LIMIT = "invalid";
      expect(() => litellm.promptWindowLimit()).toThrow(
        "No token context limit was set."
      );
      process.env.LITE_LLM_MODEL_TOKEN_LIMIT = originalLimit;
    });

    it("customPromptWindowLimit should return same as promptWindowLimit", () => {
      const litellm = new LiteLLM();
      expect(litellm.customPromptWindowLimit()).toBe(
        litellm.promptWindowLimit()
      );
    });
  });

  describe("isValidChatCompletionModel", () => {
    it("should always return true", async () => {
      const litellm = new LiteLLM();
      expect(await litellm.isValidChatCompletionModel()).toBe(true);
      expect(await litellm.isValidChatCompletionModel("any-model")).toBe(true);
    });
  });

  describe("constructPrompt", () => {
    it("should construct prompt with all components", async () => {
      const litellm = new LiteLLM();
      const promptArgs: PromptArgs = {
        systemPrompt: "You are a helpful assistant.",
        contextTexts: ["Context 1", "Context 2"],
        chatHistory: [
          { role: "user", content: "Hello" },
          { role: "assistant", content: "Hi there!" },
        ],
        userPrompt: "How are you?",
      };

      const result = await litellm.constructPrompt(promptArgs);

      expect(result).toEqual([
        {
          role: "system",
          content:
            "You are a helpful assistant.Context 1\n\n---CONTEXT---\n\nContext 2",
        },
        { role: "user", content: "Hello" },
        { role: "assistant", content: "Hi there!" },
        { role: "user", content: "How are you?" },
      ]);
    });

    it("should handle empty context texts", async () => {
      const litellm = new LiteLLM();
      const promptArgs: PromptArgs = {
        systemPrompt: "System prompt",
        contextTexts: [],
        chatHistory: [],
        userPrompt: "User prompt",
      };

      const result = await litellm.constructPrompt(promptArgs);

      expect(result).toEqual([
        { role: "system", content: "System prompt" },
        { role: "user", content: "User prompt" },
      ]);
    });

    it("should handle missing optional parameters", async () => {
      const litellm = new LiteLLM();
      const promptArgs: PromptArgs = {
        userPrompt: "User prompt",
      };

      const result = await litellm.constructPrompt(promptArgs);

      expect(result).toEqual([
        { role: "system", content: "" },
        { role: "user", content: "User prompt" },
      ]);
    });
  });

  describe("getChatCompletion", () => {
    it("should return completion response successfully", async () => {
      const mockResponse = {
        choices: [
          {
            message: { content: "Test response" },
          },
        ],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 5,
          total_tokens: 15,
        },
      };

      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockResolvedValue({
        output: mockResponse,
        duration: 1000,
      });

      const litellm = new LiteLLM();
      const messages: ChatMessage[] = [
        { role: "user", content: "Test message" },
      ];

      const result = await litellm.getChatCompletion(messages, {
        temperature: 0.5,
      });

      expect(result).toEqual({
        textResponse: "Test response",
        metrics: {
          prompt_tokens: 10,
          completion_tokens: 5,
          total_tokens: 15,
          outputTps: 5 / 1000,
          duration: 1000,
        },
      });

      expect(
        mockLLMPerformanceMonitor.measureAsyncFunction
      ).toHaveBeenCalledWith(
        (litellm["openai"].chat.completions.create as jest.Mock).mock.results[0]
          .value
      );
      expect(litellm["openai"].chat.completions.create).toHaveBeenCalledWith({
        model: "gpt-3.5-turbo",
        messages: messages,
        temperature: 0.5,
        max_tokens: 4096,
      });
    });

    it("should use default temperature", async () => {
      const mockResponse = {
        choices: [{ message: { content: "Test" } }],
        usage: { prompt_tokens: 1, completion_tokens: 1, total_tokens: 2 },
      };

      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockResolvedValue({
        output: mockResponse,
        duration: 500,
      });

      const litellm = new LiteLLM();
      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await litellm.getChatCompletion(messages, {});

      expect(litellm["openai"].chat.completions.create).toHaveBeenCalledWith(
        expect.objectContaining({ temperature: 0.7 })
      );
    });

    it("should return null for null messages", async () => {
      const litellm = new LiteLLM();
      const result = await litellm.getChatCompletion(null, {});
      expect(result).toBeNull();
    });

    it("should return null when no choices in response", async () => {
      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockResolvedValue({
        output: { choices: [] },
        duration: 100,
      });

      const litellm = new LiteLLM();
      const result = await litellm.getChatCompletion(
        [{ role: "user", content: "Test" }],
        {}
      );

      expect(result).toBeNull();
    });

    it("should handle missing usage data", async () => {
      const mockResponse = {
        choices: [{ message: { content: "Test response" } }],
      };

      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockResolvedValue({
        output: mockResponse,
        duration: 1000,
      });

      const litellm = new LiteLLM();
      const result = await litellm.getChatCompletion(
        [{ role: "user", content: "Test" }],
        {}
      );

      expect(result).toEqual({
        textResponse: "Test response",
        metrics: {
          prompt_tokens: 0,
          completion_tokens: 0,
          total_tokens: 0,
          outputTps: 0,
          duration: 1000,
        },
      });
    });

    it("should handle API errors", async () => {
      const error = new Error("API request failed");
      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockRejectedValue(error);

      const litellm = new LiteLLM();

      await expect(
        litellm.getChatCompletion([{ role: "user", content: "Test" }], {})
      ).rejects.toThrow("API request failed");
    });

    it("should handle empty message content", async () => {
      const mockResponse = {
        choices: [{ message: { content: null } }],
        usage: { prompt_tokens: 1, completion_tokens: 0, total_tokens: 1 },
      };

      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockResolvedValue({
        output: mockResponse,
        duration: 100,
      });

      const litellm = new LiteLLM();
      const result = await litellm.getChatCompletion(
        [{ role: "user", content: "Test" }],
        {}
      );

      expect(result?.textResponse).toBe("");
    });
  });

  describe("streamGetChatCompletion", () => {
    it("should stream completion successfully", async () => {
      const _mockStream = { stream: "mock" };
      const mockConvertedStream = { converted: "stream" };

      mockConvertOpenAIStream.mockResolvedValue(mockConvertedStream as any);

      const litellm = new LiteLLM();
      const messages: ChatMessage[] = [
        { role: "user", content: "Test message" },
      ];

      const result = await litellm.streamGetChatCompletion(messages, {
        temperature: 0.8,
      });

      expect(result).toBe(mockConvertedStream);
      expect(litellm["openai"].chat.completions.create).toHaveBeenCalledWith({
        model: "gpt-3.5-turbo",
        stream: true,
        messages: messages,
        temperature: 0.8,
        max_tokens: 4096,
      });
      expect(mockConvertOpenAIStream).toHaveBeenCalledWith(
        (litellm["openai"].chat.completions.create as jest.Mock).mock.results[0]
          .value,
        [{ content: "Test message" }]
      );
    });

    it("should use default temperature", async () => {
      mockConvertOpenAIStream.mockResolvedValue({ stream: "mock" } as any);

      const litellm = new LiteLLM();
      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await litellm.streamGetChatCompletion(messages, {});

      expect(litellm["openai"].chat.completions.create).toHaveBeenCalledWith(
        expect.objectContaining({ temperature: 0.7 })
      );
    });

    it("should throw error for null messages", async () => {
      const litellm = new LiteLLM();

      await expect(litellm.streamGetChatCompletion(null, {})).rejects.toThrow(
        "No messages provided"
      );
    });

    it("should handle complex message content", async () => {
      mockConvertOpenAIStream.mockResolvedValue({ stream: "mock" } as any);

      const litellm = new LiteLLM();
      const complexContent = { type: "text", text: "Complex content" };
      const messages: ChatMessage[] = [
        { role: "user", content: complexContent as any },
      ];

      await litellm.streamGetChatCompletion(messages, {});

      // The create() call returns a promise, need to check what was passed to convertOpenAIStream
      const createCall = litellm["openai"].chat.completions.create as jest.Mock;
      expect(createCall).toHaveBeenCalled();

      expect(mockConvertOpenAIStream).toHaveBeenCalledWith(
        createCall.mock.results[0].value,
        [{ content: JSON.stringify(complexContent) }]
      );
    });

    it("should handle stream conversion errors", async () => {
      const error = new Error("Stream conversion failed");
      mockConvertOpenAIStream.mockRejectedValue(error);

      const litellm = new LiteLLM();

      await expect(
        litellm.streamGetChatCompletion([{ role: "user", content: "Test" }], {})
      ).rejects.toThrow("Stream conversion failed");
    });
  });

  describe("handleStream", () => {
    it("should handle stream with default handler", async () => {
      const mockResponse = {} as ExpressResponse;
      const mockStream = {} as AsyncIterable<unknown>;
      const mockProps = { prop: "value" };

      mockHandleDefaultStreamResponseV2.mockResolvedValue("stream-result");

      const litellm = new LiteLLM();
      const result = await litellm.handleStream(
        mockResponse,
        mockStream,
        mockProps as any
      );

      expect(result).toBe("stream-result");
      expect(mockHandleDefaultStreamResponseV2).toHaveBeenCalledWith(
        mockResponse,
        mockStream,
        mockProps
      );
    });
  });

  describe("embedTextInput", () => {
    it("should embed text input successfully", async () => {
      const litellm = new LiteLLM();
      const result = await litellm.embedTextInput("Test text");

      expect(result).toEqual([0.1, 0.2, 0.3]);
      expect(litellm.embedder.embedTextInput).toHaveBeenCalledWith("Test text");
    });

    it("should handle embedding errors", async () => {
      const error = new Error("Embedding failed");
      const mockEmbedderInstance = {
        embedTextInput: jest.fn().mockRejectedValue(error),
        embedChunks: jest.fn(),
      };
      mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);

      const litellm = new LiteLLM();

      await expect(litellm.embedTextInput("Test text")).rejects.toThrow(
        "Embedding failed"
      );
    });
  });

  describe("embedChunks", () => {
    it("should embed chunks successfully", async () => {
      const litellm = new LiteLLM();
      const chunks = ["Chunk 1", "Chunk 2"];
      const result = await litellm.embedChunks(chunks);

      expect(result).toEqual([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]);
      expect(litellm.embedder.embedChunks).toHaveBeenCalledWith(chunks);
    });

    it("should handle empty chunks", async () => {
      const mockEmbedderInstance = {
        embedTextInput: jest.fn(),
        embedChunks: jest.fn().mockResolvedValue(null),
      };
      mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);

      const litellm = new LiteLLM();
      const result = await litellm.embedChunks([]);

      expect(result).toEqual([]);
    });

    it("should handle null result from embedder", async () => {
      const mockEmbedderInstance = {
        embedTextInput: jest.fn(),
        embedChunks: jest.fn().mockResolvedValue(null),
      };
      mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);

      const litellm = new LiteLLM();
      const result = await litellm.embedChunks(["Test"]);

      expect(result).toEqual([]);
    });

    it("should handle embedding errors", async () => {
      const error = new Error("Bulk embedding failed");
      const mockEmbedderInstance = {
        embedTextInput: jest.fn(),
        embedChunks: jest.fn().mockRejectedValue(error),
      };
      mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);

      const litellm = new LiteLLM();

      await expect(litellm.embedChunks(["Test"])).rejects.toThrow(
        "Bulk embedding failed"
      );
    });
  });

  describe("compressMessages", () => {
    it("should compress messages successfully", async () => {
      const { messageArrayCompressor } = require("../../../helpers/chat");
      const compressedMessages = [{ role: "system", content: "Compressed" }];
      messageArrayCompressor.mockResolvedValue(compressedMessages);

      const litellm = new LiteLLM();
      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        userPrompt: "User",
      };
      const rawHistory: ChatMessage[] = [{ role: "user", content: "History" }];

      const result = await litellm.compressMessages(promptArgs, rawHistory);

      expect(result).toEqual(compressedMessages);
      expect(messageArrayCompressor).toHaveBeenCalledWith(
        litellm,
        expect.arrayContaining([
          { role: "system", content: "System" },
          { role: "user", content: "User" },
        ]),
        rawHistory
      );
    });

    it("should handle empty parameters", async () => {
      const { messageArrayCompressor } = require("../../../helpers/chat");
      messageArrayCompressor.mockResolvedValue([]);

      const litellm = new LiteLLM();
      const result = await litellm.compressMessages();

      expect(result).toEqual([]);
      expect(messageArrayCompressor).toHaveBeenCalledWith(
        litellm,
        expect.arrayContaining([
          { role: "system", content: "" },
          { role: "user", content: "" },
        ]),
        []
      );
    });

    it("should handle compression errors", async () => {
      const { messageArrayCompressor } = require("../../../helpers/chat");
      const error = new Error("Compression failed");
      messageArrayCompressor.mockRejectedValue(error);

      const litellm = new LiteLLM();

      await expect(litellm.compressMessages({}, [])).rejects.toThrow(
        "Compression failed"
      );
    });
  });

  describe("Edge cases and error scenarios", () => {
    it("should handle network errors during API calls", async () => {
      const networkError = new Error("Network error: ECONNREFUSED");
      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockRejectedValue(networkError);

      const litellm = new LiteLLM();

      await expect(
        litellm.getChatCompletion([{ role: "user", content: "Test" }], {})
      ).rejects.toThrow("Network error: ECONNREFUSED");
    });

    it("should handle timeout errors", async () => {
      const timeoutError = new Error("Request timeout");
      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockRejectedValue(timeoutError);

      const litellm = new LiteLLM();

      await expect(
        litellm.getChatCompletion([{ role: "user", content: "Test" }], {})
      ).rejects.toThrow("Request timeout");
    });

    it("should handle rate limiting", async () => {
      const rateLimitError = new Error("Rate limit exceeded");
      rateLimitError.name = "RateLimitError";
      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockRejectedValue(rateLimitError);

      const litellm = new LiteLLM();

      await expect(
        litellm.getChatCompletion([{ role: "user", content: "Test" }], {})
      ).rejects.toThrow("Rate limit exceeded");
    });

    it("should handle invalid API responses", async () => {
      const invalidResponse = {
        // Missing choices array
        usage: { prompt_tokens: 1, completion_tokens: 1, total_tokens: 2 },
      };

      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockResolvedValue({
        output: invalidResponse,
        duration: 100,
      });

      const litellm = new LiteLLM();
      const result = await litellm.getChatCompletion(
        [{ role: "user", content: "Test" }],
        {}
      );

      expect(result).toBeNull();
    });

    it("should handle very large token limits", () => {
      process.env.LITE_LLM_MODEL_TOKEN_LIMIT = "128000";

      const litellm = new LiteLLM();

      expect(litellm.promptWindowLimit()).toBe(128000);
      expect(litellm.limits).toEqual({
        history: 128000 * 0.15,
        system: 128000 * 0.15,
        user: 128000 * 0.7,
      });
    });

    it("should handle zero token limit", () => {
      process.env.LITE_LLM_MODEL_TOKEN_LIMIT = "0";

      const litellm = new LiteLLM();

      // Zero is a valid number, so it should return 0, not throw
      expect(litellm.promptWindowLimit()).toBe(0);
    });

    it("should handle negative token limit", () => {
      process.env.LITE_LLM_MODEL_TOKEN_LIMIT = "-1";

      const litellm = new LiteLLM();

      // Negative numbers are valid numbers, so it should return -1, not throw
      expect(litellm.promptWindowLimit()).toBe(-1);
    });

    it("should handle extremely long messages", async () => {
      const longMessage = "x".repeat(10000);
      const mockResponse = {
        choices: [{ message: { content: "Response to long message" } }],
        usage: {
          prompt_tokens: 10000,
          completion_tokens: 10,
          total_tokens: 10010,
        },
      };

      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockResolvedValue({
        output: mockResponse,
        duration: 5000,
      });

      const litellm = new LiteLLM();
      const result = await litellm.getChatCompletion(
        [{ role: "user", content: longMessage }],
        {}
      );

      expect(result?.textResponse).toBe("Response to long message");
      expect(result?.metrics?.prompt_tokens).toBe(10000);
    });

    it("should handle malformed environment variables gracefully", () => {
      process.env.LITE_LLM_BASE_PATH = "";

      expect(() => new LiteLLM()).toThrow("LiteLLM base path is required");
    });

    it("should handle whitespace-only model names", () => {
      process.env.LITE_LLM_MODEL_PREF = "   ";

      // Whitespace-only string is still truthy, so it won't throw in constructor
      // The model will be "   " which might cause issues later but doesn't throw here
      const litellm = new LiteLLM();
      expect(litellm.model).toBe("   ");
    });

    it("should properly handle API key with special characters", () => {
      process.env.LITE_LLM_API_KEY = "sk-proj-!@#$%^&*()_+-=[]{}|;:,.<>?";

      const _litellm = new LiteLLM();

      expect(mockOpenAI).toHaveBeenCalledWith({
        baseURL: "http://localhost:8000",
        apiKey: "sk-proj-!@#$%^&*()_+-=[]{}|;:,.<>?",
      });
    });

    it("should handle concurrent requests", async () => {
      const mockResponses = [
        {
          choices: [{ message: { content: "Response 1" } }],
          usage: { total_tokens: 10 },
        },
        {
          choices: [{ message: { content: "Response 2" } }],
          usage: { total_tokens: 20 },
        },
        {
          choices: [{ message: { content: "Response 3" } }],
          usage: { total_tokens: 30 },
        },
      ];

      let callCount = 0;
      (
        mockLLMPerformanceMonitor.measureAsyncFunction as jest.Mock
      ).mockImplementation(() => {
        const response = mockResponses[callCount];
        callCount++;
        return Promise.resolve({ output: response, duration: 100 });
      });

      const litellm = new LiteLLM();
      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const [result1, result2, result3] = await Promise.all([
        litellm.getChatCompletion(messages, {}),
        litellm.getChatCompletion(messages, {}),
        litellm.getChatCompletion(messages, {}),
      ]);

      expect(result1?.textResponse).toBe("Response 1");
      expect(result2?.textResponse).toBe("Response 2");
      expect(result3?.textResponse).toBe("Response 3");
    });
  });
});
