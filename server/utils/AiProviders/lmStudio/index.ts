import { OpenAI as OpenA<PERSON><PERSON> } from "openai";
import type { ChatCompletionMessageParam } from "openai/resources/chat/completions";
import { NativeEmbedder } from "../../EmbeddingEngines/native";
import {
  LLMPerformanceMonitor,
  type OpenAICompatibleStream as LLMPerfOpenAICompatibleStream,
} from "../../helpers/chat/LLMPerformanceMonitor";
import { handleDefaultStreamResponseV2 } from "../../helpers/chat/responses";
import type { Response as ExpressResponse } from "express";
import { formatContextTexts } from "../../helpers";
import {
  LLMProvider,
  TokenLimits,
  ChatMessage,
  PromptArgs,
  CompletionOptions,
  CompletionResponse,
  StreamResponseProps,
  EmbeddingEngine,
  LLMStreamResponse,
} from "../../../types/ai-providers";

export class LMStudioLLM implements LLMProvider {
  private openai: OpenAIApi;
  public model: string;
  public limits: TokenLimits;
  public embedder: EmbeddingEngine;
  public defaultTemp: number;
  private settings_suffix: string | null;

  constructor(
    embedder: EmbeddingEngine | null = null,
    modelPreference: string | null = null,
    settings_suffix: string | null = null
  ) {
    this.settings_suffix = settings_suffix;
    if (!process.env[`LMSTUDIO_BASE_PATH${this.settings_suffix || ""}`])
      throw new Error("No LMStudio Base Path was set.");

    this.openai = new OpenAIApi({
      baseURL: process.env[`LMSTUDIO_BASE_PATH${this.settings_suffix || ""}`],
      apiKey: "lm-studio-dummy-key", // LMStudio doesn't validate API keys but OpenAI client requires one
    });
    this.model =
      modelPreference ||
      process.env[`LMSTUDIO_MODEL_PREF${this.settings_suffix || ""}`] ||
      "";
    this.limits = {
      history: this.promptWindowLimit() * 0.15,
      system: this.promptWindowLimit() * 0.15,
      user: this.promptWindowLimit() * 0.7,
    };

    this.embedder = embedder ?? new NativeEmbedder();
    this.defaultTemp = 0.7;
  }

  private async appendContext(contextTexts: string[] = []): Promise<string> {
    if (!contextTexts || !contextTexts.length) return "";
    return await formatContextTexts(contextTexts);
  }

  streamingEnabled(): boolean {
    return "streamGetChatCompletion" in this;
  }

  static promptWindowLimit(_modelName?: string): number {
    const limit = process.env.LMSTUDIO_MODEL_TOKEN_LIMIT || "4096";
    if (!limit || isNaN(Number(limit)))
      throw new Error("No LMStudio token context limit was set.");
    return Number(limit);
  }

  promptWindowLimit(): number {
    const limit =
      process.env[`LMSTUDIO_MODEL_TOKEN_LIMIT${this.settings_suffix || ""}`] ||
      "4096";
    if (!limit || isNaN(Number(limit)))
      throw new Error("No LMStudio token context limit was set.");
    return Number(limit);
  }

  customPromptWindowLimit(): number {
    return this.promptWindowLimit();
  }

  async isValidChatCompletionModel(_model: string = ""): Promise<boolean> {
    return true;
  }

  async constructPrompt({
    systemPrompt = "",
    contextTexts = [],
    chatHistory = [],
    userPrompt = "",
  }: PromptArgs): Promise<ChatMessage[]> {
    const formattedContext = await this.appendContext(contextTexts);
    const prompt: ChatMessage = {
      role: "system",
      content: `${systemPrompt}${formattedContext}`,
    };
    return [prompt, ...chatHistory, { role: "user", content: userPrompt }];
  }

  async getChatCompletion(
    messages: ChatMessage[] | null,
    { temperature = 0.7 }: CompletionOptions
  ): Promise<CompletionResponse | null> {
    if (!messages) return null;

    const result = await LLMPerformanceMonitor.measureAsyncFunction(
      this.openai.chat.completions.create({
        model: this.model,
        messages: messages as ChatCompletionMessageParam[],
        temperature,
      })
    );

    if (
      !Object.hasOwn(result.output, "choices") ||
      result.output.choices.length === 0
    )
      return null;

    return {
      textResponse: result.output.choices[0].message.content || "",
      metrics: {
        prompt_tokens: result.output?.usage?.prompt_tokens || 0,
        completion_tokens: result.output?.usage?.completion_tokens || 0,
        total_tokens: result.output?.usage?.total_tokens || 0,
        outputTps:
          (result.output?.usage?.completion_tokens || 0) / result.duration,
        duration: result.duration,
      },
    };
  }

  async streamGetChatCompletion(
    messages: ChatMessage[] | null,
    { temperature = 0.7 }: CompletionOptions
  ): Promise<LLMStreamResponse> {
    if (!messages) throw new Error("No messages provided");

    const streamPromise = this.openai.chat.completions.create({
      model: this.model,
      stream: true,
      messages: messages as ChatCompletionMessageParam[],
      temperature,
    });

    const measuredStreamRequest = await LLMPerformanceMonitor.measureStream(
      streamPromise as unknown as Promise<LLMPerfOpenAICompatibleStream>,
      messages.map((msg) => ({
        content:
          typeof msg.content === "string"
            ? msg.content
            : JSON.stringify(msg.content),
        role: msg.role,
      }))
    );
    return {
      stream: measuredStreamRequest,
      endMeasurement: measuredStreamRequest.endMeasurement,
    };
  }

  handleStream(
    response: ExpressResponse,
    stream: AsyncIterable<unknown>,
    responseProps: StreamResponseProps
  ): Promise<string> {
    return handleDefaultStreamResponseV2(response, stream, responseProps);
  }

  async embedTextInput(textInput: string): Promise<number[]> {
    return await this.embedder.embedTextInput(textInput);
  }

  async embedChunks(textChunks: string[] = []): Promise<number[][]> {
    const result = await this.embedder.embedChunks(textChunks);
    return result || [];
  }

  async compressMessages(
    promptArgs: PromptArgs = {},
    rawHistory: ChatMessage[] = []
  ): Promise<ChatMessage[]> {
    const { messageArrayCompressor } = require("../../helpers/chat");
    const messageArray = await this.constructPrompt(promptArgs);
    return await messageArrayCompressor(this, messageArray, rawHistory);
  }
}
