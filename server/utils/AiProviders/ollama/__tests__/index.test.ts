import { OllamaAILLM } from "../index";
import { NativeEmbedder } from "../../../EmbeddingEngines/native";
import { LLMPerformanceMonitor } from "../../../helpers/chat/LLMPerformanceMonitor";
import {
  handleReasoningStream,
  parseReasoningFromResponse,
} from "../../../helpers/chat/reasoningResponses";
import { t } from "../../../i18n";
import { formatContextTexts } from "../../../helpers";
import type { Response as ExpressResponse } from "express";
import type {
  ChatMessage,
  PromptArgs,
  EmbeddingEngine,
  Attachment,
  StreamResponseProps,
} from "../../../../types/ai-providers";

// Mock dependencies
jest.mock("ollama");
jest.mock("../../../EmbeddingEngines/native");
jest.mock("../../../helpers/chat/LLMPerformanceMonitor");
jest.mock("../../../helpers/chat/reasoningResponses", () => ({
  handleReasoningStream: jest.fn().mockResolvedValue("test response"),
  parseReasoningFromResponse: jest.fn().mockImplementation((message) => {
    return message?.content || "";
  }),
}));
jest.mock("../../../i18n", () => ({
  t: jest.fn().mockImplementation((key) => Promise.resolve(key)),
}));
jest.mock("../../../helpers", () => ({
  formatContextTexts: jest.fn().mockImplementation(async (texts: string[]) => {
    return texts.join("\n\n---CONTEXT---\n\n");
  }),
}));
jest.mock("../../../helpers/chat", () => ({
  messageArrayCompressor: jest
    .fn()
    .mockImplementation(async (_provider, messages, _rawHistory) => {
      return messages;
    }),
}));

// Mock global fetch
global.fetch = jest.fn() as jest.MockedFunction<typeof fetch>;

// Mock Ollama client
const mockOllamaClient = {
  chat: jest.fn(),
};

// Import Ollama after mocking
import { Ollama } from "ollama";
const MockOllama = Ollama as jest.MockedClass<typeof Ollama>;

describe("OllamaAILLM", () => {
  const mockNativeEmbedder = NativeEmbedder as jest.MockedClass<
    typeof NativeEmbedder
  >;
  const mockLLMPerformanceMonitor = LLMPerformanceMonitor as jest.MockedClass<
    typeof LLMPerformanceMonitor
  >;
  const mockHandleReasoningStream =
    handleReasoningStream as jest.MockedFunction<typeof handleReasoningStream>;
  const mockParseReasoningFromResponse =
    parseReasoningFromResponse as jest.MockedFunction<
      typeof parseReasoningFromResponse
    >;
  const mockT = t as jest.MockedFunction<typeof t>;
  const mockFormatContextTexts = formatContextTexts as jest.MockedFunction<
    typeof formatContextTexts
  >;
  const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

  let originalEnv: typeof process.env;
  let consoleErrorSpy: jest.SpyInstance;
  let consoleLogSpy: jest.SpyInstance;

  beforeEach(async () => {
    // Store original environment
    originalEnv = { ...process.env };

    // Set up required environment variables
    process.env.OLLAMA_BASE_PATH = "http://127.0.0.1:11434";
    process.env.OLLAMA_MODEL_PREF = "llama3.2:3b";
    process.env.OLLAMA_MODEL_TOKEN_LIMIT = "8192";
    process.env.OLLAMA_PERFORMANCE_MODE = "base";
    process.env.OLLAMA_KEEP_ALIVE_TIMEOUT = "300";

    // Clear all mocks
    jest.clearAllMocks();

    // Spy on console methods
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();
    consoleLogSpy = jest.spyOn(console, "log").mockImplementation();

    // Use fake timers to prevent real intervals from running
    jest.useFakeTimers();

    // Mock Ollama constructor
    MockOllama.mockImplementation(() => mockOllamaClient as any);

    // Mock NativeEmbedder
    mockNativeEmbedder.mockClear();
    const mockEmbedderInstance = {
      embedTextInput: jest.fn().mockResolvedValue([0.1, 0.2, 0.3]),
      embedChunks: jest.fn().mockResolvedValue([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]),
    };
    mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);

    // Mock LLMPerformanceMonitor
    mockLLMPerformanceMonitor.measureAsyncFunction = jest
      .fn()
      .mockImplementation(async (fn) => {
        const result = await fn;
        return {
          output: result,
          duration: 1000,
        };
      });

    mockLLMPerformanceMonitor.measureStream = jest.fn().mockResolvedValue({
      endMeasurement: jest.fn().mockReturnValue({
        prompt_tokens: 10,
        completion_tokens: 20,
        total_tokens: 30,
        outputTps: 20,
        duration: 1000,
      }),
    });

    // Mock fetch for availability checks - default to success
    mockFetch.mockResolvedValue({
      ok: true,
      status: 200,
    } as Response);

    // Mock translations
    mockT.mockImplementation((key) => Promise.resolve(key));
    mockFormatContextTexts.mockResolvedValue("formatted context");
    mockHandleReasoningStream.mockResolvedValue("test response");
    mockParseReasoningFromResponse.mockImplementation((message) => {
      return message?.content || "";
    });
  });

  afterEach(() => {
    // Clear all timers to prevent "Cannot log after tests are done" errors
    jest.clearAllTimers();
    jest.useRealTimers();

    // Restore original environment
    process.env = originalEnv;

    // Restore console methods
    consoleErrorSpy.mockRestore();
    consoleLogSpy.mockRestore();
  });

  describe("Constructor", () => {
    it("should initialize with default parameters", () => {
      const ollama = new OllamaAILLM();

      expect(ollama.model).toBe("llama3.2:3b");
      expect(ollama.defaultTemp).toBe(0.7);
      expect(ollama.limits).toEqual({
        history: 8192 * 0.15,
        system: 8192 * 0.15,
        user: 8192 * 0.7,
      });
      expect(MockOllama).toHaveBeenCalledWith({
        host: "http://127.0.0.1:11434",
      });
    });

    it("should initialize with custom parameters", () => {
      process.env.OLLAMA_BASE_PATH_CUSTOM = "http://custom-host:11434";
      const customEmbedder = {} as EmbeddingEngine;
      const ollama = new OllamaAILLM(customEmbedder, "custom-model", "_CUSTOM");

      expect(ollama.embedder).toBe(customEmbedder);
      expect(ollama.model).toBe("custom-model");
    });

    it("should use environment variables with suffix", () => {
      process.env.OLLAMA_BASE_PATH_CUSTOM = "http://custom-host:11434";
      process.env.OLLAMA_MODEL_PREF_CUSTOM = "custom-model";
      process.env.OLLAMA_MODEL_TOKEN_LIMIT_CUSTOM = "16384";

      const ollama = new OllamaAILLM(null, null, "_CUSTOM");

      expect(MockOllama).toHaveBeenCalledWith({
        host: "http://custom-host:11434",
      });
      expect(ollama.model).toBe("custom-model");
    });

    it("should throw error when base path is not set", () => {
      delete process.env.OLLAMA_BASE_PATH;

      expect(() => new OllamaAILLM()).toThrow(
        "No Ollama API endpoint was set."
      );
    });

    it("should prioritize model preference parameter over environment", () => {
      process.env.OLLAMA_MODEL_PREF = "env-model";
      const ollama = new OllamaAILLM(null, "param-model");

      expect(ollama.model).toBe("param-model");
    });

    it("should initialize circuit breaker with default values", () => {
      const ollama = new OllamaAILLM();

      // Circuit breaker is private, but we can test its effects
      expect(ollama.isAvailable()).toBe(false); // Should be false initially
    });
  });

  describe("promptWindowLimit methods", () => {
    it("should return static prompt window limit", () => {
      process.env.OLLAMA_MODEL_TOKEN_LIMIT = "4096";
      expect(OllamaAILLM.promptWindowLimit()).toBe(4096);
    });

    it("should return static prompt window limit with suffix", () => {
      process.env.OLLAMA_MODEL_TOKEN_LIMIT_TEST = "16384";
      expect(OllamaAILLM.promptWindowLimit("_TEST")).toBe(16384);
    });

    it("should throw error for invalid token limit", () => {
      process.env.OLLAMA_MODEL_TOKEN_LIMIT = "invalid";
      expect(() => OllamaAILLM.promptWindowLimit()).toThrow(
        "No Ollama token context limit was set."
      );
    });

    it("should return instance prompt window limit", () => {
      const ollama = new OllamaAILLM();
      expect(ollama.promptWindowLimit()).toBe(8192);
    });

    it("should return custom prompt window limit", () => {
      const ollama = new OllamaAILLM();
      expect(ollama.customPromptWindowLimit()).toBe(8192);
    });
  });

  describe("streamingEnabled", () => {
    it("should return true since streamGetChatCompletion is defined", () => {
      const ollama = new OllamaAILLM();
      expect(ollama.streamingEnabled()).toBe(true);
    });
  });

  describe("isValidChatCompletionModel", () => {
    it("should always return true for any model", async () => {
      const ollama = new OllamaAILLM();
      expect(await ollama.isValidChatCompletionModel("any-model")).toBe(true);
      expect(await ollama.isValidChatCompletionModel("")).toBe(true);
    });
  });

  describe("checkAvailability", () => {
    it("should return true when Ollama server is available", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
      } as Response);

      const ollama = new OllamaAILLM();
      const result = await ollama.checkAvailability();

      expect(result).toBe(true);
      expect(ollama.isAvailable()).toBe(true);
      expect(mockFetch).toHaveBeenCalledWith(
        "http://127.0.0.1:11434/api/version",
        expect.objectContaining({
          method: "GET",
          signal: expect.any(AbortSignal),
        })
      );
    });

    it("should return false when Ollama server is not available", async () => {
      // Reset fetch mock and set it to fail
      mockFetch.mockReset();
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
      } as Response);

      const ollama = new OllamaAILLM();
      const result = await ollama.checkAvailability();

      expect(result).toBe(false);
      expect(ollama.isAvailable()).toBe(false);
    });

    it("should handle network errors", async () => {
      // Reset fetch mock and set it to fail
      mockFetch.mockReset();
      mockFetch.mockRejectedValueOnce(new Error("Network error"));

      const ollama = new OllamaAILLM();
      const result = await ollama.checkAvailability();

      expect(result).toBe(false);
      expect(ollama.isAvailable()).toBe(false);
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        "Ollama provider availability check failed:",
        expect.any(Error)
      );
    });

    it("should timeout requests after 3 seconds", async () => {
      // Reset fetch mock and set it to fail with abort error
      mockFetch.mockReset();
      const abortError = new Error("The operation was aborted");
      abortError.name = "AbortError";
      mockFetch.mockRejectedValueOnce(abortError);

      const ollama = new OllamaAILLM();

      const result = await ollama.checkAvailability();
      expect(result).toBe(false);
    });

    it("should reset circuit breaker on successful availability check", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
      } as Response);

      const ollama = new OllamaAILLM();

      // First simulate some failures to open circuit breaker
      (ollama as any).circuitBreaker.failures = 3;
      (ollama as any).circuitBreaker.state = "OPEN";

      await ollama.checkAvailability();

      expect((ollama as any).circuitBreaker.failures).toBe(0);
      expect((ollama as any).circuitBreaker.state).toBe("CLOSED");
    });
  });

  describe("isAvailable", () => {
    it("should return false when never checked", () => {
      const ollama = new OllamaAILLM();
      expect(ollama.isAvailable()).toBe(false);
    });

    it("should return false when circuit breaker is open", () => {
      const ollama = new OllamaAILLM();
      (ollama as any).lastAvailabilityCheck = Date.now();
      (ollama as any).circuitBreaker.state = "OPEN";
      (ollama as any).circuitBreaker.lastFailureTime = Date.now() - 1000;

      expect(ollama.isAvailable()).toBe(false);
    });

    it("should return true when circuit breaker transitions to half-open", () => {
      const ollama = new OllamaAILLM();
      (ollama as any).lastAvailabilityCheck = Date.now();
      (ollama as any).circuitBreaker.state = "OPEN";
      (ollama as any).circuitBreaker.lastFailureTime = Date.now() - 31000; // 31 seconds ago
      (ollama as any).circuitBreaker.resetTimeout = 30000;

      expect(ollama.isAvailable()).toBe(true);
      expect((ollama as any).circuitBreaker.state).toBe("HALF_OPEN");
    });
  });

  describe("generateContent", () => {
    it("should return content without images when no attachments", () => {
      const ollama = new OllamaAILLM();
      const result = (ollama as any).generateContent({
        userPrompt: "test prompt",
        attachments: [],
      });

      expect(result).toEqual({
        content: "test prompt",
      });
    });

    it("should process image attachments correctly", () => {
      const ollama = new OllamaAILLM();
      const attachments: Attachment[] = [
        {
          name: "image.png",
          mime: "image/png",
          contentString:
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
        },
      ];

      const result = (ollama as any).generateContent({
        userPrompt: "test prompt",
        attachments,
      });

      expect(result).toEqual({
        content: "test prompt",
        images: [
          "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
        ],
      });
    });

    it("should handle non-image attachments", () => {
      const ollama = new OllamaAILLM();
      const attachments: Attachment[] = [
        {
          name: "document.txt",
          mime: "text/plain",
          contentString: "Document content",
        },
      ];

      const result = (ollama as any).generateContent({
        userPrompt: "test prompt",
        attachments,
      });

      expect(result).toEqual({
        content: "test prompt\n\nDocument content",
      });
    });

    it("should handle mixed attachments", () => {
      const ollama = new OllamaAILLM();
      const attachments: Attachment[] = [
        {
          name: "image.png",
          mime: "image/png",
          contentString: "data:image/png;base64,validbase64data",
        },
        {
          name: "document.txt",
          mime: "text/plain",
          contentString: "Document content",
        },
      ];

      const result = (ollama as any).generateContent({
        userPrompt: "test prompt",
        attachments,
      });

      expect(result).toEqual({
        content: "test prompt\n\nDocument content",
        images: ["validbase64data"],
      });
    });

    it("should handle invalid base64 data gracefully", () => {
      const ollama = new OllamaAILLM();
      const attachments: Attachment[] = [
        {
          name: "image.png",
          mime: "image/png",
          contentString: "invalid-base64-data!@#$",
        },
      ];

      const result = (ollama as any).generateContent({
        userPrompt: "test prompt",
        attachments,
      });

      expect(result).toEqual({
        content: "test prompt",
      });
    });

    it("should handle attachment processing errors", () => {
      const ollama = new OllamaAILLM();
      const attachments: Attachment[] = [
        {
          name: "image.png",
          mime: "image/png",
          contentString: null as any, // This will cause an error
        },
      ];

      const result = (ollama as any).generateContent({
        userPrompt: "test prompt",
        attachments,
      });

      expect(result).toEqual({
        content: "test prompt",
      });
    });
  });

  describe("constructPrompt", () => {
    it("should construct prompt with all components", async () => {
      const ollama = new OllamaAILLM();
      const promptArgs: PromptArgs = {
        systemPrompt: "You are a helpful assistant",
        contextTexts: ["context1", "context2"],
        chatHistory: [
          { role: "user", content: "previous message" },
          { role: "assistant", content: "previous response" },
        ],
        userPrompt: "current message",
        attachments: [],
      };

      const result = await ollama.constructPrompt(promptArgs);

      expect(mockFormatContextTexts).toHaveBeenCalledWith([
        "context1",
        "context2",
      ]);
      expect(result).toHaveLength(4); // system + history + user
      expect(result[0]).toEqual({
        role: "system",
        content: "You are a helpful assistantformatted context",
      });
      expect(result[3]).toEqual({
        role: "user",
        content: "current message",
      });
    });

    it("should handle empty context texts", async () => {
      const ollama = new OllamaAILLM();
      const promptArgs: PromptArgs = {
        systemPrompt: "System prompt",
        contextTexts: [],
        userPrompt: "user message",
      };

      mockFormatContextTexts.mockResolvedValueOnce("");

      const result = await ollama.constructPrompt(promptArgs);

      expect(result[0].content).toBe("System prompt");
    });

    it("should include attachments in user message", async () => {
      const ollama = new OllamaAILLM();
      const attachments: Attachment[] = [
        {
          name: "image.png",
          mime: "image/png",
          contentString: "data:image/png;base64,validbase64",
        },
      ];

      const result = await ollama.constructPrompt({
        userPrompt: "describe this image",
        attachments,
      });

      expect(result[result.length - 1]).toEqual({
        role: "user",
        content: "describe this image",
        images: ["validbase64"],
      });
    });
  });

  describe("getChatCompletion", () => {
    it("should return null for null messages", async () => {
      const ollama = new OllamaAILLM();
      const result = await ollama.getChatCompletion(null, {});

      expect(result).toBeNull();
    });

    it("should return completion response successfully", async () => {
      const ollama = new OllamaAILLM();
      const messages: ChatMessage[] = [
        { role: "user", content: "test message" },
      ];

      const mockResponse = {
        message: { content: "test response" },
        prompt_eval_count: 10,
        eval_count: 20,
      };

      mockOllamaClient.chat.mockResolvedValueOnce(mockResponse);

      const result = await ollama.getChatCompletion(messages, {
        temperature: 0.8,
      });

      expect(mockOllamaClient.chat).toHaveBeenCalledWith({
        model: "llama3.2:3b",
        stream: false,
        messages,
        keep_alive: 300,
        options: {
          temperature: 0.8,
          use_mlock: true,
        },
      });

      expect(result).toEqual({
        textResponse: "test response",
        metrics: {
          prompt_tokens: 10,
          completion_tokens: 20,
          total_tokens: 30,
          outputTps: 0.02, // 20 tokens / 1000ms
          duration: 1000,
        },
      });
    });

    it("should use performance mode settings", async () => {
      process.env.OLLAMA_PERFORMANCE_MODE = "high";
      const ollama = new OllamaAILLM();
      const messages: ChatMessage[] = [
        { role: "user", content: "test message" },
      ];

      const mockResponse = {
        message: { content: "test response" },
        prompt_eval_count: 10,
        eval_count: 20,
      };

      mockOllamaClient.chat.mockResolvedValueOnce(mockResponse);

      await ollama.getChatCompletion(messages, {});

      expect(mockOllamaClient.chat).toHaveBeenCalledWith({
        model: "llama3.2:3b",
        stream: false,
        messages,
        keep_alive: 300,
        options: {
          temperature: 0.7,
          use_mlock: true,
          num_ctx: 8192,
        },
      });
    });

    it("should handle Ollama API errors", async () => {
      const ollama = new OllamaAILLM();
      const messages: ChatMessage[] = [
        { role: "user", content: "test message" },
      ];

      const error = new Error("Ollama API error");
      mockOllamaClient.chat.mockRejectedValueOnce(error);
      mockT.mockResolvedValueOnce("Stream failed: Ollama API error");

      await expect(ollama.getChatCompletion(messages, {})).rejects.toThrow(
        "Stream failed: Ollama API error"
      );
    });

    it("should handle empty response content", async () => {
      const ollama = new OllamaAILLM();
      const messages: ChatMessage[] = [
        { role: "user", content: "test message" },
      ];

      const mockResponse = {
        message: { content: "" },
        prompt_eval_count: 10,
        eval_count: 0,
      };

      mockOllamaClient.chat.mockResolvedValueOnce(mockResponse);
      mockT.mockResolvedValueOnce("Empty response from Ollama");

      await expect(ollama.getChatCompletion(messages, {})).rejects.toThrow(
        "Empty response from Ollama"
      );
    });

    it("should handle missing usage metrics", async () => {
      const ollama = new OllamaAILLM();
      const messages: ChatMessage[] = [
        { role: "user", content: "test message" },
      ];

      const mockResponse = {
        message: { content: "test response" },
        // Missing prompt_eval_count and eval_count
      };

      mockOllamaClient.chat.mockResolvedValueOnce(mockResponse);

      const result = await ollama.getChatCompletion(messages, {});

      expect(result?.metrics).toEqual({
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
        outputTps: 0,
        duration: 1000,
      });
    });
  });

  describe("streamGetChatCompletion", () => {
    beforeEach(() => {
      // Mock successful initialization
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
      } as Response);
    });

    it("should throw error for null messages", async () => {
      const ollama = new OllamaAILLM();

      await expect(
        ollama.streamGetChatCompletion(null as any, {})
      ).rejects.toThrow("No messages provided");
    });

    it("should fail fast when provider is unavailable", async () => {
      const ollama = new OllamaAILLM();

      // Wait for initialization
      await (ollama as any).initialized;

      // Force provider to be unavailable with proper circuit breaker state
      (ollama as any).available = false;
      (ollama as any).lastAvailabilityCheck = Date.now();
      (ollama as any).circuitBreaker.state = "OPEN";
      (ollama as any).circuitBreaker.lastFailureTime = Date.now();

      await expect(
        ollama.streamGetChatCompletion([{ role: "user", content: "test" }], {})
      ).rejects.toThrow("errors.ai.ollama.provider-unavailable");
    });

    it("should handle successful streaming response", async () => {
      const ollama = new OllamaAILLM();
      const messages: ChatMessage[] = [
        { role: "user", content: "test message" },
      ];

      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { message: { content: "chunk1" } };
          yield { message: { content: "chunk2" } };
        },
      };

      mockOllamaClient.chat.mockResolvedValueOnce(mockStream);

      const result = await ollama.streamGetChatCompletion(messages, {});

      expect(mockOllamaClient.chat).toHaveBeenCalledWith({
        model: "llama3.2:3b",
        stream: true,
        messages,
        keep_alive: 300,
        options: {
          temperature: 0.7,
          use_mlock: true,
        },
      });

      expect(result).toHaveProperty("stream");
      expect(result).toHaveProperty("endMeasurement");
    });

    it("should handle non-Promise chat request", async () => {
      const ollama = new OllamaAILLM();
      const messages: ChatMessage[] = [
        { role: "user", content: "test message" },
      ];

      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { message: { content: "chunk1" } };
        },
      };

      // Mock as synchronous response
      mockOllamaClient.chat.mockReturnValueOnce(mockStream);

      const result = await ollama.streamGetChatCompletion(messages, {});

      expect(result).toHaveProperty("stream");
      expect(result).toHaveProperty("endMeasurement");
    });

    it("should handle connection errors", async () => {
      const ollama = new OllamaAILLM();
      const messages: ChatMessage[] = [
        { role: "user", content: "test message" },
      ];

      const connectionError = new Error("ECONNREFUSED");
      (connectionError as any).code = "ECONNREFUSED";
      mockOllamaClient.chat.mockRejectedValueOnce(connectionError);

      await expect(
        ollama.streamGetChatCompletion(messages, {})
      ).rejects.toThrow("errors.ai.ollama.provider-unavailable");

      expect((ollama as any).available).toBe(false);
    });

    it("should handle fetch failed errors", async () => {
      const ollama = new OllamaAILLM();
      const messages: ChatMessage[] = [
        { role: "user", content: "test message" },
      ];

      const fetchError = new Error("fetch failed");
      mockOllamaClient.chat.mockRejectedValueOnce(fetchError);

      await expect(
        ollama.streamGetChatCompletion(messages, {})
      ).rejects.toThrow("errors.ai.ollama.provider-unavailable");
    });

    it("should handle general errors", async () => {
      const ollama = new OllamaAILLM();
      const messages: ChatMessage[] = [
        { role: "user", content: "test message" },
      ];

      const error = new Error("General error");
      mockOllamaClient.chat.mockRejectedValueOnce(error);

      await expect(
        ollama.streamGetChatCompletion(messages, {})
      ).rejects.toThrow("Ollama provider error: General error");
    });

    it("should handle half-open circuit breaker state", async () => {
      const ollama = new OllamaAILLM();

      // Wait for initialization and then set up half-open state
      await (ollama as any).initialized;
      (ollama as any).available = false;
      (ollama as any).lastAvailabilityCheck = Date.now();
      (ollama as any).circuitBreaker.state = "HALF_OPEN";

      const messages: ChatMessage[] = [
        { role: "user", content: "test message" },
      ];

      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { message: { content: "chunk1" } };
        },
      };

      mockOllamaClient.chat.mockResolvedValueOnce(mockStream);

      const result = await ollama.streamGetChatCompletion(messages, {});

      // Check if the half-open log message was called at some point
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining("half-open state - attempting request")
      );
      expect(result).toHaveProperty("stream");
    });
  });

  describe("handleStream", () => {
    it("should delegate to handleReasoningStream", async () => {
      const ollama = new OllamaAILLM();
      const mockResponse = {} as ExpressResponse;
      const mockStream = {} as AsyncIterable<unknown>;
      const responseProps: StreamResponseProps = { uuid: "test-uuid" };

      mockHandleReasoningStream.mockResolvedValueOnce("stream result");

      const result = await ollama.handleStream(
        mockResponse,
        mockStream,
        responseProps
      );

      expect(mockHandleReasoningStream).toHaveBeenCalledWith(
        mockResponse,
        mockStream,
        responseProps
      );
      expect(result).toBe("stream result");
    });
  });

  describe("embedTextInput", () => {
    it("should delegate to embedder", async () => {
      const ollama = new OllamaAILLM();
      const mockEmbedder = ollama.embedder as any;

      const result = await ollama.embedTextInput("test text");

      expect(mockEmbedder.embedTextInput).toHaveBeenCalledWith("test text");
      expect(result).toEqual([0.1, 0.2, 0.3]);
    });
  });

  describe("embedChunks", () => {
    it("should delegate to embedder", async () => {
      const ollama = new OllamaAILLM();
      const mockEmbedder = ollama.embedder as any;

      const result = await ollama.embedChunks(["chunk1", "chunk2"]);

      expect(mockEmbedder.embedChunks).toHaveBeenCalledWith([
        "chunk1",
        "chunk2",
      ]);
      expect(result).toEqual([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]);
    });

    it("should handle empty chunks", async () => {
      const ollama = new OllamaAILLM();
      const mockEmbedder = ollama.embedder as any;

      const result = await ollama.embedChunks();

      expect(mockEmbedder.embedChunks).toHaveBeenCalledWith([]);
      expect(result).toEqual([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]);
    });

    it("should handle null embedder result", async () => {
      const ollama = new OllamaAILLM();
      const mockEmbedder = ollama.embedder as any;
      mockEmbedder.embedChunks.mockResolvedValueOnce(null);

      const result = await ollama.embedChunks(["chunk1"]);

      expect(result).toEqual([]);
    });
  });

  describe("compressMessages", () => {
    beforeEach(() => {
      // Mock the require statement in compressMessages
      jest.doMock("../../../helpers/chat", () => ({
        messageArrayCompressor: jest
          .fn()
          .mockImplementation(async (_provider, messages, _rawHistory) => {
            return messages;
          }),
      }));
    });

    it("should handle normal message array", async () => {
      const ollama = new OllamaAILLM();
      const promptArgs: PromptArgs = {
        userPrompt: "test prompt",
      };
      const rawHistory: ChatMessage[] = [];

      // Mock constructPrompt to return expected format
      jest
        .spyOn(ollama, "constructPrompt")
        .mockResolvedValueOnce([{ role: "user", content: "test prompt" }]);

      const result = await ollama.compressMessages(promptArgs, rawHistory);

      expect(result).toEqual([{ role: "user", content: "test prompt" }]);
    });

    it("should convert string result to message array", async () => {
      const ollama = new OllamaAILLM();

      // Mock messageArrayCompressor to return string
      const { messageArrayCompressor } = require("../../../helpers/chat");
      messageArrayCompressor.mockResolvedValueOnce("string response");

      jest
        .spyOn(ollama, "constructPrompt")
        .mockResolvedValueOnce([{ role: "user", content: "test" }]);

      const result = await ollama.compressMessages({}, []);

      expect(result).toEqual([{ role: "user", content: "string response" }]);
    });

    it("should convert non-array result to message array", async () => {
      const ollama = new OllamaAILLM();

      // Mock messageArrayCompressor to return object
      const { messageArrayCompressor } = require("../../../helpers/chat");
      messageArrayCompressor.mockResolvedValueOnce({ unexpected: "format" });

      jest
        .spyOn(ollama, "constructPrompt")
        .mockResolvedValueOnce([{ role: "user", content: "test" }]);

      const result = await ollama.compressMessages({}, []);

      expect(result).toEqual([{ role: "user", content: "[object Object]" }]);
    });

    it("should normalize array with string items", async () => {
      const ollama = new OllamaAILLM();

      // Mock messageArrayCompressor to return array with strings
      const { messageArrayCompressor } = require("../../../helpers/chat");
      messageArrayCompressor.mockResolvedValueOnce([
        "first message",
        "second message",
      ]);

      jest
        .spyOn(ollama, "constructPrompt")
        .mockResolvedValueOnce([{ role: "user", content: "test" }]);

      const result = await ollama.compressMessages({}, []);

      expect(result).toEqual([
        { role: "user", content: "first message" },
        { role: "user", content: "second message" },
      ]);
    });

    it("should normalize array with mixed content", async () => {
      const ollama = new OllamaAILLM();

      // Mock messageArrayCompressor to return mixed array
      const { messageArrayCompressor } = require("../../../helpers/chat");
      messageArrayCompressor.mockResolvedValueOnce([
        { role: "user", content: "proper message" },
        { content: "missing role" },
        { role: "assistant", content: { object: "content" } },
        null,
        123,
      ]);

      jest
        .spyOn(ollama, "constructPrompt")
        .mockResolvedValueOnce([{ role: "user", content: "test" }]);

      const result = await ollama.compressMessages({}, []);

      expect(result).toEqual([
        { role: "user", content: "proper message" },
        { role: "assistant", content: "missing role" }, // index 1, so i % 2 === 1 -> "assistant"
        { role: "assistant", content: '{"object":"content"}' },
        { role: "user", content: "null" }, // index 3, so i % 2 === 1 -> "assistant", but null becomes "user"
        { role: "user", content: "123" }, // index 4, so i % 2 === 0 -> "user"
      ]);
    });
  });

  describe("errorHandler", () => {
    // Testing internal error handler behavior based on actual implementation
    it("should handle Promise objects in error", async () => {
      const ollama = new OllamaAILLM();
      const promiseError = Promise.resolve("test error");

      await expect((ollama as any).errorHandler(promiseError)).rejects.toThrow(
        "errors.ai.ollama.provider-not-responding"
      );
    });

    it("should handle connection refused errors", async () => {
      const ollama = new OllamaAILLM();

      // Wait for initialization which may set available to true
      await (ollama as any).initialized;

      // Verify that available gets set to false by errorHandler
      const initialAvailable = (ollama as any).available;
      const connectionError = { code: "ECONNREFUSED" };

      await expect(
        (ollama as any).errorHandler(connectionError)
      ).rejects.toThrow("errors.ai.ollama.provider-unavailable");

      // The errorHandler should have changed available to false
      expect((ollama as any).available).toBe(false);
      // Confirm it actually changed if it was initially true
      if (initialAvailable) {
        expect((ollama as any).available).not.toBe(initialAvailable);
      }
    });

    it("should handle fetch failed errors", async () => {
      const ollama = new OllamaAILLM();
      const fetchError = { message: "fetch failed" };

      // According to the implementation, "fetch failed" goes to provider-unavailable, not connection-failed
      await expect((ollama as any).errorHandler(fetchError)).rejects.toThrow(
        "errors.ai.ollama.provider-unavailable"
      );
    });

    it("should handle generic errors", async () => {
      const ollama = new OllamaAILLM();
      const genericError = new Error("Generic error message");

      await expect((ollama as any).errorHandler(genericError)).rejects.toThrow(
        "Ollama provider error: Generic error message"
      );
    });

    it("should handle errors without message property", async () => {
      const ollama = new OllamaAILLM();
      const errorWithoutMessage = { someProperty: "value" };

      await expect(
        (ollama as any).errorHandler(errorWithoutMessage)
      ).rejects.toThrow("Ollama provider error: Unknown error");
    });
  });

  describe("Circuit breaker functionality", () => {
    it("should record failures and open circuit breaker", () => {
      const ollama = new OllamaAILLM();

      // Simulate failures
      (ollama as any).recordFailure();
      (ollama as any).recordFailure();
      (ollama as any).recordFailure();

      expect((ollama as any).circuitBreaker.failures).toBe(3);
      expect((ollama as any).circuitBreaker.state).toBe("OPEN");
    });

    it("should not open circuit breaker before threshold", () => {
      const ollama = new OllamaAILLM();

      // Simulate fewer failures than threshold
      (ollama as any).recordFailure();
      (ollama as any).recordFailure();

      expect((ollama as any).circuitBreaker.failures).toBe(2);
      expect((ollama as any).circuitBreaker.state).toBe("CLOSED");
    });
  });

  describe("Performance mode configuration", () => {
    it("should use base performance mode by default", () => {
      const ollama = new OllamaAILLM();
      expect((ollama as any).performanceMode).toBe("base");
    });

    it("should use environment variable for performance mode", () => {
      process.env.OLLAMA_PERFORMANCE_MODE = "high";
      const ollama = new OllamaAILLM();
      expect((ollama as any).performanceMode).toBe("high");
    });

    it("should use default keep alive timeout", () => {
      delete process.env.OLLAMA_KEEP_ALIVE_TIMEOUT;
      const ollama = new OllamaAILLM();
      expect((ollama as any).keepAlive).toBe(300);
    });

    it("should use environment variable for keep alive timeout", () => {
      process.env.OLLAMA_KEEP_ALIVE_TIMEOUT = "600";
      const ollama = new OllamaAILLM();
      expect((ollama as any).keepAlive).toBe(600);
    });
  });

  describe("Integration scenarios", () => {
    it("should handle complete workflow from prompt to response", async () => {
      const ollama = new OllamaAILLM();

      // Mock successful availability check
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
      } as Response);

      // Setup for completion
      const mockResponse = {
        message: { content: "Complete response" },
        prompt_eval_count: 50,
        eval_count: 100,
      };
      mockOllamaClient.chat.mockResolvedValueOnce(mockResponse);

      // Test prompt construction and completion
      const promptArgs: PromptArgs = {
        systemPrompt: "System instructions",
        userPrompt: "User question",
        contextTexts: ["context"],
      };

      const messages = await ollama.constructPrompt(promptArgs);
      const result = await ollama.getChatCompletion(messages, {
        temperature: 0.5,
      });

      expect(result).toEqual({
        textResponse: "Complete response",
        metrics: {
          prompt_tokens: 50,
          completion_tokens: 100,
          total_tokens: 150,
          outputTps: 0.1,
          duration: 1000,
        },
      });
    });

    it("should handle high-load concurrent requests", async () => {
      const ollama = new OllamaAILLM();

      // Mock multiple successful responses
      const mockResponse = {
        message: { content: "Response" },
        prompt_eval_count: 10,
        eval_count: 20,
      };
      mockOllamaClient.chat.mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [{ role: "user", content: "test" }];

      // Simulate concurrent requests
      const promises = Array(10)
        .fill(null)
        .map(() => ollama.getChatCompletion(messages, {}));

      const results = await Promise.all(promises);

      expect(results).toHaveLength(10);
      results.forEach((result) => {
        expect(result?.textResponse).toBe("Response");
      });
    });
  });

  describe("Memory and resource management", () => {
    it("should handle large input strings", async () => {
      const ollama = new OllamaAILLM();
      const largeInput = "a".repeat(100000); // 100KB string

      const messages: ChatMessage[] = [{ role: "user", content: largeInput }];

      const mockResponse = {
        message: { content: "Response to large input" },
        prompt_eval_count: 1000,
        eval_count: 50,
      };
      mockOllamaClient.chat.mockResolvedValueOnce(mockResponse);

      const result = await ollama.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("Response to large input");
      expect(mockOllamaClient.chat).toHaveBeenCalledWith(
        expect.objectContaining({
          messages: expect.arrayContaining([
            expect.objectContaining({ content: largeInput }),
          ]),
        })
      );
    });

    it("should handle multiple large attachments", () => {
      const ollama = new OllamaAILLM();
      const largeBase64 = "A".repeat(50000); // Large base64 string

      const attachments: Attachment[] = Array(5)
        .fill(null)
        .map((_, i) => ({
          name: `image${i}.png`,
          mime: "image/png",
          contentString: `data:image/png;base64,${largeBase64}`,
        }));

      const result = (ollama as any).generateContent({
        userPrompt: "Process these images",
        attachments,
      });

      expect(result.images).toHaveLength(5);
      expect(result.images[0]).toBe(largeBase64);
    });
  });
});
