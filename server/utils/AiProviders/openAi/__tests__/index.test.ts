import { OpenAiLLM } from "../index";
import { NativeEmbedder } from "../../../EmbeddingEngines/native";
import { LLMPerformanceMonitor } from "../../../helpers/chat/LLMPerformanceMonitor";
import { handleDefaultStreamResponseV2 } from "../../../helpers/chat/responses";
import { t } from "../../../i18n";
import { formatContextTexts } from "../../../helpers";
import type { Response as ExpressResponse } from "express";
import type {
  ChatMessage,
  PromptArgs,
  EmbeddingEngine,
  Attachment,
  StreamResponseProps,
} from "../../../../types/ai-providers";

// Mock dependencies
jest.mock("openai");
jest.mock("../../../EmbeddingEngines/native");
jest.mock("../../../helpers/chat/LLMPerformanceMonitor");
jest.mock("../../../helpers/chat/responses");
jest.mock("../../../i18n", () => ({
  t: jest.fn().mockImplementation((key) => {
    if (key === "chatboxdnd.uploaded-file-tag") return "USER UPLOADED FILE";
    return key;
  }),
}));
jest.mock("../../../helpers", () => ({
  formatContextTexts: jest.fn().mockImplementation(async (texts: string[]) => {
    return texts.join("\n\n---CONTEXT---\n\n");
  }),
}));
jest.mock("../../../helpers/chat", () => ({
  messageArrayCompressor: jest
    .fn()
    .mockImplementation(async (provider, messages, _rawHistory) => {
      return messages;
    }),
}));

// Mock OpenAI client
const mockOpenAIClient = {
  chat: {
    completions: {
      create: jest.fn(),
    },
  },
  models: {
    retrieve: jest.fn(),
  },
};

// Set up the OpenAI mock before importing
const { OpenAI } = require("openai");
(OpenAI as jest.MockedClass<typeof OpenAI>).mockImplementation(
  () => mockOpenAIClient as any
);

describe("OpenAiLLM", () => {
  const mockNativeEmbedder = NativeEmbedder as jest.MockedClass<
    typeof NativeEmbedder
  >;
  const mockLLMPerformanceMonitor = LLMPerformanceMonitor as jest.MockedClass<
    typeof LLMPerformanceMonitor
  >;
  const mockHandleDefaultStreamResponseV2 =
    handleDefaultStreamResponseV2 as jest.MockedFunction<
      typeof handleDefaultStreamResponseV2
    >;
  const _mockT = t as jest.MockedFunction<typeof t>;
  const _mockFormatContextTexts = formatContextTexts as jest.MockedFunction<
    typeof formatContextTexts
  >;

  let originalEnv: typeof process.env;
  let consoleErrorSpy: jest.SpyInstance;

  beforeEach(() => {
    // Reset the OpenAI mock before each test
    (OpenAI as jest.MockedClass<typeof OpenAI>).mockClear();
    (OpenAI as jest.MockedClass<typeof OpenAI>).mockImplementation(
      () => mockOpenAIClient as any
    );
    // Store original environment
    originalEnv = { ...process.env };

    // Set up required environment variables
    process.env.OPEN_AI_KEY = "test-openai-api-key";
    process.env.OPEN_MODEL_PREF = "gpt-4o";

    // Clear all mocks
    jest.clearAllMocks();

    // Spy on console methods
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();

    // Mock NativeEmbedder
    mockNativeEmbedder.mockClear();
    const mockEmbedderInstance = {
      embedTextInput: jest.fn().mockResolvedValue([0.1, 0.2, 0.3]),
      embedChunks: jest.fn().mockResolvedValue([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]),
    };
    mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);

    // Mock LLM Performance Monitor
    mockLLMPerformanceMonitor.measureAsyncFunction = jest
      .fn()
      .mockImplementation(async (promise) => {
        const output = await promise;
        return { output, duration: 1000 };
      });
    mockLLMPerformanceMonitor.measureStream = jest
      .fn()
      .mockImplementation(async (streamPromise) => {
        const stream = await streamPromise;
        return {
          [Symbol.asyncIterator]: stream[Symbol.asyncIterator],
          endMeasurement: jest.fn().mockReturnValue({
            prompt_tokens: 100,
            completion_tokens: 50,
            total_tokens: 150,
            outputTps: 50,
            duration: 1000,
          }),
        };
      });

    // Mock stream response handler
    mockHandleDefaultStreamResponseV2.mockResolvedValue("Streamed response");
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
    consoleErrorSpy.mockRestore();
  });

  describe("constructor and initialization", () => {
    it("should initialize with default settings", () => {
      const openai = new OpenAiLLM();

      expect(openai.model).toBe("gpt-4o");
      expect(openai.limits).toEqual({
        history: 128000 * 0.15,
        system: 128000 * 0.15,
        user: 128000 * 0.7,
      });
      expect(openai.defaultTemp).toBe(0.7);
      expect(openai.embedder).toBeDefined();
      expect(openai.useDeepSearch).toBe(false);
    });

    it("should throw error when API key is missing via static initialize", async () => {
      delete process.env.OPEN_AI_KEY;

      await expect(OpenAiLLM.initialize()).rejects.toThrow(
        "errors.ai.openai.no-key"
      );
    });

    it("should initialize via static method", async () => {
      const openai = await OpenAiLLM.initialize();

      expect(openai).toBeInstanceOf(OpenAiLLM);
      expect(openai.model).toBe("gpt-4o");
    });

    it("should use custom embedder when provided", () => {
      const customEmbedder = {} as EmbeddingEngine;
      const openai = new OpenAiLLM(customEmbedder);

      expect(openai.embedder).toBe(customEmbedder);
    });

    it("should use model preference when provided", () => {
      const openai = new OpenAiLLM(null, "gpt-4-turbo");

      expect(openai.model).toBe("gpt-4-turbo");
    });

    it("should use settings suffix when provided", () => {
      process.env.OPEN_AI_KEY_CUSTOM = "custom-api-key";
      process.env.OPEN_MODEL_PREF_CUSTOM = "gpt-3.5-turbo";

      const openai = new OpenAiLLM(null, null, "_CUSTOM");

      expect(openai.model).toBe("gpt-3.5-turbo");
      expect(mockOpenAIClient).toBeDefined();
    });

    it("should use default model when no preference is set", () => {
      delete process.env.OPEN_MODEL_PREF;

      const openai = new OpenAiLLM();

      expect(openai.model).toBe("chatgpt-4o-latest");
    });

    it("should set useDeepSearch when provided", () => {
      const openai = new OpenAiLLM(null, null, null, true);

      expect(openai.useDeepSearch).toBe(true);
    });
  });

  describe("o1 model detection", () => {
    it("should detect o1 models", () => {
      const o1Model = new OpenAiLLM(null, "o1-preview");
      expect(o1Model.isO1Model).toBe(true);

      const o1MiniModel = new OpenAiLLM(null, "o1-mini");
      expect(o1MiniModel.isO1Model).toBe(true);

      const o3MiniModel = new OpenAiLLM(null, "o3-mini-2025");
      expect(o3MiniModel.isO1Model).toBe(true);
    });

    it("should not detect non-o1 models as o1", () => {
      const gpt4Model = new OpenAiLLM(null, "gpt-4");
      expect(gpt4Model.isO1Model).toBe(false);

      const gpt35Model = new OpenAiLLM(null, "gpt-3.5-turbo");
      expect(gpt35Model.isO1Model).toBe(false);
    });
  });

  describe("streamingEnabled", () => {
    it("should return true since streamGetChatCompletion is defined", () => {
      const openai = new OpenAiLLM();
      expect(openai.streamingEnabled()).toBe(true);
    });
  });

  describe("static promptWindowLimit method", () => {
    it("should return static prompt window limit for known models", () => {
      expect(OpenAiLLM.promptWindowLimit("gpt-4o")).toBe(128000);
      expect(OpenAiLLM.promptWindowLimit("gpt-4-turbo")).toBe(128000);
      expect(OpenAiLLM.promptWindowLimit("gpt-3.5-turbo")).toBe(128000); // Uses default
      expect(OpenAiLLM.promptWindowLimit("o1-preview")).toBe(128000);
    });

    it("should return default for unknown model", () => {
      expect(OpenAiLLM.promptWindowLimit("unknown-model")).toBe(128000); // Uses default
    });

    it("should handle empty model name", () => {
      expect(OpenAiLLM.promptWindowLimit("")).toBe(128000); // Uses default
    });
  });

  describe("instance promptWindowLimit methods", () => {
    it("should return instance prompt window limit", () => {
      const openai = new OpenAiLLM();
      expect(openai.promptWindowLimit()).toBe(128000);
    });

    it("should return custom prompt window limit", () => {
      const openai = new OpenAiLLM();
      expect(openai.customPromptWindowLimit()).toBe(128000);
    });
  });

  describe("isValidChatCompletionModel", () => {
    beforeEach(() => {
      // Mock the model retrieve to return valid responses for known models
      mockOpenAIClient.models.retrieve.mockImplementation(
        (modelName: string) => {
          if (
            ["gpt-4o", "gpt-4-turbo", "gpt-3.5-turbo", "o1-preview"].includes(
              modelName
            )
          ) {
            return Promise.resolve({ id: modelName });
          }
          return Promise.reject(new Error("Model not found"));
        }
      );
    });

    it("should return true for known OpenAI models", async () => {
      const openai = new OpenAiLLM();

      expect(await openai.isValidChatCompletionModel("gpt-4o")).toBe(true);
      expect(await openai.isValidChatCompletionModel("gpt-4-turbo")).toBe(true);
      expect(await openai.isValidChatCompletionModel("gpt-3.5-turbo")).toBe(
        true
      );
      expect(await openai.isValidChatCompletionModel("o1-preview")).toBe(true);
    });

    it("should return false for unknown models", async () => {
      const openai = new OpenAiLLM();

      expect(await openai.isValidChatCompletionModel("unknown-model")).toBe(
        false
      );
      expect(await openai.isValidChatCompletionModel("claude-3")).toBe(false);
    });
  });

  describe("generateContent", () => {
    let openai: OpenAiLLM;

    beforeEach(() => {
      openai = new OpenAiLLM();
    });

    it("should return string when no attachments", () => {
      const result = openai["generateContent"]({
        userPrompt: "Hello OpenAI",
        attachments: [],
      });

      expect(result).toBe("Hello OpenAI");
    });

    it("should handle image attachments", () => {
      const attachments: Attachment[] = [
        {
          name: "image.png",
          type: "image/png",
          contentString: "data:image/png;base64,iVBORw0KGgo...",
        },
      ];

      const result = openai["generateContent"]({
        userPrompt: "Analyze this image",
        attachments,
      });

      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray).toHaveLength(2);
      expect(resultArray[0]).toEqual({
        type: "text",
        text: "Analyze this image",
      });
      expect(resultArray[1]).toEqual({
        type: "image_url",
        image_url: {
          url: "data:image/png;base64,iVBORw0KGgo...",
          detail: "high",
        },
      });
    });

    it("should handle text attachments", () => {
      const attachments: Attachment[] = [
        {
          name: "document.txt",
          contentString: "File content here",
          type: "text/plain",
        },
      ];

      const result = openai["generateContent"]({
        userPrompt: "Analyze this document",
        attachments,
      });

      // Text attachments should be included in the content array
      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray[0]).toEqual({
        type: "text",
        text: "Analyze this document",
      });
      expect(resultArray[1]).toEqual({
        type: "text",
        text: "<USER UPLOADED FILE>\nFile content here\n</USER UPLOADED FILE>",
      });
    });
  });

  describe("constructPrompt", () => {
    let openai: OpenAiLLM;

    beforeEach(() => {
      openai = new OpenAiLLM();
    });

    it("should construct prompt with all components", async () => {
      const promptArgs: PromptArgs = {
        systemPrompt: "You are a helpful assistant.",
        contextTexts: ["Context 1", "Context 2"],
        chatHistory: [
          { role: "user", content: "What is GPT?" },
          { role: "assistant", content: "GPT is a language model." },
        ],
        userPrompt: "Tell me more",
      };

      const result = await openai.constructPrompt(promptArgs);

      expect(result).toEqual([
        {
          role: "system",
          content:
            "You are a helpful assistant.Context 1\n\n---CONTEXT---\n\nContext 2",
        },
        { role: "user", content: "What is GPT?" },
        { role: "assistant", content: "GPT is a language model." },
        { role: "user", content: "Tell me more" },
      ]);
    });

    it("should handle o1 model system prompt", async () => {
      const o1Model = new OpenAiLLM(null, "o1-preview");
      const promptArgs: PromptArgs = {
        systemPrompt: "You are a helpful assistant.",
        userPrompt: "Hello",
      };

      const result = await o1Model.constructPrompt(promptArgs);

      // Current implementation treats o1 models the same as other models
      expect(result).toEqual([
        {
          role: "system",
          content: "You are a helpful assistant.",
        },
        {
          role: "user",
          content: "Hello",
        },
      ]);
    });

    it("should handle attachments in prompt", async () => {
      const attachment: Attachment = {
        name: "image.png",
        type: "image/png",
        contentString: "data:image/png;base64,test",
      };

      const promptArgs: PromptArgs = {
        systemPrompt: "Analyze images",
        userPrompt: "What's in this image?",
        attachments: [attachment],
      };

      const result = await openai.constructPrompt(promptArgs);

      const lastMessage = result[result.length - 1];
      expect(lastMessage.role).toBe("user");
      expect(Array.isArray(lastMessage.content)).toBe(true);
    });
  });

  describe("getChatCompletion", () => {
    let openai: OpenAiLLM;

    beforeEach(() => {
      openai = new OpenAiLLM();
      mockOpenAIClient.chat.completions.create.mockClear();
    });

    it("should return completion response successfully", async () => {
      const mockResponse = {
        choices: [
          {
            message: {
              content: "Response from OpenAI",
            },
          },
        ],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 20,
          total_tokens: 30,
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: "user", content: "Tell me about OpenAI" },
      ];

      const result = await openai.getChatCompletion(messages, {
        temperature: 0.8,
      });

      expect(result).toEqual({
        textResponse: "Response from OpenAI",
        metrics: {
          prompt_tokens: 10,
          completion_tokens: 20,
          total_tokens: 30,
          outputTps: 20 / 1000,
          duration: 1000,
        },
      });

      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith({
        model: "gpt-4o",
        messages,
        temperature: 0.8,
      });
    });

    it("should use default temperature when not provided", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Response" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await openai.getChatCompletion(messages, {});

      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith({
        model: "gpt-4o",
        messages,
        temperature: 0.7,
      });
    });

    it("should handle o1 models with temperature set to 1", async () => {
      const o1Model = new OpenAiLLM(null, "o1-preview");
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "O1 Response" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await o1Model.getChatCompletion(messages, { temperature: 0.8 });

      // o1 models should use temperature 1 regardless of input
      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith({
        model: "o1-preview",
        messages,
        temperature: 1,
      });
    });

    it("should throw error for invalid model", async () => {
      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];
      openai.isValidChatCompletionModel = jest.fn().mockReturnValue(false);

      await expect(openai.getChatCompletion(messages, {})).rejects.toThrow(
        "OpenAI chat: gpt-4o is not valid for chat completion!"
      );
    });

    it("should handle API errors", async () => {
      const error = new Error("OpenAI API request failed");
      mockOpenAIClient.chat.completions.create.mockRejectedValueOnce(error);
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("OpenAI API request failed");
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(openai.getChatCompletion(messages, {})).rejects.toThrow(
        "OpenAI API request failed"
      );
    });

    it("should return null for empty choices", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await openai.getChatCompletion(messages, {});

      expect(result).toBeNull();
    });

    it("should handle missing usage data", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Response without usage" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await openai.getChatCompletion(messages, {});

      expect(result?.metrics).toEqual({
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
        outputTps: undefined, // No usage data means no TPS calculation
        duration: 1000,
      });
    });
  });

  describe("streamGetChatCompletion", () => {
    let openai: OpenAiLLM;

    beforeEach(() => {
      openai = new OpenAiLLM();
      mockOpenAIClient.chat.completions.create.mockClear();
    });

    it("should stream completion successfully", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield {
            choices: [{ delta: { content: "Hello" } }],
          };
          yield {
            choices: [{ delta: { content: " from OpenAI!" } }],
          };
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockStream);

      const messages: ChatMessage[] = [{ role: "user", content: "Say hello" }];

      const result = await openai.streamGetChatCompletion(messages, {
        temperature: 0.9,
      });

      expect(result.stream).toBeDefined();
      expect(result.endMeasurement).toBeDefined();

      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith({
        model: "gpt-4o",
        stream: true,
        messages,
        temperature: 0.9,
      });
    });

    it("should handle o1 models in streaming", async () => {
      const o1Model = new OpenAiLLM(null, "o1-preview");
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { choices: [{ delta: { content: "O1 stream" } }] };
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockStream);

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await o1Model.streamGetChatCompletion(messages, { temperature: 0.5 });

      // o1 models should use temperature 1
      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith({
        model: "o1-preview",
        stream: true,
        messages,
        temperature: 1,
      });
    });

    it("should throw error for invalid model", async () => {
      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];
      openai.model = "invalid-model";

      await expect(
        openai.streamGetChatCompletion(messages, {})
      ).rejects.toThrow(
        "OpenAI chat: invalid-model is not valid for chat completion!"
      );
    });

    it("should handle stream creation errors", async () => {
      const error = new Error("Stream creation failed");
      mockOpenAIClient.chat.completions.create.mockRejectedValueOnce(error);

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(
        openai.streamGetChatCompletion(messages, {})
      ).rejects.toThrow("Stream creation failed");
    });
  });

  describe("handleStream", () => {
    let openai: OpenAiLLM;
    let mockResponse: Partial<ExpressResponse>;

    beforeEach(() => {
      openai = new OpenAiLLM();
      mockResponse = {
        write: jest.fn(),
        end: jest.fn(),
      };
    });

    it("should handle stream successfully", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { text: "OpenAI chunk 1" };
          yield { text: "OpenAI chunk 2" };
        },
      };

      const responseProps: StreamResponseProps = {
        uuid: "test-uuid",
        sources: [],
      };

      await openai.handleStream(
        mockResponse as ExpressResponse,
        mockStream,
        responseProps
      );

      expect(mockHandleDefaultStreamResponseV2).toHaveBeenCalledWith(
        mockResponse,
        mockStream,
        responseProps
      );
    });
  });

  describe("embedding methods", () => {
    let openai: OpenAiLLM;

    beforeEach(() => {
      openai = new OpenAiLLM();
    });

    it("should embed text input successfully", async () => {
      const result = await openai.embedTextInput("Test text for OpenAI");

      expect(result).toEqual([0.1, 0.2, 0.3]);
      expect(openai.embedder.embedTextInput).toHaveBeenCalledWith(
        "Test text for OpenAI"
      );
    });

    it("should embed chunks successfully", async () => {
      const chunks = ["Chunk 1 for OpenAI", "Chunk 2 for OpenAI"];
      const result = await openai.embedChunks(chunks);

      expect(result).toEqual([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]);
      expect(openai.embedder.embedChunks).toHaveBeenCalledWith(chunks);
    });

    it("should handle empty chunks array", async () => {
      const result = await openai.embedChunks([]);

      expect(result).toEqual([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]);
    });

    it("should handle null result from embedder", async () => {
      const mockEmbedderInstance = {
        embedTextInput: jest.fn(),
        embedChunks: jest.fn().mockResolvedValue(null),
      };
      mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);

      const openai = new OpenAiLLM();
      const result = await openai.embedChunks(["Test"]);

      expect(result).toEqual([]);
    });
  });

  describe("compressMessages", () => {
    let openai: OpenAiLLM;

    beforeEach(() => {
      openai = new OpenAiLLM();
    });

    it("should compress messages successfully", async () => {
      const { messageArrayCompressor } = require("../../../helpers/chat");
      const compressedMessages = [{ role: "system", content: "Compressed" }];
      messageArrayCompressor.mockResolvedValue(compressedMessages);

      const promptArgs: PromptArgs = {
        systemPrompt: "System prompt",
        userPrompt: "User prompt",
      };
      const rawHistory: ChatMessage[] = [
        { role: "user", content: "Previous message" },
      ];

      const result = await openai.compressMessages(promptArgs, rawHistory);

      expect(result).toEqual(compressedMessages);
    });
  });

  describe("security testing", () => {
    let openai: OpenAiLLM;

    beforeEach(() => {
      openai = new OpenAiLLM();
    });

    it("should handle malicious input in prompts", async () => {
      const maliciousPrompt = "<script>alert('xss')</script>";
      const messages: ChatMessage[] = [
        { role: "user", content: maliciousPrompt },
      ];

      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Safe response from OpenAI" } }],
      });

      const result = await openai.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("Safe response from OpenAI");
    });

    it("should protect API key in error messages", async () => {
      const apiKeyError = new Error("Invalid API key: sk-1234567890abcdef");
      mockOpenAIClient.chat.completions.create.mockRejectedValueOnce(
        apiKeyError
      );
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("Invalid API key: sk-1234567890abcdef");
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(openai.getChatCompletion(messages, {})).rejects.toThrow(
        "Invalid API key: sk-1234567890abcdef"
      );
    });
  });

  describe("performance testing", () => {
    let openai: OpenAiLLM;

    beforeEach(() => {
      openai = new OpenAiLLM();
    });

    it("should handle concurrent requests", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Concurrent response" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const promises = Array(10)
        .fill(null)
        .map(() => openai.getChatCompletion(messages, {}));

      const results = await Promise.all(promises);

      expect(results).toHaveLength(10);
      results.forEach((result) => {
        expect(result?.textResponse).toBe("Concurrent response");
      });
    });

    it("should handle large context windows", async () => {
      const largeContext = Array(100).fill("Large context block").join(" ");
      const messages: ChatMessage[] = Array(50)
        .fill(null)
        .map((_, i) => ({
          role: i % 2 === 0 ? "user" : "assistant",
          content: largeContext,
        }));

      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Handled large context" } }],
      });

      const result = await openai.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("Handled large context");
    });
  });

  describe("edge cases", () => {
    let openai: OpenAiLLM;

    beforeEach(() => {
      openai = new OpenAiLLM();
    });

    it("should handle rate limiting gracefully", async () => {
      const rateLimitError = new Error("Rate limit exceeded");
      (rateLimitError as any).status = 429;

      mockOpenAIClient.chat.completions.create.mockRejectedValueOnce(
        rateLimitError
      );
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("Rate limit exceeded");
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(openai.getChatCompletion(messages, {})).rejects.toThrow(
        "Rate limit exceeded"
      );
    });

    it("should handle environment variable edge cases", () => {
      process.env.OPEN_AI_KEY = "";

      // Constructor doesn't throw for empty key, but initialize does
      const openai = new OpenAiLLM();
      expect(openai).toBeDefined();
    });

    it("should handle different temperature values", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Response" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      // Test with temperature 0
      await openai.getChatCompletion(messages, { temperature: 0 });
      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith(
        expect.objectContaining({ temperature: 0 })
      );

      // Test with temperature 2
      await openai.getChatCompletion(messages, { temperature: 2 });
      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith(
        expect.objectContaining({ temperature: 2 })
      );
    });

    it("should handle unicode and special characters", async () => {
      const unicodeMessages: ChatMessage[] = [
        {
          role: "user",
          content: "Test with emojis 🚀💻🔒 and special chars: ñáéíóú",
        },
        {
          role: "assistant",
          content: "Unicode response: 你好世界 مرحبا بالعالم",
        },
      ];

      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Handled unicode ✓" } }],
      });

      const result = await openai.getChatCompletion(unicodeMessages, {});

      expect(result?.textResponse).toBe("Handled unicode ✓");
    });

    it("should handle empty responses", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await openai.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("");
    });

    it("should handle useDeepSearch getter and setter", () => {
      const openai = new OpenAiLLM();

      expect(openai.useDeepSearch).toBe(false);

      openai.useDeepSearch = true;
      expect(openai.useDeepSearch).toBe(true);

      openai.useDeepSearch = false;
      expect(openai.useDeepSearch).toBe(false);
    });

    it("should handle o1 models with complex system prompts", async () => {
      const o1Model = new OpenAiLLM(null, "o1-preview");
      const promptArgs: PromptArgs = {
        systemPrompt: "You are an expert in multiple domains.",
        contextTexts: ["Context about domain 1", "Context about domain 2"],
        userPrompt: "Analyze this complex problem",
      };

      const result = await o1Model.constructPrompt(promptArgs);

      // Current implementation treats o1 models the same as other models
      // System content is in a separate system message, not prepended to user message
      expect(result[0].role).toBe("system");
      expect(result[0].content).toContain(
        "You are an expert in multiple domains."
      );
      expect(result[0].content).toContain("Context about domain 1");
      expect(result[0].content).toContain("Context about domain 2");
      expect(result[result.length - 1].role).toBe("user");
      expect(result[result.length - 1].content).toBe(
        "Analyze this complex problem"
      );
    });
  });
});
