import { OpenAI as OpenAIA<PERSON> } from "openai";
import type { ChatCompletionMessageParam } from "openai/resources/chat/completions";
import { NativeEmbedder } from "../../EmbeddingEngines/native";
import { handleDefaultStreamResponseV2 } from "../../helpers/chat/responses";
import type { Response as ExpressResponse } from "express";
import { MODEL_MAP } from "../modelMap";
import {
  LLMPerformanceMonitor,
  type OpenAICompatibleStream as LLMPerfOpenAICompatibleStream,
} from "../../helpers/chat/LLMPerformanceMonitor";
import { t } from "../../i18n";
import { formatContextTexts } from "../../helpers";
import {
  LLMProvider,
  TokenLimits,
  ChatMessage,
  PromptArgs,
  CompletionOptions,
  CompletionResponse,
  StreamResponseProps,
  Attachment,
  TextContent,
  ImageContent,
  EmbeddingEngine,
  LLMStreamResponse,
} from "../../../types/ai-providers";

export class OpenAiLLM implements LLMProvider {
  private openai: OpenAIApi;
  public model: string;
  public limits: TokenLimits;
  public embedder: EmbeddingEngine;
  public defaultTemp: number;
  private _useDeepSearch: boolean;
  private settings_suffix: string | null;

  // Add getter and setter for useDeepSearch
  get useDeepSearch(): boolean {
    return this._useDeepSearch;
  }

  set useDeepSearch(value: boolean) {
    this._useDeepSearch = value;
  }

  static async initialize(
    embedder: EmbeddingEngine | null = null,
    modelPreference: string | null = null,
    settings_suffix: string | null = null
  ): Promise<OpenAiLLM> {
    // Check for either the suffixed or non-suffixed API key
    const apiKeyEnvVar = `OPEN_AI_KEY${settings_suffix || ""}`;
    if (!process.env[apiKeyEnvVar] && !process.env.OPEN_AI_KEY) {
      throw new Error(await t("errors.ai.openai.no-key"));
    }
    return new OpenAiLLM(embedder, modelPreference, settings_suffix);
  }

  constructor(
    embedder: EmbeddingEngine | null = null,
    modelPreference: string | null = null,
    settings_suffix: string | null = null,
    useDeepSearch: boolean = false
  ) {
    this._useDeepSearch = useDeepSearch;
    this.settings_suffix = settings_suffix;

    // Try suffixed key first, then fall back to non-suffixed
    const apiKey = this.settings_suffix
      ? process.env[`OPEN_AI_KEY${this.settings_suffix}`] ||
        process.env.OPEN_AI_KEY
      : process.env.OPEN_AI_KEY;

    this.openai = new OpenAIApi({
      apiKey: apiKey,
    });

    this.model =
      modelPreference ||
      (this.settings_suffix
        ? process.env[`OPEN_AI_MODEL_PREF${this.settings_suffix}`] ||
          process.env.OPEN_AI_MODEL_PREF
        : process.env.OPEN_AI_MODEL_PREF) ||
      "chatgpt-4o-latest";

    this.limits = {
      history: this.promptWindowLimit() * 0.15,
      system: this.promptWindowLimit() * 0.15,
      user: this.promptWindowLimit() * 0.7,
    };

    this.embedder = embedder ?? new NativeEmbedder();
    this.defaultTemp = MODEL_MAP.openai.defaults.temperature;
  }

  /**
   * Check if the model is an o1 model.
   */
  get isO1Model(): boolean {
    return this.model.startsWith("o1") || this.model.startsWith("o3-mini");
  }

  private async appendContext(contextTexts: string[] = []): Promise<string> {
    if (!contextTexts || !contextTexts.length) return "";
    return await formatContextTexts(contextTexts);
  }

  streamingEnabled(): boolean {
    return "streamGetChatCompletion" in this;
  }

  static promptWindowLimit(modelName: string): number {
    if (modelName && MODEL_MAP?.openai?.models?.[modelName]) {
      return MODEL_MAP.openai.models[modelName].context;
    }
    return MODEL_MAP.openai.defaults.contextWindow;
  }

  promptWindowLimit(): number {
    if (MODEL_MAP?.openai?.models?.[this.model]) {
      return MODEL_MAP.openai.models[this.model].context;
    }
    return MODEL_MAP.openai.defaults.contextWindow;
  }

  /**
   * Used when custom AI configurations are active.
   * Returns the standard prompt window limit for the current model.
   * This allows us to provide consistent token limits across different configurations.
   */
  customPromptWindowLimit(): number {
    return this.promptWindowLimit();
  }

  // Short circuit if name has 'gpt' since we now fetch models from OpenAI API
  // via the user API key, so the model must be relevant and real.
  // and if somehow it is not, chat will fail but that is caught.
  // we don't want to hit the OpenAI api every chat because it will get spammed
  // and introduce latency for no reason.
  async isValidChatCompletionModel(modelName: string = ""): Promise<boolean> {
    const lowerName = modelName.toLowerCase();
    const isPreset =
      lowerName.includes("gpt") ||
      lowerName.includes("o1") ||
      lowerName.includes("o3");
    if (isPreset) return true;

    const model = await this.openai.models
      .retrieve(modelName)
      .then((modelObj) => modelObj)
      .catch(() => null);
    return !!model;
  }

  /**
   * Generates appropriate content array for a message + attachments.
   */
  private generateContent({
    userPrompt,
    attachments = [],
  }: {
    userPrompt: string;
    attachments?: Attachment[];
  }): string | Array<TextContent | ImageContent> {
    if (!attachments.length) {
      return userPrompt;
    }

    const content: Array<TextContent | ImageContent> = [
      { type: "text", text: userPrompt },
    ];
    for (const attachment of attachments) {
      // Only process as image if it's an image type
      if (attachment?.type?.startsWith("image/")) {
        content.push({
          type: "image_url",
          image_url: {
            url: attachment.contentString,
            detail: "high",
          },
        });
      } else {
        // For non-image files, check if content is already wrapped
        const isAlreadyWrapped =
          /<(USER UPLOADED FILE|[^>]+)>[\s\S]*?<\/\1>/g.test(
            attachment.contentString
          );
        content.push({
          type: "text",
          text: isAlreadyWrapped
            ? attachment.contentString
            : `<${t("chatboxdnd.uploaded-file-tag")}>\n${attachment.contentString}\n</${t("chatboxdnd.uploaded-file-tag")}>`,
        });
      }
    }
    return content;
  }

  /**
   * Construct the user prompt for this model.
   */
  async constructPrompt({
    systemPrompt = "",
    contextTexts = [],
    chatHistory = [],
    userPrompt = "",
    attachments = [], // This is the specific attachment for only this prompt
  }: PromptArgs): Promise<ChatMessage[]> {
    const formattedContext = await this.appendContext(contextTexts);
    const prompt: ChatMessage = {
      role: "system",
      content: `${systemPrompt}${formattedContext}`,
    };
    return [
      prompt,
      ...chatHistory,
      {
        role: "user",
        content: this.generateContent({ userPrompt, attachments }),
      },
    ];
  }

  async getChatCompletion(
    messages: ChatMessage[] | null,
    { temperature = 0.7 }: CompletionOptions
  ): Promise<CompletionResponse | null> {
    if (!messages || !(await this.isValidChatCompletionModel(this.model)))
      throw new Error(
        `OpenAI chat: ${this.model} is not valid for chat completion!`
      );

    const result = await LLMPerformanceMonitor.measureAsyncFunction(
      this.openai.chat.completions
        .create({
          model: this.model,
          messages: messages as ChatCompletionMessageParam[],
          temperature:
            this.isO1Model || this.model === "o3" || this.model === "o4-mini"
              ? 1
              : temperature, // o1, o3, o3-mini, o4-mini only accept temperature 1
        })
        .catch((e: Error) => {
          throw new Error(e.message);
        })
    );

    if (
      !Object.prototype.hasOwnProperty.call(result.output, "choices") ||
      result.output.choices.length === 0
    )
      return null;

    return {
      textResponse: result.output.choices[0].message.content || "",
      metrics: {
        prompt_tokens: result.output?.usage?.prompt_tokens || 0,
        completion_tokens: result.output?.usage?.completion_tokens || 0,
        total_tokens: result.output?.usage?.total_tokens || 0,
        outputTps: result.output.usage
          ? result.output.usage.completion_tokens / result.duration
          : undefined,
        duration: result.duration,
      },
    };
  }

  async streamGetChatCompletion(
    messages: ChatMessage[] | null,
    { temperature = 0.7 }: CompletionOptions
  ): Promise<LLMStreamResponse> {
    if (!messages || !(await this.isValidChatCompletionModel(this.model)))
      throw new Error(
        `OpenAI chat: ${this.model} is not valid for chat completion!`
      );

    const streamPromise = this.openai.chat.completions.create({
      model: this.model,
      stream: true,
      messages: messages as ChatCompletionMessageParam[],
      temperature:
        this.isO1Model || this.model === "o3" || this.model === "o4-mini"
          ? 1
          : temperature, // o1, o3, o3-mini, o4-mini only accept temperature 1
    });

    const measuredStreamRequest = await LLMPerformanceMonitor.measureStream(
      streamPromise as unknown as Promise<LLMPerfOpenAICompatibleStream>,
      messages.map((msg) => ({
        content:
          typeof msg.content === "string"
            ? msg.content
            : JSON.stringify(msg.content),
        role: msg.role,
      }))
      // runPromptTokenCalculation: true - We manually count the tokens because OpenAI does not provide them in the stream
      // since we are not using the OpenAI API version that supports this `stream_options` param.
    );

    return {
      stream: measuredStreamRequest,
      endMeasurement: measuredStreamRequest.endMeasurement,
    };
  }

  handleStream(
    response: ExpressResponse,
    stream: AsyncIterable<unknown>,
    responseProps: StreamResponseProps
  ): Promise<string> {
    return handleDefaultStreamResponseV2(response, stream, responseProps);
  }

  // Simple wrapper for dynamic embedder & normalize interface for all LLM implementations
  async embedTextInput(textInput: string): Promise<number[]> {
    return await this.embedder.embedTextInput(textInput);
  }

  async embedChunks(textChunks: string[] = []): Promise<number[][]> {
    return (await this.embedder.embedChunks(textChunks)) || [];
  }

  async compressMessages(
    promptArgs: PromptArgs = {},
    rawHistory: ChatMessage[] = []
  ): Promise<ChatMessage[]> {
    const { messageArrayCompressor } = require("../../helpers/chat");
    const messageArray = await this.constructPrompt(promptArgs);
    return await messageArrayCompressor(
      this,
      messageArray,
      rawHistory,
      [],
      [],
      promptArgs.maxAllowedTokens
    );
  }
}

export default {
  OpenAiLLM,
};
