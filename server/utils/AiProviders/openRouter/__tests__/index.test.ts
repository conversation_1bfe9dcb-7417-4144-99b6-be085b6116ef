import { OpenRouterLLM } from "../index";
import { NativeEmbedder } from "../../../EmbeddingEngines/native";
import { LLMPerformanceMonitor } from "../../../helpers/chat/LLMPerformanceMonitor";
import { handleDefaultStreamResponseV2 } from "../../../helpers/chat/responses";
import { formatContextTexts } from "../../../helpers";
import type { Response as ExpressResponse } from "express";
import type {
  ChatMessage,
  PromptArgs,
  EmbeddingEngine,
  StreamResponseProps,
} from "../../../../types/ai-providers";

// Mock dependencies
jest.mock("openai");
jest.mock("../../../EmbeddingEngines/native");
jest.mock("../../../helpers/chat/LLMPerformanceMonitor");
jest.mock("../../../helpers/chat/responses");
jest.mock("../../../helpers", () => ({
  formatContextTexts: jest.fn().mockImplementation(async (texts: string[]) => {
    return texts.join("\n\n---CONTEXT---\n\n");
  }),
}));
jest.mock("../../../helpers/chat", () => ({
  messageArrayCompressor: jest
    .fn()
    .mockImplementation(async (provider, messages, _rawHistory) => {
      return messages;
    }),
}));

// Mock OpenAI client
const mockOpenAIClient = {
  chat: {
    completions: {
      create: jest.fn(),
    },
  },
};

// Import OpenAI after mocking
import { OpenAI as OpenAIApi } from "openai";
const MockOpenAI = OpenAIApi as jest.MockedClass<typeof OpenAIApi>;

describe("OpenRouterLLM", () => {
  const mockNativeEmbedder = NativeEmbedder as jest.MockedClass<
    typeof NativeEmbedder
  >;
  const mockLLMPerformanceMonitor = LLMPerformanceMonitor as jest.MockedClass<
    typeof LLMPerformanceMonitor
  >;
  const mockHandleDefaultStreamResponseV2 =
    handleDefaultStreamResponseV2 as jest.MockedFunction<
      typeof handleDefaultStreamResponseV2
    >;
  const mockFormatContextTexts = formatContextTexts as jest.MockedFunction<
    typeof formatContextTexts
  >;

  let originalEnv: typeof process.env;
  let consoleErrorSpy: jest.SpyInstance;

  beforeEach(() => {
    // Store original environment
    originalEnv = { ...process.env };

    // Set up required environment variables
    process.env.OPENROUTER_API_KEY = "test-openrouter-api-key";
    process.env.OPENROUTER_MODEL_PREF = "openai/gpt-4o";
    process.env.OPENROUTER_MODEL_TOKEN_LIMIT = "128000";
    process.env.SERVER_BASE_URL = "http://localhost:3001";

    // Set up non-suffix env vars for static method tests
    // The beforeEach already sets OPENROUTER_MODEL_TOKEN_LIMIT to "128000"

    // Clear all mocks
    jest.clearAllMocks();

    // Spy on console methods
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();

    // Mock OpenAI constructor
    MockOpenAI.mockImplementation(() => mockOpenAIClient as any);

    // Mock NativeEmbedder
    mockNativeEmbedder.mockClear();
    const mockEmbedderInstance = {
      embedTextInput: jest.fn().mockResolvedValue([0.1, 0.2, 0.3]),
      embedChunks: jest.fn().mockResolvedValue([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]),
    };
    mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);

    // Mock LLM Performance Monitor
    mockLLMPerformanceMonitor.measureAsyncFunction = jest
      .fn()
      .mockImplementation(async (promise) => {
        const output = await promise;
        return { output, duration: 1000 };
      });
    mockLLMPerformanceMonitor.measureStream = jest
      .fn()
      .mockImplementation(async (streamPromise) => {
        const stream = await streamPromise;
        return {
          [Symbol.asyncIterator]: stream[Symbol.asyncIterator],
          endMeasurement: jest.fn().mockReturnValue({
            prompt_tokens: 100,
            completion_tokens: 50,
            total_tokens: 150,
            outputTps: 50,
            duration: 1000,
          }),
        };
      });

    // Mock stream response handler
    mockHandleDefaultStreamResponseV2.mockResolvedValue("Streamed response");
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
    consoleErrorSpy.mockRestore();
  });

  describe("constructor and initialization", () => {
    it("should initialize with default settings", () => {
      const openrouter = new OpenRouterLLM();

      expect(openrouter.model).toBe("openai/gpt-4o");
      expect(openrouter.limits).toEqual({
        history: 128000 * 0.15,
        system: 128000 * 0.15,
        user: 128000 * 0.7,
      });
      expect(openrouter.defaultTemp).toBe(0.7);
      expect(openrouter.embedder).toBeDefined();
    });

    it("should use custom embedder when provided", () => {
      const customEmbedder = {} as EmbeddingEngine;
      const openrouter = new OpenRouterLLM(customEmbedder);

      expect(openrouter.embedder).toBe(customEmbedder);
    });

    it("should use model preference when provided", () => {
      const openrouter = new OpenRouterLLM(null, "anthropic/claude-3-sonnet");

      expect(openrouter.model).toBe("anthropic/claude-3-sonnet");
    });

    it("should use environment model preference", () => {
      process.env.OPENROUTER_MODEL_PREF = "meta-llama/llama-3.1-8b-instruct";

      const openrouter = new OpenRouterLLM();

      expect(openrouter.model).toBe("meta-llama/llama-3.1-8b-instruct");
    });

    it("should use settings suffix for environment variables", () => {
      process.env.OPENROUTER_API_KEY_CUSTOM = "custom-api-key";
      process.env.OPENROUTER_MODEL_PREF_CUSTOM = "custom-model";
      process.env.OPENROUTER_MODEL_TOKEN_LIMIT_CUSTOM = "200000";

      const openrouter = new OpenRouterLLM(null, null, "_CUSTOM");

      expect(openrouter.model).toBe("custom-model");
      expect(MockOpenAI).toHaveBeenCalledWith({
        baseURL: "https://openrouter.ai/api/v1",
        apiKey: "custom-api-key",
        defaultHeaders: {
          "HTTP-Referer": "http://localhost:3001",
          "X-Title": "ISTLegal",
        },
      });
    });

    it("should fall back to default model when no preference is set", () => {
      delete process.env.OPENROUTER_MODEL_PREF;

      const openrouter = new OpenRouterLLM();

      expect(openrouter.model).toBe("openai/gpt-3.5-turbo");
    });

    it("should throw error when API key is missing", () => {
      delete process.env.OPENROUTER_API_KEY;

      expect(() => new OpenRouterLLM()).toThrow(
        "No OpenRouter API key was set."
      );
    });

    it("should throw error when API key is empty", () => {
      process.env.OPENROUTER_API_KEY = "";

      expect(() => new OpenRouterLLM()).toThrow(
        "No OpenRouter API key was set."
      );
    });

    it("should initialize OpenAI client with correct configuration", () => {
      new OpenRouterLLM();

      expect(MockOpenAI).toHaveBeenCalledWith({
        baseURL: "https://openrouter.ai/api/v1",
        apiKey: "test-openrouter-api-key",
        defaultHeaders: {
          "HTTP-Referer": "http://localhost:3001",
          "X-Title": "ISTLegal",
        },
      });
    });

    it("should use default referer when SERVER_BASE_URL is not set", () => {
      delete process.env.SERVER_BASE_URL;

      new OpenRouterLLM();

      expect(MockOpenAI).toHaveBeenCalledWith({
        baseURL: "https://openrouter.ai/api/v1",
        apiKey: "test-openrouter-api-key",
        defaultHeaders: {
          "HTTP-Referer": "http://localhost:3001",
          "X-Title": "ISTLegal",
        },
      });
    });
  });

  describe("static promptWindowLimit method", () => {
    it("should return configured token limit", () => {
      // The static method uses the default env var without suffix
      const originalValue = process.env.OPENROUTER_MODEL_TOKEN_LIMIT;
      process.env.OPENROUTER_MODEL_TOKEN_LIMIT = "4096";
      expect(OpenRouterLLM.promptWindowLimit()).toBe(4096);
      process.env.OPENROUTER_MODEL_TOKEN_LIMIT = originalValue;
    });

    it("should use default when token limit is not set", () => {
      const originalValue = process.env.OPENROUTER_MODEL_TOKEN_LIMIT;
      delete process.env.OPENROUTER_MODEL_TOKEN_LIMIT;

      expect(OpenRouterLLM.promptWindowLimit()).toBe(4096);

      if (originalValue !== undefined) {
        process.env.OPENROUTER_MODEL_TOKEN_LIMIT = originalValue;
      }
    });

    it("should throw error when token limit is invalid", () => {
      const originalValue = process.env.OPENROUTER_MODEL_TOKEN_LIMIT;
      process.env.OPENROUTER_MODEL_TOKEN_LIMIT = "invalid";

      expect(() => OpenRouterLLM.promptWindowLimit()).toThrow(
        "No OpenRouter token context limit was set."
      );

      if (originalValue !== undefined) {
        process.env.OPENROUTER_MODEL_TOKEN_LIMIT = originalValue;
      }
    });

    it("should handle zero token limit", () => {
      const originalValue = process.env.OPENROUTER_MODEL_TOKEN_LIMIT;
      process.env.OPENROUTER_MODEL_TOKEN_LIMIT = "0";

      expect(OpenRouterLLM.promptWindowLimit()).toBe(0);

      process.env.OPENROUTER_MODEL_TOKEN_LIMIT = originalValue;
    });

    it("should handle large token limits", () => {
      const originalValue = process.env.OPENROUTER_MODEL_TOKEN_LIMIT;
      process.env.OPENROUTER_MODEL_TOKEN_LIMIT = "1000000";

      expect(OpenRouterLLM.promptWindowLimit()).toBe(1000000);

      process.env.OPENROUTER_MODEL_TOKEN_LIMIT = originalValue;
    });
  });

  describe("instance promptWindowLimit methods", () => {
    it("should return instance prompt window limit", () => {
      const openrouter = new OpenRouterLLM();
      expect(openrouter.promptWindowLimit()).toBe(128000);
    });

    it("should return custom prompt window limit", () => {
      const openrouter = new OpenRouterLLM();
      expect(openrouter.customPromptWindowLimit()).toBe(128000);
    });

    it("should use settings suffix for token limit", () => {
      process.env.OPENROUTER_MODEL_TOKEN_LIMIT_TEST = "256000";
      process.env.OPENROUTER_API_KEY_TEST = "test-key";

      const openrouter = new OpenRouterLLM(null, null, "_TEST");
      expect(openrouter.promptWindowLimit()).toBe(256000);
    });

    it("should throw error when instance token limit is invalid", () => {
      process.env.OPENROUTER_MODEL_TOKEN_LIMIT_BAD = "not-a-number";
      process.env.OPENROUTER_API_KEY_BAD = "test-key";

      expect(() => new OpenRouterLLM(null, null, "_BAD")).toThrow(
        "No OpenRouter token context limit was set."
      );
    });

    it("should fall back to default when suffix limit is not set", () => {
      process.env.OPENROUTER_API_KEY_NONEXISTENT = "test-key";
      const openrouter = new OpenRouterLLM(null, null, "_NONEXISTENT");
      expect(openrouter.promptWindowLimit()).toBe(4096);
    });
  });

  describe("streamingEnabled", () => {
    it("should return true since streamGetChatCompletion is defined", () => {
      const openrouter = new OpenRouterLLM();
      expect(openrouter.streamingEnabled()).toBe(true);
    });
  });

  describe("isValidChatCompletionModel", () => {
    it("should return true for any model (OpenRouter supports many)", async () => {
      const openrouter = new OpenRouterLLM();

      expect(await openrouter.isValidChatCompletionModel("openai/gpt-4")).toBe(
        true
      );
      expect(
        await openrouter.isValidChatCompletionModel("anthropic/claude-3-sonnet")
      ).toBe(true);
      expect(
        await openrouter.isValidChatCompletionModel(
          "meta-llama/llama-3.1-8b-instruct"
        )
      ).toBe(true);
      expect(await openrouter.isValidChatCompletionModel("unknown-model")).toBe(
        true
      );
    });

    it("should handle empty model name", async () => {
      const openrouter = new OpenRouterLLM();
      expect(await openrouter.isValidChatCompletionModel("")).toBe(true);
    });

    it("should handle undefined model name", async () => {
      const openrouter = new OpenRouterLLM();
      expect(
        await openrouter.isValidChatCompletionModel(undefined as any)
      ).toBe(true);
    });
  });

  describe("constructPrompt", () => {
    let openrouter: OpenRouterLLM;

    beforeEach(() => {
      openrouter = new OpenRouterLLM();
    });

    it("should construct prompt with all components", async () => {
      const promptArgs: PromptArgs = {
        systemPrompt: "You are a helpful assistant via OpenRouter.",
        contextTexts: ["Context 1", "Context 2"],
        chatHistory: [
          { role: "user", content: "Hello" },
          { role: "assistant", content: "Hi there!" },
        ],
        userPrompt: "How are you?",
      };

      const result = await openrouter.constructPrompt(promptArgs);

      expect(result).toEqual([
        {
          role: "system",
          content:
            "You are a helpful assistant via OpenRouter.Context 1\n\n---CONTEXT---\n\nContext 2",
        },
        { role: "user", content: "Hello" },
        { role: "assistant", content: "Hi there!" },
        { role: "user", content: "How are you?" },
      ]);
    });

    it("should handle empty context texts", async () => {
      const promptArgs: PromptArgs = {
        systemPrompt: "System prompt",
        contextTexts: [],
        chatHistory: [],
        userPrompt: "User prompt",
      };

      const result = await openrouter.constructPrompt(promptArgs);

      expect(result).toEqual([
        { role: "system", content: "System prompt" },
        { role: "user", content: "User prompt" },
      ]);
    });

    it("should handle missing optional parameters", async () => {
      const promptArgs: PromptArgs = {
        userPrompt: "User prompt",
      };

      const result = await openrouter.constructPrompt(promptArgs);

      expect(result).toEqual([
        { role: "system", content: "" },
        { role: "user", content: "User prompt" },
      ]);
    });

    it("should call formatContextTexts with correct arguments", async () => {
      const contextTexts = ["Context 1", "Context 2"];
      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        contextTexts,
        userPrompt: "User",
      };

      await openrouter.constructPrompt(promptArgs);

      expect(mockFormatContextTexts).toHaveBeenCalledWith(contextTexts);
    });

    it("should handle null context texts", async () => {
      mockFormatContextTexts.mockResolvedValueOnce("");

      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        contextTexts: null as any,
        userPrompt: "User",
      };

      const result = await openrouter.constructPrompt(promptArgs);

      expect(result[0].content).toBe("System");
    });

    it("should handle undefined context texts", async () => {
      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        userPrompt: "User",
      };

      const result = await openrouter.constructPrompt(promptArgs);

      expect(result[0].content).toBe("System");
    });
  });

  describe("getChatCompletion", () => {
    let openrouter: OpenRouterLLM;

    beforeEach(() => {
      openrouter = new OpenRouterLLM();
      mockOpenAIClient.chat.completions.create.mockClear();
    });

    it("should return completion response successfully", async () => {
      const mockResponse = {
        choices: [
          {
            message: {
              content: "This is a response from OpenRouter!",
            },
          },
        ],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 20,
          total_tokens: 30,
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: "user", content: "Test message" },
      ];

      const result = await openrouter.getChatCompletion(messages, {
        temperature: 0.5,
      });

      expect(result).toEqual({
        textResponse: "This is a response from OpenRouter!",
        metrics: {
          prompt_tokens: 10,
          completion_tokens: 20,
          total_tokens: 30,
          outputTps: 20 / 1000, // 20 tokens / 1000 ms
          duration: 1000,
        },
      });

      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith({
        model: "openai/gpt-4o",
        messages,
        temperature: 0.5,
      });
    });

    it("should use default temperature when not provided", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Response" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await openrouter.getChatCompletion(messages, {});

      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith({
        model: "openai/gpt-4o",
        messages,
        temperature: 0.7,
      });
    });

    it("should return null for null messages", async () => {
      const result = await openrouter.getChatCompletion(null, {});

      expect(result).toBeNull();
    });

    it("should handle API errors", async () => {
      const error = new Error("OpenRouter API request failed");
      mockOpenAIClient.chat.completions.create.mockRejectedValue(error);
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("OpenRouter API request failed");
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(openrouter.getChatCompletion(messages, {})).rejects.toThrow(
        "OpenRouter API request failed"
      );
    });

    it("should return null for empty choices", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await openrouter.getChatCompletion(messages, {});

      expect(result).toBeNull();
    });

    it("should return null for missing choices property", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        invalid: "response",
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await openrouter.getChatCompletion(messages, {});

      expect(result).toBeNull();
    });

    it("should handle missing usage data", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Response without usage" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await openrouter.getChatCompletion(messages, {});

      expect(result?.metrics).toEqual({
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
        outputTps: 0,
        duration: 1000,
      });
    });

    it("should handle null message content", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: null } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await openrouter.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("");
    });

    it("should handle undefined message content", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: {} }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await openrouter.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("");
    });

    it("should measure performance correctly", async () => {
      const mockResponse = {
        choices: [{ message: { content: "Measured response" } }],
        usage: {
          prompt_tokens: 100,
          completion_tokens: 50,
          total_tokens: 150,
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockResponse);
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementation(async (promise) => {
          const output = await promise;
          return { output, duration: 2500 };
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await openrouter.getChatCompletion(messages, {});

      expect(result?.metrics).toEqual({
        prompt_tokens: 100,
        completion_tokens: 50,
        total_tokens: 150,
        outputTps: 50 / 2500,
        duration: 2500,
      });
    });
  });

  describe("streamGetChatCompletion", () => {
    let openrouter: OpenRouterLLM;

    beforeEach(() => {
      openrouter = new OpenRouterLLM();
      mockOpenAIClient.chat.completions.create.mockClear();
    });

    it("should stream completion successfully", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield {
            choices: [{ delta: { content: "OpenRouter" } }],
          };
          yield {
            choices: [{ delta: { content: " streaming works!" } }],
          };
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockStream);

      const messages: ChatMessage[] = [
        { role: "user", content: "Test streaming" },
      ];

      const result = await openrouter.streamGetChatCompletion(messages, {
        temperature: 0.8,
      });

      expect(result.stream).toBeDefined();
      expect(result.endMeasurement).toBeDefined();

      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith({
        model: "openai/gpt-4o",
        stream: true,
        messages,
        temperature: 0.8,
      });
    });

    it("should use default temperature for streaming", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { choices: [{ delta: { content: "stream" } }] };
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockStream);

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await openrouter.streamGetChatCompletion(messages, {});

      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith({
        model: "openai/gpt-4o",
        stream: true,
        messages,
        temperature: 0.7,
      });
    });

    it("should throw error for null messages", async () => {
      await expect(
        openrouter.streamGetChatCompletion(null, {})
      ).rejects.toThrow("No messages provided");
    });

    it("should handle stream creation errors", async () => {
      const error = new Error("Stream creation failed");
      mockOpenAIClient.chat.completions.create.mockRejectedValue(error);

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(
        openrouter.streamGetChatCompletion(messages, {})
      ).rejects.toThrow("Stream creation failed");
    });

    it("should properly measure performance for streaming", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { choices: [{ delta: { content: "test" } }] };
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockStream);

      const messages: ChatMessage[] = [
        { role: "user", content: "Test" },
        { role: "assistant", content: "Response" },
      ];

      await openrouter.streamGetChatCompletion(messages, {});

      expect(mockLLMPerformanceMonitor.measureStream).toHaveBeenCalledWith(
        expect.any(Promise),
        [
          { content: "Test", role: "user" },
          { content: "Response", role: "assistant" },
        ]
      );
    });

    it("should handle complex message content in performance measurement", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { choices: [{ delta: { content: "test" } }] };
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockStream);

      const messages: ChatMessage[] = [
        {
          role: "user",
          content: [{ type: "text", text: "Analyze this" }],
        },
      ];

      await openrouter.streamGetChatCompletion(messages, {});

      expect(mockLLMPerformanceMonitor.measureStream).toHaveBeenCalledWith(
        expect.any(Promise),
        [
          {
            content: JSON.stringify([{ type: "text", text: "Analyze this" }]),
            role: "user",
          },
        ]
      );
    });
  });

  describe("handleStream", () => {
    let openrouter: OpenRouterLLM;
    let mockResponse: Partial<ExpressResponse>;

    beforeEach(() => {
      openrouter = new OpenRouterLLM();
      mockResponse = {
        write: jest.fn(),
        end: jest.fn(),
      };
    });

    it("should handle stream successfully", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { text: "OpenRouter chunk 1" };
          yield { text: "OpenRouter chunk 2" };
        },
      };

      const responseProps: StreamResponseProps = {
        uuid: "test-uuid",
        sources: [],
      };

      await openrouter.handleStream(
        mockResponse as ExpressResponse,
        mockStream,
        responseProps
      );

      expect(mockHandleDefaultStreamResponseV2).toHaveBeenCalledWith(
        mockResponse,
        mockStream,
        responseProps
      );
    });

    it("should pass through all response properties", async () => {
      const mockStream = { [Symbol.asyncIterator]: jest.fn() };
      const responseProps: StreamResponseProps = {
        uuid: "test-uuid",
        sources: [{ url: "http://example.com", title: "Example" }],
      };

      await openrouter.handleStream(
        mockResponse as ExpressResponse,
        mockStream,
        responseProps
      );

      expect(mockHandleDefaultStreamResponseV2).toHaveBeenCalledWith(
        mockResponse,
        mockStream,
        responseProps
      );
    });

    it("should handle empty stream", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          // Empty stream
        },
      };

      const responseProps: StreamResponseProps = {
        uuid: "empty-stream",
        sources: [],
      };

      await openrouter.handleStream(
        mockResponse as ExpressResponse,
        mockStream,
        responseProps
      );

      expect(mockHandleDefaultStreamResponseV2).toHaveBeenCalledWith(
        mockResponse,
        mockStream,
        responseProps
      );
    });
  });

  describe("embedding methods", () => {
    let openrouter: OpenRouterLLM;

    beforeEach(() => {
      openrouter = new OpenRouterLLM();
    });

    it("should embed text input successfully", async () => {
      const result = await openrouter.embedTextInput(
        "Test text for OpenRouter"
      );

      expect(result).toEqual([0.1, 0.2, 0.3]);
      expect(openrouter.embedder.embedTextInput).toHaveBeenCalledWith(
        "Test text for OpenRouter"
      );
    });

    it("should embed chunks successfully", async () => {
      const chunks = ["Chunk 1 for OpenRouter", "Chunk 2 for OpenRouter"];
      const result = await openrouter.embedChunks(chunks);

      expect(result).toEqual([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]);
      expect(openrouter.embedder.embedChunks).toHaveBeenCalledWith(chunks);
    });

    it("should handle empty chunks array", async () => {
      const result = await openrouter.embedChunks([]);

      expect(result).toEqual([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]);
    });

    it("should handle null result from embedder", async () => {
      const mockEmbedderInstance = {
        embedTextInput: jest.fn(),
        embedChunks: jest.fn().mockResolvedValue(null),
      };
      mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);

      const openrouter = new OpenRouterLLM();
      const result = await openrouter.embedChunks(["Test"]);

      expect(result).toEqual([]);
    });

    it("should handle embedding errors", async () => {
      const error = new Error("Embedding failed");
      const mockEmbedderInstance = {
        embedTextInput: jest.fn().mockRejectedValue(error),
        embedChunks: jest.fn(),
      };
      mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);

      const openrouter = new OpenRouterLLM();

      await expect(openrouter.embedTextInput("Test text")).rejects.toThrow(
        "Embedding failed"
      );
    });

    it("should handle large text input", async () => {
      const largeText = "Large text ".repeat(10000);

      await openrouter.embedTextInput(largeText);

      expect(openrouter.embedder.embedTextInput).toHaveBeenCalledWith(
        largeText
      );
    });

    it("should handle many chunks", async () => {
      const manyChunks = Array(1000)
        .fill("Chunk")
        .map((_, i) => `Chunk ${i}`);

      await openrouter.embedChunks(manyChunks);

      expect(openrouter.embedder.embedChunks).toHaveBeenCalledWith(manyChunks);
    });
  });

  describe("compressMessages", () => {
    let openrouter: OpenRouterLLM;

    beforeEach(() => {
      openrouter = new OpenRouterLLM();
    });

    it("should compress messages successfully", async () => {
      const { messageArrayCompressor } = require("../../../helpers/chat");
      const compressedMessages = [{ role: "system", content: "Compressed" }];
      messageArrayCompressor.mockResolvedValue(compressedMessages);

      const promptArgs: PromptArgs = {
        systemPrompt: "You are an assistant via OpenRouter",
        userPrompt: "Tell me about OpenRouter",
      };
      const rawHistory: ChatMessage[] = [
        { role: "user", content: "Previous message" },
      ];

      const result = await openrouter.compressMessages(promptArgs, rawHistory);

      expect(result).toEqual(compressedMessages);
      expect(messageArrayCompressor).toHaveBeenCalledWith(
        openrouter,
        expect.arrayContaining([
          { role: "system", content: "You are an assistant via OpenRouter" },
          { role: "user", content: "Tell me about OpenRouter" },
        ]),
        rawHistory
      );
    });

    it("should handle empty parameters", async () => {
      const { messageArrayCompressor } = require("../../../helpers/chat");
      messageArrayCompressor.mockResolvedValue([]);

      const result = await openrouter.compressMessages();

      expect(result).toEqual([]);
    });

    it("should handle compression errors", async () => {
      const { messageArrayCompressor } = require("../../../helpers/chat");
      const compressionError = new Error("Compression failed");
      messageArrayCompressor.mockRejectedValue(compressionError);

      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        userPrompt: "User",
      };

      await expect(openrouter.compressMessages(promptArgs, [])).rejects.toThrow(
        "Compression failed"
      );
    });
  });

  describe("security testing", () => {
    let openrouter: OpenRouterLLM;

    beforeEach(() => {
      openrouter = new OpenRouterLLM();
    });

    it("should handle malicious input in prompts", async () => {
      const maliciousPrompt = "<script>alert('xss')</script>";
      const messages: ChatMessage[] = [
        { role: "user", content: maliciousPrompt },
      ];

      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Safe response from OpenRouter" } }],
      });

      const result = await openrouter.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("Safe response from OpenRouter");
      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith({
        model: "openai/gpt-4o",
        messages: [{ role: "user", content: maliciousPrompt }],
        temperature: 0.7,
      });
    });

    it("should protect API key in error messages", async () => {
      const apiKeyError = new Error("Invalid API key: or-1234567890abcdef");
      mockOpenAIClient.chat.completions.create.mockRejectedValue(apiKeyError);
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("Invalid API key: or-1234567890abcdef");
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(openrouter.getChatCompletion(messages, {})).rejects.toThrow(
        "Invalid API key: or-1234567890abcdef"
      );
    });

    it("should handle extremely long inputs safely", async () => {
      const veryLongPrompt = "x".repeat(1000000); // 1MB of text
      const messages: ChatMessage[] = [
        { role: "user", content: veryLongPrompt },
      ];

      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Handled long input" } }],
      });

      const result = await openrouter.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("Handled long input");
    });

    it("should handle unicode edge cases", async () => {
      const unicodeMessages: ChatMessage[] = [
        {
          role: "user",
          content: "Test with emojis 🚀💻🔒 and special chars: ñáéíóú",
        },
        {
          role: "assistant",
          content: "Unicode response: 你好世界 مرحبا بالعالم",
        },
      ];

      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Handled unicode via OpenRouter" } }],
      });

      const result = await openrouter.getChatCompletion(unicodeMessages, {});

      expect(result?.textResponse).toBe("Handled unicode via OpenRouter");
    });

    it("should handle SQL injection attempts in system prompt", async () => {
      const sqlInjectionPrompt = {
        systemPrompt: "'; DROP TABLE users; --",
        userPrompt: "SELECT * FROM users WHERE id = '1' OR '1'='1'",
      };

      const messages = await openrouter.constructPrompt(sqlInjectionPrompt);

      expect(messages[0].content).toContain("'; DROP TABLE users; --");
      expect(messages[1].content).toContain("SELECT * FROM users");
    });

    it("should handle NoSQL injection attempts", async () => {
      const nosqlInjection = {
        systemPrompt: '{"$ne": null}',
        userPrompt: '{"$where": "this.username == this.password"}',
      };

      const messages = await openrouter.constructPrompt(nosqlInjection);

      expect(messages[0].content).toContain('{"$ne": null}');
      expect(messages[1].content).toContain('{"$where"');
    });
  });

  describe("performance testing", () => {
    let openrouter: OpenRouterLLM;

    beforeEach(() => {
      openrouter = new OpenRouterLLM();
    });

    it("should handle concurrent requests", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Concurrent OpenRouter response" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const promises = Array(10)
        .fill(null)
        .map(() => openrouter.getChatCompletion(messages, {}));

      const results = await Promise.all(promises);

      expect(results).toHaveLength(10);
      results.forEach((result) => {
        expect(result?.textResponse).toBe("Concurrent OpenRouter response");
      });
    });

    it("should handle large context windows", async () => {
      const largeContext = Array(100).fill("Large context block").join(" ");
      const messages: ChatMessage[] = Array(50)
        .fill(null)
        .map((_, i) => ({
          role: i % 2 === 0 ? "user" : "assistant",
          content: largeContext,
        }));

      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Handled large context" } }],
      });

      const result = await openrouter.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("Handled large context");
    });

    it("should measure performance accurately", async () => {
      const mockResponse = {
        choices: [{ message: { content: "Timed response" } }],
        usage: {
          prompt_tokens: 100,
          completion_tokens: 50,
          total_tokens: 150,
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockResponse);
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementation(async (promise) => {
          const output = await promise;
          return { output, duration: 2500 };
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await openrouter.getChatCompletion(messages, {});

      expect(result?.metrics).toEqual({
        prompt_tokens: 100,
        completion_tokens: 50,
        total_tokens: 150,
        outputTps: 50 / 2500, // 50 tokens / 2500 ms
        duration: 2500,
      });
    });

    it("should handle streaming with backpressure", async () => {
      let yieldCount = 0;
      const backpressureStream = {
        [Symbol.asyncIterator]: async function* () {
          while (yieldCount < 1000) {
            yieldCount++;
            yield { choices: [{ delta: { content: `chunk${yieldCount}` } }] };
            // Simulate backpressure with small delay
            if (yieldCount % 100 === 0) {
              await new Promise((resolve) => setTimeout(resolve, 1));
            }
          }
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(
        backpressureStream
      );

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await openrouter.streamGetChatCompletion(messages, {});

      expect(result).toBeDefined();
      expect(result.stream).toBeDefined();
    });

    it("should handle timeout scenarios", async () => {
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Request timeout")), 100)
      );

      mockOpenAIClient.chat.completions.create.mockReturnValue(timeoutPromise);
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementation(async (promise) => {
          const output = await promise;
          return { output, duration: 1000 };
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(openrouter.getChatCompletion(messages, {})).rejects.toThrow(
        "Request timeout"
      );
    });
  });

  describe("edge cases", () => {
    let openrouter: OpenRouterLLM;

    beforeEach(() => {
      openrouter = new OpenRouterLLM();
    });

    it("should handle rate limiting gracefully", async () => {
      const rateLimitError = new Error("Rate limit exceeded");
      (rateLimitError as any).status = 429;
      (rateLimitError as any).headers = {
        "retry-after": "60",
      };

      mockOpenAIClient.chat.completions.create.mockRejectedValue(
        rateLimitError
      );
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("Rate limit exceeded");
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(openrouter.getChatCompletion(messages, {})).rejects.toThrow(
        "Rate limit exceeded"
      );
    });

    it("should handle network timeouts", async () => {
      const timeoutError = new Error("Request timeout");
      (timeoutError as any).code = "ETIMEDOUT";

      mockOpenAIClient.chat.completions.create.mockRejectedValue(timeoutError);
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("Request timeout");
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(openrouter.getChatCompletion(messages, {})).rejects.toThrow(
        "Request timeout"
      );
    });

    it("should handle invalid API responses", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        invalid: "response",
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await openrouter.getChatCompletion(messages, {});

      expect(result).toBeNull();
    });

    it("should handle corrupted stream data", async () => {
      const corruptedStream = {
        [Symbol.asyncIterator]: async function* () {
          yield null;
          yield undefined;
          yield { invalid: "data" };
          yield { choices: [{ delta: { content: "valid" } }] };
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(
        corruptedStream
      );
      (mockLLMPerformanceMonitor.measureStream as jest.Mock).mockImplementation(
        async (stream) => ({
          ...stream,
          endMeasurement: jest.fn(),
        })
      );

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await openrouter.streamGetChatCompletion(messages, {});

      expect(result.stream).toBeDefined();
    });

    it("should handle environment variable edge cases", () => {
      process.env.OPENROUTER_API_KEY = "";

      // Should throw error for empty API key
      expect(() => new OpenRouterLLM()).toThrow(
        "No OpenRouter API key was set."
      );

      delete process.env.OPENROUTER_API_KEY;
      expect(() => new OpenRouterLLM()).toThrow(
        "No OpenRouter API key was set."
      );
    });

    it("should handle zero-length responses", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await openrouter.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("");
    });

    it("should handle stream interruption gracefully", async () => {
      const interruptedStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { choices: [{ delta: { content: "partial" } }] };
          throw new Error("Stream interrupted");
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(
        interruptedStream
      );

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(
        openrouter.streamGetChatCompletion(messages, {})
      ).resolves.toBeDefined();
    });

    it("should handle circular references in error objects", async () => {
      const circularError = new Error("Circular error");
      (circularError as any).inner = circularError; // Create circular reference

      mockOpenAIClient.chat.completions.create.mockRejectedValue(circularError);
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("Circular error");
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(openrouter.getChatCompletion(messages, {})).rejects.toThrow(
        "Circular error"
      );
    });

    it("should handle missing hasOwn property gracefully", async () => {
      const responseWithoutHasOwn = {
        choices: [{ message: { content: "Response" } }],
      };

      // Mock Object.hasOwn to return false
      const originalHasOwn = Object.hasOwn;
      Object.hasOwn = jest.fn().mockReturnValue(false);

      mockOpenAIClient.chat.completions.create.mockResolvedValue(
        responseWithoutHasOwn
      );

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await openrouter.getChatCompletion(messages, {});

      expect(result).toBeNull();

      // Restore original hasOwn
      Object.hasOwn = originalHasOwn;
    });

    it("should handle model switching during operation", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Response" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await openrouter.getChatCompletion(messages, {});

      // Simulate model change
      openrouter.model = "anthropic/claude-3-sonnet";

      await openrouter.getChatCompletion(messages, {});

      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith(
        expect.objectContaining({ model: "openai/gpt-4o" })
      );
      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith(
        expect.objectContaining({ model: "anthropic/claude-3-sonnet" })
      );
    });
  });

  describe("OpenRouter-specific error handling", () => {
    let openrouter: OpenRouterLLM;

    beforeEach(() => {
      openrouter = new OpenRouterLLM();
    });

    it("should handle OpenRouter quota exceeded error", async () => {
      const quotaError = new Error("You have exceeded your OpenRouter quota");
      (quotaError as any).response = {
        data: {
          error: {
            message: "You have exceeded your OpenRouter quota",
            type: "insufficient_quota",
            code: "insufficient_quota",
          },
        },
      };

      mockOpenAIClient.chat.completions.create.mockRejectedValue(quotaError);
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("You have exceeded your OpenRouter quota");
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(openrouter.getChatCompletion(messages, {})).rejects.toThrow(
        "You have exceeded your OpenRouter quota"
      );
    });

    it("should handle OpenRouter model unavailable error", async () => {
      const modelError = new Error("Model temporarily unavailable");
      (modelError as any).status = 503;
      (modelError as any).response = {
        data: {
          error: {
            message: "Model temporarily unavailable",
            type: "model_unavailable",
            code: "model_offline",
          },
        },
      };

      mockOpenAIClient.chat.completions.create.mockRejectedValue(modelError);
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("Model temporarily unavailable");
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(openrouter.getChatCompletion(messages, {})).rejects.toThrow(
        "Model temporarily unavailable"
      );
    });

    it("should handle OpenRouter invalid model error", async () => {
      const invalidModelError = new Error("Invalid model specified");
      (invalidModelError as any).status = 400;
      (invalidModelError as any).response = {
        data: {
          error: {
            message: "Invalid model specified",
            type: "invalid_request",
            code: "invalid_model",
          },
        },
      };

      mockOpenAIClient.chat.completions.create.mockRejectedValue(
        invalidModelError
      );
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("Invalid model specified");
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(openrouter.getChatCompletion(messages, {})).rejects.toThrow(
        "Invalid model specified"
      );
    });

    it("should handle OpenRouter authentication error", async () => {
      const authError = new Error("Invalid API key");
      (authError as any).status = 401;
      (authError as any).response = {
        data: {
          error: {
            message: "Invalid API key",
            type: "authentication_error",
            code: "invalid_api_key",
          },
        },
      };

      mockOpenAIClient.chat.completions.create.mockRejectedValue(authError);
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("Invalid API key");
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(openrouter.getChatCompletion(messages, {})).rejects.toThrow(
        "Invalid API key"
      );
    });

    it("should handle OpenRouter content policy violation", async () => {
      const policyError = new Error("Content policy violation");
      (policyError as any).response = {
        data: {
          error: {
            message: "Content policy violation",
            type: "content_policy_violation",
            code: "content_filtered",
          },
        },
      };

      mockOpenAIClient.chat.completions.create.mockRejectedValue(policyError);
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("Content policy violation");
          }
        });

      const messages: ChatMessage[] = [
        { role: "user", content: "Inappropriate content" },
      ];

      await expect(openrouter.getChatCompletion(messages, {})).rejects.toThrow(
        "Content policy violation"
      );
    });

    it("should preserve original error messages", async () => {
      const specificError = new Error(
        "OpenRouter service is temporarily unavailable. Please try again later."
      );
      mockOpenAIClient.chat.completions.create.mockRejectedValue(specificError);
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error(
              "OpenRouter service is temporarily unavailable. Please try again later."
            );
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(openrouter.getChatCompletion(messages, {})).rejects.toThrow(
        "OpenRouter service is temporarily unavailable. Please try again later."
      );
    });
  });

  describe("token counting and limits", () => {
    let openrouter: OpenRouterLLM;

    beforeEach(() => {
      openrouter = new OpenRouterLLM();
    });

    it("should correctly calculate token limits", () => {
      const limits = openrouter.limits;
      const total = limits.history + limits.system + limits.user;

      expect(total).toBe(openrouter.promptWindowLimit());
      expect(limits.user).toBeGreaterThan(limits.history);
      expect(limits.user).toBeGreaterThan(limits.system);
      expect(limits.user).toBe(128000 * 0.7);
      expect(limits.history).toBe(128000 * 0.15);
      expect(limits.system).toBe(128000 * 0.15);
    });

    it("should handle different token limits", () => {
      process.env.OPENROUTER_MODEL_TOKEN_LIMIT = "256000";
      const bigModel = new OpenRouterLLM();

      expect(bigModel.promptWindowLimit()).toBe(256000);
      expect(bigModel.limits.user).toBe(256000 * 0.7);

      process.env.OPENROUTER_MODEL_TOKEN_LIMIT = "4096";
      const smallModel = new OpenRouterLLM();

      expect(smallModel.promptWindowLimit()).toBe(4096);
      expect(smallModel.limits.user).toBe(4096 * 0.7);
    });

    it("should handle custom token limits with suffix", () => {
      process.env.OPENROUTER_MODEL_TOKEN_LIMIT_CUSTOM = "512000";
      process.env.OPENROUTER_API_KEY_CUSTOM = "test-key";
      const customModel = new OpenRouterLLM(null, null, "_CUSTOM");

      expect(customModel.promptWindowLimit()).toBe(512000);
      expect(customModel.limits.user).toBe(512000 * 0.7);
    });
  });

  describe("contextTexts handling", () => {
    let openrouter: OpenRouterLLM;

    beforeEach(() => {
      openrouter = new OpenRouterLLM();
      mockFormatContextTexts.mockClear();
    });

    it("should call formatContextTexts with contextTexts", async () => {
      const contextTexts = ["Context 1", "Context 2"];
      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        contextTexts,
        userPrompt: "User",
      };

      await openrouter.constructPrompt(promptArgs);

      expect(mockFormatContextTexts).toHaveBeenCalledWith(contextTexts);
    });

    it("should handle empty contextTexts", async () => {
      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        contextTexts: [],
        userPrompt: "User",
      };

      const result = await openrouter.constructPrompt(promptArgs);

      expect(result[0].content).toBe("System");
    });

    it("should handle null contextTexts", async () => {
      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        contextTexts: null as any,
        userPrompt: "User",
      };

      const result = await openrouter.constructPrompt(promptArgs);

      expect(result[0].content).toBe("System");
    });

    it("should handle undefined contextTexts", async () => {
      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        userPrompt: "User",
      };

      const result = await openrouter.constructPrompt(promptArgs);

      expect(result[0].content).toBe("System");
    });

    it("should handle large contextTexts arrays", async () => {
      const largeContextTexts = Array(1000)
        .fill("Context")
        .map((_, i) => `Context ${i}`);
      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        contextTexts: largeContextTexts,
        userPrompt: "User",
      };

      await openrouter.constructPrompt(promptArgs);

      expect(mockFormatContextTexts).toHaveBeenCalledWith(largeContextTexts);
    });
  });

  describe("temperature handling", () => {
    let openrouter: OpenRouterLLM;

    beforeEach(() => {
      openrouter = new OpenRouterLLM();
    });

    it("should use provided temperature for completion", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Response" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await openrouter.getChatCompletion(messages, { temperature: 0.9 });

      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith({
        model: "openai/gpt-4o",
        messages,
        temperature: 0.9,
      });
    });

    it("should use provided temperature for streaming", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { choices: [{ delta: { content: "stream" } }] };
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockStream);

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await openrouter.streamGetChatCompletion(messages, { temperature: 0.1 });

      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith({
        model: "openai/gpt-4o",
        stream: true,
        messages,
        temperature: 0.1,
      });
    });

    it("should handle extreme temperature values", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Response" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      // Test minimum temperature
      await openrouter.getChatCompletion(messages, { temperature: 0 });
      expect(mockOpenAIClient.chat.completions.create).toHaveBeenLastCalledWith(
        expect.objectContaining({ temperature: 0 })
      );

      // Test maximum temperature
      await openrouter.getChatCompletion(messages, { temperature: 2 });
      expect(mockOpenAIClient.chat.completions.create).toHaveBeenLastCalledWith(
        expect.objectContaining({ temperature: 2 })
      );
    });

    it("should handle negative temperature values", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Response" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await openrouter.getChatCompletion(messages, { temperature: -0.5 });
      expect(mockOpenAIClient.chat.completions.create).toHaveBeenLastCalledWith(
        expect.objectContaining({ temperature: -0.5 })
      );
    });
  });

  describe("multiple model support", () => {
    it("should support OpenAI models", () => {
      const openai = new OpenRouterLLM(null, "openai/gpt-4o");
      expect(openai.model).toBe("openai/gpt-4o");
    });

    it("should support Anthropic models", () => {
      const anthropic = new OpenRouterLLM(null, "anthropic/claude-3-sonnet");
      expect(anthropic.model).toBe("anthropic/claude-3-sonnet");
    });

    it("should support Meta models", () => {
      const meta = new OpenRouterLLM(null, "meta-llama/llama-3.1-8b-instruct");
      expect(meta.model).toBe("meta-llama/llama-3.1-8b-instruct");
    });

    it("should support Google models", () => {
      const google = new OpenRouterLLM(null, "google/gemini-pro");
      expect(google.model).toBe("google/gemini-pro");
    });

    it("should support custom models", () => {
      const custom = new OpenRouterLLM(null, "custom/my-model-v1");
      expect(custom.model).toBe("custom/my-model-v1");
    });
  });

  describe("configuration validation", () => {
    it("should validate required environment variables", () => {
      delete process.env.OPENROUTER_API_KEY;
      delete process.env.OPENROUTER_MODEL_TOKEN_LIMIT;

      expect(() => new OpenRouterLLM()).toThrow(
        "No OpenRouter API key was set."
      );
    });

    it("should validate token limit is numeric", () => {
      process.env.OPENROUTER_MODEL_TOKEN_LIMIT = "not-a-number";

      expect(() => new OpenRouterLLM()).toThrow(
        "No OpenRouter token context limit was set."
      );
    });

    it("should validate token limit is positive", () => {
      process.env.OPENROUTER_MODEL_TOKEN_LIMIT = "-1000";

      const openrouter = new OpenRouterLLM();
      expect(openrouter.promptWindowLimit()).toBe(-1000);
    });

    it("should handle whitespace in environment variables", () => {
      process.env.OPENROUTER_API_KEY = "  test-key  ";
      process.env.OPENROUTER_MODEL_TOKEN_LIMIT = "  128000  ";

      const openrouter = new OpenRouterLLM();
      expect(openrouter.promptWindowLimit()).toBe(128000);
    });
  });
});
