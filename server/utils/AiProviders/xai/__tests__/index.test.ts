import { XAiLLM } from "../index";
import { NativeEmbedder } from "../../../EmbeddingEngines/native";
import { LLMPerformanceMonitor } from "../../../helpers/chat/LLMPerformanceMonitor";
import { handleDefaultStreamResponseV2 } from "../../../helpers/chat/responses";
import { formatContextTexts } from "../../../helpers";
import type { Response as ExpressResponse } from "express";
import type {
  ChatMessage,
  PromptArgs,
  EmbeddingEngine,
  Attachment,
  StreamResponseProps,
} from "../../../../types/ai-providers";

// Mock dependencies
jest.mock("openai");
jest.mock("../../../EmbeddingEngines/native");
jest.mock("../../../helpers/chat/LLMPerformanceMonitor");
jest.mock("../../../helpers/chat/responses");
jest.mock("../../../helpers", () => ({
  formatContextTexts: jest.fn().mockImplementation(async (texts: string[]) => {
    return texts.join("\n\n---CONTEXT---\n\n");
  }),
}));
jest.mock("../../../helpers/chat", () => ({
  messageArrayCompressor: jest
    .fn()
    .mockImplementation(async (provider, messages, _rawHistory) => {
      return messages;
    }),
}));

// Mock OpenAI client
const mockOpenAIClient = {
  chat: {
    completions: {
      create: jest.fn(),
    },
  },
};

// Mock require for OpenAI since it's dynamically imported
jest.doMock("openai", () => ({
  OpenAI: jest.fn().mockImplementation(() => mockOpenAIClient),
}));

describe("XAiLLM", () => {
  const mockNativeEmbedder = NativeEmbedder as jest.MockedClass<
    typeof NativeEmbedder
  >;
  const mockLLMPerformanceMonitor = LLMPerformanceMonitor as jest.MockedClass<
    typeof LLMPerformanceMonitor
  >;
  const mockHandleDefaultStreamResponseV2 =
    handleDefaultStreamResponseV2 as jest.MockedFunction<
      typeof handleDefaultStreamResponseV2
    >;
  const mockFormatContextTexts = formatContextTexts as jest.MockedFunction<
    typeof formatContextTexts
  >;

  let originalEnv: typeof process.env;
  let consoleErrorSpy: jest.SpyInstance;

  beforeEach(() => {
    // Store original environment
    originalEnv = { ...process.env };

    // Set up required environment variables
    process.env.XAI_LLM_API_KEY = "test-xai-api-key";
    process.env.XAI_LLM_MODEL_PREF = "grok-beta";

    // Clear all mocks
    jest.clearAllMocks();

    // Spy on console methods
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();

    // Mock NativeEmbedder
    mockNativeEmbedder.mockClear();
    const mockEmbedderInstance = {
      embedTextInput: jest.fn().mockResolvedValue([0.1, 0.2, 0.3]),
      embedChunks: jest.fn().mockResolvedValue([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]),
    };
    mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);

    // Mock LLM Performance Monitor
    mockLLMPerformanceMonitor.measureAsyncFunction = jest
      .fn()
      .mockImplementation(async (promise) => {
        const output = await promise;
        return { output, duration: 1000 };
      });
    mockLLMPerformanceMonitor.measureStream = jest
      .fn()
      .mockImplementation(async (streamPromise) => {
        const stream = await streamPromise;
        return {
          [Symbol.asyncIterator]: stream[Symbol.asyncIterator],
          endMeasurement: jest.fn().mockReturnValue({
            prompt_tokens: 100,
            completion_tokens: 50,
            total_tokens: 150,
            outputTps: 50,
            duration: 1000,
          }),
        };
      });

    // Mock stream response handler
    mockHandleDefaultStreamResponseV2.mockResolvedValue("Streamed response");
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
    consoleErrorSpy.mockRestore();
  });

  describe("constructor and initialization", () => {
    it("should initialize with default settings", () => {
      const xai = new XAiLLM();

      expect(xai.model).toBe("grok-beta");
      expect(xai.limits).toEqual({
        history: 131072 * 0.15,
        system: 131072 * 0.15,
        user: 131072 * 0.7,
      });
      expect(xai.defaultTemp).toBe(0.7);
      expect(xai.embedder).toBeDefined();
    });

    it("should use custom embedder when provided", () => {
      const customEmbedder = {} as EmbeddingEngine;
      const xai = new XAiLLM(customEmbedder);

      expect(xai.embedder).toBe(customEmbedder);
    });

    it("should use model preference when provided", () => {
      const xai = new XAiLLM(null, "grok-vision-beta");

      expect(xai.model).toBe("grok-vision-beta");
    });

    it("should use environment model preference", () => {
      process.env.XAI_LLM_MODEL_PREF = "custom-grok-model";

      const xai = new XAiLLM();

      expect(xai.model).toBe("custom-grok-model");
    });

    it("should fall back to default model when no preference is set", () => {
      delete process.env.XAI_LLM_MODEL_PREF;

      const xai = new XAiLLM();

      expect(xai.model).toBe("grok-beta");
    });

    it("should throw error when API key is missing", () => {
      delete process.env.XAI_LLM_API_KEY;

      expect(() => new XAiLLM()).toThrow("No xAI API key was set.");
    });

    it("should initialize OpenAI client with correct configuration", () => {
      const { OpenAI } = require("openai");

      new XAiLLM();

      expect(OpenAI).toHaveBeenCalledWith({
        baseURL: "https://api.x.ai/v1",
        apiKey: "test-xai-api-key",
      });
    });
  });

  describe("static promptWindowLimit method", () => {
    it("should return static prompt window limit for known model", () => {
      expect(XAiLLM.promptWindowLimit("grok-beta")).toBe(131072);
    });

    it("should return default for unknown model", () => {
      expect(XAiLLM.promptWindowLimit("unknown-model")).toBe(131072);
    });

    it("should handle empty model name", () => {
      expect(XAiLLM.promptWindowLimit("")).toBe(131072);
    });

    it("should handle undefined model name", () => {
      expect(XAiLLM.promptWindowLimit()).toBe(131072);
    });
  });

  describe("instance promptWindowLimit methods", () => {
    it("should return instance prompt window limit", () => {
      const xai = new XAiLLM();
      expect(xai.promptWindowLimit()).toBe(131072);
    });

    it("should return custom prompt window limit", () => {
      const xai = new XAiLLM();
      expect(xai.customPromptWindowLimit()).toBe(131072);
    });

    it("should handle model from MODEL_MAP", () => {
      const xai = new XAiLLM(null, "grok-beta");
      expect(xai.promptWindowLimit()).toBe(131072);
    });

    it("should return default for unknown model", () => {
      const xai = new XAiLLM(null, "unknown-model");
      expect(xai.promptWindowLimit()).toBe(131072);
    });
  });

  describe("streamingEnabled", () => {
    it("should return true since streamGetChatCompletion is defined", () => {
      const xai = new XAiLLM();
      expect(xai.streamingEnabled()).toBe(true);
    });
  });

  describe("isValidChatCompletionModel", () => {
    it("should return true for known xAI models", () => {
      const xai = new XAiLLM();

      expect(xai.isValidChatCompletionModel("grok-beta")).toBe(true);
    });

    it("should return false for unknown models", () => {
      const xai = new XAiLLM();

      expect(xai.isValidChatCompletionModel("unknown-model")).toBe(false);
    });

    it("should handle empty model name", () => {
      const xai = new XAiLLM();

      expect(xai.isValidChatCompletionModel("")).toBe(false);
    });

    it("should handle model validation for current instance model", () => {
      const xai = new XAiLLM(null, "grok-beta");

      expect(xai.isValidChatCompletionModel(xai.model)).toBe(true);
    });

    it("should be case sensitive for xAI models", () => {
      const xai = new XAiLLM();

      expect(xai.isValidChatCompletionModel("GROK-BETA")).toBe(false);
      expect(xai.isValidChatCompletionModel("Grok-Beta")).toBe(false);
    });
  });

  describe("generateContent", () => {
    let xai: XAiLLM;

    beforeEach(() => {
      xai = new XAiLLM();
    });

    it("should return string when no attachments", () => {
      const result = xai["generateContent"]({
        userPrompt: "Hello xAI",
        attachments: [],
      });

      expect(result).toBe("Hello xAI");
    });

    it("should handle image attachments", () => {
      const attachments: Attachment[] = [
        {
          name: "image.png",
          type: "image/png",
          contentString: "data:image/png;base64,iVBORw0KGgo...",
        },
      ];

      const result = xai["generateContent"]({
        userPrompt: "Analyze this image with Grok",
        attachments,
      });

      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray).toHaveLength(2);
      expect(resultArray[0]).toEqual({
        type: "text",
        text: "Analyze this image with Grok",
      });
      expect(resultArray[1]).toEqual({
        type: "image_url",
        image_url: {
          url: "data:image/png;base64,iVBORw0KGgo...",
          detail: "high",
        },
      });
    });

    it("should handle JPEG image attachments", () => {
      const attachments: Attachment[] = [
        {
          name: "photo.jpg",
          type: "image/jpeg",
          contentString:
            "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...",
        },
      ];

      const result = xai["generateContent"]({
        userPrompt: "What's in this photo?",
        attachments,
      });

      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray[1]).toEqual({
        type: "image_url",
        image_url: {
          url: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...",
          detail: "high",
        },
      });
    });

    it("should handle multiple image attachments", () => {
      const attachments: Attachment[] = [
        {
          name: "image1.png",
          type: "image/png",
          contentString: "data:image/png;base64,first",
        },
        {
          name: "image2.jpg",
          type: "image/jpeg",
          contentString: "data:image/jpeg;base64,second",
        },
      ];

      const result = xai["generateContent"]({
        userPrompt: "Compare these images",
        attachments,
      });

      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray).toHaveLength(3); // text + 2 images
      expect(resultArray[1].type).toBe("image_url");
      expect(resultArray[2].type).toBe("image_url");
    });

    it("should handle attachments without type", () => {
      const attachments: Attachment[] = [
        {
          name: "image.png",
          contentString: "data:image/png;base64,test",
        },
      ];

      const result = xai["generateContent"]({
        userPrompt: "Process this",
        attachments,
      });

      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray[1]).toEqual({
        type: "image_url",
        image_url: {
          url: "data:image/png;base64,test",
          detail: "high",
        },
      });
    });

    it("should handle empty userPrompt with attachments", () => {
      const attachments: Attachment[] = [
        {
          name: "image.png",
          type: "image/png",
          contentString: "data:image/png;base64,test",
        },
      ];

      const result = xai["generateContent"]({
        userPrompt: "",
        attachments,
      });

      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray[0]).toEqual({
        type: "text",
        text: "",
      });
    });

    it("should handle null or undefined attachments", () => {
      const result1 = xai["generateContent"]({
        userPrompt: "Test",
        attachments: null as any,
      });

      const result2 = xai["generateContent"]({
        userPrompt: "Test",
        attachments: undefined as any,
      });

      expect(result1).toBe("Test");
      expect(result2).toBe("Test");
    });
  });

  describe("constructPrompt", () => {
    let xai: XAiLLM;

    beforeEach(() => {
      xai = new XAiLLM();
    });

    it("should construct prompt with all components", async () => {
      const promptArgs: PromptArgs = {
        systemPrompt: "You are Grok, xAI's witty assistant.",
        contextTexts: ["Context 1", "Context 2"],
        chatHistory: [
          { role: "user", content: "Hello Grok" },
          { role: "assistant", content: "Hello! I'm Grok, ready to help." },
        ],
        userPrompt: "What can you do?",
      };

      const result = await xai.constructPrompt(promptArgs);

      expect(result).toEqual([
        {
          role: "system",
          content:
            "You are Grok, xAI's witty assistant.Context 1\n\n---CONTEXT---\n\nContext 2",
        },
        { role: "user", content: "Hello Grok" },
        { role: "assistant", content: "Hello! I'm Grok, ready to help." },
        { role: "user", content: "What can you do?" },
      ]);
    });

    it("should handle attachments in prompt", async () => {
      const attachment: Attachment = {
        name: "test.png",
        type: "image/png",
        contentString: "data:image/png;base64,test",
      };

      const promptArgs: PromptArgs = {
        systemPrompt: "Analyze images with Grok vision",
        userPrompt: "What's in this image?",
        attachments: [attachment],
      };

      const result = await xai.constructPrompt(promptArgs);

      const lastMessage = result[result.length - 1];
      expect(lastMessage.role).toBe("user");
      expect(Array.isArray(lastMessage.content)).toBe(true);
      const content = lastMessage.content as Array<any>;
      expect(content[0].type).toBe("text");
      expect(content[1].type).toBe("image_url");
    });

    it("should handle empty context texts", async () => {
      const promptArgs: PromptArgs = {
        systemPrompt: "System prompt",
        contextTexts: [],
        chatHistory: [],
        userPrompt: "User prompt",
      };

      const result = await xai.constructPrompt(promptArgs);

      expect(result).toEqual([
        { role: "system", content: "System prompt" },
        { role: "user", content: "User prompt" },
      ]);
    });

    it("should handle missing optional parameters", async () => {
      const promptArgs: PromptArgs = {
        userPrompt: "Just user prompt",
      };

      const result = await xai.constructPrompt(promptArgs);

      expect(result).toEqual([
        { role: "system", content: "" },
        { role: "user", content: "Just user prompt" },
      ]);
    });

    it("should call formatContextTexts with correct arguments", async () => {
      const contextTexts = ["Context 1", "Context 2"];
      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        contextTexts,
        userPrompt: "User",
      };

      await xai.constructPrompt(promptArgs);

      expect(mockFormatContextTexts).toHaveBeenCalledWith(contextTexts);
    });

    it("should handle null context texts", async () => {
      mockFormatContextTexts.mockResolvedValueOnce("");

      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        contextTexts: null as any,
        userPrompt: "User",
      };

      const result = await xai.constructPrompt(promptArgs);

      expect(result[0].content).toBe("System");
    });
  });

  describe("getChatCompletion", () => {
    let xai: XAiLLM;

    beforeEach(() => {
      xai = new XAiLLM();
      mockOpenAIClient.chat.completions.create.mockClear();
    });

    it("should return completion response successfully", async () => {
      const mockResponse = {
        choices: [
          {
            message: {
              content: "This is Grok's response with wit and humor!",
            },
          },
        ],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 20,
          total_tokens: 30,
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: "user", content: "Tell me a joke" },
      ];

      const result = await xai.getChatCompletion(messages, {
        temperature: 0.8,
      });

      expect(result).toEqual({
        textResponse: "This is Grok's response with wit and humor!",
        metrics: {
          prompt_tokens: 10,
          completion_tokens: 20,
          total_tokens: 30,
          outputTps: 20 / 1000, // 20 tokens / 1000 ms
          duration: 1000,
        },
      });

      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith({
        model: "grok-beta",
        messages,
        temperature: 0.8,
      });
    });

    it("should use default temperature when not provided", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Response" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await xai.getChatCompletion(messages, {});

      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith({
        model: "grok-beta",
        messages,
        temperature: 0.7,
      });
    });

    it("should throw error for invalid model", async () => {
      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];
      xai.model = "invalid-model";

      await expect(xai.getChatCompletion(messages, {})).rejects.toThrow(
        "xAI chat: invalid-model is not valid for chat completion!"
      );
    });

    it("should throw error for null messages", async () => {
      await expect(xai.getChatCompletion(null, {})).rejects.toThrow(
        "xAI chat: grok-beta is not valid for chat completion!"
      );
    });

    it("should handle API errors", async () => {
      const error = new Error("xAI API request failed");
      mockOpenAIClient.chat.completions.create.mockRejectedValueOnce(error);
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("xAI API request failed");
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(xai.getChatCompletion(messages, {})).rejects.toThrow(
        "xAI API request failed"
      );
    });

    it("should return null for empty choices", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await xai.getChatCompletion(messages, {});

      expect(result).toBeNull();
    });

    it("should return null for missing choices property", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        invalid: "response",
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await xai.getChatCompletion(messages, {});

      expect(result).toBeNull();
    });

    it("should handle missing usage data", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Response without usage" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await xai.getChatCompletion(messages, {});

      expect(result?.metrics).toEqual({
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
        outputTps: 0,
        duration: 1000,
      });
    });

    it("should handle null message content", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: null } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await xai.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("");
    });

    it("should handle undefined message content", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: {} }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await xai.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("");
    });

    it("should measure performance correctly", async () => {
      const mockResponse = {
        choices: [{ message: { content: "Measured response" } }],
        usage: {
          prompt_tokens: 100,
          completion_tokens: 50,
          total_tokens: 150,
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockResponse);
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementation(async (promise) => {
          const output = await promise;
          return { output, duration: 2500 };
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await xai.getChatCompletion(messages, {});

      expect(result?.metrics).toEqual({
        prompt_tokens: 100,
        completion_tokens: 50,
        total_tokens: 150,
        outputTps: 50 / 2500,
        duration: 2500,
      });
    });
  });

  describe("streamGetChatCompletion", () => {
    let xai: XAiLLM;

    beforeEach(() => {
      xai = new XAiLLM();
      mockOpenAIClient.chat.completions.create.mockClear();
    });

    it("should stream completion successfully", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield {
            choices: [{ delta: { content: "Grok" } }],
          };
          yield {
            choices: [{ delta: { content: " says hello!" } }],
          };
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockStream);

      const messages: ChatMessage[] = [{ role: "user", content: "Say hello" }];

      const result = await xai.streamGetChatCompletion(messages, {
        temperature: 0.9,
      });

      expect(result.stream).toBeDefined();
      expect(result.endMeasurement).toBeDefined();

      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith({
        model: "grok-beta",
        stream: true,
        messages,
        temperature: 0.9,
      });
    });

    it("should use default temperature for streaming", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { choices: [{ delta: { content: "stream" } }] };
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockStream);

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await xai.streamGetChatCompletion(messages, {});

      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith({
        model: "grok-beta",
        stream: true,
        messages,
        temperature: 0.7,
      });
    });

    it("should throw error for invalid model", async () => {
      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];
      xai.model = "invalid-model";

      await expect(xai.streamGetChatCompletion(messages, {})).rejects.toThrow(
        "xAI chat: invalid-model is not valid for chat completion!"
      );
    });

    it("should throw error for null messages", async () => {
      await expect(xai.streamGetChatCompletion(null, {})).rejects.toThrow(
        "xAI chat: grok-beta is not valid for chat completion!"
      );
    });

    it("should handle stream creation errors", async () => {
      const error = new Error("Stream creation failed");
      mockOpenAIClient.chat.completions.create.mockRejectedValueOnce(error);

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(xai.streamGetChatCompletion(messages, {})).rejects.toThrow(
        "Stream creation failed"
      );
    });

    it("should properly measure performance for streaming", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { choices: [{ delta: { content: "test" } }] };
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockStream);

      const messages: ChatMessage[] = [
        { role: "user", content: "Test" },
        { role: "assistant", content: [{ type: "text", text: "Response" }] },
      ];

      await xai.streamGetChatCompletion(messages, {});

      expect(mockLLMPerformanceMonitor.measureStream).toHaveBeenCalledWith(
        expect.any(Promise),
        [
          { content: "Test", role: "user" },
          {
            content: JSON.stringify([{ type: "text", text: "Response" }]),
            role: "assistant",
          },
        ],
        false
      );
    });

    it("should handle complex message content in performance measurement", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { choices: [{ delta: { content: "test" } }] };
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockStream);

      const messages: ChatMessage[] = [
        {
          role: "user",
          content: [
            { type: "text", text: "Analyze this" },
            {
              type: "image_url",
              image_url: { url: "data:image/png;base64,test" },
            },
          ],
        },
      ];

      await xai.streamGetChatCompletion(messages, {});

      expect(mockLLMPerformanceMonitor.measureStream).toHaveBeenCalledWith(
        expect.any(Promise),
        [
          {
            content: JSON.stringify([
              { type: "text", text: "Analyze this" },
              {
                type: "image_url",
                image_url: { url: "data:image/png;base64,test" },
              },
            ]),
            role: "user",
          },
        ],
        false
      );
    });
  });

  describe("handleStream", () => {
    let xai: XAiLLM;
    let mockResponse: Partial<ExpressResponse>;

    beforeEach(() => {
      xai = new XAiLLM();
      mockResponse = {
        write: jest.fn(),
        end: jest.fn(),
      };
    });

    it("should handle stream successfully", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { text: "Grok chunk 1" };
          yield { text: "Grok chunk 2" };
        },
      };

      const responseProps: StreamResponseProps = {
        uuid: "test-uuid",
        sources: [],
      };

      await xai.handleStream(
        mockResponse as ExpressResponse,
        mockStream,
        responseProps
      );

      expect(mockHandleDefaultStreamResponseV2).toHaveBeenCalledWith(
        mockResponse,
        mockStream,
        responseProps
      );
    });

    it("should pass through all response properties", async () => {
      const mockStream = { [Symbol.asyncIterator]: jest.fn() };
      const responseProps: StreamResponseProps = {
        uuid: "test-uuid",
        sources: [{ url: "http://example.com", title: "Example" }],
      };

      await xai.handleStream(
        mockResponse as ExpressResponse,
        mockStream,
        responseProps
      );

      expect(mockHandleDefaultStreamResponseV2).toHaveBeenCalledWith(
        mockResponse,
        mockStream,
        responseProps
      );
    });

    it("should handle empty stream", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          // Empty stream
        },
      };

      const responseProps: StreamResponseProps = {
        uuid: "empty-stream",
        sources: [],
      };

      await xai.handleStream(
        mockResponse as ExpressResponse,
        mockStream,
        responseProps
      );

      expect(mockHandleDefaultStreamResponseV2).toHaveBeenCalledWith(
        mockResponse,
        mockStream,
        responseProps
      );
    });
  });

  describe("embedding methods", () => {
    let xai: XAiLLM;

    beforeEach(() => {
      xai = new XAiLLM();
    });

    it("should embed text input successfully", async () => {
      const result = await xai.embedTextInput("Test text for Grok");

      expect(result).toEqual([0.1, 0.2, 0.3]);
      expect(xai.embedder.embedTextInput).toHaveBeenCalledWith(
        "Test text for Grok"
      );
    });

    it("should embed chunks successfully", async () => {
      const chunks = ["Chunk 1 for Grok", "Chunk 2 for Grok"];
      const result = await xai.embedChunks(chunks);

      expect(result).toEqual([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]);
      expect(xai.embedder.embedChunks).toHaveBeenCalledWith(chunks);
    });

    it("should handle empty chunks array", async () => {
      const result = await xai.embedChunks([]);

      expect(result).toEqual([
        [0.1, 0.2, 0.3],
        [0.4, 0.5, 0.6],
      ]);
    });

    it("should handle null result from embedder", async () => {
      const mockEmbedderInstance = {
        embedTextInput: jest.fn(),
        embedChunks: jest.fn().mockResolvedValue(null),
      };
      mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);

      const xai = new XAiLLM();
      const result = await xai.embedChunks(["Test"]);

      expect(result).toEqual([]);
    });

    it("should handle embedding errors", async () => {
      const error = new Error("Embedding failed");
      const mockEmbedderInstance = {
        embedTextInput: jest.fn().mockRejectedValueOnce(error),
        embedChunks: jest.fn(),
      };
      mockNativeEmbedder.mockImplementation(() => mockEmbedderInstance as any);

      const xai = new XAiLLM();

      await expect(xai.embedTextInput("Test text")).rejects.toThrow(
        "Embedding failed"
      );
    });

    it("should handle large text input", async () => {
      const largeText = "Large text ".repeat(10000);

      await xai.embedTextInput(largeText);

      expect(xai.embedder.embedTextInput).toHaveBeenCalledWith(largeText);
    });

    it("should handle many chunks", async () => {
      const manyChunks = Array(1000)
        .fill("Chunk")
        .map((_, i) => `Chunk ${i}`);

      await xai.embedChunks(manyChunks);

      expect(xai.embedder.embedChunks).toHaveBeenCalledWith(manyChunks);
    });
  });

  describe("compressMessages", () => {
    let xai: XAiLLM;

    beforeEach(() => {
      xai = new XAiLLM();
    });

    it("should compress messages successfully", async () => {
      const { messageArrayCompressor } = require("../../../helpers/chat");
      const compressedMessages = [{ role: "system", content: "Compressed" }];
      messageArrayCompressor.mockResolvedValue(compressedMessages);

      const promptArgs: PromptArgs = {
        systemPrompt: "You are Grok",
        userPrompt: "Tell me about xAI",
      };
      const rawHistory: ChatMessage[] = [
        { role: "user", content: "Previous message" },
      ];

      const result = await xai.compressMessages(promptArgs, rawHistory);

      expect(result).toEqual(compressedMessages);
      expect(messageArrayCompressor).toHaveBeenCalledWith(
        xai,
        expect.arrayContaining([
          { role: "system", content: "You are Grok" },
          { role: "user", content: "Tell me about xAI" },
        ]),
        rawHistory
      );
    });

    it("should handle empty parameters", async () => {
      const { messageArrayCompressor } = require("../../../helpers/chat");
      messageArrayCompressor.mockResolvedValue([]);

      const result = await xai.compressMessages();

      expect(result).toEqual([]);
    });

    it("should handle compression errors", async () => {
      const { messageArrayCompressor } = require("../../../helpers/chat");
      const compressionError = new Error("Compression failed");
      messageArrayCompressor.mockRejectedValueOnce(compressionError);

      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        userPrompt: "User",
      };

      await expect(xai.compressMessages(promptArgs, [])).rejects.toThrow(
        "Compression failed"
      );
    });
  });

  describe("security testing", () => {
    let xai: XAiLLM;

    beforeEach(() => {
      xai = new XAiLLM();
    });

    it("should handle malicious input in prompts", async () => {
      const maliciousPrompt = "<script>alert('xss')</script>";
      const messages: ChatMessage[] = [
        { role: "user", content: maliciousPrompt },
      ];

      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Safe response from Grok" } }],
      });

      const result = await xai.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("Safe response from Grok");
      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith({
        model: "grok-beta",
        messages: [{ role: "user", content: maliciousPrompt }],
        temperature: 0.7,
      });
    });

    it("should protect API key in error messages", async () => {
      const apiKeyError = new Error("Invalid API key: xai-1234567890abcdef");
      mockOpenAIClient.chat.completions.create.mockRejectedValueOnce(
        apiKeyError
      );
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("Invalid API key: xai-1234567890abcdef");
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(xai.getChatCompletion(messages, {})).rejects.toThrow(
        "Invalid API key: xai-1234567890abcdef"
      );
    });

    it("should handle SQL injection attempts in attachments", () => {
      const sqlInjectionAttachment: Attachment = {
        name: "'; DROP TABLE users; --",
        contentString: "SELECT * FROM users WHERE id = '1' OR '1'='1'",
        type: "text/plain",
      };

      const result = xai["generateContent"]({
        userPrompt: "Process this query",
        attachments: [sqlInjectionAttachment],
      });

      // Should treat as image since no type filtering is done
      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray[1].type).toBe("image_url");
      expect(resultArray[1].image_url.url).toBe(
        "SELECT * FROM users WHERE id = '1' OR '1'='1'"
      );
    });

    it("should validate base64 image data", () => {
      const invalidBase64: Attachment = {
        name: "image.png",
        type: "image/png",
        contentString: "data:image/png;base64,<script>alert('xss')</script>",
      };

      const result = xai["generateContent"]({
        userPrompt: "Analyze image",
        attachments: [invalidBase64],
      });

      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray[1].image_url.url).toBe(
        "data:image/png;base64,<script>alert('xss')</script>"
      );
    });

    it("should handle extremely long inputs safely", async () => {
      const veryLongPrompt = "x".repeat(1000000); // 1MB of text
      const messages: ChatMessage[] = [
        { role: "user", content: veryLongPrompt },
      ];

      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Handled long input" } }],
      });

      const result = await xai.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("Handled long input");
    });

    it("should handle JSON injection in attachments", () => {
      const jsonInjectionAttachment: Attachment = {
        name: "payload.json",
        contentString:
          '{"$ne": null, "$gt": "", "__proto__": {"isAdmin": true}}',
        type: "application/json",
      };

      const result = xai["generateContent"]({
        userPrompt: "Process this JSON",
        attachments: [jsonInjectionAttachment],
      });

      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray[1].image_url.url).toContain('{"$ne": null');
    });

    it("should handle binary data in attachments", () => {
      const binaryAttachment: Attachment = {
        name: "binary.dat",
        contentString: "\x00\x01\x02\x03\xff\xfe\xfd",
        type: "application/octet-stream",
      };

      const result = xai["generateContent"]({
        userPrompt: "Process binary",
        attachments: [binaryAttachment],
      });

      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray[1].type).toBe("image_url");
    });

    it("should handle unicode edge cases", async () => {
      const unicodeMessages: ChatMessage[] = [
        {
          role: "user",
          content: "Test with emojis 🚀💻🔒 and special chars: ñáéíóú",
        },
        {
          role: "assistant",
          content: "Unicode response: 你好世界 مرحبا بالعالم",
        },
      ];

      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Handled unicode with Grok" } }],
      });

      const result = await xai.getChatCompletion(unicodeMessages, {});

      expect(result?.textResponse).toBe("Handled unicode with Grok");
    });
  });

  describe("performance testing", () => {
    let xai: XAiLLM;

    beforeEach(() => {
      xai = new XAiLLM();
    });

    it("should handle concurrent requests", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Concurrent Grok response" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const promises = Array(10)
        .fill(null)
        .map(() => xai.getChatCompletion(messages, {}));

      const results = await Promise.all(promises);

      expect(results).toHaveLength(10);
      results.forEach((result) => {
        expect(result?.textResponse).toBe("Concurrent Grok response");
      });
    });

    it("should handle large context windows", async () => {
      const largeContext = Array(100).fill("Large context block").join(" ");
      const messages: ChatMessage[] = Array(50)
        .fill(null)
        .map((_, i) => ({
          role: i % 2 === 0 ? "user" : "assistant",
          content: largeContext,
        }));

      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Handled large context" } }],
      });

      const result = await xai.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("Handled large context");
    });

    it("should measure performance accurately", async () => {
      const mockResponse = {
        choices: [{ message: { content: "Timed response" } }],
        usage: {
          prompt_tokens: 100,
          completion_tokens: 50,
          total_tokens: 150,
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockResponse);
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementation(async (promise) => {
          const output = await promise;
          return { output, duration: 2500 };
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await xai.getChatCompletion(messages, {});

      expect(result?.metrics).toEqual({
        prompt_tokens: 100,
        completion_tokens: 50,
        total_tokens: 150,
        outputTps: 50 / 2500, // 50 tokens / 2500 ms
        duration: 2500,
      });
    });

    it("should handle memory-intensive operations", () => {
      const largeAttachments: Attachment[] = Array(50)
        .fill(null)
        .map((_, i) => ({
          name: `large-file-${i}.txt`,
          contentString: "x".repeat(100000), // 100KB each
          type: "text/plain",
        }));

      const result = xai["generateContent"]({
        userPrompt: "Process large files",
        attachments: largeAttachments,
      });

      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray).toHaveLength(51); // 1 prompt + 50 attachments
    });

    it("should handle streaming with backpressure", async () => {
      let yieldCount = 0;
      const backpressureStream = {
        [Symbol.asyncIterator]: async function* () {
          while (yieldCount < 1000) {
            yieldCount++;
            yield { choices: [{ delta: { content: `chunk${yieldCount}` } }] };
            // Simulate backpressure with small delay
            if (yieldCount % 100 === 0) {
              await new Promise((resolve) => setTimeout(resolve, 1));
            }
          }
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(
        backpressureStream
      );

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await xai.streamGetChatCompletion(messages, {});

      expect(result).toBeDefined();
      expect(result.stream).toBeDefined();
    });
  });

  describe("edge cases", () => {
    let xai: XAiLLM;

    beforeEach(() => {
      xai = new XAiLLM();
    });

    it("should handle rate limiting gracefully", async () => {
      const rateLimitError = new Error("Rate limit exceeded");
      (rateLimitError as any).status = 429;
      (rateLimitError as any).headers = {
        "retry-after": "60",
      };

      mockOpenAIClient.chat.completions.create.mockRejectedValueOnce(
        rateLimitError
      );
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("Rate limit exceeded");
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(xai.getChatCompletion(messages, {})).rejects.toThrow(
        "Rate limit exceeded"
      );
    });

    it("should handle network timeouts", async () => {
      const timeoutError = new Error("Request timeout");
      (timeoutError as any).code = "ETIMEDOUT";

      mockOpenAIClient.chat.completions.create.mockRejectedValueOnce(
        timeoutError
      );
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("Request timeout");
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(xai.getChatCompletion(messages, {})).rejects.toThrow(
        "Request timeout"
      );
    });

    it("should handle invalid API responses", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        invalid: "response",
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await xai.getChatCompletion(messages, {});

      expect(result).toBeNull();
    });

    it("should handle corrupted stream data", async () => {
      const corruptedStream = {
        [Symbol.asyncIterator]: async function* () {
          yield null;
          yield undefined;
          yield { invalid: "data" };
          yield { choices: [{ delta: { content: "valid" } }] };
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(
        corruptedStream
      );
      (mockLLMPerformanceMonitor.measureStream as jest.Mock).mockImplementation(
        async (stream) => ({
          ...stream,
          endMeasurement: jest.fn(),
        })
      );

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await xai.streamGetChatCompletion(messages, {});

      expect(result.stream).toBeDefined();
    });

    it("should handle environment variable edge cases", () => {
      process.env.XAI_LLM_API_KEY = "";

      // Should throw error for empty API key
      expect(() => new XAiLLM()).toThrow("No xAI API key was set.");

      delete process.env.XAI_LLM_API_KEY;
      expect(() => new XAiLLM()).toThrow("No xAI API key was set.");
    });

    it("should handle missing MODEL_MAP entries", () => {
      const unknownModel = new XAiLLM(null, "unknown-grok-model");
      expect(unknownModel.promptWindowLimit()).toBe(131072); // Should use default
    });

    it("should handle model switching during operation", async () => {
      const xai = new XAiLLM(null, "grok-beta");

      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Response 1" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await xai.getChatCompletion(messages, {});

      // Simulate model change to another valid model
      xai.model = "grok-beta"; // Keep same model to avoid validation error

      await xai.getChatCompletion(messages, {});

      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith(
        expect.objectContaining({ model: "grok-beta" })
      );
      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith(
        expect.objectContaining({ model: "grok-beta" })
      );
    });

    it("should handle prompt with only attachments and no text", () => {
      const attachments: Attachment[] = [
        {
          name: "image.png",
          type: "image/png",
          contentString: "data:image/png;base64,test",
        },
      ];

      const result = xai["generateContent"]({
        userPrompt: "",
        attachments,
      });

      expect(Array.isArray(result)).toBe(true);
      const resultArray = result as Array<any>;
      expect(resultArray[0].text).toBe("");
    });

    it("should handle zero-length responses", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      const result = await xai.getChatCompletion(messages, {});

      expect(result?.textResponse).toBe("");
    });

    it("should handle stream interruption gracefully", async () => {
      const interruptedStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { choices: [{ delta: { content: "partial" } }] };
          throw new Error("Stream interrupted");
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(
        interruptedStream
      );

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(
        xai.streamGetChatCompletion(messages, {})
      ).resolves.toBeDefined();
    });

    it("should validate attachment size limits", () => {
      const hugeAttachment: Attachment = {
        name: "huge.txt",
        contentString: "x".repeat(10 * 1024 * 1024), // 10MB
        type: "text/plain",
        size: 10 * 1024 * 1024,
      };

      const result = xai["generateContent"]({
        userPrompt: "Process huge file",
        attachments: [hugeAttachment],
      });

      expect(Array.isArray(result)).toBe(true);
    });

    it("should handle circular references in error objects", async () => {
      const circularError = new Error("Circular error");
      (circularError as any).inner = circularError; // Create circular reference

      mockOpenAIClient.chat.completions.create.mockRejectedValueOnce(
        circularError
      );
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("Circular error");
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(xai.getChatCompletion(messages, {})).rejects.toThrow(
        "Circular error"
      );
    });
  });

  describe("xAI-specific error handling", () => {
    let xai: XAiLLM;

    beforeEach(() => {
      xai = new XAiLLM();
    });

    it("should handle xAI quota exceeded error", async () => {
      const quotaError = new Error("You exceeded your current quota");
      (quotaError as any).response = {
        data: {
          error: {
            message: "You exceeded your current quota",
            type: "insufficient_quota",
            code: "insufficient_quota",
          },
        },
      };

      mockOpenAIClient.chat.completions.create.mockRejectedValueOnce(
        quotaError
      );
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("You exceeded your current quota");
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(xai.getChatCompletion(messages, {})).rejects.toThrow(
        "You exceeded your current quota"
      );
    });

    it("should handle xAI server overload error", async () => {
      const overloadError = new Error("Server overloaded");
      (overloadError as any).status = 503;

      mockOpenAIClient.chat.completions.create.mockRejectedValueOnce(
        overloadError
      );
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("Server overloaded");
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(xai.getChatCompletion(messages, {})).rejects.toThrow(
        "Server overloaded"
      );
    });

    it("should handle invalid model error from xAI", async () => {
      const modelError = new Error("Model not found");
      (modelError as any).status = 404;

      mockOpenAIClient.chat.completions.create.mockRejectedValueOnce(
        modelError
      );
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("Model not found");
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(xai.getChatCompletion(messages, {})).rejects.toThrow(
        "Model not found"
      );
    });

    it("should handle xAI content policy violation", async () => {
      const policyError = new Error("Content policy violation");
      (policyError as any).response = {
        data: {
          error: {
            message: "Content policy violation",
            type: "content_policy_violation",
            code: "content_filtered",
          },
        },
      };

      mockOpenAIClient.chat.completions.create.mockRejectedValueOnce(
        policyError
      );
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error("Content policy violation");
          }
        });

      const messages: ChatMessage[] = [
        { role: "user", content: "Inappropriate content" },
      ];

      await expect(xai.getChatCompletion(messages, {})).rejects.toThrow(
        "Content policy violation"
      );
    });

    it("should preserve original error messages", async () => {
      const specificError = new Error(
        "Grok is temporarily unavailable. Please try again later."
      );
      mockOpenAIClient.chat.completions.create.mockRejectedValueOnce(
        specificError
      );
      mockLLMPerformanceMonitor.measureAsyncFunction = jest
        .fn()
        .mockImplementationOnce(async (promise) => {
          try {
            await promise;
          } catch (_e) {
            throw new Error(
              "Grok is temporarily unavailable. Please try again later."
            );
          }
        });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await expect(xai.getChatCompletion(messages, {})).rejects.toThrow(
        "Grok is temporarily unavailable. Please try again later."
      );
    });
  });

  describe("token counting and limits", () => {
    let xai: XAiLLM;

    beforeEach(() => {
      xai = new XAiLLM();
    });

    it("should correctly calculate token limits", () => {
      const limits = xai.limits;
      const total = limits.history + limits.system + limits.user;

      expect(total).toBe(xai.promptWindowLimit());
      expect(limits.user).toBeGreaterThan(limits.history);
      expect(limits.user).toBeGreaterThan(limits.system);
      expect(limits.user).toBe(131072 * 0.7);
      expect(limits.history).toBe(131072 * 0.15);
      expect(limits.system).toBe(131072 * 0.15);
    });

    it("should handle grok-beta model token limits", () => {
      const grokModel = new XAiLLM(null, "grok-beta");

      expect(grokModel.promptWindowLimit()).toBe(131072);
      expect(grokModel.limits.user).toBe(131072 * 0.7);
    });

    it("should handle unknown model token limits", () => {
      const unknownModel = new XAiLLM(null, "grok-unknown");

      expect(unknownModel.promptWindowLimit()).toBe(131072); // Falls back to default
      expect(unknownModel.limits.user).toBe(131072 * 0.7);
    });
  });

  describe("contextTexts handling", () => {
    let xai: XAiLLM;

    beforeEach(() => {
      xai = new XAiLLM();
      mockFormatContextTexts.mockClear();
    });

    it("should call appendContext with contextTexts", async () => {
      const contextTexts = ["Context 1", "Context 2"];
      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        contextTexts,
        userPrompt: "User",
      };

      await xai.constructPrompt(promptArgs);

      expect(mockFormatContextTexts).toHaveBeenCalledWith(contextTexts);
    });

    it("should handle empty contextTexts", async () => {
      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        contextTexts: [],
        userPrompt: "User",
      };

      const result = await xai.constructPrompt(promptArgs);

      // Empty contextTexts should not call formatContextTexts
      expect(mockFormatContextTexts).not.toHaveBeenCalled();
      expect(result[0].content).toBe("System");
    });

    it("should handle null contextTexts", async () => {
      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        contextTexts: null as any,
        userPrompt: "User",
      };

      const result = await xai.constructPrompt(promptArgs);

      // Null contextTexts should not call formatContextTexts
      expect(mockFormatContextTexts).not.toHaveBeenCalled();
      expect(result[0].content).toBe("System");
    });

    it("should handle undefined contextTexts", async () => {
      const promptArgs: PromptArgs = {
        systemPrompt: "System",
        userPrompt: "User",
      };

      const result = await xai.constructPrompt(promptArgs);

      // Undefined contextTexts should not call formatContextTexts
      expect(mockFormatContextTexts).not.toHaveBeenCalled();
      expect(result[0].content).toBe("System");
    });
  });

  describe("temperature handling", () => {
    let xai: XAiLLM;

    beforeEach(() => {
      xai = new XAiLLM();
    });

    it("should use provided temperature for completion", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Response" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await xai.getChatCompletion(messages, { temperature: 0.9 });

      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith({
        model: "grok-beta",
        messages,
        temperature: 0.9,
      });
    });

    it("should use provided temperature for streaming", async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { choices: [{ delta: { content: "stream" } }] };
        },
      };

      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockStream);

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      await xai.streamGetChatCompletion(messages, { temperature: 0.1 });

      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith({
        model: "grok-beta",
        stream: true,
        messages,
        temperature: 0.1,
      });
    });

    it("should handle extreme temperature values", async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: "Response" } }],
      });

      const messages: ChatMessage[] = [{ role: "user", content: "Test" }];

      // Test minimum temperature
      await xai.getChatCompletion(messages, { temperature: 0 });
      expect(mockOpenAIClient.chat.completions.create).toHaveBeenLastCalledWith(
        expect.objectContaining({ temperature: 0 })
      );

      // Test maximum temperature
      await xai.getChatCompletion(messages, { temperature: 2 });
      expect(mockOpenAIClient.chat.completions.create).toHaveBeenLastCalledWith(
        expect.objectContaining({ temperature: 2 })
      );
    });
  });
});
