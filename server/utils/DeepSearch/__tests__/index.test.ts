import { performDeepSearch, formatDeepSearchResults } from "../index";
import SystemSettings from "../../../models/systemSettings";
import { GoogleDeepSearchProvider } from "../deepsearchproviders/google";
import { BingDeepSearchProvider } from "../deepsearchproviders/bing";
import { BraveDeepSearchProvider } from "../deepsearchproviders/brave";
import { DuckDuckGoDeepSearchProvider } from "../deepsearchproviders/duckduckgo";
import { tSync } from "../../i18n";
import type {
  DeepSearchResult,
  DeepSearchSettings,
} from "../../../types/utils";
// Mock dependencies
jest.mock("../../../models/systemSettings");
jest.mock("../deepsearchproviders/google");
jest.mock("../deepsearchproviders/bing");
jest.mock("../deepsearchproviders/brave");
jest.mock("../deepsearchproviders/duckduckgo");
jest.mock("../../i18n", () => ({
  tSync: jest.fn(),
}));
jest.mock("../../../endpoints/upgradeDeepSearchPrompt", () => ({
  upgradeDeepSearchPrompt: jest.fn().mockResolvedValue("upgraded search query"),
}));
describe("DeepSearch", () => {
  const mockSystemSettings = SystemSettings as jest.Mocked<
    typeof SystemSettings
  >;
  const mockGoogleProvider = GoogleDeepSearchProvider as jest.MockedClass<
    typeof GoogleDeepSearchProvider
  >;
  const mockBingProvider = BingDeepSearchProvider as jest.MockedClass<
    typeof BingDeepSearchProvider
  >;
  const mockBraveProvider = BraveDeepSearchProvider as jest.MockedClass<
    typeof BraveDeepSearchProvider
  >;
  const mockDuckDuckGoProvider =
    DuckDuckGoDeepSearchProvider as jest.MockedClass<
      typeof DuckDuckGoDeepSearchProvider
    >;
  const mockTSync = tSync as jest.MockedFunction<typeof tSync>;
  let consoleLogSpy: jest.SpyInstance;
  let consoleErrorSpy: jest.SpyInstance;
  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();
    // Spy on console methods
    consoleLogSpy = jest.spyOn(console, "log").mockImplementation();
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();
    // Default mock for tSync
    mockTSync.mockReturnValue(
      "If any of the DeepSearch sources are relevant for the user query..."
    );
    // Default settings
    const defaultSettings: DeepSearchSettings = {
      enabled: true,
      provider: "google",
      apiKey: "test-api-key",
      modelId: "test-model",
      contextPercentage: 70,
    };
    mockSystemSettings.getDeepSearchSettings = jest
      .fn()
      .mockResolvedValue(defaultSettings);
    mockSystemSettings.get = jest.fn().mockResolvedValue({ value: "en" });
    // Mock provider search methods
    mockGoogleProvider.prototype.search = jest.fn().mockResolvedValue({
      text: "Google search summary",
      searchResults: [
        {
          title: "Result 1",
          link: "https://example1.com",
          snippet: "Snippet 1",
        },
        {
          title: "Result 2",
          link: "https://example2.com",
          snippet: "Snippet 2",
        },
      ],
    });
    mockBingProvider.prototype.search = jest.fn().mockResolvedValue({
      text: "",
      searchResults: [
        {
          title: "Bing Result",
          link: "https://bing.com",
          snippet: "Bing snippet",
        },
      ],
    });
    mockBraveProvider.prototype.search = jest.fn().mockResolvedValue({
      text: "Brave search summary",
      searchResults: [],
    });
    mockDuckDuckGoProvider.prototype.search = jest.fn().mockResolvedValue({
      text: "",
      searchResults: [
        {
          title: "DDG Result",
          link: "https://duckduckgo.com",
          snippet: "DDG snippet",
        },
      ],
    });
  });
  afterEach(() => {
    consoleLogSpy.mockRestore();
    consoleErrorSpy.mockRestore();
  });
  describe("performDeepSearch", () => {
    it("should perform search with Google provider", async () => {
      const result = await performDeepSearch("test query");
      expect(result).toEqual({
        text: "Google search summary",
        searchResults: [
          {
            title: "Result 1",
            link: "https://example1.com",
            snippet: "Snippet 1",
          },
          {
            title: "Result 2",
            link: "https://example2.com",
            snippet: "Snippet 2",
          },
        ],
      });
      expect(mockGoogleProvider).toHaveBeenCalledWith({
        apiKey: "test-api-key",
        modelId: "test-model",
        language: "en",
      });
      expect(mockGoogleProvider.prototype.search).toHaveBeenCalledWith(
        "upgraded search query"
      );
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          "[DeepSearch Factory] Creating Google provider."
        )
      );
    });
    it("should perform search with Bing provider", async () => {
      mockSystemSettings.getDeepSearchSettings.mockResolvedValue({
        enabled: true,
        provider: "bing",
        apiKey: "bing-api-key",
        modelId: "test-model",
        contextPercentage: 70,
      });
      const result = await performDeepSearch("test query");
      expect(result).toEqual({
        text: "",
        searchResults: [
          {
            title: "Bing Result",
            link: "https://bing.com",
            snippet: "Bing snippet",
          },
        ],
      });
      expect(mockBingProvider).toHaveBeenCalledWith({
        apiKey: "bing-api-key",
        modelId: "test-model",
        language: "en",
      });
    });
    it("should perform search with Brave provider", async () => {
      mockSystemSettings.getDeepSearchSettings.mockResolvedValue({
        enabled: true,
        provider: "brave",
        apiKey: "brave-api-key",
        modelId: "test-model",
        contextPercentage: 70,
      });
      const result = await performDeepSearch("test query");
      expect(result).toEqual({
        text: "Brave search summary",
        searchResults: [],
      });
      expect(mockBraveProvider).toHaveBeenCalledWith({
        apiKey: "brave-api-key",
        modelId: "test-model",
        language: "en",
      });
    });
    it("should perform search with DuckDuckGo provider without API key", async () => {
      mockSystemSettings.getDeepSearchSettings.mockResolvedValue({
        enabled: true,
        provider: "duckduckgo",
        apiKey: "",
        modelId: "test-model",
        contextPercentage: 70,
      });
      const result = await performDeepSearch("test query");
      expect(result).toEqual({
        text: "",
        searchResults: [
          {
            title: "DDG Result",
            link: "https://duckduckgo.com",
            snippet: "DDG snippet",
          },
        ],
      });
      expect(mockDuckDuckGoProvider).toHaveBeenCalledWith({
        apiKey: undefined,
        modelId: "test-model",
        language: "en",
      });
    });
    it("should return error when DeepSearch is disabled", async () => {
      mockSystemSettings.getDeepSearchSettings.mockResolvedValue({
        enabled: false,
        provider: "google",
        apiKey: "test-api-key",
        modelId: "test-model",
        contextPercentage: 70,
      });
      const result = await performDeepSearch("test query");
      expect(result).toEqual({
        text: "",
        searchResults: [],
        error: "DeepSearch is disabled or missing API key",
      });
      expect(consoleLogSpy).toHaveBeenCalledWith(
        "DeepSearch is disabled or missing API key"
      );
    });
    it("should return error when API key is missing for non-DuckDuckGo providers", async () => {
      mockSystemSettings.getDeepSearchSettings.mockResolvedValue({
        enabled: true,
        provider: "google",
        apiKey: "",
        modelId: "test-model",
        contextPercentage: 70,
      });
      const result = await performDeepSearch("test query");
      expect(result).toEqual({
        text: "",
        searchResults: [],
        error: "DeepSearch is disabled or missing API key",
      });
    });
    it("should throw error for unsupported provider", async () => {
      mockSystemSettings.getDeepSearchSettings.mockResolvedValue({
        enabled: true,
        provider: "unsupported",
        apiKey: "test-api-key",
        modelId: "test-model",
        contextPercentage: 70,
      });
      const result = await performDeepSearch("test query");
      expect(result).toEqual({
        text: "",
        searchResults: [],
        error: "Unsupported DeepSearch provider: unsupported",
      });
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          "Unsupported DeepSearch provider requested: unsupported"
        )
      );
    });
    it("should handle provider search errors", async () => {
      const searchError = new Error("Search API error");
      mockGoogleProvider.prototype.search.mockRejectedValue(searchError);
      const result = await performDeepSearch("test query");
      expect(result).toEqual({
        text: "",
        searchResults: [],
        error: "Search API error",
      });
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        "DeepSearch error:",
        searchError
      );
    });
    it("should handle query upgrade failure", async () => {
      const {
        upgradeDeepSearchPrompt,
      } = require("../../../endpoints/upgradeDeepSearchPrompt");
      upgradeDeepSearchPrompt.mockResolvedValue(null);
      const _result = await performDeepSearch("test query");
      expect(mockGoogleProvider.prototype.search).toHaveBeenCalledWith(
        "test query"
      );
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          'Failed to upgrade query, using original: "test query"'
        )
      );
    });
    it("should handle query upgrade error", async () => {
      const {
        upgradeDeepSearchPrompt,
      } = require("../../../endpoints/upgradeDeepSearchPrompt");
      upgradeDeepSearchPrompt.mockRejectedValue(new Error("Upgrade error"));
      const _result = await performDeepSearch("test query");
      expect(mockGoogleProvider.prototype.search).toHaveBeenCalledWith(
        "test query"
      );
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          "[DeepSearch] Error upgrading query: Upgrade error"
        )
      );
    });
    it("should use system language setting", async () => {
      mockSystemSettings.get.mockResolvedValue({
        id: 1,
        label: "language",
        value: "fr",
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      });
      await performDeepSearch("test query");
      expect(mockGoogleProvider).toHaveBeenCalledWith({
        apiKey: "test-api-key",
        modelId: "test-model",
        language: "fr",
      });
    });
    it("should default to English when language not set", async () => {
      mockSystemSettings.get.mockResolvedValue({
        id: 1,
        label: "language",
        value: "en",
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      });
      await performDeepSearch("test query");
      expect(mockGoogleProvider).toHaveBeenCalledWith({
        apiKey: "test-api-key",
        modelId: "test-model",
        language: "en",
      });
    });
    it("should handle null settings gracefully", async () => {
      mockSystemSettings.getDeepSearchSettings.mockResolvedValue({
        enabled: false,
        provider: "google",
        apiKey: "",
        modelId: "",
        contextPercentage: 70,
      });
      const result = await performDeepSearch("test query");
      expect(result).toEqual({
        text: "",
        searchResults: [],
        error: "DeepSearch is disabled or missing API key",
      });
    });
    it("should handle provider case insensitivity", async () => {
      mockSystemSettings.getDeepSearchSettings.mockResolvedValue({
        enabled: true,
        provider: "GOOGLE",
        apiKey: "test-api-key",
        modelId: "test-model",
        contextPercentage: 70,
      });
      const result = await performDeepSearch("test query");
      expect(mockGoogleProvider).toHaveBeenCalled();
      expect(result.text).toBe("Google search summary");
    });
  });
  describe("formatDeepSearchResults", () => {
    it("should format results with both text and search results", () => {
      const results: DeepSearchResult = {
        text: "This is a search summary",
        searchResults: [
          {
            title: "Result 1",
            link: "https://example1.com",
            snippet: "Snippet 1",
          },
          {
            title: "Result 2",
            link: "https://example2.com",
            snippet: "Snippet 2",
          },
        ],
      };
      const formatted = formatDeepSearchResults(results);
      expect(formatted).toContain("<DeepSearch data>");
      expect(formatted).toContain("Web Search Summary:");
      expect(formatted).toContain("This is a search summary");
      expect(formatted).toContain("Web Search Snippets:");
      expect(formatted).toContain("1. Title: Result 1");
      expect(formatted).toContain("   Link: https://example1.com");
      expect(formatted).toContain("   Snippet: Snippet 1");
      expect(formatted).toContain("</END DeepSearch data>");
      expect(formatted).toContain("<DeepSearch_Instruction>");
      expect(formatted).toContain("</END DeepSearch_Instruction>");
    });
    it("should format results with only text", () => {
      const results: DeepSearchResult = {
        text: "Only text summary",
        searchResults: [],
      };
      const formatted = formatDeepSearchResults(results);
      expect(formatted).toContain("Web Search Summary:");
      expect(formatted).toContain("Only text summary");
      expect(formatted).not.toContain("Web Search Snippets:");
    });
    it("should format results with only search results", () => {
      const results: DeepSearchResult = {
        text: "",
        searchResults: [
          { title: "Result", link: "https://example.com", snippet: "Snippet" },
        ],
      };
      const formatted = formatDeepSearchResults(results);
      expect(formatted).not.toContain("Web Search Summary:");
      expect(formatted).toContain("Web Search Snippets:");
      expect(formatted).toContain("1. Title: Result");
    });
    it("should return empty string for error results", () => {
      const results: DeepSearchResult = {
        text: "",
        searchResults: [],
        error: "Search failed",
      };
      const formatted = formatDeepSearchResults(results);
      expect(formatted).toBe("");
      expect(consoleLogSpy).toHaveBeenCalledWith(
        "[DeepSearch] Skipping formatting due to error:",
        "Search failed"
      );
    });
    it("should return empty string for null results", () => {
      const formatted = formatDeepSearchResults(null as any);
      expect(formatted).toBe("");
    });
    it("should return empty string for empty results", () => {
      const results: DeepSearchResult = {
        text: "",
        searchResults: [],
      };
      const formatted = formatDeepSearchResults(results);
      expect(formatted).toBe("");
      expect(consoleLogSpy).toHaveBeenCalledWith(
        "[DeepSearch] No text or search results to format."
      );
    });
    it("should handle missing search results array", () => {
      const results: DeepSearchResult = {
        text: "Text only",
        searchResults: undefined as any,
      };
      const formatted = formatDeepSearchResults(results);
      expect(formatted).toContain("Web Search Summary:");
      expect(formatted).not.toContain("Web Search Snippets:");
    });
    it("should use localized instruction when available", () => {
      mockTSync.mockReturnValue("Localized instruction text");
      const results: DeepSearchResult = {
        text: "Test",
        searchResults: [],
      };
      const formatted = formatDeepSearchResults(results);
      expect(formatted).toContain(
        "<DeepSearch_Instruction>Localized instruction text</END DeepSearch_Instruction>"
      );
      expect(mockTSync).toHaveBeenCalledWith("deep_search.instruction", null);
    });
    it("should use default instruction when localization fails", () => {
      mockTSync.mockReturnValue("");
      const results: DeepSearchResult = {
        text: "Test",
        searchResults: [],
      };
      const formatted = formatDeepSearchResults(results);
      expect(formatted).toContain(
        "If any of the DeepSearch sources are relevant for the user query"
      );
    });
    it("should handle very long formatted text in debug log", () => {
      const longText = "x".repeat(1000);
      const results: DeepSearchResult = {
        text: longText,
        searchResults: [],
      };
      formatDeepSearchResults(results);
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining("[DeepSearch Debug] Formatted text:"),
        expect.stringContaining("...")
      );
    });
    it("should log debug information about results structure", () => {
      const results: DeepSearchResult = {
        text: "Text",
        searchResults: [{ title: "Title", link: "Link", snippet: "Snippet" }],
      };
      formatDeepSearchResults(results);
      expect(consoleLogSpy).toHaveBeenCalledWith(
        "[DeepSearch Debug] Formatting results:",
        expect.objectContaining({
          hasResults: true,
          hasError: false,
          hasText: true,
          hasSearchResults: true,
          searchResultsCount: 1,
        })
      );
    });
    it("should handle search results with missing fields", () => {
      const results: DeepSearchResult = {
        text: "",
        searchResults: [
          { title: "", link: "", snippet: "" },
          { title: "Title", link: undefined as any, snippet: null as any },
        ],
      };
      const formatted = formatDeepSearchResults(results);
      expect(formatted).toContain("1. Title: ");
      expect(formatted).toContain("   Link: ");
      expect(formatted).toContain("   Snippet: ");
      expect(formatted).toContain("2. Title: Title");
      expect(formatted).toContain("   Link: undefined");
      expect(formatted).toContain("   Snippet: null");
    });
  });
  describe("Edge cases and error scenarios", () => {
    it("should handle SystemSettings throwing error", async () => {
      mockSystemSettings.getDeepSearchSettings.mockRejectedValue(
        new Error("Database error")
      );
      const result = await performDeepSearch("test query");
      expect(result).toEqual({
        text: "",
        searchResults: [],
        error: "Database error",
      });
    });
    it("should handle provider constructor throwing error", async () => {
      mockGoogleProvider.mockImplementation(() => {
        throw new Error("Provider initialization failed");
      });
      const result = await performDeepSearch("test query");
      expect(result).toEqual({
        text: "",
        searchResults: [],
        error: "Provider initialization failed",
      });
    });
    it("should handle non-Error objects in catch blocks", async () => {
      // Make the search method reject with a string instead of Error
      mockSystemSettings.getDeepSearchSettings.mockRejectedValue(
        "String error"
      );
      const result = await performDeepSearch("test query");
      expect(result).toEqual({
        text: "",
        searchResults: [],
        error: "String error",
      });
    });
    it("should handle undefined provider name", async () => {
      mockSystemSettings.getDeepSearchSettings.mockResolvedValue({
        enabled: true,
        provider: undefined as any,
        apiKey: "test-api-key",
        modelId: "test-model",
        contextPercentage: 70,
      });
      const result = await performDeepSearch("test query");
      expect(result.error).toBe("Unsupported DeepSearch provider: undefined");
    });
    it("should handle empty provider name", () => {
      mockSystemSettings.getDeepSearchSettings.mockResolvedValue({
        enabled: true,
        provider: "",
        apiKey: "test-api-key",
        modelId: "test-model",
        contextPercentage: 70,
      });
      const result = performDeepSearch("test query");
      expect(result).resolves.toHaveProperty("error");
    });
    it("should handle very long queries", async () => {
      const longQuery = "x".repeat(10000);
      // Reset the google provider for this test
      const searchMock = jest.fn().mockResolvedValue({
        text: "Google search summary for long query",
        searchResults: [],
      });
      mockGoogleProvider.mockImplementation(
        () =>
          ({
            apiKey: "test-api-key",
            modelId: "test-model",
            client: {} as any,
            safetySettings: {} as any,
            extractSearchResults: jest.fn(),
            search: searchMock,
          }) as any
      );
      // Mock the upgrade function to handle long queries
      const {
        upgradeDeepSearchPrompt,
      } = require("../../../endpoints/upgradeDeepSearchPrompt");
      upgradeDeepSearchPrompt.mockResolvedValue(
        "truncated and optimized query"
      );
      // Test that long queries are still processed successfully
      const result = await performDeepSearch(longQuery);
      // The mock should have been called
      expect(searchMock).toHaveBeenCalled();
      expect(result).toHaveProperty(
        "text",
        "Google search summary for long query"
      );
      // Verify the upgrade function was called with the long query
      expect(upgradeDeepSearchPrompt).toHaveBeenCalledWith(longQuery);
    });
    it("should handle concurrent searches", async () => {
      // Track calls to search
      let searchCallCount = 0;
      mockGoogleProvider.mockImplementation(
        () =>
          ({
            apiKey: "test-api-key",
            modelId: "test-model",
            client: {} as any,
            safetySettings: {} as any,
            extractSearchResults: jest.fn(),
            search: jest.fn().mockImplementation(() => {
              searchCallCount++;
              return Promise.resolve({
                text: `Search result ${searchCallCount}`,
                searchResults: [],
              });
            }),
          }) as any
      );
      const promises = [
        performDeepSearch("query 1"),
        performDeepSearch("query 2"),
        performDeepSearch("query 3"),
      ];
      const results = await Promise.all(promises);
      expect(results).toHaveLength(3);
      expect(searchCallCount).toBe(3);
      expect(results[0]).toHaveProperty("text");
      expect(results[1]).toHaveProperty("text");
      expect(results[2]).toHaveProperty("text");
    });
    it("should handle undefined contextPercentage", async () => {
      // Create a new mock for this test
      mockGoogleProvider.mockImplementation(
        () =>
          ({
            apiKey: "test-api-key",
            modelId: "test-model",
            client: {} as any,
            safetySettings: {} as any,
            extractSearchResults: jest.fn(),
            search: jest.fn().mockResolvedValue({
              text: "Google search summary",
              searchResults: [],
            }),
          }) as any
      );
      mockSystemSettings.getDeepSearchSettings.mockResolvedValue({
        enabled: true,
        provider: "google",
        apiKey: "test-api-key",
        modelId: "test-model",
        contextPercentage: 70,
      });
      const result = await performDeepSearch("test query");
      expect(result).toHaveProperty("text", "Google search summary");
    });
  });
});
