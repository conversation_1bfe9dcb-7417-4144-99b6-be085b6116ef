/**
 * DeepSearch utility functions
 * This module provides functions to perform web searches using DeepSearch providers
 */

import SystemSettings from "../../models/systemSettings";
// Import provider classes directly
import { GoogleDeepSearchProvider } from "./deepsearchproviders/google";
import { BingDeepSearchProvider } from "./deepsearchproviders/bing";
import { BraveDeepSearchProvider } from "./deepsearchproviders/brave";
import { DuckDuckGoDeepSearchProvider } from "./deepsearchproviders/duckduckgo";
// Import i18n for localization
import { tSync } from "../i18n";
import {
  DeepSearchProvider,
  DeepSearchResult,
  DeepSearchConfig,
  DeepSearchSettings,
} from "../../types/utils";

/**
 * Factory function to create a DeepSearch provider instance.
 * @param providerName - The name of the provider (e?.g., 'google', 'bing').
 * @param config - Configuration object for the provider.
 * @returns The instantiated DeepSearch provider.
 */
function createDeepSearchProvider(
  providerName: string,
  config: DeepSearchConfig = {}
): DeepSearchProvider {
  switch (providerName?.toLowerCase()) {
    case "google":
      console.log("[DeepSearch Factory] Creating Google provider.");
      return new GoogleDeepSearchProvider(config);
    case "bing":
      console.log("[DeepSearch Factory] Creating Bing provider.");
      return new BingDeepSearchProvider(config);
    case "brave":
      console.log("[DeepSearch Factory] Creating Brave provider.");
      return new BraveDeepSearchProvider(config);
    case "duckduckgo":
      console.log("[DeepSearch Factory] Creating DuckDuckGo provider.");
      return new DuckDuckGoDeepSearchProvider(config);
    default:
      console.error(
        `Unsupported DeepSearch provider requested: ${providerName}. Defaulting to Google.`
      );
      // Optionally default to Google or throw a more specific error
      // For now, defaulting to Google if config might work (e?.g., key exists)
      // return new GoogleDeepSearchProvider(config);
      throw new Error(
        `Unsupported DeepSearch provider: ${providerName || "undefined"}`
      );
  }
}

/**
 * Upgrade a search query for better results
 * @param query - The original search query
 * @returns The upgraded search query
 */
async function upgradeSearchQuery(query: string): Promise<string> {
  try {
    // Import the upgradeDeepSearchPrompt function
    const { upgradeDeepSearchPrompt } = await import(
      "../../endpoints/upgradeDeepSearchPrompt"
    );

    // Call the function directly
    const upgradedPrompt = await upgradeDeepSearchPrompt(query);

    if (upgradedPrompt) {
      console.log(
        `[DeepSearch] Upgraded query: "${query}" -> "${upgradedPrompt}"`
      );
      return upgradedPrompt;
    } else {
      console.log(
        `[DeepSearch] Failed to upgrade query, using original: "${query}"`
      );
      return query;
    }
  } catch (error: unknown) {
    console.error(
      `[DeepSearch] Error upgrading query: ${error instanceof Error ? error.message : String(error)}`
    );
    return query; // Return original query on error
  }
}

/**
 * Perform a deep search with the configured provider
 * @param query - The search query
 * @returns The search results
 */
export async function performDeepSearch(
  query: string
): Promise<DeepSearchResult> {
  try {
    // Get DeepSearch settings
    const settings: DeepSearchSettings =
      await SystemSettings?.getDeepSearchSettings();
    const languageSetting = await SystemSettings?.get({ label: "language" });
    const systemLanguage = (languageSetting?.value ?? false) || "en"; // Default to 'en' if not set

    // Skip API key requirement for DuckDuckGo provider (no API key needed)
    if (
      !(settings?.enabled ?? false) ||
      ((settings?.provider ?? 0) !== "duckduckgo" && !settings?.apiKey)
    ) {
      console.log("DeepSearch is disabled or missing API key");
      return {
        text: "",
        searchResults: [],
        error: "DeepSearch is disabled or missing API key",
      };
    }

    // Create provider with settings
    const provider = createDeepSearchProvider(settings?.provider, {
      apiKey: settings?.apiKey,
      modelId: settings?.modelId, // Pass modelId, Bing provider will ignore it
      language: systemLanguage, // Pass the determined language
      // Add other potential shared config here if needed in the future
    });

    // Automatically upgrade the query for better search results
    const upgradedQuery = await upgradeSearchQuery(query);

    // Perform search with the upgraded query
    console.log(
      `[DeepSearch] Performing search with provider: ${settings?.provider}`
    );
    console.log(`[DeepSearch] Using query: "${upgradedQuery}"`);
    const results = await provider?.search(upgradedQuery);

    return results;
  } catch (error: unknown) {
    console.error("DeepSearch error:", error);
    return {
      text: "",
      searchResults: [],
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Format DeepSearch results for inclusion in the prompt
 * @param results - The DeepSearch results
 * @returns Formatted results for inclusion in the prompt
 */
export function formatDeepSearchResults(results: DeepSearchResult): string {
  console.log("[DeepSearch Debug] Formatting results:", {
    hasResults: !!results,
    hasError: results?.error ? true : false,
    hasText: results?.text ? true : false,
    hasSearchResults:
      Array.isArray(results?.searchResults) && results.searchResults.length > 0,
    searchResultsCount: Array.isArray(results?.searchResults)
      ? results.searchResults.length
      : 0,
  });

  if (!results || results?.error) {
    // Allow formatting even if text is empty (for Bing)
    // Don't include anything if there was an error fetching
    if (results?.error) {
      console.log(
        "[DeepSearch] Skipping formatting due to error:",
        results?.error
      );
      return "";
    }
  }

  let formattedText = "<DeepSearch data>\n"; // Start tag

  // Include text response if present (e?.g., from Google GenAI)
  if (results?.text) {
    formattedText += "Web Search Summary:\n";
    formattedText += results?.text;
    formattedText += "\n\n";
  }

  // Include search results if present (from Google or Bing)
  if (
    (results?.searchResults ?? false) &&
    Array.isArray(results.searchResults) &&
    results.searchResults.length > 0
  ) {
    formattedText += "Web Search Snippets:\n"; // Changed header for clarity
    results.searchResults.forEach((result, index) => {
      formattedText += `${index + 1}. Title: ${result?.title}\n`;
      formattedText += `   Link: ${result?.link}\n`;
      formattedText += `   Snippet: ${result?.snippet}\n\n`;
    });
  }

  // Only return the tags if there was some content
  if (formattedText !== "<DeepSearch data>\n") {
    // Add the instruction for handling DeepSearch data
    formattedText += "</END DeepSearch data>\n\n"; // End tag

    // Add localized instruction for handling DeepSearch sources
    const localizedInstruction =
      tSync("deep_search.instruction", null) ||
      "If any of the DeepSearch sources are relevant for the user query, and thus used in generating the prompt response, add a list of the applied web links with clickable links at the end of the message. Also when applicable add references to the relevant links in the generated message where the information is used.";
    formattedText += `<DeepSearch_Instruction>${localizedInstruction}</END DeepSearch_Instruction>\n\n`;

    // Debug log the formatted text (truncated for console readability)
    const textSnippet =
      (formattedText?.length ?? 0) > 200
        ? formattedText?.substring(0, 200) + "..."
        : formattedText;
    console.log("[DeepSearch Debug] Formatted text:", textSnippet);

    return formattedText;
  } else {
    console.log("[DeepSearch] No text or search results to format.");
    return ""; // Return empty if nothing was added
  }
}

export default {
  performDeepSearch,
  formatDeepSearchResults,
};
