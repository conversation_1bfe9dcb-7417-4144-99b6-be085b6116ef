import { OpenAIClient, AzureKeyCredential } from "@azure/openai";
import { toChunks } from "../../helpers";
import { EmbeddingEngine, ProviderError } from "../../../types/ai-providers";

interface EmbeddingData {
  embedding: number[];
  index?: number;
}

interface AzureEmbeddingResponse {
  data: EmbeddingData[];
}

interface BatchResult {
  data: EmbeddingData[] | null;
  error: ProviderError | null;
}

export class AzureOpenAiEmbedder implements EmbeddingEngine {
  private openai: OpenAIClient;
  public maxConcurrentChunks: number;
  public embeddingMaxChunkLength: number;

  constructor() {
    if (!process.env.AZURE_OPENAI_ENDPOINT)
      throw new Error("No Azure API endpoint was set.");
    if (!process.env.AZURE_OPENAI_KEY)
      throw new Error("No Azure API key was set.");

    this.openai = new OpenAIClient(
      process.env.AZURE_OPENAI_ENDPOINT,
      new AzureKeyCredential(process.env.AZURE_OPENAI_KEY)
    );

    // Limit of how many strings we can process in a single pass to stay with resource or network limits
    // https://learn.microsoft.com/en-us/azure/ai-services/openai/faq#i-am-trying-to-use-embeddings-and-received-the-error--invalidrequesterror--too-many-inputs--the-max-number-of-inputs-is-1---how-do-i-fix-this-:~:text=consisting%20of%20up%20to%2016%20inputs%20per%20API%20request
    this.maxConcurrentChunks = 16;

    // https://learn.microsoft.com/en-us/answers/questions/1188074/text-embedding-ada-002-token-context-length
    this.embeddingMaxChunkLength = 2048;
  }

  async embedTextInput(textInput: string | string[]): Promise<number[]> {
    const result = await this.embedChunks(
      Array.isArray(textInput) ? textInput : [textInput]
    );
    return result?.[0] || [];
  }

  async embedChunks(textChunks: string[] = []): Promise<number[][] | null> {
    const textEmbeddingModel =
      process.env.EMBEDDING_MODEL_PREF || "text-embedding-ada-002";
    if (!textEmbeddingModel)
      throw new Error(
        "No EMBEDDING_MODEL_PREF ENV defined. This must the name of a deployment on your Azure account for an embedding model."
      );

    // Because there is a limit on how many chunks can be sent at once to Azure OpenAI
    // we concurrently execute each max batch of text chunks possible.
    // Refer to constructor maxConcurrentChunks for more info.
    const embeddingRequests: Promise<BatchResult>[] = [];
    for (const chunk of toChunks(textChunks, this.maxConcurrentChunks)) {
      embeddingRequests.push(
        new Promise<BatchResult>((resolve) => {
          this.openai
            .getEmbeddings(textEmbeddingModel, chunk)
            .then((res: AzureEmbeddingResponse) => {
              resolve({ data: res.data, error: null });
            })
            .catch((e: Error | unknown) => {
              const error: ProviderError = {
                type:
                  (e as { response?: { status?: string } })?.response?.status ||
                  "failed_to_embed",
                message: e instanceof Error ? e.message : String(e),
              };
              resolve({ data: null, error });
            });
        })
      );
    }

    const { data = [], error = null } = await Promise.all(
      embeddingRequests
    ).then((results) => {
      // If any errors were returned from Azure OpenAI abort the entire sequence because the embeddings
      // will be incomplete.
      const errors = results
        .filter((res) => !!res.error)
        .map((res) => res.error!)
        .flat();
      if ((errors?.length ?? 0) > 0) {
        return {
          data: [],
          error: `Azure OpenAI Failed to embed: ${errors
            .map((e) => e.message)
            .join(", ")}`,
        };
      }
      return {
        data: results.map((res) => res?.data || []).flat(),
        error: null,
      };
    });

    if (error) throw new Error(error);
    return (data?.length ?? 0) > 0 &&
      data.every((embd) =>
        Object.prototype.hasOwnProperty.call(embd, "embedding")
      )
      ? data.map((embd) => embd.embedding)
      : null;
  }
}

export default {
  AzureOpenAiEmbedder,
};
