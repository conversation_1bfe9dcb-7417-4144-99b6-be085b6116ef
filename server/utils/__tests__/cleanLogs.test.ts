/**
 * Comprehensive tests for log cleanup utilities
 * Tests file system operations, error handling, and edge cases
 */

import { promises as fsPromises, existsSync } from "fs";
import { cleanOldLogs } from "../cleanLogs";
import { cleanOldDocxSessionFiles } from "../files/docxSessionCleanup";

// Mock file system operations
jest.mock("fs", () => ({
  promises: {
    access: jest.fn(),
    readFile: jest.fn(),
    writeFile: jest.fn(),
    readdir: jest.fn(),
    stat: jest.fn(),
    unlink: jest.fn(),
  },
  existsSync: jest.fn(),
}));

// Mock the docx cleanup module
jest.mock("../files/docxSessionCleanup", () => ({
  cleanOldDocxSessionFiles: jest.fn(),
}));

const mockFsPromises = fsPromises as jest.Mocked<typeof fsPromises>;
const mockExistsSync = existsSync as jest.MockedFunction<typeof existsSync>;
const mockCleanOldDocxSessionFiles =
  cleanOldDocxSessionFiles as jest.MockedFunction<
    typeof cleanOldDocxSessionFiles
  >;

describe("Log Cleanup Utilities Tests", () => {
  let consoleSpy: {
    log: jest.SpyInstance;
    error: jest.SpyInstance;
    warn: jest.SpyInstance;
  };
  let originalNodeEnv: string | undefined;

  beforeEach(() => {
    jest.clearAllMocks();

    // Save original NODE_ENV and temporarily set to development
    // This is needed because cleanOldUploadLogs skips execution in test environment
    originalNodeEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = "development";

    // Spy on console methods
    consoleSpy = {
      log: jest.spyOn(console, "log").mockImplementation(),
      error: jest.spyOn(console, "error").mockImplementation(),
      warn: jest.spyOn(console, "warn").mockImplementation(),
    };
  });

  afterEach(() => {
    // Restore console methods
    Object.values(consoleSpy).forEach((spy) => spy.mockRestore());

    // Restore original NODE_ENV
    process.env.NODE_ENV = originalNodeEnv;
  });

  describe("Upload logs cleanup", () => {
    it("should clean old upload logs successfully", async () => {
      const mockLogContent = [
        { dateAdded: "2024-01-01T00:00:00Z", filename: "old.pdf" },
        { dateAdded: new Date().toISOString(), filename: "recent.pdf" },
      ];

      const logFileContent =
        mockLogContent.map((log) => JSON.stringify(log)).join("\n") + "\n";

      // Mock for logs directory check
      mockFsPromises.access
        .mockRejectedValueOnce(new Error("Not found")) // logs dir
        .mockRejectedValueOnce(new Error("Not found")) // document-builder dir
        .mockResolvedValueOnce(undefined); // upload logs file exists

      mockExistsSync.mockReturnValue(false);
      mockFsPromises.readFile.mockResolvedValue(logFileContent);
      mockFsPromises.writeFile.mockResolvedValue();
      mockCleanOldDocxSessionFiles.mockResolvedValue({
        success: true,
        deletedCount: 0,
        error: null,
      });

      await cleanOldLogs();

      expect(mockFsPromises.readFile).toHaveBeenCalled();
      expect(mockFsPromises.writeFile).toHaveBeenCalledWith(
        expect.any(String),
        expect.stringContaining("recent.pdf"),
        "utf8"
      );
      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining("Cleaned up 1 old upload logs")
      );
    });

    it("should handle missing upload logs file", async () => {
      // All access calls fail
      mockFsPromises.access.mockRejectedValue(new Error("File not found"));
      mockExistsSync.mockReturnValue(false);
      mockCleanOldDocxSessionFiles.mockResolvedValue({
        success: true,
        deletedCount: 0,
        error: null,
      });

      await cleanOldLogs();

      expect(consoleSpy.log).toHaveBeenCalledWith(
        "Upload logs file does not exist, skipping cleanup"
      );
      expect(mockFsPromises.readFile).not.toHaveBeenCalled();
    });

    it("should handle corrupted upload logs file", async () => {
      const corruptedContent = 'invalid json\n{"valid": "json"}\ninvalid again';

      // Mock for logs directory check
      mockFsPromises.access
        .mockRejectedValueOnce(new Error("Not found")) // logs dir
        .mockRejectedValueOnce(new Error("Not found")) // document-builder dir
        .mockResolvedValueOnce(undefined); // upload logs file exists

      mockExistsSync.mockReturnValue(false);
      mockFsPromises.readFile.mockResolvedValue(corruptedContent);
      mockFsPromises.writeFile.mockResolvedValue();
      mockCleanOldDocxSessionFiles.mockResolvedValue({
        success: true,
        deletedCount: 0,
        error: null,
      });

      await cleanOldLogs();

      expect(consoleSpy.error).toHaveBeenCalledWith("Error parsing log line");
      // Should still process valid entries
      expect(mockFsPromises.writeFile).toHaveBeenCalled();
    });

    it("should handle empty upload logs file", async () => {
      // Mock for logs directory check
      mockFsPromises.access
        .mockRejectedValueOnce(new Error("Not found")) // logs dir
        .mockRejectedValueOnce(new Error("Not found")) // document-builder dir
        .mockResolvedValueOnce(undefined); // upload logs file exists

      mockExistsSync.mockReturnValue(false);
      mockFsPromises.readFile.mockResolvedValue("");
      mockCleanOldDocxSessionFiles.mockResolvedValue({
        success: true,
        deletedCount: 0,
        error: null,
      });

      await cleanOldLogs();

      expect(mockFsPromises.writeFile).not.toHaveBeenCalled();
    });

    it("should handle file system errors during upload log cleanup", async () => {
      // Mock for logs directory check
      mockFsPromises.access
        .mockRejectedValueOnce(new Error("Not found")) // logs dir
        .mockRejectedValueOnce(new Error("Not found")) // document-builder dir
        .mockResolvedValueOnce(undefined); // upload logs file exists

      mockExistsSync.mockReturnValue(false);
      mockFsPromises.readFile.mockRejectedValue(new Error("Read error"));
      mockCleanOldDocxSessionFiles.mockResolvedValue({
        success: true,
        deletedCount: 0,
        error: null,
      });

      await cleanOldLogs();

      expect(consoleSpy.error).toHaveBeenCalledWith(
        "Error cleaning old upload logs"
      );
    });
  });

  describe("General log file cleanup", () => {
    it("should clean old log files successfully", async () => {
      const twoDaysAgo = Date.now() - 3 * 24 * 60 * 60 * 1000; // 3 days ago
      const yesterday = Date.now() - 1 * 24 * 60 * 60 * 1000; // 1 day ago

      mockExistsSync.mockReturnValue(true);
      // Mock multiple access calls
      mockFsPromises.access
        .mockResolvedValueOnce(undefined) // logs dir exists
        .mockResolvedValueOnce(undefined) // old.log exists before deletion
        .mockRejectedValueOnce(new Error("Not found")) // document-builder dir
        .mockRejectedValueOnce(new Error("Not found")); // upload logs file

      mockFsPromises.readdir.mockResolvedValue([
        "old.log",
        "recent.log",
      ] as any);
      mockCleanOldDocxSessionFiles.mockResolvedValue({
        success: true,
        deletedCount: 0,
        error: null,
      });

      mockFsPromises.stat
        .mockResolvedValueOnce({
          isFile: () => true,
          mtimeMs: twoDaysAgo,
        } as any)
        .mockResolvedValueOnce({
          isFile: () => true,
          mtimeMs: yesterday,
        } as any);

      mockFsPromises.unlink.mockResolvedValue();

      await cleanOldLogs();

      expect(mockFsPromises.unlink).toHaveBeenCalledWith(
        expect.stringContaining("old.log")
      );
      expect(mockFsPromises.unlink).not.toHaveBeenCalledWith(
        expect.stringContaining("recent.log")
      );
      expect(consoleSpy.log).toHaveBeenCalledWith(
        "Deleted old log file: old.log"
      );
    });

    it("should handle missing logs directory", async () => {
      mockFsPromises.access.mockRejectedValue(new Error("Directory not found"));
      mockExistsSync.mockReturnValue(false);

      await cleanOldLogs();

      expect(consoleSpy.log).toHaveBeenCalledWith(
        "Logs directory does not exist, skipping cleanup"
      );
      expect(mockFsPromises.readdir).not.toHaveBeenCalled();
    });

    it("should skip non-file entries in logs directory", async () => {
      mockExistsSync.mockReturnValue(true);
      mockFsPromises.access.mockResolvedValue();
      mockFsPromises.readdir.mockResolvedValue(["subdir", "file.log"] as any);

      mockFsPromises.stat
        .mockResolvedValueOnce({ isFile: () => false, mtimeMs: 0 } as any) // Directory
        .mockResolvedValueOnce({
          isFile: () => true,
          mtimeMs: Date.now(),
        } as any); // Recent file

      await cleanOldLogs();

      expect(mockFsPromises.unlink).not.toHaveBeenCalled();
    });

    it("should handle file system errors during log file processing", async () => {
      mockExistsSync.mockReturnValue(true);
      mockFsPromises.access.mockResolvedValue();
      mockFsPromises.readdir.mockResolvedValue(["error.log"] as any);
      mockFsPromises.stat.mockRejectedValue(new Error("Stat error"));

      await cleanOldLogs();

      expect(consoleSpy.error).toHaveBeenCalledWith(
        "Error processing log file error.log"
      );
    });

    it("should handle file deletion errors", async () => {
      const oldTime = Date.now() - 3 * 24 * 60 * 60 * 1000;

      mockExistsSync.mockReturnValue(true);
      mockFsPromises.access.mockResolvedValueOnce(); // Directory access
      mockFsPromises.readdir.mockResolvedValue(["locked.log"] as any);
      mockFsPromises.stat.mockResolvedValue({
        isFile: () => true,
        mtimeMs: oldTime,
      } as any);
      mockFsPromises.access.mockResolvedValueOnce(); // File exists check
      mockFsPromises.unlink.mockRejectedValue(new Error("Permission denied"));

      await cleanOldLogs();

      expect(consoleSpy.error).toHaveBeenCalledWith(
        "Error processing log file locked.log"
      );
    });
  });

  describe("Document builder file cleanup", () => {
    it("should clean old document builder files", async () => {
      const oldTime = Date.now() - 3 * 24 * 60 * 60 * 1000;

      mockExistsSync.mockReturnValue(true);
      mockFsPromises.access
        .mockResolvedValueOnce() // Logs directory
        .mockResolvedValueOnce() // Document builder directory
        .mockResolvedValueOnce(); // File exists check

      mockFsPromises.readdir
        .mockResolvedValueOnce([]) // Empty logs directory
        .mockResolvedValueOnce(["old-draft.docx"] as any); // Document builder files

      mockFsPromises.stat.mockResolvedValue({
        isFile: () => true,
        mtimeMs: oldTime,
      } as any);

      mockFsPromises.unlink.mockResolvedValue();

      await cleanOldLogs();

      expect(mockFsPromises.unlink).toHaveBeenCalledWith(
        expect.stringContaining("old-draft.docx")
      );
      expect(consoleSpy.log).toHaveBeenCalledWith(
        "Deleted old document-builder file: old-draft.docx"
      );
    });

    it("should handle missing document builder directory", async () => {
      mockExistsSync.mockReturnValue(true);
      mockFsPromises.access
        .mockResolvedValueOnce() // Logs directory exists
        .mockRejectedValueOnce(new Error("Document builder dir not found")); // Document builder doesn't exist

      mockFsPromises.readdir.mockResolvedValueOnce([]); // Empty logs directory

      await cleanOldLogs();

      expect(consoleSpy.log).toHaveBeenCalledWith(
        "document-builder directory does not exist, skipping cleanup"
      );
    });
  });

  describe("DOCX session file cleanup", () => {
    it("should call DOCX cleanup and handle success", async () => {
      mockExistsSync.mockReturnValue(false); // No logs directory
      mockCleanOldDocxSessionFiles.mockResolvedValue({
        success: true,
        deletedCount: 5,
        error: null,
      });

      await cleanOldLogs();

      expect(mockCleanOldDocxSessionFiles).toHaveBeenCalledWith(14);
      expect(consoleSpy.log).toHaveBeenCalledWith(
        "Successfully deleted 5 old DOCX session folders."
      );
    });

    it("should handle DOCX cleanup with partial success", async () => {
      mockExistsSync.mockReturnValue(false);
      mockCleanOldDocxSessionFiles.mockResolvedValue({
        success: true,
        deletedCount: 3,
        error: "Some files could not be deleted",
      });

      await cleanOldLogs();

      expect(consoleSpy.log).toHaveBeenCalledWith(
        "Successfully deleted 3 old DOCX session folders."
      );
      expect(consoleSpy.warn).toHaveBeenCalledWith(
        "Partial success in DOCX cleanup:",
        "Some files could not be deleted"
      );
    });

    it("should handle DOCX cleanup failure", async () => {
      mockExistsSync.mockReturnValue(false);
      mockCleanOldDocxSessionFiles.mockResolvedValue({
        success: false,
        deletedCount: 0,
        error: "Access denied",
      });

      await cleanOldLogs();

      expect(consoleSpy.error).toHaveBeenCalledWith(
        "Failed to clean old DOCX session files:",
        "Access denied"
      );
    });

    it("should handle DOCX cleanup throwing an error", async () => {
      mockExistsSync.mockReturnValue(false);
      mockCleanOldDocxSessionFiles.mockRejectedValue(
        new Error("Unexpected error")
      );

      await cleanOldLogs();

      expect(consoleSpy.error).toHaveBeenCalledWith(
        "Error during general old log/file cleanup"
      );
    });
  });

  describe("Error resilience and edge cases", () => {
    it("should handle multiple simultaneous cleanup operations", async () => {
      mockExistsSync.mockReturnValue(false);
      mockCleanOldDocxSessionFiles.mockResolvedValue({
        success: true,
        deletedCount: 0,
        error: null,
      });

      // Run multiple cleanup operations concurrently
      const promises = Array(5)
        .fill(null)
        .map(() => cleanOldLogs());

      await Promise.allSettled(promises);

      expect(mockCleanOldDocxSessionFiles).toHaveBeenCalledTimes(5);
    });

    it("should handle very large log files", async () => {
      // Create a large log file content (1000 entries)
      const largeLogs = Array(1000)
        .fill(null)
        .map((_, index) => ({
          dateAdded: new Date(
            Date.now() - index * 24 * 60 * 60 * 1000
          ).toISOString(),
          filename: `file-${index}.pdf`,
        }));

      const largeContent = largeLogs
        .map((log) => JSON.stringify(log))
        .join("\n");

      // Mock for all access calls
      mockFsPromises.access
        .mockRejectedValueOnce(new Error("Not found")) // logs dir
        .mockRejectedValueOnce(new Error("Not found")) // document-builder dir
        .mockResolvedValueOnce(undefined); // upload logs file exists

      mockExistsSync.mockReturnValue(false);
      mockFsPromises.readFile.mockResolvedValue(largeContent);
      mockFsPromises.writeFile.mockResolvedValue();
      mockCleanOldDocxSessionFiles.mockResolvedValue({
        success: true,
        deletedCount: 0,
        error: null,
      });

      await cleanOldLogs();

      expect(mockFsPromises.writeFile).toHaveBeenCalled();
    });

    it("should handle files with future timestamps", async () => {
      const futureTime = Date.now() + 24 * 60 * 60 * 1000; // 1 day in future

      mockExistsSync.mockReturnValue(true);
      mockFsPromises.access.mockResolvedValue();
      mockFsPromises.readdir.mockResolvedValue(["future.log"] as any);
      mockFsPromises.stat.mockResolvedValue({
        isFile: () => true,
        mtimeMs: futureTime,
      } as any);

      await cleanOldLogs();

      // Should not delete files with future timestamps
      expect(mockFsPromises.unlink).not.toHaveBeenCalled();
    });

    it("should handle symbolic links and special files", async () => {
      mockExistsSync.mockReturnValue(true);
      mockFsPromises.access.mockResolvedValue();
      mockFsPromises.readdir.mockResolvedValue([
        "symlink.log",
        "special",
      ] as any);

      mockFsPromises.stat
        .mockResolvedValueOnce({ isFile: () => false, mtimeMs: 0 } as any) // Symlink
        .mockResolvedValueOnce({ isFile: () => false, mtimeMs: 0 } as any); // Special file

      await cleanOldLogs();

      expect(mockFsPromises.unlink).not.toHaveBeenCalled();
    });

    it("should handle permission errors gracefully", async () => {
      mockExistsSync.mockReturnValue(true);
      mockFsPromises.access
        .mockResolvedValueOnce() // Initial directory access
        .mockRejectedValueOnce(new Error("Permission denied")); // File access

      mockFsPromises.readdir.mockResolvedValue(["restricted.log"] as any);
      mockFsPromises.stat.mockResolvedValue({
        isFile: () => true,
        mtimeMs: Date.now() - 3 * 24 * 60 * 60 * 1000,
      } as any);

      await cleanOldLogs();

      expect(consoleSpy.error).toHaveBeenCalledWith(
        "Error processing log file restricted.log"
      );
    });

    it("should handle concurrent file access", async () => {
      const oldTime = Date.now() - 3 * 24 * 60 * 60 * 1000;

      mockExistsSync.mockReturnValue(true);
      mockFsPromises.access.mockResolvedValue();
      mockFsPromises.readdir.mockResolvedValue(["concurrent.log"] as any);
      mockFsPromises.stat.mockResolvedValue({
        isFile: () => true,
        mtimeMs: oldTime,
      } as any);

      // Simulate file being deleted by another process
      mockFsPromises.access.mockResolvedValueOnce(); // Directory access
      mockFsPromises.access.mockRejectedValueOnce(new Error("File not found")); // File access fails

      await cleanOldLogs();

      expect(consoleSpy.error).toHaveBeenCalledWith(
        "Error processing log file concurrent.log"
      );
    });
  });

  describe("Performance and memory testing", () => {
    it("should handle memory pressure during large cleanups", async () => {
      // Simulate memory pressure with large file paths
      const largePaths = Array(100)
        .fill(null)
        .map(
          (_, index) =>
            "very-long-filename-that-simulates-memory-pressure-" +
            "x".repeat(100) +
            `-${index}.log`
        );

      mockExistsSync.mockReturnValue(true);
      mockFsPromises.access.mockResolvedValue();
      // Mock readdir to return largePaths for logs directory, empty array for document-builder
      mockFsPromises.readdir
        .mockResolvedValueOnce(largePaths as any) // logs directory
        .mockResolvedValueOnce([] as any); // document-builder directory

      // All files are old
      mockFsPromises.stat.mockResolvedValue({
        isFile: () => true,
        mtimeMs: Date.now() - 3 * 24 * 60 * 60 * 1000,
      } as any);

      mockFsPromises.unlink.mockResolvedValue();

      await cleanOldLogs();

      expect(mockFsPromises.unlink).toHaveBeenCalledTimes(largePaths.length);
    });

    it("should handle rapid successive cleanup calls", async () => {
      mockExistsSync.mockReturnValue(false);
      mockCleanOldDocxSessionFiles.mockResolvedValue({
        success: true,
        deletedCount: 0,
        error: null,
      });

      // Rapid successive calls
      const rapidCalls = Array(20)
        .fill(null)
        .map(() => cleanOldLogs());

      const results = await Promise.allSettled(rapidCalls);

      // All should complete successfully
      results.forEach((result) => {
        expect(result.status).toBe("fulfilled");
      });
    });
  });
});
