import { OpenAIClient, AzureKeyCredential } from "@azure/openai";
import Provider from "./ai-provider";
import InheritMultiple from "./helpers/classes";
import UnTooled from "./helpers/untooled";
import type { ChatMessage, AgentFunction } from "../../../../types/agents";
import type { ChatRequestMessage, ChatCompletions } from "@azure/openai";

interface AzureOpenAiProviderConfig {
  model?: string;
  endpoint?: string;
  apiKey?: string;
}

interface CompletionResult {
  result: string | null;
  functionCall?: {
    name: string;
    arguments: Record<string, unknown>;
  };
  cost: number;
}

interface FunctionCallResult {
  toolCall: {
    name: string;
    arguments: Record<string, unknown>;
  } | null;
  text: string | null;
}

/**
 * The agent provider for the Azure OpenAI API.
 */
class AzureOpenAiProvider extends InheritMultiple([Provider, UnTooled]) {
  model: string;
  _client: OpenAIClient;
  verbose: boolean;
  // Inherited methods from Provider
  providerLog!: (text: string, ...args: unknown[]) => void;

  // Inherited methods and properties from UnTooled
  deduplicator!: UnTooled["deduplicator"];
  cleanMsgs!: (messages: ChatMessage[]) => ChatMessage[];
  functionCall!: (
    messages: ChatMessage[],
    functions: AgentFunction[],
    chatCb: ((params: { messages: ChatMessage[] }) => Promise<string>) | null
  ) => Promise<FunctionCallResult | null>;

  constructor(config: AzureOpenAiProviderConfig = {}) {
    super();
    const {
      model = process.env.OPEN_AI_MODEL_PREF ?? "gpt-3.5-turbo",
      endpoint = process.env.AZURE_OPENAI_ENDPOINT,
      apiKey = process.env.AZURE_OPENAI_KEY,
    } = config;

    if (!endpoint || !apiKey) {
      throw new Error("Azure OpenAI endpoint and API key are required");
    }

    const client = new OpenAIClient(endpoint, new AzureKeyCredential(apiKey));

    this._client = client;
    this.model = model;
    this.verbose = true;
  }

  get client(): OpenAIClient {
    return this._client;
  }

  async handleFunctionCallChat({
    messages = [],
  }: {
    messages: ChatMessage[];
  }): Promise<string> {
    return await this.client
      .getChatCompletions(this.model, messages as ChatRequestMessage[], {
        temperature: 0,
      })
      .then((result: ChatCompletions) => {
        if (!Object.prototype.hasOwnProperty.call(result, "choices"))
          throw new Error("Azure OpenAI chat: No results!");
        if ((result?.choices.length ?? 0) === 0)
          throw new Error("Azure OpenAI chat: No results length!");
        return result?.choices[0].message?.content || "";
      })
      .catch((_: Error) => {
        return "";
      });
  }

  /**
   * Create a completion based on the received messages.
   *
   * @param messages A list of messages to send to the API.
   * @param functions Available functions for tool calling
   * @returns The completion.
   */
  async complete(
    messages: ChatMessage[],
    functions: AgentFunction[] | null = null
  ): Promise<CompletionResult> {
    try {
      let completion: { content?: string } | undefined;

      if (functions && (functions?.length ?? 0) > 0) {
        const result = await this.functionCall(
          messages,
          functions,
          this.handleFunctionCallChat?.bind(this)
        );

        if (
          result &&
          result.toolCall !== null &&
          result.toolCall !== undefined
        ) {
          this.providerLog(
            `Valid tool call found - running ${result?.toolCall.name}.`
          );
          this.deduplicator.trackRun(
            result.toolCall!.name,
            result.toolCall!.arguments
          );
          return {
            result: null,
            functionCall: {
              name: result.toolCall!.name,
              arguments: result.toolCall!.arguments,
            },
            cost: 0,
          };
        }
        completion = { content: (result?.text ?? false) || undefined };
      }

      if (!completion?.content) {
        this.providerLog(
          "Will assume chat completion without tool call inputs."
        );
        const response = await this.client.getChatCompletions(
          this.model,
          this.cleanMsgs(messages) as ChatRequestMessage[],
          {
            temperature: 0.7,
          }
        );
        completion = {
          content: response?.choices[0]?.message?.content ?? "",
        };
      }

      // The UnTooled class inherited Deduplicator is mostly useful to prevent the agent
      // from calling the exact same function over and over in a loop within a single chat exchange
      // _but_ we should enable it to call previously used tools in a new chat interaction.
      this.deduplicator.reset("runs");
      return {
        result: (completion?.content ?? false) || "",
        cost: 0,
      };
    } catch (error) {
      console.error("Azure OpenAI completion failed:", error);
      throw new Error(
        `Azure OpenAI completion failed: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  /**
   * Get the cost of the completion.
   * Stubbed since Azure OpenAI has no public cost basis.
   *
   * @param _usage The completion to get the cost for.
   * @returns The cost of the completion.
   */
  getCost(_usage: unknown): number {
    return 0;
  }
}

export default AzureOpenAiProvider;
