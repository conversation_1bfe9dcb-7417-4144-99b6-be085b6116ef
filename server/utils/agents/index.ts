import AIbitat from "./aibitat";
import AgentPlugins from "./aibitat/plugins";
import { WorkspaceAgentInvocation } from "../../models/workspaceAgentInvocation";
import { WorkspaceChats } from "../../models/workspaceChats";
import { safeJsonParse } from "../http";
import { USER_AGENT, WORKSPACE_AGENT } from "./defaults";
import ImportedPlugin from "./imported";
import MC<PERSON>ompatibilityLayer from "../MCP";
import type { workspace_agent_invocations, workspaces } from "@prisma/client";
import type { AgentProviderType, ChatMessage } from "../../types/agents";

interface AgentHandlerProps {
  uuid: string;
}

interface NoProviderModelDefault {
  azure: string;
  lmstudio: string;
  textgenwebui: null;
  "generic-openai": string;
  bedrock: string;
}

interface AgentPluginConfig {
  plugin?:
    | Array<{
        name?: string;
        plugin: (opts: Record<string, unknown>) => PluginInstance;
        startupConfig?: {
          params?: Record<string, { required?: boolean; default?: unknown }>;
        };
      }>
    | ((opts: Record<string, unknown>) => PluginInstance);
  startupConfig?: {
    params?: Record<string, { required?: boolean; default?: unknown }>;
  };
  [key: string]: unknown;
}

interface PluginInstance {
  setup: (aibitat: AIbitat) => void;
  [key: string]: unknown;
}

// Type for workspace agent invocation with workspace data
type WorkspaceAgentInvocationWithWorkspace = workspace_agent_invocations & {
  workspaces: workspaces;
};

class AgentHandler {
  private invocationUUID: string;
  private funcsToLoad: string[] = [];
  noProviderModelDefault: NoProviderModelDefault = {
    azure: "OPEN_AI_MODEL_PREF",
    lmstudio: "LMSTUDIO_MODEL_PREF",
    textgenwebui: null, // does not even use `model` in API req
    "generic-openai": "GENERIC_OPEN_AI_MODEL_PREF",
    bedrock: "AWS_BEDROCK_LLM_MODEL_PREFERENCE",
  };
  invocation: WorkspaceAgentInvocationWithWorkspace | null = null;
  aibitat: AIbitat | null = null;
  channel: string | null = null;
  provider: AgentProviderType | null = null;
  model: string | null = null;

  constructor({ uuid }: AgentHandlerProps) {
    this.invocationUUID = uuid;
  }

  log(text: string, ...args: unknown[]): void {
    console.log(`\x1b[36m[AgentHandler]\x1b[0m ${text}`, ...args);
  }

  closeAlert(): void {
    this?.log(`End ${this.invocationUUID}::${this?.provider}:${this?.model}`);
  }

  async chatHistory(limit: number = 10): Promise<ChatMessage[]> {
    try {
      const rawHistory = (
        (await WorkspaceChats?.where(
          {
            workspaceId: this?.invocation?.workspace_id || 0,
            user_id: this?.invocation?.user_id || null,
            thread_id: this?.invocation?.thread_id || null,
            api_session_id: null,
            include: true,
          },
          limit,
          { id: "desc" }
        )) || []
      ).reverse();

      const agentHistory: ChatMessage[] = [];
      rawHistory?.forEach((chatLog) => {
        agentHistory?.push(
          {
            from: USER_AGENT?.name,
            to: WORKSPACE_AGENT?.name,
            content: chatLog?.prompt,
            role: "user" as const,
            timestamp: new Date(chatLog?.createdAt || Date.now()),
          },
          {
            from: WORKSPACE_AGENT?.name,
            to: USER_AGENT?.name,
            content:
              (safeJsonParse(chatLog?.response) as { text?: string })?.text ||
              "",
            role: "assistant" as const,
            timestamp: new Date(chatLog?.createdAt || Date.now()),
          }
        );
      });
      return agentHistory;
    } catch (e) {
      this?.log(
        "Error loading chat history",
        e instanceof Error ? e.message : String(e)
      );
      return [];
    }
  }

  checkSetup(): void {
    switch (this?.provider) {
      case "openai":
        if (!process.env.OPEN_AI_KEY)
          throw new Error("OpenAI API key must be provided to use agents.");
        break;
      case "anthropic":
        if (!process.env.ANTHROPIC_API_KEY)
          throw new Error("Anthropic API key must be provided to use agents.");
        break;
      case "lmstudio":
        if (!process.env.LMSTUDIO_BASE_PATH)
          throw new Error("LMStudio base path must be provided to use agents.");
        break;
      case "ollama":
        if (!process.env.OLLAMA_BASE_PATH)
          throw new Error("Ollama base path must be provided to use agents.");
        break;
      case "groq":
        if (!process.env.GROQ_API_KEY)
          throw new Error("Groq API key must be provided to use agents.");
        break;
      case "togetherai":
        if (!process.env.TOGETHER_AI_API_KEY)
          throw new Error("TogetherAI API key must be provided to use agents.");
        break;
      case "azure":
        if (!process.env.AZURE_OPENAI_ENDPOINT || !process.env.AZURE_OPENAI_KEY)
          throw new Error(
            "Azure OpenAI API endpoint and key must be provided to use agents."
          );
        break;
      case "koboldcpp":
        if (!process.env.KOBOLD_CPP_BASE_PATH)
          throw new Error(
            "KoboldCPP must have a valid base path to use for the api."
          );
        break;
      case "localai":
        if (!process.env.LOCAL_AI_BASE_PATH)
          throw new Error(
            "LocalAI must have a valid base path to use for the api."
          );
        break;
      case "gemini":
        if (!process.env.GEMINI_API_KEY)
          throw new Error("Gemini API key must be provided to use agents.");
        break;
      case "openrouter":
        if (!process.env.OPENROUTER_API_KEY)
          throw new Error("OpenRouter API key must be provided to use agents.");
        break;
      case "mistral":
        if (!process.env.MISTRAL_API_KEY)
          throw new Error("Mistral API key must be provided to use agents.");
        break;
      case "generic-openai":
        if (!process.env.GENERIC_OPEN_AI_BASE_PATH)
          throw new Error("API base path must be provided to use agents.");
        break;
      case "perplexity":
        if (!process.env.PERPLEXITY_API_KEY)
          throw new Error("Perplexity API key must be provided to use agents.");
        break;
      case "textgenwebui":
        if (!process.env.TEXT_GEN_WEB_UI_BASE_PATH)
          throw new Error(
            "TextWebGenUI API base path must be provided to use agents."
          );
        break;
      case "bedrock":
        if (
          !process.env.AWS_BEDROCK_LLM_ACCESS_KEY_ID ||
          !process.env.AWS_BEDROCK_LLM_ACCESS_KEY ||
          !process.env.AWS_BEDROCK_LLM_REGION
        )
          throw new Error(
            "AWS Bedrock Access Keys and region must be provided to use agents."
          );
        break;
      case "fireworksai":
        if (!process.env.FIREWORKS_AI_LLM_API_KEY)
          throw new Error(
            "FireworksAI API Key must be provided to use agents."
          );
        break;
      case "deepseek":
        if (!process.env.DEEPSEEK_API_KEY)
          throw new Error("DeepSeek API Key must be provided to use agents.");
        break;
      case "xai":
        if (!process.env.XAI_LLM_API_KEY)
          throw new Error("xAI API Key must be provided to use agents.");
        break;

      default:
        throw new Error(
          "No workspace agent provider set. Please set your agent provider in the workspace's settings"
        );
    }
  }

  providerDefault(provider: AgentProviderType | null = this?.provider): string {
    switch (provider) {
      case "openai":
        return "chatgpt-4o-latest";
      case "anthropic":
        return "claude-3-sonnet-20240229";
      case "lmstudio":
        return "server-default";
      case "ollama":
        return "llama3:latest";
      case "groq":
        return "llama3-70b-8192";
      case "togetherai":
        return "mistralai/Mixtral-8x7B-Instruct-v0?.1";
      case "azure":
        return "gpt-3?.5-turbo";
      case "koboldcpp":
        return "koboldcpp-default";
      case "gemini":
        return "gemini-pro";
      case "localai":
        return "localai-default";
      case "openrouter":
        return "openrouter/auto";
      case "mistral":
        return "mistral-medium";
      case "generic-openai":
        return "generic-openai-default";
      case "perplexity":
        return "sonar-small-online";
      case "textgenwebui":
        return "textgenwebui-default";
      case "bedrock":
        return "bedrock-default";
      case "fireworksai":
        return "fireworksai-default";
      case "deepseek":
        return "deepseek-chat";
      case "xai":
        return process.env.XAI_LLM_MODEL_PREF ?? "grok-beta";
      default:
        return "unknown";
    }
  }

  private getFallbackProvider(): { provider: string; model: string } | null {
    // First, fallback to the workspace chat provider and model if they exist
    if (
      this?.invocation?.workspaces?.chatProvider &&
      this?.invocation?.workspaces?.chatModel
    ) {
      return {
        provider: this?.invocation.workspaces?.chatProvider,
        model: this?.invocation.workspaces?.chatModel,
      };
    }
    // If workspace does not have chat provider and model fallback
    // to system provider and try to load provider default model
    const systemProvider = process.env.LLM_PROVIDER as AgentProviderType;
    const systemModel = this?.providerDefault(systemProvider);
    if (systemProvider && systemModel) {
      return {
        provider: systemProvider,
        model: systemModel,
      };
    }
    return null;
  }

  /**
   * Finds or assumes the model preference value to use for API calls.
   * If multi-model loading is supported, we use their agent model selection of the workspace
   * If not supported, we attempt to fallback to the system provider value for the LLM preference
   * and if that fails - we assume a reasonable base model to exist.
   * @returns {string} the model preference value to use in API calls
   */
  private fetchModel(): string {
    // Provider was not explicitly set for workspace, so we are going to run our fallback logic
    // that will set a provider and model for us to use.
    if (!this?.provider) {
      const fallback = this.getFallbackProvider();
      if (!fallback) throw new Error("No valid provider found for the agent.");
      this.provider = fallback?.provider as AgentProviderType; // re-set the provider to the fallback provider so it is not null.
      return fallback?.model; // set its defined model based on fallback logic.
    }
    // The provider was explicitly set, so check if the workspace has an agent model set.
    if (this?.invocation?.workspaces?.agentModel) {
      return this?.invocation.workspaces?.agentModel;
    }

    // If the provider we are using is not supported or does not support multi-model loading
    // then we use the default model for the provider.
    if (
      !Object.prototype.hasOwnProperty.call(
        this?.noProviderModelDefault,
        this?.provider
      )
    ) {
      return this?.providerDefault();
    }
    // Load the model from the system environment variable for providers with no multi-model loading.
    const sysModelKey =
      this?.noProviderModelDefault[
        this?.provider as keyof NoProviderModelDefault
      ];
    if (sysModelKey) return process.env[sysModelKey] ?? this?.providerDefault();

    // Otherwise, we have no model to use - so guess a default model to use.
    return this?.providerDefault();
  }

  private providerSetupAndCheck(): void {
    this.provider = (this?.invocation?.workspaces?.agentProvider ??
      null) as AgentProviderType | null; // set provider to workspace agent provider if it exists
    this.model = this.fetchModel();

    if (!this?.provider)
      throw new Error("No valid provider found for the agent.");

    this?.log(`Start ${this.invocationUUID}::${this?.provider}:${this?.model}`);
    this?.checkSetup();
  }

  async validInvocation(): Promise<void> {
    const invocation = await WorkspaceAgentInvocation?.getWithWorkspace({
      uuid: String(this.invocationUUID),
    });
    if (invocation?.closed)
      throw new Error("This agent invocation is already closed");
    this.invocation = invocation ?? null;
  }

  parseCallOptions(
    args: Record<string, unknown>,
    config: Record<string, { required?: boolean; default?: unknown }> = {},
    pluginName?: string
  ): Record<string, unknown> {
    const callOpts: Record<string, unknown> = {};
    for (const [param, definition] of Object.entries(config)) {
      const def = definition;
      if (
        def?.required &&
        (!Object.prototype.hasOwnProperty.call(args, param) ||
          args[param] === null)
      ) {
        this?.log(
          `'${param}' required parameter for '${pluginName}' plugin is missing. Plugin may not function or crash agent.`
        );
        continue;
      }
      callOpts[param] = Object.prototype.hasOwnProperty.call(args, param)
        ? args[param]
        : def?.default || null;
    }
    return callOpts;
  }

  async attachPlugins(args: Record<string, unknown>): Promise<void> {
    for (const name of this.funcsToLoad) {
      // Load child plugin
      if (name?.includes("#")) {
        const [parent, childPluginName] = name?.split("#") || [];
        if (!Object.prototype.hasOwnProperty.call(AgentPlugins, parent)) {
          this?.log(
            `${parent} is not a valid plugin. Skipping inclusion to agent cluster.`
          );
          continue;
        }

        const parentPlugin = AgentPlugins[parent] as AgentPluginConfig;
        const childPlugin = Array.isArray(parentPlugin.plugin)
          ? parentPlugin.plugin.find(
              (child) => (child?.name ?? "") === childPluginName
            )
          : undefined;
        if (!childPlugin) {
          this?.log(
            `${parent} does not have child plugin named ${childPluginName}. Skipping inclusion to agent cluster.`
          );
          continue;
        }

        const callOpts = this?.parseCallOptions(
          args,
          childPlugin?.startupConfig?.params || {},
          name
        );
        if (this.aibitat && typeof childPlugin?.plugin === "function") {
          const pluginResult = childPlugin.plugin(callOpts);
          if (
            pluginResult &&
            typeof pluginResult === "object" &&
            "setup" in pluginResult &&
            typeof pluginResult.setup === "function"
          ) {
            this.aibitat.use(pluginResult);
          }
        }
        this?.log(
          `Attached ${parent}:${childPluginName} plugin to Agent cluster`
        );
        continue;
      }

      // Load MCP plugin. This is marked by `@@mcp_` in the array of functions to load.
      // All sub-tools are loaded here and are denoted by `pluginName:toolName` as their identifier.
      // This will replace the parent MCP server plugin with the sub-tools as child plugins so they
      // can be called directly by the agent when invoked.
      // Since to get to this point, the `activeMCPServers` method has already been called, we can
      // safely assume that the MCP server is running and the tools are available/loaded.
      if (name?.startsWith("@@mcp_")) {
        const mcpPluginName = name?.replace("@@mcp_", "");
        const plugins =
          await new MCPCompatibilityLayer().convertServerToolsToPlugins(
            mcpPluginName,
            this.aibitat
          );
        if (!plugins) {
          this?.log(
            `MCP ${mcpPluginName} not found in MCP server config. Skipping inclusion to agent cluster.`
          );
          continue;
        }

        // Remove the old function from the agent functions directly
        // and push the new ones onto the end of the array so that they are loaded properly.
        const agentConfig = this.aibitat?.agents?.get("@agent");
        if (agentConfig && agentConfig.functions) {
          const filteredFunctions = agentConfig.functions.filter(
            (f) => f !== name
          );
          for (const plugin of plugins) filteredFunctions.push(plugin?.name);
          agentConfig.functions = filteredFunctions;
        }

        plugins?.forEach((plugin) => {
          if (this.aibitat && typeof plugin?.plugin === "function") {
            const pluginInstance = plugin.plugin();
            if (pluginInstance && typeof pluginInstance.setup === "function") {
              this.aibitat.use(pluginInstance);
            }
          }
          this?.log(
            `Attached MCP::${plugin?.toolName} MCP tool to Agent cluster`
          );
        });
        continue;
      }

      // Load imported plugin. This is marked by `@@` in the array of functions to load.
      // and is the @@hubID of the plugin.
      if (name?.startsWith("@@")) {
        const hubId = name?.replace("@@", "");
        const valid = ImportedPlugin?.validateImportedPluginHandler(hubId);
        if (!valid) {
          this?.log(
            `Imported plugin by hubId ${hubId} not found in plugin directory. Skipping inclusion to agent cluster.`
          );
          continue;
        }
        const plugin = ImportedPlugin?.loadPluginByHubId(hubId);
        const callOpts = plugin?.parseCallOptions();
        if (this.aibitat && typeof plugin?.plugin === "function") {
          const pluginInstance = plugin.plugin(callOpts);
          if (pluginInstance && typeof pluginInstance.setup === "function") {
            this.aibitat.use(pluginInstance);
          }
        }
        this?.log(
          `Attached ${plugin?.name} (${hubId}) imported plugin to Agent cluster`
        );
        continue;
      }

      // Load single-stage plugin.
      if (!Object.prototype.hasOwnProperty.call(AgentPlugins, name)) {
        this?.log(
          `${name} is not a valid plugin. Skipping inclusion to agent cluster.`
        );
        continue;
      }

      const callOpts = this?.parseCallOptions(
        args,
        (AgentPlugins[name] as AgentPluginConfig).startupConfig?.params || {}
      );
      const AIbitatPlugin = AgentPlugins[name] as AgentPluginConfig;
      if (this.aibitat && typeof AIbitatPlugin?.plugin === "function") {
        const pluginInstance = AIbitatPlugin.plugin(callOpts);
        if (pluginInstance && typeof pluginInstance.setup === "function") {
          this.aibitat.use(pluginInstance);
        }
      }
      this?.log(`Attached ${name} plugin to Agent cluster`);
    }
  }

  async loadAgents(): Promise<void> {
    // Default User agent and workspace agent
    this?.log(`Attaching user and default agent to Agent cluster.`);
    if (this.aibitat) {
      this.aibitat.agent(USER_AGENT?.name, await USER_AGENT?.getDefinition());
    }
    if (this.aibitat) {
      this.aibitat.agent(
        WORKSPACE_AGENT?.name,
        await WORKSPACE_AGENT?.getDefinition(this?.provider)
      );
    }

    const userFunctions = (await USER_AGENT?.getDefinition())?.functions || [];
    const workspaceFunctions =
      (await WORKSPACE_AGENT?.getDefinition(this?.provider))?.functions || [];

    this.funcsToLoad = [
      ...userFunctions.map((f) => (typeof f === "string" ? f : f.name)),
      ...workspaceFunctions.map((f) => (typeof f === "string" ? f : f.name)),
    ];
  }

  async init(): Promise<this> {
    await this.validInvocation();
    this.providerSetupAndCheck();
    return this;
  }

  async createAIbitat(args: { socket?: unknown } = {}): Promise<void> {
    this.aibitat = new AIbitat({
      provider: this?.provider ?? "openai",
      model: this?.model ?? "chatgpt-4o-latest",
      chats: (await this.chatHistory(20)) as ChatMessage[],
      handlerProps: {
        invocation: this?.invocation,
        log: this?.log,
      },
    });

    // Attach standard websocket plugin for frontend communication.
    this?.log(
      `Attached ${AgentPlugins?.websocket.name} plugin to Agent cluster`
    );
    const websocketPlugin = AgentPlugins?.websocket as AgentPluginConfig;
    if (
      this.aibitat &&
      websocketPlugin &&
      typeof websocketPlugin?.plugin === "function"
    ) {
      const pluginInstance = websocketPlugin.plugin({
        socket: args?.socket,
        muteUserReply: true,
        introspection: true,
      });
      if (pluginInstance && typeof pluginInstance.setup === "function") {
        this.aibitat.use(pluginInstance);
      }
    }

    // Attach standard chat-history plugin for message storage.
    this?.log(
      `Attached ${AgentPlugins?.chatHistory.name} plugin to Agent cluster`
    );
    const chatHistoryPlugin = AgentPlugins?.chatHistory as AgentPluginConfig;
    if (
      this.aibitat &&
      chatHistoryPlugin &&
      typeof chatHistoryPlugin?.plugin === "function"
    ) {
      const pluginInstance = chatHistoryPlugin.plugin({});
      if (pluginInstance && typeof pluginInstance.setup === "function") {
        this.aibitat.use(pluginInstance);
      }
    }

    // Load required agents (Default + custom)
    await this.loadAgents();

    // Attach all required plugins for functions to operate.
    await this?.attachPlugins(args);
  }

  startAgentCluster() {
    if (!this.aibitat) {
      throw new Error("AIbitat not initialized");
    }
    if (!this.invocation) {
      throw new Error("Invocation not available");
    }
    return this.aibitat.start({
      from: USER_AGENT?.name,
      to: this?.channel ?? WORKSPACE_AGENT?.name,
      content: this.invocation.prompt,
    });
  }
}

export { AgentHandler };
