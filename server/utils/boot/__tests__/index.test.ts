import fs from "fs";
import https from "https";
import { Application } from "express";
import { bootSSL, bootHTTP } from "../index";
import { Telemetry } from "../../../models/telemetry";
import { BackgroundService } from "../../BackgroundWorkers";
import { EncryptionManager } from "../../EncryptionManager";
import { CommunicationKey } from "../../comKey";
import setupTelemetry from "../../telemetry";
import { initializeLanguage } from "../../i18n";

// Add Node.js types
/// <reference types="node" />

// Mock all dependencies
jest.mock("fs");
jest.mock("https");
jest.mock("@mintplex-labs/express-ws", () => ({
  default: jest.fn(() => ({})),
}));
jest.mock("../../../models/telemetry");
jest.mock("../../BackgroundWorkers");
jest.mock("../../EncryptionManager");
jest.mock("../../comKey");
jest.mock("../../telemetry");
jest.mock("../../i18n");

describe("Boot Process", () => {
  let mockApp: Partial<Application>;
  let mockServer: any;
  let originalEnv: typeof process.env;

  beforeEach(() => {
    // Save original environment
    originalEnv = { ...process.env };

    // Clear all mocks
    jest.clearAllMocks();

    // Keep test environment to suppress console logging during tests
    process.env.NODE_ENV = "test";

    // Mock Express app
    mockApp = {
      listen: jest.fn(),
    };

    // Mock server
    mockServer = {
      listen: jest.fn(),
      on: jest.fn().mockReturnThis(),
    };

    // Setup default implementations
    (mockApp.listen as jest.Mock).mockReturnValue(mockServer);
    (mockServer.listen as jest.Mock).mockImplementation((port, callback) => {
      if (callback) callback();
      return mockServer;
    });

    // Mock telemetry setup
    (setupTelemetry as jest.Mock).mockResolvedValue(undefined);
    (initializeLanguage as jest.Mock).mockResolvedValue(undefined);

    // Mock constructors
    (CommunicationKey as jest.Mock).mockImplementation(() => ({}));
    (EncryptionManager as jest.Mock).mockImplementation(() => ({}));
    (BackgroundService as unknown as jest.Mock).mockImplementation(() => ({
      boot: jest.fn(),
    }));
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
  });

  describe("bootSSL", () => {
    beforeEach(() => {
      // Set up SSL environment variables
      process.env.ENABLE_HTTPS = "true";
      process.env.HTTPS_KEY_PATH = "/path/to/key.pem";
      process.env.HTTPS_CERT_PATH = "/path/to/cert.pem";

      // Mock file system reads
      (fs.readFileSync as jest.Mock)
        .mockReturnValueOnce("mock-private-key")
        .mockReturnValueOnce("mock-certificate");

      // Mock HTTPS server creation
      (https.createServer as jest.Mock).mockReturnValue(mockServer);
    });

    it("should boot server in SSL mode successfully", async () => {
      // Set to development for this specific test
      process.env.NODE_ENV = "development";
      const consoleSpy = jest.spyOn(console, "log").mockImplementation();

      const result = bootSSL(mockApp as Application, 3001);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining("[SSL BOOT ENABLED]")
      );

      expect(fs.readFileSync).toHaveBeenCalledWith("/path/to/key.pem");
      expect(fs.readFileSync).toHaveBeenCalledWith("/path/to/cert.pem");

      expect(https.createServer).toHaveBeenCalledWith(
        {
          key: "mock-private-key",
          cert: "mock-certificate",
        },
        mockApp
      );

      expect(mockServer.listen).toHaveBeenCalledWith(
        3001,
        expect.any(Function)
      );
      expect(result).toEqual({ app: mockApp, server: mockServer });

      consoleSpy.mockRestore();
    });

    it("should initialize all services on successful boot", async () => {
      // Set to development for this specific test
      process.env.NODE_ENV = "development";
      bootSSL(mockApp as Application, 3001);

      // Trigger the listen callback
      const listenCallback = (mockServer.listen as jest.Mock).mock.calls[0][1];
      await listenCallback();

      expect(setupTelemetry).toHaveBeenCalled();
      expect(initializeLanguage).toHaveBeenCalled();
      expect(CommunicationKey).toHaveBeenCalledWith(true);
      expect(EncryptionManager).toHaveBeenCalled();
      expect(BackgroundService).toHaveBeenCalled();

      const backgroundService = (BackgroundService as unknown as jest.Mock).mock
        .results[0].value;
      expect(backgroundService.boot).toHaveBeenCalled();
    });

    it("should handle boot service initialization errors", async () => {
      // Set to development for this specific test
      process.env.NODE_ENV = "development";
      const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();
      (setupTelemetry as jest.Mock).mockRejectedValue(
        new Error("Telemetry error")
      );

      bootSSL(mockApp as Application, 3001);

      const listenCallback = (mockServer.listen as jest.Mock).mock.calls[0][1];
      await listenCallback();

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        "FAILED TO BOOT SERVER",
        expect.any(Error)
      );

      consoleErrorSpy.mockRestore();
    });

    it("should fall back to HTTP when SSL certificate loading fails", () => {
      // Set to development for this specific test
      process.env.NODE_ENV = "development";
      const consoleLogSpy = jest
        .spyOn(console, "log")
        .mockImplementation(() => {});
      const consoleErrorSpy = jest
        .spyOn(console, "error")
        .mockImplementation(() => {});

      // Clear the existing mock and set up to throw an error for this test
      (fs.readFileSync as jest.Mock).mockReset();
      (fs.readFileSync as jest.Mock).mockImplementation(() => {
        throw new Error("File not found");
      });

      const result = bootSSL(mockApp as Application, 3001);

      // Check that console.log was called for SSL boot message
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining("[SSL BOOT ENABLED]")
      );

      // Verify console.error was called with SSL BOOT FAILED message
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining("[SSL BOOT FAILED]"),
        expect.any(Object)
      );

      // The key behavior we're testing is the fallback to HTTP
      // When SSL fails, bootSSL catches the error and calls bootHTTP
      expect(result).toBeDefined();
      expect(result.app).toBe(mockApp);

      consoleLogSpy.mockRestore();
      consoleErrorSpy.mockRestore();
    });

    it("should handle server listen errors", () => {
      // Set to development for this specific test
      process.env.NODE_ENV = "development";
      const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();
      const mockError = new Error("Port already in use");

      (mockServer.on as jest.Mock).mockImplementation((event, handler) => {
        if (event === "error") {
          handler(mockError);
        }
        return mockServer;
      });

      bootSSL(mockApp as Application, 3001);

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining("[SSL BOOT FAILED]"),
        mockError
      );

      consoleErrorSpy.mockRestore();
    });

    it("should skip server listen in test environment", () => {
      process.env.NODE_ENV = "test";
      const consoleSpy = jest.spyOn(console, "log").mockImplementation();

      bootSSL(mockApp as Application, 3001);

      expect(mockServer.listen).not.toHaveBeenCalled();
      expect(consoleSpy).toHaveBeenCalledWith(
        "[TEST ENV] SSL server .listen() and boot services skipped."
      );

      consoleSpy.mockRestore();
    });

    it("should use custom port when provided", () => {
      // Set to development for this specific test
      process.env.NODE_ENV = "development";
      bootSSL(mockApp as Application, 8443);

      expect(mockServer.listen).toHaveBeenCalledWith(
        8443,
        expect.any(Function)
      );
    });

    it("should initialize express-ws after server creation", () => {
      // Set to development for this specific test
      process.env.NODE_ENV = "development";
      const expressWs = require("@mintplex-labs/express-ws");

      bootSSL(mockApp as Application, 3001);

      expect(expressWs.default).toHaveBeenCalledWith(mockApp);
    });
  });

  describe("bootHTTP", () => {
    it("should boot server in HTTP mode successfully", async () => {
      // Set to development for this specific test
      process.env.NODE_ENV = "development";
      const consoleSpy = jest.spyOn(console, "log").mockImplementation();

      const result = bootHTTP(mockApp as Application, 3001);

      expect(mockApp.listen).toHaveBeenCalledWith(3001, expect.any(Function));
      expect(result).toEqual({ app: mockApp, server: mockServer });

      // Trigger the listen callback
      const listenCallback = (mockApp.listen as jest.Mock).mock.calls[0][1];
      await listenCallback();

      expect(consoleSpy).toHaveBeenCalledWith(
        "Primary server in HTTP mode listening on port 3001"
      );

      consoleSpy.mockRestore();
    });

    it("should initialize all services on successful boot", async () => {
      // Set to development for this specific test
      process.env.NODE_ENV = "development";
      bootHTTP(mockApp as Application, 3001);

      // Trigger the listen callback
      const listenCallback = (mockApp.listen as jest.Mock).mock.calls[0][1];
      await listenCallback();

      expect(setupTelemetry).toHaveBeenCalled();
      expect(initializeLanguage).toHaveBeenCalled();
      expect(CommunicationKey).toHaveBeenCalledWith(true);
      expect(EncryptionManager).toHaveBeenCalled();
      expect(BackgroundService).toHaveBeenCalled();

      const backgroundService = (BackgroundService as unknown as jest.Mock).mock
        .results[0].value;
      expect(backgroundService.boot).toHaveBeenCalled();
    });

    it("should handle boot service initialization errors", async () => {
      // Set to development for this specific test
      process.env.NODE_ENV = "development";
      const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();
      (initializeLanguage as jest.Mock).mockRejectedValue(
        new Error("Language init error")
      );

      bootHTTP(mockApp as Application, 3001);

      const listenCallback = (mockApp.listen as jest.Mock).mock.calls[0][1];
      await listenCallback();

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        "FAILED TO BOOT SERVER",
        expect.any(Error)
      );

      consoleErrorSpy.mockRestore();
    });

    it("should handle server listen errors", () => {
      // Set to development for this specific test
      process.env.NODE_ENV = "development";
      const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();
      const mockError = new Error("Port already in use");

      (mockApp.listen as jest.Mock).mockReturnValue({
        on: jest.fn().mockImplementation((event, handler) => {
          if (event === "error") {
            handler(mockError);
          }
          return mockServer;
        }),
      });

      bootHTTP(mockApp as Application, 3001);

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining("[HTTP BOOT FAILED]"),
        mockError
      );

      consoleErrorSpy.mockRestore();
    });

    it("should skip server listen in test environment", () => {
      process.env.NODE_ENV = "test";
      const consoleSpy = jest.spyOn(console, "log").mockImplementation();

      const result = bootHTTP(mockApp as Application, 3001);

      expect(mockApp.listen).not.toHaveBeenCalled();
      expect(consoleSpy).toHaveBeenCalledWith(
        "[TEST ENV] HTTP server .listen() and boot services skipped."
      );
      expect(result).toEqual({ app: mockApp, server: null });

      consoleSpy.mockRestore();
    });

    it("should throw error when app is not provided", () => {
      expect(() => bootHTTP(null as any)).toThrow(
        "Application instance is required"
      );
    });

    it("should use default port when not provided", () => {
      // Set to development for this specific test
      process.env.NODE_ENV = "development";
      bootHTTP(mockApp as Application);

      expect(mockApp.listen).toHaveBeenCalledWith(3001, expect.any(Function));
    });

    it("should use custom port when provided", () => {
      // Set to development for this specific test
      process.env.NODE_ENV = "development";
      bootHTTP(mockApp as Application, 8080);

      expect(mockApp.listen).toHaveBeenCalledWith(8080, expect.any(Function));
    });
  });

  describe("Signal handlers", () => {
    let processOnceSpy: jest.SpyInstance;
    let processOnSpy: jest.SpyInstance;
    let processKillSpy: jest.SpyInstance;
    let telemetryFlushSpy: jest.SpyInstance;

    beforeEach(() => {
      processOnceSpy = jest
        .spyOn(process, "once")
        .mockImplementation(() => process);
      processOnSpy = jest
        .spyOn(process, "on")
        .mockImplementation(() => process);
      processKillSpy = jest.spyOn(process, "kill").mockImplementation();
      telemetryFlushSpy = jest.spyOn(Telemetry, "flush").mockImplementation();
    });

    afterEach(() => {
      processOnceSpy.mockRestore();
      processOnSpy.mockRestore();
      processKillSpy.mockRestore();
      telemetryFlushSpy.mockRestore();
    });

    it("should register signal handlers on server error", () => {
      // Set to development for this specific test
      process.env.NODE_ENV = "development";
      const mockError = new Error("Server error");
      (mockApp.listen as jest.Mock).mockReturnValue({
        on: jest.fn().mockImplementation((event, handler) => {
          if (event === "error") {
            handler(mockError);
          }
          return mockServer;
        }),
      });

      bootHTTP(mockApp as Application, 3001);

      expect(processOnceSpy).toHaveBeenCalledWith(
        "SIGUSR2",
        expect.any(Function)
      );
      expect(processOnSpy).toHaveBeenCalledWith("SIGINT", expect.any(Function));
    });

    it("should handle SIGUSR2 signal", () => {
      // Set to development for this specific test
      process.env.NODE_ENV = "development";
      const mockError = new Error("Server error");
      let sigusr2Handler: Function | undefined;

      processOnceSpy.mockImplementation((signal, handler) => {
        if (signal === "SIGUSR2") {
          sigusr2Handler = handler;
        }
        return process;
      });

      (mockApp.listen as jest.Mock).mockReturnValue({
        on: jest.fn().mockImplementation((event, handler) => {
          if (event === "error") {
            handler(mockError);
          }
          return mockServer;
        }),
      });

      bootHTTP(mockApp as Application, 3001);

      // Verify handler was registered
      expect(sigusr2Handler).toBeDefined();

      // Trigger SIGUSR2 handler
      if (sigusr2Handler) {
        sigusr2Handler();
      }

      expect(telemetryFlushSpy).toHaveBeenCalled();
      expect(processKillSpy).toHaveBeenCalledWith(process.pid, "SIGUSR2");
    });

    it("should handle SIGINT signal", () => {
      // Set to development for this specific test
      process.env.NODE_ENV = "development";
      const mockError = new Error("Server error");
      let sigintHandler: Function | undefined;

      processOnSpy.mockImplementation((signal, handler) => {
        if (signal === "SIGINT") {
          sigintHandler = handler;
        }
        return process;
      });

      (mockApp.listen as jest.Mock).mockReturnValue({
        on: jest.fn().mockImplementation((event, handler) => {
          if (event === "error") {
            handler(mockError);
          }
          return mockServer;
        }),
      });

      bootHTTP(mockApp as Application, 3001);

      // Verify handler was registered
      expect(sigintHandler).toBeDefined();

      // Trigger SIGINT handler
      if (sigintHandler) {
        sigintHandler();
      }

      expect(telemetryFlushSpy).toHaveBeenCalled();
      expect(processKillSpy).toHaveBeenCalledWith(process.pid, "SIGINT");
    });
  });

  describe("Edge cases and error scenarios", () => {
    it("should handle missing SSL environment variables", () => {
      // Set to development for this specific test
      process.env.NODE_ENV = "development";
      delete process.env.HTTPS_KEY_PATH;
      delete process.env.HTTPS_CERT_PATH;

      const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();
      const consoleLogSpy = jest.spyOn(console, "log").mockImplementation();

      bootSSL(mockApp as Application, 3001);

      // Just verify error was logged with SSL boot failure
      expect(consoleErrorSpy).toHaveBeenCalled();
      const errorCall = consoleErrorSpy.mock.calls[0];
      expect(errorCall[0]).toContain("[SSL BOOT FAILED]");

      consoleErrorSpy.mockRestore();
      consoleLogSpy.mockRestore();
    });

    it("should handle concurrent service initialization failures", async () => {
      // Set to development for this specific test
      process.env.NODE_ENV = "development";
      const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();

      (setupTelemetry as jest.Mock).mockRejectedValue(
        new Error("Telemetry error")
      );
      (initializeLanguage as jest.Mock).mockRejectedValue(
        new Error("Language error")
      );

      bootHTTP(mockApp as Application, 3001);

      const listenCallback = (mockApp.listen as jest.Mock).mock.calls[0][1];
      await listenCallback();

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        "FAILED TO BOOT SERVER",
        expect.any(Error)
      );

      consoleErrorSpy.mockRestore();
    });

    it("should handle BackgroundService boot failure", async () => {
      // Set to development for this specific test
      process.env.NODE_ENV = "development";
      const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();

      (BackgroundService as unknown as jest.Mock).mockImplementation(() => ({
        boot: jest.fn().mockImplementation(() => {
          throw new Error("Background service error");
        }),
      }));

      bootHTTP(mockApp as Application, 3001);

      const listenCallback = (mockApp.listen as jest.Mock).mock.calls[0][1];
      await listenCallback();

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        "FAILED TO BOOT SERVER",
        expect.any(Error)
      );

      consoleErrorSpy.mockRestore();
    });

    it("should handle HTTPS createServer failure", () => {
      // Set to development for this specific test
      process.env.NODE_ENV = "development";
      const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();
      const consoleLogSpy = jest.spyOn(console, "log").mockImplementation();

      (fs.readFileSync as jest.Mock)
        .mockReturnValueOnce("mock-private-key")
        .mockReturnValueOnce("mock-certificate");

      (https.createServer as jest.Mock).mockImplementation(() => {
        throw new Error("HTTPS creation failed");
      });

      const result = bootSSL(mockApp as Application, 3001);

      // Verify console.error was called
      expect(consoleErrorSpy).toHaveBeenCalled();

      // The specific format of the error message
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining("[SSL BOOT FAILED]"),
        expect.objectContaining({
          stacktrace: expect.any(String),
        })
      );

      // Should fall back to HTTP
      expect(result.app).toBe(mockApp);

      consoleErrorSpy.mockRestore();
      consoleLogSpy.mockRestore();
    });
  });
});
