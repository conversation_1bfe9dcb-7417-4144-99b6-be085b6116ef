import fs from "fs";
import https from "https";
import { Application } from "express";

const expressWs = require("@mintplex-labs/express-ws");
import { Telemetry } from "../../models/telemetry";
import { BackgroundService } from "../BackgroundWorkers";
import { EncryptionManager } from "../EncryptionManager";
import { CommunicationKey } from "../comKey";
import setupTelemetry from "../telemetry";
import { initializeLanguage } from "../i18n";
import type { BootResult } from "../../types/utils";

// Testing SSL? You can make a self signed certificate and point the ENVs to that location
// make a directory in server called 'sslcert' - cd into it
// - openssl genrsa -aes256 -passout pass:gsahdg -out server.pass.key 4096
// - openssl rsa -passin pass:gsahdg -in server.pass.key -out server.key
// - rm server.pass.key
// - openssl req -new -key server.key -out server.csr
// Update .env keys with the correct values and boot. These are temporary and not real SSL certs - only use for local.
// Test with https://localhost:3001/api/ping
// build and copy frontend to server/public with correct API_BASE and start server in prod model and all should be ok
export function bootSSL(app: Application, port: number = 3001): BootResult {
  try {
    console.log(
      `\x1b[33m[SSL BOOT ENABLED]\x1b[0m Loading the certificate and key for HTTPS mode...`
    );
    const privateKey = fs.readFileSync(process.env.HTTPS_KEY_PATH!);
    const certificate = fs.readFileSync(process.env.HTTPS_CERT_PATH!);
    const credentials = { key: privateKey, cert: certificate };
    const server = https.createServer(credentials, app);

    if (process.env.NODE_ENV !== "test") {
      server
        .listen(port, async () => {
          try {
            await setupTelemetry();
            await initializeLanguage();
            new CommunicationKey(true);
            new EncryptionManager();
            new BackgroundService().boot();
            console.log(
              `Primary server in HTTPS mode listening on port ${port}`
            );
          } catch (e) {
            console.error("FAILED TO BOOT SERVER", e);
          }
        })
        .on("error", (error: Error) => {
          console.error(`\x1b[31m[SSL BOOT FAILED]\x1b[0m`, error);
          catchSigTerms();
        });
    } else {
      console.log("[TEST ENV] SSL server .listen() and boot services skipped.");
    }

    expressWs.default(app); // Apply same certificate + server for WSS connections
    return { app, server };
  } catch (e) {
    const error = e as Error;
    console.error(
      `\x1b[31m[SSL BOOT FAILED]\x1b[0m ${error.message} - falling back to HTTP boot.`,
      {
        ENABLE_HTTPS: process.env.ENABLE_HTTPS,
        HTTPS_KEY_PATH: process.env.HTTPS_KEY_PATH,
        HTTPS_CERT_PATH: process.env.HTTPS_CERT_PATH,
        stacktrace: error.stack,
      }
    );
    return bootHTTP(app, port);
  }
}

export function bootHTTP(app: Application, port: number = 3001): BootResult {
  if (!app) throw new Error("Application instance is required");

  if (process.env.NODE_ENV !== "test") {
    const server = app
      .listen(port, async () => {
        try {
          await setupTelemetry();
          await initializeLanguage();
          new CommunicationKey(true);
          new EncryptionManager();
          new BackgroundService().boot();
          console.log(`Primary server in HTTP mode listening on port ${port}`);
        } catch (e) {
          console.error("FAILED TO BOOT SERVER", e);
        }
      })
      .on("error", (error: Error) => {
        console.error(`\x1b[31m[HTTP BOOT FAILED]\x1b[0m`, error);
        catchSigTerms();
      });
    return { app, server };
  } else {
    console.log("[TEST ENV] HTTP server .listen() and boot services skipped.");
  }

  return { app, server: null };
}

function catchSigTerms(): void {
  process.once("SIGUSR2", function () {
    Telemetry.flush();
    process.kill(process.pid, "SIGUSR2");
  });
  process.on("SIGINT", function () {
    Telemetry.flush();
    process.kill(process.pid, "SIGINT");
  });
}
