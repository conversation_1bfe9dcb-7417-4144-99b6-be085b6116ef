jest.mock("node-fetch");

// Import the function we're testing from local test file
import { optimizeContext as originalOptimizeContext } from "../contextWindow";
import type {
  LLMProvider,
  EmbeddingEngine,
  TokenLimits,
  ChatMessage as AIProviderChatMessage,
  PromptArgs,
} from "../../../types/ai-providers";

// Create a mock EmbeddingEngine
const mockEmbeddingEngine: EmbeddingEngine = {
  embedTextInput: jest.fn().mockResolvedValue([]),
  embedChunks: jest.fn().mockResolvedValue([]),
};

// Create a mock LLMProvider that implements all required methods
const createMockLLMProvider = (
  contextWindowSize: number,
  model: string
): LLMProvider & { contextWindowSize: number } => ({
  contextWindowSize,
  model,
  limits: {
    history: 4000,
    system: 2000,
    user: 2000,
  } as TokenLimits,
  embedder: mockEmbeddingEngine,
  defaultTemp: 0.7,
  streamingEnabled: jest.fn().mockReturnValue(true),
  promptWindowLimit: jest.fn().mockReturnValue(contextWindowSize),
  customPromptWindowLimit: jest.fn().mockReturnValue(contextWindowSize),
  isValidChatCompletionModel: jest.fn().mockResolvedValue(true),
  constructPrompt: jest.fn().mockResolvedValue([]),
  getChatCompletion: jest.fn().mockResolvedValue(null),
  embedTextInput: jest.fn().mockResolvedValue([]),
  embedChunks: jest.fn().mockResolvedValue([]),
  compressMessages: jest.fn().mockResolvedValue([]),
});

// Using the actual OptimizeContextParams type from contextWindow.ts through inference
type OptimizeContextParams = Parameters<typeof originalOptimizeContext>[0];
type ContextWindowSettings = OptimizeContextParams["settings"];
type MockDocumentManager = OptimizeContextParams["documentManager"];

// Define document data types for tests
// interface TestDocument {
//   pageContent: string;
//   token_count_estimate: number;
// }

interface TestAttachment {
  id: string;
  name: string;
  type: string;
  content: string;
  tokens: number;
  pageContent?: string;
  token_count_estimate?: number;
}

interface TestChunk {
  id: string;
  content: string;
  tokens: number;
  pageContent?: string;
  token_count_estimate?: number;
}

// Helper function to convert test data to DocumentData format
function toDocumentData(item: TestAttachment | TestChunk) {
  return {
    ...item,
    pageContent: item.pageContent || item.content,
    token_count_estimate: item.token_count_estimate || item.tokens,
  };
}

// Define proper TypeScript interfaces for test types
interface TokenInfo {
  type: string;
  systemTokens?: number;
  userTokens?: number;
  historyTokens?: number;
  totalTokens: number;
  contextWindowSize?: number;
  didCannonball?: boolean;
  test?: string;
}

interface TestReporter {
  tokenInfo: TokenInfo[];
  addTokenInfo(info: TokenInfo): void;
  clear(): void;
}

// Using AIProviderChatMessage from imports instead of local ChatMessage

// interface CompressMessagesParams {
//   systemPrompt: string;
//   userPrompt: string;
//   chatHistory?: AIProviderChatMessage[];
// }

// Using LLMProvider instead of local LLMConnector interface

interface EventCallback {
  (_event: string, _callback: (_data?: unknown) => void): void;
}

interface StreamEventEmitter {
  on: EventCallback;
}

// Mock required modules with correct paths
jest.mock("../streamLQA");
jest.mock("../../helpers/tiktoken", () => ({
  TokenManager: jest.fn().mockImplementation(() => ({
    countFromString: jest.fn((str: string | null | undefined) =>
      Math.ceil(((str?.length ?? false) || 0) / 4)
    ),
    tokenStats: jest.fn(() => ({ tokens: 1000 })),
  })),
}));
jest.mock("../../../models/workspaceChats");
jest.mock("../../../models/systemSettings");

// Mock DocumentManager with correct path
jest.mock("../../DocumentManager", () => ({
  DocumentManager: jest.fn().mockImplementation(function () {
    // Create an instance that can be configured by each test
    return {
      workspace: {},
      maxTokens: 1000,
      documentStoragePath: "/tmp",
      log: jest.fn(),
      // checkVectorCacheAndRecentOwner: jest.fn(), // Method not available in DocumentManager interface
      // getRagContent: jest.fn(), // Method not available in DocumentManager interface
      pinnedDocs: jest.fn().mockResolvedValue([]),
      pdrDocs: jest.fn().mockResolvedValue([]),
      pinnedDocuments: jest.fn().mockResolvedValue([]),
      pdrDocuments: jest.fn().mockResolvedValue([]),
    };
  }),
}));

// Mock VectorDb
const mockPerformSimilaritySearch = jest.fn() as jest.MockedFunction<
  (params: unknown) => unknown
>;
const mockHasNamespace = jest.fn() as jest.MockedFunction<() => unknown>;
const mockNamespaceCount = jest.fn() as jest.MockedFunction<() => unknown>;
const mockGetAdjacentVectors = jest.fn() as jest.MockedFunction<() => unknown>;

// Create a test reporter object to capture token info for each test
const testReporter: TestReporter = {
  tokenInfo: [],

  addTokenInfo(info: TokenInfo) {
    this.tokenInfo.push(info);
    // Use process.stdout.write to ensure output is shown even during test runs
    // Simulate logging the token info for debugging/analysis in real scenarios
    // This helps verify the structure and content of what would be logged.
    // console.log(`\n[SERVER LOG] TOKEN INFO: ${JSON.stringify(info)}\n`);
  },

  clear() {
    this.tokenInfo = [];
  },
};

// Override console.log for test runs to intercept token logging
const originalConsoleLog = console.log as (...args: unknown[]) => void;
console.log = function (message: unknown, ..._args: unknown[]) {
  // Check if this is a token info log from our module
  if (typeof message === "string" && message.includes("[TOKEN INFO]")) {
    const jsonStart = message.indexOf("{");
    if (jsonStart !== -1) {
      try {
        const jsonData = JSON.parse(message.substring(jsonStart)) as TokenInfo;
        // Add to test reporter and display nicely formatted
        testReporter.addTokenInfo(jsonData);
      } catch {
        // If JSON parsing fails, just log normally
        originalConsoleLog(message, ..._args);
      }
    } else {
      originalConsoleLog(message, ..._args);
    }
  } else {
    // Pass through normal logs
    originalConsoleLog(message, ..._args);
  }
};

// Create LLM providers with different context window sizes
const createMockLLMConnector = (
  contextWindowSize = 128000
): LLMProvider & { contextWindowSize: number } => {
  // Track whether cannonballing occurred for this connector
  let didCannonball = false;
  let totalTokenCount = 0;

  // Create base LLMProvider with required properties
  const baseLLMProvider = createMockLLMProvider(
    contextWindowSize,
    "test-model"
  );

  // Override compressMessages with custom implementation
  baseLLMProvider.compressMessages = jest
    .fn()
    .mockImplementation(
      async (promptArgs: PromptArgs, rawHistory: AIProviderChatMessage[]) => {
        const { systemPrompt = "", userPrompt = "" } = promptArgs;
        const chatHistory = rawHistory;

        // Calculate token counts
        const systemTokens = Math.ceil(
          ((systemPrompt?.length ?? false) || 0) / 4
        );
        const userTokens = Math.ceil(((userPrompt?.length ?? false) || 0) / 4);

        let historyTokens = 0;
        if (chatHistory && chatHistory.length) {
          historyTokens = chatHistory.reduce(
            (sum: number, msg: AIProviderChatMessage) => {
              const contentLength =
                typeof msg.content === "string" ? msg.content.length : 0;
              return sum + Math.ceil(contentLength / 4);
            },
            0
          );
        }

        // Total tokens before any compression
        const beforeCompressionTokens =
          systemTokens + userTokens + historyTokens;

        // Determine if cannonballing needs to happen
        didCannonball = beforeCompressionTokens > contextWindowSize;
        totalTokenCount = beforeCompressionTokens;

        // Capture token info
        testReporter.addTokenInfo({
          type: "compression",
          systemTokens,
          userTokens,
          historyTokens,
          totalTokens: beforeCompressionTokens,
          contextWindowSize,
          didCannonball,
          test: expect.getState().currentTestName,
        });

        // Return the messages (simulate compression if needed)
        const result = [
          { role: "system", content: systemPrompt },
          { role: "user", content: userPrompt },
          ...(chatHistory || []).map((msg: AIProviderChatMessage) => ({
            role: msg.role,
            content: typeof msg.content === "string" ? msg.content : "",
          })),
        ] as AIProviderChatMessage[];

        return result;
      }
    );

  // Add additional mock methods that might be used in tests
  const mockConnector = baseLLMProvider as LLMProvider & {
    contextWindowSize: number;
    countTokens: jest.MockedFunction<() => number>;
    tokenWindowLimit: jest.MockedFunction<() => number>;
    streamChat: jest.MockedFunction<() => unknown>;
    streamResponse: jest.MockedFunction<() => unknown>;
    streamWithHistory: jest.MockedFunction<() => unknown>;
    streamGetChatCompletion: jest.MockedFunction<() => Promise<unknown>>;
    handleStream: jest.MockedFunction<() => Promise<string>>;
  };
  mockConnector.countTokens = jest.fn().mockReturnValue(1000);
  mockConnector.tokenWindowLimit = jest.fn().mockReturnValue(contextWindowSize);
  mockConnector.streamChat = jest.fn();
  mockConnector.streamResponse = jest.fn();
  mockConnector.streamWithHistory = jest.fn();
  mockConnector.streamGetChatCompletion = jest
    .fn()
    .mockImplementation(async () => {
      // Log final token info
      testReporter.addTokenInfo({
        type: "final",
        totalTokens: totalTokenCount,
        didCannonball,
        test: expect.getState().currentTestName,
      });

      return {
        on: (_event: string, _callback: (_data?: unknown) => void) => {
          if (_event === "data") {
            _callback({ choices: [{ delta: { content: "Test response" } }] });
          }
          if (_event === "end") {
            _callback();
          }
        },
      } as StreamEventEmitter;
    });
  mockConnector.handleStream = jest.fn().mockImplementation(async () => {
    return "Test response";
  });

  return baseLLMProvider;
};

// Default mock LLM connector (128k context window)
const mockLLMConnector = createMockLLMConnector(128000);
// LLM connector with 1 million token context window
const mockLargeLLMConnector = createMockLLMConnector(1000000);

// Mock LLM provider factory - needs to be initialized as a proper jest mock function
const mockLLMProviderFactory = jest.fn().mockReturnValue(mockLLMConnector);

// Mock SystemSettings
jest.mock("../../../models/systemSettings", () => ({
  SystemSettings: {
    getPdrSettings: jest.fn().mockResolvedValue({
      adjacentVector: 2,
      keepPdrVectors: true,
    }),
    getDynamicContextSettings: jest.fn().mockResolvedValue(70),
    currentSettings: jest.fn().mockResolvedValue({
      attachment_context_percentage: 70,
      dynamic_context_window_percentage: 70,
      LLMProvider_CUAI: null,
    }),
  },
}));

// Mock helpers properly without referencing outer scope variables
jest.mock("../../helpers", () => ({
  getVectorDbClass: () => ({
    performSimilaritySearch: mockPerformSimilaritySearch,
    hasNamespace: mockHasNamespace,
    namespaceCount: mockNamespaceCount,
    getAdjacentVectors: mockGetAdjacentVectors,
  }),
  getLLMProvider: () => mockLLMProviderFactory(),
}));

// Mock workspace chats model
jest.mock("../../../models/workspaceChats", () => ({
  WorkspaceChats: {
    new: jest.fn().mockResolvedValue({ id: "test-chat-id" }),
    get: jest.fn().mockResolvedValue(null),
    where: jest.fn().mockResolvedValue([]),
  },
}));

// Mock the streamChatWithWorkspaceLQA function to ensure it calls required functions
jest.mock("../streamLQA", () => {
  return {
    streamChatWithWorkspaceLQA: jest.fn(
      async (
        _request: unknown,
        response: unknown,
        workspace: {
          slug: string;
          topN: number;
          similarityThreshold: number;
          chatModel?: string;
        }
      ) => {
        // Call performSimilaritySearch with the expected parameters
        mockPerformSimilaritySearch({
          namespace: workspace.slug,
          topN: workspace.topN,
          similarityThreshold: workspace.similarityThreshold,
        });

        // Get the DocumentManager mock and call pdrDocs
        const { DocumentManager } = require("../../../utils/DocumentManager");
        const mockDocManager = new DocumentManager() as {
          pdrDocs: () => Promise<unknown>;
        };
        await mockDocManager.pdrDocs();

        // Use the appropriate LLM connector based on the workspace model
        const connector =
          workspace.chatModel === "gpt-4-1m"
            ? mockLargeLLMConnector
            : mockLLMConnector;

        // Simulate compressMessages being called
        await connector.compressMessages(
          {
            systemPrompt: "system prompt",
            userPrompt: "user prompt",
          },
          []
        );

        // Call the appropriate stream completion method if available
        if (connector.streamGetChatCompletion) {
          await connector.streamGetChatCompletion([], {});
        }

        // Return like the original would
        return response;
      }
    ),
  };
});

// Create a wrapped version of the function for testing
const optimizeContext = async (
  params: Parameters<typeof originalOptimizeContext>[0]
) => {
  const result = await originalOptimizeContext(params);

  // Add logging after every call
  try {
    const totalTokens = result.totalTokens;
    if (totalTokens && totalTokens > 128000) {
      console.log(
        `\n\nWARNING: Total tokens (${totalTokens}) exceed context window (128000)\n\n`
      );
    }
  } catch {
    // Ignore errors in logging
  }

  return result;
};

describe("Context Window Management", () => {
  let mockDocumentManager: MockDocumentManager;
  let mockSettings: ContextWindowSettings;

  // Set up mocks before each test
  beforeEach(() => {
    // Create mock document manager with all required properties
    mockDocumentManager = {
      workspace: { id: 1, name: "Test Workspace" },
      maxTokens: 1000,
      documentStoragePath: "/tmp",
      log: jest.fn(),
      // checkVectorCacheAndRecentOwner: jest.fn(), // Method not available in DocumentManager interface
      // getRagContent: jest.fn(), // Method not available in DocumentManager interface
      pinnedDocs: jest.fn().mockResolvedValue([]),
      pdrDocs: jest.fn().mockResolvedValue([]),
      pinnedDocuments: jest.fn().mockResolvedValue([]),
      pdrDocuments: jest.fn().mockResolvedValue([]),
    } as unknown as MockDocumentManager;

    // Create mock settings
    mockSettings = {
      contextWindowPercentage: 70,
      pinnedDocsPercent: 0.1,
      chatHistoryPercent: 0.5,
      pdrDocsPercent: 0.4,
    };

    // Reset the mock factory to return the default LLM connector
    (
      mockLLMProviderFactory as jest.MockedFunction<
        typeof mockLLMProviderFactory
      >
    ).mockReturnValue(mockLLMConnector);

    // Reset vector db mocks

    // Make sure we reset the reporter between tests
    testReporter.clear();
  });

  // Add verification for token information after each test
  afterEach(() => {
    // Log completion of test for clarity
    console.log(`Test completed: ${expect.getState().currentTestName}`);

    // Verify we have at least some token info for the test
    expect(testReporter.tokenInfo.length).toBeGreaterThan(0);
  });

  // Restore original console.log after all tests
  afterAll(() => {
    console.log = originalConsoleLog;
  });

  /*
   * Test 1: Using a 128k context window with moderate-sized attachments and various chunk sizes
   */
  it("should optimize context window with 128k LLM and mixed chunk sizes", async () => {
    // Configure mocks with realistic data
    const mockSystemPrompt =
      "You are a helpful assistant. Your job is to provide detailed information about legal topics.".repeat(
        10
      );
    const mockUserPrompt =
      "Tell me about contract law and its implications in international business.".repeat(
        20
      );
    const mockChatHistory: AIProviderChatMessage[] = [
      {
        role: "user",
        content: "What are the key principles of contract law?".repeat(5),
      },
      {
        role: "assistant",
        content: "Contract law is governed by several key principles...".repeat(
          30
        ),
      },
    ];

    // Setup WorkspaceChats mock to return our chat history
    require("../../../models/workspaceChats").WorkspaceChats.where.mockResolvedValue(
      mockChatHistory
    );

    // Create attachment with significant token count (about 10,000 tokens)
    const largeAttachment: TestAttachment = {
      id: "att1",
      name: "Contract Analysis",
      type: "text",
      content:
        "This detailed analysis covers contract law principles...".repeat(500),
      tokens: 10000,
      pageContent:
        "This detailed analysis covers contract law principles...".repeat(500),
      token_count_estimate: 10000,
    };

    // Create vector chunks with varying sizes
    const smallChunks: TestChunk[] = Array(10)
      .fill(null)
      .map((_, i) => ({
        id: `chunk_${i}`,
        content: "Small chunk of legal information".repeat(5),
        tokens: 100,
        pageContent: "Small chunk of legal information".repeat(5),
        token_count_estimate: 100,
      }));

    const mediumChunks: TestChunk[] = Array(5)
      .fill(null)
      .map((_, i) => ({
        id: `medium_${i}`,
        content:
          "Medium sized chunk with more detailed information about legal concepts".repeat(
            20
          ),
        tokens: 500,
        pageContent:
          "Medium sized chunk with more detailed information about legal concepts".repeat(
            20
          ),
        token_count_estimate: 500,
      }));

    const largeChunks: TestChunk[] = Array(2)
      .fill(null)
      .map((_, i) => ({
        id: `large_${i}`,
        content:
          "Extensive legal analysis with precedents and case studies".repeat(
            100
          ),
        tokens: 2500,
        pageContent:
          "Extensive legal analysis with precedents and case studies".repeat(
            100
          ),
        token_count_estimate: 2500,
      }));

    // Configure mock document manager to return our test documents
    (mockDocumentManager.pinnedDocs as jest.Mock).mockResolvedValue([
      toDocumentData(largeAttachment),
    ]);
    (mockDocumentManager.pdrDocs as jest.Mock).mockResolvedValue([
      ...smallChunks.map(toDocumentData),
      ...mediumChunks.map(toDocumentData),
      ...largeChunks.map(toDocumentData),
    ]);

    // Create LLM connector with 128k context window
    const mockLLM = createMockLLMConnector(128000);

    // Explicitly call compressMessages to ensure token calculation happens
    await mockLLM.compressMessages(
      {
        systemPrompt: mockSystemPrompt,
        userPrompt: mockUserPrompt,
      },
      mockChatHistory
    );

    // Call the function under test
    const result = await optimizeContext({
      llm: mockLLM,
      workspaceId: "ws123",
      userId: "user1",
      threadId: "thread1",
      sessionId: "session1",
      systemPrompt: mockSystemPrompt,
      userPrompt: mockUserPrompt,
      documentManager: mockDocumentManager,
      settings: mockSettings,
      maxTokens: 128000,
    });

    expect(result).toBeDefined();
    // Verify that documents were included in the result
    expect(result.pinnedDocs.length).toBeGreaterThan(0);
    expect(result.pdrDocs.length).toBeGreaterThan(0);
    // Verify token counts (should be closer to context window max)
    expect(result.totalTokens).toBeGreaterThan(10000);
  });

  /*
   * Test 2: Using a 1M context window with large attachments and many chunks
   */
  it("should handle 1M context window with large attachments and many chunks", async () => {
    // Configure mocks with realistic data
    const mockSystemPrompt =
      "You are a legal research assistant. Your purpose is to analyze large volumes of legal documents.".repeat(
        20
      );
    const mockUserPrompt =
      "Analyze these contracts and provide a detailed summary of the terms, obligations, and potential risks.".repeat(
        30
      );
    const mockChatHistory: AIProviderChatMessage[] = [
      {
        role: "user",
        content: "I need help with these legal documents.".repeat(10),
      },
      {
        role: "assistant",
        content:
          "I'd be happy to help analyze your legal documents. Please provide the details.".repeat(
            20
          ),
      },
      {
        role: "user",
        content: "Here are the contracts I mentioned.".repeat(5),
      },
      {
        role: "assistant",
        content:
          "Thank you for providing the contracts. Let me review them carefully...".repeat(
            40
          ),
      },
    ];

    // Setup WorkspaceChats mock to return our chat history
    require("../../../models/workspaceChats").WorkspaceChats.where.mockResolvedValue(
      mockChatHistory
    );

    // Create massive attachments (about 100,000 tokens total)
    const hugeAttachments: TestAttachment[] = Array(3)
      .fill(null)
      .map((_, i) => ({
        id: `huge_${i}`,
        name: `Contract Analysis ${i}`,
        type: "text",
        content:
          "This extremely detailed analysis covers all aspects of contract law...".repeat(
            10000
          ),
        tokens: 33333,
      }));

    // Create many vector chunks with significant size
    const manyLargeChunks: TestChunk[] = Array(100)
      .fill(null)
      .map((_, i) => ({
        id: `chunk_${i}`,
        content:
          "Large detailed chunk with comprehensive legal information".repeat(
            50
          ),
        tokens: 1250,
      }));

    // Configure mock document manager to return our test documents
    (mockDocumentManager.pinnedDocs as jest.Mock).mockResolvedValue(
      hugeAttachments.map(toDocumentData)
    );
    (mockDocumentManager.pdrDocs as jest.Mock).mockResolvedValue(
      manyLargeChunks.map(toDocumentData)
    );

    // Create LLM connector with 1M context window
    const mockLLM = createMockLLMConnector(1000000);

    // Explicitly call compressMessages to ensure token calculation happens
    await mockLLM.compressMessages(
      {
        systemPrompt: mockSystemPrompt,
        userPrompt: mockUserPrompt,
      },
      mockChatHistory
    );

    // Call the function under test
    const result = await optimizeContext({
      llm: mockLLM,
      workspaceId: "ws123",
      userId: "user1",
      threadId: "thread1",
      sessionId: "session1",
      systemPrompt: mockSystemPrompt,
      userPrompt: mockUserPrompt,
      documentManager: mockDocumentManager,
      settings: mockSettings,
      maxTokens: 1000000,
    });

    expect(result).toBeDefined();
    // Verify that documents were included in the result
    expect(result.pinnedDocs.length).toBeGreaterThan(0);
    expect(result.pdrDocs.length).toBeGreaterThan(0);
    // Verify token counts (should be closer to context window max)
    expect(result.totalTokens).toBeGreaterThan(100000);
  });

  /*
   * Test 3: Test with various chat history sizes and PDR processing
   */
  it("should optimize context with various chat history sizes and PDR processing", async () => {
    // Configure mocks with realistic data
    const mockSystemPrompt =
      "You are a legal assistant specialized in analyzing precedent cases. Your task is to find relevant precedents.".repeat(
        15
      );
    const mockUserPrompt =
      "Can you analyze these case documents and identify precedents that could support our current litigation regarding intellectual property infringement?".repeat(
        15
      );

    // Create a chat history with multiple messages
    const mockChatHistory: AIProviderChatMessage[] = [
      {
        role: "user",
        content: "I need help with an intellectual property case.".repeat(5),
      },
      {
        role: "assistant",
        content:
          "I'd be happy to help with your intellectual property case. Could you provide more details about the specific situation?".repeat(
            15
          ),
      },
      {
        role: "user",
        content:
          "We believe a competitor has infringed on our software patent.".repeat(
            10
          ),
      },
      {
        role: "assistant",
        content:
          "I understand your concern about the potential patent infringement. To assist you properly, I'll need to know more about your patent, the alleged infringement, and any evidence you have gathered.".repeat(
            15
          ),
      },
      {
        role: "user",
        content:
          "The patent covers our unique algorithm for data processing.".repeat(
            8
          ),
      },
      {
        role: "assistant",
        content:
          "Thank you for the information about your patent. Let me analyze the situation based on similar precedent cases involving software algorithm patents and potential infringement scenarios.".repeat(
            20
          ),
      },
    ];

    // Setup WorkspaceChats mock to return our chat history
    require("../../../models/workspaceChats").WorkspaceChats.where.mockResolvedValue(
      mockChatHistory
    );

    // Create PDR-specific vectors (30,000 tokens total)
    const pdrVectors: TestChunk[] = Array(20)
      .fill(null)
      .map((_, i) => ({
        id: `pdr_${i}`,
        content:
          "This document contains analysis of patent infringement precedents and legal arguments.".repeat(
            150
          ),
        tokens: 1500,
      }));

    // Configure mock document manager to return our test documents
    const mockAttachments: TestAttachment[] = [
      {
        id: "att1",
        name: "Patent Documentation",
        type: "text",
        content:
          "Detailed patent documentation and evidence of infringement...".repeat(
            2000
          ),
        tokens: 20000,
      },
    ];

    (
      mockDocumentManager.pinnedDocs as jest.MockedFunction<
        typeof mockDocumentManager.pinnedDocs
      >
    ).mockResolvedValue(mockAttachments.map(toDocumentData));
    (
      mockDocumentManager.pdrDocs as jest.MockedFunction<
        typeof mockDocumentManager.pdrDocs
      >
    ).mockResolvedValue(pdrVectors.map(toDocumentData));

    // Create LLM connector with 128k context window
    const mockLLM = createMockLLMConnector(128000);

    // Explicitly call compressMessages to ensure token calculation happens
    await mockLLM.compressMessages(
      {
        systemPrompt: mockSystemPrompt,
        userPrompt: mockUserPrompt,
      },
      mockChatHistory
    );

    // Call the function under test
    const result = await optimizeContext({
      llm: mockLLM,
      workspaceId: "ws123",
      userId: "user1",
      threadId: "thread1",
      sessionId: "session1",
      systemPrompt: mockSystemPrompt,
      userPrompt: mockUserPrompt,
      documentManager: mockDocumentManager,
      settings: mockSettings,
      maxTokens: 128000,
    });

    expect(result).toBeDefined();
    // Verify that documents were included in the result
    expect(result.pinnedDocs.length).toBeGreaterThan(0);
    expect(result.pdrDocs.length).toBeGreaterThan(0);
    // Verify token counts (should be closer to context window max)
    expect(result.totalTokens).toBeGreaterThan(20000);
  });

  /*
   * Test 4: Test edge case with very small context window percentage
   */
  it("should handle small context window percentage without triggering cannonballing", async () => {
    // Configure mocks with realistic data
    const mockSystemPrompt =
      "You are a legal research assistant tasked with providing concise answers.".repeat(
        5
      );
    const mockUserPrompt =
      "Summarize the key points of contract termination clauses and their enforceability.".repeat(
        8
      );

    // Create a minimal chat history
    const mockChatHistory: AIProviderChatMessage[] = [
      {
        role: "user",
        content: "I need information about contract termination.".repeat(3),
      },
      {
        role: "assistant",
        content:
          "I'd be happy to explain contract termination clauses and their implications.".repeat(
            5
          ),
      },
    ];

    // Setup WorkspaceChats mock to return our chat history
    require("../../../models/workspaceChats").WorkspaceChats.where.mockResolvedValue(
      mockChatHistory
    );

    // Create small attachments (only 5% of context window)
    const smallAttachment: TestAttachment = {
      id: "small_att",
      name: "Contract Clause Summary",
      type: "text",
      content:
        "This document provides a brief overview of termination clauses.".repeat(
          100
        ),
      tokens: 6400, // 5% of 128k
    };

    // Create small vector chunks (total 12,800 tokens - 10% of context window)
    const smallVectors: TestChunk[] = Array(16)
      .fill(null)
      .map((_, i) => ({
        id: `small_vec_${i}`,
        content:
          "Brief information about contract termination precedents.".repeat(20),
        tokens: 800,
      }));

    // Configure mock document manager to return our test documents
    (mockDocumentManager.pinnedDocs as jest.Mock).mockResolvedValue([
      toDocumentData(smallAttachment),
    ]);
    (
      mockDocumentManager.pdrDocs as jest.MockedFunction<
        typeof mockDocumentManager.pdrDocs
      >
    ).mockResolvedValue(smallVectors.map(toDocumentData));

    // Mock settings to use small percentage of context window
    const smallSettings: ContextWindowSettings = {
      ...mockSettings,
      contextWindowPercentage: 20,
    };

    // Create LLM connector with 128k context window
    const mockLLM = createMockLLMConnector(128000);

    // Explicitly call compressMessages to ensure token calculation happens
    await mockLLM.compressMessages(
      {
        systemPrompt: mockSystemPrompt,
        userPrompt: mockUserPrompt,
      },
      mockChatHistory
    );

    // Call the function under test
    const result = await optimizeContext({
      llm: mockLLM,
      workspaceId: "ws123",
      userId: "user1",
      threadId: "thread1",
      sessionId: "session1",
      systemPrompt: mockSystemPrompt,
      userPrompt: mockUserPrompt,
      documentManager: mockDocumentManager,
      settings: smallSettings,
      maxTokens: 128000,
    });

    expect(result).toBeDefined();
    // Verify that documents were included in the result
    expect(result.pinnedDocs.length).toBeGreaterThan(0);
    expect(result.pdrDocs.length).toBeGreaterThan(0);
    // Verify token counts
    expect(result.totalTokens).toBeGreaterThan(5000);
  });

  /*
   * Test 5: Test with attachments of exactly 10% context window
   */
  it("should handle attachments of exactly 10% context window", async () => {
    // Configure mocks with realistic data
    const mockSystemPrompt =
      "You are a legal assistant providing information based on provided documents.".repeat(
        5
      );
    const mockUserPrompt =
      "What are the main considerations in corporate merger agreements?".repeat(
        10
      );

    // Create a minimal chat history
    const mockChatHistory: AIProviderChatMessage[] = [
      {
        role: "user",
        content: "Can you help me understand merger agreements?".repeat(3),
      },
      {
        role: "assistant",
        content:
          "Certainly! Merger agreements are complex legal documents that outline the terms of a corporate merger.".repeat(
            10
          ),
      },
    ];

    // Setup WorkspaceChats mock to return our chat history
    require("../../../models/workspaceChats").WorkspaceChats.where.mockResolvedValue(
      mockChatHistory
    );

    // Create attachment that's exactly 10% of context window
    const preciseAttachment: TestAttachment = {
      id: "precise_att",
      name: "Merger Agreement Template",
      type: "text",
      content:
        "This merger agreement template covers all standard clauses and considerations...".repeat(
          640
        ),
      tokens: 12800, // Exactly 10% of 128k
    };

    // Create vector chunks totaling 25% of context window
    const mediumVectors: TestChunk[] = Array(16)
      .fill(null)
      .map((_, i) => ({
        id: `medium_vec_${i}`,
        content:
          "Information about corporate merger precedents and legal implications.".repeat(
            50
          ),
        tokens: 2000,
      }));

    // Configure mock document manager to return our test documents
    (mockDocumentManager.pinnedDocs as jest.Mock).mockResolvedValue([
      toDocumentData(preciseAttachment),
    ]);
    (
      mockDocumentManager.pdrDocs as jest.MockedFunction<
        typeof mockDocumentManager.pdrDocs
      >
    ).mockResolvedValue(mediumVectors.map(toDocumentData));

    // Create LLM connector with 128k context window
    const mockLLM = createMockLLMConnector(128000);

    // Explicitly call compressMessages to ensure token calculation happens
    await mockLLM.compressMessages(
      {
        systemPrompt: mockSystemPrompt,
        userPrompt: mockUserPrompt,
      },
      mockChatHistory
    );

    // Call the function under test
    const result = await optimizeContext({
      llm: mockLLM,
      workspaceId: "ws123",
      userId: "user1",
      threadId: "thread1",
      sessionId: "session1",
      systemPrompt: mockSystemPrompt,
      userPrompt: mockUserPrompt,
      documentManager: mockDocumentManager,
      settings: mockSettings,
      maxTokens: 128000,
    });

    expect(result).toBeDefined();
    // Verify that documents were included in the result
    expect(result.pinnedDocs.length).toBeGreaterThan(0);
    expect(result.pdrDocs.length).toBeGreaterThan(0);
    // Verify token counts
    expect(result.totalTokens).toBeGreaterThan(12000);
  });

  /*
   * Test 6: Test with extreme document loads (real-world intensive token usage)
   */
  it("should efficiently manage context with large vector chunks and extensive PDR content", async () => {
    // Create a specialized legal assistant system prompt
    const mockSystemPrompt =
      "You are a specialized legal assistant focused on intellectual property law, particularly patent litigation.".repeat(
        2
      );

    // Create a specialized user prompt about semiconductor patents
    const mockUserPrompt =
      "Please analyze recent patent infringement cases related to semiconductor manufacturing technology.".repeat(
        2
      );

    // Create substantial chat history to simulate real usage
    const mockChatHistory: AIProviderChatMessage[] = [
      {
        role: "user",
        content:
          "Could you explain how patent claims for semiconductor manufacturing processes are typically structured?",
      },
      {
        role: "assistant",
        content:
          "Semiconductor manufacturing patent claims typically follow a structure that includes apparatus claims, method claims, and product-by-process claims.",
      },
    ];

    // Setup WorkspaceChats mock to return our chat history
    require("../../../models/workspaceChats").WorkspaceChats.where.mockResolvedValue(
      mockChatHistory
    );

    // Create large vector chunks with moderate token counts
    const largeVectorChunks: TestChunk[] = Array(10)
      .fill(null)
      .map((_, i) => ({
        id: `vector_${i}`,
        content:
          `This is a technical analysis of semiconductor patent case ${i}`.repeat(
            20
          ),
        tokens: 1000,
        relevance: 1 - i * 0.1, // Decreasing relevance
      }));

    // Create PDR documents with reasonable token counts
    // Total: 5K tokens per doc × 10 docs = 50K tokens
    const largePdrDocs: TestChunk[] = Array(10)
      .fill(null)
      .map((_, i) => ({
        id: `pdr_${i}`,
        content:
          `This legal brief contains case history and technical expert testimony for patent case ${i}.`.repeat(
            50
          ),
        tokens: 5000,
        sourceId: `vector_${i}`, // Each PDR doc is linked to a vector chunk
      }));

    // Create a few large attachments (10K tokens each)
    const hugeAttachments: TestAttachment[] = Array(3)
      .fill(null)
      .map((_, i) => ({
        id: `attachment_${i}`,
        name: `Patent Documentation ${i}`,
        type: "text",
        content:
          `This document contains the full patent specification for US Patent ${10000000 + i}.`.repeat(
            100
          ),
        tokens: 10000,
      }));

    // Configure mock document manager to return our test documents
    (
      mockDocumentManager.pinnedDocs as jest.MockedFunction<
        typeof mockDocumentManager.pinnedDocs
      >
    ).mockResolvedValue(hugeAttachments.map(toDocumentData));
    (mockDocumentManager.pdrDocs as jest.Mock).mockResolvedValue([
      ...largeVectorChunks.map(toDocumentData),
      ...largePdrDocs.map(toDocumentData),
    ]);

    // Create LLM connector with 128k context window (a typical size)
    const mockLLM = createMockLLMConnector(128000);

    // Create settings with very small context window percentage to force cannonballing
    const restrictedSettings: ContextWindowSettings = {
      ...mockSettings,
      contextWindowPercentage: 20, // Only 20% of context window available (to force cannonballing)
      pinnedDocsPercent: 0.2, // Adjust percentages for the smaller window
      chatHistoryPercent: 0.3,
      pdrDocsPercent: 0.5,
    };

    // Explicitly call compressMessages to ensure token calculation happens
    await mockLLM.compressMessages(
      {
        systemPrompt: mockSystemPrompt,
        userPrompt: mockUserPrompt,
      },
      mockChatHistory
    );

    // Call the function under test
    const result = await optimizeContext({
      llm: mockLLM,
      workspaceId: "ws123",
      userId: "user1",
      threadId: "thread1",
      sessionId: "session1",
      systemPrompt: mockSystemPrompt,
      userPrompt: mockUserPrompt,
      documentManager: mockDocumentManager,
      settings: restrictedSettings,
      maxTokens: 128000,
    });

    // Log detailed statistics about optimization
    const originalDocumentCount =
      hugeAttachments.length + largeVectorChunks.length + largePdrDocs.length;
    const optimizedDocumentCount =
      result.pinnedDocs.length + result.pdrDocs.length;
    const chatHistoryRetained =
      (result.chatHistory.length / mockChatHistory.length) * 100;
    const pdrDocsRetained =
      (result.pdrDocs.length /
        (largeVectorChunks.length + largePdrDocs.length)) *
      100;

    console.log("\n\n===== OPTIMIZATION STATS =====");
    console.log(`Original Document Count: ${originalDocumentCount}`);
    console.log(`Optimized Document Count: ${optimizedDocumentCount}`);
    console.log(`Chat History Retained: ${chatHistoryRetained.toFixed(2)}%`);
    console.log(`PDR Docs Retained: ${pdrDocsRetained.toFixed(2)}%`);
    console.log(`Total Optimized Tokens: ${result.totalTokens}`);
    console.log(`Context Window Size: ${128000}`);
    console.log(`Effective Context Window (20%): ${128000 * 0.2}`);
    console.log(`Cannonballed: ${result.cannonballed}`);

    // Add detailed logs for each document type
    console.log("\n----- Document Token Details -----");
    console.log(`System Prompt Tokens: ${mockSystemPrompt.length / 4}`);
    console.log(`User Prompt Tokens: ${mockUserPrompt.length / 4}`);

    if (result.pinnedDocs.length > 0) {
      console.log("\nPinned Documents:");
      result.pinnedDocs.forEach((doc, i) => {
        console.log(`  Doc ${i}: ${doc.id} - ${doc.tokens} tokens`);
      });
    }

    if (result.pdrDocs.length > 0) {
      console.log("\nPDR Documents:");
      result.pdrDocs.forEach((doc, i) => {
        console.log(
          `  Doc ${i}: ${doc.id} - ${doc.tokens} tokens ${doc.relevance ? `(relevance: ${doc.relevance})` : ""}`
        );
      });
    }

    console.log("\nChat History:");
    result.chatHistory.forEach((msg, i) => {
      console.log(
        `  Message ${i}: ${msg.role} - ${msg.tokens || "unknown"} tokens`
      );
    });
    console.log("=====================================\n\n");

    expect(result).toBeDefined();

    // Verify that documents were included in the result but properly limited
    expect(optimizedDocumentCount).toBeLessThan(originalDocumentCount);

    // The total should be close to the effective context window size (20% of 128K)
    expect(result.totalTokens).toBeGreaterThan(1000);

    // Calculate the effective context window size
    const effectiveContextWindowSize = 128000 * 0.2;

    // NEW: When source documents exceed context window, we should use at least 80% of available context
    const totalSourceTokens =
      hugeAttachments.reduce((sum, doc) => sum + doc.tokens, 0) +
      largeVectorChunks.reduce((sum, doc) => sum + doc.tokens, 0) +
      largePdrDocs.reduce((sum, doc) => sum + doc.tokens, 0);

    if (totalSourceTokens > effectiveContextWindowSize) {
      const minimumExpectedTokens = effectiveContextWindowSize * 0.8;
      console.log(
        `\nSource documents (${totalSourceTokens} tokens) exceed effective context window (${effectiveContextWindowSize} tokens)`
      );
      console.log(
        `Expecting at least ${minimumExpectedTokens} tokens to be used (80% of effective context window)`
      );
      console.log(
        `Actual tokens used: ${result.totalTokens} (${((result.totalTokens / effectiveContextWindowSize) * 100).toFixed(2)}% of effective context window)`
      );

      // Print a summary of docs in the final result for visibility
      console.log(`\n--- FINAL DOCUMENT BREAKDOWN ---`);
      console.log(`System tokens: ${mockSystemPrompt.length / 4}`);
      console.log(`User tokens: ${mockUserPrompt.length / 4}`);
      console.log(
        `Pinned docs count: ${result.pinnedDocs.length} (total ${result.pinnedDocs.reduce((sum, doc) => sum + doc.tokens, 0)} tokens)`
      );
      console.log(
        `PDR docs count: ${result.pdrDocs.length} (total ${result.pdrDocs.reduce((sum, doc) => sum + doc.tokens, 0)} tokens)`
      );
      console.log(
        `Chat history tokens: ${result.chatHistory.reduce((sum, msg) => sum + ((msg as { tokens?: number }).tokens || 0), 0)}`
      );
      console.log(`Total tokens: ${result.totalTokens}`);
      console.log(
        `Context utilization: ${((result.totalTokens / effectiveContextWindowSize) * 100).toFixed(2)}%`
      );

      expect(result.totalTokens).toBeGreaterThanOrEqual(minimumExpectedTokens);
    }

    expect(result.totalTokens).toBeLessThanOrEqual(effectiveContextWindowSize);

    // With the restricted context window, cannonballing should be triggered
    expect(result.cannonballed).toBe(true);

    // Verify that the most relevant documents were kept
    if (result.pdrDocs.length > 0) {
      // Check if the most relevant docs (lowest index) were retained
      const highestPdrIndex = Math.max(
        ...result.pdrDocs
          .filter((doc) => {
            const docWithId = doc as unknown as { id: string };
            return (
              docWithId.id &&
              typeof docWithId.id === "string" &&
              docWithId.id.startsWith("pdr_")
            );
          })
          .map((doc) =>
            parseInt((doc as unknown as { id: string }).id.split("_")[1])
          )
      );

      // The optimization should prefer more relevant documents (lower indexes)
      expect(highestPdrIndex).toBeLessThan(5);
    }
  });

  // New tests specifically for context window percentage calculation fix
  describe("Context Window Percentage Calculation", () => {
    it("should correctly calculate effective context window with 70% setting", async () => {
      const mockSettings: ContextWindowSettings = {
        contextWindowPercentage: 70,
        pinnedDocsPercent: 0.1,
        chatHistoryPercent: 0.5,
        pdrDocsPercent: 0.4,
      };

      const mockLLM = createMockLLMProvider(100000, "test-model");

      const result = await optimizeContext({
        llm: mockLLM,
        workspaceId: "test-ws",
        userId: "test-user",
        threadId: "test-thread",
        sessionId: "test-session",
        systemPrompt: "Test system prompt",
        userPrompt: "Test user prompt",
        documentManager: mockDocumentManager,
        settings: mockSettings,
        maxTokens: 100000,
      });

      expect(result).toBeDefined();
      // With 70% of 100000 = 70000 effective tokens
      // The result should fit within this limit
      expect(result.totalTokens).toBeLessThanOrEqual(70000);
    });

    it("should correctly calculate effective context window with 50% setting", async () => {
      const mockSettings: ContextWindowSettings = {
        contextWindowPercentage: 50,
        pinnedDocsPercent: 0.1,
        chatHistoryPercent: 0.5,
        pdrDocsPercent: 0.4,
      };

      const mockLLM = createMockLLMProvider(100000, "test-model");

      const result = await optimizeContext({
        llm: mockLLM,
        workspaceId: "test-ws",
        userId: "test-user",
        threadId: "test-thread",
        sessionId: "test-session",
        systemPrompt: "Test system prompt",
        userPrompt: "Test user prompt",
        documentManager: mockDocumentManager,
        settings: mockSettings,
        maxTokens: 100000,
      });

      expect(result).toBeDefined();
      // With 50% of 100000 = 50000 effective tokens
      expect(result.totalTokens).toBeLessThanOrEqual(50000);
    });

    it("should use default 90% when contextWindowPercentage is undefined", async () => {
      const mockSettings: ContextWindowSettings = {
        // contextWindowPercentage is intentionally undefined
        pinnedDocsPercent: 0.1,
        chatHistoryPercent: 0.5,
        pdrDocsPercent: 0.4,
      };

      const mockLLM = createMockLLMProvider(100000, "test-model");

      const result = await optimizeContext({
        llm: mockLLM,
        workspaceId: "test-ws",
        userId: "test-user",
        threadId: "test-thread",
        sessionId: "test-session",
        systemPrompt: "Test system prompt",
        userPrompt: "Test user prompt",
        documentManager: mockDocumentManager,
        settings: mockSettings,
        maxTokens: 100000,
      });

      expect(result).toBeDefined();
      // With default 90% of 100000 = 90000 effective tokens
      expect(result.totalTokens).toBeLessThanOrEqual(90000);
    });

    it("should handle 0% contextWindowPercentage correctly", async () => {
      const mockSettings: ContextWindowSettings = {
        contextWindowPercentage: 0,
        pinnedDocsPercent: 0.1,
        chatHistoryPercent: 0.5,
        pdrDocsPercent: 0.4,
      };

      const mockLLM = createMockLLMProvider(100000, "test-model");

      const result = await optimizeContext({
        llm: mockLLM,
        workspaceId: "test-ws",
        userId: "test-user",
        threadId: "test-thread",
        sessionId: "test-session",
        systemPrompt: "Test system prompt",
        userPrompt: "Test user prompt",
        documentManager: mockDocumentManager,
        settings: mockSettings,
        maxTokens: 100000,
      });

      expect(result).toBeDefined();
      // With 0% of 100000 = 0 effective tokens, should only include required prompts
      expect(result.totalTokens).toBeGreaterThan(0); // Still has system + user prompts
      expect(result.pinnedDocs).toHaveLength(0);
      expect(result.pdrDocs).toHaveLength(0);
      expect(result.chatHistory).toHaveLength(0);
    });

    it("should handle 100% contextWindowPercentage correctly", async () => {
      const mockSettings: ContextWindowSettings = {
        contextWindowPercentage: 100,
        pinnedDocsPercent: 0.1,
        chatHistoryPercent: 0.5,
        pdrDocsPercent: 0.4,
      };

      const mockLLM = createMockLLMProvider(100000, "test-model");

      const result = await optimizeContext({
        llm: mockLLM,
        workspaceId: "test-ws",
        userId: "test-user",
        threadId: "test-thread",
        sessionId: "test-session",
        systemPrompt: "Test system prompt",
        userPrompt: "Test user prompt",
        documentManager: mockDocumentManager,
        settings: mockSettings,
        maxTokens: 100000,
      });

      expect(result).toBeDefined();
      // With 100% of 100000 = 100000 effective tokens
      expect(result.totalTokens).toBeLessThanOrEqual(100000);
    });

    it("should correctly convert percentage values to decimals", async () => {
      // Test that the calculation (contextWindowPercentage / 100) works correctly
      const testCases = [
        { percentage: 10, expected: 0.1 },
        { percentage: 25, expected: 0.25 },
        { percentage: 50, expected: 0.5 },
        { percentage: 75, expected: 0.75 },
        { percentage: 90, expected: 0.9 },
        { percentage: 100, expected: 1.0 },
      ];

      for (const testCase of testCases) {
        const mockSettings: ContextWindowSettings = {
          contextWindowPercentage: testCase.percentage,
          pinnedDocsPercent: 0.1,
          chatHistoryPercent: 0.5,
          pdrDocsPercent: 0.4,
        };

        const mockLLM = createMockLLMProvider(100000, "test-model");
        const contextWindowSize = 100000;
        const expectedEffectiveWindow = Math.floor(
          contextWindowSize * testCase.expected
        );

        const result = await optimizeContext({
          llm: mockLLM,
          workspaceId: "test-ws",
          userId: "test-user",
          threadId: "test-thread",
          sessionId: "test-session",
          systemPrompt: "Test",
          userPrompt: "Test",
          documentManager: mockDocumentManager,
          settings: mockSettings,
          maxTokens: contextWindowSize,
        });

        expect(result).toBeDefined();
        // The total tokens should not exceed the calculated effective window
        expect(result.totalTokens).toBeLessThanOrEqual(expectedEffectiveWindow);
      }
    });

    it("should handle edge case with very small context windows", async () => {
      const mockSettings: ContextWindowSettings = {
        contextWindowPercentage: 70,
        pinnedDocsPercent: 0.1,
        chatHistoryPercent: 0.5,
        pdrDocsPercent: 0.4,
      };

      // Very small context window
      const mockLLM = createMockLLMProvider(1000, "test-model");

      const result = await optimizeContext({
        llm: mockLLM,
        workspaceId: "test-ws",
        userId: "test-user",
        threadId: "test-thread",
        sessionId: "test-session",
        systemPrompt: "Test system prompt that is somewhat long",
        userPrompt: "Test user prompt that is also somewhat long",
        documentManager: mockDocumentManager,
        settings: mockSettings,
        maxTokens: 1000,
      });

      expect(result).toBeDefined();
      // With 70% of 1000 = 700 effective tokens
      expect(result.totalTokens).toBeLessThanOrEqual(700);
      // Should handle gracefully even with very limited space
      expect(result.totalTokens).toBeGreaterThan(0);
    });
  });
});
