/**
 * Context Window Management
 *
 * This module is intended for use with the Unit Test of Context Window handling.
 *
 * This module provides functions to optimize context window usage
 * by implementing strategies like:
 * 1. Cannonballing - truncating content to fit context window
 * 2. Token allocation - distributing token budget across different content types
 * 3. Prioritizing - ranking content by relevance and keeping what fits
 */

import { TokenManager } from "../helpers/tiktoken";
import { DocumentManager } from "../DocumentManager";
import { WorkspaceChats } from "../../models/workspaceChats";
import { workspace_chats } from "@prisma/client";

import { LL<PERSON>rovider } from "../../types/ai-providers";

interface LogData {
  component: string;
  phase: string;
  [key: string]: unknown;
}

interface OptimizeContextParams {
  llm: LLMProvider & { contextWindowSize: number };
  workspaceId: string;
  userId: string;
  threadId: string;
  sessionId: string;
  systemPrompt: string;
  userPrompt: string;
  documentManager: DocumentManager;
  settings: {
    contextWindowPercentage?: number;
    pinnedDocsPercent?: number;
    chatHistoryPercent?: number;
    pdrDocsPercent?: number;
  };
  maxTokens?: number | null;
}

interface OptimizedItem {
  id: number;
  content?: string;
  prompt?: string;
  response?: string;
  tokens: number;
  relevance?: number;
  truncated?: boolean;
  [key: string]: unknown;
}

interface OptimizedContext {
  systemPrompt: string;
  userPrompt: string;
  chatHistory: OptimizedItem[];
  pinnedDocs: OptimizedItem[];
  pdrDocs: OptimizedItem[];
  totalTokens: number;
  cannonballed: boolean;
}

// Create a logger function that ensures output appears in server console
const logTokenInfo = (
  phase: string,
  data: Omit<LogData, "component" | "phase">
): void => {
  const logData: LogData = {
    component: "ContextWindow",
    phase,
    ...data,
  };
  console.log(`[TOKEN INFO] ${JSON.stringify(logData)}`);
};

/**
 * Optimizes context window usage by distributing token budget across different content types
 */
async function optimizeContext({
  llm,
  workspaceId,
  userId,
  threadId,
  sessionId,
  systemPrompt,
  userPrompt,
  documentManager,
  settings,
  maxTokens = null,
}: OptimizeContextParams): Promise<OptimizedContext> {
  // Configure context window size
  const contextWindowSize = maxTokens || llm.contextWindowSize;
  const effectiveContextWindow = Math.floor(
    contextWindowSize * ((settings?.contextWindowPercentage ?? 90) / 100)
  );

  // Get token manager for this model
  const tokenManager = new TokenManager(llm.model);

  // Calculate tokens for system and user prompts (these are required and can't be truncated)
  const systemPromptTokens = tokenManager.countFromString(systemPrompt || "");
  const userPromptTokens = tokenManager.countFromString(userPrompt || "");
  const requiredTokens = systemPromptTokens + userPromptTokens;

  // Log initial token information
  logTokenInfo("initial", {
    systemTokens: systemPromptTokens,
    userTokens: userPromptTokens,
    requiredTokens,
    contextWindowSize,
    effectiveContextWindow,
    workspaceId,
    threadId,
    sessionId,
  });

  // Check if we need to cannonball (only system+user already exceed context window)
  let needsCannonballing = requiredTokens > effectiveContextWindow;

  // Get recent chat history
  const rawChatHistory: workspace_chats[] = await WorkspaceChats.where({
    workspaceId,
    user_id: userId,
    thread_id: threadId,
    api_session_id: sessionId,
  });

  // Count tokens for chat history
  let chatHistoryTokens = 0;
  const chatHistory: OptimizedItem[] = rawChatHistory.map(
    (msg: workspace_chats, index: number) => {
      const promptStr = String(msg.prompt || "");
      const responseStr = String(msg.response || "");
      const tokens =
        tokenManager.countFromString(promptStr) +
        tokenManager.countFromString(responseStr);
      chatHistoryTokens += tokens;
      return {
        id: msg.id || index,
        content: `${promptStr}\n${responseStr}`,
        prompt: promptStr,
        response: responseStr,
        tokens,
        workspaceId: msg.workspaceId,
        user_id: msg.user_id,
        thread_id: msg.thread_id,
        api_session_id: msg.api_session_id,
        include: msg.include,
        feedbackScore: msg.feedbackScore,
        createdAt: msg.createdAt,
        lastUpdatedAt: msg.lastUpdatedAt,
      };
    }
  );

  // Log chat history token information
  logTokenInfo("chatHistory", {
    systemTokens: systemPromptTokens,
    userTokens: userPromptTokens,
    historyTokens: chatHistoryTokens,
    totalTokens: requiredTokens + chatHistoryTokens,
    historyMessageCount: chatHistory.length,
    contextWindowSize,
    didCannonball: needsCannonballing,
    workspaceId,
    threadId,
    sessionId,
  });

  // Retrieve pinned documents
  const pinnedDocs = await documentManager.pinnedDocs();
  let pinnedDocsTokens = 0;

  // Define proper interface for document data
  interface DocumentData {
    id?: string | number;
    token_count_estimate?: number;
    tokens?: number;
    pageContent?: string;
    content?: string;
    [key: string]: unknown;
  }

  // Count tokens for pinned docs
  (pinnedDocs as DocumentData[]).forEach((doc: DocumentData) => {
    // Use existing token count if available, otherwise calculate
    if (
      doc.token_count_estimate &&
      typeof doc.token_count_estimate === "number"
    ) {
      doc.tokens = doc.token_count_estimate;
      pinnedDocsTokens += doc.token_count_estimate;
    } else if (doc.tokens && typeof doc.tokens === "number") {
      pinnedDocsTokens += doc.tokens;
    } else {
      const contentStr = String(doc.pageContent || doc.content || "");
      const tokens = tokenManager.countFromString(contentStr);
      doc.tokens = tokens;
      pinnedDocsTokens += tokens;
    }
  });

  // Retrieve PDR documents if needed
  const pdrDocs = await documentManager.pdrDocs();
  let pdrDocsTokens = 0;

  // Count tokens for PDR docs
  (pdrDocs as DocumentData[]).forEach((doc: DocumentData) => {
    // Use existing token count if available, otherwise calculate
    if (
      doc.token_count_estimate &&
      typeof doc.token_count_estimate === "number"
    ) {
      doc.tokens = doc.token_count_estimate;
      pdrDocsTokens += doc.token_count_estimate;
    } else if (doc.tokens && typeof doc.tokens === "number") {
      pdrDocsTokens += doc.tokens;
    } else {
      const contentStr = String(doc.pageContent || doc.content || "");
      const tokens = tokenManager.countFromString(contentStr);
      doc.tokens = tokens;
      pdrDocsTokens += tokens;
    }
  });

  // Log total tokens before optimization
  const totalTokensBeforeOptimization =
    requiredTokens + chatHistoryTokens + pinnedDocsTokens + pdrDocsTokens;

  logTokenInfo("preOptimization", {
    systemTokens: systemPromptTokens,
    userTokens: userPromptTokens,
    historyTokens: chatHistoryTokens,
    pinnedDocsTokens,
    pdrDocsTokens,
    pinnedDocCount: pinnedDocs.length,
    pdrDocCount: pdrDocs.length,
    totalTokens: totalTokensBeforeOptimization,
    contextWindowSize,
    effectiveContextWindow,
    didCannonball: needsCannonballing,
    workspaceId,
    threadId,
    sessionId,
  });

  // Check if we need to cannonball based on total tokens
  needsCannonballing = totalTokensBeforeOptimization > effectiveContextWindow;

  // Calculate available tokens for optional content
  const availableTokens = Math.max(0, effectiveContextWindow - requiredTokens);

  // Target percentages for different content types
  const pinnedDocsTargetPercent = (settings?.pinnedDocsPercent ?? false) || 0.1;
  const chatHistoryTargetPercent =
    (settings?.chatHistoryPercent ?? false) || 0.5;
  const pdrDocsTargetPercent = (settings?.pdrDocsPercent ?? false) || 0.4;

  // Allocate tokens based on target percentages
  const pinnedDocsAllocation = Math.floor(
    availableTokens * pinnedDocsTargetPercent
  );
  const chatHistoryAllocation = Math.floor(
    availableTokens * chatHistoryTargetPercent
  );
  const pdrDocsAllocation = Math.floor(availableTokens * pdrDocsTargetPercent);

  logTokenInfo("allocation", {
    availableTokens,
    pinnedDocsAllocation,
    chatHistoryAllocation,
    pdrDocsAllocation,
    pinnedDocsTargetPercent,
    chatHistoryTargetPercent,
    pdrDocsTargetPercent,
    workspaceId,
    threadId,
    sessionId,
  });

  // Convert documents to OptimizedItem format
  const pinnedDocsAsOptimized: OptimizedItem[] = (
    pinnedDocs as DocumentData[]
  ).map((doc: DocumentData, index: number) => {
    const docId =
      typeof doc.id === "number"
        ? doc.id
        : typeof doc.id === "string"
          ? parseInt(doc.id, 10)
          : index;
    return {
      ...doc,
      id: docId,
      content: String(doc.pageContent || doc.content || ""),
      tokens: doc.tokens || 0,
    };
  });

  const pdrDocsAsOptimized: OptimizedItem[] = (pdrDocs as DocumentData[]).map(
    (doc: DocumentData, index: number) => {
      const docId =
        typeof doc.id === "number"
          ? doc.id
          : typeof doc.id === "string"
            ? parseInt(doc.id, 10)
            : index;
      return {
        ...doc,
        id: docId,
        content: String(doc.pageContent || doc.content || ""),
        tokens: doc.tokens || 0,
      };
    }
  );

  // Optimize each collection
  let optimizedPinnedDocs = optimizeCollection(
    pinnedDocsAsOptimized,
    pinnedDocsAllocation,
    tokenManager
  );
  let optimizedChatHistory = optimizeCollection(
    chatHistory,
    chatHistoryAllocation,
    tokenManager
  );
  let optimizedPdrDocs = optimizeCollection(
    pdrDocsAsOptimized,
    pdrDocsAllocation,
    tokenManager
  );

  // Calculate optimized token counts
  let optimizedPinnedDocsTokens = sumTokens(optimizedPinnedDocs);
  let optimizedChatHistoryTokens = sumTokens(optimizedChatHistory);
  let optimizedPdrDocsTokens = sumTokens(optimizedPdrDocs);

  let totalOptimizedTokens =
    requiredTokens +
    optimizedPinnedDocsTokens +
    optimizedChatHistoryTokens +
    optimizedPdrDocsTokens;

  // NEW: Efficiency check - if we're using less than 80% of the effective context window
  // despite having plenty of source documents, redistribute the remaining tokens
  const minimumEfficiencyTarget = effectiveContextWindow * 0.8;

  if (
    totalOptimizedTokens < minimumEfficiencyTarget &&
    totalTokensBeforeOptimization > effectiveContextWindow
  ) {
    // We have room to include more content
    const remainingTokens = effectiveContextWindow - totalOptimizedTokens;

    logTokenInfo("efficiencyBoost", {
      currentUsage: totalOptimizedTokens,
      targetUsage: minimumEfficiencyTarget,
      remainingTokens,
      effectiveContextWindow,
      workspaceId,
      threadId,
      sessionId,
    });

    // Prioritize adding more PDR docs first (most relevant content)
    if (pdrDocs.length > optimizedPdrDocs.length) {
      const additionalPdrAllocation = pdrDocsAllocation + remainingTokens;
      const newPdrDocs = optimizeCollection(
        pdrDocsAsOptimized,
        additionalPdrAllocation,
        tokenManager
      );

      // Update optimized collections
      optimizedPdrDocs = newPdrDocs;
      optimizedPdrDocsTokens = sumTokens(optimizedPdrDocs);

      // Recalculate total tokens
      totalOptimizedTokens =
        requiredTokens +
        optimizedPinnedDocsTokens +
        optimizedChatHistoryTokens +
        optimizedPdrDocsTokens;

      logTokenInfo("pdrBoost", {
        newPdrTokens: optimizedPdrDocsTokens,
        newPdrCount: optimizedPdrDocs.length,
        newTotalTokens: totalOptimizedTokens,
        workspaceId,
        threadId,
        sessionId,
      });
    }
  }

  // Additional safeguard: if we're still over the limit after optimization,
  // perform a second pass of optimization with stricter limits
  if (totalOptimizedTokens > effectiveContextWindow) {
    logTokenInfo("secondPassRequired", {
      firstPassTokens: totalOptimizedTokens,
      excessTokens: totalOptimizedTokens - effectiveContextWindow,
      effectiveContextWindow,
      workspaceId,
      threadId,
      sessionId,
    });

    // We need to reduce further - focus on preserving more important content first
    // Calculate how much we need to reduce overall
    const excessTokens = totalOptimizedTokens - effectiveContextWindow;

    // Prioritize reduction strategy:
    // 1. First reduce PDR docs if they exist
    // 2. Then reduce pinned docs if needed
    // 3. Finally reduce chat history as a last resort

    let remainingExcess = excessTokens;

    // Phase 1: Reduce PDR docs
    if (optimizedPdrDocsTokens > 0 && remainingExcess > 0) {
      const newPdrAllocation = Math.max(
        0,
        optimizedPdrDocsTokens - remainingExcess
      );
      const newPdrDocs = optimizeCollection(
        optimizedPdrDocs,
        newPdrAllocation,
        tokenManager
      );

      const newPdrTokens = sumTokens(newPdrDocs);
      remainingExcess -= optimizedPdrDocsTokens - newPdrTokens;

      // Update the optimized collection
      optimizedPdrDocs = newPdrDocs;
      optimizedPdrDocsTokens = newPdrTokens;

      logTokenInfo("pdrReduction", {
        originalTokens: optimizedPdrDocsTokens,
        newTokens: newPdrTokens,
        tokensSaved: optimizedPdrDocsTokens - newPdrTokens,
        remainingExcess,
        workspaceId,
        threadId,
        sessionId,
      });
    }

    // Phase 2: Reduce pinned docs if still necessary
    if (optimizedPinnedDocsTokens > 0 && remainingExcess > 0) {
      const newPinnedAllocation = Math.max(
        0,
        optimizedPinnedDocsTokens - remainingExcess
      );
      const newPinnedDocs = optimizeCollection(
        optimizedPinnedDocs,
        newPinnedAllocation,
        tokenManager
      );

      const newPinnedTokens = sumTokens(newPinnedDocs);
      remainingExcess -= optimizedPinnedDocsTokens - newPinnedTokens;

      // Update the optimized collection
      optimizedPinnedDocs = newPinnedDocs;
      optimizedPinnedDocsTokens = newPinnedTokens;

      logTokenInfo("pinnedReduction", {
        originalTokens: optimizedPinnedDocsTokens,
        newTokens: newPinnedTokens,
        tokensSaved: optimizedPinnedDocsTokens - newPinnedTokens,
        remainingExcess,
        workspaceId,
        threadId,
        sessionId,
      });
    }

    // Phase 3: Reduce chat history if still necessary
    if (optimizedChatHistoryTokens > 0 && remainingExcess > 0) {
      const newChatAllocation = Math.max(
        0,
        optimizedChatHistoryTokens - remainingExcess
      );
      const newChatHistory = optimizeCollection(
        optimizedChatHistory,
        newChatAllocation,
        tokenManager
      );

      const newChatTokens = sumTokens(newChatHistory);
      remainingExcess -= optimizedChatHistoryTokens - newChatTokens;

      // Update the optimized collection
      optimizedChatHistory = newChatHistory;
      optimizedChatHistoryTokens = newChatTokens;

      logTokenInfo("chatHistoryReduction", {
        originalTokens: optimizedChatHistoryTokens,
        newTokens: newChatTokens,
        tokensSaved: optimizedChatHistoryTokens - newChatTokens,
        remainingExcess,
        workspaceId,
        threadId,
        sessionId,
      });
    }

    // Recalculate final token count
    totalOptimizedTokens =
      requiredTokens +
      optimizedPinnedDocsTokens +
      optimizedChatHistoryTokens +
      optimizedPdrDocsTokens;

    // Final safety check - if we're still over after optimization attempts,
    // force a reduction by truncating PDR docs first, then pinned docs
    if (totalOptimizedTokens > effectiveContextWindow) {
      logTokenInfo("emergencyReduction", {
        stillExcessTokens: totalOptimizedTokens - effectiveContextWindow,
        effectiveContextWindow,
        workspaceId,
        threadId,
        sessionId,
      });

      // Emergency reduction: clear all PDR docs if needed
      if (optimizedPdrDocsTokens > 0) {
        const excessAfterPdrClear =
          totalOptimizedTokens -
          optimizedPdrDocsTokens -
          effectiveContextWindow;

        if (excessAfterPdrClear <= 0) {
          // We can reduce PDR docs to fix the issue
          const tokensToKeep = optimizedPdrDocsTokens + excessAfterPdrClear;
          optimizedPdrDocs = optimizeCollection(
            optimizedPdrDocs,
            Math.max(0, tokensToKeep),
            tokenManager
          );
          optimizedPdrDocsTokens = sumTokens(optimizedPdrDocs);
        } else {
          // Clear all PDR docs and continue reduction
          optimizedPdrDocs = [];
          optimizedPdrDocsTokens = 0;

          // If we need to reduce pinned docs too
          if (optimizedPinnedDocsTokens > 0) {
            const excessAfterPinnedClear =
              totalOptimizedTokens -
              optimizedPdrDocsTokens -
              optimizedPinnedDocsTokens -
              effectiveContextWindow;

            if (excessAfterPinnedClear <= 0) {
              // We can reduce pinned docs to fix the issue
              const tokensToKeep =
                optimizedPinnedDocsTokens + excessAfterPinnedClear;
              optimizedPinnedDocs = optimizeCollection(
                optimizedPinnedDocs,
                Math.max(0, tokensToKeep),
                tokenManager
              );
              optimizedPinnedDocsTokens = sumTokens(optimizedPinnedDocs);
            } else {
              // Clear all pinned docs
              optimizedPinnedDocs = [];
              optimizedPinnedDocsTokens = 0;

              // Last resort: reduce chat history
              if (optimizedChatHistoryTokens > 0) {
                const chatTokensToKeep = Math.max(
                  0,
                  effectiveContextWindow - requiredTokens
                );
                optimizedChatHistory = optimizeCollection(
                  optimizedChatHistory,
                  chatTokensToKeep,
                  tokenManager
                );
                optimizedChatHistoryTokens = sumTokens(optimizedChatHistory);
              }
            }
          }
        }

        // Recalculate token count after emergency reduction
        totalOptimizedTokens =
          requiredTokens +
          optimizedPinnedDocsTokens +
          optimizedChatHistoryTokens +
          optimizedPdrDocsTokens;
      }
    }
  }

  // Log final token counts
  logTokenInfo("final", {
    systemTokens: systemPromptTokens,
    userTokens: userPromptTokens,
    historyTokens: optimizedChatHistoryTokens,
    pinnedDocsTokens: optimizedPinnedDocsTokens,
    pdrDocsTokens: optimizedPdrDocsTokens,
    optimizedHistoryCount: optimizedChatHistory.length,
    optimizedPinnedCount: optimizedPinnedDocs.length,
    optimizedPdrCount: optimizedPdrDocs.length,
    originalHistoryCount: chatHistory.length,
    originalPinnedCount: pinnedDocs.length,
    originalPdrCount: pdrDocs.length,
    totalTokens: totalOptimizedTokens,
    contextWindowSize,
    effectiveContextWindow,
    didCannonball: needsCannonballing,
    workspaceId,
    threadId,
    sessionId,
    tokenReduction: totalTokensBeforeOptimization - totalOptimizedTokens,
    reductionPercent:
      totalTokensBeforeOptimization > 0
        ? Math.round(
            (1 - totalOptimizedTokens / totalTokensBeforeOptimization) * 100
          )
        : 0,
  });

  // Return optimized context data
  return {
    systemPrompt,
    userPrompt,
    chatHistory: optimizedChatHistory,
    pinnedDocs: optimizedPinnedDocs,
    pdrDocs: optimizedPdrDocs,
    totalTokens: totalOptimizedTokens,
    cannonballed: needsCannonballing,
  };
}

/**
 * Helper function to optimize a collection of objects with token counts
 * to fit within a target token budget
 */
function optimizeCollection(
  collection: OptimizedItem[],
  targetTokens: number,
  tokenManager: TokenManager
): OptimizedItem[] {
  if (!collection || collection.length === 0) return [];

  // If target is zero or negative, return empty array
  if (targetTokens <= 0) return [];

  // Sort by most recent or highest relevance
  const sortedCollection = [...collection].sort((a, b) => {
    // If both have relevance scores, sort by relevance
    if (a.relevance !== undefined && b.relevance !== undefined) {
      return b.relevance - a.relevance;
    }

    // Otherwise sort by most recent (assuming higher IDs are newer)
    return b.id - a.id;
  });

  // Keep adding items until we reach the target token budget
  const optimized: OptimizedItem[] = [];
  let tokenCount = 0;

  for (const item of sortedCollection) {
    // Skip items with no token count
    if (!item.tokens) continue;

    if (tokenCount + item.tokens <= targetTokens) {
      // We can fit the entire item
      optimized.push(item);
      tokenCount += item.tokens;
    } else if (tokenCount < targetTokens && "content" in item) {
      // We can fit part of the item - try to truncate content
      const remainingTokens = targetTokens - tokenCount;
      // MODIFIED: Lower the minimum token threshold to allow more aggressive truncation
      // The original threshold was 100 tokens, which is too high for large documents
      if (remainingTokens >= 50) {
        // Only truncate if we have reasonable space left
        // Create a truncated version of the item
        const truncatedItem = { ...item };

        // Calculate roughly how much text we can keep
        // This is approximate - for a more precise approach, would need to truncate token by token
        const keepRatio = remainingTokens / item.tokens;
        const textLength = item.content!.length;
        const approxKeepLength = Math.floor(textLength * keepRatio);

        // Truncate content and update token count
        truncatedItem.content =
          item.content!.substring(0, approxKeepLength) +
          "... [content truncated to fit context window]";
        truncatedItem.tokens = tokenManager.countFromString(
          truncatedItem.content
        );
        truncatedItem.truncated = true;

        optimized.push(truncatedItem);
        tokenCount += truncatedItem.tokens;
      }
      // Break after attempting to fit a truncated item
      break;
    } else {
      // Can't fit more items
      break;
    }
  }

  return optimized;
}

/**
 * Helper function to sum up tokens in a collection
 */
function sumTokens(collection: OptimizedItem[]): number {
  return collection.reduce((sum, item) => sum + (item.tokens || 0), 0);
}

export { optimizeContext };
