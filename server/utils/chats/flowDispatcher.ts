/**
 * Flow Dispatcher for Document Drafting
 *
 * This module provides a unified entry point for different document drafting flows:
 * - "main": The main document flow (either with a main document or adapting if none is provided)
 * - "noMain": The explicit no-main-document flow
 * - "referenceFiles": The reference files comparison flow
 *
 * Uses the modular flow system for all document drafting operations.
 */

import { Request, Response } from "express";
import { FlowOrchestrator } from "./flows/core/FlowOrchestrator";
import { mainDocFlowConfig } from "./flows/configurations/mainDocFlowConfig";
import { noMainDocFlowConfig } from "./flows/configurations/noMainDocFlowConfig";
import { referenceFlowConfig } from "./flows/configurations/referenceFlowConfig";
import { purgeDocumentBuilder } from "../files";
import {
  WorkspaceData,
  UserData,
  FlowConfig,
  FlowOptions as FlowOrchestratorOptions,
} from "../../types/chat-flow";
// Only import FlowConfig once, and do not import FlowConfiguration from chat-agent
// AbortSignal import not used due to type conflicts
// import { AbortSignal as AbortControllerSignal } from "abort-controller";

// Additional type definitions for chat flow data
export interface ThreadData {
  id: string;
  name?: string;
  [key: string]: unknown;
}

export interface AttachmentData {
  id: string;
  filename: string;
  type?: string;
  content?: string;
  [key: string]: unknown;
}

// Flow type definitions
export type FlowType = "main" | "noMain" | "referenceFiles";

export interface LegalTask {
  flowType?: FlowType;
  id?: string;
  name?: string;
  description?: string;
  configuration?: Record<string, unknown>;
}

export interface FlowOptions {
  flowType?: FlowType;
  request: Request;
  response: Response;
  workspace: WorkspaceData; // Properly typed workspace
  message: string;
  chatMode?: string;
  user?: UserData; // Properly typed user
  thread?: ThreadData; // Properly typed thread
  attachments?: AttachmentData[];
  chatId?: string;
  isCanvasChat?: boolean;
  preventChatCreation?: boolean;
  settings_suffix?: string;
  invoice_ref?: string;
  vectorSearchMode?: string;
  hasUploadedFile?: boolean;
  displayMessage?: string | null;
  useDeepSearch?: boolean;
  cdbOptions?: [string?, string?, string?, FlowType?, AttachmentData[]?]; // [legalPrompt, customInstructions, mainDocNameFromOptions, flowType, referenceFiles]
  mainDocName?: string | null;
  legalTask?: LegalTask | null;
  abortSignal?: AbortSignal | null;
}

export interface FlowRunnerOptions extends FlowOptions {
  mainDocName: string | null;
  legalTask: LegalTask | null;
  abortSignal: AbortSignal | null;
  flowType: FlowType;
  referenceFiles: AttachmentData[] | null;
}

/**
 * Flow configurations for different flow types
 */
const flowConfigs = new Map<FlowType, FlowConfig>([
  ["main", mainDocFlowConfig],
  ["noMain", noMainDocFlowConfig],
  ["referenceFiles", referenceFlowConfig],
]);

/**
 * Determine the appropriate flow type based on legal task configuration and mainDocName
 *
 * @param explicitFlowType - Explicitly requested flow type (if provided)
 * @param legalTask - The legal task configuration object
 * @param mainDocName - The name of the main document (if provided)
 * @param referenceFiles - Reference files array for referenceFiles flow
 * @returns The determined flow type ("main", "noMain", or "referenceFiles")
 */
export function determineFlowType(
  explicitFlowType?: FlowType | null,
  legalTask?: LegalTask | null,
  mainDocName?: string | null,
  referenceFiles: AttachmentData[] | null = null
): FlowType {
  // Case 1: Explicit flow type is provided and valid
  if (
    explicitFlowType &&
    ["main", "noMain", "referenceFiles"].includes(explicitFlowType)
  ) {
    console.log(
      `[FLOW DISPATCHER] Using explicit flow type: ${explicitFlowType}`
    );
    return explicitFlowType;
  }

  // Case 2: Legal task configuration specifies flow type
  if (
    legalTask &&
    legalTask.flowType &&
    ["main", "noMain", "referenceFiles"].includes(legalTask.flowType)
  ) {
    console.log(
      `[FLOW DISPATCHER] Using legal task config flow type: ${legalTask.flowType}`
    );
    return legalTask.flowType;
  }

  // Case 3: Check for reference files flow
  if (
    referenceFiles &&
    Array.isArray(referenceFiles) &&
    (referenceFiles?.length ?? 0) > 0
  ) {
    console.log(
      `[FLOW DISPATCHER] Detected reference files, using referenceFiles flow`
    );
    return "referenceFiles";
  }

  // Case 4: Determine by presence of mainDocName
  if (mainDocName) {
    console.log(`[FLOW DISPATCHER] Main document provided, using main flow`);
    return "main";
  } else {
    console.log(`[FLOW DISPATCHER] No main document, using noMain flow`);
    return "noMain";
  }
}

/**
 * Main flow dispatcher - routes to the appropriate modular flow based on flowType
 *
 * @param options - Options for the flow
 * @returns Promise that resolves to the flow result
 */
export async function runFlow(
  options: FlowOptions = {} as FlowOptions
): Promise<string> {
  const { abortSignal = null, ...baseOptions } = options;

  // Extract flow determination parameters with improved parsing
  const mainDocName =
    baseOptions.mainDocName ||
    (baseOptions.cdbOptions && baseOptions.cdbOptions[2]) ||
    null;
  const explicitFlowType =
    baseOptions.flowType ||
    (baseOptions.cdbOptions && baseOptions.cdbOptions[3]) ||
    null;
  const referenceFiles =
    (baseOptions.cdbOptions && baseOptions.cdbOptions[4]) || null;
  const legalTask = baseOptions.legalTask || null;

  const determinedFlowType = determineFlowType(
    explicitFlowType,
    legalTask,
    mainDocName,
    referenceFiles
  );

  const flowRunnerOptions: FlowRunnerOptions = {
    ...baseOptions,
    mainDocName,
    legalTask,
    abortSignal,
    flowType: determinedFlowType,
    referenceFiles,
  };

  console.log(
    `[FLOW DISPATCHER] Starting ${determinedFlowType} flow for chatId: ${flowRunnerOptions.chatId}`,
    {
      mainDocName: mainDocName ? "provided" : "not provided",
      explicitFlowType: explicitFlowType || "none",
      referenceFiles: referenceFiles
        ? `${referenceFiles.length} files`
        : "none",
    }
  );

  let result: string;

  try {
    // Use modular flow system directly
    console.log(
      `[FLOW DISPATCHER] Using modular flow system for ${determinedFlowType} flow`
    );
    result = await runModularFlow(determinedFlowType, flowRunnerOptions);

    console.log(
      `[FLOW DISPATCHER] ${determinedFlowType} flow completed successfully for chatId: ${flowRunnerOptions.chatId}`
    );
  } catch (error) {
    console.error(
      `[FLOW DISPATCHER] Error during ${determinedFlowType} flow execution for chatId ${flowRunnerOptions.chatId}:`,
      error
    );

    if (baseOptions.response && !baseOptions.response.writableEnded) {
      // Ensure an error response is sent via SSE if possible
      const { writeResponseChunk } = require("../helpers/chat/responses"); // Local require for safety
      writeResponseChunk(baseOptions.response, {
        uuid: flowRunnerOptions.chatId,
        type: "abort",
        error:
          error instanceof Error
            ? error.message
            : "Flow execution failed unexpectedly.",
        close: true,
      });
    }

    // Re-throw the error so the calling endpoint handler can also catch it and terminate the response.
    throw error;
  } finally {
    // Cleanup temporary files
    console.log(
      `[FLOW DISPATCHER] Flow finished or errored for chatId: ${flowRunnerOptions.chatId}. Attempting cleanup.`
    );
    try {
      const removed = purgeDocumentBuilder({ uuid: flowRunnerOptions.chatId });
      if (removed > 0) {
        console.log(
          `[FLOW DISPATCHER] Cleaned up ${removed} temporary files from document-builder.`
        );
      }
    } catch (cleanupError) {
      console.error(
        `[FLOW DISPATCHER] Error during cleanup for chatId ${flowRunnerOptions.chatId}:`,
        cleanupError
      );
    }
  }

  // Return the flow's final combined document content (if available)
  return result;
}

/**
 * Run flow using the modular system
 *
 * @param flowType - The type of flow to run
 * @param options - Options for the flow
 * @returns Promise that resolves to the result of the flow execution
 */
export async function runModularFlow(
  flowType: FlowType,
  options: FlowRunnerOptions
): Promise<string> {
  const flowConfig = flowConfigs.get(flowType);
  if (!flowConfig) {
    throw new Error(
      `[FLOW DISPATCHER] No modular configuration available for flow type: ${flowType}`
    );
  }

  // Ensure chatId is defined - it's required by FlowOrchestrator
  if (!options.chatId) {
    throw new Error("[FLOW DISPATCHER] chatId is required for flow execution");
  }

  // For CDB flows (main flow type), override the settings_suffix to use _CDB
  const effectiveSettingsSuffix =
    flowType === "main" && process.env.LLM_PROVIDER_CDB
      ? "_CDB"
      : options.settings_suffix;

  // Create FlowOptions compatible with FlowOrchestrator expectations
  const flowOptions: FlowOrchestratorOptions = {
    chatId: options.chatId,
    response: options.response,
    workspace: options.workspace,
    user:
      options.user ||
      ({
        id: 0,
        username: "",
        email: "",
        suspended: 0,
        role: "user",
        createdAt: new Date().toISOString(),
        lastUpdatedAt: new Date().toISOString(),
      } as unknown as UserData),
    message: options.message,
    abortSignal: undefined, // AbortSignal type conflict - omitting for now
    cdbOptions: (options.cdbOptions || []).map((opt) => String(opt)),
    settings_suffix: effectiveSettingsSuffix,
  };

  // Convert FlowConfiguration to FlowConfig
  const flowConfigConverted = flowConfig;
  const orchestrator = new FlowOrchestrator(flowOptions, flowConfigConverted);
  await orchestrator.initialize();
  return await orchestrator.execute();
}

/**
 * Add a new flow configuration to the modular system
 * Allows dynamic addition of new flow types
 *
 * @param flowType - The flow type to add
 * @param config - The flow configuration
 */
export function addFlowConfig(flowType: FlowType, config: FlowConfig): void {
  flowConfigs.set(flowType, config);
  console.log(
    `[FLOW DISPATCHER] Added flow configuration for type: ${flowType}`
  );
}

/**
 * Get available flow configurations
 *
 * @returns Object containing available flow types and their configurations
 */
export function getAvailableFlowConfigs(): Record<string, FlowConfig> {
  const availableFlows: Record<string, FlowConfig> = {};
  const entries = Array.from(flowConfigs.entries());
  for (const [flowType, config] of entries) {
    availableFlows[flowType] = config;
  }
  return availableFlows;
}
