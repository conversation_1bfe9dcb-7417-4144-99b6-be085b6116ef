import { mainDocFlowConfig } from "../mainDocFlowConfig";
import { SetupStageProcessor } from "../../processors/SetupStageProcessor";
import { DocumentProcessingStageProcessor } from "../../processors/DocumentProcessingStageProcessor";
import { SectionPlanningStageProcessor } from "../../processors/SectionPlanningStageProcessor";
import { AgenticCombinationStageProcessor } from "../../processors/AgenticCombinationStageProcessor";
import { LegalIssueIdentificationProcessor } from "../../processors/LegalIssueIdentificationProcessor";
import LegalMemoProcessor from "../../processors/LegalMemoProcessor";
import { IterativeSectionDraftingProcessor } from "../../processors/IterativeSectionDraftingProcessor";

describe("mainDocFlowConfig", () => {
  describe("Configuration Structure", () => {
    it("should have correct flow type and description", () => {
      expect(mainDocFlowConfig.flowType).toBe("main");
      expect(mainDocFlowConfig.description).toContain("main document");
    });

    it("should have exactly 9 stages defined", () => {
      expect(mainDocFlowConfig.stages).toBeDefined();
      expect(mainDocFlowConfig.stages.length).toBe(9);
    });

    it("should have stages in correct order", () => {
      const expectedStageNames: any = [
        "setup",
        "mainDocumentExtraction",
        "documentProcessing",
        "mainDocumentSectionGeneration",
        "sectionMapping",
        "legalIssueIdentification",
        "legalMemoGeneration",
        "sectionDrafting",
        "combination",
      ];

      const actualStageNames: any = mainDocFlowConfig.stages.map(
        (stage: any) => stage.name
      );
      expect(actualStageNames).toEqual(expectedStageNames);
    });
  });

  describe("Stage Processors", () => {
    it("should use correct processors for each stage", () => {
      const stageProcessorMap: any = {
        setup: SetupStageProcessor,
        documentProcessing: DocumentProcessingStageProcessor,
        mainDocumentSectionGeneration: SectionPlanningStageProcessor,
        legalIssueIdentification: LegalIssueIdentificationProcessor,
        legalMemoGeneration: LegalMemoProcessor,
        sectionDrafting: IterativeSectionDraftingProcessor,
        combination: AgenticCombinationStageProcessor,
      };

      mainDocFlowConfig.stages.forEach((stage: any) => {
        if (stageProcessorMap[stage.name]) {
          // Check the processor field which contains the actual class
          expect(stage.processor).toBe(stageProcessorMap[stage.name]);
        } else if (stage.name === "combination") {
          // Combination stage uses a dynamic processor
          expect(stage.processor).toBe(AgenticCombinationStageProcessor);
        }
      });
    });

    it("should have custom processors for main document specific stages", () => {
      const mainDocExtraction: any = mainDocFlowConfig.stages.find(
        (s: any) => s.name === "mainDocumentExtraction"
      );
      const sectionMapping: any = mainDocFlowConfig.stages.find(
        (s: any) => s.name === "sectionMapping"
      );

      expect(mainDocExtraction).toBeDefined();
      expect(mainDocExtraction.processor).toBeDefined();
      expect(mainDocExtraction.processor.name).toContain(
        "MainDocumentExtractionProcessor"
      );

      expect(sectionMapping).toBeDefined();
      expect(sectionMapping.processor).toBeDefined();
      expect(sectionMapping.processor.name).toContain(
        "SectionMappingProcessor"
      );
    });
  });

  describe("Stage Options", () => {
    it("should configure document processing stage to not skip relevance check", () => {
      const docProcessingStage: any = mainDocFlowConfig.stages.find(
        (s: any) => s.name === "documentProcessing"
      );
      expect(docProcessingStage.options).toEqual({
        skipRelevanceCheck: false,
      });
    });

    it("should configure section generation stage to use main document", () => {
      const sectionGenStage: any = mainDocFlowConfig.stages.find(
        (s: any) => s.name === "mainDocumentSectionGeneration"
      );
      expect(sectionGenStage.options).toEqual({
        sectionSource: "mainDocument",
      });
    });

    it("should configure legal issue identification with proper options", () => {
      const legalIssueStage: any = mainDocFlowConfig.stages.find(
        (s: any) => s.name === "legalIssueIdentification"
      );
      expect(legalIssueStage.options).toEqual({
        enableWorkspaceRouting: true,
        temperature: 0.3,
      });
    });
  });

  describe("Custom Processor Validation", () => {
    it("mainDocumentExtraction processor should extract main document", async () => {
      const stage = mainDocFlowConfig.stages.find(
        (s: any) => s.name === "mainDocumentExtraction"
      );
      expect(stage).toBeDefined();
      const MainDocExtractorClass: any = stage!.processor;
      const processor: any = new MainDocExtractorClass();

      const mockContext: any = {
        processedDocuments: [
          { displayName: "doc1.pdf", content: "content1" },
          { displayName: "main.docx", content: "main content" },
        ],
      };
      const mockDependencies: any = {
        abortChecker: jest.fn(),
        options: { mainDocName: "main.docx" },
        workspaceManager: {
          documentExists: jest.fn().mockReturnValue(true),
          readDocumentFile: jest.fn().mockReturnValue({
            id: "main-id",
            displayName: "main.docx",
            content: "main content",
          }),
        },
      };

      const result: any = await processor.process(
        mockContext,
        mockDependencies
      );

      expect(result.mainDocument).toBeDefined();
      expect(result.mainDocument.displayName).toBe("main.docx");
      expect(result.mainDocumentContent).toBe("main content");
    });

    it("mainDocumentExtraction should throw error if main document not found", async () => {
      const stage = mainDocFlowConfig.stages.find(
        (s: any) => s.name === "mainDocumentExtraction"
      );
      expect(stage).toBeDefined();
      const MainDocExtractorClass: any = stage!.processor;
      const processor: any = new MainDocExtractorClass();

      const mockContext: any = {
        processedDocuments: [{ displayName: "doc1.pdf", content: "content1" }],
      };
      const mockDependencies: any = {
        abortChecker: jest.fn(),
        options: { mainDocName: "missing.docx" },
        workspaceManager: {
          documentExists: jest.fn().mockReturnValue(false),
        },
      };

      await expect(
        processor.process(mockContext, mockDependencies)
      ).rejects.toThrow("Main document not found: missing.docx.json");
    });

    it("sectionMapping processor should process sections correctly", async () => {
      const stage = mainDocFlowConfig.stages.find(
        (s: any) => s.name === "sectionMapping"
      );
      expect(stage).toBeDefined();
      const SectionMappingClass: any = stage!.processor;
      const processor: any = new SectionMappingClass();

      const mockContext: any = {
        sectionList: [
          {
            index_number: 1,
            title: "Section 1",
            relevant_documents: ["doc1.pdf"],
          },
          { index_number: 2, title: "Section 2" },
        ],
        docDescriptions: [
          { "Doc Name": "doc1.pdf" },
          { "Doc Name": "doc2.pdf" },
        ],
      };
      const mockDependencies: any = {
        progressManager: { updateStep: jest.fn() },
        abortChecker: jest.fn(),
      };

      const result: any = await processor.process(
        mockContext,
        mockDependencies
      );

      expect(result.sectionList).toBeDefined();
      expect(result.sectionList[0].relevantDocumentNames).toContain("doc1.pdf");
      expect(
        result.sectionList[1].relevantDocumentNames.length
      ).toBeGreaterThan(0);
    });
  });

  describe("Stage Dependencies", () => {
    it("each stage should have required dependencies from previous stages", () => {
      const contextDependencies: any = {
        mainDocumentExtraction: [],
        documentProcessing: [],
        mainDocumentSectionGeneration: ["mainDocument", "mainDocumentContent"],
        sectionMapping: ["sectionList", "docDescriptions"],
        legalIssueIdentification: ["sectionList"],
        legalMemoGeneration: ["sectionIssues"],
        sectionDrafting: ["sectionList"],
        combination: ["draftedSections"],
      };

      // This validates that the flow is properly structured with dependencies
      Object.entries(contextDependencies).forEach(([stageName]) => {
        const stage: any = mainDocFlowConfig.stages.find(
          (s: any) => s.name === stageName
        );
        expect(stage).toBeDefined();
        // In a real implementation, we'd check that the processor expects these inputs
      });
    });
  });
});
