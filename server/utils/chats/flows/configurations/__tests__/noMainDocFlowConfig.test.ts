import { noMainDocFlowConfig } from "../noMainDocFlowConfig";
import { SetupStageProcessor } from "../../processors/SetupStageProcessor";
import { DocumentProcessingStageProcessor } from "../../processors/DocumentProcessingStageProcessor";
import { SectionPlanningStageProcessor } from "../../processors/SectionPlanningStageProcessor";
import type { FlowConfig } from "../../../../../types/chat-flow";

import { LegalIssueIdentificationProcessor } from "../../processors/LegalIssueIdentificationProcessor";
import LegalMemoProcessor from "../../processors/LegalMemoProcessor";
import { IterativeSectionDraftingProcessor } from "../../processors/IterativeSectionDraftingProcessor";

describe("noMainDocFlowConfig", () => {
  describe("Configuration Structure", () => {
    it("should have correct flow type and description", () => {
      expect(noMainDocFlowConfig.flowType).toBe("noMain");
      expect(noMainDocFlowConfig.description).toContain(
        "without a main document"
      );
    });

    it("should have exactly 8 stages defined", () => {
      expect(noMainDocFlowConfig.stages).toBeDefined();
      expect(noMainDocFlowConfig.stages.length).toBe(8);
    });

    it("should have stages in correct order", () => {
      const expectedStageNames = [
        "setup",
        "documentProcessing",
        "sectionPlanningFromSummaries",
        "documentToSectionMapping",
        "legalIssueIdentification",
        "legalMemoGeneration",
        "sectionDrafting",
        "combination",
      ];

      const actualStageNames = noMainDocFlowConfig.stages.map(
        (stage) => stage.name
      );
      expect(actualStageNames).toEqual(expectedStageNames);
    });
  });

  describe("Stage Processors", () => {
    it("should use correct processors for each stage", () => {
      const stageProcessorMap: Record<string, unknown> = {
        setup: SetupStageProcessor,
        documentProcessing: DocumentProcessingStageProcessor,
        sectionPlanningFromSummaries: SectionPlanningStageProcessor,
        legalIssueIdentification: LegalIssueIdentificationProcessor,
        legalMemoGeneration: LegalMemoProcessor,
        sectionDrafting: IterativeSectionDraftingProcessor,
      };

      noMainDocFlowConfig.stages.forEach((stage) => {
        if (stageProcessorMap[stage.name]) {
          expect(stage.processor).toBe(stageProcessorMap[stage.name]);
        }
      });
    });

    it("should have a custom processor for documentToSectionMapping", () => {
      const mappingStage = noMainDocFlowConfig.stages.find(
        (s) => s.name === "documentToSectionMapping"
      );
      expect(mappingStage).toBeDefined();
      expect(mappingStage?.processor).toBeDefined();
      const processorName =
        (mappingStage?.processor as { name?: string })?.name || "";
      expect(processorName).toContain("DocumentToSectionMappingProcessor");
    });

    it("should not have main document specific stages", () => {
      const stageNames = noMainDocFlowConfig.stages.map((s) => s.name);
      expect(stageNames).not.toContain("mainDocumentExtraction");
      expect(stageNames).not.toContain("mainDocumentSectionGeneration");
      expect(stageNames).not.toContain("sectionMapping");
    });
  });

  describe("Stage Options", () => {
    it("should configure document processing with skipRelevanceCheck", () => {
      const docProcessingStage = noMainDocFlowConfig.stages.find(
        (s) => s.name === "documentProcessing"
      );
      expect(docProcessingStage?.options).toEqual({
        skipRelevanceCheck: true,
      });
    });

    it("should configure section planning to use document summaries", () => {
      const sectionPlanningStage = noMainDocFlowConfig.stages.find(
        (s) => s.name === "sectionPlanningFromSummaries"
      );
      expect(sectionPlanningStage?.options).toEqual({
        sectionSource: "summaries",
      });
    });

    it("should configure legal issue identification with workspace routing", () => {
      const legalIssueStage = noMainDocFlowConfig.stages.find(
        (s) => s.name === "legalIssueIdentification"
      );
      expect(legalIssueStage?.options).toEqual({
        enableWorkspaceRouting: true,
        temperature: 0.3,
      });
    });

    it("should configure legal memo generation with proper options", () => {
      const legalMemoStage = noMainDocFlowConfig.stages.find(
        (s) => s.name === "legalMemoGeneration"
      );
      expect(legalMemoStage?.options).toEqual({
        maxRetries: 3,
        enableWorkspaceRouting: true,
      });
    });

    it("should configure section drafting for noMain flow", () => {
      const sectionDraftingStage = noMainDocFlowConfig.stages.find(
        (s) => s.name === "sectionDrafting"
      );
      expect(sectionDraftingStage?.options).toEqual({
        flowType: "noMain",
        temperature: 0.7,
        saveIntermediateResults: true,
      });
    });
  });

  describe("Flow-specific Options", () => {
    it("should have correct flow-level options", () => {
      expect(noMainDocFlowConfig.metadata).toBeDefined();
      expect((noMainDocFlowConfig.metadata as any)?.options).toEqual({
        requireMainDocument: false,
        enableIterativeProcessing: true,
        enableLegalMemoGeneration: true,
        maxSections: 20,
        equalDocumentWeight: true,
        comprehensiveCoverage: true,
      });
    });
  });

  describe("Prompt Configurations", () => {
    it("should define all required prompts", () => {
      expect(noMainDocFlowConfig.metadata).toBeDefined();
      expect((noMainDocFlowConfig.metadata as any)?.prompts).toEqual({
        sectionGeneration: "CURRENT_DEFAULT_SECTION_LIST_FROM_SUMMARIES",
        documentSummary: "CURRENT_DEFAULT_DOCUMENT_SUMMARY",
        documentRelevance: "CURRENT_DEFAULT_DOCUMENT_RELEVANCE",
        sectionDrafting: "CURRENT_DEFAULT_SECTION_DRAFTING",
        legalIssueIdentification: "CURRENT_DEFAULT_SECTION_LEGAL_ISSUES",
      });
    });
  });

  describe("Validation Rules", () => {
    it("should have correct validation configuration", () => {
      expect(noMainDocFlowConfig.metadata).toBeDefined();
      expect((noMainDocFlowConfig.metadata as any)?.validation).toEqual({
        requiredInputs: ["workspace", "user", "message"],
        requiredStages: [
          "setup",
          "documentProcessing",
          "sectionPlanningFromSummaries",
          "combination",
        ],
      });
    });

    it("should not require mainDocument stage", () => {
      expect(noMainDocFlowConfig.metadata).toBeDefined();
      expect((noMainDocFlowConfig.metadata as any)?.validation).toBeDefined();
      const validation = (noMainDocFlowConfig.metadata as any)?.validation as {
        requiredStages?: string[];
      };
      expect(validation?.requiredStages).not.toContain(
        "mainDocumentExtraction"
      );
    });
  });

  describe("Custom Processor Tests", () => {
    it("documentToSectionMapping should distribute documents across sections", async () => {
      const mappingStage = noMainDocFlowConfig.stages.find(
        (s) => s.name === "documentToSectionMapping"
      );
      expect(mappingStage).toBeDefined();
      if (!mappingStage) return;

      const DocumentToSectionMappingClass =
        mappingStage.processor as unknown as new (..._args: unknown[]) => {
          log: unknown;
          process: (
            _context: unknown,
            _dependencies: unknown
          ) => Promise<{
            sectionList: Array<{
              relevantDocumentNames: string[];
              [key: string]: unknown;
            }>;
          }>;
        };
      expect(DocumentToSectionMappingClass).toBeDefined();
      const processor = new DocumentToSectionMappingClass();
      processor.log = jest.fn();

      const mockContext = {
        sectionList: [
          { index_number: 1, title: "Section 1", relevant_documents: [] },
          { index_number: 2, title: "Section 2", relevant_documents: [] },
          { index_number: 3, title: "Section 3", relevant_documents: [] },
        ],
        docDescriptions: [
          { "Doc Name": "doc1.pdf" },
          { "Doc Name": "doc2.pdf" },
          { "Doc Name": "doc3.pdf" },
          { "Doc Name": "doc4.pdf" },
        ],
      };
      const mockDependencies = {
        abortChecker: jest.fn(),
        progressManager: {
          updateStep: jest.fn(),
        },
      };

      const result = await processor.process(mockContext, mockDependencies);

      // Should distribute documents across sections
      expect(result.sectionList).toBeDefined();
      expect(result.sectionList.length).toBe(3);

      // Each section should have at least one document
      result.sectionList.forEach((section) => {
        expect(section.relevantDocumentNames).toBeDefined();
        expect(section.relevantDocumentNames.length).toBeGreaterThan(0);
      });

      // All documents should be distributed
      const allAssignedDocs = result.sectionList.flatMap(
        (s) => s.relevantDocumentNames
      );
      expect(allAssignedDocs.length).toBeGreaterThanOrEqual(4);
    });
  });

  describe("Flow Differences from Main Flow", () => {
    it("should include legal memo generation unlike reference flow", () => {
      const hasLegalMemo = noMainDocFlowConfig.stages.some(
        (s) => s.name === "legalMemoGeneration"
      );
      expect(hasLegalMemo).toBe(true);
    });

    it("should have documentToSectionMapping for intelligent document distribution", () => {
      const hasDocMapping = noMainDocFlowConfig.stages.some(
        (s) => s.name === "documentToSectionMapping"
      );
      expect(hasDocMapping).toBe(true);
    });

    it("should treat all documents equally without main document prioritization", () => {
      const config = noMainDocFlowConfig as FlowConfig & {
        metadata?: {
          options?: {
            equalDocumentWeight?: boolean;
            requireMainDocument?: boolean;
          };
        };
      };
      expect(config.metadata).toBeDefined();
      expect(config.metadata?.options).toBeDefined();
      const options = config.metadata?.options;
      expect(options?.equalDocumentWeight).toBe(true);
      expect(options?.requireMainDocument).toBe(false);
    });
  });

  describe("Stage Dependencies", () => {
    it("each stage should have required dependencies from previous stages", () => {
      const contextDependencies: Record<string, string[]> = {
        documentProcessing: ["documents"],
        sectionPlanningFromSummaries: ["docDescriptions", "processedDocuments"],
        documentToSectionMapping: ["sectionList", "docDescriptions"],
        legalIssueIdentification: ["sectionList"],
        legalMemoGeneration: ["sectionIssues"],
        sectionDrafting: ["sectionList", "legalMemos"],
        combination: ["draftedSections"],
      };

      // Validate that stages are properly ordered with their dependencies
      Object.entries(contextDependencies).forEach(([stageName]) => {
        const stage = noMainDocFlowConfig.stages.find(
          (s) => s.name === stageName
        );
        expect(stage).toBeDefined();
      });
    });

    it("should work without a main document throughout the flow", () => {
      // Ensure no stage requires mainDocument or mainDocumentContent
      noMainDocFlowConfig.stages.forEach((stage) => {
        expect(stage.name).not.toContain("mainDocument");
        expect(stage.name).not.toContain("mainDoc");
      });
    });
  });

  describe("Flow Completeness", () => {
    it("should cover all essential document drafting steps", () => {
      const essentialSteps = [
        "setup",
        "documentProcessing",
        "sectionPlanning", // Covered by sectionPlanningFromSummaries
        "legalIssueIdentification",
        "legalMemoGeneration",
        "sectionDrafting",
        "combination",
      ];

      const stageNames = noMainDocFlowConfig.stages.map((s) => s.name);
      essentialSteps.forEach((step) => {
        if (step === "sectionPlanning") {
          expect(
            stageNames.some((name) => name.includes("sectionPlanning"))
          ).toBe(true);
        } else {
          expect(stageNames).toContain(step);
        }
      });
    });
  });
});
