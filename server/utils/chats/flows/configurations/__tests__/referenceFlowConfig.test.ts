import { referenceFlowConfig } from "../referenceFlowConfig";
import { SetupStageProcessor } from "../../processors/SetupStageProcessor";
import { SectionPlanningStageProcessor } from "../../processors/SectionPlanningStageProcessor";

import { IterativeSectionDraftingProcessor } from "../../processors/IterativeSectionDraftingProcessor";

describe("referenceFlowConfig", () => {
  describe("Configuration Structure", () => {
    it("should have correct flow type and description", () => {
      expect(referenceFlowConfig.flowType).toBe("referenceFiles");
      expect(referenceFlowConfig.description).toContain("Compliance analysis");
      expect(referenceFlowConfig.description).toContain("reference standards");
    });

    it("should have exactly 8 stages defined", () => {
      expect(referenceFlowConfig.stages).toBeDefined();
      expect(referenceFlowConfig.stages.length).toBe(8);
    });

    it("should have stages in correct order", () => {
      const expectedStageNames: any = [
        "setup",
        "documentCategorization",
        "referenceAnalysis",
        "reviewFileAnalysis",
        "documentDescriptionGeneration",
        "complianceSectionGeneration",
        "complianceSectionDrafting",
        "combination",
      ];

      const actualStageNames: any = referenceFlowConfig.stages.map(
        (stage: any) => stage.name
      );
      expect(actualStageNames).toEqual(expectedStageNames);
    });
  });

  describe("Stage Processors", () => {
    it("should use correct processors for standard stages", () => {
      const stageProcessorMap: any = {
        setup: SetupStageProcessor,
      };

      referenceFlowConfig.stages.forEach((stage: any) => {
        if (stageProcessorMap[stage.name]) {
          expect(stage.processor).toBe(stageProcessorMap[stage.name]);
        } else if (stage.name === "combination") {
          // Combination stage uses AgenticCombinationStageProcessor
          expect(stage.processor.name).toBe("AgenticCombinationStageProcessor");
        }
      });
    });

    it("should have custom processors for reference-specific stages", () => {
      const documentCategorizationStage: any = referenceFlowConfig.stages.find(
        (s: any) => s.name === "documentCategorization"
      );
      const referenceAnalysisStage: any = referenceFlowConfig.stages.find(
        (s: any) => s.name === "referenceAnalysis"
      );
      const reviewFileAnalysisStage: any = referenceFlowConfig.stages.find(
        (s: any) => s.name === "reviewFileAnalysis"
      );

      expect(documentCategorizationStage).toBeDefined();
      expect(documentCategorizationStage.processor).toBeDefined();
      expect(documentCategorizationStage.processor.name).toContain(
        "DocumentCategorizationProcessor"
      );

      expect(referenceAnalysisStage).toBeDefined();
      expect(referenceAnalysisStage.processor).toBeDefined();
      expect(referenceAnalysisStage.processor.name).toContain(
        "ReferenceAnalysisProcessor"
      );

      expect(reviewFileAnalysisStage).toBeDefined();
      expect(reviewFileAnalysisStage.processor).toBeDefined();
      expect(reviewFileAnalysisStage.processor.name).toContain(
        "ReviewFileAnalysisProcessor"
      );
    });

    it("should extend SectionPlanningStageProcessor for compliance section generation", () => {
      const complianceSectionGeneration: any = referenceFlowConfig.stages.find(
        (s: any) => s.name === "complianceSectionGeneration"
      );

      expect(complianceSectionGeneration).toBeDefined();
      expect(complianceSectionGeneration.processor).toBe(
        SectionPlanningStageProcessor
      );
    });

    it("should use IterativeSectionDraftingProcessor for compliance section drafting", () => {
      const complianceSectionDrafting: any = referenceFlowConfig.stages.find(
        (s: any) => s.name === "complianceSectionDrafting"
      );

      expect(complianceSectionDrafting).toBeDefined();
      expect(complianceSectionDrafting.processor).toBe(
        IterativeSectionDraftingProcessor
      );
    });
  });

  describe("Stage Options", () => {
    it("should configure compliance section generation with correct options", () => {
      const complianceSectionGeneration: any = referenceFlowConfig.stages.find(
        (s: any) => s.name === "complianceSectionGeneration"
      );

      expect(complianceSectionGeneration.options).toEqual({
        sectionSource: "summaries",
        promptOverrides: {
          sectionGenerationPrompt: "CURRENT_DEFAULT_REFERENCE_REVIEW_SECTIONS",
        },
      });
    });

    it("should configure compliance section drafting with correct options", () => {
      const complianceSectionDrafting: any = referenceFlowConfig.stages.find(
        (s: any) => s.name === "complianceSectionDrafting"
      );

      expect(complianceSectionDrafting.options).toEqual({
        flowType: "referenceFiles",
        temperature: 0.7,
        saveIntermediateResults: true,
      });
    });
  });

  describe("Flow Options", () => {
    it("should have correct flow-level options", () => {
      expect(referenceFlowConfig.options).toEqual({
        requireReferenceFiles: true,
        requireReviewFiles: true,
        enableComplianceScoring: true,
        enableIssueTracking: true,
        maxComplianceAreas: 10,
        complianceThreshold: 80,
        generateRecommendations: true,
      });
    });
  });

  describe("Prompt Configurations", () => {
    it("should define all required prompts", () => {
      expect(referenceFlowConfig.prompts).toEqual({
        documentCategorization: {
          SYSTEM_PROMPT: "CURRENT_DEFAULT_DOCUMENT_CATEGORIZATION",
        },
        referenceAnalysis: {
          SYSTEM_PROMPT: "CURRENT_DEFAULT_REFERENCE_ANALYSIS",
        },
        reviewAnalysis: { SYSTEM_PROMPT: "CURRENT_DEFAULT_REVIEW_ANALYSIS" },
        complianceReporting: {
          SYSTEM_PROMPT: "CURRENT_DEFAULT_COMPLIANCE_REPORTING",
        },
      });
    });
  });

  describe("Validation Rules", () => {
    it("should have correct validation configuration", () => {
      expect(referenceFlowConfig.validation).toEqual({
        requiredInputs: ["workspace", "user", "message"],
        requiredStages: [
          "setup",
          "documentCategorization",
          "referenceAnalysis",
          "reviewFileAnalysis",
          "documentDescriptionGeneration",
          "complianceSectionGeneration",
          "complianceSectionDrafting",
          "combination",
        ],
        minimumDocuments: 2,
      });
    });
  });

  describe("Custom Processor Validation", () => {
    it("documentCategorization should separate reference files from review files", async () => {
      const categorizationStage = referenceFlowConfig.stages.find(
        (s: any) => s.name === "documentCategorization"
      );
      expect(categorizationStage).toBeDefined();
      const DocumentCategorizationClass: any = categorizationStage?.processor;
      expect(DocumentCategorizationClass).toBeDefined();
      const processor: any = new DocumentCategorizationClass();

      const mockContext: any = {
        documents: [
          {
            id: "ref1",
            fileName: "reference1.pdf",
            displayName: "reference1.pdf",
            content: "standard content",
          },
          {
            id: "doc1",
            fileName: "document1.docx",
            displayName: "document1.docx",
            content: "review content",
          },
        ],
      };
      const mockDependencies: any = {
        abortChecker: jest.fn(),
        progressManager: {
          updateStep: jest.fn(),
        },
        options: {
          referenceFiles: ["test-workspace/reference1.pdf"],
        },
        workspace: {
          name: "test-workspace",
        },
      };

      processor.log = jest.fn(); // Mock the log method
      const result: any = await processor.process(
        mockContext,
        mockDependencies
      );

      expect(result.referenceFiles).toBeDefined();
      expect(result.referenceFiles.length).toBe(1);
      expect(result.referenceFiles[0].fileName).toBe("reference1.pdf");
      expect(result.reviewFiles).toBeDefined();
      expect(result.reviewFiles.length).toBe(1);
      expect(result.reviewFiles[0].fileName).toBe("document1.docx");
    });

    it("referenceAnalysis should extract requirements from reference files", async () => {
      const referenceAnalysisStage = referenceFlowConfig.stages.find(
        (s: any) => s.name === "referenceAnalysis"
      );
      expect(referenceAnalysisStage).toBeDefined();
      const ReferenceAnalysisClass: any = referenceAnalysisStage?.processor;
      expect(ReferenceAnalysisClass).toBeDefined();
      const processor: any = new ReferenceAnalysisClass();

      // Test helper methods
      const text: any = `
        Requirements:
        - The system must provide data encryption
        - Users shall authenticate before access

        Standards:
        - Follow ISO 27001 standard
        - Implement GDPR guidelines

        This covers Data Protection and Security areas.
      `;

      const requirements: any = processor.extractRequirementsFromText(
        text,
        "test.pdf"
      );
      expect(requirements).toBeDefined();
      expect(requirements.length).toBeGreaterThan(0);
      expect(
        requirements.some(
          (req: any) =>
            req.toLowerCase().includes("must") ||
            req.toLowerCase().includes("shall")
        )
      ).toBe(true);

      const standards: any = processor.extractStandardsFromText(
        text,
        "test.pdf"
      );
      expect(standards).toBeDefined();
      expect(standards.length).toBeGreaterThan(0);
      expect(standards[0]).toContain("standard");

      const areas: any = processor.extractComplianceAreasFromText(text);
      expect(areas).toBeDefined();
      expect(areas).toContain("Data Protection");
      expect(areas).toContain("Security");
    });

    it("reviewFileAnalysis should calculate compliance scores", async () => {
      const reviewFileAnalysisStage = referenceFlowConfig.stages.find(
        (s: any) => s.name === "reviewFileAnalysis"
      );
      expect(reviewFileAnalysisStage).toBeDefined();
      const ReviewFileAnalysisClass: any = reviewFileAnalysisStage?.processor;
      expect(ReviewFileAnalysisClass).toBeDefined();
      const processor: any = new ReviewFileAnalysisClass();

      const issues: any = [
        { severity: "high", type: "violation" },
        { severity: "medium", type: "gap" },
        { severity: "low", type: "gap" },
      ];

      const score: any = processor.calculateComplianceScore(issues);
      expect(score).toBe(55); // 100 - 25 - 15 - 5
    });
  });

  describe("Flow Characteristics", () => {
    it("should be designed for compliance analysis workflow", () => {
      const stageNames: any = referenceFlowConfig.stages.map(
        (s: any) => s.name
      );

      // Should have document categorization
      expect(stageNames).toContain("documentCategorization");

      // Should have both reference and review analysis
      expect(stageNames).toContain("referenceAnalysis");
      expect(stageNames).toContain("reviewFileAnalysis");

      // Should generate compliance sections
      expect(stageNames).toContain("complianceSectionGeneration");
      expect(stageNames).toContain("complianceSectionDrafting");
    });

    it("should include document description generation for enhanced context", () => {
      const stageNames: any = referenceFlowConfig.stages.map(
        (s: any) => s.name
      );
      expect(stageNames).toContain("documentDescriptionGeneration");
    });
  });

  describe("Stage Dependencies", () => {
    it("each stage should have access to required context from previous stages", () => {
      const contextDependencies: any = {
        documentCategorization: ["documents"],
        referenceAnalysis: ["referenceFiles"],
        reviewFileAnalysis: ["reviewFiles", "aggregatedRequirements"],
        documentDescriptionGeneration: ["referenceFiles", "reviewFiles"],
        complianceSectionGeneration: [
          "processedDocuments",
          "complianceSummary",
        ],
        complianceSectionDrafting: [
          "sectionList",
          "processedDocuments",
          "complianceSummary",
        ],
        combination: ["draftedSections"],
      };

      Object.entries(contextDependencies).forEach(([stageName]) => {
        const stage: any = referenceFlowConfig.stages.find(
          (s: any) => s.name === stageName
        );
        expect(stage).toBeDefined();
      });
    });
  });
});
