import { SetupStageProcessor } from "../processors/SetupStageProcessor";
import { DocumentProcessingStageProcessor } from "../processors/DocumentProcessingStageProcessor";
import { SectionPlanningStageProcessor } from "../processors/SectionPlanningStageProcessor";
import { AgenticCombinationStageProcessor } from "../processors/AgenticCombinationStageProcessor";
import { StageProcessor } from "../core/StageProcessor";
import { LegalIssueIdentificationProcessor } from "../processors/LegalIssueIdentificationProcessor";
import { LegalMemoProcessor } from "../processors/LegalMemoProcessor";
import { IterativeSectionDraftingProcessor } from "../processors/IterativeSectionDraftingProcessor";
import type { FlowConfig } from "../../../../types/chat-flow";
import {
  FlowContext,
  StageDependencies,
  Section,
} from "../../../../types/chat-flow";
// Remove the import of FlowConfiguration, FlowStageConfig from chat-agent
// Restore the local interface, but update processor type to only allow constructors
// Local type definition for compatibility

/**
 * Main Document Extraction Processor
 * Extracts and validates the main document for the flow
 */
class MainDocumentExtractionProcessor extends StageProcessor {
  async process(
    _context: FlowContext,
    dependencies: StageDependencies & {
      options: {
        mainDocName?: string;
        cdbOptions?: string[];
      };
    }
  ): Promise<
    Partial<FlowContext> & {
      mainDocumentName: string;
      mainDocumentContent: string;
      mainDocument: {
        id: string;
        displayName: string;
        content: string;
      };
    }
  > {
    const { workspaceManager, abortChecker, options } = dependencies;

    abortChecker();

    // Extract main document name from options
    const mainDocName =
      options.mainDocName ||
      (options.cdbOptions && options.cdbOptions[2]) ||
      null;

    if (!mainDocName) {
      throw new Error("Main document name not provided for main document flow");
    }

    // Normalize the name
    const normalizedMainDocName = mainDocName.replace(/\.json$/i, "");

    // Check if main document exists
    if (!workspaceManager.documentExists(`${normalizedMainDocName}.json`)) {
      throw new Error(`Main document not found: ${normalizedMainDocName}.json`);
    }

    // Read main document
    const mainDocument = workspaceManager.readDocumentFile(
      `${normalizedMainDocName}.json`
    );

    if (!mainDocument.content || mainDocument.content.trim().length === 0) {
      throw new Error("Main document has no content");
    }

    this.log("info", "Main document extracted", {
      docId: mainDocument.id,
      displayName: mainDocument.displayName,
      contentLength: mainDocument.content.length,
    });

    return {
      mainDocumentName: normalizedMainDocName,
      mainDocumentContent: mainDocument.content,
      mainDocument: mainDocument,
    };
  }
}

/**
 * Section Mapping Processor
 * Maps documents to relevant sections for processing
 */
class SectionMappingProcessor extends StageProcessor {
  async process(
    context: FlowContext & {
      sectionList: Array<{
        relevant_documents?: string[];
        relevantDocumentNames?: string[];
        [key: string]: unknown;
      }>;
      mainDocumentName?: string;
    },
    dependencies: StageDependencies
  ): Promise<
    Partial<FlowContext> & {
      sectionList: Array<{
        relevantDocumentNames: string[];
        [key: string]: unknown;
      }>;
    }
  > {
    const { progressManager, abortChecker } = dependencies;

    abortChecker();
    progressManager.updateStep("Mapping documents to sections...");

    // For main doc flow, sections are already generated from the main document
    // We just need to ensure document mapping is complete

    if (!context.sectionList || context.sectionList.length === 0) {
      throw new Error("No section list available for document mapping");
    }

    // Sections from main document should already have some structure
    // We can enhance the mapping by distributing documents more intelligently
    const mappedSections: Section[] = context.sectionList.map((section) => ({
      ...section,
      relevantDocumentNames:
        (section.relevant_documents as string[]) ||
        section.relevantDocumentNames ||
        [],
    }));

    // CRITICAL: Add main document to all sections in main document flow
    // This ensures the main document content is available during section drafting
    if (context.mainDocumentName) {
      const mainDocName = `${context.mainDocumentName}.json`;
      for (const section of mappedSections) {
        if (!section.relevantDocumentNames) {
          section.relevantDocumentNames = [];
        }
        // Add main document as the first document for each section
        const relevantDocs = (section.relevantDocumentNames as string[]) || [];
        if (!relevantDocs.includes(mainDocName)) {
          relevantDocs.unshift(mainDocName);
          section.relevantDocumentNames = relevantDocs;
        }
      }

      this.log("info", "Added main document to all sections", {
        mainDocName,
        sectionsCount: mappedSections.length,
      });
    }

    // If any sections don't have documents mapped, distribute available documents
    const sectionsWithoutDocs = mappedSections.filter(
      (section) =>
        !section.relevantDocumentNames ||
        (Array.isArray(section.relevantDocumentNames) &&
          section.relevantDocumentNames.length === 0)
    );

    if (
      (sectionsWithoutDocs?.length ?? 0) > 0 &&
      Array.isArray(context.docDescriptions)
    ) {
      const availableDocs = context.docDescriptions.map(
        (doc) => doc["Doc Name"] || doc.DisplayName || String(doc)
      );
      const docsPerSection = Math.ceil(
        availableDocs.length / mappedSections.length
      );

      let docIndex = 0;
      for (const section of sectionsWithoutDocs) {
        const endIndex = Math.min(
          docIndex + docsPerSection,
          availableDocs.length
        );
        section.relevantDocumentNames = availableDocs.slice(docIndex, endIndex);
        docIndex = endIndex;
      }
    }

    this.log("info", "Section mapping completed", {
      totalSections: mappedSections.length,
      sectionsWithDocs: mappedSections.filter(
        (s) =>
          s.relevantDocumentNames && (s?.relevantDocumentNames?.length ?? 0) > 0
      ).length,
    });

    return {
      sectionList: mappedSections as Section[],
    };
  }
}

/**
 * Main Document Flow Configuration
 *
 * Defines the stage pipeline and configuration for flows that have a primary "main" document.
 * This flow generates sections from the main document content and then processes all documents
 * to create comprehensive legal document drafts.
 */
export const mainDocFlowConfig: FlowConfig = {
  id: "mainDocFlow",
  flowType: "main",
  name: "Main Document Flow",
  description:
    "Document drafting flow with a main document for section generation",

  // Stage pipeline - executed in sequence
  stages: [
    {
      name: "setup",
      processor: SetupStageProcessor as unknown as new (
        ...args: unknown[]
      ) => StageProcessor,
      description: "Initialize workspace, load prompts, validate documents",
      options: {},
    },
    {
      name: "mainDocumentExtraction",
      processor: MainDocumentExtractionProcessor as unknown as new (
        ...args: unknown[]
      ) => StageProcessor,
      description: "Extract and validate the main document",
      options: {},
    },
    {
      name: "documentProcessing",
      processor: DocumentProcessingStageProcessor as unknown as new (
        ...args: unknown[]
      ) => StageProcessor,
      description:
        "Generate descriptions and check relevance for all documents",
      options: {
        skipRelevanceCheck: false,
      },
    },
    {
      name: "mainDocumentSectionGeneration",
      processor: SectionPlanningStageProcessor as unknown as new (
        ...args: unknown[]
      ) => StageProcessor,
      description: "Generate section list from main document content",
      options: {
        sectionSource: "mainDocument",
      },
    },
    {
      name: "sectionMapping",
      processor: SectionMappingProcessor as unknown as new (
        ...args: unknown[]
      ) => StageProcessor,
      description: "Map documents to relevant sections",
      options: {},
    },
    {
      name: "legalIssueIdentification",
      processor: LegalIssueIdentificationProcessor as unknown as new (
        ...args: unknown[]
      ) => StageProcessor,
      description: "Identify legal issues for each section using LLM analysis",
      options: {
        enableWorkspaceRouting: true,
        temperature: 0.3,
      },
    },
    {
      name: "legalMemoGeneration",
      processor: LegalMemoProcessor as unknown as new (
        ...args: unknown[]
      ) => StageProcessor,
      description: "Generate legal memos for identified issues",
      options: {
        maxRetries: 3,
        enableWorkspaceRouting: true,
      },
    },
    {
      name: "sectionDrafting",
      processor: IterativeSectionDraftingProcessor as unknown as new (
        ...args: unknown[]
      ) => StageProcessor,
      description:
        "Draft sections using iterative processing with contextual documents and memos",
      options: {
        flowType: "main",
        temperature: 0.7,
        saveIntermediateResults: true,
      },
    },
    {
      name: "combination",
      processor: AgenticCombinationStageProcessor as unknown as new (
        ...args: unknown[]
      ) => StageProcessor,
      description: "AI-enhanced combination with document refinement",
      options: {
        // Enable agentic editing for comprehensive document enhancement
        agenticEditingEnabled: true,
        granularEditingEnabled: true,
        lineEditingEnabled: true,

        // Comprehensive editing for main document flow
        granularEditingIterations: 3,
        lineEditingIterations: 2,
        granularBatchSize: 50,
        lineBatchSize: 50,

        // Standard options
        includeMetadata: true,
        sendFinalResult: true,
      },
    },
  ],

  // Flow metadata
  prompts: {
    sectionGeneration: {
      SYSTEM_PROMPT: "CURRENT_DEFAULT_SECTION_LIST_FROM_MAIN",
    },
    documentSummary: { SYSTEM_PROMPT: "CURRENT_DEFAULT_DOCUMENT_SUMMARY" },
    documentRelevance: { SYSTEM_PROMPT: "CURRENT_DEFAULT_DOCUMENT_RELEVANCE" },
    sectionDrafting: { SYSTEM_PROMPT: "CURRENT_DEFAULT_SECTION_DRAFTING" },
  },
  metadata: {
    // Flow-specific options
    options: {
      requireMainDocument: true,
      enableIterativeProcessing: true,
      enableLegalMemoGeneration: true,
      maxSections: 20,
    },

    // Flow validation rules
    validation: {
      requiredInputs: ["workspace", "user", "message", "mainDocName"],
      requiredStages: [
        "setup",
        "mainDocumentExtraction",
        "documentProcessing",
        "combination",
      ],
    },
  },
};
