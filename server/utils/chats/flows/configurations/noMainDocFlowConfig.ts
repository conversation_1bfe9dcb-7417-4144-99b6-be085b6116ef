import { SetupStageProcessor } from "../processors/SetupStageProcessor";
import { DocumentProcessingStageProcessor } from "../processors/DocumentProcessingStageProcessor";
import { SectionPlanningStageProcessor } from "../processors/SectionPlanningStageProcessor";
import { StageProcessor } from "../core/StageProcessor";
import { LegalIssueIdentificationProcessor } from "../processors/LegalIssueIdentificationProcessor";
import LegalMemoProcessor from "../processors/LegalMemoProcessor";
import { IterativeSectionDraftingProcessor } from "../processors/IterativeSectionDraftingProcessor";
import {
  FlowContext,
  StageDependencies,
  Section,
} from "../../../../types/chat-flow";
import type { FlowConfig } from "../../../../types/chat-flow";
import { CombinationProcessorFactory } from "../processors/CombinationProcessorFactory";

// Remove unused interfaces - using generic types from flow context

/**
 * Document to Section Mapping Processor
 * Maps documents to sections for comprehensive coverage in noMain flow
 */
class DocumentToSectionMappingProcessor extends StageProcessor {
  async process(
    context: FlowContext & {
      sectionList: Array<{
        relevant_documents?: string[];
        relevantDocumentNames?: string[];
        [key: string]: unknown;
      }>;
      docDescriptions: Array<{
        "Doc Name": string;
        [key: string]: unknown;
      }>;
    },
    dependencies: StageDependencies
  ): Promise<
    Partial<FlowContext> & {
      sectionList: Array<{
        relevantDocumentNames: string[];
        [key: string]: unknown;
      }>;
    }
  > {
    const { progressManager, abortChecker } = dependencies;

    abortChecker();
    progressManager.updateStep("Mapping documents to sections...");

    if (!context.sectionList || context.sectionList.length === 0) {
      throw new Error("No section list available for document mapping");
    }

    if (!context.docDescriptions || context.docDescriptions.length === 0) {
      throw new Error("No document descriptions available for mapping");
    }

    // For noMain flow, we need to intelligently distribute documents across sections
    const mappedSections = context.sectionList.map((section) => ({
      ...section,
      relevantDocumentNames: (section.relevant_documents ||
        section.relevantDocumentNames ||
        []) as string[],
    }));

    // Check which sections need document mapping
    const sectionsNeedingDocs = mappedSections.filter(
      (section) =>
        !section.relevantDocumentNames ||
        !Array.isArray(section.relevantDocumentNames) ||
        section.relevantDocumentNames.length === 0
    );

    if ((sectionsNeedingDocs?.length ?? 0) > 0) {
      const availableDocs = context.docDescriptions.map(
        (doc) => doc["Doc Name"]
      );

      // Distribute documents evenly across sections that need them
      const docsPerSection = Math.ceil(
        availableDocs.length / sectionsNeedingDocs.length
      );

      let docIndex = 0;
      for (const section of sectionsNeedingDocs) {
        const endIndex = Math.min(
          docIndex + docsPerSection,
          availableDocs.length
        );
        section.relevantDocumentNames = availableDocs.slice(docIndex, endIndex);
        docIndex = endIndex;

        // If we've distributed all documents, break
        if (docIndex >= availableDocs.length) break;
      }

      // If there are remaining documents, distribute them among sections that already have documents
      if (docIndex < availableDocs.length) {
        const remainingDocs = availableDocs.slice(docIndex);
        const sectionsWithDocs = mappedSections.filter(
          (s) =>
            s.relevantDocumentNames &&
            (s?.relevantDocumentNames?.length ?? 0) > 0
        );

        if ((sectionsWithDocs?.length ?? 0) > 0) {
          remainingDocs.forEach((doc, index) => {
            const targetSection =
              sectionsWithDocs[index % sectionsWithDocs.length];
            targetSection.relevantDocumentNames!.push(doc);
          });
        }
      }
    }

    // Ensure all sections have at least one document (if documents are available)
    if ((context?.docDescriptions?.length ?? 0) > 0) {
      const sectionsWithoutDocs = mappedSections.filter(
        (s) => !s.relevantDocumentNames || s.relevantDocumentNames.length === 0
      );
      if ((sectionsWithoutDocs?.length ?? 0) > 0) {
        const availableDocs = context.docDescriptions.map(
          (doc) => doc["Doc Name"]
        );
        sectionsWithoutDocs.forEach((section, index) => {
          // Assign at least one document to each section
          const docIndex = index % availableDocs.length;
          section.relevantDocumentNames = [availableDocs[docIndex] as string];
        });
      }
    }

    this.log("info", "Document to section mapping completed", {
      totalSections: mappedSections.length,
      sectionsWithDocs: mappedSections.filter(
        (s) =>
          s.relevantDocumentNames && (s?.relevantDocumentNames?.length ?? 0) > 0
      ).length,
      totalDocuments: context.docDescriptions.length,
    });

    return {
      sectionList: mappedSections as Section[],
    };
  }
}

/**
 * No Main Document Flow Configuration
 *
 * Defines the stage pipeline and configuration for flows that do not have a primary "main" document.
 * This flow generates sections from document summaries and legal task analysis, treating all documents
 * with equal importance for comprehensive legal document drafts.
 */
export const noMainDocFlowConfig: FlowConfig = {
  id: "noMainDocFlow",
  flowType: "noMain",
  name: "No Main Document Flow",
  description:
    "Document drafting flow without a main document, using document summaries for section generation",

  // Stage pipeline - executed in sequence
  stages: [
    {
      name: "setup",
      processor: SetupStageProcessor as unknown as new (
        ...args: unknown[]
      ) => StageProcessor,
      description: "Initialize workspace, load prompts, validate documents",
      options: {},
    },
    {
      name: "documentProcessing",
      processor: DocumentProcessingStageProcessor as unknown as new (
        ...args: unknown[]
      ) => StageProcessor,
      description:
        "Generate descriptions for all documents (no relevance filtering in noMain flow)",
      options: {
        skipRelevanceCheck: true, // In noMain flow, we consider all documents as potentially relevant
      },
    },
    {
      name: "sectionPlanningFromSummaries",
      processor: SectionPlanningStageProcessor as unknown as new (
        ...args: unknown[]
      ) => StageProcessor,
      description:
        "Generate section list from document summaries and legal task",
      options: {
        sectionSource: "summaries",
      },
    },
    {
      name: "documentToSectionMapping",
      processor: DocumentToSectionMappingProcessor as unknown as new (
        ...args: unknown[]
      ) => StageProcessor,
      description:
        "Map documents to relevant sections for comprehensive coverage",
      options: {},
    },
    {
      name: "legalIssueIdentification",
      processor: LegalIssueIdentificationProcessor as unknown as new (
        ...args: unknown[]
      ) => StageProcessor,
      description: "Identify legal issues for each section using LLM analysis",
      options: {
        enableWorkspaceRouting: true,
        temperature: 0.3,
      },
    },
    {
      name: "legalMemoGeneration",
      processor: LegalMemoProcessor as unknown as new (
        ...args: unknown[]
      ) => StageProcessor,
      description: "Generate legal memos for identified issues",
      options: {
        maxRetries: 3,
        enableWorkspaceRouting: true,
      },
    },
    {
      name: "sectionDrafting",
      processor: IterativeSectionDraftingProcessor as unknown as new (
        ...args: unknown[]
      ) => StageProcessor,
      description:
        "Draft sections using iterative processing with contextual documents and memos",
      options: {
        flowType: "noMain",
        temperature: 0.7,
        saveIntermediateResults: true,
      },
    },
    {
      name: "combination",
      processor: CombinationProcessorFactory.createDynamicProcessor(
        "noMain"
      ) as any,
      description: "Combine sections into final document",
      options: {
        includeMetadata: true,
        sendFinalResult: true,
      },
    },
  ],

  prompts: {
    sectionGeneration: "CURRENT_DEFAULT_SECTION_LIST_FROM_SUMMARIES",
    documentSummary: "CURRENT_DEFAULT_DOCUMENT_SUMMARY",
    documentRelevance: "CURRENT_DEFAULT_DOCUMENT_RELEVANCE",
    sectionDrafting: "CURRENT_DEFAULT_SECTION_DRAFTING",
    legalIssueIdentification: "CURRENT_DEFAULT_SECTION_LEGAL_ISSUES",
  } as any,
  metadata: {
    prompts: {
      sectionGeneration: "CURRENT_DEFAULT_SECTION_LIST_FROM_SUMMARIES",
      documentSummary: "CURRENT_DEFAULT_DOCUMENT_SUMMARY",
      documentRelevance: "CURRENT_DEFAULT_DOCUMENT_RELEVANCE",
      sectionDrafting: "CURRENT_DEFAULT_SECTION_DRAFTING",
      legalIssueIdentification: "CURRENT_DEFAULT_SECTION_LEGAL_ISSUES",
    },
    validation: {
      requiredInputs: ["workspace", "user", "message"],
      requiredStages: [
        "setup",
        "documentProcessing",
        "sectionPlanningFromSummaries",
        "combination",
      ],
    },
    options: {
      requireMainDocument: false,
      enableIterativeProcessing: true,
      enableLegalMemoGeneration: true,
      maxSections: 20,
      equalDocumentWeight: true,
      comprehensiveCoverage: true,
    },
  },
};
