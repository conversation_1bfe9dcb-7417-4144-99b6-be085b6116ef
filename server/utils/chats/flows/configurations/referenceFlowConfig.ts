import { SetupStageProcessor } from "../processors/SetupStageProcessor";
import { AgenticCombinationStageProcessor } from "../processors/AgenticCombinationStageProcessor";
import { StageProcessor } from "../core/StageProcessor";
import { IterativeSectionDraftingProcessor } from "../processors/IterativeSectionDraftingProcessor";
import type { FlowConfig } from "../../../../types/chat-flow";
import {
  FlowContext,
  StageDependencies,
  StageProcessor as IStageProcessor,
  ProcessedDocument,
} from "../../../../types/chat-flow";
import { SectionPlanningStageProcessor } from "../processors/SectionPlanningStageProcessor";

/**
 * Reference Files Comparison Flow Configuration
 *
 * Defines the stage pipeline and configuration for compliance analysis flows.
 * This flow categorizes documents into reference files (standards) and review files (to be analyzed),
 * then generates a compliance report comparing review documents against reference standards.
 */
// Type definitions for compliance analysis
interface ComplianceIssue {
  type: "violation" | "gap" | "error";
  severity: "high" | "medium" | "low";
  requirement: string;
  issue: string;
  location: string;
  recommendation: string;
}

// Remove unused ReferenceAnalysis interface

// Remove unused ComplianceSummary interface

// Remove unused interfaces - using generic types from flow context

// Local type definitions for compatibility
interface DocumentForProcessing {
  id: string;
  name: string;
  displayName: string;
  content: string;
  description?: string;
  metadata?: Record<string, unknown>;
  isRelevant?: boolean;
}

export const referenceFlowConfig: FlowConfig = {
  id: "referenceFlow",
  name: "Reference Files Flow",
  flowType: "referenceFiles",
  description:
    "Compliance analysis flow comparing review documents against reference standards",

  // Stage pipeline - executed in sequence (5 stages for compliance analysis)
  stages: [
    {
      name: "setup",
      processor: SetupStageProcessor as unknown as new (
        ...args: unknown[]
      ) => IStageProcessor,
      description: "Initialize workspace, load prompts, validate documents",
      options: {},
    },
    {
      name: "documentCategorization",
      processor: class DocumentCategorizationProcessor extends StageProcessor {
        constructor(...args: unknown[]) {
          const options = (args[0] ?? {}) as Record<string, unknown>;
          super(options);
        }
        async process(
          context: FlowContext & {
            documents: DocumentForProcessing[];
          },
          dependencies: StageDependencies
        ): Promise<
          Partial<FlowContext> & {
            referenceFiles: DocumentForProcessing[];
            reviewFiles: DocumentForProcessing[];
            documentCategories: {
              reference: string[];
              review: string[];
            };
          }
        > {
          const { progressManager, abortChecker, options, workspace } =
            dependencies;

          abortChecker();
          progressManager.updateStep(
            "Categorizing documents into reference files and review files..."
          );

          if (!context.documents || context.documents.length === 0) {
            throw new Error("No documents available for categorization");
          }

          // Log all available options for debugging
          this.log("info", "DocumentCategorizationProcessor - Full options", {
            optionsKeys: Object.keys(options || {}),
            hasReferenceFiles: !!options?.referenceFiles,
            hasCdbOptions: !!options?.cdbOptions,
            referenceFilesValue: options?.referenceFiles,
            cdbOptionsValue: options?.cdbOptions,
            cdbOptions4: options?.cdbOptions?.[4],
          });

          // Check if reference file selection is provided in options
          // The flow dispatcher sets options.referenceFiles directly
          const referenceFileNames: string[] = Array.isArray(
            options.referenceFiles
          )
            ? options.referenceFiles
            : Array.isArray(options.cdbOptions?.[4])
              ? options.cdbOptions[4]
              : [];

          this.log("info", "Starting document categorization", {
            totalDocs: context.documents.length,
            referenceFileNamesFromOptions: referenceFileNames,
            referenceFileNamesLength: referenceFileNames.length,
            referenceFileNamesType: typeof referenceFileNames,
            isArray: Array.isArray(referenceFileNames),
            availableDocs: context.documents?.map(
              (doc: DocumentForProcessing) => ({
                id: doc.id,
                name: doc.name,
                displayName: doc.displayName,
              })
            ),
            workspaceName: workspace.name,
            optionsKeys: Object.keys(options),
            cdbOptions: options.cdbOptions,
          });

          const referenceFiles: DocumentForProcessing[] = [];
          const reviewFiles: DocumentForProcessing[] = [];

          if (referenceFileNames && referenceFileNames.length > 0) {
            // User has explicitly selected reference files
            context.documents?.forEach((doc: DocumentForProcessing) => {
              // Frontend sends reference files as "workspace.name/filename.json"
              // doc.id is the filename without .json extension
              // doc.name has the full filename with .json extension

              const isReference = referenceFileNames.some((refName) => {
                // Extract just the filename from the reference name
                const refFilename = refName.split("/").pop();
                if (!refFilename) return false;

                // The frontend sends .docx filenames, but storage has .docx.json
                // Match by checking if the doc.name is the refFilename + .json
                const filenameMatch =
                  doc.displayName === refFilename ||
                  doc.displayName === `${refFilename}.json` ||
                  doc.id === refFilename ||
                  doc.id === refFilename.replace(".json", "");

                this.log("info", "Comparing for reference file match", {
                  docId: doc.id,
                  docDisplayName: doc.displayName,
                  refNameFromFrontend: refName,
                  extractedRefFilename: refFilename,
                  filenameMatch,
                  workspaceName: workspace.name,
                });

                return filenameMatch;
              });

              if (isReference) {
                referenceFiles.push(doc);
              } else {
                reviewFiles.push(doc);
              }
            });
          } else {
            // Auto-categorization based on document content and naming patterns
            context.documents?.forEach((doc: DocumentForProcessing) => {
              const content = doc.content.toLowerCase();
              const name = (doc.displayName || doc.id).toLowerCase();

              // Heuristics for reference documents
              const isReference =
                name.includes("standard") ||
                name.includes("regulation") ||
                name.includes("policy") ||
                name.includes("guideline") ||
                name.includes("requirement") ||
                name.includes("rule") ||
                content.includes("shall") ||
                content.includes("must") ||
                content.includes("requirement") ||
                content.includes("standard") ||
                content.includes("compliance");

              if (isReference) {
                referenceFiles.push(doc);
              } else {
                reviewFiles.push(doc);
              }
            });
          }

          // Log categorization results before validation
          this.log("info", "Document categorization results", {
            referenceFilesCount: referenceFiles.length,
            reviewFilesCount: reviewFiles.length,
            referenceFiles: referenceFiles.map((f) => ({
              id: f.id,
              displayName: f.displayName,
            })),
            reviewFiles: reviewFiles.map((f) => ({
              id: f.id,
              displayName: f.displayName,
            })),
            hadExplicitSelection:
              referenceFileNames && referenceFileNames.length > 0,
          });

          // Ensure we have both types of documents
          if (referenceFiles.length === 0) {
            this.log("error", "No reference files identified", {
              referenceFileNamesProvided: referenceFileNames,
              documentsAvailable: context.documents.map(
                (d: DocumentForProcessing) => ({
                  id: d.id,
                  name: d.name,
                })
              ),
            });
            throw new Error(
              "No reference files identified. Reference files contain standards, rules, or regulations to compare against."
            );
          }

          if (reviewFiles.length === 0) {
            throw new Error(
              "No review files identified. Review files are documents to be analyzed for compliance."
            );
          }

          this.log("info", "Document categorization completed", {
            totalDocuments: context.documents.length,
            referenceFiles: referenceFiles.length,
            reviewFiles: reviewFiles.length,
          });

          return {
            referenceFiles,
            reviewFiles,
            documentCategories: {
              reference: referenceFiles.map((f) => f.id),
              review: reviewFiles.map((f) => f.id),
            },
          };
        }
      },
      description: "Categorize documents into reference and review files",
      options: {},
    },
    {
      name: "referenceAnalysis",
      processor: class ReferenceAnalysisProcessor extends StageProcessor {
        constructor(...args: unknown[]) {
          const options = (args[0] ?? {}) as Record<string, unknown>;
          super(options);
        }
        async process(
          context: FlowContext & {
            referenceFiles: DocumentForProcessing[];
            allPrompts: Record<
              string,
              { SYSTEM_PROMPT?: string; USER_PROMPT?: string }
            >;
          },
          dependencies: StageDependencies
        ): Promise<
          Partial<FlowContext> & {
            referenceAnalysis: Array<{
              documentId: string;
              displayName: string;
              rawAnalysis: string;
              extractedRequirements: string[];
              keyStandards: string[];
              complianceAreas: string[];
            }>;
            aggregatedRequirements: string[];
            aggregatedStandards: string[];
            complianceAreas: string[];
          }
        > {
          const { progressManager, abortChecker, llmCoordinator, legalTask } =
            dependencies;

          abortChecker();
          progressManager.updateStep(
            "Analyzing reference files to extract rules and standards..."
          );

          if (!context.referenceFiles || context.referenceFiles.length === 0) {
            throw new Error("No reference files available for analysis");
          }

          // Get LLM components
          const _llmConnector = llmCoordinator.getConnector();
          const temperature = llmCoordinator.getTemperature();
          const prompts = context.allPrompts;

          progressManager.updateStep("Extracting compliance requirements...", {
            total: context.referenceFiles.length,
          });

          const referenceAnalysis = [];

          // Analyze each reference file to extract requirements
          for (let index = 0; index < context.referenceFiles.length; index++) {
            const refFile = context.referenceFiles[index];
            abortChecker();

            progressManager.sendSubStepProgress(
              index + 1,
              context.referenceFiles.length,
              `Analyzing: ${refFile.displayName}`,
              refFile.displayName,
              -1
            );

            try {
              // Use LLM to extract requirements and standards from reference document
              const systemPrompt =
                prompts?.CURRENT_DEFAULT_REFERENCE_FILES_DESCRIPTION
                  ?.SYSTEM_PROMPT ||
                `You are an expert legal analyst. Analyze the provided reference document to extract key compliance requirements, standards, and rules. Focus on identifying specific requirements that can be used to evaluate other documents for compliance.`;

              const userPrompt = `Legal Task: ${legalTask}

Reference Document: ${refFile.displayName}

Content:
${refFile.content}

Extract and analyze:
1. Key compliance requirements from this document
2. Important standards or procedures outlined
3. Compliance areas this document covers
4. Specific rules or regulations mentioned

Provide your analysis in a structured format focusing on extractable compliance criteria.`;

              const compressedMessages = await llmCoordinator.compressMessages({
                systemPrompt,
                userPrompt,
              });

              const result = await llmCoordinator.getChatCompletion(
                compressedMessages,
                {
                  temperature,
                }
              );

              const analysisText =
                result.textResponse || "Unable to analyze document";

              // Parse the LLM response to extract structured data
              // This is a simplified extraction - could be enhanced with more sophisticated parsing
              const extractedRequirements = this.extractRequirementsFromText(
                analysisText,
                refFile.displayName
              );
              const keyStandards = this.extractStandardsFromText(
                analysisText,
                refFile.displayName
              );
              const complianceAreas =
                this.extractComplianceAreasFromText(analysisText);

              const analysis = {
                documentId: refFile.id,
                displayName: refFile.displayName,
                rawAnalysis: analysisText,
                extractedRequirements,
                keyStandards,
                complianceAreas,
              };

              referenceAnalysis.push(analysis);

              this.log("info", "Completed reference file analysis", {
                docId: refFile.id,
                requirementsFound: extractedRequirements.length,
                standardsFound: keyStandards.length,
                complianceAreas: complianceAreas.length,
              });
            } catch (error: unknown) {
              this.log("error", "Failed to analyze reference file", {
                docId: refFile.id,
                error: (error as Error).message,
              });

              // Continue with other files instead of failing completely
              const fallbackAnalysis = {
                documentId: refFile.id,
                displayName: refFile.displayName,
                rawAnalysis: "Analysis failed",
                extractedRequirements: [
                  `Requirement from ${refFile.displayName} (analysis failed)`,
                ],
                keyStandards: [
                  `Standard from ${refFile.displayName} (analysis failed)`,
                ],
                complianceAreas: ["General Compliance"],
              };
              referenceAnalysis.push(fallbackAnalysis);
            }

            progressManager.sendSubStepProgress(
              index + 1,
              context.referenceFiles.length,
              `Completed: ${refFile.displayName}`,
              refFile.displayName,
              100
            );
          }

          // Aggregate all requirements across reference files
          const allRequirements = referenceAnalysis.flatMap(
            (analysis) => analysis.extractedRequirements
          );
          const allStandards = referenceAnalysis.flatMap(
            (analysis) => analysis.keyStandards
          );
          const uniqueComplianceAreas = new Set(
            referenceAnalysis.flatMap((analysis) => analysis.complianceAreas)
          );
          const allComplianceAreas = Array.from(uniqueComplianceAreas);

          this.log("info", "Reference analysis completed", {
            referenceFilesAnalyzed: referenceAnalysis.length,
            totalRequirements: allRequirements.length,
            totalStandards: allStandards.length,
            complianceAreas: allComplianceAreas.length,
          });

          return {
            referenceAnalysis,
            aggregatedRequirements: allRequirements,
            aggregatedStandards: allStandards,
            complianceAreas: allComplianceAreas,
          };
        }

        // Helper methods for extracting structured data from LLM responses
        extractRequirementsFromText(text: string, docName: string): string[] {
          const lines = text.split("\n");
          const requirements = [];

          for (const line of lines) {
            if (
              line.toLowerCase().includes("requirement") ||
              line.toLowerCase().includes("must") ||
              line.toLowerCase().includes("shall")
            ) {
              const cleaned = line.trim().replace(/^[•\-*\d.]+\s*/, "");
              if (cleaned.length > 10) {
                requirements.push(cleaned);
              }
            }
          }

          // Ensure we have at least one requirement
          if (requirements.length === 0) {
            requirements.push(
              `Compliance requirement extracted from ${docName}`
            );
          }

          return requirements.slice(0, 5); // Limit to 5 requirements per document
        }

        extractStandardsFromText(text: string, docName: string): string[] {
          const lines = text.split("\n");
          const standards = [];

          for (const line of lines) {
            if (
              line.toLowerCase().includes("standard") ||
              line.toLowerCase().includes("procedure") ||
              line.toLowerCase().includes("guideline")
            ) {
              const cleaned = line.trim().replace(/^[•\-*\d.]+\s*/, "");
              if (cleaned.length > 10) {
                standards.push(cleaned);
              }
            }
          }

          // Ensure we have at least one standard
          if (standards.length === 0) {
            standards.push(`Standard procedure from ${docName}`);
          }

          return standards.slice(0, 3); // Limit to 3 standards per document
        }

        extractComplianceAreasFromText(text: string): string[] {
          const areas = [];
          const commonAreas = [
            "Data Protection",
            "Privacy",
            "Security",
            "Quality Assurance",
            "Operational Procedures",
            "Risk Management",
            "Financial Compliance",
            "Regulatory Compliance",
            "Safety Standards",
            "Environmental Compliance",
          ];

          for (const area of commonAreas) {
            if (text.toLowerCase().includes(area.toLowerCase())) {
              areas.push(area);
            }
          }

          // Ensure we have at least one area
          if (areas.length === 0) {
            areas.push("General Compliance");
          }

          return areas.slice(0, 4); // Limit to 4 areas per document
        }
      },
      description: "Analyze reference files for requirements and standards",
      options: {},
    },
    {
      name: "reviewFileAnalysis",
      processor: class ReviewFileAnalysisProcessor extends StageProcessor {
        constructor(...args: unknown[]) {
          const options = (args[0] ?? {}) as Record<string, unknown>;
          super(options);
        }
        async process(
          context: FlowContext & {
            reviewFiles: DocumentForProcessing[];
            aggregatedRequirements: string[];
            aggregatedStandards: string[];
            complianceAreas: string[];
            referenceAnalysis: Array<{
              documentId: string;
              displayName: string;
              rawAnalysis: string;
              extractedRequirements: string[];
              keyStandards: string[];
              complianceAreas: string[];
            }>;
            allPrompts: Record<
              string,
              { SYSTEM_PROMPT?: string; USER_PROMPT?: string }
            >;
          },
          dependencies: StageDependencies
        ): Promise<
          Partial<FlowContext> & {
            reviewAnalysis: Array<{
              documentId: string;
              displayName: string;
              rawAnalysis: string;
              complianceStatus: string;
              issuesFound: number;
              issues: ComplianceIssue[];
              complianceScore: number;
            }>;
            complianceIssues: ComplianceIssue[];
            overallComplianceScore: number;
            complianceSummary: Record<string, unknown>;
          }
        > {
          const { progressManager, abortChecker, llmCoordinator, legalTask } =
            dependencies;

          abortChecker();
          progressManager.updateStep(
            "Analyzing review files for compliance issues..."
          );

          if (!context.reviewFiles || context.reviewFiles.length === 0) {
            throw new Error("No review files available for analysis");
          }

          if (
            !context.aggregatedRequirements ||
            context.aggregatedRequirements.length === 0
          ) {
            throw new Error(
              "No reference requirements available for comparison"
            );
          }

          // Get LLM components
          const _llmConnector = llmCoordinator.getConnector();
          const temperature = llmCoordinator.getTemperature();
          const prompts = context.allPrompts;

          progressManager.updateStep("Checking compliance issues...", {
            total: context.reviewFiles.length,
          });

          const reviewAnalysis: Array<{
            documentId: string;
            displayName: string;
            rawAnalysis: string;
            complianceStatus: string;
            issuesFound: number;
            issues: ComplianceIssue[];
            complianceScore: number;
          }> = [];
          const complianceIssues: ComplianceIssue[] = [];

          // Analyze each review file against reference requirements
          for (let index = 0; index < context.reviewFiles.length; index++) {
            const reviewFile = context.reviewFiles[index];
            abortChecker();

            progressManager.sendSubStepProgress(
              index + 1,
              context.reviewFiles.length,
              `Analyzing: ${reviewFile.displayName}`,
              reviewFile.displayName,
              -1
            );

            try {
              // Use LLM to compare review file against requirements
              const systemPrompt =
                prompts?.CURRENT_DEFAULT_REVIEW_FILES_DESCRIPTION
                  ?.SYSTEM_PROMPT ||
                `You are an expert compliance analyst. Compare the provided review document against the reference requirements to identify compliance issues, gaps, and violations. Provide specific, actionable findings.`;

              const userPrompt = `Legal Task: ${legalTask}

Reference Requirements:
${context.aggregatedRequirements.join("\n- ")}

Review Document: ${reviewFile.displayName}

Content:
${reviewFile.content}

Analyze this document for compliance issues by:
1. Identifying specific violations of the reference requirements
2. Finding gaps where required elements are missing
3. Assessing overall compliance level
4. Providing specific recommendations for remediation

Focus on concrete, actionable findings with specific locations or sections where possible.`;

              const compressedMessages = await llmCoordinator.compressMessages({
                systemPrompt,
                userPrompt,
              });

              const result = await llmCoordinator.getChatCompletion(
                compressedMessages,
                {
                  temperature,
                }
              );

              const complianceAnalysisText =
                result.textResponse || "Unable to analyze compliance";

              // Parse the LLM response to extract issues
              const issues = this.extractComplianceIssuesFromText(
                complianceAnalysisText,
                reviewFile.displayName,
                context.aggregatedRequirements
              );
              const complianceScore = this.calculateComplianceScore(issues);

              const analysis = {
                documentId: reviewFile.id,
                displayName: reviewFile.displayName,
                rawAnalysis: complianceAnalysisText,
                complianceStatus:
                  complianceScore >= 80 ? "compliant" : "non-compliant",
                issuesFound: issues.length,
                issues,
                complianceScore,
              };

              reviewAnalysis.push(analysis);
              complianceIssues.push(...issues);

              this.log("info", "Completed review file analysis", {
                docId: reviewFile.id,
                complianceScore,
                issuesFound: issues.length,
                status: analysis.complianceStatus,
              });
            } catch (error: unknown) {
              this.log("error", "Failed to analyze review file", {
                docId: reviewFile.id,
                error: (error as Error).message,
              });

              // Continue with other files
              const fallbackAnalysis = {
                documentId: reviewFile.id,
                displayName: reviewFile.displayName,
                rawAnalysis: "Analysis failed",
                complianceStatus: "unknown",
                issuesFound: 1,
                issues: [
                  {
                    type: "error" as const,
                    severity: "medium" as const,
                    requirement: "Analysis requirement",
                    issue: `Failed to analyze ${reviewFile.displayName}`,
                    location: "Document",
                    recommendation: "Retry analysis or manual review required",
                  } as ComplianceIssue,
                ],
                complianceScore: 50,
              };
              reviewAnalysis.push(fallbackAnalysis);
              complianceIssues.push(...fallbackAnalysis.issues);
            }

            progressManager.sendSubStepProgress(
              index + 1,
              context.reviewFiles.length,
              `Completed: ${reviewFile.displayName}`,
              reviewFile.displayName,
              100
            );
          }

          this.log("info", "Review file analysis completed", {
            reviewFilesAnalyzed: reviewAnalysis.length,
            totalIssuesFound: complianceIssues.length,
            averageComplianceScore:
              reviewAnalysis.reduce((sum, r) => sum + r.complianceScore, 0) /
              reviewAnalysis.length,
          });

          // Create a compliance summary for section drafting
          const complianceSummary = {
            referenceAnalysis: context.referenceAnalysis,
            reviewAnalysis,
            complianceIssues,
            overallComplianceScore:
              reviewAnalysis.reduce((sum, r) => sum + r.complianceScore, 0) /
              reviewAnalysis.length,
            aggregatedRequirements: context.aggregatedRequirements,
            aggregatedStandards: context.aggregatedStandards,
            complianceAreas: context.complianceAreas,
          };

          return {
            reviewAnalysis,
            complianceIssues,
            overallComplianceScore: complianceSummary.overallComplianceScore,
            complianceSummary, // Add this for section drafting
          };
        }

        // Helper methods for parsing compliance analysis
        extractComplianceIssuesFromText(
          text: string,
          docName: string,
          requirements: string[]
        ): ComplianceIssue[] {
          const issues = [];
          const lines = text.split("\n");

          // Look for violations, gaps, and issues in the text
          for (const line of lines) {
            const lowerLine = line.toLowerCase();
            if (
              lowerLine.includes("violation") ||
              lowerLine.includes("missing") ||
              lowerLine.includes("gap") ||
              lowerLine.includes("issue") ||
              lowerLine.includes("non-compliant") ||
              lowerLine.includes("fails to")
            ) {
              let severity: "high" | "medium" | "low" = "medium";
              if (
                lowerLine.includes("critical") ||
                lowerLine.includes("high") ||
                lowerLine.includes("major")
              ) {
                severity = "high";
              } else if (
                lowerLine.includes("minor") ||
                lowerLine.includes("low")
              ) {
                severity = "low";
              }

              let type: "violation" | "gap" | "error" = "gap";
              if (
                lowerLine.includes("violation") ||
                lowerLine.includes("violates")
              ) {
                type = "violation";
              }

              const cleaned = line.trim().replace(/^[•\-*\d.]+\s*/, "");
              if (cleaned.length > 15) {
                issues.push({
                  type,
                  severity,
                  requirement: requirements[0] || "General requirement",
                  issue: cleaned,
                  location: "Document content",
                  recommendation: "Review and address identified issue",
                });
              }
            }
          }

          // If no specific issues found but analysis suggests problems, create a general issue
          if (
            issues.length === 0 &&
            text.toLowerCase().includes("non-compliant")
          ) {
            issues.push({
              type: "gap" as const,
              severity: "medium" as const,
              requirement: requirements[0] || "General requirement",
              issue: `Compliance concerns identified in ${docName}`,
              location: "Document review",
              recommendation: "Detailed review recommended",
            });
          }

          return issues.slice(0, 5); // Limit to 5 issues per document
        }

        calculateComplianceScore(issues: ComplianceIssue[]): number {
          if (issues.length === 0) return 95;

          let score = 100;
          for (const issue of issues) {
            switch (issue.severity) {
              case "high":
                score = score - 25;
                break;
              case "medium":
                score = score - 15;
                break;
              case "low":
                score = score - 5;
                break;
            }
          }

          return Math.max(0, score);
        }
      },
      description: "Analyze review files for compliance issues",
      options: {},
    },
    {
      name: "documentDescriptionGeneration",
      processor: class DocumentDescriptionProcessor extends StageProcessor {
        constructor(...args: unknown[]) {
          const options = (args[0] ?? {}) as Record<string, unknown>;
          super(options);
        }
        async process(
          context: FlowContext & {
            referenceFiles: DocumentForProcessing[];
            reviewFiles: DocumentForProcessing[];
            allPrompts: Record<
              string,
              { SYSTEM_PROMPT?: string; USER_PROMPT?: string }
            >;
          },
          dependencies: StageDependencies
        ): Promise<
          Partial<FlowContext> & {
            docDescriptions: Array<{
              "Doc Name": string;
              DisplayName: string;
              Description: string;
              DocumentType: string;
            }>;
            processedDocuments: ProcessedDocument[];
          }
        > {
          const { progressManager, abortChecker, llmCoordinator, legalTask } =
            dependencies;

          abortChecker();
          progressManager.updateStep(
            "Generating document descriptions for compliance analysis..."
          );

          if (!context.referenceFiles || !context.reviewFiles) {
            throw new Error("Reference files and review files are required");
          }

          // Get LLM components
          const _llmConnector = llmCoordinator.getConnector();
          const temperature = llmCoordinator.getTemperature();
          const prompts = context.allPrompts;

          const allFiles = [...context.referenceFiles, ...context.reviewFiles];
          const docDescriptions: Array<{
            "Doc Name": string;
            DisplayName: string;
            Description: string;
            DocumentType: string;
          }> = [];

          progressManager.updateStep("Creating document summaries...", {
            total: allFiles.length,
          });

          // Generate descriptions for all documents (reference and review)
          for (let index = 0; index < allFiles.length; index++) {
            const document = allFiles[index];
            abortChecker();

            progressManager.sendSubStepProgress(
              index + 1,
              allFiles.length,
              `Processing: ${document.displayName}`,
              document.displayName,
              -1
            );

            try {
              // Determine if this is a reference or review file for context
              const isReference = context.referenceFiles.includes(document);
              const documentType = isReference ? "reference" : "review";

              const systemPrompt = isReference
                ? prompts?.CURRENT_DEFAULT_REFERENCE_FILES_DESCRIPTION
                    ?.SYSTEM_PROMPT ||
                  `You are analyzing a reference document that contains standards, rules, or regulations. Focus on extracting compliance requirements and standards.`
                : prompts?.CURRENT_DEFAULT_REVIEW_FILES_DESCRIPTION
                    ?.SYSTEM_PROMPT ||
                  `You are analyzing a review document that needs to be checked for compliance. Focus on identifying the document's content and compliance posture.`;

              const userPrompt = `Legal Task: ${legalTask}

Document Type: ${documentType} document
Document Name: ${document.displayName}

Content:
${document.content}

Provide a comprehensive description of this document focusing on:
${
  isReference
    ? "- Compliance requirements and standards it defines\n- Rules and regulations it contains\n- Key compliance areas it covers"
    : "- Main content and purpose\n- Compliance-relevant aspects\n- Areas that may need compliance review"
}

Create a detailed but concise description suitable for compliance analysis.`;

              const compressedMessages = await llmCoordinator.compressMessages({
                systemPrompt,
                userPrompt,
              });

              const result = await llmCoordinator.getChatCompletion(
                compressedMessages,
                {
                  temperature,
                }
              );

              const description =
                result.textResponse ||
                `Description for ${document.displayName}`;

              docDescriptions.push({
                "Doc Name": document.id,
                DisplayName: document.displayName,
                Description: description,
                DocumentType: documentType,
              });

              this.log("info", "Generated document description", {
                docId: document.id,
                documentType,
                descriptionLength: description.length,
              });
            } catch (error: unknown) {
              this.log("error", "Failed to generate document description", {
                docId: document.id,
                error: (error as Error).message,
              });

              // Fallback description
              const isReference = context.referenceFiles.includes(document);
              docDescriptions.push({
                "Doc Name": document.id,
                DisplayName: document.displayName,
                Description: `${isReference ? "Reference" : "Review"} document: ${document.displayName}. Analysis pending.`,
                DocumentType: isReference ? "reference" : "review",
              });
            }

            progressManager.sendSubStepProgress(
              index + 1,
              allFiles.length,
              `Completed: ${document.displayName}`,
              document.displayName,
              100
            );
          }

          this.log("info", "Document description generation completed", {
            totalDescriptions: docDescriptions.length,
            referenceDescriptions: docDescriptions.filter(
              (d) => d.DocumentType === "reference"
            ).length,
            reviewDescriptions: docDescriptions.filter(
              (d) => d.DocumentType === "review"
            ).length,
          });

          // Create processedDocuments array for compatibility with IterativeSectionDraftingProcessor
          // This combines all documents (reference and review) with their descriptions
          const processedDocuments = docDescriptions.map((desc) => {
            // Find the original document
            const originalDoc = allFiles.find(
              (doc) => doc.id === desc["Doc Name"]
            );

            return {
              id: desc["Doc Name"],
              fileName: desc["Doc Name"],
              displayName: desc.DisplayName,
              content: originalDoc?.content || "",
              metadata: (originalDoc?.metadata || {}) as Record<
                string,
                unknown
              >,
            } as ProcessedDocument;
          });

          return {
            docDescriptions,
            processedDocuments,
          };
        }
      },
      description: "Generate document descriptions for all files",
      options: {},
    },
    {
      name: "complianceSectionGeneration",
      processor: SectionPlanningStageProcessor,
      description:
        "Generate compliance report section structure based on analysis results",
      options: {
        sectionSource: "summaries",
        // Override prompts for reference flow
        promptOverrides: {
          sectionGenerationPrompt: "CURRENT_DEFAULT_REFERENCE_REVIEW_SECTIONS",
        },
      },
    },
    {
      name: "complianceSectionDrafting",
      processor: IterativeSectionDraftingProcessor as unknown as new (
        ...args: unknown[]
      ) => IStageProcessor,
      description:
        "Draft sections of the compliance report based on analysis results",
      options: {
        flowType: "referenceFiles",
        temperature: 0.7,
        saveIntermediateResults: true,
      },
    },
    {
      name: "combination",
      processor: AgenticCombinationStageProcessor as unknown as new (
        ...args: unknown[]
      ) => IStageProcessor,
      description: "Combine sections into final compliance report",
      options: {
        includeMetadata: true,
        sendFinalResult: true,
      },
    },
  ],

  // Prompt configurations
  prompts: {
    documentCategorization: {
      SYSTEM_PROMPT: "CURRENT_DEFAULT_DOCUMENT_CATEGORIZATION",
    },
    referenceAnalysis: {
      SYSTEM_PROMPT: "CURRENT_DEFAULT_REFERENCE_ANALYSIS",
    },
    reviewAnalysis: {
      SYSTEM_PROMPT: "CURRENT_DEFAULT_REVIEW_ANALYSIS",
    },
    complianceReporting: {
      SYSTEM_PROMPT: "CURRENT_DEFAULT_COMPLIANCE_REPORTING",
    },
  },

  // Flow-specific options
  options: {
    requireReferenceFiles: true,
    requireReviewFiles: true,
    enableComplianceScoring: true,
    enableIssueTracking: true,
    maxComplianceAreas: 10,
    complianceThreshold: 80,
    generateRecommendations: true,
  },

  // Flow validation rules
  validation: {
    requiredInputs: ["workspace", "user", "message"],
    requiredStages: [
      "setup",
      "documentCategorization",
      "referenceAnalysis",
      "reviewFileAnalysis",
      "documentDescriptionGeneration",
      "complianceSectionGeneration",
      "complianceSectionDrafting",
      "combination",
    ],
    minimumDocuments: 2,
  },
};
