import {
  create<PERSON>bor<PERSON><PERSON><PERSON><PERSON>,
  handleAbortSignal,
} from "../../helpers/documentProcessing";
import { purgeDocumentBuilder } from "../../../files";
import { writeResponseChunk } from "../../../helpers/chat/responses";

import { ProgressManager } from "./ProgressManager";
import { WorkspaceManager } from "./WorkspaceManager";
import { LLMCoordinator } from "./LLMCoordinator";
import { Response as ExpressResponse } from "express";
import type {
  FlowOptions,
  FlowConfig,
  FlowContext,
  FlowOrchestratorStage,
  ExecutionSummary,
  StageProcessor,
  WorkspaceData,
  UserData,
  StageDependencies,
} from "../../../../types/chat-flow";
import type { FilteredUser } from "../../../../types/models";
import type { workspaces as PrismaWorkspace } from "@prisma/client";
import { ChatError } from "../../../../types/chat-agent";
import { ContextWindowManager } from "../../helpers/contextWindowManager";
import { TokenTracker } from "../../helpers/tokenTracker";

/**
 * Flow Orchestrator
 *
 * Manages the execution of document drafting flows using configurable stage processors.
 * Provides a unified interface for running different flow types with shared infrastructure.
 */
export class FlowOrchestrator {
  options: FlowOptions;
  config: FlowConfig;
  stages: FlowOrchestratorStage[] = [];
  context: FlowContext = {
    // Flow execution data that gets passed between stages
    documents: [],
    processedDocuments: [],
    docDescriptions: [],
    sectionList: [],
    legalIssues: [],
    memos: [],
    sectionOutputs: [],
    finalContent: "",
    metrics: {},
    errors: [],
  };

  // Shared infrastructure
  progressManager: ProgressManager | null = null;
  workspaceManager: WorkspaceManager | null = null;
  llmCoordinator: LLMCoordinator | null = null;
  abortChecker: (() => void) | null = null;

  // Extract key options
  chatId: string;
  response: ExpressResponse;
  workspace: WorkspaceData;
  user: UserData;
  legalTask: string;
  abortSignal?: AbortSignal;
  customInstructions: string;
  flowType: string;

  constructor(options: FlowOptions, flowConfig: FlowConfig) {
    this.options = options;
    this.config = flowConfig;

    const {
      chatId,
      workspace,
      user,
      message: legalTask,
      abortSignal,
      cdbOptions = [],
    } = options;

    this.chatId = chatId;
    this.response = options.response;
    this.workspace = workspace;
    this.user = user;
    this.legalTask = legalTask;
    this.abortSignal = abortSignal;
    this.customInstructions = cdbOptions[1] || "";

    // Determine flow type from config
    this.flowType = flowConfig.flowType || "unknown";
  }

  /**
   * Initialize the flow orchestrator with shared infrastructure
   * @returns {Promise<void>}
   */
  async initialize(): Promise<void> {
    try {
      console.log(
        `[FlowOrchestrator] Initializing ${this.flowType} flow for chatId: ${this.chatId}`
      );

      // Initialize progress manager
      this.progressManager = new ProgressManager(
        this.response,
        this.chatId,
        this.flowType
      );
      this.progressManager.setTotalSteps(this.config.stages.length);

      console.log(
        `[FlowOrchestrator] Progress manager initialized with ${this.config.stages.length} total steps`
      );

      // Initialize workspace manager
      this.workspaceManager = new WorkspaceManager(
        this.user as unknown as FilteredUser,
        this.workspace as PrismaWorkspace
      );
      await this.workspaceManager.initialize();

      // Initialize LLM coordinator with settings_suffix if available
      this.llmCoordinator = new LLMCoordinator(
        this.workspace as PrismaWorkspace,
        {
          settings_suffix:
            (this.options.settings_suffix as string) || undefined,
        }
      );
      await this.llmCoordinator.initialize();

      // Create abort checker
      this.abortChecker = createAbortChecker(
        this.abortSignal,
        this.chatId,
        this.response,
        this.flowType
      );

      // Build stage pipeline from configuration
      this.buildStagePipeline();

      // Store initialization data in context
      this.context.initialization = {
        workspaceInfo: this.workspaceManager.getWorkspaceInfo(),
        llmProviderInfo: this.llmCoordinator.getProviderInfo(),
        availableContextWindow: this.llmCoordinator.getAvailableContextWindow(),
      };
    } catch (error) {
      console.error(
        `[FlowOrchestrator] Initialization failed for ${this.flowType} flow:`,
        error
      );
      this?.progressManager?.sendFinalError(
        error instanceof Error ? error : new Error(String(error))
      );
      throw error;
    }
  }

  /**
   * Build the stage pipeline from flow configuration
   * @private
   */
  private buildStagePipeline(): void {
    this.stages = [];

    for (const stageConfig of this.config.stages) {
      if (!stageConfig.processor) {
        throw new Error(
          `Stage configuration missing processor: ${JSON.stringify(stageConfig)}`
        );
      }

      // Create stage processor instance
      const StageProcessorClass = stageConfig.processor;
      const stageOptions = {
        ...stageConfig.options,
        prompts: this.config.prompts,
        flowType: this.flowType,
      };

      const processor = new StageProcessorClass(stageOptions);

      this.stages.push({
        name: stageConfig.name,
        processor,
        config: stageConfig,
      });
    }
  }

  /**
   * Execute the complete flow
   * @returns {Promise<string>} Final document content
   */
  async execute(): Promise<string> {
    const startTime = Date.now();

    try {
      console.log(
        `[FlowOrchestrator] Starting ${this.flowType} flow execution with ${this.stages.length} stages`
      );

      // Execute each stage in sequence
      for (let i = 0; i < this.stages.length; i++) {
        const stage = this.stages[i];
        await this.executeStage(stage, i + 1);
      }

      // Calculate execution metrics
      const endTime = Date.now();
      this.context.metrics.totalExecutionTime = Math.round(
        (endTime - startTime) / 1000
      );
      this.context.metrics.llmMetrics = this?.llmCoordinator?.getMetrics();

      // Generate comprehensive token usage report like legacy system
      const tokenReport = this?.llmCoordinator?.generateTokenReport() || {};
      console.log(
        `[${this.flowType.toUpperCase()} FLOW] Token Usage Report:`,
        JSON.stringify(tokenReport, null, 2)
      );

      // Add token usage to metrics for compatibility with legacy system
      // Define proper interfaces for token report structure
      interface TokenReportData {
        tokenTracker?: {
          summary?: Record<string, unknown>;
          budgetAnalysis?: {
            budgetUtilization?: number;
          };
          iterativeMetrics?: {
            totalIterations?: number;
          };
        };
        connector?: Record<string, unknown>;
        contextManager?: Record<string, unknown>;
      }

      const tokenReportData = tokenReport as TokenReportData;
      const tokenTracker = tokenReportData?.tokenTracker || {};
      const budgetAnalysis = tokenTracker?.budgetAnalysis || {};
      const iterativeMetrics = tokenTracker?.iterativeMetrics || {};

      this.context.metrics.tokenUsageReport = {
        summary: tokenTracker?.summary || {},
        utilizationRate: budgetAnalysis?.budgetUtilization || 0,
        totalIterations: iterativeMetrics?.totalIterations || 0,
        iterativeProcessingUsed:
          (iterativeMetrics?.totalIterations || 0) >
          (this.context?.sectionOutputs?.length || 0),
        providerInfo: tokenReportData.connector || {},
        contextManager: tokenReportData.contextManager || {},
      };

      // Send final completion
      this?.progressManager?.sendFinalCompletion(
        "Document drafting completed successfully",
        {
          finalContent: this.context.finalContent,
          metrics: this.context.metrics,
          sectionCount: this.context.sectionOutputs.length,
        }
      );

      console.log(
        `[FlowOrchestrator] ${this.flowType} flow completed successfully in ${this.context.metrics.totalExecutionTime}s`
      );

      return this.context.finalContent;
    } catch (error) {
      const err = error as Error & { isAbortSignal?: boolean };

      // Handle abort signals gracefully
      if (err.isAbortSignal) {
        console.log(
          `[FlowOrchestrator] ${this.flowType} flow aborted by client`
        );
        // Use the shared abort handler with proper cleanup
        try {
          // Create a proper ChatError for the abort handler
          const chatError = new ChatError(
            err.message || "Flow execution was aborted",
            "FLOW_ABORTED",
            { originalError: err }
          );
          chatError.isAbortSignal = true;
          chatError.chatId = this.chatId;
          chatError.flowType = this.flowType;

          await handleAbortSignal(
            chatError,
            purgeDocumentBuilder,
            writeResponseChunk,
            this.response
          );
        } catch (handlerError) {
          console.error(
            `[FlowOrchestrator] Abort handler failed:`,
            handlerError
          );
        }
        return "";
      }

      console.error(
        `[FlowOrchestrator] Flow execution failed for ${this.flowType}:`,
        error
      );

      this?.progressManager?.sendFinalError(
        err instanceof Error ? err : new Error(String(err))
      );
      this.context.errors.push({
        stage: "flow-execution",
        error: err instanceof Error ? err.message : String(err),
        timestamp: new Date().toISOString(),
      });

      throw error;
    } finally {
      // Cleanup temporary files
      await this.cleanup();
    }
  }

  /**
   * Execute a single stage
   * @param {FlowOrchestratorStage} stage - Stage configuration with processor
   * @param {number} stepNumber - Current step number
   * @private
   */
  private async executeStage(
    stage: FlowOrchestratorStage,
    stepNumber: number
  ): Promise<void> {
    const { name, processor, config: _config } = stage;
    const stageStartTime = Date.now();

    try {
      // Check abort before starting stage
      this.abortChecker?.();

      // Check if stage should be skipped
      if (processor.shouldSkip && processor.shouldSkip(this.context)) {
        console.log(
          `[FlowOrchestrator] Skipping stage ${name} (conditions not met)`
        );
        return;
      }

      // Validate stage inputs
      if (processor.validateInputs && !processor.validateInputs(this.context)) {
        throw new Error(`Stage ${name} input validation failed`);
      }

      // Start stage progress
      console.log(`[FlowOrchestrator] Starting stage ${stepNumber}: ${name}`);
      this?.progressManager?.startStep(stepNumber, `Starting ${name} stage...`);

      console.log(`[FlowOrchestrator] Executing stage ${stepNumber}: ${name}`);

      // Prepare dependencies for the stage
      const dependencies: StageDependencies = {
        progressManager: this.progressManager!,
        workspaceManager: this.workspaceManager!,
        llmCoordinator: this.llmCoordinator!,
        contextWindowManager:
          this.llmCoordinator!.getContextManager() as ContextWindowManager,
        tokenTracker: this.llmCoordinator!.getTokenTracker() as TokenTracker,
        abortChecker: this.abortChecker!,
        legalTask: this.legalTask,
        customInstructions: this.customInstructions,
        chatId: this.chatId,
        workspace: this.workspace,
        user: this.user,
        options: {
          ...this.options,
          // Pass resolved prompts from context if available
          prompts: this.context.allPrompts || this.options.prompts,
        },
        flowType: this.flowType,
      };

      // Execute the stage
      console.log(
        `[FlowOrchestrator] Processing stage ${name} with dependencies`
      );
      const stageResult = await processor.process(this.context, dependencies);
      console.log(
        `[FlowOrchestrator] Stage ${name} process() completed, result type:`,
        typeof stageResult
      );

      // Update context with stage results
      if (stageResult && typeof stageResult === "object") {
        Object.assign(this.context, stageResult);
      }

      // Track stage execution time
      const stageEndTime = Date.now();
      const stageExecutionTime = Math.round(
        (stageEndTime - stageStartTime) / 1000
      );
      this.context.metrics[`${name}_execution_time`] = stageExecutionTime;

      // Complete stage progress
      this?.progressManager?.completeStep(
        `${name} stage completed in ${stageExecutionTime}s`
      );

      console.log(
        `[FlowOrchestrator] Stage ${name} completed successfully in ${stageExecutionTime}s`
      );
    } catch (error) {
      const err = error as Error & { isAbortSignal?: boolean };

      // Handle abort signals gracefully
      if (err.isAbortSignal) {
        console.log(`[FlowOrchestrator] Stage ${name} aborted by client`);
        // Send abort signal to progress manager
        this?.progressManager?.sendAbort?.();
        // Re-throw abort signal to propagate up the chain
        throw error;
      }

      console.error(`[FlowOrchestrator] Stage ${name} failed:`, error);

      // Track stage error
      this.context.errors.push({
        stage: name,
        error: err instanceof Error ? err.message : String(err),
        timestamp: new Date().toISOString(),
      });

      // Send stage error progress
      this?.progressManager?.sendError(
        err instanceof Error ? err : new Error(String(err)),
        stepNumber
      );

      // Re-throw to stop flow execution
      throw error;
    }
  }

  /**
   * Add a stage to the pipeline
   * @param {string} stageName - Name of the stage
   * @param {StageProcessor} stageProcessor - Stage processor instance
   * @param {Record<string, unknown>} stageConfig - Additional stage configuration
   */
  addStage(
    stageName: string,
    stageProcessor: StageProcessor,
    stageConfig: Record<string, unknown> = {}
  ): void {
    this.stages.push({
      name: stageName,
      processor: stageProcessor,
      config: {
        name: stageName,
        processor: stageProcessor.constructor as new (
          ...args: unknown[]
        ) => StageProcessor,
        ...stageConfig,
      },
    });
  }

  /**
   * Get current context data
   * @returns {FlowContext} Current context
   */
  getContext(): FlowContext {
    return { ...this.context };
  }

  /**
   * Update context data
   * @param {Partial<FlowContext>} updates - Context updates to apply
   */
  updateContext(updates: Partial<FlowContext>): void {
    Object.assign(this.context, updates);
  }

  /**
   * Get shared infrastructure instances
   * @returns {Object} Infrastructure instances
   */
  getInfrastructure() {
    return {
      progressManager: this.progressManager,
      workspaceManager: this.workspaceManager,
      llmCoordinator: this.llmCoordinator,
      abortChecker: this.abortChecker,
    };
  }

  /**
   * Cleanup resources and temporary files
   * @private
   */
  private async cleanup(): Promise<void> {
    try {
      console.log(
        `[FlowOrchestrator] Starting cleanup for ${this.flowType} flow`
      );

      // Purge temporary document builder files
      const removed = purgeDocumentBuilder({ uuid: this.chatId });
      if (removed > 0) {
        console.log(`[FlowOrchestrator] Cleaned up ${removed} temporary files`);
      }

      // Reset LLM metrics for next flow
      if (this.llmCoordinator) {
        this.llmCoordinator.resetMetrics();
      }
    } catch (cleanupError) {
      console.error(
        `[FlowOrchestrator] Cleanup failed for ${this.flowType} flow:`,
        cleanupError
      );
      // Don't throw cleanup errors - they shouldn't fail the overall flow
    }
  }

  /**
   * Get execution summary
   * @returns {ExecutionSummary} Flow execution summary
   */
  getExecutionSummary(): ExecutionSummary {
    return {
      flowType: this.flowType,
      chatId: this.chatId,
      stagesExecuted: this.stages.length,
      documentsProcessed: this.context.documents.length,
      sectionsGenerated: this.context.sectionList.length,
      sectionsDrafted: this.context.sectionOutputs.length,
      errors: this.context.errors,
      metrics: this.context.metrics,
      finalContentLength: this.context.finalContent
        ? this.context.finalContent.length
        : 0,
    };
  }
}
