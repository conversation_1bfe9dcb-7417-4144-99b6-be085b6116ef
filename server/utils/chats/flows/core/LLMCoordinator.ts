import { <PERSON><PERSON><PERSON><PERSON>ider } from "../../../helpers";
import { ContextWindowManager } from "../../helpers/contextWindowManager";
import { TokenTracker } from "../../helpers/tokenTracker";
import { LLMConnector } from "../../LLMConnector";

import type {
  LLMCoordinatorOptions,
  TokenBudgetParams,
  TokenBudget,
  ChatCompletionResult,
  LLMProviderInfo,
} from "../../../../types/chat-flow";
import type { Workspace } from "../../../../types/models";
import type {
  ChatMessage,
  CompletionOptions,
} from "../../../../types/ai-providers";

/**
 * LLM Coordinator
 *
 * Centralized management of LLM connections, context window management, and token tracking
 * for document drafting flows.
 */
export class LLMCoordinator {
  workspace: Workspace;
  options: LLMCoordinatorOptions;
  connector: LLMConnector | null = null;
  contextManager: ContextWindowManager | null = null;
  tokenTracker: TokenTracker | null = null;
  temperature: number | null = null;
  private _initialized: boolean = false;

  constructor(workspace: Workspace, options: LLMCoordinatorOptions = {}) {
    this.workspace = workspace;
    this.options = {
      enableTokenTracking: true,
      enableContextManagement: true,
      maxIterations: 10,
      reservedOutputTokens: 4000,
      ...options,
    };
  }

  /**
   * Initialize the LLM coordinator with provider and configuration
   * @returns {Promise<void>}
   */
  async initialize(): Promise<void> {
    if (this._initialized) {
      return;
    }

    // Setup LLM connector
    this.connector = this.setupLLMConnector();

    // Setup temperature
    this.temperature =
      this?.workspace?.openAiTemp ?? this.connector.getProvider().defaultTemp;

    // Setup context window manager if enabled
    if (this.options.enableContextManagement) {
      this.contextManager = new ContextWindowManager(this.connector, {
        maxIterations: this.options.maxIterations,
        reservedOutputTokens: this.options.reservedOutputTokens,
        enableTokenTracking: this.options.enableTokenTracking,
        logTokenUsage: this.options.logTokenUsage || false,
      });
    }

    // Setup token tracker if enabled
    if (this.options.enableTokenTracking) {
      this.tokenTracker = new TokenTracker(this.connector, {
        enableDetailedTracking: true,
        trackContentTypes: true,
        logTokenUsage: this.options.logTokenUsage || false,
      });
    }

    this._initialized = true;
  }

  /**
   * Setup LLM connector based on environment configuration
   * @private
   * @returns {BaseLLMProvider} LLM connector instance
   */
  private setupLLMConnector(): LLMConnector {
    // Check for CDB-specific LLM provider
    if (process.env.LLM_PROVIDER_CDB) {
      let modelPrefCDB: string | undefined;

      switch (process.env.LLM_PROVIDER_CDB.toLowerCase()) {
        case "openai":
          modelPrefCDB = process.env.OPEN_MODEL_PREF_CDB;
          break;
        case "anthropic":
          modelPrefCDB = process.env.ANTHROPIC_MODEL_PREF_CDB;
          break;
        case "gemini":
          modelPrefCDB = process.env.GEMINI_LLM_MODEL_PREF_CDB;
          break;
        case "lmstudio":
          modelPrefCDB = process.env.LMSTUDIO_MODEL_PREF_CDB;
          break;
        case "azure":
          modelPrefCDB = process.env.AZURE_OPENAI_MODEL_PREF_CDB;
          break;
        case "ollama":
          modelPrefCDB = process.env.OLLAMA_MODEL_PREF_CDB;
          break;
        default:
          modelPrefCDB = process.env.OPEN_MODEL_PREF_CDB; // Fallback
          break;
      }

      return new LLMConnector({
        provider: process.env.LLM_PROVIDER_CDB,
        model: modelPrefCDB || undefined,
      });
    } else {
      // Use standard system LLM
      return new LLMConnector();
    }
  }

  /**
   * Get the LLM connector instance
   * @returns {LLMConnector} LLM connector
   */
  getConnector(): LLMConnector {
    this._ensureInitialized();
    return this.connector!;
  }

  /**
   * Get the context window manager instance
   * @returns {ContextWindowManager|null} Context manager or null if disabled
   */
  getContextManager(): ContextWindowManager | null {
    this._ensureInitialized();
    return this.contextManager;
  }

  /**
   * Get the token tracker instance
   * @returns {TokenTracker|null} Token tracker or null if disabled
   */
  getTokenTracker(): TokenTracker | null {
    this._ensureInitialized();
    return this.tokenTracker;
  }

  /**
   * Get the configured temperature
   * @returns {number} Temperature value
   */
  getTemperature(): number {
    this._ensureInitialized();
    return this.temperature!;
  }

  /**
   * Get available context window size
   * @returns {number} Available tokens for content
   */
  getAvailableContextWindow(): number {
    this._ensureInitialized();

    if (this.contextManager) {
      return this.contextManager.getAvailableContextWindow();
    }

    // Fallback calculation
    const contextWindowLimit = this.connector!.promptWindowLimit();
    const reserved = this.options.reservedOutputTokens || 4000;
    return Math.floor(contextWindowLimit * 0.7) - reserved;
  }

  /**
   * Calculate token budget for a specific operation
   * @param {TokenBudgetParams} params - Budget parameters
   * @returns {TokenBudget} Token budget breakdown
   */
  calculateTokenBudget(params: TokenBudgetParams): TokenBudget {
    this._ensureInitialized();

    if (this.contextManager) {
      return this.contextManager.calculateTokenBudget(params);
    }

    // Fallback budget calculation
    const totalTokens = this.getAvailableContextWindow();
    return {
      totalTokens,
      reservedTokens: this.options.reservedOutputTokens || 4000,
      availableTokens: totalTokens,
    };
  }

  /**
   * Perform a chat completion with token tracking
   * @param {ChatMessage[]} messages - Messages for completion
   * @param {Partial<CompletionOptions>} options - Completion options
   * @param {string | null} stage - Stage name for tracking
   * @param {string | null} operation - Operation name for tracking
   * @returns {Promise<ChatCompletionResult>} Completion result
   */
  async getChatCompletion(
    messages: ChatMessage[],
    options: Partial<CompletionOptions> = {},
    stage: string | null = null,
    operation: string | null = null
  ): Promise<ChatCompletionResult> {
    this._ensureInitialized();

    const completionOptions = {
      temperature: this.temperature ?? 0.7,
      ...options,
    };

    // Perform the completion
    const result = await this.connector!.getChatCompletion(
      messages,
      completionOptions
    );

    // Convert CompletionResponse to ChatCompletionResult
    const chatResult: ChatCompletionResult = result
      ? {
          textResponse: result.textResponse,
          metrics: result.metrics
            ? {
                prompt_tokens: result.metrics.prompt_tokens,
                completion_tokens: result.metrics.completion_tokens,
                total_tokens: result.metrics.total_tokens,
                ...(result.metrics.outputTps !== undefined && {
                  outputTps: result.metrics.outputTps,
                }),
                ...(result.metrics.duration !== undefined && {
                  duration: result.metrics.duration,
                }),
              }
            : {},
        }
      : {
          textResponse: "",
          metrics: {},
        };

    // Update internal metrics
    this.updateLastTokens(chatResult);

    // Track with token tracker if available
    if (this.tokenTracker && stage && operation) {
      this.tokenTracker.trackLLMResponse(chatResult, stage, operation);
    }

    return chatResult;
  }

  /**
   * Compress messages for the LLM (overload for array or object)
   * @param {ChatMessage[] | object} messagesOrParams - Messages array or message params object
   * @returns {Promise<ChatMessage[]>} Compressed messages
   */
  async compressMessages(
    messagesOrParams:
      | ChatMessage[]
      | {
          systemPrompt?: string;
          contextTexts?: string[];
          chatHistory?: ChatMessage[];
          userPrompt?: string;
          attachments?: Array<{ contentString: string; name: string }>;
        }
  ): Promise<ChatMessage[]> {
    this._ensureInitialized();
    if (Array.isArray(messagesOrParams)) {
      // Wrap array as PromptArgs
      return await this.connector!.compressMessages(
        { chatHistory: messagesOrParams },
        messagesOrParams
      );
    }
    // If already PromptArgs, pass as first arg and use chatHistory or [] as second
    return await this.connector!.compressMessages(
      messagesOrParams,
      messagesOrParams.chatHistory || []
    );
  }

  /**
   * Update last token metrics
   * @private
   * @param {ChatCompletionResult} result - LLM result
   */
  private updateLastTokens(result: ChatCompletionResult): void {
    if (!result || !this.connector) return;

    const tokens =
      result?.metrics?.total_tokens ??
      (result?.metrics?.prompt_tokens || 0) +
        (result?.metrics?.completion_tokens || 0);

    const connectorWithMetrics = this
      .connector as unknown as BaseLLMProvider & {
      metrics?: Record<string, unknown>;
    };
    connectorWithMetrics.metrics = {
      ...(connectorWithMetrics.metrics || {}),
      lastCompletionTokens: tokens,
    };
  }

  /**
   * Get current metrics from the LLM connector
   * @returns {Record<string, unknown>} Current metrics
   */
  getMetrics(): Record<string, unknown> {
    this._ensureInitialized();
    const connectorWithMetrics = this
      .connector as unknown as BaseLLMProvider & {
      metrics?: Record<string, unknown>;
    };
    return {
      connector: connectorWithMetrics.metrics || {},
      contextManager: this.contextManager
        ? this.contextManager.getMetrics()
        : null,
      tokenTracker: this.tokenTracker ? this.tokenTracker.getMetrics() : null,
    };
  }

  /**
   * Generate a comprehensive token usage report
   * @returns {Record<string, unknown>} Token usage report
   */
  generateTokenReport(): Record<string, unknown> {
    this._ensureInitialized();

    const connectorWithMetrics = this
      .connector as unknown as BaseLLMProvider & {
      metrics?: Record<string, unknown>;
      provider?: string;
    };

    const report: Record<string, unknown> = {
      connector: connectorWithMetrics.metrics || {},
      temperature: this.temperature,
      provider: connectorWithMetrics.provider || "unknown",
      model: this.connector!.model || "unknown",
    };

    if (this.contextManager) {
      report.contextManager = this.contextManager.generateTokenReport();
    }

    if (this.tokenTracker) {
      report.tokenTracker = this.tokenTracker.generateReport();
    }

    return report;
  }

  /**
   * Reset all metrics
   */
  resetMetrics(): void {
    const connectorWithMetrics = this
      .connector as unknown as BaseLLMProvider & {
      metrics?: Record<string, unknown>;
    };
    if (connectorWithMetrics && connectorWithMetrics.metrics) {
      connectorWithMetrics.metrics = {};
    }

    if (this.contextManager) {
      this.contextManager.resetMetrics();
    }

    if (this.tokenTracker) {
      this.tokenTracker.resetMetrics();
    }
  }

  /**
   * Get provider information
   * @returns {LLMProviderInfo} Provider details
   */
  getProviderInfo(): LLMProviderInfo {
    this._ensureInitialized();

    const connectorWithProvider = this
      .connector as unknown as BaseLLMProvider & {
      provider?: string;
    };

    return {
      provider: connectorWithProvider.provider || "unknown",
      model: this.connector!.model || "unknown",
      temperature: this.temperature!,
      contextWindowLimit: this.connector!.promptWindowLimit(),
      availableContextWindow: this.getAvailableContextWindow(),
    };
  }

  /**
   * Ensure the coordinator is initialized
   * @private
   */
  private _ensureInitialized(): void {
    if (!this._initialized) {
      throw new Error(
        "LLMCoordinator must be initialized before use. Call initialize() first."
      );
    }
  }
}
