import { Response } from "express";
import { writeResponseChunk } from "../../../helpers/chat/responses";

interface ProgressData {
  step?: number;
  status?: "starting" | "in_progress" | "complete" | "error" | "aborted";
  message?: string;
  progress?: number; // -1 for loading, 0-100 for progress, -2 for error, -3 for aborted
  total?: number;
  subStep?: number;
  label?: string;
  data?: Record<string, unknown>;
  totalSteps?: number;
  labelArgs?: Record<string, unknown>;
  error?: string;
}

interface ProgressChunkData extends ProgressData {
  uuid: string;
  flowType: string;
  type: "cdbProgress" | "progress";
}

/**
 * Progress Manager
 *
 * Handles standardized progress reporting for document drafting flows.
 * Provides consistent progress messaging and error reporting across all flow types.
 */
class ProgressManager {
  private response: Response;
  private chatId: string;
  private flowType: string;
  private currentStep: number = 0;
  private totalSteps: number = 0;

  constructor(response: Response, chatId: string, flowType: string) {
    this.response = response;
    this.chatId = chatId;
    this.flowType = flowType;
  }

  /**
   * Set the total number of steps for this flow
   */
  setTotalSteps(total: number): void {
    this.totalSteps = total;
  }

  /**
   * Send progress update to the frontend
   */
  sendProgress(data: ProgressData = {}): void {
    const translated = { ...data };

    // Auto-translate status to progress if not provided
    if (
      translated.progress === undefined &&
      typeof translated.status === "string"
    ) {
      switch (translated.status) {
        case "starting":
        case "in_progress":
          translated.progress = -1; // loading state
          break;
        case "complete":
          translated.progress = 100; // done
          break;
        case "error":
          translated.progress = -2; // failed
          break;
        default:
          // leave progress undefined for unknown statuses
          break;
      }
    }

    // Set step if not provided but we're tracking steps
    if (translated.step === undefined && this.currentStep > 0) {
      translated.step = this.currentStep;
    }

    // Set total steps for frontend progress calculation
    if (translated.totalSteps === undefined && this.totalSteps > 0) {
      translated.totalSteps = this.totalSteps;
    }

    const progressData: Omit<ProgressChunkData, "type"> = {
      uuid: this.chatId,
      flowType: this.flowType,
      ...translated,
    };

    try {
      const cdbProgressChunk = {
        sources: [],
        type: "cdbProgress",
        textResponse: null,
        close: false,
        error: false,
        ...progressData,
        uuid: this.chatId,
      };

      const progressChunk = {
        sources: [],
        type: "progress",
        textResponse: null,
        close: false,
        error: false,
        ...progressData,
        uuid: this.chatId,
      };

      // Send cdbProgress for specialized CDB handlers (modals, etc.)
      writeResponseChunk(this.response, cdbProgressChunk);

      // Send progress for main chat interface (ChatProgress component)
      writeResponseChunk(this.response, progressChunk);
    } catch (err) {
      console.error(
        `[ProgressManager] Failed to send progress chunk for ${this.flowType} flow:`,
        err
      );
      // Don't throw here - we don't want progress reporting to break the flow
    }
  }

  /**
   * Start a new step
   */
  startStep(
    stepNumber: number,
    message: string,
    additionalData: ProgressData = {}
  ): void {
    this.currentStep = stepNumber;
    this.sendProgress({
      step: stepNumber,
      status: "starting",
      message,
      ...additionalData,
    });
  }

  /**
   * Update progress for current step
   */
  updateStep(message: string, additionalData: ProgressData = {}): void {
    this.sendProgress({
      step: this.currentStep,
      status: "in_progress",
      message,
      ...additionalData,
    });
  }

  /**
   * Complete current step
   */
  completeStep(message: string, data: Record<string, unknown> = {}): void {
    this.sendProgress({
      step: this.currentStep,
      status: "complete",
      message,
      data,
    });
  }

  /**
   * Report error for current step
   */
  sendError(error: Error | string, step: number | null = null): void {
    const errorMessage = typeof error === "string" ? error : error.message;

    this.sendProgress({
      step: step || this.currentStep,
      status: "error",
      message: errorMessage,
      progress: -2,
    });
  }

  /**
   * Send sub-step progress (for steps that have multiple sub-items)
   */
  sendSubStepProgress(
    subStep: number,
    total: number,
    message: string,
    label: string | null = null,
    progress: number = -1
  ): void {
    const data: ProgressData = {
      step: this.currentStep,
      subStep,
      total,
      message,
      progress,
    };

    if (label) {
      data.label = label;
    }

    this.sendProgress(data);
  }

  /**
   * Send final completion message
   */
  sendFinalCompletion(
    message: string,
    data: Record<string, unknown> = {}
  ): void {
    this.sendProgress({
      step: this.totalSteps || this.currentStep,
      status: "complete",
      message,
      progress: 100,
      data,
    });
  }

  /**
   * Send final error message
   */
  sendFinalError(error: Error | string): void {
    const errorMessage = typeof error === "string" ? error : error.message;

    this.sendProgress({
      step: this.currentStep,
      status: "error",
      message: errorMessage,
      progress: -2,
    });
  }

  /**
   * Send abort notification to frontend
   */
  sendAbort(): void {
    this.sendProgress({
      step: this.currentStep,
      status: "aborted",
      message: "Document processing was cancelled by user.",
      progress: -3, // Special progress value for aborted state
    });
  }
}

export { ProgressManager, type ProgressData, type ProgressChunkData };
