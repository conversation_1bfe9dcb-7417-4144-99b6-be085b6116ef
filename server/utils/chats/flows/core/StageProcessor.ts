import { get<PERSON><PERSON><PERSON>ider } from "../../../helpers";
import {
  FlowContext,
  StageDependencies,
  StageProcessor as IStageProcessor,
  ProgressManager,
  WorkspaceManager,
  LLMCoordinator,
  ContextWindowManager,
  TokenTracker,
  WorkspaceData,
  UserData,
} from "../../../../types/chat-flow";
import { Response } from "express";

interface MockDependencies extends StageDependencies {
  prompts: {
    CURRENT_DEFAULT_MEMO_CREATION: {
      PROMPT_TEMPLATE: string;
    };
    CURRENT_DEFAULT_SECTION_LEGAL_ISSUES: {
      SYSTEM_PROMPT: string;
      USER_PROMPT: string;
    };
    CURRENT_DEFAULT_SECTION_DRAFTING: {
      SYSTEM_PROMPT: string;
      USER_PROMPT: string;
    };
  };
}

interface MockContext extends FlowContext {
  sectionList: Array<{
    sectionNumber: number;
    title: string;
    relevantDocumentNames: string[];
    identifiedLegalIssues: Array<{
      Issue: string;
      WORKSPACE_SLUG_FOR_LEGALDATA: string;
    }>;
  }>;
  workspaceDetails: {
    slug: string;
    type: string;
  };
  legalQAWorkspaces: Array<{
    slug: string;
    name: string;
  }>;
}

/**
 * Base Stage Processor
 *
 * Abstract base class for all flow stage processors.
 * Provides common functionality and enforces interface consistency.
 */
abstract class StageProcessor implements IStageProcessor {
  options: Record<string, unknown>;
  name: string;

  constructor(options: Record<string, unknown> = {}) {
    if (this.constructor === StageProcessor) {
      throw new Error(
        "StageProcessor is abstract and cannot be instantiated directly"
      );
    }
    this.options = options;
    this.name = this.constructor.name;
  }

  /**
   * Execute the stage processing
   * @param context - Shared context object containing data from previous stages
   * @param dependencies - Dependencies injected by the orchestrator
   * @returns Stage result with any data to pass to next stages
   */
  abstract process(
    context: FlowContext,
    dependencies: StageDependencies
  ): Promise<Partial<FlowContext>>;

  /**
   * Execute method for standalone testing
   * Creates mock dependencies and calls process method
   * @returns Stage result
   */
  async execute(): Promise<Partial<FlowContext>> {
    // Create mock dependencies for testing
    const mockDependencies = this.createMockDependencies();
    const mockContext = this.createMockContext();

    return await this.process(mockContext, mockDependencies);
  }

  /**
   * Create mock dependencies for testing
   */
  protected createMockDependencies(): MockDependencies {
    // Minimal valid WorkspaceData (Workspace) object
    const mockWorkspace = {
      id: 1,
      name: "Test Workspace",
      slug: "test-workspace",
      vectorTag: null,
      createdAt: new Date(),
      lastUpdatedAt: new Date(),
      type: "legal-drafting",
    };
    // Minimal valid UserData (FilteredUser) object
    const mockUser = {
      id: 1,
      email: "<EMAIL>",
      createdAt: new Date(),
      lastUpdatedAt: new Date(),
      role: "default",
      suspended: 0,
      pfpFilename: null,
      seen_recovery_codes: false,
      custom_ai_userselected: false,
      custom_ai_selected_engine: "",
      custom_system_prompt: null,
      organizationId: null,
      username: "testuser",
      economy_system_id: null,
    };
    return {
      progressManager: {
        updateStep: () => {},
        sendSubStepProgress: () => {},
      } as unknown as ProgressManager,
      workspaceManager: {} as WorkspaceManager,
      llmCoordinator: {
        getConnector: () => getLLMProvider(),
        getTemperature: () => 0.7,
      } as LLMCoordinator,
      contextWindowManager: {
        getAvailableContextWindow: () => 128000,
        LLMConnector: getLLMProvider(),
      } as unknown as ContextWindowManager,
      tokenTracker: {
        startStage: (stageName: string) => ({
          stageName,
          startTime: Date.now(),
          addTokens: () => {},
          finish: () => {},
        }),
        getTokenUsageForStage: () => 50,
        trackContentTokens: () => {},
        trackLLMResponse: () => {},
      } as unknown as TokenTracker,
      abortChecker: () => {},
      legalTask: "Test legal task for integration testing",
      customInstructions: "",
      chatId: "test-chat-id-123",
      workspace: mockWorkspace as WorkspaceData,
      user: mockUser as UserData,
      options: {
        chatId: "test-chat-id-123",
        response: {} as Response,
        workspace: mockWorkspace,
        user: mockUser,
        message: "test message",
      },
      flowType: "test",
      prompts: {
        CURRENT_DEFAULT_MEMO_CREATION: {
          PROMPT_TEMPLATE:
            'Create a legal memorandum addressing the legal issue "{{issue}}" in the same language as the legal task "{{task}}".',
        },
        CURRENT_DEFAULT_SECTION_LEGAL_ISSUES: {
          SYSTEM_PROMPT: "You are a legal analyst. Identify legal issues.",
          USER_PROMPT: "Analyze sections: {{sections}}",
        },
        CURRENT_DEFAULT_SECTION_DRAFTING: {
          SYSTEM_PROMPT: "You are a legal document drafter.",
          USER_PROMPT: "Draft section {{sectionNumber}} titled '{{title}}'.",
        },
      },
    };
  }

  /**
   * Create mock context for testing
   */
  protected createMockContext(): MockContext {
    return {
      documents: [],
      processedDocuments: [],
      docDescriptions: [],
      legalIssues: [],
      memos: [],
      sectionOutputs: [],
      finalContent: "",
      metrics: {},
      errors: [],
      sectionList: [
        {
          sectionNumber: 1,
          title: "Contract Formation Requirements",
          relevantDocumentNames: ["main-contract.pdf", "formation-law.pdf"],
          identifiedLegalIssues: [
            {
              Issue: "Contract formation requirements",
              WORKSPACE_SLUG_FOR_LEGALDATA: "contracts-legal-qa",
            },
            {
              Issue: "Consideration validity",
              WORKSPACE_SLUG_FOR_LEGALDATA: "commercial-law",
            },
          ],
        },
        {
          sectionNumber: 2,
          title: "Compliance Analysis",
          relevantDocumentNames: ["compliance-guide.pdf"],
          identifiedLegalIssues: [
            {
              Issue: "Regulatory compliance requirements",
              WORKSPACE_SLUG_FOR_LEGALDATA: "regulatory-legal-qa",
            },
          ],
        },
      ],
      workspaceDetails: {
        slug: "integration-test-workspace",
        type: "legal-drafting",
      },
      legalQAWorkspaces: [
        { slug: "contracts-legal-qa", name: "Contracts Legal QA" },
        { slug: "commercial-law", name: "Commercial Law" },
        { slug: "regulatory-legal-qa", name: "Regulatory Legal QA" },
      ],
    };
  }

  /**
   * Validate the inputs for the stage.
   * This method should be overridden by subclasses if specific validation is needed.
   * @param context - The shared context object.
   * @returns True if inputs are valid, otherwise false.
   */
  validateInputs(_context: FlowContext): boolean {
    return true;
  }

  /**
   * Determine if the stage should be skipped.
   * This method can be overridden by subclasses.
   * @param context - The shared context object.
   * @returns True if the stage should be skipped, otherwise false.
   */
  shouldSkip(_context: FlowContext): boolean {
    return false;
  }

  /**
   * Get the stage name for logging and progress tracking
   * @returns Stage name
   */
  getStageName(): string {
    return this.name.replace("StageProcessor", "").toLowerCase();
  }

  /**
   * Handle abort signal during processing
   * @param abortSignal - Abort signal
   * @param chatId - Chat ID for logging
   */
  checkAbort(abortSignal: AbortSignal | undefined, chatId: string): void {
    if (abortSignal?.aborted) {
      const error = new Error(
        `Stage ${this.getStageName()} aborted for chat ${chatId}`
      ) as Error & { isAbort: boolean };
      error.isAbort = true;
      throw error;
    }
  }

  /**
   * Log stage execution information
   * @param level - Log level (info, warn, error)
   * @param message - Log message
   * @param data - Additional data to log
   */
  log(
    level: "info" | "warn" | "error",
    message: string,
    data: Record<string, unknown> = {}
  ): void {
    const logData = {
      stage: this.getStageName(),
      message,
      ...data,
    };

    switch (level) {
      case "error":
        console.error(`[${this.name}]`, logData);
        break;
      case "warn":
        console.warn(`[${this.name}]`, logData);
        break;
      default:
        console.log(`[${this.name}]`, logData);
    }
  }
}

export { StageProcessor, type MockDependencies, type MockContext };
