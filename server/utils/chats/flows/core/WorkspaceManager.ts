import * as fs from "fs";
import * as path from "path";
import { getUserDocumentPathName } from "../../../files/multer";
import type { Workspace, FilteredUser } from "../../../../types/models";
import type { AuthenticatedUser } from "../../../../types/shared";
import type {
  WorkspaceManager as IWorkspaceManager,
  WorkspaceManagerOptions as IWorkspaceManagerOptions,
  DocumentFile as IDocumentFile,
} from "../../../../types/chat-flow";

interface DocumentFileOptions {
  extensions?: string[];
  filter?: (fileName: string) => boolean;
}

interface ReadDocumentFilesOptions {
  skipErrors?: boolean;
}

interface GetAllDocumentsOptions {
  skipErrors?: boolean;
  skipEmpty?: boolean;
}

interface DocumentFile {
  id: string;
  fileName: string;
  displayName: string;
  content: string;
  metadata: Record<string, unknown>;
  raw: unknown;
}

interface DocumentReadResult {
  documents: DocumentFile[];
  errors: Array<{ fileName: string; error: string }>;
}

interface AllDocumentsResult extends DocumentReadResult {
  totalFiles: number;
  processedFiles: number;
  skippedFiles: number;
}

interface WorkspaceInfo {
  slug: string;
  path: string;
  totalDocuments: number;
  validDocuments: number;
  errorDocuments: number;
  user: {
    id?: number;
    email?: string;
  };
}

/**
 * Workspace Manager
 *
 * Handles workspace validation, document file operations, and workspace-related utilities
 * for document drafting flows.
 */
class WorkspaceManager implements IWorkspaceManager {
  private user: FilteredUser;
  private workspace: Workspace;
  private workspacePath: string | null = null;
  private documentBuilderBasePath: string | null = null;
  private _initialized: boolean = false;

  constructor(user: FilteredUser, workspace: Workspace) {
    this.user = user;
    this.workspace = workspace;
  }

  /**
   * Initialize workspace paths and validate workspace existence
   * @throws Error if workspace cannot be initialized
   */
  async initialize(): Promise<void> {
    if (this._initialized) {
      return;
    }

    // Setup workspace document path
    // For document-drafting workspaces, we need to use the workspace owner's ID
    // to match how files are uploaded and stored (see workspaces.js upload endpoints)
    const userForPath: AuthenticatedUser = {
      ...this.user,
      id: this.workspace.user_id,
      suspended: 0,
      createdAt: this.user.createdAt || new Date().toISOString(),
      lastUpdatedAt: this.user.lastUpdatedAt || new Date().toISOString(),
    } as AuthenticatedUser;
    const folderName = getUserDocumentPathName(
      userForPath,
      true,
      this.workspace.slug
    );
    this.workspacePath = path.join(
      __dirname,
      "../../../../storage/documents",
      folderName
    );

    // Setup document builder path
    this.documentBuilderBasePath = path.join(
      __dirname,
      "../../../../storage/document-builder"
    );

    // Validate and create necessary directories
    await this.validateWorkspace();
    await this.ensureDocumentBuilderDirectory();

    this._initialized = true;
  }

  /**
   * Validate that the workspace exists and is accessible
   * @throws Error if workspace is not found or not accessible
   */
  async validateWorkspace(): Promise<void> {
    if (!this.workspacePath || !fs.existsSync(this.workspacePath)) {
      throw new Error(
        `Workspace documents not found at: ${this.workspacePath}`
      );
    }

    // Check if we can read the directory
    try {
      fs.readdirSync(this.workspacePath);
    } catch (error) {
      throw new Error(
        `Cannot access workspace directory: ${(error as Error).message}`
      );
    }
  }

  /**
   * Ensure document builder directory exists
   */
  async ensureDocumentBuilderDirectory(): Promise<void> {
    if (
      this.documentBuilderBasePath &&
      !fs.existsSync(this.documentBuilderBasePath)
    ) {
      fs.mkdirSync(this.documentBuilderBasePath, { recursive: true });
    }
  }

  /**
   * Get all document files in the workspace
   * @param options - Filter options
   * @returns Array of document filenames
   */
  getDocumentFiles(options: IWorkspaceManagerOptions = {}): string[] {
    this._ensureInitialized();

    const { extensions = [".json"], filter = null } = options;

    let files = fs.readdirSync(this.workspacePath!);

    // Filter by extensions
    if ((extensions?.length ?? 0) > 0) {
      files = files.filter((file) =>
        extensions.some((ext) => file.toLowerCase().endsWith(ext.toLowerCase()))
      );
    }

    // Apply custom filter if provided
    if (filter && typeof filter === "function") {
      files = files.filter(filter);
    }

    return files;
  }

  /**
   * Read and parse a document file
   * @param fileName - Name of the file to read
   * @returns Parsed document with metadata
   * @throws Error if file cannot be read or parsed
   */
  readDocumentFile(fileName: string): IDocumentFile {
    this._ensureInitialized();

    const filePath = path.join(this.workspacePath!, fileName);

    if (!fs.existsSync(filePath)) {
      throw new Error(`Document file not found: ${fileName}`);
    }

    try {
      const raw = fs.readFileSync(filePath, "utf8");
      const parsed = JSON.parse(raw);

      // Extract basic information
      const docId = fileName.replace(/\.json$/i, "");
      const displayName = parsed?.metadata?.title || docId;
      const content = parsed.pageContent || "";

      return {
        id: docId,
        fileName,
        displayName,
        content,
        metadata: parsed.metadata || {},
        raw: parsed,
      };
    } catch (error) {
      throw new Error(
        `Failed to read or parse document ${fileName}: ${(error as Error).message}`
      );
    }
  }

  /**
   * Read multiple document files
   * @param fileNames - Array of file names to read
   * @param options - Options
   * @returns Array of parsed documents with errors
   */
  readDocumentFiles(
    fileNames: string[],
    options: { skipErrors?: boolean } = {}
  ): {
    documents: IDocumentFile[];
    errors: Array<{ fileName: string; error: string }>;
  } {
    const { skipErrors = false } = options;
    const documents: IDocumentFile[] = [];
    const errors: Array<{ fileName: string; error: string }> = [];

    for (const fileName of fileNames) {
      try {
        const document = this.readDocumentFile(fileName);
        documents.push(document);
      } catch (error) {
        if (skipErrors) {
          errors.push({ fileName, error: (error as Error).message });
          console.warn(
            `[WorkspaceManager] Skipping file ${fileName}: ${(error as Error).message}`
          );
        } else {
          throw error;
        }
      }
    }

    return { documents, errors };
  }

  /**
   * Get all documents in the workspace with their content
   * @param options - Options
   * @returns Array of document objects with metadata
   */
  getAllDocuments(options: GetAllDocumentsOptions = {}): AllDocumentsResult {
    const { skipErrors = true, skipEmpty = true } = options;

    const fileNames = this.getDocumentFiles();
    const { documents, errors } = this.readDocumentFiles(fileNames, {
      skipErrors,
    });

    let filteredDocuments = documents;

    // Filter out empty documents if requested
    if (skipEmpty) {
      filteredDocuments = documents.filter(
        (doc) => doc.content && doc.content.trim().length > 0
      );
    }

    return {
      documents: filteredDocuments,
      errors,
      totalFiles: fileNames.length,
      processedFiles: documents.length,
      skippedFiles: errors.length,
    };
  }

  /**
   * Get workspace path
   * @returns Workspace path
   */
  getWorkspacePath(): string {
    this._ensureInitialized();
    return this.workspacePath!;
  }

  /**
   * Get document builder base path
   * @returns Document builder path
   */
  getDocumentBuilderPath(): string {
    this._ensureInitialized();
    return this.documentBuilderBasePath!;
  }

  /**
   * Check if a document file exists
   * @param fileName - Name of the file to check
   * @returns Whether the file exists
   */
  documentExists(fileName: string): boolean {
    this._ensureInitialized();
    const filePath = path.join(this.workspacePath!, fileName);
    return fs.existsSync(filePath);
  }

  /**
   * Get workspace metadata
   * @returns Workspace information
   */
  getWorkspaceInfo(): WorkspaceInfo {
    this._ensureInitialized();

    const documentFiles = this.getDocumentFiles();
    const { documents, errors } = this.getAllDocuments();

    return {
      slug: this.workspace.slug,
      path: this.workspacePath!,
      totalDocuments: documentFiles.length,
      validDocuments: documents.length,
      errorDocuments: errors.length,
      user: {
        id: this?.user?.id,
        email: this?.user?.email,
      },
    };
  }

  /**
   * Ensure the workspace manager is initialized
   */
  private _ensureInitialized(): void {
    if (!this._initialized) {
      throw new Error(
        "WorkspaceManager must be initialized before use. Call initialize() first."
      );
    }
  }
}

export {
  WorkspaceManager,
  type DocumentFileOptions,
  type ReadDocumentFilesOptions,
  type GetAllDocumentsOptions,
  type DocumentFile,
  type DocumentReadResult,
  type AllDocumentsResult,
  type WorkspaceInfo,
};
