import { FlowOrchestrator } from "../FlowOrchestrator";
import { ProgressManager } from "../ProgressManager";
import { WorkspaceManager } from "../WorkspaceManager";
import { LLMCoordinator } from "../LLMCoordinator";
import * as documentProcessingHelper from "../../../helpers/documentProcessing";
import * as fileHelper from "../../../../files";

// Mock dependencies
jest.mock("../ProgressManager");
jest.mock("../WorkspaceManager");
jest.mock("../LLMCoordinator");
jest.mock("../../../helpers/documentProcessing", () => ({
  ...jest.requireActual("../../../helpers/documentProcessing"),
  createAbortChecker: jest.fn(() => jest.fn()),
}));
jest.mock("../../../../files", () => ({
  purgeDocumentBuilder: jest.fn(),
  default: {
    purgeDocumentBuilder: jest.fn(),
  },
}));
jest.mock("../../../../../endpoints/document", () => ({
  getUserDocumentPathName: jest.fn().mockReturnValue("/mock/path"),
}));

// Mock Stage Processors
const MockStageProcessor: any = jest.fn().mockImplementation(() => ({
  validateInputs: jest.fn().mockReturnValue(true),
  shouldSkip: jest.fn().mockReturnValue(false),
  process: jest.fn().mockResolvedValue({}),
}));

const mockFlowConfig: any = {
  flowType: "test-flow",
  stages: [
    { name: "Stage 1", processor: MockStageProcessor },
    { name: "Stage 2", processor: MockStageProcessor },
  ],
  prompts: {
    systemPrompt: "test system prompt",
    userPrompt: "test user prompt",
  },
};

const mockOptions: any = {
  chatId: "test-chat",
  response: {},
  workspace: {},
  user: {},
  message: "test task",
  abortSignal: {},
};

describe("FlowOrchestrator", () => {
  let orchestrator: any;

  beforeEach(() => {
    jest.clearAllMocks();
    orchestrator = new FlowOrchestrator(mockOptions, mockFlowConfig);
  });

  describe("Initialization", () => {
    it("should initialize all managers and build the pipeline", async () => {
      // Mock the instances returned by the constructors
      const mockProgressManagerInstance: any = {
        setTotalSteps: jest.fn(),
        sendFinalCompletion: jest.fn(),
        sendFinalError: jest.fn(),
        startStep: jest.fn(),
        completeStep: jest.fn(),
        sendError: jest.fn(),
        sendAbort: jest.fn(),
      };

      const mockWorkspaceManagerInstance: any = {
        initialize: jest.fn().mockResolvedValue(undefined),
        getWorkspaceInfo: jest.fn().mockReturnValue({}),
      };

      const mockLLMCoordinatorInstance: any = {
        initialize: jest.fn().mockResolvedValue(undefined),
        getProviderInfo: jest.fn().mockReturnValue({}),
        getAvailableContextWindow: jest.fn().mockReturnValue(4000),
        getMetrics: jest.fn().mockReturnValue({}),
        getContextManager: jest.fn().mockReturnValue({}),
        getTokenTracker: jest.fn().mockReturnValue({}),
        resetMetrics: jest.fn(),
        generateTokenReport: jest.fn().mockReturnValue({
          tokenTracker: {
            summary: {},
            budgetAnalysis: { budgetUtilization: 0 },
            iterativeMetrics: { totalIterations: 0 },
          },
          connector: {},
          contextManager: {},
        }),
      };

      (
        ProgressManager as jest.MockedClass<typeof ProgressManager>
      ).mockImplementation(() => mockProgressManagerInstance);
      (
        WorkspaceManager as jest.MockedClass<typeof WorkspaceManager>
      ).mockImplementation(() => mockWorkspaceManagerInstance);
      (
        LLMCoordinator as jest.MockedClass<typeof LLMCoordinator>
      ).mockImplementation(() => mockLLMCoordinatorInstance);

      await orchestrator.initialize();

      expect(ProgressManager).toHaveBeenCalled();
      expect(WorkspaceManager).toHaveBeenCalled();
      expect(LLMCoordinator).toHaveBeenCalled();
      expect(documentProcessingHelper.createAbortChecker).toHaveBeenCalled();
      expect(orchestrator.stages.length).toBe(2);
      expect(orchestrator.stages[0].processor).toBeInstanceOf(Object);
    });
  });

  describe("Execution", () => {
    beforeEach(async () => {
      // Mock the instances returned by the constructors
      const mockProgressManagerInstance: any = {
        setTotalSteps: jest.fn(),
        sendFinalCompletion: jest.fn(),
        sendFinalError: jest.fn(),
        startStep: jest.fn(),
        completeStep: jest.fn(),
        sendError: jest.fn(),
        sendAbort: jest.fn(),
      };

      const mockWorkspaceManagerInstance: any = {
        initialize: jest.fn().mockResolvedValue(undefined),
        getWorkspaceInfo: jest.fn().mockReturnValue({}),
      };

      const mockLLMCoordinatorInstance: any = {
        initialize: jest.fn().mockResolvedValue(undefined),
        getProviderInfo: jest.fn().mockReturnValue({}),
        getAvailableContextWindow: jest.fn().mockReturnValue(4000),
        getMetrics: jest.fn().mockReturnValue({}),
        getContextManager: jest.fn().mockReturnValue({}),
        getTokenTracker: jest.fn().mockReturnValue({}),
        resetMetrics: jest.fn(),
        generateTokenReport: jest.fn().mockReturnValue({
          tokenTracker: {
            summary: {},
            budgetAnalysis: { budgetUtilization: 0 },
            iterativeMetrics: { totalIterations: 0 },
          },
          connector: {},
          contextManager: {},
        }),
      };

      (
        ProgressManager as jest.MockedClass<typeof ProgressManager>
      ).mockImplementation(() => mockProgressManagerInstance);
      (
        WorkspaceManager as jest.MockedClass<typeof WorkspaceManager>
      ).mockImplementation(() => mockWorkspaceManagerInstance);
      (
        LLMCoordinator as jest.MockedClass<typeof LLMCoordinator>
      ).mockImplementation(() => mockLLMCoordinatorInstance);

      // Initialize to set up the instances
      await orchestrator.initialize();
    });

    it("should execute all stages in sequence successfully", async () => {
      // Arrange
      const stage1Result: any = { data1: "value1" };
      const stage2Result: any = { data2: "value2" };
      (orchestrator.stages[0].processor.process as any).mockResolvedValue(
        stage1Result
      );
      (orchestrator.stages[1].processor.process as any).mockResolvedValue(
        stage2Result
      );

      // Act
      await orchestrator.execute();

      // Assert
      expect(orchestrator.stages[0].processor.process).toHaveBeenCalled();
      expect(orchestrator.stages[1].processor.process).toHaveBeenCalled();
      const finalContext: any = orchestrator.getContext();
      expect(finalContext.data1).toBe("value1");
      expect(finalContext.data2).toBe("value2");
      expect(
        orchestrator.progressManager.sendFinalCompletion
      ).toHaveBeenCalled();
    });

    it("should skip a stage if shouldSkip returns true", async () => {
      // Arrange
      (orchestrator.stages[0].processor.shouldSkip as any).mockReturnValue(
        true
      );

      // Act
      await orchestrator.execute();

      // Assert
      expect(orchestrator.stages[0].processor.process).not.toHaveBeenCalled();
      expect(orchestrator.stages[1].processor.process).toHaveBeenCalled(); // Stage 2 should still run
    });

    it("should stop execution if a stage fails input validation", async () => {
      // Arrange
      (orchestrator.stages[1].processor.validateInputs as any).mockReturnValue(
        false
      );

      // Act & Assert
      await expect(orchestrator.execute()).rejects.toThrow(
        "Stage Stage 2 input validation failed"
      );
      expect(orchestrator.stages[0].processor.process).toHaveBeenCalled();
      expect(orchestrator.stages[1].processor.process).not.toHaveBeenCalled();
    });

    it("should stop execution if a stage process throws an error", async () => {
      // Arrange
      const stageError: any = new Error("Stage 1 failed");
      (orchestrator.stages[0].processor.process as any).mockRejectedValue(
        stageError
      );

      // Act & Assert
      await expect(orchestrator.execute()).rejects.toThrow("Stage 1 failed");
      expect(orchestrator.stages[1].processor.process).not.toHaveBeenCalled();
      expect(orchestrator.progressManager.sendFinalError).toHaveBeenCalledWith(
        stageError
      );
    });
  });

  describe("Cleanup", () => {
    it("should call purgeDocumentBuilder on cleanup", async () => {
      // Get the mocked purgeDocumentBuilder from the mocked module
      const mockPurgeDocumentBuilder =
        fileHelper.purgeDocumentBuilder as jest.MockedFunction<
          typeof fileHelper.purgeDocumentBuilder
        >;
      mockPurgeDocumentBuilder.mockReturnValue(1);

      // Create a mock processor class
      class MockProcessor {
        process = jest.fn().mockResolvedValue({ testData: "success" });
        shouldSkip = jest.fn().mockReturnValue(false);
        validateInputs = jest.fn().mockReturnValue(true);
      }

      // Create a flow config with proper processors that will actually execute
      const testFlowConfig: any = {
        flowType: "test",
        stages: [
          {
            name: "test-stage",
            processor: MockProcessor,
          },
        ],
      };

      const orchestrator: any = new FlowOrchestrator(
        mockOptions,
        testFlowConfig
      );

      // Mock the instances returned by the constructors for this test
      const mockProgressManagerInstance: any = {
        setTotalSteps: jest.fn(),
        sendFinalCompletion: jest.fn(),
        sendFinalError: jest.fn(),
        startStep: jest.fn(),
        completeStep: jest.fn(),
        sendError: jest.fn(),
        sendAbort: jest.fn(),
      };

      const mockWorkspaceManagerInstance: any = {
        initialize: jest.fn().mockResolvedValue(undefined),
        getWorkspaceInfo: jest.fn().mockReturnValue({}),
      };

      const mockLLMCoordinatorInstance: any = {
        initialize: jest.fn().mockResolvedValue(undefined),
        getProviderInfo: jest.fn().mockReturnValue({}),
        getAvailableContextWindow: jest.fn().mockReturnValue(4000),
        getMetrics: jest.fn().mockReturnValue({}),
        getContextManager: jest.fn().mockReturnValue({}),
        getTokenTracker: jest.fn().mockReturnValue({}),
        resetMetrics: jest.fn(),
        generateTokenReport: jest.fn().mockReturnValue({
          tokenTracker: {
            summary: {},
            budgetAnalysis: { budgetUtilization: 0 },
            iterativeMetrics: { totalIterations: 0 },
          },
          connector: {},
          contextManager: {},
        }),
      };

      (
        ProgressManager as jest.MockedClass<typeof ProgressManager>
      ).mockImplementation(() => mockProgressManagerInstance);
      (
        WorkspaceManager as jest.MockedClass<typeof WorkspaceManager>
      ).mockImplementation(() => mockWorkspaceManagerInstance);
      (
        LLMCoordinator as jest.MockedClass<typeof LLMCoordinator>
      ).mockImplementation(() => mockLLMCoordinatorInstance);

      await orchestrator.initialize();

      // Execute should complete and trigger cleanup in finally block
      await orchestrator.execute();

      // The cleanup is called in the finally block after execution
      expect(mockPurgeDocumentBuilder).toHaveBeenCalledWith({
        uuid: "test-chat",
      });
    });
  });
});
