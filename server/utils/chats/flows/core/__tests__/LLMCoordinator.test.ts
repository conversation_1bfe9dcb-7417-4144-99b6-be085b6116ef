/**
 * LLMCoordinator Integration Tests
 *
 * Tests the LLMCoordinator class which manages LLM interactions within the new modular flow system.
 * This replaces legacy LLM handling and integrates with FlowOrchestrator and other modular components.
 */

import { LLMCoordinator } from "../LLMCoordinator";
import type { ChatMessage } from "../../../../../types/chat-agent";

// Mock dependencies for the new modular system
jest.mock("../../../../helpers", () => ({
  getLLMProvider: jest.fn(),
}));

jest.mock("../../../helpers/contextWindowManager", () => ({
  ContextWindowManager: jest.fn(),
}));

jest.mock("../../../helpers/tokenTracker", () => ({
  TokenTracker: jest.fn(),
}));

import { getLLMProvider } from "../../../../helpers";
import { ContextWindowManager } from "../../../helpers/contextWindowManager";
import { TokenTracker } from "../../../helpers/tokenTracker";
import type { LLMProvider } from "../../../../../types/ai-providers";
import type { Workspace } from "../../../../../types/models";

// Mock interfaces for type safety
interface MockLLMProvider {
  getChatCompletion: jest.Mock;
  compressMessages: jest.Mock;
  model: string;
  defaultTemp: number;
  promptWindowLimit: jest.Mock;
  metrics: { lastCompletionTokens: number };
}

interface MockContextManager {
  calculateTokenBudget: jest.Mock;
  chunkContent: jest.Mock;
  getMetrics: jest.Mock;
  resetMetrics: jest.Mock;
}

interface MockTokenTracker {
  startStage: jest.Mock;
  trackContentTokens: jest.Mock;
  trackIteration: jest.Mock;
  trackBudget: jest.Mock;
  addError: jest.Mock;
  addWarning: jest.Mock;
  getMetrics: jest.Mock;
  generateReport: jest.Mock;
  resetMetrics: jest.Mock;
}

describe("LLMCoordinator Integration Tests", () => {
  let coordinator: LLMCoordinator;
  let mockOptions: Record<string, unknown>;
  let mockLLMProvider: MockLLMProvider;
  let mockContextManager: MockContextManager;
  let mockTokenTracker: MockTokenTracker;

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup mock LLM provider
    mockLLMProvider = {
      getChatCompletion: jest.fn().mockResolvedValue({
        textResponse: "Mock LLM response",
        metrics: { total_tokens: 100 },
      }),
      compressMessages: jest.fn().mockResolvedValue([]),
      model: "gpt-4",
      defaultTemp: 0.7,
      promptWindowLimit: jest.fn().mockReturnValue(128000),
      metrics: { lastCompletionTokens: 100 },
    };

    // Setup mock context manager
    mockContextManager = {
      calculateTokenBudget: jest.fn().mockReturnValue({
        totalTokens: 100000,
        reservedTokens: 2000,
        availableTokens: 98000,
      }),
      chunkContent: jest.fn().mockReturnValue([
        { content: "chunk1", tokens: 500 },
        { content: "chunk2", tokens: 500 },
      ]),
      getMetrics: jest.fn().mockReturnValue({
        tokensUsed: 1000,
        chunksProcessed: 5,
      }),
      resetMetrics: jest.fn(),
    };

    // Setup mock token tracker
    mockTokenTracker = {
      startStage: jest.fn().mockReturnValue({
        stage: "test-stage",
        startTime: Date.now(),
      }),
      trackContentTokens: jest.fn().mockReturnValue(100),
      trackIteration: jest.fn(),
      trackBudget: jest.fn(),
      addError: jest.fn(),
      addWarning: jest.fn(),
      getMetrics: jest.fn().mockReturnValue({
        totalTokens: 1000,
        stages: [],
        efficiency: 0.85,
      }),
      generateReport: jest.fn().mockReturnValue({
        totalTokens: 1000,
        stages: [],
        efficiency: 0.85,
      }),
      resetMetrics: jest.fn(),
    };

    // Setup mocks
    jest
      .mocked(getLLMProvider)
      .mockReturnValue(mockLLMProvider as unknown as LLMProvider);
    jest
      .mocked(ContextWindowManager)
      .mockImplementation(
        () =>
          mockContextManager as unknown as InstanceType<
            typeof ContextWindowManager
          >
      );
    jest
      .mocked(TokenTracker)
      .mockImplementation(
        () => mockTokenTracker as unknown as InstanceType<typeof TokenTracker>
      );

    // Setup test options
    mockOptions = {
      enableTokenTracking: true,
      enableContextManagement: true,
      maxIterations: 10,
      reservedOutputTokens: 4000,
    };

    const mockWorkspace: Workspace = {
      id: 1,
      name: "Test Workspace",
      slug: "test-workspace",
      vectorTag: null,
      createdAt: new Date(),
      lastUpdatedAt: new Date(),
    };
    coordinator = new LLMCoordinator(mockWorkspace);

    mockLLMProvider.compressMessages = jest
      .fn()
      .mockImplementation((_msgs) => []);
  });

  describe("Modular System Integration", () => {
    test("should be constructable as part of the modular system", () => {
      expect(coordinator).toBeInstanceOf(LLMCoordinator);
      expect(coordinator.options).toEqual(mockOptions);
    });

    test("should require initialization before use", async () => {
      const messages: ChatMessage[] = [
        {
          id: "1",
          role: "user",
          content: "Test message",
          timestamp: new Date(),
        },
      ];

      await expect(coordinator.getChatCompletion(messages)).rejects.toThrow(
        "LLMCoordinator must be initialized before use"
      );
    });

    test("should initialize successfully", async () => {
      await coordinator.initialize();

      expect(getLLMProvider).toHaveBeenCalled();
      expect(ContextWindowManager).toHaveBeenCalledWith(
        expect.any(Object),
        expect.objectContaining({
          enableTokenTracking: true,
          maxIterations: 10,
          reservedOutputTokens: 4000,
        })
      );
      expect(TokenTracker).toHaveBeenCalled();
    });

    test("should provide LLM functionality after initialization", async () => {
      await coordinator.initialize();

      const messages: ChatMessage[] = [
        {
          id: "1",
          role: "user",
          content: "Test message",
          timestamp: new Date(),
        },
      ];
      const result = await coordinator.getChatCompletion(messages);

      expect(mockLLMProvider.getChatCompletion).toHaveBeenCalledWith(messages, {
        temperature: 0.7,
      });
      expect(result).toEqual({
        textResponse: "Mock LLM response",
        metrics: { total_tokens: 100 },
      });
    });

    test("should calculate token budgets", async () => {
      await coordinator.initialize();

      const budget = coordinator.calculateTokenBudget({
        systemPrompt: "Test prompt",
        userPromptTemplate: "Test template",
        reservedTokens: 2000,
      });

      expect(mockContextManager.calculateTokenBudget).toHaveBeenCalled();
      expect(budget).toEqual({
        totalTokens: 100000,
        reservedTokens: 2000,
        availableTokens: 98000,
      });
    });

    test("should provide metrics when enabled", async () => {
      await coordinator.initialize();

      const metrics = coordinator.getMetrics();

      expect(metrics).toBeDefined();
      expect(metrics.connector).toBeDefined();
      expect(metrics.contextManager).toBeDefined();
      expect(metrics.tokenTracker).toBeDefined();
    });

    test("should handle LLM provider errors gracefully", async () => {
      await coordinator.initialize();

      const error = new Error("Provider error");
      jest.mocked(mockLLMProvider.getChatCompletion).mockRejectedValue(error);

      const messages: ChatMessage[] = [
        {
          id: "1",
          role: "user",
          content: "Test message",
          timestamp: new Date(),
        },
      ];

      await expect(coordinator.getChatCompletion(messages)).rejects.toThrow(
        "Provider error"
      );
    });

    test("should compress messages when needed", async () => {
      await coordinator.initialize();

      const messages: ChatMessage[] = [
        { id: "1", role: "user", content: "Message 1", timestamp: new Date() },
        {
          id: "2",
          role: "assistant",
          content: "Response 1",
          timestamp: new Date(),
        },
        { id: "3", role: "user", content: "Message 2", timestamp: new Date() },
      ];

      const compressed = await coordinator.compressMessages(messages);

      expect(mockLLMProvider.compressMessages).toHaveBeenCalledWith(
        { chatHistory: messages },
        messages
      );
      expect(compressed).toEqual([]);
    });
  });

  describe("Integration with Flow System", () => {
    test("should work with FlowOrchestrator", async () => {
      await coordinator.initialize();

      // Simulate usage in a flow
      const messages: ChatMessage[] = [
        {
          id: "1",
          role: "user",
          content: "Flow test message",
          timestamp: new Date(),
        },
      ];
      const result = await coordinator.getChatCompletion(messages);

      expect(result).toBeDefined();
      expect(mockLLMProvider.getChatCompletion).toHaveBeenCalled();
    });

    test("should provide context management for flows", async () => {
      await coordinator.initialize();

      const budget = coordinator.calculateTokenBudget({
        systemPrompt: "Flow system prompt",
        userPromptTemplate: "Test template",
        reservedTokens: 3000,
      });

      expect(budget).toBeDefined();
      expect(budget.availableTokens).toBeGreaterThan(0);
    });

    test("should handle disabled components gracefully", () => {
      const disabledWorkspace: Workspace = {
        id: 2,
        name: "Disabled Workspace",
        slug: "disabled-workspace",
        vectorTag: null,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      };
      const disabledCoordinator = new LLMCoordinator(disabledWorkspace);
      expect(disabledCoordinator).toBeInstanceOf(LLMCoordinator);
    });
  });

  describe("New Modular System Verification", () => {
    test("should be part of the new modular flow architecture", () => {
      // Verify this is not using legacy patterns
      expect(coordinator.constructor.name).toBe("LLMCoordinator");

      // Verify it has the expected modular interface
      expect(typeof coordinator.initialize).toBe("function");
      expect(typeof coordinator.getChatCompletion).toBe("function");
      expect(typeof coordinator.calculateTokenBudget).toBe("function");
      expect(typeof coordinator.getMetrics).toBe("function");
    });

    test("should integrate with other modular components", async () => {
      await coordinator.initialize();

      // Verify it creates and uses modular components
      expect(ContextWindowManager).toHaveBeenCalled();
      expect(TokenTracker).toHaveBeenCalled();
      expect(getLLMProvider).toHaveBeenCalled();
    });

    test("should use modular architecture patterns", async () => {
      await coordinator.initialize();

      // Verify it uses modular components instead of direct LLM handling
      expect((coordinator as any).connector).toBeDefined();
      expect((coordinator as any).contextManager).toBeDefined();
      expect((coordinator as any).tokenTracker).toBeDefined();

      // Verify proper initialization pattern
      expect((coordinator as any)._initialized).toBe(true);

      // Verify it delegates to modular components
      expect(getLLMProvider).toHaveBeenCalled();
      expect(ContextWindowManager).toHaveBeenCalledWith(
        expect.any(Object),
        expect.objectContaining({
          enableTokenTracking: true,
          maxIterations: 10,
          reservedOutputTokens: 4000,
        })
      );
      expect(TokenTracker).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(Object)
      );
    });
  });
});
