import { ProgressManager } from "../ProgressManager";
import { writeResponseChunk } from "../../../../helpers/chat/responses";

jest.mock("../../../../helpers/chat/responses", () => ({
  writeResponseChunk: jest.fn(),
}));

const mockWriteResponseChunk = writeResponseChunk as jest.MockedFunction<
  typeof writeResponseChunk
>;

describe("ProgressManager", () => {
  let mockResponse: any;
  let progressManager: any;
  const chatId: any = "test-chat-123";
  const flowType: any = "main";

  beforeEach(() => {
    jest.clearAllMocks();
    mockResponse = {
      write: jest.fn(),
      writableEnded: false,
    };
    progressManager = new ProgressManager(mockResponse, chatId, flowType);
  });

  describe("Initialization", () => {
    it("should initialize with correct properties", () => {
      expect(progressManager.response).toBe(mockResponse);
      expect(progressManager.chatId).toBe(chatId);
      expect(progressManager.flowType).toBe(flowType);
      expect(progressManager.currentStep).toBe(0);
      expect(progressManager.totalSteps).toBe(0);
    });

    it("should handle errors when sending messages", () => {
      (
        writeResponseChunk as jest.MockedFunction<typeof writeResponseChunk>
      ).mockImplementation(() => {
        throw new Error("Write failed");
      });

      // Should not throw
      expect(() => progressManager.updateStep("Test step")).not.toThrow();
      expect(mockWriteResponseChunk).toHaveBeenCalled();
    });
  });

  describe("Step Management", () => {
    it("should set total steps correctly", () => {
      progressManager.setTotalSteps(5);
      expect(progressManager.totalSteps).toBe(5);
    });

    it("should start a new step with correct data", () => {
      progressManager.setTotalSteps(5);
      progressManager.startStep(2, "Processing documents");

      expect(progressManager.currentStep).toBe(2);

      // Check that writeResponseChunk was called at least once
      expect(mockWriteResponseChunk).toHaveBeenCalled();

      // Find the cdbProgress call
      const cdbProgressCall: any = mockWriteResponseChunk.mock.calls.find(
        (call: any) => call[1].type === "cdbProgress"
      );

      expect(cdbProgressCall).toBeDefined();
      expect(cdbProgressCall[1]).toMatchObject({
        type: "cdbProgress",
        uuid: chatId,
        flowType: flowType,
        step: 2,
        status: "starting",
        message: "Processing documents",
      });
    });

    it("should update step with in_progress status", () => {
      progressManager.currentStep = 2;
      progressManager.updateStep("Still processing...");

      expect(writeResponseChunk).toHaveBeenCalledWith(
        mockResponse,
        expect.objectContaining({
          type: "cdbProgress",
          status: "in_progress",
          message: "Still processing...",
        })
      );
    });

    it("should complete step with success status", () => {
      progressManager.currentStep = 3;
      progressManager.completeStep("Step completed successfully");

      expect(writeResponseChunk).toHaveBeenCalledWith(
        mockResponse,
        expect.objectContaining({
          type: "cdbProgress",
          status: "complete",
          message: "Step completed successfully",
          progress: 100,
        })
      );
    });
  });

  describe("Sub-step Progress", () => {
    it("should send sub-step progress updates", () => {
      progressManager.currentStep = 2;
      progressManager.sendSubStepProgress(
        1,
        5,
        "Analyzing document",
        "document1.pdf",
        50
      );

      expect(writeResponseChunk).toHaveBeenCalledWith(
        mockResponse,
        expect.objectContaining({
          type: "cdbProgress",
          step: 2,
          subStep: 1,
          total: 5,
          message: "Analyzing document",
          label: "document1.pdf",
          progress: 50,
        })
      );
    });

    it("should handle sub-step progress without label", () => {
      progressManager.sendSubStepProgress(2, 3, "Processing");

      expect(writeResponseChunk).toHaveBeenCalledWith(
        mockResponse,
        expect.objectContaining({
          subStep: 2,
          total: 3,
          message: "Processing",
          progress: -1,
        })
      );
    });
  });

  describe("Progress Status Translation", () => {
    it("should translate status to progress values", () => {
      progressManager.sendProgress({
        status: "starting",
        message: "Starting...",
      });
      expect(writeResponseChunk).toHaveBeenCalledWith(
        mockResponse,
        expect.objectContaining({
          status: "starting",
          progress: -1,
        })
      );

      jest.clearAllMocks();
      progressManager.sendProgress({ status: "complete", message: "Done!" });
      expect(writeResponseChunk).toHaveBeenCalledWith(
        mockResponse,
        expect.objectContaining({
          status: "complete",
          progress: 100,
        })
      );

      jest.clearAllMocks();
      progressManager.sendProgress({ status: "error", message: "Failed" });
      expect(writeResponseChunk).toHaveBeenCalledWith(
        mockResponse,
        expect.objectContaining({
          status: "error",
          progress: -2,
        })
      );
    });
  });

  describe("Completion Handling", () => {
    it("should send final completion message", () => {
      progressManager.setTotalSteps(5);
      progressManager.currentStep = 5;
      progressManager.sendFinalCompletion("All steps completed");

      expect(writeResponseChunk).toHaveBeenCalledWith(
        mockResponse,
        expect.objectContaining({
          type: "cdbProgress",
          step: 5,
          status: "complete",
          message: "All steps completed",
          progress: 100,
        })
      );
    });

    it("should include data in completion message", () => {
      progressManager.sendFinalCompletion("Done", { documentId: "123" });

      expect(writeResponseChunk).toHaveBeenCalledWith(
        mockResponse,
        expect.objectContaining({
          status: "complete",
          message: "Done",
          data: { documentId: "123" },
        })
      );
    });
  });

  describe("Error Handling", () => {
    it("should send error messages", () => {
      const error: any = new Error("Processing failed");
      progressManager.currentStep = 3;
      progressManager.sendError(error);

      expect(writeResponseChunk).toHaveBeenCalledWith(
        mockResponse,
        expect.objectContaining({
          type: "cdbProgress",
          step: 3,
          status: "error",
          message: "Processing failed",
          progress: -2,
        })
      );
    });

    it("should handle string errors", () => {
      progressManager.sendError("Something went wrong");

      expect(writeResponseChunk).toHaveBeenCalledWith(
        mockResponse,
        expect.objectContaining({
          status: "error",
          message: "Something went wrong",
        })
      );
    });

    it("should send final error with current step", () => {
      progressManager.currentStep = 3;
      progressManager.sendFinalError(new Error("Fatal error"));

      expect(writeResponseChunk).toHaveBeenCalledWith(
        mockResponse,
        expect.objectContaining({
          step: 3,
          status: "error",
          message: "Fatal error",
          progress: -2,
        })
      );
    });
  });

  describe("Abort Handling", () => {
    it("should send abort notification", () => {
      progressManager.currentStep = 3;
      progressManager.sendAbort();

      expect(writeResponseChunk).toHaveBeenCalledWith(
        mockResponse,
        expect.objectContaining({
          type: "cdbProgress",
          step: 3,
          status: "aborted",
          message: "Document processing was cancelled by user.",
          progress: -3,
        })
      );
    });
  });

  describe("Flow Type Inclusion", () => {
    it("should include flow type in all messages", () => {
      progressManager.sendProgress({ message: "Test" });

      expect(writeResponseChunk).toHaveBeenCalledWith(
        mockResponse,
        expect.objectContaining({
          flowType: "main",
          uuid: chatId,
        })
      );
    });

    it("should send cdbProgress type", () => {
      progressManager.sendProgress({ message: "Test" });

      expect(mockWriteResponseChunk).toHaveBeenCalled();

      // Check that cdbProgress was sent
      const types: any = mockWriteResponseChunk.mock.calls.map(
        (call: any) => call[1].type
      );
      expect(types).toContain("cdbProgress");
    });
  });
});
