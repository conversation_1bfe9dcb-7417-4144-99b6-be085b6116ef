// External dependencies
import fs from "fs";
import path from "path";

// Internal modules
import { WorkspaceManager } from "../WorkspaceManager";

jest.mock("fs");
jest.mock("../../../../../endpoints/document", () => ({
  getUserDocumentPathName: jest.fn(
    (user: any, _isWorkspace: any, slug: any) => `${user.id}/${slug}`
  ),
}));

// Type the mocked fs module
const mockFs = fs as jest.Mocked<typeof fs>;
const mockExistsSync = mockFs.existsSync as jest.MockedFunction<
  typeof fs.existsSync
>;
const mockReaddirSync = mockFs.readdirSync as jest.MockedFunction<
  typeof fs.readdirSync
>;
const mockReadFileSync = mockFs.readFileSync as jest.MockedFunction<
  typeof fs.readFileSync
>;
const mockMkdirSync = mockFs.mkdirSync as jest.MockedFunction<
  typeof fs.mkdirSync
>;

describe("WorkspaceManager", () => {
  let workspaceManager: any;
  let mockUser: any;
  let mockWorkspace: any;

  beforeEach(() => {
    jest.clearAllMocks();

    mockUser = {
      id: 1,
      username: "testuser",
    };

    mockWorkspace = {
      id: 1,
      slug: "test-workspace",
      user_id: 1,
    };

    // Default fs mocks
    mockExistsSync.mockReturnValue(true);
    mockReaddirSync.mockReturnValue(["doc1.json", "doc2.json"] as any);
    mockReadFileSync.mockImplementation((filepath: any) => {
      if (filepath.includes("doc1.json")) {
        return JSON.stringify({
          pageContent: "Document 1 content",
          metadata: { title: "Document 1" },
          token_count_estimate: 100,
        });
      }
      if (filepath.includes("doc2.json")) {
        return JSON.stringify({
          pageContent: "Document 2 content",
          metadata: { title: "Document 2" },
          token_count_estimate: 150,
        });
      }
      return "{}";
    });
    mockMkdirSync.mockReturnValue(undefined);

    workspaceManager = new WorkspaceManager(mockUser, mockWorkspace);
  });

  describe("Initialization", () => {
    it("should initialize with correct properties", () => {
      expect(workspaceManager.user).toBe(mockUser);
      expect(workspaceManager.workspace).toBe(mockWorkspace);
      expect(workspaceManager._initialized).toBe(false);
    });

    it("should initialize workspace paths correctly", async () => {
      await workspaceManager.initialize();

      expect(workspaceManager._initialized).toBe(true);
      expect(workspaceManager.workspacePath).toBeDefined();
      expect(workspaceManager.documentBuilderBasePath).toBeDefined();
    });

    it("should validate workspace on initialization", async () => {
      mockExistsSync.mockReturnValue(false);

      await expect(workspaceManager.initialize()).rejects.toThrow(
        "Workspace documents not found"
      );
    });
  });

  describe("Document Loading", () => {
    beforeEach(async () => {
      await workspaceManager.initialize();
    });

    it("should get document files from workspace directory", () => {
      const files: any = workspaceManager.getDocumentFiles();

      expect(mockReaddirSync).toHaveBeenCalledWith(
        workspaceManager.workspacePath
      );
      expect(files).toHaveLength(2);
      expect(files).toEqual(["doc1.json", "doc2.json"]);
    });

    it("should filter documents by extension", () => {
      mockReaddirSync.mockReturnValue([
        "doc1.json",
        "readme.txt",
        "doc2.json",
        ".hidden",
      ] as any);

      const files: any = workspaceManager.getDocumentFiles({
        extensions: [".json"],
      });

      expect(files).toHaveLength(2);
      expect(files.every((file: any) => file.endsWith(".json"))).toBe(true);
    });

    it("should read a single document file", () => {
      const document: any = workspaceManager.readDocumentFile("doc1.json");

      expect(document).toMatchObject({
        id: "doc1",
        fileName: "doc1.json",
        displayName: "Document 1",
        content: "Document 1 content",
        metadata: { title: "Document 1" },
      });
    });

    it("should handle missing document file", () => {
      mockExistsSync.mockReturnValue(false);

      expect(() => workspaceManager.readDocumentFile("missing.json")).toThrow(
        "Document file not found"
      );
    });

    it("should get all documents with content", () => {
      const result: any = workspaceManager.getAllDocuments();

      expect(result.documents).toHaveLength(2);
      expect(result.documents[0]).toMatchObject({
        id: "doc1",
        displayName: "Document 1",
        content: "Document 1 content",
      });
    });

    it("should skip empty documents when requested", () => {
      mockReadFileSync.mockImplementation((filepath: any) => {
        if (filepath.includes("doc1.json")) {
          return JSON.stringify({
            pageContent: "",
            metadata: { title: "Empty Document" },
          });
        }
        return JSON.stringify({
          pageContent: "Valid content",
          metadata: { title: "Document 2" },
        });
      });

      const result: any = workspaceManager.getAllDocuments({ skipEmpty: true });

      expect(result.documents).toHaveLength(1);
      expect(result.documents[0].content).toBe("Valid content");
    });
  });

  describe("Document File Operations", () => {
    beforeEach(async () => {
      await workspaceManager.initialize();
    });

    it("should check if document exists", () => {
      const exists: any = workspaceManager.documentExists("doc1.json");
      expect(exists).toBe(true);

      mockExistsSync.mockReturnValue(false);
      const notExists: any = workspaceManager.documentExists("missing.json");
      expect(notExists).toBe(false);
    });
  });

  describe("Path Management", () => {
    beforeEach(async () => {
      await workspaceManager.initialize();
    });

    it("should get workspace path", () => {
      const workspacePath: any = workspaceManager.getWorkspacePath();
      expect(workspacePath).toBeDefined();
      expect(workspacePath).toContain(path.normalize("storage/documents"));
    });

    it("should get document builder path", () => {
      const builderPath: any = workspaceManager.getDocumentBuilderPath();
      expect(builderPath).toBeDefined();
      expect(builderPath).toContain(path.normalize("storage/document-builder"));
    });
  });

  describe("Error Handling", () => {
    beforeEach(async () => {
      await workspaceManager.initialize();
    });

    it("should handle file read errors gracefully", () => {
      mockReadFileSync.mockImplementation(() => {
        throw new Error("File corrupted");
      });

      expect(() => workspaceManager.readDocumentFile("doc1.json")).toThrow(
        "Failed to read or parse document"
      );
    });

    it("should skip errors when reading multiple files if requested", () => {
      mockReadFileSync.mockImplementation((filepath: any) => {
        if (filepath.includes("doc1.json")) {
          throw new Error("File corrupted");
        }
        return JSON.stringify({ pageContent: "Valid content" });
      });

      const result: any = workspaceManager.readDocumentFiles(
        ["doc1.json", "doc2.json"],
        { skipErrors: true }
      );

      expect(result.documents).toHaveLength(1);
      expect(result.errors).toHaveLength(1);
    });

    it("should throw on uninitialized access", () => {
      const newManager: any = new WorkspaceManager(mockUser, mockWorkspace);

      expect(() => newManager.getDocumentFiles()).toThrow(
        "WorkspaceManager must be initialized before use. Call initialize() first."
      );
    });
  });
});
