/**
 * Modular Flow System Demonstration
 *
 * This file demonstrates how the new modular flow system works using the
 * FlowOrchestrator and configurable stage processors.
 */

import { FlowOrchestrator } from "../core/FlowOrchestrator";
import { mainDocFlowConfig } from "../configurations/mainDocFlowConfig";
import { SetupStageProcessor } from "../processors/SetupStageProcessor";
import { CombinationStageProcessor } from "../processors/CombinationStageProcessor";
import { StageProcessor } from "../core/StageProcessor";
import {
  FlowConfig,
  FlowOptions as IFlowOptions,
  ExecutionSummary as IExecutionSummary,
  FlowContext,
  StageDependencies,
} from "../../../../types/chat-flow";

// Type definitions

interface CustomFlowConfig extends FlowConfig {
  description: string;
}

/**
 * Demo function showing how to use the modular flow system
 * This replaces the large monolithic flow functions with a configurable orchestrator
 */
async function runMainDocFlowModular(options: IFlowOptions): Promise<string> {
  try {
    console.log("[DEMO] Starting modular main document flow");

    // Create flow orchestrator with options and configuration
    const orchestrator = new FlowOrchestrator(options, mainDocFlowConfig);

    // Initialize the orchestrator (sets up shared infrastructure)
    await orchestrator.initialize();

    console.log("[DEMO] Flow orchestrator initialized with configuration:");
    console.log("  - Flow Type:", mainDocFlowConfig.flowType);
    console.log("  - Stages:", mainDocFlowConfig.stages.length);
    console.log(
      "  - Stage Pipeline:",
      mainDocFlowConfig.stages.map((s) => s.name).join(" -> ")
    );

    // Execute the complete flow
    const finalContent = await orchestrator.execute();

    // Get execution summary
    const summary: IExecutionSummary = orchestrator.getExecutionSummary();
    console.log("[DEMO] Flow execution completed:");
    console.log("  - Stages Executed:", summary.stagesExecuted);
    console.log("  - Documents Processed:", summary.documentsProcessed);
    console.log("  - Sections Generated:", summary.sectionsGenerated);
    console.log("  - Final Content Length:", summary.finalContentLength);
    console.log(
      "  - Total Execution Time:",
      summary.metrics.totalExecutionTime + "s"
    );

    return finalContent;
  } catch (error: unknown) {
    console.error(
      "[DEMO] Modular flow failed:",
      error instanceof Error ? error.message : String(error)
    );
    throw error;
  }
}

/**
 * Comparison function showing the benefits of the modular approach
 */
function compareApproaches(): void {
  console.log("\n" + "=".repeat(60));
  console.log("MODULAR FLOW SYSTEM BENEFITS");
  console.log("=".repeat(60));

  console.log("\n1. CODE REUSABILITY:");
  console.log("   ✓ Stage processors shared across all flow types");
  console.log(
    "   ✓ Common infrastructure (progress, workspace, LLM management)"
  );
  console.log("   ✓ Eliminates duplicate code between flows");

  console.log("\n2. MAINTAINABILITY:");
  console.log("   ✓ Changes to a stage affect all flows consistently");
  console.log("   ✓ Easy to add new stages or modify existing ones");
  console.log("   ✓ Clear separation of concerns");

  console.log("\n3. TESTABILITY:");
  console.log("   ✓ Each stage processor can be tested independently");
  console.log("   ✓ Mock dependencies for isolated testing");
  console.log("   ✓ Flow configurations can be tested separately");

  console.log("\n4. EXTENSIBILITY:");
  console.log("   ✓ New flows created by configuring stages");
  console.log("   ✓ Easy to add flow-specific processors");
  console.log("   ✓ Plugin-like architecture for custom stages");

  console.log("\n5. DEBUGGING:");
  console.log("   ✓ Clear stage-by-stage execution tracking");
  console.log("   ✓ Detailed metrics and logging per stage");
  console.log("   ✓ Easy to identify where issues occur");

  console.log("\n6. PERFORMANCE:");
  console.log("   ✓ Shared LLM connections and context management");
  console.log("   ✓ Consistent token tracking across stages");
  console.log("   ✓ Reduced memory overhead from shared utilities");
}

/**
 * Example of how to create a custom flow configuration
 */
function createCustomFlowExample(): CustomFlowConfig {
  console.log("\n" + "=".repeat(60));
  console.log("CUSTOM FLOW CREATION EXAMPLE");
  console.log("=".repeat(60));

  class CustomProcessor extends StageProcessor {
    async process(
      _context: FlowContext,
      dependencies: StageDependencies
    ): Promise<Partial<FlowContext>> {
      const { progressManager, abortChecker } = dependencies;

      abortChecker();
      progressManager.updateStep("Running custom processing...");

      // Custom stage logic here
      this.log("info", "Custom processing stage executed");

      return {
        customData: "This is custom processing output",
      };
    }
  }

  const customFlowConfig: CustomFlowConfig = {
    flowType: "custom",
    description: "Example custom flow configuration",

    stages: [
      {
        name: "setup",
        processor: SetupStageProcessor as any,
        options: {},
      },
      {
        name: "customProcessing",
        processor: CustomProcessor as any,
        options: { customOption: true },
      },
      {
        name: "combination",
        processor: CombinationStageProcessor as any,
        options: {},
      },
    ],

    prompts: {
      customPrompt: {
        PROMPT_TEMPLATE: "CUSTOM_PROMPT_KEY",
      },
    },

    options: {
      enableCustomFeature: true,
    },
  };

  console.log("\nCustom flow configuration created with:");
  console.log("  - Flow Type:", customFlowConfig.flowType);
  console.log(
    "  - Stages:",
    customFlowConfig.stages.map((s) => s.name).join(", ")
  );
  console.log("  - Custom Processor: Included");

  return customFlowConfig;
}

/**
 * Migration guide for existing flows
 */
function migrationGuide(): void {
  console.log("\n" + "=".repeat(60));
  console.log("MIGRATION GUIDE");
  console.log("=".repeat(60));

  console.log("\nSTEPS TO MIGRATE EXISTING FLOWS:");
  console.log("1. Identify common code patterns in existing flows");
  console.log("2. Extract common logic into stage processors");
  console.log("3. Create flow configuration for each flow type");
  console.log("4. Replace flow function with FlowOrchestrator usage");
  console.log("5. Test that behavior is identical to original");
  console.log("6. Remove duplicate code from original flows");

  console.log("\nBEFORE (Monolithic):");
  console.log("   runMainDocFlow() - 1,761 lines");
  console.log("   runNoMainDocFlow() - 1,342 lines");
  console.log("   runReferenceFlow() - 629 lines");
  console.log("   Total: 3,732 lines with lots of duplication");

  console.log("\nAFTER (Modular):");
  console.log("   FlowOrchestrator - 400 lines (reusable)");
  console.log("   Stage Processors - ~200 lines each (reusable)");
  console.log("   Flow Configurations - ~100 lines each (declarative)");
  console.log("   Total: ~1,200 lines with no duplication");

  console.log("\nCODE REDUCTION: ~67% reduction in total lines");
}

// Export demo functions
export {
  runMainDocFlowModular,
  compareApproaches,
  createCustomFlowExample,
  migrationGuide,
};

// Run demo if this file is executed directly
if (require.main === module) {
  console.log("MODULAR FLOW SYSTEM DEMONSTRATION");
  console.log("=".repeat(60));

  compareApproaches();
  createCustomFlowExample();
  migrationGuide();

  console.log("\n" + "=".repeat(60));
  console.log("DEMO COMPLETED");
  console.log("=".repeat(60));
}
