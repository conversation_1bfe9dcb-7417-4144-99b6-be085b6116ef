/**
 * @deprecated
 * This file is part of the legacy document drafting system and is no longer in use.
 * The logic has been replaced by a flexible, configuration-driven system.
 *
 * @see {@link server/utils/chats/flowDispatcher.ts} - The new entry point for all flows.
 * @see {@link server/utils/chats/flows/configurations/noMainDocFlowConfig.ts} - The new configuration for this flow.
 * @see {@link server/docs/configuration-driven-flows.md} - for a full explanation of the new system.
 *
 * Do not use this file for new development. It will be removed in a future update.
 */

// Type definitions for the legacy interface
interface LegacyNoMainDocFlowOptions {
  abortSignal?: AbortSignal | null;
  chatId: string;
  response: unknown;
  request: unknown;
  workspace: unknown;
  message: string;
  chatMode?: string;
  user?: unknown;
  thread?: unknown;
  attachments?: unknown[];
  isCanvasChat?: boolean;
  preventChatCreation?: boolean;
  settings_suffix?: string;
  invoice_ref?: string;
  vectorSearchMode?: string;
  hasUploadedFile?: boolean;
  displayMessage?: string | null;
  useDeepSearch?: boolean;
  cdbOptions?: unknown[];
  [key: string]: unknown;
}

/**
 * @deprecated Legacy no main document flow function
 * Use the modular flow system instead
 */
async function runNoMainDocFlow(
  _options: LegacyNoMainDocFlowOptions
): Promise<string> {
  console.warn(
    "⚠️  DEPRECATED: runNoMainDocFlow is deprecated. Use the modular flow system via flowDispatcher instead."
  );
  console.warn(
    "📖 See server/docs/configuration-driven-flows.md for migration guide."
  );

  throw new Error(
    "Legacy flow functions are no longer supported. Please use the modular flow system via flowDispatcher.ts"
  );
}

export { runNoMainDocFlow };
