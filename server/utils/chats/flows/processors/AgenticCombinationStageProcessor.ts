import * as fs from "fs";
import * as path from "path";
import { StageProcessor } from "../core/StageProcessor";
import SystemSettings from "../../../../models/systemSettings";
import { writeResponseChunk } from "../../../helpers/chat/responses";
import * as robustLlmUtils from "../../../robustLlmUtils";
const { initializeRobustLLMConnector, getActiveLLMConfig } = robustLlmUtils;
import { ProseMirrorDocument } from "../../../../types/prosemirror";
import {
  convertProseMirrorToMarkdown,
  createProseMirrorDocumentFromSections,
} from "../../../proseMirror/documentUtils";
import {
  performLineLevelEditing,
  performGranularEditing,
  countWords,
} from "../../../documentEditing/editingLogic";
import { EditingStatistics } from "../../../../types/document-processing";
import type { LLMConfig as RobustLLMConfig } from "../../../robustLlmUtils/config/llmConfig";
import { LLMConnector } from "../../LLMConnector";
import {
  FlowContext,
  StageDependencies,
  SectionOutput,
  ProgressManager,
  Memo,
} from "../../../../types/chat-flow";
import { Response } from "express";
import type { FlowMetrics } from "../../../../types/chat-flow";

interface AgenticCombinationConfig {
  // Master switches
  agenticEditingEnabled?: boolean;
  granularEditingEnabled?: boolean;
  lineEditingEnabled?: boolean;

  // Granular editing parameters
  granularEditingIterations?: number;
  granularSuggestionsToRequest?: number;
  granularMaxLLMAttempts?: number;
  granularBatchSize?: number;

  // Line-level editing parameters
  lineEditingIterations?: number;
  lineSuggestionsToRequest?: number;
  lineMaxLLMAttempts?: number;
  lineBatchSize?: number;

  // Development and debugging
  saveFullDocumentIterations?: boolean;
  includeMetadata?: boolean;
  sendFinalResult?: boolean;

  [key: string]: unknown;
}

interface SectionForProseMirror {
  title: string;
  content: string;
  sectionNumber: number;
}

interface AgenticEditingResult {
  success: boolean;
  finalDocument?: string;
  editedPmDoc?: ProseMirrorDocument;
  error?: string;
}

interface AgenticEditParams {
  maxLineSuggestionAttempts: number;
  suggestionsPerLineBatch: number;
  lineBatchSize: number;
  maxGranularSuggestionAttempts: number;
  suggestionsPerGranularBatch: number;
  granularBatchSize: number;
  language: string;
  [key: string]: unknown;
}

interface EditingResult {
  success: boolean;
  data?: ProseMirrorDocument;
  error?: string;
  stats?: EditingStatistics;
}

interface CombinationMetrics {
  sectionsProcessed: number;
  finalContentLength: number;
  finalWordCount: number;
  initialWordCount: number;
  wordChangeFromEditing: number;
  agenticEditingApplied: boolean;
  combinedAt: string;
  [key: string]: string | number | boolean;
}

interface LegalMemo {
  id: string;
  content: string;
  title?: string;
  issue: string;
  memoFilePath: string;
  [key: string]: unknown;
}

/**
 * Agentic Combination Stage Processor
 *
 * Enhanced combination processor that incorporates agentic editing capabilities.
 * Can replace the standard combination step with AI-powered document refinement.
 *
 * Features:
 * - Granular editing with multiple iterations
 * - Line-level editing with configurable parameters
 * - Language detection and adaptation
 * - Progress tracking and error handling
 * - Configurable editing parameters
 * - Development file saving for debugging
 */
class AgenticCombinationStageProcessor extends StageProcessor {
  private config: Required<AgenticCombinationConfig>;

  constructor(...args: unknown[]) {
    const options = (args[0] ?? {}) as AgenticCombinationConfig;
    super(options);
    // Default configuration - can be overridden via options
    this.config = {
      // Master switches
      agenticEditingEnabled: options.agenticEditingEnabled ?? true,
      granularEditingEnabled: options.granularEditingEnabled ?? true,
      lineEditingEnabled: options.lineEditingEnabled ?? true,
      // Granular editing parameters
      granularEditingIterations: options.granularEditingIterations ?? 3,
      granularSuggestionsToRequest: options.granularSuggestionsToRequest ?? 5,
      granularMaxLLMAttempts: options.granularMaxLLMAttempts ?? 3,
      granularBatchSize: options.granularBatchSize ?? 50,
      // Line-level editing parameters
      lineEditingIterations: options.lineEditingIterations ?? 2,
      lineSuggestionsToRequest: options.lineSuggestionsToRequest ?? 5,
      lineMaxLLMAttempts: options.lineMaxLLMAttempts ?? 3,
      lineBatchSize: options.lineBatchSize ?? 50,
      // Development and debugging
      saveFullDocumentIterations: options.saveFullDocumentIterations ?? false,
      includeMetadata: options.includeMetadata ?? true,
      sendFinalResult: options.sendFinalResult ?? true,
      ...options,
    } as Required<AgenticCombinationConfig>;
  }

  /**
   * Process the agentic combination stage
   */
  async process(
    context: FlowContext,
    dependencies: StageDependencies
  ): Promise<Partial<FlowContext>> {
    const { progressManager, abortChecker, chatId, options } = dependencies;

    this.log("info", "Starting agentic combination stage", {
      sectionOutputsCount: context?.sectionOutputs?.length || 0,
      config: this.config,
      chatId,
    });

    // Check abort signal
    abortChecker();

    if (!context.sectionOutputs || context.sectionOutputs.length === 0) {
      throw new Error("No section outputs available for agentic combination");
    }

    progressManager.updateStep("Building initial document from sections...");

    try {
      // Step 1: Build initial ProseMirror document from sections
      const sectionsForProseMirror: SectionForProseMirror[] =
        context.sectionOutputs.map((section: SectionOutput, index: number) => ({
          title: section.title || `Section ${index + 1}`,
          content: section.content || "(Content not available)",
          sectionNumber: section.sectionNumber || index + 1,
        }));

      this.log("info", "Building ProseMirror document", {
        sectionsCount: sectionsForProseMirror.length,
        sampleSectionContent:
          sectionsForProseMirror[0]?.content?.substring(0, 100) || "No content",
      });

      const initialProseMirrorDoc: ProseMirrorDocument =
        await createProseMirrorDocumentFromSections(sectionsForProseMirror);

      // Validate that we have a proper document
      if (
        !initialProseMirrorDoc ||
        !initialProseMirrorDoc.content ||
        initialProseMirrorDoc.content.length === 0
      ) {
        this.log(
          "warn",
          "ProseMirror document creation resulted in empty document",
          {
            inputSections: sectionsForProseMirror.length,
            hasContent: sectionsForProseMirror.some(
              (s) => s.content && s.content.trim().length > 0
            ),
          }
        );

        // Create a fallback document with section content as plain text
        const fallbackContent = sectionsForProseMirror
          .filter((s) => s.content && s.content.trim())
          .map((s) => `# ${s.title}\n\n${s.content}`)
          .join("\n\n");

        if (fallbackContent.trim()) {
          this.log("info", "Creating fallback document from section content");
          const { parseMarkdownToProseMirrorJSON } = await import(
            "../../../proseMirror/markdownParser"
          );
          const fallbackDoc: ProseMirrorDocument =
            await parseMarkdownToProseMirrorJSON(fallbackContent);
          if (
            fallbackDoc &&
            fallbackDoc.content &&
            (fallbackDoc?.content?.length ?? 0) > 0
          ) {
            initialProseMirrorDoc.content = fallbackDoc.content;
          }
        }
      }

      let finalDocument = convertProseMirrorToMarkdown(initialProseMirrorDoc);
      const initialWordCount = countWords(finalDocument);

      this.log("info", "Initial document built", {
        wordCount: initialWordCount,
        length: finalDocument.length,
      });

      // Check abort after initial document creation
      abortChecker();

      // Step 2: Apply agentic editing if enabled
      if (this.config.agenticEditingEnabled) {
        // Calculate total sub-tasks for progress tracking
        const totalSubTasks =
          (this.config.granularEditingEnabled
            ? this.config.granularEditingIterations
            : 0) +
          (this.config.lineEditingEnabled
            ? this.config.lineEditingIterations
            : 0);

        progressManager.updateStep(
          "Performing agentic editing enhancements...",
          { total: totalSubTasks }
        );

        const agenticResult = await this.performAgenticEditing(
          initialProseMirrorDoc,
          context,
          dependencies,
          progressManager,
          abortChecker
        );

        if (agenticResult.success) {
          finalDocument = agenticResult.finalDocument!;

          this.log("info", "Agentic editing completed", {
            initialWordCount,
            finalWordCount: countWords(finalDocument),
            wordChange: countWords(finalDocument) - initialWordCount,
          });
        } else {
          this.log("warn", "Agentic editing failed, using original document", {
            error: agenticResult.error,
          });
        }
      } else {
        this.log(
          "info",
          "Agentic editing disabled, using standard combination"
        );
      }

      // Step 3: Add metadata if requested
      if (this.config.includeMetadata) {
        finalDocument = this.addDocumentMetadata(finalDocument, context);
      }

      // Step 4: Calculate final metrics
      const combinationMetrics: CombinationMetrics = {
        sectionsProcessed: context.sectionOutputs.length,
        finalContentLength: finalDocument.length,
        finalWordCount: countWords(finalDocument),
        initialWordCount,
        wordChangeFromEditing: countWords(finalDocument) - initialWordCount,
        agenticEditingApplied: this.config.agenticEditingEnabled,
        combinedAt: new Date().toISOString(),
      };

      this.log("info", "Document combination completed", combinationMetrics);

      // Step 5: Send final result to client if enabled
      if (this.config.sendFinalResult) {
        await this.sendFinalResult(finalDocument, chatId, options.response);
      }

      // Return data to add to context
      return {
        finalContent: finalDocument,
        metrics: {
          ...context.metrics,
          agenticCombination: combinationMetrics,
        } as FlowMetrics,
      };
    } catch (_error) {
      console.error("Error in combination stage:", _error);
      throw _error;
    }
  }

  /**
   * Perform the agentic editing process
   */
  private async performAgenticEditing(
    initialProseMirrorDoc: ProseMirrorDocument,
    context: FlowContext,
    dependencies: StageDependencies,
    progressManager: ProgressManager,
    abortChecker: () => void
  ): Promise<AgenticEditingResult> {
    const { chatId } = dependencies;

    try {
      // Initialize LLM connector for agentic editing
      // Get settings_suffix from flow options if available
      const settings_suffix =
        (dependencies.options?.settings_suffix as string) || undefined;

      const llmConfig: RobustLLMConfig = getActiveLLMConfig(settings_suffix);
      const LLMConnector = await initializeRobustLLMConnector(llmConfig);

      if (!LLMConnector) {
        throw new Error(
          "Failed to initialize LLM connector for agentic editing"
        );
      }

      // Detect language for editing
      const detectedLanguage = await this.detectLanguage(
        dependencies.legalTask || "Document editing task",
        LLMConnector
      );

      // Prepare agentic editing parameters
      const agenticEditParams: AgenticEditParams = {
        maxLineSuggestionAttempts: this.config.lineMaxLLMAttempts,
        suggestionsPerLineBatch: this.config.lineSuggestionsToRequest,
        lineBatchSize: this.config.lineBatchSize,
        maxGranularSuggestionAttempts: this.config.granularMaxLLMAttempts,
        suggestionsPerGranularBatch: this.config.granularSuggestionsToRequest,
        granularBatchSize: this.config.granularBatchSize,
        language: detectedLanguage,
      };

      // Format research text from available memos
      const researchMemos = this.formatResearchMemosFromContext(context);

      // Setup file paths for development saves
      const documentBuilderBasePath = this.getDocumentBuilderBasePath();
      let fullDocIterationsPath: string | null = null;

      if (this.config.saveFullDocumentIterations && chatId) {
        fullDocIterationsPath = path.join(
          documentBuilderBasePath,
          "dev_full_doc_iterations",
          chatId
        );
        this.ensureDirectoryExists(fullDocIterationsPath);
      }

      let agentEditedPmDoc = JSON.parse(JSON.stringify(initialProseMirrorDoc)); // Deep copy
      const legalTask = dependencies.legalTask || "Document improvement task";

      // Track current sub-task position for progress reporting
      let currentSubTask = 0;
      const totalSubTasks =
        (this.config.granularEditingEnabled
          ? this.config.granularEditingIterations
          : 0) +
        (this.config.lineEditingEnabled
          ? this.config.lineEditingIterations
          : 0);

      // Perform granular editing if enabled
      if (this.config.granularEditingEnabled) {
        progressManager.updateStep("Performing granular agentic editing...");

        agentEditedPmDoc = await this.performGranularEditingIterations(
          agentEditedPmDoc,
          researchMemos,
          legalTask,
          detectedLanguage,
          llmConfig,
          agenticEditParams,
          progressManager,
          abortChecker,
          fullDocIterationsPath,
          chatId,
          currentSubTask,
          totalSubTasks
        );

        currentSubTask += this.config.granularEditingIterations;
      }

      // Perform line-level editing if enabled
      if (this.config.lineEditingEnabled) {
        progressManager.updateStep("Performing line-level agentic editing...");

        agentEditedPmDoc = await this.performLineLevelEditingIterations(
          agentEditedPmDoc,
          researchMemos,
          legalTask,
          detectedLanguage,
          llmConfig,
          agenticEditParams,
          progressManager,
          abortChecker,
          fullDocIterationsPath,
          chatId,
          documentBuilderBasePath,
          currentSubTask,
          totalSubTasks
        );
      }

      // Convert final document to markdown
      const finalEditedMarkdown =
        convertProseMirrorToMarkdown(agentEditedPmDoc);

      // Save final edited document if requested
      if (this.config.saveFullDocumentIterations && chatId) {
        const finalDocPath = path.join(
          documentBuilderBasePath,
          `final-document-agentic-edited-${chatId}.md`
        );
        fs.writeFileSync(finalDocPath, finalEditedMarkdown, "utf8");
        this.log("info", "Saved final agentic edited document", {
          path: finalDocPath,
          wordCount: countWords(finalEditedMarkdown),
        });
      }

      return {
        success: true,
        finalDocument: finalEditedMarkdown,
        editedPmDoc: agentEditedPmDoc,
      };
    } catch (_error) {
      this.log("error", "Agentic editing process failed", {
        error: _error instanceof Error ? _error.message : String(_error),
      });
      return {
        success: false,
        error: _error instanceof Error ? _error.message : String(_error),
        finalDocument: convertProseMirrorToMarkdown(initialProseMirrorDoc),
      };
    }
  }

  /**
   * Perform granular editing iterations
   */
  private async performGranularEditingIterations(
    pmDoc: ProseMirrorDocument,
    researchMemos: LegalMemo[],
    legalTask: string,
    language: string,
    llmConfig: RobustLLMConfig,
    editParams: AgenticEditParams,
    progressManager: ProgressManager,
    abortChecker: () => void,
    fullDocIterationsPath: string | null,
    chatId: string,
    startingSubTask: number = 0,
    totalSubTasks: number = 0
  ): Promise<ProseMirrorDocument> {
    let currentDoc = JSON.parse(JSON.stringify(pmDoc));
    const preGranularWordCount = countWords(
      convertProseMirrorToMarkdown(currentDoc)
    );

    this.log("info", "Starting granular editing", {
      iterations: this.config.granularEditingIterations,
      initialWordCount: preGranularWordCount,
    });

    for (let i = 0; i < this.config.granularEditingIterations; i++) {
      abortChecker();

      const currentSubTaskNumber = startingSubTask + i + 1;
      const iterationLabel = `Granular edit ${i + 1}/${this.config.granularEditingIterations}`;

      // Send sub-step progress - starting iteration
      progressManager.sendSubStepProgress(
        currentSubTaskNumber,
        totalSubTasks,
        `granularEditStarting:${i + 1}:${this.config.granularEditingIterations}`,
        iterationLabel,
        -1 // Loading state
      );

      const iterStartWordCount = countWords(
        convertProseMirrorToMarkdown(currentDoc)
      );

      try {
        // Ensure llmConfig.model is always a string
        const granularLlmConfig = {
          ...llmConfig,
          model: llmConfig.model || "default",
          provider: llmConfig.provider as any,
        };
        const granularResult = (await performGranularEditing(
          currentDoc,
          researchMemos,
          legalTask,
          language,
          granularLlmConfig,
          editParams,
          (progressMsg: string) => {
            progressManager.updateStep(
              `Granular iter ${i + 1}: ${progressMsg}`
            );
          }
        )) as EditingResult;

        if (granularResult.success && granularResult.data) {
          currentDoc = granularResult.data;
          const iterEndWordCount = countWords(
            convertProseMirrorToMarkdown(currentDoc)
          );
          const wordChange = iterEndWordCount - iterStartWordCount;

          this.log("info", "Granular iteration completed", {
            iteration: i + 1,
            wordChange,
            finalWordCount: iterEndWordCount,
            stats: granularResult.stats,
          });

          // Send sub-step progress - iteration completed
          progressManager.sendSubStepProgress(
            currentSubTaskNumber,
            totalSubTasks,
            `granularEditCompleted:${i + 1}:${this.config.granularEditingIterations}:${wordChange > 0 ? "+" : ""}${wordChange}`,
            iterationLabel,
            100 // Complete
          );

          // Save iteration if development mode enabled
          if (fullDocIterationsPath && chatId) {
            this.saveIterationDocument(
              currentDoc,
              fullDocIterationsPath,
              `granular_edit_iter_${i + 1}`,
              chatId
            );
          }
        } else {
          this.log("warn", "Granular iteration failed", {
            iteration: i + 1,
            error: granularResult.error,
          });

          // Send sub-step progress - iteration failed
          progressManager.sendSubStepProgress(
            currentSubTaskNumber,
            totalSubTasks,
            `granularEditFailed:${i + 1}:${this.config.granularEditingIterations}`,
            iterationLabel,
            -2 // Error state
          );

          // Break on critical failures
          if (
            granularResult.error &&
            granularResult.error.includes("Failed to initialize LLM")
          ) {
            this.log(
              "warn",
              "Critical granular editing failure, stopping iterations"
            );
            break;
          }
        }
      } catch (_error) {
        this.log("error", "Granular editing iteration error", {
          iteration: i + 1,
          error: _error instanceof Error ? _error.message : String(_error),
        });
      }
    }

    const postGranularWordCount = countWords(
      convertProseMirrorToMarkdown(currentDoc)
    );
    const totalGranularChange = postGranularWordCount - preGranularWordCount;

    this.log("info", "Granular editing completed", {
      totalWordChange: totalGranularChange,
      finalWordCount: postGranularWordCount,
    });

    return currentDoc;
  }

  /**
   * Perform line-level editing iterations
   */
  private async performLineLevelEditingIterations(
    pmDoc: ProseMirrorDocument,
    researchMemos: LegalMemo[],
    legalTask: string,
    language: string,
    llmConfig: RobustLLMConfig,
    editParams: AgenticEditParams,
    progressManager: ProgressManager,
    abortChecker: () => void,
    fullDocIterationsPath: string | null,
    chatId: string,
    basePath: string,
    startingSubTask: number = 0,
    totalSubTasks: number = 0
  ): Promise<ProseMirrorDocument> {
    let currentDoc = JSON.parse(JSON.stringify(pmDoc));
    const preLineWordCount = countWords(
      convertProseMirrorToMarkdown(currentDoc)
    );

    this.log("info", "Starting line-level editing", {
      iterations: this.config.lineEditingIterations,
      initialWordCount: preLineWordCount,
    });

    for (let i = 0; i < this.config.lineEditingIterations; i++) {
      abortChecker();

      const currentSubTaskNumber = startingSubTask + i + 1;
      const iterationLabel = `Line edit ${i + 1}/${this.config.lineEditingIterations}`;

      // Send sub-step progress - starting iteration
      progressManager.sendSubStepProgress(
        currentSubTaskNumber,
        totalSubTasks,
        `lineEditStarting:${i + 1}:${this.config.lineEditingIterations}`,
        iterationLabel,
        -1 // Loading state
      );

      const iterStartWordCount = countWords(
        convertProseMirrorToMarkdown(currentDoc)
      );

      try {
        // Ensure llmConfig.model is always a string
        const lineLlmConfig = {
          ...llmConfig,
          model: llmConfig.model || "default",
          provider: llmConfig.provider as any,
        };
        const lineResult = (await performLineLevelEditing(
          currentDoc,
          researchMemos,
          legalTask,
          language,
          lineLlmConfig,
          editParams,
          (progressMsg: string) => {
            progressManager.updateStep(`Line iter ${i + 1}: ${progressMsg}`);
          },
          chatId,
          basePath
        )) as EditingResult;

        if (lineResult.success && lineResult.data) {
          currentDoc = lineResult.data;
          const iterEndWordCount = countWords(
            convertProseMirrorToMarkdown(currentDoc)
          );
          const wordChange = iterEndWordCount - iterStartWordCount;

          this.log("info", "Line-level iteration completed", {
            iteration: i + 1,
            wordChange,
            finalWordCount: iterEndWordCount,
            stats: lineResult.stats,
          });

          // Send sub-step progress - iteration completed
          progressManager.sendSubStepProgress(
            currentSubTaskNumber,
            totalSubTasks,
            `lineEditCompleted:${i + 1}:${this.config.lineEditingIterations}:${wordChange > 0 ? "+" : ""}${wordChange}`,
            iterationLabel,
            100 // Complete
          );

          // Save iteration if development mode enabled
          if (fullDocIterationsPath && chatId) {
            this.saveIterationDocument(
              currentDoc,
              fullDocIterationsPath,
              `line_edit_iter_${i + 1}`,
              chatId
            );
          }
        } else {
          this.log("warn", "Line-level iteration failed", {
            iteration: i + 1,
            error: lineResult.error,
          });

          // Send sub-step progress - iteration failed
          progressManager.sendSubStepProgress(
            currentSubTaskNumber,
            totalSubTasks,
            `lineEditFailed:${i + 1}:${this.config.lineEditingIterations}`,
            iterationLabel,
            -2 // Error state
          );

          // Break on critical failures
          if (
            lineResult.error &&
            lineResult.error.includes("Failed to initialize LLM")
          ) {
            this.log(
              "warn",
              "Critical line-level editing failure, stopping iterations"
            );
            break;
          }
        }
      } catch (_error) {
        this.log("error", "Line-level editing iteration error", {
          iteration: i + 1,
          error: _error instanceof Error ? _error.message : String(_error),
        });
      }
    }

    const postLineWordCount = countWords(
      convertProseMirrorToMarkdown(currentDoc)
    );
    const totalLineChange = postLineWordCount - preLineWordCount;

    this.log("info", "Line-level editing completed", {
      totalWordChange: totalLineChange,
      finalWordCount: postLineWordCount,
    });

    return currentDoc;
  }

  /**
   * Detect language for agentic editing
   */
  private async detectLanguage(
    text: string,
    llmConnector: LLMConnector
  ): Promise<string> {
    if (!text || !llmConnector) return "English"; // Default fallback

    try {
      const systemPrompt =
        "You are a language detection expert. Your task is to identify the language of the given text. Respond with only the name of the language in English (e.g., 'English', 'Swedish', 'French'). Do not add any other text, explanation, or punctuation.";
      const userPrompt = `What language is this text written in?\n\nText: "${text}"`;

      const messages = [
        { role: "system" as const, content: systemPrompt },
        { role: "user" as const, content: userPrompt },
      ];

      const response = await llmConnector.getChatCompletion(messages, {
        temperature: 0,
      });

      if (response && response.textResponse) {
        const language = response.textResponse.trim().replace(/['".]/g, "");
        this.log("info", "Language detected", { language });
        return language;
      }
    } catch (_error) {
      this.log("error", "Language detection failed", {
        error: _error instanceof Error ? _error.message : String(_error),
      });
    }

    // Fallback to system default
    try {
      const systemLanguage = await SystemSettings.get({
        label: "default_language",
      });
      return systemLanguage?.value || "English";
    } catch {
      this.log("warn", "Failed to get system default language, using English");
      return "English";
    }
  }

  /**
   * Format research memos from context
   */
  private formatResearchMemosFromContext(context: FlowContext): LegalMemo[] {
    const legalMemos: LegalMemo[] = [];

    // Extract memos from section outputs
    if (context.sectionOutputs && Array.isArray(context.sectionOutputs)) {
      context.sectionOutputs.forEach((section: SectionOutput) => {
        if (
          section.relevantMemos &&
          (section?.relevantMemos?.length ?? 0) > 0
        ) {
          section.relevantMemos.forEach((memoObj: Memo) => {
            if (memoObj.memoFilePath && fs.existsSync(memoObj.memoFilePath)) {
              try {
                const memoContent = fs.readFileSync(
                  memoObj.memoFilePath,
                  "utf8"
                );
                const issueText =
                  typeof memoObj.issue === "string"
                    ? memoObj.issue
                    : "Unknown issue";
                legalMemos.push({
                  id: `memo-${legalMemos.length + 1}`,
                  title: `Memo on: ${issueText}`,
                  content: memoContent,
                  issue: issueText,
                  memoFilePath: memoObj.memoFilePath || "",
                });
              } catch (_error) {
                this.log("warn", "Failed to read memo file", {
                  path: memoObj.memoFilePath,
                  error:
                    _error instanceof Error ? _error.message : String(_error),
                });
              }
            }
          });
        }
      });
    }

    // Return memos array directly
    return legalMemos;
  }

  /**
   * Get document builder base path
   */
  private getDocumentBuilderBasePath(): string {
    return path.join(__dirname, "../../../../storage/document-builder");
  }

  /**
   * Ensure directory exists
   */
  private ensureDirectoryExists(dirPath: string): void {
    try {
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        this.log("info", "Created directory", { path: dirPath });
      }
    } catch (_error) {
      this.log("error", "Failed to create directory", {
        path: dirPath,
        error: _error instanceof Error ? _error.message : String(_error),
      });
    }
  }

  /**
   * Save iteration document for debugging
   */
  private saveIterationDocument(
    pmDoc: ProseMirrorDocument,
    basePath: string,
    iterationType: string,
    chatId: string
  ): void {
    try {
      const markdown = convertProseMirrorToMarkdown(pmDoc);
      const filename = `full_document_${iterationType}_${chatId}.md`;
      const filePath = path.join(basePath, filename);

      fs.writeFileSync(filePath, markdown, "utf8");

      this.log("info", "Saved iteration document", {
        type: iterationType,
        path: filePath,
        wordCount: countWords(markdown),
      });
    } catch (_error) {
      this.log("error", "Failed to save iteration document", {
        type: iterationType,
        error: _error instanceof Error ? _error.message : String(_error),
      });
    }
  }

  /**
   * Add metadata header to the final document
   */
  private addDocumentMetadata(content: string, context: FlowContext): string {
    const metadata = this.buildDocumentMetadata(context);
    if (!metadata) return content;
    return `${metadata}\n\n${content}`;
  }

  /**
   * Build document metadata section
   */
  private buildDocumentMetadata(context: FlowContext): string {
    const metadataLines: string[] = [];

    metadataLines.push("<!-- Agentic Document Generation Metadata -->");
    metadataLines.push(`<!-- Generated: ${new Date().toISOString()} -->`);
    metadataLines.push(
      `<!-- Agentic Editing Applied: ${this.config.agenticEditingEnabled} -->`
    );

    if (this.config.agenticEditingEnabled) {
      metadataLines.push(
        `<!-- Granular Editing: ${this.config.granularEditingEnabled} (${this.config.granularEditingIterations} iterations) -->`
      );
      metadataLines.push(
        `<!-- Line-level Editing: ${this.config.lineEditingEnabled} (${this.config.lineEditingIterations} iterations) -->`
      );
    }

    if (context?.metrics?.agenticCombination) {
      const metrics = context.metrics.agenticCombination;
      metadataLines.push(
        `<!-- Final Word Count: ${metrics.finalWordCount} -->`
      );
      metadataLines.push(
        `<!-- Word Change from Editing: ${metrics.wordChangeFromEditing} -->`
      );
    }

    metadataLines.push("<!-- End Agentic Metadata -->");
    return metadataLines.join("\n");
  }

  /**
   * Send final result to the client
   */
  private async sendFinalResult(
    finalDocument: string,
    chatId: string,
    response: Response
  ): Promise<void> {
    try {
      if (!response || response.writableEnded) {
        this.log("warn", "Response stream not available for final result");
        return;
      }

      writeResponseChunk(response, {
        uuid: chatId,
        type: "text",
        text: finalDocument,
        sources: [],
        close: true,
      });

      this.log("info", "Final agentic result sent to client", {
        contentLength: finalDocument.length,
        wordCount: countWords(finalDocument),
      });
    } catch (_error) {
      this.log("error", "Failed to send final result to client", {
        error: _error instanceof Error ? _error.message : String(_error),
      });
    }
  }

  /**
   * Validate inputs for agentic combination stage
   */
  validateInputs(context: FlowContext): boolean {
    if (!context.sectionOutputs || !Array.isArray(context.sectionOutputs)) {
      this.log("error", "Invalid input: sectionOutputs must be an array");
      return false;
    }

    if (context.sectionOutputs.length === 0) {
      this.log("error", "Invalid input: no section outputs provided");
      return false;
    }

    return true;
  }

  /**
   * Check if agentic combination stage should be skipped
   */
  shouldSkip(context: FlowContext): boolean {
    // Skip if final content is already generated
    return !!(context.finalContent && (context?.finalContent?.length ?? 0) > 0);
  }
}

export {
  AgenticCombinationStageProcessor,
  type AgenticCombinationConfig,
  type SectionForProseMirror,
  type AgenticEditingResult,
  type AgenticEditParams,
  type EditingResult,
  type LegalMemo,
  type CombinationMetrics,
};
