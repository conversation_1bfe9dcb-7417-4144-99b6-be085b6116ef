import { CombinationStageProcessor } from "./CombinationStageProcessor";
import { AgenticCombinationStageProcessor } from "./AgenticCombinationStageProcessor";
import SystemSettings from "../../../../models/systemSettings";
import { StageProcessor } from "../core/StageProcessor";
import { FlowContext, StageDependencies } from "../../../../types/chat-flow";

// Type definitions
type FlowType = "main" | "noMain" | "referenceFiles";
type ProcessorType = "regular" | "agentic";

interface CombinationProcessorOptions {
  [key: string]: unknown;
}

// Remove custom ProcessorContext interface - use FlowContext directly

type ProcessorClass =
  | typeof CombinationStageProcessor
  | typeof AgenticCombinationStageProcessor;

/**
 * Factory for creating the appropriate combination processor based on flow settings
 */
class CombinationProcessorFactory {
  /**
   * Get the appropriate combination processor for the flow type
   * @param flowType - The type of flow (main, noMain, referenceFiles)
   * @param options - Processor options
   * @returns The appropriate combination processor class
   */
  static async getCombinationProcessor(
    flowType: FlowType,
    _options: CombinationProcessorOptions = {}
  ): Promise<ProcessorClass> {
    try {
      // Get the flow-specific setting for combination processor type
      const settingKey = `flow_${flowType.toLowerCase()}_use_combination_processor`;
      const setting = await SystemSettings.get({ label: settingKey });
      const processorType: ProcessorType =
        (setting?.value as ProcessorType) ?? "regular";

      console.log(
        `[CombinationProcessorFactory] Using ${processorType} processor for ${flowType} flow`
      );

      // Return the appropriate processor based on the setting
      if (processorType === "agentic") {
        return AgenticCombinationStageProcessor;
      } else {
        return CombinationStageProcessor;
      }
    } catch (error) {
      console.error(
        "[CombinationProcessorFactory] Error getting processor type, defaulting to regular:",
        error
      );
      return CombinationStageProcessor;
    }
  }

  /**
   * Create a dynamic processor class that determines the actual processor at runtime
   * This allows the flow configuration to remain static while the processor selection is dynamic
   */
  static createDynamicProcessor(flowType: FlowType): typeof StageProcessor {
    class DynamicCombinationProcessor extends StageProcessor {
      public flowType: FlowType;
      public actualProcessor: StageProcessor | null = null;

      constructor(options: CombinationProcessorOptions = {}) {
        super(options);
        this.flowType = flowType;
        this.actualProcessor = null;
      }

      async initialize(options?: CombinationProcessorOptions): Promise<void> {
        // Get the appropriate processor class
        const ProcessorClass =
          await CombinationProcessorFactory.getCombinationProcessor(
            this.flowType,
            options
          );
        // Create an instance of the actual processor
        this.actualProcessor = new ProcessorClass(options);
      }

      async process(
        context: FlowContext,
        dependencies: StageDependencies
      ): Promise<Partial<FlowContext>> {
        // Initialize the actual processor if not already done
        if (!this.actualProcessor) {
          await this.initialize(this.options);
        }

        // Delegate to the actual processor
        return this.actualProcessor!.process(context, dependencies);
      }

      validateInputs(context: FlowContext): boolean {
        if (!this.actualProcessor) {
          // Use default validation
          return !!(
            context &&
            context.sectionOutputs &&
            Array.isArray(context.sectionOutputs) &&
            (context?.sectionOutputs?.length ?? 0) > 0
          );
        }
        return this.actualProcessor.validateInputs(context);
      }

      shouldSkip(context: FlowContext): boolean {
        if (!this.actualProcessor) {
          return false;
        }
        return this.actualProcessor.shouldSkip(context);
      }
    }
    return DynamicCombinationProcessor;
  }
}

export { CombinationProcessorFactory };
