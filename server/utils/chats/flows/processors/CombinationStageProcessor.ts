import { StageProcessor } from "../core/StageProcessor";
import { Response } from "express";
import { combineSectionOutputs } from "../../helpers/documentProcessing";
import { writeResponseChunk } from "../../../helpers/chat/responses";

import type {
  FlowContext,
  StageDependencies,
} from "../../../../types/chat-flow";

interface CombinationOptions extends Record<string, unknown> {
  includeMetadata?: boolean;
  sendFinalResult?: boolean;
  skipCombination?: boolean;
}

interface CombinationMetrics extends Record<string, unknown> {
  sectionsProcessed: number;
  finalContentLength: number;
  averageSectionLength: number;
  combinedAt: string;
}

interface SectionOutput {
  title?: string;
  content?: string;
}

/**
 * Combination Stage Processor
 *
 * Handles the final combination of section outputs into a complete document:
 * - Combines individual section drafts
 * - Formats the final document structure
 * - Sends the final result to the client
 * - <PERSON>les document finalization logic
 */
export class CombinationStageProcessor extends StageProcessor {
  public options: Required<CombinationOptions>;

  constructor(...args: unknown[]) {
    const options = (args[0] ?? {}) as CombinationOptions;
    super(options);
    this.options = {
      includeMetadata: true,
      sendFinalResult: true,
      skipCombination: false,
      ...options,
    };
  }

  /**
   * Process the combination stage
   */
  async process(
    context: FlowContext,
    dependencies: StageDependencies
  ): Promise<Partial<FlowContext>> {
    const { progressManager, abortChecker, chatId, options } = dependencies;

    this.log("info", "Starting combination stage", {
      sectionOutputsCount: context?.sectionOutputs?.length || 0,
      chatId,
    });

    // Check abort signal
    abortChecker();

    if (!context.sectionOutputs || context.sectionOutputs.length === 0) {
      throw new Error("No section outputs available for combination");
    }

    progressManager.updateStep("Combining & finalizing document...", {
      total: 3,
    });

    try {
      // Step 1: Combine section outputs into final document
      progressManager.sendSubStepProgress(
        1,
        3,
        "Combining section outputs into final document...",
        "Document Assembly",
        -1
      );
      const finalContent = combineSectionOutputs(context.sectionOutputs);

      if (!finalContent || finalContent.trim().length === 0) {
        throw new Error("Failed to generate final document content");
      }
      progressManager.sendSubStepProgress(
        1,
        3,
        "Section outputs combined successfully",
        "Document Assembly",
        100
      );

      abortChecker(); // Check abort after combination

      // Step 2: Prepare final document with metadata if requested
      progressManager.sendSubStepProgress(
        2,
        3,
        "Adding document metadata and formatting...",
        "Metadata Processing",
        -1
      );
      let finalDocument = finalContent;

      if (this.options.includeMetadata) {
        finalDocument = this.addDocumentMetadata(finalContent, context);
      }
      progressManager.sendSubStepProgress(
        2,
        3,
        "Document metadata added",
        "Metadata Processing",
        100
      );

      // Step 3: Finalize document and prepare output
      progressManager.sendSubStepProgress(
        3,
        3,
        "Finalizing document and preparing output...",
        "Document Finalization",
        -1
      );

      // Calculate combination metrics
      const combinationMetrics: CombinationMetrics = {
        sectionsProcessed: context.sectionOutputs.length,
        finalContentLength: finalDocument.length,
        averageSectionLength: this.calculateAverageSectionLength(
          context.sectionOutputs
        ),
        combinedAt: new Date().toISOString(),
      };

      this.log("info", "Document combination completed", combinationMetrics);

      // Send final result to client if enabled
      if (this.options.sendFinalResult) {
        await this.sendFinalResult(finalDocument, chatId, options.response);
      }

      progressManager.sendSubStepProgress(
        3,
        3,
        "Document finalization completed",
        "Document Finalization",
        100
      );

      // Return data to add to context
      return {
        finalContent: finalDocument,
        metrics: {
          ...context.metrics,
          combination:
            combinationMetrics as import("../../../../types/chat-flow").CombinationStageMetrics,
        },
      };
    } catch (error) {
      this.log("error", "Combination stage failed", {
        error: (error as Error).message,
      });
      throw new Error(
        `Document combination failed: ${(error as Error).message}`
      );
    }
  }

  /**
   * Add metadata header to the final document
   */
  private addDocumentMetadata(content: string, context: FlowContext): string {
    const metadata = this.buildDocumentMetadata(context);

    if (!metadata) {
      return content;
    }

    return `${metadata}\n\n${content}`;
  }

  /**
   * Build document metadata section
   */
  private buildDocumentMetadata(context: FlowContext): string {
    const metadataLines: string[] = [];

    // Add generation info
    metadataLines.push("<!-- Document Generation Metadata -->");
    metadataLines.push(`<!-- Generated: ${new Date().toISOString()} -->`);

    if (context?.metrics?.setup?.setupCompletedAt) {
      metadataLines.push(
        `<!-- Processing Started: ${context.metrics.setup.setupCompletedAt} -->`
      );
    }

    // Add document processing info
    if (context?.metrics?.documentProcessing) {
      const docMetrics = context.metrics.documentProcessing;
      metadataLines.push(
        `<!-- Documents Processed: ${docMetrics.processedDocuments}/${docMetrics.totalDocuments} -->`
      );
    }

    // Add section info
    if (context?.metrics?.sectionPlanning) {
      const sectionMetrics = context.metrics.sectionPlanning;
      metadataLines.push(
        `<!-- Sections Generated: ${sectionMetrics.sectionsGenerated} (from ${sectionMetrics.sectionSource}) -->`
      );
    }

    // Add execution time if available
    if (context?.metrics?.totalExecutionTime) {
      metadataLines.push(
        `<!-- Total Execution Time: ${context.metrics.totalExecutionTime}s -->`
      );
    }

    metadataLines.push("<!-- End Metadata -->");

    return metadataLines.join("\n");
  }

  /**
   * Calculate average section length for metrics
   */
  private calculateAverageSectionLength(
    sectionOutputs: SectionOutput[]
  ): number {
    if (!sectionOutputs || sectionOutputs.length === 0) {
      return 0;
    }

    const totalLength = sectionOutputs.reduce((sum, section) => {
      const contentLength = section.content ? section.content.length : 0;
      return sum + contentLength;
    }, 0);

    return Math.round(totalLength / sectionOutputs.length);
  }

  /**
   * Send final result to the client
   */
  private async sendFinalResult(
    finalDocument: string,
    chatId: string,
    response: Response
  ): Promise<void> {
    try {
      if (!response || response.writableEnded) {
        this.log("warn", "Response stream not available for final result");
        return;
      }

      // Send the final document content
      writeResponseChunk(response, {
        uuid: chatId,
        type: "text",
        textResponse: finalDocument,
        sources: [], // Could include document sources here
        close: true,
        error: false,
      });

      this.log("info", "Final result sent to client", {
        contentLength: finalDocument.length,
      });
    } catch (error) {
      this.log("error", "Failed to send final result to client", {
        error: (error as Error).message,
      });
      // Don't throw here - the document was generated successfully
    }
  }

  /**
   * Validate inputs for combination stage
   */
  validateInputs(context: FlowContext): boolean {
    if (!context.sectionOutputs || !Array.isArray(context.sectionOutputs)) {
      this.log("error", "Invalid input: sectionOutputs must be an array");
      return false;
    }

    if (context.sectionOutputs.length === 0) {
      this.log("error", "Invalid input: no section outputs provided");
      return false;
    }

    // Check that section outputs have required properties
    for (const [index, section] of context.sectionOutputs.entries()) {
      if (!section.title && !section.content) {
        this.log("error", "Invalid section output: missing title and content", {
          index,
          section,
        });
        return false;
      }
    }

    return true;
  }

  /**
   * Check if combination stage should be skipped
   */
  shouldSkip(context: FlowContext): boolean {
    // Skip only if no section outputs are available for combination
    const hasSectionOutputs =
      context.sectionOutputs && (context?.sectionOutputs?.length ?? 0) > 0;

    // Only skip if no section outputs are available or explicitly configured to skip
    return !hasSectionOutputs || this.options.skipCombination === true;
  }
}
