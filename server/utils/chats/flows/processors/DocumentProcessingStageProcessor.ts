import { StageProcessor } from "../core/StageProcessor";
import {
  generateDocumentDescriptionIterative,
  generateDocumentRelevanceIterative,
  saveDocumentDescriptions,
} from "../../helpers/documentProcessing";

import type {
  FlowContext,
  StageDependencies,
  ProcessedDocument as FlowProcessedDocument,
  DocumentDescription as FlowDocumentDescription,
} from "../../../../types/chat-flow";

interface DocumentProcessingOptions {
  skipRelevanceCheck?: boolean;
  skipDocumentProcessing?: boolean;
  prompts?: Record<
    string,
    { SYSTEM_PROMPT?: string; USER_PROMPT?: string; PROMPT_TEMPLATE?: string }
  >;
  flowType?: string;
}

// Use DocumentDescription from chat-flow types
type DocumentDescription = FlowDocumentDescription;

// Interface removed - unused
// interface ProcessedDocument extends DocumentForProcessing {
//   Description: string;
//   IsRelevant: boolean;
// }

interface DocumentMetrics {
  totalDocuments: number;
  processedDocuments: number;
  relevantDocuments: number;
  descriptionsFilePath: string;
  [key: string]: unknown;
}

/**
 * Document Processing Stage Processor
 *
 * Handles document description generation and relevance checking:
 * - Generates descriptions for all documents
 * - Checks document relevance to the legal task
 * - Filters out irrelevant documents
 * - Saves document descriptions for later use
 */
export class DocumentProcessingStageProcessor extends StageProcessor {
  declare public options: DocumentProcessingOptions & Record<string, unknown>;

  constructor(...args: unknown[]) {
    const options = (args[0] ?? {}) as DocumentProcessingOptions;
    super(options as Record<string, unknown>);
    this.options = {
      skipRelevanceCheck: false,
      ...options,
    };
  }

  /**
   * Process the document processing stage
   */
  async process(
    context: FlowContext,
    dependencies: StageDependencies
  ): Promise<Partial<FlowContext>> {
    const {
      progressManager,
      llmCoordinator,
      abortChecker,
      legalTask,
      chatId,
      flowType,
    } = dependencies;

    this.log("info", "Starting document processing stage", {
      documentCount: context.documents.length,
      chatId,
    });

    // Check abort signal
    abortChecker();

    if (!context.documents || context.documents.length === 0) {
      throw new Error("No documents available for processing");
    }

    // Get LLM components
    const llmConnector =
      llmCoordinator.getConnector() as unknown as import("../../LLMConnector").LLMConnector;

    const tokenTracker = llmCoordinator.getTokenTracker();
    const temperature = llmCoordinator.getTemperature();

    // Get prompts for this stage
    const prompts =
      this.options.prompts ||
      (
        context as FlowContext & {
          allPrompts?: DocumentProcessingOptions["prompts"];
        }
      ).allPrompts;

    const processedDocuments: FlowProcessedDocument[] = [];
    const docDescriptions: DocumentDescription[] = [];
    const documentMetrics: Record<string, number | string> = {};

    // Update progress for starting document processing
    progressManager.updateStep(
      "Processing documents: Generating descriptions and checking relevance...",
      { total: context.documents.length }
    );

    // Process each document
    for (const [docIndex, document] of context.documents.entries()) {
      abortChecker(); // Check abort before processing each document

      const currentDocIndex = docIndex + 1;
      const docLabel = document.displayName;

      // Send sub-step progress
      progressManager.sendSubStepProgress(
        currentDocIndex,
        context.documents.length,
        `Processing: ${docLabel} - Generating description...`,
        docLabel,
        -1 // Loading state
      );

      try {
        this.log("info", "Processing document", {
          docId: document.id,
          displayName: document.displayName,
          contentLength: document.content.length,
        });

        // Generate document description using iterative processing
        const description = await generateDocumentDescriptionIterative(
          document.displayName,
          document.content,
          legalTask || "",
          llmConnector,
          {
            customSystemPrompt:
              prompts?.CURRENT_DEFAULT_DOCUMENT_SUMMARY?.SYSTEM_PROMPT,
            customUserPromptTemplate:
              prompts?.CURRENT_DEFAULT_DOCUMENT_SUMMARY?.USER_PROMPT,
            temperature,
          }
        );

        // Track tokens for description generation
        const descriptionTokens =
          (
            tokenTracker as {
              getTokenUsageForStage?: (stage: string) => number;
            }
          )?.getTokenUsageForStage?.("description") ||
          (llmConnector as { metrics?: { lastCompletionTokens?: number } })
            ?.metrics?.lastCompletionTokens ||
          0;
        documentMetrics[`desc_tokens_${document.id}`] = descriptionTokens;

        abortChecker(); // Check abort after description generation

        // Check document relevance (unless skipped)
        let isRelevant = true;
        if (!this.options.skipRelevanceCheck) {
          progressManager.sendSubStepProgress(
            currentDocIndex,
            context.documents.length,
            `Processing: ${docLabel} - Checking relevance...`,
            docLabel,
            -1
          );

          isRelevant = await generateDocumentRelevanceIterative(
            document.displayName,
            document.content,
            legalTask || "",
            llmConnector,
            {
              customSystemPrompt:
                prompts?.CURRENT_DEFAULT_DOCUMENT_RELEVANCE?.SYSTEM_PROMPT,
              customUserPromptTemplate:
                prompts?.CURRENT_DEFAULT_DOCUMENT_RELEVANCE?.USER_PROMPT,
              temperature,
              flowType: flowType || "unknown",
            }
          );

          // Track tokens for relevance checking
          const relevanceTokens =
            (
              tokenTracker as {
                getTokenUsageForStage?: (stage: string) => number;
              }
            )?.getTokenUsageForStage?.("relevance") ||
            (llmConnector as { metrics?: { lastCompletionTokens?: number } })
              ?.metrics?.lastCompletionTokens ||
            0;
          documentMetrics[`relevance_tokens_${document.id}`] = relevanceTokens;
        }

        // Add to descriptions list (for section generation)
        docDescriptions.push({
          "Doc Name": document.id,
          DisplayName: document.displayName,
          Description: description,
        });

        // Add to processed documents (only if relevant)
        if (isRelevant) {
          processedDocuments.push({
            id: document.id,
            fileName: document.name || document.fileName,
            displayName: document.displayName,
            content: document.content,
            metadata: {
              ...document.metadata,
              Description: description,
              IsRelevant: true,
            },
          } as unknown as FlowProcessedDocument);
        } else {
          this.log("info", "Document marked as not relevant", {
            docId: document.id,
            displayName: document.displayName,
          });
        }

        // Mark this document sub-step as completed
        progressManager.sendSubStepProgress(
          currentDocIndex,
          context.documents.length,
          `Processed: ${docLabel}`,
          docLabel,
          100 // Complete
        );
      } catch (error) {
        this.log("error", "Failed to process document", {
          docId: document.id,
          error: (error as Error).message,
        });
        documentMetrics[`error_${document.id}`] = (error as Error).message;

        // For now, continue with other documents rather than failing the entire flow
        continue;
      }
    }

    abortChecker(); // Check abort after all documents processed

    if (docDescriptions.length === 0) {
      throw new Error("No documents were successfully processed");
    }

    // Save document descriptions
    const descriptionsFilePath = saveDocumentDescriptions(
      docDescriptions,
      chatId
    );

    const processingMetrics: DocumentMetrics = {
      totalDocuments: context.documents.length,
      processedDocuments: docDescriptions.length,
      relevantDocuments: processedDocuments.length,
      descriptionsFilePath,
      ...documentMetrics,
    };

    this.log("info", "Document processing stage completed", processingMetrics);

    // Return data to add to context
    const result: Partial<FlowContext> = {
      docDescriptions: docDescriptions, // Provide full DocumentDescription objects
      processedDocuments: processedDocuments.map(
        (doc): FlowProcessedDocument => ({
          ...doc,
          displayName: doc.displayName,
          content: doc.content,
          metadata: doc.metadata,
        })
      ),
      metrics: {
        ...context.metrics,
        documentProcessing: processingMetrics,
      },
    };

    return result;
  }

  /**
   * Validate inputs for document processing stage
   */
  validateInputs(context: FlowContext): boolean {
    if (!context.documents || !Array.isArray(context.documents)) {
      this.log("error", "Invalid input: documents must be an array");
      return false;
    }

    if (context.documents.length === 0) {
      this.log("error", "Invalid input: no documents provided");
      return false;
    }

    // Check that documents have required properties
    for (const doc of context.documents) {
      if (!doc.id || !doc.content) {
        this.log("error", "Invalid document: missing id or content", { doc });
        return false;
      }
    }

    return true;
  }

  /**
   * Check if document processing stage should be skipped
   */
  shouldSkip(context: FlowContext): boolean {
    // Skip only if no documents are available for processing
    const hasDocuments =
      context.documents && (context?.documents?.length ?? 0) > 0;

    // Only skip if no documents are available or explicitly configured to skip
    return !hasDocuments || this.options.skipDocumentProcessing === true;
  }
}
