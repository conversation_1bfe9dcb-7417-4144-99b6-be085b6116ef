import { StageProcessor } from "../core/StageProcessor";
import { FlowContext, StageDependencies } from "../../../../types/chat-flow";

class DocumentToSectionMappingProcessor extends StageProcessor {
  constructor(...args: unknown[]) {
    const options = (args[0] ?? {}) as Record<string, unknown>;
    super(options);
    this.options = {
      ...options,
    };
  }
  async process(
    _context: FlowContext,
    _dependencies: StageDependencies
  ): Promise<Partial<FlowContext>> {
    throw new Error(
      "DocumentToSectionMappingProcessor.process() not implemented"
    );
  }
}

export { DocumentToSectionMappingProcessor };
