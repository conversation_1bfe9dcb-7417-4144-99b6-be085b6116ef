import { StageProcessor } from "../core/StageProcessor";
import {
  FlowContext,
  StageDependencies,
  Section,
  LegalIssue,
  ProgressManager,
  LLMCoordinator,
  ContextWindowManager,
  TokenTracker,
  WorkspaceData,
  FlowPrompts,
  FlowMetrics,
} from "../../../../types/chat-flow";
import {
  processIterativeSectionDraftingList,
  DocumentSection,
  SectionDraftingOptions,
  ProcessedDocument,
} from "../../helpers/documentProcessing";
import * as fs from "fs";
import * as path from "path";

// Type definitions
type SectionWithMixedFields = SectionForDrafting & DocumentSection;
interface TokenUsage {
  totalTokens: number;
  iterations: number;
  isIterative?: boolean;
}

interface SectionForDrafting {
  title?: string;
  Description?: string;
  index_number?: number;
  draftedContent?: string;
  tokenUsage?: TokenUsage;
  error?: string;
  relevantDocumentNames?: string[];
  identifiedLegalIssues?: LegalIssue[];
  relevant_documents?: string[];
  legal_issues_to_address?: string[] | LegalIssue[];
}

// Removed unused interface SectionOutput

interface DraftingMetrics {
  sectionsProcessed: number;
  successfulSections: number;
  failedSections: number;
  totalTokens: number;
  totalContentLength: number;
  averageContentLength: number;
  iterativeSectionsCount: number;
  averageIterations: number;
  totalProcessingTimeSeconds: number;
  averageTimePerSection: number;
  tokensPerSecond: number;
  completedAt: string;
}

// Using SectionDraftingOptions from documentProcessing.ts instead of local DraftingOptions

interface ProgressCallbacks {
  onSectionProgress: (
    sectionIndex: number,
    sectionTitle: string,
    status: string
  ) => void;
  onTokenUsage: (sectionIndex: number, tokenUsage: TokenUsage) => void;
  getMetrics: () => Record<string, unknown>;
}

interface DraftingDependencies {
  progressManager: ProgressManager;
  llmCoordinator: LLMCoordinator;
  contextWindowManager?: ContextWindowManager;
  tokenTracker?: TokenTracker;
  abortChecker: () => void;
  legalTask: string;
  chatId: string;
  workspace: WorkspaceData;
  flowType?: string;
}

interface DraftingContext {
  sectionList?: SectionForDrafting[];
  processedDocuments?: ProcessedDocument[];
  allPrompts?: FlowPrompts;
  mainDocumentName?: string;
  metrics?: FlowMetrics;
}

export interface IterativeDraftingOptions {
  temperature?: number;
  saveIntermediateResults?: boolean;
  enableProgressCallbacks?: boolean;
  flowType?: string;
  skipSectionDrafting?: boolean;
}

// Removed unused interface DraftingResult

/**
 * Iterative Section Drafting Stage Processor
 *
 * Handles sophisticated section drafting with iterative processing:
 * - Uses existing processIterativeSectionDraftingList helper
 * - Implements retry logic with keepalive events
 * - Iterative context window management for large documents
 * - Progress tracking with detailed sub-step reporting
 * - Token usage tracking and metrics
 * - Error recovery and intermediate result saving
 *
 * This replaces the mock implementations in flow configurations.
 * Based on legacy mainDoc.js Step 6 (lines 1498-1613) and noMainDoc.js Step 6.
 */
class IterativeSectionDraftingProcessor extends StageProcessor {
  public options: IterativeDraftingOptions & Record<string, unknown>;

  constructor(...args: unknown[]) {
    const options = (args[0] ?? {}) as SectionDraftingOptions;
    super(options as unknown as Record<string, unknown>);
    this.options = {
      ...options,
    };
  }

  /**
   * Convert Section[] to DocumentSection[] for compatibility with processIterativeSectionDraftingList
   */
  private convertSectionListToDocumentSections(
    sections: Section[]
  ): DocumentSection[] {
    return sections.map((section) => ({
      index_number: section.sectionNumber,
      title: section.title,
      description: section.title, // Use title as description since Section doesn't have a description field
      relevant_documents: section.relevantDocumentNames,
      legal_issues_to_address:
        section.identifiedLegalIssues?.map((issue) => issue.Issue) || [],
      // Legacy fields for backward compatibility
      relevantDocumentNames: section.relevantDocumentNames,
    }));
  }

  /**
   * Process iterative section drafting for all sections
   * @param context - Shared context object
   * @param dependencies - Injected dependencies
   * @returns Updated context with drafted sections
   */
  async process(
    context: FlowContext,
    dependencies: StageDependencies
  ): Promise<Partial<FlowContext>> {
    const {
      progressManager,
      llmCoordinator,
      contextWindowManager,
      tokenTracker,
      abortChecker,
      legalTask: _legalTask,
      chatId,
      workspace: _workspace,
    } = dependencies;

    this.log("info", "Starting iterative section drafting stage", {
      sectionsToProcess: context?.sectionList?.length || 0,
      flowType: this.options.flowType,
      chatId,
      tokenTrackerAvailable: !!tokenTracker,
      contextManagerAvailable: !!contextWindowManager,
    });

    abortChecker();

    if (!context.sectionList || context.sectionList.length === 0) {
      throw new Error("No sections available for drafting");
    }

    // Prepare progress tracking
    progressManager.updateStep(
      "Drafting individual sections with iterative processing...",
      { total: context.sectionList.length }
    );

    // Prepare options for the iterative drafting helper
    const draftingOptions = this.prepareDraftingOptions(
      context as unknown as DraftingContext,
      dependencies as unknown as DraftingDependencies
    );

    // Prepare progress callbacks
    const callbacks = this.prepareProgressCallbacks(
      context.sectionList.length,
      progressManager,
      abortChecker,
      tokenTracker
    );

    let draftedSections: SectionWithMixedFields[];
    const startTime = Date.now();

    try {
      // Create a compatibility object that provides the LLMConnector interface
      const contextWindowManagerCompat = contextWindowManager || {
        LLMConnector: llmCoordinator.getConnector(),
      };

      // Convert Section[] to DocumentSection[] for compatibility
      const documentSections = this.convertSectionListToDocumentSections(
        context.sectionList
      );

      draftedSections = await processIterativeSectionDraftingList(
        documentSections,
        draftingOptions,
        contextWindowManagerCompat as ContextWindowManager,
        tokenTracker,
        callbacks
      );
    } catch (error) {
      // Handle user abort gracefully
      if (
        (error instanceof Error &&
          error.message === "Process aborted by user") ||
        (error instanceof Error &&
          (error as Error & { isAbort?: boolean }).isAbort)
      ) {
        this.log("info", "Section drafting aborted by user");
        throw error;
      }

      this.log("error", "Section drafting failed", {
        error: error instanceof Error ? error.message : String(error),
        sectionsProcessed: context.sectionList.length,
      });
      throw error;
    }

    const endTime = Date.now();
    const totalTime = Math.round((endTime - startTime) / 1000);

    // Save intermediate results
    if (this.options.saveIntermediateResults) {
      await this.saveIntermediateResults(
        draftedSections as SectionForDrafting[],
        chatId
      );
    }

    // Calculate metrics
    const draftingMetrics = this.calculateDraftingMetrics(
      draftedSections as SectionForDrafting[],
      totalTime
    );

    this.log(
      "info",
      "Iterative section drafting completed",
      draftingMetrics as unknown as Record<string, unknown>
    );

    // Convert draftedSections to base Section format for FlowContext
    const convertedSectionList = draftedSections.map(
      (section: SectionWithMixedFields) => ({
        sectionNumber: section.index_number || 0,
        title: section.title || "",
        relevantDocumentNames:
          section.relevant_documents || section.relevantDocumentNames || [],
        identifiedLegalIssues: ((): LegalIssue[] => {
          const issues =
            section.legal_issues_to_address ||
            section.identifiedLegalIssues ||
            [];
          // Convert string array to LegalIssue array if needed
          if (
            Array.isArray(issues) &&
            issues.length > 0 &&
            typeof issues[0] === "string"
          ) {
            return (issues as string[]).map((issue: string) => ({
              Issue: issue,
              WORKSPACE_SLUG_FOR_LEGALDATA: _workspace.slug || "",
            }));
          }
          return issues as LegalIssue[];
        })(),
      })
    );

    return {
      sectionList: convertedSectionList,
      sectionOutputs: draftedSections.map((section) => ({
        title: section.title || "",
        content: section.draftedContent || "",
        sectionNumber: section.index_number || 0,
      })),
      metrics: {
        ...context.metrics,
        sectionDrafting: draftingMetrics,
      },
    };
  }

  /**
   * Prepare options for the iterative drafting helper
   */
  private prepareDraftingOptions(
    context: DraftingContext,
    dependencies: DraftingDependencies
  ): SectionDraftingOptions {
    const { legalTask, workspace, chatId, flowType } = dependencies;

    // Determine workspace path
    const workspacePath =
      (typeof workspace?.path === "string" ? workspace.path : null) ||
      path.join(__dirname, "../../../storage/uploads");

    // Determine document builder path
    const documentBuilderBasePath = path.join(
      __dirname,
      "../../../../storage/document-builder"
    );

    return {
      legalTask,
      processedDocuments: context.processedDocuments || [],
      workspacePath,
      mainDocNameInitial:
        this.options.flowType === "main"
          ? context.mainDocumentName || undefined
          : undefined,
      temperature: this.options.temperature || 0.7,
      AllPrompts: (context.allPrompts || {}) as Record<
        string,
        { PROMPT_TEMPLATE?: string; [key: string]: unknown }
      >, // Type cast for compatibility
      chatId,
      documentBuilderBasePath,
      flowType: flowType || "unknown",
    };
  }

  /**
   * Prepare progress callbacks for the iterative drafting helper
   */
  private prepareProgressCallbacks(
    totalSections: number,
    progressManager: ProgressManager,
    abortChecker: () => void,
    tokenTracker?: TokenTracker
  ): ProgressCallbacks {
    const metrics: Record<string, unknown> = {};

    return {
      // Progress callback for UI updates
      onSectionProgress: (
        sectionIndex: number,
        sectionTitle: string,
        status: string
      ) => {
        // Check for abort during section processing
        try {
          abortChecker();
        } catch {
          console.error("Failed to parse JSON response from LLM");
          return;
        }

        const isComplete = status === "Complete";
        const isDrafting = status.includes("Drafting");
        const isError = status === "Error";

        // Format message to match expected patterns
        let message: string;
        if (isComplete) {
          message = `Drafted Section ${sectionIndex + 1}: ${sectionTitle}`;
        } else if (isDrafting) {
          message = `Drafting Section ${sectionIndex + 1}: ${sectionTitle}...`;
        } else if (isError) {
          message = `Error drafting Section ${sectionIndex + 1}: ${sectionTitle}`;
        } else {
          message = `Drafting Section ${sectionIndex + 1}: ${sectionTitle}...`;
        }

        progressManager.sendSubStepProgress(
          sectionIndex + 1,
          totalSections,
          message,
          sectionTitle,
          isComplete ? 100 : isError ? -2 : -1
        );
      },

      // Token usage callback for metrics
      onTokenUsage: (sectionIndex: number, tokenUsage: TokenUsage) => {
        const sectionNumber = sectionIndex + 1;
        metrics[`draft_sec_${sectionNumber}_tokens`] = tokenUsage.totalTokens;
        metrics[`draft_sec_${sectionNumber}_iterations`] =
          tokenUsage.iterations;

        if (tokenUsage.isIterative) {
          metrics[`draft_sec_${sectionNumber}_iterative`] = true;
        }

        // Update token tracker if available
        if (tokenTracker) {
          // Create a stage tracker for this specific section if not already created
          const stageTracker = (
            tokenTracker as unknown as {
              startStage: (name: string) => {
                addTokens: (tokens: number, type: string) => void;
                finish: () => void;
              };
            }
          ).startStage(`section-${sectionNumber}-drafting`);
          stageTracker.addTokens(tokenUsage.totalTokens, "section-drafting");
          stageTracker.finish();
        }
      },

      // Store metrics for later retrieval
      getMetrics: () => metrics,
    };
  }

  /**
   * Save intermediate results for debugging and recovery
   */
  private async saveIntermediateResults(
    draftedSections: SectionForDrafting[],
    chatId: string
  ): Promise<void> {
    try {
      const documentBuilderPath = path.join(
        __dirname,
        "../../../../storage/document-builder"
      );

      if (!fs.existsSync(documentBuilderPath)) {
        fs.mkdirSync(documentBuilderPath, { recursive: true });
      }

      const filePath = path.join(
        documentBuilderPath,
        `section-list-with-drafts-${chatId}.json`
      );

      fs.writeFileSync(
        filePath,
        JSON.stringify(draftedSections, null, 2),
        "utf8"
      );

      this.log("info", "Section drafts saved", { filePath });
    } catch (error) {
      this.log("error", "Failed to save section drafts", {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Calculate comprehensive metrics for section drafting
   */
  private calculateDraftingMetrics(
    draftedSections: SectionForDrafting[],
    totalTimeSeconds: number
  ): DraftingMetrics {
    const successfulSections = draftedSections.filter(
      (section) => section.draftedContent && !section.error
    );

    const totalTokens = draftedSections.reduce(
      (sum, section) => sum + (section?.tokenUsage?.totalTokens || 0),
      0
    );

    const totalContentLength = successfulSections.reduce(
      (sum, section) => sum + (section?.draftedContent?.length || 0),
      0
    );

    const iterativeSections = draftedSections.filter(
      (section) => section?.tokenUsage?.isIterative
    );

    const averageIterations =
      draftedSections.reduce(
        (sum, section) => sum + (section?.tokenUsage?.iterations || 0),
        0
      ) / draftedSections.length;

    return {
      sectionsProcessed: draftedSections.length,
      successfulSections: successfulSections.length,
      failedSections: draftedSections.length - successfulSections.length,
      totalTokens,
      totalContentLength,
      averageContentLength:
        totalContentLength / (successfulSections.length || 1),
      iterativeSectionsCount: iterativeSections.length,
      averageIterations: Math.round(averageIterations * 100) / 100,
      totalProcessingTimeSeconds: totalTimeSeconds,
      averageTimePerSection: totalTimeSeconds / draftedSections.length,
      tokensPerSecond: totalTokens / (totalTimeSeconds || 1),
      completedAt: new Date().toISOString(),
    };
  }

  /**
   * Validation methods
   */
  validateInputs(context: FlowContext): boolean {
    const draftingContext = context as unknown as DraftingContext;
    if (
      !draftingContext.sectionList ||
      !Array.isArray(draftingContext.sectionList)
    ) {
      this.log("error", "Invalid input: sectionList must be an array");
      return false;
    }

    if (draftingContext.sectionList.length === 0) {
      this.log("warn", "Empty section list provided");
      return false;
    }

    // Check if sections have minimum required properties
    const invalidSections = draftingContext.sectionList.filter(
      (section) => !section.title && !section.Description
    );

    if ((invalidSections?.length ?? 0) > 0) {
      this.log("error", "Some sections missing required title or description", {
        invalidCount: invalidSections.length,
      });
      return false;
    }

    return true;
  }

  shouldSkip(context: FlowContext): boolean {
    const draftingContext = context as unknown as DraftingContext;
    // Skip only if no sections are available for drafting
    const hasSections =
      draftingContext.sectionList &&
      (draftingContext?.sectionList?.length ?? 0) > 0;

    // Only skip if no sections are available or explicitly configured to skip
    return !hasSections || this.options.skipSectionDrafting === true;
  }
}

export { IterativeSectionDraftingProcessor };
