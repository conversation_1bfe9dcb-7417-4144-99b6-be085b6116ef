import { StageProcessor } from "../core/StageProcessor";
import { fillTemplate } from "../../helpers/documentProcessing";
import { parseLLMJsonResponse } from "../../helpers/llmResponseParser";
import * as fs from "fs";
import * as path from "path";
import {
  FlowContext,
  StageDependencies,
  LLMCoordinator,
  FlowPrompts,
} from "../../../../types/chat-flow";
import { LLMConnector } from "../../LLMConnector";
import { Workspace } from "../../../../models/workspace";

interface LegalIssueIdentificationOptions {
  temperature?: number;
  enableWorkspaceRouting?: boolean;
  saveIntermediateResults?: boolean;
  skipIssueIdentification?: boolean;
  [key: string]: unknown;
}

interface LegalIssue {
  Issue: string;
  WORKSPACE_SLUG_FOR_LEGALDATA?: string | null;
}

interface SectionWithIssues {
  index_number?: number;
  title?: string;
  Description?: string;
  relevantDocumentNames?: string[];
  identifiedLegalIssues: (LegalIssue | string)[];
  error?: string;
  [key: string]: unknown;
}

interface ParseResponse {
  success: boolean;
  data?: LegalIssue[];
  error?: string;
  attemptsMade?: number;
}

interface ProcessingMetrics {
  [key: string]: number | string | boolean;
}

interface IssueIdentificationMetrics {
  sectionsProcessed: number;
  totalSections: number;
  totalUniqueIssues: number;
  successfulSections: number;
  processingMetrics: ProcessingMetrics;
  completedAt: string;
}

/**
 * Legal Issue Identification Stage Processor
 *
 * Analyzes sections to identify specific legal issues that require memo generation.
 * This replaces the mock implementation that was inline in flow configurations.
 *
 * Features:
 * - LLM-based analysis of each section
 * - JSON parsing with error handling
 * - Workspace routing suggestions
 * - Progress tracking with sub-steps
 * - Robust error recovery
 *
 * Based on legacy mainDoc.js Step 4 (lines 901-1040) and noMainDoc.js Step 4.
 */
class LegalIssueIdentificationProcessor extends StageProcessor {
  public options: Required<LegalIssueIdentificationOptions>;

  constructor(...args: unknown[]) {
    const options = (args[0] ?? {}) as LegalIssueIdentificationOptions;
    super(options);
    this.options = {
      temperature: 0.3, // Lower temperature for more consistent analysis
      enableWorkspaceRouting: true,
      saveIntermediateResults: true,
      skipIssueIdentification: false,
      ...options,
    };
  }

  /**
   * Process legal issue identification for all sections
   */
  async process(
    context: FlowContext,
    dependencies: StageDependencies
  ): Promise<Partial<FlowContext>> {
    const {
      progressManager,
      llmCoordinator,
      abortChecker,
      legalTask,
      chatId,
      // prompts, // TODO: Fix types - this is not in FlowDependencies yet
      flowType,
    } = dependencies;

    // Get prompts from options or processor options
    const prompts =
      (dependencies.options as { prompts?: FlowPrompts })?.prompts ||
      (this.options.prompts as FlowPrompts);

    this.log("info", "Starting legal issue identification stage", {
      sectionsToAnalyze: context?.sectionList?.length || 0,
      chatId,
    });

    abortChecker();

    if (!context.sectionList || context.sectionList.length === 0) {
      throw new Error("No sections available for legal issue identification");
    }

    progressManager.updateStep("Identifying legal issues for each section...", {
      total: context.sectionList.length,
    });

    // Prepare workspace routing information if enabled
    const workspacePromptAddition = this.options.enableWorkspaceRouting
      ? await this.prepareWorkspaceRoutingPrompt()
      : "";

    // Process each section individually
    const sectionsWithIssues: SectionWithIssues[] = [];
    const processingMetrics: ProcessingMetrics = {};

    for (let i = 0; i < context.sectionList.length; i++) {
      abortChecker();

      const section = context.sectionList[i];
      const sectionNumber = Number(section.sectionNumber) || i + 1;
      const sectionTitle = section.title || `Section ${sectionNumber}`;

      // Send progress update
      progressManager.sendSubStepProgress(
        i + 1,
        context.sectionList.length,
        `Identifying issues for Section ${sectionNumber}: ${sectionTitle}...`,
        sectionTitle as string,
        -1 // In progress
      );

      try {
        const sectionWithIssues = await this.analyzeSectionForLegalIssues(
          section,
          sectionNumber,
          sectionTitle,
          legalTask,
          workspacePromptAddition,
          llmCoordinator,
          prompts,
          processingMetrics,
          flowType
        );

        sectionsWithIssues.push(sectionWithIssues);

        // Send completion progress
        progressManager.sendSubStepProgress(
          i + 1,
          context.sectionList.length,
          `Identified issues for Section ${sectionNumber}: ${sectionTitle}`,
          sectionTitle as string,
          sectionWithIssues.error ? -2 : 100 // Error state or complete
        );
      } catch (error) {
        this.log("error", `Failed to analyze section ${sectionNumber}`, {
          sectionTitle,
          error: error instanceof Error ? error.message : String(error),
        });

        // Add section with error state
        sectionsWithIssues.push({
          ...section,
          identifiedLegalIssues: [
            `Error analyzing issues: ${error instanceof Error ? error.message : String(error)}`,
          ],
          error: `Failed to identify legal issues: ${error instanceof Error ? error.message : String(error)}`,
        });

        progressManager.sendSubStepProgress(
          i + 1,
          context.sectionList.length,
          `Error - Failed to analyze Section ${sectionNumber}: ${sectionTitle}`,
          sectionTitle as string,
          -2 // Error state
        );
      }
    }

    // Save intermediate results
    if (this.options.saveIntermediateResults) {
      await this.saveIntermediateResults(sectionsWithIssues, chatId);
    }

    // Collect metrics and unique issues
    const allLegalIssues = this.collectAllUniqueIssues(sectionsWithIssues);

    this.log("info", "Legal issue identification completed", {
      sectionsProcessed: sectionsWithIssues.length,
      totalUniqueIssues: allLegalIssues.length,
      successfulSections: sectionsWithIssues.filter((s) => !s.error).length,
    });

    return {
      sectionList: sectionsWithIssues as unknown as typeof context.sectionList,
      legalIssues: allLegalIssues as unknown as typeof context.legalIssues,
      identifiedIssues: allLegalIssues, // Add this for test compatibility
      metrics: {
        ...context.metrics,
        issueIdentification: {
          sectionsProcessed: sectionsWithIssues.length,
          totalSections: context.sectionList.length, // Add totalSections for test compatibility
          totalUniqueIssues: allLegalIssues.length,
          successfulSections: sectionsWithIssues.filter((s) => !s.error).length,
          processingMetrics,
          completedAt: new Date().toISOString(),
        } as IssueIdentificationMetrics,
      },
    };
  }

  /**
   * Analyze a single section for legal issues
   * Based on legacy mainDoc.js lines 917-995
   */
  private async analyzeSectionForLegalIssues(
    section: Record<string, unknown>,
    sectionNumber: number,
    sectionTitle: string,
    legalTask: string,
    workspacePromptAddition: string,
    llmCoordinator: LLMCoordinator,
    prompts: FlowPrompts | undefined,
    processingMetrics: ProcessingMetrics,
    flowType: string
  ): Promise<SectionWithIssues> {
    const relevantDocNamesString =
      (
        ((section as Record<string, unknown>)
          .relevantDocumentNames as string[]) || []
      ).join(", ") || "(none specified)";

    // Prepare prompts
    const userPromptBase = fillTemplate(
      prompts?.CURRENT_DEFAULT_SECTION_LEGAL_ISSUES?.USER_PROMPT ||
        'For section {{sectionNumber}} titled "{{title}}" of the legal task "{{task}}", and considering the relevant documents: {{docs}}, list the specific legal topics or data points for which neutral background information should be fetched from a Legal-QA database.',
      {
        sectionNumber: sectionNumber.toString(),
        title: sectionTitle,
        task: legalTask,
        docs: relevantDocNamesString,
        flowType: flowType || "unknown",
      }
    );

    const userPrompt = userPromptBase + workspacePromptAddition;
    const systemPrompt =
      prompts?.CURRENT_DEFAULT_SECTION_LEGAL_ISSUES?.SYSTEM_PROMPT ||
      'You are an expert legal analyst. Identify specific legal topics for which factual information should be retrieved. Return your answer as valid JSON array with objects containing "Issue" and "WORKSPACE_SLUG_FOR_LEGALDATA" fields.';

    this.log("info", `Requesting legal issues for Section ${sectionNumber}`, {
      sectionTitle,
      promptLength: userPrompt.length,
    });

    // Make LLM request
    const compressedMessages = await llmCoordinator.compressMessages({
      systemPrompt,
      userPrompt,
    });

    const issuesResult = await llmCoordinator.getChatCompletion(
      compressedMessages,
      { temperature: this.options.temperature }
    );

    // Track token usage - check multiple sources for token count
    let tokenUsage = 0;

    // Try different ways to get token count
    const coordinatorMetrics = llmCoordinator.getMetrics() as Record<
      string,
      unknown
    >;

    if (
      coordinatorMetrics &&
      typeof coordinatorMetrics === "object" &&
      coordinatorMetrics !== null &&
      "connector" in coordinatorMetrics &&
      coordinatorMetrics.connector &&
      typeof coordinatorMetrics.connector === "object" &&
      coordinatorMetrics.connector !== null &&
      "lastCompletionTokens" in coordinatorMetrics.connector
    ) {
      tokenUsage =
        (coordinatorMetrics.connector as { lastCompletionTokens?: number })
          .lastCompletionTokens ?? 0;
    } else if (issuesResult?.metrics?.total_tokens) {
      tokenUsage = issuesResult.metrics.total_tokens;
    } else if (issuesResult?.metrics?.completion_tokens) {
      tokenUsage = issuesResult.metrics.completion_tokens;
    } else if (issuesResult.textResponse) {
      // Fallback: estimate tokens from response length
      tokenUsage = Math.ceil(issuesResult.textResponse.length / 4);
    }

    processingMetrics[`legal_issues_sec_${sectionNumber}_tokens`] = tokenUsage;

    this.log("info", `Received legal issues for Section ${sectionNumber}`, {
      responseLength: issuesResult?.textResponse?.length || 0,
      tokens: tokenUsage,
      coordinatorMetrics: coordinatorMetrics?.connector,
      resultMetrics: issuesResult.metrics,
    });

    // Parse the response using the enhanced parser
    const identifiedLegalIssues = await this.parseIssuesResponse(
      issuesResult.textResponse,
      sectionNumber,
      sectionTitle,
      llmCoordinator
    );

    return {
      ...section,
      identifiedLegalIssues,
    };
  }

  /**
   * Parse LLM response to extract legal issues using the enhanced LLM response parser
   * Based on legacy mainDoc.js lines 943-995 but now using the comprehensive parser
   */
  private async parseIssuesResponse(
    responseText: string,
    sectionNumber: number,
    sectionTitle: string,
    llmCoordinator: LLMCoordinator
  ): Promise<(LegalIssue | string)[]> {
    try {
      // Use the comprehensive LLM response parser with validation and optional LLM fix
      const parseResult = await parseLLMJsonResponse(responseText, {
        maxAttempts: 3,
        enableLlmFix: true,
        llmConnector: llmCoordinator.getConnector() as LLMConnector,
        validator: (data: unknown) => Array.isArray(data),
      });

      if (parseResult.success) {
        const parsedIssues = parseResult.data as Record<string, unknown>[];

        this.log(
          "info",
          `Successfully parsed legal issues for Section ${sectionNumber} with ${parseResult.attemptsMade} attempts`,
          {
            sectionTitle,
            issuesCount: parsedIssues?.length || 0,
          }
        );

        // Process parsed issues based on flow type
        return parsedIssues.map(
          (issueObj: Record<string, unknown>): LegalIssue | string => {
            if (typeof issueObj === "object" && issueObj !== null) {
              return {
                Issue: String(issueObj?.Issue ?? issueObj),
                WORKSPACE_SLUG_FOR_LEGALDATA: this.sanitizeSlug(
                  issueObj?.WORKSPACE_SLUG_FOR_LEGALDATA
                ),
              };
            }
            return typeof issueObj === "string" ? issueObj : String(issueObj);
          }
        );
      } else {
        this.log(
          "error",
          `Failed to parse legal issues JSON for Section ${sectionNumber}`,
          {
            sectionTitle,
            error: parseResult.error,
            rawResponse: responseText?.substring(0, 500) || "no response",
            attemptsMade: parseResult.attemptsMade,
          }
        );

        return [`Error parsing issues: ${parseResult.error}`];
      }
    } catch (err) {
      this.log(
        "error",
        `Unexpected error during legal issues parsing for Section ${sectionNumber}`,
        {
          sectionTitle,
          error: err instanceof Error ? err.message : String(err),
          rawResponse: responseText?.substring(0, 500) || "no response",
        }
      );

      return [
        `Error parsing issues: ${err instanceof Error ? err.message : String(err)}`,
      ];
    }
  }

  /**
   * Prepare workspace routing prompt addition
   * Based on legacy mainDoc.js lines 861-895
   */
  private async prepareWorkspaceRoutingPrompt(): Promise<string> {
    try {
      const lqaWorkspaces = await Workspace.where({ type: "legal-qa" });

      if (!lqaWorkspaces || lqaWorkspaces.length === 0) {
        this.log("info", "No Legal-QA workspaces found for routing");
        return "";
      }

      const workspaceList = lqaWorkspaces
        .filter((ws: Record<string, unknown>) => ws?.name && ws?.slug)
        .map((ws: Record<string, unknown>) => `- ${ws.name} (slug: ${ws.slug})`)
        .join("\n");

      if (workspaceList.length === 0) {
        return "";
      }

      return `\n\nAvailable Legal-QA workspace slugs:\n${workspaceList}\n\nFor each issue, suggest the most relevant workspace slug using the WORKSPACE_SLUG_FOR_LEGALDATA field.`;
    } catch (error) {
      this.log("warn", "Failed to prepare workspace routing prompt", {
        error: (error as Error).message,
      });
      return "";
    }
  }

  /**
   * Save intermediate results for debugging
   */
  private async saveIntermediateResults(
    sectionsWithIssues: SectionWithIssues[],
    chatId: string
  ): Promise<void> {
    try {
      const documentBuilderPath = path.join(
        __dirname,
        "../../../../storage/document-builder"
      );

      if (!fs.existsSync(documentBuilderPath)) {
        fs.mkdirSync(documentBuilderPath, { recursive: true });
      }

      const filePath = path.join(
        documentBuilderPath,
        `section-list-with-issues-${chatId}.json`
      );

      fs.writeFileSync(
        filePath,
        JSON.stringify(sectionsWithIssues, null, 2),
        "utf8"
      );

      this.log("info", "Section list with issues saved", { filePath });
    } catch (error) {
      this.log("error", "Failed to save intermediate results", {
        error: (error as Error).message,
      });
    }
  }

  /**
   * Collect all unique legal issues from sections
   */
  private collectAllUniqueIssues(
    sectionsWithIssues: SectionWithIssues[]
  ): string[] {
    const allIssues: string[] = [];
    const seenIssues = new Set<string>();

    sectionsWithIssues.forEach((section) => {
      if (
        section.identifiedLegalIssues &&
        Array.isArray(section.identifiedLegalIssues)
      ) {
        section.identifiedLegalIssues.forEach((issueObj) => {
          const issueText =
            typeof issueObj === "object"
              ? (issueObj as LegalIssue)?.Issue
              : issueObj;
          if (
            issueText &&
            typeof issueText === "string" &&
            !seenIssues.has(issueText)
          ) {
            seenIssues.add(issueText);
            allIssues.push(issueText);
          }
        });
      }
    });

    return allIssues;
  }

  /**
   * Utility: sanitize potential slug strings from LLM
   */
  private sanitizeSlug(slugStr: unknown): string | null {
    if (!slugStr || typeof slugStr !== "string") return null;
    let cleaned = slugStr.trim().toLowerCase();
    if (["none", "null", "undefined"].includes(cleaned)) return null;
    cleaned = cleaned.replace(/\s+/g, "-").replace(/[^a-z0-9-_]/g, "");
    return (cleaned?.length ?? 0) > 0 ? cleaned : null;
  }

  /**
   * Validate inputs for legal issue identification stage
   */
  validateInputs(context: FlowContext): boolean {
    if (!context.sectionList || !Array.isArray(context.sectionList)) {
      this.log("error", "Invalid input: sectionList must be an array");
      return false;
    }

    if (context.sectionList.length === 0) {
      this.log("warn", "Empty section list provided");
      return false;
    }

    return true;
  }

  /**
   * Check if legal issue identification should be skipped
   */
  shouldSkip(_context: FlowContext): boolean {
    // Only skip if explicitly configured to skip issue identification
    // Don't skip based on existing issues since each new message might require fresh analysis
    return this.options.skipIssueIdentification === true;
  }
}

export {
  LegalIssueIdentificationProcessor,
  type LegalIssueIdentificationOptions,
  type LegalIssue,
  type SectionWithIssues,
  type ParseResponse,
  type ProcessingMetrics,
  type IssueIdentificationMetrics,
};
