import { StageProcessor } from "../core/StageProcessor";
import {
  generateLegalMemo,
  type LLMConnector as LegalMemoLLMConnector,
} from "../../../helpers/legalMemo";
import { Workspace } from "../../../../models/workspace";
import { fillTemplate } from "../../helpers/documentProcessing";
import * as fs from "fs";
import * as path from "path";
import {
  FlowContext,
  StageDependencies,
  Section,
  LegalIssue,
  ProgressManager,
} from "../../../../types/chat-flow";
import type { Workspace as WorkspaceType } from "../../../../types/models";

interface LegalMemoOptions {
  maxRetries?: number;
  retryDelay?: number;
  keepaliveInterval?: number;
  enableWorkspaceRouting?: boolean;
  skipMemoGeneration?: boolean;
  [key: string]: number | boolean | string | undefined;
}

interface LegalIssueDetails {
  issueObject: {
    Issue: string;
    WORKSPACE_SLUG_FOR_LEGALDATA?: string | null;
  };
  sectionIndex: number;
  relevantDocumentNames: string[];
}

interface GeneratedMemo {
  fileName: string;
  filePath: string;
  content: string;
  issueText: string;
  workspaceSlug: string;
  tokenCount?: number;
  relevantSectionIndices: number[];
  error?: string;
  [key: string]: unknown;
}

interface MemoIndexEntry {
  issueText: string;
  memoFileName: string | null;
  memoFilePath: string | null;
  relevantSectionIndices: number[];
  workspaceSlug: string | null;
  error: string | null;
}

interface SectionWithMemos extends Section {
  relevantMemos?: MemoReference[];
}

interface MemoReference {
  issue: string;
  memoFileName: string;
  memoFilePath: string;
  generated: boolean;
  targetWorkspaceSlug: string;
}

interface MemoGenerationResult {
  memo: string;
  tokenCount?: number;
}

/**
 * Legal Memo Generation Stage Processor
 *
 * Handles legal memo generation for identified issues:
 * - Fetches Legal-QA workspaces
 * - Routes legal issues to appropriate workspaces
 * - Generates detailed legal memos using LLM
 * - Implements retry logic and error handling
 * - Saves memo files and updates section references
 *
 * This is a critical component that was completely missing from the modular system.
 * Implementation based on legacy mainDoc.js Step 5 (lines 830-1498) and noMainDoc.js Step 5.
 */
class LegalMemoProcessor extends StageProcessor {
  public options: Required<LegalMemoOptions>;

  constructor(...args: unknown[]) {
    const options = (args[0] ?? {}) as LegalMemoOptions;
    super(options);
    this.options = {
      maxRetries: 3,
      retryDelay: 1000,
      keepaliveInterval: 5000,
      enableWorkspaceRouting: true,
      skipMemoGeneration: false,
      ...options,
    };
  }

  /**
   * Process the legal memo generation stage
   */
  async process(
    context: FlowContext,
    dependencies: StageDependencies
  ): Promise<Partial<FlowContext>> {
    const { progressManager, tokenTracker, chatId } = dependencies;

    this.log("info", "Starting legal memo generation stage", {
      chatId,
      tokenTrackerAvailable: !!tokenTracker,
    });

    try {
      // Extract all unique legal issues from sections
      const allIssues = this.extractLegalIssues(context.sectionList || []);

      if (allIssues.size === 0) {
        this.log("info", "No legal issues found for memo generation");
        return {
          memos: [],
          memoIndex: [],
          sectionList: context.sectionList || [],
        };
      }

      this.log("info", `Starting memo generation for ${allIssues.size} issues`);

      // Initialize progress tracking
      progressManager.updateStep(
        `Preparing legal memo generation for ${allIssues.size} issues...`,
        { total: allIssues.size }
      );

      // Start token tracking stage for memo generation
      const memoStageTracker = tokenTracker
        ? (
            tokenTracker as {
              startStage: (stage: string) => {
                addTokens: (count: number, type: string) => void;
                finish: () => void;
              };
            }
          ).startStage("legalMemos")
        : null;

      // Fetch and validate Legal-QA workspaces
      const currentWorkspace =
        dependencies.workspace as unknown as WorkspaceType;
      const workspaces = await this.fetchLegalQAWorkspaces(currentWorkspace);

      // Generate memos for all issues
      const { generatedMemos, memoIndex } = await this.generateMemosForIssues(
        allIssues,
        workspaces,
        dependencies,
        context // Pass context to access allPrompts
      );

      // Complete the token tracking stage
      if (memoStageTracker) {
        const totalMemoTokens = generatedMemos.reduce(
          (sum, memo) => sum + (memo.tokenCount || 0),
          0
        );
        memoStageTracker.addTokens(totalMemoTokens, "memo-generation");
        memoStageTracker.finish();

        this.log(
          "info",
          `Token tracking: Generated ${generatedMemos.length} memos using ${totalMemoTokens} tokens`
        );
      }

      // Update sections with memo references
      const updatedSectionList = this.updateSectionsWithMemoReferences(
        context.sectionList || [],
        generatedMemos
      );

      // Complete the step
      progressManager.completeStep(
        `Generated ${generatedMemos.length} legal memos successfully`
      );

      this.log("info", `Legal memo generation completed`, {
        totalIssues: allIssues.size,
        successfulMemos: generatedMemos.length,
        failedMemos: allIssues.size - generatedMemos.length,
      });

      return {
        memos: generatedMemos,
        memoIndex,
        sectionList: updatedSectionList,
      };
    } catch (error) {
      this.log("error", "Legal memo generation failed", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });

      // Don't crash the entire flow - continue without memos
      progressManager.sendError(
        error instanceof Error ? error : new Error(String(error))
      );

      // Return empty memos but preserve existing section list
      return {
        memos: [],
        memoIndex: [],
        sectionList: context.sectionList || [],
        errors: [
          ...(context.errors || []),
          {
            stage: "legal-memo",
            error: error instanceof Error ? error.message : String(error),
            timestamp: new Date().toISOString(),
          },
        ],
      };
    }
  }

  /**
   * Extract all unique legal issues from sections
   * Based on legacy mainDoc.js lines 1042-1060
   */
  private extractLegalIssues(
    sectionList: Section[]
  ): Map<string, LegalIssueDetails> {
    const allIssues = new Map<string, LegalIssueDetails>();

    sectionList.forEach((section, sectionIndex) => {
      (section.identifiedLegalIssues || []).forEach(
        (issueObj: LegalIssue | string) => {
          if (
            typeof issueObj === "object" &&
            issueObj !== null &&
            issueObj.Issue
          ) {
            const issueText = issueObj.Issue;
            if (!allIssues.has(issueText)) {
              allIssues.set(issueText, {
                issueObject: issueObj,
                sectionIndex,
                relevantDocumentNames: section.relevantDocumentNames || [],
              });
            }
          } else if (typeof issueObj === "string") {
            // Handle simple string issues from noMainDoc flow
            if (!allIssues.has(issueObj)) {
              allIssues.set(issueObj, {
                issueObject: { Issue: issueObj },
                sectionIndex,
                relevantDocumentNames: section.relevantDocumentNames || [],
              });
            }
          }
        }
      );
    });

    return allIssues;
  }

  /**
   * Fetch and validate Legal-QA workspaces
   * Based on legacy mainDoc.js lines 830-901
   */
  private async fetchLegalQAWorkspaces(
    currentWorkspace: WorkspaceType
  ): Promise<Map<string, WorkspaceType>> {
    const workspaceMap = new Map<string, WorkspaceType>();

    try {
      this.log("info", "Fetching Legal-QA workspaces for memo generation");
      const lqaWorkspaces = await Workspace.where({ type: "legal-qa" });

      if (!lqaWorkspaces || lqaWorkspaces.length === 0) {
        this.log(
          "warn",
          "No Legal-QA workspaces found, using current workspace as fallback"
        );
      } else {
        lqaWorkspaces.forEach((ws: WorkspaceType) => {
          if (ws?.name && ws?.slug) {
            workspaceMap.set(ws.slug, ws);
          }
        });
        this.log("info", `Found ${lqaWorkspaces.length} Legal-QA workspaces`);
      }
    } catch (error) {
      this.log("error", "Failed to fetch Legal-QA workspaces", {
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // ALWAYS add current workspace as fallback, regardless of Legal-QA workspaces
    if (currentWorkspace && currentWorkspace.slug) {
      workspaceMap.set(currentWorkspace.slug, currentWorkspace);
      this.log(
        "info",
        `Added current workspace as fallback: ${currentWorkspace.slug}`
      );
    }

    if (workspaceMap.size === 0) {
      throw new Error(
        "No valid workspaces available for memo generation - current workspace is invalid or missing"
      );
    }

    return workspaceMap;
  }

  /**
   * Generate memos for all identified issues
   * Based on legacy mainDoc.js lines 1071-1431
   */
  private async generateMemosForIssues(
    allIssues: Map<string, LegalIssueDetails>,
    workspaces: Map<string, WorkspaceType>,
    dependencies: StageDependencies,
    context: FlowContext
  ): Promise<{ generatedMemos: GeneratedMemo[]; memoIndex: MemoIndexEntry[] }> {
    const {
      progressManager,
      llmCoordinator: _llmCoordinator,
      abortChecker,
      legalTask: _legalTask,
    } = dependencies;
    const generatedMemos: GeneratedMemo[] = [];
    const memoIndex: MemoIndexEntry[] = [];
    let memoCounter = 0;

    for (const [issueText, issueDetails] of Array.from(allIssues.entries())) {
      abortChecker();

      memoCounter++;
      const suggestedWorkspaceSlug = this.sanitizeSlug(
        issueDetails?.issueObject?.WORKSPACE_SLUG_FOR_LEGALDATA
      );

      // Select target workspace
      const targetWorkspace = this.selectTargetWorkspace(
        suggestedWorkspaceSlug,
        workspaces
      );

      if (!targetWorkspace) {
        this.log("error", `No valid workspace found for issue: "${issueText}"`);
        this.handleMemoGenerationFailure(
          issueText,
          "No valid workspace found for memo generation",
          memoIndex,
          progressManager,
          memoCounter,
          allIssues.size
        );
        continue;
      }

      // Validate workspace before attempting memo generation
      if (!targetWorkspace.slug || typeof targetWorkspace.slug !== "string") {
        throw new Error(`Invalid workspace slug: ${targetWorkspace.slug}`);
      }

      // Send progress update before starting memo generation
      progressManager.sendSubStepProgress(
        memoCounter,
        allIssues.size,
        `Generating legal memo ${memoCounter} of ${allIssues.size}...`,
        String(issueText).substring(0, 50),
        -1 // Loading state
      );

      // Generate memo with retry logic
      const memoResult = await this.generateMemoWithRetry(
        issueText,
        issueDetails,
        targetWorkspace,
        dependencies,
        context,
        memoCounter,
        allIssues.size
      );

      if (memoResult) {
        generatedMemos.push(memoResult);
        memoIndex.push({
          issueText,
          memoFileName: memoResult.fileName,
          memoFilePath: memoResult.filePath,
          relevantSectionIndices: memoResult.relevantSectionIndices || [],
          workspaceSlug: targetWorkspace.slug,
          error: null,
        });
      } else {
        // Add failed memo to index for tracking
        memoIndex.push({
          issueText,
          memoFileName: null,
          memoFilePath: null,
          relevantSectionIndices: [],
          workspaceSlug: targetWorkspace.slug,
          error: `Failed to generate memo after ${this.options.maxRetries} attempts`,
        });
      }
    }

    return { generatedMemos, memoIndex };
  }

  /**
   * Generate memo with retry logic and error handling
   * Based on legacy mainDoc.js lines 1180-1431
   */
  private async generateMemoWithRetry(
    issueText: string,
    issueDetails: LegalIssueDetails,
    targetWorkspace: WorkspaceType,
    dependencies: StageDependencies,
    context: FlowContext,
    memoCounter: number,
    totalMemos: number
  ): Promise<GeneratedMemo | null> {
    const { progressManager, llmCoordinator, abortChecker, legalTask, chatId } =
      dependencies;
    const { maxRetries, retryDelay, keepaliveInterval } = this.options;

    let lastError: Error | null = null;
    let keepaliveTimer: NodeJS.Timeout | null = null;

    // Set up keepalive events for long-running operations
    if (keepaliveInterval > 0) {
      keepaliveTimer = setInterval(() => {
        try {
          abortChecker();
          progressManager.sendSubStepProgress(
            memoCounter,
            totalMemos,
            `Generating memo for: ${String(issueText)}...`,
            String(issueText).substring(0, 50),
            -1 // In progress
          );
        } catch {
          if (keepaliveTimer) clearInterval(keepaliveTimer);
        }
      }, keepaliveInterval);
    }

    try {
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        abortChecker();

        try {
          this.log("info", `Memo generation attempt ${attempt}/${maxRetries}`, {
            issueText: String(issueText).substring(0, 100),
            workspace: targetWorkspace.slug,
          });

          let memoResult: MemoGenerationResult | null = null;
          try {
            const docsForMemo =
              (issueDetails.relevantDocumentNames || []).join(", ") ||
              "(general legal principles)";

            // Get memo template from context (resolved prompts), with fallback to default
            let memoTemplate: string;
            const contextWithPrompts = context as FlowContext & {
              allPrompts?: {
                CURRENT_DEFAULT_MEMO_CREATION?: {
                  PROMPT_TEMPLATE?: string;
                };
              };
            };
            if (
              contextWithPrompts.allPrompts?.CURRENT_DEFAULT_MEMO_CREATION
                ?.PROMPT_TEMPLATE
            ) {
              memoTemplate =
                contextWithPrompts.allPrompts.CURRENT_DEFAULT_MEMO_CREATION
                  .PROMPT_TEMPLATE;
            } else {
              // Fallback to default template
              memoTemplate =
                'Create a legal memorandum addressing the legal issue "{{issue}}" in the same language as the legal task "{{task}}". In the analysis, consider the following documents (reference them by name where relevant): {{docs}}. Include extensive legal analysis, jurisprudence, references to relevant legal sections in applicable legislation analysis in a structured format. The purpose of the memo is to provide the local legal context for the continued drafting of part of a response stemming from the legal task';
            }

            this.log("info", "Preparing system prompt with template", {
              templateLength: memoTemplate?.length || 0,
              docsForMemo: docsForMemo.substring(0, 100),
            });

            // Prepare system prompt using fillTemplate
            const systemPrompt = fillTemplate(memoTemplate, {
              docs: docsForMemo,
              issue: String(issueText),
              task: String(legalTask),
            });

            this.log("info", "About to call generateLegalMemo", {
              systemPromptLength: systemPrompt?.length || 0,
              userPromptLength: String(issueText)?.length || 0,
              workspaceSlug: targetWorkspace.slug,
              hasLLMConnector: !!llmCoordinator.getConnector(),
              temperature: llmCoordinator.getTemperature(),
            });

            // Generate the legal memo using the helper
            memoResult = await generateLegalMemo({
              workspace: targetWorkspace as any,
              systemPrompt,
              userPrompt: String(issueText),
              LLMConnector:
                llmCoordinator.getConnector() as unknown as LegalMemoLLMConnector,
              temperature: llmCoordinator.getTemperature(),
              tokenLimit: null,
              settings: { skipContext: true },
            });

            this.log("info", "generateLegalMemo completed successfully", {
              memoLength: memoResult?.memo?.length || 0,
              tokenCount: memoResult?.tokenCount || 0,
            });
          } catch {
            console.error("Failed to parse JSON response from LLM");
            return null;
          }

          if (!memoResult || !memoResult.memo) {
            throw new Error(
              "Empty memo result returned from generateLegalMemo"
            );
          }

          // Save memo file
          const fileName = `legal-memo-${memoCounter}-${chatId}.md`;
          const filePath = await this.saveMemoFile(fileName, memoResult.memo);

          this.log("info", `Memo generation successful on attempt ${attempt}`, {
            fileName,
            contentLength: memoResult.memo.length,
          });

          // Send completion progress
          progressManager.sendSubStepProgress(
            memoCounter,
            totalMemos,
            `Generated memo for: ${String(issueText)}`,
            String(issueText).substring(0, 50),
            100 // Complete
          );

          return {
            fileName,
            filePath,
            content: memoResult.memo,
            issueText: String(issueText),
            workspaceSlug: targetWorkspace.slug,
            tokenCount: memoResult.tokenCount,
            relevantSectionIndices: [issueDetails.sectionIndex + 1], // Convert to 1-based indexing
          };
        } catch (error) {
          lastError = error instanceof Error ? error : new Error(String(error));
          this.log("warn", `Memo generation attempt ${attempt} failed`, {
            error: error instanceof Error ? error.message : String(error),
            workspace: targetWorkspace.slug,
          });

          if (attempt < maxRetries) {
            await new Promise((resolve) => setTimeout(resolve, retryDelay));
          }
        }
      }

      // All attempts failed
      this.log("error", `All ${maxRetries} memo generation attempts failed`, {
        issueText: String(issueText).substring(0, 100),
        lastError: lastError?.message,
      });

      this.handleMemoGenerationFailure(
        String(issueText),
        `Failed using ${targetWorkspace.slug}: ${lastError?.message}`,
        [], // Will be handled by caller
        progressManager,
        memoCounter,
        totalMemos
      );

      return null;
    } finally {
      if (keepaliveTimer) {
        clearInterval(keepaliveTimer);
      }
    }
  }

  /**
   * Save memo file to storage
   */
  private async saveMemoFile(
    fileName: string,
    content: string
  ): Promise<string> {
    const documentBuilderPath = path.join(
      __dirname,
      "../../../../storage/document-builder"
    );

    // Ensure directory exists
    if (!fs.existsSync(documentBuilderPath)) {
      fs.mkdirSync(documentBuilderPath, { recursive: true });
    }

    const filePath = path.join(documentBuilderPath, fileName);

    try {
      fs.writeFileSync(filePath, content, { encoding: "utf8", mode: 0o644 });
      this.log("info", `Memo saved successfully: ${fileName}`);
      return filePath;
    } catch (error) {
      this.log("error", `Failed to save memo file: ${fileName}`, {
        error: error instanceof Error ? error.message : String(error),
      });

      // Try backup location only in production environment, not in tests
      if (process.env.NODE_ENV !== "test" && !process.env.JEST_WORKER_ID) {
        try {
          const backupPath = path.join(process.cwd(), "temp", fileName);
          const backupDir = path.dirname(backupPath);
          if (!fs.existsSync(backupDir)) {
            fs.mkdirSync(backupDir, { recursive: true });
          }
          fs.writeFileSync(backupPath, content, "utf8");
          this.log("info", `Memo saved to backup location: ${backupPath}`);
          return backupPath;
        } catch (backupError) {
          this.log("error", `Failed to save memo to backup location:`, {
            error:
              backupError instanceof Error
                ? backupError.message
                : String(backupError),
          });
          throw new Error(
            `Failed to save memo file: primary error: ${error instanceof Error ? error.message : String(error)}, backup error: ${backupError instanceof Error ? backupError.message : String(backupError)}`
          );
        }
      }

      throw error;
    }
  }

  /**
   * Handle memo generation failure
   */
  private handleMemoGenerationFailure(
    issueText: string,
    errorMessage: string,
    memoIndex: MemoIndexEntry[],
    progressManager: ProgressManager,
    memoCounter: number,
    totalMemos: number
  ): void {
    memoIndex.push({
      issueText,
      error: errorMessage,
      memoFileName: null,
      memoFilePath: null,
      relevantSectionIndices: [],
      workspaceSlug: null,
    });

    progressManager.sendSubStepProgress(
      memoCounter,
      totalMemos,
      `Error - ${errorMessage}: ${String(issueText)}...`,
      String(issueText).substring(0, 50),
      -2 // Error state
    );
  }

  /**
   * Update sections with memo references
   */
  private updateSectionsWithMemoReferences(
    sectionList: Section[],
    generatedMemos: GeneratedMemo[]
  ): SectionWithMemos[] {
    const memoMap = new Map<string, GeneratedMemo>();
    generatedMemos.forEach((memo) => {
      memoMap.set(memo.issueText, memo);
    });

    return sectionList.map((section): SectionWithMemos => {
      if (!section.identifiedLegalIssues) return section;

      const relevantMemos: MemoReference[] = [];
      section.identifiedLegalIssues.forEach((issueObj: LegalIssue | string) => {
        const issueText =
          typeof issueObj === "object" ? issueObj.Issue : issueObj;
        const memo = memoMap.get(issueText);
        if (memo) {
          relevantMemos.push({
            issue: issueText,
            memoFileName: memo.fileName,
            memoFilePath: memo.filePath,
            generated: true,
            targetWorkspaceSlug: memo.workspaceSlug,
          });
        }
      });

      return {
        ...section,
        relevantMemos,
      };
    });
  }

  /**
   * Utility: sanitize potential slug strings from LLM
   */
  private sanitizeSlug(slugStr: string | null | undefined): string | null {
    if (!slugStr || typeof slugStr !== "string") return null;
    let cleaned = slugStr.trim().toLowerCase();
    if (["none", "null", "undefined"].includes(cleaned)) return null;
    cleaned = cleaned.replace(/\s+/g, "-").replace(/[^a-z0-9-_]/g, "");
    return (cleaned?.length ?? 0) > 0 ? cleaned : null;
  }

  /**
   * Select target workspace for memo generation
   */
  private selectTargetWorkspace(
    suggestedSlug: string | null,
    workspaces: Map<string, WorkspaceType>
  ): WorkspaceType | null {
    // Try suggested workspace first
    if (suggestedSlug && workspaces.has(suggestedSlug)) {
      const workspace = workspaces.get(suggestedSlug);
      if (this.validateWorkspace(workspace)) {
        this.log("info", `Using suggested workspace: ${workspace.slug}`);
        return workspace;
      }
    }

    // Fallback to first valid workspace
    for (const workspace of Array.from(workspaces.values())) {
      if (this.validateWorkspace(workspace)) {
        this.log("info", `Using fallback workspace: ${workspace.slug}`);
        return workspace;
      }
    }

    return null;
  }

  /**
   * Validate workspace object
   */
  private validateWorkspace(
    workspace: WorkspaceType | undefined
  ): workspace is WorkspaceType {
    return !!(
      workspace &&
      workspace.slug &&
      typeof workspace.slug === "string" &&
      (workspace?.slug?.length ?? 0) > 0
    );
  }

  /**
   * Validate inputs for legal memo generation stage
   */
  validateInputs(context: FlowContext): boolean {
    if (!context.sectionList || !Array.isArray(context.sectionList)) {
      // Return true for empty or missing sectionList - this is valid input
      return true;
    }

    return true;
  }

  /**
   * Check if legal memo generation stage should be skipped
   */
  shouldSkip(context: FlowContext): boolean {
    // Skip only if no issues are available for memo generation
    const hasIssues = context?.sectionList?.some(
      (section: Section) =>
        section.identifiedLegalIssues &&
        (section?.identifiedLegalIssues?.length ?? 0) > 0
    );

    // Only skip if no issues are available or explicitly configured to skip
    return !hasIssues || this.options.skipMemoGeneration === true;
  }
}

export {
  LegalMemoProcessor,
  type LegalMemoOptions,
  type LegalIssueDetails,
  type GeneratedMemo,
  type MemoIndexEntry,
  type SectionWithMemos,
  type MemoReference,
  type MemoGenerationResult,
};
