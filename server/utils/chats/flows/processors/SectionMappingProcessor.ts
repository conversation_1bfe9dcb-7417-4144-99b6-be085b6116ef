import { StageProcessor } from "../core/StageProcessor";
import { FlowContext, StageDependencies } from "../../../../types/chat-flow";

class SectionMappingProcessor extends StageProcessor {
  constructor(options: Record<string, unknown> = {}) {
    super(options);
    this.options = {
      ...options,
    };
  }
  // TODO: Implement process and other methods as needed
  async process(
    _context: FlowContext,
    _dependencies: StageDependencies
  ): Promise<Partial<FlowContext>> {
    throw new Error("SectionMappingProcessor.process() not implemented");
  }
}

export { SectionMappingProcessor };
