import { StageProcessor } from "../core/StageProcessor";
import { FlowContext, StageDependencies } from "../../../../types/chat-flow";
import {
  generateSectionListFromSummaries,
  fillTemplate,
} from "../../helpers/documentProcessing";
import { parseLLMJsonResponse } from "../../helpers/llmResponseParser";
import { LLMConnector } from "../../LLMConnector";

// Removed unused DocDescription interface (using string[] from FlowContext)

interface Section {
  index_number?: number;
  index?: number;
  title?: string;
  Title?: string;
  description?: string;
  Description?: string;
  relevant_documents?: string[];
  relevantDocumentNames?: string[];
  legal_issues_to_address?: string[];
  legalIssues?: string[];
}

interface NormalizedSection {
  index_number: number;
  title: string;
  description: string;
  relevant_documents: string[];
  legal_issues_to_address: string[];
}

interface SectionPlanningMetrics {
  sectionsGenerated: number;
  sectionSource: string;
  averageDocsPerSection: number;
  generatedAt: string;
  [key: string]: unknown;
}

// Note: SectionPlanningContext extends FlowContext with additional optional fields

interface SectionPlanningOptions {
  sectionSource?: "summaries" | "mainDocument";
  skipSectionPlanning?: boolean;
  promptOverrides?: {
    sectionGenerationPrompt?: string;
  };
  prompts?: Record<
    string,
    { SYSTEM_PROMPT?: string; USER_PROMPT?: string; PROMPT_TEMPLATE?: string }
  >;
}

// Removed unused SectionPlanningResult interface (using Partial<FlowContext>)

/**
 * Section Planning Stage Processor
 *
 * Handles section generation for document drafting flows:
 * - Generates sections from main document (if available)
 * - Generates sections from document summaries
 * - Supports different section generation strategies based on flow type
 * - Maps documents to relevant sections
 */
class SectionPlanningStageProcessor extends StageProcessor {
  declare public options: SectionPlanningOptions & Record<string, unknown>;

  constructor(...args: unknown[]) {
    const options = (args[0] ?? {}) as SectionPlanningOptions;
    super(options as Record<string, unknown>);
    this.options = {
      sectionSource: "summaries", // 'summaries' or 'mainDocument'
      ...options,
    };
  }

  /**
   * Process the section planning stage
   * @param context - Shared context object
   * @param dependencies - Injected dependencies
   * @returns Section planning results to add to context
   */
  async process(
    context: FlowContext,
    dependencies: StageDependencies
  ): Promise<Partial<FlowContext>> {
    const {
      progressManager,
      llmCoordinator,
      abortChecker,
      legalTask,
      customInstructions,
      chatId,
    } = dependencies;

    // Get prompts from options or processor options
    const stagePrompts =
      (dependencies.options as Record<string, unknown>)?.prompts ||
      this.options.prompts ||
      {};

    this.log("info", "Starting section planning stage", {
      sectionSource: this.options.sectionSource,
      docDescriptionsCount: context?.docDescriptions?.length || 0,
      chatId,
    });

    // Check abort signal
    abortChecker();

    if (!context.docDescriptions || context.docDescriptions.length === 0) {
      throw new Error(
        "No document descriptions available for section planning"
      );
    }

    // Get LLM components
    const llmConnector =
      llmCoordinator.getConnector() as unknown as LLMConnector;
    const temperature = llmCoordinator.getTemperature();

    // Get prompts for this stage
    const prompts = context.allPrompts || stagePrompts;

    progressManager.updateStep("Planning document sections...", { total: 3 });

    try {
      // Step 1: Analyze document summaries
      progressManager.sendSubStepProgress(
        1,
        3,
        "Analyzing document summaries...",
        "Analysis",
        -1
      );

      let sectionList: Section[];
      let sectionSource: string;

      // Determine section generation strategy
      if (
        this.options.sectionSource === "mainDocument" &&
        context.mainDocumentContent
      ) {
        progressManager.sendSubStepProgress(
          1,
          3,
          "Analyzing main document content...",
          "Analysis",
          50
        );
        sectionSource = "main_document";
      } else {
        progressManager.sendSubStepProgress(
          1,
          3,
          "Analyzing document summaries...",
          "Analysis",
          50
        );
        sectionSource = "summaries";
      }
      progressManager.sendSubStepProgress(
        1,
        3,
        "Document analysis complete",
        "Analysis",
        100
      );

      // Step 2: Generate section structure
      progressManager.sendSubStepProgress(
        2,
        3,
        "Generating section structure...",
        "Section Generation",
        -1
      );

      if (
        this.options.sectionSource === "mainDocument" &&
        context.mainDocumentContent
      ) {
        // Generate sections from main document
        sectionList = await this.generateSectionsFromMainDocument(
          context.mainDocumentContent as string,
          legalTask,
          llmConnector as unknown as LLMConnector,
          prompts,
          temperature
        );
      } else {
        // Generate sections from document summaries
        sectionList = await this.generateSectionsFromSummaries(
          context.docDescriptions.map((doc) => doc.Description),
          legalTask,
          llmConnector as unknown as LLMConnector,
          prompts as any,
          customInstructions,
          temperature
        );
      }

      abortChecker(); // Check abort after section generation

      if (
        !sectionList ||
        !Array.isArray(sectionList) ||
        sectionList.length === 0
      ) {
        throw new Error("Failed to generate valid section list");
      }

      // Normalize section list structure
      const normalizedSectionList = this.normalizeSectionList(sectionList);
      progressManager.sendSubStepProgress(
        2,
        3,
        "Section structure generated",
        "Section Generation",
        100
      );

      // Step 3: Map documents to sections
      progressManager.sendSubStepProgress(
        3,
        3,
        "Mapping documents to sections...",
        "Document Mapping",
        -1
      );
      const finalSectionList = await this.mapDocumentsToSections(
        normalizedSectionList,
        context.docDescriptions.map((doc) => doc.Description),
        abortChecker
      );
      progressManager.sendSubStepProgress(
        3,
        3,
        "Section planning completed",
        "Document Mapping",
        100
      );

      const sectionMetrics: SectionPlanningMetrics = {
        sectionsGenerated: finalSectionList.length,
        sectionSource,
        averageDocsPerSection:
          this.calculateAverageDocsPerSection(finalSectionList),
        generatedAt: new Date().toISOString(),
      };

      this.log(
        "info",
        "Section planning completed",
        sectionMetrics as unknown as Record<string, unknown>
      );

      // Convert NormalizedSection to base Section format for FlowContext
      const convertedSectionList = finalSectionList.map((section, index) => ({
        sectionNumber: section.index_number || index + 1,
        title: section.title,
        relevantDocumentNames: section.relevant_documents || [],
        identifiedLegalIssues: [],
      }));

      // Return data to add to context
      return {
        sectionList: convertedSectionList,
        metrics: {
          ...context.metrics,
          sectionPlanning:
            sectionMetrics as import("../../../../types/chat-flow").SectionPlanningMetrics,
        },
      };
    } catch (error) {
      this.log("error", "Section planning failed", {
        error: error instanceof Error ? error.message : String(error),
      });
      throw new Error(
        `Section planning failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Generate sections from main document content
   */
  private async generateSectionsFromMainDocument(
    mainDocumentContent: string,
    legalTask: string,
    llmConnector: LLMConnector,
    prompts: unknown,
    temperature: number
  ): Promise<Section[]> {
    const systemPrompt = (prompts as Record<string, Record<string, string>>)
      ?.CURRENT_DEFAULT_SECTION_LIST_FROM_MAIN?.SYSTEM_PROMPT;
    const userPromptTemplate = (
      prompts as Record<string, Record<string, string>>
    )?.CURRENT_DEFAULT_SECTION_LIST_FROM_MAIN?.USER_PROMPT;

    if (!systemPrompt || !userPromptTemplate) {
      throw new Error("Missing prompts for main document section generation");
    }

    const userPrompt = fillTemplate(userPromptTemplate, {
      task: legalTask,
      content: mainDocumentContent,
    });

    const compressedMessages = await llmConnector.compressMessages({
      systemPrompt,
      userPrompt,
    });

    const result = await llmConnector.getChatCompletion(compressedMessages, {
      temperature,
    });

    // Parse JSON response using the enhanced LLM response parser
    const parseResult = await parseLLMJsonResponse(result?.textResponse || "", {
      maxAttempts: 3,
      enableLlmFix: true,
      llmConnector: llmConnector,
      validator: (data: unknown) =>
        Array.isArray(data) && (data as unknown[])?.length > 0,
    });

    if (parseResult.success) {
      this.log("info", "Successfully parsed section list from main document", {
        sectionsCount: (parseResult.data as Section[]).length,
        attemptsMade: parseResult.attemptsMade,
      });
      return parseResult.data as Section[];
    } else {
      this.log(
        "error",
        "Failed to parse section list JSON from main document",
        {
          error: parseResult.error,
          response: result?.textResponse?.substring(0, 500) || "no response",
          attemptsMade: parseResult.attemptsMade,
        }
      );
      throw new Error(`Failed to parse section list: ${parseResult.error}`);
    }
  }

  /**
   * Generate sections from document summaries
   */
  private async generateSectionsFromSummaries(
    docDescriptions: string[],
    legalTask: string,
    llmConnector: LLMConnector,
    prompts: Record<string, Record<string, string>> | undefined,
    customInstructions: string = "",
    temperature: number
  ): Promise<Section[]> {
    // Check for prompt overrides for specific flows (e.g., reference flow)
    let promptKey = "CURRENT_DEFAULT_SECTION_LIST_FROM_SUMMARIES";
    if (this.options?.promptOverrides?.sectionGenerationPrompt) {
      promptKey = this.options.promptOverrides.sectionGenerationPrompt;
    }

    const systemPrompt = (prompts as Record<string, Record<string, string>>)[
      promptKey
    ]?.SYSTEM_PROMPT;
    const userPromptTemplate = (
      prompts as Record<string, Record<string, string>>
    )[promptKey]?.USER_PROMPT;

    if (!systemPrompt || !userPromptTemplate) {
      throw new Error("Missing prompts for summary-based section generation");
    }

    // Convert string descriptions to DocumentDescription format
    const documentDescriptions = docDescriptions.map((desc, index) => ({
      "Doc Name": `Document ${index + 1}`,
      DisplayName: `Document ${index + 1}`,
      Description: desc,
    }));

    return await generateSectionListFromSummaries(
      documentDescriptions as {
        "Doc Name": string;
        DisplayName: string;
        Description: string;
      }[],
      legalTask,
      llmConnector as LLMConnector,
      customInstructions,
      {
        customSystemPrompt: systemPrompt,
        customUserPromptTemplate: userPromptTemplate,
        temperature,
      }
    );
  }

  /**
   * Normalize section list to ensure consistent structure
   */
  private normalizeSectionList(sectionList: Section[]): NormalizedSection[] {
    return sectionList.map((section, index) => ({
      index_number: section.index_number || section.index || index + 1,
      title: section.title || section.Title || `Section ${index + 1}`,
      description: section.description || section.Description || "",
      relevant_documents:
        section.relevant_documents || section.relevantDocumentNames || [],
      legal_issues_to_address:
        section.legal_issues_to_address || section.legalIssues || [],
    }));
  }

  /**
   * Map documents to sections if not already mapped
   */
  private async mapDocumentsToSections(
    sectionList: NormalizedSection[],
    docDescriptions: string[],
    abortChecker: () => void
  ): Promise<NormalizedSection[]> {
    // For now, if sections don't have documents mapped, we'll distribute them
    // This could be enhanced with LLM-based document-to-section mapping

    const sectionsWithoutDocs = sectionList.filter(
      (section) =>
        !section.relevant_documents || section.relevant_documents.length === 0
    );

    if ((sectionsWithoutDocs?.length ?? 0) > 0) {
      this.log("info", "Mapping documents to sections without documents", {
        sectionsWithoutDocs: sectionsWithoutDocs.length,
      });

      // Simple distribution strategy - evenly distribute documents across sections
      // Since docDescriptions are now strings, use them directly
      const docNames = docDescriptions;
      const docsPerSection = Math.ceil(docNames.length / sectionList.length);

      for (let i = 0; i < sectionList.length; i++) {
        abortChecker(); // Check abort during mapping

        if (
          !sectionList[i].relevant_documents ||
          sectionList[i].relevant_documents.length === 0
        ) {
          const startIdx = i * docsPerSection;
          const endIdx = Math.min(startIdx + docsPerSection, docNames.length);
          sectionList[i].relevant_documents = docNames.slice(startIdx, endIdx);
        }
      }
    }

    return sectionList;
  }

  /**
   * Calculate average documents per section for metrics
   */
  private calculateAverageDocsPerSection(
    sectionList: NormalizedSection[]
  ): number {
    const totalDocs = sectionList.reduce((sum, section) => {
      return (
        sum +
        (section.relevant_documents ? section.relevant_documents.length : 0)
      );
    }, 0);
    return (sectionList?.length ?? 0) > 0
      ? Math.round((totalDocs / sectionList.length) * 10) / 10
      : 0;
  }

  /**
   * Validate inputs for section planning stage
   * @param context - Shared context object
   * @returns Whether inputs are valid
   */
  validateInputs(context: FlowContext): boolean {
    if (!context.docDescriptions || !Array.isArray(context.docDescriptions)) {
      this.log("error", "Invalid input: docDescriptions must be an array");
      return false;
    }

    if (context.docDescriptions.length === 0) {
      this.log("error", "Invalid input: no document descriptions provided");
      return false;
    }

    // If main document source is specified, check for main document content
    if (
      this.options.sectionSource === "mainDocument" &&
      !context.mainDocumentContent
    ) {
      this.log(
        "warn",
        "Main document source specified but no main document content available, falling back to summaries"
      );
      this.options.sectionSource = "summaries";
    }

    return true;
  }

  /**
   * Check if section planning stage should be skipped
   * @param context - Shared context object
   * @returns Whether to skip this stage
   */
  shouldSkip(context: FlowContext): boolean {
    // Skip only if no documents are available for section planning
    const hasDocuments =
      context.documents && (context?.documents?.length ?? 0) > 0;

    // Only skip if no documents are available or explicitly configured to skip
    return !hasDocuments || this.options.skipSectionPlanning === true;
  }
}

export { SectionPlanningStageProcessor };
