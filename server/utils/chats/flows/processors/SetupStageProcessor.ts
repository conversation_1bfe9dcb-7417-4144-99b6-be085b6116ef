import { StageProcessor } from "../core/StageProcessor";
import { getResolvedPrompts } from "../../helpers/promptManager";
import type {
  FlowContext,
  StageDependencies,
} from "../../../../types/chat-flow";

// Type definitions
interface SetupMetrics extends Record<string, unknown> {
  totalDocumentsInWorkspace: number;
  validDocumentsLoaded: number;
  skippedDocuments: number;
  setupCompletedAt: string;
}

interface WorkspaceInfo {
  totalDocuments: number;
  validDocuments: number;
  errorDocuments: number;
}

interface LLMProviderInfo {
  provider: string;
  model: string;
  availableContextWindow: number;
}

interface DocumentResult {
  documents: Array<{
    id: string;
    fileName: string;
    displayName: string;
    content: string;
    metadata: Record<string, unknown>;
  }>;
  totalFiles: number;
  skippedFiles: number;
}

interface SetupContext {
  metrics?: Record<string, unknown>;
}

/**
 * Setup Stage Processor
 *
 * Handles common setup logic for document drafting flows:
 * - Validates workspace and documents
 * - Loads prompts
 * - Initializes metrics tracking
 * - Performs initial abort checks
 */
class SetupStageProcessor extends StageProcessor {
  constructor(...args: unknown[]) {
    const options = (args[0] ?? {}) as Record<string, unknown>;
    super(options);
  }

  /**
   * Process the setup stage
   * @param context - Shared context object
   * @param dependencies - Injected dependencies
   * @returns Setup results to add to context
   */
  async process(
    context: FlowContext,
    dependencies: StageDependencies
  ): Promise<Partial<FlowContext>> {
    const {
      progressManager,
      workspaceManager,
      llmCoordinator,
      abortChecker,
      chatId,
    } = dependencies;

    this.log("info", "Starting setup stage", { chatId });

    // Check abort signal
    abortChecker();

    // Update progress with total steps
    progressManager.updateStep("Initializing document processing setup...", {
      total: 4,
    });

    try {
      // Step 1: Load resolved prompts
      progressManager.sendSubStepProgress(
        1,
        4,
        "Loading and resolving prompts...",
        "Prompt Loading",
        -1
      );
      const allPrompts = await getResolvedPrompts();
      progressManager.sendSubStepProgress(
        1,
        4,
        "Prompts loaded successfully",
        "Prompt Loading",
        100
      );

      // Step 2: Get workspace information
      progressManager.sendSubStepProgress(
        2,
        4,
        "Validating workspace and documents...",
        "Workspace Validation",
        -1
      );
      const workspaceInfo: WorkspaceInfo = workspaceManager.getWorkspaceInfo();
      this.log("info", "Workspace validated", {
        totalDocuments: workspaceInfo.totalDocuments,
        validDocuments: workspaceInfo.validDocuments,
        errorDocuments: workspaceInfo.errorDocuments,
      });
      progressManager.sendSubStepProgress(
        2,
        4,
        "Workspace validated successfully",
        "Workspace Validation",
        100
      );

      // Step 3: Get LLM provider information
      progressManager.sendSubStepProgress(
        3,
        4,
        "Initializing LLM provider...",
        "LLM Setup",
        -1
      );
      const llmProviderInfo: LLMProviderInfo = llmCoordinator.getProviderInfo();
      this.log("info", "LLM provider initialized", {
        provider: llmProviderInfo.provider,
        model: llmProviderInfo.model,
        availableContextWindow: llmProviderInfo.availableContextWindow,
      });
      progressManager.sendSubStepProgress(
        3,
        4,
        "LLM provider ready",
        "LLM Setup",
        100
      );

      // Step 4: Get all documents from workspace
      progressManager.sendSubStepProgress(
        4,
        4,
        "Loading documents from workspace...",
        "Document Loading",
        -1
      );
      const documentResult: DocumentResult = workspaceManager.getAllDocuments({
        skipErrors: true,
        skipEmpty: true,
      });

      if (documentResult.documents.length === 0) {
        throw new Error("No valid documents found in workspace for processing");
      }
      progressManager.sendSubStepProgress(
        4,
        4,
        "Setup completed successfully",
        "Document Loading",
        100
      );

      // Check abort before completing
      abortChecker();

      // Initialize metrics
      const setupMetrics: SetupMetrics = {
        totalDocumentsInWorkspace: documentResult.totalFiles,
        validDocumentsLoaded: documentResult.documents.length,
        skippedDocuments: documentResult.skippedFiles,
        setupCompletedAt: new Date().toISOString(),
      };

      this.log("info", "Setup stage completed successfully", setupMetrics);

      // Return data to add to context
      return {
        allPrompts,
        documents: documentResult.documents,
        workspaceInfo,
        llmProviderInfo,
        metrics: {
          ...context.metrics,
          setup:
            setupMetrics as import("../../../../types/chat-flow").SetupStageMetrics,
        },
      };
    } catch (error) {
      this.log("error", "Setup stage failed", {
        error: (error as Error).message,
      });
      throw new Error(`Setup stage failed: ${(error as Error).message}`);
    }
  }

  /**
   * Validate inputs for setup stage
   * @param context - Shared context object
   * @returns Whether inputs are valid
   */
  validateInputs(_context: SetupContext): boolean {
    // Setup stage doesn't require previous stage outputs
    return true;
  }

  /**
   * Check if setup stage should be skipped
   * @param context - Shared context object
   * @returns Whether to skip this stage
   */
  shouldSkip(_context: SetupContext): boolean {
    // Setup stage should never be skipped
    return false;
  }
}

export { SetupStageProcessor };
