import { AgenticCombinationStageProcessor } from "../AgenticCombinationStageProcessor";
import {
  performGranularEditing,
  performLineLevelEditing,
} from "../../../../documentEditing/editingLogic";
import { writeResponseChunk } from "../../../../helpers/chat/responses";

// Mock dependencies
jest.mock("../../../../../models/systemSettings", () => ({
  default: {
    get: jest.fn().mockResolvedValue({ value: "English" }),
  },
  SystemSettings: {
    get: jest.fn().mockResolvedValue({ value: "English" }),
  },
}));

jest.mock("../../../../helpers/chat/responses", () => ({
  writeResponseChunk: jest.fn(),
}));

jest.mock("../../../../robustLlmUtils", () => ({
  initializeRobustLLMConnector: jest.fn().mockResolvedValue({
    getChatCompletion: jest.fn().mockResolvedValue({
      textResponse: "English",
    }),
    defaultTemp: 0.7,
  }),
  getActiveLLMConfig: jest.fn().mockReturnValue({
    provider: "openai",
    model: "gpt-3.5-turbo",
  }),
}));

jest.mock("../../../../proseMirror/documentUtils", () => ({
  convertProseMirrorToMarkdown: jest
    .fn()
    .mockReturnValue("Mock markdown content"),
  createProseMirrorDocumentFromSections: jest.fn().mockResolvedValue({
    type: "doc",
    content: [
      {
        type: "paragraph",
        content: [{ type: "text", text: "Mock content" }],
      },
    ],
  }),
  pruneEmptyListItems: jest.fn(),
}));

jest.mock("../../../../documentEditing/editingLogic", () => ({
  performLineLevelEditing: jest.fn().mockResolvedValue({
    success: true,
    data: {
      type: "doc",
      content: [
        {
          type: "paragraph",
          content: [{ type: "text", text: "Line edited content" }],
        },
      ],
    },
    stats: { suggestionsGenerated: 5, suggestionsApproved: 3 },
  }),
  performGranularEditing: jest.fn().mockResolvedValue({
    success: true,
    data: {
      type: "doc",
      content: [
        {
          type: "paragraph",
          content: [{ type: "text", text: "Granular edited content" }],
        },
      ],
    },
    stats: { suggestionsGenerated: 8, suggestionsApproved: 5 },
  }),
  countWords: jest.fn((text: any) => (text ? text.split(/\s+/).length : 0)),
}));

jest.mock("fs", () => ({
  readFileSync: jest.fn().mockReturnValue("Mock memo content"),
  writeFileSync: jest.fn(),
  existsSync: jest.fn().mockReturnValue(true),
  mkdirSync: jest.fn(),
}));

describe("AgenticCombinationStageProcessor", () => {
  let processor: any;
  let mockContext: any;
  let mockDependencies: any;

  beforeEach(() => {
    jest.clearAllMocks();

    processor = new AgenticCombinationStageProcessor({
      agenticEditingEnabled: true,
      granularEditingEnabled: true,
      lineEditingEnabled: true,
      granularEditingIterations: 2,
      lineEditingIterations: 1,
      saveFullDocumentIterations: false,
      includeMetadata: false, // Disable metadata for cleaner tests
    });

    mockContext = {
      sectionOutputs: [
        {
          title: "Section 1",
          content: "Content for section 1",
          sectionNumber: 1,
          relevantMemos: [
            {
              issue: "Legal issue 1",
              memoFilePath: "/path/to/memo1.md",
            },
          ],
        },
        {
          title: "Section 2",
          content: "Content for section 2",
          sectionNumber: 2,
        },
      ],
      metrics: {},
    };

    mockDependencies = {
      progressManager: {
        updateStep: jest.fn(),
        sendSubStepProgress: jest.fn(),
      },
      abortChecker: jest.fn(),
      chatId: "test-chat-123",
      legalTask: "Test legal task",
      workspace: { slug: "test-workspace" },
      options: {
        response: {
          writableEnded: false,
        },
      },
    };
  });

  describe("Constructor and Configuration", () => {
    test("should initialize with default configuration", () => {
      const defaultProcessor: any = new AgenticCombinationStageProcessor();

      expect(defaultProcessor.config.agenticEditingEnabled).toBe(true);
      expect(defaultProcessor.config.granularEditingEnabled).toBe(true);
      expect(defaultProcessor.config.lineEditingEnabled).toBe(true);
      expect(defaultProcessor.config.granularEditingIterations).toBe(3);
      expect(defaultProcessor.config.lineEditingIterations).toBe(2);
    });

    test("should initialize with custom configuration", () => {
      const customProcessor: any = new AgenticCombinationStageProcessor({
        agenticEditingEnabled: false,
        granularEditingIterations: 5,
        lineEditingIterations: 3,
        saveFullDocumentIterations: true,
      });

      expect(customProcessor.config.agenticEditingEnabled).toBe(false);
      expect(customProcessor.config.granularEditingIterations).toBe(5);
      expect(customProcessor.config.lineEditingIterations).toBe(3);
      expect(customProcessor.config.saveFullDocumentIterations).toBe(true);
    });
  });

  describe("Process Method", () => {
    test("should successfully process with agentic editing enabled", async () => {
      const result: any = await processor.process(
        mockContext,
        mockDependencies
      );

      expect(result).toBeDefined();
      expect(result.finalContent).toBe("Mock markdown content");
      expect(result.metrics.agenticCombination).toBeDefined();
      expect(result.metrics.agenticCombination.sectionsProcessed).toBe(2);
    });

    test("should process without agentic editing when disabled", async () => {
      processor.config.agenticEditingEnabled = false;

      const result: any = await processor.process(
        mockContext,
        mockDependencies
      );

      expect(result).toBeDefined();
      expect(result.finalContent).toBe("Mock markdown content");
      expect(result.metrics.agenticCombination).toBeDefined();
    });

    test("should handle empty section outputs", async () => {
      mockContext.sectionOutputs = [];

      await expect(
        processor.process(mockContext, mockDependencies)
      ).rejects.toThrow("No section outputs available for agentic combination");
    });

    test("should handle abort signal", async () => {
      mockDependencies.abortChecker = jest.fn(() => {
        throw new Error("Aborted");
      });

      await expect(
        processor.process(mockContext, mockDependencies)
      ).rejects.toThrow("Aborted");
    });
  });

  describe("Agentic Editing Process", () => {
    test("should perform granular editing iterations", async () => {
      await processor.process(mockContext, mockDependencies);

      expect(performGranularEditing).toHaveBeenCalledTimes(
        processor.config.granularEditingIterations
      );
    });

    test("should perform line-level editing iterations", async () => {
      await processor.process(mockContext, mockDependencies);

      expect(performLineLevelEditing).toHaveBeenCalledTimes(
        processor.config.lineEditingIterations
      );
    });

    test("should handle editing failures gracefully", async () => {
      // Mock a failure in granular editing
      (
        performGranularEditing as jest.MockedFunction<
          typeof performGranularEditing
        >
      ).mockResolvedValueOnce({
        success: false,
        error: "Granular editing failed",
        data: null,
        stats: null,
      } as any);

      const result: any = await processor.process(
        mockContext,
        mockDependencies
      );

      // Should still complete successfully despite editing failure
      expect(result).toBeDefined();
      expect(result.finalContent).toBeDefined();
    });

    test("should skip granular editing when disabled", async () => {
      processor.config.granularEditingEnabled = false;

      await processor.process(mockContext, mockDependencies);

      expect(performGranularEditing).not.toHaveBeenCalled();
    });

    test("should skip line-level editing when disabled", async () => {
      processor.config.lineEditingEnabled = false;

      await processor.process(mockContext, mockDependencies);

      expect(performLineLevelEditing).not.toHaveBeenCalled();
    });
  });

  describe("Language Detection", () => {
    test("should detect language from legal task", async () => {
      const mockLLMConnector: any = {
        getChatCompletion: jest.fn().mockResolvedValue({
          textResponse: "Swedish",
        }),
      };

      const language: any = await processor.detectLanguage(
        "Detta är en svensk text",
        mockLLMConnector
      );

      expect(language).toBe("Swedish");
      expect(mockLLMConnector.getChatCompletion).toHaveBeenCalled();
    });

    test("should fallback to system default on detection failure", async () => {
      const mockLLMConnector: any = {
        getChatCompletion: jest.fn().mockRejectedValue(new Error("LLM failed")),
      };

      const language: any = await processor.detectLanguage(
        "Some text",
        mockLLMConnector
      );

      expect(language).toBe("English");
    });

    test("should return English as ultimate fallback", async () => {
      const mockSystemSettings =
        require("../../../../../models/systemSettings").default;
      mockSystemSettings.get.mockRejectedValueOnce(
        new Error("Settings failed")
      );

      const mockLLMConnector: any = {
        getChatCompletion: jest.fn().mockRejectedValue(new Error("LLM failed")),
      };

      const language: any = await processor.detectLanguage(
        "Some text",
        mockLLMConnector
      );

      expect(language).toBe("English");
    });
  });

  // Note: formatResearchTextFromContext method was removed from implementation
  // Tests removed to match actual implementation

  describe("Input Validation", () => {
    test("should validate valid inputs", () => {
      const isValid: any = processor.validateInputs(mockContext);
      expect(isValid).toBe(true);
    });

    test("should reject invalid section outputs", () => {
      const invalidContext: any = { sectionOutputs: null };
      const isValid: any = processor.validateInputs(invalidContext);
      expect(isValid).toBe(false);
    });

    test("should reject empty section outputs", () => {
      const emptyContext: any = { sectionOutputs: [] };
      const isValid: any = processor.validateInputs(emptyContext);
      expect(isValid).toBe(false);
    });
  });

  describe("Skip Conditions", () => {
    test("should not skip when no final content exists", () => {
      const shouldSkip: any = processor.shouldSkip(mockContext);
      expect(shouldSkip).toBe(false);
    });

    test("should skip when final content already exists", () => {
      const contextWithFinalContent: any = {
        ...mockContext,
        finalContent: "Existing final content",
      };

      const shouldSkip: any = processor.shouldSkip(contextWithFinalContent);
      expect(shouldSkip).toBe(true);
    });
  });

  describe("Metadata Generation", () => {
    test("should add metadata to document", () => {
      // Create a processor with metadata enabled for this specific test
      const metadataProcessor: any = new AgenticCombinationStageProcessor({
        includeMetadata: true,
      });

      const content: any = "Original content";
      const contextWithMetrics: any = {
        ...mockContext,
        metrics: {
          agenticCombination: {
            finalWordCount: 100,
            wordChangeFromEditing: 25,
          },
        },
      };

      const documentWithMetadata: any = metadataProcessor.addDocumentMetadata(
        content,
        contextWithMetrics
      );

      expect(documentWithMetadata).toContain(
        "<!-- Agentic Document Generation Metadata -->"
      );
      expect(documentWithMetadata).toContain("<!-- Final Word Count: 100 -->");
      expect(documentWithMetadata).toContain(
        "<!-- Word Change from Editing: 25 -->"
      );
      expect(documentWithMetadata).toContain(content);
    });
  });

  describe("Progress Reporting", () => {
    test("should update progress during processing", async () => {
      await processor.process(mockContext, mockDependencies);

      expect(mockDependencies.progressManager.updateStep).toHaveBeenCalledWith(
        "Building initial document from sections..."
      );
      expect(mockDependencies.progressManager.updateStep).toHaveBeenCalledWith(
        "Performing agentic editing enhancements...",
        { total: 3 }
      );
    });

    test("should report granular editing progress", async () => {
      await processor.process(mockContext, mockDependencies);

      expect(mockDependencies.progressManager.updateStep).toHaveBeenCalledWith(
        "Performing granular agentic editing..."
      );
    });

    test("should report line-level editing progress", async () => {
      await processor.process(mockContext, mockDependencies);

      expect(mockDependencies.progressManager.updateStep).toHaveBeenCalledWith(
        "Performing line-level agentic editing..."
      );
    });

    test("should report detailed sub-step progress for agentic editing iterations", async () => {
      await processor.process(mockContext, mockDependencies);

      // Should report sub-step progress for granular editing iterations with i18n keys
      expect(
        mockDependencies.progressManager.sendSubStepProgress
      ).toHaveBeenCalledWith(
        1, // First sub-task
        3, // Total sub-tasks (2 granular + 1 line-level)
        "granularEditStarting:1:2", // i18n key format
        "Granular edit 1/2",
        -1 // Loading state
      );

      // Should report completion for granular editing iterations with i18n keys
      expect(
        mockDependencies.progressManager.sendSubStepProgress
      ).toHaveBeenCalledWith(
        1,
        3,
        expect.stringMatching(/^granularEditCompleted:1:2:[+-]?\d+$/), // i18n key with word change
        "Granular edit 1/2",
        100 // Complete state
      );

      // Should report sub-step progress for line-level editing iterations with i18n keys
      expect(
        mockDependencies.progressManager.sendSubStepProgress
      ).toHaveBeenCalledWith(
        3, // Third sub-task (after 2 granular edits)
        3, // Total sub-tasks
        "lineEditStarting:1:1", // i18n key format
        "Line edit 1/1",
        -1 // Loading state
      );
    });
  });

  describe("Final Result Handling", () => {
    test("should send final result when enabled", async () => {
      await processor.process(mockContext, mockDependencies);

      expect(writeResponseChunk).toHaveBeenCalledWith(
        mockDependencies.options.response,
        expect.objectContaining({
          uuid: "test-chat-123",
          type: "text",
          close: true,
        })
      );
    });

    test("should not send final result when disabled", async () => {
      processor.config.sendFinalResult = false;

      await processor.process(mockContext, mockDependencies);

      expect(writeResponseChunk).not.toHaveBeenCalled();
    });

    test("should handle response stream errors gracefully", async () => {
      const {
        writeResponseChunk,
      } = require("../../../../helpers/chat/responses");
      (
        writeResponseChunk as jest.MockedFunction<typeof writeResponseChunk>
      ).mockImplementationOnce(() => {
        throw new Error("Response stream error");
      });

      // Should not throw error even if response fails
      const result: any = await processor.process(
        mockContext,
        mockDependencies
      );
      expect(result).toBeDefined();
    });
  });

  describe("Error Handling", () => {
    test("should handle LLM initialization failure", async () => {
      const {
        initializeRobustLLMConnector,
      } = require("../../../../robustLlmUtils");
      (
        initializeRobustLLMConnector as jest.MockedFunction<
          typeof initializeRobustLLMConnector
        >
      ).mockResolvedValueOnce(null);

      const result: any = await processor.process(
        mockContext,
        mockDependencies
      );

      // Should fallback to original document
      expect(result.finalContent).toBe("Mock markdown content");
    });

    test("should handle document building errors", async () => {
      const {
        createProseMirrorDocumentFromSections,
      } = require("../../../../proseMirror/documentUtils");
      (
        createProseMirrorDocumentFromSections as jest.MockedFunction<
          typeof createProseMirrorDocumentFromSections
        >
      ).mockRejectedValueOnce(new Error("Document building failed"));

      await expect(
        processor.process(mockContext, mockDependencies)
      ).rejects.toThrow("Document building failed");
    });

    test("should handle empty section content with fallback creation", async () => {
      const emptyContext: any = {
        sectionOutputs: [
          { title: "Section 1", content: "" },
          { title: "Section 2", content: "   " }, // whitespace only
          { title: "Section 3", content: null },
        ],
      };

      const result: any = await processor.process(
        emptyContext,
        mockDependencies
      );

      expect(result).toBeDefined();
      expect(result.finalContent).toBeDefined();
    });

    test("should handle mixed valid and invalid section content", async () => {
      const mixedContext: any = {
        sectionOutputs: [
          { title: "Valid Section", content: "This is valid content." },
          { title: "Empty Section", content: "" },
          { title: "Null Section", content: null },
          { title: "Valid Section 2", content: "More valid content." },
        ],
      };

      const result: any = await processor.process(
        mixedContext,
        mockDependencies
      );

      expect(result).toBeDefined();
      expect(result.finalContent).toBeDefined();
    });

    test("should handle markdown parsing errors gracefully", async () => {
      const contextWithInvalidMarkdown: any = {
        sectionOutputs: [
          {
            title: "Invalid Markdown",
            content: "<<<invalid>>> [[[markdown]]]",
          },
        ],
      };

      const result: any = await processor.process(
        contextWithInvalidMarkdown,
        mockDependencies
      );

      expect(result).toBeDefined();
      expect(result.finalContent).toBeDefined();
    });

    test("should report error progress for failed editing iterations", async () => {
      // Mock the editing functions to return failures
      (
        performGranularEditing as jest.MockedFunction<
          typeof performGranularEditing
        >
      ).mockResolvedValueOnce({
        success: false,
        error: "Granular editing failed for test",
        data: null,
        stats: null,
      } as any);

      (
        performLineLevelEditing as jest.MockedFunction<
          typeof performLineLevelEditing
        >
      ).mockResolvedValueOnce({
        success: false,
        error: "Line editing failed for test",
        data: null,
        stats: null,
      } as any);

      // Create a processor with error-prone configuration
      const errorProneProcessor: any = new AgenticCombinationStageProcessor({
        granularEditingIterations: 1,
        lineEditingIterations: 1,
        agenticEditingEnabled: true,
      });

      await errorProneProcessor.process(mockContext, mockDependencies);

      // Should have sent error progress for failed granular iteration with i18n keys
      expect(
        mockDependencies.progressManager.sendSubStepProgress
      ).toHaveBeenCalledWith(
        1, // First sub-task
        2, // Total sub-tasks (1 granular + 1 line)
        "granularEditFailed:1:1", // i18n key format
        "Granular edit 1/1",
        -2 // Error state
      );

      // Should have sent error progress for failed line iteration with i18n keys
      expect(
        mockDependencies.progressManager.sendSubStepProgress
      ).toHaveBeenCalledWith(
        2, // Second sub-task
        2, // Total sub-tasks
        "lineEditFailed:1:1", // i18n key format
        "Line edit 1/1",
        -2 // Error state
      );
    });

    test("should handle abort signals during processing", async () => {
      const abortingDependencies: any = {
        ...mockDependencies,
        abortChecker: jest.fn(() => {
          throw new Error("Operation aborted");
        }),
      };

      await expect(
        processor.process(mockContext, abortingDependencies)
      ).rejects.toThrow("Operation aborted");
    });

    test("should validate section outputs structure", async () => {
      const invalidStructureContext: any = {
        sectionOutputs: [
          "not an object", // Invalid structure
          { title: "Valid", content: "Content" },
          {
            /* missing title and content */
          },
        ],
      };

      const result: any = await processor.process(
        invalidStructureContext,
        mockDependencies
      );

      expect(result).toBeDefined();
      expect(result.finalContent).toBeDefined();
    });
  });
});
