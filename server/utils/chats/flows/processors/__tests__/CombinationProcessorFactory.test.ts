// Mock dependencies first
jest.mock("../../../../../models/systemSettings");
jest.mock("../CombinationStageProcessor");
jest.mock("../AgenticCombinationStageProcessor");

import { CombinationProcessorFactory } from "../CombinationProcessorFactory";
import { CombinationStageProcessor } from "../CombinationStageProcessor";
import { AgenticCombinationStageProcessor } from "../AgenticCombinationStageProcessor";
import SystemSettings from "../../../../../models/systemSettings";

// Create mock constructors for the processor classes
const MockCombinationStageProcessor =
  CombinationStageProcessor as jest.MockedClass<
    typeof CombinationStageProcessor
  >;
const MockAgenticCombinationStageProcessor =
  AgenticCombinationStageProcessor as jest.MockedClass<
    typeof AgenticCombinationStageProcessor
  >;

describe("CombinationProcessorFactory", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Set up SystemSettings mock
    if (!SystemSettings.get) {
      SystemSettings.get = jest.fn();
    }

    // Reset console mocks
    jest.spyOn(console, "log").mockImplementation(() => {});
    jest.spyOn(console, "error").mockImplementation(() => {});
  });

  afterEach(() => {
    jest.mocked(console.log).mockRestore();
    jest.mocked(console.error).mockRestore();
  });

  describe("getCombinationProcessor", () => {
    it("should return regular processor when setting is 'regular'", async () => {
      jest.mocked(SystemSettings.get).mockResolvedValue({
        id: 1,
        label: "flow_main_use_combination_processor",
        value: "regular",
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      });

      const ProcessorClass: any =
        await CombinationProcessorFactory.getCombinationProcessor("main", {});

      expect(ProcessorClass).toBe(CombinationStageProcessor);
      expect(SystemSettings.get).toHaveBeenCalledWith({
        label: "flow_main_use_combination_processor",
      });
      expect(console.log).toHaveBeenCalledWith(
        "[CombinationProcessorFactory] Using regular processor for main flow"
      );
    });

    it("should return agentic processor when setting is 'agentic'", async () => {
      jest.mocked(SystemSettings.get).mockResolvedValue({
        id: 2,
        label: "flow_nomain_use_combination_processor",
        value: "agentic",
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      });

      const ProcessorClass: any =
        await CombinationProcessorFactory.getCombinationProcessor("noMain", {});

      expect(ProcessorClass).toBe(AgenticCombinationStageProcessor);
      expect(SystemSettings.get).toHaveBeenCalledWith({
        label: "flow_nomain_use_combination_processor",
      });
      expect(console.log).toHaveBeenCalledWith(
        "[CombinationProcessorFactory] Using agentic processor for noMain flow"
      );
    });

    it("should default to regular processor when setting is not found", async () => {
      jest.mocked(SystemSettings.get).mockResolvedValue(null);

      const ProcessorClass: any =
        await CombinationProcessorFactory.getCombinationProcessor(
          "referenceFiles",
          {}
        );

      expect(ProcessorClass).toBe(CombinationStageProcessor);
      expect(SystemSettings.get).toHaveBeenCalledWith({
        label: "flow_referencefiles_use_combination_processor",
      });
    });

    it("should handle errors and default to regular processor", async () => {
      jest
        .mocked(SystemSettings.get)
        .mockRejectedValue(new Error("Database error"));

      const ProcessorClass: any =
        await CombinationProcessorFactory.getCombinationProcessor("main", {});

      expect(ProcessorClass).toBe(CombinationStageProcessor);
      expect(console.error).toHaveBeenCalledWith(
        "[CombinationProcessorFactory] Error getting processor type, defaulting to regular:",
        expect.any(Error)
      );
    });

    it("should handle different flow types correctly", async () => {
      const flowTypes: any = ["main", "noMain", "referenceFiles"];

      for (const flowType of flowTypes) {
        jest.mocked(SystemSettings.get).mockResolvedValue({
          id: 3,
          label: `flow_${flowType.toLowerCase()}_use_combination_processor`,
          value: "agentic",
          createdAt: new Date(),
          lastUpdatedAt: new Date(),
        });

        await CombinationProcessorFactory.getCombinationProcessor(
          flowType as any,
          {}
        );

        expect(SystemSettings.get).toHaveBeenCalledWith({
          label: `flow_${flowType.toLowerCase()}_use_combination_processor`,
        });
      }
    });
  });

  describe("createDynamicProcessor", () => {
    it("should create a dynamic processor class", () => {
      const DynamicProcessor: any =
        CombinationProcessorFactory.createDynamicProcessor("main");

      expect(DynamicProcessor).toBeDefined();
      expect(typeof DynamicProcessor).toBe("function");

      const instance: any = new DynamicProcessor({ test: "option" });
      expect(instance["flowType"]).toBe("main");
      expect(instance["actualProcessor"]).toBeNull();
    });

    it("should initialize actual processor on first process call", async () => {
      jest.mocked(SystemSettings.get).mockResolvedValue({
        id: 4,
        label: "flow_main_use_combination_processor",
        value: "agentic",
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      });

      const DynamicProcessor: any =
        CombinationProcessorFactory.createDynamicProcessor("main");
      const instance: any = new DynamicProcessor({ temperature: 0.5 });

      const mockContext: any = { sectionOutputs: [] };
      const mockDependencies: any = {};

      // Mock the actual processor
      const mockProcess: any = jest
        .fn()
        .mockResolvedValue({ finalContent: "test" });
      MockAgenticCombinationStageProcessor.mockImplementation(() => {
        const mockProcessor: any = {
          process: mockProcess,
        };
        return mockProcessor;
      });

      await instance.process(mockContext, mockDependencies);

      expect(instance["actualProcessor"]).toBeDefined();
      expect(mockProcess).toHaveBeenCalledWith(mockContext, mockDependencies);
    });

    it("should reuse initialized processor on subsequent calls", async () => {
      jest.mocked(SystemSettings.get).mockResolvedValue({
        id: 5,
        label: "flow_main_use_combination_processor",
        value: "regular",
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      });

      const DynamicProcessor: any =
        CombinationProcessorFactory.createDynamicProcessor("noMain");
      const instance: any = new DynamicProcessor();

      const mockProcess: any = jest
        .fn()
        .mockResolvedValue({ finalContent: "test" });
      MockCombinationStageProcessor.mockImplementation(() => {
        const mockProcessor: any = {
          process: mockProcess,
        };
        return mockProcessor;
      });

      // First call
      await instance.process({}, {});
      const firstProcessor: any = instance["actualProcessor"];

      // Second call
      await instance.process({}, {});
      const secondProcessor: any = instance["actualProcessor"];

      expect(firstProcessor).toBe(secondProcessor);
      expect(mockProcess).toHaveBeenCalledTimes(2);
    });

    it("should delegate validateInputs to actual processor", () => {
      const DynamicProcessor: any =
        CombinationProcessorFactory.createDynamicProcessor("main");
      const instance: any = new DynamicProcessor();

      const mockValidateInputs: any = jest.fn().mockReturnValue(true);
      instance["actualProcessor"] = {
        validateInputs: mockValidateInputs,
      };

      const context: any = { sectionOutputs: [] };
      const result: any = instance.validateInputs(context);

      expect(mockValidateInputs).toHaveBeenCalledWith(context);
      expect(result).toBe(true);
    });

    it("should use default validation when processor not initialized", () => {
      const DynamicProcessor: any =
        CombinationProcessorFactory.createDynamicProcessor("main");
      const instance: any = new DynamicProcessor();

      // Valid context
      let result = instance.validateInputs({
        sectionOutputs: [{ title: "Test" }],
      });
      expect(result).toBe(true);

      // Invalid context - no sectionOutputs
      result = instance.validateInputs({});
      expect(result).toBe(false);

      // Invalid context - empty array
      result = instance.validateInputs({ sectionOutputs: [] });
      expect(result).toBe(false);

      // Invalid context - not an array
      result = instance.validateInputs({ sectionOutputs: "not array" });
      expect(result).toBe(false);

      // Invalid context - null
      result = instance.validateInputs(null);
      expect(result).toBe(false);

      // Invalid context - undefined
      result = instance.validateInputs(undefined);
      expect(result).toBe(false);
    });

    it("should delegate shouldSkip to actual processor", () => {
      const DynamicProcessor: any =
        CombinationProcessorFactory.createDynamicProcessor("main");
      const instance: any = new DynamicProcessor();

      const mockShouldSkip: any = jest.fn().mockReturnValue(true);
      instance["actualProcessor"] = {
        shouldSkip: mockShouldSkip,
      };

      const context: any = { sectionOutputs: [] };
      const result: any = instance.shouldSkip(context);

      expect(mockShouldSkip).toHaveBeenCalledWith(context);
      expect(result).toBe(true);
    });

    it("should return false for shouldSkip when processor not initialized", () => {
      const DynamicProcessor: any =
        CombinationProcessorFactory.createDynamicProcessor("main");
      const instance: any = new DynamicProcessor();

      const result: any = instance.shouldSkip({});
      expect(result).toBe(false);
    });
  });
});
