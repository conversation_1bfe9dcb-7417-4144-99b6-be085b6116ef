// Internal modules
import { CombinationStageProcessor } from "../CombinationStageProcessor";
import * as documentProcessingHelper from "../../../helpers/documentProcessing";
import * as responseHelper from "../../../../helpers/chat/responses";

// Mock dependencies
jest.mock("../../../helpers/documentProcessing", () => ({
  combineSectionOutputs: jest.fn(),
}));
jest.mock("../../../../helpers/chat/responses", () => ({
  writeResponseChunk: jest.fn(),
}));

const mockProgressManager: any = {
  updateStep: jest.fn(),
  sendSubStepProgress: jest.fn(),
};

const mockAbortChecker: any = jest.fn();

describe("CombinationStageProcessor", () => {
  let processor: any;
  let context: any;
  let dependencies: any;

  beforeEach(() => {
    jest.clearAllMocks();
    processor = new CombinationStageProcessor();
    context = {
      sectionOutputs: [
        { title: "Section 1", content: "Content 1" },
        { title: "Section 2", content: "Content 2" },
      ],
      metrics: {},
    };
    dependencies = {
      progressManager: mockProgressManager,
      abortChecker: mockAbortChecker,
      chatId: "test-chat-id",
      options: { response: { writableEnded: false } },
    };
  });

  describe("process", () => {
    it("should combine sections and send the final result by default", async () => {
      // Arrange
      const combinedContent: any = "Content 1\n\nContent 2";
      (
        documentProcessingHelper.combineSectionOutputs as jest.MockedFunction<
          typeof documentProcessingHelper.combineSectionOutputs
        >
      ).mockReturnValue(combinedContent);

      // Act
      const result: any = await processor.process(context, dependencies);

      // Assert
      expect(
        documentProcessingHelper.combineSectionOutputs
      ).toHaveBeenCalledWith(context.sectionOutputs);
      expect(result.finalContent).toContain(combinedContent);
      expect(result.finalContent).toContain(
        "<!-- Document Generation Metadata -->"
      ); // Metadata is on by default
      expect(result.metrics.combination).toBeDefined();
      expect(responseHelper.writeResponseChunk).toHaveBeenCalled();
    });

    it("should not include metadata if option is false", async () => {
      // Arrange
      processor = new CombinationStageProcessor({ includeMetadata: false });
      (
        documentProcessingHelper.combineSectionOutputs as jest.MockedFunction<
          typeof documentProcessingHelper.combineSectionOutputs
        >
      ).mockReturnValue("Combined");

      // Act
      const result: any = await processor.process(context, dependencies);

      // Assert
      expect(result.finalContent).toBe("Combined");
      expect(result.finalContent).not.toContain("<!--");
    });

    it("should not send final result if option is false", async () => {
      // Arrange
      processor = new CombinationStageProcessor({ sendFinalResult: false });
      (
        documentProcessingHelper.combineSectionOutputs as jest.MockedFunction<
          typeof documentProcessingHelper.combineSectionOutputs
        >
      ).mockReturnValue("Combined");

      // Act
      await processor.process(context, dependencies);

      // Assert
      expect(responseHelper.writeResponseChunk).not.toHaveBeenCalled();
    });

    it("should throw an error if no section outputs are available", async () => {
      // Arrange
      context.sectionOutputs = [];

      // Act & Assert
      await expect(processor.process(context, dependencies)).rejects.toThrow(
        "No section outputs available for combination"
      );
    });

    it("should throw an error if combined content is empty", async () => {
      // Arrange
      (
        documentProcessingHelper.combineSectionOutputs as jest.MockedFunction<
          typeof documentProcessingHelper.combineSectionOutputs
        >
      ).mockReturnValue("");

      // Act & Assert
      await expect(processor.process(context, dependencies)).rejects.toThrow(
        "Document combination failed: Failed to generate final document content"
      );
    });
  });

  describe("validateInputs", () => {
    it("should return true if sectionOutputs exist", () => {
      expect(processor.validateInputs(context)).toBe(true);
    });

    it("should return false if sectionOutputs are missing or empty", () => {
      expect(processor.validateInputs({ sectionOutputs: [] })).toBe(false);
      expect(processor.validateInputs({})).toBe(false);
    });
  });

  describe("shouldSkip", () => {
    it("should return false if finalContent does not exist", () => {
      expect(processor.shouldSkip(context)).toBe(false);
    });

    it("should return false even if finalContent already exists", () => {
      const contextWithContent: any = {
        sectionOutputs: [{ title: "Section 1", content: "Content" }],
        finalContent: "Final document content",
      };
      expect(processor.shouldSkip(contextWithContent)).toBe(false);
    });

    it("should return true if no section outputs are available", () => {
      expect(processor.shouldSkip({ sectionOutputs: [] })).toBe(true);
      expect(processor.shouldSkip({})).toBe(true);
    });

    it("should return true if explicitly configured to skip", () => {
      const processorWithSkip: any = new CombinationStageProcessor({
        skipCombination: true,
      });
      expect(processorWithSkip.shouldSkip(context)).toBe(true);
    });
  });
});
