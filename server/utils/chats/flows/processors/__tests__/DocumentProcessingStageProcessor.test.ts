// Mock dependencies
jest.mock("../../../helpers/documentProcessing", () => ({
  generateDocumentDescriptionIterative: jest.fn(),
  generateDocumentRelevanceIterative: jest.fn(),
  saveDocumentDescriptions: jest.fn(),
}));

import { DocumentProcessingStageProcessor } from "../DocumentProcessingStageProcessor";
import {
  generateDocumentDescriptionIterative,
  generateDocumentRelevanceIterative,
  saveDocumentDescriptions,
} from "../../../helpers/documentProcessing";
import { FlowContext } from "../../../../../types/chat-flow";

// Helper function to create a valid FlowContext
function createMockFlowContext(
  overrides: Partial<FlowContext> = {}
): FlowContext {
  return {
    documents: [],
    processedDocuments: [],
    docDescriptions: [],
    sectionList: [],
    legalIssues: [],
    memos: [],
    sectionOutputs: [],
    finalContent: "",
    metrics: {},
    errors: [],
    ...overrides,
  };
}

const mockProgressManager: any = {
  updateStep: jest.fn(),
  sendSubStepProgress: jest.fn(),
};

const mockLlmConnector: any = {
  metrics: { lastCompletionTokens: 100 },
};

const mockTokenTracker: any = {
  getTokenUsageForStage: jest.fn(),
};

const mockLlmCoordinator: any = {
  getConnector: () => mockLlmConnector,
  getContextManager: () => ({}),
  getTokenTracker: () => mockTokenTracker,
  getTemperature: () => 0.5,
};

const mockAbortChecker: any = jest.fn();

describe("DocumentProcessingStageProcessor", () => {
  let processor: DocumentProcessingStageProcessor;
  let context: FlowContext;
  let dependencies: any;

  beforeEach(() => {
    jest.clearAllMocks();
    processor = new DocumentProcessingStageProcessor();
    context = createMockFlowContext({
      documents: [
        {
          id: "doc1",
          fileName: "doc1.txt",
          displayName: "Document 1",
          content: "Content 1",
        },
        {
          id: "doc2",
          fileName: "doc2.txt",
          displayName: "Document 2",
          content: "Content 2",
        },
      ],
      allPrompts: {},
      metrics: {},
    });
    dependencies = {
      progressManager: mockProgressManager,
      llmCoordinator: mockLlmCoordinator,
      abortChecker: mockAbortChecker,
      legalTask: "Test legal task",
      chatId: "test-chat-id",
    };
  });

  describe("process", () => {
    it("should process documents successfully, checking relevance", async () => {
      // Arrange
      jest
        .mocked(generateDocumentDescriptionIterative)
        .mockResolvedValue("Description");
      jest.mocked(generateDocumentRelevanceIterative).mockResolvedValue(true);
      jest.mocked(mockTokenTracker.getTokenUsageForStage).mockReturnValue(50);
      jest.mocked(saveDocumentDescriptions).mockReturnValue("test-path");

      // Act
      const result: any = await processor.process(context, dependencies);

      // Assert
      expect(generateDocumentDescriptionIterative).toHaveBeenCalledTimes(2);
      expect(generateDocumentRelevanceIterative).toHaveBeenCalledTimes(2);
      expect(saveDocumentDescriptions).toHaveBeenCalled();
      expect(result?.processedDocuments?.length).toBe(2);
      expect(result.processedDocuments?.[0]?.metadata?.Description).toBe(
        "Description"
      );
      expect(result?.docDescriptions?.length).toBe(2);
      expect(result?.metrics?.documentProcessing).toBeDefined();
    });

    it("should skip relevance check if option is true", async () => {
      // Arrange
      processor = new DocumentProcessingStageProcessor({
        skipRelevanceCheck: true,
      });
      jest
        .mocked(generateDocumentDescriptionIterative)
        .mockResolvedValue("Description");
      jest.mocked(mockTokenTracker.getTokenUsageForStage).mockReturnValue(50);
      jest.mocked(saveDocumentDescriptions).mockReturnValue("test-path");

      // Act
      const result: any = await processor.process(context, dependencies);

      // Assert
      expect(generateDocumentDescriptionIterative).toHaveBeenCalledTimes(2);
      expect(generateDocumentRelevanceIterative).not.toHaveBeenCalled();
      expect(result?.processedDocuments?.length).toBe(2);
      expect(result.processedDocuments?.[0]?.metadata?.IsRelevant).toBe(true);
    });

    it("should filter out irrelevant documents", async () => {
      // Arrange
      jest
        .mocked(generateDocumentDescriptionIterative)
        .mockResolvedValue("Description");
      jest
        .mocked(generateDocumentRelevanceIterative)
        .mockResolvedValueOnce(true)
        .mockResolvedValueOnce(false);
      jest.mocked(mockTokenTracker.getTokenUsageForStage).mockReturnValue(50);
      jest.mocked(saveDocumentDescriptions).mockReturnValue("test-path");

      // Act
      const result: any = await processor.process(context, dependencies);

      // Assert
      expect(result?.processedDocuments?.length).toBe(1);
      expect(result?.docDescriptions?.length).toBe(2); // Both get descriptions, only relevant ones are processed
    });

    it("should continue processing if one document fails", async () => {
      // Arrange
      jest
        .mocked(generateDocumentDescriptionIterative)
        .mockRejectedValueOnce(new Error("Test error"))
        .mockResolvedValueOnce("Description");
      jest.mocked(generateDocumentRelevanceIterative).mockResolvedValue(true);
      jest.mocked(mockTokenTracker.getTokenUsageForStage).mockReturnValue(50);
      jest.mocked(saveDocumentDescriptions).mockReturnValue("test-path");

      // Act
      const result: any = await processor.process(context, dependencies);

      // Assert
      expect(result?.processedDocuments?.length).toBe(1);
      expect(result?.docDescriptions?.length).toBe(1);
    });

    it("should throw an error if all documents fail to process", async () => {
      // Arrange
      jest
        .mocked(generateDocumentDescriptionIterative)
        .mockRejectedValue(new Error("Test error"));

      // Act & Assert
      await expect(processor.process(context, dependencies)).rejects.toThrow(
        "No documents were successfully processed"
      );
    });

    it("should throw an error if no documents are in context", async () => {
      // Arrange
      context.documents = [];

      // Act & Assert
      await expect(processor.process(context, dependencies)).rejects.toThrow(
        "No documents available for processing"
      );
    });
  });

  describe("validateInputs", () => {
    it("should return true if documents exist", () => {
      const result: any = processor.validateInputs(context);
      expect(result).toBe(true);
    });

    it("should return false if documents are missing or empty", () => {
      expect(
        processor.validateInputs(createMockFlowContext({ documents: [] }))
      ).toBe(false);
      expect(
        processor.validateInputs(createMockFlowContext({ documents: [] }))
      ).toBe(false);
      expect(
        processor.validateInputs(
          createMockFlowContext({ documents: undefined })
        )
      ).toBe(false);
    });
  });

  describe("shouldSkip", () => {
    it("should return false if no existing docDescriptions", () => {
      const result: any = processor.shouldSkip(context);
      expect(result).toBe(false);
    });

    it("should return false even if docDescriptions already exist", () => {
      context.docDescriptions = [
        {
          "Doc Name": "test",
          DisplayName: "test",
          Description: "existing description",
        },
      ];
      const result: any = processor.shouldSkip(context);
      expect(result).toBe(false);
    });

    it("should return true if no documents available", () => {
      context.documents = [];
      const result: any = processor.shouldSkip(context);
      expect(result).toBe(true);
    });

    it("should return true if explicitly configured to skip", () => {
      processor = new DocumentProcessingStageProcessor({
        skipDocumentProcessing: true,
      });
      const result: any = processor.shouldSkip(context);
      expect(result).toBe(true);
    });
  });
});
