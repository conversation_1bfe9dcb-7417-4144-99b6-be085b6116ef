// Mock the document processing helper
jest.mock("../../../helpers/documentProcessing", () => ({
  processIterativeSectionDraftingList: jest.fn(),
}));

import { IterativeSectionDraftingProcessor } from "../IterativeSectionDraftingProcessor";
import { processIterativeSectionDraftingList } from "../../../helpers/documentProcessing";
import {
  FlowContext,
  StageDependencies,
  ProgressManager,
  LLMCoordinator,
  ContextWindowManager,
  TokenTracker,
  WorkspaceData,
  UserData,
  WorkspaceManager,
  FlowOptions,
} from "../../../../../types/chat-flow";
import { Response } from "express";

const mockProcessIterativeSectionDraftingList = jest.mocked(
  processIterativeSectionDraftingList
);

const mockResponse: Partial<Response> = {
  write: jest.fn(),
  end: jest.fn(),
  status: jest.fn().mockReturnThis(),
  json: jest.fn().mockReturnThis(),
  send: jest.fn().mockReturnThis(),
  sendStatus: jest.fn().mockReturnThis(),
  header: jest.fn().mockReturnThis(),
  set: jest.fn().mockReturnThis(),
};

const mockProgressManager: jest.Mocked<ProgressManager> = {
  setTotalSteps: jest.fn(),
  sendProgress: jest.fn(),
  startStep: jest.fn(),
  updateStep: jest.fn(),
  completeStep: jest.fn(),
  sendError: jest.fn(),
  sendSubStepProgress: jest.fn(),
  sendFinalCompletion: jest.fn(),
  sendFinalError: jest.fn(),
  sendAbort: jest.fn(),
};

const mockLlmConnector: Record<string, unknown> = {
  model: "test-model",
  limits: {},
  embedder: {},
  defaultTemp: 0.7,
  streamingEnabled: jest.fn().mockReturnValue(false),
  promptWindowLimit: jest.fn().mockReturnValue(8000),
  customPromptWindowLimit: jest.fn().mockReturnValue(8000),
  isValidChatCompletionModel: jest.fn().mockReturnValue(true),
  constructPrompt: jest.fn(),
  getChatCompletion: jest.fn(),
  embedTextInput: jest.fn(),
  embedChunks: jest.fn(),
  compressMessages: jest.fn(),
};

const mockTokenTracker: jest.Mocked<TokenTracker> = {
  getTokenUsageForStage: jest.fn(),
  startStage: jest.fn(),
  trackLLMResponse: jest.fn(),
  getMetrics: jest.fn(),
} as unknown as jest.Mocked<TokenTracker>;

const mockContextWindowManager: jest.Mocked<ContextWindowManager> = {
  getAvailableContextWindow: jest.fn().mockReturnValue(4000),
  calculateTokenBudget: jest.fn(),
  processIteratively: jest.fn(),
} as unknown as jest.Mocked<ContextWindowManager>;

const mockWorkspaceData: WorkspaceData = {
  id: 1,
  name: "Test Workspace",
  slug: "test-workspace",
  path: "/test/path",
  vectorTag: null,
  openAiPrompt: undefined,
  pdr: undefined,
  type: undefined,
  createdAt: new Date(),
  lastUpdatedAt: new Date(),
};

const mockUserData: UserData = {
  id: 1,
  username: "testuser",
  email: "<EMAIL>",
  role: "user",
  createdAt: new Date(),
  lastUpdatedAt: new Date(),
  organizationId: 1,
  pfpFilename: "",
  suspended: 0 as any,
  seen_recovery_codes: false,
  custom_ai_userselected: false,
  custom_ai_selected_engine: "",
  economy_system_id: null,
  custom_system_prompt: "",
};

const mockWorkspaceManager: jest.Mocked<WorkspaceManager> = {
  initialize: jest.fn(),
  validateWorkspace: jest.fn(),
  ensureDocumentBuilderDirectory: jest.fn(),
  getDocumentFiles: jest.fn(),
  readDocumentFile: jest.fn(),
  readDocumentFiles: jest.fn(),
  getAllDocuments: jest.fn(),
  getWorkspacePath: jest.fn(),
  getDocumentBuilderPath: jest.fn(),
  documentExists: jest.fn(),
  getWorkspaceInfo: jest.fn(),
};

const mockFlowOptions: FlowOptions = {
  chatId: "test-chat-id",
  response: mockResponse as Response,
  workspace: mockWorkspaceData,
  user: mockUserData,
  message: "Test message",
};

const mockLlmCoordinator: jest.Mocked<LLMCoordinator> = {
  workspace: mockWorkspaceData,
  options: {},
  connector: null,
  contextManager: null,
  tokenTracker: null,
  temperature: null,
  initialize: jest.fn(),
  getConnector: jest.fn().mockReturnValue(mockLlmConnector),
  getContextManager: jest.fn().mockReturnValue(mockContextWindowManager),
  getTokenTracker: jest.fn().mockReturnValue(mockTokenTracker),
  getTemperature: jest.fn().mockReturnValue(0.7),
  getAvailableContextWindow: jest.fn().mockReturnValue(4000),
  calculateTokenBudget: jest.fn(),
  getChatCompletion: jest.fn(),
  chatCompletion: jest.fn(),
  compressMessages: jest.fn(),
  getMetrics: jest.fn(),
  generateTokenReport: jest.fn(),
  resetMetrics: jest.fn(),
  getProviderInfo: jest.fn(),
} as jest.Mocked<LLMCoordinator>;

// Helper function to create a valid FlowContext
function createMockFlowContext(
  overrides: Partial<FlowContext> = {}
): FlowContext {
  return {
    documents: [],
    processedDocuments: [],
    docDescriptions: [],
    sectionList: [],
    legalIssues: [],
    memos: [],
    sectionOutputs: [],
    finalContent: "",
    metrics: {},
    errors: [],
    ...overrides,
  };
}

const mockAbortChecker: jest.MockedFunction<() => void> = jest.fn();

describe("IterativeSectionDraftingProcessor", () => {
  let processor: IterativeSectionDraftingProcessor;
  let context: FlowContext;
  let dependencies: StageDependencies;

  beforeEach(() => {
    jest.clearAllMocks();
    processor = new IterativeSectionDraftingProcessor();

    context = createMockFlowContext({
      sectionList: [
        { sectionNumber: 1, title: "Section 1", relevantDocumentNames: [] },
        { sectionNumber: 2, title: "Section 2", relevantDocumentNames: [] },
      ],
      processedDocuments: [],
      allPrompts: {},
      metrics: {},
    });

    dependencies = {
      progressManager: mockProgressManager,
      llmCoordinator: mockLlmCoordinator,
      contextWindowManager: mockContextWindowManager,
      tokenTracker: mockTokenTracker,
      abortChecker: mockAbortChecker,
      legalTask: "Test legal task",
      chatId: "test-chat-id",
      workspace: mockWorkspaceData,
      flowType: "main",
      workspaceManager: mockWorkspaceManager,
      user: mockUserData,
      customInstructions: "",
      options: mockFlowOptions,
    };
  });

  describe("process", () => {
    it("should draft all sections successfully", async () => {
      // Arrange
      const mockDraftedSections: Array<{
        title: string;
        index_number: number;
        description: string;
        relevant_documents: string[];
        legal_issues_to_address: string[];
        draftedContent: string;
        tokenUsage: {
          totalTokens: number;
          iterations: number;
          isIterative: boolean;
        };
      }> = [
        {
          title: "Section 1",
          index_number: 1,
          description: "Description 1",
          relevant_documents: ["doc1"],
          legal_issues_to_address: ["issue1"],
          draftedContent: "Draft 1",
          tokenUsage: {
            totalTokens: 100,
            iterations: 1,
            isIterative: false,
          },
        },
        {
          title: "Section 2",
          index_number: 2,
          description: "Description 2",
          relevant_documents: ["doc2"],
          legal_issues_to_address: ["issue2"],
          draftedContent: "Draft 2",
          tokenUsage: {
            totalTokens: 150,
            iterations: 1,
            isIterative: false,
          },
        },
      ];

      mockProcessIterativeSectionDraftingList.mockResolvedValue(
        mockDraftedSections
      );

      // Act
      const result: Partial<FlowContext> = await processor.process(
        context,
        dependencies
      );

      // Assert
      expect(mockProcessIterativeSectionDraftingList).toHaveBeenCalled();

      // The sectionList is converted to simplified format
      expect(result.sectionList).toEqual([
        {
          sectionNumber: 1,
          title: "Section 1",
          relevantDocumentNames: ["doc1"],
          identifiedLegalIssues: [
            {
              Issue: "issue1",
              WORKSPACE_SLUG_FOR_LEGALDATA: "test-workspace",
            },
          ],
        },
        {
          sectionNumber: 2,
          title: "Section 2",
          relevantDocumentNames: ["doc2"],
          identifiedLegalIssues: [
            {
              Issue: "issue2",
              WORKSPACE_SLUG_FOR_LEGALDATA: "test-workspace",
            },
          ],
        },
      ]);

      // The sectionOutputs contain the drafts
      expect(result.sectionOutputs).toEqual([
        {
          title: "Section 1",
          content: "Draft 1",
          sectionNumber: 1,
        },
        {
          title: "Section 2",
          content: "Draft 2",
          sectionNumber: 2,
        },
      ]);

      expect(result?.metrics?.sectionDrafting).toBeDefined();
    });

    it("should throw an error if section drafting fails", async () => {
      // Arrange
      mockProcessIterativeSectionDraftingList.mockRejectedValue(
        new Error("Drafting failed")
      );

      // Act & Assert
      await expect(processor.process(context, dependencies)).rejects.toThrow(
        "Drafting failed"
      );
    });

    it("should re-throw user abort errors", async () => {
      // Arrange
      mockProcessIterativeSectionDraftingList.mockRejectedValue(
        new Error("Process aborted by user")
      );

      // Act & Assert
      await expect(processor.process(context, dependencies)).rejects.toThrow(
        "Process aborted by user"
      );
    });

    it("should throw an error if no sections are available", async () => {
      // Arrange
      context.sectionList = [];

      // Act & Assert
      await expect(processor.process(context, dependencies)).rejects.toThrow(
        "No sections available for drafting"
      );
    });
  });

  describe("validateInputs", () => {
    it("should return true if sectionList exists", () => {
      const result: boolean = processor.validateInputs(context);
      expect(result).toBe(true);
    });

    it("should return false if sectionList is missing or empty", () => {
      expect(
        processor.validateInputs(createMockFlowContext({ sectionList: [] }))
      ).toBe(false);
      expect(
        processor.validateInputs(createMockFlowContext({ sectionList: [] }))
      ).toBe(false);
      expect(
        processor.validateInputs(
          createMockFlowContext({ sectionList: undefined })
        )
      ).toBe(false);
    });
  });

  describe("shouldSkip", () => {
    it("should return false if no existing drafts or outputs", () => {
      const result: boolean = processor.shouldSkip(context);
      expect(result).toBe(false);
    });

    it("should return false even if sections have existing drafts", () => {
      context.sectionList = [
        {
          sectionNumber: 1,
          title: "Section 1",
          relevantDocumentNames: [],
          draftedContent: "existing",
        },
      ];
      const result: boolean = processor.shouldSkip(context);
      expect(result).toBe(false);
    });

    it("should return false even if sectionOutputs already exist", () => {
      context.sectionOutputs = [
        { sectionNumber: 1, title: "Section 1", content: "existing" },
      ];
      const result: boolean = processor.shouldSkip(context);
      expect(result).toBe(false);
    });

    it("should return true if no sections are available", () => {
      context.sectionList = [];
      const result: boolean = processor.shouldSkip(context);
      expect(result).toBe(true);
    });

    it("should return true if explicitly configured to skip", () => {
      processor = new IterativeSectionDraftingProcessor({
        skipSectionDrafting: true,
      });
      const result: boolean = processor.shouldSkip(context);
      expect(result).toBe(true);
    });
  });
});
