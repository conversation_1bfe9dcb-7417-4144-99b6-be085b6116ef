import { LegalIssueIdentificationProcessor } from "../LegalIssueIdentificationProcessor";
import "fs/promises";

// Mock dependencies
jest.mock("fs/promises", () => ({
  writeFile: jest.fn(),
  mkdir: jest.fn(),
}));

const mockProgressManager: any = {
  updateStep: jest.fn(),
  sendSubStepProgress: jest.fn(),
};

// Mock data
const mockIssues: any = [
  {
    Issue: "Test Issue",
    WORKSPACE_SLUG_FOR_LEGALDATA: "test-slug",
  },
];

const mockLlmConnector: any = {
  compressMessages: jest.fn((messages: any) => Promise.resolve(messages)),
  getChatCompletion: jest.fn().mockResolvedValue({
    textResponse: JSON.stringify(mockIssues),
  }),
};

const mockLlmCoordinator: any = {
  getConnector: jest.fn(() => mockLlmConnector),
  compressMessages: jest.fn((messages: any) => Promise.resolve(messages)),
  getChatCompletion: jest.fn().mockResolvedValue({
    textResponse: JSON.stringify(mockIssues),
  }),
  getTemperature: jest.fn(() => 0.5),
  metrics: { lastCompletionTokens: 100 },
  getMetrics: jest.fn(() => ({
    connector: { lastCompletionTokens: 120 },
  })),
};

const mockAbortChecker: any = jest.fn();

describe("LegalIssueIdentificationProcessor", () => {
  let processor: any;
  let context: any;
  let dependencies: any;

  beforeEach(() => {
    jest.clearAllMocks();
    processor = new LegalIssueIdentificationProcessor();
    context = {
      sectionList: [
        {
          index_number: 1,
          title: "Section 1",
          relevantDocumentNames: ["doc1.json"],
        },
        {
          index_number: 2,
          title: "Section 2",
          relevantDocumentNames: ["doc2.json"],
        },
      ],
      metrics: {},
    };
    dependencies = {
      progressManager: mockProgressManager,
      llmCoordinator: mockLlmCoordinator,
      abortChecker: mockAbortChecker,
      legalTask: "Test legal task",
      chatId: "test-chat-id",
      prompts: {
        CURRENT_DEFAULT_SECTION_LEGAL_ISSUES: {
          USER_PROMPT: "user prompt",
          SYSTEM_PROMPT: "system prompt",
        },
      },
    };
  });

  describe("process", () => {
    it("should identify legal issues for all sections successfully", async () => {
      // Arrange
      (
        mockLlmCoordinator.getChatCompletion as jest.MockedFunction<
          typeof mockLlmCoordinator.getChatCompletion
        >
      ).mockResolvedValue({
        textResponse: JSON.stringify(mockIssues),
      });

      // Act
      const result: any = await processor.process(context, dependencies);

      // Assert
      expect(mockLlmCoordinator.getChatCompletion).toHaveBeenCalledTimes(2);
      expect(result.sectionList.length).toBe(2);
      expect(result.sectionList[0].identifiedLegalIssues).toEqual(mockIssues);
      expect(result.legalIssues.length).toBe(1); // Unique issues
      expect(result.metrics.issueIdentification).toBeDefined();
    });

    it("should handle a section analysis failure gracefully", async () => {
      // Arrange
      mockLlmCoordinator.getChatCompletion
        .mockResolvedValueOnce({
          textResponse: JSON.stringify(mockIssues),
        })
        .mockRejectedValueOnce(new Error("LLM failed"));

      // Act
      const result: any = await processor.process(context, dependencies);

      // Assert
      expect(result.sectionList.length).toBe(2);
      expect(result.sectionList[0].identifiedLegalIssues).toEqual(mockIssues);
      expect(result.sectionList[1].error).toContain(
        "Failed to identify legal issues"
      );
      expect(result.legalIssues.length).toBe(2); // 1 from success + 1 error message
    });

    it("should throw an error if no sections are available", async () => {
      // Arrange
      context.sectionList = [];

      // Act & Assert
      await expect(processor.process(context, dependencies)).rejects.toThrow(
        "No sections available for legal issue identification"
      );
    });
  });

  describe("validateInputs", () => {
    it("should return true if sectionList exists", () => {
      expect(processor.validateInputs(context)).toBe(true);
    });

    it("should return false if sectionList is missing or empty", () => {
      expect(processor.validateInputs({ sectionList: [] })).toBe(false);
      expect(processor.validateInputs({})).toBe(false);
    });
  });

  describe("shouldSkip", () => {
    it("should return false if no existing identified issues", () => {
      expect(processor.shouldSkip(context)).toBe(false);
    });

    it("should return false even if sections have existing identified issues", () => {
      const contextWithIssues: any = {
        sectionList: [
          { title: "Section 1", identifiedLegalIssues: ["Issue 1"] },
          { title: "Section 2" },
        ],
      };
      expect(processor.shouldSkip(contextWithIssues)).toBe(false);
    });

    it("should return false even if sections have existing identified issues as objects", () => {
      const contextWithIssueObjects: any = {
        sectionList: [
          {
            title: "Section 1",
            identifiedLegalIssues: [
              { Issue: "Issue 1", WORKSPACE_SLUG_FOR_LEGALDATA: "workspace1" },
            ],
          },
        ],
      };
      expect(processor.shouldSkip(contextWithIssueObjects)).toBe(false);
    });

    it("should return true if explicitly configured to skip", () => {
      const processorWithSkip: any = new LegalIssueIdentificationProcessor({
        skipIssueIdentification: true,
      });
      expect(processorWithSkip.shouldSkip(context)).toBe(true);
    });
  });
});
