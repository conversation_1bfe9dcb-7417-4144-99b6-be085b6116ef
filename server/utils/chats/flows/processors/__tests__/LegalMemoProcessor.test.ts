import LegalMemoProcessor from "../LegalMemoProcessor";
import { Workspace } from "../../../../../models/workspace";
import { generateLegalMemo } from "../../../../helpers/legalMemo";
import * as fs from "fs";

// Mock dependencies
jest.mock("../../../../../models/workspace");
jest.mock("../../../../helpers/legalMemo", () => ({
  generateLegalMemo: jest.fn(),
}));
jest.mock("fs/promises", () => ({
  writeFile: jest.fn(),
  mkdir: jest.fn(),
}));
jest.mock("fs", () => ({
  existsSync: jest.fn().mockReturnValue(true),
  mkdirSync: jest.fn(),
  writeFileSync: jest.fn(),
}));

const mockProgressManager: any = {
  updateStep: jest.fn(),
  sendSubStepProgress: jest.fn(),
  completeStep: jest.fn(),
  sendError: jest.fn(),
};

const mockLlmCoordinator: any = {
  getConnector: () => ({}),
  getTemperature: () => 0.3,
};

const mockAbortChecker: any = jest.fn();

describe("LegalMemoProcessor", () => {
  let processor: any;
  let context: any;
  let dependencies: any;
  let originalConsoleLog: typeof console.log;
  let originalConsoleWarn: typeof console.warn;
  let originalConsoleError: typeof console.error;

  beforeAll(() => {
    // Mock console methods to suppress verbose output
    originalConsoleLog = console.log;
    originalConsoleWarn = console.warn;
    originalConsoleError = console.error;
    console.log = jest.fn();
    console.warn = jest.fn();
    console.error = jest.fn();
  });

  afterAll(() => {
    // Restore console methods
    console.log = originalConsoleLog;
    console.warn = originalConsoleWarn;
    console.error = originalConsoleError;
  });

  afterEach(() => {
    // Restore all mocks after each test to ensure isolation
    jest.restoreAllMocks();
  });

  beforeEach(() => {
    jest.clearAllMocks();

    // Suppress console output from LegalMemoProcessor during tests
    jest.spyOn(LegalMemoProcessor.prototype, "log").mockImplementation(() => {
      // Suppress all logging output during tests
      return;
    });

    processor = new LegalMemoProcessor();
    context = {
      sectionList: [
        {
          title: "Section 1",
          identifiedLegalIssues: [
            { Issue: "Issue 1", WORKSPACE_SLUG_FOR_LEGALDATA: "lqa-1" },
          ],
        },
        {
          title: "Section 2",
          identifiedLegalIssues: [
            { Issue: "Issue 2", WORKSPACE_SLUG_FOR_LEGALDATA: "lqa-2" },
          ],
        },
        {
          title: "Section 3",
          identifiedLegalIssues: [
            { Issue: "Issue 1", WORKSPACE_SLUG_FOR_LEGALDATA: "lqa-1" },
          ], // Duplicate issue
        },
      ],
      metrics: {},
    };
    dependencies = {
      progressManager: mockProgressManager,
      llmCoordinator: mockLlmCoordinator,
      abortChecker: mockAbortChecker,
      legalTask: "Test legal task",
      chatId: "test-chat-id",
      workspace: { slug: "current-ws", name: "Current Workspace" },
      prompts: {
        legalMemoSystemPrompt: "System prompt for testing",
        legalMemoUserPrompt: "User prompt for testing: {issueText}",
      },
    };
  });

  describe("process", () => {
    it("should generate memos for unique legal issues successfully", async () => {
      // Arrange
      // --- WORKAROUND for Jest cache/build issue ---
      jest
        .spyOn(LegalMemoProcessor.prototype, "shouldSkip")
        .mockReturnValue(false);
      // ---
      const mockWorkspaces: any = [
        { slug: "lqa-1", name: "LQA Workspace 1" },
        { slug: "lqa-2", name: "LQA Workspace 2" },
      ];
      (
        Workspace.where as jest.MockedFunction<typeof Workspace.where>
      ).mockResolvedValue(mockWorkspaces);
      (
        generateLegalMemo as jest.MockedFunction<typeof generateLegalMemo>
      ).mockResolvedValue({
        memo: "Memo content for testing",
        tokenCount: 100,
        sources: [],
        tokenUsage: {
          prompt_tokens: 50,
          completion_tokens: 50,
          total_tokens: 100,
        },
      });

      // Act
      const result: any = await processor.process(context, dependencies);

      // Assert
      expect(Workspace.where).toHaveBeenCalledWith({ type: "legal-qa" });
      expect(generateLegalMemo).toHaveBeenCalledTimes(2);
      expect(result.memos.length).toBe(2);
      expect(result.memoIndex.length).toBe(2);
      expect(result.sectionList[0].relevantMemos).toBeDefined();
      expect(result.sectionList[0].relevantMemos.length).toBeGreaterThan(0);
      expect(result.sectionList[2].relevantMemos).toBeDefined();
      expect(result.sectionList[2].relevantMemos[0].memoFileName).toEqual(
        result.sectionList[0].relevantMemos[0].memoFileName
      ); // Check for memo reference sharing
      expect(fs.writeFileSync).toHaveBeenCalled(); // saveMemoArtifacts
    });

    it("should skip memo generation if no legal issues are identified", async () => {
      // Arrange
      // --- WORKAROUND for Jest cache/build issue ---
      jest
        .spyOn(LegalMemoProcessor.prototype, "shouldSkip")
        .mockReturnValue(true);
      // ---
      context.sectionList = [{ title: "Section 1", identifiedLegalIssues: [] }];

      // Act
      const result: any = await processor.process(context, dependencies);

      // Assert
      expect(generateLegalMemo).not.toHaveBeenCalled();
      expect(result.memos.length).toBe(0);
      expect(result.sectionList[0].relevantMemos).toBeUndefined();
    });

    it("should handle memo generation failure with retry", async () => {
      // Arrange
      // --- WORKAROUND for Jest cache/build issue ---
      jest
        .spyOn(LegalMemoProcessor.prototype, "shouldSkip")
        .mockReturnValue(false);
      // ---
      (
        Workspace.where as jest.MockedFunction<typeof Workspace.where>
      ).mockResolvedValue([{ slug: "lqa-1", name: "LQA 1" }] as any);

      // First call returns null (simulating parse failure), second call succeeds
      (generateLegalMemo as jest.MockedFunction<typeof generateLegalMemo>)
        .mockResolvedValueOnce(null as any) // First attempt returns null (simulating parse failure)
        .mockResolvedValueOnce({
          memo: "Success memo content",
          tokenCount: 100,
          sources: [],
          tokenUsage: {
            prompt_tokens: 50,
            completion_tokens: 50,
            total_tokens: 100,
          },
        } as any);

      processor = new LegalMemoProcessor({ maxRetries: 2, retryDelay: 10 });
      context.sectionList = [
        { title: "S1", identifiedLegalIssues: [{ Issue: "I1" }] },
      ];

      // Act
      const result: any = await processor.process(context, dependencies);

      // Assert
      expect(generateLegalMemo).toHaveBeenCalledTimes(2); // 1 fail + 1 success
      expect(result.memos.length).toBe(1);
      expect(result.memos[0].content).toBeDefined();
    });

    it("should record an error if memo generation fails all retries", async () => {
      // Arrange
      // --- WORKAROUND for Jest cache/build issue ---
      jest
        .spyOn(LegalMemoProcessor.prototype, "shouldSkip")
        .mockReturnValue(false);
      // ---
      (
        Workspace.where as jest.MockedFunction<typeof Workspace.where>
      ).mockResolvedValue([{ slug: "lqa-1", name: "LQA 1" }] as any);

      // Mock to always return null (simulating consistent parse failures)
      (
        generateLegalMemo as jest.MockedFunction<typeof generateLegalMemo>
      ).mockResolvedValue(null as any);

      processor = new LegalMemoProcessor({ maxRetries: 2, retryDelay: 10 });
      context.sectionList = [
        { title: "S1", identifiedLegalIssues: [{ Issue: "I1" }] },
      ];

      // Act
      const result: any = await processor.process(context, dependencies);

      // Assert
      expect(generateLegalMemo).toHaveBeenCalledTimes(2); // 2 total attempts
      expect(result.memos.length).toBe(0); // No successful memos
      expect(result.memoIndex.length).toBe(1); // But should have an error entry
      expect(result.memoIndex[0].error).toBeDefined();
      expect(result.memoIndex[0].error).toContain(
        "Failed to generate memo after 2 attempts"
      );
    });

    it("should send progress updates at appropriate times", async () => {
      // Arrange
      jest
        .spyOn(LegalMemoProcessor.prototype, "shouldSkip")
        .mockReturnValue(false);

      (
        Workspace.where as jest.MockedFunction<typeof Workspace.where>
      ).mockResolvedValue([{ slug: "lqa-1", name: "LQA 1" }] as any);
      (
        generateLegalMemo as jest.MockedFunction<typeof generateLegalMemo>
      ).mockResolvedValue({
        memo: "Success memo content",
        tokenCount: 100,
        sources: [],
        tokenUsage: {
          prompt_tokens: 50,
          completion_tokens: 50,
          total_tokens: 100,
        },
      } as any);

      context.sectionList = [
        {
          title: "S1",
          identifiedLegalIssues: [{ Issue: "I1" }, { Issue: "I2" }],
        },
      ];

      // Act
      const result: any = await processor.process(context, dependencies);

      // Assert
      // Check initial preparation message
      expect(mockProgressManager.updateStep).toHaveBeenCalledWith(
        "Preparing legal memo generation for 2 issues...",
        { total: 2 }
      );

      // Check sub-step progress calls for each memo
      expect(mockProgressManager.sendSubStepProgress).toHaveBeenCalledWith(
        1,
        2,
        "Generating legal memo 1 of 2...",
        "I1",
        -1
      );

      expect(mockProgressManager.sendSubStepProgress).toHaveBeenCalledWith(
        1,
        2,
        "Generated memo for: I1",
        "I1",
        100
      );

      expect(mockProgressManager.sendSubStepProgress).toHaveBeenCalledWith(
        2,
        2,
        "Generating legal memo 2 of 2...",
        "I2",
        -1
      );

      expect(mockProgressManager.sendSubStepProgress).toHaveBeenCalledWith(
        2,
        2,
        "Generated memo for: I2",
        "I2",
        100
      );

      // Check final completion message
      expect(mockProgressManager.completeStep).toHaveBeenCalledWith(
        "Generated 2 legal memos successfully"
      );

      expect(result.memos.length).toBe(2);
    });
  });

  describe("validateInputs", () => {
    it("should return true even if sectionList is empty", () => {
      expect(processor.validateInputs({ sectionList: [] })).toBe(true);
      expect(processor.validateInputs({})).toBe(true);
    });
  });

  describe("shouldSkip", () => {
    it("should return false if sectionList has issues and no existing memos", () => {
      const contextWithIssues: any = {
        sectionList: [
          {
            title: "Section 1",
            identifiedLegalIssues: [{ Issue: "Issue 1" }],
          },
        ],
      };
      expect(processor.shouldSkip(contextWithIssues)).toBe(false);
    });

    it("should return true if sectionList is empty", () => {
      const contextEmpty: any = { sectionList: [] };
      expect(processor.shouldSkip(contextEmpty)).toBe(true);
    });

    it("should return true if no sections have identified issues", () => {
      const contextNoIssues: any = {
        sectionList: [{ title: "Section 1" }, { title: "Section 2" }],
      };
      expect(processor.shouldSkip(contextNoIssues)).toBe(true);
    });

    it("should return false even if memos already generated", () => {
      const contextWithMemos: any = {
        sectionList: [
          {
            title: "Section 1",
            identifiedLegalIssues: [{ Issue: "Issue 1" }],
          },
        ],
        memos: [{ issueText: "Issue 1", fileName: "memo1.json" }],
      };
      expect(processor.shouldSkip(contextWithMemos)).toBe(false);
    });

    it("should return true if explicitly configured to skip", () => {
      const processorWithSkip: any = new LegalMemoProcessor({
        skipMemoGeneration: true,
      });
      const contextWithIssues: any = {
        sectionList: [
          {
            title: "Section 1",
            identifiedLegalIssues: [{ Issue: "Issue 1" }],
          },
        ],
      };
      expect(processorWithSkip.shouldSkip(contextWithIssues)).toBe(true);
    });
  });
});
