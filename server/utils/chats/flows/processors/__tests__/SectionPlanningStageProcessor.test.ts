/// <reference types="jest" />

// Mock dependencies
jest.mock("../../../helpers/documentProcessing", () => ({
  generateSectionListFromSummaries: jest.fn(),
  fillTemplate: jest.fn(),
}));

jest.mock("../../../helpers/llmResponseParser", () => ({
  parseLLMJsonResponse: jest.fn(),
}));

import { SectionPlanningStageProcessor } from "../SectionPlanningStageProcessor";
import {
  generateSectionListFromSummaries,
  fillTemplate,
} from "../../../helpers/documentProcessing";
import { parseLLMJsonResponse } from "../../../helpers/llmResponseParser";
import { FlowContext } from "../../../../../types/chat-flow";

const mockProgressManager: any = {
  updateStep: jest.fn(),
  sendSubStepProgress: jest.fn(),
};

const mockLlmConnector: any = {
  getChatCompletion: jest.fn(),
  compressMessages: jest.fn(),
  metrics: { lastCompletionTokens: 100 },
};

const mockLlmCoordinator: any = {
  getConnector: () => mockLlmConnector,
  getContextManager: () => ({}),
  getTokenTracker: () => ({}),
  getTemperature: () => 0.7,
};

const mockAbortChecker: any = jest.fn();

// Helper function to create a valid FlowContext
function createMockFlowContext(overrides: any = {}): FlowContext {
  return {
    documents: [],
    processedDocuments: [],
    docDescriptions: [],
    sectionList: [],
    legalIssues: [],
    memos: [],
    sectionOutputs: [],
    finalContent: "",
    metrics: {},
    errors: [],
    ...overrides,
  };
}

describe("SectionPlanningStageProcessor", () => {
  let processor: SectionPlanningStageProcessor;
  let context: FlowContext;
  let dependencies: any;

  beforeEach(() => {
    jest.clearAllMocks();
    processor = new SectionPlanningStageProcessor();

    context = createMockFlowContext({
      documents: [
        {
          id: "doc1",
          fileName: "doc1.txt",
          displayName: "Document 1",
          content: "Content 1",
        },
      ],
      docDescriptions: ["Document 1: Desc 1"],
      allPrompts: {
        CURRENT_DEFAULT_SECTION_LIST_FROM_SUMMARIES: {
          SYSTEM_PROMPT: "Generate sections from summaries",
          USER_PROMPT: "Task: {task}\nSummaries: {summaries}",
        },
        CURRENT_DEFAULT_SECTION_LIST_FROM_MAIN: {
          SYSTEM_PROMPT: "Generate sections from main document",
          USER_PROMPT: "Task: {task}\nContent: {content}",
        },
      },
      metrics: {},
    });

    dependencies = {
      progressManager: mockProgressManager,
      llmCoordinator: mockLlmCoordinator,
      abortChecker: mockAbortChecker,
      legalTask: "Test legal task",
      customInstructions: "",
      chatId: "test-chat-id",
    };
  });

  describe("process", () => {
    it("should generate sections from summaries by default", async () => {
      // Arrange
      const mockRawSections: any = [
        {
          index_number: 1,
          title: "Raw Section 1",
          description: "This is a raw description.",
          relevant_documents: ["doc1"],
          legal_issues_to_address: [],
        },
      ];

      jest
        .mocked(generateSectionListFromSummaries)
        .mockResolvedValue(mockRawSections);

      // Act
      const result: any = await processor.process(context, dependencies);

      // Assert
      expect(generateSectionListFromSummaries).toHaveBeenCalled();
      expect(result.sectionList).toEqual([
        {
          sectionNumber: 1,
          title: "Raw Section 1",
          relevantDocumentNames: ["doc1"],
          identifiedLegalIssues: [],
        },
      ]);
      expect(result?.metrics?.sectionPlanning).toBeDefined();
    });

    it("should generate sections from main document when specified", async () => {
      // Arrange
      processor = new SectionPlanningStageProcessor({
        sectionSource: "mainDocument",
      });
      context.mainDocumentContent = "Main document content";

      const mockRawSections: any = [
        {
          index_number: 1,
          title: "Main Doc Section",
          description: "Description for Main Doc Section",
          relevant_documents: ["doc1"],
          legal_issues_to_address: [],
        },
      ];

      jest.mocked(fillTemplate).mockReturnValue("User prompt");
      mockLlmConnector.compressMessages.mockResolvedValue({
        systemPrompt: "System prompt",
        userPrompt: "User prompt",
      });
      const responseContent: any = JSON.stringify(mockRawSections);
      mockLlmConnector.getChatCompletion.mockResolvedValue({
        choices: [{ message: { content: responseContent } }],
        usage: { total_tokens: 100 },
        textResponse: responseContent,
      });
      jest.mocked(parseLLMJsonResponse).mockResolvedValue({
        success: true,
        data: mockRawSections,
        error: null,
        attemptsMade: 1,
      });

      // Act
      const result: any = await processor.process(context, dependencies);

      // Assert
      expect(mockLlmConnector.getChatCompletion).toHaveBeenCalled();
      expect(result.sectionList?.[0].title).toBe("Main Doc Section");
      expect(result.sectionList?.[0].sectionNumber).toBe(1);
      expect(result.sectionList?.[0].relevantDocumentNames).toEqual(["doc1"]);
    });

    it("should throw an error if no doc descriptions are available", async () => {
      // Arrange
      context.docDescriptions = [];

      // Act & Assert
      await expect(processor.process(context, dependencies)).rejects.toThrow(
        "No document descriptions available for section planning"
      );
    });

    it("should throw an error if section generation returns empty list", async () => {
      // Arrange
      jest.mocked(generateSectionListFromSummaries).mockResolvedValue([]);

      // Act & Assert
      await expect(processor.process(context, dependencies)).rejects.toThrow(
        "Failed to generate valid section list"
      );
    });

    it("should process sections and return normalized section list", async () => {
      // Arrange
      const mockRawSections: any = [
        {
          index_number: 1,
          title: "Raw Section 1",
          description: "This is a raw description.",
          relevant_documents: ["doc1"],
          legal_issues_to_address: [],
        },
      ];

      jest
        .mocked(generateSectionListFromSummaries)
        .mockResolvedValue(mockRawSections);

      // Act
      const result: any = await processor.process(context, dependencies);

      // Assert
      expect(result).toEqual({
        sectionList: [
          {
            sectionNumber: 1,
            title: "Raw Section 1",
            relevantDocumentNames: ["doc1"],
            identifiedLegalIssues: [],
          },
        ],
        metrics: {
          sectionPlanning: expect.any(Object),
        },
      });
    });
  });

  describe("validateInputs", () => {
    it("should return true if docDescriptions exist", () => {
      const result: any = processor.validateInputs(context);
      expect(result).toBe(true);
    });

    it("should return false if docDescriptions are missing or empty", () => {
      expect(
        processor.validateInputs(createMockFlowContext({ docDescriptions: [] }))
      ).toBe(false);
      expect(
        processor.validateInputs(createMockFlowContext({ docDescriptions: [] }))
      ).toBe(false);
      expect(
        processor.validateInputs(createMockFlowContext({ docDescriptions: [] }))
      ).toBe(false);
    });
  });

  describe("shouldSkip", () => {
    it("should return false if documents are available", () => {
      const result: any = processor.shouldSkip(context);
      expect(result).toBe(false);
    });

    it("should return false even if sectionList already exists", () => {
      context.sectionList = [
        {
          sectionNumber: 1,
          title: "Existing Section",
          relevantDocumentNames: [],
        },
      ];
      const result: any = processor.shouldSkip(context);
      expect(result).toBe(false);
    });

    it("should return true if no documents are available", () => {
      context.documents = [];
      const result: any = processor.shouldSkip(context);
      expect(result).toBe(true);
    });

    it("should return true if explicitly configured to skip", () => {
      processor = new SectionPlanningStageProcessor({
        skipSectionPlanning: true,
      });
      const result: any = processor.shouldSkip(context);
      expect(result).toBe(true);
    });
  });
});
