import { SetupStageProcessor } from "../SetupStageProcessor";
import { getResolvedPrompts } from "../../../helpers/promptManager";

// Mock dependencies
jest.mock("../../../helpers/promptManager", () => ({
  getResolvedPrompts: jest.fn(),
}));

const mockGetResolvedPrompts = getResolvedPrompts as jest.MockedFunction<
  typeof getResolvedPrompts
>;

const mockProgressManager: any = {
  updateStep: jest.fn(),
  sendSubStepProgress: jest.fn(),
};

const mockWorkspaceManager: any = {
  getWorkspaceInfo: jest.fn(),
  getAllDocuments: jest.fn(),
};

const mockLlmCoordinator: any = {
  getProviderInfo: jest.fn(),
};

const mockAbortChecker: any = jest.fn();

describe("SetupStageProcessor", () => {
  let processor: any;
  let context: any;
  let dependencies: any;

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();

    processor = new SetupStageProcessor();
    context = { metrics: {} };
    dependencies = {
      progressManager: mockProgressManager,
      workspaceManager: mockWorkspaceManager,
      llmCoordinator: mockLlmCoordinator,
      abortChecker: mockAbortChecker,
      chatId: "test-chat-id",
    };
  });

  describe("process", () => {
    it("should process the setup stage successfully and return the correct context data", async () => {
      // Arrange
      const mockPrompts: any = { prompt1: "test prompt" };
      const mockWorkspaceInfo: any = { slug: "test-ws", totalDocuments: 1 };
      const mockProviderInfo: any = {
        provider: "test-provider",
        model: "test-model",
      };
      const mockDocuments: any = [{ id: "doc1", content: "test content" }];
      const mockDocumentResult: any = {
        documents: mockDocuments,
        totalFiles: 1,
        skippedFiles: 0,
      };

      mockGetResolvedPrompts.mockResolvedValue(mockPrompts);
      (
        mockWorkspaceManager.getWorkspaceInfo as jest.MockedFunction<
          typeof mockWorkspaceManager.getWorkspaceInfo
        >
      ).mockReturnValue(mockWorkspaceInfo);
      (
        mockWorkspaceManager.getAllDocuments as jest.MockedFunction<
          typeof mockWorkspaceManager.getAllDocuments
        >
      ).mockReturnValue(mockDocumentResult);
      (
        mockLlmCoordinator.getProviderInfo as jest.MockedFunction<
          typeof mockLlmCoordinator.getProviderInfo
        >
      ).mockReturnValue(mockProviderInfo);

      // Act
      const result: any = await processor.process(context, dependencies);

      // Assert
      expect(mockProgressManager.updateStep).toHaveBeenCalledWith(
        "Initializing document processing setup...",
        { total: 4 }
      );
      expect(mockGetResolvedPrompts).toHaveBeenCalled();
      expect(mockWorkspaceManager.getWorkspaceInfo).toHaveBeenCalled();
      expect(mockWorkspaceManager.getAllDocuments).toHaveBeenCalledWith({
        skipErrors: true,
        skipEmpty: true,
      });
      expect(mockLlmCoordinator.getProviderInfo).toHaveBeenCalled();
      expect(mockAbortChecker).toHaveBeenCalledTimes(2);

      expect(result.allPrompts).toBe(mockPrompts);
      expect(result.documents).toBe(mockDocuments);
      expect(result.workspaceInfo).toBe(mockWorkspaceInfo);
      expect(result.llmProviderInfo).toBe(mockProviderInfo);
      expect(result.metrics).toBeDefined();
      expect(result.metrics.setup.totalDocumentsInWorkspace).toBe(1);
      expect(result.metrics.setup.validDocumentsLoaded).toBe(1);
    });

    it("should throw an error if no valid documents are found", async () => {
      // Arrange
      const mockPrompts: any = { prompt1: "test prompt" };
      const mockWorkspaceInfo: any = { slug: "test-ws", totalDocuments: 1 };
      const mockProviderInfo: any = {
        provider: "test-provider",
        model: "test-model",
      };

      mockGetResolvedPrompts.mockResolvedValue(mockPrompts);
      (
        mockWorkspaceManager.getWorkspaceInfo as jest.MockedFunction<
          typeof mockWorkspaceManager.getWorkspaceInfo
        >
      ).mockReturnValue(mockWorkspaceInfo);
      (
        mockWorkspaceManager.getAllDocuments as jest.MockedFunction<
          typeof mockWorkspaceManager.getAllDocuments
        >
      ).mockReturnValue({
        documents: [],
        totalFiles: 0,
        skippedFiles: 0,
      });
      (
        mockLlmCoordinator.getProviderInfo as jest.MockedFunction<
          typeof mockLlmCoordinator.getProviderInfo
        >
      ).mockReturnValue(mockProviderInfo);

      // Act & Assert
      await expect(processor.process(context, dependencies)).rejects.toThrow(
        "Setup stage failed: No valid documents found in workspace for processing"
      );
    });

    it("should throw an error if getResolvedPrompts fails", async () => {
      // Arrange
      const errorMessage: any = "Failed to load prompts";
      mockGetResolvedPrompts.mockRejectedValue(new Error(errorMessage));

      // Act & Assert
      await expect(processor.process(context, dependencies)).rejects.toThrow(
        `Setup stage failed: ${errorMessage}`
      );
    });

    it("should call abortChecker and throw if it throws", async () => {
      // Arrange
      const abortError: any = new Error("Aborted");
      (
        mockAbortChecker as jest.MockedFunction<typeof mockAbortChecker>
      ).mockImplementation(() => {
        throw abortError;
      });

      // Act & Assert
      await expect(processor.process(context, dependencies)).rejects.toThrow(
        abortError
      );
    });
  });

  describe("validateInputs", () => {
    it("should always return true", () => {
      expect(processor.validateInputs({})).toBe(true);
    });
  });

  describe("shouldSkip", () => {
    it("should always return false", () => {
      expect(processor.shouldSkip({})).toBe(false);
    });
  });
});
