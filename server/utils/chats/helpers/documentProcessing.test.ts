// Unit tests for documentProcessing.js helpers

import {
  fillTemplate,
  generateDocumentDescription,
  generateDocumentRelevance,
  selectMainDocument,
  generateSectionListFromSummaries,
  generateDocumentSectionIndices,
  saveDocumentDescriptions, // Will require fs mocking if tested directly for file I/O
  combineSectionOutputs,
} from "./documentProcessing";

import fs from "fs"; // Import fs to mock its methods
import path from "path"; // Added path import

// Mock LLMConnector globally for these tests
const mockLLMConnector: any = {
  compressMessages: jest.fn(async (messages: any) => messages), // Simple pass-through mock
  getChatCompletion: jest.fn(),
  model: "mock-model", // Needed for TokenManager
  metrics: { lastCompletionTokens: 0 },
};

// Mock TokenManager globally
jest.mock("../../helpers/tiktoken", () => ({
  TokenManager: jest.fn().mockImplementation(() => ({
    countFromString: jest.fn((str: any) => str.length), // Simple length-based count for testing
  })),
}));

// Mock the fs module
jest.mock("fs");

describe("documentProcessing helpers", () => {
  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
    (mockLLMConnector.compressMessages as any).mockClear();
    (mockLLMConnector.getChatCompletion as any).mockClear();
    mockLLMConnector.metrics.lastCompletionTokens = 0;
    (fs.writeFileSync as any).mockClear();
    (fs.mkdirSync as any).mockClear();
    (fs.existsSync as any).mockClear();
    // Use fake timers to speed up tests with setTimeout
    jest.useFakeTimers();
  });

  afterEach(() => {
    // Restore real timers after each test
    jest.useRealTimers();
    jest.clearAllMocks();
  });

  describe("fillTemplate", () => {
    it("should replace single placeholder correctly", () => {
      const template: any = "Hello, {{name}}!";
      const vars: any = { name: "World" };
      expect(fillTemplate(template, vars)).toBe("Hello, World!");
    });

    it("should replace multiple placeholders correctly", () => {
      const template: any = "{{greeting}}, {{name}}! You are {{age}}.";
      const vars: any = { greeting: "Hi", name: "Alice", age: 30 };
      expect(fillTemplate(template, vars)).toBe("Hi, Alice! You are 30.");
    });

    it("should handle templates with no placeholders", () => {
      const template: any = "Just a static string.";
      const vars: any = { name: "Test" };
      expect(fillTemplate(template, vars)).toBe("Just a static string.");
    });

    it("should handle empty vars object", () => {
      const template: any = "Hello, {{name}}!";
      const vars: any = {};
      expect(fillTemplate(template, vars)).toBe("Hello, {{name}}!");
    });

    it("should handle placeholders not in vars", () => {
      const template: any = "{{greeting}}, {{name}}!";
      const vars: any = { greeting: "Hey" };
      expect(fillTemplate(template, vars)).toBe("Hey, {{name}}!");
    });
  });

  describe("generateDocumentDescription", () => {
    const docName: any = "testDoc.pdf";
    const content: any = "This is the full document content.";
    const legalTask: any = "Review this document for compliance.";
    const defaultOptions: any = {};

    it("should call LLMConnector with correct prompts and return description", async () => {
      const mockDescription: any = "This is a mock summary of the document.";
      (mockLLMConnector.getChatCompletion as any).mockResolvedValueOnce({
        textResponse: mockDescription,
      });
      mockLLMConnector.metrics.lastCompletionTokens = 50; // Simulate token usage

      const result: any = await generateDocumentDescription(
        docName,
        content,
        legalTask,
        mockLLMConnector,
        defaultOptions
      );

      expect(mockLLMConnector.compressMessages).toHaveBeenCalledWith({
        systemPrompt:
          "You are an expert at analyzing legal documents and providing concise descriptions. Make a detailed summary of the document content and how it is relevant to the legal task. Write the summary in the same language as the legal task.",
        userPrompt: `Generate a detailed summary of the following document content in relation to the legal task "${legalTask}". Focus on the main topics and key information:\n\n${content}`,
      });
      expect(mockLLMConnector.getChatCompletion).toHaveBeenCalledWith(
        expect.anything(), // Compressed messages are passed through by mock
        { temperature: 0.7 } // Default temperature
      );
      expect(result).toBe(mockDescription);
    });

    it("should use custom prompts and temperature if provided", async () => {
      const mockDescription: any = "Custom summary.";
      const customSystemPrompt: any = "Custom system prompt for description.";
      const customUserPromptTemplate: any =
        "Task: {{task}}, Content: {{content}}";
      const customTemp: any = 0.9;
      const options: any = {
        customSystemPrompt,
        customUserPromptTemplate,
        temperature: customTemp,
      };
      (mockLLMConnector.getChatCompletion as any).mockResolvedValueOnce({
        textResponse: mockDescription,
      });

      const result: any = await generateDocumentDescription(
        docName,
        content,
        legalTask,
        mockLLMConnector,
        options
      );

      expect(mockLLMConnector.compressMessages).toHaveBeenCalledWith({
        systemPrompt: customSystemPrompt,
        userPrompt: `Task: ${legalTask}, Content: ${content}`,
      });
      expect(mockLLMConnector.getChatCompletion).toHaveBeenCalledWith(
        expect.anything(),
        { temperature: customTemp }
      );
      expect(result).toBe(mockDescription);
    });
  });

  describe("generateDocumentRelevance", () => {
    const docName: any = "testDoc.pdf";
    const content: any = "Relevant content here.";
    const legalTask: any = "Check relevance.";
    const defaultOptions: any = {};

    it("should return true for 'true' string response from LLM", async () => {
      (mockLLMConnector.getChatCompletion as any).mockResolvedValueOnce({
        textResponse: "true",
      });
      const result: any = await generateDocumentRelevance(
        docName,
        content,
        legalTask,
        mockLLMConnector,
        defaultOptions
      );
      expect(result).toBe(true);
      expect(mockLLMConnector.compressMessages).toHaveBeenCalledWith({
        systemPrompt:
          "You are an expert at evaluating whether a document is relevant to a legal task. Answer strictly with true or false. Respond in the same language used in the legal task prompt.",
        userPrompt: `For the legal task "${legalTask}", is the following document content relevant? Answer strictly "true" or "false":\n\n${content}`,
      });
      expect(mockLLMConnector.getChatCompletion).toHaveBeenCalledWith(
        expect.anything(),
        { temperature: 0.7 }
      );
    });

    it("should return false for 'false' string response from LLM", async () => {
      (mockLLMConnector.getChatCompletion as any).mockResolvedValueOnce({
        textResponse: "false",
      });
      const result: any = await generateDocumentRelevance(
        docName,
        content,
        legalTask,
        mockLLMConnector,
        defaultOptions
      );
      expect(result).toBe(false);
    });

    it("should handle case-insensitivity and extra whitespace for 'true'", async () => {
      (mockLLMConnector.getChatCompletion as any).mockResolvedValueOnce({
        textResponse: "  TrUe  ",
      });
      const result: any = await generateDocumentRelevance(
        docName,
        content,
        legalTask,
        mockLLMConnector,
        defaultOptions
      );
      expect(result).toBe(true);
    });

    it("should return false for any other string response", async () => {
      (mockLLMConnector.getChatCompletion as any).mockResolvedValueOnce({
        textResponse: "not sure",
      });
      const result: any = await generateDocumentRelevance(
        docName,
        content,
        legalTask,
        mockLLMConnector,
        defaultOptions
      );
      expect(result).toBe(false);
    });

    it("should use custom prompts and temperature if provided", async () => {
      const customSystemPrompt: any = "Custom relevance system.";
      const customUserPromptTemplate: any =
        "Is {{content}} relevant for {{task}}?";
      const customTemp: any = 0.2;
      const options: any = {
        customSystemPrompt,
        customUserPromptTemplate,
        temperature: customTemp,
      };
      (mockLLMConnector.getChatCompletion as any).mockResolvedValueOnce({
        textResponse: "true",
      });

      await generateDocumentRelevance(
        docName,
        content,
        legalTask,
        mockLLMConnector,
        options
      );

      expect(mockLLMConnector.compressMessages).toHaveBeenCalledWith({
        systemPrompt: customSystemPrompt,
        userPrompt: `Is ${content} relevant for ${legalTask}?`,
      });
      expect(mockLLMConnector.getChatCompletion).toHaveBeenCalledWith(
        expect.anything(),
        { temperature: customTemp }
      );
    });
  });

  describe("selectMainDocument", () => {
    const descriptions: any = [
      { "Doc Name": "doc1.pdf", Description: "Summary of document 1." },
      {
        "Doc Name": "main_document.docx",
        Description: "This is clearly the main one.",
      },
      { "Doc Name": "appendix.txt", Description: "Supporting details." },
    ];
    const legalTask: any = "Draft a response to the main document.";
    const defaultOptions: any = {};

    it("should call LLMConnector and return the selected document name", async () => {
      const expectedMainDoc: any = "main_document.docx";
      (mockLLMConnector.getChatCompletion as any).mockResolvedValueOnce({
        textResponse: expectedMainDoc,
      });

      const result: any = await selectMainDocument(
        descriptions,
        legalTask,
        mockLLMConnector,
        defaultOptions
      );

      const expectedSummariesString: any = descriptions
        .map((d: any) => `${d["Doc Name"]}: ${d.Description}`)
        .join("\n\n");

      expect(mockLLMConnector.compressMessages).toHaveBeenCalledWith({
        systemPrompt:
          "You are an expert legal assistant who selects the primary document among a set of summaries. Provide only the exact document name.",
        userPrompt: `${expectedSummariesString}\n\nGiven the summaries of documents for the legal task "${legalTask}", which document is the main document? Return only the exact Doc Name.`,
      });
      expect(mockLLMConnector.getChatCompletion).toHaveBeenCalledWith(
        expect.anything(),
        { temperature: 0.7 }
      );
      expect(result).toBe(expectedMainDoc);
    });

    it("should use custom prompts and temperature if provided", async () => {
      const customSystemPrompt: any = "Select primary doc:";
      const customUserPromptTemplate: any =
        "Task: {{task}}\nSummaries:\n{{summaries}}\nWhich is main?";
      const customTemp: any = 0.1;
      const options: any = {
        customSystemPrompt,
        customUserPromptTemplate,
        temperature: customTemp,
      };
      const expectedMainDoc: any = "doc1.pdf";
      (mockLLMConnector.getChatCompletion as any).mockResolvedValueOnce({
        textResponse: expectedMainDoc,
      });

      const result: any = await selectMainDocument(
        descriptions,
        legalTask,
        mockLLMConnector,
        options
      );

      const expectedSummariesString: any = descriptions
        .map((d: any) => `${d["Doc Name"]}: ${d.Description}`)
        .join("\n\n");

      expect(mockLLMConnector.compressMessages).toHaveBeenCalledWith({
        systemPrompt: customSystemPrompt,
        userPrompt: `Task: ${legalTask}\nSummaries:\n${expectedSummariesString}\nWhich is main?`,
      });
      expect(mockLLMConnector.getChatCompletion).toHaveBeenCalledWith(
        expect.anything(),
        { temperature: customTemp }
      );
      expect(result).toBe(expectedMainDoc);
    });

    it("should trim whitespace from the LLM response", async () => {
      (mockLLMConnector.getChatCompletion as any).mockResolvedValueOnce({
        textResponse: "  main_document.docx  ",
      });
      const result: any = await selectMainDocument(
        descriptions,
        legalTask,
        mockLLMConnector,
        defaultOptions
      );
      expect(result).toBe("main_document.docx");
    });
  });

  describe("generateSectionListFromSummaries", () => {
    const docSummaries: any = [
      { "Doc Name": "doc1.pdf", Description: "Summary of document 1 content." },
      {
        "Doc Name": "doc2.docx",
        Description: "Summary of document 2 details.",
      },
    ];
    const legalTask: any = "Create a sales agreement."; // This would be the upgraded prompt
    const defaultOptions: any = {};

    const expectedDefaultSystemPrompt: any = `You are an expert legal assistant. Draft a JSON-structured list of sections for a legal document based on the provided document summaries and legal task.

CRITICAL: You must return ONLY a valid JSON array. Do not include any explanatory text, markdown formatting, or conversational responses.

The JSON array must contain objects with exactly these fields:
  - "index_number": integer
  - "title": string
  - "description": string
  - "relevant_documents": array<string>
  - "legal_issues_to_address": array<string>

Example output:
[
  {
    "index_number": 1,
    "title": "Introduction",
    "description": "Purpose of the submission and background.",
    "relevant_documents": ["document1.pdf", "document2.docx"],
    "legal_issues_to_address": ["Jurisdiction", "Timeliness"]
  }
]

IMPORTANT: 
- Start your response with [ and end with ]
- Do not use markdown code blocks
- Do not add any text before or after the JSON
- Use the same language as the legal task for all free-text fields
- If you cannot create the JSON, return an empty array: []`;

    it("should call LLMConnector with default prompts and return parsed section list", async () => {
      const mockGeneratedSections: any = [{ title: "Section 1" }];
      (mockLLMConnector.getChatCompletion as any).mockResolvedValueOnce({
        textResponse: JSON.stringify(mockGeneratedSections),
      });

      const expectedFormattedSummaries: any = docSummaries
        .map((s: any) => `${s["Doc Name"]}: ${s["Description"]}`)
        .join("\n\n"); // Use actual newlines for comparison with function output

      // Construct with actual newlines
      const expectedUserPromptContent: any = `Legal Task: ${legalTask}\n\nDocument Summaries:\n${expectedFormattedSummaries}\n\nBased on these document summaries, identify appropriate sections for a comprehensive legal document that addresses the task. Return only a JSON array with the structure specified.`;

      const sections: any = await generateSectionListFromSummaries(
        docSummaries,
        legalTask,
        mockLLMConnector,
        "", // No custom instructions for this test
        defaultOptions
      );

      expect(mockLLMConnector.compressMessages).toHaveBeenCalledWith({
        systemPrompt: expectedDefaultSystemPrompt,
        userPrompt: expectedUserPromptContent,
      });
      expect(mockLLMConnector.getChatCompletion).toHaveBeenCalledWith(
        {
          systemPrompt: expectedDefaultSystemPrompt,
          userPrompt: expectedUserPromptContent,
        },
        { temperature: 0.7 }
      );
      expect(sections).toEqual(mockGeneratedSections);
    });

    it("should use custom prompts and temperature if provided", async () => {
      const mockGeneratedSections: any = [{ title: "Custom Section" }];
      const customSystemPrompt: any = "Custom system prompt for sections.";
      // This template should NOT include customInstructions, as the function prepends them.
      const customUserPromptTemplate: any =
        "Task: {{task}}, Summaries: {{summaries}}.";
      const customInstructions: any = "Follow these instructions.";
      const customTemp: any = 0.5;
      const options: any = {
        customSystemPrompt,
        customUserPromptTemplate,
        temperature: customTemp,
      };
      (mockLLMConnector.getChatCompletion as any).mockResolvedValueOnce({
        textResponse: JSON.stringify(mockGeneratedSections),
      });

      const expectedFormattedSummaries: any = docSummaries
        .map((s: any) => `${s["Doc Name"]}: ${s["Description"]}`)
        .join("\n\n"); // Use actual newlines

      // 1. Create the base user prompt using the template
      const expectedBaseUserPrompt: any = fillTemplate(
        customUserPromptTemplate,
        {
          task: legalTask,
          summaries: expectedFormattedSummaries,
        }
      );

      // 2. Prepend custom instructions, mirroring the function's logic
      const expectedUserPromptContent: any = `${customInstructions}\n\n${expectedBaseUserPrompt}`;

      const sections: any = await generateSectionListFromSummaries(
        docSummaries,
        legalTask,
        mockLLMConnector,
        customInstructions,
        options
      );

      expect(mockLLMConnector.compressMessages).toHaveBeenCalledWith({
        systemPrompt: customSystemPrompt,
        userPrompt: expectedUserPromptContent,
      });
      expect(mockLLMConnector.getChatCompletion).toHaveBeenCalledWith(
        {
          systemPrompt: customSystemPrompt,
          userPrompt: expectedUserPromptContent,
        },
        { temperature: customTemp }
      );
      expect(sections).toEqual(mockGeneratedSections);
    });

    it("should handle malformed JSON response from LLM gracefully", async () => {
      // Mock all 3 retry attempts to return non-JSON
      (mockLLMConnector.getChatCompletion as any)
        .mockResolvedValueOnce({ textResponse: "This is not JSON" })
        .mockResolvedValueOnce({ textResponse: "Still not JSON" })
        .mockResolvedValueOnce({ textResponse: "Never JSON" });

      const resultPromise = generateSectionListFromSummaries(
        docSummaries,
        legalTask,
        mockLLMConnector,
        "",
        defaultOptions
      );

      // Advance timers through all retry delays
      await jest.advanceTimersByTimeAsync(1000); // First retry delay
      await jest.advanceTimersByTimeAsync(2000); // Second retry delay
      await jest.advanceTimersByTimeAsync(3000); // Third retry delay

      const result: any = await resultPromise;
      expect(result).toEqual([]); // Expects empty array on JSON parse error as per safeJsonParse
      expect(mockLLMConnector.getChatCompletion).toHaveBeenCalledTimes(3); // Should retry 3 times
    }, 15000);

    it("should handle LLM error gracefully", async () => {
      // Mock multiple rejections for all 3 retry attempts
      (mockLLMConnector.getChatCompletion as any)
        .mockRejectedValueOnce(new Error("LLM failed"))
        .mockRejectedValueOnce(new Error("LLM failed"))
        .mockRejectedValueOnce(new Error("LLM failed"));

      const resultPromise = generateSectionListFromSummaries(
        docSummaries,
        legalTask,
        mockLLMConnector,
        "",
        defaultOptions
      );

      // Advance timers through all retry delays
      await jest.advanceTimersByTimeAsync(1000); // First retry delay
      await jest.advanceTimersByTimeAsync(2000); // Second retry delay
      await jest.advanceTimersByTimeAsync(3000); // Third retry delay

      const result: any = await resultPromise;
      expect(result).toEqual([]); // Now expects empty array instead of throwing
      expect(mockLLMConnector.getChatCompletion).toHaveBeenCalledTimes(3); // Should retry 3 times
    }, 15000);
  });

  describe("generateDocumentSectionIndices", () => {
    const docName: any = "doc1.pdf";
    const content: any = "Content relevant to sections 1 and 3.";
    const sectionList: any = [
      { index_number: 1, title: "Introduction" },
      { index_number: 2, title: "Background" },
      { index_number: 3, title: "Analysis" },
    ];
    const defaultOptions: any = {};

    it("should call LLM and parse valid JSON array of indices", async () => {
      const mockIndices: any = [1, 3];
      (mockLLMConnector.getChatCompletion as any).mockResolvedValueOnce({
        textResponse: JSON.stringify(mockIndices),
      });

      const result: any = await generateDocumentSectionIndices(
        docName,
        content,
        sectionList,
        mockLLMConnector,
        defaultOptions
      );

      const expectedSectionLines: any = sectionList
        .map((s: any) => `${s.index_number}. ${s.title}`.trim())
        .join("\n");
      expect(mockLLMConnector.compressMessages).toHaveBeenCalledWith({
        systemPrompt: expect.stringContaining(
          "Decide which sections are supported"
        ), // Default system prompt
        userPrompt: `Sections:\n${expectedSectionLines}\n\nDocument (${docName}) content:\n${content}`,
      });
      expect(mockLLMConnector.getChatCompletion).toHaveBeenCalledWith(
        expect.anything(),
        { temperature: 0 }
      ); // Default temp is 0
      expect(result).toEqual(mockIndices);
    });

    it("should handle JSON in markdown code block for indices", async () => {
      const mockIndices: any = [2];
      const llmResponse: any =
        "```json\n" + JSON.stringify(mockIndices) + "\n```";
      (mockLLMConnector.getChatCompletion as any).mockResolvedValueOnce({
        textResponse: llmResponse,
      });
      const result: any = await generateDocumentSectionIndices(
        docName,
        content,
        sectionList,
        mockLLMConnector,
        defaultOptions
      );
      expect(result).toEqual(mockIndices);
    });

    it("should return empty array for invalid JSON response or non-array", async () => {
      (mockLLMConnector.getChatCompletion as any).mockResolvedValueOnce({
        textResponse: "Not an array.",
      });
      let result = await generateDocumentSectionIndices(
        docName,
        content,
        sectionList,
        mockLLMConnector,
        defaultOptions
      );
      expect(result).toEqual([]);

      (mockLLMConnector.getChatCompletion as any).mockResolvedValueOnce({
        textResponse: JSON.stringify({ not: "an array" }),
      });
      result = await generateDocumentSectionIndices(
        docName,
        content,
        sectionList,
        mockLLMConnector,
        defaultOptions
      );
      expect(result).toEqual([]);
    });

    it("should return empty array if sectionList is empty or not an array", async () => {
      let result = await generateDocumentSectionIndices(
        docName,
        content,
        [],
        mockLLMConnector,
        defaultOptions
      );
      expect(result).toEqual([]);
      expect(mockLLMConnector.compressMessages).not.toHaveBeenCalled(); // Should not call LLM if no sections

      result = await generateDocumentSectionIndices(
        docName,
        content,
        [],
        mockLLMConnector,
        defaultOptions
      );
      expect(result).toEqual([]);
      expect(mockLLMConnector.compressMessages).not.toHaveBeenCalled();
    });

    it("should use custom system prompt and temperature if provided", async () => {
      const customSystemPrompt: any = "Custom section indexer.";
      const customTemp: any = 0.5;
      const options: any = { customSystemPrompt, temperature: customTemp };
      (mockLLMConnector.getChatCompletion as any).mockResolvedValueOnce({
        textResponse: "[1]",
      });

      await generateDocumentSectionIndices(
        docName,
        content,
        sectionList,
        mockLLMConnector,
        options
      );

      expect(mockLLMConnector.compressMessages).toHaveBeenCalledWith(
        expect.objectContaining({
          systemPrompt: customSystemPrompt,
        })
      );
      expect(mockLLMConnector.getChatCompletion).toHaveBeenCalledWith(
        expect.anything(),
        { temperature: customTemp }
      );
    });
  });

  describe("saveDocumentDescriptions", () => {
    const descriptions: any = [
      { "Doc Name": "test.pdf", Description: "A test document" },
    ];
    const chatId: any = "chat123";
    const subdirectory: any = "main";
    // Corrected: __dirname in tests will refer to the *test file's* directory.
    // The documentProcessing.js file is one level up in `../` from a `__tests__` or `helpers` dir if conventional.
    // For this example, assuming documentProcessing.js is in `./` relative to this test file based on current import.
    // Actual pathing in `saveDocumentDescriptions` is `path.join(__dirname, "../../../storage/document-builder")`
    // which means from `documentProcessing.js` it goes up three levels.
    // So, from test file (assuming it's alongside or one level deeper than `documentProcessing.js`)
    // we need to construct a path that mirrors this. Let's assume test file is in `server/utils/chats/helpers/__tests__`
    // then `documentProcessing.js` is `../documentProcessing.js`.
    // `../../../` from `documentProcessing.js` means `server/storage/document-builder`
    // So from test file, `../../../../storage/document-builder` if test file is in __tests__
    // For simplicity, let's mock path.join or use a fixed string if __dirname context is tricky in tests.
    // However, the function itself uses __dirname internally. The key is to ensure our *expected* paths match that logic.

    // Let's use a simpler approach for base path in test, as the internal __dirname is hard to perfectly replicate here.
    // The crucial part is that the function *constructs* paths correctly, which we test via args to fs calls.
    // Mock base for cleaner assertions
    let originalDirnameGetter: any;

    beforeAll(() => {
      // Mock path.join to control its behavior relative to the function's __dirname
      // This is a bit advanced; a simpler way is to check the *relative* parts of the path.
      // For now, we'll assume path.join works and focus on what the function passes to fs.
      // We can also mock __dirname for documentProcessing.js if it becomes an issue.
      const documentProcessingModulePath: any = require.resolve(
        "./documentProcessing"
      );
      const documentProcessingDir: any = path.dirname(
        documentProcessingModulePath
      );

      originalDirnameGetter = Object.getOwnPropertyDescriptor(
        global,
        "__dirname"
      );
      Object.defineProperty(global, "__dirname", {
        value: documentProcessingDir,
        configurable: true,
      });
    });

    afterAll(() => {
      if (originalDirnameGetter) {
        Object.defineProperty(global, "__dirname", originalDirnameGetter);
      }
    });

    it("should write descriptions to a file with chatId and subdirectory", () => {
      // Path the function will construct internally using its __dirname
      const functionInternalBasePath: any = path.resolve(
        global.__dirname,
        "../../../storage/document-builder"
      );
      const expectedDir: any = path.join(
        functionInternalBasePath,
        subdirectory
      );
      const expectedFilePath: any = path.join(
        expectedDir,
        `document-descriptions-${chatId}.json`
      );

      (fs.existsSync as any).mockReturnValue(true);
      const resultPath: any = saveDocumentDescriptions(
        descriptions,
        chatId,
        subdirectory
      );

      expect(fs.mkdirSync).not.toHaveBeenCalled();
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        expectedFilePath,
        JSON.stringify(descriptions, null, 2),
        "utf8"
      );
      expect(resultPath).toBe(expectedFilePath);
    });

    it("should create subdirectory if it does not exist", () => {
      const functionInternalBasePath: any = path.resolve(
        global.__dirname,
        "../../../storage/document-builder"
      );
      const expectedDir: any = path.join(
        functionInternalBasePath,
        subdirectory
      );
      const expectedFilePath: any = path.join(
        expectedDir,
        `document-descriptions-${chatId}.json`
      );

      (fs.existsSync as any).mockReturnValueOnce(false);
      (fs.existsSync as any).mockReturnValueOnce(true);

      saveDocumentDescriptions(descriptions, chatId, subdirectory);

      expect(fs.mkdirSync).toHaveBeenCalledWith(expectedDir, {
        recursive: true,
      });
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        expectedFilePath,
        JSON.stringify(descriptions, null, 2),
        "utf8"
      );
    });

    it("should use Date.now() for filename if chatId is not provided", () => {
      const mockDateNow: any = 1678886400000;
      const spyDateNow: any = jest
        .spyOn(Date, "now")
        .mockImplementation(() => mockDateNow);
      const functionInternalBasePath: any = path.resolve(
        global.__dirname,
        "../../../storage/document-builder"
      );
      const expectedDir: any = path.join(
        functionInternalBasePath,
        subdirectory
      );
      const expectedFilePath: any = path.join(
        expectedDir,
        `document-descriptions-${mockDateNow}.json`
      );

      (fs.existsSync as any).mockReturnValue(true);
      const resultPath: any = saveDocumentDescriptions(
        descriptions,
        undefined,
        subdirectory
      );

      expect(fs.writeFileSync).toHaveBeenCalledWith(
        expectedFilePath,
        JSON.stringify(descriptions, null, 2),
        "utf8"
      );
      expect(resultPath).toBe(expectedFilePath);
      (spyDateNow as jest.MockedFunction<typeof spyDateNow>).mockRestore();
    });

    it("should save to base document-builder path if subdirectory is empty", () => {
      const functionInternalBasePath: any = path.resolve(
        global.__dirname,
        "../../../storage/document-builder"
      );
      const expectedFilePath: any = path.join(
        functionInternalBasePath,
        `document-descriptions-${chatId}.json`
      );
      (fs.existsSync as any).mockReturnValue(true);

      const resultPath: any = saveDocumentDescriptions(
        descriptions,
        chatId,
        ""
      );

      expect(fs.mkdirSync).not.toHaveBeenCalled();
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        expectedFilePath,
        JSON.stringify(descriptions, null, 2),
        "utf8"
      );
      expect(resultPath).toBe(expectedFilePath);
    });

    it("should throw error if writeFileSync fails", () => {
      (fs.existsSync as any).mockReturnValue(true);
      const writeError: any = new Error("Disk full");
      (fs.writeFileSync as any).mockImplementation(() => {
        throw writeError;
      });
      expect(() =>
        saveDocumentDescriptions(descriptions, chatId, subdirectory)
      ).toThrow(writeError);
    });
  });

  describe("combineSectionOutputs", () => {
    test("should combine sections with proper newlines, not escaped backslashes", () => {
      const sectionOutputs: any = [
        { title: "Section 1", content: "Content for section 1." },
        { title: "Section 2", content: "Content for section 2." },
      ];

      const result: any = combineSectionOutputs(sectionOutputs);

      // Check that the result contains actual newlines, not escaped backslashes
      expect(result).toContain("\n\n");
      expect(result).not.toContain("\\n\\n");

      // Check the exact format without separator
      const expected: any =
        "## Section 1\n\nContent for section 1.\n\n## Section 2\n\nContent for section 2.";
      expect(result).toBe(expected);
    });

    test("should handle empty array", () => {
      const result: any = combineSectionOutputs([]);
      expect(result).toBe("");
    });

    test("should handle sections with missing title or content", () => {
      const sectionOutputs: any = [
        { title: "Good Section", content: "Good content" },
        { title: null, content: "Missing title" },
        { title: "Missing content", content: null },
      ];

      const result: any = combineSectionOutputs(sectionOutputs);

      expect(result).toContain("## Good Section\n\nGood content");
      expect(result).toContain("## Untitled Section\n\nMissing title");
      expect(result).toContain("## Missing content\n\n(Content not available)");
    });
  });

  // More tests will be added here for other functions
});
