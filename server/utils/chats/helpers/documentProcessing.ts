import * as fs from "fs";
import * as path from "path";
import { Response } from "express";
import { TokenManager } from "../../helpers/tiktoken";
import { ContextWindowManager } from "./contextWindowManager";
import { TokenTracker } from "./tokenTracker";
import { LLMResponse, ChatError } from "../../../types/chat-agent";
import { CompletionResponse } from "../../../types/ai-providers";
import { LLMConnector } from "../LLMConnector";
import type { ChatCompletionResult } from "../../../types/chat-flow";
import type { ResponseChunkData } from "../../helpers/chat/responses";

// Document processing interfaces
export interface DocumentDescription {
  "Doc Name": string;
  DisplayName?: string;
  Description: string;
  Content?: string;
  pageContent?: string;
  tokens?: number;
}

export interface DocumentSection {
  index_number: number;
  title: string;
  description: string;
  relevant_documents: string[];
  legal_issues_to_address: string[];
  // Legacy fields
  index?: number;
  "Topic Number"?: number;
  Title?: string;
  Description?: string;
  relevantDocumentNames?: string[];
  generatedMemos?: MemoData[];
}

export interface MemoData {
  issue?: string;
  Issue?: string;
  memo?: string;
  content?: string;
  tokens?: number;
}

export interface DraftedSection extends DocumentSection {
  draftedContent: string;
  tokenUsage: {
    totalTokens: number;
    iterations: number;
    isIterative: boolean;
  };
}

export interface GenerateOptions {
  customSystemPrompt?: string;
  customUserPromptTemplate?: string;
  temperature?: number;
  reservedTokens?: number;
  overlapTokens?: number;
  logTokenUsage?: boolean;
  flowType?: string;
}

export interface ProcessingSectionContext {
  sectionNumber: number;
  sectionTitle: string;
  legalTask: string;
  neighborContext: string;
  flowType: string;
  description: string;
}

export interface ProcessedDocument {
  name: string;
  content: string;
  DisplayName?: string;
  "Doc Name"?: string;
  Content?: string;
  pageContent?: string;
  tokens?: number;
}

export interface ProcessedMemo {
  issue: string;
  content: string;
  tokens?: number;
}

export interface SectionDraftingResult {
  content: string;
  tokensUsed: number;
  iteration: number;
  documentsProcessed: number;
  memosProcessed: number;
  llmResponse?: LLMResponse;
}

export interface SectionDraftingOptions {
  legalTask: string;
  processedDocuments?: ProcessedDocument[];
  workspacePath?: string;
  mainDocNameInitial?: string;
  temperature?: number;
  AllPrompts?: Record<
    string,
    { PROMPT_TEMPLATE?: string; [key: string]: unknown }
  >;
  chatId?: string;
  documentBuilderBasePath?: string;
  flowType?: string;
}

export interface SectionDraftingCallbacks {
  onSectionProgress?: (index: number, title: string, status: string) => void;
  onTokenUsage?: (
    index: number,
    usage: {
      totalTokens: number;
      iterations: number;
      isIterative: boolean;
    }
  ) => void;
}

export interface SectionDraftingPrompts {
  systemPrompt: string;
  userPrompt: string;
  initialUserPrompt?: string;
  refineUserPrompt?: string;
}

function updateLastTokens(
  result: ChatCompletionResult | LLMResponse | CompletionResponse,
  LLM: LLMConnector
): void {
  if (!result || !LLM) return;
  // const _tokens =
  //   result?.metrics?.total_tokens ??
  //   ((result?.metrics?.prompt_tokens) || 0) +
  //     ((result?.metrics?.completion_tokens) || 0);
  // TODO: Fix metrics property - needs to be added to LLMConnector interface
  // LLM.metrics = { ...(LLM.metrics || {}), lastCompletionTokens: tokens };
}

/**
 * Generate a detailed summary/description of a document relevant to a legal task.
 */
export async function generateDocumentDescription(
  _docName: string,
  content: string,
  legalTask: string,
  LLMConnector: LLMConnector,
  options: GenerateOptions = {},
  _chatId: string | null = null,
  _flowType: string = "main"
): Promise<string> {
  // Note: docName, chatId, and flowType are kept for API compatibility
  // even though they're not currently used in this function

  // Default prompts (can be overridden via options)
  const DEFAULT_SYSTEM_PROMPT =
    "You are an expert at analyzing legal documents and providing concise descriptions. Make a detailed summary of the document content and how it is relevant to the legal task. Write the summary in the same language as the legal task.";

  const DEFAULT_USER_PROMPT_TEMPLATE = `Generate a detailed summary of the following document content in relation to the legal task "{{task}}". Focus on the main topics and key information:\n\n{{content}}`;

  const systemPrompt = options.customSystemPrompt || DEFAULT_SYSTEM_PROMPT;
  const userPromptTemplate =
    options.customUserPromptTemplate || DEFAULT_USER_PROMPT_TEMPLATE;

  // Fill template placeholders
  const userPrompt = fillTemplate(userPromptTemplate, {
    task: legalTask,
    content: content,
  });

  const compressedMessages = await LLMConnector.compressMessages({
    systemPrompt,
    userPrompt,
  });

  const descriptionResult = await LLMConnector.getChatCompletion(
    compressedMessages,
    {
      temperature: options.temperature || 0.7,
    }
  );
  if (descriptionResult) {
    updateLastTokens(descriptionResult, LLMConnector);
  }

  const generatedDescription = descriptionResult?.textResponse || "";

  return generatedDescription;
}

/**
 * Determine if a document is relevant to a legal task.
 */
export async function generateDocumentRelevance(
  _docName: string,
  content: string,
  legalTask: string,
  LLMConnector: LLMConnector,
  options: GenerateOptions = {}
): Promise<boolean> {
  // Note: docName is kept for API compatibility even though it's not currently used
  // Default prompts (can be overridden via options)
  const DEFAULT_SYSTEM_PROMPT =
    "You are an expert at evaluating whether a document is relevant to a legal task. Answer strictly with true or false. Respond in the same language used in the legal task prompt.";

  const DEFAULT_USER_PROMPT_TEMPLATE = `For the legal task "{{task}}", is the following document content relevant? Answer strictly "true" or "false":\n\n{{content}}`;

  const systemPrompt = options.customSystemPrompt || DEFAULT_SYSTEM_PROMPT;
  const userPromptTemplate =
    options.customUserPromptTemplate || DEFAULT_USER_PROMPT_TEMPLATE;

  // Fill template placeholders
  const userPrompt = fillTemplate(userPromptTemplate, {
    task: legalTask,
    content: content,
  });

  const compressedMessages = await LLMConnector.compressMessages({
    systemPrompt,
    userPrompt,
  });

  const relevanceResult = await LLMConnector.getChatCompletion(
    compressedMessages,
    {
      temperature: options.temperature || 0.7,
    }
  );
  if (relevanceResult) {
    updateLastTokens(relevanceResult, LLMConnector);
  }

  return (
    relevanceResult?.textResponse?.trim().toLowerCase().startsWith("true") ||
    false
  );
}

/**
 * Select which document is the "main" document based on descriptions.
 */
export async function selectMainDocument(
  descriptions: DocumentDescription[],
  legalTask: string,
  LLMConnector: LLMConnector,
  options: GenerateOptions = {}
): Promise<string> {
  // Format the summaries for the prompt
  const listSummaries = descriptions
    .map((d) => `${d["Doc Name"]}: ${d.Description}`)
    .join("\n\n");

  // Default prompts (can be overridden via options)
  const DEFAULT_SYSTEM_PROMPT =
    "You are an expert legal assistant who selects the primary document among a set of summaries. Provide only the exact document name.";

  const DEFAULT_USER_PROMPT_TEMPLATE = `{{summaries}}\n\nGiven the summaries of documents for the legal task "{{task}}", which document is the main document? Return only the exact Doc Name.`;

  const systemPrompt = options.customSystemPrompt || DEFAULT_SYSTEM_PROMPT;
  const userPromptTemplate =
    options.customUserPromptTemplate || DEFAULT_USER_PROMPT_TEMPLATE;

  // Fill template placeholders
  const userPrompt = fillTemplate(userPromptTemplate, {
    summaries: listSummaries,
    task: legalTask,
  });

  const compressed = await LLMConnector.compressMessages({
    systemPrompt,
    userPrompt,
  });

  const result = await LLMConnector.getChatCompletion(compressed, {
    temperature: options.temperature || 0.7,
  });
  if (result) {
    updateLastTokens(result, LLMConnector);
  }

  return result?.textResponse?.trim() || "";
}

/**
 * Save document descriptions to a JSON file.
 */
export function saveDocumentDescriptions(
  descriptions: DocumentDescription[],
  chatId?: string,
  subdirectory: string = ""
): string {
  // If no chatId is provided, use a timestamp
  const fileIdentifier = chatId || Date.now();
  const fileName = `document-descriptions-${fileIdentifier}.json`;

  // Create the base path and subdirectory if it doesn't exist
  let basePath = path.join(__dirname, "../../../storage/document-builder");

  if (subdirectory) {
    basePath = path.join(basePath, subdirectory);
  }

  if (!fs.existsSync(basePath)) {
    fs.mkdirSync(basePath, { recursive: true });
  }

  const filePath = path.join(basePath, fileName);

  try {
    const formattedContent = JSON.stringify(descriptions, null, 2);
    fs.writeFileSync(filePath, formattedContent, "utf8");
    console.log(`Document descriptions saved: ${filePath}`);
    return filePath;
  } catch (error: unknown) {
    console.error(
      `Error writing document descriptions file: ${error instanceof Error ? error.message : String(error)}`
    );
    throw error;
  }
}

/**
 * Generate section list from document summaries for the "no main document" flow.
 */
export async function generateSectionListFromSummaries(
  docSummaries: DocumentDescription[],
  legalTask: string,
  LLMConnector: LLMConnector,
  customInstructions: string = "",
  options: GenerateOptions = {}
): Promise<DocumentSection[]> {
  // Default prompts specific to creating sections from multiple documents
  const DEFAULT_SYSTEM_PROMPT = `You are an expert legal assistant. Draft a JSON-structured list of sections for a legal document based on the provided document summaries and legal task.

CRITICAL: You must return ONLY a valid JSON array. Do not include any explanatory text, markdown formatting, or conversational responses.

The JSON array must contain objects with exactly these fields:
  - "index_number": integer
  - "title": string
  - "description": string
  - "relevant_documents": array<string>
  - "legal_issues_to_address": array<string>

Example output:
[
  {
    "index_number": 1,
    "title": "Introduction",
    "description": "Purpose of the submission and background.",
    "relevant_documents": ["document1.pdf", "document2.docx"],
    "legal_issues_to_address": ["Jurisdiction", "Timeliness"]
  }
]

IMPORTANT: 
- Start your response with [ and end with ]
- Do not use markdown code blocks
- Do not add any text before or after the JSON
- Use the same language as the legal task for all free-text fields
- If you cannot create the JSON, return an empty array: []`;

  const DEFAULT_USER_PROMPT_TEMPLATE = `Legal Task: {{task}}

Document Summaries:
{{summaries}}

Based on these document summaries, identify appropriate sections for a comprehensive legal document that addresses the task. Return only a JSON array with the structure specified.`;

  const systemPrompt = options.customSystemPrompt || DEFAULT_SYSTEM_PROMPT;
  const userPromptTemplate =
    options.customUserPromptTemplate || DEFAULT_USER_PROMPT_TEMPLATE;

  // Format document summaries - use DisplayName to avoid UUID exposure
  const formattedSummaries = docSummaries
    .map((doc) => `${doc.DisplayName || doc["Doc Name"]}: ${doc.Description}`)
    .join("\n\n");

  // Fill template
  const baseUserPrompt = fillTemplate(userPromptTemplate, {
    task: legalTask,
    summaries: formattedSummaries,
  });

  // Add custom instructions if provided
  const userPrompt = customInstructions
    ? `${customInstructions}\n\n${baseUserPrompt}`
    : baseUserPrompt;

  // Retry mechanism with progressively stricter prompts
  const MAX_RETRIES = 3;
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
    try {
      let attemptSystemPrompt = systemPrompt;
      let attemptTemperature = options.temperature || 0.7;

      // Make prompts progressively stricter
      if (attempt === 2) {
        attemptTemperature = 0.3; // Lower temperature
        attemptSystemPrompt =
          systemPrompt +
          "\n\nREMINDER: You must respond with ONLY a JSON array starting with [ and ending with ]. No other text allowed.";
      } else if (attempt === 3) {
        attemptTemperature = 0.1; // Very low temperature
        attemptSystemPrompt =
          "RESPOND ONLY WITH JSON. " +
          systemPrompt +
          "\n\nCRITICAL: Your response must be a valid JSON array. Any other response format will cause system failure.";
      }

      console.log(
        `generateSectionListFromSummaries: Attempt ${attempt}/${MAX_RETRIES} (temperature: ${attemptTemperature})`
      );

      const compressed = await LLMConnector.compressMessages({
        systemPrompt: attemptSystemPrompt,
        userPrompt,
      });

      const result = await LLMConnector.getChatCompletion(compressed, {
        temperature: attemptTemperature,
      });
      if (result) {
        updateLastTokens(result, LLMConnector);
      }

      const text = result?.textResponse?.trim() || "";

      // Enhanced JSON extraction with better validation
      let jsonText = text;

      // First try to extract from markdown code blocks
      const codeBlockMatch = text.match(/```(?:json)?\r?\n([\s\S]*?)```/);
      if (codeBlockMatch && codeBlockMatch[1]) {
        jsonText = codeBlockMatch[1].trim();
      }

      // Basic validation before parsing
      if (!jsonText.startsWith("[") && !jsonText.startsWith("{")) {
        console.error(
          `generateSectionListFromSummaries: Attempt ${attempt} - Response doesn't look like JSON`,
          { response: text.substring(0, 200) + "..." }
        );

        // Try to find JSON-like content in the response
        const jsonLikeMatch = text.match(/(\[[\s\S]*\])/);
        if (jsonLikeMatch) {
          jsonText = jsonLikeMatch[1];
          console.log(
            `generateSectionListFromSummaries: Attempt ${attempt} - Found JSON-like content, attempting to parse`
          );
        } else {
          throw new Error(
            `LLM returned non-JSON response: ${text.substring(0, 100)}...`
          );
        }
      }

      const parsedResult = JSON.parse(jsonText);

      // Validate the structure
      if (!Array.isArray(parsedResult)) {
        throw new Error("Expected JSON array but got: " + typeof parsedResult);
      }

      console.log(
        `generateSectionListFromSummaries: Successfully parsed JSON on attempt ${attempt}`
      );
      return parsedResult as DocumentSection[];
    } catch (err: unknown) {
      lastError = err instanceof Error ? err : new Error(String(err));
      console.error(
        `generateSectionListFromSummaries: Attempt ${attempt}/${MAX_RETRIES} failed`,
        {
          error: err instanceof Error ? err.message : String(err),
          attempt,
          temperature:
            attempt === 1
              ? options.temperature || 0.7
              : attempt === 2
                ? 0.3
                : 0.1,
        }
      );

      // If this is the last attempt, break and handle error below
      if (attempt === MAX_RETRIES) {
        break;
      }

      // Wait before retry (exponential backoff)
      await new Promise((resolve) => setTimeout(resolve, 1000 * attempt));
    }
  }

  // All retries failed
  console.error("generateSectionListFromSummaries: All retry attempts failed", {
    error: lastError instanceof Error ? lastError.message : String(lastError),
    totalAttempts: MAX_RETRIES,
  });

  // Return empty array but log the full context for debugging
  return [];
}

/**
 * Map a document to relevant section indices.
 */
export async function generateDocumentSectionIndices(
  docName: string,
  content: string,
  sectionList: DocumentSection[],
  LLMConnector: LLMConnector,
  options: GenerateOptions = {}
): Promise<number[]> {
  if (!Array.isArray(sectionList) || sectionList.length === 0) return [];

  // Build a concise string of section identifiers for the LLM prompt
  const sectionLines = sectionList
    .map((s) => {
      const idx = s.index_number ?? s.index ?? s["Topic Number"] ?? "";
      const title = s.title ?? s.Title ?? s.Description ?? "";
      return `${idx}. ${title}`.trim();
    })
    .join("\n");

  const DEFAULT_SYSTEM_PROMPT = `You are a legal analyst. Decide which sections are supported by the given document content.
Return ONLY a JSON array of section index numbers (integers). No markdown fences.
Example output: [1,3,5]
Ensure any explanatory text (if unavoidable) is in the same language as the legal task.`;

  const systemPrompt = options.customSystemPrompt || DEFAULT_SYSTEM_PROMPT;
  const userPrompt = `Sections:\n${sectionLines}\n\nDocument (${docName}) content:\n${content}`;

  const compressed = await LLMConnector.compressMessages({
    systemPrompt,
    userPrompt,
  });

  const result = await LLMConnector.getChatCompletion(compressed, {
    temperature: options.temperature || 0,
  });
  if (result) {
    updateLastTokens(result, LLMConnector);
  }

  try {
    const trimmed = result?.textResponse?.trim() || "";
    const body = (trimmed.match(/```(?:json)?\r?\n([\s\S]*?)```/i) || [
      null,
      trimmed,
    ])[1];
    const arr = JSON.parse(body || trimmed);
    return Array.isArray(arr) ? arr : [];
  } catch (err) {
    console.error(
      `Failed to parse section index JSON for document ${docName}:`,
      err
    );
    return [];
  }
}

/**
 * Helper function to fill placeholders in prompt templates.
 */
export function fillTemplate(
  template: string,
  vars: Record<string, string | number | boolean | null | undefined>
): string {
  return Object.entries(vars).reduce(
    (str, [key, val]) => str.split(`{{${key}}}`).join(String(val ?? "")),
    template
  );
}

/**
 * Combines section outputs into a single markdown string.
 */
export function combineSectionOutputs(
  sectionOutputs: Array<{ title: string; content: string }> = []
): string {
  if (!Array.isArray(sectionOutputs) || sectionOutputs.length === 0) {
    return ""; // Return empty string if input is invalid or empty
  }

  return sectionOutputs
    .map((sec) => {
      // Ensure title and content are strings, provide defaults if missing
      const title =
        typeof (sec?.title ?? 0) === "string" ? sec.title : "Untitled Section";
      const content =
        typeof (sec?.content ?? 0) === "string"
          ? sec.content
          : "(Content not available)";
      return `## ${title}\n\n${content}`;
    })
    .join("\n\n"); // Join sections seamlessly without separator
}

/**
 * Generate document description with iterative chunking for large documents
 */
export async function generateDocumentDescriptionIterative(
  docName: string,
  content: string,
  legalTask: string,
  LLMConnector: LLMConnector,
  options: GenerateOptions = {}
): Promise<string> {
  const contextManager = new ContextWindowManager(LLMConnector, {
    ...options,
    logTokenUsage: options.logTokenUsage || false,
  });
  const tm = new TokenManager(LLMConnector.model);
  const tokenTracker = contextManager.getTokenTracker();

  // Start tracking this stage
  const stageTracker = tokenTracker
    ? tokenTracker.startStage("documentDescriptions")
    : null;

  // Calculate token budget for description generation
  const systemPrompt =
    options.customSystemPrompt ||
    "You are an expert at analyzing legal documents and providing concise descriptions. Make a detailed summary of the document content and how it is relevant to the legal task.";

  const userPromptTemplate =
    options.customUserPromptTemplate ||
    `Generate a detailed summary of the following document content in relation to the legal task "{{task}}". Focus on the main topics and key information:\n\n{{content}}`;

  const budget = contextManager.calculateTokenBudget({
    systemPrompt,
    userPromptTemplate: fillTemplate(userPromptTemplate, {
      task: legalTask,
      content: "",
    }),
    reservedTokens: options.reservedTokens || 2000,
  });

  const actualTokenCount = tm.countFromString(content);

  // Track content tokens
  if (tokenTracker) {
    tokenTracker.trackContentTokens(content, "documents", docName);
    tokenTracker.trackContentTokens(
      systemPrompt,
      "systemPrompts",
      "document-description"
    );
    // TODO: Fix budget type mismatch
    // tokenTracker.trackBudget(budget, actualTokenCount);
  }

  // If content fits within budget, use standard processing
  if (actualTokenCount <= budget.availableTokens) {
    const result = await generateDocumentDescription(
      docName,
      content,
      legalTask,
      LLMConnector,
      options
    );

    // Track the LLM response
    // TODO: Fix metrics property - needs to be added to LLMConnector interface
    // if (tokenTracker && LLMConnector?.metrics?.lastCompletionTokens) {
    //   stageTracker?.addTokens(
    //     LLMConnector.metrics.lastCompletionTokens,
    //     "standard-processing"
    //   );
    // }

    stageTracker?.finish();
    return result;
  }

  console.log(
    `[ITERATIVE] Document ${docName} (${actualTokenCount} tokens) exceeds budget (${budget.availableTokens}). Using iterative processing.`
  );

  // Chunk the content
  const chunks = contextManager.chunkContent(content, {
    maxTokensPerChunk: budget.availableTokens,
    overlapTokens: options.overlapTokens || 200,
  });

  const allDescriptions: string[] = [];

  // Process each chunk
  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];
    const chunkUserPrompt = fillTemplate(userPromptTemplate, {
      task: legalTask,
      content: `Document: ${docName} (Part ${i + 1} of ${chunks.length})\n\n${chunk.content}`,
      flowType: options.flowType || "unknown",
    });

    // Track chunk processing
    if (tokenTracker) {
      tokenTracker.trackContentTokens(
        chunk.content,
        "documents",
        `${docName}-chunk-${i + 1}`
      );
    }

    const compressedMessages = await LLMConnector.compressMessages({
      systemPrompt,
      userPrompt: chunkUserPrompt,
    });

    const result = await LLMConnector.getChatCompletion(compressedMessages, {
      temperature: options.temperature || 0.7,
    });

    if (result) {
      updateLastTokens(result, LLMConnector);
    }

    // Track LLM response
    if (tokenTracker) {
      tokenTracker.trackLLMResponse(
        result,
        "documentDescriptions",
        `chunk-${i + 1}`
      );
    }

    if (stageTracker) {
      stageTracker.addTokens(
        0, // TODO: Fix metrics property
        `chunk-${i + 1}`
      );
    }

    if (result?.textResponse) {
      allDescriptions.push(result.textResponse);
    }
  }

  stageTracker?.finish();

  // Combine descriptions with clear separators
  return allDescriptions.join("\n\n---\n\n");
}

/**
 * Generate document relevance check with iterative chunking for large documents
 */
export async function generateDocumentRelevanceIterative(
  docName: string,
  content: string,
  legalTask: string,
  LLMConnector: LLMConnector,
  options: GenerateOptions = {}
): Promise<boolean> {
  const contextManager = new ContextWindowManager(LLMConnector, {
    ...options,
    logTokenUsage: options.logTokenUsage || false,
  });
  const tm = new TokenManager(LLMConnector.model);
  const tokenTracker = contextManager.getTokenTracker();

  // Start tracking this stage
  const stageTracker = tokenTracker
    ? tokenTracker.startStage("relevanceChecking")
    : null;

  // Calculate token budget for relevance checking
  const systemPrompt =
    options.customSystemPrompt ||
    "You are an expert at evaluating whether a document is relevant to a legal task. Answer strictly with true or false.";

  const userPromptTemplate =
    options.customUserPromptTemplate ||
    `For the legal task "{{task}}", is the following document content relevant? Answer strictly "true" or "false":\n\n{{content}}`;

  const budget = contextManager.calculateTokenBudget({
    systemPrompt,
    userPromptTemplate: fillTemplate(userPromptTemplate, {
      task: legalTask,
      content: "",
      flowType: options.flowType || "unknown",
    }),
    reservedTokens: options.reservedTokens || 500,
  });

  const actualTokenCount = tm.countFromString(content);

  // Track content tokens
  if (tokenTracker) {
    tokenTracker.trackContentTokens(content, "documents", docName);
    tokenTracker.trackContentTokens(
      systemPrompt,
      "systemPrompts",
      "document-relevance"
    );
    // TODO: Fix budget type mismatch
    // tokenTracker.trackBudget(budget, actualTokenCount);
  }

  // If content fits within budget, use standard processing
  if (actualTokenCount <= budget.availableTokens) {
    const result = await generateDocumentRelevance(
      docName,
      content,
      legalTask,
      LLMConnector,
      options
    );

    // Track the LLM response
    // TODO: Fix metrics property - needs to be added to LLMConnector interface
    // if (tokenTracker && LLMConnector?.metrics?.lastCompletionTokens) {
    //   stageTracker?.addTokens(
    //     LLMConnector.metrics.lastCompletionTokens,
    //     "standard-processing"
    //   );
    // }

    stageTracker?.finish();
    return result;
  }

  console.log(
    `[ITERATIVE] Relevance check for ${docName} (${actualTokenCount} tokens) exceeds budget (${budget.availableTokens}). Using iterative processing.`
  );

  // Chunk the content
  const chunks = contextManager.chunkContent(content, {
    maxTokensPerChunk: budget.availableTokens,
    overlapTokens: options.overlapTokens || 100,
  });

  // Process each chunk - if ANY chunk is relevant, document is relevant
  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];
    const chunkUserPrompt = fillTemplate(userPromptTemplate, {
      task: legalTask,
      content: `Document: ${docName} (Part ${i + 1} of ${chunks.length})\n\n${chunk.content}`,
      flowType: options.flowType || "unknown",
    });

    // Track chunk processing
    if (tokenTracker) {
      tokenTracker.trackContentTokens(
        chunk.content,
        "documents",
        `${docName}-chunk-${i + 1}`
      );
    }

    const compressedMessages = await LLMConnector.compressMessages({
      systemPrompt,
      userPrompt: chunkUserPrompt,
    });

    const result = await LLMConnector.getChatCompletion(compressedMessages, {
      temperature: options.temperature || 0.3,
    });

    if (result) {
      updateLastTokens(result, LLMConnector);
    }

    // Track LLM response
    if (tokenTracker) {
      tokenTracker.trackLLMResponse(
        result,
        "relevanceChecking",
        `chunk-${i + 1}`
      );
    }

    if (stageTracker) {
      stageTracker.addTokens(
        0, // TODO: Fix metrics property
        `chunk-${i + 1}`
      );
    }

    // If any chunk is relevant, the document is relevant
    if (result?.textResponse?.trim().toLowerCase().startsWith("true")) {
      stageTracker?.finish();
      return true;
    }
  }

  stageTracker?.finish();

  // If no chunk was relevant, document is not relevant
  return false;
}

/**
 * High-level iterative section drafting processor that handles a list of sections
 * This is the function called by the flows (mainDoc.js, noMainDoc.js)
 */
export async function processIterativeSectionDraftingList(
  sectionList: DocumentSection[],
  options: SectionDraftingOptions,
  contextWindowManager: ContextWindowManager,
  tokenTracker: TokenTracker | null,
  callbacks: SectionDraftingCallbacks = {}
): Promise<DraftedSection[]> {
  const {
    legalTask,
    processedDocuments = [],
    // workspacePath, // Currently unused but kept in options for API compatibility
    temperature = 0.7,
    AllPrompts,
    flowType,
  } = options;

  const { onSectionProgress, onTokenUsage } = callbacks;

  // Get the section drafting prompts from AllPrompts
  let sectionDraftingPrompts: SectionDraftingPrompts;

  if (
    AllPrompts &&
    AllPrompts.CURRENT_DEFAULT_SECTION_DRAFTING &&
    "systemPrompt" in AllPrompts.CURRENT_DEFAULT_SECTION_DRAFTING &&
    "userPrompt" in AllPrompts.CURRENT_DEFAULT_SECTION_DRAFTING
  ) {
    sectionDraftingPrompts =
      AllPrompts.CURRENT_DEFAULT_SECTION_DRAFTING as unknown as SectionDraftingPrompts;
  } else {
    console.warn(
      "[SECTION DRAFTING] CURRENT_DEFAULT_SECTION_DRAFTING prompts not found, using fallback prompts"
    );
    sectionDraftingPrompts = {
      systemPrompt:
        "You are a legal document drafter. Create professional legal content for the given section.",
      userPrompt:
        "Draft section {{sectionNumber}} titled '{{title}}' for the legal task '{{task}}'. Use the provided documents: {{docs}}",
    };
  }

  const draftedSections: DraftedSection[] = [];

  for (let i = 0; i < sectionList.length; i++) {
    const section = sectionList[i];
    const sectionNumber = section.index_number || i + 1;
    const sectionTitle = section.title || `Section ${sectionNumber}`;

    if (onSectionProgress) {
      onSectionProgress(i, sectionTitle, "Starting");
    }

    // --- ENHANCED: Section drafting with keepalive events ---
    const MAX_RETRIES = 3;
    const RETRY_DELAY = 2000; // 2 seconds
    const KEEPALIVE_INTERVAL = 20000; // 20 seconds

    let keepaliveInterval: NodeJS.Timeout | null = null;
    let sectionResult: DraftedSection | null = null;
    let lastError: Error | null = null;

    try {
      // Start keepalive interval to prevent frontend timeout
      keepaliveInterval = setInterval(() => {
        if (onSectionProgress) {
          onSectionProgress(i, sectionTitle, "Drafting (in progress)");
        }
        console.log(
          `[SECTION DRAFTING] Keepalive: Section ${sectionNumber} - ${sectionTitle} is still being drafted...`
        );
      }, KEEPALIVE_INTERVAL);

      // Retry loop for section drafting
      for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
        try {
          console.log(
            `[SECTION DRAFTING] Section ${sectionNumber} (${sectionTitle}) - Attempt ${attempt}/${MAX_RETRIES}`
          );

          // Prepare documents for this section
          // Check both DisplayName and Doc Name for compatibility
          const relevantDocs = processedDocuments.filter(
            (doc: ProcessedDocument) =>
              (doc.DisplayName &&
                section?.relevant_documents?.includes(doc.DisplayName)) ||
              (doc["Doc Name"] &&
                section?.relevant_documents?.includes(doc["Doc Name"])) ||
              (doc["Doc Name"] &&
                section?.relevant_documents?.includes(
                  doc["Doc Name"].replace(".json", "")
                )) ||
              (doc.DisplayName &&
                section?.relevantDocumentNames?.includes(doc.DisplayName)) ||
              (doc["Doc Name"] &&
                section?.relevantDocumentNames?.includes(doc["Doc Name"])) ||
              (doc["Doc Name"] &&
                section?.relevantDocumentNames?.includes(
                  doc["Doc Name"].replace(".json", "")
                ))
          );

          // Prepare memos for this section (if any)
          const relevantMemos = section.generatedMemos || [];

          // Create section context
          const sectionContext: ProcessingSectionContext = {
            sectionNumber,
            sectionTitle,
            legalTask,
            neighborContext: `Section ${sectionNumber} of ${sectionList.length}`,
            flowType: flowType || "unknown",
            description: section.Description || section.description || "",
          };

          // Prepare documents for the processor
          // Use DisplayName first to avoid UUID exposure
          const documentsForProcessor: ProcessedDocument[] = relevantDocs.map(
            (doc: ProcessedDocument) => ({
              name: doc.DisplayName || doc["Doc Name"] || "Unknown Document",
              content: doc.Content || doc.pageContent || "",
            })
          );

          // Prepare memos for the processor
          const memosForProcessor: ProcessedMemo[] = relevantMemos.map(
            (memo: MemoData) => ({
              issue: memo.issue || memo.Issue || "Legal Issue",
              content: memo.memo || memo.content || "",
            })
          );

          const startTime = Date.now();

          // Use the lower-level processIterativeSectionDrafting function
          const result = await processIterativeSectionDraftingSingle({
            documents: documentsForProcessor,
            memos: memosForProcessor,
            iteration: 1,
            previousResult: "",
            context: sectionContext,
            // TODO: Fix LLMConnector access - need to get from dependencies
            LLMConnector: contextWindowManager as unknown as LLMConnector,
            prompts: {
              systemPrompt: sectionDraftingPrompts.systemPrompt,
              userPrompt: sectionDraftingPrompts.userPrompt,
            },
            temperature,
            tokenTracker,
          });

          const endTime = Date.now();
          const draftingTime = Math.round((endTime - startTime) / 1000);

          console.log(
            `[SECTION DRAFTING] Section ${sectionNumber} (${sectionTitle}) completed in ${draftingTime}s on attempt ${attempt}`
          );

          // Success - store result and break retry loop
          sectionResult = {
            ...section,
            draftedContent: result.content,
            tokenUsage: {
              totalTokens: result.tokensUsed,
              iterations: 1,
              isIterative: false,
            },
          };

          lastError = null;
          break; // Exit retry loop on success
        } catch (attemptError: unknown) {
          lastError =
            attemptError instanceof Error
              ? attemptError
              : new Error(String(attemptError));
          console.error(
            `[SECTION DRAFTING] Section ${sectionNumber} attempt ${attempt} failed:`,
            attemptError instanceof Error
              ? attemptError.message
              : String(attemptError)
          );

          if (attempt < MAX_RETRIES) {
            console.log(
              `[SECTION DRAFTING] Retrying section ${sectionNumber} in ${RETRY_DELAY}ms...`
            );
            await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY));
          }
        }
      }

      // Clear keepalive interval
      if (keepaliveInterval) {
        clearInterval(keepaliveInterval);
        keepaliveInterval = null;
      }

      // Check if we have a successful result
      if (sectionResult && !lastError) {
        draftedSections.push(sectionResult);

        if (onSectionProgress) {
          onSectionProgress(i, sectionTitle, "Complete");
        }

        if (onTokenUsage) {
          onTokenUsage(i, sectionResult.tokenUsage);
        }
      } else {
        // All retries failed
        throw (
          lastError ||
          new Error(
            `Failed to draft section ${sectionNumber} after ${MAX_RETRIES} attempts`
          )
        );
      }
    } catch (error: unknown) {
      console.error(
        `Error drafting section ${sectionNumber}:`,
        error instanceof Error ? error.message : String(error)
      );

      // Clear keepalive interval on error
      if (keepaliveInterval) {
        clearInterval(keepaliveInterval);
        keepaliveInterval = null;
      }

      // Add a section with error content
      const errorSection: DraftedSection = {
        ...section,
        draftedContent: `Error drafting section: ${error instanceof Error ? error.message : String(error)}`,
        tokenUsage: { totalTokens: 0, iterations: 0, isIterative: false },
      };

      draftedSections.push(errorSection);

      if (onSectionProgress) {
        onSectionProgress(i, sectionTitle, "Error");
      }
    }
  }

  return draftedSections;
}

/**
 * Iterative section drafting processor for use with ContextWindowManager
 */
export async function processIterativeSectionDraftingSingle({
  documents = [],
  memos = [],
  iteration,
  previousResult = "",
  context = {} as ProcessingSectionContext,
  LLMConnector,
  prompts,
  temperature = 0.7,
  tokenTracker = null,
}: {
  documents?: ProcessedDocument[];
  memos?: ProcessedMemo[];
  iteration: number;
  previousResult?: string;
  context: ProcessingSectionContext;
  LLMConnector: LLMConnector;
  prompts: SectionDraftingPrompts;
  temperature?: number;
  tokenTracker?: TokenTracker | null;
}): Promise<SectionDraftingResult> {
  const {
    sectionNumber,
    sectionTitle,
    legalTask,
    neighborContext,
    flowType,
    description,
  } = context;

  // Track stage if tokenTracker is available
  const operationName =
    iteration === 1 ? "initial-drafting" : "refinement-drafting";

  // Prepare content for this iteration
  let sectionContext = "Relevant Documents:\n";

  if ((documents?.length ?? 0) > 0) {
    for (const doc of documents) {
      sectionContext += `Document: ${doc.name}\n${doc.content}\n\n`;

      // Track document content tokens
      if (tokenTracker) {
        tokenTracker.trackContentTokens(
          doc.content,
          "documents",
          `${doc.name}-iter-${iteration}`
        );
      }
    }
  } else {
    sectionContext += "(No document content available for this iteration)\n";
  }

  sectionContext += "\nRelevant Legal Memos:\n";
  if ((memos?.length ?? 0) > 0) {
    for (const memo of memos) {
      sectionContext += `Memo for Issue: ${memo.issue}\n${memo.content}\n\n`;

      // Track memo content tokens
      if (tokenTracker) {
        tokenTracker.trackContentTokens(
          memo.content,
          "memos",
          `${memo.issue}-iter-${iteration}`
        );
      }
    }
  } else {
    sectionContext += "(No specific legal memos for this iteration)\n";
  }

  let userPrompt: string;

  if (iteration === 1) {
    // Initial iteration - create from scratch
    userPrompt = fillTemplate(prompts.initialUserPrompt || prompts.userPrompt, {
      sectionNumber: sectionNumber.toString(),
      title: sectionTitle,
      task: legalTask,
      docs: sectionContext,
      neighborContext: neighborContext || "(No neighboring sections defined)",
      flowType: flowType || "unknown",
      description: description || "",
    });
  } else {
    // Refinement iteration - enhance existing content
    userPrompt = fillTemplate(prompts.refineUserPrompt || prompts.userPrompt, {
      sectionNumber: sectionNumber.toString(),
      title: sectionTitle,
      task: legalTask,
      previousDraft: previousResult,
      newContent: sectionContext,
      neighborContext: neighborContext || "(No neighboring sections defined)",
      flowType: flowType || "unknown",
      description: description || "",
    });

    // Track previous result tokens
    if (tokenTracker && previousResult) {
      tokenTracker.trackContentTokens(
        previousResult,
        "previousResults",
        `section-${sectionNumber}-iter-${iteration - 1}`
      );
    }
  }

  // Track prompt tokens
  if (tokenTracker) {
    tokenTracker.trackContentTokens(
      prompts.systemPrompt,
      "systemPrompts",
      `section-drafting-iter-${iteration}`
    );
    tokenTracker.trackContentTokens(
      userPrompt,
      "userPrompts",
      `section-${sectionNumber}-iter-${iteration}`
    );
  }

  const compressedMessages = await LLMConnector.compressMessages({
    systemPrompt: prompts.systemPrompt,
    userPrompt,
  });

  const result = await LLMConnector.getChatCompletion(compressedMessages, {
    temperature,
  });

  if (result) {
    updateLastTokens(result, LLMConnector);
  }

  // Track LLM response
  if (tokenTracker) {
    tokenTracker.trackLLMResponse(
      result,
      "sectionDrafting",
      `section-${sectionNumber}-${operationName}`
    );
  }

  return {
    content: result?.textResponse?.trim() || "",
    tokensUsed: 0, // TODO: Fix metrics property
    iteration,
    documentsProcessed: documents.length,
    memosProcessed: memos.length,
    llmResponse: result as unknown as LLMResponse, // Type assertion for compatibility
  };
}

/**
 * Creates a robust abort checker function that properly handles client cancellation
 */
export function createAbortChecker(
  abortSignal: AbortSignal | undefined,
  chatId: string,
  _response: Response,
  flowType: string = "unknown"
): () => void {
  // Note: response is kept for API compatibility even though it's not currently used
  return () => {
    if (abortSignal && abortSignal.aborted) {
      const error = new ChatError(
        `Client aborted - terminating ${flowType} document flow.`,
        "ABORT_SIGNAL",
        { chatId, flowType }
      );
      error.isAbortSignal = true;
      error.chatId = chatId;
      error.flowType = flowType;
      throw error;
    }
  };
}

/**
 * Handles abort signal errors by cleaning up and notifying the frontend
 */
export function handleAbortSignal(
  error: ChatError,
  purgeDocumentBuilder: (params: { uuid: string }) => void,
  writeResponseChunk: (response: Response, data: ResponseChunkData) => void,
  response: Response | null = null
): null {
  if (!error.isAbortSignal) {
    throw error; // Re-throw non-abort errors
  }

  console.log(
    `[${(error.flowType || "UNKNOWN").toUpperCase()} FLOW] Flow terminated due to client abort: ${error.message}`
  );

  // Clean up using existing purgeDocumentBuilder
  try {
    purgeDocumentBuilder({ uuid: error.chatId || "" });
    console.log(
      `[${(error.flowType || "UNKNOWN").toUpperCase()} FLOW] Cleaned up document builder files for chat ${error.chatId || "unknown"}`
    );
  } catch (cleanupError: unknown) {
    console.error(
      `[${(error.flowType || "UNKNOWN").toUpperCase()} FLOW] Error during cleanup: ${cleanupError instanceof Error ? cleanupError.message : String(cleanupError)}`
    );
  }

  // Send abort notification to frontend if response is available
  if (response && writeResponseChunk) {
    try {
      writeResponseChunk(response, {
        uuid: error.chatId || "",
        type: "cdbProgress",
        flowType: error.flowType || "unknown",
        status: "aborted",
        progress: -3, // Special progress value for aborted state
        message: "Document processing was cancelled by user.",
      });

      // Send final response
      writeResponseChunk(response, {
        uuid: error.chatId || "",
        type: "text",
        text: "Document processing was cancelled by the user.",
        sources: [],
        close: true,
      });
    } catch (responseError: unknown) {
      console.error(
        `[${(error.flowType || "UNKNOWN").toUpperCase()} FLOW] Failed to send abort notification:`,
        responseError instanceof Error
          ? responseError.message
          : String(responseError)
      );
    }
  }

  return null; // Return null to indicate abortion
}
