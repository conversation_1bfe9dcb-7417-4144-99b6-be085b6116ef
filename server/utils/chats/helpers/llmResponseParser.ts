// helpers/llmResponseParser.ts

import { LLMConnector } from "../LLMConnector";

// LLM Response Parser interfaces
export interface ParseResult<T = unknown> {
  success: boolean;
  data: T | null;
  error: string | null;
  rawResponse?: string;
  attemptsMade?: number;
}

export interface ParseOptions {
  maxAttempts?: number;
  validator?: (data: unknown) => boolean;
  enableLlmFix?: boolean;
  llmConnector?: LLMConnector; // Properly typed LLMConnector
}

export interface RegexParseOptions {
  minMatches?: number;
  failOnEmptyInput?: boolean;
}

export interface SuggestionEdit {
  id: string;
  type: "CHANGE" | "ADD" | "DELETE";
  reason: string;
  content: string;
}

export interface ValidationResult {
  id: string;
  type: "APPROVE" | "DISAPPROVE";
  comment: string;
}

export interface SuggestionsParseResult {
  success: boolean;
  edits: SuggestionEdit[] | null;
  error: string | null;
}

export interface ValidationParseResult {
  success: boolean;
  results: Record<string, { type: string; comment: string }> | null;
  error: string | null;
}

// Shared logger instance
const logParser = (
  level: string,
  message: string,
  data: unknown = null
): void => {
  const now = new Date();
  const year = now.getFullYear();
  const month = (now.getMonth() + 1).toString().padStart(2, "0");
  const day = now.getDate().toString().padStart(2, "0");
  const hours = now.getHours().toString().padStart(2, "0");
  const minutes = now.getMinutes().toString().padStart(2, "0");
  const seconds = now.getSeconds().toString().padStart(2, "0");
  const timestamp = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

  const logPrefix = `[LLMParser:${level.toUpperCase()}][${timestamp}]`;
  const previewLength = 500; // Max length for data preview

  if (data) {
    let dataPreview: string;
    try {
      // Attempt to stringify, handles objects/arrays better
      dataPreview = JSON.stringify(data);
      if (dataPreview.length > previewLength) {
        dataPreview = dataPreview.substring(0, previewLength) + "...";
      }
    } catch {
      // Fallback for non-serializable data or errors
      dataPreview =
        String(data).substring(0, previewLength) +
        (String(data).length > previewLength ? "..." : "");
    }
    console.log(`${logPrefix} ${message}`, dataPreview);
  } else {
    console.log(`${logPrefix} ${message}`);
  }
};

/**
 * Filters out invalid ASCII control characters from a string, except for tab, newline, and carriage return.
 */
export function filterInvalidControlChars(text: string): string {
  if (typeof text !== "string") return "";
  // This regex removes characters in the C0 control character range (U+0000 to U+001F)
  // except for tab (U+0009), newline (U+000A), and carriage return (U+000D), which are valid in JSON strings.
  // It also removes the DEL character (U+007F).
  // eslint-disable-next-line no-control-regex
  return text.replace(/[\u0000-\u0008\u000B\u000C\u000E-\u001F\u007F]/g, "");
}

/**
 * Extracts the first potential JSON object or array string from a larger text block.
 */
export function extractPotentialJsonString(text: string): string | null {
  if (!text || typeof text !== "string") {
    return null;
  }

  // 1. Remove common markdown code fences more thoroughly
  // Handle multi-line code blocks and variations
  let cleanedText = text
    .replace(/^```(?:json|JSON)?\s*\n?/m, "") // Start fence with optional newline
    .replace(/\n?\s*```$/m, "") // End fence with optional newline
    .trim();

  // Also handle inline code blocks that might contain JSON
  if (
    cleanedText.includes("```") &&
    (cleanedText.includes("{") || cleanedText.includes("["))
  ) {
    // Extract content between backticks
    const codeBlockMatch = cleanedText.match(
      /```(?:json|JSON)?\s*\n?([\s\S]*?)\n?\s*```/
    );
    if (codeBlockMatch && codeBlockMatch[1]) {
      cleanedText = codeBlockMatch[1].trim();
    }
  }

  // 2. Find the first opening bracket ({ or [)
  const firstBracketIndex = cleanedText.search(/[{[]/);
  if (firstBracketIndex === -1) {
    // Don't log at debug level for normal parsing attempts
    return null; // No JSON start found
  }

  // 3. Find the last closing bracket (} or ]) that corresponds to the first opening bracket
  let openCount = 0;
  let lastClosingIndex = -1;
  const startChar = cleanedText[firstBracketIndex];
  const endChar = startChar === "{" ? "}" : "]";

  for (let i = firstBracketIndex; i < cleanedText.length; i++) {
    if (cleanedText[i] === startChar) {
      openCount++;
    } else if (cleanedText[i] === endChar) {
      openCount--;
      if (openCount === 0) {
        lastClosingIndex = i;
        break; // Found the matching closing bracket
      }
    }
  }

  if (lastClosingIndex === -1) {
    // Fallback: find the very last closing bracket (less accurate)
    const lastBracketIndex = cleanedText.lastIndexOf("}");
    const lastSquareBracketIndex = cleanedText.lastIndexOf("]");
    lastClosingIndex = Math.max(lastBracketIndex, lastSquareBracketIndex);

    if (lastClosingIndex === -1 || lastClosingIndex < firstBracketIndex) {
      return null;
    }
  }

  // 4. Extract the potential JSON string
  const potentialJson = cleanedText.substring(
    firstBracketIndex,
    lastClosingIndex + 1
  );
  return potentialJson;
}

/**
 * Attempts to parse a string as JSON, with retries and optional LLM-based fixing.
 */
export async function parseLLMJsonResponse(
  llmResponseText: string,
  options: ParseOptions = {}
): Promise<ParseResult> {
  const {
    maxAttempts = 2, // Note: this is for parsing + LLM fix attempts within this function
    validator = null,
    enableLlmFix = false,
    llmConnector = null,
  } = options;

  const filteredLlmResponseText = filterInvalidControlChars(llmResponseText);

  let currentText = filteredLlmResponseText;
  let parsedJson: unknown = null;
  let lastError: string | null = null;
  let attemptsMade = 0;

  if (!currentText || typeof currentText !== "string") {
    return {
      success: false,
      data: null,
      error: "Input text is empty or not a string.",
      rawResponse: filteredLlmResponseText,
      attemptsMade,
    };
  }

  while (attemptsMade < maxAttempts) {
    attemptsMade++;
    lastError = null;

    const extractedText = extractPotentialJsonString(currentText);
    let textToParse = extractedText;

    if (!textToParse) {
      lastError = "Could not extract a potential JSON string.";
      if (
        (attemptsMade === 1 && currentText.trim().startsWith("{")) ||
        currentText.trim().startsWith("[")
      ) {
        textToParse = currentText.trim();
      } else {
        if (attemptsMade >= maxAttempts || !enableLlmFix || !llmConnector)
          break;
        // If extraction fails and we have LLM fix, we'll use original text for the fix
        currentText = filteredLlmResponseText;
      }
    }

    if (textToParse) {
      try {
        // Attempt to parse the JSON
        parsedJson = JSON.parse(textToParse);
        // log.info(`Successfully parsed JSON within LLMParser (attempt ${attemptsMade}/${maxAttempts}).`);

        // If a validator function is provided, use it
        if (validator && typeof validator === "function") {
          if (!validator(parsedJson)) {
            lastError = "Parsed JSON failed structure validation.";
            logParser(
              "warn",
              `${lastError} (Parser attempt ${attemptsMade}/${maxAttempts}).`,
              {
                parsedJsonPreview: JSON.stringify(parsedJson).substring(0, 100),
              }
            );
            parsedJson = null;
          } else {
            // Success - no need to log unless there were multiple attempts
            if (attemptsMade > 1) {
              logParser(
                "info",
                `Parsed JSON passed structure validation after ${attemptsMade} attempts.`
              );
            }
            return {
              success: true,
              data: parsedJson,
              error: null,
              rawResponse: filteredLlmResponseText,
              attemptsMade,
            };
          }
        } else {
          return {
            success: true,
            data: parsedJson,
            error: null,
            rawResponse: filteredLlmResponseText,
            attemptsMade,
          };
        }
      } catch (parseError: unknown) {
        lastError = (parseError as Error).message || "Failed to parse JSON";
        logParser(
          "warn",
          `JSON parse error (Parser attempt ${attemptsMade}/${maxAttempts}): ${lastError}`
        );
        parsedJson = null;
      }
    }

    // Retry / LLM Fix Logic
    if (attemptsMade < maxAttempts && enableLlmFix && llmConnector) {
      try {
        // Prompts are in English as per the example file
        const fixSystemPrompt = `You are a computer programmer and JSON expert. Your task is to correct potentially malformed JSON text. Only output the corrected JSON object or array. Do not add any explanatory text, markdown, or code block fences around the JSON. Ensure the JSON is complete and syntactically correct.`;
        const fixUserPrompt = `The following text should contain a valid JSON object or array, but it might be malformed or incomplete. Please correct it and provide only the valid JSON as your response. Input text:\n${currentText}\n\nCorrected JSON:`;

        const fixCompressedMessages = await llmConnector.compressMessages({
          // Assuming llmConnector has compressMessages
          systemPrompt: fixSystemPrompt,
          userPrompt: fixUserPrompt,
        });

        const fixResult = await llmConnector.getChatCompletion(
          // Assuming llmConnector has getChatCompletion
          fixCompressedMessages,
          { temperature: 0.1 } // Low temperature for deterministic fix
        );
        currentText = fixResult?.textResponse || currentText; // Use the fixed text for the next parsing attempt
        // The loop will continue, and the next iteration will use this 'currentText'
      } catch (fixError: unknown) {
        logParser(
          "error",
          `LLM call for JSON fix failed: ${(fixError as Error).message}`
        );
        lastError = `LLM fix attempt failed: ${(fixError as Error).message}`;
        break; // Critical error during fix, stop further attempts in this function.
      }
    } else if (attemptsMade < maxAttempts && !parsedJson) {
      // Only log retry if we actually failed and are not LLM fixing
      logParser(
        "info",
        `JSON parsing failed on parser attempt ${attemptsMade}/${maxAttempts}. Will try again if more attempts remain.`
      );
      currentText = filteredLlmResponseText; // Reset to original filtered text for the next parsing attempt
    }
  } // End while loop

  logParser(
    "error",
    `All JSON parsing attempts failed within LLMParser after ${attemptsMade} attempts. Last error: ${lastError}.`,
    {
      originalTextPreview: filteredLlmResponseText.substring(0, 200),
    }
  );

  return {
    success: false,
    data: null,
    error:
      lastError || "All attempts to parse JSON failed after potential LLM fix.",
    rawResponse: filteredLlmResponseText,
    attemptsMade,
  };
}

// --- GENERIC REGEX PARSER (from example, keeping as is for now) ---
/**
 * Parses raw text using a regular expression and transforms matches into structured data.
 */
export function parseWithRegex<T>(
  rawText: string,
  regex: RegExp,
  transformFn: (match: RegExpExecArray) => T | null,
  options: RegexParseOptions = {}
): ParseResult<T[]> {
  const { minMatches = 0, failOnEmptyInput = true } = options;
  const results: T[] = [];

  if (
    !rawText ||
    typeof rawText !== "string" ||
    (failOnEmptyInput && rawText.trim().length === 0)
  ) {
    const errorMsg = "Input text is empty or invalid.";
    logParser("warn", `Regex parsing failed: ${errorMsg}`);
    return { success: false, data: null, error: errorMsg };
  }

  if (!regex.global) {
    const errorMsg = "Regex must have the global 'g' flag enabled.";
    logParser("error", `Regex parsing setup error: ${errorMsg}`);
    return { success: false, data: null, error: errorMsg };
  }

  let match: RegExpExecArray | null;
  let execCounter = 0;
  const maxExec = 10000;

  try {
    while ((match = regex.exec(rawText)) !== null && execCounter < maxExec) {
      execCounter++;
      try {
        const transformed = transformFn(match);
        if (transformed !== null) {
          results.push(transformed);
        }
      } catch (transformError: unknown) {
        logParser(
          "warn",
          "Error during regex match transformation, skipping match.",
          {
            matchPreview: match[0].substring(0, 50),
            error:
              transformError instanceof Error
                ? transformError.message
                : String(transformError),
          }
        );
      }
    }
    if (execCounter >= maxExec) {
      logParser(
        "error",
        `Regex parsing safety break triggered after ${maxExec} executions.`
      );
      return {
        success: false,
        data: null,
        error: "Regex execution limit reached, potential infinite loop.",
      };
    }
  } catch (regexError: unknown) {
    logParser(
      "error",
      `Error during regex execution: ${regexError instanceof Error ? regexError.message : String(regexError)}`
    );
    return {
      success: false,
      data: null,
      error: `Regex execution failed: ${regexError instanceof Error ? regexError.message : String(regexError)}`,
    };
  }

  // Only log if there's something noteworthy
  if (results.length === 0 && execCounter > 0) {
    logParser(
      "debug",
      `Regex parsing complete. Found ${results.length} valid matches from ${execCounter} attempts.`
    );
  }

  if (results.length < minMatches) {
    const errorMsg = `Parsing failed: Found ${results.length} matches, but minimum required is ${minMatches}.`;
    logParser(
      "warn",
      errorMsg +
        (execCounter > 0 ? " Check response format." : " No matches found.")
    );
    return { success: false, data: null, error: errorMsg };
  }

  if (
    minMatches === 0 &&
    results.length === 0 &&
    execCounter > 0 &&
    rawText.trim().length > 0
  ) {
    logParser(
      "warn",
      `Regex parsing resulted in 0 transformed items, though regex matches might have occurred. Input format might be incorrect or transformFn skipped all.`
    );
    return { success: true, data: [], error: null };
  }

  return { success: true, data: results, error: null };
}

// Specific parsers from example
export function parseSuggestionsLLMResponse(
  rawResponse: string
): SuggestionsParseResult {
  const suggestionRegex =
    /<Line id="(\d+)"\s+type="(CHANGE|ADD|DELETE)"\s+reason="((?:[^"\\]|\\.)*)">([\s\S]*?)<\/Line id="\1">/g;

  const transformFn = (match: RegExpExecArray): SuggestionEdit | null => {
    const [, id, type, reason, rawContent] = match;
    const decodedContent = rawContent
      .replace(/&amp;/g, "&")
      .replace(/&lt;/g, "<")
      .replace(/&gt;/g, ">")
      .replace(/&quot;/g, '"')
      .replace(/&apos;/g, "'")
      .trim();

    if (!["CHANGE", "ADD", "DELETE"].includes(type)) {
      logParser(
        "warn",
        `Skipping suggestion transform: Invalid type '${type}' for line ${id}.`
      );
      return null;
    }
    if ((type === "CHANGE" || type === "ADD") && !decodedContent) {
      logParser(
        "warn",
        `Skipped ${type} suggestion for line ${id} due to missing content after decoding/trimming.`
      );
      return null;
    }
    if (type === "DELETE" && decodedContent) {
      logParser(
        "warn",
        `Found unexpected content for DELETE suggestion on line ${id}. Ignoring content.`
      );
      return { id, type: type as "DELETE", reason, content: "" };
    } else {
      return {
        id,
        type: type as "CHANGE" | "ADD" | "DELETE",
        reason,
        content: decodedContent,
      };
    }
  };
  const result = parseWithRegex<SuggestionEdit>(
    rawResponse,
    suggestionRegex,
    transformFn,
    {
      minMatches: 0,
    }
  );
  return { success: result.success, edits: result.data, error: result.error };
}

export function parseValidationLLMResponse(
  rawValidationResponse: string
): ValidationParseResult {
  const validationRegex =
    /<Line id="(\d+)"\s+type="(APPROVE|DISAPPROVE)"\s+comment="([^"]*)">\s*<\/Line id="\1">/g;
  const transformFn = (match: RegExpExecArray): ValidationResult | null => {
    const [, id, type, comment] = match;
    if (!["APPROVE", "DISAPPROVE"].includes(type)) {
      logParser(
        "warn",
        `Skipping validation transform: Invalid type '${type}' for line ${id}.`
      );
      return null;
    }
    return { id, type: type as "APPROVE" | "DISAPPROVE", comment };
  };
  const parseResult = parseWithRegex<ValidationResult>(
    rawValidationResponse,
    validationRegex,
    transformFn,
    { minMatches: 0 }
  );
  if (!parseResult.success) {
    return { success: false, results: null, error: parseResult.error };
  }
  const aggregatedResults: Record<string, { type: string; comment: string }> =
    {};
  if (parseResult.data) {
    for (const item of parseResult.data) {
      aggregatedResults[item.id] = { type: item.type, comment: item.comment };
    }
  }
  logParser(
    "info",
    `Aggregated ${Object.keys(aggregatedResults).length} validation results.`
  );
  return { success: true, results: aggregatedResults, error: null };
}

/**
 * Extracts a JSON object from a text that might contain other non-JSON content.
 */
export function safeJsonParse(text: string): ParseResult {
  if (typeof text !== "string") {
    return {
      success: false,
      data: null,
      error: "Invalid input: Not a string.",
    };
  }

  try {
    // First, remove any non-printable ASCII characters that might be lingering,
    // which are not valid in JSON strings (except for allowed escaped characters).
    // This regex removes characters in the C0 control character range (except for tab, newline, carriage return).
    // It also removes the DEL character.
    const cleanText = text.replace(
      // eslint-disable-next-line no-control-regex
      /[\u0000-\u0008\u000B\u000C\u000E-\u001F\u007F]/g,
      ""
    );
    const data = JSON.parse(cleanText);
    return { success: true, data, error: null };
  } catch (error: unknown) {
    return {
      success: false,
      data: null,
      error: error instanceof Error ? error.message : "Failed to parse JSON",
    };
  }
}
