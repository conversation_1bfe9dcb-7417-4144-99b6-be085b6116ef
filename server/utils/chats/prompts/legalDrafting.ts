/**
 * Default prompts for legal document drafting
 *
 * This module centralizes all prompts used in the document drafting flows,
 * both for the main document flow and the no-main-document flow.
 *
 * DEVELOPER NOTE: When adding a new prompt that will be configurable via a system setting:
 * 1. Ensure the `systemSettingName` (e.g., "cdb_new_prompt_system_prompt") is added to
 *    `SystemSettings.protectedFields` (or `supportedFields` if appropriate) in `server/models/systemSettings.js`.
 * 2. If the setting requires specific validation (e.g., number, boolean), add a validator
 *    to `SystemSettings.validations` in `server/models/systemSettings.js`.
 * 3. Ensure the new setting key is retrievable via `SystemSettings.currentSettings()`
 *    (and other relevant settings aggregation methods like `llmPreferenceKeys` if applicable)
 *    in `server/models/systemSettings.js` so it can be managed in the UI.
 * 4. The `generateSystemSettingPKeyForLegalDrafting` helper can be used to create consistent
 *    `systemSettingName` values (e.g., `cdb_yourpromptkey_system_prompt`).
 *    The test suite in `server/utils/chats/prompts/__tests__/legalDrafting.test.js` will automatically
 *    verify that this `systemSettingName` is a known key in `SystemSettings`.
 *
 * INTERNATIONALIZATION (i18n) NOTE:
 * When adding or modifying entries in `exportedLegalPrompts` for the Document Builder page:
 * - For `GROUP_TITLE` and `GROUP_DESCRIPTION` entries:
 *   - The `label` field (e.g., "document-builder.prompts.group.document_summary.title") is the translation key.
 *   - The `defaultContent` field provides the English translation for this key.
 * - For `SYSTEM_PROMPT`, `USER_PROMPT`, and `PROMPT_TEMPLATE` entries:
 *   - The `label` field (e.g., "document-builder.prompts.document-summary-system-label") is the translation key for the prompt's title/label on the UI.
 *     Its English text should be the `defaultContent` of this entry.
 *   - The `description` field (e.g., "document-builder.prompts.document-summary-system-description") is the translation key for the prompt's help text/description on the UI.
 *     Its English text should be a human-readable explanation of the prompt.
 * - All these translation keys (derived from `label` and `description` fields) MUST be added to all frontend locale files
 *   (e.g., `frontend/src/locales/en/common.js`, `frontend/src/locales/sv/common.js`, etc.) with their respective translations.
 */

// ======================================================================
// TYPE DEFINITIONS
// ======================================================================

export interface PromptTemplate {
  SYSTEM_PROMPT: string;
  USER_PROMPT: string;
}

export interface PromptWithTemplate {
  PROMPT_TEMPLATE: string;
}

export interface SystemPromptOnly {
  SYSTEM_PROMPT: string;
}

export type PromptFieldType =
  | "GROUP_TITLE"
  | "GROUP_DESCRIPTION"
  | "SYSTEM_PROMPT"
  | "USER_PROMPT"
  | "PROMPT_TEMPLATE";

export interface ExportedLegalPrompt {
  /** Identifier for the prompt group (e.g., "DEFAULT_DOCUMENT_SUMMARY"). */
  promptKey: string;
  /** The specific field this entry represents. */
  promptField: PromptFieldType;
  /** Translation key for the UI label/title. */
  label: string;
  /** Default English text. For GROUP_TITLE/GROUP_DESCRIPTION, this is the translation for `label`. For actual prompts, this is the prompt content itself. */
  defaultContent: string;
  /** Optional. The system setting name if this prompt is configurable. */
  systemSettingName?: string;
  /** Optional. Translation key for the UI description/help text (primarily for SYSTEM_PROMPT, USER_PROMPT, PROMPT_TEMPLATE). */
  description?: string;
}

// ======================================================================
// SHARED PROMPTS - Used by multiple flows
// ======================================================================

// Document description/summary prompt
export const DEFAULT_DOCUMENT_SUMMARY: PromptTemplate = {
  SYSTEM_PROMPT:
    "You are an expert at analyzing legal documents and providing concise descriptions. Make a detailed summary of the document content and how it is relevant to the legal task. Write the summary in the same language as the legal task.",
  USER_PROMPT: `Generate a detailed summary of the following document content in relation to the legal task "{{task}}". Focus on the main topics and key information:\n\n{{content}}`,
};

// Document relevance check prompt
export const DEFAULT_DOCUMENT_RELEVANCE: PromptTemplate = {
  SYSTEM_PROMPT:
    "You are an expert at evaluating whether a document is relevant to a legal task. Answer strictly with true or false. Respond in the same language used in the legal task prompt.\n\nFlow-specific considerations:\n- Main Document Flow: Assess relevance as supporting material to the main document\n- No Main Document Flow: Evaluate comprehensive relevance for multi-source synthesis\n- Reference Files Flow: Determine compliance-related relevance and regulatory applicability",
  USER_PROMPT: `For the legal task "{{task}}" (Flow: {{flowType}}), is the following document content relevant? Answer strictly "true" or "false":\n\n{{content}}`,
};

// Section drafting prompt
export const DEFAULT_SECTION_DRAFTING: PromptTemplate = {
  SYSTEM_PROMPT: `You are a legal expert specialized in writing professional legal documents.
    You write highly professional legal texts, using only the specified documents and respecting the flow of neighboring sections.
    Your text is persuasive and completely in line with the legal task.
    You only write in the same language as the legal task.
    You divide the legal content logically into numbered sections, sub-sections, and sub-sub-sections.
    You carefully mark out headings and subheadings with markdown.
    You provide proper numbering and title for every subsection.
    You do not include any summary at the end of the section.
    You do not include any additional text or explanations outside of the section content.
    You write the final text without placeholders.
    NEVER create empty bullet points or list items - every bullet point must contain meaningful content.
    NEVER use placeholder text like "..." or "[content to be added]" - write complete, substantive text.
    
    Flow-specific guidance:
    - Main Document Flow: Center analysis around the main document while incorporating supporting materials
    - No Main Document Flow: Synthesize information from all sources with equal weight
    - Reference Files Flow: Focus on compliance analysis and comparison against reference standards`,

  USER_PROMPT: `Generate section {{sectionNumber}} titled "{{title}}" for the legal task "{{task}}".
    
    Flow Context: {{flowType}}
    
    Use content from these documents: {{docs}}.
    Also consider these other sections: {{neighborContext}}.
    
    Section Description: {{description}}
    
    Output only the content of this section, with no placeholders.
    Ensure the output is in the same language as the legal task.
    The section text should be coherent and well-structured, with a clear flow of ideas.
    Completely avoid placeholders.
    Structure the legal content logically with numbered sub-sections and sub-sub-sections as needed.
    You carefully mark out headings and subheadings with markdown.
    Provide proper numbering and title for every subsection.
    You do not include any summary at the end of the section.
    Use legal terminology appropriately and ensure that the content is relevant to the legal task.
    Do not include any additional text or explanations outside of the section content.
    CRITICAL: Do not create empty bullet points or list items. Every bullet point must contain meaningful, complete text.
    CRITICAL: Do not use placeholder text - write complete, substantive content for every element.`,
};

// Section legal issues identification prompt
export const DEFAULT_SECTION_LEGAL_ISSUES: PromptTemplate = {
  SYSTEM_PROMPT:
    'You are an expert legal analyst. Your task is to identify specific legal topics for which factual information should be retrieved to support drafting the given section. Focus on clarifying legal context, not solving the overall task. Use the same language as the legal task. For each topic, suggest the most relevant Legal-QA workspace slug. The topics should be relevant to the section and legal task and aim to keep memo retrieval concise, i.e. try to keep the number of topics at a necessary minimum.\n\nFlow-specific considerations:\n- Main Document Flow: Focus on legal issues that arise from or relate to the main document\'s content\n- No Main Document Flow: Identify comprehensive legal topics that synthesize across all source documents\n- Reference Files Flow: Prioritize compliance-related legal issues and regulatory framework questions\n\nReturn your answer strictly as valid JSON. Example output:\n[\n  {"Issue": "Jurisdiction for the current case", "WORKSPACE_SLUG_FOR_LEGALDATA": "civil-procedure"},\n  {"Issue": "Contract formation", "WORKSPACE_SLUG_FOR_LEGALDATA": "contracts"}\n]',
  USER_PROMPT: `For section {{sectionNumber}} titled "{{title}}" of the legal task "{{task}}", and considering the relevant documents: {{docs}}, list the specific legal topics or data points for which neutral background information (e.g., definitions, statute summaries, key case principles) should be fetched from a Legal-QA database. Do not try to solve the task itself.\n\nFlow Context: {{flowType}}\n\nReturn ONLY the JSON array described above (no markdown/code fences) where each element contains an "Issue" and a "WORKSPACE_SLUG_FOR_LEGALDATA" indicating the best-matching workspace slug from the list provided in the user prompt.`,
};

// Legal memo creation prompt
export const DEFAULT_MEMO_CREATION: PromptWithTemplate = {
  PROMPT_TEMPLATE:
    'Create a legal memorandum addressing the legal issue "{{issue}}" in the same language as the legal task "{{task}}. In the analysis, consider the following documents (reference them by name where relevant): {{docs}}. Include extensive legal analysis, jurisprudence, references to relevant legal sections in applicable legislation analysis in a structured format. The purpose of the memo is to provide the local legal context for the continued drafting of part of a response stemming fromthe legal task',
};

// Document-to-section index mapping prompt
export const DEFAULT_SECTION_INDEX: SystemPromptOnly = {
  SYSTEM_PROMPT: `You are a legal analyst. Decide which sections are supported by the given document content.
Return ONLY a JSON array of section index numbers (integers). No markdown fences.
Example output: [1,3,5]
Ensure any explanatory text (if unavoidable) is in the same language as the legal task.`,
};

// Section draft refinement prompt
export const SECTION_DRAFT_REFINEMENT: PromptTemplate = {
  SYSTEM_PROMPT: `You are a highly experienced expert legal document author.
    Your task is to improve an existing version of a document section.
    Improve text clarity, coherence, accuracy, and overall quality.
    Focus on the specific instructions for refinement provided in the user prompt.
    Write in the same language as the legal task.
    Remove all placeholder text, writing instructions or meta-comments from the text.
    You do not include any summary at the end of the section.
    Output only the refined section content.`,

  USER_PROMPT: `You are refining section {{sectionNumber}} titled "{{title}}" for the legal task "{{task}}".
Improve the draft below.
Improve text clarity, coherence, accuracy, and overall quality.
Strictly remove ALL placeholder text, writing instructions or meta-comments from the text.
Ensure the section content is logically structured with numbered sub-sections and sub-sub-sections as needed.
Ensure appropriate markdown codes for headings, subheadings, and bullet points are used.
Use content from these documents as primary sources: {{docs}}.
Also consider these other sections for context: {{neighborContext}}.
Output only the improved content of this section, with no placeholder text, writing instructions or meta-comments.
Ensure the output is in the same language as the legal task.
Do not include any summary at the end of the section.
The previous draft is:
---
{{previousDraft}}
---
`,
};

// ======================================================================
// MAIN DOCUMENT FLOW PROMPTS - Used only when there's a main document
// ======================================================================

// Main document selection prompt
export const DEFAULT_SELECT_MAIN_DOCUMENT: PromptTemplate = {
  SYSTEM_PROMPT:
    "You are an expert legal assistant who selects the primary document among a set of summaries. Provide only the exact document name. Respond in the same language as the legal task.",
  USER_PROMPT: `{{summaries}}\n\nGiven the summaries of documents for the legal task "{{task}}", which document is the main document? Return only the exact Doc Name.`,
};

// Section list generation from main document content
export const DEFAULT_SECTION_LIST_FROM_MAIN: PromptTemplate = {
  SYSTEM_PROMPT: `You are an expert legal assistant. Draft a JSON-structured list of sections for a legal document based on the provided main document content and legal task.
Return ONLY the JSON array (no markdown fences) where each element has:
  - "index_number": integer
  - "title": string
  - "description": string
  - "relevant_circumstances_to_comment_on": array<string>
  - "legal_issues_to_address": array<string>

Example:
[
  {
    "index_number": 1,
    "title": "Introduction",
    "description": "Purpose of the submission and background.",
    "relevant_circumstances_to_comment_on": ["Court order", "Parties"],
    "legal_issues_to_address": ["Jurisdiction", "Timeliness"]
  }
]
Use the same language as the legal task for all free-text fields (title, description, circumstances, issues).`,
  USER_PROMPT: `Draft a JSON-structured list of sections for a legal document based on the following Legal Task: {{task}}\n\n and following the section in the main Document Content:\n{{content}} Return ONLY the JSON array (no markdown fences) where each element has:
  - "index_number": integer
  - "title": string
  - "description": string
  - "relevant_circumstances_to_comment_on": array<string>
  - "legal_issues_to_address": array<string>

Example:
[
  {
    "index_number": 1,
    "title": "Introduction",
    "description": "Purpose of the submission and background.",
    "relevant_circumstances_to_comment_on": ["Court order", "Parties"],
    "legal_issues_to_address": ["Jurisdiction", "Timeliness"]
  }
]`,
};

// ======================================================================
// NO MAIN DOCUMENT FLOW PROMPTS - Used when there's no main document
// ======================================================================

// Section list generation from summaries (specific to no-main flow)
export const DEFAULT_SECTION_LIST_FROM_SUMMARIES: PromptTemplate = {
  SYSTEM_PROMPT: `You are an expert legal assistant. Draft a JSON-structured list of sections for a legal document based on the provided document summaries and legal task.

CRITICAL: You must return ONLY a valid JSON array. Do not include any explanatory text, markdown formatting, or conversational responses.

The JSON array must contain objects with exactly these fields:
  - "index_number": integer
  - "title": string
  - "description": string
  - "relevant_documents": array<string>
  - "legal_issues_to_address": array<string>

Example output:
[
  {
    "index_number": 1,
    "title": "Introduction",
    "description": "Purpose of the submission and background.",
    "relevant_documents": ["document1.pdf", "document2.docx"],
    "legal_issues_to_address": ["Jurisdiction", "Timeliness"]
  }
]

IMPORTANT: 
- Start your response with [ and end with ]
- Do not use markdown code blocks
- Do not add any text before or after the JSON
- Use the same language as the legal task for all free-text fields
- If you cannot create the JSON, return an empty array: []`,
  USER_PROMPT: `Legal Task: {{task}}\n\nDocument Summaries:\n{{summaries}}\n\nBased on these document summaries, identify appropriate sections for a comprehensive legal document that addresses the task. Return only a JSON array with the structure specified.`,
};

// After the DEFAULT_SECTION_LIST_FROM_SUMMARIES constant, add new prompt constants
export const DEFAULT_REFERENCE_FILES_DESCRIPTION: PromptTemplate = {
  SYSTEM_PROMPT:
    "You are a legal expert specialized in regulatory compliance. This is a reference file containing rules and regulations. Provide a concise summary of the rules in this file in relation to the legal task. Write the summary in the same language as the legal task.",
  USER_PROMPT:
    'For the legal task "{{task}}", summarize the following reference file content:\n\n{{content}}',
};

export const DEFAULT_REVIEW_FILES_DESCRIPTION: PromptTemplate = {
  SYSTEM_PROMPT:
    "You are a legal expert specialized in compliance review. This is a review file from the provided corpus. Compare its content against the reference rules and indicate any potential breaches or compliance points. Write your analysis in the same language as the legal task.",
  USER_PROMPT:
    'For the legal task "{{task}}", analyze the following review file content in relation to the rules:\n\n{{content}}',
};

export const DEFAULT_REFERENCE_REVIEW_SECTIONS: PromptTemplate = {
  SYSTEM_PROMPT: `You are an expert legal assistant. Draft a JSON-structured list of sections for a legal document based on the provided document summaries and legal task.
Return ONLY the JSON array (no markdown fences) where each element has:
  - "index_number": integer
  - "title": string
  - "description": string
  - "relevant_documents": array<string>

Example:
[
  {
    "index_number": 1,
    "title": "Introduction",
    "description": "Purpose of the submission and background.",
    "relevant_documents": ["document1.pdf", "document2.docx"],
  }
]

Use the same language as the legal task for all free-text fields (title, description). Each section's relevant_documents array must contain at least one reference file and any review files that contain violations. If no violations are found in review files, include relevant review files that provide context or demonstrate compliance with the reference standards.`,
  USER_PROMPT: "Legal Task: {{task}}\n\nDocument Summaries:\n{{summaries}}",
};

// ======================================================================
// REFERENCE FILES FLOW PROMPTS - Used when there's a reference file
// ======================================================================

// Compliance section drafting prompt for reference files flow
export const DEFAULT_REFERENCE_SECTION_DRAFTING: PromptTemplate = {
  SYSTEM_PROMPT:
    "You are a legal expert tasked with drafting a section of a compliance report. Generate one section at a time in professional legal style, comparing review documents against reference rules to identify breaches or compliance issues. Write in the same language as the legal task.",
  USER_PROMPT: `Draft section {{sectionNumber}} titled "{{title}}" for the legal task "{{task}}".

Section Description: {{description}}

Relevant Documents: {{docs}}

Previously Drafted Sections: {{previousSections}}

Instructions: Focus on comparing the review documents against the reference rules and highlighting any compliance issues or breaches, with specific references. Draft ONLY the content for this section. Do NOT include the title in your response, as it will be added automatically. Ensure it flows logically from the previous content and directly addresses the section's description and the overall legal task.`,
};

// Helper function to generate system setting PKeys
// Adapted from server/utils/chats/helpers/promptManager.js to avoid circular deps
function generateSystemSettingPKeyForLegalDrafting(
  defaultPromptKey: string,
  fieldKey: string
): string {
  const baseName = defaultPromptKey.replace(/^DEFAULT_/, "").toLowerCase();
  return `cdb_${baseName}_${fieldKey.toLowerCase()}`;
}

/**
 * Array of prompt configurations exported for use in the Document Builder settings page and other parts of the system.
 *
 * When adding new entries:
 * - For `GROUP_TITLE` and `GROUP_DESCRIPTION`:
 *   - `label` is the translation key. `defaultContent` is its English text.
 * - For `SYSTEM_PROMPT`, `USER_PROMPT`, `PROMPT_TEMPLATE`:
 *   - `label` is the translation key for the UI title. `defaultContent` is the actual prompt text.
 *   - `description` is the translation key for the UI help text.
 * - ALL translation keys (from `label` and `description` fields) must be added to all frontend locale files.
 */
export const exportedLegalPrompts: ExportedLegalPrompt[] = [
  {
    promptKey: "DEFAULT_DOCUMENT_SUMMARY",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.document_summary.title",
    defaultContent: "Document Summary Prompts",
  },
  {
    promptKey: "DEFAULT_DOCUMENT_SUMMARY",
    promptField: "GROUP_DESCRIPTION",
    label: "document-builder.prompts.group.document_summary.description",
    defaultContent: "Configure system and user prompts for Document Summary.",
  },
  {
    promptKey: "DEFAULT_DOCUMENT_SUMMARY",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.document-summary-system-label",
    defaultContent: DEFAULT_DOCUMENT_SUMMARY.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_DOCUMENT_SUMMARY",
      "SYSTEM_PROMPT"
    ),
    description: "document-builder.prompts.document-summary-system-description",
  },
  {
    promptKey: "DEFAULT_DOCUMENT_SUMMARY",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.document-summary-user-label",
    defaultContent: DEFAULT_DOCUMENT_SUMMARY.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_DOCUMENT_SUMMARY",
      "USER_PROMPT"
    ),
    description: "document-builder.prompts.document-summary-user-description",
  },
  {
    promptKey: "DEFAULT_DOCUMENT_RELEVANCE",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.document_relevance.title",
    defaultContent: "Document Relevance Prompts",
  },
  {
    promptKey: "DEFAULT_DOCUMENT_RELEVANCE",
    promptField: "GROUP_DESCRIPTION",
    label: "document-builder.prompts.group.document_relevance.description",
    defaultContent: "Configure system and user prompts for Document Relevance.",
  },
  {
    promptKey: "DEFAULT_DOCUMENT_RELEVANCE",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.document-relevance-system-label",
    defaultContent: DEFAULT_DOCUMENT_RELEVANCE.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_DOCUMENT_RELEVANCE",
      "SYSTEM_PROMPT"
    ),
    description:
      "document-builder.prompts.document-relevance-system-description",
  },
  {
    promptKey: "DEFAULT_DOCUMENT_RELEVANCE",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.document-relevance-user-label",
    defaultContent: DEFAULT_DOCUMENT_RELEVANCE.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_DOCUMENT_RELEVANCE",
      "USER_PROMPT"
    ),
    description: "document-builder.prompts.document-relevance-user-description",
  },
  {
    promptKey: "DEFAULT_SECTION_DRAFTING",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.section_drafting.title",
    defaultContent: "Section Drafting Prompts",
  },
  {
    promptKey: "DEFAULT_SECTION_DRAFTING",
    promptField: "GROUP_DESCRIPTION",
    label: "document-builder.prompts.group.section_drafting.description",
    defaultContent: "Configure system and user prompts for Section Drafting.",
  },
  {
    promptKey: "DEFAULT_SECTION_DRAFTING",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.section-drafting-system-label",
    defaultContent: DEFAULT_SECTION_DRAFTING.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SECTION_DRAFTING",
      "SYSTEM_PROMPT"
    ),
    description: "document-builder.prompts.section-drafting-system-description",
  },
  {
    promptKey: "DEFAULT_SECTION_DRAFTING",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.section-drafting-user-label",
    defaultContent: DEFAULT_SECTION_DRAFTING.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SECTION_DRAFTING",
      "USER_PROMPT"
    ),
    description: "document-builder.prompts.section-drafting-user-description",
  },
  {
    promptKey: "DEFAULT_SECTION_LEGAL_ISSUES",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.section_legal_issues.title",
    defaultContent: "Section Legal Issues Prompts",
  },
  {
    promptKey: "DEFAULT_SECTION_LEGAL_ISSUES",
    promptField: "GROUP_DESCRIPTION",
    label: "document-builder.prompts.group.section_legal_issues.description",
    defaultContent:
      "Configure system and user prompts for Section Legal Issues.",
  },
  {
    promptKey: "DEFAULT_SECTION_LEGAL_ISSUES",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.section-legal-issues-system-label",
    defaultContent: DEFAULT_SECTION_LEGAL_ISSUES.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SECTION_LEGAL_ISSUES",
      "SYSTEM_PROMPT"
    ),
    description:
      "document-builder.prompts.section-legal-issues-system-description",
  },
  {
    promptKey: "DEFAULT_SECTION_LEGAL_ISSUES",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.section-legal-issues-user-label",
    defaultContent: DEFAULT_SECTION_LEGAL_ISSUES.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SECTION_LEGAL_ISSUES",
      "USER_PROMPT"
    ),
    description:
      "document-builder.prompts.section-legal-issues-user-description",
  },
  {
    promptKey: "DEFAULT_MEMO_CREATION",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.memo_creation.title",
    defaultContent: "Memo Creation Prompts",
  },
  {
    promptKey: "DEFAULT_MEMO_CREATION",
    promptField: "GROUP_DESCRIPTION",
    label: "document-builder.prompts.group.memo_creation.description",
    defaultContent: "Configure prompts for Memo Creation.",
  },
  {
    promptKey: "DEFAULT_MEMO_CREATION",
    promptField: "PROMPT_TEMPLATE",
    label: "document-builder.prompts.memo-creation-template-label",
    defaultContent: DEFAULT_MEMO_CREATION.PROMPT_TEMPLATE,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_MEMO_CREATION",
      "PROMPT_TEMPLATE"
    ),
    description: "document-builder.prompts.memo-creation-template-description",
  },
  {
    promptKey: "DEFAULT_SECTION_INDEX",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.section_index.title",
    defaultContent: "Section Index Prompts",
  },
  {
    promptKey: "DEFAULT_SECTION_INDEX",
    promptField: "GROUP_DESCRIPTION",
    label: "document-builder.prompts.group.section_index.description",
    defaultContent: "Configure prompts for Section Index.",
  },
  {
    promptKey: "DEFAULT_SECTION_INDEX",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.section-index-system-label",
    defaultContent: DEFAULT_SECTION_INDEX.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SECTION_INDEX",
      "SYSTEM_PROMPT"
    ),
    description: "document-builder.prompts.section-index-system-description",
  },
  // Main Document Flow Prompts
  {
    promptKey: "DEFAULT_SELECT_MAIN_DOCUMENT",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.select_main_document.title",
    defaultContent: "Select Main Document Prompts",
  },
  {
    promptKey: "DEFAULT_SELECT_MAIN_DOCUMENT",
    promptField: "GROUP_DESCRIPTION",
    label: "document-builder.prompts.group.select_main_document.description",
    defaultContent:
      "Configure system and user prompts for Select Main Document.",
  },
  {
    promptKey: "DEFAULT_SELECT_MAIN_DOCUMENT",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.select-main-document-system-label",
    defaultContent: DEFAULT_SELECT_MAIN_DOCUMENT.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SELECT_MAIN_DOCUMENT",
      "SYSTEM_PROMPT"
    ),
    description:
      "document-builder.prompts.select-main-document-system-description",
  },
  {
    promptKey: "DEFAULT_SELECT_MAIN_DOCUMENT",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.select-main-document-user-label",
    defaultContent: DEFAULT_SELECT_MAIN_DOCUMENT.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SELECT_MAIN_DOCUMENT",
      "USER_PROMPT"
    ),
    description:
      "document-builder.prompts.select-main-document-user-description",
  },
  {
    promptKey: "DEFAULT_SECTION_LIST_FROM_MAIN",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.section_list_from_main.title",
    defaultContent: "Section List From Main Prompts",
  },
  {
    promptKey: "DEFAULT_SECTION_LIST_FROM_MAIN",
    promptField: "GROUP_DESCRIPTION",
    label: "document-builder.prompts.group.section_list_from_main.description",
    defaultContent:
      "Configure system and user prompts for Section List From Main.",
  },
  {
    promptKey: "DEFAULT_SECTION_LIST_FROM_MAIN",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.section-list-from-main-system-label",
    defaultContent: DEFAULT_SECTION_LIST_FROM_MAIN.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SECTION_LIST_FROM_MAIN",
      "SYSTEM_PROMPT"
    ),
    description:
      "document-builder.prompts.section-list-from-main-system-description",
  },
  {
    promptKey: "DEFAULT_SECTION_LIST_FROM_MAIN",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.section-list-from-main-user-label",
    defaultContent: DEFAULT_SECTION_LIST_FROM_MAIN.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SECTION_LIST_FROM_MAIN",
      "USER_PROMPT"
    ),
    description:
      "document-builder.prompts.section-list-from-main-user-description",
  },
  // No Main Document Flow Prompts
  {
    promptKey: "DEFAULT_SECTION_LIST_FROM_SUMMARIES",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.section_list_from_summaries.title",
    defaultContent: "Section List From Summaries Prompts",
  },
  {
    promptKey: "DEFAULT_SECTION_LIST_FROM_SUMMARIES",
    promptField: "GROUP_DESCRIPTION",
    label:
      "document-builder.prompts.group.section_list_from_summaries.description",
    defaultContent:
      "Configure system and user prompts for Section List From Summaries.",
  },
  {
    promptKey: "DEFAULT_SECTION_LIST_FROM_SUMMARIES",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.section-list-from-summaries-system-label",
    defaultContent: DEFAULT_SECTION_LIST_FROM_SUMMARIES.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SECTION_LIST_FROM_SUMMARIES",
      "SYSTEM_PROMPT"
    ),
    description:
      "document-builder.prompts.section-list-from-summaries-system-description",
  },
  {
    promptKey: "DEFAULT_SECTION_LIST_FROM_SUMMARIES",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.section-list-from-summaries-user-label",
    defaultContent: DEFAULT_SECTION_LIST_FROM_SUMMARIES.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SECTION_LIST_FROM_SUMMARIES",
      "USER_PROMPT"
    ),
    description:
      "document-builder.prompts.section-list-from-summaries-user-description",
  },
  // Reference Files Description Prompts
  {
    promptKey: "DEFAULT_REFERENCE_FILES_DESCRIPTION",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.reference_files_description.title",
    defaultContent: "Reference Files Description Prompts",
  },
  {
    promptKey: "DEFAULT_REFERENCE_FILES_DESCRIPTION",
    promptField: "GROUP_DESCRIPTION",
    label:
      "document-builder.prompts.group.reference_files_description.description",
    defaultContent:
      "Configure system and user prompts for Reference Files Description.",
  },
  {
    promptKey: "DEFAULT_REFERENCE_FILES_DESCRIPTION",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.reference-files-description-system-label",
    defaultContent: DEFAULT_REFERENCE_FILES_DESCRIPTION.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_REFERENCE_FILES_DESCRIPTION",
      "SYSTEM_PROMPT"
    ),
    description:
      "document-builder.prompts.reference-files-description-system-description",
  },
  {
    promptKey: "DEFAULT_REFERENCE_FILES_DESCRIPTION",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.reference-files-description-user-label",
    defaultContent: DEFAULT_REFERENCE_FILES_DESCRIPTION.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_REFERENCE_FILES_DESCRIPTION",
      "USER_PROMPT"
    ),
    description:
      "document-builder.prompts.reference-files-description-user-description",
  },
  // Review Files Description Prompts
  {
    promptKey: "DEFAULT_REVIEW_FILES_DESCRIPTION",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.review_files_description.title",
    defaultContent: "Review Files Description Prompts",
  },
  {
    promptKey: "DEFAULT_REVIEW_FILES_DESCRIPTION",
    promptField: "GROUP_DESCRIPTION",
    label:
      "document-builder.prompts.group.review_files_description.description",
    defaultContent:
      "Configure system and user prompts for Review Files Description.",
  },
  {
    promptKey: "DEFAULT_REVIEW_FILES_DESCRIPTION",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.review-files-description-system-label",
    defaultContent: DEFAULT_REVIEW_FILES_DESCRIPTION.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_REVIEW_FILES_DESCRIPTION",
      "SYSTEM_PROMPT"
    ),
    description:
      "document-builder.prompts.review-files-description-system-description",
  },
  {
    promptKey: "DEFAULT_REVIEW_FILES_DESCRIPTION",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.review-files-description-user-label",
    defaultContent: DEFAULT_REVIEW_FILES_DESCRIPTION.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_REVIEW_FILES_DESCRIPTION",
      "USER_PROMPT"
    ),
    description:
      "document-builder.prompts.review-files-description-user-description",
  },
  // Reference/Review Sections Prompts
  {
    promptKey: "DEFAULT_REFERENCE_REVIEW_SECTIONS",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.reference_review_sections.title",
    defaultContent: "Reference/Review Sections Prompts",
  },
  {
    promptKey: "DEFAULT_REFERENCE_REVIEW_SECTIONS",
    promptField: "GROUP_DESCRIPTION",
    label:
      "document-builder.prompts.group.reference_review_sections.description",
    defaultContent:
      "Configure system and user prompts for Reference/Review Sections.",
  },
  {
    promptKey: "DEFAULT_REFERENCE_REVIEW_SECTIONS",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.reference-review-sections-system-label",
    defaultContent: DEFAULT_REFERENCE_REVIEW_SECTIONS.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_REFERENCE_REVIEW_SECTIONS",
      "SYSTEM_PROMPT"
    ),
    description:
      "document-builder.prompts.reference-review-sections-system-description",
  },
  {
    promptKey: "DEFAULT_REFERENCE_REVIEW_SECTIONS",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.reference-review-sections-user-label",
    defaultContent: DEFAULT_REFERENCE_REVIEW_SECTIONS.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_REFERENCE_REVIEW_SECTIONS",
      "USER_PROMPT"
    ),
    description:
      "document-builder.prompts.reference-review-sections-user-description",
  },
  // Compliance section drafting prompt for reference files flow
  {
    promptKey: "DEFAULT_REFERENCE_SECTION_DRAFTING",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.compliance_section_drafting.title",
    defaultContent: "Compliance Section Drafting Prompts",
  },
  {
    promptKey: "DEFAULT_REFERENCE_SECTION_DRAFTING",
    promptField: "GROUP_DESCRIPTION",
    label:
      "document-builder.prompts.group.compliance_section_drafting.description",
    defaultContent:
      "Configure system and user prompts for Compliance Section Drafting.",
  },
  {
    promptKey: "DEFAULT_REFERENCE_SECTION_DRAFTING",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.compliance-section-drafting-system-label",
    defaultContent: DEFAULT_REFERENCE_SECTION_DRAFTING.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_REFERENCE_SECTION_DRAFTING",
      "SYSTEM_PROMPT"
    ),
    description:
      "document-builder.prompts.compliance-section-drafting-system-description",
  },
  {
    promptKey: "DEFAULT_REFERENCE_SECTION_DRAFTING",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.compliance-section-drafting-user-label",
    defaultContent: DEFAULT_REFERENCE_SECTION_DRAFTING.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_REFERENCE_SECTION_DRAFTING",
      "USER_PROMPT"
    ),
    description:
      "document-builder.prompts.compliance-section-drafting-user-description",
  },
  // Section Draft Refinement Prompts
  {
    promptKey: "DEFAULT_SECTION_DRAFT_REFINEMENT",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.section_draft_refinement.title",
    defaultContent: "Section Draft Refinement Prompts",
  },
  {
    promptKey: "DEFAULT_SECTION_DRAFT_REFINEMENT",
    promptField: "GROUP_DESCRIPTION",
    label:
      "document-builder.prompts.group.section_draft_refinement.description",
    defaultContent:
      "Configure system and user prompts for Section Draft Refinement.",
  },
  {
    promptKey: "DEFAULT_SECTION_DRAFT_REFINEMENT",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.section-draft-refinement-system-label",
    defaultContent: SECTION_DRAFT_REFINEMENT.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SECTION_DRAFT_REFINEMENT",
      "SYSTEM_PROMPT"
    ),
    description:
      "document-builder.prompts.section-draft-refinement-system-description",
  },
  {
    promptKey: "DEFAULT_SECTION_DRAFT_REFINEMENT",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.section-draft-refinement-user-label",
    defaultContent: SECTION_DRAFT_REFINEMENT.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SECTION_DRAFT_REFINEMENT",
      "USER_PROMPT"
    ),
    description:
      "document-builder.prompts.section-draft-refinement-user-description",
  },
];

// Export all prompt constants for backwards compatibility
export const DEFAULT_SECTION_DRAFT_REFINEMENT = SECTION_DRAFT_REFINEMENT;
