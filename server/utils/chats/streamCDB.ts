import { Request, Response } from "express";
import { v4 as uuidv4 } from "uuid";
import { runFlow } from "./flowDispatcher";
import { purgeDocumentBuilder } from "../files";
import { writeResponseChunk } from "../helpers/chat/responses";
import { WorkspaceChats } from "../../models/workspaceChats";
// Import proper types from models
import { FilteredUser, Workspace, WorkspaceThread } from "../../types/models";
import {
  FlowOptions,
  ThreadData,
  FlowType,
  AttachmentData,
} from "./flowDispatcher";

interface CDBOptions extends FlowOptions {
  isCanvasChat?: boolean;
  preventChatCreation?: boolean;
  settings_suffix?: string;
  invoice_ref?: string;
  vectorSearchMode?: string;
  hasUploadedFile?: boolean;
  displayMessage?: string | null;
  useDeepSearch?: boolean;
  cdbOptions?: [string?, string?, string?, FlowType?, AttachmentData[]?];
  mainDocName?: string | null;
}

interface LegalTaskConfig {
  name?: string;
  flowType?: string;
  [key: string]: unknown;
}

/**
 * streamChatWithWorkspaceCDB - Main entry point for Complex Document Builder (CDB) flows.
 *
 * This function now acts as a thin wrapper around the flowDispatcher, which routes
 * to the appropriate drafting flow (main document or no main document) based on
 * legal task configuration and provided options.
 *
 * @param request - The Express request object.
 * @param response - The Express response object.
 * @param workspace - The current workspace object.
 * @param message - The user's initial message or legal task description.
 * @param chatMode - The mode of chat interaction (e.g., "chat", "query").
 * @param user - The user object.
 * @param thread - The current chat thread object.
 * @param attachments - Any attachments to the message.
 * @param chatIdInput - The unique ID for this chat interaction.
 * @param isCanvasChat - Flag indicating if the chat is a canvas chat.
 * @param preventChatCreation - Flag to prevent creation of a new chat record.
 * @param settings_suffix - Suffix for system settings lookups.
 * @param invoice_ref - Invoice reference number.
 * @param vectorSearchMode - Vector search mode to use.
 * @param hasUploadedFile - Flag indicating if a file was uploaded with the request.
 * @param displayMessage - A message to display to the user (overrides the main message).
 * @param useDeepSearch - Flag to enable deep search capabilities.
 * @param cdbOptions - Array of CDB-specific options:
 *                     [0]: legalPrompt (string) - The core legal prompt.
 *                     [1]: customInstructions (string) - Additional user instructions.
 *                     [2]: mainDocNameFromOptions (string) - Name of the main document, if specified in options.
 * @param legalTaskConfig - Configuration object for the selected legal task, if any.
 *                          This object can contain `flowType` and other task-specific settings.
 * @returns Promise<void> - The function streams responses and does not return a direct value.
 */
async function streamChatWithWorkspaceCDB(
  request: Request,
  response: Response,
  workspace: Workspace,
  message: string,
  chatMode: string = "chat",
  user: FilteredUser | null = null,
  thread: WorkspaceThread | null = null,
  attachments: unknown[] = [],
  chatIdInput: string,
  isCanvasChat: boolean = false,
  preventChatCreation: boolean = false,
  settings_suffix: string = "",
  invoice_ref?: string,
  vectorSearchMode: string = "default",
  hasUploadedFile: boolean = false,
  displayMessage: string | null = null,
  useDeepSearch: boolean = false,
  cdbOptions: string[] = [],
  legalTaskConfig: LegalTaskConfig | null = null
): Promise<void> {
  const resolvedChatId = chatIdInput || uuidv4();

  console.log(
    `[CDB PROCESS START] ChatId: ${resolvedChatId}, Workspace: ${(workspace?.slug ?? false) || "unknown"}, Thread: ${(thread?.slug ?? false) || "none"}, User: ${(user?.id ?? false) || "unknown"}, Task: ${(legalTaskConfig?.name ?? false) || displayMessage || "custom"}`
  );

  const abortController = new AbortController();
  const { signal: abortSignal } = abortController;

  const startTime = Date.now();
  let cleanupExecuted = false;
  let flowCompleted = false;

  const executeCleanup = (reason: string): void => {
    // Only cleanup if the flow hasn't completed successfully
    if (cleanupExecuted || flowCompleted) return;
    cleanupExecuted = true;

    const connectionDuration = Date.now() - startTime;

    console.log(
      `[CDB PROCESS CANCELLED] Reason: ${reason}, ChatId: ${resolvedChatId}, Workspace: ${(workspace?.slug ?? false) || "unknown"}, Thread: ${(thread?.slug ?? false) || "none"}, User: ${(user?.id ?? false) || "unknown"}, Duration: ${connectionDuration}ms`
    );

    try {
      abortController.abort();
      purgeDocumentBuilder({ uuid: resolvedChatId });
    } catch (e) {
      console.error(
        `[CDB] Error during abort cleanup for ${resolvedChatId}:`,
        e
      );
    }
  };

  // Setup abort detection - only trigger if the flow hasn't completed
  request.on("close", () => {
    if (!flowCompleted) executeCleanup("request.close");
  });
  request.on("aborted", () => {
    if (!flowCompleted) executeCleanup("request.aborted");
  });
  response.on("close", () => {
    if (!flowCompleted) executeCleanup("response.close");
  });

  let finalDocumentContent = "";
  let flowError: Error | null = null;

  try {
    // Extract mainDocName from cdbOptions[2] if available
    const mainDocName =
      cdbOptions && cdbOptions.length > 2 && cdbOptions[2] !== undefined
        ? cdbOptions[2]
        : null;

    // Consolidate all options for the dispatcher
    const flowOptions: FlowOptions = {
      request,
      response,
      workspace: workspace,
      message,
      chatMode,
      user: user ?? undefined,
      thread: thread
        ? ({ ...thread, id: String(thread.id) } as ThreadData)
        : undefined,
      attachments: attachments as AttachmentData[],
      chatId: resolvedChatId,
      legalTask: legalTaskConfig as import("./flowDispatcher").LegalTask | null,
      abortSignal,
      // Pass additional CDB-specific options
      isCanvasChat,
      preventChatCreation,
      settings_suffix,
      invoice_ref,
      vectorSearchMode,
      hasUploadedFile,
      displayMessage,
      useDeepSearch,
      cdbOptions: [
        cdbOptions[0],
        cdbOptions[1],
        cdbOptions[2],
        cdbOptions[3] as FlowType | undefined,
        Array.isArray(cdbOptions[4])
          ? (cdbOptions[4] as AttachmentData[])
          : undefined,
      ],
      mainDocName,
    };

    // runFlow handles streaming and may return final document content
    // If content is returned, we use it for database saving and final response
    // If empty string is returned, streaming happened during flow execution
    const flowResult = await runFlow(flowOptions);
    finalDocumentContent = flowResult || "";
    flowCompleted = true;

    console.log(
      `[CDB FLOW SUCCESS] ChatId: ${resolvedChatId}, Document length: ${(finalDocumentContent?.length ?? false) || 0} characters`
    );
  } catch (error) {
    flowError = error instanceof Error ? error : new Error(String(error));
    flowCompleted = true;
    console.error(
      `[CDB FLOW ERROR] ChatId: ${resolvedChatId}, Error: ${error instanceof Error ? error.message : String(error)}`
    );
  }

  try {
    // Create a chat entry first if we have valid content and are not prevented from chat creation
    let savedChatId = resolvedChatId;
    if (
      (finalDocumentContent?.length ?? 0) > 0 &&
      !preventChatCreation &&
      !flowError
    ) {
      const { chat } = await WorkspaceChats.new({
        workspaceId: workspace.id,
        prompt: displayMessage || message,
        response: {
          text: finalDocumentContent,
          sources: [],
          type: chatMode,
          attachments,
        },
        threadId: (thread?.id ?? false) || null,
        user,
        invoice_ref,
      });
      savedChatId = chat ? String(chat.id) : resolvedChatId;
    }

    // Send the complete final document to the frontend (only if we have content)
    if (
      finalDocumentContent &&
      (finalDocumentContent?.length ?? 0) > 0 &&
      !flowError
    ) {
      writeResponseChunk(response, {
        uuid: resolvedChatId,
        sources: [],
        type: "textResponse",
        textResponse: finalDocumentContent,
        close: true, // Set close to true for final message
        error: false,
        chatId: savedChatId, // Include the saved chat ID
      });
    } else {
      // Send finalization even if no content to prevent UI hanging
      writeResponseChunk(response, {
        uuid: resolvedChatId,
        type: "finalizeResponseStream",
        close: true,
        error: flowError
          ? flowError.message
          : (finalDocumentContent?.length ?? 0) === 0
            ? "No content generated"
            : null,
        sources: [],
        textResponse: null,
      });
    }
  } catch (error) {
    console.error(
      "Error creating chat entry or sending final response:",
      error
    );
    // Send error response to prevent UI hanging
    writeResponseChunk(response, {
      uuid: resolvedChatId,
      type: "abort",
      error:
        (error instanceof Error ? error.message : String(error)) ||
        "Failed to save or send final document",
      close: true,
      sources: [],
      textResponse: null,
    });
  }
}

export { streamChatWithWorkspaceCDB, type CDBOptions, type LegalTaskConfig };
