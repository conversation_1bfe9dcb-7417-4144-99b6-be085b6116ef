import { Request, Response } from "express";
import { v4 as uuidv4 } from "uuid";
import { WorkspaceChats } from "../../models/workspaceChats";
import { getLL<PERSON>rovider } from "../helpers";
import { writeResponseChunk } from "../helpers/chat/responses";
import { recentChatHistory, sourceIdentifier } from "./index";
import { TokenManager } from "../helpers/tiktoken";
import SystemSettings from "../../models/systemSettings";
import type { User, Workspace, WorkspaceThread } from "../../types/models";
import type { ChatMessage } from "../../types/ai-providers";

interface CanvasSource {
  id: string;
  title: string;
  content: string;
  metadata?: Record<string, unknown>;
  url: string;
  [key: string]: unknown;
}

interface SourceDocument {
  title?: string;
  published?: string;
  [key: string]: unknown;
}

interface CanvasUser {
  id: number;
  username: string;
  custom_ai_userselected?: boolean;
  custom_ai_selected_engine?: string;
  [key: string]: string | number | boolean | Date | null | undefined;
}

interface ChatPromptArgs {
  systemPrompt: string;
  contextTexts: string[];
  chatHistory: string;
  userPrompt: string;
  attachments: unknown[];
  tokenLimit: number | null;
  model: string;
  agentMode: boolean;
}

// Define the dedicated system prompt for standard canvas chats
const DEFAULT_CANVAS_SYSTEM_PROMPT =
  "You are an assistant helping the user refine a previous AI-generated response. Use the provided sources and context from the *original* response to accurately implement the user's requested adjustments. Always respond in the same language as the user's message.";

// Define the dedicated system prompt for canvas chats with uploaded files
const DEFAULT_CANVAS_UPLOAD_SYSTEM_PROMPT =
  "You are an assistant helping the user work with the uploaded document content. Focus exclusively on the content that was uploaded to the canvas, and respond to the user's instructions regarding this content. Always respond in the same language as the user's message.";

// Main function for streaming canvas chat responses
async function streamCanvasChat(
  _request: Request, // Unused parameter, but kept for consistency with other stream functions
  response: Response,
  workspace: Workspace,
  message: string, // The user's message that triggered the canvas chat
  providedSources: CanvasSource[], // Sources from the original message this canvas chat stems from
  user: CanvasUser | null = null,
  thread: WorkspaceThread | null = null,
  _chatId?: string, // Unused parameter
  hasUploadedFile: boolean = false, // Flag to indicate if a file has been uploaded to Canvas
  docxContent: string | null = null // Content of the uploaded DOCX file
): Promise<void> {
  const uuid = uuidv4();
  console.log(`[Canvas Chat ${uuid}] Starting stream...`);

  // LLM Selection Logic
  let chatProvider: string | undefined;
  let chatModel: string | undefined;
  let settingsSuffix = "";

  if (user?.custom_ai_userselected) {
    const cuaiSuffix = user?.custom_ai_selected_engine || "_CUAI";
    const systemSettings = await SystemSettings.currentSettings();
    const llmProvider = (systemSettings as Record<string, unknown>)?.[
      `LLMProvider${cuaiSuffix}`
    ] as string | undefined;

    if (llmProvider && llmProvider !== "none") {
      let modelPrefKey: string;
      const providerLower = llmProvider.toLowerCase();
      switch (providerLower) {
        case "gemini":
          modelPrefKey = `GeminiLLMModelPref${cuaiSuffix}`;
          break;
        case "ollama":
          modelPrefKey = `OllamaLLMModelPref${cuaiSuffix}`;
          break;
        case "native":
          modelPrefKey = `NativeLLMModelPref${cuaiSuffix}`;
          break;
        case "litellm":
          modelPrefKey = `LiteLLMModelPref${cuaiSuffix}`;
          break;
        case "openai":
          modelPrefKey = `OpenAiModelPref${cuaiSuffix}`;
          break;
        case "azureopenai":
          modelPrefKey = `AzureOpenAiModelPref${cuaiSuffix}`;
          break;
        case "genericopenai":
          modelPrefKey = `GenericOpenAiModelPref${cuaiSuffix}`;
          break;
        default: {
          const providerPrefix = llmProvider
            .split("-")
            .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
            .join("");
          modelPrefKey = `${providerPrefix}ModelPref${cuaiSuffix}`;
        }
      }
      chatProvider = llmProvider;
      chatModel = (systemSettings as Record<string, unknown>)[
        modelPrefKey
      ] as string;
      if (chatProvider && chatModel) {
        settingsSuffix = cuaiSuffix;
      }
    }
  }

  // Fallback to workspace/system defaults if no custom AI or custom AI not found
  if (!chatProvider || !chatModel) {
    chatProvider = workspace?.chatProvider || process.env.LLM_PROVIDER;
    chatModel = workspace?.chatModel ?? undefined;

    // If we still don't have a model, check the environment variables based on the provider
    if (!chatModel && chatProvider) {
      const providerLower = chatProvider?.toLowerCase();
      let modelPrefKey: string;

      switch (providerLower) {
        case "gemini":
          modelPrefKey = "GEMINI_LLM_MODEL_PREF";
          break;
        case "ollama":
          modelPrefKey = "OLLAMA_MODEL_PREF";
          break;
        case "native":
          modelPrefKey = "NATIVE_LLM_MODEL_PREF";
          break;
        case "litellm":
          modelPrefKey = "LITE_LLM_MODEL_PREF";
          break;
        case "openai":
          modelPrefKey = "OPEN_AI_MODEL_PREF";
          break;
        case "azureopenai":
          modelPrefKey = "AZURE_OPEN_AI_MODEL_PREF";
          break;
        case "genericopenai":
          modelPrefKey = "GENERIC_OPEN_AI_MODEL_PREF";
          break;
        default: {
          // For most providers: uppercase first letter, then "MODEL_PREF"
          const providerPrefix =
            chatProvider?.charAt(0).toUpperCase() +
            chatProvider?.slice(1).toLowerCase();
          modelPrefKey = `${providerPrefix?.toUpperCase()}_MODEL_PREF`;
        }
      }

      if (modelPrefKey && process.env[modelPrefKey]) {
        chatModel = process.env[modelPrefKey];
      }
    }

    console.log(
      `[Canvas Chat ${uuid}][DEBUG_CUAI] Falling back to workspace/system defaults: provider="${chatProvider}", model="${chatModel}"`
    );
  }

  const LLMConnector = getLLMProvider({
    provider: chatProvider,
    model: chatModel,
    workspace,
    settings_suffix: settingsSuffix,
  });
  console.log(
    `[Canvas Chat ${uuid}][DEBUG_CUAI] LLMConnector created with model: ${LLMConnector?.model || "unknown"}`
  );

  const messageLimit = workspace?.openAiHistory || 20; // Keep history limit
  let completeText: string;

  // Determine whether to use sources based on file upload status
  let sources: CanvasSource[] = [];
  let sourcesSave: string[] = [];

  // Only use provided sources if no file has been uploaded
  if (!hasUploadedFile) {
    sources = providedSources || [];
    sourcesSave = sources?.map((source) =>
      sourceIdentifier(source as unknown as SourceDocument)
    );
    console.log(
      `[Canvas Chat ${uuid}] Using ${sources?.length} provided sources.`
    );
  } else {
    console.log(
      `[Canvas Chat ${uuid}] File uploaded to Canvas - skipping sources.`
    );
  }

  try {
    // For uploaded files, we don't want to include chat history
    // Only fetch chat history if no file has been uploaded
    let historyText = "";
    if (!hasUploadedFile) {
      // Directly proceed to history preparation and chat prompt construction
      const { chatHistory } = await recentChatHistory({
        user: user || undefined,
        workspace: workspace,
        thread: thread || undefined,
        messageLimit,
      });
      historyText = Array.isArray(chatHistory)
        ? (chatHistory as ChatMessage[])
            .map((h) => `${h?.role || "unknown"}: ${h?.content || ""}`)
            .join("\n")
        : "";
    } else {
      console.log(
        `[Canvas Chat ${uuid}] File uploaded to Canvas - skipping chat history.`
      );
    }

    // Token management setup
    // Select the appropriate system prompt based on whether a file was uploaded
    let systemPrompt: string;
    if (hasUploadedFile) {
      const customUploadPrompt =
        await SystemSettings?.getCanvasUploadSystemPrompt();
      systemPrompt =
        customUploadPrompt && customUploadPrompt?.trim()
          ? customUploadPrompt
          : DEFAULT_CANVAS_UPLOAD_SYSTEM_PROMPT;
    } else {
      const customPrompt = await SystemSettings?.getCanvasSystemPrompt();
      systemPrompt =
        customPrompt && customPrompt?.trim()
          ? customPrompt
          : DEFAULT_CANVAS_SYSTEM_PROMPT;
    }

    console.log(
      `[Canvas Chat ${uuid}] Using ${hasUploadedFile ? "uploaded file" : "standard"} system prompt`
    );
    const promptWindowLimit = LLMConnector?.promptWindowLimit(); // Use the connector's limit

    // Create a TokenManager instance for the current model
    const tokenizer = new TokenManager(LLMConnector?.model);

    const contextTokenLimit = Math.floor(
      promptWindowLimit *
        (typeof workspace?.openAiHistoryContextRefresher === "number"
          ? workspace.openAiHistoryContextRefresher
          : 0.7)
    );
    const systemPromptTokens = LLMConnector?.promptWindowLimit()
      ? tokenizer?.countFromString(systemPrompt) // Use correct method name
      : 0;
    const historyTokens = LLMConnector?.promptWindowLimit()
      ? tokenizer?.countFromString(historyText) // Use correct method name
      : 0;
    const responseTokenLimit = LLMConnector?.promptWindowLimit()
      ? promptWindowLimit - systemPromptTokens - historyTokens
      : null;

    // Prepare the user prompt based on whether we have an uploaded file or generated content
    let enhancedUserPrompt = message;
    let documentContent = "";

    if (hasUploadedFile && docxContent) {
      // Initial upload mode: Include the docxContent in the user prompt
      documentContent = docxContent;
      enhancedUserPrompt = `${message}\n\n<UPLOADED_DOCUMENT>\n${docxContent}\n</UPLOADED_DOCUMENT>`;
      console.log(
        `[Canvas Chat ${uuid}] Including uploaded document content in prompt (${docxContent?.length} characters).`
      );
    } else if (hasUploadedFile) {
      // If hasUploadedFile is true but we don't have docxContent, try to get it from the message
      const uploadedFileMatch = message?.match(
        /<UPLOADED_DOCUMENT>([\s\S]*?)<\/UPLOADED_DOCUMENT>/i
      );
      if (uploadedFileMatch && uploadedFileMatch[1]) {
        documentContent = uploadedFileMatch[1].trim();
        enhancedUserPrompt = message; // Keep the original message as it already contains the document
        console.log(
          `[Canvas Chat ${uuid}] Found document content in message (${documentContent?.length} characters).`
        );
      } else {
        console.warn(
          `[Canvas Chat ${uuid}] Warning: hasUploadedFile is true but no docxContent provided.`
        );
      }
    } else if (docxContent) {
      // Generated content mode: Include the current Canvas content in the user prompt
      documentContent = docxContent;
      enhancedUserPrompt = `${message}\n\n<CURRENT_CANVAS_CONTENT>\n${docxContent}\n</CURRENT_CANVAS_CONTENT>`;
      console.log(
        `[Canvas Chat ${uuid}] Including current Canvas content in prompt (${docxContent?.length} characters).`
      );
    }

    // Calculate token limits including document content
    const documentTokens = documentContent
      ? tokenizer?.countFromString(documentContent)
      : 0;
    const messageTokens = tokenizer?.countFromString(message);
    const availableTokens =
      contextTokenLimit - systemPromptTokens - historyTokens - messageTokens;

    // If document content exceeds available tokens, truncate it while preserving message
    if (documentTokens > availableTokens) {
      console.log(
        `[Canvas Chat ${uuid}] Document content (${documentTokens} tokens) exceeds available tokens (${availableTokens}). Truncating.`
      );

      // Use the safe truncateToTokenLength method
      const truncatedContent = tokenizer?.truncateToTokenLength(
        documentContent,
        availableTokens
      );

      if (hasUploadedFile) {
        enhancedUserPrompt = `${message}\n\n<UPLOADED_DOCUMENT>\n${truncatedContent}\n</UPLOADED_DOCUMENT>`;
      } else {
        enhancedUserPrompt = `${message}\n\n<CURRENT_CANVAS_CONTENT>\n${truncatedContent}\n</CURRENT_CANVAS_CONTENT>`;
      }
    }

    const promptArgs: ChatPromptArgs = {
      systemPrompt,
      contextTexts: [], // No context texts from vector search
      chatHistory: hasUploadedFile ? "" : historyText, // Skip chat history only for initial uploaded files
      userPrompt: enhancedUserPrompt,
      attachments: [], // Canvas chat likely won't have new attachments
      tokenLimit: LLMConnector?.promptWindowLimit() ? contextTokenLimit : null,
      model: LLMConnector?.model,
      agentMode: false, // Assuming no agent mode for canvas chat
    };

    console.log(
      `[Canvas Chat ${uuid}] Prompt constructed with ${hasUploadedFile ? "no" : "standard"} chat history.`
    );
    const messages = await LLMConnector?.compressMessages(
      {
        systemPrompt: promptArgs.systemPrompt,
        userPrompt: promptArgs.userPrompt,
        contextTexts: promptArgs.contextTexts,
        chatHistory: Array.isArray(promptArgs.chatHistory)
          ? (promptArgs.chatHistory as ChatMessage[])
          : [],
        attachments:
          promptArgs.attachments as unknown as import("../../types/ai-providers").Attachment[],
        maxAllowedTokens: promptArgs.tokenLimit || undefined,
      },
      []
    );

    console.log(`[Canvas Chat ${uuid}] Prompt constructed. Calling LLM...`);

    // LLM Call and Streaming
    if (!LLMConnector) {
      writeResponseChunk(response, {
        uuid,
        type: "abort",
        textResponse: "LLM Connector not available",
        sources: [],
        close: true,
        error: "LLM Connector initialization failed",
      });
      return;
    }

    const stream = await LLMConnector.streamGetChatCompletion!(messages, {
      temperature: workspace?.openAiTemp ?? LLMConnector.defaultTemp,
      maxTokens: responseTokenLimit || undefined,
    });

    if (!stream) {
      writeResponseChunk(response, {
        uuid,
        type: "abort",
        textResponse:
          "The LLM could not reply to your message. Please try again later.",
        sources: [],
        close: true,
        error: "No stream returned from LLM.",
      });
      return;
    }

    // Write response chunks
    let streamedText = "";
    let isFirstChunk = true;

    for await (const chunk of stream.stream) {
      // Ensure chunk format is consistent
      let content =
        (chunk as { choices?: Array<{ delta?: { content?: string } }> })
          ?.choices?.[0]?.delta?.content || "";
      if (content === null || content === undefined) content = ""; // Handle null/undefined content
      streamedText += content;

      const chunkData = {
        uuid,
        sources: isFirstChunk
          ? sources.map((s) => ({ ...s, url: s.url || "" }))
          : [],
        type: "textResponseChunk",
        textResponse: content,
        close: false,
        error: false,
      };

      writeResponseChunk(response, chunkData);
      isFirstChunk = false;
    }

    // Final closing chunk
    writeResponseChunk(response, {
      uuid,
      type: "textResponseChunk",
      sources: [],
      textResponse: "",
      close: true,
      error: false,
    });

    completeText = streamedText;
  } catch (error) {
    console.error(`[Canvas Chat ${uuid}] Error during streaming:`, error);
    writeResponseChunk(response, {
      uuid,
      type: "abort",
      textResponse: "There was an error processing your request.",
      sources: [],
      close: true,
      error: error instanceof Error ? error.message : String(error),
    });
    return; // Important to exit after error
  }

  // Save the chat message if successful and text is generated
  if (completeText) {
    console.log(`[Canvas Chat ${uuid}] Saving message to database.`);
    await WorkspaceChats?.new({
      workspaceId: workspace?.id,
      prompt: message,
      response: {
        text: completeText,
        sources: sourcesSave,
        type: "canvas_chat", // Indicate this is from canvas chat
      },
      user: user as User,
      threadId: thread?.id,
    });
  } else {
    console.log(
      `[Canvas Chat ${uuid}] No complete text generated, not saving.`
    );
  }
}

export {
  streamCanvasChat,
  DEFAULT_CANVAS_SYSTEM_PROMPT,
  DEFAULT_CANVAS_UPLOAD_SYSTEM_PROMPT,
  type CanvasSource,
  type ChatPromptArgs,
};
