import { DocxLoader } from "@langchain/community/document_loaders/fs/docx";
import { tSync } from "../i18n";

interface EditOptions {
  provider?: string;
  model?: string;
}

interface EditResult {
  originalText: string;
  editedText: string;
}

interface LLMProcessorResponse {
  success: boolean;
  processedText?: string;
}

/**
 * Edits a DOCX file using LLM based on user instructions
 * @param docxPath - Path to the DOCX file
 * @param instructions - User instructions for editing
 * @param options - Additional options
 * @returns Original and edited text content
 */
export async function editDocxWithLLM(
  docxPath: string,
  instructions: string,
  options: EditOptions = {}
): Promise<EditResult> {
  try {
    // Extract text from DOCX
    const loader = new DocxLoader(docxPath);
    const docs = await loader.load();

    // Combine all document content
    let originalText = "";
    for (const doc of docs) {
      if (doc.pageContent && doc.pageContent.length) {
        originalText += doc.pageContent + "\n\n";
      }
    }

    if (!originalText.trim()) {
      throw new Error(tSync("docxEdit.errors.noContent"));
    }

    // We'll send the document text and instructions to the dedicated endpoint

    // Use the dedicated DOCX LLM processor endpoint
    console.log("Using DOCX LLM processor endpoint");
    const { default: fetch } = await import("node-fetch");
    const response = await fetch(
      "http://localhost:3001/api/docx-edit/process-with-llm",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          documentText: originalText,
          instructions: instructions,
          provider: options.provider,
          model: options.model,
        }),
      }
    );

    if (!response.ok) {
      throw new Error(
        `Error from DOCX LLM processor endpoint: ${response.statusText}`
      );
    }

    const result = (await response.json()) as LLMProcessorResponse;

    if (!result.success || !result.processedText) {
      throw new Error("Failed to get edited document from LLM");
    }

    const editedText = result.processedText;

    return {
      originalText,
      editedText,
    };
  } catch (error) {
    console.error("Error editing DOCX with LLM:", error);
    console.error("Error stack:", (error as Error).stack);
    console.error("Provider:", options.provider || process.env.LLM_PROVIDER);
    console.error("Model:", options.model || process.env.OPEN_AI_MODEL_PREF);
    throw error; // Throw the original error to preserve the stack trace
  }
}
