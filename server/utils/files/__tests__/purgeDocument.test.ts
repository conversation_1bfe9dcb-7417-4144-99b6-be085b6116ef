// Unit tests for purgeDocument error handling
import { purgeDocument } from "../purgeDocument";

// Mock dependencies
jest.mock("../../../models/workspace", () => ({
  Workspace: {
    get: jest.fn(),
  },
}));

jest.mock("../../../models/documents", () => ({
  Document: {
    removeDocuments: jest.fn(),
  },
}));

jest.mock("../index", () => ({
  normalizePath: jest.fn(),
  purgeVectorCache: jest.fn(),
  purgeSourceDocument: jest.fn(),
  documentsPath: "/mock/documents",
  isWithin: jest.fn(),
}));

jest.mock("fs", () => ({
  existsSync: jest.fn(),
  rmdirSync: jest.fn(),
  promises: {
    access: jest.fn(),
    unlink: jest.fn(),
  },
  constants: {
    W_OK: 2,
  },
}));

jest.mock("path", () => ({
  resolve: jest.fn(),
  basename: jest.fn(),
  dirname: jest.fn(),
}));

describe("purgeDocument Error Handling", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Setup console spy
    jest.spyOn(console, "error").mockImplementation(() => {});
    jest.spyOn(console, "log").mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it("should return false for invalid document path", async () => {
    const { normalizePath } = require("../index");
    normalizePath.mockReturnValue(null);

    const result = await purgeDocument("invalid-path");
    expect(result).toBe(false);
  });

  it("should log errors and re-throw when unexpected error occurs", async () => {
    const { normalizePath } = require("../index");
    const { Workspace } = require("../../../models/workspace");

    normalizePath.mockReturnValue("valid/path");
    Workspace.get.mockRejectedValue(new Error("Database connection failed"));

    await expect(purgeDocument("valid/path")).rejects.toThrow(
      "Database connection failed"
    );

    expect(console.error).toHaveBeenCalledWith(
      "Error during document purge for valid/path:",
      "Database connection failed"
    );
    expect(console.error).toHaveBeenCalledWith(
      "Full error details:",
      expect.any(Error)
    );
  });

  it("should handle non-Error objects being thrown", async () => {
    const { normalizePath } = require("../index");
    const { Workspace } = require("../../../models/workspace");

    normalizePath.mockReturnValue("valid/path");
    Workspace.get.mockRejectedValue("String error");

    await expect(purgeDocument("valid/path")).rejects.toBe("String error");

    expect(console.error).toHaveBeenCalledWith(
      "Error during document purge for valid/path:",
      "String error"
    );
  });

  it("should successfully purge document when all operations succeed", async () => {
    const {
      normalizePath,
      purgeVectorCache,
      purgeSourceDocument,
    } = require("../index");
    const { Workspace } = require("../../../models/workspace");
    const { Document } = require("../../../models/documents");
    const fs = require("fs");
    const path = require("path");

    normalizePath.mockReturnValue("valid/path");
    Workspace.get.mockResolvedValue({
      id: 1,
      slug: "test-workspace",
      type: "normal",
    });
    Document.removeDocuments.mockResolvedValue(true);
    fs.existsSync.mockReturnValue(false);
    path.resolve.mockReturnValue("/mock/path");
    path.basename.mockReturnValue("document.pdf");
    path.dirname.mockReturnValue("/mock");
    purgeVectorCache.mockResolvedValue(true);
    purgeSourceDocument.mockResolvedValue(true);

    const result = await purgeDocument("valid/path");
    expect(result).toBe(true);
  });
});
