import fs from "fs";
import path from "path";
import {
  purgeVectorCache,
  purgeSourceDocument,
  normalizePath,
  isWithin,
  documentsPath,
} from ".";
import { Document } from "../../models/documents";
import { Workspace } from "../../models/workspace";
import { cleanOldDocxSessionFiles } from "./docxSessionCleanup";

// Type definitions
export interface PurgeDocumentResult {
  success: boolean;
  error?: string;
}

/**
 * Helper function to safely delete a file with retries
 */
async function safeDeleteFile(filePath: string): Promise<boolean> {
  const maxRetries = 3;
  const retryDelay = 100; // ms

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // Check if file exists and is writable
      try {
        await fs.promises.access(filePath, fs.constants.W_OK);
      } catch {
        console.log(
          `File ${filePath} does not exist in ${documentsPath}, nothing to do.`
        );
        return false;
      }

      await fs.promises.unlink(filePath);
      console.log(`Successfully deleted file: ${filePath}`);
      return true;
    } catch (err) {
      const error = err as Error;
      console.log(
        `Attempt ${attempt} failed to delete ${filePath}: ${error.message}`
      );
      if (attempt < maxRetries) {
        await new Promise((resolve) => setTimeout(resolve, retryDelay));
      }
    }
  }
  return false;
}

/**
 * Purges a document from the system including database records and file system cleanup
 * @param docPath - The path of the document to purge
 * @returns Promise<boolean> - Returns true if successful
 * @throws Error if purge operation fails
 */
export async function purgeDocument(
  docPath: string | null = null
): Promise<boolean> {
  if (!docPath || !normalizePath(docPath)) {
    console.log("Invalid document path provided:", docPath);
    return false;
  }

  try {
    // Get the workspace from the path to determine if it's a document drafting workspace
    const pathParts = docPath.split("/");
    const workspaceSlug = pathParts[0];
    const workspace = await Workspace.get({ slug: workspaceSlug });

    if (workspace && workspace.type === "document-drafting") {
      // For document drafting, construct the proper path with user ID
      const documentDraftingPath = `document-drafting/user-${workspace.user_id}_${workspace.slug}`;
      docPath = docPath.replace(workspaceSlug, documentDraftingPath);
    }

    // First handle database cleanup
    if (workspace) {
      try {
        await Document.removeDocuments(workspace, [docPath]);
      } catch (dbError: unknown) {
        console.error("Database cleanup failed:", dbError);
        // Continue with file cleanup even if DB cleanup fails
      }
    }

    // Then handle file system cleanup
    // Clean up frontend hotdir PDF file if it exists
    const frontendHotdir = path.resolve(
      __dirname,
      "../../../frontend/public/hotdir"
    );
    const normalizedPath = normalizePath(docPath);
    const pdfFilename = normalizedPath.replace(/\.json$/, "");
    const pdfBasename = path.basename(pdfFilename);

    // Check both direct file and potential subdirectory
    const frontendPdfPath = path.resolve(frontendHotdir, pdfBasename);
    const frontendPdfSubdirPath = path.resolve(
      frontendHotdir,
      pdfBasename,
      pdfBasename
    );

    try {
      if (fs.existsSync(frontendPdfPath)) {
        await safeDeleteFile(frontendPdfPath);
      }

      if (fs.existsSync(frontendPdfSubdirPath)) {
        await safeDeleteFile(frontendPdfSubdirPath);
        // Try to remove the empty directory
        try {
          fs.rmdirSync(path.dirname(frontendPdfSubdirPath));
        } catch (dirErr) {
          const error = dirErr as Error;
          console.log(`Could not remove subdirectory: ${error.message}`);
          // Non-critical error, continue
        }
      }
    } catch (fsError: unknown) {
      console.error("Error during file system cleanup:", fsError);
      // Continue with remaining cleanup
    }

    try {
      await purgeVectorCache(docPath);
    } catch (vectorError: unknown) {
      console.error("Vector cache cleanup failed:", vectorError);
      // Continue with remaining cleanup
    }

    try {
      await purgeSourceDocument(docPath);
    } catch (sourceError: unknown) {
      console.error("Source document cleanup failed:", sourceError);
      // Continue with remaining cleanup
    }

    return true;
  } catch (error: unknown) {
    // Log the error for debugging purposes
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`Error during document purge for ${docPath}:`, errorMessage);
    console.error("Full error details:", error);

    // Re-throw the error for the calling endpoint to handle
    throw error;
  }
}

/**
 * Purges an entire folder and all its contents
 */
export async function purgeFolder(
  folderName: string | null = null,
  slug: string | null = null
): Promise<void> {
  if (!folderName) return;

  // Get the workspace to determine if it's a document drafting workspace
  const workspace = await Workspace.get({ slug: slug || "" });
  let workspacePath = slug;

  if (workspace && workspace.type === "document-drafting") {
    // For document drafting, construct the proper path with user ID
    workspacePath = `document-drafting/user-${workspace.user_id ?? undefined}_${workspace.slug}`;
  }

  const subFolder = normalizePath(folderName);
  const subFolderPath = path.resolve(documentsPath, workspacePath!, subFolder);
  const documentsPathWorkspace = path.resolve(documentsPath, workspacePath!);

  const validRemovableSubFolders = fs
    .readdirSync(documentsPathWorkspace)
    .map((folder) => {
      // Filter out any results which are not folders or
      // are the protected custom-documents folder.
      if (folder === "custom-documents") return null;
      const subfolderPath = path.resolve(documentsPathWorkspace, folder);
      if (!fs.lstatSync(subfolderPath).isDirectory()) return null;
      return folder;
    })
    .filter((subFolder): subFolder is string => !!subFolder);

  if (
    !validRemovableSubFolders.includes(subFolder) ||
    !fs.existsSync(subFolderPath) ||
    !isWithin(documentsPath, subFolderPath)
  )
    return;

  const docPaths = fs
    .readdirSync(subFolderPath)
    .map((file) =>
      path.join(subFolderPath, file).replace(documentsPath + "/", "")
    );
  const purgePromises: Array<() => Promise<boolean>> = [];

  // Remove associated Vector-cache files
  for (const docPath of docPaths) {
    const rmVectorCache = () =>
      new Promise<boolean>((resolve) =>
        purgeVectorCache(docPath).then(() => resolve(true))
      );
    purgePromises.push(rmVectorCache);
  }

  // Only remove from the specific workspace
  if (workspace) {
    const rmWorkspaceDoc = () =>
      new Promise<boolean>((resolve) =>
        Document.removeDocuments(workspace, docPaths).then(() => resolve(true))
      );
    purgePromises.push(rmWorkspaceDoc);
  }

  await Promise.all(purgePromises.flat().map((f) => f()));
  fs.rmSync(subFolderPath, { recursive: true }); // Delete target document-folder and source files.

  return;
}

// Re-export for backward compatibility
export { cleanOldDocxSessionFiles };
