import { filterImageReferences } from "../imageUtils";
import * as fs from "fs";

// Mock fs module
jest.mock("fs");
const mockedFs = fs as jest.Mocked<typeof fs>;

describe("filterImageReferences", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("valid image references", () => {
    it("should keep HTTP URLs unchanged", () => {
      const text = "![Alt text](https://example.com/image.png)";
      const result = filterImageReferences(text);
      expect(result).toBe(text);
    });

    it("should keep HTTPS URLs unchanged", () => {
      const text = "![Alt text](http://example.com/image.jpg)";
      const result = filterImageReferences(text);
      expect(result).toBe(text);
    });

    it("should keep data URLs unchanged", () => {
      const text =
        "![Alt text](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==)";
      const result = filterImageReferences(text);
      expect(result).toBe(text);
    });

    it("should keep absolute paths unchanged", () => {
      const text = "![Alt text](/absolute/path/to/image.png)";
      const result = filterImageReferences(text);
      expect(result).toBe(text);
    });

    it("should keep existing relative paths unchanged", () => {
      mockedFs.existsSync.mockReturnValue(true);
      const text = "![Alt text](./relative/path/image.png)";
      const result = filterImageReferences(text);
      expect(result).toBe(text);
      expect(mockedFs.existsSync).toHaveBeenCalledWith(
        "./relative/path/image.png"
      );
    });
  });

  describe("problematic image references", () => {
    it("should replace generic image.png with placeholder", () => {
      const text = "![Alt text](image.png)";
      const result = filterImageReferences(text);
      expect(result).toBe("[Alt text]");
    });

    it("should replace generic image.jpg with placeholder", () => {
      const text = "![Description](image.jpg)";
      const result = filterImageReferences(text);
      expect(result).toBe("[Description]");
    });

    it("should replace non-existent relative paths with placeholder", () => {
      mockedFs.existsSync.mockReturnValue(false);
      const text = "![Chart](./charts/missing.png)";
      const result = filterImageReferences(text);
      expect(result).toBe("[Chart]");
      expect(mockedFs.existsSync).toHaveBeenCalledWith("./charts/missing.png");
    });

    it("should use title as placeholder when alt text is empty", () => {
      const text = '![](image.png "My Title")';
      const result = filterImageReferences(text);
      expect(result).toBe("[My Title]");
    });

    it("should use default 'Image' placeholder when both alt and title are empty", () => {
      const text = "![](image.png)";
      const result = filterImageReferences(text);
      expect(result).toBe("[Image]");
    });
  });

  describe("edge cases", () => {
    it("should return empty string for null/undefined input", () => {
      expect(filterImageReferences("")).toBe("");
      expect(filterImageReferences(null as any)).toBe("");
      expect(filterImageReferences(undefined as any)).toBe("");
    });

    it("should handle text with no image references", () => {
      const text = "This is just plain text with no images.";
      const result = filterImageReferences(text);
      expect(result).toBe(text);
    });

    it("should handle multiple images in the same text", () => {
      mockedFs.existsSync.mockReturnValue(false);
      const text = `
        Here are some images:
        ![Valid](https://example.com/valid.png)
        ![Generic](image.png)
        ![Missing](./missing.jpg)
        ![Another valid](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)
      `;
      const result = filterImageReferences(text);
      expect(result).toContain("![Valid](https://example.com/valid.png)");
      expect(result).toContain("[Generic]");
      expect(result).toContain("[Missing]");
      expect(result).toContain(
        "![Another valid](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)"
      );
    });

    it("should handle fs.existsSync errors gracefully", () => {
      mockedFs.existsSync.mockImplementation(() => {
        throw new Error("Permission denied");
      });
      const text = "![Test](./test.png)";
      const result = filterImageReferences(text);
      expect(result).toBe("[Test]");
    });

    it("should handle malformed markdown gracefully", () => {
      const text = "![Broken markdown(missing closing bracket";
      const result = filterImageReferences(text);
      expect(result).toBe(text); // Should return original text when regex fails to match
    });
  });

  describe("case sensitivity", () => {
    it("should detect generic images case-insensitively", () => {
      expect(filterImageReferences("![Test](IMAGE.PNG)")).toBe("[Test]");
      expect(filterImageReferences("![Test](Image.Jpg)")).toBe("[Test]");
      expect(filterImageReferences("![Test](image.GIF)")).toBe("[Test]");
    });
  });
});
