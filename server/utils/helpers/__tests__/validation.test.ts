/**
 * Comprehensive tests for validation utilities
 * Tests CSS color validation, XSS prevention, and input sanitization
 */

import { isValidCssColor, sanitizeCssColor } from "../validation";

describe("CSS Color Validation Tests", () => {
  describe("isValidCssColor - Valid colors", () => {
    it("should accept valid hex colors", () => {
      const validHexColors = [
        "#fff",
        "#ffffff",
        "#FFF",
        "#FFFFFF",
        "#123",
        "#123456",
        "#abcdef",
        "#ABCDEF",
        "#fffa", // 4-digit hex with alpha
        "#ffffffff", // 8-digit hex with alpha
      ];

      validHexColors.forEach((color) => {
        expect(isValidCssColor(color)).toBe(true);
      });
    });

    it("should accept valid RGB colors", () => {
      const validRgbColors = [
        "rgb(255, 255, 255)",
        "rgb(0, 0, 0)",
        "rgb(128, 128, 128)",
        "rgb(255,255,255)", // No spaces
        "rgb( 255 , 255 , 255 )", // Extra spaces
        "rgb(0,128,255)",
      ];

      validRgbColors.forEach((color) => {
        expect(isValidCssColor(color)).toBe(true);
      });
    });

    it("should accept valid RGBA colors", () => {
      const validRgbaColors = [
        "rgba(255, 255, 255, 1)",
        "rgba(0, 0, 0, 0)",
        "rgba(128, 128, 128, 0.5)",
        "rgba(255, 255, 255, 1.0)",
        "rgba(255,255,255,0.5)", // No spaces
        "rgba( 255 , 255 , 255 , 0.5 )", // Extra spaces
      ];

      validRgbaColors.forEach((color) => {
        expect(isValidCssColor(color)).toBe(true);
      });
    });

    it("should accept valid HSL colors", () => {
      const validHslColors = [
        "hsl(360, 100%, 50%)",
        "hsl(0, 0%, 0%)",
        "hsl(180, 50%, 50%)",
        "hsl(360,100%,50%)", // No spaces
        "hsl( 360 , 100% , 50% )", // Extra spaces
      ];

      validHslColors.forEach((color) => {
        expect(isValidCssColor(color)).toBe(true);
      });
    });

    it("should accept valid HSLA colors", () => {
      const validHslaColors = [
        "hsla(360, 100%, 50%, 1)",
        "hsla(0, 0%, 0%, 0)",
        "hsla(180, 50%, 50%, 0.5)",
        "hsla(360, 100%, 50%, 1.0)",
        "hsla(360,100%,50%,0.5)", // No spaces
        "hsla( 360 , 100% , 50% , 0.5 )", // Extra spaces
      ];

      validHslaColors.forEach((color) => {
        expect(isValidCssColor(color)).toBe(true);
      });
    });

    it("should accept valid named colors", () => {
      const validNamedColors = [
        "red",
        "blue",
        "green",
        "white",
        "black",
        "transparent",
        "cornflowerblue",
        "darkslategray",
        "lightgoldenrodyellow",
        "rebeccapurple",
      ];

      validNamedColors.forEach((color) => {
        expect(isValidCssColor(color)).toBe(true);
      });
    });

    it("should handle case insensitive named colors", () => {
      const caseVariations = [
        "RED",
        "Blue",
        "GREEN",
        "White",
        "BLACK",
        "Transparent",
        "CornFlowerBlue",
      ];

      caseVariations.forEach((color) => {
        expect(isValidCssColor(color)).toBe(true);
      });
    });
  });

  describe("isValidCssColor - Invalid colors", () => {
    it("should reject malformed hex colors", () => {
      const invalidHexColors = [
        "#", // Too short
        "#ff", // Too short
        "#fffff", // 5 digits
        "#fffffff", // 7 digits
        "#fffffffff", // 9 digits
        "#xyz", // Invalid characters
        "#ghijkl", // Invalid characters
        "fff", // Missing #
        "#ff-ff-ff", // Invalid format
      ];

      invalidHexColors.forEach((color) => {
        expect(isValidCssColor(color)).toBe(false);
      });
    });

    it("should reject malformed RGB colors", () => {
      const invalidRgbColors = [
        "rgb(256, 255, 255)", // Values > 255
        "rgb(-1, 0, 0)", // Negative values
        "rgb(255, 255)", // Missing value
        "rgb(255, 255, 255, 255)", // Too many values
        "rgb(255.5, 255, 255)", // Decimal not allowed
        "rgb(255 255 255)", // Missing commas
        "rgb(a, b, c)", // Non-numeric values
      ];

      invalidRgbColors.forEach((color) => {
        expect(isValidCssColor(color)).toBe(false);
      });
    });

    it("should reject malformed RGBA colors", () => {
      const invalidRgbaColors = [
        "rgba(255, 255, 255)", // Missing alpha
        "rgba(255, 255, 255, 2)", // Alpha > 1
        "rgba(255, 255, 255, -1)", // Alpha < 0
        "rgba(256, 255, 255, 0.5)", // RGB values > 255
        "rgba(255, 255, 255, 1.1)", // Alpha > 1.0
        "rgba(255, 255, 255, 0.)", // Invalid decimal
      ];

      invalidRgbaColors.forEach((color) => {
        expect(isValidCssColor(color)).toBe(false);
      });
    });

    it("should reject malformed HSL colors", () => {
      const invalidHslColors = [
        "hsl(361, 100%, 50%)", // Hue > 360
        "hsl(180, 101%, 50%)", // Saturation > 100%
        "hsl(180, 50%, 101%)", // Lightness > 100%
        "hsl(-1, 50%, 50%)", // Negative hue
        "hsl(180, -1%, 50%)", // Negative saturation
        "hsl(180, 50%, -1%)", // Negative lightness
        "hsl(180, 50%)", // Missing value
        "hsl(180 50% 50%)", // Missing commas
      ];

      invalidHslColors.forEach((color) => {
        expect(isValidCssColor(color)).toBe(false);
      });
    });

    it("should reject invalid named colors", () => {
      const invalidNamedColors = [
        "invalidcolor",
        "notacolor",
        "redd", // Typo
        "blueish", // Not a standard color
        "color123",
        "", // Empty string
        " ", // Just space
      ];

      invalidNamedColors.forEach((color) => {
        expect(isValidCssColor(color)).toBe(false);
      });
    });
  });

  describe("isValidCssColor - XSS and injection prevention", () => {
    it("should reject javascript: URLs", () => {
      const javascriptInjections = [
        "javascript:alert('xss')",
        "JAVASCRIPT:alert('xss')",
        "JavaScript:alert('xss')",
        "javascript: alert('xss')",
        "red; javascript:alert('xss')",
      ];

      javascriptInjections.forEach((injection) => {
        expect(isValidCssColor(injection)).toBe(false);
      });
    });

    it("should reject CSS expressions", () => {
      const expressionInjections = [
        "expression(alert('xss'))",
        "EXPRESSION(alert('xss'))",
        "Expression(alert('xss'))",
        "expression (alert('xss'))",
        "red; expression(alert('xss'))",
      ];

      expressionInjections.forEach((injection) => {
        expect(isValidCssColor(injection)).toBe(false);
      });
    });

    it("should reject url() references", () => {
      const urlInjections = [
        "url(javascript:alert('xss'))",
        "URL(javascript:alert('xss'))",
        "url (javascript:alert('xss'))",
        "red; url(malicious.js)",
      ];

      urlInjections.forEach((injection) => {
        expect(isValidCssColor(injection)).toBe(false);
      });
    });

    it("should reject @import statements", () => {
      const importInjections = [
        "@import url(malicious.css)",
        "@IMPORT url(malicious.css)",
        "@Import url(malicious.css)",
        "red; @import url(malicious.css)",
      ];

      importInjections.forEach((injection) => {
        expect(isValidCssColor(injection)).toBe(false);
      });
    });

    it("should reject behavior and binding properties", () => {
      const behaviorInjections = [
        "behavior: url(malicious.htc)",
        "BEHAVIOR: url(malicious.htc)",
        "binding: url(malicious.xml)",
        "BINDING: url(malicious.xml)",
        "red; behavior: url(malicious.htc)",
      ];

      behaviorInjections.forEach((injection) => {
        expect(isValidCssColor(injection)).toBe(false);
      });
    });

    it("should reject script tags in various encodings", () => {
      const scriptInjections = [
        "<script>alert('xss')</script>",
        "<SCRIPT>alert('xss')</SCRIPT>",
        "</script>",
        "</SCRIPT>",
        "&lt;script&gt;alert('xss')&lt;/script&gt;",
        "&lt;SCRIPT&gt;alert('xss')&lt;/SCRIPT&gt;",
        "red; <script>alert('xss')</script>",
      ];

      scriptInjections.forEach((injection) => {
        expect(isValidCssColor(injection)).toBe(false);
      });
    });
  });

  describe("isValidCssColor - Edge cases", () => {
    it("should handle null and undefined inputs", () => {
      expect(isValidCssColor(null as unknown as string)).toBe(false);
      expect(isValidCssColor(undefined as unknown as string)).toBe(false);
    });

    it("should handle non-string inputs", () => {
      expect(isValidCssColor(123 as unknown as string)).toBe(false);
      expect(isValidCssColor({} as unknown as string)).toBe(false);
      expect(isValidCssColor([] as unknown as string)).toBe(false);
      expect(isValidCssColor(true as unknown as string)).toBe(false);
    });

    it("should handle very short strings", () => {
      expect(isValidCssColor("")).toBe(false);
      expect(isValidCssColor("a")).toBe(false);
      expect(isValidCssColor("ab")).toBe(false);
    });

    it("should handle strings with only whitespace", () => {
      expect(isValidCssColor("   ")).toBe(false);
      expect(isValidCssColor("\n")).toBe(false);
      expect(isValidCssColor("\t")).toBe(false);
      expect(isValidCssColor(" \n\t ")).toBe(false);
    });

    it("should trim whitespace from valid colors", () => {
      expect(isValidCssColor("  red  ")).toBe(true);
      expect(isValidCssColor("\n#fff\n")).toBe(true);
      expect(isValidCssColor("\trgb(255, 255, 255)\t")).toBe(true);
    });

    it("should handle very long strings", () => {
      const longString = "a".repeat(10000);
      expect(isValidCssColor(longString)).toBe(false);
    });
  });

  describe("sanitizeCssColor", () => {
    it("should return trimmed valid colors", () => {
      expect(sanitizeCssColor("  red  ")).toBe("red");
      expect(sanitizeCssColor("\n#fff\n")).toBe("#fff");
      expect(sanitizeCssColor("\trgb(255, 255, 255)\t")).toBe(
        "rgb(255, 255, 255)"
      );
    });

    it("should return null for invalid colors", () => {
      expect(sanitizeCssColor("invalid")).toBeNull();
      expect(sanitizeCssColor("javascript:alert('xss')")).toBeNull();
      expect(sanitizeCssColor("")).toBeNull();
      expect(sanitizeCssColor(null as unknown as string)).toBeNull();
    });

    it("should handle mixed case colors properly", () => {
      expect(sanitizeCssColor("RED")).toBe("RED");
      expect(sanitizeCssColor("Blue")).toBe("Blue");
      expect(sanitizeCssColor("#FFF")).toBe("#FFF");
    });

    it("should preserve complex valid color formats", () => {
      const complexColors = [
        "rgba(255, 128, 0, 0.75)",
        "hsla(120, 100%, 50%, 0.3)",
        "rgb(255, 255, 255)",
        "#ff8800aa",
      ];

      complexColors.forEach((color) => {
        expect(sanitizeCssColor(color)).toBe(color);
      });
    });
  });

  describe("Performance and boundary testing", () => {
    it("should handle rapid successive validations", () => {
      const testColors = [
        "red",
        "blue",
        "green",
        "#fff",
        "#000",
        "rgb(255,0,0)",
        "invalid",
        "javascript:alert()",
        "hsl(120,100%,50%)",
        "transparent",
      ];

      // Test 1000 rapid validations
      for (let i = 0; i < 100; i++) {
        testColors.forEach((color) => {
          isValidCssColor(color);
        });
      }

      // Should complete without issues
      expect(true).toBe(true);
    });

    it("should handle strings with special characters", () => {
      const specialCharStrings = [
        "red\0null",
        "blue\x01control",
        "green\uFEFFbom",
        "#fff\x7Fdelete",
        "rgb(255,255,255)\x00",
      ];

      specialCharStrings.forEach((color) => {
        expect(isValidCssColor(color)).toBe(false);
      });
    });

    it("should handle unicode characters", () => {
      const unicodeStrings = [
        "红色", // Chinese for red
        "赤", // Japanese for red
        "κόκκινο", // Greek for red
        "красный", // Russian for red
        "🔴", // Red circle emoji
      ];

      unicodeStrings.forEach((color) => {
        expect(isValidCssColor(color)).toBe(false);
      });
    });

    it("should validate all predefined named colors", () => {
      // Test some specific named colors that should be valid
      const namedColors = [
        "aliceblue",
        "antiquewhite",
        "aqua",
        "aquamarine",
        "azure",
        "beige",
        "bisque",
        "black",
        "blanchedalmond",
        "blue",
        "cornflowerblue",
        "crimson",
        "cyan",
        "darkblue",
        "darkcyan",
        "lightgoldenrodyellow",
        "rebeccapurple",
        "transparent",
      ];

      namedColors.forEach((color) => {
        expect(isValidCssColor(color)).toBe(true);
      });
    });
  });
});
