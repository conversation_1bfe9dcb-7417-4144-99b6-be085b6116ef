import { promises as fs, constants } from "fs";
import * as path from "path";
import { get<PERSON><PERSON><PERSON>ider } from "./index";
import SystemSettings from "../../models/systemSettings";
import { LLMProvider } from "../../types/ai-providers";

/**
 * Interface for chat messages
 */
interface ChatMessage {
  role: "system" | "user" | "assistant";
  content: string;
}

/**
 * Interface for documentation structure
 */
interface DocumentationItem {
  file: string;
  content: string;
}

/**
 * Interface for aggregated documentation
 */
interface AggregatedDocumentation {
  cursorRules: DocumentationItem[];
  backendDocs: DocumentationItem[];
  frontendDocs: DocumentationItem[];
  collectorDocs: DocumentationItem[];
  relevantFiles: string[];
}

/**
 * Interface for system report
 */
interface SystemReport {
  id: number;
  title: string;
  description: string;
  type: "INCIDENT" | "FEATURE_REQUEST";
  affected_service: string;
  severity?: string;
}

/**
 * Interface for documentation paths
 */
interface DocumentationPaths {
  cursorRules: string;
  serverDocs: string;
  frontendDocs: string;
  collectorDocs: string;
}

/**
 * Auto-Coding Prompt Generator Service
 *
 * This service generates structured prompts for Cursor integration based on
 * system reports (bug reports and feature requests). It aggregates relevant
 * documentation and uses LLM to create comprehensive auto-coding instructions.
 */
class AutoCodingPromptGenerator {
  private documentationCache: Map<string, string>;
  private cursorRulesCache: Map<string, string>;
  private maxContextLength: number;
  private enablePathValidation: boolean;
  private enableDebugLogging: boolean;
  private paths!: DocumentationPaths;

  constructor() {
    this.documentationCache = new Map();
    this.cursorRulesCache = new Map();
    this.maxContextLength = 32000; // Conservative limit for most LLMs

    // Production-ready configuration - disable verbose logging by default
    this.enablePathValidation = false;
    this.enableDebugLogging = false;

    // Pre-compute and validate paths to ensure they exist
    this.initializePaths();
  }

  /**
   * Initialize and validate documentation paths
   * This ensures paths are correct regardless of where the process is started
   */
  private initializePaths(): void {
    this.paths = {
      cursorRules: path.resolve(__dirname, "../../../.cursor/rules"),
      serverDocs: path.resolve(__dirname, "../../docs"),
      frontendDocs: path.resolve(__dirname, "../../../frontend/docs"),
      collectorDocs: path.resolve(__dirname, "../../../collector/docs"),
    };

    // Log path resolution for debugging when enabled
    if (this.enableDebugLogging) {
      console.log("[AutoCodingPromptGenerator] Resolved paths:", {
        cursorRules: this.paths.cursorRules,
        serverDocs: this.paths.serverDocs,
        frontendDocs: this.paths.frontendDocs,
        collectorDocs: this.paths.collectorDocs,
      });
    }

    // Validate paths exist when enabled (development only by default)
    if (this.enablePathValidation) {
      this.validatePathsAsync();
    }
  }

  /**
   * Validate that documentation paths exist (async, for development only)
   */
  private async validatePathsAsync(): Promise<void> {
    for (const [name, dirPath] of Object.entries(this.paths)) {
      try {
        await fs.access(dirPath, constants.R_OK);
        if (this.enableDebugLogging) {
          console.log(
            `[AutoCodingPromptGenerator] ✓ ${name} path exists: ${dirPath}`
          );
        }
      } catch {
        console.warn(
          `[AutoCodingPromptGenerator] ⚠ ${name} path not accessible: ${dirPath}`
        );
        console.warn(
          `  This may cause issues when generating prompts for reports requiring ${name}`
        );
      }
    }
  }

  /**
   * Generate auto-coding prompt for a system report
   * @param report - System report object from database
   * @returns Generated Cursor-compatible prompt
   */
  async generatePrompt(report: SystemReport): Promise<string> {
    try {
      // Aggregate relevant documentation
      const documentation = await this.aggregateDocumentation(report);

      // Get LLM provider for auto-coding
      const llmProvider = await this.getLLMProviderForAutoCoding();

      // Create meta-prompt for LLM
      const metaPrompt = await this.createMetaPrompt(report, documentation);

      // Generate prompt using LLM
      const generatedPrompt = await this.generatePromptWithLLM(
        llmProvider,
        metaPrompt
      );

      // Format for Cursor Slack integration
      const cursorPrompt = this.formatForCursor(generatedPrompt, report);

      return cursorPrompt;
    } catch (error) {
      console.error(
        "[AutoCodingPromptGenerator] Error generating prompt:",
        error
      );
      // Fallback to basic prompt if LLM generation fails
      return this.generateFallbackPrompt(report);
    }
  }

  /**
   * Aggregate relevant documentation based on report type and affected service
   * @param report - System report object
   * @returns Aggregated documentation
   */
  private async aggregateDocumentation(
    report: SystemReport
  ): Promise<AggregatedDocumentation> {
    const documentation: AggregatedDocumentation = {
      cursorRules: [],
      backendDocs: [],
      frontendDocs: [],
      collectorDocs: [],
      relevantFiles: [],
    };

    try {
      // Get cursor rules based on report characteristics
      documentation.cursorRules = await this.getRelevantCursorRules(report);

      // Get comprehensive backend documentation
      documentation.backendDocs =
        await this.getComprehensiveBackendDocs(report);

      // Get comprehensive frontend documentation
      documentation.frontendDocs =
        await this.getComprehensiveFrontendDocs(report);

      // Get collector documentation if relevant
      if (this.isDocumentProcessingRelated(report)) {
        documentation.collectorDocs =
          await this.getComprehensiveCollectorDocs(report);
      }

      // Identify relevant files based on affected service
      documentation.relevantFiles = this.identifyRelevantFiles(report);
    } catch (error) {
      console.error(
        "[AutoCodingPromptGenerator] Error aggregating documentation:",
        error
      );
    }

    return documentation;
  }

  /**
   * Get relevant cursor rules based on report characteristics
   * @param report - System report object
   * @returns Relevant cursor rules
   */
  private async getRelevantCursorRules(
    report: SystemReport
  ): Promise<DocumentationItem[]> {
    const rules: DocumentationItem[] = [];
    const rulesDir = this.paths.cursorRules;

    try {
      // Always include core rules
      const coreRules = [
        "coding-standards.mdc",
        "security-guidelines.mdc",
        "testing-guidelines.mdc",
        "documentation-updates.mdc",
      ];

      // Add service-specific rules
      const serviceRules = this.getServiceSpecificRules(
        report.affected_service
      );

      // Add type-specific rules
      const typeRules = this.getTypeSpecificRules(report.type);

      const allRuleFiles = [...coreRules, ...serviceRules, ...typeRules];

      for (const ruleFile of allRuleFiles) {
        const rulePath = path.join(rulesDir, ruleFile);
        try {
          const content = await this.readFileWithCache(
            rulePath,
            this.cursorRulesCache
          );
          if (content) {
            rules.push({
              file: ruleFile,
              content: content, // Include full content without truncation
            });
          }
        } catch {
          // Rule file doesn't exist, skip silently
          continue;
        }
      }
    } catch {
      console.error("[AutoCodingPromptGenerator] Error reading cursor rules");
    }

    return rules;
  }

  /**
   * Get service-specific cursor rules
   * @param service - Affected service
   * @returns Service-specific rule files
   */
  private getServiceSpecificRules(service: string): string[] {
    const serviceRuleMap: Record<string, string[]> = {
      AUTHENTICATION: [
        "backend-development.mdc",
        "user-endpoint-development.mdc",
      ],
      DOCUMENT_MANAGEMENT: ["document-processing.mdc", "document-sync.mdc"],
      CHAT_SYSTEM: [
        "chat-system.mdc",
        "llm-integration.mdc",
        "context-window-management.mdc",
      ],
      SEARCH: [
        "vector-search-overview.mdc",
        "embedding-models.mdc",
        "lancedb-integration.mdc",
      ],
      ADMIN: ["system-settings.mdc", "backend-api.mdc"],
      UI_UX: [
        "frontend-development.mdc",
        "ui-components.mdc",
        "wcag-accessibility-compliance.mdc",
      ],
    };

    return (
      serviceRuleMap[service] || [
        "backend-development.mdc",
        "frontend-development.mdc",
      ]
    );
  }

  /**
   * Get type-specific cursor rules
   * @param type - Report type (INCIDENT or FEATURE_REQUEST)
   * @returns Type-specific rule files
   */
  private getTypeSpecificRules(type: "INCIDENT" | "FEATURE_REQUEST"): string[] {
    if (type === "FEATURE_REQUEST") {
      return ["implementation-strategy.mdc", "form-guidelines.mdc"];
    }
    return ["linting-and-code-quality.mdc"];
  }

  /**
   * Get relevant backend documentation files based on affected service
   * @param service - Affected service
   * @returns Relevant doc files
   */
  private getRelevantBackendDocFiles(service: string): string[] {
    const serviceDocMap: Record<string, string[]> = {
      AUTHENTICATION: ["troubleshooting_user_endpoints.md", "UserRoles.md"],
      DOCUMENT_MANAGEMENT: ["document_builder_prompts.md", "docx_handling.md"],
      CHAT_SYSTEM: [
        "iterative_context_window_management.md",
        "vector_and_context_handling.md",
      ],
      SEARCH: ["lancedb.md", "deepsearch-implementation.md"],
      ADMIN: ["implementing_system_settings.md", "ENDPOINTS.md"],
      UI_UX: ["README.md"],
    };

    return serviceDocMap[service] || ["ENDPOINTS.md", "README.md"];
  }

  /**
   * Get relevant frontend documentation files based on affected service
   * @param service - Affected service
   * @returns Relevant doc files
   */
  private getRelevantFrontendDocFiles(service: string): string[] {
    const serviceDocMap: Record<string, string[]> = {
      AUTHENTICATION: ["README.md"],
      DOCUMENT_MANAGEMENT: ["document-builder-page.md", "attachments.md"],
      CHAT_SYSTEM: ["chat-system-architecture.md", "chat-progress-system.md"],
      SEARCH: ["deepsearch-settings.md"],
      ADMIN: ["system-reports-feature.md"],
      UI_UX: ["ui-component-architecture.md", "ui-components.md"],
    };

    return serviceDocMap[service] || ["README.md"];
  }

  /**
   * Identify relevant files to focus on based on affected service
   * @param report - System report object
   * @returns List of relevant file paths
   */
  private identifyRelevantFiles(report: SystemReport): string[] {
    const serviceFileMap: Record<string, string[]> = {
      AUTHENTICATION: [
        "server/utils/middleware/validatedRequest.js",
        "server/models/user.js",
        "frontend/src/components/Auth/",
      ],
      DOCUMENT_MANAGEMENT: [
        "server/endpoints/document.js",
        "server/models/documents.js",
        "frontend/src/components/Modals/ManageWorkspace/",
      ],
      CHAT_SYSTEM: [
        "server/endpoints/chat.js",
        "server/utils/chats/",
        "frontend/src/components/WorkspaceChat/",
      ],
      SEARCH: [
        "server/utils/vectorDbProviders/",
        "server/utils/EmbeddingEngines/",
        "frontend/src/pages/GeneralSettings/",
      ],
      ADMIN: [
        "server/endpoints/admin.js",
        "server/models/systemSettings.js",
        "frontend/src/pages/Admin/",
      ],
      UI_UX: [
        "frontend/src/components/",
        "frontend/src/utils/",
        "frontend/src/pages/",
      ],
    };

    return serviceFileMap[report.affected_service] || [];
  }

  /**
   * Check if report is document processing related
   * @param report - System report object
   * @returns True if document processing related
   */
  private isDocumentProcessingRelated(report: SystemReport): boolean {
    return (
      report.affected_service === "DOCUMENT_MANAGEMENT" ||
      report.description.toLowerCase().includes("document") ||
      report.description.toLowerCase().includes("file") ||
      report.description.toLowerCase().includes("upload") ||
      report.description.toLowerCase().includes("parsing") ||
      report.description.toLowerCase().includes("collector")
    );
  }

  /**
   * Get comprehensive backend documentation
   * @param report - System report object
   * @returns All relevant backend documentation
   */
  private async getComprehensiveBackendDocs(
    report: SystemReport
  ): Promise<DocumentationItem[]> {
    const docs: DocumentationItem[] = [];
    const backendDocsDir = this.paths.serverDocs;

    try {
      // Get all markdown files from backend docs directory
      const allDocFiles = await this.scanDocumentationDirectory(
        backendDocsDir,
        ".md"
      );

      // Prioritize service-specific docs but include others for context
      const relevantDocs = this.getRelevantBackendDocFiles(
        report.affected_service
      );
      const prioritizedFiles = [
        ...relevantDocs,
        ...allDocFiles.filter((f) => !relevantDocs.includes(f)),
      ];

      // Include all backend documentation without size limits
      for (const docFile of prioritizedFiles) {
        const docPath = path.join(backendDocsDir, docFile);
        try {
          const content = await this.readFileWithCache(
            docPath,
            this.documentationCache
          );
          if (content) {
            docs.push({
              file: docFile,
              content: content, // Include full content without truncation
            });
          }
        } catch {
          continue;
        }
      }
    } catch {
      console.error(
        "[AutoCodingPromptGenerator] Error reading comprehensive backend docs"
      );
    }

    return docs;
  }

  /**
   * Get comprehensive frontend documentation
   * @param report - System report object
   * @returns All relevant frontend documentation
   */
  private async getComprehensiveFrontendDocs(
    report: SystemReport
  ): Promise<DocumentationItem[]> {
    const docs: DocumentationItem[] = [];
    const frontendDocsDir = this.paths.frontendDocs;

    try {
      // Get all markdown files from frontend docs directory
      const allDocFiles = await this.scanDocumentationDirectory(
        frontendDocsDir,
        ".md"
      );

      // Prioritize service-specific docs but include others for context
      const relevantDocs = this.getRelevantFrontendDocFiles(
        report.affected_service
      );
      const prioritizedFiles = [
        ...relevantDocs,
        ...allDocFiles.filter((f) => !relevantDocs.includes(f)),
      ];

      // Include all frontend documentation without size limits
      for (const docFile of prioritizedFiles) {
        const docPath = path.join(frontendDocsDir, docFile);
        try {
          const content = await this.readFileWithCache(
            docPath,
            this.documentationCache
          );
          if (content) {
            docs.push({
              file: docFile,
              content: content, // Include full content without truncation
            });
          }
        } catch {
          continue;
        }
      }
    } catch {
      console.error(
        "[AutoCodingPromptGenerator] Error reading comprehensive frontend docs"
      );
    }

    return docs;
  }

  /**
   * Get comprehensive collector documentation
   * @param _report - System report object (unused but kept for future filtering)
   * @returns All relevant collector documentation
   */
  private async getComprehensiveCollectorDocs(
    _report: SystemReport
  ): Promise<DocumentationItem[]> {
    const docs: DocumentationItem[] = [];
    const collectorDocsDir = this.paths.collectorDocs;

    try {
      // Get all markdown files from collector docs directory
      const allDocFiles = await this.scanDocumentationDirectory(
        collectorDocsDir,
        ".md"
      );

      // Include all collector documentation without size limits
      for (const docFile of allDocFiles) {
        const docPath = path.join(collectorDocsDir, docFile);
        try {
          const content = await this.readFileWithCache(
            docPath,
            this.documentationCache
          );
          if (content) {
            docs.push({
              file: docFile,
              content: content, // Include full content without truncation
            });
          }
        } catch {
          continue;
        }
      }
    } catch {
      console.error(
        "[AutoCodingPromptGenerator] Error reading comprehensive collector docs"
      );
    }

    return docs;
  }

  /**
   * Scan documentation directory for files
   * @param dirPath - Directory path to scan
   * @param extension - File extension to filter by (e.g., ".md")
   * @returns Array of file names
   */
  private async scanDocumentationDirectory(
    dirPath: string,
    extension: string = ".md"
  ): Promise<string[]> {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });

      const files: string[] = [];

      for (const entry of entries) {
        if (entry.isFile() && entry.name.endsWith(extension)) {
          files.push(entry.name);
        } else if (entry.isDirectory()) {
          // Recursively scan subdirectories
          try {
            const subDirPath = path.join(dirPath, entry.name);
            const subFiles = await this.scanDocumentationDirectory(
              subDirPath,
              extension
            );
            // Add subdirectory files with relative path
            subFiles.forEach((file) => files.push(path.join(entry.name, file)));
          } catch {
            // Skip inaccessible subdirectories
            continue;
          }
        }
      }

      return files;
    } catch {
      console.error(
        `[AutoCodingPromptGenerator] Error scanning directory ${dirPath}`
      );
      return [];
    }
  }

  /**
   * Create meta-prompt for LLM to generate Cursor prompt
   * @param report - System report
   * @param documentation - Aggregated documentation
   * @returns Meta-prompt for LLM
   */
  private async createMetaPrompt(
    report: SystemReport,
    documentation: AggregatedDocumentation
  ): Promise<string> {
    const reportType =
      report.type === "FEATURE_REQUEST" ? "feature request" : "bug report";
    const severity = report.severity ? ` (Severity: ${report.severity})` : "";

    // Build comprehensive documentation context
    const documentationContext = this.buildDocumentationContext(documentation);

    // Get the auto-coding template from system settings with language detection

    const template = await this.getAutoCodingTemplate();

    // If we have a custom template, use it and replace variables
    if (template && template.includes("{{")) {
      let populatedTemplate = template
        .replace("{{title}}", report.title)
        .replace("{{description}}", report.description)
        .replace("{{type}}", report.type)
        .replace(
          "{{affected_service}}",
          report.affected_service || "Not specified"
        )
        .replace("{{severity}}", report.severity || "Not specified")
        .replace("{{reportType}}", reportType);

      // Replace documentation context placeholder
      populatedTemplate = populatedTemplate
        .replace(
          "[Comprehensive documentation context from server/docs/, frontend/docs/, collector/docs/, and .cursor/rules/ will be automatically inserted here]",
          documentationContext
        )
        .replace(
          "[Documentation context will be automatically inserted here]",
          documentationContext
        );

      return populatedTemplate;
    }

    // Otherwise, use the default logic for backward compatibility
    let prompt = `Generate a comprehensive auto-coding prompt for Cursor Slack integration based on this ${reportType}${severity}:

**Report Details:**
Title: ${report.title}
Description: ${report.description}
Type: ${report.type}
Affected Service: ${report.affected_service || "Not specified"}

**Available Documentation Context:**

`;

    // Add cursor rules
    if ((documentation?.cursorRules?.length ?? 0) > 0) {
      prompt += "**Coding Standards & Guidelines:**\n";
      documentation.cursorRules.forEach((rule) => {
        prompt += `- ${rule.file}: ${rule.content.substring(0, 500)}...\n`;
      });
      prompt += "\n";
    }

    // Add backend documentation
    if ((documentation?.backendDocs?.length ?? 0) > 0) {
      prompt += "**Backend Documentation:**\n";
      documentation.backendDocs.forEach((doc) => {
        prompt += `- ${doc.file}: ${doc.content.substring(0, 500)}...\n`;
      });
      prompt += "\n";
    }

    // Add frontend documentation
    if ((documentation?.frontendDocs?.length ?? 0) > 0) {
      prompt += "**Frontend Documentation:**\n";
      documentation.frontendDocs.forEach((doc) => {
        prompt += `- ${doc.file}: ${doc.content.substring(0, 500)}...\n`;
      });
      prompt += "\n";
    }

    // Add collector documentation
    if ((documentation?.collectorDocs?.length ?? 0) > 0) {
      prompt += "**Collector Documentation:**\n";
      documentation.collectorDocs.forEach((doc) => {
        prompt += `- ${doc.file}: ${doc.content.substring(0, 500)}...\n`;
      });
      prompt += "\n";
    }

    // Add relevant files
    if ((documentation?.relevantFiles?.length ?? 0) > 0) {
      prompt += "**Relevant Files to Focus On:**\n";
      documentation.relevantFiles.forEach((file) => {
        prompt += `- ${file}\n`;
      });
      prompt += "\n";
    }

    prompt += `**Instructions:**
Generate a structured auto-coding prompt that follows these requirements:

1. **Clear Problem Statement**: Summarize the issue/feature in a clear, actionable way
2. **Technical Requirements**: Include specific technical requirements based on the documentation
3. **Implementation Guidelines**: Reference relevant coding standards and architectural patterns
4. **File Suggestions**: Suggest specific files or directories to focus on
5. **Testing Requirements**: Include testing expectations
6. **Documentation Updates**: Mention if documentation updates are needed

The prompt should be professional, comprehensive, and provide enough context for an AI assistant to implement the ${reportType} effectively while following the project's standards.

Format the response as a clear, structured prompt without any @Cursor syntax (that will be added later). Focus on the technical content and implementation guidance.`;

    return this.truncateContent(prompt, this.maxContextLength - 1000); // Leave room for response
  }

  /**
   * Build comprehensive documentation context string
   * @param documentation - Aggregated documentation object
   * @returns Formatted documentation context
   */
  private buildDocumentationContext(
    documentation: AggregatedDocumentation
  ): string {
    let context = "";

    // Add cursor rules
    if (
      documentation.cursorRules &&
      (documentation?.cursorRules?.length ?? 0) > 0
    ) {
      context += "**Coding Standards & Guidelines:**\n";
      documentation.cursorRules.forEach((rule) => {
        context += `\n**${rule.file}:**\n${rule.content}\n`;
      });
      context += "\n";
    }

    // Add backend documentation
    if (
      documentation.backendDocs &&
      (documentation?.backendDocs?.length ?? 0) > 0
    ) {
      context += "**Backend Documentation:**\n";
      documentation.backendDocs.forEach((doc) => {
        context += `\n**${doc.file}:**\n${doc.content}\n`;
      });
      context += "\n";
    }

    // Add frontend documentation
    if (
      documentation.frontendDocs &&
      (documentation?.frontendDocs?.length ?? 0) > 0
    ) {
      context += "**Frontend Documentation:**\n";
      documentation.frontendDocs.forEach((doc) => {
        context += `\n**${doc.file}:**\n${doc.content}\n`;
      });
      context += "\n";
    }

    // Add collector documentation
    if (
      documentation.collectorDocs &&
      (documentation?.collectorDocs?.length ?? 0) > 0
    ) {
      context += "**Collector/Document Processing Documentation:**\n";
      documentation.collectorDocs.forEach((doc) => {
        context += `\n**${doc.file}:**\n${doc.content}\n`;
      });
      context += "\n";
    }

    // Add relevant files
    if (
      documentation.relevantFiles &&
      (documentation?.relevantFiles?.length ?? 0) > 0
    ) {
      context += "**Relevant Files to Focus On:**\n";
      documentation.relevantFiles.forEach((file) => {
        context += `- ${file}\n`;
      });
      context += "\n";
    }

    return context.trim() || "No specific documentation context available.";
  }

  /**
   * Generate prompt using LLM
   * @param llmProvider - LLM provider instance
   * @param metaPrompt - Meta-prompt for generation
   * @returns Generated prompt
   */
  private async generatePromptWithLLM(
    llmProvider: LLMProvider,
    metaPrompt: string
  ): Promise<string> {
    try {
      const messages: ChatMessage[] = [
        {
          role: "system",
          content:
            "You are an expert software engineering assistant that creates detailed auto-coding prompts for development tasks. Generate comprehensive, actionable prompts that include technical context, implementation guidance, and quality requirements.",
        },
        {
          role: "user",
          content: metaPrompt,
        },
      ];

      const props = {
        model: llmProvider.model,
        max_tokens: Math.floor(llmProvider.promptWindowLimit() * 0.4), // Use 40% for response
        messages,
        temperature: 0.1, // Low temperature for consistent, structured output
      };

      const response = await llmProvider.getChatCompletion(props.messages, {
        temperature: props.temperature,
      });

      return response?.textResponse || "Unable to generate prompt";
    } catch (error) {
      console.error("[AutoCodingPromptGenerator] LLM generation error:", error);
      throw error;
    }
  }

  /**
   * Format generated prompt for Cursor Slack integration
   * @param generatedPrompt - LLM-generated prompt
   * @param report - Original system report
   * @returns Cursor-formatted prompt
   */
  private formatForCursor(
    generatedPrompt: string,
    report: SystemReport
  ): string {
    // Get default repository configuration
    const repoName = process.env.CURSOR_DEFAULT_REPO || "RahSwe/ISTLegal";
    const defaultBranch = process.env.CURSOR_DEFAULT_BRANCH || "develop";
    const preferredModel = this.getPreferredCursorModel(report);

    // Format with Cursor syntax
    let cursorPrompt = `@Cursor [repo=${repoName}, branch=${defaultBranch}`;

    if (preferredModel) {
      cursorPrompt += `, model=${preferredModel}`;
    }

    cursorPrompt += `]\n\n${generatedPrompt}`;

    // Add footer with report reference
    cursorPrompt += `\n\n---\n*Auto-generated from System Report #${report.id}: ${report.title}*`;

    return cursorPrompt;
  }

  /**
   * Get preferred Cursor model based on report complexity
   * @param report - System report
   * @returns Preferred model or null for default
   */
  private getPreferredCursorModel(report: SystemReport): string | null {
    // Use more capable models for complex issues
    if (
      report.severity === "CRITICAL" ||
      report.affected_service === "AUTHENTICATION" ||
      report.description.length > 500
    ) {
      return "claude-3-5-sonnet";
    }

    // Use default model for simpler issues
    return null;
  }

  /**
   * Generate fallback prompt if LLM generation fails
   * @param report - System report
   * @returns Basic Cursor prompt
   */
  private generateFallbackPrompt(report: SystemReport): string {
    const repoName = process.env.CURSOR_DEFAULT_REPO || "RahSwe/ISTLegal";
    const defaultBranch = process.env.CURSOR_DEFAULT_BRANCH || "develop";
    const actionType = report.type === "FEATURE_REQUEST" ? "Implement" : "Fix";

    return `@Cursor [repo=${repoName}, branch=${defaultBranch}]

${actionType}: ${report.title}

**Description:** ${report.description}

**Type:** ${report.type}
**Affected Service:** ${report.affected_service || "General"}
${report.severity ? `**Severity:** ${report.severity}` : ""}

**Requirements:**
- Follow IST Legal coding standards and best practices
- Ensure proper error handling and user feedback
- Add appropriate tests for the implementation
- Update documentation if needed
- Maintain backward compatibility where applicable

Please implement this ${report.type === "FEATURE_REQUEST" ? "feature" : "fix"} according to the project's architectural patterns and coding guidelines.

---
*Auto-generated from System Report #${report.id}*`;
  }

  /**
   * Get LLM provider configured for auto-coding
   * @returns LLM provider instance
   */
  private async getLLMProviderForAutoCoding(): Promise<LLMProvider> {
    try {
      // Try to get dedicated auto-coding LLM provider
      const acProviderSetting = await SystemSettings.get({
        label: "LLMProvider_AC",
      });

      let providerChoice = acProviderSetting?.value ?? undefined;
      let suffixForProvider: string | undefined = "_AC";

      if (!providerChoice || providerChoice === "system-standard") {
        // Fall back to system default
        providerChoice = process.env.LLM_PROVIDER || "openai";
        suffixForProvider = undefined;
      }

      const LLMProvider = getLLMProvider({
        provider: providerChoice,
        settings_suffix: suffixForProvider,
      });

      // Validate the provider
      if (
        !LLMProvider ||
        !LLMProvider.isValidChatCompletionModel(LLMProvider.model)
      ) {
        throw new Error(`Invalid LLM provider configuration for auto-coding`);
      }

      return LLMProvider;
    } catch (error) {
      console.error(
        "[AutoCodingPromptGenerator] Error getting LLM provider:",
        error
      );
      // Final fallback to basic OpenAI
      return getLLMProvider({ provider: "openai" });
    }
  }

  /**
   * Read file with caching and improved error handling
   * @param filePath - Path to file
   * @param cache - Cache map to use
   * @returns File content
   */
  private async readFileWithCache(
    filePath: string,
    cache: Map<string, string>
  ): Promise<string | null> {
    if (cache.has(filePath)) {
      return cache.get(filePath) || null;
    }

    try {
      // Ensure the path exists and is accessible
      await fs.access(filePath, constants.R_OK);
      const content = await fs.readFile(filePath, "utf8");
      cache.set(filePath, content);
      return content;
    } catch (error) {
      // Log detailed error when debug logging is enabled
      if (this.enableDebugLogging) {
        console.warn(
          `[AutoCodingPromptGenerator] Could not read file: ${filePath}`,
          error instanceof Error ? error.message : String(error)
        );
      }
      return null; // File doesn't exist or is not accessible
    }
  }

  /**
   * Truncate content to fit within limits
   * @param content - Content to truncate
   * @param maxLength - Maximum length
   * @returns Truncated content
   */
  private truncateContent(content: string, maxLength: number): string {
    if (content.length <= maxLength) {
      return content;
    }

    return content.substring(0, maxLength - 3) + "...";
  }

  /**
   * Clear documentation cache (for memory management)
   */
  clearCache(): void {
    this.documentationCache.clear();
    this.cursorRulesCache.clear();
  }

  /**
   * Get the auto-coding template from system settings
   * @returns Auto-coding template
   */
  private async getAutoCodingTemplate(): Promise<string> {
    try {
      const template = await SystemSettings.getAutoCodingTemplate();
      return template;
    } catch {
      console.error("Failed to get auto-coding template");
      return "Generate a comprehensive auto-coding prompt for Cursor Slack integration based on this report.";
    }
  }
}

// Export singleton instance
export default new AutoCodingPromptGenerator();
