/**
 * Environment Variable Mapping Utilities
 *
 * This module handles the mapping between backend environment variables
 * and frontend-expected camelCase keys for the /setup-complete endpoint.
 *
 * The frontend expects keys like:
 * - OpenAiKey, OpenAiModelPref
 * - AnthropicApiKey, AnthropicModelPref
 * - etc.
 *
 * But the backend stores them as:
 * - OPEN_AI_KEY, OPEN_AI_MODEL_PREF
 * - ANTHROPIC_API_KEY, ANTHROPIC_MODEL_PREF
 * - etc.
 */

// Provider suffix definitions
export const PROVIDER_SUFFIXES = {
  DD: "_DD",
  DD_2: "_DD_2",
  VA: "_VA",
  CDB: "_CDB",
  CUAI: "_CUAI",
  CUAI2: "_CUAI2",
  CUAI3: "_CUAI3",
  CUAI4: "_CUAI4",
  CUAI5: "_CUAI5",
  CUAI6: "_CUAI6",
  PU: "_PU",
  TM: "_TM",
  SUPPORT: "_SUPPORT",
};

// Provider configuration for mapping API keys and model preferences
interface ProviderConfig {
  apiKeyEnvPattern: string;
  apiKeyFrontendPattern: string;
  modelPrefEnvPattern?: string;
  modelPrefFrontendPattern?: string;
}

const PROVIDER_CONFIGS: Record<string, ProviderConfig> = {
  openai: {
    apiKeyEnvPattern: "OPEN_AI_KEY",
    apiKeyFrontendPattern: "OpenAiKey",
    modelPrefEnvPattern: "OPEN_AI_MODEL_PREF",
    modelPrefFrontendPattern: "OpenAiModelPref",
  },
  anthropic: {
    apiKeyEnvPattern: "ANTHROPIC_API_KEY",
    apiKeyFrontendPattern: "AnthropicApiKey",
    modelPrefEnvPattern: "ANTHROPIC_MODEL_PREF",
    modelPrefFrontendPattern: "AnthropicModelPref",
  },
  gemini: {
    apiKeyEnvPattern: "GEMINI_API_KEY",
    apiKeyFrontendPattern: "GeminiApiKey",
    modelPrefEnvPattern: "GEMINI_LLM_MODEL_PREF",
    modelPrefFrontendPattern: "GeminiLLMModelPref",
  },
  xai: {
    apiKeyEnvPattern: "XAI_LLM_API_KEY",
    apiKeyFrontendPattern: "XAIApiKey",
    modelPrefEnvPattern: "XAI_LLM_MODEL_PREF",
    modelPrefFrontendPattern: "XAIModelPref",
  },
  groq: {
    apiKeyEnvPattern: "GROQ_API_KEY",
    apiKeyFrontendPattern: "GroqApiKey",
    modelPrefEnvPattern: "GROQ_MODEL_PREF",
    modelPrefFrontendPattern: "GroqModelPref",
  },
  localai: {
    apiKeyEnvPattern: "LOCAL_AI_API_KEY",
    apiKeyFrontendPattern: "LocalAiApiKey",
    modelPrefEnvPattern: "LOCAL_AI_MODEL_PREF",
    modelPrefFrontendPattern: "LocalAiModelPref",
  },
};

// Additional providers with only model preferences (no API keys)
const MODEL_ONLY_PROVIDERS = [
  { envPattern: "MISTRAL_MODEL_PREF", frontendPattern: "MistralModelPref" },
  { envPattern: "OLLAMA_MODEL_PREF", frontendPattern: "OllamaLLMModelPref" },
  { envPattern: "LMSTUDIO_MODEL_PREF", frontendPattern: "LMStudioModelPref" },
  {
    envPattern: "TOGETHER_AI_MODEL_PREF",
    frontendPattern: "TogetherAiModelPref",
  },
  {
    envPattern: "FIREWORKS_AI_LLM_MODEL_PREF",
    frontendPattern: "FireworksAiLLMModelPref",
  },
  {
    envPattern: "PERPLEXITY_MODEL_PREF",
    frontendPattern: "PerplexityModelPref",
  },
  {
    envPattern: "OPENROUTER_MODEL_PREF",
    frontendPattern: "OpenRouterModelPref",
  },
  { envPattern: "COHERE_MODEL_PREF", frontendPattern: "CohereModelPref" },
  { envPattern: "DEEPSEEK_MODEL_PREF", frontendPattern: "DeepSeekModelPref" },
  {
    envPattern: "NATIVE_LLM_MODEL_PREF",
    frontendPattern: "NativeLLMModelPref",
  },
  {
    envPattern: "GENERIC_OPEN_AI_MODEL_PREF",
    frontendPattern: "GenericOpenAiModelPref",
  },
  {
    envPattern: "AWS_BEDROCK_LLM_MODEL_PREFERENCE",
    frontendPattern: "AwsBedrockLLMModel",
  },
  {
    envPattern: "AZURE_OPENAI_MODEL_PREF",
    frontendPattern: "AzureOpenAiModelPref",
  },
];

/**
 * Maps environment variables to frontend-expected keys for all provider suffixes
 */
export function mapSuffixedProviderSettings(
  envSettings: Record<string, string>
): Record<string, string> {
  const envToFrontendKeyMap: Record<string, string> = {};

  // Map all provider suffixes
  Object.entries(PROVIDER_SUFFIXES).forEach(([_suffixName, suffix]) => {
    // Map configured providers (API keys and model preferences)
    Object.entries(PROVIDER_CONFIGS).forEach(([_providerName, config]) => {
      // API Key mapping
      const apiKeyEnvKey = `${config.apiKeyEnvPattern}${suffix}`;
      const apiKeyFrontendKey = `${config.apiKeyFrontendPattern}${suffix}`;
      if (envSettings[apiKeyEnvKey]) {
        envToFrontendKeyMap[apiKeyFrontendKey] = envSettings[apiKeyEnvKey];
      }

      // Model preference mapping
      if (config.modelPrefEnvPattern && config.modelPrefFrontendPattern) {
        const modelEnvKey = `${config.modelPrefEnvPattern}${suffix}`;
        const modelFrontendKey = `${config.modelPrefFrontendPattern}${suffix}`;
        if (envSettings[modelEnvKey]) {
          envToFrontendKeyMap[modelFrontendKey] = envSettings[modelEnvKey];
        }
      }
    });

    // Map additional model-only providers
    MODEL_ONLY_PROVIDERS.forEach(({ envPattern, frontendPattern }) => {
      const envKey = `${envPattern}${suffix}`;
      const frontendKey = `${frontendPattern}${suffix}`;
      if (envSettings[envKey]) {
        envToFrontendKeyMap[frontendKey] = envSettings[envKey];
      }
    });
  });

  return envToFrontendKeyMap;
}

/**
 * Maps base provider settings (without suffixes) for backward compatibility
 */
export function mapBaseProviderSettings(
  envSettings: Record<string, string>
): Record<string, string> {
  const envToFrontendKeyMap: Record<string, string> = {};

  // Map base API keys (without suffixes)
  Object.entries(PROVIDER_CONFIGS).forEach(([_providerName, config]) => {
    // API Key mapping
    if (envSettings[config.apiKeyEnvPattern]) {
      envToFrontendKeyMap[config.apiKeyFrontendPattern] =
        envSettings[config.apiKeyEnvPattern];
      // Also map to _DD suffix for backward compatibility
      envToFrontendKeyMap[`${config.apiKeyFrontendPattern}_DD`] =
        envSettings[config.apiKeyEnvPattern];
    }

    // Model preference mapping
    if (
      config.modelPrefEnvPattern &&
      config.modelPrefFrontendPattern &&
      envSettings[config.modelPrefEnvPattern]
    ) {
      envToFrontendKeyMap[config.modelPrefFrontendPattern] =
        envSettings[config.modelPrefEnvPattern];
      // Also map to _DD suffix for backward compatibility
      envToFrontendKeyMap[`${config.modelPrefFrontendPattern}_DD`] =
        envSettings[config.modelPrefEnvPattern];
    }
  });

  // Map base model-only providers
  MODEL_ONLY_PROVIDERS.forEach(({ envPattern, frontendPattern }) => {
    if (envSettings[envPattern]) {
      envToFrontendKeyMap[frontendPattern] = envSettings[envPattern];
      envToFrontendKeyMap[`${frontendPattern}_DD`] = envSettings[envPattern];
    }
  });

  return envToFrontendKeyMap;
}

/**
 * Complete mapping function that combines all provider mappings
 */
export function mapEnvToFrontendKeys(
  envSettings: Record<string, string>
): Record<string, string> {
  const suffixedMappings = mapSuffixedProviderSettings(envSettings);
  const baseMappings = mapBaseProviderSettings(envSettings);

  // Suffixed mappings should take precedence over base mappings
  return { ...baseMappings, ...suffixedMappings };
}

/**
 * Gets all environment keys that should be included in the system settings
 */
export function getSystemEnvironmentKeys(): string[] {
  return [
    // Core LLM settings
    "LLM_PROVIDER",
    "LLM_PROVIDER_DD",
    "LLM_PROVIDER_DD_2",
    "LLM_PROVIDER_VA",
    "LLM_PROVIDER_CDB",
    "LLM_PROVIDER_CUAI",
    "LLM_PROVIDER_CUAI2",
    "LLM_PROVIDER_CUAI3",
    "LLM_PROVIDER_CUAI4",
    "LLM_PROVIDER_CUAI5",
    "LLM_PROVIDER_CUAI6",
    "LLM_PROVIDER_PU",
    "LLM_PROVIDER_TM",
    "LLM_PROVIDER_SUPPORT",

    // OpenAI settings - generate all suffixed versions
    ...generateSuffixedKeys("OPEN_AI_KEY"),
    ...generateSuffixedKeys("OPEN_AI_MODEL_PREF"),

    // Anthropic settings
    ...generateSuffixedKeys("ANTHROPIC_API_KEY"),
    ...generateSuffixedKeys("ANTHROPIC_MODEL_PREF"),

    // Gemini settings
    ...generateSuffixedKeys("GEMINI_API_KEY"),
    ...generateSuffixedKeys("GEMINI_LLM_MODEL_PREF"),
    ...generateSuffixedKeys("GEMINI_SAFETY_SETTING"),

    // Ollama settings
    ...generateSuffixedKeys("OLLAMA_BASE_PATH"),
    ...generateSuffixedKeys("OLLAMA_MODEL_PREF"),
    ...generateSuffixedKeys("OLLAMA_MODEL_TOKEN_LIMIT"),
    ...generateSuffixedKeys("OLLAMA_PERFORMANCE_MODE"),
    ...generateSuffixedKeys("OLLAMA_KEEP_ALIVE_TIMEOUT"),

    // LMStudio settings
    ...generateSuffixedKeys("LMSTUDIO_BASE_PATH"),
    ...generateSuffixedKeys("LMSTUDIO_MODEL_PREF"),
    ...generateSuffixedKeys("LMSTUDIO_MODEL_TOKEN_LIMIT"),

    // LocalAI settings
    ...generateSuffixedKeys("LOCAL_AI_BASE_PATH"),
    ...generateSuffixedKeys("LOCAL_AI_MODEL_PREF"),
    ...generateSuffixedKeys("LOCAL_AI_MODEL_TOKEN_LIMIT"),
    ...generateSuffixedKeys("LOCAL_AI_API_KEY"),

    // TogetherAI settings
    ...generateSuffixedKeys("TOGETHER_AI_API_KEY"),
    ...generateSuffixedKeys("TOGETHER_AI_MODEL_PREF"),

    // Fireworks AI settings
    ...generateSuffixedKeys("FIREWORKS_AI_LLM_API_KEY"),
    ...generateSuffixedKeys("FIREWORKS_AI_LLM_MODEL_PREF"),

    // Perplexity settings
    ...generateSuffixedKeys("PERPLEXITY_API_KEY"),
    ...generateSuffixedKeys("PERPLEXITY_MODEL_PREF"),

    // Mistral settings
    ...generateSuffixedKeys("MISTRAL_API_KEY"),
    ...generateSuffixedKeys("MISTRAL_MODEL_PREF"),

    // Groq settings
    ...generateSuffixedKeys("GROQ_API_KEY"),
    ...generateSuffixedKeys("GROQ_MODEL_PREF"),

    // OpenRouter settings
    ...generateSuffixedKeys("OPENROUTER_API_KEY"),
    ...generateSuffixedKeys("OPENROUTER_MODEL_PREF"),

    // Cohere settings
    ...generateSuffixedKeys("COHERE_API_KEY"),
    ...generateSuffixedKeys("COHERE_MODEL_PREF"),

    // DeepSeek settings
    ...generateSuffixedKeys("DEEPSEEK_API_KEY"),
    ...generateSuffixedKeys("DEEPSEEK_MODEL_PREF"),

    // xAI settings
    ...generateSuffixedKeys("XAI_LLM_API_KEY"),
    ...generateSuffixedKeys("XAI_LLM_MODEL_PREF"),

    // Native LLM settings
    ...generateSuffixedKeys("NATIVE_LLM_MODEL_PREF"),

    // Embedding settings
    ...generateSuffixedKeys("EMBEDDING_ENGINE"),
    ...generateSuffixedKeys("EMBEDDING_MODEL_PREF"),
    "EMBEDDING_BASE_PATH",

    // Vector database settings
    "VECTOR_DB",
    "CHROMA_SERVER_HOST",
    "CHROMA_SERVER_PORT",
    "CHROMA_SERVER_SSL",
    "CHROMA_SERVER_HEADERS",
    "CHROMA_ENDPOINT",
    "CHROMA_API_HEADER",
    "CHROMA_API_KEY",
    "WEAVIATE_ENDPOINT",
    "WEAVIATE_API_KEY",
    "QDRANT_ENDPOINT",
    "QDRANT_API_KEY",
    "PINECONE_API_KEY",
    "PINECONE_INDEX",
    "MILVUS_ADDRESS",
    "MILVUS_USERNAME",
    "MILVUS_PASSWORD",
    "ZILLIZ_ENDPOINT",
    "ZILLIZ_API_TOKEN",
    "ASTRA_DB_APPLICATION_TOKEN",
    "ASTRA_DB_ENDPOINT",

    // Azure OpenAI settings
    "AZURE_OPENAI_ENDPOINT",
    "AZURE_OPENAI_KEY",
    "AZURE_OPENAI_MODEL_PREF",
    "AZURE_OPENAI_EMBEDDING_MODEL_PREF",
    "AZURE_OPENAI_TOKEN_LIMIT",

    // Generic OpenAI settings
    "GENERIC_OPEN_AI_BASE_PATH",
    "GENERIC_OPEN_AI_KEY",
    "GENERIC_OPEN_AI_MODEL_PREF",
    "GENERIC_OPEN_AI_MAX_TOKENS",

    // AWS Bedrock settings
    "AWS_BEDROCK_LLM_ACCESS_KEY_ID",
    "AWS_BEDROCK_LLM_ACCESS_KEY",
    "AWS_BEDROCK_LLM_REGION",
    "AWS_BEDROCK_LLM_MODEL_PREFERENCE",
    "AWS_BEDROCK_LLM_MODEL_TOKEN_LIMIT",

    // VoyageAI settings
    "VOYAGEAI_API_KEY",

    // Jina settings
    "JINA_API_KEY",
    "JINA_DIMENSIONS",
    "JINA_TASK",
    "JINA_LATE_CHUNKING",
    "JINA_EMBEDDING_TYPE",

    // TTS settings
    "TTS_PROVIDER",
    "TTS_OPEN_AI_KEY",
    "TTS_OPEN_AI_VOICE_MODEL",
    "TTS_ELEVEN_LABS_KEY",
    "TTS_ELEVEN_LABS_VOICE_MODEL",
    "TTS_PIPER_VOICE_MODEL",

    // Whisper settings
    "WHISPER_PROVIDER",
    "WHISPER_MODEL_PREF",

    // Agent settings
    "AGENT_GSE_CTX",
    "AGENT_GSE_KEY",
    "AGENT_SEARCHAPI_API_KEY",
    "AGENT_SEARCHAPI_ENGINE",
    "AGENT_SERPER_DEV_KEY",
    "AGENT_BING_SEARCH_API_KEY",
    "AGENT_SERPLY_API_KEY",
    "AGENT_SEARXNG_API_URL",
    "AGENT_TAVILY_API_KEY",

    // Rexor settings
    "REXOR_API_BASE_URL",
    "REXOR_AUTH_URL",
    "REXOR_CLIENT_ID_DEV",
    "REXOR_CLIENT_ID_PROD",
    "REXOR_API_HOST",

    // Other important settings
    "AUTH_TOKEN",
    "JWT_SECRET",
    "DISABLE_TELEMETRY",
    "MULTI_USER_MODE",
    "TELEMETRY_ID",
  ];
}

/**
 * Helper function to generate suffixed versions of environment keys
 */
function generateSuffixedKeys(baseKey: string): string[] {
  return [
    baseKey, // Base key without suffix
    ...Object.values(PROVIDER_SUFFIXES).map((suffix) => `${baseKey}${suffix}`),
  ];
}
