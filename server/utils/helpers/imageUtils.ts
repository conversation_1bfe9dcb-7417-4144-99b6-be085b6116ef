import * as fs from "fs";

/**
 * Safely checks if a file exists without throwing errors
 * @param filePath - Path to check
 * @returns true if file exists, false otherwise
 */
function safeFileExists(filePath: string): boolean {
  try {
    return fs.existsSync(filePath);
  } catch (_error) {
    // If we can't check the file (permissions, invalid path, etc.),
    // assume it doesn't exist to be safe
    return false;
  }
}

/**
 * Filters out problematic image references that can cause DOCX conversion to fail.
 * Handles cases where images reference non-existent files or invalid paths.
 *
 * This function:
 * - Keeps valid HTTP/HTTPS URLs
 * - Keeps valid data: URLs
 * - Keeps valid absolute paths
 * - Keeps valid relative paths that exist on the filesystem
 * - Filters out generic placeholder images (image.png, image.jpg, etc.)
 * - Filters out relative paths that don't exist on the filesystem
 *
 * @param text - The markdown content to filter
 * @returns Filtered markdown content with image references processed
 */
export function filterImageReferences(text: string): string {
  if (!text) return "";

  // Pattern to match markdown image syntax: ![alt text](image_url "optional title")
  // This handles basic markdown image syntax but could be extended for reference-style images
  const imageRegex = /!\[([^\]]*)\]\(([^)]+?)(?:\s+"([^"]*)")?\)/g;

  try {
    return text.replace(imageRegex, (match, altText, imagePath, title) => {
      try {
        // Check if the image path looks like a generic placeholder
        const isGenericImage = /^image\.(png|jpg|jpeg|gif|webp|svg)$/i.test(
          imagePath.trim()
        );

        // Check if it's a relative path that doesn't exist
        const isRelativeNonExistentPath =
          !imagePath.startsWith("http") &&
          !imagePath.startsWith("data:") &&
          !imagePath.startsWith("/") &&
          !safeFileExists(imagePath);

        if (isGenericImage || isRelativeNonExistentPath) {
          // Replace problematic image references with a text placeholder
          const placeholderText = altText || title || "Image";
          return `[${placeholderText}]`;
        }

        // Keep valid image references as-is
        return match;
      } catch (_error) {
        // If there's an error processing this specific image reference,
        // replace it with a placeholder to prevent breaking the entire conversion
        const placeholderText = altText || title || "Image";
        return `[${placeholderText}]`;
      }
    });
  } catch (error) {
    // If there's a fundamental error with the regex or text processing,
    // return the original text to avoid breaking the conversion entirely
    console.warn("Error filtering image references:", error);
    return text;
  }
}
