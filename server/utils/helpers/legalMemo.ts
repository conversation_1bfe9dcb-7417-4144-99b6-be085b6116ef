import DocumentDrafting, {
  CompressOptions,
  ChatMessage as DDChatMessage,
} from "../DocumentDrafting";
import { getLLMProvider } from "../helpers";
import {
  ChatMessage as AIChatMessage,
  Attachment,
} from "../../types/ai-providers";

// Type definitions for legal memo generation
export interface Workspace {
  id: number;
  slug: string;
  name: string;
  type?: string;
  metadata?: Record<string, unknown>;
  settings?: Record<string, unknown>;
  createdAt?: Date;
  updatedAt?: Date;
}

// Use the ChatMessage type from DocumentDrafting module
type ChatMessage = DDChatMessage;

interface ChatCompletionOptions {
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stop?: string[];
  stream?: boolean;
  model?: string;
}

export interface LLMConnector {
  model?: string;
  promptWindowLimit: () => number;
  getChatCompletion: (
    messages: ChatMessage[],
    options?: ChatCompletionOptions
  ) => Promise<{
    content: string;
    usage?: {
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
    };
  }>;
  compressMessages: (options: CompressOptions) => Promise<ChatMessage[]>;
  constructor: {
    name: string;
  };
  metrics?: {
    lastCompletionTokens?: number;
    totalTokensUsed?: number;
    requestCount?: number;
    averageResponseTime?: number;
  };
  settings?: Record<string, unknown>;
  apiKey?: string;
  baseUrl?: string;
}

export interface GenerateLegalMemoParams {
  workspace: Workspace;
  systemPrompt: string;
  userPrompt: string;
  LLMConnector?: LLMConnector | null;
  temperature?: number;
  tokenLimit?: number | null;
  settings?: Record<string, unknown> | null;
}

export interface GenerateLegalMemoResult {
  memo: string;
  tokenCount: number;
  sources: Array<{
    id: string;
    title: string;
    content: string;
    score?: number;
    metadata?: Record<string, unknown>;
  }>;
  tokenUsage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface GenerateMemosForWorkspacesParams {
  workspaces?: Workspace[];
  systemPrompt: string;
  userPrompt: string;
  LLMConnector?: LLMConnector | null;
  temperature?: number;
  tokenLimit?: number | null;
}

export interface WorkspaceMemoResult {
  workspaceSlug: string;
  memo?: string;
  tokenCount?: number;
  sources?: Array<{
    id: string;
    title: string;
    content: string;
    score?: number;
    metadata?: Record<string, unknown>;
  }>;
  error?: Error;
}

export interface DocumentDraftingSettings {
  skipContext?: boolean;
  maxContextLength?: number;
  temperature?: number;
  tokenLimit?: number;
  includeMetadata?: boolean;
  processingMode?: "fast" | "thorough";
}

/**
 * Generate a legal memo for a given workspace.
 * @param params - Parameters for memo generation
 * @returns Promise resolving to memo result
 */
export async function generateLegalMemo({
  workspace,
  systemPrompt,
  userPrompt,
  LLMConnector = null,
  temperature = 0.7,
  tokenLimit = null,
  settings = null,
}: GenerateLegalMemoParams): Promise<GenerateLegalMemoResult> {
  if (!workspace || !workspace.slug) {
    throw new Error("A valid workspace object with slug is required.");
  }

  if (!systemPrompt || !userPrompt) {
    throw new Error("Both systemPrompt and userPrompt are required.");
  }

  // Additional workspace validation
  if (typeof workspace.slug !== "string" || workspace.slug.length === 0) {
    throw new Error(`Invalid workspace slug: ${workspace.slug}`);
  }

  try {
    if (!LLMConnector) {
      const llmProvider = getLLMProvider();
      // Adapt LLMProvider to LLMConnector interface
      LLMConnector = {
        model: llmProvider.model,
        promptWindowLimit: llmProvider.promptWindowLimit.bind(llmProvider),
        getChatCompletion: async (messages, options) => {
          const response = await llmProvider.getChatCompletion(
            messages,
            options || {}
          );
          return {
            content: response?.textResponse || "",
            usage: response?.metrics,
          };
        },
        compressMessages: llmProvider.compressMessages
          ? async (options: CompressOptions): Promise<ChatMessage[]> => {
              // Adapt CompressOptions to the format expected by the LLM provider
              const promptArgs = {
                systemPrompt: options.systemPrompt,
                userPrompt: options.userPrompt,
                contextTexts: options.contextTexts || [],
                chatHistory: options.chatHistory || [],
                attachments: (options.attachments || []) as Attachment[],
              };
              const compressedMessages = await llmProvider.compressMessages(
                promptArgs,
                []
              );
              // Convert AIChatMessage[] to DDChatMessage[]
              return compressedMessages.map((msg: AIChatMessage) => ({
                role: msg.role,
                content:
                  typeof msg.content === "string"
                    ? msg.content
                    : JSON.stringify(msg.content),
              }));
            }
          : async (options: CompressOptions): Promise<ChatMessage[]> => {
              // Fallback implementation if compressMessages is not available
              console.warn(
                `[LEGAL MEMO HELPER] LLM provider ${llmProvider.constructor.name} does not have compressMessages method, using fallback`
              );
              // Construct messages manually from the options
              const messages: ChatMessage[] = [];
              if (options.systemPrompt) {
                messages.push({
                  role: "system",
                  content: options.systemPrompt,
                });
              }
              if (options.userPrompt) {
                messages.push({
                  role: "user",
                  content: options.userPrompt,
                });
              }
              return messages;
            },
        constructor: llmProvider.constructor,
        metrics: {},
      };
    }

    console.log(
      `[LEGAL MEMO HELPER] Creating DocumentDrafting instance for workspace: ${workspace.slug}`
    );
    console.log(`[LEGAL MEMO HELPER] Workspace properties:`, {
      slug: workspace.slug,
      id: workspace.id,
      name: workspace.name,
      type: workspace.type,
      hasOtherProps: Object.keys(workspace).length,
    });

    // Safer logging for LLM Connector properties
    try {
      const connectorProps = {
        hasConnector: !!LLMConnector,
        connectorType: "unknown",
        hasModel: false,
        model: "unknown",
        hasPromptWindowLimit: false,
        promptWindowLimit: "N/A" as string | number,
      };

      if (LLMConnector) {
        connectorProps.connectorType =
          LLMConnector?.constructor?.name || "unnamed";
        connectorProps.hasModel = !!LLMConnector.model;
        connectorProps.model = LLMConnector.model || "not set";
        connectorProps.hasPromptWindowLimit =
          typeof LLMConnector.promptWindowLimit === "function";
        if (connectorProps.hasPromptWindowLimit) {
          try {
            connectorProps.promptWindowLimit =
              LLMConnector.promptWindowLimit!();
          } catch (e: unknown) {
            const error = e as Error;
            connectorProps.promptWindowLimit = `Error calling: ${error.message}`;
          }
        }
      }
      console.log(
        `[LEGAL MEMO HELPER] LLM Connector properties:`,
        connectorProps
      );
    } catch (logError: unknown) {
      const error = logError as Error;
      console.error(
        `[LEGAL MEMO HELPER] CRITICAL: Failed to log LLM Connector properties. This may indicate an issue with the LLMConnector object.`,
        {
          error: error.message,
          stack: error.stack,
        }
      );
    }

    let drafting: DocumentDrafting;
    try {
      console.log(
        `[LEGAL MEMO HELPER] About to call DocumentDrafting constructor...`
      );
      drafting = new DocumentDrafting(workspace as any, LLMConnector! as any);
    } catch (e: unknown) {
      const error = e as Error;
      console.error(
        `[LEGAL MEMO HELPER] CRITICAL: Failed to create DocumentDrafting instance. This may indicate an issue with the LLMConnector object.`,
        {
          error: error.message,
          stack: error.stack,
        }
      );
      throw error;
    }

    if (settings && typeof settings === "object") {
      (
        drafting as unknown as { _settings: DocumentDraftingSettings }
      )._settings = {
        ...((drafting as unknown as { _settings?: DocumentDraftingSettings })
          ._settings || {}),
        ...settings,
      } as DocumentDraftingSettings;
      console.log(
        `[LEGAL MEMO HELPER] Applied settings:`,
        (drafting as unknown as { _settings: DocumentDraftingSettings })
          ._settings
      );
    } else {
      // Add default settings to skip context loading if no settings provided
      // This can help avoid vector search or PDR issues
      (
        drafting as unknown as { _settings: DocumentDraftingSettings }
      )._settings = {
        skipContext: true,
      } satisfies DocumentDraftingSettings;
      console.log(
        `[LEGAL MEMO HELPER] Applied default settings (skipContext=true):`,
        (drafting as unknown as { _settings: DocumentDraftingSettings })
          ._settings
      );
    }

    console.log(`[LEGAL MEMO HELPER] Calling generateMemoForWorkspace with:`, {
      legalIssuesLength: (userPrompt?.length ?? false) || 0,
      systemPromptLength: (systemPrompt?.length ?? false) || 0,
      temperature,
      tokenLimit,
    });

    const {
      memo,
      tokenCount: draftTokenCount,
      sources,
      tokenUsage,
    } = await drafting.generateMemoForWorkspace({
      legalIssues: userPrompt,
      memoPrompt: systemPrompt,
      temperature,
      tokenLimit: tokenLimit ?? undefined,
    });

    console.log(
      `[LEGAL MEMO HELPER] Successfully generated memo for workspace: ${workspace.slug}, length: ${(memo?.length ?? false) || 0}`
    );

    // Ensure we always have a reliable token count
    let finalTokenCount = draftTokenCount;
    if (typeof finalTokenCount !== "number" || Number.isNaN(finalTokenCount)) {
      try {
        const { TokenManager } = await import("./tiktoken");
        const tokenManager = new TokenManager();
        finalTokenCount = tokenManager.countFromString(memo);
      } catch {
        // Fallback in case TokenManager import fails or memo is invalid
        finalTokenCount = 0;
      }
    }

    return {
      memo,
      tokenCount: finalTokenCount,
      sources: sources as any,
      tokenUsage: tokenUsage as any,
    };
  } catch (error: unknown) {
    // Add context to the original error to preserve stack trace and error type
    const err = error as Error & { workspace?: string; userPrompt?: string };
    const originalMessage = err.message;
    const errorContext = {
      workspace: workspace.slug,
      workspaceId: workspace.id,
      workspaceType: workspace.type,
      userPromptLength: (userPrompt?.length ?? false) || 0,
      systemPromptLength: (systemPrompt?.length ?? false) || 0,
      LLMProviderType: LLMConnector?.constructor?.name || "unknown",
      originalError: originalMessage,
      errorStack: err.stack,
    };

    err.message = `Failed to generate legal memo for workspace "${workspace.slug}": ${originalMessage}`;
    err.workspace = workspace.slug;
    err.userPrompt =
      userPrompt?.substring(0, 100) +
      ((userPrompt?.length ?? 0) > 100 ? "..." : "");

    // Log the error with comprehensive context
    console.error(
      `[LEGAL MEMO HELPER] ERROR - Detailed context:`,
      errorContext
    );

    throw err;
  }
}

/**
 * Convenience helper that loops over an array of workspaces.
 * Returns an array of `{ workspaceSlug, memo, tokenCount, sources, error }` objects.
 * @param params - Parameters for generating memos for multiple workspaces
 * @returns Array of workspace memo results
 */
export async function generateMemosForWorkspaces({
  workspaces = [],
  systemPrompt,
  userPrompt,
  LLMConnector = null,
  temperature = 0.7,
  tokenLimit = null,
}: GenerateMemosForWorkspacesParams): Promise<WorkspaceMemoResult[]> {
  const results: WorkspaceMemoResult[] = [];

  for (const ws of workspaces) {
    try {
      const { memo, tokenCount, sources } = await generateLegalMemo({
        workspace: ws,
        systemPrompt,
        userPrompt,
        LLMConnector,
        temperature,
        tokenLimit,
      });
      results.push({ workspaceSlug: ws.slug, memo, tokenCount, sources });
    } catch (err: unknown) {
      results.push({
        workspaceSlug: ws?.slug ?? "unknown",
        error: err as Error,
      });
    }
  }
  return results;
}

// Types are already exported above, no need to re-export
