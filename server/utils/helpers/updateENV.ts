import { t } from "../i18n";

// Check if a key should be excluded based on sensitive patterns
function isSensitiveKey(key: string): boolean {
  const sensitivePatterns = [
    /^.*_API_KEY$/,
    /^.*_SECRET$/,
    /^.*_TOKEN$/,
    /^.*_PASSWORD$/,
    /^DATABASE_URL$/,
    /^JWT_SECRET$/,
    /^AUTH_TOKEN$/,
  ];

  return sensitivePatterns.some((pattern) => pattern.test(key));
}

// Check if a value appears to be a test/development value
function isTestValue(key: string, value: string): boolean {
  if (!value) return false;

  const testValuePatterns = [
    // Test database URLs
    /^file:\.\/.*\.db$/,
    /^sqlite:.*test.*\.db$/,
    /.*:memory:.*$/,

    // Test/mock API keys
    /^sk-test-/,
    /^test_/,
    /^mock_/,
    /^fake_/,
    /^dummy_/,

    // Development/test tokens
    /^dev_/,
    /^development_/,
    /^test_token_/,
    /^localhost/,
    /^127\.0\.0\.1/,
    /^http:\/\/localhost/,
  ];

  // Special case for specific keys with known test patterns
  if (key === "DATABASE_URL" && value.includes("test")) {
    return true;
  }

  return testValuePatterns.some((pattern) => pattern.test(value));
}

// Type definitions
interface ValidationFunction {
  (input?: string, force?: boolean): string | null | Promise<string | null>;
}

interface SettingConfig {
  envKey: string;
  checks: ValidationFunction[];
  postUpdate?: Array<
    (
      key: string,
      prevValue: string | undefined,
      nextValue: string
    ) => Promise<void>
  >;
}

interface KeyMapping {
  [key: string]: SettingConfig;
}

interface NewEnvValues {
  [key: string]: string;
}

interface UpdateENVResult {
  newValues: NewEnvValues;
  error: string | false;
}

interface EnvData {
  newValues?: NewEnvValues;
  [key: string]: string | number | boolean | NewEnvValues | undefined;
}

// Define all possible suffixes
export const PROVIDER_SUFFIXES = {
  DEFAULT: "",
  DOCUMENT_DRAFTING: "_DD",
  DOCUMENT_DRAFTING_2: "_DD_2",
  VALIDATE_ANSWER: "_VA",
  COMPLEX_DOCUMENT_BUILDER: "_CDB",
  PROMPT_UPGRADE: "_PU",
  TEMPLATE_MODAL: "_TM",
  SUPPORT: "_SUPPORT",
  CUSTOM_USER_AI_1: "_CUAI",
  CUSTOM_USER_AI_2: "_CUAI2",
  CUSTOM_USER_AI_3: "_CUAI3",
  CUSTOM_USER_AI_4: "_CUAI4",
  CUSTOM_USER_AI_5: "_CUAI5",
  CUSTOM_USER_AI_6: "_CUAI6",
};

// Helper function to create base settings with checks
const createBaseSetting = (
  envKey: string,
  checks: ValidationFunction[] = [],
  additionalProps: Partial<SettingConfig> = {}
): SettingConfig => ({
  envKey,
  checks,
  ...additionalProps,
});

// Helper function to generate suffixed key mappings
const generateSuffixedMappings = (
  baseKey: string,
  baseEnvKey: string,
  baseChecks: ValidationFunction[] = [],
  customMappings: KeyMapping = {}
): KeyMapping => {
  const mappings: KeyMapping = {};

  Object.values(PROVIDER_SUFFIXES).forEach((suffix) => {
    const key = baseKey + suffix;
    const envKey = baseEnvKey + suffix;

    // Allow custom overrides from customMappings
    mappings[key] = {
      ...createBaseSetting(envKey, baseChecks),
      ...(customMappings[key] || {}),
    };
  });

  return mappings;
};

// Forward declare validation functions (defined below)

// --------------------- //
// Validation Functions  //
// --------------------- //

async function isNotEmpty(input: string = ""): Promise<string | null> {
  return !input || input.length === 0
    ? await t("errors.env.empty-value")
    : null;
}

// Create the KEY_MAPPING using the generator
export const KEY_MAPPING: KeyMapping = {
  ...generateSuffixedMappings("LLMProvider", "LLM_PROVIDER", [
    isNotEmpty,
    supportedLLM,
  ]),

  ...generateSuffixedMappings("OpenAiKey", "OPEN_AI_KEY", [
    isNotEmpty,
    validOpenAIKey,
  ]),
  ...generateSuffixedMappings("OpenAiModelPref", "OPEN_AI_MODEL_PREF", [
    isNotEmpty,
  ]),

  ...generateSuffixedMappings("AzureOpenAiEndpoint", "AZURE_OPENAI_ENDPOINT", [
    isNotEmpty,
  ]),
  ...generateSuffixedMappings("AzureOpenAiKey", "AZURE_OPENAI_KEY", [
    isNotEmpty,
  ]),
  ...generateSuffixedMappings(
    "AzureOpenAiTokenLimit",
    "AZURE_OPENAI_TOKEN_LIMIT",
    [validOpenAiTokenLimit]
  ),
  ...generateSuffixedMappings("AzureOpenAiModelPref", "OPEN_AI_MODEL_PREF", [
    isNotEmpty,
  ]),
  ...generateSuffixedMappings(
    "AzureOpenAiEmbeddingModelPref",
    "EMBEDDING_MODEL_PREF",
    [isNotEmpty]
  ),

  ...generateSuffixedMappings("AnthropicApiKey", "ANTHROPIC_API_KEY", [
    isNotEmpty,
    validAnthropicApiKey,
  ]),
  ...generateSuffixedMappings("AnthropicModelPref", "ANTHROPIC_MODEL_PREF", [
    isNotEmpty,
    validAnthropicModel,
  ]),

  ...generateSuffixedMappings("GeminiApiKey", "GEMINI_API_KEY", [isNotEmpty]),
  ...generateSuffixedMappings("GeminiLLMModelPref", "GEMINI_LLM_MODEL_PREF", [
    isNotEmpty,
  ]),
  ...generateSuffixedMappings("GeminiSafetySetting", "GEMINI_SAFETY_SETTING", [
    validGeminiSafetySetting,
  ]),

  ...generateSuffixedMappings("LMStudioBasePath", "LMSTUDIO_BASE_PATH", [
    isNotEmpty,
    validLLMExternalBasePath,
    validDockerizedUrl,
  ]),
  ...generateSuffixedMappings("LMStudioModelPref", "LMSTUDIO_MODEL_PREF", []),
  ...generateSuffixedMappings(
    "LMStudioTokenLimit",
    "LMSTUDIO_MODEL_TOKEN_LIMIT",
    [nonZero]
  ),

  ...generateSuffixedMappings("LocalAiBasePath", "LOCAL_AI_BASE_PATH", [
    isNotEmpty,
    validLLMExternalBasePath,
    validDockerizedUrl,
  ]),
  ...generateSuffixedMappings("LocalAiModelPref", "LOCAL_AI_MODEL_PREF", []),
  ...generateSuffixedMappings(
    "LocalAiTokenLimit",
    "LOCAL_AI_MODEL_TOKEN_LIMIT",
    [nonZero]
  ),
  ...generateSuffixedMappings("LocalAiApiKey", "LOCAL_AI_API_KEY", []),

  ...generateSuffixedMappings("OllamaLLMBasePath", "OLLAMA_BASE_PATH", [
    isNotEmpty,
    validOllamaLLMBasePath,
    validDockerizedUrl,
  ]),
  ...generateSuffixedMappings("OllamaLLMModelPref", "OLLAMA_MODEL_PREF", []),
  ...generateSuffixedMappings(
    "OllamaLLMTokenLimit",
    "OLLAMA_MODEL_TOKEN_LIMIT",
    [nonZero]
  ),
  ...generateSuffixedMappings(
    "OllamaLLMPerformanceMode",
    "OLLAMA_PERFORMANCE_MODE",
    []
  ),
  ...generateSuffixedMappings(
    "OllamaLLMKeepAliveSeconds",
    "OLLAMA_KEEP_ALIVE_TIMEOUT",
    [isInteger]
  ),

  ...generateSuffixedMappings("MistralApiKey", "MISTRAL_API_KEY", [isNotEmpty]),
  ...generateSuffixedMappings("MistralModelPref", "MISTRAL_MODEL_PREF", [
    isNotEmpty,
  ]),

  ...generateSuffixedMappings("NativeLLMModelPref", "NATIVE_LLM_MODEL_PREF", [
    isDownloadedModel,
  ]),

  ...generateSuffixedMappings("TogetherAiApiKey", "TOGETHER_AI_API_KEY", [
    isNotEmpty,
  ]),
  ...generateSuffixedMappings("TogetherAiModelPref", "TOGETHER_AI_MODEL_PREF", [
    isNotEmpty,
  ]),

  ...generateSuffixedMappings(
    "FireworksAiLLMApiKey",
    "FIREWORKS_AI_LLM_API_KEY",
    [isNotEmpty]
  ),
  ...generateSuffixedMappings(
    "FireworksAiLLMModelPref",
    "FIREWORKS_AI_LLM_MODEL_PREF",
    [isNotEmpty]
  ),

  ...generateSuffixedMappings("PerplexityApiKey", "PERPLEXITY_API_KEY", [
    isNotEmpty,
  ]),
  ...generateSuffixedMappings("PerplexityModelPref", "PERPLEXITY_MODEL_PREF", [
    isNotEmpty,
  ]),

  ...generateSuffixedMappings("OpenRouterApiKey", "OPENROUTER_API_KEY", [
    isNotEmpty,
  ]),
  ...generateSuffixedMappings("OpenRouterModelPref", "OPENROUTER_MODEL_PREF", [
    isNotEmpty,
  ]),

  ...generateSuffixedMappings("GroqApiKey", "GROQ_API_KEY", [isNotEmpty]),
  ...generateSuffixedMappings("GroqModelPref", "GROQ_MODEL_PREF", [isNotEmpty]),

  ...generateSuffixedMappings("CohereApiKey", "COHERE_API_KEY", [isNotEmpty]),
  ...generateSuffixedMappings("CohereModelPref", "COHERE_MODEL_PREF", [
    isNotEmpty,
  ]),

  ...generateSuffixedMappings("VoyageAiApiKey", "VOYAGEAI_API_KEY", [
    isNotEmpty,
  ]),

  ...generateSuffixedMappings("JinaApiKey", "JINA_API_KEY", [isNotEmpty]),
  ...generateSuffixedMappings("JinaDimensions", "JINA_DIMENSIONS", [
    isNotEmpty,
  ]),
  ...generateSuffixedMappings("JinaTask", "JINA_TASK", [isNotEmpty]),
  ...generateSuffixedMappings("JinaLateChunking", "JINA_LATE_CHUNKING", [
    isNotEmpty,
  ]),
  ...generateSuffixedMappings("JinaEmbeddingType", "JINA_EMBEDDING_TYPE", [
    isNotEmpty,
  ]),

  ...generateSuffixedMappings("DeepSeekApiKey", "DEEPSEEK_API_KEY", [
    isNotEmpty,
  ]),
  ...generateSuffixedMappings("DeepSeekModelPref", "DEEPSEEK_MODEL_PREF", [
    isNotEmpty,
  ]),

  ...generateSuffixedMappings("XAIApiKey", "XAI_LLM_API_KEY", [isNotEmpty]),
  ...generateSuffixedMappings("XAIModelPref", "XAI_LLM_MODEL_PREF", [
    isNotEmpty,
  ]),

  ...generateSuffixedMappings(
    "AwsBedrockLLMAccessKeyId",
    "AWS_BEDROCK_LLM_ACCESS_KEY_ID",
    [isNotEmpty]
  ),
  ...generateSuffixedMappings(
    "AwsBedrockLLMAccessKey",
    "AWS_BEDROCK_LLM_ACCESS_KEY",
    [isNotEmpty]
  ),
  ...generateSuffixedMappings("AwsBedrockLLMRegion", "AWS_BEDROCK_LLM_REGION", [
    isNotEmpty,
  ]),
  ...generateSuffixedMappings(
    "AwsBedrockLLMModel",
    "AWS_BEDROCK_LLM_MODEL_PREFERENCE",
    [isNotEmpty]
  ),
  ...generateSuffixedMappings(
    "AwsBedrockLLMTokenLimit",
    "AWS_BEDROCK_LLM_MODEL_TOKEN_LIMIT",
    [nonZero]
  ),

  ...generateSuffixedMappings(
    "GenericOpenAiBasePath",
    "GENERIC_OPEN_AI_BASE_PATH",
    [isValidURL]
  ),
  ...generateSuffixedMappings(
    "GenericOpenAiModelPref",
    "GENERIC_OPEN_AI_MODEL_PREF",
    [isNotEmpty]
  ),
  ...generateSuffixedMappings(
    "GenericOpenAiTokenLimit",
    "GENERIC_OPEN_AI_MODEL_TOKEN_LIMIT",
    [nonZero]
  ),
  ...generateSuffixedMappings(
    "GenericOpenAiKey",
    "GENERIC_OPEN_AI_API_KEY",
    []
  ),
  ...generateSuffixedMappings(
    "GenericOpenAiMaxTokens",
    "GENERIC_OPEN_AI_MAX_TOKENS",
    [nonZero]
  ),

  ...generateSuffixedMappings(
    "ContextualEmbedding",
    "CONTEXTUAL_EMBEDDING",
    []
  ),
  ...generateSuffixedMappings(
    "ContextualSystemPrompt",
    "CONTEXTUAL_SYSTEM_PROMPT",
    [isNotEmpty]
  ),
  ...generateSuffixedMappings(
    "ContextualUserPrompt",
    "CONTEXTUAL_USER_PROMPT",
    [isNotEmpty, validContextualUserPrompt]
  ),

  ...generateSuffixedMappings("ChromaEndpoint", "CHROMA_ENDPOINT", [
    isValidURL,
    validChromaURL,
    validDockerizedUrl,
  ]),
  ...generateSuffixedMappings("ChromaApiHeader", "CHROMA_API_HEADER", []),
  ...generateSuffixedMappings("ChromaApiKey", "CHROMA_API_KEY", []),

  ...generateSuffixedMappings("WeaviateEndpoint", "WEAVIATE_ENDPOINT", [
    isValidURL,
    validDockerizedUrl,
  ]),
  ...generateSuffixedMappings("WeaviateApiKey", "WEAVIATE_API_KEY", []),

  ...generateSuffixedMappings("QdrantEndpoint", "QDRANT_ENDPOINT", [
    isValidURL,
    validDockerizedUrl,
  ]),
  ...generateSuffixedMappings("QdrantApiKey", "QDRANT_API_KEY", []),
  ...generateSuffixedMappings("PineConeKey", "PINECONE_API_KEY", []),
  ...generateSuffixedMappings("PineConeIndex", "PINECONE_INDEX", []),

  ...generateSuffixedMappings("MilvusAddress", "MILVUS_ADDRESS", [
    isValidURL,
    validDockerizedUrl,
  ]),
  ...generateSuffixedMappings("MilvusUsername", "MILVUS_USERNAME", [
    isNotEmpty,
  ]),
  ...generateSuffixedMappings("MilvusPassword", "MILVUS_PASSWORD", [
    isNotEmpty,
  ]),

  ...generateSuffixedMappings("ZillizEndpoint", "ZILLIZ_ENDPOINT", [
    isValidURL,
  ]),
  ...generateSuffixedMappings("ZillizApiToken", "ZILLIZ_API_TOKEN", [
    isNotEmpty,
  ]),

  ...generateSuffixedMappings(
    "AstraDBApplicationToken",
    "ASTRA_DB_APPLICATION_TOKEN",
    [isNotEmpty]
  ),
  ...generateSuffixedMappings("AstraDBEndpoint", "ASTRA_DB_ENDPOINT", [
    isNotEmpty,
  ]),
  // Special cases and non-LLM settings
  BinaryLLM_DD: {
    envKey: "BINARY_LLM_DD",
    checks: [isNotEmpty],
  },
  BinaryLLMUserLevel_DD: {
    envKey: "BINARY_LLM_USER_LEVEL_DD",
    checks: [isNotEmpty],
  },

  // Vector Database Settings
  VectorDB: {
    envKey: "VECTOR_DB",
    checks: [isNotEmpty, supportedVectorDB],
  },

  // Embedding Settings
  EmbeddingEngine: {
    envKey: "EMBEDDING_ENGINE",
    checks: [supportedEmbeddingModel],
  },
  EmbeddingBasePath: {
    envKey: "EMBEDDING_BASE_PATH",
    checks: [isNotEmpty, validDockerizedUrl],
  },
  EmbeddingModelPref: {
    envKey: "EMBEDDING_MODEL_PREF",
    checks: [isNotEmpty],
  },
  EmbeddingModelMaxChunkLength: {
    envKey: "EMBEDDING_MODEL_MAX_CHUNK_LENGTH",
    checks: [nonZero],
  },

  // Gemini Embedding Settings
  GeminiEmbeddingApiKey: {
    envKey: "GEMINI_EMBEDDING_API_KEY",
    checks: [isNotEmpty],
  },

  // System Settings
  AuthToken: {
    envKey: "AUTH_TOKEN",
    checks: [requiresForceMode, noRestrictedChars],
  },
  JWTSecret: {
    envKey: "JWT_SECRET",
    checks: [requiresForceMode],
  },
  DisableTelemetry: {
    envKey: "DISABLE_TELEMETRY",
    checks: [],
  },

  // Whisper Settings
  WhisperProvider: {
    envKey: "WHISPER_PROVIDER",
    checks: [isNotEmpty, supportedTranscriptionProvider],
    postUpdate: [],
  },
  WhisperModelPref: {
    envKey: "WHISPER_MODEL_PREF",
    checks: [validLocalWhisper],
    postUpdate: [],
  },

  // Text-to-Speech Settings
  TextToSpeechProvider: {
    envKey: "TTS_PROVIDER",
    checks: [supportedTTSProvider],
  },
  TTSOpenAIKey: {
    envKey: "TTS_OPEN_AI_KEY",
    checks: [validOpenAIKey],
  },

  // Slack Integration Settings
  SlackWebhookUrl: {
    envKey: "SLACK_WEBHOOK_URL",
    checks: [isNotEmpty, validSlackWebhookUrl],
  },
  SlackSystemReportsEnabled: {
    envKey: "SLACK_SYSTEM_REPORTS_ENABLED",
    checks: [],
  },
  TTSOpenAIVoiceModel: {
    envKey: "TTS_OPEN_AI_VOICE_MODEL",
    checks: [],
  },
  TTSElevenLabsKey: {
    envKey: "TTS_ELEVEN_LABS_KEY",
    checks: [isNotEmpty],
  },
  TTSElevenLabsVoiceModel: {
    envKey: "TTS_ELEVEN_LABS_VOICE_MODEL",
    checks: [],
  },
  TTSPiperTTSVoiceModel: {
    envKey: "TTS_PIPER_VOICE_MODEL",
    checks: [],
  },
};

// --------------------- //
// Other Validation Functions  //
// --------------------- //

async function nonZero(input: string = ""): Promise<string | null> {
  if (isNaN(Number(input))) return await t("errors.env.must-be-number");
  return Number(input) <= 0 ? await t("errors.env.non-zero-required") : null;
}

async function isInteger(input: string = ""): Promise<string | null> {
  if (isNaN(Number(input))) return await t("errors.env.must-be-number");
  return null;
}

async function isValidURL(input: string = ""): Promise<string | null> {
  try {
    new URL(input);
    return null;
  } catch {
    return await t("errors.env.invalid-url");
  }
}

async function validOpenAIKey(input: string = ""): Promise<string | null> {
  return input.startsWith("sk-")
    ? null
    : await t("errors.env.openai-key-format");
}

function validSlackWebhookUrl(input: string = ""): string | null {
  const slackWebhookPattern =
    /^https:\/\/hooks\.slack\.com\/services\/[A-Z0-9]+\/[A-Z0-9]+\/[A-Za-z0-9]+$/;
  return slackWebhookPattern.test(input)
    ? null
    : `${input} is not a valid Slack webhook URL. It should be in the format: https://hooks.slack.com/services/...`;
}

async function validContextualUserPrompt(
  input: string = ""
): Promise<string | null> {
  if (!input.includes("{file}") || !input.includes("{chunk}")) {
    return await t("errors.env.contextual-prompt-format");
  }
  return null;
}

async function validAnthropicApiKey(
  input: string = ""
): Promise<string | null> {
  return input.startsWith("sk-ant-")
    ? null
    : await t("errors.env.anthropic-key-format");
}

/* async function _validJinaApiKey(_input: string = ""): Promise<string | null> {
  return _input.startsWith("jina_") ? null : await t("jina.api-key-error");
} */

async function validLLMExternalBasePath(
  input: string = ""
): Promise<string | null> {
  try {
    new URL(input);
    if (!input.includes("v1"))
      return await t("errors.env.external-llm-url-format");
    if (input.split("").slice(-1)?.[0] === "/")
      return await t("errors.env.url-no-trailing-slash");
    return null;
  } catch {
    return await t("errors.env.invalid-url");
  }
}

async function validOllamaLLMBasePath(
  input: string = ""
): Promise<string | null> {
  try {
    new URL(input);
    if (input.split("").slice(-1)?.[0] === "/")
      return await t("errors.env.url-no-trailing-slash");
    return null;
  } catch {
    return await t("errors.env.invalid-url");
  }
}

async function supportedTTSProvider(
  input: string = ""
): Promise<string | null> {
  const validSelection = [
    "native",
    "openai",
    "elevenlabs",
    "piper_local",
  ].includes(input);
  return validSelection
    ? null
    : await t("errors.env.invalid-tts-provider", { input });
}

async function validLocalWhisper(input: string = ""): Promise<string | null> {
  const validSelection = [
    "Xenova/whisper-small",
    "Xenova/whisper-large",
    "openai/whisper-large-v3-turbo",
  ].includes(input);
  return validSelection
    ? null
    : await t("errors.env.invalid-whisper-model", { input });
}

async function supportedLLM(input: string = ""): Promise<string | null> {
  const validSelection = [
    "openai",
    "azure",
    "anthropic",
    "gemini",
    "lmstudio",
    "localai",
    "ollama",
    "native",
    "togetherai",
    "fireworksai",
    "mistral",
    "huggingface",
    "perplexity",
    "openrouter",
    "groq",
    "koboldcpp",
    "textgenwebui",
    "cohere",
    "litellm",
    "generic-openai",
    "bedrock",
    "deepseek",
    "xai",
    "system-standard",
  ].includes(input);
  return validSelection
    ? null
    : await t("errors.env.invalid-llm-provider", { input });
}

function supportedTranscriptionProvider(input: string = ""): string | null {
  const validSelection = ["openai", "local"].includes(input);
  return validSelection ? null : `Invalid transcription provider: ${input}`;
}

function validGeminiSafetySetting(input: string = ""): string | null {
  const validModes = [
    "BLOCK_NONE",
    "BLOCK_ONLY_HIGH",
    "BLOCK_MEDIUM_AND_ABOVE",
    "BLOCK_LOW_AND_ABOVE",
  ];
  return validModes.includes(input)
    ? null
    : `Invalid Gemini Safety Setting. Must be one of: ${validModes.join(", ")}`;
}

function validAnthropicModel(input: string = ""): string | null {
  const validModels = [
    // Claude 4 Models (Latest Generation)
    "claude-opus-4-20250514",
    "claude-sonnet-4-20250514",
    "claude-opus-4-0",
    "claude-sonnet-4-0",
    // Claude 3.x Models
    "claude-3-haiku-20240307",
    "claude-3-opus-latest",
    "claude-3-sonnet-20240229",
    "claude-3-5-haiku-latest",
    "claude-3-5-sonnet-20240620",
    "claude-3-5-sonnet-20241022",
    "claude-3-7-sonnet-20250219",
    "claude-3-7-sonnet-latest",
  ];
  return validModels.includes(input)
    ? null
    : `Invalid Anthropic model. Must be one of: ${validModels.join(", ")}`;
}

function supportedEmbeddingModel(input: string = ""): string | null {
  const supported = [
    "openai",
    "azure",
    "gemini",
    "localai",
    "native",
    "ollama",
    "lmstudio",
    "cohere",
    "voyageai",
    "litellm",
    "generic-openai",
    "jina",
  ];
  return supported.includes(input)
    ? null
    : `Invalid embedding engine: ${input}. Must be one of: ${supported.join(", ")}`;
}

function supportedVectorDB(input: string = ""): string | null {
  const supported = [
    "chroma",
    "pinecone",
    "lancedb",
    "weaviate",
    "qdrant",
    "milvus",
    "zilliz",
    "astra",
  ];
  return supported.includes(input)
    ? null
    : `Invalid VectorDB type. Must be one of: ${supported.join(", ")}`;
}

function validChromaURL(input: string = ""): string | null {
  return input.slice(-1) === "/"
    ? `Chroma Instance URL should not end with a slash`
    : null;
}

function validOpenAiTokenLimit(input: string = ""): string | null {
  const tokenLimit = Number(input);
  if (isNaN(tokenLimit)) return "Token limit is not a number";
  if (![4096, 8192, 16384, 32768, 128000].includes(tokenLimit)) {
    return "Invalid OpenAI token limit. Allowed: 4096, 8192, 16384, 32768, 128000";
  }
  return null;
}

function requiresForceMode(
  input?: string,
  forceModeEnabled: boolean = false
): string | null {
  return forceModeEnabled === true
    ? null
    : "Cannot set this setting unless force mode is enabled.";
}

function isDownloadedModel(input: string = ""): string | null {
  const fs = require("fs");
  const path = require("path");
  const storageDir = path.resolve(
    process.env.STORAGE_DIR
      ? path.resolve(process.env.STORAGE_DIR, "models", "downloaded")
      : path.resolve(__dirname, `../../storage/models/downloaded`)
  );
  if (!fs.existsSync(storageDir))
    return "Downloaded models directory does not exist";

  const files = fs
    .readdirSync(storageDir)
    .filter((file: string) => file.includes(".gguf"));
  return files.includes(input)
    ? null
    : "Model file not found in downloaded models";
}

async function validDockerizedUrl(input: string = ""): Promise<string | null> {
  // Check Docker runtime env var
  if (process.env.IST_LEGAL_RUNTIME !== "docker") return null;

  try {
    const { isPortInUse, getLocalHosts } =
      require("./portAvailabilityChecker") as {
        isPortInUse: (port: number, hostname: string) => Promise<boolean>;
        getLocalHosts: () => string[];
      };
    const localInterfaces: string[] = getLocalHosts();
    const url = new URL(input);
    const hostname = url.hostname.toLowerCase();
    const port = parseInt(url.port, 10);

    // If not a loopback, skip this check.
    if (!localInterfaces.includes(hostname)) return null;
    if (isNaN(port)) return "Invalid URL: port is not specified or invalid.";

    const isPortAvailableFromDocker: boolean = await isPortInUse(
      port,
      hostname
    );
    if (isPortAvailableFromDocker) {
      return (
        "Port is not running a reachable service on loopback address from inside the container. " +
        "Please use host.docker.internal (or ********** on Linux), a real machine IP, or a domain."
      );
    }
  } catch (error) {
    console.error((error as Error).message);
    return "An error occurred while validating the URL.";
  }

  return null;
}

/* function _validHuggingFaceEndpoint(_input: string = ""): string | null {
  return _input.endsWith(".cloud")
    ? null
    : `Your Hugging Face Inference Endpoint should end with ".cloud"`;
} */

function noRestrictedChars(input: string = ""): string | null {
  const regExp = /^[a-zA-Z0-9_\-!@$%^&*();]+$/;
  return !regExp.test(input)
    ? "Your password/token has restricted characters. Allowed: a-z, A-Z, 0-9, and _-!@$%^&*();"
    : null;
}

// -------------- //
// Update Helpers //
// -------------- //

export async function updateENV(
  newENVs: EnvData = {},
  force: boolean = false,
  userId: number | null = null
): Promise<UpdateENVResult> {
  let error = "";
  const validKeys = Object.keys(KEY_MAPPING);

  console.log(
    "updateENV called with:",
    JSON.stringify(newENVs).substring(0, 200)
  );

  // Handle both direct object and { newValues: {...} } structure
  // This fixes compatibility with the frontend
  const envData: NewEnvValues = newENVs.newValues || (newENVs as NewEnvValues);

  // Make sure envData is an object
  if (!envData || typeof envData !== "object") {
    console.error("Invalid environment data format:", envData);
    return {
      newValues: {},
      error: "Invalid environment data format. Expected an object.",
    };
  }

  // Safety check - log the structure for debugging
  console.log(
    `Received ${Object.keys(envData).length} environment variables to update`
  );

  // Create a reverse mapping from env keys to KEY_MAPPING keys
  const envKeyToMappingKey: Record<string, string> = {};
  for (const [mappingKey, config] of Object.entries(KEY_MAPPING)) {
    envKeyToMappingKey[config.envKey] = mappingKey;
  }

  // Transform frontend keys (like LLM_PROVIDER) to KEY_MAPPING keys (like LLMProvider)
  const transformedData: NewEnvValues = {};
  for (const [key, value] of Object.entries(envData)) {
    // Skip masked values
    if (typeof value === "string" && value.includes("******")) {
      continue;
    }

    // Check if this is a direct env key that needs transformation
    if (envKeyToMappingKey[key]) {
      transformedData[envKeyToMappingKey[key]] = value;
    } else if (validKeys.includes(key)) {
      // Already in correct format
      transformedData[key] = value;
    } else if (/^[A-Z_]+$/.test(key)) {
      // Custom environment variable
      transformedData[key] = value;
    }
  }

  // Split environment variables into two categories:
  // 1. Keys defined in KEY_MAPPING (validated keys)
  // 2. Custom keys that aren't in KEY_MAPPING

  // Process keys defined in KEY_MAPPING (with validation)
  const validatedENVKeys = Object.keys(transformedData).filter((key) =>
    validKeys.includes(key)
  );

  // Collect custom environment variables (basic validation only)
  const customENVKeys = Object.keys(transformedData).filter(
    (key) => !validKeys.includes(key) && /^[A-Z_]+$/.test(key) // Only accept uppercase letters and underscores
  );

  const newValues: NewEnvValues = {};

  // Process validated keys (with full validation)
  for (const key of validatedENVKeys) {
    const { envKey, checks, postUpdate = [] } = KEY_MAPPING[key];
    const prevValue = process.env[envKey];
    const nextValue = String(transformedData[key]);

    const errors = await executeValidationChecks(checks, nextValue, force);
    if (errors && errors.length > 0) {
      error += errors.join("\n");
      break;
    }

    newValues[key] = nextValue;
    process.env[envKey] = String(nextValue);

    for (const postUpdateFunc of postUpdate) {
      await postUpdateFunc(key, prevValue, String(nextValue));
    }
  }

  // Process custom env variables (basic validation only)
  for (const key of customENVKeys) {
    // Use the key as is for process.env (these are direct environment variables)
    const nextValue = String(transformedData[key]);

    // Basic sanity check - no complex validation needed since these are custom
    if (nextValue && typeof nextValue === "string") {
      newValues[key] = nextValue;
      process.env[key] = String(nextValue);
      console.log(
        `\x1b[36m[updateENV]\x1b[0m Setting custom environment variable: ${key}`
      );
    }
  }

  if (error.length === 0) {
    // Persist changes to .env only if there were no validation errors
    const dumpResult = dumpENV();
    if (!dumpResult) {
      error = "Failed to update the .env file.";
    }
  }

  await logChangesToEventLog(newValues, userId);
  return { newValues, error: error && error.length > 0 ? error : false };
}

async function executeValidationChecks(
  checks: ValidationFunction[],
  value: string,
  force: boolean
): Promise<string[]> {
  const results = await Promise.all(
    checks.map((validator) => validator(value, force))
  );
  return results.filter((err): err is string => typeof err === "string");
}

async function logChangesToEventLog(
  newValues: NewEnvValues = {},
  userId: number | null = null
): Promise<void> {
  const { EventLogs } = require("../../models/eventLogs");
  const eventMapping = {
    LLMProvider: "update_llm_provider",
    LLMProvider_DD: "update_llm_provider",
    EmbeddingEngine: "update_embedding_engine",
    VectorDB: "update_vector_db",
    LLMProvider_PU: "update_pu_llm_provider",
    OpenAiKey_PU: "update_pu_openai_key",
    OpenAiModelPref_PU: "update_pu_openai_model",
    AnthropicApiKey_PU: "update_pu_anthropic_key",
    AnthropicModelPref_PU: "update_pu_anthropic_model",
    GeminiLLMApiKey_PU: "update_pu_gemini_key",
    GeminiLLMModelPref_PU: "update_pu_gemini_model",
  };

  for (const [key, eventName] of Object.entries(eventMapping)) {
    if (!Object.prototype.hasOwnProperty.call(newValues, key)) continue;
    await EventLogs.logEvent(eventName, {}, userId);
  }
  return;
}

export function dumpENV(): boolean {
  const fs = require("fs");
  const path = require("path");

  const frozenEnvs: Record<string, string> = {};
  const protectedKeys = [
    // All known environment keys from KEY_MAPPING:
    ...Object.values(KEY_MAPPING).map((val) => val.envKey),
    // Manually add keys we still want to preserve even if not in KEY_MAPPING
    "STORAGE_DIR",
    "SERVER_PORT",
    // Persistent data encryption
    "SIG_KEY",
    "SIG_SALT",
    // Password schema or security settings
    "PASSWORDMINCHAR",
    "PASSWORDMAXCHAR",
    "PASSWORDLOWERCASE",
    "PASSWORDUPPERCASE",
    "PASSWORDNUMERIC",
    "PASSWORDSYMBOL",
    "PASSWORDREQUIREMENTS",
    // HTTPS setup
    "ENABLE_HTTPS",
    "HTTPS_CERT_PATH",
    "HTTPS_KEY_PATH",
    // OCR Language Support
    "TARGET_OCR_LANG",
  ];

  // Avoid newline or quote escaping vulnerabilities
  function sanitizeValue(value: string | undefined): string {
    if (!value) return "";
    const stringValue = typeof value === "string" ? value : String(value);
    const offendingChars =
      /[\n\r\t\v\f\u0085\u00a0\u1680\u180e\u2000-\u200a\u2028\u2029\u202f\u205f\u3000"'`#]/;
    const firstOffendingCharIndex = stringValue.search(offendingChars);
    if (firstOffendingCharIndex === -1) return stringValue;
    return stringValue.substring(0, firstOffendingCharIndex);
  }

  // Define test-specific environment variables to exclude from production .env
  const testExcludedKeys = [
    "NODE_ENV", // When set to 'test' by Jest
    "TS_JEST", // TypeScript Jest flag
    "JEST_WORKER_ID", // Jest worker process ID
    "DATABASE_URL", // When set by Jest to use test database
    "OPENAI_API_KEY", // When set by Jest to test API key
    "CLAUDE_CODE_ENTRYPOINT", // Claude Code CLI flag
    "CLAUDECODE", // Claude Code environment flag
    "OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE", // OpenTelemetry test config
    "COREPACK_ENABLE_AUTO_PIN", // Node.js package manager config
    "GIT_EDITOR", // Git editor override for testing
    "SQLITE_EXEMPT_PATH_FROM_VNODE_GUARDS", // SQLite test configuration
    // Common CI/CD environment variables
    "CI", // Continuous Integration flag
    "GITHUB_ACTIONS", // GitHub Actions environment
    "JENKINS_URL", // Jenkins CI environment
    "TRAVIS", // Travis CI environment
    "CIRCLECI", // CircleCI environment
    "GITLAB_CI", // GitLab CI environment
    "BUILDKITE", // Buildkite CI environment
    "DRONE", // Drone CI environment
  ];

  // Capture all existing environment variables from the current .env file
  try {
    const currentEnvPath = path.join(__dirname, "../../.env");
    if (fs.existsSync(currentEnvPath)) {
      const currentEnvContent = fs.readFileSync(currentEnvPath, {
        encoding: "utf8",
      });
      const currentLines = currentEnvContent.split("\n");

      for (const line of currentLines) {
        const match = line.match(/^([A-Z_]+)=['"]?(.*?)['"]?$/);
        if (match && match[1] && !line.startsWith("#")) {
          const key = match[1];
          // Only add to frozenEnvs if it's not already in process.env
          // This ensures new values take precedence over existing ones
          // Also exclude test-specific variables, sensitive keys, and test values
          if (
            !frozenEnvs[key] &&
            !process.env[key] &&
            !testExcludedKeys.includes(key) &&
            !isSensitiveKey(key) &&
            !isTestValue(key, match[2])
          ) {
            frozenEnvs[key] = match[2];
          }
        }
      }
    }
  } catch (error) {
    console.error(
      `\x1b[31m[updateENV]\x1b[0m Error reading current .env file:`,
      (error as Error).message
    );
  }

  // Add all current environment variables that we want to preserve
  for (const key of protectedKeys) {
    const envValue = process.env[key] || null;
    if (!envValue) continue;
    frozenEnvs[key] = sanitizeValue(envValue);
  }

  // Add all other environment variables from process.env that aren't specifically protected
  // but might have been custom-added, excluding test-specific variables, sensitive keys, and test values
  for (const key in process.env) {
    const value = process.env[key] || "";
    if (
      key.match(/^[A-Z][A-Z0-9_]*$/) && // Stricter pattern: start with letter, then letters/numbers/underscores
      !frozenEnvs[key] &&
      !key.startsWith("npm_") &&
      !testExcludedKeys.includes(key) &&
      !isSensitiveKey(key) &&
      !isTestValue(key, value)
    ) {
      frozenEnvs[key] = sanitizeValue(value);
    }
  }

  let envResult = `# Auto-dump ENV from system call on ${new Date().toISOString()}\n`;
  envResult += Object.entries(frozenEnvs)
    .map(([key, value]) => `${key}='${value}'`)
    .join("\n");

  // Update server .env file
  try {
    const envPath = path.join(__dirname, "../../.env");
    fs.writeFileSync(envPath, envResult, { encoding: "utf8", flag: "w" });
    console.log(
      `\x1b[36m[updateENV]\x1b[0m Updated server .env file successfully`
    );
  } catch {
    console.error("Failed to write environment file");
    return false;
  }

  // Also update collector's .env file for specific variables like JINA_API_KEY
  // that are needed by both services
  try {
    // Support both Docker runtime and local development paths
    const collectorEnvPath =
      process.env.IST_LEGAL_RUNTIME === "docker"
        ? "/app/collector/.env"
        : path.join(__dirname, "../../../collector/.env");
    if (fs.existsSync(collectorEnvPath)) {
      // Read existing collector env content
      const collectorEnvContent = fs.readFileSync(collectorEnvPath, {
        encoding: "utf8",
      });
      const lines = collectorEnvContent.split("\n");
      const updatedLines = [];

      // List of keys that should be synced to collector
      // Only sync the API_KEY as required
      const keysToSync = ["JINA_API_KEY"];

      // Track which keys we've updated
      const updatedKeys = new Set();

      // Update existing lines
      for (const line of lines) {
        const match = line.match(/^([A-Z_]+)=(.*)/);
        if (match) {
          const key = match[1];
          if (keysToSync.includes(key) && frozenEnvs[key]) {
            updatedLines.push(`${key}='${frozenEnvs[key]}'`);
            updatedKeys.add(key);
          } else {
            updatedLines.push(line);
          }
        } else {
          updatedLines.push(line);
        }
      }

      // Add any keys that weren't in the file already
      for (const key of keysToSync) {
        if (!updatedKeys.has(key) && frozenEnvs[key]) {
          updatedLines.push(`${key}='${frozenEnvs[key]}'`);
        }
      }

      // Write back to collector .env
      fs.writeFileSync(collectorEnvPath, updatedLines.join("\n"), {
        encoding: "utf8",
      });
      console.log(
        `\x1b[36m[updateENV]\x1b[0m Updated collector .env file with ${keysToSync.filter((key) => frozenEnvs[key]).join(", ")} values`
      );
    } else {
      console.warn(
        `\x1b[33m[updateENV]\x1b[0m Collector .env file not found at ${collectorEnvPath}`
      );
    }
  } catch (error) {
    console.error(
      `\x1b[31m[updateENV]\x1b[0m Error updating collector .env file:`,
      (error as Error).message
    );
    // Don't return false here to prevent server env updates from failing
    // when collector env updates fail
  }

  return true;
}
