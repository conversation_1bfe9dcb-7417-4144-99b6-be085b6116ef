/**
 * Validation utilities for user inputs to prevent injection attacks
 */

/**
 * Validates if a string is a valid CSS color for the server-side.
 * This is a replicate of the frontend's `isValidCssColor` but without
 * the browser-specific `document.createElement` check.
 */
export function isValidCssColor(color: string): boolean {
  if (!color || typeof color !== "string") {
    return false;
  }

  const trimmedColor = color.trim();

  // Check for minimum length
  if (trimmedColor.length < 3) {
    return false;
  }

  // Check for dangerous patterns that could be used for injection
  const dangerousPatterns = [
    /javascript:/i,
    /expression\s*\(/i,
    /url\s*\(/i,
    /@import/i,
    /behavior\s*:/i,
    /binding\s*:/i,
    /<script/i,
    /<\/script/i,
    /&lt;script/i,
    /&lt;\/script/i,
  ];

  for (const pattern of dangerousPatterns) {
    if (pattern.test(trimmedColor)) {
      return false;
    }
  }

  // List of valid CSS color names
  const validColorNames = [
    "aliceblue",
    "antiquewhite",
    "aqua",
    "aquamarine",
    "azure",
    "beige",
    "bisque",
    "black",
    "blanchedalmond",
    "blue",
    "blueviolet",
    "brown",
    "burlywood",
    "cadetblue",
    "chartreuse",
    "chocolate",
    "coral",
    "cornflowerblue",
    "cornsilk",
    "crimson",
    "cyan",
    "darkblue",
    "darkcyan",
    "darkgoldenrod",
    "darkgray",
    "darkgrey",
    "darkgreen",
    "darkkhaki",
    "darkmagenta",
    "darkolivegreen",
    "darkorange",
    "darkorchid",
    "darkred",
    "darksalmon",
    "darkseagreen",
    "darkslateblue",
    "darkslategray",
    "darkslategrey",
    "darkturquoise",
    "darkviolet",
    "deeppink",
    "deepskyblue",
    "dimgray",
    "dimgrey",
    "dodgerblue",
    "firebrick",
    "floralwhite",
    "forestgreen",
    "fuchsia",
    "gainsboro",
    "ghostwhite",
    "gold",
    "goldenrod",
    "gray",
    "grey",
    "green",
    "greenyellow",
    "honeydew",
    "hotpink",
    "indianred",
    "indigo",
    "ivory",
    "khaki",
    "lavender",
    "lavenderblush",
    "lawngreen",
    "lemonchiffon",
    "lightblue",
    "lightcoral",
    "lightcyan",
    "lightgoldenrodyellow",
    "lightgray",
    "lightgrey",
    "lightgreen",
    "lightpink",
    "lightsalmon",
    "lightseagreen",
    "lightskyblue",
    "lightslategray",
    "lightslategrey",
    "lightsteelblue",
    "lightyellow",
    "lime",
    "limegreen",
    "linen",
    "magenta",
    "maroon",
    "mediumaquamarine",
    "mediumblue",
    "mediumorchid",
    "mediumpurple",
    "mediumseagreen",
    "mediumslateblue",
    "mediumspringgreen",
    "mediumturquoise",
    "mediumvioletred",
    "midnightblue",
    "mintcream",
    "mistyrose",
    "moccasin",
    "navajowhite",
    "navy",
    "oldlace",
    "olive",
    "olivedrab",
    "orange",
    "orangered",
    "orchid",
    "palegoldenrod",
    "palegreen",
    "paleturquoise",
    "palevioletred",
    "papayawhip",
    "peachpuff",
    "peru",
    "pink",
    "plum",
    "powderblue",
    "purple",
    "rebeccapurple",
    "red",
    "rosybrown",
    "royalblue",
    "saddlebrown",
    "salmon",
    "sandybrown",
    "seagreen",
    "seashell",
    "sienna",
    "silver",
    "skyblue",
    "slateblue",
    "slategray",
    "slategrey",
    "snow",
    "springgreen",
    "steelblue",
    "tan",
    "teal",
    "thistle",
    "tomato",
    "turquoise",
    "violet",
    "wheat",
    "white",
    "whitesmoke",
    "yellow",
    "yellowgreen",
    "transparent",
  ];

  // Check if it is a named color
  if (validColorNames.includes(trimmedColor.toLowerCase())) {
    return true;
  }

  // Check hex colors (#fff, #ffff, #ffffff, #ffffffff)
  const hexPattern =
    /^#([0-9A-Fa-f]{3}|[0-9A-Fa-f]{4}|[0-9A-Fa-f]{6}|[0-9A-Fa-f]{8})$/;
  if (hexPattern.test(trimmedColor)) {
    return true;
  }

  // Check RGB colors with value validation
  const rgbPattern = /^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/;
  const rgbMatch = trimmedColor.match(rgbPattern);
  if (rgbMatch) {
    const [, r, g, b] = rgbMatch;
    const rNum = parseInt(r, 10);
    const gNum = parseInt(g, 10);
    const bNum = parseInt(b, 10);
    return (
      rNum >= 0 &&
      rNum <= 255 &&
      gNum >= 0 &&
      gNum <= 255 &&
      bNum >= 0 &&
      bNum <= 255
    );
  }

  // Check RGBA colors with value validation
  const rgbaPattern =
    /^rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*([0-9]*\.?[0-9]+)\s*\)$/;
  const rgbaMatch = trimmedColor.match(rgbaPattern);
  if (rgbaMatch) {
    const [, r, g, b, a] = rgbaMatch;
    const rNum = parseInt(r, 10);
    const gNum = parseInt(g, 10);
    const bNum = parseInt(b, 10);
    const aNum = parseFloat(a);
    return (
      rNum >= 0 &&
      rNum <= 255 &&
      gNum >= 0 &&
      gNum <= 255 &&
      bNum >= 0 &&
      bNum <= 255 &&
      aNum >= 0 &&
      aNum <= 1
    );
  }

  // Check HSL colors with value validation
  const hslPattern =
    /^hsl\(\s*(\d{1,3})\s*,\s*(\d{1,3})%\s*,\s*(\d{1,3})%\s*\)$/;
  const hslMatch = trimmedColor.match(hslPattern);
  if (hslMatch) {
    const [, h, s, l] = hslMatch;
    const hNum = parseInt(h, 10);
    const sNum = parseInt(s, 10);
    const lNum = parseInt(l, 10);
    return (
      hNum >= 0 &&
      hNum <= 360 &&
      sNum >= 0 &&
      sNum <= 100 &&
      lNum >= 0 &&
      lNum <= 100
    );
  }

  // Check HSLA colors with value validation
  const hslaPattern =
    /^hsla\(\s*(\d{1,3})\s*,\s*(\d{1,3})%\s*,\s*(\d{1,3})%\s*,\s*([0-9]*\.?[0-9]+)\s*\)$/;
  const hslaMatch = trimmedColor.match(hslaPattern);
  if (hslaMatch) {
    const [, h, s, l, a] = hslaMatch;
    const hNum = parseInt(h, 10);
    const sNum = parseInt(s, 10);
    const lNum = parseInt(l, 10);
    const aNum = parseFloat(a);
    return (
      hNum >= 0 &&
      hNum <= 360 &&
      sNum >= 0 &&
      sNum <= 100 &&
      lNum >= 0 &&
      lNum <= 100 &&
      aNum >= 0 &&
      aNum <= 1
    );
  }

  return false;
}

/**
 * Sanitizes a CSS color by validating it and returning a safe version
 */
export function sanitizeCssColor(color: string): string | null {
  if (!isValidCssColor(color)) {
    return null;
  }

  return color.trim();
}
