/**
 * Validation utilities for user inputs to prevent injection attacks
 */

/**
 * Validates if a string is a valid CSS color for the server-side.
 * This is a replicate of the frontend's `isValidCssColor` but without
 * the browser-specific `document.createElement` check.
 */
export function isValidCssColor(color: string): boolean {
  if (!color || typeof color !== "string") {
    return false;
  }

  const trimmedColor = color.trim();

  // Check for minimum length
  if (trimmedColor.length < 3) {
    return false;
  }

  // Check for dangerous patterns that could be used for injection
  const dangerousPatterns = [
    /javascript:/i,
    /expression\s*\(/i,
    /url\s*\(/i,
    /@import/i,
    /behavior\s*:/i,
    /binding\s*:/i,
    /<script/i,
    /<\/script/i,
    /&lt;script/i,
    /&lt;\/script/i,
  ];

  for (const pattern of dangerousPatterns) {
    if (pattern.test(trimmedColor)) {
      return false;
    }
  }

  // List of valid CSS color names
  const validColorNames = [
    "aliceblue",
    "antiquewhite",
    "aqua",
    "aquamarine",
    "azure",
    "beige",
    "bisque",
    "black",
    "blanchedalmond",
    "blue",
    "blueviolet",
    "brown",
    "burlywood",
    "cadetblue",
    "chartreuse",
    "chocolate",
    "coral",
    "cornflowerblue",
    "cornsilk",
    "crimson",
    "cyan",
    "darkblue",
    "darkcyan",
    "darkgoldenrod",
    "darkgray",
    "darkgrey",
    "darkgreen",
    "darkkhaki",
    "darkmagenta",
    "darkolivegreen",
    "darkorange",
    "darkorchid",
    "darkred",
    "darksalmon",
    "darkseagreen",
    "darkslateblue",
    "darkslategray",
    "darkslategrey",
    "darkturquoise",
    "darkviolet",
    "deeppink",
    "deepskyblue",
    "dimgray",
    "dimgrey",
    "dodgerblue",
    "firebrick",
    "floralwhite",
    "forestgreen",
    "fuchsia",
    "gainsboro",
    "ghostwhite",
    "gold",
    "goldenrod",
    "gray",
    "grey",
    "green",
    "greenyellow",
    "honeydew",
    "hotpink",
    "indianred",
    "indigo",
    "ivory",
    "khaki",
    "lavender",
    "lavenderblush",
    "lawngreen",
    "lemonchiffon",
    "lightblue",
    "lightcoral",
    "lightcyan",
    "lightgoldenrodyellow",
    "lightgray",
    "lightgrey",
    "lightgreen",
    "lightpink",
    "lightsalmon",
    "lightseagreen",
    "lightskyblue",
    "lightslategray",
    "lightslategrey",
    "lightsteelblue",
    "lightyellow",
    "lime",
    "limegreen",
    "linen",
    "magenta",
    "maroon",
    "mediumaquamarine",
    "mediumblue",
    "mediumorchid",
    "mediumpurple",
    "mediumseagreen",
    "mediumslateblue",
    "mediumspringgreen",
    "mediumturquoise",
    "mediumvioletred",
    "midnightblue",
    "mintcream",
    "mistyrose",
    "moccasin",
    "navajowhite",
    "navy",
    "oldlace",
    "olive",
    "olivedrab",
    "orange",
    "orangered",
    "orchid",
    "palegoldenrod",
    "palegreen",
    "paleturquoise",
    "palevioletred",
    "papayawhip",
    "peachpuff",
    "peru",
    "pink",
    "plum",
    "powderblue",
    "purple",
    "rebeccapurple",
    "red",
    "rosybrown",
    "royalblue",
    "saddlebrown",
    "salmon",
    "sandybrown",
    "seagreen",
    "seashell",
    "sienna",
    "silver",
    "skyblue",
    "slateblue",
    "slategray",
    "slategrey",
    "snow",
    "springgreen",
    "steelblue",
    "tan",
    "teal",
    "thistle",
    "tomato",
    "turquoise",
    "violet",
    "wheat",
    "white",
    "whitesmoke",
    "yellow",
    "yellowgreen",
    "transparent",
  ];

  // Check if it is a named color
  if (validColorNames.includes(trimmedColor.toLowerCase())) {
    return true;
  }

  // Additional validation for common color formats
  const colorPatterns = [
    /^#([0-9A-Fa-f]{3,4}){1,2}$/, // Hex colors (#fff, #ffffff, #fffa, #ffffffff)
    /^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/, // RGB colors
    /^rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(0(\.\d+)?|1(\.0)?)\s*\)$/, // RGBA colors
    /^hsl\(\s*(\d{1,3})\s*,\s*(\d{1,3})%\s*,\s*(\d{1,3})%\s*\)$/, // HSL colors
    /^hsla\(\s*(\d{1,3})\s*,\s*(\d{1,3})%\s*,\s*(\d{1,3})%\s*,\s*(0(\.\d+)?|1(\.0)?)\s*\)$/, // HSLA colors
  ];

  // Check if it matches common color patterns
  return colorPatterns.some((pattern) => pattern.test(trimmedColor));
}

/**
 * Sanitizes a CSS color by validating it and returning a safe version
 */
export function sanitizeCssColor(color: string): string | null {
  if (!isValidCssColor(color)) {
    return null;
  }

  return color.trim();
}
