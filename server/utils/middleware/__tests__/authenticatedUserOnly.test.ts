import { Request, Response, NextFunction } from "express";
import { authenticatedUserOnly } from "../authenticatedUserOnly";

describe("authenticatedUserOnly middleware", () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;
  let mockStatus: jest.Mock;
  let mockJson: jest.Mock;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup mock request
    mockRequest = {
      headers: {},
      path: "/test-path",
      params: {},
      query: {},
      body: {},
    };

    // Create mock functions
    mockStatus = jest.fn().mockReturnThis();
    mockJson = jest.fn().mockReturnThis();

    // Setup mock response with locals
    mockResponse = {
      locals: {},
      status: mockStatus,
      json: mockJson,
    };

    // Setup mock next function
    mockNext = jest.fn();
  });

  describe("Authentication Success", () => {
    it("should allow access when user is authenticated with valid id", () => {
      // Arrange
      mockResponse.locals!.user = {
        id: 1,
        username: "testuser",
        role: "default",
      };

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockNext).toHaveBeenCalledTimes(1);
      expect(mockNext).toHaveBeenCalledWith();
      expect(mockStatus).not.toHaveBeenCalled();
      expect(mockJson).not.toHaveBeenCalled();
    });

    it("should allow access for admin user", () => {
      // Arrange
      mockResponse.locals!.user = {
        id: 42,
        username: "admin",
        role: "admin",
        email: "<EMAIL>",
      };

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockNext).toHaveBeenCalledTimes(1);
      expect(mockStatus).not.toHaveBeenCalled();
    });

    it("should allow access for manager user", () => {
      // Arrange
      mockResponse.locals!.user = {
        id: 123,
        username: "manager",
        role: "manager",
        organizationId: 1,
      };

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockNext).toHaveBeenCalledTimes(1);
      expect(mockStatus).not.toHaveBeenCalled();
    });

    it("should return 401 for user with numeric zero as id", () => {
      // Arrange
      mockResponse.locals!.user = {
        id: 0,
        username: "zerouser",
        role: "default",
      };

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockNext).not.toHaveBeenCalled();
      expect(mockStatus).toHaveBeenCalledWith(401);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: "Authentication required",
      });
    });

    it("should allow access for user with additional properties", () => {
      // Arrange
      mockResponse.locals!.user = {
        id: 999,
        username: "fulluser",
        role: "superuser",
        email: "<EMAIL>",
        organizationId: 5,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
        suspended: 0,
        customProperty: "custom",
      };

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockNext).toHaveBeenCalledTimes(1);
      expect(mockStatus).not.toHaveBeenCalled();
    });
  });

  describe("Authentication Failure", () => {
    it("should return 401 when user is not set in res.locals", () => {
      // Arrange
      // res.locals.user is undefined

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockNext).not.toHaveBeenCalled();
      expect(mockStatus).toHaveBeenCalledWith(401);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: "Authentication required",
      });
    });

    it("should return 401 when user is null", () => {
      // Arrange
      mockResponse.locals!.user = null;

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockNext).not.toHaveBeenCalled();
      expect(mockStatus).toHaveBeenCalledWith(401);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: "Authentication required",
      });
    });

    it("should return 401 when user object exists but has no id", () => {
      // Arrange
      mockResponse.locals!.user = {
        username: "noIdUser",
        role: "default",
      };

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockNext).not.toHaveBeenCalled();
      expect(mockStatus).toHaveBeenCalledWith(401);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: "Authentication required",
      });
    });

    it("should return 401 when user id is null", () => {
      // Arrange
      mockResponse.locals!.user = {
        id: null,
        username: "nullIdUser",
        role: "default",
      };

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockNext).not.toHaveBeenCalled();
      expect(mockStatus).toHaveBeenCalledWith(401);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: "Authentication required",
      });
    });

    it("should return 401 when user id is undefined", () => {
      // Arrange
      mockResponse.locals!.user = {
        id: undefined,
        username: "undefinedIdUser",
        role: "default",
      };

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockNext).not.toHaveBeenCalled();
      expect(mockStatus).toHaveBeenCalledWith(401);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: "Authentication required",
      });
    });
  });

  describe("Edge Cases", () => {
    it("should throw error when res.locals is undefined", () => {
      // Arrange
      mockResponse.locals = undefined;

      // Act & Assert
      expect(() => {
        authenticatedUserOnly(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );
      }).toThrow(TypeError);

      expect(mockNext).not.toHaveBeenCalled();
      expect(mockStatus).not.toHaveBeenCalled();
    });

    it("should return 401 when user is an empty object", () => {
      // Arrange
      mockResponse.locals!.user = {};

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockNext).not.toHaveBeenCalled();
      expect(mockStatus).toHaveBeenCalledWith(401);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: "Authentication required",
      });
    });

    it("should return 401 when user id is an empty string", () => {
      // Arrange
      mockResponse.locals!.user = {
        id: "",
        username: "emptyIdUser",
        role: "default",
      };

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockNext).not.toHaveBeenCalled();
      expect(mockStatus).toHaveBeenCalledWith(401);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: "Authentication required",
      });
    });

    it("should allow access when user id is a string number", () => {
      // Arrange
      mockResponse.locals!.user = {
        id: "123",
        username: "stringIdUser",
        role: "default",
      };

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockNext).toHaveBeenCalledTimes(1);
      expect(mockStatus).not.toHaveBeenCalled();
    });

    it("should allow access when user id is negative", () => {
      // Arrange
      mockResponse.locals!.user = {
        id: -1,
        username: "negativeIdUser",
        role: "default",
      };

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockNext).toHaveBeenCalledTimes(1);
      expect(mockStatus).not.toHaveBeenCalled();
    });
  });

  describe("Security Edge Cases", () => {
    it("should return 401 when user contains malicious properties", () => {
      // Arrange
      mockResponse.locals!.user = {
        id: null,
        username: "<script>alert('XSS')</script>",
        role: "admin'; DROP TABLE users; --",
        __proto__: { isAdmin: true },
      };

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockNext).not.toHaveBeenCalled();
      expect(mockStatus).toHaveBeenCalledWith(401);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: "Authentication required",
      });
    });

    it("should return 401 when user is a function", () => {
      // Arrange
      mockResponse.locals!.user = (() => ({ id: 1 })) as any;

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockNext).not.toHaveBeenCalled();
      expect(mockStatus).toHaveBeenCalledWith(401);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: "Authentication required",
      });
    });

    it("should return 401 when user is an array", () => {
      // Arrange
      mockResponse.locals!.user = [{ id: 1 }] as any;

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockNext).not.toHaveBeenCalled();
      expect(mockStatus).toHaveBeenCalledWith(401);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: "Authentication required",
      });
    });

    it("should return 401 when user id is NaN", () => {
      // Arrange
      mockResponse.locals!.user = {
        id: NaN,
        username: "nanIdUser",
        role: "default",
      };

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockNext).not.toHaveBeenCalled();
      expect(mockStatus).toHaveBeenCalledWith(401);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: "Authentication required",
      });
    });

    it("should allow access when user id is Infinity", () => {
      // Arrange
      mockResponse.locals!.user = {
        id: Infinity,
        username: "infinityIdUser",
        role: "default",
      };

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockNext).toHaveBeenCalledTimes(1);
      expect(mockStatus).not.toHaveBeenCalled();
    });
  });

  describe("Response Handling", () => {
    it("should not call next() after sending 401 response", () => {
      // Arrange
      mockResponse.locals!.user = null;

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockNext).not.toHaveBeenCalled();
      expect(mockStatus).toHaveBeenCalledTimes(1);
      expect(mockJson).toHaveBeenCalledTimes(1);
    });

    it("should set proper response headers for 401", () => {
      // Arrange
      mockResponse.locals!.user = null;

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      expect(mockStatus).toHaveBeenCalledWith(401);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: "Authentication required",
      });
    });

    it("should maintain response chain for error responses", () => {
      // Arrange
      mockResponse.locals!.user = null;

      // Act
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Assert
      // Verify that status returns 'this' for chaining
      const statusResult = mockStatus.mock.results[0].value;
      expect(statusResult).toBe(mockResponse);
    });
  });

  describe("Multiple Invocations", () => {
    it("should handle multiple sequential calls correctly", () => {
      // First call - authenticated
      mockResponse.locals!.user = { id: 1 };
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );
      expect(mockNext).toHaveBeenCalledTimes(1);

      // Reset mocks
      (mockNext as jest.Mock).mockClear();
      mockStatus.mockClear();
      mockJson.mockClear();

      // Second call - not authenticated
      mockResponse.locals!.user = null;
      authenticatedUserOnly(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );
      expect(mockNext).not.toHaveBeenCalled();
      expect(mockStatus).toHaveBeenCalledWith(401);
    });
  });

  describe("Type Safety", () => {
    it("should handle when _req parameter is used correctly", () => {
      // The middleware uses _req to indicate it's not used
      // This test ensures the middleware works regardless of request content
      const weirdRequest = {
        ...mockRequest,
        weirdProperty: "weird value",
        method: "WEIRD",
      } as any;

      mockResponse.locals!.user = { id: 1 };

      authenticatedUserOnly(weirdRequest, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledTimes(1);
    });
  });
});
