import { Request, Response, NextFunction } from "express";

// Unmock the multiUserProtected module to test the actual implementation
jest.unmock("../multiUserProtected");

import {
  strictMultiUserRoleValid,
  flexUserRoleValid,
  isMultiUserSetup,
  canManageSystemTemplates,
  canManageOrgTemplates,
  canManageUserTemplates,
  legalTemplateScopeGuard,
  ROLES,
} from "../multiUserProtected";
import SystemSettings from "../../../models/systemSettings";
import { userFromSession } from "../../http";
import { UserRole } from "../../../types/shared";

// Mock dependencies
jest.mock("../../../models/systemSettings");
jest.mock("../../http");

describe("multiUserProtected", () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup mock request
    mockRequest = {
      headers: {},
      path: "/test-path",
      params: {},
      query: {},
      body: {},
    };

    // Create fresh mock functions for each test
    const mockSendStatus = jest.fn().mockReturnThis();
    const mockStatus = jest.fn().mockReturnThis();
    const mockJson = jest.fn().mockReturnThis();

    // Setup mock response with locals
    mockResponse = {
      locals: {},
      sendStatus: mockSendStatus,
      status: mockStatus,
      json: mockJson,
    };

    // Setup mock next function
    mockNext = jest.fn();

    // Set default mock implementations
    (SystemSettings.isMultiUserMode as jest.Mock).mockResolvedValue(true);
    (userFromSession as jest.Mock).mockResolvedValue(null);
  });

  describe("strictMultiUserRoleValid", () => {
    it("should allow access when role is 'all'", async () => {
      const middleware = strictMultiUserRoleValid([ROLES.all]);

      await middleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalled();
      expect(mockResponse.sendStatus).not.toHaveBeenCalled();
    });

    it("should return 401 when multi-user mode is disabled", async () => {
      (SystemSettings.isMultiUserMode as jest.Mock).mockResolvedValue(false);
      mockResponse.locals!.multiUserMode = undefined;

      const middleware = strictMultiUserRoleValid([ROLES.admin]);

      await middleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.sendStatus).toHaveBeenCalledWith(401);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it("should return 401 when user is not authenticated", async () => {
      (SystemSettings.isMultiUserMode as jest.Mock).mockResolvedValue(true);
      (userFromSession as jest.Mock).mockResolvedValue(null);

      const middleware = strictMultiUserRoleValid([ROLES.admin]);

      await middleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.sendStatus).toHaveBeenCalledWith(401);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it("should return 403 when user lacks required role", async () => {
      (SystemSettings.isMultiUserMode as jest.Mock).mockResolvedValue(true);
      (userFromSession as jest.Mock).mockResolvedValue({
        id: 1,
        role: UserRole.DEFAULT,
      });

      const middleware = strictMultiUserRoleValid([ROLES.admin]);

      await middleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.sendStatus).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it("should allow access when user has required role", async () => {
      (SystemSettings.isMultiUserMode as jest.Mock).mockResolvedValue(true);
      (userFromSession as jest.Mock).mockResolvedValue({
        id: 1,
        role: UserRole.ADMIN,
      });

      const middleware = strictMultiUserRoleValid([ROLES.admin]);

      await middleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalled();
      expect(mockResponse.sendStatus).not.toHaveBeenCalled();
    });

    it("should use cached multiUserMode from response locals", async () => {
      mockResponse.locals!.multiUserMode = true;
      (userFromSession as jest.Mock).mockResolvedValue({
        id: 1,
        role: UserRole.ADMIN,
      });

      const middleware = strictMultiUserRoleValid([ROLES.admin]);

      await middleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(SystemSettings.isMultiUserMode).not.toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalled();
    });

    it("should use cached user from response locals", async () => {
      mockResponse.locals!.multiUserMode = true;
      mockResponse.locals!.user = { id: 1, role: UserRole.ADMIN };

      const middleware = strictMultiUserRoleValid([ROLES.admin]);

      await middleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(userFromSession).not.toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalled();
    });

    it("should allow access for multiple allowed roles", async () => {
      (SystemSettings.isMultiUserMode as jest.Mock).mockResolvedValue(true);
      (userFromSession as jest.Mock).mockResolvedValue({
        id: 1,
        role: UserRole.MANAGER,
      });

      const middleware = strictMultiUserRoleValid([ROLES.admin, ROLES.manager]);

      await middleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalled();
      expect(mockResponse.sendStatus).not.toHaveBeenCalled();
    });

    it("should use default roles when none provided", async () => {
      (SystemSettings.isMultiUserMode as jest.Mock).mockResolvedValue(true);
      (userFromSession as jest.Mock).mockResolvedValue({
        id: 1,
        role: UserRole.ADMIN,
      });

      const middleware = strictMultiUserRoleValid();

      await middleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe("flexUserRoleValid", () => {
    it("should allow access when role is 'all'", async () => {
      (SystemSettings.isMultiUserMode as jest.Mock).mockResolvedValue(false);

      const middleware = flexUserRoleValid([ROLES.all]);

      await middleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalled();
      expect(mockResponse.sendStatus).not.toHaveBeenCalled();
    });

    it("should bypass checks when multi-user mode is disabled", async () => {
      (SystemSettings.isMultiUserMode as jest.Mock).mockResolvedValue(false);

      const middleware = flexUserRoleValid([ROLES.admin]);

      await middleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalled();
      expect(userFromSession).not.toHaveBeenCalled();
    });

    it("should return 401 when user is not authenticated in multi-user mode", async () => {
      (SystemSettings.isMultiUserMode as jest.Mock).mockResolvedValue(true);
      (userFromSession as jest.Mock).mockResolvedValue(null);

      const middleware = flexUserRoleValid([ROLES.admin]);

      await middleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.sendStatus).toHaveBeenCalledWith(401);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it("should return 403 when user lacks required role in multi-user mode", async () => {
      (SystemSettings.isMultiUserMode as jest.Mock).mockResolvedValue(true);
      (userFromSession as jest.Mock).mockResolvedValue({
        id: 1,
        role: UserRole.DEFAULT,
      });

      const middleware = flexUserRoleValid([ROLES.admin]);

      await middleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.sendStatus).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it("should allow access when user has required role in multi-user mode", async () => {
      (SystemSettings.isMultiUserMode as jest.Mock).mockResolvedValue(true);
      (userFromSession as jest.Mock).mockResolvedValue({
        id: 1,
        role: UserRole.ADMIN,
      });

      const middleware = flexUserRoleValid([ROLES.admin]);

      await middleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalled();
      expect(mockResponse.sendStatus).not.toHaveBeenCalled();
    });

    it("should log debug information in development mode", async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "development";
      const consoleSpy = jest.spyOn(console, "log").mockImplementation();

      (SystemSettings.isMultiUserMode as jest.Mock).mockResolvedValue(true);
      (userFromSession as jest.Mock).mockResolvedValue({
        id: 1,
        role: UserRole.ADMIN,
      });

      const middleware = flexUserRoleValid([ROLES.admin]);

      await middleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(consoleSpy).toHaveBeenCalledTimes(2); // Debug logs
      expect(mockNext).toHaveBeenCalled();

      process.env.NODE_ENV = originalEnv;
      consoleSpy.mockRestore();
    });
  });

  describe("isMultiUserSetup", () => {
    it("should allow access when multi-user mode is enabled", async () => {
      (SystemSettings.isMultiUserMode as jest.Mock).mockResolvedValue(true);

      await isMultiUserSetup(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalled();
      expect(mockResponse.status).not.toHaveBeenCalled();
    });

    it("should return 403 when multi-user mode is disabled", async () => {
      (SystemSettings.isMultiUserMode as jest.Mock).mockResolvedValue(false);

      await isMultiUserSetup(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: "Invalid request",
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe("canManageSystemTemplates", () => {
    it("should return true for admin users", () => {
      const user = {
        id: 1,
        role: UserRole.ADMIN,
        organizationId: 1,
        username: "admin",
        email: "<EMAIL>",
        suspended: 0,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      };
      expect(canManageSystemTemplates(user)).toBe(true);
    });

    it("should return false for non-admin users", () => {
      const manager = {
        id: 1,
        role: UserRole.MANAGER,
        organizationId: 1,
        username: "manager",
        email: "<EMAIL>",
        suspended: 0,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      };
      const superuser = {
        id: 1,
        role: UserRole.SUPERUSER,
        organizationId: 1,
        username: "superuser",
        email: "<EMAIL>",
        suspended: 0,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      };
      const defaultUser = {
        id: 1,
        role: UserRole.DEFAULT,
        organizationId: 1,
        username: "user",
        email: "<EMAIL>",
        suspended: 0,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      };

      expect(canManageSystemTemplates(manager)).toBe(false);
      expect(canManageSystemTemplates(superuser)).toBe(false);
      expect(canManageSystemTemplates(defaultUser)).toBe(false);
    });

    it("should return false for null user", () => {
      expect(canManageSystemTemplates(null)).toBe(false);
    });
  });

  describe("canManageOrgTemplates", () => {
    it("should return true for admin users regardless of organization", () => {
      const admin = {
        id: 1,
        role: UserRole.ADMIN,
        organizationId: 1,
        username: "admin",
        email: "<EMAIL>",
        suspended: 0,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      };
      expect(canManageOrgTemplates(admin)).toBe(true);
    });

    it("should return true for managers in their own organization", () => {
      const manager = {
        id: 1,
        role: UserRole.MANAGER,
        organizationId: 1,
        username: "manager",
        email: "<EMAIL>",
        suspended: 0,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      };
      expect(canManageOrgTemplates(manager, 1)).toBe(true);
    });

    it("should return true for superusers in their own organization", () => {
      const superuser = {
        id: 1,
        role: UserRole.SUPERUSER,
        organizationId: 1,
        username: "superuser",
        email: "<EMAIL>",
        suspended: 0,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      };
      expect(canManageOrgTemplates(superuser, 1)).toBe(true);
    });

    it("should return false for managers in different organization", () => {
      const manager = {
        id: 1,
        role: UserRole.MANAGER,
        organizationId: 1,
        username: "manager",
        email: "<EMAIL>",
        suspended: 0,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      };
      expect(canManageOrgTemplates(manager, 2)).toBe(false);
    });

    it("should return false for default users", () => {
      const user = {
        id: 1,
        role: UserRole.DEFAULT,
        organizationId: 1,
        username: "user",
        email: "<EMAIL>",
        suspended: 0,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      };
      expect(canManageOrgTemplates(user)).toBe(false);
    });

    it("should return false for null user", () => {
      expect(canManageOrgTemplates(null)).toBe(false);
    });
  });

  describe("canManageUserTemplates", () => {
    it("should return true for admin users for any user", () => {
      const admin = {
        id: 1,
        role: UserRole.ADMIN,
        organizationId: 1,
        username: "admin",
        email: "<EMAIL>",
        suspended: 0,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      };
      expect(canManageUserTemplates(admin)).toBe(true);
    });

    it("should return true for users managing their own templates", () => {
      const user = {
        id: 1,
        role: UserRole.DEFAULT,
        organizationId: 1,
        username: "user",
        email: "<EMAIL>",
        suspended: 0,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      };
      expect(canManageUserTemplates(user, 1)).toBe(true);
    });

    it("should return false for users managing other users templates", () => {
      const user = {
        id: 1,
        role: UserRole.DEFAULT,
        organizationId: 1,
        username: "user",
        email: "<EMAIL>",
        suspended: 0,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      };
      expect(canManageUserTemplates(user, 2)).toBe(false);
    });

    it("should return false for null user", () => {
      expect(canManageUserTemplates(null)).toBe(false);
    });
  });

  describe("legalTemplateScopeGuard", () => {
    describe("system scope", () => {
      it("should allow admin access", async () => {
        (userFromSession as jest.Mock).mockResolvedValue({
          id: 1,
          role: UserRole.ADMIN,
        });

        const middleware = legalTemplateScopeGuard("system");

        await middleware(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockNext).toHaveBeenCalled();
        expect(mockResponse.status).not.toHaveBeenCalled();
      });

      it("should deny non-admin access", async () => {
        (userFromSession as jest.Mock).mockResolvedValue({
          id: 1,
          role: UserRole.MANAGER,
        });

        const middleware = legalTemplateScopeGuard("system");

        await middleware(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockResponse.status).toHaveBeenCalledWith(403);
        expect(mockResponse.json).toHaveBeenCalledWith({
          success: false,
          error: "Unauthorized",
        });
        expect(mockNext).not.toHaveBeenCalled();
      });
    });

    describe("org scope", () => {
      it("should allow admin access to any organization", async () => {
        mockRequest.query = { organizationId: "2" };
        (userFromSession as jest.Mock).mockResolvedValue({
          id: 1,
          role: UserRole.ADMIN,
          organizationId: 1,
        });

        const middleware = legalTemplateScopeGuard("org");

        await middleware(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockNext).toHaveBeenCalled();
      });

      it("should allow manager access to own organization", async () => {
        mockRequest.query = { organizationId: "1" };
        (userFromSession as jest.Mock).mockResolvedValue({
          id: 1,
          role: UserRole.MANAGER,
          organizationId: 1,
        });

        const middleware = legalTemplateScopeGuard("org");

        await middleware(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockNext).toHaveBeenCalled();
      });

      it("should deny manager access to different organization", async () => {
        mockRequest.query = { organizationId: "2" };
        (userFromSession as jest.Mock).mockResolvedValue({
          id: 1,
          role: UserRole.MANAGER,
          organizationId: 1,
        });

        const middleware = legalTemplateScopeGuard("org");

        await middleware(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockResponse.status).toHaveBeenCalledWith(403);
        expect(mockNext).not.toHaveBeenCalled();
      });

      it("should check body for organizationId if not in query", async () => {
        mockRequest.body = { organizationId: 1 };
        (userFromSession as jest.Mock).mockResolvedValue({
          id: 1,
          role: UserRole.MANAGER,
          organizationId: 1,
        });

        const middleware = legalTemplateScopeGuard("org");

        await middleware(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockNext).toHaveBeenCalled();
      });
    });

    describe("user scope", () => {
      it("should allow admin access to any user", async () => {
        mockRequest.params = { id: "2" };
        (userFromSession as jest.Mock).mockResolvedValue({
          id: 1,
          role: UserRole.ADMIN,
        });

        const middleware = legalTemplateScopeGuard("user");

        await middleware(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockNext).toHaveBeenCalled();
      });

      it("should allow users to manage their own templates", async () => {
        mockRequest.params = { id: "1" };
        (userFromSession as jest.Mock).mockResolvedValue({
          id: 1,
          role: UserRole.DEFAULT,
        });

        const middleware = legalTemplateScopeGuard("user");

        await middleware(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockNext).toHaveBeenCalled();
      });

      it("should use current user id when no param provided", async () => {
        mockRequest.params = {};
        (userFromSession as jest.Mock).mockResolvedValue({
          id: 1,
          role: UserRole.DEFAULT,
        });

        const middleware = legalTemplateScopeGuard("user");

        await middleware(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockNext).toHaveBeenCalled();
      });

      it("should deny users managing other users templates", async () => {
        mockRequest.params = { id: "2" };
        (userFromSession as jest.Mock).mockResolvedValue({
          id: 1,
          role: UserRole.DEFAULT,
        });

        const middleware = legalTemplateScopeGuard("user");

        await middleware(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockResponse.status).toHaveBeenCalledWith(403);
        expect(mockNext).not.toHaveBeenCalled();
      });

      it("should use cached user from response locals", async () => {
        mockRequest.params = { id: "1" };
        mockResponse.locals!.user = { id: 1, role: UserRole.DEFAULT };

        const middleware = legalTemplateScopeGuard("user");

        await middleware(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(userFromSession).not.toHaveBeenCalled();
        expect(mockNext).toHaveBeenCalled();
      });
    });
  });

  describe("ROLES constant", () => {
    it("should have all expected role values", () => {
      expect(ROLES).toEqual({
        all: "<all>",
        admin: UserRole.ADMIN,
        manager: UserRole.MANAGER,
        superuser: UserRole.SUPERUSER,
        default: UserRole.DEFAULT,
      });
    });
  });

  describe("Error handling", () => {
    it("should handle database errors gracefully", async () => {
      (SystemSettings.isMultiUserMode as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      const middleware = strictMultiUserRoleValid([ROLES.admin]);

      // The middleware doesn't catch errors, so they will bubble up
      await expect(
        middleware(mockRequest as Request, mockResponse as Response, mockNext)
      ).rejects.toThrow("Database error");
    });

    it("should handle userFromSession errors gracefully", async () => {
      (SystemSettings.isMultiUserMode as jest.Mock).mockResolvedValue(true);
      (userFromSession as jest.Mock).mockRejectedValue(
        new Error("Session error")
      );

      const middleware = strictMultiUserRoleValid([ROLES.admin]);

      // The middleware doesn't catch errors, so they will bubble up
      await expect(
        middleware(mockRequest as Request, mockResponse as Response, mockNext)
      ).rejects.toThrow("Session error");
    });
  });

  describe("Edge cases", () => {
    it("should handle empty role arrays", async () => {
      (SystemSettings.isMultiUserMode as jest.Mock).mockResolvedValue(true);
      (userFromSession as jest.Mock).mockResolvedValue({
        id: 1,
        role: UserRole.ADMIN,
      });

      const middleware = strictMultiUserRoleValid([]);

      await middleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.sendStatus).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it("should handle undefined user role", async () => {
      (SystemSettings.isMultiUserMode as jest.Mock).mockResolvedValue(true);
      (userFromSession as jest.Mock).mockResolvedValue({
        id: 1,
        // role is undefined
      });

      const middleware = strictMultiUserRoleValid([ROLES.admin]);

      await middleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.sendStatus).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it("should handle invalid organizationId in legalTemplateScopeGuard", async () => {
      mockRequest.query = { organizationId: "invalid" };
      (userFromSession as jest.Mock).mockResolvedValue({
        id: 1,
        role: UserRole.MANAGER,
        organizationId: 1,
      });

      const middleware = legalTemplateScopeGuard("org");

      await middleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });
  });
});
