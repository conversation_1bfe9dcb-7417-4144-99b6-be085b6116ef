/**
 * Comprehensive test suite for requireAdminRole middleware
 *
 * Tests all requireAdminRole functionality for 100% code coverage including:
 * - Admin and Manager role access validation
 * - Authentication requirement enforcement
 * - Proper error responses and status codes
 * - Security edge cases and malicious inputs
 * - Role-based access control verification
 */

import { Request, Response, NextFunction } from "express";
import { requireAdminRole } from "../requireAdminRole";
import { tSync } from "../../i18n";
import { UserRole } from "../../../types/shared";

// Mock dependencies
jest.mock("../../i18n", () => ({
  tSync: jest.fn(),
}));

describe("requireAdminRole Middleware", () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockRequest = {};
    mockResponse = {
      locals: {},
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };
    mockNext = jest.fn();

    // Reset mocks
    jest.clearAllMocks();

    // Setup default i18n responses
    (tSync as jest.Mock).mockImplementation((key: string) => {
      const translations: Record<string, string> = {
        "errors.auth.authentication-required": "Authentication required",
        "errors.auth.insufficient-permissions": "Insufficient permissions",
      };
      return translations[key] || key;
    });
  });

  describe("Successful Access Cases", () => {
    test("should allow access for ADMIN user", () => {
      const adminUser = {
        id: 1,
        username: "admin",
        role: UserRole.ADMIN,
      };
      mockResponse.locals!.user = adminUser;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledTimes(1);
      expect(mockResponse.status).not.toHaveBeenCalled();
      expect(mockResponse.json).not.toHaveBeenCalled();
    });

    test("should allow access for MANAGER user", () => {
      const managerUser = {
        id: 2,
        username: "manager",
        role: UserRole.MANAGER,
      };
      mockResponse.locals!.user = managerUser;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledTimes(1);
      expect(mockResponse.status).not.toHaveBeenCalled();
      expect(mockResponse.json).not.toHaveBeenCalled();
    });

    test("should handle admin user with additional properties", () => {
      const adminUserWithExtras = {
        id: 1,
        username: "admin",
        role: UserRole.ADMIN,
        email: "<EMAIL>",
        organizationId: 1,
        createdAt: new Date(),
        extraProperty: "should not interfere",
      };
      mockResponse.locals!.user = adminUserWithExtras;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledTimes(1);
    });

    test("should handle manager user with additional properties", () => {
      const managerUserWithExtras = {
        id: 2,
        username: "manager",
        role: UserRole.MANAGER,
        department: "Legal",
        permissions: ["read", "write"],
      };
      mockResponse.locals!.user = managerUserWithExtras;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledTimes(1);
    });
  });

  describe("Authentication Failure Cases", () => {
    test("should reject when no user in res.locals", () => {
      // No user set in res.locals
      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: "Authentication required",
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test("should reject when user is null", () => {
      mockResponse.locals!.user = null;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: "Authentication required",
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test("should reject when user is undefined", () => {
      mockResponse.locals!.user = undefined;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: "Authentication required",
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test("should reject when res.locals is undefined", () => {
      mockResponse.locals = undefined;

      expect(() =>
        requireAdminRole(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        )
      ).toThrow();
    });

    test("should reject when user is empty object", () => {
      mockResponse.locals!.user = {};

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: "Insufficient permissions",
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe("Insufficient Permissions Cases", () => {
    test("should reject DEFAULT role user", () => {
      const defaultUser = {
        id: 3,
        username: "user",
        role: UserRole.DEFAULT,
      };
      mockResponse.locals!.user = defaultUser;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: "Insufficient permissions",
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test("should reject SUPERUSER role user", () => {
      const superUser = {
        id: 4,
        username: "superuser",
        role: UserRole.SUPERUSER,
      };
      mockResponse.locals!.user = superUser;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: "Insufficient permissions",
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test("should reject user with no role property", () => {
      const userWithoutRole = {
        id: 5,
        username: "norole",
        // No role property
      };
      mockResponse.locals!.user = userWithoutRole;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: "Insufficient permissions",
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test("should reject user with null role", () => {
      const userWithNullRole = {
        id: 6,
        username: "nullrole",
        role: null,
      };
      mockResponse.locals!.user = userWithNullRole;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: "Insufficient permissions",
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test("should reject user with undefined role", () => {
      const userWithUndefinedRole = {
        id: 7,
        username: "undefrole",
        role: undefined,
      };
      mockResponse.locals!.user = userWithUndefinedRole;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: "Insufficient permissions",
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test("should reject user with invalid role string", () => {
      const userWithInvalidRole = {
        id: 8,
        username: "invalidrole",
        role: "INVALID_ROLE",
      };
      mockResponse.locals!.user = userWithInvalidRole;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: "Insufficient permissions",
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe("Security Edge Cases", () => {
    test("should handle user as malicious object", () => {
      const maliciousUser = {
        id: 9,
        username: "malicious",
        role: UserRole.ADMIN,
        // Malicious properties that shouldn't affect authorization
        __proto__: { role: UserRole.DEFAULT },
        constructor: { role: UserRole.DEFAULT },
        toString: () => UserRole.DEFAULT,
        valueOf: () => UserRole.DEFAULT,
      };
      mockResponse.locals!.user = maliciousUser;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Should still allow access because the actual role property is ADMIN
      expect(mockNext).toHaveBeenCalledTimes(1);
      expect(mockResponse.status).not.toHaveBeenCalled();
    });

    test("should handle user as array", () => {
      const arrayUser = [{ role: UserRole.ADMIN }] as any;
      mockResponse.locals!.user = arrayUser;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: "Insufficient permissions",
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test("should handle user as function", () => {
      const functionUser = (() => ({ role: UserRole.ADMIN })) as any;
      mockResponse.locals!.user = functionUser;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });

    test("should handle role value injection attempts", () => {
      const injectionUser = {
        id: 10,
        username: "injection",
        role: `${UserRole.ADMIN}; DROP TABLE users; --`,
      };
      mockResponse.locals!.user = injectionUser;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });

    test("should handle XSS attempts in user data", () => {
      const xssUser = {
        id: 11,
        username: "<script>alert('xss')</script>",
        role: UserRole.ADMIN,
        email: "javascript:alert('xss')",
      };
      mockResponse.locals!.user = xssUser;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Should allow access as role is valid ADMIN
      expect(mockNext).toHaveBeenCalledTimes(1);
      expect(mockResponse.status).not.toHaveBeenCalled();
    });

    test("should handle numeric role values", () => {
      const numericRoleUser = {
        id: 12,
        username: "numeric",
        role: 1, // Numeric instead of string enum
      };
      mockResponse.locals!.user = numericRoleUser;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });

    test("should handle boolean role values", () => {
      const booleanRoleUser = {
        id: 13,
        username: "boolean",
        role: true, // Boolean instead of string enum
      };
      mockResponse.locals!.user = booleanRoleUser;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe("Response Handling", () => {
    test("should return proper 401 response structure for unauthenticated", () => {
      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: "Authentication required",
      });
      expect(mockResponse.json).toHaveBeenCalledTimes(1);
    });

    test("should return proper 403 response structure for insufficient permissions", () => {
      const defaultUser = {
        id: 1,
        username: "user",
        role: UserRole.DEFAULT,
      };
      mockResponse.locals!.user = defaultUser;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: "Insufficient permissions",
      });
      expect(mockResponse.json).toHaveBeenCalledTimes(1);
    });

    test("should use i18n for error messages", () => {
      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(tSync).toHaveBeenCalledWith("errors.auth.authentication-required");
    });

    test("should use i18n for permission error messages", () => {
      const defaultUser = {
        id: 1,
        username: "user",
        role: UserRole.DEFAULT,
      };
      mockResponse.locals!.user = defaultUser;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(tSync).toHaveBeenCalledWith(
        "errors.auth.insufficient-permissions"
      );
    });

    test("should maintain response method chaining", () => {
      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveReturnedWith(mockResponse);
      expect(mockResponse.json).toHaveReturnedWith(mockResponse);
    });

    test("should not call next() after sending error response", () => {
      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).not.toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalled();
    });

    test("should handle i18n translation failures gracefully", () => {
      (tSync as jest.Mock).mockImplementation(() => {
        throw new Error("Translation failed");
      });

      const defaultUser = {
        id: 1,
        username: "user",
        role: UserRole.DEFAULT,
      };
      mockResponse.locals!.user = defaultUser;

      // Should not throw error, but middleware might not work as expected
      expect(() =>
        requireAdminRole(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        )
      ).toThrow("Translation failed");
    });
  });

  describe("Multiple Invocations", () => {
    test("should handle sequential calls with different users", () => {
      // First call with admin user
      const adminUser = { id: 1, role: UserRole.ADMIN };
      mockResponse.locals!.user = adminUser;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledTimes(1);

      // Reset mocks for second call
      jest.clearAllMocks();

      // Second call with default user
      const defaultUser = { id: 2, role: UserRole.DEFAULT };
      mockResponse.locals!.user = defaultUser;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });

    test("should handle concurrent calls with same user", () => {
      const adminUser = { id: 1, role: UserRole.ADMIN };
      mockResponse.locals!.user = adminUser;

      // Multiple concurrent calls
      for (let i = 0; i < 5; i++) {
        requireAdminRole(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );
      }

      expect(mockNext).toHaveBeenCalledTimes(5);
      expect(mockResponse.status).not.toHaveBeenCalled();
    });
  });

  describe("Type Safety", () => {
    test("should work with properly typed request object", () => {
      const typedRequest: Request = mockRequest as Request;
      const adminUser = { id: 1, role: UserRole.ADMIN };
      mockResponse.locals!.user = adminUser;

      requireAdminRole(typedRequest, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledTimes(1);
    });

    test("should work with extended request types", () => {
      interface ExtendedRequest extends Request {
        customProperty?: string;
      }

      const extendedRequest: ExtendedRequest = {
        ...mockRequest,
        customProperty: "test",
      } as ExtendedRequest;

      const adminUser = { id: 1, role: UserRole.ADMIN };
      mockResponse.locals!.user = adminUser;

      requireAdminRole(extendedRequest, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledTimes(1);
    });

    test("should handle middleware function signature correctly", () => {
      const adminUser = { id: 1, role: UserRole.ADMIN };
      mockResponse.locals!.user = adminUser;

      // Test that the function matches the expected middleware signature
      const middleware = requireAdminRole;
      expect(typeof middleware).toBe("function");
      expect(middleware.length).toBe(3); // Should accept 3 parameters

      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledTimes(1);
    });
  });

  describe("Edge Cases and Error Handling", () => {
    test("should handle corrupted response object", () => {
      const corruptedResponse = {
        locals: { user: null },
        status: null, // Corrupted status method
        json: jest.fn(),
      } as any;

      expect(() =>
        requireAdminRole(mockRequest as Request, corruptedResponse, mockNext)
      ).toThrow();
    });

    test("should handle response with missing json method", () => {
      const responseWithoutJson = {
        locals: { user: null },
        status: jest.fn().mockReturnThis(),
        // Missing json method
      } as any;

      expect(() =>
        requireAdminRole(mockRequest as Request, responseWithoutJson, mockNext)
      ).toThrow();
    });

    test("should handle deeply nested user object", () => {
      const deeplyNestedUser = {
        profile: {
          user: {
            details: {
              id: 1,
              role: UserRole.ADMIN,
            },
          },
        },
      };
      mockResponse.locals!.user = deeplyNestedUser;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Should fail because the role is not at the top level
      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });

    test("should handle user with circular references", () => {
      const circularUser: any = {
        id: 1,
        role: UserRole.ADMIN,
      };
      circularUser.self = circularUser; // Create circular reference

      mockResponse.locals!.user = circularUser;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Should still work because the role property is accessible
      expect(mockNext).toHaveBeenCalledTimes(1);
    });

    test("should handle user with getter properties", () => {
      const userWithGetter = {
        id: 1,
        get role() {
          return UserRole.ADMIN;
        },
      };
      mockResponse.locals!.user = userWithGetter;

      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledTimes(1);
    });

    test("should handle user with property that throws on access", () => {
      const userWithThrowingProperty = {
        id: 1,
        get role() {
          throw new Error("Property access error");
        },
      };
      mockResponse.locals!.user = userWithThrowingProperty;

      expect(() =>
        requireAdminRole(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        )
      ).toThrow("Property access error");
    });
  });

  describe("Performance Tests", () => {
    test("should handle high-frequency authorization checks efficiently", () => {
      const adminUser = { id: 1, role: UserRole.ADMIN };
      mockResponse.locals!.user = adminUser;

      const startTime = Date.now();
      let callCount = 0;

      // Perform 1000 authorization checks
      for (let i = 0; i < 1000; i++) {
        const localNext = jest.fn();
        requireAdminRole(
          mockRequest as Request,
          mockResponse as Response,
          localNext
        );
        if (localNext.mock.calls.length > 0) {
          callCount++;
        }
      }

      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(100); // Should complete within 100ms
      expect(callCount).toBe(1000);
    });

    test("should handle authorization with large user objects efficiently", () => {
      // Create a user object with many properties
      const largeUser = {
        id: 1,
        role: UserRole.ADMIN,
        ...Array.from({ length: 1000 }, (_, i) => ({
          [`property_${i}`]: `value_${i}`,
        })).reduce((acc, obj) => ({ ...acc, ...obj }), {}),
      };

      mockResponse.locals!.user = largeUser;

      const startTime = Date.now();
      requireAdminRole(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(10); // Should complete quickly
      expect(mockNext).toHaveBeenCalledTimes(1);
    });
  });
});
