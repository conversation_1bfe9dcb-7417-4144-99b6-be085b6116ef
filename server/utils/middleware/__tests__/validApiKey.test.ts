/**
 * Comprehensive tests for API key validation middleware
 * Tests authentication, authorization, and security measures
 */

// Mock Prisma and dependencies before imports
jest.mock("../../prisma", () => ({
  __esModule: true,
  default: {
    $connect: jest.fn(),
    $disconnect: jest.fn(),
  },
}));

jest.mock("../../../models/apiKeys", () => ({
  ApiKey: {
    get: jest.fn(),
    findAll: jest.fn(),
    where: jest.fn(),
  },
}));

jest.mock("../../http", () => ({
  userFromSession: jest.fn(),
  reqBody: jest.fn(),
}));

jest.mock("../../../models/systemSettings", () => ({
  __esModule: true,
  default: {
    isMultiUserMode: jest.fn(),
  },
}));

import { Request, Response, NextFunction } from "express";
import { validApiKey } from "../validApiKey";
import { ApiKey } from "../../../models/apiKeys";
import SystemSettings from "../../../models/systemSettings";

// Type the mocked ApiKey and SystemSettings
const mockApiKey = ApiKey as jest.Mocked<typeof ApiKey>;
const mockSystemSettings = SystemSettings as jest.Mocked<typeof SystemSettings>;

describe("API Key Validation Middleware Tests", () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockReq = {
      headers: {},
      ip: "127.0.0.1",
      method: "GET",
      url: "/api/test",
      header: jest.fn((name: string): string | undefined => {
        const lowerName = name.toLowerCase();
        if (lowerName === "authorization") {
          return (
            String(
              mockReq.headers?.authorization ||
                mockReq.headers?.Authorization ||
                ""
            ) || undefined
          );
        }
        if (lowerName === "x-api-key") {
          return String(mockReq.headers?.["x-api-key"] || "") || undefined;
        }
        return undefined;
      }) as any,
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      end: jest.fn(),
      locals: {}, // Ensure locals is always present for all tests
    };

    mockNext = jest.fn();

    // Clear all mocks
    jest.clearAllMocks();

    // Set up default SystemSettings mock
    mockSystemSettings.isMultiUserMode.mockResolvedValue(true);
  });

  describe("Valid API key scenarios", () => {
    it("should accept valid API key in Authorization header", async () => {
      const validKey = "valid-api-key-123";
      mockReq.headers = {
        authorization: `Bearer ${validKey}`,
      };

      mockApiKey.get.mockResolvedValue({
        id: 1,
        secret: validKey,
        createdBy: 1,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      });

      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      expect(mockApiKey.get).toHaveBeenCalledWith({ secret: validKey });
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    it("should reject request with x-api-key header (not supported)", async () => {
      const validKey = "x-api-key-456";
      mockReq.headers = {
        "x-api-key": validKey,
      };

      // The middleware only checks Authorization header, not x-api-key
      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      expect(mockApiKey.get).not.toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: "No valid api key found.",
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it("should prioritize Authorization header over x-api-key", async () => {
      const authKey = "auth-key-123";
      const xApiKey = "x-api-key-456";

      mockReq.headers = {
        authorization: `Bearer ${authKey}`,
        "x-api-key": xApiKey,
      };

      mockApiKey.get.mockResolvedValue({
        id: 1,
        secret: authKey,
        createdBy: 1,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      });

      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      expect(mockApiKey.get).toHaveBeenCalledWith({ secret: authKey });
      expect(mockApiKey.get).not.toHaveBeenCalledWith({ secret: xApiKey });
    });

    it("should handle case-insensitive header names", async () => {
      const validKey = "case-insensitive-key";
      mockReq.headers = {
        "X-API-KEY": validKey, // Uppercase - x-api-key is not supported
      };

      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      // Since x-api-key is not supported, should reject
      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: "No valid api key found.",
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe("Invalid API key scenarios", () => {
    it("should reject request with no API key", async () => {
      mockReq.headers = {};

      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: "No valid api key found.",
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it("should reject empty Authorization header", async () => {
      mockReq.headers = {
        authorization: "",
      };

      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: "No valid api key found.",
      });
    });

    it("should reject empty x-api-key header (not supported)", async () => {
      mockReq.headers = {
        "x-api-key": "",
      };

      // The middleware only checks Authorization header, not x-api-key
      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: "No valid api key found.",
      });
    });

    it("should reject invalid API key format", async () => {
      mockReq.headers = {
        authorization: "InvalidFormat",
      };

      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: "No valid api key found.",
      });
    });

    it("should reject non-existent API key", async () => {
      const nonExistentKey = "non-existent-key";
      mockReq.headers = {
        authorization: `Bearer ${nonExistentKey}`,
      };

      mockApiKey.get.mockResolvedValue(null);

      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      expect(mockApiKey.get).toHaveBeenCalledWith({ secret: nonExistentKey });
      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: "No valid api key found.",
      });
    });

    it("should reject inactive API key", async () => {
      const inactiveKey = "inactive-key";
      mockReq.headers = {
        authorization: `Bearer ${inactiveKey}`,
      };

      // Since api_keys model doesn't have an 'active' field,
      // we'll simulate an inactive key by returning null (key not found)
      mockApiKey.get.mockResolvedValue(null);

      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: "No valid api key found.",
      });
    });
  });

  describe("Security attack scenarios", () => {
    it("should handle SQL injection attempts in API key", async () => {
      const maliciousKey = "'; DROP TABLE api_keys; --";
      mockReq.headers = {
        authorization: `Bearer ${maliciousKey}`,
      };

      mockApiKey.get.mockResolvedValue(null);

      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      // The middleware splits by space, so only the first part is used
      expect(mockApiKey.get).toHaveBeenCalledWith({ secret: "';" });
      expect(mockRes.status).toHaveBeenCalledWith(403);
    });

    it("should handle extremely long API keys", async () => {
      const longKey = "a".repeat(10000);
      mockReq.headers = {
        authorization: `Bearer ${longKey}`,
      };

      mockApiKey.get.mockResolvedValue(null);

      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
    });

    it("should handle special characters in API key", async () => {
      const specialKey = "key-with-!@#$%^&*()_+-=[]{}|;:,.<>?";
      mockReq.headers = {
        authorization: `Bearer ${specialKey}`,
      };

      mockApiKey.get.mockResolvedValue(null);

      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      expect(mockApiKey.get).toHaveBeenCalledWith({ secret: specialKey });
      expect(mockRes.status).toHaveBeenCalledWith(403);
    });

    it("should handle null bytes in API key", async () => {
      const nullByteKey = "key\\x00with\\x00nulls";
      mockReq.headers = {
        authorization: `Bearer ${nullByteKey}`,
      };

      mockApiKey.get.mockResolvedValue(null);

      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
    });

    it("should handle unicode characters in API key", async () => {
      const unicodeKey = "key-with-üñíçødé-characters-🔑";
      mockReq.headers = {
        authorization: `Bearer ${unicodeKey}`,
      };

      mockApiKey.get.mockResolvedValue(null);

      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      expect(mockApiKey.get).toHaveBeenCalledWith({ secret: unicodeKey });
      expect(mockRes.status).toHaveBeenCalledWith(403);
    });

    it("should handle multiple Bearer tokens", async () => {
      mockReq.headers = {
        authorization: "Bearer key1 Bearer key2",
      };

      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
    });

    it("should handle case variations of Bearer", async () => {
      const testKey = "test-key";

      const variations = [
        `bearer ${testKey}`,
        `BEARER ${testKey}`,
        `Bearer ${testKey}`,
        `BeArEr ${testKey}`,
      ];

      for (const auth of variations) {
        jest.clearAllMocks();
        mockReq.headers = { authorization: auth };
        mockApiKey.get.mockResolvedValue({
          id: 1,
          secret: testKey,
          createdBy: 1,
          createdAt: new Date(),
          lastUpdatedAt: new Date(),
        });

        await validApiKey(mockReq as Request, mockRes as Response, mockNext);

        if (auth.toLowerCase().startsWith("bearer ")) {
          expect(mockNext).toHaveBeenCalled();
        } else {
          expect(mockRes.status).toHaveBeenCalledWith(403);
        }
      }
    });
  });

  describe("Database error handling", () => {
    it("should handle database connection errors", async () => {
      const validKey = "valid-key";
      mockReq.headers = {
        authorization: `Bearer ${validKey}`,
      };

      mockApiKey.get.mockRejectedValue(new Error("Database connection failed"));

      // The middleware doesn't catch errors, so they propagate
      await expect(
        validApiKey(mockReq as Request, mockRes as Response, mockNext)
      ).rejects.toThrow("Database connection failed");
    });

    it("should handle database timeout errors", async () => {
      const validKey = "timeout-key";
      mockReq.headers = {
        authorization: `Bearer ${validKey}`,
      };

      const timeoutError = new Error("Query timeout");
      timeoutError.name = "TimeoutError";
      mockApiKey.get.mockRejectedValue(timeoutError);

      // The middleware doesn't catch errors, so they propagate
      await expect(
        validApiKey(mockReq as Request, mockRes as Response, mockNext)
      ).rejects.toThrow("Query timeout");
    });

    it("should handle unexpected database errors", async () => {
      const validKey = "error-key";
      mockReq.headers = {
        authorization: `Bearer ${validKey}`,
      };

      mockApiKey.get.mockRejectedValue(new Error("Unexpected error"));

      // The middleware doesn't catch errors, so they propagate
      await expect(
        validApiKey(mockReq as Request, mockRes as Response, mockNext)
      ).rejects.toThrow("Unexpected error");
    });
  });

  describe("Rate limiting and performance", () => {
    it("should handle rapid successive API key validations", async () => {
      const validKey = "rate-limit-key";
      const requests = Array(100)
        .fill(null)
        .map(() => ({
          headers: { authorization: `Bearer ${validKey}` },
          ip: "127.0.0.1",
          method: "GET",
          url: "/api/test",
          header: jest.fn((name: string) => {
            if (name.toLowerCase() === "authorization") {
              return `Bearer ${validKey}`;
            }
            return undefined;
          }),
        }));

      mockApiKey.get.mockResolvedValue({
        id: 1,
        secret: validKey,
        createdBy: 1,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      });

      const promises = requests.map((req) =>
        validApiKey(req as unknown as Request, mockRes as Response, mockNext)
      );

      await Promise.allSettled(promises);

      expect(mockApiKey.get).toHaveBeenCalledTimes(100);
    });

    it("should handle concurrent validations of different keys", async () => {
      const keys = Array(10)
        .fill(null)
        .map((_, i) => `concurrent-key-${i}`);

      const requests = keys.map((key) => ({
        headers: { authorization: `Bearer ${key}` },
        ip: "127.0.0.1",
        method: "GET",
        url: "/api/test",
        header: jest.fn((name: string) => {
          if (name.toLowerCase() === "authorization") {
            return `Bearer ${key}`;
          }
          return undefined;
        }),
      }));

      mockApiKey.get.mockImplementation((clause: any) => {
        const secret = clause?.secret;
        if (secret && keys.includes(secret)) {
          return Promise.resolve({
            id: keys.indexOf(secret) + 1,
            secret,
            createdBy: keys.indexOf(secret) + 1,
            createdAt: new Date(),
            lastUpdatedAt: new Date(),
          });
        }
        return Promise.resolve(null);
      });

      const promises = requests.map((req) =>
        validApiKey(req as unknown as Request, mockRes as Response, mockNext)
      );

      const results = await Promise.allSettled(promises);

      results.forEach((result) => {
        expect(result.status).toBe("fulfilled");
      });

      expect(mockNext).toHaveBeenCalledTimes(10);
    });
  });

  describe("Edge cases and boundary conditions", () => {
    it("should handle missing headers object", async () => {
      mockReq.headers = undefined as any;

      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
    });

    it("should handle non-string header values", async () => {
      mockReq.headers = {
        authorization: 123 as any,
      };

      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
    });

    it("should handle array header values", async () => {
      mockReq.headers = {
        authorization: ["Bearer key1", "Bearer key2"] as any,
      };

      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
    });

    it("should handle whitespace-only API keys", async () => {
      mockReq.headers = {
        authorization: "Bearer    ",
      };

      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
    });

    it("should handle API key with only Bearer prefix", async () => {
      mockReq.headers = {
        authorization: "Bearer",
      };

      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
    });

    it("should handle malformed Bearer format", async () => {
      const malformedAuths = [
        "Bearer",
        "Bearer ",
        " Bearer key",
        "Bearerkey",
        "Bearer  key  extra",
      ];

      for (const auth of malformedAuths) {
        jest.clearAllMocks();
        mockReq.headers = { authorization: auth };

        await validApiKey(mockReq as Request, mockRes as Response, mockNext);

        expect(mockRes.status).toHaveBeenCalledWith(403);
      }
    });

    it("should handle response object errors", async () => {
      const validKey = "response-error-key";
      mockReq.headers = {
        authorization: `Bearer ${validKey}`,
      };

      mockRes.status = jest.fn().mockImplementation(() => {
        throw new Error("Response error");
      });

      mockApiKey.get.mockResolvedValue(null);

      // The middleware doesn't catch errors, so it will throw
      await expect(
        validApiKey(mockReq as Request, mockRes as Response, mockNext)
      ).rejects.toThrow("Response error");
    });
  });

  describe("Security headers and metadata", () => {
    it("should not expose database structure in error messages", async () => {
      const validKey = "db-structure-test";
      mockReq.headers = {
        authorization: `Bearer ${validKey}`,
      };

      const dbError = new Error("Table 'api_keys' doesn't exist");
      mockApiKey.get.mockRejectedValue(dbError);

      // The middleware doesn't catch errors, so they propagate
      await expect(
        validApiKey(mockReq as Request, mockRes as Response, mockNext)
      ).rejects.toThrow("Table 'api_keys' doesn't exist");
    });

    it("should handle requests with suspicious user agents", async () => {
      const validKey = "user-agent-test";
      mockReq.headers = {
        authorization: `Bearer ${validKey}`,
        "user-agent": "sqlmap/1.0 (http://sqlmap.org)",
      };

      mockApiKey.get.mockResolvedValue({
        id: 1,
        secret: validKey,
        createdBy: 1,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
      });

      await validApiKey(mockReq as Request, mockRes as Response, mockNext);

      // Should still process valid API key regardless of user agent
      expect(mockNext).toHaveBeenCalled();
    });
  });
});
