import { Request, Response, NextFunction } from "express";

// Unmock the validWorkspace module to test the actual implementation
jest.unmock("../validWorkspace");

import {
  validWorkspaceSlug,
  validWorkspaceAndThreadSlug,
} from "../validWorkspace";
import { Workspace } from "../../../models/workspace";
import { WorkspaceThread } from "../../../models/workspaceThread";
import { WorkspaceShare } from "../../../models/workspaceShare";
import { ThreadShare } from "../../../models/threadShare";
import { userFromSession, multiUserMode } from "../../http";
import { UserRole } from "../../../types/shared";

// Mock dependencies
jest.mock("../../../models/workspace", () => ({
  Workspace: {
    get: jest.fn(),
    getWithUser: jest.fn(),
    where: jest.fn(),
  },
}));
jest.mock("../../../models/workspaceThread", () => ({
  WorkspaceThread: {
    get: jest.fn(),
    where: jest.fn(),
  },
}));
jest.mock("../../../models/workspaceShare", () => ({
  WorkspaceShare: {
    hasAccess: jest.fn(),
  },
}));
jest.mock("../../../models/threadShare", () => ({
  ThreadShare: {
    hasAccess: jest.fn(),
  },
}));
jest.mock("../../http", () => ({
  userFromSession: jest.fn(),
  multiUserMode: jest.fn(),
}));

describe("validWorkspace middleware", () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  // Mock workspace data
  const mockWorkspace = {
    id: 1,
    name: "Test Workspace",
    slug: "test-workspace",
    user_id: 1,
    organizationId: 1,
    createdAt: new Date(),
    documents: [],
  };

  // Mock thread data
  const mockThread = {
    id: 1,
    name: "Test Thread",
    slug: "test-thread",
    workspace_id: 1,
    user_id: 1,
    createdAt: new Date(),
  };

  // Mock user data
  const mockUser = {
    id: 1,
    username: "testuser",
    email: "<EMAIL>",
    role: UserRole.DEFAULT,
    organizationId: 1,
    suspended: 0,
    createdAt: new Date(),
    lastUpdatedAt: new Date(),
  };

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup mock request
    mockRequest = {
      params: {},
      query: {},
      body: {},
      headers: {},
    };

    // Create fresh mock functions for each test
    const mockSend = jest.fn().mockReturnThis();
    const mockStatus = jest.fn().mockReturnThis();
    const mockJson = jest.fn().mockReturnThis();

    // Setup mock response
    mockResponse = {
      locals: {},
      send: mockSend,
      status: mockStatus,
      json: mockJson,
    };

    // Setup mock next function
    mockNext = jest.fn();

    // Set default mock implementations
    (userFromSession as jest.Mock).mockResolvedValue(null);
    (multiUserMode as jest.Mock).mockReturnValue(false);
    (Workspace.getWithUser as jest.Mock).mockResolvedValue(null);
    (Workspace.get as jest.Mock).mockResolvedValue(null);
    (Workspace.where as jest.Mock).mockResolvedValue([]);
    (WorkspaceShare.hasAccess as jest.Mock).mockResolvedValue(false);
    (WorkspaceThread.get as jest.Mock).mockResolvedValue(null);
    (WorkspaceThread.where as jest.Mock).mockResolvedValue([]);
    (ThreadShare.hasAccess as jest.Mock).mockResolvedValue(false);
  });

  describe("validWorkspaceSlug", () => {
    describe("Parameter validation", () => {
      it("should return 400 when slug is missing", async () => {
        mockRequest.params = {};

        await validWorkspaceSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockResponse.status).toHaveBeenCalledWith(400);
        expect(mockResponse.send).toHaveBeenCalledWith(
          "Workspace slug is required."
        );
        expect(mockNext).not.toHaveBeenCalled();
      });

      it("should handle slug from different sources", async () => {
        // Test with slug in params
        mockRequest.params = { slug: "test-workspace" };
        (Workspace.get as jest.Mock).mockResolvedValue(mockWorkspace);

        await validWorkspaceSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockNext).toHaveBeenCalled();
        expect(mockResponse.locals!.workspace).toEqual(mockWorkspace);
      });
    });

    describe("Single-user mode access", () => {
      beforeEach(() => {
        (multiUserMode as jest.Mock).mockReturnValue(false);
      });

      it("should allow access when workspace exists", async () => {
        mockRequest.params = { slug: "test-workspace" };
        (Workspace.get as jest.Mock).mockResolvedValue(mockWorkspace);

        await validWorkspaceSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(Workspace.get).toHaveBeenCalledWith(
          { slug: "test-workspace" },
          false
        );
        expect(mockNext).toHaveBeenCalled();
        expect(mockResponse.locals!.workspace).toEqual(mockWorkspace);
      });

      it("should return 404 when workspace does not exist", async () => {
        mockRequest.params = { slug: "non-existent" };
        (Workspace.get as jest.Mock).mockResolvedValue(null);

        await validWorkspaceSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockResponse.status).toHaveBeenCalledWith(404);
        expect(mockResponse.send).toHaveBeenCalledWith(
          "Workspace does not exist or you don't have access."
        );
        expect(mockNext).not.toHaveBeenCalled();
      });

      it("should handle includeDocuments query parameter", async () => {
        mockRequest.params = { slug: "test-workspace" };
        mockRequest.query = { includeDocuments: "true" };
        (Workspace.get as jest.Mock).mockResolvedValue(mockWorkspace);

        await validWorkspaceSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(Workspace.get).toHaveBeenCalledWith(
          { slug: "test-workspace" },
          true
        );
        expect(mockNext).toHaveBeenCalled();
      });
    });

    describe("Multi-user mode access", () => {
      beforeEach(() => {
        (multiUserMode as jest.Mock).mockImplementation(() => true);
        (userFromSession as jest.Mock).mockResolvedValue(mockUser);
      });

      it("should allow direct workspace access for user", async () => {
        mockRequest.params = { slug: "test-workspace" };
        (Workspace.getWithUser as jest.Mock).mockResolvedValue(mockWorkspace);

        await validWorkspaceSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(Workspace.getWithUser).toHaveBeenCalledWith(mockUser, {
          slug: "test-workspace",
        });
        expect(mockNext).toHaveBeenCalled();
        expect(mockResponse.locals!.workspace).toEqual(mockWorkspace);
      });

      it("should check shared access when direct access fails", async () => {
        mockRequest.params = { slug: "test-workspace" };
        (Workspace.getWithUser as jest.Mock).mockResolvedValue(null);
        (Workspace.where as jest.Mock).mockResolvedValue([mockWorkspace]);
        (WorkspaceShare.hasAccess as jest.Mock).mockResolvedValue(true);

        await validWorkspaceSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(Workspace.where).toHaveBeenCalledWith({
          slug: "test-workspace",
        });
        expect(WorkspaceShare.hasAccess).toHaveBeenCalledWith(1, 1);
        expect(mockNext).toHaveBeenCalled();
        expect(mockResponse.locals!.workspace).toEqual(mockWorkspace);
      });

      it("should deny access when user has no shared access", async () => {
        mockRequest.params = { slug: "test-workspace" };
        (Workspace.getWithUser as jest.Mock).mockResolvedValue(null);
        (Workspace.where as jest.Mock).mockResolvedValue([mockWorkspace]);
        (WorkspaceShare.hasAccess as jest.Mock).mockResolvedValue(false);

        await validWorkspaceSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockResponse.status).toHaveBeenCalledWith(404);
        expect(mockResponse.send).toHaveBeenCalledWith(
          "Workspace does not exist or you don't have access."
        );
        expect(mockNext).not.toHaveBeenCalled();
      });

      it("should handle no workspaces found in where query", async () => {
        mockRequest.params = { slug: "test-workspace" };
        (Workspace.getWithUser as jest.Mock).mockResolvedValue(null);
        (Workspace.where as jest.Mock).mockResolvedValue([]);

        await validWorkspaceSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockResponse.status).toHaveBeenCalledWith(404);
        expect(WorkspaceShare.hasAccess).not.toHaveBeenCalled();
      });

      it("should handle null workspaces array", async () => {
        mockRequest.params = { slug: "test-workspace" };
        (Workspace.getWithUser as jest.Mock).mockResolvedValue(null);
        (Workspace.where as jest.Mock).mockResolvedValue(null);

        await validWorkspaceSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockResponse.status).toHaveBeenCalledWith(404);
        expect(WorkspaceShare.hasAccess).not.toHaveBeenCalled();
      });

      it("should handle empty workspaces array", async () => {
        mockRequest.params = { slug: "test-workspace" };
        (Workspace.getWithUser as jest.Mock).mockResolvedValue(null);
        (Workspace.where as jest.Mock).mockResolvedValue([]);

        await validWorkspaceSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockResponse.status).toHaveBeenCalledWith(404);
        expect(WorkspaceShare.hasAccess).not.toHaveBeenCalled();
      });

      it("should handle unauthenticated user in multi-user mode", async () => {
        mockRequest.params = { slug: "test-workspace" };
        (userFromSession as jest.Mock).mockResolvedValue(null);
        (Workspace.get as jest.Mock).mockResolvedValue(null);

        await validWorkspaceSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(Workspace.getWithUser).not.toHaveBeenCalled();
        expect(Workspace.get).toHaveBeenCalledWith(
          { slug: "test-workspace" },
          false
        );
        expect(mockResponse.status).toHaveBeenCalledWith(404);
      });
    });

    describe("Security edge cases", () => {
      it("should handle SQL injection attempts in slug", async () => {
        const maliciousSlug = "'; DROP TABLE workspaces; --";
        mockRequest.params = { slug: maliciousSlug };
        (Workspace.get as jest.Mock).mockResolvedValue(null);

        await validWorkspaceSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(Workspace.get).toHaveBeenCalledWith(
          { slug: maliciousSlug },
          false
        );
        expect(mockResponse.status).toHaveBeenCalledWith(404);
      });

      it("should handle XSS attempts in slug", async () => {
        const xssSlug = "<script>alert('xss')</script>";
        mockRequest.params = { slug: xssSlug };
        (Workspace.get as jest.Mock).mockResolvedValue(null);

        await validWorkspaceSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockResponse.status).toHaveBeenCalledWith(404);
        expect(mockResponse.send).toHaveBeenCalledWith(
          "Workspace does not exist or you don't have access."
        );
      });

      it("should handle extremely long slugs", async () => {
        const longSlug = "a".repeat(1000);
        mockRequest.params = { slug: longSlug };
        (Workspace.get as jest.Mock).mockResolvedValue(null);

        await validWorkspaceSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockResponse.status).toHaveBeenCalledWith(404);
      });
    });

    describe("Concurrent access and race conditions", () => {
      it("should handle concurrent workspace checks", async () => {
        mockRequest.params = { slug: "test-workspace" };
        (multiUserMode as jest.Mock).mockImplementation(() => true);
        (userFromSession as jest.Mock).mockResolvedValue(mockUser);

        // Simulate slow database queries
        (Workspace.getWithUser as jest.Mock).mockImplementation(
          () => new Promise((resolve) => setTimeout(() => resolve(null), 100))
        );
        (Workspace.where as jest.Mock).mockImplementation(
          () =>
            new Promise((resolve) =>
              setTimeout(() => resolve([mockWorkspace]), 50)
            )
        );
        (WorkspaceShare.hasAccess as jest.Mock).mockImplementation(
          () => new Promise((resolve) => setTimeout(() => resolve(true), 50))
        );

        const promise1 = validWorkspaceSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );
        const promise2 = validWorkspaceSlug(
          { ...mockRequest } as Request,
          { ...mockResponse, locals: {} } as Response,
          jest.fn()
        );

        await Promise.all([promise1, promise2]);

        expect(Workspace.getWithUser).toHaveBeenCalledTimes(2);
        expect(Workspace.where).toHaveBeenCalledTimes(2);
        expect(WorkspaceShare.hasAccess).toHaveBeenCalledTimes(2);
      });
    });

    describe("Error handling", () => {
      it("should handle database errors in Workspace.get", async () => {
        mockRequest.params = { slug: "test-workspace" };
        (Workspace.get as jest.Mock).mockRejectedValue(
          new Error("Database error")
        );

        await expect(
          validWorkspaceSlug(
            mockRequest as Request,
            mockResponse as Response,
            mockNext
          )
        ).rejects.toThrow("Database error");
      });

      it("should handle errors in WorkspaceShare.hasAccess", async () => {
        mockRequest.params = { slug: "test-workspace" };
        (multiUserMode as jest.Mock).mockImplementation(() => true);
        (userFromSession as jest.Mock).mockResolvedValue(mockUser);
        (Workspace.getWithUser as jest.Mock).mockResolvedValue(null);
        (Workspace.where as jest.Mock).mockResolvedValue([mockWorkspace]);
        (WorkspaceShare.hasAccess as jest.Mock).mockRejectedValue(
          new Error("Access check failed")
        );

        await expect(
          validWorkspaceSlug(
            mockRequest as Request,
            mockResponse as Response,
            mockNext
          )
        ).rejects.toThrow("Access check failed");
      });
    });
  });

  describe("validWorkspaceAndThreadSlug", () => {
    describe("Parameter validation", () => {
      it("should return 400 when workspace slug is missing", async () => {
        mockRequest.params = { threadSlug: "test-thread" };

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockResponse.status).toHaveBeenCalledWith(400);
        expect(mockResponse.send).toHaveBeenCalledWith(
          "Workspace slug is required."
        );
        expect(mockNext).not.toHaveBeenCalled();
      });

      it("should return 400 when thread slug is missing", async () => {
        mockRequest.params = { slug: "test-workspace" };

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockResponse.status).toHaveBeenCalledWith(400);
        expect(mockResponse.send).toHaveBeenCalledWith(
          "Thread slug is required."
        );
        expect(mockNext).not.toHaveBeenCalled();
      });

      it("should validate both slugs are present", async () => {
        mockRequest.params = {
          slug: "test-workspace",
          threadSlug: "test-thread",
        };
        (Workspace.get as jest.Mock).mockResolvedValue(mockWorkspace);
        (WorkspaceThread.get as jest.Mock).mockResolvedValue(mockThread);

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockNext).toHaveBeenCalled();
        expect(mockResponse.locals!.workspace).toEqual(mockWorkspace);
        expect(mockResponse.locals!.thread).toEqual(mockThread);
      });
    });

    describe("Workspace validation (same as validWorkspaceSlug)", () => {
      beforeEach(() => {
        mockRequest.params = {
          slug: "test-workspace",
          threadSlug: "test-thread",
        };
      });

      it("should validate workspace access before checking thread", async () => {
        (Workspace.get as jest.Mock).mockResolvedValue(null);

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockResponse.status).toHaveBeenCalledWith(404);
        expect(mockResponse.send).toHaveBeenCalledWith(
          "Workspace does not exist or you don't have access."
        );
        expect(WorkspaceThread.get).not.toHaveBeenCalled();
      });

      it("should check workspace shared access in multi-user mode", async () => {
        (multiUserMode as jest.Mock).mockImplementation(() => true);
        (userFromSession as jest.Mock).mockResolvedValue(mockUser);
        (Workspace.getWithUser as jest.Mock).mockResolvedValue(null);
        (Workspace.where as jest.Mock).mockResolvedValue([mockWorkspace]);
        (WorkspaceShare.hasAccess as jest.Mock).mockResolvedValue(true);
        (WorkspaceThread.get as jest.Mock).mockResolvedValue(mockThread);

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(WorkspaceShare.hasAccess).toHaveBeenCalledWith(1, 1);
        expect(mockNext).toHaveBeenCalled();
      });
    });

    describe("Thread access - Privileged users", () => {
      beforeEach(() => {
        mockRequest.params = {
          slug: "test-workspace",
          threadSlug: "test-thread",
        };
        (Workspace.get as jest.Mock).mockResolvedValue(mockWorkspace);
      });

      it("should allow access in non-multi-user mode (privileged)", async () => {
        (multiUserMode as jest.Mock).mockReturnValue(false);
        (WorkspaceThread.get as jest.Mock).mockResolvedValue(mockThread);

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(WorkspaceThread.get).toHaveBeenCalledWith({
          slug: "test-thread",
          workspace_id: 1,
        });
        expect(mockNext).toHaveBeenCalled();
      });

      it("should allow admin access to any thread", async () => {
        (multiUserMode as jest.Mock).mockImplementation(() => true);
        (userFromSession as jest.Mock).mockResolvedValue({
          ...mockUser,
          role: UserRole.ADMIN,
        });
        (Workspace.getWithUser as jest.Mock).mockResolvedValue(mockWorkspace);
        (WorkspaceThread.get as jest.Mock).mockResolvedValue(mockThread);

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(WorkspaceThread.get).toHaveBeenCalledWith({
          slug: "test-thread",
          workspace_id: 1,
        });
        expect(mockNext).toHaveBeenCalled();
      });

      it("should allow manager access to any thread", async () => {
        (multiUserMode as jest.Mock).mockImplementation(() => true);
        (userFromSession as jest.Mock).mockResolvedValue({
          ...mockUser,
          role: UserRole.MANAGER,
        });
        (Workspace.getWithUser as jest.Mock).mockResolvedValue(mockWorkspace);
        (WorkspaceThread.get as jest.Mock).mockResolvedValue(mockThread);

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(WorkspaceThread.get).toHaveBeenCalledWith({
          slug: "test-thread",
          workspace_id: 1,
        });
        expect(mockNext).toHaveBeenCalled();
      });

      it("should allow superuser access to any thread", async () => {
        (multiUserMode as jest.Mock).mockImplementation(() => true);
        (userFromSession as jest.Mock).mockResolvedValue({
          ...mockUser,
          role: UserRole.SUPERUSER,
        });
        (Workspace.getWithUser as jest.Mock).mockResolvedValue(mockWorkspace);
        (WorkspaceThread.get as jest.Mock).mockResolvedValue(mockThread);

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(WorkspaceThread.get).toHaveBeenCalledWith({
          slug: "test-thread",
          workspace_id: 1,
        });
        expect(mockNext).toHaveBeenCalled();
      });
    });

    describe("Thread access - Workspace owners", () => {
      beforeEach(() => {
        mockRequest.params = {
          slug: "test-workspace",
          threadSlug: "test-thread",
        };
        (multiUserMode as jest.Mock).mockImplementation(() => true);
      });

      it("should allow workspace owner access to all threads", async () => {
        const ownerUser = { ...mockUser, id: 1 };
        const ownedWorkspace = { ...mockWorkspace, user_id: 1 };

        (userFromSession as jest.Mock).mockResolvedValue(ownerUser);
        (Workspace.getWithUser as jest.Mock).mockResolvedValue(ownedWorkspace);
        (WorkspaceThread.get as jest.Mock).mockResolvedValue(mockThread);

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(WorkspaceThread.get).toHaveBeenCalledWith({
          slug: "test-thread",
          workspace_id: 1,
        });
        expect(mockNext).toHaveBeenCalled();
      });

      it("should handle workspace-level shared access", async () => {
        (userFromSession as jest.Mock).mockResolvedValue(mockUser);
        (Workspace.getWithUser as jest.Mock).mockResolvedValue(mockWorkspace);
        (WorkspaceShare.hasAccess as jest.Mock).mockResolvedValue(true);
        (WorkspaceThread.get as jest.Mock).mockResolvedValue(mockThread);

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(WorkspaceShare.hasAccess).toHaveBeenCalledWith(1, 1);
        expect(WorkspaceThread.get).toHaveBeenCalledWith({
          slug: "test-thread",
          workspace_id: 1,
        });
        expect(mockNext).toHaveBeenCalled();
      });

      it("should handle error in workspace-level access check", async () => {
        const nonOwnerUser = { ...mockUser, id: 2 }; // Different user, not owner
        const nonOwnedWorkspace = { ...mockWorkspace, user_id: 1 }; // Owned by user 1, not 2
        (userFromSession as jest.Mock).mockResolvedValue(nonOwnerUser);
        (Workspace.getWithUser as jest.Mock).mockResolvedValue(
          nonOwnedWorkspace
        );
        (WorkspaceShare.hasAccess as jest.Mock).mockRejectedValue(
          new Error("Access check error")
        );
        const consoleSpy = jest.spyOn(console, "error").mockImplementation();
        (WorkspaceThread.get as jest.Mock).mockResolvedValue(mockThread);

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(consoleSpy).toHaveBeenCalledWith(
          "Failed checking workspace-level access:",
          expect.any(Error)
        );
        expect(WorkspaceThread.get).toHaveBeenCalledWith({
          slug: "test-thread",
          user_id: 2,
        });

        consoleSpy.mockRestore();
      });
    });

    describe("Thread access - Regular users", () => {
      beforeEach(() => {
        mockRequest.params = {
          slug: "test-workspace",
          threadSlug: "test-thread",
        };
        (multiUserMode as jest.Mock).mockImplementation(() => true);
        // Regular user who is NOT the workspace owner
        const regularUser = { ...mockUser, id: 2 };
        const nonOwnedWorkspace = { ...mockWorkspace, user_id: 1 }; // Owned by user 1, not 2
        (userFromSession as jest.Mock).mockResolvedValue(regularUser);
        (Workspace.getWithUser as jest.Mock).mockResolvedValue(
          nonOwnedWorkspace
        );
      });

      it("should check user's direct thread access", async () => {
        (WorkspaceThread.get as jest.Mock).mockResolvedValue(mockThread);

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(WorkspaceThread.get).toHaveBeenCalledWith({
          slug: "test-thread",
          user_id: 2,
        });
        expect(mockNext).toHaveBeenCalled();
      });

      it("should check thread shared access when direct access fails", async () => {
        (WorkspaceThread.get as jest.Mock).mockResolvedValue(null);
        (WorkspaceThread.where as jest.Mock).mockResolvedValue([mockThread]);
        (ThreadShare.hasAccess as jest.Mock).mockResolvedValue(true);

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(WorkspaceThread.where).toHaveBeenCalledWith({
          slug: "test-thread",
          workspace_id: 1,
        });
        expect(ThreadShare.hasAccess).toHaveBeenCalledWith(1, 2);
        expect(mockNext).toHaveBeenCalled();
      });

      it("should deny access when no thread shared access", async () => {
        (WorkspaceThread.get as jest.Mock).mockResolvedValue(null);
        (WorkspaceThread.where as jest.Mock).mockResolvedValue([mockThread]);
        (ThreadShare.hasAccess as jest.Mock).mockResolvedValue(false);

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockResponse.status).toHaveBeenCalledWith(404);
        expect(mockResponse.send).toHaveBeenCalledWith(
          "Workspace thread does not exist or you don't have access."
        );
        expect(mockNext).not.toHaveBeenCalled();
      });

      it("should handle no threads found in where query", async () => {
        (WorkspaceThread.get as jest.Mock).mockResolvedValue(null);
        (WorkspaceThread.where as jest.Mock).mockResolvedValue([]);

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockResponse.status).toHaveBeenCalledWith(404);
        expect(ThreadShare.hasAccess).not.toHaveBeenCalled();
      });

      it("should handle null threads array", async () => {
        (WorkspaceThread.get as jest.Mock).mockResolvedValue(null);
        (WorkspaceThread.where as jest.Mock).mockResolvedValue(null);

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockResponse.status).toHaveBeenCalledWith(404);
        expect(ThreadShare.hasAccess).not.toHaveBeenCalled();
      });

      it("should handle user_id: false when user id is null", async () => {
        (userFromSession as jest.Mock).mockResolvedValue({
          ...mockUser,
          id: null,
        });
        (WorkspaceThread.get as jest.Mock).mockResolvedValue(null);

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(WorkspaceThread.get).toHaveBeenCalledWith({
          slug: "test-thread",
          user_id: 0,
        });
      });
    });

    describe("Security edge cases for threads", () => {
      beforeEach(() => {
        mockRequest.params = {
          slug: "test-workspace",
          threadSlug: "test-thread",
        };
        (Workspace.get as jest.Mock).mockResolvedValue(mockWorkspace);
      });

      it("should handle SQL injection attempts in thread slug", async () => {
        const maliciousSlug = "'; DROP TABLE threads; --";
        mockRequest.params!.threadSlug = maliciousSlug;
        (WorkspaceThread.get as jest.Mock).mockResolvedValue(null);

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(WorkspaceThread.get).toHaveBeenCalledWith(
          expect.objectContaining({ slug: maliciousSlug })
        );
        expect(mockResponse.status).toHaveBeenCalledWith(404);
      });

      it("should handle XSS attempts in thread slug", async () => {
        const xssSlug = "<script>alert('xss')</script>";
        mockRequest.params!.threadSlug = xssSlug;
        (WorkspaceThread.get as jest.Mock).mockResolvedValue(null);

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockResponse.status).toHaveBeenCalledWith(404);
        expect(mockResponse.send).toHaveBeenCalledWith(
          "Workspace thread does not exist or you don't have access."
        );
      });
    });

    describe("Concurrent thread access", () => {
      it("should handle concurrent thread checks", async () => {
        mockRequest.params = {
          slug: "test-workspace",
          threadSlug: "test-thread",
        };
        (multiUserMode as jest.Mock).mockImplementation(() => true);
        (userFromSession as jest.Mock).mockResolvedValue(mockUser);
        (Workspace.getWithUser as jest.Mock).mockResolvedValue(mockWorkspace);

        // Simulate slow queries
        (WorkspaceThread.get as jest.Mock).mockImplementation(
          () => new Promise((resolve) => setTimeout(() => resolve(null), 100))
        );
        (WorkspaceThread.where as jest.Mock).mockImplementation(
          () =>
            new Promise((resolve) =>
              setTimeout(() => resolve([mockThread]), 50)
            )
        );
        (ThreadShare.hasAccess as jest.Mock).mockImplementation(
          () => new Promise((resolve) => setTimeout(() => resolve(true), 50))
        );

        const promise1 = validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );
        const promise2 = validWorkspaceAndThreadSlug(
          { ...mockRequest } as Request,
          { ...mockResponse, locals: {} } as Response,
          jest.fn()
        );

        await Promise.all([promise1, promise2]);

        expect(WorkspaceThread.get).toHaveBeenCalledTimes(2);
        expect(WorkspaceThread.where).toHaveBeenCalledTimes(2);
        expect(ThreadShare.hasAccess).toHaveBeenCalledTimes(2);
      });
    });

    describe("Error handling for threads", () => {
      beforeEach(() => {
        mockRequest.params = {
          slug: "test-workspace",
          threadSlug: "test-thread",
        };
        (Workspace.get as jest.Mock).mockResolvedValue(mockWorkspace);
      });

      it("should handle database errors in WorkspaceThread.get", async () => {
        (WorkspaceThread.get as jest.Mock).mockRejectedValue(
          new Error("Thread database error")
        );

        await expect(
          validWorkspaceAndThreadSlug(
            mockRequest as Request,
            mockResponse as Response,
            mockNext
          )
        ).rejects.toThrow("Thread database error");
      });

      it("should handle errors in ThreadShare.hasAccess", async () => {
        (multiUserMode as jest.Mock).mockImplementation(() => true);
        (userFromSession as jest.Mock).mockResolvedValue(mockUser);
        (Workspace.getWithUser as jest.Mock).mockResolvedValue(mockWorkspace);
        (WorkspaceThread.get as jest.Mock).mockResolvedValue(null);
        (WorkspaceThread.where as jest.Mock).mockResolvedValue([mockThread]);
        (ThreadShare.hasAccess as jest.Mock).mockRejectedValue(
          new Error("Thread access check failed")
        );

        await expect(
          validWorkspaceAndThreadSlug(
            mockRequest as Request,
            mockResponse as Response,
            mockNext
          )
        ).rejects.toThrow("Thread access check failed");
      });
    });

    describe("Edge cases", () => {
      it("should handle undefined includeDocuments", async () => {
        mockRequest.params = { slug: "test-workspace" };
        mockRequest.query = {};
        (Workspace.get as jest.Mock).mockResolvedValue(mockWorkspace);

        await validWorkspaceSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(Workspace.get).toHaveBeenCalledWith(
          { slug: "test-workspace" },
          false
        );
      });

      it("should handle includeDocuments as non-string value", async () => {
        mockRequest.params = { slug: "test-workspace" };
        mockRequest.query = { includeDocuments: true as any };
        (Workspace.get as jest.Mock).mockResolvedValue(mockWorkspace);

        await validWorkspaceSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(Workspace.get).toHaveBeenCalledWith(
          { slug: "test-workspace" },
          true
        );
      });

      it("should handle workspace with no user_id for ownership check", async () => {
        mockRequest.params = {
          slug: "test-workspace",
          threadSlug: "test-thread",
        };
        (multiUserMode as jest.Mock).mockImplementation(() => true);
        (userFromSession as jest.Mock).mockResolvedValue(mockUser);
        const workspaceNoUser = { ...mockWorkspace, user_id: null };
        (Workspace.getWithUser as jest.Mock).mockResolvedValue(workspaceNoUser);
        (WorkspaceThread.get as jest.Mock).mockResolvedValue(mockThread);

        await validWorkspaceAndThreadSlug(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockNext).toHaveBeenCalled();
      });
    });
  });
});
