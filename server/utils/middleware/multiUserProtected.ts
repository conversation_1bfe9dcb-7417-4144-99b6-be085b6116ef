import { Request, Response, NextFunction } from "express";
import SystemSettings from "../../models/systemSettings";
import { userFromSession } from "../http";
import { MiddlewareFunction } from "../../types/auth";
import { AuthenticatedUser, UserRole } from "../../types/shared";

export const ROLES = {
  all: "<all>" as const,
  admin: UserRole.ADMIN,
  manager: UserRole.MANAGER,
  superuser: UserRole.SUPERUSER,
  default: UserRole.DEFAULT,
} as const;

const DEFAULT_ROLES = [ROLES.admin];

// Explicitly check that multi user mode is enabled as well as that the
// requesting user has the appropriate role to modify or call the URL.
export function strictMultiUserRoleValid(
  allowedRoles: (UserRole | typeof ROLES.all)[] = DEFAULT_ROLES
): MiddlewareFunction {
  return async (
    request: Request,
    response: Response,
    next: NextFunction
  ): Promise<void> => {
    // If the access-control is allowable for all - skip validations and continue;
    if (allowedRoles.includes(ROLES.all)) {
      next();
      return;
    }

    const multiUserMode =
      response?.locals?.multiUserMode ??
      (await SystemSettings.isMultiUserMode());
    if (!multiUserMode) {
      response.sendStatus(401);
      return;
    }

    const user =
      response?.locals?.user ?? (await userFromSession(request, response));

    if (!user) {
      response.sendStatus(401);
      return;
    }

    if (allowedRoles.includes(user.role as UserRole)) {
      next();
      return;
    }

    // User is authenticated but doesn't have the required role
    response.sendStatus(403);
  };
}

// Apply role permission checks IF the current system is in multi-user mode.
// This is relevant for routes that are shared between MUM and single-user mode.
// Checks if the requesting user has the appropriate role to modify or call the URL.
export function flexUserRoleValid(
  allowedRoles: (UserRole | typeof ROLES.all)[] = DEFAULT_ROLES
): MiddlewareFunction {
  return async (
    request: Request,
    response: Response,
    next: NextFunction
  ): Promise<void> => {
    // If the access-control is allowable for all - skip validations and continue;
    // It does not matter if multi-user or not.
    if (allowedRoles.includes(ROLES.all)) {
      next();
      return;
    }

    // Bypass if not in multi-user mode
    const multiUserMode =
      response?.locals?.multiUserMode ??
      (await SystemSettings.isMultiUserMode());
    if (!multiUserMode) {
      next();
      return;
    }

    const user =
      response?.locals?.user ?? (await userFromSession(request, response));

    // Debug logging for development
    if (process.env.NODE_ENV === "development") {
      console.log(
        `[DEBUG] Role validation - user: ${!!user}, role: ${user?.role}, allowedRoles: ${JSON.stringify(allowedRoles)}, path: ${request.path}`
      );
    }

    if (!user) {
      response.sendStatus(401);
      return;
    }

    if (allowedRoles.includes(user.role as UserRole)) {
      if (process.env.NODE_ENV === "development") {
        console.log(`[DEBUG] Role validation passed for ${user.role}`);
      }
      next();
      return;
    }

    // User is authenticated but doesn't have the required role
    response.sendStatus(403);
  };
}

// Middleware check on a public route if the instance is in a valid
// multi-user set up.
export const isMultiUserSetup: MiddlewareFunction = async (
  _request: Request,
  response: Response,
  next: NextFunction
): Promise<void> => {
  const multiUserMode = await SystemSettings.isMultiUserMode();
  if (!multiUserMode) {
    response.status(403).json({
      error: "Invalid request",
    });
    return;
  }

  next();
};

// Legal template permissions
export function canManageSystemTemplates(
  user: AuthenticatedUser | null
): boolean {
  return (user?.role ?? 0) === UserRole.ADMIN;
}

export function canManageOrgTemplates(
  user: AuthenticatedUser | null,
  orgId: number
): boolean {
  // Only admins can manage any organization templates; superusers and managers only for their own org
  if (!user) return false;
  if (user.role === UserRole.ADMIN) return true;
  if (
    (user.role === UserRole.MANAGER || user.role === UserRole.SUPERUSER) &&
    user.organizationId === orgId
  ) {
    return true;
  }
  return false;
}

export function canManageUserTemplates(
  user: AuthenticatedUser | null,
  userId: number
): boolean {
  // Admins can manage any user's templates
  if ((user?.role ?? 0) === UserRole.ADMIN) {
    return true;
  }
  return user !== null && user.id === userId;
}

/**
 * Middleware to guard legal template endpoints by scope: 'system', 'org', 'user'
 */
export function legalTemplateScopeGuard(
  scope: "system" | "org" | "user"
): MiddlewareFunction {
  return async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const user =
      res.locals.user ||
      ((await userFromSession(req, res)) as AuthenticatedUser | null);
    let authorized = false;

    if (scope === "system") {
      authorized = canManageSystemTemplates(user);
    } else if (scope === "org") {
      const orgId = Number(req.query.organizationId || req.body.organizationId);
      authorized = canManageOrgTemplates(user, orgId);
    } else if (scope === "user") {
      const userId = req.params.id
        ? Number(req.params.id)
        : (user?.id ?? false) || 0;
      authorized = canManageUserTemplates(user, userId);
    }

    if (!authorized) {
      res.status(403).json({ success: false, error: "Unauthorized" });
      return;
    }
    next();
  };
}
