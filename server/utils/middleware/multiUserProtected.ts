import { Request, Response, NextFunction } from "express";
import SystemSettings from "../../models/systemSettings";
import { userFromSession } from "../http";
import { AuthenticatedUser, UserRole } from "../../types/shared";

export const ROLES = {
  all: "<all>" as const,
  admin: UserRole.ADMIN,
  manager: UserRole.MANAGER,
  superuser: UserRole.SUPERUSER,
  default: UserRole.DEFAULT,
} as const;

export function strictMultiUserRoleValid(
  roles: (UserRole | typeof ROLES.all)[] = [ROLES.admin]
) {
  return async function (req: Request, res: Response, next: NextFunction) {
    if (!roles || roles.length === 0) {
      res.sendStatus(403);
      return;
    }
    if (roles.includes(ROLES.all)) {
      next();
      return;
    }
    const multiUserMode =
      res?.locals?.multiUserMode ?? (await SystemSettings.isMultiUserMode());
    if (!multiUserMode) {
      res.sendStatus(401);
      return;
    }
    const user = res?.locals?.user ?? (await userFromSession(req, res));
    if (!user) {
      res.sendStatus(401);
      return;
    }
    if (!user.role || !roles.includes(user.role as UserRole)) {
      res.sendStatus(403);
      return;
    }
    next();
  };
}

// Apply role permission checks IF the current system is in multi-user mode.
// This is relevant for routes that are shared between MUM and single-user mode.
// Checks if the requesting user has the appropriate role to modify or call the URL.
export function flexUserRoleValid(
  roles: (UserRole | typeof ROLES.all)[] = [ROLES.admin]
) {
  return async function (req: Request, res: Response, next: NextFunction) {
    if (!roles || roles.length === 0) {
      res.sendStatus(403);
      return;
    }

    // Check if we're in multi-user mode before proceeding
    const multiUserMode =
      res?.locals?.multiUserMode ?? (await SystemSettings.isMultiUserMode());

    if (roles.includes(ROLES.all)) {
      // In multi-user mode, ROLES.all means "allow all authenticated users"
      // In single-user mode, ROLES.all means "allow all users"
      if (!multiUserMode) {
        next();
        return;
      }
      // In multi-user mode, we still need to check for authentication
      const user = res?.locals?.user ?? (await userFromSession(req, res));
      if (!user) {
        res.sendStatus(401);
        return;
      }
      next();
      return;
    }
    if (!multiUserMode) {
      next();
      return;
    }
    const user = res?.locals?.user ?? (await userFromSession(req, res));
    if (process.env.NODE_ENV === "development") {
      console.log(
        `[DEBUG] Role validation - user: ${!!user}, role: ${user?.role}, allowedRoles: ${JSON.stringify(roles)}, path: ${req.path}`
      );
    }
    if (!user) {
      res.sendStatus(401);
      return;
    }
    if (!user.role || !roles.includes(user.role as UserRole)) {
      res.sendStatus(403);
      return;
    }
    if (process.env.NODE_ENV === "development") {
      console.log(`[DEBUG] Role validation passed for ${user.role}`);
    }
    next();
  };
}

export function canManageSystemTemplates(
  user: AuthenticatedUser | null
): boolean {
  return !!user && user.role === UserRole.ADMIN;
}

export function canManageOrgTemplates(
  user: AuthenticatedUser | null,
  orgId?: number
): boolean {
  if (!user) return false;
  if (user.role === UserRole.ADMIN) return true;
  if (
    (user.role === UserRole.MANAGER || user.role === UserRole.SUPERUSER) &&
    orgId &&
    user.organizationId === orgId
  )
    return true;
  return false;
}

export function canManageUserTemplates(
  user: AuthenticatedUser | null,
  userId?: number
): boolean {
  if (!user) return false;
  if (user.role === UserRole.ADMIN) return true;
  if (userId && user.id === userId) return true;
  return false;
}

export async function isMultiUserSetup(
  _request: Request,
  response: Response,
  next: NextFunction
): Promise<void> {
  const multiUserMode = await SystemSettings.isMultiUserMode();
  if (!multiUserMode) {
    response.status(403).json({
      error: "Invalid request",
    });
    return;
  }
  next();
}

/**
 * Middleware to guard legal template endpoints by scope: 'system', 'org', 'user'
 */
export function legalTemplateScopeGuard(scope: string) {
  return async function (req: Request, res: Response, next: NextFunction) {
    const user = res.locals.user || (await userFromSession(req, res));
    let authorized = false;

    if (scope === "system") {
      authorized = canManageSystemTemplates(user);
    } else if (scope === "org") {
      const orgId = Number(req.query.organizationId || req.body.organizationId);
      authorized = canManageOrgTemplates(user, orgId);
    } else if (scope === "user") {
      const userId = req.params.id
        ? Number(req.params.id)
        : (user?.id ?? false) || 0;
      authorized = canManageUserTemplates(user, userId);
    }

    if (!authorized) {
      res.status(403).json({ success: false, error: "Unauthorized" });
      return;
    }
    next();
  };
}
