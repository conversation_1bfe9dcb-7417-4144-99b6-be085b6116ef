import { Request, Response, NextFunction } from "express";
import SystemSettings from "../../models/systemSettings";
import { User } from "../../models/user";
import { UserToken } from "../../models/userToken";
import { EncryptionManager } from "../EncryptionManager";
import { decodeJWT } from "../http";
import { MiddlewareFunction } from "../../types/auth";

const EncryptionMgr = new EncryptionManager();

export const validatedRequest: MiddlewareFunction = async (
  request: Request,
  response: Response,
  next: NextFunction
): Promise<void> => {
  const multiUserMode = await SystemSettings.isMultiUserMode();
  response.locals.multiUserMode = multiUserMode;

  if (multiUserMode) {
    return await validateMultiUserRequest(request, response, next);
  }

  // When in development passthrough auth token for ease of development.
  // Or if the user simply did not set an Auth token or JWT Secret
  if (
    process.env.NODE_ENV === "development" ||
    !process.env.AUTH_TOKEN ||
    !process.env.JWT_SECRET
  ) {
    next();
    return;
  }

  if (!process.env.AUTH_TOKEN) {
    response.status(401).json({
      error: "You need to set an AUTH_TOKEN environment variable.",
    });
    return;
  }

  const auth = request.header("Authorization");
  const token = auth ? auth.split(" ")[1] : null;

  if (!token) {
    response.status(401).json({
      error: "No auth token found.",
    });
    return;
  }

  const decoded = decodeJWT(token);
  const { p } = decoded || {};

  if (!p || typeof p !== "string" || !/\w{32}:\w{32}/.test(p)) {
    response.status(401).json({
      error: "Token expired or failed validation.",
    });
    return;
  }

  // Since the blame of this comment we have been encrypting the `p` property of JWTs with the persistent
  // encryptionManager PEM's. This prevents us from storing the `p` unencrypted in the JWT itself, which could
  // be unsafe. As a consequence, existing JWTs with invalid `p` values that do not match the regex
  // in ln:44 will be marked invalid so they can be logged out and forced to log back in and obtain an encrypted token.
  // This kind of methodology only applies to single-user password mode.
  let decryptedP: string | null = null;
  try {
    decryptedP = EncryptionMgr.decrypt(p);
  } catch {
    // Decryption failed, treat as invalid credentials
  }

  if (!decryptedP || decryptedP !== process.env.AUTH_TOKEN) {
    response.status(401).json({
      error: "Invalid auth credentials.",
    });
    return;
  }

  next();
};

async function validateMultiUserRequest(
  request: Request,
  response: Response,
  next: NextFunction
): Promise<void> {
  const auth = request.header("Authorization");
  const token = auth ? auth.split(" ")[1] : null;

  if (!token) {
    response.status(401).json({
      error: "No auth token found.",
    });
    return;
  }

  // First decode the JWT to get user information
  const valid = decodeJWT(token);

  if (!valid || !valid.id) {
    response.status(401).json({
      error: "Invalid auth token.",
    });
    return;
  }

  try {
    // JWT token validation is already done by decodeJWT above
    // which uses JWT.verify to validate signature and expiration
    // Additional security layer: ensure the specific JWT token exists in database
    let dbToken = null;

    if (valid.jti) {
      // New token format with jti - verify specific token exists
      dbToken = await UserToken.findByToken(valid.jti);
      if (!dbToken) {
        response.status(401).json({
          error: "Invalid or expired token.",
        });
        return;
      }
    } else {
      // Legacy token format without jti - fallback to old behavior for backward compatibility
      const userTokens = await UserToken.findByUserId(valid.id);
      if (!userTokens || userTokens.length === 0) {
        response.status(401).json({
          error: "Invalid or expired token.",
        });
        return;
      }
      // Use the most recent token for legacy tokens
      dbToken = userTokens[userTokens.length - 1];
    }

    // Update the token's last used timestamp
    if (dbToken && typeof dbToken.id === "number") {
      await UserToken.updateLastUsed(dbToken.id);
    }

    // Ensure valid.id is a valid number before calling User.get
    const userId = parseInt(String(valid.id));

    if (isNaN(userId)) {
      response.status(401).json({
        error: "Invalid or expired token.",
      });
      return;
    }

    const user = await User.get({ id: userId });

    if (!user) {
      response.status(401).json({
        error: "Invalid or expired token.",
      });
      return;
    }

    if (user.suspended) {
      response.status(401).json({
        error: "Invalid or expired token.",
      });
      return;
    }

    response.locals.user = user;
    next();
  } catch (error) {
    console.error("Database error in validatedRequest:", error);
    response.status(401).json({
      error: "Invalid or expired token.",
    });
    return;
  }
}
