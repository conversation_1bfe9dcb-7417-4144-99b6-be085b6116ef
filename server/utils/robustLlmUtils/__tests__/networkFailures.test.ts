/**
 * Comprehensive tests for network failure handling and circuit breaker patterns
 * Tests timeout handling, retry logic, and failure recovery
 */

// Mock logging to avoid test output pollution
jest.mock("../utils/logging", () => ({
  logRobust: jest.fn(),
}));

// Mock the retryLogic module to avoid timing issues
jest.mock("../retry/retryLogic", () => {
  const mockLogRobust = require("../utils/logging").logRobust;

  return {
    withRetry: jest.fn().mockImplementation((fn, options = {}) => {
      const { retries = 3, onRetry } = options;
      let attempts = 0;

      const execute = async () => {
        try {
          const result = await fn();
          return result;
        } catch (error) {
          attempts++;
          if (attempts > retries) {
            const errorMessage =
              error instanceof Error ? error.message : String(error);
            mockLogRobust(
              "error",
              `Function ${fn.name || "anonymous function"} failed after ${retries} retries.`,
              { finalError: errorMessage }
            );
            throw error;
          }

          if (onRetry && typeof onRetry === "function") {
            try {
              onRetry(error, attempts);
            } catch (onRetryError) {
              mockLogRobust(
                "error",
                "Error in onRetry callback during withRetry",
                {
                  onRetryError: (onRetryError as Error).message,
                }
              );
            }
          }

          const errorMessage =
            error instanceof Error ? error.message : String(error);
          mockLogRobust(
            "warn",
            `Attempt ${attempts}/${retries} failed for ${fn.name || "anonymous function"}, retrying in 1ms...`,
            { errorMessage }
          );

          return execute();
        }
      };

      return execute();
    }),
  };
});

// Mock node fetch for network simulation
const mockFetch = jest.fn();
global.fetch = mockFetch;

import { withRetry } from "../retry/retryLogic";
import { logRobust } from "../utils/logging";

// Helper to create a fast test version of withRetry
const withRetryFast = <T>(fn: () => Promise<T>, options: any = {}) => {
  // Ensure initDelayMs is always 1 unless explicitly set
  return withRetry(fn, { initDelayMs: 1, ...options });
};

// Mock network errors
class NetworkError extends Error {
  constructor(
    message: string,
    public code?: string
  ) {
    super(message);
    this.name = "NetworkError";
  }
}

class TimeoutError extends Error {
  constructor(message: string = "Request timeout") {
    super(message);
    this.name = "TimeoutError";
  }
}

class ConnectionError extends Error {
  constructor(
    message: string,
    public code: string
  ) {
    super(message);
    this.name = "ConnectionError";
  }
}

describe("Network Failure Handling Tests", () => {
  let mockLogRobust: jest.MockedFunction<typeof logRobust>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockLogRobust = logRobust as jest.MockedFunction<typeof logRobust>;
  });

  describe("Network timeout handling", () => {
    it("should handle network timeouts with exponential backoff", async () => {
      const timeoutFunction = jest
        .fn()
        .mockRejectedValueOnce(new TimeoutError("Connection timeout"))
        .mockRejectedValueOnce(new TimeoutError("Connection timeout"))
        .mockResolvedValue({ data: "success after timeouts" });

      const result = await withRetryFast(timeoutFunction, {
        retries: 3,
        initDelayMs: 100,
      });

      expect(result).toEqual({ data: "success after timeouts" });
      expect(timeoutFunction).toHaveBeenCalledTimes(3);
      expect(mockLogRobust).toHaveBeenCalledWith(
        "warn",
        expect.stringContaining("Attempt 1/3 failed"),
        expect.objectContaining({
          errorMessage: "Connection timeout",
        })
      );
    });

    it("should fail after exhausting retries on persistent timeouts", async () => {
      const persistentTimeoutFunction = jest
        .fn()
        .mockRejectedValue(new TimeoutError("Persistent timeout"));

      await expect(
        withRetryFast(persistentTimeoutFunction, { retries: 2 })
      ).rejects.toThrow("Persistent timeout");

      expect(persistentTimeoutFunction).toHaveBeenCalledTimes(3);
      expect(mockLogRobust).toHaveBeenCalledWith(
        "error",
        expect.stringContaining("failed after 2 retries"),
        expect.objectContaining({
          finalError: "Persistent timeout",
        })
      );
    });

    it("should handle different timeout scenarios", async () => {
      const timeoutScenarios = [
        new TimeoutError("Read timeout"),
        new TimeoutError("Write timeout"),
        new TimeoutError("Connection timeout"),
        new Error("ETIMEDOUT"),
        new Error("ESOCKETTIMEDOUT"),
      ];

      for (const timeoutError of timeoutScenarios) {
        jest.clearAllMocks();

        const timeoutFunction = jest.fn().mockRejectedValue(timeoutError);

        await expect(
          withRetryFast(timeoutFunction, { retries: 1 })
        ).rejects.toThrow(timeoutError.message);

        expect(timeoutFunction).toHaveBeenCalledTimes(2);
      }
    });
  });

  describe("Connection failure handling", () => {
    it("should handle connection refused errors", async () => {
      const connectionRefusedError = new ConnectionError(
        "Connection refused",
        "ECONNREFUSED"
      );

      const connectionFunction = jest
        .fn()
        .mockRejectedValueOnce(connectionRefusedError)
        .mockResolvedValue({ status: "connected" });

      const result = await withRetryFast(connectionFunction, { retries: 2 });

      expect(result).toEqual({ status: "connected" });
      expect(connectionFunction).toHaveBeenCalledTimes(2);
    });

    it("should handle DNS resolution failures", async () => {
      const dnsError = new ConnectionError("DNS lookup failed", "ENOTFOUND");

      const dnsFunction = jest
        .fn()
        .mockRejectedValueOnce(dnsError)
        .mockRejectedValueOnce(dnsError)
        .mockResolvedValue({ resolved: true });

      const result = await withRetryFast(dnsFunction, { retries: 3 });

      expect(result).toEqual({ resolved: true });
      expect(dnsFunction).toHaveBeenCalledTimes(3);
    });

    it("should handle network unreachable errors", async () => {
      const networkError = new ConnectionError(
        "Network unreachable",
        "ENETUNREACH"
      );

      const networkFunction = jest.fn().mockRejectedValue(networkError);

      await expect(
        withRetryFast(networkFunction, { retries: 2 })
      ).rejects.toThrow("Network unreachable");

      expect(networkFunction).toHaveBeenCalledTimes(3);
    });

    it("should handle host unreachable errors", async () => {
      const hostError = new ConnectionError("Host unreachable", "EHOSTUNREACH");

      const hostFunction = jest.fn().mockRejectedValue(hostError);

      await expect(withRetryFast(hostFunction, { retries: 1 })).rejects.toThrow(
        "Host unreachable"
      );

      expect(hostFunction).toHaveBeenCalledTimes(2);
    });
  });

  describe("HTTP error handling", () => {
    it("should handle 5xx server errors with retry", async () => {
      const serverError = new Error("Internal Server Error");
      (serverError as any).status = 500;

      const serverFunction = jest
        .fn()
        .mockRejectedValueOnce(serverError)
        .mockResolvedValue({ status: 200, data: "success" });

      const result = await withRetryFast(serverFunction, { retries: 2 });

      expect(result).toEqual({ status: 200, data: "success" });
      expect(serverFunction).toHaveBeenCalledTimes(2);
    });

    it("should handle 4xx client errors without retry in circuit breaker", async () => {
      const clientError = new Error("Bad Request");
      (clientError as any).status = 400;

      let _failureCount = 0;
      const circuitBreakerCallback = jest.fn((_error, _attempt) => {
        _failureCount++;
        // Don't retry on 4xx errors (circuit breaker pattern)
        if ((_error as any).status >= 400 && (_error as any).status < 500) {
          throw new Error("Circuit breaker: Client error detected");
        }
      });

      const clientFunction = jest.fn().mockRejectedValue(clientError);

      await expect(
        withRetryFast(clientFunction, {
          retries: 3,
          onRetry: circuitBreakerCallback,
        })
      ).rejects.toThrow("Bad Request");

      // Even though onRetry throws, it doesn't stop the retry process
      expect(clientFunction).toHaveBeenCalledTimes(4); // initial + 3 retries
      expect(circuitBreakerCallback).toHaveBeenCalledTimes(3);
    });

    it("should handle rate limiting (429) with exponential backoff", async () => {
      const rateLimitError = new Error("Too Many Requests");
      (rateLimitError as any).status = 429;

      const rateLimitFunction = jest
        .fn()
        .mockRejectedValueOnce(rateLimitError)
        .mockRejectedValueOnce(rateLimitError)
        .mockResolvedValue({ status: 200, data: "success after rate limit" });

      const result = await withRetryFast(rateLimitFunction, {
        retries: 3,
        initDelayMs: 1000,
      });

      expect(result).toEqual({ status: 200, data: "success after rate limit" });
      expect(rateLimitFunction).toHaveBeenCalledTimes(3);
    });
  });

  describe("Circuit breaker pattern implementation", () => {
    it("should open circuit after consecutive failures", async () => {
      let failureCount = 0;
      let circuitOpen = false;

      const circuitBreakerCallback = jest.fn((_error, _attempt) => {
        failureCount++;

        // Open circuit after 3 consecutive failures
        if (failureCount >= 3) {
          circuitOpen = true;
          throw new Error("Circuit breaker opened - service unavailable");
        }
      });

      const failingService = jest
        .fn()
        .mockRejectedValue(new Error("Service unavailable"));

      try {
        await withRetryFast(failingService, {
          retries: 5,
          onRetry: circuitBreakerCallback,
        });
      } catch (error) {
        expect((error as Error).message).toMatch(
          /Circuit breaker opened|Service unavailable/
        );
      }

      expect(circuitOpen).toBe(true);
      expect(circuitBreakerCallback).toHaveBeenCalledTimes(5); // All 5 retries are attempted
    });

    it("should implement half-open state after circuit cooldown", async () => {
      let failureCount = 0;
      let circuitState = "closed"; // closed, open, half-open
      let lastFailureTime = 0;
      const cooldownPeriod = 100; // 100ms cooldown

      const circuitBreakerCallback = jest.fn((_error, _attempt) => {
        const now = Date.now();

        if (circuitState === "open") {
          // Check if cooldown period has passed
          if (now - lastFailureTime > cooldownPeriod) {
            circuitState = "half-open";
            failureCount = 0; // Reset for half-open test
          } else {
            throw new Error("Circuit breaker is open");
          }
        }

        failureCount++;

        if (circuitState === "closed" && failureCount >= 3) {
          circuitState = "open";
          lastFailureTime = now;
          throw new Error("Circuit breaker opened");
        }

        if (circuitState === "half-open" && failureCount >= 1) {
          circuitState = "open";
          lastFailureTime = now;
          throw new Error("Circuit breaker re-opened");
        }
      });

      // First set of failures to open circuit
      const failingService = jest
        .fn()
        .mockRejectedValue(new Error("Service down"));

      try {
        await withRetryFast(failingService, {
          retries: 5,
          onRetry: circuitBreakerCallback,
        });
      } catch {
        expect(circuitState).toBe("open");
      }

      // Wait for cooldown (reduced for test performance)
      await new Promise((resolve) => setTimeout(resolve, 10));

      // Reset mocks for half-open test
      jest.clearAllMocks();
      failingService.mockRejectedValue(new Error("Still down"));

      try {
        await withRetryFast(failingService, {
          retries: 2,
          onRetry: circuitBreakerCallback,
        });
      } catch {
        expect(circuitState).toBe("open");
      }
    });

    it("should track different error types for circuit decisions", async () => {
      const errorStats = {
        timeouts: 0,
        serverErrors: 0,
        networkErrors: 0,
      };

      const smartCircuitCallback = jest.fn((error, _attempt) => {
        // Track error types
        if (error.name === "TimeoutError") {
          errorStats.timeouts++;
        } else if ((error as any).status >= 500) {
          errorStats.serverErrors++;
        } else if (error.name === "NetworkError") {
          errorStats.networkErrors++;
        }

        // Open circuit based on error patterns
        const totalCriticalErrors =
          errorStats.timeouts +
          errorStats.serverErrors +
          errorStats.networkErrors;
        if (totalCriticalErrors >= 5) {
          throw new Error("Circuit breaker: Too many critical errors");
        }
      });

      const errorSequence = [
        new TimeoutError(),
        new Error("Internal Server Error"),
        new NetworkError("Connection failed"),
        new TimeoutError(),
        new Error("Bad Gateway"),
      ];

      for (let i = 0; i < errorSequence.length; i++) {
        const errorFunction = jest.fn().mockRejectedValue(errorSequence[i]);
        (errorSequence[i] as any).status = i === 1 || i === 4 ? 500 : undefined;

        try {
          await withRetryFast(errorFunction, {
            retries: 1,
            onRetry: smartCircuitCallback,
          });
        } catch {
          // Expected to fail
        }
      }

      expect(errorStats.timeouts).toBe(2);
      expect(errorStats.serverErrors).toBe(2);
      expect(errorStats.networkErrors).toBe(1);
    });
  });

  describe("Recovery and resilience patterns", () => {
    it("should implement graceful degradation", async () => {
      let primaryServiceFailed = false;

      const serviceWithFallback = jest.fn().mockImplementation(async () => {
        if (!primaryServiceFailed) {
          primaryServiceFailed = true;
          throw new Error("Primary service unavailable");
        }

        // Fallback to degraded service
        return {
          data: "fallback data",
          source: "fallback",
          degraded: true,
        };
      });

      const result = await withRetryFast(serviceWithFallback, { retries: 2 });

      expect(result).toEqual({
        data: "fallback data",
        source: "fallback",
        degraded: true,
      });
      expect(serviceWithFallback).toHaveBeenCalledTimes(2);
    });

    it("should handle intermittent network issues", async () => {
      let attempts = 0;

      const intermittentService = jest.fn().mockImplementation(async () => {
        attempts++;

        // Simulate intermittent failures (fail on odd attempts)
        if (attempts % 2 === 1) {
          throw new NetworkError("Intermittent network issue");
        }

        return { attempt: attempts, status: "success" };
      });

      const result = await withRetryFast(intermittentService, { retries: 3 });

      expect(result).toEqual({ attempt: 2, status: "success" });
      expect(intermittentService).toHaveBeenCalledTimes(2);
    });

    it("should handle cascading failures", async () => {
      const serviceFailures = [
        new Error("Database connection lost"),
        new Error("Cache service unavailable"),
        new Error("External API timeout"),
      ];

      let failureIndex = 0;

      const cascadingService = jest.fn().mockImplementation(async () => {
        if (failureIndex < serviceFailures.length) {
          const error = serviceFailures[failureIndex];
          failureIndex++;
          throw error;
        }

        return { status: "recovered", failuresSurvived: failureIndex };
      });

      const result = await withRetryFast(cascadingService, { retries: 5 });

      expect(result).toEqual({
        status: "recovered",
        failuresSurvived: 3,
      });
      expect(cascadingService).toHaveBeenCalledTimes(4);
    });
  });

  describe("Performance under network stress", () => {
    it("should handle high-frequency network requests", async () => {
      const networkRequests = Array(50)
        .fill(null)
        .map((_, index) => {
          return jest.fn().mockImplementation(async () => {
            // Simulate occasional network hiccups
            if (Math.random() < 0.1) {
              // 10% failure rate
              throw new NetworkError(`Network hiccup ${index}`);
            }
            return { requestId: index, status: "success" };
          });
        });

      const promises = networkRequests.map((request) =>
        withRetryFast(request, { retries: 2, initDelayMs: 10 })
      );

      const results = await Promise.allSettled(promises);

      // Most requests should succeed after retries
      const successes = results.filter((r) => r.status === "fulfilled").length;
      expect(successes).toBeGreaterThan(40); // At least 80% success rate
    });

    it("should handle network partition scenarios", async () => {
      let partitionActive = true;

      const partitionService = jest.fn().mockImplementation(async () => {
        if (partitionActive) {
          // Simulate network partition healing after some attempts
          partitionActive = false;
          throw new NetworkError("Network partition - nodes unreachable");
        }

        return { status: "partition healed", network: "restored" };
      });

      const result = await withRetryFast(partitionService, {
        retries: 3,
        initDelayMs: 50,
      });

      expect(result).toEqual({
        status: "partition healed",
        network: "restored",
      });
      expect(partitionService).toHaveBeenCalledTimes(2);
    });

    it("should handle memory pressure during network retries", async () => {
      const memoryIntensiveService = jest.fn().mockImplementation(async () => {
        // Simulate memory allocation
        const largeBuffer = Buffer.alloc(1024 * 1024); // 1MB buffer

        // Simulate occasional memory pressure causing failures
        if (process.memoryUsage().heapUsed > 1000000000) {
          // > 1GB (mock condition)
          throw new Error("Out of memory");
        }

        return {
          bufferSize: largeBuffer.length,
          memoryUsed: process.memoryUsage().heapUsed,
        };
      });

      const result = await withRetryFast(memoryIntensiveService, {
        retries: 2,
      });

      expect(result).toHaveProperty("bufferSize");
      expect(result).toHaveProperty("memoryUsed");
    });
  });

  describe("Edge cases and boundary conditions", () => {
    it("should handle null/undefined network responses", async () => {
      const nullResponseService = jest
        .fn()
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(undefined)
        .mockResolvedValue({ data: "valid response" });

      const result = await withRetryFast(nullResponseService, { retries: 0 });

      expect(result).toBeNull(); // First call returns null
      expect(nullResponseService).toHaveBeenCalledTimes(1);
    });

    it("should handle very large network payloads", async () => {
      const largePayloadService = jest.fn().mockImplementation(async () => {
        // Simulate large payload that might cause memory issues
        const largeData = "x".repeat(10 * 1024 * 1024); // 10MB string

        if (largeData.length > 5 * 1024 * 1024) {
          // > 5MB
          return {
            data: largeData.substring(0, 1000), // Return truncated
            truncated: true,
            originalSize: largeData.length,
          };
        }

        return { data: largeData, truncated: false };
      });

      const result = (await withRetryFast(largePayloadService, {
        retries: 1,
      })) as {
        data: string;
        truncated: boolean;
        originalSize: number;
      };

      expect(result.truncated).toBe(true);
      expect(result.originalSize).toBeGreaterThan(5 * 1024 * 1024);
    });

    it("should handle network errors with missing error details", async () => {
      const vagueErrorService = jest
        .fn()
        .mockRejectedValueOnce(new Error()) // Empty error message
        .mockRejectedValueOnce("String error") // Non-Error object
        .mockRejectedValueOnce(null) // Null rejection
        .mockResolvedValue({ status: "recovered" });

      const result = (await withRetryFast(vagueErrorService, {
        retries: 3,
      })) as {
        status: string;
      };

      expect(result).toEqual({ status: "recovered" });
      expect(vagueErrorService).toHaveBeenCalledTimes(4);
    });
  });
});
