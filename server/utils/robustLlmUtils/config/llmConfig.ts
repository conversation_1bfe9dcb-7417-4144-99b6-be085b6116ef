/**
 * LLM configuration utilities for Robust LLM Utils
 */

import { logRobust } from "../utils/logging";

/**
 * LLM configuration object interface
 */
export interface LLMConfig {
  provider: string;
  settings_suffix?: string;
  apiKey?: string;
  model?: string;
  modelName?: string;
  basePath?: string;
  tokenLimit?: string;
  safetySetting?: string;
}

/**
 * Helper to get a specific config value, prioritizing suffixed env vars
 * @param baseKey - Base environment variable key
 * @param suffixForEnv - Suffix for environment variable
 * @param fallbackEnvKey - Fallback environment variable key
 * @returns Configuration value
 */
export function getConfigValue(
  baseKey: string,
  suffixForEnv?: string,
  fallbackEnvKey?: string
): string | undefined {
  if (suffixForEnv) {
    const suffixedValue = process.env[`${baseKey}${suffixForEnv}`];
    if (suffixedValue !== undefined) return suffixedValue;
  }
  if (fallbackEnvKey) {
    const fallbackValue = process.env[fallbackEnvKey];
    if (fallbackValue !== undefined) return fallbackValue;
  }
  return process.env[baseKey]; // General fallback
}

/**
 * Retrieves the active LLM configuration.
 * Uses environment variables, prioritizing those with a suffix if provided (e.g., LLM_PROVIDER_CDB, GEMINI_API_KEY_CDB).
 * Falls back to general environment variables if suffixed ones are not found.
 * @param settings_suffix - Optional suffix for environment variable names (e.g., '_CDB', '_DD').
 * @returns LLM configuration object (e.g., { provider, apiKey, model, modelName, settings_suffix, basePath }).
 */
export function getActiveLLMConfig(settings_suffix?: string): LLMConfig {
  const suffix = settings_suffix || "";
  // Determine provider: try suffixed, then general, then default to 'gemini' (as seen in user's .env)
  const provider = (
    process.env[`LLM_PROVIDER${suffix}`] ||
    process.env.LLM_PROVIDER ||
    "gemini"
  ).toLowerCase();

  const config: LLMConfig = { provider, settings_suffix: suffix };

  logRobust(
    "info",
    `Fetching LLM config for provider: ${provider}${suffix ? ` with suffix: ${suffix}` : ""}`
  );

  switch (provider) {
    case "openai":
      config.apiKey =
        process.env[`OPEN_AI_KEY${suffix}`] || process.env.OPEN_AI_KEY;
      config.model =
        process.env[`OPEN_AI_MODEL_PREF${suffix}`] ||
        process.env.OPEN_AI_MODEL_PREF ||
        "gpt-4o";
      config.modelName = config.model;
      // OpenAI might also have a base path for proxies or specific deployments
      config.basePath =
        process.env[`OPEN_AI_BASE_PATH${suffix}`] ||
        process.env.OPEN_AI_BASE_PATH;
      break;
    case "gemini":
      config.apiKey =
        process.env[`GEMINI_API_KEY${suffix}`] || process.env.GEMINI_API_KEY;
      config.model =
        process.env[`GEMINI_LLM_MODEL_PREF${suffix}`] ||
        process.env.GEMINI_LLM_MODEL_PREF ||
        "gemini-2.5-flash";
      config.modelName = config.model;
      // Gemini specific settings, e.g., safety from your .env
      config.safetySetting =
        process.env[`GEMINI_SAFETY_SETTING${suffix}`] ||
        process.env.GEMINI_SAFETY_SETTING ||
        process.env[`LLM_SAFETY_LEVEL${suffix}`] ||
        process.env.LLM_SAFETY_LEVEL;
      break;
    case "azure": // Alias for azureopenai
    case "azureopenai":
      config.apiKey =
        process.env[`AZURE_OPENAI_KEY${suffix}`] ||
        process.env.AZURE_OPENAI_KEY;
      config.model =
        process.env[`OPEN_AI_MODEL_PREF${suffix}`] ||
        process.env.OPEN_AI_MODEL_PREF; // Azure uses OPEN_AI_MODEL_PREF for deployment name
      config.modelName = config.model;
      config.basePath =
        process.env[`AZURE_OPENAI_ENDPOINT${suffix}`] ||
        process.env.AZURE_OPENAI_ENDPOINT;
      // Note: Azure also has EMBEDDING_MODEL_PREF, but that's for the embedder, not LLM directly for chat.
      break;
    case "anthropic":
      config.apiKey =
        process.env[`ANTHROPIC_API_KEY${suffix}`] ||
        process.env.ANTHROPIC_API_KEY;
      config.model =
        process.env[`ANTHROPIC_MODEL_PREF${suffix}`] ||
        process.env.ANTHROPIC_MODEL_PREF ||
        "claude-3-opus-20240229";
      config.modelName = config.model;
      break;
    case "localai":
      config.apiKey =
        process.env[`LOCAL_AI_API_KEY${suffix}`] ||
        process.env.LOCAL_AI_API_KEY; // May or may not be required
      config.model =
        process.env[`LOCAL_AI_MODEL_PREF${suffix}`] ||
        process.env.LOCAL_AI_MODEL_PREF;
      config.modelName = config.model;
      config.basePath =
        process.env[`LOCAL_AI_BASE_PATH${suffix}`] ||
        process.env.LOCAL_AI_BASE_PATH;
      config.tokenLimit =
        process.env[`LOCAL_AI_MODEL_TOKEN_LIMIT${suffix}`] ||
        process.env.LOCAL_AI_MODEL_TOKEN_LIMIT;
      break;
    case "ollama":
      config.model =
        process.env[`OLLAMA_MODEL_PREF${suffix}`] ||
        process.env.OLLAMA_MODEL_PREF;
      config.modelName = config.model;
      config.basePath =
        process.env[`OLLAMA_BASE_PATH${suffix}`] ||
        process.env.OLLAMA_BASE_PATH;
      config.tokenLimit =
        process.env[`OLLAMA_MODEL_TOKEN_LIMIT${suffix}`] ||
        process.env.OLLAMA_MODEL_TOKEN_LIMIT;
      // Ollama usually doesn't require an API key.
      break;
    // Add other providers here, following the pattern of:
    // 1. Check for {VAR_NAME}{suffix}
    // 2. Fallback to {VAR_NAME}
    // 3. Fallback to a reasonable default if applicable
    default:
      logRobust(
        "warn",
        `LLM provider '${provider}' not explicitly configured in getActiveLLMConfig. API key, model, and other settings might be missing or incorrect.`
      );
      // Attempt to grab a generic API key and model if LLM_PROVIDER was set but not handled by a case
      config.apiKey = process.env.API_KEY; // A very generic fallback
      config.model = "default";
      config.modelName = "default";
  }

  // General check for API key if the provider isn't one known to work without it (e.g., ollama, potentially localai)
  const providersWithoutApiKey = ["ollama", "localai", "lmstudio"]; // lmstudio added as it might work without a key
  if (!config.apiKey && !providersWithoutApiKey.includes(provider)) {
    logRobust(
      "warn",
      `API key for LLM provider '${provider}'${suffix ? ` with suffix '${suffix}'` : ""} is missing or not found. This might cause issues.`
    );
    // Not throwing an error to allow flexibility, but logging a clear warning.
  }

  logRobust(
    "info",
    `Retrieved LLM config for ${provider}${suffix ? ` (suffix: ${suffix})` : ""}`,
    { ...config }
  );
  return config;
}
