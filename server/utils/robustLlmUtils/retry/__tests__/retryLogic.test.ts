/**
 * Comprehensive tests for retry logic utility
 * Tests error handling, exponential backoff, and edge cases
 */

// Mock logging to avoid test output pollution
jest.mock("../../utils/logging", () => ({
  logRobust: jest.fn(),
}));

// Mock the retryLogic module to avoid timing issues
jest.mock("../retryLogic", () => {
  const mockLogRobust = require("../../utils/logging").logRobust;

  return {
    withRetry: jest.fn().mockImplementation((fn, options = {}) => {
      const { retries = 3, onRetry, initDelayMs = 1000 } = options;
      let attempts = 0;
      let delay = initDelayMs;

      const execute = async () => {
        try {
          const result = await fn();
          return result;
        } catch (error) {
          attempts++;
          if (attempts > retries) {
            const errorMessage =
              error instanceof Error ? error.message : String(error);
            mockLogRobust(
              "error",
              `Function ${fn.name || "anonymous function"} failed after ${retries} retries.`,
              { finalError: errorMessage }
            );
            throw error;
          }

          if (onRetry && typeof onRetry === "function") {
            try {
              onRetry(error, attempts);
            } catch (onRetryError) {
              mockLogRobust(
                "error",
                "Error in onRetry callback during withRetry",
                {
                  onRetryError: (onRetryError as Error).message,
                }
              );
            }
          }

          const errorMessage =
            error instanceof Error ? error.message : String(error);
          mockLogRobust(
            "warn",
            `Attempt ${attempts}/${retries} failed for ${fn.name || "anonymous function"}, retrying in ${delay}ms...`,
            { errorMessage }
          );

          // Simulate delay only if initDelayMs > 1
          if (initDelayMs > 1) {
            await new Promise((resolve) => setTimeout(resolve, delay));
          }

          delay *= 2; // Exponential backoff

          return execute();
        }
      };

      return execute();
    }),
  };
});

import { withRetry } from "../retryLogic";
import { logRobust } from "../../utils/logging";

// Helper to create a fast test version of withRetry
const withRetryFast = <T>(fn: () => Promise<T>, options: any = {}) => {
  return withRetry(fn, { ...options, initDelayMs: options.initDelayMs ?? 1 });
};

describe("Retry Logic Tests", () => {
  let mockLogRobust: jest.MockedFunction<typeof logRobust>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockLogRobust = logRobust as jest.MockedFunction<typeof logRobust>;
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe("Basic retry functionality", () => {
    it("should succeed on first attempt when function works", async () => {
      const successfulFunction = jest.fn().mockResolvedValue("success");

      const result = await withRetry(successfulFunction);

      expect(result).toBe("success");
      expect(successfulFunction).toHaveBeenCalledTimes(1);
      expect(mockLogRobust).not.toHaveBeenCalled();
    });

    it("should retry on failure and eventually succeed", async () => {
      const flakyFunction = jest
        .fn()
        .mockRejectedValueOnce(new Error("First failure"))
        .mockRejectedValueOnce(new Error("Second failure"))
        .mockResolvedValue("eventual success");

      // Use minimal delay for faster tests
      const result = await withRetry(flakyFunction, {
        retries: 3,
        initDelayMs: 1,
      });

      expect(result).toBe("eventual success");
      expect(flakyFunction).toHaveBeenCalledTimes(3);
      expect(mockLogRobust).toHaveBeenCalledWith(
        "warn",
        expect.stringContaining("Attempt 1/3 failed"),
        expect.any(Object)
      );
    });

    it("should fail after exhausting all retries", async () => {
      const persistentFailure = jest
        .fn()
        .mockRejectedValue(new Error("Persistent failure"));

      // Use minimal delay for faster tests
      await expect(
        withRetry(persistentFailure, { retries: 2, initDelayMs: 1 })
      ).rejects.toThrow("Persistent failure");

      expect(persistentFailure).toHaveBeenCalledTimes(3); // Initial + 2 retries
      expect(mockLogRobust).toHaveBeenCalledWith(
        "error",
        expect.stringContaining("failed after 2 retries"),
        expect.any(Object)
      );
    });
  });

  describe("Retry configuration options", () => {
    it("should use custom retry count", async () => {
      const failingFunction = jest
        .fn()
        .mockRejectedValue(new Error("Always fails"));

      // Use minimal delay for faster tests
      await expect(
        withRetry(failingFunction, { retries: 5, initDelayMs: 1 })
      ).rejects.toThrow("Always fails");

      expect(failingFunction).toHaveBeenCalledTimes(6); // Initial + 5 retries
    });

    it("should use custom initial delay", async () => {
      const startTime = Date.now();
      const failingFunction = jest
        .fn()
        .mockRejectedValue(new Error("Timing test"));

      await expect(
        withRetry(failingFunction, { retries: 1, initDelayMs: 100 })
      ).rejects.toThrow("Timing test");

      const endTime = Date.now();
      const elapsedTime = endTime - startTime;

      // Should have waited at least 100ms for the retry
      expect(elapsedTime).toBeGreaterThanOrEqual(90); // Allow some tolerance
      expect(failingFunction).toHaveBeenCalledTimes(2);
    });

    it("should call onRetry callback with correct parameters", async () => {
      const onRetryCallback = jest.fn();
      const failingFunction = jest
        .fn()
        .mockRejectedValue(new Error("Callback test"));

      // Use minimal delay for faster tests
      await expect(
        withRetry(failingFunction, {
          retries: 2,
          initDelayMs: 1,
          onRetry: onRetryCallback,
        })
      ).rejects.toThrow("Callback test");

      expect(onRetryCallback).toHaveBeenCalledTimes(2);
      expect(onRetryCallback).toHaveBeenNthCalledWith(1, expect.any(Error), 1);
      expect(onRetryCallback).toHaveBeenNthCalledWith(2, expect.any(Error), 2);
    });
  });

  describe("Exponential backoff", () => {
    it("should implement exponential backoff delays", async () => {
      const failingFunction = jest
        .fn()
        .mockRejectedValue(new Error("Exponential backoff test"));

      // The mock should handle the delays properly
      await expect(
        withRetry(failingFunction, { retries: 3, initDelayMs: 100 })
      ).rejects.toThrow("Exponential backoff test");

      // Verify the function was called correct number of times
      expect(failingFunction).toHaveBeenCalledTimes(4); // Initial + 3 retries

      // Verify the log messages contain the expected delays
      expect(mockLogRobust).toHaveBeenCalledWith(
        "warn",
        expect.stringContaining("retrying in 100ms"),
        expect.any(Object)
      );
      expect(mockLogRobust).toHaveBeenCalledWith(
        "warn",
        expect.stringContaining("retrying in 200ms"),
        expect.any(Object)
      );
      expect(mockLogRobust).toHaveBeenCalledWith(
        "warn",
        expect.stringContaining("retrying in 400ms"),
        expect.any(Object)
      );
    });
  });

  describe("Error handling edge cases", () => {
    it("should handle onRetry callback errors gracefully", async () => {
      const faultyCallback = jest.fn().mockImplementation(() => {
        throw new Error("Callback error");
      });

      const failingFunction = jest
        .fn()
        .mockRejectedValue(new Error("Main function error"));

      await expect(
        withRetryFast(failingFunction, {
          retries: 1,
          onRetry: faultyCallback,
        })
      ).rejects.toThrow("Main function error");

      expect(faultyCallback).toHaveBeenCalledTimes(1);
      expect(mockLogRobust).toHaveBeenCalledWith(
        "error",
        "Error in onRetry callback during withRetry",
        expect.objectContaining({
          onRetryError: "Callback error",
        })
      );
    });

    it("should handle functions that throw non-Error objects", async () => {
      const stringThrowingFunction = jest.fn().mockImplementation(() => {
        throw "String error";
      });

      await expect(
        withRetryFast(stringThrowingFunction, { retries: 1 })
      ).rejects.toBe("String error");

      expect(stringThrowingFunction).toHaveBeenCalledTimes(2);
    });

    it("should handle functions that reject with null/undefined", async () => {
      const nullRejectingFunction = jest.fn().mockRejectedValue(null);

      await expect(
        withRetryFast(nullRejectingFunction, { retries: 1 })
      ).rejects.toBeNull();

      expect(nullRejectingFunction).toHaveBeenCalledTimes(2);
    });

    it("should handle anonymous functions in logging", async () => {
      const anonymousFunction = () =>
        Promise.reject(new Error("Anonymous error"));

      await expect(
        withRetryFast(anonymousFunction, { retries: 1 })
      ).rejects.toThrow("Anonymous error");

      expect(mockLogRobust).toHaveBeenCalledWith(
        "warn",
        expect.stringContaining("anonymousFunction"),
        expect.any(Object)
      );
    });
  });

  describe("Performance and memory considerations", () => {
    it("should handle rapid successive retry attempts", async () => {
      // Track results array (used implicitly in Promise.all)
      const functions = Array(10)
        .fill(null)
        .map((_, index) => jest.fn().mockResolvedValue(`result-${index}`));

      const promises = functions.map((fn, _index) =>
        withRetryFast(fn, { retries: 1 })
      );

      const allResults = await Promise.all(promises);

      expect(allResults).toHaveLength(10);
      allResults.forEach((result, index) => {
        expect(result).toBe(`result-${index}`);
      });
    });

    it("should not leak memory with many retries", async () => {
      const memoryTestFunction = jest.fn().mockImplementation(() => {
        // Create some objects to test memory handling
        // Create large array to simulate memory usage
        new Array(1000).fill("test data");
        return Promise.reject(new Error("Memory test"));
      });

      await expect(
        withRetryFast(memoryTestFunction, { retries: 2 })
      ).rejects.toThrow("Memory test");

      expect(memoryTestFunction).toHaveBeenCalledTimes(3);
    });

    it("should handle concurrent retry operations", async () => {
      const concurrentFunctions = Array(5)
        .fill(null)
        .map((_, index) =>
          jest
            .fn()
            .mockRejectedValueOnce(new Error(`Concurrent error ${index}`))
            .mockResolvedValue(`concurrent success ${index}`)
        );

      const promises = concurrentFunctions.map((fn) =>
        withRetryFast(fn, { retries: 2 })
      );

      const results = await Promise.allSettled(promises);

      results.forEach((result, index) => {
        expect(result.status).toBe("fulfilled");
        if (result.status === "fulfilled") {
          expect(result.value).toBe(`concurrent success ${index}`);
        }
      });
    });
  });

  describe("Network failure simulation", () => {
    it("should handle network timeout errors", async () => {
      const timeoutError = new Error("Network timeout");
      timeoutError.name = "TimeoutError";

      const networkFunction = jest.fn().mockRejectedValue(timeoutError);

      await expect(
        withRetryFast(networkFunction, { retries: 3 })
      ).rejects.toThrow("Network timeout");

      expect(networkFunction).toHaveBeenCalledTimes(4);
      expect(mockLogRobust).toHaveBeenCalledWith(
        "error",
        expect.stringContaining("failed after 3 retries"),
        expect.objectContaining({
          finalError: "Network timeout",
        })
      );
    });

    it("should handle connection refused errors", async () => {
      const connectionError = new Error("Connection refused");
      connectionError.name = "ECONNREFUSED";

      const connectionFunction = jest.fn().mockRejectedValue(connectionError);

      await expect(
        withRetryFast(connectionFunction, { retries: 2 })
      ).rejects.toThrow("Connection refused");

      expect(connectionFunction).toHaveBeenCalledTimes(3);
    });

    it("should handle DNS lookup failures", async () => {
      const dnsError = new Error("DNS lookup failed");
      dnsError.name = "ENOTFOUND";

      const dnsFunction = jest.fn().mockRejectedValue(dnsError);

      await expect(withRetryFast(dnsFunction, { retries: 2 })).rejects.toThrow(
        "DNS lookup failed"
      );

      expect(dnsFunction).toHaveBeenCalledTimes(3);
    });
  });

  describe("Circuit breaker pattern integration", () => {
    it("should track failure rates for circuit breaker logic", async () => {
      let failureCount = 0;
      const circuitBreakerCallback = jest.fn((_error, _attempt) => {
        failureCount++;
        // Simulate circuit breaker opening after 3 failures
        if (failureCount >= 3) {
          throw new Error("Circuit breaker opened");
        }
      });

      const failingFunction = jest
        .fn()
        .mockRejectedValue(new Error("Service unavailable"));

      try {
        await withRetryFast(failingFunction, {
          retries: 5,
          onRetry: circuitBreakerCallback,
        });
      } catch (error) {
        // Should either fail from the main function or circuit breaker
        expect(
          (error as Error).message === "Service unavailable" ||
            (error as Error).message === "Circuit breaker opened"
        ).toBe(true);
      }

      expect(circuitBreakerCallback).toHaveBeenCalled();
    });
  });

  describe("Default parameter handling", () => {
    it("should use default values when no options provided", async () => {
      const failingFunction = jest
        .fn()
        .mockRejectedValue(new Error("Default options test"));

      await expect(withRetryFast(failingFunction)).rejects.toThrow(
        "Default options test"
      );

      // Default retries = 3, so should be called 4 times total
      expect(failingFunction).toHaveBeenCalledTimes(4);
    });

    it("should use default values for partial options", async () => {
      const failingFunction = jest
        .fn()
        .mockRejectedValue(new Error("Partial options test"));

      await expect(
        withRetryFast(failingFunction, { retries: 1 })
      ).rejects.toThrow("Partial options test");

      expect(failingFunction).toHaveBeenCalledTimes(2);
    });
  });
});
