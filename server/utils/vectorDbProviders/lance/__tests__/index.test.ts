/**
 * LanceDB Vector Database Provider Test Suite
 *
 * Tests comprehensive LanceDB operations including:
 * - Connection management and heartbeat
 * - Namespace operations (create, delete, exists)
 * - Document operations (add, delete, vectorization)
 * - Similarity search (standard and reranked)
 * - Vector operations and adjacent retrieval
 * - Error handling and edge cases
 * - Performance and memory management
 */

import {
  describe,
  expect,
  it,
  beforeEach,
  afterEach,
  jest,
} from "@jest/globals";
import { LanceDb } from "../index";
import { VectorDbProvider, VectorDbError } from "../../../../types/vectorDb";

// Type definitions for mocks
interface MockLLMConnector {
  embedTextInput: jest.MockedFunction<(input: string) => Promise<number[]>>;
}

interface MockTable {
  vectorSearch: jest.MockedFunction<(vector: number[]) => MockVectorSearch>;
  distanceType: jest.MockedFunction<(type: string) => MockTable>;
  limit: jest.MockedFunction<(count: number) => MockTable>;
  toArray: jest.MockedFunction<() => Promise<Record<string, unknown>[]>>;
  add: jest.MockedFunction<(data: Record<string, unknown>[]) => Promise<void>>;
  countRows: jest.MockedFunction<() => Promise<number>>;
  delete: jest.MockedFunction<(condition: string) => Promise<void>>;
  query: jest.MockedFunction<() => MockTable>;
  where: jest.MockedFunction<(condition: string) => MockTable>;
  select: jest.MockedFunction<(fields: string[]) => MockTable>;
  schema: jest.MockedFunction<() => Promise<{ fields: { name: string }[] }>>;
  [Symbol.asyncIterator]: jest.MockedFunction<
    () => AsyncIterableIterator<Record<string, unknown>>
  >;
}

interface MockVectorSearch {
  distanceType: jest.MockedFunction<(type: string) => MockVectorSearch>;
  limit: jest.MockedFunction<(count: number) => MockVectorSearch>;
  toArray: jest.MockedFunction<() => Promise<Record<string, unknown>[]>>;
}

interface MockClient {
  tableNames: jest.MockedFunction<() => Promise<string[]>>;
  openTable: jest.MockedFunction<(name: string) => Promise<MockTable>>;
  createTable: jest.MockedFunction<
    (name: string, data: Record<string, unknown>[]) => Promise<MockTable>
  >;
  dropTable: jest.MockedFunction<(name: string) => Promise<void>>;
  uri: string;
  isOpen: jest.MockedFunction<() => boolean>;
  close: jest.MockedFunction<() => Promise<void>>;
  display: jest.MockedFunction<() => string>;
  createEmptyTable: jest.MockedFunction<
    (name: string, schema: Record<string, unknown>) => Promise<MockTable>
  >;
}

// Mock all external dependencies BEFORE imports
jest.mock("@lancedb/lancedb", () => ({
  connect: jest.fn(),
}));
jest.mock("uuid", () => ({
  v4: jest.fn().mockImplementation(() => "test-uuid-1234"),
}));
jest.mock("fs", () => ({
  rm: jest.fn(),
}));
jest.mock("path", () => ({
  basename: jest.fn((filePath: string) =>
    typeof filePath === "string" ? (filePath.split("/").pop() ?? "") : ""
  ) as (filePath: string) => string,
}));

// Mock models and utilities
jest.mock("../../../../models/systemSettings", () => ({
  __esModule: true,
  default: {
    getValueOrFallback: jest.fn(),
    get: jest.fn(),
  },
}));

jest.mock("../../../../models/vectors", () => ({
  DocumentVectors: {
    where: jest.fn(),
    bulkInsert: jest.fn(),
  },
}));

jest.mock("../../../files", () => ({
  cachedVectorInformation: jest.fn(),
}));

jest.mock("../../../chats", () => ({
  sourceIdentifier: jest
    .fn()
    .mockImplementation((item: any) => item.id || "test-id"),
}));

jest.mock("../../../EmbeddingRerankers/native", () => {
  const mockRerank = jest.fn();
  return {
    NativeEmbeddingReranker: jest.fn().mockImplementation(() => ({
      rerank: mockRerank,
    })),
    getMockRerank: () => mockRerank,
  };
});

jest.mock("../../../helpers/tiktoken", () => ({
  TokenManager: jest.fn().mockImplementation(() => ({
    countFromString: jest.fn().mockReturnValue(10),
  })),
}));

describe("LanceDB Vector Database Provider", () => {
  let mockClient: MockClient;
  let mockTable: MockTable;
  let mockLancedb: { connect: jest.MockedFunction<() => Promise<MockClient>> };
  let originalEnv: typeof process.env;

  beforeEach(() => {
    originalEnv = { ...process.env };

    // Clear process.env and set test values
    process.env = {
      ...originalEnv,
      VECTOR_DB: "lancedb",
      STORAGE_DIR: "/tmp/test-storage",
    };

    // Mock table operations
    const mockVectorSearch = {
      distanceType: jest
        .fn<(type: string) => MockVectorSearch>()
        .mockReturnThis(),
      limit: jest.fn<(count: number) => MockVectorSearch>().mockReturnThis(),
      toArray: jest
        .fn<() => Promise<Record<string, unknown>[]>>()
        .mockResolvedValue([]),
    } as unknown as MockVectorSearch;

    mockTable = {
      vectorSearch: jest
        .fn<(vector: number[]) => MockVectorSearch>()
        .mockReturnValue(mockVectorSearch),
      distanceType: jest.fn<(type: string) => MockTable>().mockReturnThis(),
      limit: jest.fn<(count: number) => MockTable>().mockReturnThis(),
      toArray: jest
        .fn<() => Promise<Record<string, unknown>[]>>()
        .mockResolvedValue([]),
      add: jest
        .fn<(data: Record<string, unknown>[]) => Promise<void>>()
        .mockResolvedValue(undefined),
      countRows: jest.fn<() => Promise<number>>().mockResolvedValue(0),
      delete: jest
        .fn<(condition: string) => Promise<void>>()
        .mockResolvedValue(undefined),
      query: jest.fn<() => MockTable>().mockReturnThis(),
      where: jest.fn<(condition: string) => MockTable>().mockReturnThis(),
      select: jest.fn<(fields: string[]) => MockTable>().mockReturnThis(),
      schema: jest
        .fn<() => Promise<{ fields: { name: string }[] }>>()
        .mockResolvedValue({
          fields: [{ name: "docId" }, { name: "text" }],
        }),
      [Symbol.asyncIterator]: jest
        .fn<() => AsyncIterableIterator<Record<string, unknown>>>()
        .mockImplementation(async function* () {
          yield {
            toArray: () => [
              { text: "test text 1", index: 0 },
              { text: "test text 2", index: 1 },
            ],
          };
        }),
    } as unknown as MockTable;

    // Mock client operations
    mockClient = {
      tableNames: jest
        .fn<() => Promise<string[]>>()
        .mockResolvedValue(["test-namespace"]),
      openTable: jest
        .fn<(name: string) => Promise<MockTable>>()
        .mockResolvedValue(mockTable),
      createTable: jest
        .fn<
          (name: string, data: Record<string, unknown>[]) => Promise<MockTable>
        >()
        .mockResolvedValue(mockTable),
      dropTable: jest
        .fn<(name: string) => Promise<void>>()
        .mockResolvedValue(undefined),
      uri: "/tmp/test-storage/lancedb",
      isOpen: jest.fn<() => boolean>().mockReturnValue(true),
      close: jest.fn<() => Promise<void>>().mockResolvedValue(undefined),
      display: jest.fn<() => string>().mockReturnValue("mock-client"),
      createEmptyTable:
        jest.fn<
          (name: string, schema: Record<string, unknown>) => Promise<MockTable>
        >(),
    } as unknown as MockClient;

    // Mock lancedb module
    mockLancedb = require("@lancedb/lancedb");
    mockLancedb.connect = jest
      .fn<() => Promise<MockClient>>()
      .mockResolvedValue(mockClient) as jest.MockedFunction<
      () => Promise<MockClient>
    >;

    jest.clearAllMocks();
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe("Connection Management", () => {
    it("should connect to LanceDB successfully", async () => {
      const { client } = await LanceDb.connect();

      // The actual implementation uses the computed uri from LanceDb.uri
      expect(mockLancedb.connect).toHaveBeenCalledWith(LanceDb.uri);
      expect(client).toBe(mockClient);
    });

    it("should throw error when VECTOR_DB is not set to lancedb", async () => {
      process.env.VECTOR_DB = "other";

      await expect(LanceDb.connect()).rejects.toThrow(
        new VectorDbError("Invalid ENV settings", VectorDbProvider.LANCE)
      );
    });

    it("should use default storage directory when STORAGE_DIR is not set", async () => {
      delete process.env.STORAGE_DIR;

      await LanceDb.connect();

      // The actual implementation uses the computed uri from LanceDb.uri
      expect(mockLancedb.connect).toHaveBeenCalledWith(LanceDb.uri);
    });

    it("should perform heartbeat check", async () => {
      const result = await LanceDb.heartbeat();

      expect(result).toHaveProperty("heartbeat");
      expect(typeof result.heartbeat).toBe("number");
      expect(mockLancedb.connect).toHaveBeenCalled();
    });
  });

  describe("Distance and Similarity Conversion", () => {
    it("should convert distance to similarity correctly", () => {
      expect(LanceDb.distanceToSimilarity(0)).toBe(1);
      expect(LanceDb.distanceToSimilarity(0.5)).toBe(0.5);
      expect(LanceDb.distanceToSimilarity(1)).toBe(1);
      expect(LanceDb.distanceToSimilarity(1.5)).toBe(1);
      expect(LanceDb.distanceToSimilarity(-0.5)).toBe(0.5);
      expect(LanceDb.distanceToSimilarity(null)).toBe(0);
      expect(LanceDb.distanceToSimilarity(undefined as any)).toBe(0);
    });

    it("should handle edge cases in distance conversion", () => {
      expect(LanceDb.distanceToSimilarity(0)).toBe(1);
      expect(LanceDb.distanceToSimilarity(1)).toBe(1);
      expect(LanceDb.distanceToSimilarity(2)).toBe(1);
      expect(LanceDb.distanceToSimilarity(-1)).toBe(0);
      // NaN is handled by the "typeof distance !== 'number'" check
      expect(LanceDb.distanceToSimilarity(NaN)).toBe(0);
    });
  });

  describe("Table Operations", () => {
    it("should list all tables", async () => {
      const tables = await LanceDb.tables();

      expect(tables).toEqual(["test-namespace"]);
      expect(mockClient.tableNames).toHaveBeenCalled();
    });

    it("should count total vectors across all tables", async () => {
      mockTable.countRows.mockResolvedValue(100);
      mockClient.tableNames.mockResolvedValue(["table1", "table2"]);

      const count = await LanceDb.totalVectors();

      expect(count).toBe(200);
      expect(mockClient.openTable).toHaveBeenCalledTimes(2);
    });

    it("should handle empty tables in total vector count", async () => {
      mockTable.countRows.mockResolvedValue(0);
      mockClient.tableNames.mockResolvedValue(["empty-table"]);

      const count = await LanceDb.totalVectors();

      expect(count).toBe(0);
    });
  });

  describe("Namespace Operations", () => {
    it("should check if namespace exists", async () => {
      const exists = await LanceDb.hasNamespace("test-namespace");

      expect(exists).toBe(true);
      expect(mockClient.tableNames).toHaveBeenCalled();
    });

    it("should return false for non-existent namespace", async () => {
      const exists = await LanceDb.hasNamespace("non-existent");

      expect(exists).toBe(false);
    });

    it("should return false for null namespace", async () => {
      const exists = await LanceDb.hasNamespace(null);

      expect(exists).toBe(false);
    });

    it("should get namespace count", async () => {
      mockTable.countRows.mockResolvedValue(150);

      const count = await LanceDb.namespaceCount("test-namespace");

      expect(count).toBe(150);
      expect(mockClient.openTable).toHaveBeenCalledWith("test-namespace");
    });

    it("should return 0 for non-existent namespace count", async () => {
      mockClient.tableNames.mockResolvedValue([]);

      const count = await LanceDb.namespaceCount("non-existent");

      expect(count).toBe(0);
    });

    it("should get namespace client", async () => {
      const result = await LanceDb.namespace(
        mockClient as unknown as any,
        "test-namespace"
      );

      expect(result).toEqual({
        name: "test-namespace",
        client: mockTable,
      });
    });

    it("should return null for non-existent namespace client", async () => {
      mockClient.openTable.mockRejectedValue(new Error("Table not found"));

      const result = await LanceDb.namespace(
        mockClient as unknown as any,
        "non-existent"
      );

      expect(result).toBeNull();
    });

    it("should throw error for null namespace in namespace method", async () => {
      await expect(
        LanceDb.namespace(mockClient as unknown as any, null)
      ).rejects.toThrow("No namespace value provided.");
    });
  });

  describe("Collection Operations", () => {
    it("should create new collection with data", async () => {
      mockClient.tableNames.mockResolvedValue([]);
      const testData = [
        {
          id: "test-id-1",
          vector: [0.1, 0.2, 0.3],
          text: "Test text content that is long enough for Arrow encoding",
          index: 0,
        },
      ];

      const result = await LanceDb.updateOrCreateCollection(
        mockClient as unknown as any,
        testData,
        "new-namespace"
      );

      expect(result).toBe(true);
      expect(mockClient.createTable).toHaveBeenCalledWith(
        "new-namespace",
        testData
      );
    });

    it("should update existing collection with data", async () => {
      mockClient.tableNames.mockResolvedValue(["existing-namespace"]);
      const testData = [
        {
          id: "test-id-1",
          vector: [0.1, 0.2, 0.3],
          text: "Test text content that is long enough for Arrow encoding",
          index: 0,
        },
      ];

      const result = await LanceDb.updateOrCreateCollection(
        mockClient as unknown as any,
        testData,
        "existing-namespace"
      );

      expect(result).toBe(true);
      expect(mockClient.openTable).toHaveBeenCalledWith("existing-namespace");
      expect(mockTable.add).toHaveBeenCalledWith(testData);
    });

    it("should pad short text content for Arrow encoding", async () => {
      mockClient.tableNames.mockResolvedValue([]);
      const testData = [
        {
          id: "test-id-1",
          vector: [0.1, 0.2, 0.3],
          text: "Short", // This will be padded
          index: 0,
        },
      ];

      await LanceDb.updateOrCreateCollection(
        mockClient as unknown as any,
        testData,
        "test-namespace"
      );

      expect(mockClient.createTable).toHaveBeenCalledWith("test-namespace", [
        {
          id: "test-id-1",
          vector: [0.1, 0.2, 0.3],
          text: "Short".padEnd(44, " "),
          index: 0,
        },
      ]);
    });

    it("should handle empty text content", async () => {
      mockClient.tableNames.mockResolvedValue([]);
      const testData = [
        {
          id: "test-id-1",
          vector: [0.1, 0.2, 0.3],
          text: "",
          index: 0,
        },
      ];

      await LanceDb.updateOrCreateCollection(
        mockClient as unknown as any,
        testData,
        "test-namespace"
      );

      expect(mockClient.createTable).toHaveBeenCalledWith("test-namespace", [
        {
          id: "test-id-1",
          vector: [0.1, 0.2, 0.3],
          text: "Empty content".padEnd(44, " "),
          index: 0,
        },
      ]);
    });

    it("should throw error for empty data", async () => {
      await expect(
        LanceDb.updateOrCreateCollection(
          mockClient as unknown as any,
          [],
          "test-namespace"
        )
      ).rejects.toThrow("No valid data provided to updateOrCreateCollection");
    });

    it("should throw error for null data", async () => {
      await expect(
        LanceDb.updateOrCreateCollection(
          mockClient as unknown as any,
          null as any,
          "test-namespace"
        )
      ).rejects.toThrow("No valid data provided to updateOrCreateCollection");
    });

    it("should handle collection creation errors", async () => {
      mockClient.tableNames.mockResolvedValue([]);
      mockClient.createTable.mockRejectedValue(new Error("Creation failed"));

      const testData = [
        {
          id: "test-id-1",
          vector: [0.1, 0.2, 0.3],
          text: "Test text content that is long enough for Arrow encoding",
          index: 0,
        },
      ];

      await expect(
        LanceDb.updateOrCreateCollection(
          mockClient as unknown as any,
          testData,
          "test-namespace"
        )
      ).rejects.toThrow("Creation failed");
    });
  });

  describe("Document Operations", () => {
    it("should check if document has docId", async () => {
      mockTable.query.mockReturnValue({
        where: jest.fn<(condition: string) => MockTable>().mockReturnThis(),
        limit: jest.fn<(count: number) => MockTable>().mockReturnThis(),
        [Symbol.asyncIterator]: jest
          .fn<() => AsyncIterableIterator<Record<string, unknown>>>()
          .mockImplementation(async function* () {
            yield {
              toArray: () => [{ docId: "test-doc-id" }],
            };
          }),
      } as any);

      const hasDocId = await LanceDb.checkDocumentHasDocId(
        "test-namespace",
        "test-doc-id"
      );

      expect(hasDocId).toBe(true);
    });

    it("should return false if document docId not found", async () => {
      mockTable.query.mockReturnValue({
        where: jest.fn<(condition: string) => MockTable>().mockReturnThis(),
        limit: jest.fn<(count: number) => MockTable>().mockReturnThis(),
        [Symbol.asyncIterator]: jest
          .fn<() => AsyncIterableIterator<Record<string, unknown>>>()
          .mockImplementation(async function* () {
            yield {
              toArray: () => [],
            };
          }),
      } as any);

      const hasDocId = await LanceDb.checkDocumentHasDocId(
        "test-namespace",
        "missing-doc-id"
      );

      expect(hasDocId).toBe(false);
    });

    it("should return true if schema has no docId field", async () => {
      mockTable.schema.mockResolvedValue({
        fields: [{ name: "text" }, { name: "vector" }],
      });

      const hasDocId = await LanceDb.checkDocumentHasDocId(
        "test-namespace",
        "test-doc-id"
      );

      expect(hasDocId).toBe(true);
    });

    it("should handle schema errors gracefully", async () => {
      mockTable.query.mockReturnValue({
        where: jest.fn<(condition: string) => MockTable>().mockReturnThis(),
        limit: jest.fn<(count: number) => MockTable>().mockReturnThis(),
        [Symbol.asyncIterator]: jest
          .fn<() => AsyncIterableIterator<Record<string, unknown>>>()
          .mockImplementation(async function* () {
            yield* [];
            throw new Error("Schema error: field not found");
          }),
      } as any);

      const hasDocId = await LanceDb.checkDocumentHasDocId(
        "test-namespace",
        "test-doc-id"
      );

      expect(hasDocId).toBe(true);
    });

    it("should return false for non-existent namespace", async () => {
      mockClient.tableNames.mockResolvedValue([]);

      const hasDocId = await LanceDb.checkDocumentHasDocId(
        "non-existent",
        "test-doc-id"
      );

      expect(hasDocId).toBe(false);
    });

    it("should delete document from namespace", async () => {
      const { DocumentVectors } = require("../../../../models/vectors");
      DocumentVectors.where.mockResolvedValue([
        { vectorId: "vector-1" },
        { vectorId: "vector-2" },
      ]);

      const result = await LanceDb.deleteDocumentFromNamespace(
        "test-namespace",
        "test-doc-id"
      );

      expect(result).toBe(true);
      expect(mockTable.delete).toHaveBeenCalledWith(
        "id IN ('vector-1','vector-2')"
      );
    });

    it("should handle delete when no vectors found", async () => {
      const { DocumentVectors } = require("../../../../models/vectors");
      DocumentVectors.where.mockResolvedValue([]);

      const result = await LanceDb.deleteDocumentFromNamespace(
        "test-namespace",
        "test-doc-id"
      );

      expect(result).toBeUndefined();
      expect(mockTable.delete).not.toHaveBeenCalled();
    });

    it("should handle delete from non-existent namespace", async () => {
      mockClient.tableNames.mockResolvedValue([]);

      const result = await LanceDb.deleteDocumentFromNamespace(
        "non-existent",
        "test-doc-id"
      );

      expect(result).toBeUndefined();
    });
  });

  describe("Vector Operations", () => {
    it("should get adjacent vectors", async () => {
      const { TokenManager } = require("../../../helpers/tiktoken");
      const _mockTokenManager = new TokenManager();

      mockTable.query.mockReturnValue({
        where: jest.fn<(condition: string) => MockTable>().mockReturnThis(),
        select: jest.fn<(fields: string[]) => MockTable>().mockReturnThis(),
        [Symbol.asyncIterator]: jest
          .fn<() => AsyncIterableIterator<Record<string, unknown>>>()
          .mockImplementation(async function* () {
            yield {
              toArray: () => [
                { text: "text 0", index: 0 },
                { text: "text 1", index: 1 },
                { text: "text 2", index: 2 },
                { text: "text 3", index: 3 },
                { text: "text 4", index: 4 },
              ],
            };
          }),
      } as any);

      const result = await LanceDb.getAdjacentVectors(
        "test-namespace",
        "test-title",
        2,
        2
      );

      expect(result).toEqual({
        previous: ["text 0", "text 1"],
        next: ["text 3", "text 4"],
        totalTokens: 40, // 4 texts * 10 tokens each
      });
    });

    it("should handle adjacent vectors at the beginning", async () => {
      mockTable.query.mockReturnValue({
        where: jest.fn<(condition: string) => MockTable>().mockReturnThis(),
        select: jest.fn<(fields: string[]) => MockTable>().mockReturnThis(),
        [Symbol.asyncIterator]: jest
          .fn<() => AsyncIterableIterator<Record<string, unknown>>>()
          .mockImplementation(async function* () {
            yield {
              toArray: () => [
                { text: "text 0", index: 0 },
                { text: "text 1", index: 1 },
                { text: "text 2", index: 2 },
              ],
            };
          }),
      } as any);

      const result = await LanceDb.getAdjacentVectors(
        "test-namespace",
        "test-title",
        0,
        2
      );

      expect(result).toEqual({
        previous: [],
        next: ["text 1", "text 2"],
        totalTokens: 20,
      });
    });

    it("should handle adjacent vectors at the end", async () => {
      mockTable.query.mockReturnValue({
        where: jest.fn<(condition: string) => MockTable>().mockReturnThis(),
        select: jest.fn<(fields: string[]) => MockTable>().mockReturnThis(),
        [Symbol.asyncIterator]: jest
          .fn<() => AsyncIterableIterator<Record<string, unknown>>>()
          .mockImplementation(async function* () {
            yield {
              toArray: () => [
                { text: "text 0", index: 0 },
                { text: "text 1", index: 1 },
                { text: "text 2", index: 2 },
              ],
            };
          }),
      } as any);

      const result = await LanceDb.getAdjacentVectors(
        "test-namespace",
        "test-title",
        2,
        2
      );

      expect(result).toEqual({
        previous: ["text 0", "text 1"],
        next: [],
        totalTokens: 20,
      });
    });

    it("should throw error for non-existent index", async () => {
      mockTable.query.mockReturnValue({
        where: jest.fn<(condition: string) => MockTable>().mockReturnThis(),
        select: jest.fn<(fields: string[]) => MockTable>().mockReturnThis(),
        [Symbol.asyncIterator]: jest
          .fn<() => AsyncIterableIterator<Record<string, unknown>>>()
          .mockImplementation(async function* () {
            yield {
              toArray: () => [
                { text: "text 0", index: 0 },
                { text: "text 1", index: 1 },
              ],
            };
          }),
      } as any);

      await expect(
        LanceDb.getAdjacentVectors("test-namespace", "test-title", 5, 1)
      ).rejects.toThrow("No text found for index 5");
    });

    it("should throw error for non-existent namespace in adjacent vectors", async () => {
      mockClient.tableNames.mockResolvedValue([]);

      await expect(
        LanceDb.getAdjacentVectors("non-existent", "test-title", 1, 1)
      ).rejects.toThrow("Namespace does not exist");
    });

    it("should handle query errors in adjacent vectors", async () => {
      mockTable.query.mockReturnValue({
        where: jest.fn<(condition: string) => MockTable>().mockReturnThis(),
        select: jest.fn<(fields: string[]) => MockTable>().mockReturnThis(),
        [Symbol.asyncIterator]: jest
          .fn<() => AsyncIterableIterator<Record<string, unknown>>>()
          .mockImplementation(async function* () {
            yield* [];
            throw new Error("Query failed");
          }),
      } as any);

      await expect(
        LanceDb.getAdjacentVectors("test-namespace", "test-title", 1, 1)
      ).rejects.toThrow("Failed to retrieve adjacent vectors: Query failed");
    });
  });

  describe("Similarity Search", () => {
    it("should perform standard similarity search", async () => {
      const mockLLMConnector: MockLLMConnector = {
        embedTextInput: jest
          .fn<(input: string) => Promise<number[]>>()
          .mockResolvedValue([0.1, 0.2, 0.3]),
      };

      const mockVectorSearch: MockVectorSearch = {
        distanceType: jest
          .fn<(type: string) => MockVectorSearch>()
          .mockReturnThis(),
        limit: jest.fn<(count: number) => MockVectorSearch>().mockReturnThis(),
        toArray: jest
          .fn<() => Promise<Record<string, unknown>[]>>()
          .mockResolvedValue([
            {
              id: "test-1",
              text: "Test result 1",
              _distance: 0.1,
              vector: [0.1, 0.2, 0.3],
            },
            {
              id: "test-2",
              text: "Test result 2",
              _distance: 0.2,
              vector: [0.2, 0.3, 0.4],
            },
          ]),
      } as unknown as MockVectorSearch;

      mockTable.vectorSearch.mockReturnValue(mockVectorSearch);

      const result = await LanceDb.performSimilaritySearch({
        namespace: "test-namespace",
        input: "test query",
        LLMConnector: mockLLMConnector,
        similarityThreshold: 0.25,
        topN: 4,
      });

      expect(result.contextTexts).toHaveLength(2);
      expect(result.sources).toHaveLength(2);
      expect(result.message).toBe(false);
      expect(mockLLMConnector.embedTextInput).toHaveBeenCalledWith(
        "test query"
      );
    });

    it("should handle empty namespace in similarity search", async () => {
      mockClient.tableNames.mockResolvedValue([]);

      const mockLLMConnector: MockLLMConnector = {
        embedTextInput: jest
          .fn<(input: string) => Promise<number[]>>()
          .mockResolvedValue([0.1, 0.2, 0.3]),
      };

      const result = await LanceDb.performSimilaritySearch({
        namespace: "non-existent",
        input: "test query",
        LLMConnector: mockLLMConnector,
      });

      expect(result.contextTexts).toEqual([]);
      expect(result.sources).toEqual([]);
      expect(result.message).toBe(
        "Invalid query - no documents found for workspace!"
      );
    });

    it("should perform reranked similarity search", async () => {
      const SystemSettings =
        require("../../../../models/systemSettings").default;
      // Mock the specific setting that controls global reranking
      SystemSettings.get.mockImplementation(async (params: any) => {
        if (params.label === "enable_lancedb_rerank") {
          return { value: "on" };
        }
        return { value: "off" };
      });
      SystemSettings.getValueOrFallback.mockResolvedValue(50);

      // Get the mock reranker from the module
      const { getMockRerank } = require("../../../EmbeddingRerankers/native");

      const mockRerank = getMockRerank();
      mockRerank.mockResolvedValue([
        {
          text: "Reranked result 1",
          rerank_score: 0.9,
          id: "rerank-1",
          docId: "doc-1",
          metadata: {
            title: "Test Document 1",
            published: "2023-01-01",
            filename: "test1.pdf",
            source: "/path/to/test1.pdf",
          },
        },
        {
          text: "Reranked result 2",
          rerank_score: 0.8,
          id: "rerank-2",
          docId: "doc-2",
          metadata: {
            title: "Test Document 2",
            published: "2023-01-02",
            filename: "test2.pdf",
            source: "/path/to/test2.pdf",
          },
        },
      ]);

      const mockVectorSearch: MockVectorSearch = {
        distanceType: jest
          .fn<(type: string) => MockVectorSearch>()
          .mockReturnThis(),
        limit: jest.fn<(count: number) => MockVectorSearch>().mockReturnThis(),
        toArray: jest
          .fn<() => Promise<Record<string, unknown>[]>>()
          .mockResolvedValue([
            {
              id: "test-1",
              text: "Test result 1",
              _distance: 0.1,
              docId: "doc-1",
              title: "Test Document 1",
              published: "2023-01-01",
              filename: "test1.pdf",
              source: "/path/to/test1.pdf",
            },
            {
              id: "test-2",
              text: "Test result 2",
              _distance: 0.2,
              docId: "doc-2",
              title: "Test Document 2",
              published: "2023-01-02",
              filename: "test2.pdf",
              source: "/path/to/test2.pdf",
            },
          ]),
      } as unknown as MockVectorSearch;

      mockTable.vectorSearch.mockReturnValue(mockVectorSearch);

      const mockLLMConnector: MockLLMConnector = {
        embedTextInput: jest
          .fn<(input: string) => Promise<number[]>>()
          .mockResolvedValue([0.1, 0.2, 0.3]),
      };

      const result = await LanceDb.performSimilaritySearch({
        namespace: "test-namespace",
        input: "test query",
        LLMConnector: mockLLMConnector,
        rerank: true,
        similarityThreshold: 0.1, // Lower threshold to be safe
      });

      // Check that the reranking function was called
      expect(mockRerank).toHaveBeenCalled();

      // The results should be present
      expect(result.contextTexts).toHaveLength(2);
      expect(result.contextTexts).toContain("Reranked result 1");
      expect(result.contextTexts).toContain("Reranked result 2");

      // Sources should have the proper structure with metadata
      expect(result.sources).toHaveLength(2);
      expect(result.sources[0]).toHaveProperty("title", "Test Document 1");
      expect(result.sources[0]).toHaveProperty("docId", "doc-1");
      expect(result.sources[1]).toHaveProperty("title", "Test Document 2");
      expect(result.sources[1]).toHaveProperty("docId", "doc-2");
    });

    it("should handle workspace topN setting", async () => {
      const mockLLMConnector: MockLLMConnector = {
        embedTextInput: jest
          .fn<(input: string) => Promise<number[]>>()
          .mockResolvedValue([0.1, 0.2, 0.3]),
      };

      const mockVectorSearch: MockVectorSearch = {
        distanceType: jest
          .fn<(type: string) => MockVectorSearch>()
          .mockReturnThis(),
        limit: jest.fn<(count: number) => MockVectorSearch>().mockReturnThis(),
        toArray: jest
          .fn<() => Promise<Record<string, unknown>[]>>()
          .mockResolvedValue([]),
      } as unknown as MockVectorSearch;

      mockTable.vectorSearch.mockReturnValue(mockVectorSearch);

      await LanceDb.performSimilaritySearch({
        namespace: "test-namespace",
        input: "test query",
        LLMConnector: mockLLMConnector,
        topN: 4,
        workspace: { topN: 10 },
      });

      expect(mockVectorSearch.limit).toHaveBeenCalledWith(10);
    });

    it("should throw error for invalid similarity search parameters", async () => {
      await expect(
        LanceDb.performSimilaritySearch({
          namespace: "",
          input: "test query",
          LLMConnector: {
            embedTextInput: jest.fn<
              (input: string) => Promise<number[]>
            >() as any,
          },
        })
      ).rejects.toThrow("Invalid request to performSimilaritySearch.");
    });
  });

  describe("Namespace Management", () => {
    it("should delete vectors in namespace", async () => {
      const result = await LanceDb.deleteVectorsInNamespace(
        mockClient as unknown as any,
        "test-namespace"
      );

      expect(result).toBe(true);
      expect(mockClient.dropTable).toHaveBeenCalledWith("test-namespace");
    });

    it("should throw error when deleting vectors with null namespace", async () => {
      await expect(
        LanceDb.deleteVectorsInNamespace(mockClient as unknown as any, null)
      ).rejects.toThrow("No namespace value provided.");
    });

    it("should check namespace existence with client", async () => {
      const exists = await LanceDb.namespaceExists(
        mockClient as unknown as any,
        "test-namespace"
      );

      expect(exists).toBe(true);
      expect(mockClient.tableNames).toHaveBeenCalled();
    });

    it("should throw error for null namespace in namespaceExists", async () => {
      await expect(
        LanceDb.namespaceExists(mockClient as unknown as any, null)
      ).rejects.toThrow("No namespace value provided.");
    });
  });

  describe("Document Processing", () => {
    it("should add document to namespace with cached vectors", async () => {
      const { cachedVectorInformation } = require("../../../files");
      cachedVectorInformation.mockResolvedValue({
        exists: true,
        chunks: [
          [
            {
              values: [0.1, 0.2, 0.3],
              metadata: {
                text: "Test chunk 1 with sufficient content for embedding processing",
                id: "chunk-1",
              },
            },
            {
              values: [0.4, 0.5, 0.6],
              metadata: {
                text: "Test chunk 2 with sufficient content for embedding processing",
                id: "chunk-2",
              },
            },
          ],
        ],
      });

      const { DocumentVectors } = require("../../../../models/vectors");
      DocumentVectors.bulkInsert.mockResolvedValue(undefined);

      const result = await LanceDb.addDocumentToNamespace(
        "test-namespace",
        {
          pageContent:
            "Test document content that is definitely long enough for embedding processing to work properly",
          docId: "test-doc-id",
        },
        "/path/to/test-file.pdf"
      );

      expect(result).toEqual({ vectorized: true, error: null });
      expect(DocumentVectors.bulkInsert).toHaveBeenCalled();
    });

    it("should handle empty page content", async () => {
      const result = await LanceDb.addDocumentToNamespace("test-namespace", {
        pageContent: "",
        docId: "test-doc-id",
      });

      expect(result).toEqual({
        vectorized: false,
        error: "Document has no content to embed",
      });
    });

    it("should handle content too small for embedding", async () => {
      const result = await LanceDb.addDocumentToNamespace("test-namespace", {
        pageContent: "Short",
        docId: "test-doc-id",
      });

      expect(result).toEqual({
        vectorized: false,
        error: "Document content is too small for embedding (5 characters)",
      });
    });

    it("should handle no chunks in cached vector information", async () => {
      const { cachedVectorInformation } = require("../../../files");
      cachedVectorInformation.mockResolvedValue({
        exists: true,
        chunks: [],
      });

      const result = await LanceDb.addDocumentToNamespace(
        "test-namespace",
        {
          pageContent:
            "Test document content that is long enough for embedding",
          docId: "test-doc-id",
        },
        "/path/to/test-file.pdf"
      );

      expect(result).toEqual({
        vectorized: false,
        error: "No chunks found in cached vector information",
      });
    });

    it("should handle addDocumentToNamespace errors", async () => {
      const { cachedVectorInformation } = require("../../../files");
      cachedVectorInformation.mockRejectedValue(new Error("Cache error"));

      const result = await LanceDb.addDocumentToNamespace("test-namespace", {
        pageContent: "Test document content that is long enough for embedding",
        docId: "test-doc-id",
      });

      expect(result).toEqual({
        vectorized: false,
        error: "Cache error",
      });
    });
  });

  describe("Data Curation", () => {
    it("should curate sources correctly", () => {
      const sources = [
        {
          text: "Source 1",
          vector: [0.1, 0.2, 0.3],
          _distance: 0.1,
          docId: "doc-1",
          metadata: { title: "Document 1" },
        },
        {
          text: "Source 2",
          vector: [0.4, 0.5, 0.6],
          _distance: 0.2,
          docId: "doc-2",
          filename: "file2.pdf",
        },
      ];

      const curated = LanceDb.curateSources(sources);

      expect(curated).toEqual([
        {
          title: "Document 1",
          text: "Source 1",
          docId: "doc-1",
        },
        {
          filename: "file2.pdf",
          text: "Source 2",
          docId: "doc-2",
        },
      ]);
    });

    it("should handle empty sources array", () => {
      const curated = LanceDb.curateSources([]);

      expect(curated).toEqual([]);
    });

    it("should handle sources with no metadata", () => {
      const sources = [
        {
          text: "Source 1",
          vector: [0.1, 0.2, 0.3],
          _distance: 0.1,
        },
      ];

      const curated = LanceDb.curateSources(sources);

      expect(curated).toEqual([]);
    });
  });

  describe("Reset and Cleanup", () => {
    it("should reset database", async () => {
      const fs = require("fs");
      fs.rm.mockImplementation(
        (path: string, options: any, callback: Function) => {
          callback();
        }
      );

      const result = await LanceDb.reset();

      expect(result).toEqual({ reset: true });
      expect(fs.rm).toHaveBeenCalledWith(
        "/tmp/test-storage/lancedb",
        { recursive: true },
        expect.any(Function)
      );
    });
  });

  describe("Error Handling", () => {
    it("should handle connection errors", async () => {
      mockLancedb.connect.mockRejectedValue(new Error("Connection failed"));

      await expect(LanceDb.connect()).rejects.toThrow("Connection failed");
    });

    it("should handle table operation errors", async () => {
      mockClient.tableNames.mockRejectedValue(new Error("Table query failed"));

      await expect(LanceDb.tables()).rejects.toThrow("Table query failed");
    });

    it("should handle vector search errors", async () => {
      const mockVectorSearch: MockVectorSearch = {
        distanceType: jest
          .fn<(type: string) => MockVectorSearch>()
          .mockReturnThis() as jest.MockedFunction<
          (type: string) => MockVectorSearch
        >,
        limit: jest
          .fn<(count: number) => MockVectorSearch>()
          .mockReturnThis() as jest.MockedFunction<
          (count: number) => MockVectorSearch
        >,
        toArray: jest
          .fn<() => Promise<Record<string, unknown>[]>>()
          .mockRejectedValue(new Error("Search failed")) as jest.MockedFunction<
          () => Promise<Record<string, unknown>[]>
        >,
      } as unknown as MockVectorSearch;

      mockTable.vectorSearch.mockReturnValue(mockVectorSearch);

      const mockLLMConnector: MockLLMConnector = {
        embedTextInput: jest
          .fn<(input: string) => Promise<number[]>>()
          .mockResolvedValue([0.1, 0.2, 0.3]),
      };

      await expect(
        LanceDb.performSimilaritySearch({
          namespace: "test-namespace",
          input: "test query",
          LLMConnector: mockLLMConnector,
        })
      ).rejects.toThrow("Search failed");
    });
  });

  describe("Performance and Edge Cases", () => {
    it("should handle large vector arrays", async () => {
      const largeData = Array.from({ length: 1000 }, (_, i) => ({
        id: `test-id-${i}`,
        vector: Array.from({ length: 1536 }, () => Math.random()),
        text: `Test text content ${i}`.padEnd(44, " "),
        index: i,
      }));

      mockClient.tableNames.mockResolvedValue([]);

      const result = await LanceDb.updateOrCreateCollection(
        mockClient as unknown as any,
        largeData,
        "large-namespace"
      );

      expect(result).toBe(true);
      expect(mockClient.createTable).toHaveBeenCalledWith(
        "large-namespace",
        largeData
      );
    });

    it("should handle concurrent operations", async () => {
      const promises = Array.from({ length: 10 }, (_, i) =>
        LanceDb.namespaceCount(`namespace-${i}`)
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(10);
      results.forEach((result) => {
        expect(typeof result).toBe("number");
      });
    });

    it("should handle memory-intensive operations", async () => {
      const largeText = "x".repeat(100000);
      const testData = [
        {
          id: "large-text-id",
          vector: Array.from({ length: 1536 }, () => Math.random()),
          text: largeText,
          index: 0,
        },
      ];

      mockClient.tableNames.mockResolvedValue([]);

      const result = await LanceDb.updateOrCreateCollection(
        mockClient as unknown as any,
        testData,
        "memory-test"
      );

      expect(result).toBe(true);
    });
  });
});
