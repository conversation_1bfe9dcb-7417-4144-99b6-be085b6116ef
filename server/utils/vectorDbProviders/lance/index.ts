import * as lancedb from "@lancedb/lancedb";
import { v4 as uuidv4 } from "uuid";
import * as fs from "fs";
import * as path from "path";

import { VectorDbProvider, VectorDbError } from "../../../types/vectorDb";

// Import helper functions (these will need to be typed when migrated)
import SystemSettings from "../../../models/systemSettings";
import { cachedVectorInformation } from "../../files";
import { sourceIdentifier } from "../../chats";
import { NativeEmbeddingReranker } from "../../EmbeddingRerankers/native";
import { RerankedDocument } from "../../../types/utils";

// Extended reranked document to include distance property
interface ExtendedRerankedDocument extends RerankedDocument {
  _distance?: number;
}
import { TokenManager } from "../../helpers/tiktoken";
import { DocumentVectors } from "../../../models/vectors";

// LanceDB Client connection type
type LanceClient = Awaited<ReturnType<typeof lancedb.connect>>;

// LanceDB query result row
interface LanceQueryRow {
  text: string;
  index: number;
  [key: string]: unknown;
}

// LanceDB source document with score
interface LanceSourceDoc {
  text: string;
  score?: number;
  [key: string]: unknown;
}

// Document data interface
interface DocumentData {
  pageContent: string | string[];
  docId: string;
  [key: string]: unknown;
}

// Vector record for Lance DB
interface LanceVectorRecord {
  id: string;
  vector: number[];
  text: string;
  index: number;
  docId?: string;
  filename?: string;
  source?: string;
  [key: string]: unknown;
}

// Similarity search parameters
interface SimilaritySearchParams {
  namespace: string;
  input: string;
  LLMConnector: {
    embedTextInput: (input: string) => Promise<number[]>;
  };
  similarityThreshold?: number;
  topN?: number;
  filterIdentifiers?: string[];
  rerank?: boolean;
  workspace?: Record<string, unknown> | null;
}

// Reranked similarity response parameters
interface RerankedSimilarityParams {
  client: LanceClient;
  namespace: string;
  query: string;
  queryVector: number[];
  topN?: number;
  similarityThreshold?: number;
  filterIdentifiers?: string[];
}

// Standard similarity response parameters
interface StandardSimilarityParams {
  client: LanceClient;
  namespace: string;
  queryVector: number[];
  similarityThreshold?: number;
  topN?: number;
  filterIdentifiers?: string[];
}

// Similarity response result
interface SimilarityResponseResult {
  contextTexts: string[];
  sourceDocuments: Array<Record<string, unknown>>;
  scores: number[];
}

// Adjacent vectors result
interface AdjacentVectorsResult {
  previous: string[];
  next: string[];
  totalTokens: number;
}

export const LanceDb = {
  uri: `${
    process.env.STORAGE_DIR ? `${process.env.STORAGE_DIR}/` : "./storage/"
  }lancedb`,
  name: "LanceDb" as const,

  async connect(): Promise<{ client: LanceClient }> {
    if (process.env.VECTOR_DB !== "lancedb") {
      throw new VectorDbError("Invalid ENV settings", VectorDbProvider.LANCE);
    }

    const client = await lancedb.connect(this.uri);
    return { client };
  },

  distanceToSimilarity(distance: number | null = null): number {
    if (distance === null || typeof distance !== "number" || isNaN(distance))
      return 0.0;
    if (distance >= 1.0) return 1;
    if (distance < 0) return 1 - Math.abs(distance);
    return 1 - distance;
  },

  async heartbeat(): Promise<{ heartbeat: number }> {
    await this.connect();
    return { heartbeat: Number(new Date()) };
  },

  async tables(): Promise<string[]> {
    const { client } = await this.connect();
    return await client.tableNames();
  },

  async totalVectors(): Promise<number> {
    const { client } = await this.connect();
    const tables = await client.tableNames();
    let count = 0;
    for (const tableName of tables) {
      const table = await client.openTable(tableName);
      count += await table.countRows();
    }
    return count;
  },

  async namespaceCount(namespace: string | null = null): Promise<number> {
    const { client } = await this.connect();
    const exists = await this.namespaceExists(client, namespace);
    if (!exists) return 0;

    const table = await client.openTable(namespace || "default");
    return (await table.countRows()) || 0;
  },

  /**
   * Performs a SimilaritySearch + Reranking on a namespace.
   * The function implements a 3-step process:
   * 1. Initial vector search: Fetches topN + rerank_vector_count vectors
   * 2. Re-ranking: Applies re-ranking to all fetched vectors
   * 3. Final selection: Filters by similarity threshold and returns top N results
   */
  async rerankedSimilarityResponse({
    client,
    namespace,
    query,
    queryVector,
    topN = 4,
    similarityThreshold = 0.25,
    filterIdentifiers = [],
  }: RerankedSimilarityParams): Promise<SimilarityResponseResult> {
    const reranker = new NativeEmbeddingReranker();
    const collection = await client.openTable(namespace);
    const result: SimilarityResponseResult = {
      contextTexts: [],
      sourceDocuments: [],
      scores: [],
    };

    // Step 1: Get additional vectors for re-ranking
    const rerankVectorCount = Number(
      await SystemSettings.getValueOrFallback(
        { label: "rerank_vector_count" },
        50
      )
    );

    // Calculate search limit using topN + rerank_vector_count
    const searchLimit = Number(topN) + Number(rerankVectorCount);

    console.log(
      `LanceDB: Fetching ${searchLimit} vectors for re-ranking (topN: ${topN}, additional: ${rerankVectorCount})`
    );

    const vectorSearchResults = await collection
      .vectorSearch(queryVector)
      .distanceType("cosine")
      .limit(searchLimit)
      .toArray();

    // Step 2: Re-rank the fetched vectors
    console.log(
      `LanceDB: Starting re-ranking of ${vectorSearchResults.length} results...`
    );

    try {
      // Re-rank all fetched vectors to ensure we have the best possible candidates
      const rerankResults = await reranker.rerank(query, vectorSearchResults, {
        topK: searchLimit,
      });
      console.log(
        `LanceDB: Re-ranking complete. Processing ${rerankResults.length} re-ranked results.`
      );

      // Step 3: Filter by similarity threshold and take top N
      const validResults = rerankResults
        .filter((item: ExtendedRerankedDocument) => {
          const score =
            (item?.rerank_score ?? false) ||
            this.distanceToSimilarity(item._distance);
          return (
            score >= similarityThreshold &&
            !filterIdentifiers.includes(sourceIdentifier(item))
          );
        })
        .sort((a: ExtendedRerankedDocument, b: ExtendedRerankedDocument) => {
          const scoreA =
            (a?.rerank_score ?? false) ||
            this.distanceToSimilarity(a._distance);
          const scoreB =
            (b?.rerank_score ?? false) ||
            this.distanceToSimilarity(b._distance);
          return scoreB - scoreA;
        })
        .slice(0, Number(topN));

      console.log(
        `LanceDB: Found ${validResults.length} results above similarity threshold ${similarityThreshold}`
      );

      validResults.forEach((item: ExtendedRerankedDocument) => {
        // Destructure and ignore unused variables with underscore prefix
        const { vector: _, ...rest } = item;
        const score =
          (item?.rerank_score ?? false) ||
          this.distanceToSimilarity(item._distance);

        result.contextTexts.push(rest.text);
        result.sourceDocuments.push({
          ...rest,
          score,
          // Ensure docId is included in the source document
          ...(rest.docId ? { docId: rest.docId } : {}),
        });
        result.scores.push(score);
      });
    } catch (e: unknown) {
      console.error("LanceDB::rerankedSimilarityResponse Error:", e);
      console.error(
        "LanceDB::rerankedSimilarityResponse",
        e instanceof Error ? e.message : "Unknown error"
      );
    }

    return result;
  },

  /**
   * Performs a standard SimilaritySearch on a given LanceDB namespace.
   */
  async similarityResponse({
    client,
    namespace,
    queryVector,
    similarityThreshold = 0.25,
    topN = 4,
    filterIdentifiers = [],
  }: StandardSimilarityParams): Promise<SimilarityResponseResult> {
    const collection = await client.openTable(namespace);
    const result: SimilarityResponseResult = {
      contextTexts: [],
      sourceDocuments: [],
      scores: [],
    };

    const effectiveTopN = Number(topN) > 0 ? Math.floor(Number(topN)) : 4;

    const response = await collection
      .vectorSearch(queryVector)
      .distanceType("cosine")
      .limit(effectiveTopN)
      .toArray();

    response.forEach((item: LanceVectorRecord & { _distance: number }) => {
      if (this.distanceToSimilarity(item._distance) < similarityThreshold)
        return;
      // Destructure and ignore unused variables with underscore prefix
      const { vector: _, ...rest } = item;
      if (filterIdentifiers.includes(sourceIdentifier(rest))) {
        console.log(
          "LanceDB: A source was filtered from context as its parent document is pinned."
        );
        return;
      }

      result.contextTexts.push(rest.text);
      result.sourceDocuments.push({
        ...rest,
        score: this.distanceToSimilarity(item._distance),
        // Ensure docId is included in the source document
        ...(rest.docId ? { docId: rest.docId } : {}),
      });
      result.scores.push(this.distanceToSimilarity(item._distance));
    });

    return result;
  },

  async namespace(
    client: LanceClient,
    namespace: string | null = null
  ): Promise<Record<string, unknown> | null> {
    if (!namespace) throw new Error("No namespace value provided.");
    const collection = await client.openTable(namespace).catch(() => false);
    if (!collection) return null;

    return {
      name: namespace,
      client: collection,
    };
  },

  async updateOrCreateCollection(
    client: LanceClient,
    data: LanceVectorRecord[] = [],
    namespace: string
  ): Promise<boolean> {
    try {
      // Validate data before sending to Lance
      if (!data || !Array.isArray(data) || data.length === 0) {
        throw new Error("No valid data provided to updateOrCreateCollection");
      }

      // Ensure all text fields meet minimum byte requirement for Arrow encoding
      const validatedData = data.map((item) => {
        const newItem = { ...item };

        // Ensure text field is never empty
        if (!newItem.text || newItem.text.length === 0) {
          newItem.text = "Empty content";
        }

        // Fix for Arrow error: ensure text meets minimum byte requirement
        if (
          typeof newItem.text === "string" &&
          Buffer.from(newItem.text, "utf8").length < 44
        ) {
          newItem.text = newItem.text.padEnd(44, " ");
        }

        return newItem;
      });

      const hasNamespace = await this.hasNamespace(namespace);
      if (hasNamespace) {
        const collection = await client.openTable(namespace);
        await collection.add(validatedData);
      } else {
        await client.createTable(namespace, validatedData);
      }
      return true;
    } catch (error: unknown) {
      console.error(
        `Error in updateOrCreateCollection: ${error instanceof Error ? error.message : "Unknown error"}`
      );
      throw error;
    }
  },

  async hasNamespace(namespace: string | null = null): Promise<boolean> {
    if (!namespace) return false;
    const { client } = await this.connect();
    const exists = await this.namespaceExists(client, namespace);
    return exists;
  },

  async namespaceExists(
    client: LanceClient,
    namespace: string | null = null
  ): Promise<boolean> {
    if (!namespace) throw new Error("No namespace value provided.");
    const collections = await client.tableNames();
    return collections.includes(namespace);
  },

  async deleteVectorsInNamespace(
    client: LanceClient,
    namespace: string | null = null
  ): Promise<boolean> {
    if (!namespace) throw new Error("No namespace value provided.");
    await client.dropTable(namespace);
    return true;
  },

  async deleteDocumentFromNamespace(
    namespace: string,
    docId: string
  ): Promise<boolean | undefined> {
    const { client } = await this.connect();
    const exists = await this.namespaceExists(client, namespace);
    if (!exists) {
      console.error(
        `LanceDB:deleteDocumentFromNamespace - namespace ${namespace} does not exist.`
      );
      return;
    }

    const table = await client.openTable(namespace);
    const vectorIds = (await DocumentVectors.where({ docId })).map(
      (record) => record.vectorId
    );

    if (vectorIds.length === 0) return;
    await table.delete(
      `id IN (${vectorIds.map((v: string) => `'${v}'`).join(",")})`
    );
    return true;
  },

  async getAdjacentVectors(
    namespace: string,
    title: string,
    index: number,
    adjacentCount: number = 1
  ): Promise<AdjacentVectorsResult> {
    const { client } = await this.connect();
    const tokenManager = new TokenManager();
    if (!(await this.namespaceExists(client, namespace))) {
      throw new Error("Namespace does not exist");
    }
    try {
      const table = await client.openTable(namespace);

      let results: Array<{ text: string; index: number }> = [];
      for await (const batch of table
        .query()
        .where(`title = '${title}'`)
        .select(["text", "index"])) {
        const rows = batch.toArray();
        results = results.concat(
          rows.map((row: unknown) => {
            const lanceRow = row as LanceQueryRow;
            return {
              text: lanceRow.text,
              index: lanceRow.index,
            };
          })
        );
      }

      const currentPosition = results.findIndex((row) => row.index === index);
      if (currentPosition === -1) {
        throw new Error(`No text found for index ${index}`);
      }

      const previousTexts: string[] = [];
      const nextTexts: string[] = [];
      let totalTokenCount = 0;

      for (let i = 1; i <= adjacentCount; i++) {
        if (currentPosition - i >= 0) {
          const text = results[currentPosition - i].text;
          previousTexts.unshift(text);
          totalTokenCount += tokenManager.countFromString(text);
        }
      }

      for (let i = 1; i <= adjacentCount; i++) {
        if (currentPosition + i < results.length) {
          const text = results[currentPosition + i].text;
          nextTexts.push(text);
          totalTokenCount += tokenManager.countFromString(text);
        }
      }

      return {
        previous: previousTexts,
        next: nextTexts,
        totalTokens: totalTokenCount,
      };
    } catch (error: unknown) {
      console.error("Error in getAdjacentVectors:", error);
      throw new Error(
        `Failed to retrieve adjacent vectors: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  },

  async addDocumentToNamespace(
    namespace: string,
    documentData: DocumentData,
    fullFilePath: string | null = null,
    skipCache: boolean = false
  ): Promise<{ vectorized: boolean; error: string | null }> {
    // Derive file-level metadata
    const filename = fullFilePath ? path.basename(fullFilePath) : undefined;
    const docpath = fullFilePath;

    try {
      const { pageContent, docId } = documentData;
      if (!pageContent || pageContent.length === 0) {
        console.error("Empty page content detected in addDocumentToNamespace");
        return { vectorized: false, error: "Document has no content to embed" };
      }

      // Validate content is substantial enough to be embedded
      if (typeof pageContent === "string" && pageContent.length < 50) {
        console.error(
          `Content too small for embedding: ${pageContent.length} characters`
        );
        return {
          vectorized: false,
          error: `Document content is too small for embedding (${pageContent.length} characters)`,
        };
      }

      if (!skipCache) {
        const cacheResult = await cachedVectorInformation(fullFilePath);
        if (
          typeof cacheResult === "object" &&
          "exists" in cacheResult &&
          cacheResult.exists
        ) {
          const { client } = await this.connect();
          const { chunks } = cacheResult;
          const documentVectors: Array<{ docId: string; vectorId: string }> =
            [];
          const submissions: LanceVectorRecord[] = [];

          if (!chunks || chunks.length === 0) {
            console.error("No chunks found in cached vector information");
            return {
              vectorized: false,
              error: "No chunks found in cached vector information",
            };
          }

          for (const chunk of chunks) {
            if (Array.isArray(chunk)) {
              chunk.forEach(
                (
                  chunkItem: {
                    values: number[];
                    metadata: Record<string, unknown> & {
                      text?: string;
                      id?: string;
                    };
                  },
                  _index: number
                ) => {
                  const id = uuidv4();
                  // Extract metadata excluding its own id
                  const { id: _id, ...chunkMetadata } = chunkItem.metadata;
                  documentVectors.push({ docId, vectorId: id });
                  submissions.push({
                    id: id,
                    vector: chunkItem.values,
                    index: _index,
                    docId: docId, // Add docId to the vector record
                    text: chunkMetadata.text || "Empty content",
                    // Include original chunk metadata
                    ...chunkMetadata,
                    // Include file-level metadata
                    filename,
                    source: docpath || undefined,
                  });
                }
              );
            }
          }
          await this.updateOrCreateCollection(client, submissions, namespace);
          await DocumentVectors.bulkInsert(documentVectors);
          return { vectorized: true, error: null };
        }
      }

      // If we are here then we are going to embed and store a novel document.
      // We have to do this manually as opposed to using LangChains `xyz.fromDocuments`
      // because we then cannot atomically control our namespace to granularly find/remove documents
      // from vectordb.
      const { Workspace } = await import("../../../models/workspace");
      const workspace = await Workspace.get({ slug: namespace });
      const { getEmbeddingEngineSelection } = await import("../../helpers");
      const EmbedderEngine = getEmbeddingEngineSelection({
        workspace: workspace as any,
      });

      if (!EmbedderEngine) {
        console.error("No embedding engine available");
        return {
          vectorized: false,
          error: "No embedding engine available",
        };
      }

      // Check if the content is already split into chunks (e.g., by Jina)
      let textChunks: string[];
      const { TextSplitter } = await import("../../TextSplitter");
      const maxChunkSize = TextSplitter.determineMaxChunkSize(
        Number(
          await SystemSettings.getValueOrFallback(
            { label: "text_splitter_chunk_size" },
            EmbedderEngine?.embeddingMaxChunkLength
          )
        ),
        EmbedderEngine?.embeddingMaxChunkLength
      );

      console.log(
        `Using max chunk size: ${maxChunkSize} for document with length ${
          Array.isArray(pageContent)
            ? pageContent.length
            : (pageContent as string).length
        }`
      );

      // Ensure content is valid for embedding
      if (typeof pageContent !== "string" && !Array.isArray(pageContent)) {
        console.error(`Invalid pageContent type: ${typeof pageContent}`);
        return {
          vectorized: false,
          error: `Invalid content type: ${typeof pageContent}`,
        };
      }

      if (Array.isArray(pageContent)) {
        console.log("Using pre-split chunks from Jina");
        // Validate and potentially re-chunk any oversized chunks
        const oversizedChunks = pageContent.filter(
          (chunk) => chunk.length > maxChunkSize
        );
        if (oversizedChunks.length > 0) {
          console.log(
            `Found ${oversizedChunks.length} oversized chunks. Re-chunking...`
          );
          const textSplitter = new TextSplitter({
            chunkSize: maxChunkSize,
            chunkOverlap: Number(
              await SystemSettings.getValueOrFallback(
                { label: "text_splitter_chunk_overlap" },
                20
              )
            ),
            method: "basic", // Use basic for re-chunking to avoid API calls
            chunkHeaderMeta: {
              sourceDocument: (documentData as any)?.title,
              published: (documentData as any)?.published || "unknown",
            },
          });

          // Process each chunk, re-splitting if necessary
          const processedChunks = [];
          for (const chunk of pageContent) {
            if (chunk.length > maxChunkSize) {
              const subChunks = await textSplitter.splitText(chunk);
              processedChunks.push(...subChunks);
            } else {
              processedChunks.push(chunk);
            }
          }
          textChunks = processedChunks;
        } else {
          textChunks = pageContent;
        }
      } else {
        const textSplitter = new TextSplitter({
          chunkSize: maxChunkSize,
          chunkOverlap: Number(
            await SystemSettings.getValueOrFallback(
              { label: "text_splitter_chunk_overlap" },
              20
            )
          ),
          method: (await SystemSettings.getValueOrFallback(
            { label: "text_splitter_method" },
            "basic"
          )) as "basic" | "jina",
          chunkHeaderMeta: {
            sourceDocument: (documentData as any)?.title,
            published: (documentData as any)?.published || "unknown",
          },
        });

        try {
          console.log("Splitting text into chunks...");
          textChunks = await textSplitter.splitText(pageContent);
          console.log(
            `Successfully split text into ${textChunks.length} chunks`
          );
        } catch (error: unknown) {
          console.error("Error splitting text:", error);
          return {
            vectorized: false,
            error: `Text splitting failed: ${error instanceof Error ? error.message : String(error)}`,
          };
        }

        // Validate chunks
        if (!textChunks || textChunks.length === 0) {
          console.error("No chunks were created from the document.");
          return {
            vectorized: false,
            error: "Document splitting failed - no chunks created.",
          };
        }

        // Filter out empty chunks
        const validChunks = textChunks.filter(
          (chunk) => chunk && chunk.length > 0
        );
        if (validChunks.length === 0) {
          console.error("All text chunks were empty after filtering.");
          return {
            vectorized: false,
            error: "Document splitting produced only empty chunks.",
          };
        }

        if (validChunks.length !== textChunks.length) {
          console.warn(
            `Filtered out ${textChunks.length - validChunks.length} empty chunks`
          );
          textChunks = validChunks;
        }
      }

      console.log("Chunks to process:", textChunks.length);
      const documentVectors: Array<{ docId: string; vectorId: string }> = [];
      const vectors: Array<{
        id: string;
        values: number[];
        metadata: Record<string, unknown>;
      }> = [];
      const submissions: LanceVectorRecord[] = [];
      let contextualize = false;
      if (process.env.CONTEXTUAL_EMBEDDING === "on") contextualize = true;

      let vectorValues: number[][] = [];
      let contextualizedChunks: string[] = [];

      try {
        if (!contextualize) {
          console.log(
            `Embedding ${textChunks.length} chunks without contextualization`
          );
          const result = await EmbedderEngine.embedChunks(textChunks);

          // Check if there was an error in the embedding process
          if (result && (result as any).error) {
            console.error(
              "Embedding engine returned error:",
              (result as any).error
            );
            return {
              vectorized: false,
              error: `Embedding failed: ${(result as any).error}`,
            };
          }

          // Check if result is a Promise (shouldn't happen, but just in case)
          if (result instanceof Promise) {
            console.error(
              "Embedding engine returned a Promise instead of a result"
            );
            try {
              const resolvedResult = await result;
              vectorValues = resolvedResult as number[][];
              const skipToValidation = true;
              if (skipToValidation) {
                console.log(
                  "Skipping to validation with resolved Promise result"
                );
              }
            } catch (promiseError: unknown) {
              console.error("Failed to resolve Promise result:", promiseError);
              return {
                vectorized: false,
                error: `Embedding failed: Promise resolution error - ${promiseError instanceof Error ? promiseError.message : String(promiseError)}`,
              };
            }
          } else {
            vectorValues = result as number[][];
          }
        } else {
          try {
            console.log(
              `Embedding ${textChunks.length} chunks with contextualization`
            );
            const { Contextualize } = await import("../../contextualization");
            const contextualizer = new Contextualize(pageContent as string);
            contextualizedChunks =
              await contextualizer.contextualizeChunks(textChunks);
            const combinedChunks = contextualizedChunks.map(
              (contextualizedChunk, i) => {
                return contextualizedChunk + textChunks[i];
              }
            );
            const result = await EmbedderEngine.embedChunks(combinedChunks);

            // Check if there was an error in the embedding process
            if (result && (result as any).error) {
              console.error(
                "Embedding engine returned error:",
                (result as any).error
              );
              return {
                vectorized: false,
                error: `Embedding failed: ${(result as any).error}`,
              };
            }

            // Check if result is a Promise (shouldn't happen, but just in case)
            if (result instanceof Promise) {
              console.error(
                "Contextualized embedding engine returned a Promise instead of a result"
              );
              try {
                // Try to resolve the promise
                const resolvedResult = await result;
                console.log(
                  "Successfully resolved Promise result for contextualized embedding:",
                  {
                    type: typeof resolvedResult,
                    hasEmbeddings:
                      resolvedResult &&
                      typeof resolvedResult === "object" &&
                      "embeddings" in resolvedResult,
                  }
                );
                vectorValues = resolvedResult as number[][];
                // Skip the rest of the embedding logic by jumping to the validation section
                // We'll do this by setting a flag and using an if statement below
                const skipToValidation = true;
                if (skipToValidation) {
                  // Log the result structure for debugging
                  console.log(
                    "Skipping to validation with resolved Promise result for contextualized embedding"
                  );
                  // Continue with the rest of the function
                }
              } catch (promiseError: unknown) {
                console.error(
                  "Failed to resolve Promise result for contextualized embedding:",
                  promiseError
                );
                return {
                  vectorized: false,
                  error: `Contextualized embedding failed: Promise resolution error - ${promiseError instanceof Error ? promiseError.message : String(promiseError)}`,
                };
              }
            } else {
              // Log the result structure for debugging
              console.log("Contextualized embedding result structure:", {
                type: typeof result,
                isArray: Array.isArray(result),
                hasEmbeddings:
                  result &&
                  typeof result === "object" &&
                  "embeddings" in result,
                keys:
                  result && typeof result === "object"
                    ? Object.keys(result)
                    : [],
              });

              vectorValues = result as number[][];
            }
          } catch (error: unknown) {
            console.error("Contextualization failed:", error);
            return {
              vectorized: false,
              error:
                "Contextualization failed: " +
                (error instanceof Error ? error.message : String(error)),
            };
          }
        }

        // Validate vector values
        if (!vectorValues) {
          console.error("Embedding returned null or undefined");
          return {
            vectorized: false,
            error: "Embedding engine returned null or undefined",
          };
        }

        // Handle the case where vectorValues is an object with embeddings property (new JinaEmbedder format)
        if (
          typeof vectorValues === "object" &&
          !Array.isArray(vectorValues) &&
          (vectorValues as any).embeddings
        ) {
          console.log(
            "Detected object with embeddings property, extracting embeddings array"
          );
          vectorValues = (vectorValues as any).embeddings;
        }

        if (!Array.isArray(vectorValues)) {
          console.error(`Embedding returned non-array: ${typeof vectorValues}`);
          return {
            vectorized: false,
            error: `Embedding engine returned invalid type: ${typeof vectorValues}`,
          };
        }

        if (vectorValues.length === 0) {
          console.error("Embedding returned empty array");
          return {
            vectorized: false,
            error: "Embedding engine returned empty array",
          };
        }

        console.log(`Successfully embedded ${vectorValues.length} chunks`);
      } catch (error: unknown) {
        console.error("Embedding failed:", error);
        return {
          vectorized: false,
          error: `Embedding failed: ${error instanceof Error ? error.message : String(error)}`,
        };
      }

      if (!!vectorValues && vectorValues.length > 0) {
        for (const [i, vector] of vectorValues.entries()) {
          const vectorRecord = {
            id: uuidv4(),
            values: vector,
            // [DO NOT REMOVE]
            // LangChain will be unable to find your text if you embed manually and dont include the `text` key.
            // https://github.com/hwchase17/langchainjs/blob/2def486af734c0ca87285a48f1a04c057ab74bdf/langchain/src/vectorstores/pinecone.ts#L64
            metadata: {
              ...documentData,
              text: textChunks[i],
            },
          };

          vectors.push(vectorRecord);
          // Ensure text field is never empty to avoid Arrow error
          let text = textChunks[i] || "Empty content";
          if (!text || text.length === 0) {
            text = "Empty content";
          }

          // Fix for Arrow error: ensure text meets minimum byte requirement
          if (Buffer.from(text, "utf8").length < 44) {
            text = text.padEnd(44, " ");
          }

          // Create submission with validated text field
          const submission: LanceVectorRecord = {
            ...(vectorRecord.metadata as Record<string, unknown>),
            id: vectorRecord.id,
            vector: vectorRecord.values,
            index: i,
            text: text,
            docId: docId, // Add docId to the vector record
            // Include file-level metadata for display and matching
            filename,
            source: docpath || "",
            ...(contextualize && { context: contextualizedChunks[i] || "" }),
          };

          submissions.push(submission);
          documentVectors.push({ docId, vectorId: vectorRecord.id });
        }
      } else {
        throw new Error(
          "Could not embed document chunks! This document will not be recorded."
        );
      }

      if (vectors.length > 0) {
        const { toChunks } = await import("../../helpers");
        const { storeVectorResult } = await import("../../files");
        const chunks = [];
        for (const chunk of toChunks(vectors, 500)) chunks.push(chunk);

        console.log("Inserting vectorized chunks into LanceDB collection.");
        const { client } = await this.connect();
        await this.updateOrCreateCollection(client, submissions, namespace);
        await storeVectorResult(chunks, fullFilePath);
      }
      await DocumentVectors.bulkInsert(documentVectors);
      return { vectorized: true, error: null };
    } catch (e: unknown) {
      console.error(
        "addDocumentToNamespace",
        e instanceof Error ? e.message : String(e)
      );
      return {
        vectorized: false,
        error: e instanceof Error ? e.message : String(e),
      };
    }
  },

  async performSimilaritySearch({
    namespace,
    input = "",
    LLMConnector,
    similarityThreshold = 0.25,
    topN = 4,
    filterIdentifiers = [],
    rerank = false,
    workspace = null,
  }: SimilaritySearchParams): Promise<{
    contextTexts: string[];
    sources: Record<string, unknown>[];
    message: string | false;
  }> {
    if (!namespace || !input || !LLMConnector) {
      throw new Error("Invalid request to performSimilaritySearch.");
    }

    const { client } = await this.connect();
    if (!(await this.namespaceExists(client, namespace))) {
      return {
        contextTexts: [],
        sources: [],
        message: "Invalid query - no documents found for workspace!",
      };
    }

    // Use workspace's topN setting if available, otherwise use the provided default
    const effectiveTopN = (workspace?.topN ?? false) || topN;

    // Check if re-ranking is globally enabled
    const globalRerank = await SystemSettings.get({
      label: "enable_lancedb_rerank",
    });
    const isGloballyEnabled = (globalRerank?.value ?? 0) === "on";

    // Only perform re-ranking if both global and workspace settings allow it
    const shouldRerank = isGloballyEnabled && rerank;

    const queryVector = await LLMConnector.embedTextInput(input);
    const result = shouldRerank
      ? await this.rerankedSimilarityResponse({
          client,
          namespace,
          query: input,
          queryVector,
          similarityThreshold,
          topN: Number(effectiveTopN),
          filterIdentifiers,
        })
      : await this.similarityResponse({
          client,
          namespace,
          queryVector,
          similarityThreshold,
          topN: Number(effectiveTopN),
          filterIdentifiers,
        });

    // Map source documents to include text
    let sources = result.sourceDocuments.map(
      (metadata: Record<string, unknown>, i: number) => {
        return { ...metadata, text: result.contextTexts[i] };
      }
    );

    // Apply similarity bump to starred documents if workspace is provided
    if (workspace && workspace.id) {
      const {
        applySimilarityBumpToStarredDocuments,
      } = require("../../helpers/starredDocuments");
      sources = await applySimilarityBumpToStarredDocuments(
        sources,
        workspace.id
      );

      // Re-sort sources by score after applying the similarity bump
      sources.sort(
        (a: LanceSourceDoc, b: LanceSourceDoc) =>
          (b.score || 0) - (a.score || 0)
      );

      // Rebuild contextTexts array to match the new order of sources
      const newContextTexts = sources.map(
        (source: LanceSourceDoc) => source.text
      );
      result.contextTexts = newContextTexts;
      result.scores = sources.map(
        (source: LanceSourceDoc) => source.score || 0
      );
    }

    return {
      contextTexts: result.contextTexts,
      sources: this.curateSources(sources),
      message: false,
    };
  },

  async checkDocumentHasDocId(
    namespace: string,
    docId: string
  ): Promise<boolean> {
    try {
      if (!namespace || !docId) return false;

      const { client } = await this.connect();
      if (!(await this.namespaceExists(client, namespace))) {
        console.log(`[LANCE-DB] Namespace ${namespace} does not exist`);
        return false;
      }

      const table = await client.openTable(namespace);

      // First check if the schema has a docId field
      const schema = await table.schema();
      const hasDocIdField = schema.fields.some(
        (field: { name: string }) => field.name === "docId"
      );

      if (!hasDocIdField) {
        console.log(
          `[LANCE-DB] Schema for namespace ${namespace} does not have docId field`
        );
        // If the schema doesn't have docId field, we'll assume the document is properly vectorized
        // This prevents unnecessary re-vectorization prompts for older documents
        return true;
      }

      // Query for vectors with this docId
      let hasDocId = false;
      try {
        for await (const batch of table
          .query()
          .where(`"docId" = '${docId}'`)
          .limit(1)) {
          const rows = batch.toArray();
          if ((rows?.length ?? 0) > 0) {
            hasDocId = true;
            break;
          }
        }
      } catch (queryError: unknown) {
        // If there's a schema error in the query, log it but don't fail the check
        if (
          queryError instanceof Error &&
          queryError.toString().includes("Schema error")
        ) {
          console.log(
            `[LANCE-DB] Schema error when querying for docId: ${queryError.message}`
          );
          // Return true to prevent unnecessary re-vectorization
          return true;
        }
        // For other errors, rethrow
        throw queryError;
      }

      return hasDocId;
    } catch (error: unknown) {
      console.error(`[LANCE-DB] Error checking if document has docId:`, error);
      // Return true for schema-related errors to prevent unnecessary re-vectorization
      if (error instanceof Error && error.toString().includes("Schema error")) {
        return true;
      }
      return false;
    }
  },

  async reset(): Promise<{ reset: boolean }> {
    const { client } = await this.connect();
    fs.rm(
      `${(client as LanceClient & { uri: string }).uri}`,
      { recursive: true },
      () => null
    );
    return { reset: true };
  },

  curateSources(
    sources: Array<Record<string, unknown>> = []
  ): Array<Record<string, unknown>> {
    const documents = [];
    for (const source of sources) {
      // Destructure and ignore unused variables with underscore prefix
      const { text, vector: _v, _distance: _d, docId, ...rest } = source;
      const metadata = Object.prototype.hasOwnProperty.call(rest, "metadata")
        ? rest.metadata
        : rest;

      // Ensure metadata is an object before spreading
      if (
        metadata &&
        typeof metadata === "object" &&
        Object.keys(metadata).length > 0
      ) {
        documents.push({
          ...(metadata as Record<string, unknown>),
          ...(text ? { text } : {}),
          ...(docId ? { docId } : {}), // Preserve docId if it exists
        });
      }
    }

    return documents;
  },
};
