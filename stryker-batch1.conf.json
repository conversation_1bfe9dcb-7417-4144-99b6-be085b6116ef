{"$schema": "./node_modules/@stryker-mutator/core/schema/stryker-schema.json", "packageManager": "npm", "reporters": ["html", "clear-text", "progress", "json"], "testRunner": "command", "commandRunner": {"command": "npm test -- server/models/__tests__/systemSettings.test.ts server/models/__tests__/user.test.ts server/models/__tests__/workspace.test.ts server/models/__tests__/documents.test.ts server/models/__tests__/apiKeys.test.ts"}, "coverageAnalysis": "perTest", "mutate": ["server/models/systemSettings.ts", "server/models/userClass.ts", "server/models/workspace.ts", "server/models/documents.ts", "server/models/apiKeys.ts"], "thresholds": {"high": 90, "low": 80, "break": 70}, "timeoutMS": 300000, "timeoutFactor": 5, "concurrency": 2, "tempDirName": "stryker-tmp", "cleanTempDir": "always", "ignorePatterns": ["test-results/**/*", "reports/**/*", "coverage/**/*", "*.log", "stryker-tmp/**/*"], "disableTypeChecks": "**/*.ts", "logLevel": "info", "fileLogLevel": "debug", "allowConsoleColors": true, "htmlReporter": {"fileName": "reports/mutation/batch1-mutation-report.html"}, "jsonReporter": {"fileName": "reports/mutation/batch1-mutation-report.json"}, "plugins": ["@stryker-mutator/jest-runner"]}