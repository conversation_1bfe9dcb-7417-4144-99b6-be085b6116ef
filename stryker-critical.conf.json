{"$schema": "./node_modules/@stryker-mutator/core/schema/stryker-schema.json", "packageManager": "npm", "reporters": ["html", "clear-text", "progress", "json"], "testRunner": "command", "commandRunner": {"command": "npm test -- server/models/__tests__/systemSettings.test.ts server/models/__tests__/user.test.ts server/models/__tests__/workspace.test.ts server/models/__tests__/documents.test.ts server/models/__tests__/workspaceChats.test.ts server/models/__tests__/organization.test.ts server/models/__tests__/apiKeys.test.ts server/models/__tests__/eventLogs.test.ts server/endpoints/__tests__/admin.test.ts server/endpoints/__tests__/system-simple.test.ts server/endpoints/__tests__/chat-simple.test.ts server/endpoints/__tests__/workspaces.test.ts server/endpoints/__tests__/document.test.ts server/endpoints/__tests__/embedManagement.test.ts server/endpoints/__tests__/workspaceThreads.test.ts server/endpoints/__tests__/workspaceDocuments.test.ts server/endpoints/__tests__/user.test.ts server/endpoints/__tests__/requestLegalAssistance.test.ts server/endpoints/__tests__/generateLegalTaskPrompt.test.ts server/endpoints/__tests__/autoCodePrompt.test.ts"}, "coverageAnalysis": "perTest", "mutate": ["server/models/systemSettings.ts", "server/models/userClass.ts", "server/models/workspace.ts", "server/models/documents.ts", "server/models/workspaceChats.ts", "server/models/organization.ts", "server/models/apiKeys.ts", "server/models/eventLogs.ts", "server/endpoints/admin.ts", "server/endpoints/system.ts", "server/endpoints/chat.ts", "server/endpoints/workspaces.ts", "server/endpoints/document.ts", "server/endpoints/embedManagement.ts", "server/endpoints/workspaceThreads.ts", "server/endpoints/workspaceDocuments.ts", "server/endpoints/user.ts", "server/endpoints/requestLegalAssistance.ts", "server/endpoints/generateLegalTaskPrompt.ts", "server/endpoints/autoCodePrompt.ts"], "thresholds": {"high": 90, "low": 80, "break": 70}, "timeoutMS": 300000, "timeoutFactor": 5, "concurrency": 4, "tempDirName": "stryker-tmp", "cleanTempDir": "always", "ignorePatterns": ["test-results/**/*", "reports/**/*", "coverage/**/*", "*.log", "stryker-tmp/**/*", "node_modules/**/*", ".git/**/*"], "disableTypeChecks": "**/*.ts", "logLevel": "info", "fileLogLevel": "debug", "allowConsoleColors": true, "htmlReporter": {"fileName": "reports/mutation/critical-files-mutation-report.html"}, "jsonReporter": {"fileName": "reports/mutation/critical-files-mutation-report.json"}, "plugins": ["@stryker-mutator/jest-runner"], "jest": {"projectType": "custom", "configFile": "jest.mutation.config.cjs", "enableFindRelatedTests": false, "config": {"testEnvironment": "node"}}, "incremental": true, "incrementalFile": "reports/mutation/.stryker-incremental.json"}