{"$schema": "./node_modules/@stryker-mutator/core/schema/stryker-schema.json", "packageManager": "npm", "reporters": ["html", "clear-text", "progress", "json"], "testRunner": "jest", "coverageAnalysis": "perTest", "mutate": ["server/models/systemSettings.ts", "server/models/workspace.ts", "server/models/workspaceChats.ts", "server/models/eventLogs.ts"], "thresholds": {"high": 90, "low": 80, "break": 70}, "timeoutMS": 300000, "timeoutFactor": 5, "concurrency": 1, "tempDirName": "stryker-tmp", "cleanTempDir": "always", "ignorePatterns": ["test-results/**/*", "reports/**/*", "coverage/**/*", "*.log", "stryker-tmp/**/*"], "disableTypeChecks": "**/*.ts", "logLevel": "info", "fileLogLevel": "debug", "allowConsoleColors": true, "htmlReporter": {"fileName": "reports/mutation/isolated-mutation-report.html"}, "jsonReporter": {"fileName": "reports/mutation/isolated-mutation-report.json"}, "jest": {"projectType": "custom", "configFile": "jest.mutation.isolated.config.cjs", "enableFindRelatedTests": false}, "plugins": ["@stryker-mutator/jest-runner"]}