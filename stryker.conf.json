{"$schema": "./node_modules/@stryker-mutator/core/schema/stryker-schema.json", "packageManager": "npm", "reporters": ["html", "clear-text", "progress", "dashboard"], "testRunner": "command", "commandRunner": {"command": "npm test -- server/models/__tests__/eventLogs.test.ts server/models/__tests__/user.test.ts server/models/__tests__/workspace.test.ts"}, "coverageAnalysis": "perTest", "mutate": ["server/models/eventLogs.ts", "server/models/userClass.ts", "server/models/workspace.ts"], "thresholds": {"high": 90, "low": 80, "break": 70}, "timeoutMS": 180000, "timeoutFactor": 3, "concurrency": 2, "tempDirName": "stryker-tmp", "cleanTempDir": "always", "ignorePatterns": ["test-results/**/*", "reports/**/*", "coverage/**/*", "*.log", "stryker-tmp/**/*"], "logLevel": "info", "fileLogLevel": "debug", "allowConsoleColors": true, "dashboard": {"project": "github.com/ISTLegal/ISTLegal", "version": "develop", "module": "core"}, "htmlReporter": {"fileName": "reports/mutation/mutation-report.html"}, "jest": {"projectType": "custom", "configFile": "jest.mutation.config.cjs", "enableFindRelatedTests": false, "config": {"testEnvironment": "node"}}, "disableTypeChecks": "**/*.ts", "plugins": ["@stryker-mutator/jest-runner"]}