/**
 * End-to-End Test for CDB Progress Stuck Issue
 *
 * This test reproduces the exact issue where:
 * 1. User opens AskLegalQuestion modal
 * 2. Selects a legal task and clicks confirm
 * 3. Progress tracker starts showing but gets stuck on first task
 * 4. No server or browser logs appear
 *
 * This test helps identify where in the flow the issue occurs.
 */

const { test, expect, beforeEach, afterEach } = require("@playwright/test");

test.describe("CDB Progress Stuck Issue Reproduction", () => {
  let page;
  let context;

  beforeEach(async ({ browser }) => {
    // Create a new context and page for each test
    context = await browser.newContext();
    page = await context.newPage();

    // Enable console logging to catch any client-side errors
    page.on("console", (msg) => {
      console.log(`[Browser Console ${msg.type()}]:`, msg.text());
    });

    // Enable network request monitoring
    page.on("request", (request) => {
      if (
        request.url().includes("/stream-chat") ||
        request.url().includes("/cdb")
      ) {
        console.log(`[Network Request]: ${request.method()} ${request.url()}`);
      }
    });

    page.on("response", (response) => {
      if (
        response.url().includes("/stream-chat") ||
        response.url().includes("/cdb")
      ) {
        console.log(
          `[Network Response]: ${response.status()} ${response.url()}`
        );
      }
    });

    // Navigate to the application
    await page.goto("http://localhost:3000");

    // Wait for the app to load and perform login if needed
    // This would depend on your specific authentication setup
    await page.waitForSelector("body");
  });

  afterEach(async () => {
    if (context) {
      await context.close();
    }
  });

  test("should reproduce CDB progress stuck issue", async () => {
    console.log("\n=== Starting CDB Progress Stuck Issue Test ===\n");

    // Step 1: Navigate to a workspace
    console.log("Step 1: Navigating to workspace...");

    // Wait for workspace to be visible (adjust selector based on your UI)
    await page.waitForSelector('[data-testid="workspace-container"]', {
      timeout: 10000,
    });

    // Step 2: Open the AskLegalQuestion modal
    console.log("Step 2: Opening AskLegalQuestion modal...");

    // Look for button/link that opens the modal (adjust selector)
    const askLegalQuestionButton = page
      .locator(
        '[data-testid="ask-legal-question"], text="Ask Legal Question", text="Perform Legal Task"'
      )
      .first();
    await askLegalQuestionButton.waitFor({ timeout: 5000 });
    await askLegalQuestionButton.click();

    // Verify modal opened
    await page.waitForSelector('[data-testid="ask-legal-question-modal"]', {
      timeout: 5000,
    });
    console.log("✓ AskLegalQuestion modal opened");

    // Step 3: Select a legal task category
    console.log("Step 3: Selecting legal task category...");

    // Wait for categories to load
    await page.waitForSelector('[data-testid="legal-task-category"]', {
      timeout: 5000,
    });

    // Click on first available category
    const firstCategory = page
      .locator('[data-testid="legal-task-category"]')
      .first();
    await firstCategory.click();
    console.log("✓ Legal task category selected");

    // Step 4: Select a specific task
    console.log("Step 4: Selecting specific legal task...");

    // Wait for subcategories/tasks to load
    await page.waitForSelector('[data-testid="legal-task-item"]', {
      timeout: 5000,
    });

    // Click on first available task
    const firstTask = page.locator('[data-testid="legal-task-item"]').first();
    await firstTask.click();
    console.log("✓ Legal task selected");

    // Step 5: Fill in task details if needed
    console.log("Step 5: Filling task details...");

    // If main document selection is required
    const mainDocSelect = page.locator('select[id="main-document-select"]');
    if (await mainDocSelect.isVisible()) {
      await mainDocSelect.selectOption({ index: 1 }); // Select first available document
      console.log("✓ Main document selected");
    }

    // Add custom instructions if field exists
    const customInstructions = page.locator('textarea[placeholder*="custom"]');
    if (await customInstructions.isVisible()) {
      await customInstructions.fill("Test custom instructions for CDB flow");
      console.log("✓ Custom instructions added");
    }

    // Step 6: Start monitoring for progress tracking
    console.log("Step 6: Setting up progress tracking monitors...");

    let progressUpdatesReceived = [];
    let networkRequestsMade = [];
    let consoleErrors = [];

    // Monitor console for progress-related logs
    page.on("console", (msg) => {
      const text = msg.text();
      if (
        text.includes("[AskLegalQuestion]") ||
        text.includes("[ProgressStore]") ||
        text.includes("[useThreadProgress]") ||
        text.includes("cdbProgress")
      ) {
        progressUpdatesReceived.push(text);
        console.log(`[Progress Update]: ${text}`);
      }
      if (msg.type() === "error") {
        consoleErrors.push(text);
        console.log(`[Console Error]: ${text}`);
      }
    });

    // Monitor network requests
    page.on("request", (request) => {
      if (
        request.url().includes("stream-chat") &&
        request.method() === "POST"
      ) {
        networkRequestsMade.push({
          url: request.url(),
          method: request.method(),
          postData: request.postData(),
        });
        console.log(`[CDB Request]: ${request.method()} ${request.url()}`);
      }
    });

    // Step 7: Click confirm to start CDB process
    console.log("Step 7: Starting CDB process...");

    const confirmButton = page.locator(
      'button:has-text("Confirm"), button:has-text("Start"), [data-testid="confirm-legal-task"]'
    );
    await confirmButton.waitFor({ timeout: 5000 });

    // Record time when process starts
    const processStartTime = Date.now();

    await confirmButton.click();
    console.log("✓ CDB process initiated");

    // Step 8: Monitor for progress tracker appearance
    console.log("Step 8: Monitoring progress tracker...");

    // Look for progress tracker UI elements
    const progressTracker = page
      .locator(
        '[data-testid="progress-tracker"], [data-testid="cdb-progress"], .progress-container'
      )
      .first();

    try {
      await progressTracker.waitFor({ timeout: 5000 });
      console.log("✓ Progress tracker appeared");

      // Check if progress tracker shows any steps
      const progressSteps = page.locator(
        '[data-testid="progress-step"], .progress-step'
      );
      const stepCount = await progressSteps.count();
      console.log(`Progress tracker shows ${stepCount} steps`);
    } catch (error) {
      console.log("⚠ Progress tracker did not appear within 5 seconds");
    }

    // Step 9: Wait and monitor for progress updates
    console.log("Step 9: Waiting for progress updates...");

    // Wait for up to 30 seconds for progress updates
    const waitTime = 30000;
    const checkInterval = 1000;
    let elapsedTime = 0;
    let lastProgressState = null;

    while (elapsedTime < waitTime) {
      await page.waitForTimeout(checkInterval);
      elapsedTime += checkInterval;

      // Check current progress state
      const currentStep = await page
        .locator('[data-testid="current-step"]')
        .textContent()
        .catch(() => "N/A");
      const currentStatus = await page
        .locator('[data-testid="step-status"]')
        .textContent()
        .catch(() => "N/A");

      const progressState = `Step: ${currentStep}, Status: ${currentStatus}`;

      if (progressState !== lastProgressState) {
        console.log(`[${elapsedTime}ms] Progress State: ${progressState}`);
        lastProgressState = progressState;
      }

      // Check if process completed
      const isCompleted = await page
        .locator('[data-testid="process-completed"]')
        .isVisible()
        .catch(() => false);
      if (isCompleted) {
        console.log("✓ CDB process completed successfully");
        break;
      }

      // Check for error states
      const hasError = await page
        .locator('[data-testid="process-error"]')
        .isVisible()
        .catch(() => false);
      if (hasError) {
        const errorMessage = await page
          .locator('[data-testid="error-message"]')
          .textContent()
          .catch(() => "Unknown error");
        console.log(`✗ CDB process failed with error: ${errorMessage}`);
        break;
      }
    }

    const processEndTime = Date.now();
    const totalProcessTime = processEndTime - processStartTime;

    // Step 10: Analyze results
    console.log("\n=== Analysis Results ===");
    console.log(`Total process time: ${totalProcessTime}ms`);
    console.log(`Progress updates received: ${progressUpdatesReceived.length}`);
    console.log(`Network requests made: ${networkRequestsMade.length}`);
    console.log(`Console errors: ${consoleErrors.length}`);

    if (progressUpdatesReceived.length > 0) {
      console.log("\\nProgress updates:");
      progressUpdatesReceived.forEach((update, i) => {
        console.log(`  ${i + 1}. ${update}`);
      });
    }

    if (networkRequestsMade.length > 0) {
      console.log("\\nNetwork requests:");
      networkRequestsMade.forEach((req, i) => {
        console.log(`  ${i + 1}. ${req.method} ${req.url}`);
        if (req.postData) {
          try {
            const data = JSON.parse(req.postData);
            console.log(
              `     CDB: ${data.cdb}, Options: ${data.cdbOptions?.length} items`
            );
          } catch (e) {
            console.log(`     Post data length: ${req.postData.length}`);
          }
        }
      });
    }

    if (consoleErrors.length > 0) {
      console.log("\\nConsole errors:");
      consoleErrors.forEach((error, i) => {
        console.log(`  ${i + 1}. ${error}`);
      });
    }

    // Step 11: Assert expected behavior
    console.log("\\n=== Test Assertions ===");

    // Test should verify that progress updates were received
    if (progressUpdatesReceived.length === 0) {
      console.log("❌ ISSUE REPRODUCED: No progress updates received");
      console.log("   This indicates the progress tracking is stuck");
    } else {
      console.log("✅ Progress updates were received successfully");
    }

    // Test should verify that network requests were made
    if (networkRequestsMade.length === 0) {
      console.log("❌ ISSUE REPRODUCED: No CDB network requests made");
      console.log("   This indicates the request never reached the server");
    } else {
      console.log("✅ CDB network requests were made");
    }

    // Test should verify no unexpected errors
    if (consoleErrors.length > 0) {
      console.log("⚠ Console errors were found (may indicate issues)");
    } else {
      console.log("✅ No console errors found");
    }

    console.log("\\n=== End of Test ===\\n");

    // Final assertion for the test framework
    // This test is designed to document the issue rather than pass/fail
    // In a real scenario, you'd want progress updates to be received
    expect(networkRequestsMade.length).toBeGreaterThan(0); // At least verify request was made
  });

  test("should verify progress store behavior independently", async () => {
    console.log("\\n=== Testing Progress Store Behavior ===\\n");

    // Navigate to app and inject test script
    await page.goto("http://localhost:3000");
    await page.waitForSelector("body");

    // Inject progress store test
    const progressStoreTest = await page.evaluate(() => {
      // Test progress store directly in browser context
      const testResults = {
        progressStoreAvailable: false,
        canStartProcess: false,
        canUpdateProgress: false,
        processState: null,
        errors: [],
      };

      try {
        // Check if progress store is available
        if (window.useProgressStore || (window as any).progressStore) {
          testResults.progressStoreAvailable = true;

          const store =
            window.useProgressStore?.getState?.() ||
            (window as any).progressStore?.getState?.();

          if (store) {
            // Test starting a process
            try {
              store.startProcess("test-thread", 9, "main");
              testResults.canStartProcess = true;

              // Test updating progress
              store.updateProgress(
                {
                  step: 1,
                  status: "in_progress",
                  message: "Test progress update",
                },
                "test-thread"
              );
              testResults.canUpdateProgress = true;

              // Get current state
              testResults.processState = store.getThreadState("test-thread");
            } catch (error) {
              testResults.errors.push(
                `Progress operations failed: ${error.message}`
              );
            }
          }
        }
      } catch (error) {
        testResults.errors.push(
          `Progress store access failed: ${error.message}`
        );
      }

      return testResults;
    });

    console.log("Progress store test results:");
    console.log(`- Available: ${progressStoreTest.progressStoreAvailable}`);
    console.log(`- Can start process: ${progressStoreTest.canStartProcess}`);
    console.log(
      `- Can update progress: ${progressStoreTest.canUpdateProgress}`
    );
    console.log(`- Current state:`, progressStoreTest.processState);

    if (progressStoreTest.errors.length > 0) {
      console.log("Errors found:");
      progressStoreTest.errors.forEach((error) => console.log(`  - ${error}`));
    }

    // Assert basic functionality works
    expect(progressStoreTest.errors.length).toBe(0);
    expect(progressStoreTest.progressStoreAvailable).toBe(true);
  });
});

module.exports = {};
